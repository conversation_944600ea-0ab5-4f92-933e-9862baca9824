<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soul Potion Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background-color: #2d2d2d;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 2px solid #444;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #4CAF50;
            color: white;
        }
        .error {
            background-color: #f44336;
            color: white;
        }
        .info {
            background-color: #2196F3;
            color: white;
        }
        .test-button {
            background-color: #9966ff;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .test-button:hover {
            background-color: #7744dd;
        }
        code {
            background: #000;
            padding: 10px;
            display: block;
            color: #0f0;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Soul Potion Implementation Test</h1>
        
        <div class="test-section">
            <h2>Implementation Status</h2>
            <div id="implementation-status" class="status info">Checking implementation...</div>
        </div>

        <div class="test-section">
            <h2>Soul Potion Details</h2>
            <div id="potion-details" class="status info">Loading soul potion details...</div>
        </div>

        <div class="test-section">
            <h2>Test Commands</h2>
            <p>Use these commands in the browser console when in a dungeon:</p>
            <code>
// Basic Commands<br>
itemlist() - See all items including soul potion<br>
give('soul_potion') - Add soul potion to card inventory<br>
// Then drag the soul potion from inventory to screen to heal<br><br>

// Advanced Testing<br>
damagePlayer(10) - Take 10 damage for testing<br>
give('soul_potion') - Add soul potion to inventory<br>
// Drag soul potion to center of screen to heal 6 health<br>
console.log(dungeonHandler.playerController.actualHealth) - Check health<br><br>

// Card System Testing<br>
testSoulPotion() - Quick test function<br>
giveForestBlessing() - Add forest blessing for comparison<br>
give('call_of_ascension') - Add flight card for comparison<br><br>

// Loot Testing<br>
// Open chests to find soul potions naturally<br>
// Soul potions appear in all chest types
            </code>
        </div>

        <div class="test-section">
            <h2>Expected Features</h2>
            <ul>
                <li>✅ Soul potion appears in itemlist() command</li>
                <li>✅ Appears in card inventory bar when given</li>
                <li>✅ Beautiful card artwork with bottle, liquid, and aura</li>
                <li>✅ Animated card with floating liquid and particles</li>
                <li>✅ Drag-and-drop functionality like cards</li>
                <li>✅ Purple border and glow matching soul theme</li>
                <li>✅ Restores exactly 6 health points when dragged to screen</li>
                <li>✅ Beautiful healing effect with purple soul orbs</li>
                <li>✅ Player glows purple when healed</li>
                <li>✅ Available in all chest types and loot pools</li>
                <li>✅ Common rarity (60% drop chance from normal chests)</li>
                <li>✅ Works alongside cards in the same inventory</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>Visual Design</h2>
            <p>The soul potion features:</p>
            <ul>
                <li><strong>Crystal Bottle</strong>: Hexagonal silver crystal bottle</li>
                <li><strong>Soul Liquid</strong>: Three layers of glowing purple liquid with swirling effect</li>
                <li><strong>Cork Stopper</strong>: Dark cork sealing the bottle</li>
                <li><strong>Mystical Aura</strong>: Floating purple energy particles around the bottle</li>
                <li><strong>Animation</strong>: Gentle floating motion and slow rotation</li>
                <li><strong>Lighting</strong>: Built-in point light for atmospheric glow</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // Test soul potion implementation
        import { ITEM_TYPES, getItemData } from './src/entities/ItemTypes.js';

        document.addEventListener('DOMContentLoaded', function() {
            const implementationStatus = document.getElementById('implementation-status');
            const potionDetails = document.getElementById('potion-details');
            
            try {
                // Check if soul potion exists in item types
                const hasSoulPotionType = 'SOUL_POTION' in ITEM_TYPES;
                const soulPotionId = ITEM_TYPES.SOUL_POTION;
                
                // Get soul potion data
                const soulPotionData = getItemData('soul_potion');
                
                // Check if it's in the full item list
                const allItems = Object.keys(ITEM_TYPES).map(key => ITEM_TYPES[key]);
                const inItemList = allItems.includes('soul_potion');
                
                if (hasSoulPotionType && soulPotionData && inItemList) {
                    implementationStatus.textContent = '✅ SUCCESS: Soul potion fully implemented!';
                    implementationStatus.className = 'status success';
                    
                    // Show potion details
                    potionDetails.innerHTML = `
                        <h3>Soul Potion Details:</h3>
                        <ul>
                            <li><strong>Name:</strong> ${soulPotionData.name}</li>
                            <li><strong>Category:</strong> ${soulPotionData.category}</li>
                            <li><strong>Rarity:</strong> ${soulPotionData.rarity}</li>
                            <li><strong>Effect:</strong> ${soulPotionData.effect} (${soulPotionData.effectValue} health)</li>
                            <li><strong>Description:</strong> ${soulPotionData.description}</li>
                            <li><strong>Glow Color:</strong> #${soulPotionData.glow.color.toString(16).padStart(6, '0')}</li>
                            <li><strong>Glow Intensity:</strong> ${soulPotionData.glow.intensity}</li>
                        </ul>
                    `;
                    potionDetails.className = 'status success';
                } else {
                    implementationStatus.textContent = '❌ FAILED: Soul potion implementation incomplete';
                    implementationStatus.className = 'status error';
                    
                    potionDetails.textContent = 'Implementation issues detected. Check console for details.';
                    potionDetails.className = 'status error';
                }
                
            } catch (error) {
                implementationStatus.textContent = '❌ ERROR: ' + error.message;
                implementationStatus.className = 'status error';
                
                potionDetails.textContent = 'Error loading soul potion data: ' + error.message;
                potionDetails.className = 'status error';
                
                console.error('Soul potion test error:', error);
            }
        });
    </script>
</body>
</html>