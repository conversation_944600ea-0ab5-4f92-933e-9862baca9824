<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Event Room Door Visibility</title>
    <style>
        body { margin: 0; font-family: Arial; background: #000; color: #fff; }
        #info { position: absolute; top: 10px; left: 10px; }
        #canvas-container { width: 100vw; height: 100vh; }
    </style>
</head>
<body>
    <div id="info">
        <h3>Event Room Door Visibility Test</h3>
        <p>Testing if door models are created properly</p>
        <div id="status"></div>
    </div>
    <div id="canvas-container"></div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        const status = document.getElementById('status');
        
        // Create scene
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x202020);
        
        // Camera
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(5, 5, 5);
        
        // Renderer
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.getElementById('canvas-container').appendChild(renderer.domElement);
        
        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        
        // Lights
        scene.add(new THREE.AmbientLight(0xffffff, 0.6));
        const dirLight = new THREE.DirectionalLight(0xffffff, 0.4);
        dirLight.position.set(5, 10, 5);
        scene.add(dirLight);
        
        // Grid
        scene.add(new THREE.GridHelper(10, 10));
        
        // Import door functions
        async function testDoors() {
            try {
                status.innerHTML = 'Loading door modules...';
                
                // Import the door creation modules
                const { createWaterArchwayDoor, createSandstoneArchwayDoor } = await import('./src/generators/prefabs/waterArchwayDoor.js');
                const { createStoneArchwayDoor } = await import('./src/generators/prefabs/stoneArchwayDoor.js');
                
                status.innerHTML = 'Creating doors...';
                
                // Test parameters
                const DOOR_WIDTH = 2.5;
                const DOOR_HEIGHT = 3.5;
                const WALL_DEPTH = 0.5;
                
                // Test water archway door
                const waterDoor = createWaterArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
                if (waterDoor) {
                    waterDoor.position.x = -3;
                    scene.add(waterDoor);
                    status.innerHTML += '<br>✅ Water door created';
                    console.log('Water door:', waterDoor);
                    console.log('Water door children:', waterDoor.children.length);
                    console.log('Water door visible:', waterDoor.visible);
                } else {
                    status.innerHTML += '<br>❌ Water door failed';
                }
                
                // Test sandstone archway door
                const sandstoneDoor = createSandstoneArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
                if (sandstoneDoor) {
                    sandstoneDoor.position.x = 0;
                    scene.add(sandstoneDoor);
                    status.innerHTML += '<br>✅ Sandstone door created';
                    console.log('Sandstone door:', sandstoneDoor);
                    console.log('Sandstone door children:', sandstoneDoor.children.length);
                    console.log('Sandstone door visible:', sandstoneDoor.visible);
                } else {
                    status.innerHTML += '<br>❌ Sandstone door failed';
                }
                
                // Test stone archway door
                const stoneDoor = createStoneArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
                if (stoneDoor) {
                    stoneDoor.position.x = 3;
                    scene.add(stoneDoor);
                    status.innerHTML += '<br>✅ Stone door created';
                    console.log('Stone door:', stoneDoor);
                    console.log('Stone door children:', stoneDoor.children.length);
                    console.log('Stone door visible:', stoneDoor.visible);
                } else {
                    status.innerHTML += '<br>❌ Stone door failed';
                }
                
                // Log scene info
                console.log('Scene children:', scene.children.length);
                scene.traverse(child => {
                    if (child.isMesh) {
                        console.log('Mesh found:', child.name, 'visible:', child.visible, 'material:', child.material);
                    }
                });
                
            } catch (error) {
                status.innerHTML = '❌ Error: ' + error.message;
                console.error('Test failed:', error);
            }
        }
        
        // Run test
        testDoors();
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();
        
        // Handle resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>