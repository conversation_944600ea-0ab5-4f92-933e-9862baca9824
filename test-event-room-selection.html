<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Room Selection Test</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        pre {
            background: #000;
            padding: 10px;
            border: 1px solid #00ff00;
            overflow-x: auto;
        }
        .highlight {
            color: #ff0;
            font-weight: bold;
        }
        .event-room {
            color: #00ffff;
        }
        .boss-room {
            color: #ff0000;
        }
        .secret-room {
            color: #ff00ff;
        }
        button {
            background: #00ff00;
            color: #000;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #00dd00;
        }
    </style>
</head>
<body>
    <h1>Event Room Selection Test</h1>
    <button onclick="runTest()">Generate New Dungeon</button>
    <div id="output"></div>

    <script type="module">
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
        import { EventRoomManager } from './src/systems/EventRoomManager.js';
        import { BossRoomManager } from './src/systems/BossRoomManager.js';
        import { getAvailableEventRooms, getEventRoom } from './src/gameData/eventRooms/index.js';
        
        const output = document.getElementById('output');
        
        function log(message, className = '') {
            const div = document.createElement('div');
            if (className) {
                div.className = className;
            }
            div.textContent = message;
            output.appendChild(div);
        }
        
        window.runTest = async function() {
            output.innerHTML = '';
            
            log('=== Event Room Selection Test ===', 'highlight');
            
            // Clear used event rooms for testing
            localStorage.removeItem('usedEventRooms');
            
            // Show all available event rooms
            const allEventRooms = getAvailableEventRooms();
            log('\nAll Available Event Rooms:', 'highlight');
            for (const roomId of allEventRooms) {
                const roomData = getEventRoom(roomId);
                if (roomData) {
                    const doors = Object.entries(roomData.availableConnections)
                        .filter(([_, allowed]) => allowed)
                        .map(([dir]) => dir)
                        .join(', ');
                    log(`  ${roomData.name} (${roomId}) - Doors: ${doors}`, 'event-room');
                }
            }
            
            // Generate multiple dungeons to test selection
            log('\n\n=== Generating 5 Dungeons to Test Random Selection ===', 'highlight');
            
            const selectedRooms = [];
            
            for (let i = 0; i < 5; i++) {
                log(`\n--- Dungeon ${i + 1} ---`);
                
                const dungeonGenerator = new DungeonGenerator();
                dungeonGenerator.eventRoomManager = new EventRoomManager(null, null);
                dungeonGenerator.bossRoomManager = new BossRoomManager(null, null);
                
                const areaData = {
                    id: 'catacombs',
                    name: 'Catacombs',
                    enemies: [{ type: 'zombie', weight: 1 }],
                    allowedShapes: ['SQUARE_1X1']
                };
                
                const layout = await dungeonGenerator.generateLayout(areaData.id);
                
                // Find which event room was placed
                let eventRoom = null;
                let bossRoom = null;
                let secretRooms = [];
                
                for (const [id, room] of dungeonGenerator.layout) {
                    if (room.type === 'EVENT') {
                        eventRoom = room;
                    } else if (room.type === 'Boss') {
                        bossRoom = room;
                    } else if (room.hasSecretRoom) {
                        secretRooms.push(room);
                    }
                }
                
                if (eventRoom) {
                    const eventData = eventRoom.roomData.eventRoomData;
                    selectedRooms.push(eventData.name);
                    
                    log(`  Event Room: ${eventData.name}`, 'event-room');
                    log(`  Location: Room ${eventRoom.id} at (${eventRoom.coords.x}, ${eventRoom.coords.y})`);
                    log(`  Entrance: ${eventRoom.roomData.entranceDirection}`);
                }
                
                if (bossRoom) {
                    log(`  Boss Room: Room ${bossRoom.id} at (${bossRoom.coords.x}, ${bossRoom.coords.y})`, 'boss-room');
                    
                    // Check boss room connections
                    const connections = Object.entries(bossRoom.connections)
                        .filter(([_, id]) => id !== null)
                        .map(([dir, id]) => `${dir}->Room ${id}`);
                    log(`  Boss Connections: ${connections.join(', ')}`);
                }
                
                if (secretRooms.length > 0) {
                    for (const room of secretRooms) {
                        log(`  Secret Room Host: Room ${room.id} at (${room.coords.x}, ${room.coords.y})`, 'secret-room');
                    }
                }
                
                // Verify room order - boss should be furthest
                if (eventRoom && bossRoom) {
                    const eventDist = Math.abs(eventRoom.coords.x) + Math.abs(eventRoom.coords.y);
                    const bossDist = Math.abs(bossRoom.coords.x) + Math.abs(bossRoom.coords.y);
                    
                    if (bossDist > eventDist) {
                        log('  ✅ Boss room is further than event room (correct!)', 'highlight');
                    } else {
                        log('  ❌ Boss room is NOT further than event room (incorrect!)', 'highlight');
                    }
                }
                
                // Show ASCII map
                const asciiMap = dungeonGenerator.generateASCIIMap();
                const mapPre = document.createElement('pre');
                mapPre.textContent = asciiMap;
                output.appendChild(mapPre);
            }
            
            // Show selection distribution
            log('\n\n=== Selection Distribution ===', 'highlight');
            const distribution = {};
            for (const room of selectedRooms) {
                distribution[room] = (distribution[room] || 0) + 1;
            }
            
            for (const [room, count] of Object.entries(distribution)) {
                log(`${room}: ${count} time(s)`, 'event-room');
            }
            
            // Show used event rooms
            const usedRooms = localStorage.getItem('usedEventRooms');
            if (usedRooms) {
                const used = JSON.parse(usedRooms);
                log('\n\nUsed Event Rooms (won\'t appear in next stage):', 'highlight');
                log(used.join(', '), 'event-room');
            }
        }
        
        // Run test on load
        runTest();
    </script>
</body>
</html>