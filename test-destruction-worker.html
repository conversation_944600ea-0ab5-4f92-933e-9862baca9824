<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Destruction Worker Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .pass {
            background: #2d5a2d;
            border-left: 4px solid #4caf50;
        }
        .fail {
            background: #5a2d2d;
            border-left: 4px solid #f44336;
        }
        .info {
            background: #2d4a5a;
            border-left: 4px solid #2196f3;
        }
        button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        #log {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Destruction Worker System Test</h1>
        
        <div class="test-result info">
            <strong>Testing:</strong> Web Worker-based debris processing for improved performance
        </div>
        
        <button onclick="testWorkerCreation()">Test Worker Creation</button>
        <button onclick="testEnemyDestruction()">Test Enemy Destruction</button>
        <button onclick="testObjectDestruction()">Test Object Destruction</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <h2>Test Results:</h2>
        <div id="results"></div>
        
        <h2>Debug Log:</h2>
        <div id="log"></div>
    </div>

    <script type="module">
        import { WorkerManager } from './src/core/WorkerManager.js';
        
        let workerManager;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function addResult(message, isPass) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isPass ? 'pass' : 'fail'}`;
            resultDiv.innerHTML = `<strong>${isPass ? '✅ PASS' : '❌ FAIL'}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        async function testWorkerCreation() {
            log('Testing WorkerManager creation...');
            
            try {
                workerManager = new WorkerManager();
                log('WorkerManager created successfully');
                
                // Test destruction worker initialization
                const workerId = await workerManager.initializeDestructionWorker();
                log(`Destruction worker initialized with ID: ${workerId}`);
                
                const isReady = workerManager.isDestructionWorkerReady();
                log(`Destruction worker ready: ${isReady}`);
                
                addResult('WorkerManager and destruction worker created successfully', true);
                
            } catch (error) {
                log(`Error creating worker: ${error.message}`);
                addResult(`Worker creation failed: ${error.message}`, false);
            }
        }
        
        async function testEnemyDestruction() {
            if (!workerManager || !workerManager.isDestructionWorkerReady()) {
                addResult('Worker not ready - run Worker Creation test first', false);
                return;
            }
            
            log('Testing enemy destruction processing...');
            
            try {
                // Mock enemy data
                const mockEnemyData = {
                    id: 'test_skeleton_001',
                    bodyPartGroups: [
                        {
                            name: 'head',  
                            worldPosition: { x: 0, y: 2, z: 0 },
                            material: { color: 0xF8E8C8 },
                            meshes: [
                                {
                                    worldPosition: { x: 0, y: 2, z: 0 },
                                    worldQuaternion: { x: 0, y: 0, z: 0, w: 1 },
                                    material: { color: 0xF8E8C8 }
                                }
                            ]
                        },
                        {
                            name: 'core',
                            worldPosition: { x: 0, y: 1, z: 0 },
                            material: { color: 0xE0C8A0 },
                            meshes: [
                                {
                                    worldPosition: { x: 0, y: 1, z: 0 },
                                    worldQuaternion: { x: 0, y: 0, z: 0, w: 1 },
                                    material: { color: 0xE0C8A0 }
                                }
                            ]
                        }
                    ]
                };
                
                const result = await workerManager.processEnemyDestruction(
                    mockEnemyData,
                    'skeleton',
                    { x: 1, y: 0, z: 0 }, // projectile velocity
                    -50, // ground level
                    [], // collision objects
                    Date.now() // creation time
                );
                
                log(`Enemy destruction completed. Generated ${result.debris.length} debris pieces`);
                
                // Verify debris structure
                let validDebris = 0;
                result.debris.forEach((debris, index) => {
                    if (debris.position && debris.velocity && debris.userData) {
                        validDebris++;
                    } else {
                        log(`Invalid debris piece ${index}: missing required properties`);
                    }
                });
                
                const allValid = validDebris === result.debris.length;
                log(`Valid debris pieces: ${validDebris}/${result.debris.length}`);
                
                addResult(`Enemy destruction: ${result.debris.length} debris pieces generated, ${validDebris} valid`, allValid);
                
            } catch (error) {
                log(`Error testing enemy destruction: ${error.message}`);
                addResult(`Enemy destruction failed: ${error.message}`, false);
            }
        }
        
        async function testObjectDestruction() {
            if (!workerManager || !workerManager.isDestructionWorkerReady()) {
                addResult('Worker not ready - run Worker Creation test first', false);
                return;
            }
            
            log('Testing object destruction processing...');
            
            try {
                // Mock object data with voxels
                const mockObjectData = {
                    id: 'test_vase_001',
                    name: 'stone_vase',
                    position: { x: 0, y: 0, z: 0 },
                    material: { color: 0x8B4513 },
                    originalVoxels: [
                        {
                            worldPosition: { x: 0, y: 0, z: 0 },
                            material: { color: 0x8B4513 }
                        },
                        {
                            worldPosition: { x: 0, y: 0.5, z: 0 },
                            material: { color: 0x8B4513 }
                        },
                        {
                            worldPosition: { x: 0, y: 1, z: 0 },
                            material: { color: 0x8B4513 }
                        }
                    ],
                    voxelScale: 30
                };
                
                const result = await workerManager.processObjectDestruction(
                    mockObjectData,
                    'stone_vase',
                    { x: 0, y: 1, z: 0 }, // point of impact
                    { x: -1, y: 0, z: 0 }, // projectile velocity
                    [] // collision objects
                );
                
                log(`Object destruction completed. Generated ${result.debris.length} debris pieces`);
                
                // Verify debris structure
                let validDebris = 0;
                result.debris.forEach((debris, index) => {
                    if (debris.position && debris.velocity && debris.userData) {
                        validDebris++;
                    } else {
                        log(`Invalid debris piece ${index}: missing required properties`);
                    }
                });
                
                const allValid = validDebris === result.debris.length;
                log(`Valid debris pieces: ${validDebris}/${result.debris.length}`);
                
                addResult(`Object destruction: ${result.debris.length} debris pieces generated, ${validDebris} valid`, allValid);
                
            } catch (error) {
                log(`Error testing object destruction: ${error.message}`);
                addResult(`Object destruction failed: ${error.message}`, false);
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('results').innerHTML = '';
        }
        
        // Make functions available globally
        window.testWorkerCreation = testWorkerCreation;
        window.testEnemyDestruction = testEnemyDestruction;
        window.testObjectDestruction = testObjectDestruction;
        window.clearLog = clearLog;
        
        // Auto-run basic test
        log('Destruction Worker Test System loaded');
        log('Click buttons above to run tests');
    </script>
</body>
</html>