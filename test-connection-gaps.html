<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimap Connection Gap Analysis</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #333;
            border-radius: 8px;
        }
        
        .minimap-container {
            display: inline-block;
            margin: 20px;
            background: #444;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        
        .gap-indicator {
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .good-connection {
            color: #51cf66;
            font-weight: bold;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #555;
            color: #fff;
            border: 1px solid #777;
            cursor: pointer;
            border-radius: 4px;
        }
        
        button:hover {
            background: #666;
        }
    </style>
</head>
<body>
    <h1>🔍 Minimap Connection Gap Analysis</h1>
    <p>Testing all room shape combinations to identify remaining connection issues.</p>
    
    <div style="text-align: center; margin: 20px 0;">
        <button onclick="testBasicShapes()">Test Basic Shapes</button>
        <button onclick="testComplexShapes()">Test Complex Shapes</button>
        <button onclick="testRectangularShapes()">Test Rectangular Shapes</button>
        <button onclick="testAllCombinations()">Test All Combinations</button>
    </div>
    
    <div id="test-results"></div>
    
    <script>
        // Current positioning logic from DungeonHandler.js
        function calculatePosition(shapeKey, baseX, baseY, cellSize) {
            let gridX = baseX;
            let gridY = baseY;
            
            if (shapeKey === 'SQUARE_1X1') {
                gridX = baseX;
                gridY = baseY;
            } else if (shapeKey === 'U_SHAPE_DOWN') {
                gridX = baseX - cellSize * 1.5; // Center opening at baseX
                gridY = baseY - cellSize * 2; // Bottom edge at baseY
            } else if (shapeKey === 'U_SHAPE_UP') {
                gridX = baseX - cellSize * 1.5; // Center opening at baseX
                gridY = baseY; // Top edge at baseY
            } else if (shapeKey === 'U_SHAPE_LEFT') {
                gridX = baseX; // Left edge at baseX
                gridY = baseY - cellSize * 1.5; // Center opening at baseY
            } else if (shapeKey === 'U_SHAPE_RIGHT') {
                gridX = baseX - cellSize * 2; // Right edge at baseX
                gridY = baseY - cellSize * 1.5; // Center opening at baseY
            } else if (shapeKey === 'RECT_3X1') {
                gridX = baseX - cellSize; // Center horizontally
                gridY = baseY - cellSize * 0.5; // Center vertically
            } else if (shapeKey === 'RECT_1X2') {
                gridX = baseX - cellSize * 0.5; // Center horizontally
                gridY = baseY; // Top edge aligned with grid point
            } else if (shapeKey === 'RECT_1X3') {
                gridX = baseX - cellSize * 0.5; // Center horizontally
                gridY = baseY - cellSize; // Center the 3-tall rectangle
            } else if (shapeKey === 'RECT_2X1') {
                gridX = baseX;
                gridY = baseY - cellSize * 0.5; // Center vertically
            } else if (shapeKey === 'SQUARE_2X2') {
                gridX = baseX - cellSize * 0.5;
                gridY = baseY - cellSize * 0.5;
            } else if (shapeKey === 'T_SHAPE') {
                gridX = baseX - cellSize; // Center the 3-wide shape
                gridY = baseY - cellSize; // Position so stem connection aligns with grid
            } else if (shapeKey === 'L_SHAPE') {
                gridX = baseX;
                gridY = baseY;
            } else if (shapeKey === 'CROSS_SHAPE') {
                gridX = baseX - cellSize;
                gridY = baseY - cellSize;
            }
            
            return { gridX, gridY };
        }
        
        function createRoomShapeSVG(shapeKey, cellSize = 16) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'U_SHAPE_DOWN':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `<path d="M0,0 L${cellSize},0 L${cellSize},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${width},0 L${width},${height} L0,${height} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'U_SHAPE_UP':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `<path d="M0,${cellSize} L0,${height} L${width},${height} L${width},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${cellSize},0 L${cellSize},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'U_SHAPE_LEFT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // NEW SWAPPED PATH
                    svgContent = `<path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${cellSize * 2} L${width},${cellSize * 2} L${width},${height} L0,${height} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'U_SHAPE_RIGHT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // NEW SWAPPED PATH
                    svgContent = `<path d="M${width},0 L0,0 L0,${height} L${width},${height} L${width},${cellSize * 2} L${cellSize},${cellSize * 2} L${cellSize},${cellSize} L${width},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'T_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `<path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${height} L0,${height} L0,${cellSize} L${cellSize},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'L_SHAPE':
                    width = cellSize * 2;
                    height = cellSize * 2;
                    svgContent = `<path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${height} L0,${height} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'CROSS_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 3;
                    svgContent = `<path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${cellSize * 2} L${cellSize * 2},${cellSize * 2} L${cellSize * 2},${height} L${cellSize},${height} L${cellSize},${cellSize * 2} L0,${cellSize * 2} L0,${cellSize} L${cellSize},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'RECT_3X1':
                    width = cellSize * 3;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                case 'RECT_1X2':
                    width = cellSize;
                    height = cellSize * 2;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                case 'RECT_1X3':
                    width = cellSize;
                    height = cellSize * 3;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                case 'RECT_2X1':
                    width = cellSize * 2;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                case 'SQUARE_2X2':
                    width = cellSize * 2;
                    height = cellSize * 2;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function createTestMinimap(rooms, title) {
            const cellSize = 18;
            const gridUnit = cellSize * 1.8;
            
            // Find bounds
            let minX = Math.min(...rooms.map(r => r.coords.x));
            let maxX = Math.max(...rooms.map(r => r.coords.x));
            let minY = Math.min(...rooms.map(r => r.coords.y));
            let maxY = Math.max(...rooms.map(r => r.coords.y));
            
            const mapWidth = (maxX - minX + 1) * gridUnit + cellSize * 6;
            const mapHeight = (maxY - minY + 1) * gridUnit + cellSize * 6;
            
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)';
            svg.style.background = '#333';
            
            // Add grid lines for reference
            for (let x = 0; x <= maxX - minX; x++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridX = x * gridUnit + cellSize * 2;
                line.setAttribute('x1', gridX);
                line.setAttribute('y1', 0);
                line.setAttribute('x2', gridX);
                line.setAttribute('y2', mapHeight);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.5');
                svg.appendChild(line);
            }
            
            for (let y = 0; y <= maxY - minY; y++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridY = y * gridUnit + cellSize * 2;
                line.setAttribute('x1', 0);
                line.setAttribute('y1', gridY);
                line.setAttribute('x2', mapWidth);
                line.setAttribute('y2', gridY);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.5');
                svg.appendChild(line);
            }
            
            // Add rooms
            let hasGaps = false;
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, cellSize);
                
                const baseX = (room.coords.x - minX) * gridUnit + cellSize * 2;
                const baseY = (maxY - room.coords.y) * gridUnit + cellSize * 2;
                
                const { gridX, gridY } = calculatePosition(shapeKey, baseX, baseY, cellSize);
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                let roomColor = room.type === 'Start' ? '#4CAF50' : '#8a8a8a';
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                // Add connection point marker
                const connectionPoint = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                connectionPoint.setAttribute('cx', width/2);
                connectionPoint.setAttribute('cy', height/2);
                connectionPoint.setAttribute('r', '2');
                connectionPoint.setAttribute('fill', '#ffff00');
                connectionPoint.setAttribute('stroke', '#000');
                connectionPoint.setAttribute('stroke-width', '1');
                roomGroup.appendChild(connectionPoint);
                
                svg.appendChild(roomGroup);
            });
            
            return { svg, hasGaps };
        }
        
        function testBasicShapes() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🧪 Basic Shape Connection Tests</h2>';
            
            // Test 1: Square to U_SHAPE_UP
            const test1Rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 1, y: 0 } }
            ];
            
            const container1 = document.createElement('div');
            container1.className = 'minimap-container';
            const { svg: svg1, hasGaps: gaps1 } = createTestMinimap(test1Rooms, 'Square → U_UP');
            container1.innerHTML = `<h4>Square → U_UP ${gaps1 ? '<span class="gap-indicator">❌ GAP</span>' : '<span class="good-connection">✅ OK</span>'}</h4>`;
            container1.appendChild(svg1);
            results.appendChild(container1);
            
            // Test 2: U_SHAPE_LEFT to Square
            const test2Rooms = [
                { id: 0, type: 'Start', shapeKey: 'U_SHAPE_LEFT', coords: { x: 0, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 } }
            ];
            
            const container2 = document.createElement('div');
            container2.className = 'minimap-container';
            const { svg: svg2, hasGaps: gaps2 } = createTestMinimap(test2Rooms, 'U_LEFT → Square');
            container2.innerHTML = `<h4>U_LEFT → Square ${gaps2 ? '<span class="gap-indicator">❌ GAP</span>' : '<span class="good-connection">✅ OK</span>'}</h4>`;
            container2.appendChild(svg2);
            results.appendChild(container2);
            
            // Test 3: T_SHAPE connections
            const test3Rooms = [
                { id: 0, type: 'Start', shapeKey: 'T_SHAPE', coords: { x: 1, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 } },
                { id: 2, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 1 } },
                { id: 3, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 1 } }
            ];
            
            const container3 = document.createElement('div');
            container3.className = 'minimap-container';
            const { svg: svg3, hasGaps: gaps3 } = createTestMinimap(test3Rooms, 'T_SHAPE Hub');
            container3.innerHTML = `<h4>T_SHAPE Hub ${gaps3 ? '<span class="gap-indicator">❌ GAP</span>' : '<span class="good-connection">✅ OK</span>'}</h4>`;
            container3.appendChild(svg3);
            results.appendChild(container3);
        }
        
        function testComplexShapes() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🔧 Complex Shape Connection Tests</h2>';
            
            // Test all U-shapes in cross pattern
            const crossRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 2 } },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 2, y: 1 } },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 3, y: 2 } },
                { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 2, y: 3 } },
                { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 1, y: 2 } }
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            const { svg, hasGaps } = createTestMinimap(crossRooms, 'All U-Shapes Cross');
            container.innerHTML = `<h4>All U-Shapes Cross ${hasGaps ? '<span class="gap-indicator">❌ GAPS DETECTED</span>' : '<span class="good-connection">✅ PERFECT</span>'}</h4>`;
            container.appendChild(svg);
            results.appendChild(container);
        }
        
        function testRectangularShapes() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>📏 Rectangular Shape Connection Tests</h2>';
            
            // Test rectangular shapes
            const rectRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'RECT_3X1', coords: { x: 1, y: 0 } },
                { id: 2, type: 'Normal', shapeKey: 'RECT_1X2', coords: { x: 2, y: 1 } },
                { id: 3, type: 'Normal', shapeKey: 'RECT_1X3', coords: { x: 0, y: 1 } },
                { id: 4, type: 'Normal', shapeKey: 'SQUARE_2X2', coords: { x: 1, y: 2 } }
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            const { svg, hasGaps } = createTestMinimap(rectRooms, 'Rectangular Shapes');
            container.innerHTML = `<h4>Rectangular Shapes ${hasGaps ? '<span class="gap-indicator">❌ GAPS DETECTED</span>' : '<span class="good-connection">✅ PERFECT</span>'}</h4>`;
            container.appendChild(svg);
            results.appendChild(container);
        }
        
        function testAllCombinations() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🌟 Comprehensive Connection Test</h2>';
            
            // Problematic combinations found in real gameplay
            const problematicRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 2 } },
                { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 1, y: 2 } },
                { id: 2, type: 'Normal', shapeKey: 'T_SHAPE', coords: { x: 2, y: 1 } },
                { id: 3, type: 'Normal', shapeKey: 'CROSS_SHAPE', coords: { x: 3, y: 2 } },
                { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 2, y: 3 } },
                { id: 5, type: 'Normal', shapeKey: 'RECT_2X1', coords: { x: 4, y: 2 } }
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            const { svg, hasGaps } = createTestMinimap(problematicRooms, 'Complex Layout');
            container.innerHTML = `<h4>Complex Mixed Layout ${hasGaps ? '<span class="gap-indicator">❌ GAPS FOUND</span>' : '<span class="good-connection">✅ ALL CONNECTED</span>'}</h4>`;
            container.appendChild(svg);
            results.appendChild(container);
            
            // Analysis summary
            const summary = document.createElement('div');
            summary.className = 'test-section';
            summary.innerHTML = `
                <h3>🔍 Gap Analysis Summary</h3>
                <p><strong>Most Likely Problem Areas:</strong></p>
                <ul>
                    <li><strong>L_SHAPE:</strong> Corner positioning may not align with adjacent room edges</li>
                    <li><strong>T_SHAPE:</strong> Multiple connection points need precise alignment</li>
                    <li><strong>CROSS_SHAPE:</strong> Four connection points - high chance of misalignment</li>
                    <li><strong>RECT_2X1:</strong> May need positioning adjustment like other rectangles</li>
                </ul>
                <p><strong>Need to check:</strong> Are the baseX/baseY calculations accounting for each shape's actual connection points?</p>
            `;
            results.appendChild(summary);
        }
        
        // Start with basic shapes test
        testBasicShapes();
    </script>
</body>
</html>