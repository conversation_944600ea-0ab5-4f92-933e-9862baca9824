// Debug script to check room type after createEvent
window.debugRoomType = function() {
    const dungeonHandler = window.dungeonHandler || window.sceneManager?.currentHandler;
    if (\!dungeonHandler) {
        console.error("No dungeon handler found");
        return;
    }
    
    const currentRoomId = dungeonHandler.currentRoomId;
    const roomData = dungeonHandler.floorLayout?.get(currentRoomId);
    
    if (\!roomData) {
        console.error(`No room data for current room ${currentRoomId}`);
        return;
    }
    
    console.log("=== ROOM TYPE DEBUG ===");
    console.log("Current room ID:", currentRoomId);
    console.log("Room type:", roomData.type);
    console.log("Room type === 'EVENT':", roomData.type === 'EVENT');
    console.log("Room type === 'Event':", roomData.type === 'Event');
    console.log("Has eventRoomData:", \!\!roomData.eventRoomData);
    console.log("Event room name:", roomData.eventRoomData?.name);
    
    // Check if event room manager has active handler
    if (dungeonHandler.eventRoomManager) {
        console.log("\n=== EVENT ROOM MANAGER STATE ===");
        console.log("Has active handler:", \!\!dungeonHandler.eventRoomManager.activeRoomHandler);
        console.log("Current event room ID:", dungeonHandler.eventRoomManager.currentEventRoomId);
    }
};

// Auto-run
debugRoomType();
