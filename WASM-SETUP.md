# Setting up llama.cpp WebAssembly

## Option 1: Compile from Source (Recommended)

### Prerequisites
- Emscripten SDK (emsdk)
- Git
- Make/CMake

### Steps

1. **Install Emscripten**
   ```bash
   # Download and install emsdk
   git clone https://github.com/emscripten-core/emsdk.git
   cd emsdk
   ./emsdk install latest
   ./emsdk activate latest
   source ./emsdk_env.sh
   ```

2. **Clone llama.cpp**
   ```bash
   git clone https://github.com/ggerganov/llama.cpp.git
   cd llama.cpp
   ```

3. **Build for WebAssembly**
   ```bash
   # Build with Emscripten
   emmake make -j4
   
   # Or with CMake
   mkdir build-wasm
   cd build-wasm
   emcmake cmake .. -DLLAMA_WASM=ON
   emmake make -j4
   ```

4. **Copy WASM files**
   ```bash
   # Copy the generated files to your project
   cp llama.js /path/to/your/project/src/ai/
   cp llama.wasm /path/to/your/project/src/ai/
   ```

## Option 2: Use Pre-built Binaries

Some projects provide pre-built WASM binaries:

### llama.cpp-python
```bash
pip install llama-cpp-python[server]
# Look for WASM files in the installation
```

### Web-based Projects
Check these projects for WASM builds:
- https://github.com/ngxson/wllama
- https://github.com/mlc-ai/web-llm
- https://github.com/Mozilla-Ocho/llamafile

## Option 3: Alternative WASM Implementations

### 1. wllama (Recommended for beginners)
```bash
npm install @wllama/wllama
```

### 2. web-llm
```bash
npm install @mlc-ai/web-llm
```

## Integration Steps

Once you have the WASM files:

1. **Place files in your project:**
   ```
   src/ai/
   ├── llama.js
   ├── llama.wasm
   ├── LlamaWASM.js
   └── LocalLlamaService.js
   ```

2. **Update LlamaWASM.js:**
   ```javascript
   // Replace the TODO section in initialize()
   const wasmModule = await import('./llama.js');
   this.module = await wasmModule.default({
       locateFile: (path) => {
           if (path.endsWith('.wasm')) {
               return './src/ai/llama.wasm';
           }
           return path;
       }
   });
   ```

3. **Update model loading:**
   ```javascript
   // In loadModel() method
   const modelParams = this.module.llama_model_default_params();
   this.model = this.module.llama_load_model_from_file(modelPath, modelParams);
   ```

## Quick Start with wllama

If you want to get started quickly:

```bash
cd /Users/<USER>/Documents/tarot11
npm init -y
npm install @wllama/wllama
```

Then update your code to use wllama instead of raw llama.cpp WASM.

## Troubleshooting

- **CORS Issues**: Make sure your server serves WASM files with proper headers
- **Memory Issues**: Increase WASM memory limits if needed
- **File Size**: Q2_K models are smaller but may need specific compilation flags

## Next Steps

1. Choose your approach (compile from source vs pre-built)
2. Test with your TinyLLaMA model
3. Integrate with the character system
4. Optimize for your specific use case