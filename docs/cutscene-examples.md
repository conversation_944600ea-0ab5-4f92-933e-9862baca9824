# Cutscene System Examples

The cutscene system allows you to create cinematic sequences where the player character moves along predefined paths. The system supports both first-person and third-person camera modes, and you can switch between them during a cutscene.

## Basic Usage

### Third-Person Cutscene
```javascript
// Get the cutscene system from dungeonHandler
const cutsceneSystem = this.dungeonHandler.cutsceneSystem;

// Define a path using local room coordinates
const path = [
    { x: 0, y: 0.3, z: 25 },     // Starting position
    { x: 0, y: 0.3, z: 15 },     // Waypoint
    { x: 0.27, y: 0.3, z: 8.2 }  // Final position
];

// Create cutscene configuration
const config = {
    mode: 'third-person',
    path: path,
    speed: 3.0, // Units per second
    onComplete: () => {
        console.log('Cutscene finished!');
    }
};

// Play the cutscene
cutsceneSystem.play(config);
```

### First-Person Cutscene with Camera Rotation
```javascript
// Define path and camera rotations
const path = [
    { x: 0, y: 0.3, z: 0 },
    { x: 10, y: 0.3, z: 0 },
    { x: 10, y: 0.3, z: 10 }
];

// Camera rotations for each waypoint (x: up/down, y: left/right in radians)
const cameraRotations = [
    { x: 0, y: 0 },                    // Look straight ahead
    { x: 0, y: Math.PI / 2 },          // Turn right 90 degrees
    { x: -Math.PI / 6, y: Math.PI / 2 } // Look up slightly
];

const config = {
    mode: 'first-person',
    path: path,
    cameraRotations: cameraRotations,
    speed: 2.0,
    onComplete: () => {
        console.log('First-person cutscene complete');
    }
};

cutsceneSystem.play(config);
```

## Integration Examples

### Boss Room Entrance
```javascript
// In CatacombsBossRoom.js
playEntranceCutscene() {
    const cutsceneSystem = this.dungeonHandler.cutsceneSystem;
    
    const cutscenePath = [
        { x: 0, y: 0.3, z: 25 },    // Near the door
        { x: 0, y: 0.3, z: 15 },    // Moving toward center
        { x: 0.27, y: 0.3, z: 8.2 } // Final position
    ];
    
    const cutsceneConfig = {
        mode: 'third-person',
        path: cutscenePath,
        speed: 3.0,
        onComplete: () => {
            // Spawn boss after cutscene
            this.spawnBoss();
        }
    };
    
    cutsceneSystem.play(cutsceneConfig);
}
```

### Event Room Discovery
```javascript
// In an event room handler
playDiscoveryCutscene() {
    const cutsceneSystem = this.dungeonHandler.cutsceneSystem;
    
    // Walk to the center and look around
    const path = [
        { x: 0, y: 0.3, z: 20 },
        { x: 0, y: 0.3, z: 0 },
        { x: 5, y: 0.3, z: 0 },
        { x: 0, y: 0.3, z: 0 }
    ];
    
    // Look around the room
    const cameraRotations = [
        { y: 0 },                  // Forward
        { y: 0 },                  // Forward
        { y: Math.PI / 2 },        // Look right
        { y: -Math.PI / 2 }        // Look left
    ];
    
    const config = {
        mode: 'first-person',
        path: path,
        cameraRotations: cameraRotations,
        speed: 2.0,
        onComplete: () => {
            // Show dialogue or start event
            this.startRoomEvent();
        }
    };
    
    cutsceneSystem.play(config);
}
```

### Dungeon Room Transitions
```javascript
// Special room entrance
if (roomData.hasSpecialEntrance) {
    const cutsceneSystem = this.cutsceneSystem;
    
    // Create dramatic entrance
    const config = {
        mode: 'third-person',
        path: [
            { x: 0, y: 0.3, z: -20 },
            { x: 0, y: 0.3, z: 0 }
        ],
        speed: 1.5, // Slow walk
        onComplete: () => {
            // Enable normal controls
            console.log('Dramatic entrance complete');
        }
    };
    
    cutsceneSystem.play(config);
}
```

## Stopping Cutscenes

You can stop a cutscene at any time:
```javascript
// Stop the current cutscene
cutsceneSystem.stop();
```

## Camera Mode Switching

You can switch between first-person and third-person during a cutscene:

```javascript
const config = {
    mode: 'third-person', // Starting mode
    path: [
        { x: 0, y: 0.3, z: 20 },
        { x: 0, y: 0.3, z: 10 },
        { x: 0, y: 0.3, z: 0 }
    ],
    cameraModes: [
        'third-person',  // First waypoint
        'first-person',  // Switch at second waypoint
        'third-person'   // Back to third at final waypoint
    ],
    cameraRotations: [
        null,                    // No rotation in third-person
        { x: 0, y: Math.PI / 2 }, // Look right in first-person
        null                     // No rotation in third-person
    ]
};
```

## Using Cutscene Definitions

Cutscenes can be stored as separate files and loaded on demand:

### Creating a Cutscene Definition

```javascript
// src/cutscenes/definitions/myCustomCutscene.js
import { BaseCutscene } from '../BaseCutscene.js';

export class MyCustomCutscene extends BaseCutscene {
    constructor() {
        super();
        this.id = 'my_custom_cutscene';
        this.name = 'My Custom Cutscene';
    }
    
    getConfig(context) {
        return {
            mode: 'third-person',
            path: [
                { x: 0, y: 0.3, z: 0 },
                { x: 10, y: 0.3, z: 10 }
            ],
            speed: 3.0,
            onComplete: () => {
                console.log('Cutscene finished!');
            }
        };
    }
}
```

### Registering and Playing

```javascript
// Register the cutscene
import { cutsceneManager } from './cutscenes/CutsceneManager.js';
cutsceneManager.register('my_custom_cutscene', './cutscenes/definitions/myCustomCutscene.js');

// Play it later
cutsceneManager.play('my_custom_cutscene', dungeonHandler, { 
    additionalData: 'any context you need' 
});
```

### Using in Rooms

```javascript
// In a boss room or event room
playEntranceCutscene() {
    const context = {
        room: this,
        onCutsceneComplete: () => {
            this.spawnBoss();
        }
    };
    
    cutsceneManager.play('catacombs_boss_entrance', this.dungeonHandler, context);
}
```

## Built-in Cutscenes

### Catacombs Boss Entrance
- ID: `catacombs_boss_entrance`
- Player walks to center, examines monoliths in first-person, returns to third-person

### Mysterious Altar Discovery
- ID: `mysterious_altar_discovery`
- Player approaches altar, examines it closely, walks around it

### First-Person Inspection
- ID: `first_person_inspection`
- Simple inspection cutscene, player stays in place but camera looks around

## Quick Inspection Helper

For simple first-person inspections:

```javascript
import { createInspectionCutscene } from './cutscenes/definitions/firstPersonInspection.js';

// Look up at something
const inspection = createInspectionCutscene(
    { x: -Math.PI / 4, y: 0 }, // Look up 45 degrees
    {
        duration: 2.0,
        message: 'You notice something on the ceiling...',
        onComplete: () => console.log('Inspection complete')
    }
);

inspection.play(dungeonHandler);
```

## Notes

- Player controls are automatically locked during cutscenes
- Mobile controls are also disabled during cutscenes
- The camera is automatically restored after cutscenes
- Cutscenes use local room coordinates (same as object placement)
- Movement is smooth and interpolated
- You can chain multiple cutscenes using the onComplete callback
- Camera mode switches happen at waypoints
- Cutscene definitions are loaded on-demand for better performance