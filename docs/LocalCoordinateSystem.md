# Local Coordinate System Documentation

## Overview

The Local Coordinate System provides a consistent, shape-aware coordinate reference for room debugging and precise object placement in Soulpath. Each room shape has its own local coordinate system that remains consistent regardless of the room's position in the dungeon grid.

## Features

- **Consistent Coordinates**: Local coordinates are the same for each room shape type across different dungeon generations
- **Shape-Aware**: Validates coordinates against room geometry (L-shapes, T-shapes, etc.)
- **Real-Time Debug Display**: Shows current coordinates when ESP is enabled (TAB key)
- **Console Logging**: Provides placement commands for precise object positioning
- **Validation**: Checks if coordinates are within valid room areas

## Usage

### Debug Display

1. **Press TAB** to toggle ESP mode and see real-time local coordinates
2. **Press TAB again** to log detailed coordinate information to console
3. The debug display shows:
   - Current local coordinates (x, y, z)
   - Room ID and shape type
   - Coordinate validation status
   - Generated placement command

### Console Commands

When you press TAB, the console will show:

```
=== ROOM DEBUG INFO (TAB) ===
Room ID: 0
Room Shape: SQUARE_1X1 (Square 1x1)
Local Coordinates: (2.456, 0.500, -3.123)
Position Status: VALID
Placement Command: place vase in room 0 at local coordinates (2.456, 0.500, -3.123)
=============================
```

### Coordinate System

- **Origin (0,0,0)**: Center of each room shape at ground level
- **X-axis**: Horizontal (negative = west, positive = east)
- **Y-axis**: Vertical (0 = ground level, positive = up)
- **Z-axis**: Horizontal (negative = north, positive = south)
- **Units**: World units (same as Three.js scene units)

## Room Shape Definitions

### SQUARE_1X1
- **Bounds**: -7 to +7 on both X and Z axes
- **Center**: (0, 0, 0)
- **Description**: Standard square room

### L_SHAPE
- **Bounds**: Complex shape with two rectangular areas
- **Center**: (-7, 0, 3.5) - offset to center of L
- **Valid Areas**: 
  - Top-left vertical: (-14 to 0, -14 to 0)
  - Bottom horizontal: (-14 to +14, 0 to +14)

### T_SHAPE
- **Bounds**: Horizontal bar + vertical stem
- **Center**: (0, 0, 0)
- **Valid Areas**:
  - Top horizontal bar: (-21 to +21, -14 to 0)
  - Bottom vertical stem: (-7 to +7, 0 to +14)

### CROSS_SHAPE
- **Bounds**: Intersecting horizontal and vertical bars
- **Center**: (0, 0, 0)
- **Valid Areas**:
  - Horizontal bar: (-21 to +21, -7 to +7)
  - Vertical bar: (-7 to +7, -21 to +21)

## Example Placements

### Square Room Examples
```javascript
// Center of room
{ x: 0, y: 0.1, z: 0 }

// Northwest corner
{ x: -5, y: 0.1, z: -5 }

// Southeast corner  
{ x: 5, y: 0.1, z: 5 }
```

### L-Shape Examples
```javascript
// Top-left corner of L
{ x: -10, y: 0.1, z: -10 }

// Center of L shape
{ x: -7, y: 0.1, z: 7 }

// Bottom-right of horizontal part
{ x: 10, y: 0.1, z: 10 }
```

## API Reference

### LocalCoordinateSystem Class

#### Methods

- `setCurrentRoom(roomData)` - Set the current room for coordinate calculations
- `worldToLocal(worldPosition)` - Convert world coordinates to local coordinates
- `localToWorld(localCoords)` - Convert local coordinates to world coordinates
- `updateDebugDisplay(playerPosition, isVisible)` - Update the debug overlay
- `validateLocalCoordinate(localCoords, shapeKey)` - Validate coordinates for a room shape
- `generatePlacementCommand(localCoords, roomId, objectType)` - Generate placement command string

#### Static Methods

- `createPlacementExample(localCoords, roomShape, objectType)` - Test coordinate placement
- `getExamplePlacements()` - Get example coordinates for each room shape

### Usage Example

```javascript
import { LocalCoordinateSystem } from '../systems/LocalCoordinateSystem.js';

// Create coordinate system
const coordSystem = new LocalCoordinateSystem();

// Set current room
coordSystem.setCurrentRoom({
    id: 0,
    shapeKey: 'SQUARE_1X1',
    coords: { x: 0, y: 0 }
});

// Convert player position to local coordinates
const localCoords = coordSystem.worldToLocal(player.position);
console.log(`Local position: (${localCoords.local.x}, ${localCoords.local.y}, ${localCoords.local.z})`);

// Generate placement command
const command = coordSystem.generatePlacementCommand(localCoords.local, 0, 'treasure_chest');
console.log(command); // "place treasure_chest in room 0 at local coordinates (x, y, z)"
```

## Integration

The Local Coordinate System is automatically integrated into:

- **DungeonHandler**: Updates current room and manages debug display
- **PlayerController**: TAB key triggers coordinate logging
- **ESP System**: Shows real-time coordinates when enabled

## Benefits

1. **Consistent Placement**: Objects can be placed at the same relative position in any room of the same shape
2. **Debug-Friendly**: Easy to identify and reproduce specific positions
3. **Shape-Aware**: Prevents placement in invalid areas of complex room shapes
4. **Persistent Reference**: Coordinates work across different dungeon generations
5. **Developer Tools**: Built-in validation and command generation for rapid development

## Notes

- Coordinates are independent of dungeon grid position
- Y=0 represents the ground level (may be curved)
- Invalid coordinates (outside room bounds) are clearly marked
- The system accounts for all room shapes including complex ones like U-shapes and crosses
- Debug display only appears when ESP is enabled to avoid UI clutter
