<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimap Gap Fix Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        #minimap-grid {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: transparent;
            border: none;
            border-radius: 0;
            padding: 12px;
            opacity: 0.95;
            z-index: 1000;
            max-width: 400px;
            max-height: 400px;
        }
        
        #minimap-grid svg {
            display: block;
            max-width: 100%;
            max-height: 100%;
        }
        
        .test-controls {
            margin-top: 50px;
        }
        
        button {
            margin: 5px;
            padding: 10px;
            background: #444;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
        }
        
        button:hover {
            background: #555;
        }
        
        .test-case {
            margin: 20px 0;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        
        .before-after {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        
        .comparison {
            flex: 1;
            text-align: center;
        }
        
        .comparison h4 {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>Minimap Gap Fix Test</h1>
    
    <div class="test-case">
        <h3>🔧 Fixed Issue: Black gaps between connected rectangular rooms</h3>
        <p><strong>Problem:</strong> RECT_3X1 and other rectangular rooms were not properly positioned, causing visual disconnects</p>
        <p><strong>Solution:</strong> Added proper positioning logic for all rectangular room types</p>
    </div>
    
    <div class="test-controls">
        <button onclick="testProblemScenario()">Test Problem Scenario</button>
        <button onclick="testAllRectangular()">Test All Rectangular Shapes</button>
        <button onclick="testConnectedPath()">Test Connected Path</button>
    </div>
    
    <div id="minimap-grid"></div>
    
    <script>
        // Helper function to create room shape SVG (same as DungeonHandler.js)
        function createRoomShapeSVG(shapeKey, room, cellSize = 14) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'RECT_2X1':
                    width = cellSize * 2;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'RECT_3X1':
                    width = cellSize * 3;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'RECT_1X2':
                    width = cellSize;
                    height = cellSize * 2;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'RECT_1X3':
                    width = cellSize;
                    height = cellSize * 3;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function createMinimap(rooms, title) {
            const minimapElement = document.getElementById('minimap-grid');
            minimapElement.innerHTML = `<h3>${title}</h3>`;
            
            // Find bounds
            let minX = rooms[0].coords.x, maxX = rooms[0].coords.x;
            let minY = rooms[0].coords.y, maxY = rooms[0].coords.y;
            
            rooms.forEach(room => {
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            });
            
            // Calculate adaptive scaling
            const dungeonWidth = maxX - minX + 1;
            const dungeonHeight = maxY - minY + 1;
            
            const maxMinimapWidth = 300;
            const maxMinimapHeight = 200;
            
            const baseCellSize = 16;
            const connectionSpacing = 1.8;
            const scaleX = Math.min(maxMinimapWidth / (dungeonWidth * baseCellSize * connectionSpacing * 2), 1.0);
            const scaleY = Math.min(maxMinimapHeight / (dungeonHeight * baseCellSize * connectionSpacing * 2), 1.0);
            const scale = Math.min(scaleX, scaleY);
            
            const cellSize = Math.max(baseCellSize * scale, 6);
            const gridUnit = cellSize * connectionSpacing;
            const mapWidth = dungeonWidth * gridUnit + cellSize * 6;
            const mapHeight = dungeonHeight * gridUnit + cellSize * 6;
            
            console.log(`[Test] Cell size: ${cellSize.toFixed(1)}px, Grid unit: ${gridUnit.toFixed(1)}px`);
            
            // Create SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)';
            svg.style.border = '1px solid #666';
            svg.style.background = '#444';
            
            // Add rooms with FIXED positioning
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, room, cellSize);
                
                // FIXED positioning logic (matches DungeonHandler.js fix)
                const gridUnitX = cellSize * 1.8;
                const gridUnitY = cellSize * 1.8;
                const baseX = (room.coords.x - minX) * gridUnitX + cellSize * 2; 
                const baseY = (maxY - room.coords.y) * gridUnitY + cellSize * 2;
                
                let gridX = baseX;
                let gridY = baseY;
                
                // FIXED: Proper positioning for all rectangular shapes
                if (shapeKey === 'SQUARE_1X1') {
                    gridX = baseX;
                    gridY = baseY;
                } else if (shapeKey === 'RECT_2X1') {
                    gridX = baseX;
                    gridY = baseY - cellSize * 0.5;
                } else if (shapeKey === 'RECT_3X1') {
                    // FIXED: Center the 3-wide rectangle
                    gridX = baseX - cellSize;
                    gridY = baseY - cellSize * 0.5;
                } else if (shapeKey === 'RECT_1X2') {
                    // FIXED: Center horizontally, align top with grid
                    gridX = baseX - cellSize * 0.5;
                    gridY = baseY;
                } else if (shapeKey === 'RECT_1X3') {
                    // FIXED: Center horizontally and vertically
                    gridX = baseX - cellSize * 0.5;
                    gridY = baseY - cellSize;
                }
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                // Color coding
                let roomColor = '#8a8a8a';
                if (room.id === 0) roomColor = '#ffffff';
                else if (room.type === 'Boss') roomColor = '#ff3333';
                else if (room.type === 'Event') roomColor = '#3399ff';
                else if (room.type === 'Start') roomColor = '#33ff33';
                
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                // Add glow
                if (room.id === 0) {
                    roomGroup.style.filter = 'drop-shadow(0 0 4px #ffffff)';
                }
                
                // Add tooltip
                const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
                title.textContent = `Room ${room.id} (${room.type}) - ${shapeKey}`;
                roomGroup.appendChild(title);
                
                svg.appendChild(roomGroup);
            });
            
            minimapElement.appendChild(svg);
        }
        
        function testProblemScenario() {
            // The exact scenario user reported: RECT_3X1 → corridor → SQUARE_1X1
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'RECT_3X1', coords: { x: 0, y: 1 }, visited: true },  // 3x1 rectangle
                { id: 2, type: 'Normal', shapeKey: 'RECT_1X2', coords: { x: 0, y: 2 }, visited: true },  // Short corridor
                { id: 3, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 3 }, visited: true }  // Square
            ];
            
            createMinimap(rooms, 'Problem Scenario: RECT_3X1 → Corridor → Square (FIXED)');
            console.log('Generated problem scenario test - gaps should be ELIMINATED');
        }
        
        function testAllRectangular() {
            // Test all rectangular room types
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'RECT_2X1', coords: { x: 1, y: 0 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'RECT_3X1', coords: { x: 2, y: 0 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'RECT_1X2', coords: { x: 3, y: 0 }, visited: true },
                { id: 4, type: 'Normal', shapeKey: 'RECT_1X3', coords: { x: 3, y: 1 }, visited: true },
                { id: 5, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 2 }, visited: true }
            ];
            
            createMinimap(rooms, 'All Rectangular Room Types (FIXED Positioning)');
            console.log('Generated all rectangular shapes test');
        }
        
        function testConnectedPath() {
            // Test a connected path with various room types
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'RECT_3X1', coords: { x: 1, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'RECT_1X2', coords: { x: 1, y: 2 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 3 }, visited: true },
                { id: 4, type: 'Event', shapeKey: 'RECT_2X1', coords: { x: 2, y: 3 }, visited: true },
                { id: 5, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 3 }, visited: true }
            ];
            
            createMinimap(rooms, 'Connected Path Test (No Gaps)');
            console.log('Generated connected path test');
        }
        
        // Test problem scenario by default
        testProblemScenario();
    </script>
</body>
</html>