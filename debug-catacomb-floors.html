<!DOCTYPE html>
<html>
<head>
    <title>Debug Catacomb Floors</title>
    <style>
        body { margin: 0; overflow: hidden; }
        #log { position: absolute; top: 10px; left: 10px; color: lime; font-family: monospace; background: rgba(0,0,0,0.8); padding: 10px; max-height: 90vh; overflow-y: auto; }
    </style>
</head>
<body>
    <div id="log"></div>
    <script type="importmap">
        {
            "imports": {
                "three": "./lib/three.module.js",
                "three/examples/jsm/": "./lib/",
                "three/addons/": "./lib/"
            }
        }
    </script>
    <script type="module">
        // Capture console logs
        const logDiv = document.getElementById('log');
        const originalLog = console.log;
        console.log = function(...args) {
            const msg = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : arg).join(' ');
            if (msg.includes('[FloorScan]') || msg.includes('[addFloorSegment]') || msg.includes('[Room') && msg.includes('floor')) {
                logDiv.innerHTML += msg + '<br>';
            }
            originalLog.apply(console, args);
        };

        import('./main.js').then(() => {
            console.log('Game started - check logs for floor scanning issues');
        });
    </script>
</body>
</html>