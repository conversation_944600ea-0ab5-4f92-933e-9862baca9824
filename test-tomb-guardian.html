<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tomb Guardian Enemy Test</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
            font-family: Arial, sans-serif;
        }
        
        #debug-info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #0f0;
            font-size: 12px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            max-width: 400px;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="debug-info">
        <h3>Tomb Guardian Test</h3>
        <div id="status">Loading...</div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "./lib/three.module.js",
                "three/examples/jsm/postprocessing/EffectComposer": "./lib/postprocessing/EffectComposer.js",
                "three/examples/jsm/postprocessing/RenderPass": "./lib/postprocessing/RenderPass.js",
                "three/examples/jsm/postprocessing/ShaderPass": "./lib/postprocessing/ShaderPass.js",
                "three/examples/jsm/postprocessing/OutlinePass": "./lib/postprocessing/OutlinePass.js",
                "three/examples/jsm/shaders/CopyShader": "./lib/shaders/CopyShader.js",
                "three/examples/jsm/shaders/FXAAShader": "./lib/shaders/FXAAShader.js",
                "three/examples/jsm/controls/OrbitControls": "./lib/controls/OrbitControls.js",
                "three/addons/": "./lib/"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
        import { createTombGuardianEnemyModel } from './src/generators/prefabs/tombGuardianEnemy.js';
        import { getEnemyData } from './src/entities/EnemyTypes.js';
        
        let scene, camera, renderer, controls;
        let debugInfo = document.getElementById('status');
        
        function updateDebug(text) {
            debugInfo.innerHTML = text;
            console.log(text);
        }
        
        function init() {
            updateDebug('Initializing scene...');
            
            // Scene setup
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            scene.fog = new THREE.Fog(0x222222, 10, 50);
            
            // Camera setup
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 100);
            camera.position.set(10, 10, 10);
            
            // Renderer setup
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.body.appendChild(renderer.domElement);
            
            // Controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.camera.left = -10;
            directionalLight.shadow.camera.right = 10;
            directionalLight.shadow.camera.top = 10;
            directionalLight.shadow.camera.bottom = -10;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Ground plane
            const groundGeometry = new THREE.PlaneGeometry(50, 50);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.receiveShadow = true;
            scene.add(ground);
            
            // Grid helper
            const gridHelper = new THREE.GridHelper(50, 50, 0x444444, 0x222222);
            scene.add(gridHelper);
            
            // Test tomb guardian spawning
            testTombGuardian();
            
            // Start animation loop
            animate();
        }
        
        function testTombGuardian() {
            updateDebug('Testing tomb_guardian enemy...');
            
            try {
                // Get enemy data
                const enemyData = getEnemyData('tomb_guardian');
                if (!enemyData) {
                    updateDebug('ERROR: tomb_guardian not found in enemy data!');
                    return;
                }
                
                updateDebug(`Found tomb_guardian data:<br>
                    Health: ${enemyData.health}<br>
                    Speed: ${enemyData.baseSpeed}<br>
                    AI Type: ${enemyData.aiType}<br>
                    Has Shield Bash: ${enemyData.hasShieldBash}<br>
                    Model Prefab: ${enemyData.modelPrefab ? 'YES' : 'NO'}`);
                
                // Create the model
                if (!enemyData.modelPrefab) {
                    updateDebug('ERROR: No model prefab for tomb_guardian!');
                    return;
                }
                
                const model = enemyData.modelPrefab(2.0); // Default scale
                if (!model) {
                    updateDebug('ERROR: Failed to create tomb_guardian model!');
                    return;
                }
                
                updateDebug(`Created tomb_guardian model successfully!<br>
                    Children: ${model.children.length}<br>
                    Scale: ${model.scale.x}<br>
                    UserData: ${JSON.stringify(model.userData)}`);
                
                // Position the model
                model.position.set(0, 0, 0);
                
                // Add to scene
                scene.add(model);
                
                // Enable shadows
                model.traverse(child => {
                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                    }
                });
                
                updateDebug(`SUCCESS: Tomb Guardian spawned!<br>
                    Position: (${model.position.x}, ${model.position.y}, ${model.position.z})<br>
                    Visible: ${model.visible}<br>
                    In Scene: ${scene.children.includes(model)}`);
                
                // Test creating multiple guardians
                const positions = [
                    { x: -5, z: -5 },
                    { x: 5, z: -5 },
                    { x: -5, z: 5 },
                    { x: 5, z: 5 }
                ];
                
                positions.forEach((pos, index) => {
                    const guardian = enemyData.modelPrefab(1.5); // Smaller scale
                    guardian.position.set(pos.x, 0, pos.z);
                    guardian.traverse(child => {
                        if (child.isMesh) {
                            child.castShadow = true;
                            child.receiveShadow = true;
                        }
                    });
                    scene.add(guardian);
                });
                
                updateDebug(`SUCCESS: Spawned 5 Tomb Guardians total!<br>
                    Use mouse to rotate camera and inspect models.<br>
                    Check console for any errors.`);
                
            } catch (error) {
                updateDebug(`ERROR: ${error.message}<br>${error.stack}`);
                console.error(error);
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            controls.update();
            
            // Rotate the guardians slowly
            scene.traverse(child => {
                if (child.userData && child.userData.type === 'tombGuardian') {
                    child.rotation.y += 0.01;
                }
            });
            
            renderer.render(scene, camera);
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Initialize everything
        init();
    </script>
</body>
</html>