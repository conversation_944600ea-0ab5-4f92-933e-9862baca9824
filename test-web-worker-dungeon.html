<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Worker Dungeon Generation Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            background: #222;
        }
        .metrics {
            background: #111;
            padding: 10px;
            border-left: 3px solid #00ff00;
            margin: 10px 0;
        }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #444;
        }
        .output {
            background: #000;
            padding: 10px;
            border: 1px solid #333;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .performance-chart {
            background: #111;
            padding: 15px;
            border: 1px solid #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏰 Web Worker Dungeon Generation Test</h1>
        
        <div class="test-section">
            <h2>Performance Comparison</h2>
            <button onclick="runPerformanceTest()">Run Performance Test</button>
            <button onclick="runStressTest()">Run Stress Test (10 generations)</button>
            <button onclick="toggleWebWorker()">Toggle Web Worker: <span id="worker-status">ON</span></button>
            
            <div class="comparison">
                <div>
                    <h3>🔧 Web Worker Results</h3>
                    <div class="metrics" id="worker-metrics">
                        <div>Time: <span id="worker-time">-</span>ms</div>
                        <div>Rooms: <span id="worker-rooms">-</span></div>
                        <div>Status: <span id="worker-status-text">Ready</span></div>
                    </div>
                    <div class="output" id="worker-output"></div>
                </div>
                
                <div>
                    <h3>⚙️ Synchronous Results</h3>
                    <div class="metrics" id="sync-metrics">
                        <div>Time: <span id="sync-time">-</span>ms</div>
                        <div>Rooms: <span id="sync-rooms">-</span></div>
                        <div>Status: <span id="sync-status-text">Ready</span></div>
                    </div>
                    <div class="output" id="sync-output"></div>
                </div>
            </div>
            
            <div class="performance-chart" id="performance-chart">
                <h3>📊 Performance Metrics</h3>
                <div id="performance-data">Run tests to see performance comparison</div>
            </div>
        </div>

        <div class="test-section">
            <h2>Feature Compatibility Test</h2>
            <button onclick="testFeatureCompatibility()">Test Feature Compatibility</button>
            <div class="output" id="compatibility-output"></div>
        </div>

        <div class="test-section">
            <h2>Manual Testing</h2>
            <button onclick="generateSingleDungeon()">Generate Single Dungeon</button>
            <button onclick="showASCIIMap()">Show ASCII Map</button>
            <button onclick="testMinimapCompatibility()">Test Minimap Compatibility</button>
            <button onclick="testEntranceDirections()">Test Entrance Directions</button>
            <button onclick="getWorkerMetrics()">Get Worker Metrics</button>
            <div class="output" id="manual-output"></div>
        </div>
    </div>

    <script type="module">
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';

        let generator = new DungeonGenerator();
        let workerEnabled = true;

        window.toggleWebWorker = () => {
            workerEnabled = !workerEnabled;
            generator.useWebWorker = workerEnabled;
            document.getElementById('worker-status').textContent = workerEnabled ? 'ON' : 'OFF';
            log('manual-output', `Web worker ${workerEnabled ? 'enabled' : 'disabled'}`);
        };

        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function updateMetrics(prefix, time, rooms, status) {
            document.getElementById(`${prefix}-time`).textContent = time;
            document.getElementById(`${prefix}-rooms`).textContent = rooms;
            document.getElementById(`${prefix}-status-text`).textContent = status;
        }

        window.runPerformanceTest = async () => {
            log('worker-output', '🚀 Starting performance test...');
            log('sync-output', '🚀 Starting performance test...');

            try {
                // Test web worker version
                updateMetrics('worker', '-', '-', 'Generating...');
                const workerStart = performance.now();
                
                generator.useWebWorker = true;
                const workerLayout = await generator.generateLayout('catacombs');
                
                const workerTime = Math.round(performance.now() - workerStart);
                const workerRooms = workerLayout.size;
                
                updateMetrics('worker', workerTime, workerRooms, 'Complete');
                log('worker-output', `✅ Web worker generation: ${workerTime}ms, ${workerRooms} rooms`);

                // Test synchronous version
                updateMetrics('sync', '-', '-', 'Generating...');
                const syncStart = performance.now();
                
                generator.useWebWorker = false;
                const syncLayout = await generator.generateLayout('catacombs');
                
                const syncTime = Math.round(performance.now() - syncStart);
                const syncRooms = syncLayout.size;
                
                updateMetrics('sync', syncTime, syncRooms, 'Complete');
                log('sync-output', `✅ Synchronous generation: ${syncTime}ms, ${syncRooms} rooms`);

                // Update performance chart
                const speedup = syncTime > 0 ? ((syncTime - workerTime) / syncTime * 100).toFixed(1) : 0;
                document.getElementById('performance-data').innerHTML = `
                    <div>Web Worker: ${workerTime}ms (${workerRooms} rooms)</div>
                    <div>Synchronous: ${syncTime}ms (${syncRooms} rooms)</div>
                    <div><strong>Performance: ${speedup > 0 ? speedup + '% faster' : Math.abs(speedup) + '% slower'}</strong></div>
                    <div>Main thread blocking reduced by: ${workerTime < syncTime ? 'Yes' : 'No'}</div>
                `;

                // Restore original setting
                generator.useWebWorker = workerEnabled;

            } catch (error) {
                log('worker-output', `❌ Error: ${error.message}`);
                log('sync-output', `❌ Error: ${error.message}`);
                updateMetrics('worker', '-', '-', 'Error');
                updateMetrics('sync', '-', '-', 'Error');
            }
        };

        window.runStressTest = async () => {
            log('manual-output', '🔥 Starting stress test - 10 generations...');
            
            const results = { worker: [], sync: [] };
            
            for (let i = 1; i <= 10; i++) {
                log('manual-output', `  Test ${i}/10...`);
                
                try {
                    // Worker test
                    generator.useWebWorker = true;
                    const workerStart = performance.now();
                    await generator.generateLayout('catacombs');
                    results.worker.push(performance.now() - workerStart);
                    
                    // Sync test
                    generator.useWebWorker = false;
                    const syncStart = performance.now();
                    await generator.generateLayout('catacombs');
                    results.sync.push(performance.now() - syncStart);
                    
                } catch (error) {
                    log('manual-output', `  ❌ Test ${i} failed: ${error.message}`);
                }
            }
            
            // Calculate averages
            const avgWorker = results.worker.reduce((a, b) => a + b, 0) / results.worker.length;
            const avgSync = results.sync.reduce((a, b) => a + b, 0) / results.sync.length;
            
            log('manual-output', `📊 Stress test complete:`);
            log('manual-output', `  Worker average: ${avgWorker.toFixed(2)}ms`);
            log('manual-output', `  Sync average: ${avgSync.toFixed(2)}ms`);
            log('manual-output', `  Performance gain: ${((avgSync - avgWorker) / avgSync * 100).toFixed(1)}%`);
            
            // Restore setting
            generator.useWebWorker = workerEnabled;
        };

        window.testFeatureCompatibility = async () => {
            log('compatibility-output', '🔍 Testing feature compatibility...');
            
            try {
                // Test with worker
                generator.useWebWorker = true;
                const workerLayout = await generator.generateLayout('catacombs');
                
                // Test with sync
                generator.useWebWorker = false;
                const syncLayout = await generator.generateLayout('catacombs');
                
                // Extract room data for testing
                const workerRooms = Array.from(workerLayout.values());
                const syncRooms = Array.from(syncLayout.values());
                
                // Compare results
                const checks = [
                    // Basic room structure
                    { name: 'Room count similar', pass: Math.abs(workerLayout.size - syncLayout.size) <= 3 },
                    { name: 'Start room exists (worker)', pass: workerRooms.some(r => r.type === 'Start') },
                    { name: 'Start room exists (sync)', pass: syncRooms.some(r => r.type === 'Start') },
                    { name: 'Boss room exists (worker)', pass: workerRooms.some(r => r.type === 'Boss') },
                    { name: 'Boss room exists (sync)', pass: syncRooms.some(r => r.type === 'Boss') },
                    
                    // Room properties
                    { name: 'Rooms have coordinates', pass: workerRooms.every(r => r.coords && typeof r.coords.x === 'number') },
                    { name: 'Rooms have connections', pass: workerRooms.every(r => r.connections) },
                    { name: 'Rooms have getCenter method', pass: workerRooms.every(r => typeof r.getCenter === 'function') },
                    
                    // Door compatibility
                    { name: 'Connection objects match neighbors', pass: workerRooms.every(r => r.connections === r.connections) },
                    { name: 'Start room has north connection', pass: workerRooms.find(r => r.type === 'Start')?.connections?.n !== null },
                    
                    // Special room features
                    { name: 'Secret rooms assigned (worker)', pass: workerRooms.some(r => r.hasSecretRoom) },
                    { name: 'Secret rooms assigned (sync)', pass: syncRooms.some(r => r.hasSecretRoom) },
                    
                    // Room state properties
                    { name: 'Rooms have state object', pass: workerRooms.every(r => r.state && typeof r.state === 'object') },
                    { name: 'Rooms have enemiesCleared flag', pass: workerRooms.every(r => typeof r.state?.enemiesCleared === 'boolean') },
                    { name: 'Rooms have initialEnemies array', pass: workerRooms.every(r => Array.isArray(r.state?.initialEnemies)) },
                    
                    // Connection integrity
                    { name: 'All connections are reciprocal', pass: workerRooms.every(room => {
                        return ['n', 's', 'e', 'w'].every(dir => {
                            const neighborId = room.connections[dir];
                            if (neighborId === null) return true;
                            const neighbor = workerRooms.find(r => r.id === neighborId);
                            if (!neighbor) return false;
                            const oppositeDir = {'n':'s', 's':'n', 'e':'w', 'w':'e'}[dir];
                            return neighbor.connections[oppositeDir] === room.id;
                        });
                    }) },
                    
                    // Minimap compatibility
                    { name: 'ASCII map can be generated', pass: (() => {
                        try {
                            const map = generator.generateASCIIMap();
                            return typeof map === 'string' && map.length > 0;
                        } catch (error) {
                            return false;
                        }
                    })() },
                    
                    // Both data structures populated
                    { name: 'Both layout and floorLayout populated', pass: generator.layout.size > 0 && generator.floorLayout.size > 0 },
                    { name: 'Layout and floorLayout sizes match', pass: generator.layout.size === generator.floorLayout.size },
                    
                    // Entrance direction system
                    { name: 'Boss room has entrance direction', pass: (() => {
                        const bossRooms = workerRooms.filter(r => r.type === 'Boss');
                        return bossRooms.length > 0 && bossRooms.every(r => r.entranceDirection);
                    })() },
                    { name: 'Start room has north connection', pass: (() => {
                        const startRoom = workerRooms.find(r => r.type === 'Start');
                        return startRoom && startRoom.connections.n !== null;
                    })() }
                ];
                
                checks.forEach(check => {
                    log('compatibility-output', `  ${check.pass ? '✅' : '❌'} ${check.name}`);
                });
                
                const passCount = checks.filter(c => c.pass).length;
                log('compatibility-output', `\n📋 Compatibility: ${passCount}/${checks.length} tests passed`);
                
                if (passCount === checks.length) {
                    log('compatibility-output', '🎉 Full compatibility confirmed!');
                    log('compatibility-output', '✅ Doors, connections, and all room features working correctly');
                } else {
                    log('compatibility-output', '⚠️  Some compatibility issues detected');
                    const failedChecks = checks.filter(c => !c.pass);
                    failedChecks.forEach(check => {
                        log('compatibility-output', `   ❌ ${check.name}`);
                    });
                }
                
                // Test door generation specifically
                log('compatibility-output', '\n🚪 Testing door generation compatibility...');
                const startRoom = workerRooms.find(r => r.type === 'Start');
                if (startRoom) {
                    const connectionCount = Object.values(startRoom.connections).filter(c => c !== null).length;
                    log('compatibility-output', `  Start room connections: ${connectionCount}`);
                    log('compatibility-output', `  North: ${startRoom.connections.n !== null ? '✅' : '❌'}`);
                    log('compatibility-output', `  South: ${startRoom.connections.s !== null ? '✅' : '❌'}`);
                    log('compatibility-output', `  East: ${startRoom.connections.e !== null ? '✅' : '❌'}`);
                    log('compatibility-output', `  West: ${startRoom.connections.w !== null ? '✅' : '❌'}`);
                }
                
                // Restore setting
                generator.useWebWorker = workerEnabled;
                
            } catch (error) {
                log('compatibility-output', `❌ Compatibility test failed: ${error.message}`);
            }
        };

        window.generateSingleDungeon = async () => {
            log('manual-output', '🏰 Generating single dungeon...');
            
            try {
                const start = performance.now();
                const layout = await generator.generateLayout('catacombs');
                const time = Math.round(performance.now() - start);
                
                log('manual-output', `✅ Generated ${layout.size} rooms in ${time}ms`);
                
                // Room type breakdown
                const types = {};
                layout.forEach(room => {
                    types[room.type] = (types[room.type] || 0) + 1;
                });
                
                log('manual-output', '  Room types:');
                Object.entries(types).forEach(([type, count]) => {
                    log('manual-output', `    ${type}: ${count}`);
                });
                
            } catch (error) {
                log('manual-output', `❌ Generation failed: ${error.message}`);
            }
        };

        window.showASCIIMap = async () => {
            try {
                // Generate fresh dungeon to test ASCII map
                await generator.generateLayout('catacombs');
                
                log('manual-output', '🗺️  Testing ASCII Map generation...');
                log('manual-output', `Layout has ${generator.layout.size} Room instances`);
                log('manual-output', `FloorLayout has ${generator.floorLayout.size} room data objects`);
                
                const map = generator.generateASCIIMap();
                log('manual-output', '✅ ASCII Map generated successfully:');
                log('manual-output', '\n' + map);
                
                // Verify layout integrity for ASCII map
                const rooms = Array.from(generator.layout.values());
                log('manual-output', `\n📊 ASCII Map room analysis:`);
                
                const roomTypes = {};
                rooms.forEach(room => {
                    roomTypes[room.type] = (roomTypes[room.type] || 0) + 1;
                });
                
                Object.entries(roomTypes).forEach(([type, count]) => {
                    log('manual-output', `  ${type}: ${count} rooms`);
                });
                
            } catch (error) {
                log('manual-output', `❌ ASCII map failed: ${error.message}`);
                console.error('ASCII map error details:', error);
            }
        };

        window.testMinimapCompatibility = async () => {
            log('manual-output', '🗺️  Testing minimap compatibility...');
            
            try {
                // Test with web worker
                generator.useWebWorker = true;
                const layout = await generator.generateLayout('catacombs');
                
                log('manual-output', `Generated ${layout.size} rooms with web worker`);
                
                // Test minimap data structure
                const rooms = Array.from(layout.values());
                const minimapChecks = [
                    { name: 'All rooms have id', pass: rooms.every(r => typeof r.id === 'number') },
                    { name: 'All rooms have coords', pass: rooms.every(r => r.coords && typeof r.coords.x === 'number' && typeof r.coords.y === 'number') },
                    { name: 'All rooms have type', pass: rooms.every(r => typeof r.type === 'string') },
                    { name: 'All rooms have visited flag', pass: rooms.every(r => typeof r.visited === 'boolean') },
                    { name: 'All rooms have connections', pass: rooms.every(r => r.connections && typeof r.connections === 'object') },
                    { name: 'Start room exists', pass: rooms.some(r => r.type === 'Start') },
                    { name: 'Boss room exists', pass: rooms.some(r => r.type === 'Boss') }
                ];
                
                log('manual-output', '🔍 Minimap data structure validation:');
                minimapChecks.forEach(check => {
                    log('manual-output', `  ${check.pass ? '✅' : '❌'} ${check.name}`);
                });
                
                // Test coordinate bounds (minimap needs these)
                const coords = rooms.map(r => r.coords);
                const minX = Math.min(...coords.map(c => c.x));
                const maxX = Math.max(...coords.map(c => c.x));
                const minY = Math.min(...coords.map(c => c.y));
                const maxY = Math.max(...coords.map(c => c.y));
                
                log('manual-output', `📐 Coordinate bounds: X(${minX} to ${maxX}), Y(${minY} to ${maxY})`);
                log('manual-output', `📐 Grid size: ${maxX - minX + 1} x ${maxY - minY + 1}`);
                
                // Test start room visibility (should be visited)
                const startRoom = rooms.find(r => r.type === 'Start');
                if (startRoom) {
                    // Mark start room as visited for minimap test
                    startRoom.visited = true;
                    log('manual-output', `🏁 Start room ${startRoom.id} at (${startRoom.coords.x}, ${startRoom.coords.y}) - visited: ${startRoom.visited}`);
                    
                    // Test connections
                    const connections = Object.entries(startRoom.connections).filter(([dir, id]) => id !== null);
                    log('manual-output', `🔗 Start room connections: ${connections.length}`);
                    connections.forEach(([dir, id]) => {
                        log('manual-output', `    ${dir}: connects to room ${id}`);
                    });
                }
                
                const passCount = minimapChecks.filter(c => c.pass).length;
                if (passCount === minimapChecks.length) {
                    log('manual-output', '✅ Minimap compatibility confirmed!');
                    log('manual-output', '   All room data structures are compatible with minimap rendering');
                } else {
                    log('manual-output', `⚠️  ${minimapChecks.length - passCount} minimap compatibility issues found`);
                }
                
            } catch (error) {
                log('manual-output', `❌ Minimap test failed: ${error.message}`);
            }
        };

        window.testEntranceDirections = async () => {
            log('manual-output', '🚪 Testing entrance direction system...');
            
            try {
                // Test with web worker
                generator.useWebWorker = true;
                const layout = await generator.generateLayout('catacombs');
                
                log('manual-output', `Generated ${layout.size} rooms with web worker`);
                
                // Test boss room entrance directions
                const rooms = Array.from(layout.values());
                const bossRooms = rooms.filter(r => r.type === 'Boss');
                
                log('manual-output', `\n🏰 Boss Room Entrance Direction Test:`);
                
                if (bossRooms.length > 0) {
                    bossRooms.forEach(bossRoom => {
                        log('manual-output', `  Boss Room ${bossRoom.id}:`);
                        log('manual-output', `    Entrance Direction: ${bossRoom.entranceDirection || 'Not set'}`);
                        
                        // Check connections
                        const connections = Object.entries(bossRoom.connections).filter(([dir, id]) => id !== null);
                        log('manual-output', `    Connections: ${connections.length}`);
                        
                        connections.forEach(([dir, id]) => {
                            const directionName = {'n': 'north', 's': 'south', 'e': 'east', 'w': 'west'}[dir];
                            log('manual-output', `      ${directionName}: connects to room ${id}`);
                        });
                        
                        // Verify entrance direction matches actual connections
                        const hasNorth = bossRoom.connections.n !== null;
                        const hasSouth = bossRoom.connections.s !== null;
                        const hasEast = bossRoom.connections.e !== null;
                        const hasWest = bossRoom.connections.w !== null;
                        
                        let actualEntrance = 'none';
                        if (hasSouth) actualEntrance = 'south';
                        else if (hasNorth) actualEntrance = 'north';
                        else if (hasEast) actualEntrance = 'east';
                        else if (hasWest) actualEntrance = 'west';
                        
                        const entranceMatch = (bossRoom.entranceDirection === actualEntrance);
                        log('manual-output', `    Entrance direction match: ${entranceMatch ? '✅' : '❌'} (expected: ${actualEntrance})`);
                    });
                } else {
                    log('manual-output', '  ❌ No boss rooms found');
                }
                
                // Test event room entrance directions (these are set in post-processing)
                log('manual-output', `\n🎭 Event Room Entrance Direction Test:`);
                
                const eventRooms = rooms.filter(r => r.type === 'EVENT');
                if (eventRooms.length > 0) {
                    eventRooms.forEach(eventRoom => {
                        log('manual-output', `  Event Room ${eventRoom.id}:`);
                        log('manual-output', `    Entrance Direction: ${eventRoom.entranceDirection || 'Not set'}`);
                        log('manual-output', `    Event Room Data: ${eventRoom.eventRoomData ? 'Present' : 'Missing'}`);
                        
                        // Check host connection
                        const connections = Object.entries(eventRoom.connections).filter(([dir, id]) => id !== null);
                        log('manual-output', `    Host connections: ${connections.length}`);
                        
                        connections.forEach(([dir, id]) => {
                            const directionName = {'n': 'north', 's': 'south', 'e': 'east', 'w': 'west'}[dir];
                            log('manual-output', `      ${directionName}: host room ${id}`);
                        });
                    });
                } else {
                    log('manual-output', '  ℹ️  No event rooms found (normal - they are added in post-processing)');
                }
                
                // Test start room connections (should have north connection)
                log('manual-output', `\n🏁 Start Room Connection Test:`);
                
                const startRoom = rooms.find(r => r.type === 'Start');
                if (startRoom) {
                    log('manual-output', `  Start Room ${startRoom.id}:`);
                    
                    const connections = Object.entries(startRoom.connections).filter(([dir, id]) => id !== null);
                    log('manual-output', `    Total connections: ${connections.length}`);
                    
                    const hasNorthConnection = startRoom.connections.n !== null;
                    log('manual-output', `    North connection: ${hasNorthConnection ? '✅' : '❌'} (required)`);
                    
                    if (hasNorthConnection) {
                        log('manual-output', `    North connects to room: ${startRoom.connections.n}`);
                    }
                    
                    connections.forEach(([dir, id]) => {
                        const directionName = {'n': 'north', 's': 'south', 'e': 'east', 'w': 'west'}[dir];
                        log('manual-output', `      ${directionName}: room ${id}`);
                    });
                } else {
                    log('manual-output', '  ❌ No start room found');
                }
                
                // Summary
                const bossCount = bossRooms.length;
                const eventCount = eventRooms.length;
                const startCount = rooms.filter(r => r.type === 'Start').length;
                
                log('manual-output', `\n📊 Entrance Direction Summary:`);
                log('manual-output', `  Boss rooms: ${bossCount} (should have preferred entrance)`);
                log('manual-output', `  Event rooms: ${eventCount} (entrance set in post-processing)`);
                log('manual-output', `  Start rooms: ${startCount} (should have north connection)`);
                
                const allGood = bossCount > 0 && startCount === 1;
                log('manual-output', `  Overall: ${allGood ? '✅ Good' : '⚠️  Needs attention'}`);
                
            } catch (error) {
                log('manual-output', `❌ Entrance direction test failed: ${error.message}`);
                console.error('Entrance direction test error:', error);
            }
        };

        window.getWorkerMetrics = async () => {
            try {
                const metrics = await generator.getWorkerPerformanceMetrics();
                if (metrics) {
                    log('manual-output', '📊 Worker Performance Metrics:');
                    log('manual-output', `  Total requests: ${metrics.totalRequests}`);
                    log('manual-output', `  Average time: ${metrics.averageTime.toFixed(2)}ms`);
                    log('manual-output', `  Total time: ${metrics.totalTime.toFixed(2)}ms`);
                    if (metrics.memoryUsage) {
                        log('manual-output', `  Memory used: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
                    }
                } else {
                    log('manual-output', '⚠️  No worker metrics available (worker not active)');
                }
            } catch (error) {
                log('manual-output', `❌ Failed to get metrics: ${error.message}`);
            }
        };

        // Initialize
        log('manual-output', '🚀 Web Worker Dungeon Test initialized');
        log('manual-output', `Web worker support: ${generator.useWebWorker ? 'Available' : 'Not available'}`);
    </script>
</body>
</html>