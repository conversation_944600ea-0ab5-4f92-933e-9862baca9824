# Precise Switch Timeline

This document explains how to use the new "precise_switch" timeline that switches patterns at exactly 19 seconds.

## Timeline Overview

The "precise_switch" timeline is designed to demonstrate precise control over pattern switching at specific time points in the music. It has been simplified to use only ONE pattern at a time:

1. **0-19 seconds**: ONLY petal spread pattern
2. **19-60 seconds**: ONLY laser grid pattern (switches exactly at 19 seconds)

We've also created an alternative "single_patterns" timeline with more pattern changes:

1. **0-19 seconds**: ONLY petal spread pattern
2. **19-35 seconds**: ONLY laser grid pattern
3. **35-50 seconds**: ONLY spiral wave pattern
4. **50-65 seconds**: ONLY hellburst pattern
5. **65-80 seconds**: ONLY inhale exhale pattern

## How to Use This Timeline

### In-Game Activation

To use this timeline in the game:

1. Start a boss battle
2. Open the developer console (F12 in most browsers)
3. Run the following command for the simple two-pattern timeline:
   ```javascript
   game.dungeonHandler.currentRoom.boss.bossController.loadTimeline("precise_switch");
   ```

4. OR, run this command for the more complex timeline with five different patterns:
   ```javascript
   game.dungeonHandler.currentRoom.boss.bossController.loadTimeline("single_patterns");
   ```

### Code Integration

If you want to set this timeline as the default for a boss battle, you can modify the boss initialization code:

```javascript
// In your boss initialization code
bossController.loadTimeline("precise_switch");
```

Or you can set it as an option when creating the BossController:

```javascript
const bossController = new BossController(dungeonHandler, boss, audioManager, {
    timelineName: "precise_switch",
    // other options...
});
```

## Customizing the Timeline

You can modify the timeline in `src/data/bossTimelines.js` to change:

- The exact timing of pattern switches
- Which patterns play during each section
- The intensity and speed of patterns
- How frequently patterns trigger

For example, to change the first switch to happen at 25 seconds instead of 19, modify:

```javascript
{
    startTime: 0,
    endTime: 19,  // Change this to 25
    patterns: ["petal_spread"],
    // ...
},
{
    startTime: 19,  // Change this to 25
    endTime: 35,
    // ...
}
```

## Testing the Timeline

To test if the timeline is working correctly:

1. Enable debug mode to see timeline information
2. Watch for the pattern switch at exactly 19 seconds
3. Verify that the debug overlay shows the correct timeline section
4. Confirm that ONLY ONE pattern is being used at a time

The debug overlay will display:
- Current timeline progress
- Current music time
- Current timeline section description
- Current patterns being used

## Troubleshooting

If you're still seeing multiple patterns or the boss isn't following the timeline:

1. Make sure you've loaded the timeline correctly using the console command
2. Check that the music is playing (the timeline is synced to music time)
3. Try restarting the boss battle after loading the timeline
4. Verify in the debug overlay that the timeline is active and showing the correct section

If the boss is still using multiple patterns, you might need to disable the regular pattern selection logic temporarily by adding this line to the console:

```javascript
game.dungeonHandler.currentRoom.boss.bossController.patternManager.useOnlyTimelinePatterns = true;
```

This will force the boss to only use patterns specified in the timeline.
