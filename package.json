{"name": "tarot11", "version": "1.0.0", "main": "main.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Deroqs/soulpath.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Deroqs/soulpath/issues"}, "homepage": "https://github.com/Deroqs/soulpath#readme", "description": "", "dependencies": {"@wllama/wllama": "^2.3.2"}}