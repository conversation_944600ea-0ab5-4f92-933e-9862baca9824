# 🎮 Vibe-Reactive Boss System

A dynamic, music-synced bullet hell boss system that reacts to music intensity, rhythm, and energy in real-time.

## 🌟 Features

- **Real-time Music Analysis**: Uses FFT to analyze music frequency and energy
- **Dynamic Bullet Patterns**: 7 unique patterns that adapt to music intensity
- **Reactive Boss Behavior**: Boss speed and attack frequency sync with the music
- **Performance Optimized**: Adaptive complexity based on FPS and projectile count
- **Visual Effects**: Screen shake and background pulses on music beats

## 📁 Files

- `src/audio/BossMusicAnalyzer.js` - Real-time music analysis using FFT
- `src/projectiles/BulletPatternManager.js` - Bullet pattern management
- `src/ai/brains/BossController.js` - Main controller for the boss system
- `src/ai/brains/BossCombatAI.js` - Integration with game AI
- `ultra-debug.js` - Debug overlay for development
- `ultra-boss-test.html` - Test page for the boss system
- `run-ultra-test.sh` - <PERSON><PERSON>t to run the test

## 🚀 Quick Start

1. Run the test script:
   ```
   ./run-ultra-test.sh
   ```

2. The test will:
   - Load the game
   - Display the debug overlay
   - Teleport you to the boss room
   - Start the music-reactive boss battle

## 📊 Debug Overlay

The debug overlay shows:
- 🎵 Music Intensity (0-100%)
- 🔄 Current Bullet Pattern
- 🥁 Beat Detection Status
- ⚡ FPS Counter
- 🔫 Active Projectile Count

## 📚 Documentation

For more information, see:
- `BOSS_SYSTEM_DOCUMENTATION.md` - Detailed system documentation
- `BOSS_SYSTEM_QUICKSTART.md` - Quick start guide for developers

## 🔧 Customization

The system is highly customizable:
- Adjust frequency bands for different music styles
- Modify bullet patterns and their intensity ranges
- Create new patterns with custom behaviors
- Fine-tune performance parameters

## 🎮 Gameplay Experience

The boss battle dynamically adapts to the music:
- Low intensity sections: Ambient, peaceful patterns
- Medium intensity: Rhythmic, elegant patterns
- High intensity: Chaotic, challenging patterns
- Beat-synchronized visual effects

## 🔍 Testing

Multiple test options are available:
- `run-ultra-test.sh` - Ultra-optimized version for lower-end systems
- `run-boss-test.sh` - Standard version
- `run-direct-test.sh` - Direct test that bypasses game initialization

## 🛠️ Performance Optimization

If you experience lag:
- Reduce `maxProjectiles` in `BulletPatternManager.js`
- Increase pattern cooldowns
- Use the ultra-optimized version
