// Debug room 24 event room configuration
window.debugRoom24 = function() {
    const room = window.dungeonHandler?.floorLayout?.get(24);
    if (!room) {
        console.error("Room 24 not found!");
        return;
    }
    
    console.log("=== ROOM 24 EVENT ROOM DEBUG ===");
    console.log("Room type:", room.type);
    console.log("Room shape:", room.shape);
    console.log("Has eventRoomData:", !!room.eventRoomData);
    console.log("Has eventMechanics:", !!room.eventMechanics);
    
    if (room.eventRoomData) {
        console.log("\nEvent Room Data:");
        console.log("- ID:", room.eventRoomData.id);
        console.log("- Name:", room.eventRoomData.name);
        console.log("- Has availableConnections:", !!room.eventRoomData.availableConnections);
        if (room.eventRoomData.availableConnections) {
            console.log("- Available connections:", room.eventRoomData.availableConnections);
        } else {
            console.log("- availableConnections is missing or undefined!");
        }
    }
    
    if (room.eventMechanics) {
        console.log("\nEvent Mechanics:");
        console.log("- Has availableConnections:", !!room.eventMechanics.availableConnections);
        if (room.eventMechanics.availableConnections) {
            console.log("- Mechanics available connections:", room.eventMechanics.availableConnections);
        }
    }
    
    console.log("\nActual room connections:", room.connections);
    console.log("Room entrances:", room.entrances);
    
    // Check what the room generator would see
    const availableConnections = room.eventRoomData?.availableConnections || room.eventMechanics?.availableConnections;
    console.log("\nCombined availableConnections check:", availableConnections);
    
    if (!availableConnections) {
        console.error("❌ NO availableConnections found in either eventRoomData or eventMechanics!");
    }
    
    return room;
};

// Auto-run
debugRoom24();