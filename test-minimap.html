<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimap Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        #minimap-grid {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: transparent;
            border: none;
            border-radius: 0;
            padding: 12px;
            opacity: 0.95;
            z-index: 1000;
            max-width: 400px;
            max-height: 400px;
        }
        
        #minimap-grid svg {
            display: block;
            max-width: 100%;
            max-height: 100%;
        }
        
        .test-controls {
            margin-top: 50px;
        }
        
        button {
            margin: 5px;
            padding: 10px;
            background: #444;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
        }
        
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <h1>Minimap Shape Test</h1>
    <div class="test-controls">
        <button onclick="testMinimap('simple')">Test Simple Layout</button>
        <button onclick="testMinimap('complex')">Test Complex Shapes</button>
        <button onclick="testMinimap('large')">Test Large Layout</button>
        <button onclick="testMinimap('allshapes')">Test All Shapes</button>
    </div>
    
    <div id="minimap-grid"></div>
    
    <script>
        // Helper function to create room shape SVG
        function createRoomShapeSVG(shapeKey, room, cellSize = 14) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'L_SHAPE':
                    width = cellSize * 2;
                    height = cellSize * 2;
                    // Create vertically flipped L-shape for correct minimap display
                    svgContent = `
                        <path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'T_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    // Create vertically flipped T-shape: vertical stem at top and horizontal bar at bottom
                    svgContent = `
                        <path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${height} L0,${height} L0,${cellSize} L${cellSize},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_DOWN':
                    width = cellSize * 3;
                    height = cellSize * 2;  
                    svgContent = `
                        <path d="M0,0 L${cellSize},0 L${cellSize},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${width},0 L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_RIGHT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // Create proper U-shape with opening at right - vertical left bar with top and bottom arms extending right
                    svgContent = `
                        <path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${cellSize * 2} L${width},${cellSize * 2} L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_LEFT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // Create proper U-shape with opening at left - has top and bottom horizontal parts
                    svgContent = `
                        <path d="M${cellSize},0 L${width},0 L${width},${height} L${cellSize},${height} L${cellSize},${cellSize * 2} L0,${cellSize * 2} L0,${cellSize} L${cellSize},${cellSize} L${cellSize},0 Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'CROSS_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 3;
                    svgContent = `
                        <path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${cellSize * 2} L${cellSize * 2},${cellSize * 2} L${cellSize * 2},${height} L${cellSize},${height} L${cellSize},${cellSize * 2} L0,${cellSize * 2} L0,${cellSize} L${cellSize},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'RECT_2X1':
                    width = cellSize * 2;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'BOSS_ARENA':
                    width = cellSize * 2;
                    height = cellSize * 2;
                    svgContent = `
                        <rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>
                        <rect x="2" y="2" width="${width-4}" height="${height-4}" fill="none" stroke="currentColor" stroke-width="1"/>
                    `;
                    break;
                
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function testMinimap(layout) {
            const minimapElement = document.getElementById('minimap-grid');
            minimapElement.innerHTML = '';
            
            let rooms = [];
            
            if (layout === 'simple') {
                rooms = [
                    { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true, connections: { e: 1, n: null, s: null, w: null } },
                    { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 }, visited: true, connections: { e: 2, w: 0, n: null, s: null } },
                    { id: 2, type: 'Boss', shapeKey: 'BOSS_ARENA', coords: { x: 2, y: 0 }, visited: true, connections: { w: 1, n: null, s: null, e: null } }
                ];
            } else if (layout === 'complex') {
                rooms = [
                    { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 }, visited: true, connections: { n: 1, s: 3, e: null, w: null } },
                    { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 0, y: 0 }, visited: true, connections: { s: 0, e: 2, n: null, w: null } },
                    { id: 2, type: 'Normal', shapeKey: 'T_SHAPE', coords: { x: 2, y: 0 }, visited: true, connections: { w: 1, s: 5, n: null, e: null } },
                    { id: 3, type: 'Event', shapeKey: 'U_SHAPE_DOWN', coords: { x: 1, y: 2 }, visited: true, connections: { n: 0, w: 4, e: 6, s: null } },
                    { id: 4, type: 'Normal', shapeKey: 'CROSS_SHAPE', coords: { x: 0, y: 2 }, visited: true, connections: { e: 3, n: null, s: null, w: null } },
                    { id: 5, type: 'Boss', shapeKey: 'BOSS_ARENA', coords: { x: 3, y: 1 }, visited: true, connections: { n: 2, e: null, s: null, w: null } },
                    { id: 6, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 2, y: 2 }, visited: true, connections: { w: 3, n: null, s: null, e: null } }
                ];
            } else if (layout === 'allshapes') {
                rooms = [
                    { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true, connections: { e: 1, n: null, s: null, w: null } },
                    { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 1, y: 0 }, visited: true, connections: { w: 0, e: 2, n: null, s: null } },
                    { id: 2, type: 'Normal', shapeKey: 'T_SHAPE', coords: { x: 2, y: 0 }, visited: true, connections: { w: 1, e: 3, n: null, s: null } },
                    { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 3, y: 0 }, visited: true, connections: { w: 2, s: 4, n: null, e: null } },
                    { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 3, y: 1 }, visited: true, connections: { n: 3, s: 5, e: null, w: null } },
                    { id: 5, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 3, y: 2 }, visited: true, connections: { n: 4, s: 6, e: null, w: null } },
                    { id: 6, type: 'Normal', shapeKey: 'CROSS_SHAPE', coords: { x: 3, y: 3 }, visited: true, connections: { n: 5, w: 7, e: null, s: null } },
                    { id: 7, type: 'Normal', shapeKey: 'RECT_2X1', coords: { x: 2, y: 3 }, visited: true, connections: { e: 6, w: 8, n: null, s: null } },
                    { id: 8, type: 'Boss', shapeKey: 'BOSS_ARENA', coords: { x: 1, y: 3 }, visited: true, connections: { e: 7, n: null, s: null, w: null } }
                ];
            } else if (layout === 'large') {
                rooms = [];
                let id = 0;
                const shapes = ['SQUARE_1X1', 'L_SHAPE', 'T_SHAPE', 'U_SHAPE_DOWN', 'U_SHAPE_RIGHT', 'U_SHAPE_LEFT', 'RECT_2X1', 'CROSS_SHAPE'];
                const roomGrid = [];
                
                // First pass: create rooms
                for (let y = 0; y < 5; y++) {
                    roomGrid[y] = [];
                    for (let x = 0; x < 6; x++) {
                        if (Math.random() > 0.3) { // 70% chance of room
                            const shapeKey = shapes[Math.floor(Math.random() * shapes.length)];
                            let type = 'Normal';
                            if (id === 0) type = 'Start';
                            else if (id === 1) type = 'Event';
                            else if (Math.random() > 0.9) type = 'Boss';
                            
                            const room = {
                                id: id++,
                                type: type,
                                shapeKey: shapeKey,
                                coords: { x, y },
                                visited: true,
                                connections: { n: null, s: null, e: null, w: null }
                            };
                            
                            rooms.push(room);
                            roomGrid[y][x] = room;
                        } else {
                            roomGrid[y][x] = null;
                        }
                    }
                }
                
                // Second pass: create connections
                rooms.forEach(room => {
                    const { x, y } = room.coords;
                    
                    // Check north
                    if (y > 0 && roomGrid[y-1][x]) {
                        room.connections.n = roomGrid[y-1][x].id;
                    }
                    // Check south  
                    if (y < 4 && roomGrid[y+1][x]) {
                        room.connections.s = roomGrid[y+1][x].id;
                    }
                    // Check east
                    if (x < 5 && roomGrid[y][x+1]) {
                        room.connections.e = roomGrid[y][x+1].id;
                    }
                    // Check west
                    if (x > 0 && roomGrid[y][x-1]) {
                        room.connections.w = roomGrid[y][x-1].id;
                    }
                });
            }
            
            // Find bounds
            let minX = rooms[0].coords.x, maxX = rooms[0].coords.x;
            let minY = rooms[0].coords.y, maxY = rooms[0].coords.y;
            
            rooms.forEach(room => {
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            });
            
            // Calculate adaptive scaling based on dungeon size
            const dungeonWidth = maxX - minX + 1;
            const dungeonHeight = maxY - minY + 1;
            
            // Maximum minimap size (fits in corner without blocking UI)
            const maxMinimapWidth = 250;
            const maxMinimapHeight = 200;
            
            // Calculate optimal cell size for connected room display
            const baseCellSize = 12;
            const connectionSpacing = 1.8; // Tight spacing for natural connections
            const scaleX = Math.min(maxMinimapWidth / (dungeonWidth * baseCellSize * connectionSpacing * 2), 1.0);
            const scaleY = Math.min(maxMinimapHeight / (dungeonHeight * baseCellSize * connectionSpacing * 2), 1.0);
            const scale = Math.min(scaleX, scaleY);
            
            const cellSize = Math.max(baseCellSize * scale, 4); // Minimum 4px cells for visibility
            const gridUnit = cellSize * connectionSpacing; // Grid unit for positioning
            const mapWidth = dungeonWidth * gridUnit + cellSize * 6; // Extra padding for largest shapes
            const mapHeight = dungeonHeight * gridUnit + cellSize * 6;
            
            console.log(`[Test Minimap] Dungeon size: ${dungeonWidth}x${dungeonHeight}, Scale: ${scale.toFixed(2)}, Cell size: ${cellSize.toFixed(1)}px`);
            
            // Create SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)'; // Rotate 180 degrees and flip left/right
            
            // No connection lines - using tight spacing for connectivity
            
            // Add rooms with tighter spacing
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, room, cellSize);
                
                // Calculate positioning for true physical connection (no gaps between connected rooms)
                const gridUnitX = cellSize * 1.8; // Spacing for connection calculations
                const gridUnitY = cellSize * 1.8;
                const baseX = (room.coords.x - minX) * gridUnitX + cellSize * 2; 
                const baseY = (maxY - room.coords.y) * gridUnitY + cellSize * 2;
                
                // Position each shape so its connection points align with the grid
                let gridX = baseX;
                let gridY = baseY;
                
                // Shape positioning based on where their connection points should be
                if (shapeKey === 'SQUARE_1X1') {
                    // 1x1: centered on grid point
                    gridX = baseX;
                    gridY = baseY;
                } else if (shapeKey === 'L_SHAPE') {
                    // L-shape: corner at grid point
                    gridX = baseX;
                    gridY = baseY;
                } else if (shapeKey === 'SQUARE_2X2' || shapeKey === 'BOSS_ARENA') {
                    // 2x2: center at grid point
                    gridX = baseX - cellSize * 0.5;
                    gridY = baseY - cellSize * 0.5;
                } else if (shapeKey === 'T_SHAPE') {
                    // T-shape: center stem at grid point
                    gridX = baseX - cellSize;
                    gridY = baseY;
                } else if (shapeKey === 'U_SHAPE_DOWN') {
                    // U down: center opening at grid point
                    gridX = baseX - cellSize;
                    gridY = baseY;
                } else if (shapeKey === 'U_SHAPE_UP') {
                    // U up: center opening at grid point
                    gridX = baseX - cellSize;
                    gridY = baseY - cellSize;
                } else if (shapeKey === 'U_SHAPE_LEFT') {
                    // U left: center opening at grid point
                    gridX = baseX - cellSize;
                    gridY = baseY - cellSize;
                } else if (shapeKey === 'U_SHAPE_RIGHT') {
                    // U right: center opening at grid point
                    gridX = baseX;
                    gridY = baseY - cellSize;
                } else if (shapeKey === 'CROSS_SHAPE') {
                    // Cross: center at grid point
                    gridX = baseX - cellSize;
                    gridY = baseY - cellSize;
                } else if (shapeKey === 'RECT_2X1' || shapeKey === 'RECTANGULAR') {
                    // Rectangle 2x1: left edge aligned with grid point
                    gridX = baseX;
                    gridY = baseY - cellSize * 0.5; // Center vertically
                }
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                // Enhanced color scheme
                let roomColor = '#8a8a8a';
                if (room.id === 0) roomColor = '#ffffff'; // Current room
                else if (room.type === 'Boss') roomColor = '#ff3333';
                else if (room.type === 'Event') roomColor = '#3399ff';
                else if (room.type === 'Start') roomColor = '#33ff33';
                else if (room.type === 'Secret') roomColor = '#ffaa00';
                
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                // No borders - solid shapes only
                
                // Enhanced glow effects
                if (room.id === 0) {
                    roomGroup.style.filter = 'drop-shadow(0 0 4px #ffffff) drop-shadow(0 0 8px rgba(255,255,255,0.5))';
                } else if (room.type === 'Boss') {
                    roomGroup.style.filter = 'drop-shadow(0 0 3px #ff3333)';
                } else if (room.type === 'Event') {
                    roomGroup.style.filter = 'drop-shadow(0 0 3px #3399ff)';
                } else if (room.type === 'Start') {
                    roomGroup.style.filter = 'drop-shadow(0 0 3px #33ff33)';
                } else if (room.type === 'Secret') {
                    roomGroup.style.filter = 'drop-shadow(0 0 3px #ffaa00)';
                }
                
                // Add tooltip
                const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
                title.textContent = `Room ${room.id} (${room.type}) - ${shapeKey}`;
                roomGroup.appendChild(title);
                
                svg.appendChild(roomGroup);
            });
            
            minimapElement.appendChild(svg);
            console.log(`Generated ${layout} minimap with ${rooms.length} rooms`);
        }
        
        // Test simple layout by default
        testMinimap('simple');
    </script>
</body>
</html>