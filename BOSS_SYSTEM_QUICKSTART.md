# Vibe-Reactive Boss System: Quick Start Guide

## Overview

This quick start guide will help you get up and running with the vibe-reactive, music-synced bullet hell boss system. The system creates dynamic boss battles where bullet patterns and boss behavior are synchronized with the music.

## Installation

The system is already integrated into the game. The main components are:

- `src/audio/BossMusicAnalyzer.js` - Analyzes music in real-time
- `src/projectiles/BulletPatternManager.js` - Manages bullet patterns
- `src/ai/brains/BossController.js` - Coordinates the system
- `src/ai/brains/BossCombatAI.js` - Integrates with the game's AI

## Basic Usage

### 1. Creating a Music-Reactive Boss

To create a music-reactive boss, add the `musicReactive: true` flag to your enemy definition:

```javascript
// In EnemyTypes.js
catacombs_overlord: {
    // ... other properties
    aiType: AI_BRAIN_TYPES.BOSS,
    musicReactive: true,
    // ... other properties
}
```

### 2. Testing the System

Use the provided test scripts to quickly test the system:

```bash
# Run the ultra-optimized test
./run-ultra-test.sh

# Run the original test
./run-boss-test.sh
```

### 3. Debugging

The system includes a debug overlay that shows:
- Music intensity
- Current bullet pattern
- Beat detection
- FPS counter
- Projectile count

To enable the debug overlay, include `ultra-debug.js` in your HTML file.

## Customization

### 1. Adjusting Bullet Patterns

Modify the patterns array in `BulletPatternManager.js` to adjust intensity ranges and cooldowns:

```javascript
this.patterns = [
    { name: "petal_spread", minIntensity: 0, maxIntensity: 25, cooldown: 1.0 },
    // Add or modify patterns here
];
```

### 2. Tuning Music Analysis

Adjust frequency bands and sensitivity in `BossMusicAnalyzer.js`:

```javascript
// Frequency bands (Hz)
this.bands = {
    bass: { min: 20, max: 250 },
    midrange: { min: 250, max: 2000 },
    treble: { min: 2000, max: 16000 }
};

// Beat detection
this.beatDetection = {
    threshold: 1.5, // Adjust for more/less sensitive beat detection
    minBeatInterval: 200, // Minimum ms between beats
    // ...
};
```

### 3. Performance Optimization

If you experience lag, adjust these parameters in `BulletPatternManager.js`:

```javascript
// Maximum projectiles
const maxProjectiles = 30; // Reduce this number for better performance

// Pattern scale factor
const performanceScaleFactor = Math.min(scaleFactor, 0.5); // Reduce for fewer bullets
```

## Example: Creating a Custom Pattern

Here's how to add a new bullet pattern:

1. Add the pattern definition to the `patterns` array in `BulletPatternManager.js`:

```javascript
{ name: "custom_pattern", minIntensity: 30, maxIntensity: 70, cooldown: 0.8 }
```

2. Create a spawn method for the pattern:

```javascript
_spawnCustomPattern(position, intensity, projectileType, scaleFactor = 1.0) {
    // Number of projectiles scales with intensity and scaleFactor
    const baseProjectileCount = 10 + Math.floor(intensity * 10);
    const projectileCount = Math.max(5, Math.floor(baseProjectileCount * scaleFactor));
    
    // Spawn projectiles
    for (let i = 0; i < projectileCount; i++) {
        // Calculate direction
        const angle = (i / projectileCount) * Math.PI * 2;
        const direction = new THREE.Vector3(
            Math.cos(angle),
            0,
            Math.sin(angle)
        ).normalize();
        
        // Spawn projectile
        this._spawnProjectile(
            position.clone(),
            direction,
            projectileType,
            0.8 + (intensity * 0.5)
        );
    }
}
```

3. Add the pattern to the switch statement in `_spawnPattern`:

```javascript
case "custom_pattern":
    this._spawnCustomPattern(bossPosition, normalizedIntensity, projectileType, performanceScaleFactor);
    break;
```

## Troubleshooting

### Music Not Playing

If the boss music doesn't play:

1. Check the browser console for errors
2. Verify that Tone.js is initialized
3. Make sure the music file exists at `assets/music/catacombs/catacomb_boss.wav`

### Debug Overlay Not Visible

If the debug overlay isn't visible:

1. Include `ultra-debug.js` in your HTML file
2. Check the browser console for errors
3. Try using `ultra-boss-test.html` which has a pre-created overlay

### Performance Issues

If you experience lag:

1. Reduce `maxProjectiles` in `BulletPatternManager.js`
2. Increase pattern cooldowns
3. Use the ultra-optimized version with `run-ultra-test.sh`

## Further Reading

For more detailed information, see:
- `BOSS_SYSTEM_DOCUMENTATION.md` - Full system documentation
- Code comments in the component files
