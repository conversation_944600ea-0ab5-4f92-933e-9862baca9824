<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instanced Voxels Performance Test - Proper Comparison</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #222;
            color: #fff;
        }
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        #stats {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            line-height: 1.5;
        }
        .stat-line {
            margin: 5px 0;
        }
        .good { color: #4f4; }
        .bad { color: #f44; }
        .huge-gain { color: #4ff; font-weight: bold; }
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
        }
        button {
            display: block;
            margin: 5px 0;
            padding: 10px 20px;
            background: #444;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
            width: 200px;
        }
        button:hover {
            background: #555;
        }
        .active {
            background: #363;
            border-color: #4f4;
        }
        #info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="stats">
            <div class="stat-line">FPS: <span id="fps">0</span></div>
            <div class="stat-line">Draw Calls: <span id="drawCalls">0</span></div>
            <div class="stat-line">Triangles: <span id="triangles">0</span></div>
            <div class="stat-line">Total Voxels: <span id="voxels">0</span></div>
            <div class="stat-line">Mode: <span id="mode">Individual Meshes</span></div>
            <div class="stat-line">Performance Gain: <span id="gain">-</span></div>
        </div>
        <div id="controls">
            <button id="individualMode" class="active">Individual Meshes</button>
            <button id="instancedMode">Instanced Rendering</button>
            <button id="add100">Add 100 Voxels</button>
            <button id="add1000">Add 1000 Voxels</button>
            <button id="add5000">Add 5000 Voxels</button>
            <button id="clear">Clear All</button>
        </div>
        <div id="info">
            <strong>Test Info:</strong><br>
            This test compares individual mesh rendering (one mesh per voxel) vs instanced rendering (all voxels in one draw call per material).
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "./libs/three/build/three.module.js",
            "three/addons/": "./libs/three/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { VoxelInstanceManager } from './src/systems/VoxelInstanceManager.js';
        import { VOXEL_SIZE } from './src/generators/prefabs/shared.js';

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x333333);
        scene.fog = new THREE.Fog(0x333333, 10, 50);

        // Camera
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(10, 10, 10);
        camera.lookAt(0, 0, 0);

        // Renderer
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(renderer.domElement);

        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -10;
        directionalLight.shadow.camera.right = 10;
        directionalLight.shadow.camera.top = 10;
        directionalLight.shadow.camera.bottom = -10;
        scene.add(directionalLight);

        // Ground
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x555555 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);

        // Grid helper
        const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x444444);
        scene.add(gridHelper);

        // State
        let useInstanced = false;
        let voxelCount = 0;
        let currentVoxelGroup = null;
        let lastTime = performance.now();
        let frames = 0;
        let fps = 0;
        let individualStats = { drawCalls: 0, triangles: 0 };
        let instancedStats = { drawCalls: 0, triangles: 0 };

        // Materials for voxels
        const materials = [
            new THREE.MeshStandardMaterial({ color: 0xff0000 }),
            new THREE.MeshStandardMaterial({ color: 0x00ff00 }),
            new THREE.MeshStandardMaterial({ color: 0x0000ff }),
            new THREE.MeshStandardMaterial({ color: 0xffff00 }),
            new THREE.MeshStandardMaterial({ color: 0xff00ff })
        ];

        // Stats elements
        const fpsElement = document.getElementById('fps');
        const drawCallsElement = document.getElementById('drawCalls');
        const trianglesElement = document.getElementById('triangles');
        const voxelsElement = document.getElementById('voxels');
        const modeElement = document.getElementById('mode');
        const gainElement = document.getElementById('gain');

        // Create individual voxels (worst case - one mesh per voxel)
        function createIndividualVoxels(count) {
            const group = new THREE.Group();
            const geometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);
            
            for (let i = 0; i < count; i++) {
                const material = materials[Math.floor(Math.random() * materials.length)];
                const mesh = new THREE.Mesh(geometry, material);
                
                // Random position
                mesh.position.set(
                    (Math.random() - 0.5) * 10,
                    Math.random() * 5,
                    (Math.random() - 0.5) * 10
                );
                
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                group.add(mesh);
            }
            
            return group;
        }

        // Create instanced voxels (best case - one draw call per material)
        function createInstancedVoxels(count) {
            const manager = new VoxelInstanceManager();
            
            for (let i = 0; i < count; i++) {
                const material = materials[Math.floor(Math.random() * materials.length)];
                const position = new THREE.Vector3(
                    (Math.random() - 0.5) * 10,
                    Math.random() * 5,
                    (Math.random() - 0.5) * 10
                );
                
                manager.addVoxel(position, VOXEL_SIZE, material);
            }
            
            return manager.build();
        }

        // Add voxels
        function addVoxels(count) {
            if (currentVoxelGroup) {
                scene.remove(currentVoxelGroup);
                currentVoxelGroup.traverse(child => {
                    if (child.geometry && child.geometry.dispose) child.geometry.dispose();
                });
            }
            
            voxelCount += count;
            
            if (useInstanced) {
                currentVoxelGroup = createInstancedVoxels(voxelCount);
            } else {
                currentVoxelGroup = createIndividualVoxels(voxelCount);
            }
            
            scene.add(currentVoxelGroup);
            updateVoxelCount();
        }

        // Clear voxels
        function clearVoxels() {
            if (currentVoxelGroup) {
                scene.remove(currentVoxelGroup);
                currentVoxelGroup.traverse(child => {
                    if (child.geometry && child.geometry.dispose) child.geometry.dispose();
                });
                currentVoxelGroup = null;
            }
            voxelCount = 0;
            updateVoxelCount();
        }

        // Update voxel count
        function updateVoxelCount() {
            voxelsElement.textContent = voxelCount.toLocaleString();
        }

        // Calculate stats
        function calculateStats() {
            renderer.info.reset();
            renderer.render(scene, camera);
            
            const info = renderer.info;
            const drawCalls = info.render.calls;
            const triangles = info.render.triangles;
            
            if (useInstanced) {
                instancedStats = { drawCalls, triangles };
            } else {
                individualStats = { drawCalls, triangles };
            }
            
            drawCallsElement.textContent = drawCalls.toLocaleString();
            trianglesElement.textContent = triangles.toLocaleString();
            
            // Update colors based on performance
            if (drawCalls < 50) {
                drawCallsElement.className = 'good';
            } else if (drawCalls < 500) {
                drawCallsElement.className = '';
            } else {
                drawCallsElement.className = 'bad';
            }
            
            // Calculate gain if we have both stats
            if (individualStats.drawCalls > 0 && instancedStats.drawCalls > 0) {
                const reduction = ((individualStats.drawCalls - instancedStats.drawCalls) / individualStats.drawCalls * 100).toFixed(1);
                gainElement.textContent = `${reduction}% fewer draw calls`;
                
                if (reduction > 90) {
                    gainElement.className = 'huge-gain';
                } else if (reduction > 50) {
                    gainElement.className = 'good';
                } else {
                    gainElement.className = '';
                }
            }
        }

        // Mode switching
        document.getElementById('individualMode').addEventListener('click', () => {
            useInstanced = false;
            document.getElementById('individualMode').classList.add('active');
            document.getElementById('instancedMode').classList.remove('active');
            modeElement.textContent = 'Individual Meshes';
            clearVoxels();
        });

        document.getElementById('instancedMode').addEventListener('click', () => {
            useInstanced = true;
            document.getElementById('instancedMode').classList.add('active');
            document.getElementById('individualMode').classList.remove('active');
            modeElement.textContent = 'Instanced Rendering';
            clearVoxels();
        });

        // Add voxels buttons
        document.getElementById('add100').addEventListener('click', () => addVoxels(100));
        document.getElementById('add1000').addEventListener('click', () => addVoxels(1000));
        document.getElementById('add5000').addEventListener('click', () => addVoxels(5000));
        document.getElementById('clear').addEventListener('click', clearVoxels);

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Update FPS
            frames++;
            const currentTime = performance.now();
            if (currentTime >= lastTime + 1000) {
                fps = Math.round((frames * 1000) / (currentTime - lastTime));
                fpsElement.textContent = fps;
                fpsElement.className = fps > 50 ? 'good' : fps > 30 ? '' : 'bad';
                frames = 0;
                lastTime = currentTime;
                
                // Update stats once per second
                calculateStats();
            }
            
            // Update controls
            controls.update();
            
            // Render
            renderer.render(scene, camera);
        }

        // Handle resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Start
        animate();
    </script>
</body>
</html>