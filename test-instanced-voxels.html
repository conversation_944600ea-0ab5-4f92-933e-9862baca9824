<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instanced Voxels Performance Test</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #222;
            color: #fff;
        }
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        #stats {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            line-height: 1.5;
        }
        .stat-line {
            margin: 5px 0;
        }
        .good { color: #4f4; }
        .bad { color: #f44; }
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 5px;
        }
        button {
            display: block;
            margin: 5px 0;
            padding: 10px 20px;
            background: #444;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
            width: 200px;
        }
        button:hover {
            background: #555;
        }
        .active {
            background: #363;
            border-color: #4f4;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="stats">
            <div class="stat-line">FPS: <span id="fps">0</span></div>
            <div class="stat-line">Draw Calls: <span id="drawCalls">0</span></div>
            <div class="stat-line">Triangles: <span id="triangles">0</span></div>
            <div class="stat-line">Objects: <span id="objects">0</span></div>
            <div class="stat-line">Mode: <span id="mode">Normal</span></div>
            <div class="stat-line">Performance Gain: <span id="gain">-</span></div>
        </div>
        <div id="controls">
            <button id="normalMode" class="active">Normal Rendering</button>
            <button id="instancedMode">Instanced Rendering</button>
            <button id="add10">Add 10 Objects</button>
            <button id="add100">Add 100 Objects</button>
            <button id="clear">Clear All</button>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "./libs/three/build/three.module.js",
            "three/addons/": "./libs/three/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { createStoneRubbleObject } from './src/generators/prefabs/stoneRubbleObject.js';
        import { createStoneRubbleObjectInstanced } from './src/generators/prefabs/stoneRubbleObjectInstanced.js';

        // Scene setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x333333);
        scene.fog = new THREE.Fog(0x333333, 10, 50);

        // Camera
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(5, 5, 5);
        camera.lookAt(0, 0, 0);

        // Renderer
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(renderer.domElement);

        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.6);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.camera.near = 0.1;
        directionalLight.shadow.camera.far = 50;
        directionalLight.shadow.camera.left = -10;
        directionalLight.shadow.camera.right = 10;
        directionalLight.shadow.camera.top = 10;
        directionalLight.shadow.camera.bottom = -10;
        scene.add(directionalLight);

        // Ground
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x555555 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);

        // Grid helper
        const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x444444);
        scene.add(gridHelper);

        // State
        let useInstanced = false;
        const objects = [];
        let lastTime = performance.now();
        let frames = 0;
        let fps = 0;
        let normalStats = { drawCalls: 0, triangles: 0 };
        let instancedStats = { drawCalls: 0, triangles: 0 };

        // Stats elements
        const fpsElement = document.getElementById('fps');
        const drawCallsElement = document.getElementById('drawCalls');
        const trianglesElement = document.getElementById('triangles');
        const objectsElement = document.getElementById('objects');
        const modeElement = document.getElementById('mode');
        const gainElement = document.getElementById('gain');

        // Add objects function
        function addObjects(count) {
            for (let i = 0; i < count; i++) {
                const x = (Math.random() - 0.5) * 15;
                const z = (Math.random() - 0.5) * 15;
                
                const result = useInstanced 
                    ? createStoneRubbleObjectInstanced({ seed: Math.random() * 10000 })
                    : createStoneRubbleObject({ seed: Math.random() * 10000 });
                
                result.group.position.set(x, 0, z);
                scene.add(result.group);
                objects.push(result.group);
            }
            updateObjectCount();
        }

        // Clear objects
        function clearObjects() {
            objects.forEach(obj => {
                scene.remove(obj);
                obj.traverse(child => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
            });
            objects.length = 0;
            updateObjectCount();
        }

        // Update object count
        function updateObjectCount() {
            objectsElement.textContent = objects.length;
        }

        // Calculate stats
        function calculateStats() {
            renderer.info.reset();
            renderer.render(scene, camera);
            
            const info = renderer.info;
            const drawCalls = info.render.calls;
            const triangles = info.render.triangles;
            
            if (useInstanced) {
                instancedStats = { drawCalls, triangles };
            } else {
                normalStats = { drawCalls, triangles };
            }
            
            drawCallsElement.textContent = drawCalls;
            trianglesElement.textContent = triangles.toLocaleString();
            
            // Update colors based on performance
            if (drawCalls < 100) {
                drawCallsElement.className = 'good';
            } else {
                drawCallsElement.className = 'bad';
            }
            
            // Calculate gain if we have both stats
            if (normalStats.drawCalls > 0 && instancedStats.drawCalls > 0) {
                const reduction = ((normalStats.drawCalls - instancedStats.drawCalls) / normalStats.drawCalls * 100).toFixed(1);
                gainElement.textContent = `${reduction}% fewer draw calls`;
                gainElement.className = reduction > 50 ? 'good' : '';
            }
        }

        // Mode switching
        document.getElementById('normalMode').addEventListener('click', () => {
            useInstanced = false;
            document.getElementById('normalMode').classList.add('active');
            document.getElementById('instancedMode').classList.remove('active');
            modeElement.textContent = 'Normal';
            clearObjects();
        });

        document.getElementById('instancedMode').addEventListener('click', () => {
            useInstanced = true;
            document.getElementById('instancedMode').classList.add('active');
            document.getElementById('normalMode').classList.remove('active');
            modeElement.textContent = 'Instanced';
            clearObjects();
        });

        // Add objects buttons
        document.getElementById('add10').addEventListener('click', () => addObjects(10));
        document.getElementById('add100').addEventListener('click', () => addObjects(100));
        document.getElementById('clear').addEventListener('click', clearObjects);

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Update FPS
            frames++;
            const currentTime = performance.now();
            if (currentTime >= lastTime + 1000) {
                fps = Math.round((frames * 1000) / (currentTime - lastTime));
                fpsElement.textContent = fps;
                fpsElement.className = fps > 50 ? 'good' : fps > 30 ? '' : 'bad';
                frames = 0;
                lastTime = currentTime;
                
                // Update stats once per second
                calculateStats();
            }
            
            // Update controls
            controls.update();
            
            // Render
            renderer.render(scene, camera);
        }

        // Handle resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Start with some objects
        addObjects(20);
        animate();
    </script>
</body>
</html>