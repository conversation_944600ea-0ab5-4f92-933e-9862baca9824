<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Minimap Test</title>
    <link rel="stylesheet" href="src/styles/modern-minimap.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #fff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-button {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .test-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .test-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .info-card h3 {
            margin-top: 0;
            color: #4ecdc4;
        }
        
        .performance-metrics {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background: #4ecdc4; }
        .status-warning { background: #ffd93d; }
        .status-error { background: #ff6b6b; }
        
        .demo-area {
            position: relative;
            height: 400px;
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            overflow: hidden;
        }
        
        .demo-player {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }
        
        .comparison-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .comparison-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
        }
        
        .comparison-card.old {
            border-color: rgba(255, 107, 107, 0.3);
        }
        
        .comparison-card.new {
            border-color: rgba(78, 205, 196, 0.3);
        }
        
        @media (max-width: 768px) {
            .comparison-section {
                grid-template-columns: 1fr;
            }
            
            .test-controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🗺️ Modern Minimap System</h1>
            <p>High-performance minimap with smooth animations and stunning visual design</p>
        </div>
        
        <div class="test-controls">
            <button class="test-button" onclick="initializeModernMinimap()">Initialize Modern Minimap</button>
            <button class="test-button" onclick="simulateMovement()">Simulate Player Movement</button>
            <button class="test-button" onclick="addRandomRoom()">Add Random Room</button>
            <button class="test-button" onclick="togglePerformanceMode()">Toggle Performance Mode</button>
            <button class="test-button" onclick="showPerformanceReport()">Performance Report</button>
        </div>
        
        <div class="test-info">
            <div class="info-card">
                <h3>🚀 Performance Improvements</h3>
                <div id="performance-status">
                    <div><span class="status-indicator status-good"></span>Smooth 30 FPS animations</div>
                    <div><span class="status-indicator status-good"></span>Efficient SVG caching</div>
                    <div><span class="status-indicator status-good"></span>Reduced DOM manipulations</div>
                    <div><span class="status-indicator status-good"></span>Hardware acceleration</div>
                </div>
            </div>
            
            <div class="info-card">
                <h3>✨ Visual Enhancements</h3>
                <div>
                    <div><span class="status-indicator status-good"></span>Glassmorphism UI design</div>
                    <div><span class="status-indicator status-good"></span>Smooth state transitions</div>
                    <div><span class="status-indicator status-good"></span>Real-time player tracking</div>
                    <div><span class="status-indicator status-good"></span>Adaptive scaling</div>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Live Metrics</h3>
                <div class="performance-metrics" id="live-metrics">
                    <div>Frame Time: <span id="frame-time">0.0ms</span></div>
                    <div>Update Rate: <span id="update-rate">30 FPS</span></div>
                    <div>Memory Usage: <span id="memory-usage">0 nodes</span></div>
                    <div>Performance Level: <span id="perf-level">High</span></div>
                </div>
            </div>
            
            <div class="info-card">
                <h3>🎮 Interactive Demo</h3>
                <p>The minimap will appear in the top-right corner when initialized. Use the controls above to test different features.</p>
                <div class="demo-area" id="demo-area">
                    <div class="demo-player" id="demo-player"></div>
                </div>
            </div>
        </div>
        
        <div class="comparison-section">
            <div class="comparison-card old">
                <h3>❌ Old System Issues</h3>
                <ul style="text-align: left;">
                    <li>60+ DOM updates per second</li>
                    <li>Complete HTML reconstruction</li>
                    <li>No position interpolation</li>
                    <li>Performance bottlenecks</li>
                    <li>Jarring visual transitions</li>
                    <li>No caching system</li>
                </ul>
            </div>
            
            <div class="comparison-card new">
                <h3>✅ Modern System Benefits</h3>
                <ul style="text-align: left;">
                    <li>Efficient animation loop</li>
                    <li>SVG caching & reuse</li>
                    <li>Smooth interpolation</li>
                    <li>Performance monitoring</li>
                    <li>Stunning visual design</li>
                    <li>Adaptive optimizations</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script type="module">
        import ModernMinimap from './src/systems/ModernMinimap.js';
        
        let modernMinimap = null;
        let demoPlayer = document.getElementById('demo-player');
        let currentRoom = 0;
        let rooms = new Map();
        
        // Mock dungeon handler for testing
        const mockDungeonHandler = {
            currentRoomId: 0,
            floorLayout: new Map(),
            playerController: {
                playerMesh: {
                    position: { x: 0, y: 0, z: 0 }
                }
            }
        };
        
        // Initialize with some test rooms
        function initializeTestRooms() {
            const testRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'RECT_2X1', coords: { x: 1, y: 0 }, visited: true },
                { id: 2, type: 'Event', shapeKey: 'L_SHAPE', coords: { x: 2, y: 0 }, visited: true },
                { id: 3, type: 'Boss', shapeKey: 'BOSS_ARENA', coords: { x: 2, y: 1 }, visited: false }
            ];
            
            testRooms.forEach(room => {
                mockDungeonHandler.floorLayout.set(room.id, room);
            });
        }
        
        // Global functions for buttons
        window.initializeModernMinimap = function() {
            if (modernMinimap) {
                modernMinimap.dispose();
            }
            
            initializeTestRooms();
            modernMinimap = new ModernMinimap(mockDungeonHandler);
            window.modernMinimap = modernMinimap;
            
            updateLiveMetrics();
            console.log('Modern minimap initialized for testing');
        };
        
        window.simulateMovement = function() {
            if (!modernMinimap) return;
            
            // Simulate player movement between rooms
            const roomIds = Array.from(mockDungeonHandler.floorLayout.keys());
            currentRoom = (currentRoom + 1) % roomIds.length;
            
            const room = mockDungeonHandler.floorLayout.get(roomIds[currentRoom]);
            if (room) {
                room.visited = true;
                mockDungeonHandler.currentRoomId = room.id;
                modernMinimap.setCurrentRoom(room.id);
                modernMinimap.markRoomVisited(room.id);
                
                // Update demo player position
                demoPlayer.style.left = (room.coords.x * 80 + 50) + 'px';
                demoPlayer.style.top = (room.coords.y * 80 + 50) + 'px';
            }
        };
        
        window.addRandomRoom = function() {
            if (!modernMinimap) return;
            
            const newId = mockDungeonHandler.floorLayout.size;
            const types = ['Normal', 'Event', 'Boss', 'SECRET'];
            const shapes = ['SQUARE_1X1', 'RECT_2X1', 'L_SHAPE', 'U_SHAPE_DOWN'];
            
            const newRoom = {
                id: newId,
                type: types[Math.floor(Math.random() * types.length)],
                shapeKey: shapes[Math.floor(Math.random() * shapes.length)],
                coords: { 
                    x: Math.floor(Math.random() * 5), 
                    y: Math.floor(Math.random() * 3) 
                },
                visited: Math.random() > 0.5
            };
            
            mockDungeonHandler.floorLayout.set(newId, newRoom);
            modernMinimap.forceUpdate();
        };
        
        window.togglePerformanceMode = function() {
            if (!modernMinimap?.performanceMonitor) return;
            
            const currentLevel = modernMinimap.performanceMonitor.performanceLevel;
            const levels = ['high', 'medium', 'low'];
            const nextLevel = levels[(levels.indexOf(currentLevel) + 1) % levels.length];
            
            modernMinimap.performanceMonitor.setPerformanceLevel(nextLevel);
            updateLiveMetrics();
        };
        
        window.showPerformanceReport = function() {
            if (window.modernMinimap) {
                window.getMinimapPerformance();
            }
        };
        
        function updateLiveMetrics() {
            if (!modernMinimap) return;
            
            const metrics = modernMinimap.performanceMetrics;
            const perfMonitor = modernMinimap.performanceMonitor;
            
            document.getElementById('frame-time').textContent = metrics.lastFrameTime.toFixed(1) + 'ms';
            document.getElementById('update-rate').textContent = Math.round(1000 / modernMinimap.updateInterval) + ' FPS';
            document.getElementById('memory-usage').textContent = modernMinimap.roomElements.size + ' nodes';
            
            if (perfMonitor) {
                document.getElementById('perf-level').textContent = perfMonitor.performanceLevel;
            }
        }
        
        // Update metrics every second
        setInterval(updateLiveMetrics, 1000);
        
        // Auto-initialize for demo
        setTimeout(() => {
            window.initializeModernMinimap();
        }, 1000);
    </script>
</body>
</html>
