services:
  llamacpp-wasm-builder:
    container_name: llamacpp-wasm-builder
    build:
      context: .
      dockerfile_inline: |
        ARG EMSDK_IMAGE_TAG=none  # modify it in build_wasm.sh
        FROM emscripten/emsdk:$${EMSDK_IMAGE_TAG}
        RUN apt update && apt install -y git
      args:
        EMSDK_IMAGE_TAG: ${EMSDK_IMAGE_TAG}
    user: "${D_UID}:${D_GID}"
    volumes:
      - ..:/source:Z
    entrypoint: /bin/bash
    command:
      - -c
      - |
        set -e
        cd /source

        mkdir -p wasm/single-thread
        cd wasm/single-thread

        export SHARED_EMCC_CFLAGS="--no-entry -O3 -msimd128 -DNDEBUG -flto=full -frtti -fwasm-exceptions -sEXPORT_ALL=1 -sEXPORT_ES6=0 -sMODULARIZE=0 -sINITIAL_MEMORY=128MB -sMAXIMUM_MEMORY=4096MB -sALLOW_MEMORY_GROWTH=1 -sFORCE_FILESYSTEM=1 -sEXPORTED_FUNCTIONS=_main,_wllama_malloc,_wllama_start,_wllama_action,_wllama_exit,_wllama_debug -sEXPORTED_RUNTIME_METHODS=ccall,cwrap -sNO_EXIT_RUNTIME=1"

        # emcc --clear-cache

        emcmake cmake ../..
        export EMCC_CFLAGS="$$SHARED_EMCC_CFLAGS"
        emmake make wllama -j

        cd ../..
        mkdir -p wasm/multi-thread
        cd wasm/multi-thread
        export EMCC_CFLAGS="" # temporary clear it
        emcmake cmake ../..
        export EMCC_CFLAGS="$$SHARED_EMCC_CFLAGS -pthread -sUSE_PTHREADS=1 -sPTHREAD_POOL_SIZE=Module[\\\"pthreadPoolSize\\\"]"
        emmake make wllama -j

        # go back to root
        cd ../..

        # list files
        echo "wasm/single-thread"; ls -lh wasm/single-thread
        echo "wasm/multi-thread";  ls -lh wasm/multi-thread

        # copy output files
        mkdir -p src/multi-thread
        cp wasm/multi-thread/wllama.js         src/multi-thread
        cp wasm/multi-thread/wllama.wasm       src/multi-thread
        cp wasm/single-thread/wllama.js        src/single-thread
        cp wasm/single-thread/wllama.wasm      src/single-thread
