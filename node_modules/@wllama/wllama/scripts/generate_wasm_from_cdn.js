import fs from 'fs';
import path from 'path';

const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
const outputPath = './src/wasm-from-cdn.ts';

const version = packageJson.version;
const outputContent = `
// This file is generated by scripts/generate_wasm_from_cdn.js
// Do not edit this file directly

const WasmFromCDN = {
  'single-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@${version}/src/single-thread/wllama.wasm',
  'multi-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@${version}/src/multi-thread/wllama.wasm',
};

export default WasmFromCDN;
`.trim();

fs.writeFileSync(outputPath, outputContent);
