<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Tomb Guardian Spawn Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        Press 'T' to spawn a Tomb Guardian at a random position<br>
        Check console for spawn logs
    </div>
    
    <script type="importmap">
        {
            "imports": {
                "three": "./lib/three.module.js",
                "three/examples/jsm/": "./lib/",
                "three/addons/": "./lib/"
            }
        }
    </script>
    
    <script type="module">
        // Import the main game
        import './main.js';
        
        // Wait for game to initialize
        setTimeout(() => {
            if (window.sceneManager && window.sceneManager.currentHandler) {
                console.log('🛡️ Tomb Guardian spawn test ready!');
                console.log('🛡️ Press T to spawn a tomb guardian');
                
                // Add keyboard listener
                window.addEventListener('keydown', (event) => {
                    if (event.key === 't' || event.key === 'T') {
                        const handler = window.sceneManager.currentHandler;
                        
                        // Only work in dungeon scene
                        if (handler && handler.scene && handler._spawnEnemy) {
                            console.log('🛡️ Forcing tomb_guardian spawn...');
                            
                            // Generate random position
                            const randomX = (Math.random() - 0.5) * 20;
                            const randomZ = (Math.random() - 0.5) * 20;
                            const position = new THREE.Vector3(randomX, 0.5, randomZ);
                            
                            // Spawn the tomb guardian
                            try {
                                const enemy = handler._spawnEnemy('tomb_guardian', position);
                                if (enemy) {
                                    console.log('🛡️ Tomb Guardian spawned successfully!', enemy);
                                } else {
                                    console.error('🛡️ Failed to spawn Tomb Guardian - no enemy returned');
                                }
                            } catch (error) {
                                console.error('🛡️ Error spawning Tomb Guardian:', error);
                            }
                        } else {
                            console.log('🛡️ Not in dungeon scene, cannot spawn enemies');
                        }
                    }
                });
                
                // Also modify room data to force tomb guardians
                const handler = window.sceneManager.currentHandler;
                if (handler && handler.floorLayout) {
                    // Find current room and modify its enemy list
                    const currentRoom = handler.floorLayout.get(handler.currentRoomId);
                    if (currentRoom && !currentRoom.state.enemiesCleared) {
                        console.log('🛡️ Modifying current room to spawn tomb guardians');
                        currentRoom.state.initialEnemies = ['tomb_guardian', 'tomb_guardian'];
                        
                        // Force room repopulation
                        handler.populateRoom(currentRoom);
                    }
                }
            } else {
                console.log('Game not ready yet, waiting...');
            }
        }, 3000);
    </script>
</body>
</html>