<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Room Door Debug Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: monospace;
            background-color: #000;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
            max-width: 400px;
        }
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 100;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.162.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.162.0/examples/jsm/"
            }
        }
    </script>
</head>
<body>
    <div id="info">
        <h3>Event Room Door Debug Test</h3>
        <div id="status">Loading...</div>
    </div>
    <div id="controls">
        <button onclick="testWaterDoor()">Test Water Door</button>
        <button onclick="testSandstoneDoor()">Test Sandstone Door</button>
        <button onclick="testDefaultDoor()">Test Default Door</button>
        <button onclick="toggleWireframe()">Toggle Wireframe</button>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        
        // Import door creation functions
        import { createWaterArchwayDoor, createSandstoneArchwayDoor } from './src/generators/prefabs/waterArchwayDoor.js';
        import { createStoneArchwayDoor } from './src/generators/prefabs/stoneArchwayDoor.js';
        
        // Constants from the game
        const DOOR_WIDTH = 3.5;
        const DOOR_HEIGHT = 4.5;
        const WALL_DEPTH = 1.0;
        
        // Three.js setup
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222222);
        
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(10, 10, 10);
        
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);
        
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        
        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);
        
        // Grid helper
        const gridHelper = new THREE.GridHelper(20, 20);
        scene.add(gridHelper);
        
        // Axes helper
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
        
        // Current door group
        let currentDoor = null;
        let wireframeMode = false;
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }
        
        function clearCurrentDoor() {
            if (currentDoor) {
                scene.remove(currentDoor);
                currentDoor = null;
            }
        }
        
        function analyzeDoor(doorGroup, doorType) {
            let meshCount = 0;
            let visibleCount = 0;
            let materials = new Set();
            
            doorGroup.traverse(child => {
                if (child.isMesh) {
                    meshCount++;
                    if (child.visible) visibleCount++;
                    if (child.material) {
                        materials.add(child.material.color ? child.material.color.getHexString() : 'unknown');
                    }
                }
            });
            
            updateStatus(`
                <strong>${doorType} Door Analysis:</strong><br>
                - Door Group: ${doorGroup ? 'Created' : 'NULL'}<br>
                - Children: ${doorGroup.children.length}<br>
                - Meshes: ${meshCount} (${visibleCount} visible)<br>
                - Position: ${doorGroup.position.toArray().map(v => v.toFixed(2)).join(', ')}<br>
                - Rotation: ${doorGroup.rotation.toArray().map(v => (v * 180 / Math.PI).toFixed(1)).join(', ')}°<br>
                - Materials: ${Array.from(materials).join(', ')}<br>
                - Visible: ${doorGroup.visible}<br>
                - Name: ${doorGroup.name || 'unnamed'}<br>
                - UserData: ${JSON.stringify(doorGroup.userData)}
            `);
        }
        
        window.testWaterDoor = function() {
            clearCurrentDoor();
            console.log('Creating water door...');
            currentDoor = createWaterArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
            if (currentDoor) {
                scene.add(currentDoor);
                analyzeDoor(currentDoor, 'Water');
                console.log('Water door:', currentDoor);
            } else {
                updateStatus('Failed to create water door!');
            }
        };
        
        window.testSandstoneDoor = function() {
            clearCurrentDoor();
            console.log('Creating sandstone door...');
            currentDoor = createSandstoneArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
            if (currentDoor) {
                scene.add(currentDoor);
                analyzeDoor(currentDoor, 'Sandstone');
                console.log('Sandstone door:', currentDoor);
            } else {
                updateStatus('Failed to create sandstone door!');
            }
        };
        
        window.testDefaultDoor = function() {
            clearCurrentDoor();
            console.log('Creating default stone door...');
            currentDoor = createStoneArchwayDoor(DOOR_WIDTH, DOOR_HEIGHT, WALL_DEPTH);
            if (currentDoor) {
                scene.add(currentDoor);
                analyzeDoor(currentDoor, 'Default Stone');
                console.log('Default door:', currentDoor);
            } else {
                updateStatus('Failed to create default door!');
            }
        };
        
        window.toggleWireframe = function() {
            wireframeMode = !wireframeMode;
            scene.traverse(child => {
                if (child.isMesh && child.material) {
                    child.material.wireframe = wireframeMode;
                }
            });
        };
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Start with a water door
        testWaterDoor();
        
        animate();
    </script>
</body>
</html>