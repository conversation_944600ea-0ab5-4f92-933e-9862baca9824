// Debug room 18 configuration
window.debugRoom18 = function() {
    const room = window.dungeonHandler.floorLayout.get(18);
    if (!room) {
        console.error("Room 18 not found!");
        return;
    }
    
    console.log("=== ROOM 18 DEBUG ===");
    console.log("Room type:", room.type);
    console.log("Room connections:", room.connections);
    console.log("Room entrances:", room.entrances);
    
    if (room.eventRoomData) {
        console.log("\nEvent Room Data:");
        console.log("- ID:", room.eventRoomData.id);
        console.log("- Name:", room.eventRoomData.name);
        console.log("- Available connections:", room.eventRoomData.availableConnections);
    } else {
        console.error("NO EVENT ROOM DATA FOUND!");
    }
    
    if (room.eventMechanics && room.eventMechanics.availableConnections) {
        console.log("\nEvent Mechanics Available Connections:", room.eventMechanics.availableConnections);
    }
    
    // Check which event room this is
    if (window.dungeonHandler.eventRoomManager) {
        const eventRoomKey = window.dungeonHandler.eventRoomManager.getEventRoomKeyForFloor(1);
        console.log("\nEvent room key for floor 1:", eventRoomKey);
    }
    
    return room;
};

// Auto-run on load
console.log("Debug script loaded. Run debugRoom18() to check room 18 configuration.");