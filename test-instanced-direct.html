<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct InstancedMesh Comparison</title>
    <style>
        body {
            margin: 0;
            font-family: monospace;
            background: #000;
            color: #fff;
        }
        #stats {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border: 1px solid #333;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
        }
        .good { color: #0f0; }
        .bad { color: #f00; }
    </style>
</head>
<body>
    <div id="stats">
        <div>FPS: <span id="fps">0</span></div>
        <div>Draw Calls: <span id="drawCalls">0</span></div>
        <div>Mode: <span id="mode">-</span></div>
        <div>Voxel Count: <span id="count">0</span></div>
        <div style="margin-top: 10px;">
            <button onclick="switchMode('individual')">Individual Meshes</button>
            <button onclick="switchMode('instanced')">Instanced Mesh</button>
            <button onclick="addMoreVoxels()">Add 1000 Voxels</button>
            <button onclick="clearVoxels()">Clear</button>
        </div>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "./libs/three/build/three.module.js"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';

        // Scene setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(5, 5, 5);
        camera.lookAt(0, 0, 0);

        const renderer = new THREE.WebGLRenderer();
        renderer.setSize(window.innerWidth, window.innerHeight);
        document.body.appendChild(renderer.domElement);

        // Add light
        const light = new THREE.DirectionalLight(0xffffff, 1);
        light.position.set(5, 5, 5);
        scene.add(light);
        scene.add(new THREE.AmbientLight(0x404040));

        // Materials (shared for fair comparison)
        const redMaterial = new THREE.MeshPhongMaterial({ color: 0xff0000 });
        const greenMaterial = new THREE.MeshPhongMaterial({ color: 0x00ff00 });
        const blueMaterial = new THREE.MeshPhongMaterial({ color: 0x0000ff });
        const materials = [redMaterial, greenMaterial, blueMaterial];

        // Geometry (shared)
        const boxGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);

        // State
        let mode = 'individual';
        let voxelCount = 0;
        let voxelGroup = null;

        // Create individual meshes (one draw call per mesh)
        function createIndividualMeshes(count) {
            const group = new THREE.Group();
            
            for (let i = 0; i < count; i++) {
                const material = materials[i % materials.length];
                const mesh = new THREE.Mesh(boxGeometry, material);
                
                mesh.position.set(
                    (Math.random() - 0.5) * 4,
                    (Math.random() - 0.5) * 4,
                    (Math.random() - 0.5) * 4
                );
                
                group.add(mesh);
            }
            
            return group;
        }

        // Create instanced meshes (one draw call per material type)
        function createInstancedMeshes(count) {
            const group = new THREE.Group();
            
            // Create one InstancedMesh per material
            materials.forEach((material, matIndex) => {
                // Count how many instances use this material
                const instanceCount = Math.ceil(count / materials.length);
                const instancedMesh = new THREE.InstancedMesh(boxGeometry, material, instanceCount);
                
                let instanceIndex = 0;
                const matrix = new THREE.Matrix4();
                
                for (let i = matIndex; i < count; i += materials.length) {
                    matrix.setPosition(
                        (Math.random() - 0.5) * 4,
                        (Math.random() - 0.5) * 4,
                        (Math.random() - 0.5) * 4
                    );
                    
                    instancedMesh.setMatrixAt(instanceIndex, matrix);
                    instanceIndex++;
                }
                
                instancedMesh.instanceMatrix.needsUpdate = true;
                group.add(instancedMesh);
            });
            
            return group;
        }

        // Switch rendering mode
        window.switchMode = function(newMode) {
            mode = newMode;
            recreateVoxels();
            document.getElementById('mode').textContent = mode === 'individual' ? 'Individual Meshes' : 'Instanced Meshes';
        };

        // Add more voxels
        window.addMoreVoxels = function() {
            voxelCount += 1000;
            recreateVoxels();
        };

        // Clear voxels
        window.clearVoxels = function() {
            voxelCount = 0;
            recreateVoxels();
        };

        // Recreate voxels with current mode
        function recreateVoxels() {
            if (voxelGroup) {
                scene.remove(voxelGroup);
            }
            
            if (voxelCount > 0) {
                voxelGroup = mode === 'individual' 
                    ? createIndividualMeshes(voxelCount)
                    : createInstancedMeshes(voxelCount);
                scene.add(voxelGroup);
            }
            
            document.getElementById('count').textContent = voxelCount;
        }

        // Stats
        let frames = 0;
        let lastTime = performance.now();

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            // Rotate camera
            const time = Date.now() * 0.001;
            camera.position.x = Math.cos(time * 0.5) * 5;
            camera.position.z = Math.sin(time * 0.5) * 5;
            camera.lookAt(0, 0, 0);
            
            // Render
            renderer.render(scene, camera);
            
            // Update stats
            frames++;
            const currentTime = performance.now();
            if (currentTime >= lastTime + 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                document.getElementById('fps').textContent = fps;
                document.getElementById('fps').className = fps > 30 ? 'good' : 'bad';
                
                const drawCalls = renderer.info.render.calls;
                document.getElementById('drawCalls').textContent = drawCalls;
                document.getElementById('drawCalls').className = drawCalls < 100 ? 'good' : 'bad';
                
                frames = 0;
                lastTime = currentTime;
                renderer.info.reset();
            }
        }

        // Handle resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // Start
        switchMode('individual');
        animate();
    </script>
</body>
</html>