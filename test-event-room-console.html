<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Room Console Test</title>
    <style>
        body {
            background: #222;
            color: #eee;
            font-family: monospace;
            padding: 20px;
        }
        .output {
            background: #333;
            border: 1px solid #555;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            height: 400px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            background: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>Event Room Generation Test</h1>
    <button onclick="runTest()">Run Test</button>
    <button onclick="clearOutput()">Clear</button>
    <div id="output" class="output"></div>
    
    <script type="module">
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
        
        window.runTest = async function() {
            const output = document.getElementById('output');
            output.textContent = '🎪 Testing Event Room Generation...\n\n';
            
            for (let i = 0; i < 5; i++) {
                output.textContent += `--- Test ${i + 1} ---\n`;
                const generator = new DungeonGenerator();
                generator.currentFloorNumber = 1;
                
                try {
                    const layout = await generator.generateLayout('catacombs');
                    
                    // Count event rooms
                    let eventRoomCount = 0;
                    let eventRoomDetails = [];
                    
                    for (const [id, room] of layout) {
                        if (room.type === 'EVENT') {
                            eventRoomCount++;
                            eventRoomDetails.push({
                                id: id,
                                name: room.eventRoomName || 'Unknown',
                                eventId: room.eventRoomId || 'Unknown'
                            });
                        }
                    }
                    
                    if (eventRoomCount > 0) {
                        output.textContent += `✅ ${eventRoomCount} event room(s) generated!\n`;
                        eventRoomDetails.forEach(detail => {
                            output.textContent += `   Room ${detail.id}: ${detail.name} (${detail.eventId})\n`;
                        });
                    } else {
                        output.textContent += '❌ No event rooms generated!\n';
                    }
                    
                    // Also show ASCII map
                    output.textContent += '\nASCII Map:\n';
                    const originalLog = console.log;
                    const logs = [];
                    console.log = (...args) => logs.push(args.join(' '));
                    generator.generateASCIIMap();
                    console.log = originalLog;
                    
                    // Extract just the map portion
                    const mapStart = logs.findIndex(line => line.includes('DUNGEON ASCII MAP'));
                    const mapEnd = logs.findIndex(line => line.includes('LEGEND:'));
                    if (mapStart !== -1 && mapEnd !== -1) {
                        for (let j = mapStart + 2; j < mapEnd - 1; j++) {
                            if (logs[j]) output.textContent += logs[j] + '\n';
                        }
                    }
                    
                } catch (error) {
                    output.textContent += `❌ Error: ${error.message}\n`;
                    console.error(error);
                }
                
                output.textContent += '\n';
            }
            
            output.textContent += '✅ Test complete!\n';
        };
        
        window.clearOutput = function() {
            document.getElementById('output').textContent = '';
        };
    </script>
</body>
</html>