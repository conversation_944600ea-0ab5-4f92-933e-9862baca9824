<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Room Fix Test</title>
    <style>
        body {
            background: #222;
            color: #eee;
            font-family: monospace;
            padding: 20px;
        }
        .test-output {
            background: #333;
            border: 1px solid #555;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success { color: #0f0; }
        .error { color: #f00; }
        .warning { color: #ff0; }
    </style>
</head>
<body>
    <h1>Testing Event Room Fix</h1>
    <div id="test-results"></div>
    
    <script type="module">
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
        import { EventRoomManager } from './src/systems/EventRoomManager.js';
        
        const resultsDiv = document.getElementById('test-results');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-output ${type}`;
            div.textContent = message;
            resultsDiv.appendChild(div);
            console.log(message);
        }
        
        async function testDungeonGeneration() {
            log('Starting dungeon generation test...', 'info');
            
            try {
                // Create generator with event room manager
                const generator = new DungeonGenerator();
                const eventManager = new EventRoomManager(null, null);
                generator.eventRoomManager = eventManager;
                generator.currentFloorNumber = 1;
                
                log('Generator initialized with EventRoomManager', 'success');
                
                // Generate multiple dungeons to test consistency
                for (let i = 0; i < 5; i++) {
                    log(`\n--- Generating Dungeon ${i + 1} ---`, 'info');
                    
                    const layout = await generator.generateLayout();
                    
                    // Count room types
                    let normalCount = 0;
                    let eventCount = 0;
                    let bossCount = 0;
                    let startCount = 0;
                    let secretCount = 0;
                    
                    for (const [id, room] of layout) {
                        switch (room.type) {
                            case 'Normal': normalCount++; break;
                            case 'EVENT': eventCount++; break;
                            case 'Boss': bossCount++; break;
                            case 'Start': startCount++; break;
                            case 'Secret': secretCount++; break;
                        }
                    }
                    
                    log(`Total rooms: ${layout.size}`, 'info');
                    log(`Normal: ${normalCount}, Event: ${eventCount}, Boss: ${bossCount}, Start: ${startCount}, Secret: ${secretCount}`, 'info');
                    
                    if (eventCount === 0) {
                        log('❌ NO EVENT ROOMS GENERATED!', 'error');
                    } else {
                        log(`✅ ${eventCount} event room(s) generated!`, 'success');
                        
                        // Show which event rooms were placed
                        for (const [id, room] of layout) {
                            if (room.type === 'EVENT') {
                                const eventName = room.roomData?.eventRoomName || 'Unknown';
                                const eventId = room.roomData?.eventRoomId || 'Unknown';
                                log(`  Event Room ${id}: "${eventName}" (${eventId})`, 'success');
                            }
                        }
                    }
                    
                    // Generate ASCII map
                    generator.generateASCIIMap();
                }
                
                log('\n✅ All tests complete!', 'success');
                
            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
                log(`Stack: ${error.stack}`, 'error');
            }
        }
        
        // Run the test
        testDungeonGeneration();
    </script>
</body>
</html>