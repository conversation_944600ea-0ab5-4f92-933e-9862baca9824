# Catacombs Collision System Analysis

## Overview
The catacombs collision system for destructible objects is a comprehensive system that spans multiple files and follows a clear flow from object creation to destruction.

## 1. Object Creation in Catacombs

### Area Definition (`/src/gameData/areas.js`)
The catacombs area defines destructible objects in its `interiorObjects` array:

```javascript
interiorObjects: [
    {
        type: 'stone_vase',
        probability: 0.20,
        minQuantity: 4,
        maxQuantity: 8,
        placement: 'floor',
        placementDetail: ['random', 'corners'],
        isDestructible: true,
        destructionEffect: 'collapse',
        health: 1
    },
    {
        type: 'stone_pillar',
        probability: 0.25,
        minQuantity: 4,
        maxQuantity: 4,
        placement: 'floor',
        placementDetail: ['corners'],
        isDestructible: true,
        destructionEffect: 'collapse',
        health: 1
    },
    {
        type: 'stone_rubble',
        probability: 0.20,
        minQuantity: 2,
        maxQuantity: 6,
        placement: 'floor',
        placementDetail: ['random'],
        isDestructible: true,
        destructionEffect: 'collapse',
        health: 1
    }
]
```

### Object Prefabs
Each destructible object has a dedicated prefab that creates voxel-based 3D models:

- **Stone Vase** (`/src/generators/prefabs/stoneVaseObject.js`): 11-voxel tall pot with a carved band
- **Stone Pillar** (`/src/generators/prefabs/stonePillarObject.js`): Multi-section pillar with base, shaft, and top
- **Stone Rubble** (`/src/generators/prefabs/stoneRubbleObject.js`): Random pile of 6-12 voxel pieces

All prefabs include crucial userData for destruction:
```javascript
group.userData = {
    objectType: 'stone_vase', // or pillar/rubble
    isDestructible: true,
    destructionEffect: 'collapse',
    health: 1,
    originalVoxels: [...], // Array of voxel data for true destruction
    voxelScale: voxelSize   // Scale used for reconstruction
};
```

## 2. Object Placement and Collision Array Addition

### Environment Object Manager (`/src/systems/EnvironmentObjectManager.js`)
This system:
- Selects which objects to spawn based on probabilities
- Determines placement positions (corners, random, etc.)
- Creates object instances using prefab functions
- Marks objects as destructible based on their role

### Room Generator (`/src/scenes/roomGenerator.js`)
The room generator handles the actual placement:

1. **Placement Logic** (lines 1430-1787):
   - Pillars are placed first in corners when possible
   - Other objects use available floor/wall points
   - Each object gets collision detection setup

2. **Critical Collision Addition** (lines 1737-1768):
```javascript
if (objToPlace.isDestructible && objectGroup) {
    // Generate unique identifier
    const objectUniqueId = `floor_object_${roomData.id}_${objToPlace.type}_${placedCount}_${Date.now()}`;
    objectGroup.userData.uniqueId = objectUniqueId;
    
    // ADD TO COLLISION MESHES ARRAY
    if (objectGroup.isMesh) {
        collisionMeshes.push(objectGroup);
    } else if (objectGroup.isGroup) {
        collisionMeshes.push(objectGroup); // Parent group is added to collision
        
        // Child meshes inherit userData but are NOT added to collision array
        objectGroup.traverse(child => {
            if (child.isMesh && child !== objectGroup) {
                child.userData.isDestructible = false; // Only parent is directly destructible
                child.userData.parentDestructible = objectGroup;
                child.userData.parentUniqueId = objectUniqueId;
            }
        });
    }
}
```

3. **Transfer to DungeonHandler**: The `collisionMeshes` array is returned from `generateRoomVisuals()` and becomes `this.collisionObjects` in DungeonHandler.

## 3. Collision Detection in Projectile System

### Projectile Class (`/src/projectiles/Projectile.js`)
The projectile collision detection (lines 228-370) follows this logic:

1. **Scan Collision Objects** (lines 250-310):
```javascript
for (const obj of collisionObjects) {
    // Check if object or its parent is destructible
    let isDestructible = obj.userData?.isDestructible;
    let destructibleObject = obj;
    
    if (!isDestructible && obj.parent && obj.parent.userData?.isDestructible) {
        isDestructible = true;
        destructibleObject = obj.parent;
    }
    
    if (isDestructible === true) {
        // Use 3D distance for precise collision detection
        const distance3D = projectilePos.distanceTo(objectWorldPos);
        const hitRadius = 1.0; // Standard hit radius
        
        if (distance3D <= hitRadius) {
            // Found destructible target - store closest one
            if (distance3D < closestDistance) {
                closestDistance = distance3D;
                closestDestructible = { obj, destructibleObject, distance: distance3D };
            }
        }
    }
}
```

2. **Destroy Closest Target**: Only the closest destructible object is destroyed to prevent collateral damage.

## 4. Object Destruction Process

### DungeonHandler (`/src/scenes/DungeonHandler.js`)
When a projectile hits a destructible object:

1. **Impact Detection** (lines 5587-5604):
```javascript
for (const obj of this.collisionObjects) {
    if (!obj.userData || !obj.userData.isDestructible) continue;
    
    const distance = obj.position.distanceTo(position);
    if (distance < closestDistance) {
        closestDistance = distance;
        closestObject = obj;
    }
}

if (closestObject && closestDistance < 2.0) {
    this._handleObjectDestruction(closestObject, position, velocity);
}
```

2. **True Voxel Destruction** (lines 8658-8749):
```javascript
if (objUserData.originalVoxels && objUserData.voxelScale) {
    // Create individual debris pieces from original voxel data
    objUserData.originalVoxels.forEach((voxelData, index) => {
        // Performance optimization: only use every 4th-8th voxel
        if (index % voxelStep !== 0) return;
        
        // Create debris mesh with original voxel color
        const material = _getMaterialByHex_Cached(voxelData.c);
        const voxelMesh = new THREE.Mesh(baseVoxelGeo, material);
        
        // Position debris at original voxel location in world space
        tempVec.set(voxelData.x * voxelScale, voxelData.y * voxelScale, voxelData.z * voxelScale);
        tempVec.applyMatrix4(objectWorldMatrix);
        voxelMesh.position.copy(tempVec);
        
        // Apply physics with projectile momentum
        const explosionDir = voxelMesh.position.clone().sub(objectCenterPos).normalize();
        if (projectileVelocity) {
            explosionDir.lerp(projectileVelocity.clone().normalize(), 0.6);
        }
        
        // Add to scene as physics debris
        this.scene.add(voxelMesh);
        this.debrisObjects.push({ mesh: voxelMesh, velocity, /* ... */ });
    });
}
```

## 5. Working Examples in Catacombs

### Stone Vase Destruction
- **Spawns**: 20% chance, 4-8 vases per room in random positions and corners
- **Structure**: 11-voxel tall pot with varied stone grain colors and dark carved band
- **Collision**: Parent group added to `collisionObjects` array
- **Destruction**: Creates ~12-25 colored debris pieces (every 8th voxel for performance)
- **Physics**: Debris flies outward with projectile momentum influence

### Stone Pillar Destruction  
- **Spawns**: 25% chance, exactly 4 pillars per room in corners
- **Structure**: Multi-section pillar (base + shaft + top) with weathering variation
- **Collision**: Parent group added to `collisionObjects` array
- **Destruction**: Creates ~25-50 debris pieces (every 4th voxel)
- **Physics**: Large debris pieces with tumbling motion

### Stone Rubble Destruction
- **Spawns**: 20% chance, 2-6 piles per room randomly placed
- **Structure**: 6-12 randomly positioned and rotated voxel pieces
- **Collision**: Parent group added to `collisionObjects` array  
- **Destruction**: Creates ~3-12 debris pieces (every 4th voxel)
- **Physics**: Smaller debris with scattered explosion pattern

## Key Design Principles

1. **Performance Optimization**: Only parent groups are in collision arrays, not individual child meshes
2. **True Voxel Destruction**: Objects reconstruct from original voxel data for authentic destruction
3. **Collision Hierarchy**: Parent objects handle destruction, children inherit properties but aren't directly targetable
4. **Projectile Momentum**: Debris inherits directional momentum from projectile impact
5. **Debris Reduction**: Performance optimizations reduce debris count by 75-87.5% while maintaining visual impact

This system provides believable, physics-based destruction of environment objects while maintaining good performance through careful optimization of debris generation and collision detection.