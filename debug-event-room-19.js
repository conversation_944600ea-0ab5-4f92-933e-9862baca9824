// Debug event room 19
window.debugEventRoom19 = function() {
    const room = window.dungeonHandler?.floorLayout?.get(19);
    if (!room) {
        console.error("Room 19 not found!");
        return;
    }
    
    console.log("=== ROOM 19 EVENT ROOM DEBUG ===");
    console.log("Room type:", room.type);
    console.log("Has eventRoomData:", !!room.eventRoomData);
    console.log("Has eventMechanics:", !!room.eventMechanics);
    
    if (room.eventRoomData) {
        console.log("\nEvent Room Data:");
        console.log("- ID:", room.eventRoomData.id);
        console.log("- Name:", room.eventRoomData.name);
        console.log("- Has availableConnections:", !!room.eventRoomData.availableConnections);
        if (room.eventRoomData.availableConnections) {
            console.log("- Available connections:", room.eventRoomData.availableConnections);
        }
    }
    
    if (room.eventMechanics) {
        console.log("\nEvent Mechanics:");
        console.log("- Has availableConnections:", !!room.eventMechanics.availableConnections);
        if (room.eventMechanics.availableConnections) {
            console.log("- Mechanics available connections:", room.eventMechanics.availableConnections);
        }
    }
    
    console.log("\nActual room connections:", room.connections);
    console.log("Room entrances:", room.entrances);
    
    // Check the event room manager
    if (window.dungeonHandler?.eventRoomManager) {
        const eventRoomKey = window.dungeonHandler.eventRoomManager.floorEventRooms?.get(1);
        console.log("\nEvent room key for floor 1:", eventRoomKey);
    }
};

debugEventRoom19();