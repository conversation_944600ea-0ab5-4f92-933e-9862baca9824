<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Worker Fixes Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2e7d32; border: 1px solid #4caf50; }
        .error { background: #c62828; border: 1px solid #f44336; }
        .warning { background: #f57c00; border: 1px solid #ff9800; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        pre {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Worker Fixes Verification</h1>
        <p>This page verifies that all critical worker integration fixes are working correctly.</p>
        
        <button onclick="runAllTests()">🧪 Run All Tests</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        function addResult(message, type = 'success') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        window.clearResults = clearResults;

        window.runAllTests = async function() {
            clearResults();
            addResult('🚀 Starting worker fixes verification...', 'success');
            
            // Wait for worker system to be ready
            let attempts = 0;
            while (!window.workerIntegration && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.workerIntegration) {
                addResult('❌ Worker integration not available - system may still be loading', 'error');
                return;
            }
            
            addResult('✅ Worker integration system available', 'success');
            
            // Test 1: Bullet Pattern Worker Fix
            try {
                addResult('🔫 Testing bullet pattern worker fix...', 'warning');
                const pattern = await window.workerIntegration.generateBulletPattern({
                    patternType: 'spiral',
                    position: { x: 0, y: 1, z: 0 }, // Serialized position
                    targetPosition: { x: 5, y: 1, z: 5 }, // Serialized target
                    intensity: 0.8,
                    speedMultiplier: 1.0,
                    customParams: { arms: 4 }
                });
                
                if (pattern && pattern.bullets && pattern.bullets.length > 0) {
                    addResult(`✅ Bullet pattern fix working: Generated ${pattern.bullets.length} bullets`, 'success');
                } else {
                    addResult('❌ Bullet pattern fix failed: No bullets generated', 'error');
                }
            } catch (error) {
                addResult(`❌ Bullet pattern fix failed: ${error.message}`, 'error');
            }
            
            // Test 2: Mesh Processing Worker Fix
            try {
                addResult('🧊 Testing mesh processing worker fix...', 'warning');
                const result = await window.workerIntegration.processMesh({
                    voxels: [
                        { position: { x: 0, y: 0, z: 0 }, color: { r: 1, g: 0, b: 0 }, size: 1 },
                        { position: { x: 1, y: 0, z: 0 }, color: { r: 0, g: 1, b: 0 }, size: 1 },
                        { position: { x: 0, y: 1, z: 0 }, color: { r: 0, g: 0, b: 1 }, size: 1 }
                    ],
                    voxelScale: 1.0
                }, 'processVoxelMesh');
                
                if (result && result.meshData && result.meshData.length > 0) {
                    addResult(`✅ Mesh processing fix working: Processed ${result.meshData.length} mesh groups`, 'success');
                } else {
                    addResult('❌ Mesh processing fix failed: No mesh data generated', 'error');
                }
            } catch (error) {
                addResult(`❌ Mesh processing fix failed: ${error.message}`, 'error');
            }
            
            // Test 3: Pathfinding Worker
            try {
                addResult('🗺️ Testing pathfinding worker...', 'warning');
                const path = await window.workerIntegration.findPath({
                    start: { x: 0, y: 0, z: 0 },
                    end: { x: 3, y: 0, z: 3 },
                    obstacles: [],
                    gridSize: 1.0
                });
                
                if (Array.isArray(path) && path.length > 0) {
                    addResult(`✅ Pathfinding working: Found path with ${path.length} waypoints`, 'success');
                } else {
                    addResult('❌ Pathfinding failed: No path found', 'error');
                }
            } catch (error) {
                addResult(`❌ Pathfinding failed: ${error.message}`, 'error');
            }
            
            // Test 4: Animation Worker
            try {
                addResult('🎬 Testing animation worker...', 'warning');
                const animations = [{
                    id: 'test_anim',
                    type: 'rotation',
                    properties: { from: { x: 0, y: 0, z: 0 }, to: { x: 0, y: Math.PI, z: 0 } },
                    duration: 1000,
                    loop: true,
                    startTime: Date.now()
                }];
                
                const updates = await window.workerIntegration.processAnimations(
                    animations, 
                    0.016, 
                    Date.now() * 0.001
                );
                
                if (Array.isArray(updates) && updates.length > 0) {
                    addResult(`✅ Animation processing working: Processed ${updates.length} animations`, 'success');
                } else {
                    addResult('❌ Animation processing failed: No updates generated', 'error');
                }
            } catch (error) {
                addResult(`❌ Animation processing failed: ${error.message}`, 'error');
            }
            
            // Test 5: Performance Metrics
            try {
                addResult('📊 Testing performance metrics...', 'warning');
                const metrics = await window.workerIntegration.getPerformanceMetrics();
                
                if (metrics && typeof metrics === 'object' && !metrics.error) {
                    const workerCount = Object.keys(metrics.workers || {}).length;
                    addResult(`✅ Performance metrics working: Monitoring ${workerCount} worker types`, 'success');
                } else {
                    addResult('❌ Performance metrics failed: Invalid response', 'error');
                }
            } catch (error) {
                addResult(`❌ Performance metrics failed: ${error.message}`, 'error');
            }
            
            // Test 6: Error Handling (Expected to fail)
            try {
                addResult('🚨 Testing error handling (expected to fail)...', 'warning');
                await window.workerIntegration.processMesh({ invalid: 'data' }, 'processVoxelMesh');
                addResult('❌ Error handling test failed: Should have thrown an error', 'error');
            } catch (error) {
                if (error.message.includes('Invalid') || error.message.includes('voxel') || error.message.includes('undefined')) {
                    addResult('✅ Error handling working: Correctly rejected invalid data', 'success');
                } else {
                    addResult(`❌ Error handling unexpected: ${error.message}`, 'error');
                }
            }
            
            addResult('🎉 All tests completed! Check results above.', 'success');
        };
        
        // Auto-run tests after page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.workerIntegration) {
                    runAllTests();
                } else {
                    addResult('⏳ Waiting for worker system to initialize...', 'warning');
                    setTimeout(runAllTests, 3000);
                }
            }, 1000);
        });
    </script>
</body>
</html>
