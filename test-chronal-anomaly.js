/**
 * Test script for Chronal Anomaly Event Room
 * Verifies import resolution, registration, validation, and handler instantiation
 */

// Test 1: Import resolution
console.log('🧪 TEST 1: Import Resolution');
try {
    // Test importing the event rooms index
    const { EVENT_ROOMS, getAvailableEventRooms, getEventRoom, validateEventRoom } = await import('./src/gameData/eventRooms/index.js');
    console.log('✅ Successfully imported event rooms index');
    
    // Test importing the specific room file
    const chronalAnomalyModule = await import('./src/gameData/eventRooms/chronalAnomaly.js');
    console.log('✅ Successfully imported chronalAnomaly.js');
    console.log('Module structure:', Object.keys(chronalAnomalyModule));
    console.log('Default export keys:', Object.keys(chronalAnomalyModule.default || {}));
    
} catch (error) {
    console.error('❌ Import failed:', error);
}

console.log('\n🧪 TEST 2: Room Registration');
try {
    const { EVENT_ROOMS, getAvailableEventRooms } = await import('./src/gameData/eventRooms/index.js');
    
    console.log('Available event rooms:', getAvailableEventRooms());
    console.log('EVENT_ROOMS registry keys:', Object.keys(EVENT_ROOMS));
    
    // Check if chronal_anomaly is in the available rooms
    const availableRooms = getAvailableEventRooms();
    const isChronalAnomalyAvailable = availableRooms.includes('chronal_anomaly');
    
    if (isChronalAnomalyAvailable) {
        console.log('✅ Chronal Anomaly is properly registered and discoverable');
    } else {
        console.log('❌ Chronal Anomaly is NOT in available rooms list');
        console.log('Available rooms:', availableRooms);
    }
    
} catch (error) {
    console.error('❌ Registration test failed:', error);
}

console.log('\n🧪 TEST 3: Room Data Validation');
try {
    const { getEventRoom, validateEventRoom } = await import('./src/gameData/eventRooms/index.js');
    
    const roomData = getEventRoom('chronal_anomaly');
    
    if (roomData) {
        console.log('✅ Room data retrieved successfully');
        console.log('Room ID:', roomData.id);
        console.log('Room Name:', roomData.name);
        console.log('Room Shape:', roomData.shape);
        console.log('Objects count:', roomData.objects?.length || 0);
        
        const validation = validateEventRoom(roomData);
        if (validation.valid) {
            console.log('✅ Room data validation passed');
        } else {
            console.log('❌ Room data validation failed:', validation.error);
        }
    } else {
        console.log('❌ Failed to retrieve room data for chronal_anomaly');
    }
    
} catch (error) {
    console.error('❌ Validation test failed:', error);
}

console.log('\n🧪 TEST 4: Handler Class Validation');
try {
    const chronalAnomalyModule = await import('./src/gameData/eventRooms/chronalAnomaly.js');
    const HandlerClass = chronalAnomalyModule.default?.handler;
    
    if (HandlerClass) {
        console.log('✅ Handler class found');
        console.log('Handler class name:', HandlerClass.name);
        
        // Test instantiation (with mock parameters)
        const mockDungeonHandler = { 
            currentRoomGroup: null,
            eventRoomManager: null,
            audioManager: null 
        };
        const mockRoomData = { id: 'chronal_anomaly', name: 'Test Room' };
        
        try {
            const handlerInstance = new HandlerClass(123, mockDungeonHandler, mockRoomData);
            console.log('✅ Handler instance created successfully');
            console.log('Instance constructor name:', handlerInstance.constructor.name);
            console.log('Instance state keys:', Object.keys(handlerInstance.state || {}));
            
            // Test if key methods exist
            const requiredMethods = ['handleInteraction', 'handleEnemyDefeat'];
            const methodsExist = requiredMethods.every(method => typeof handlerInstance[method] === 'function');
            
            if (methodsExist) {
                console.log('✅ Required methods exist');
            } else {
                console.log('❌ Some required methods missing');
            }
            
        } catch (instError) {
            console.error('❌ Handler instantiation failed:', instError);
        }
        
    } else {
        console.log('❌ Handler class not found in module export');
        console.log('Available exports:', Object.keys(chronalAnomalyModule.default || {}));
    }
    
} catch (error) {
    console.error('❌ Handler class test failed:', error);
}

console.log('\n🧪 TEST 5: Object Placement Validation');
try {
    const { getEventRoom } = await import('./src/gameData/eventRooms/index.js');
    const roomData = getEventRoom('chronal_anomaly');
    
    if (roomData && roomData.objects) {
        console.log('✅ Objects array found');
        console.log(`Total objects: ${roomData.objects.length}`);
        
        // Check for key objects
        const keyObjects = ['central_conduit', 'temporal_orb_north', 'temporal_orb_east', 'temporal_orb_south', 'temporal_orb_west'];
        const missingObjects = [];
        
        keyObjects.forEach(objectId => {
            const found = roomData.objects.some(obj => obj.userData?.objectId === objectId);
            if (!found) {
                missingObjects.push(objectId);
            }
        });
        
        if (missingObjects.length === 0) {
            console.log('✅ All key objects are properly defined');
        } else {
            console.log('❌ Missing key objects:', missingObjects);
        }
        
        // Validate object structure
        const invalidObjects = roomData.objects.filter(obj => 
            !obj.type || !obj.position || !obj.userData?.objectId
        );
        
        if (invalidObjects.length === 0) {
            console.log('✅ All objects have valid structure');
        } else {
            console.log('❌ Some objects have invalid structure:', invalidObjects.length);
        }
        
    } else {
        console.log('❌ No objects array found in room data');
    }
    
} catch (error) {
    console.error('❌ Object placement validation failed:', error);
}

console.log('\n🧪 TEST 6: EventRoomManager Integration');
try {
    // Test if EventRoomManager can handle the conversion
    const EventRoomManagerModule = await import('./src/systems/EventRoomManager.js');
    const { EventRoomManager } = EventRoomManagerModule;
    
    console.log('✅ EventRoomManager imported successfully');
    
    // Test the room ID to filename conversion
    const mockManager = new EventRoomManager(null, null);
    const filename = mockManager.convertRoomIdToFilename('chronal_anomaly');
    
    console.log('Converted filename:', filename);
    
    if (filename === 'chronalAnomaly.js') {
        console.log('✅ Room ID to filename conversion works correctly');
    } else {
        console.log('❌ Room ID to filename conversion failed');
        console.log('Expected: chronalAnomaly.js, Got:', filename);
    }
    
} catch (error) {
    console.error('❌ EventRoomManager integration test failed:', error);
}

console.log('\n🎯 SUMMARY:');
console.log('Test completed. Check above for any ❌ failures that need to be addressed.');