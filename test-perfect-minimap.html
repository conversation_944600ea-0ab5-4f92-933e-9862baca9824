<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfect Minimap Connections Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .minimap-container {
            background: #333;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .minimap-container svg {
            background: #444;
            border: 1px solid #666;
            border-radius: 5px;
        }
        
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .info {
            color: #2196F3;
            margin: 10px 0;
        }
        
        button {
            margin: 5px;
            padding: 8px 16px;
            background: #555;
            color: #fff;
            border: 1px solid #777;
            cursor: pointer;
            border-radius: 3px;
        }
        
        button:hover {
            background: #666;
        }
    </style>
</head>
<body>
    <h1>🎯 Perfect Minimap Connections Test</h1>
    <div class="success">✅ All fixes applied: U-shapes render correctly + connections align perfectly</div>
    
    <div class="test-container">
        <div class="minimap-container">
            <h3>Before Fixes</h3>
            <div class="info">❌ Issues: T-shapes, gaps, misaligned connections</div>
            <div id="before-minimap"></div>
        </div>
        
        <div class="minimap-container">
            <h3>After Fixes</h3>
            <div class="success">✅ Perfect: Proper U-shapes, seamless connections</div>
            <div id="after-minimap"></div>
        </div>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <button onclick="showComplexLayout()">Complex Layout Test</button>
        <button onclick="showAllShapes()">All U-Shapes Test</button>
        <button onclick="showConnectionTest()">Connection Test</button>
    </div>
    
    <script>
        // Create room shape SVG with FIXED paths and positioning
        function createRoomShapeSVG(shapeKey, room, cellSize = 16) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'U_SHAPE_DOWN':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    // FIXED: Proper ∪ shape
                    svgContent = `
                        <path d="M0,0 L${cellSize},0 L${cellSize},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${width},0 L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_UP':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    // FIXED: Proper ∩ shape
                    svgContent = `
                        <path d="M0,${cellSize} L0,${height} L${width},${height} L${width},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${cellSize},0 L${cellSize},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_LEFT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // FIXED: Proper ⊂ shape
                    svgContent = `
                        <path d="M${width},0 L0,0 L0,${height} L${width},${height} L${width},${cellSize * 2} L${cellSize},${cellSize * 2} L${cellSize},${cellSize} L${width},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_RIGHT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    // FIXED: Proper ⊃ shape
                    svgContent = `
                        <path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${cellSize * 2} L${width},${cellSize * 2} L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'RECT_3X1':
                    width = cellSize * 3;
                    height = cellSize;
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
                
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function createMinimap(rooms, containerId, title, useFixedPositioning = true) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<h4>${title}</h4>`;
            
            // Find bounds
            let minX = rooms[0].coords.x, maxX = rooms[0].coords.x;
            let minY = rooms[0].coords.y, maxY = rooms[0].coords.y;
            
            rooms.forEach(room => {
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            });
            
            const cellSize = 16;
            const gridUnit = cellSize * 1.8;
            const mapWidth = (maxX - minX + 1) * gridUnit + cellSize * 6;
            const mapHeight = (maxY - minY + 1) * gridUnit + cellSize * 6;
            
            // Create SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)';
            svg.style.margin = '0 auto';
            
            // Add rooms
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, room, cellSize);
                
                const baseX = (room.coords.x - minX) * gridUnit + cellSize * 2; 
                const baseY = (maxY - room.coords.y) * gridUnit + cellSize * 2;
                
                let gridX = baseX;
                let gridY = baseY;
                
                if (useFixedPositioning) {
                    // FIXED positioning - align door connections
                    if (shapeKey === 'SQUARE_1X1') {
                        gridX = baseX;
                        gridY = baseY;
                    } else if (shapeKey === 'U_SHAPE_DOWN') {
                        gridX = baseX - cellSize * 1.5; // Center opening at baseX
                        gridY = baseY - cellSize * 2; // Bottom edge at baseY
                    } else if (shapeKey === 'U_SHAPE_UP') {
                        gridX = baseX - cellSize * 1.5; // Center opening at baseX
                        gridY = baseY; // Top edge at baseY
                    } else if (shapeKey === 'U_SHAPE_LEFT') {
                        gridX = baseX; // Left edge at baseX
                        gridY = baseY - cellSize * 1.5; // Center opening at baseY
                    } else if (shapeKey === 'U_SHAPE_RIGHT') {
                        gridX = baseX - cellSize * 2; // Right edge at baseX
                        gridY = baseY - cellSize * 1.5; // Center opening at baseY
                    } else if (shapeKey === 'RECT_3X1') {
                        gridX = baseX - cellSize; // Center horizontally
                        gridY = baseY - cellSize * 0.5; // Center vertically
                    }
                } else {
                    // OLD positioning - centered, causes gaps
                    if (shapeKey === 'U_SHAPE_DOWN' || shapeKey === 'U_SHAPE_UP') {
                        gridX = baseX - cellSize;
                        gridY = baseY;
                    } else if (shapeKey === 'U_SHAPE_LEFT' || shapeKey === 'U_SHAPE_RIGHT') {
                        gridX = baseX;
                        gridY = baseY - cellSize;
                    } else if (shapeKey === 'RECT_3X1') {
                        gridX = baseX;
                        gridY = baseY;
                    }
                }
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                // Color coding
                let roomColor = '#8a8a8a';
                if (room.id === 0) roomColor = '#ffffff';
                else if (room.type === 'Boss') roomColor = '#ff3333';
                else if (room.type === 'Event') roomColor = '#3399ff';
                else if (room.type === 'Start') roomColor = '#33ff33';
                
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                svg.appendChild(roomGroup);
            });
            
            container.appendChild(svg);
        }
        
        function showComplexLayout() {
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 2 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 2, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 3, y: 2 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 2, y: 3 }, visited: true },
                { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 1, y: 2 }, visited: true },
                { id: 5, type: 'Normal', shapeKey: 'RECT_3X1', coords: { x: 2, y: 4 }, visited: true },
                { id: 6, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 5 }, visited: true }
            ];
            
            createMinimap(rooms, 'before-minimap', 'Before: Gaps & T-shapes', false);
            createMinimap(rooms, 'after-minimap', 'After: Perfect connections', true);
        }
        
        function showAllShapes() {
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 1, y: 0 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 2, y: 0 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 0, y: 1 }, visited: true },
                { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 1, y: 1 }, visited: true },
                { id: 5, type: 'Boss', shapeKey: 'RECT_3X1', coords: { x: 2, y: 1 }, visited: true }
            ];
            
            createMinimap(rooms, 'before-minimap', 'Before: T-shapes everywhere', false);
            createMinimap(rooms, 'after-minimap', 'After: All proper U-shapes', true);
        }
        
        function showConnectionTest() {
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 1, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 2 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 2, y: 2 }, visited: true },
                { id: 4, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 2 }, visited: true }
            ];
            
            createMinimap(rooms, 'before-minimap', 'Before: Misaligned connections', false);
            createMinimap(rooms, 'after-minimap', 'After: Perfect alignment', true);
        }
        
        // Show complex layout by default
        showComplexLayout();
    </script>
</body>
</html>