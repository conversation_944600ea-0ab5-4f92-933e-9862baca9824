# Card Implementation Guide

This guide explains the precise step-by-step process for adding a new card to the Soulpath game. Follow these steps exactly to ensure proper integration with all game systems.

## Overview

Adding a new card requires modifications to three core files:
1. `src/entities/ItemTypes.js` - Card data definition
2. `src/systems/CardSystem.js` - Card effect implementation  
3. `src/entities/Card.js` - Card artwork and animation

## Step 1: Add Card Constant to ItemTypes.js

### Location: `/src/entities/ItemTypes.js`

1. **Find the ITEM_TYPES object** (around line 26)
2. **Add your card constant** before the closing brace:
   ```javascript
   WILD_MAGIC: 'wild_magic',
   GAMBLERS_LUCK: 'gamblers_luck',
   ASTRAL_RECALL: 'astral_recall'  // <-- Add here
   };
   ```

## Step 2: Add Card Data Definition

### Location: Same file, ITEM_DATA object (around line 95)

1. **Find the end of the ITEM_DATA object** (around line 1247)
2. **Add your card data** before the closing brace:
   ```javascript
   gamblers_luck: {
       // ... existing card data
   },

   astral_recall: {
       name: 'Astral Recall',
       description: 'Instantly teleports you back to the entrance of the current dungeon floor through astral projection. Emergency escape when overwhelmed by danger.',
       category: ITEM_CATEGORY.CARD,
       rarity: ITEM_RARITY.RARE,
       effect: 'astral_recall',
       effectValue: 1, // Single use teleportation
       cardType: 'mobility',
       voxelModel: 'astral_recall_card',
       allowedBiomes: ['any'],
       allowedRoomTypes: ['any'],
       glow: {
           color: 0x9370DB, // Astral purple glow
           intensity: 1.4
       },
       blockedBy: [],
       soulWeightInfluence: 'balanced',
       // Card-specific properties
       cardBorderColor: 0x9370DB, // Astral purple border (rare)
       cardAnimation: 'astral_recall'
   }
   };
   ```

### Required Properties:
- **name**: Display name
- **description**: Card description text
- **category**: Always `ITEM_CATEGORY.CARD`
- **rarity**: `COMMON`, `RARE`, `EPIC`, or `LEGENDARY`
- **effect**: Unique effect identifier (used in CardSystem.js)
- **effectValue**: Numeric value for the effect
- **cardType**: Category like 'attack', 'mobility', 'utility', etc.
- **voxelModel**: Card model identifier
- **glow**: Color and intensity for card glow
- **cardBorderColor**: Border color (usually matches glow)
- **cardAnimation**: Animation identifier (matches cardType)

## Step 3: Add Effect Case to CardSystem.js

### Location: `/src/systems/CardSystem.js`

1. **Find the executeCardEffect method** (around line 530)
2. **Find the switch statement** with card effects
3. **Add your case** before the default case:
   ```javascript
   case 'gamblers_luck':
       return this.executeGamblersLuck(card, targetPosition);
   
   case 'astral_recall':  // <-- Add here
       return this.executeAstralRecall(card, targetPosition);
   
   default:
   ```

## Step 4: Implement Card Effect Method

### Location: Same file, end of class (around line 13300)

1. **Find the end of the CardSystem class** (before the closing brace)
2. **Add your effect method** before the `dispose()` method:
   ```javascript
   /**
    * Execute Astral Recall effect - teleport player to dungeon entrance
    * @param {Card} card - The Astral Recall card being played
    * @param {Object} targetPosition - Target position (not used for this effect)
    * @returns {boolean} Success status
    */
   executeAstralRecall(card, targetPosition) {
       console.log('[CardSystem] Executing Astral Recall - Emergency teleportation to safety');
       
       if (!this.dungeonHandler || !this.dungeonHandler.playerController) {
           console.warn('[CardSystem] Cannot execute Astral Recall - missing dungeon handler or player controller');
           return false;
       }

       const playerController = this.dungeonHandler.playerController;
       const player = playerController.playerMesh;
       
       if (!player) {
           console.warn('[CardSystem] Cannot execute Astral Recall - player mesh not found');
           return false;
       }

       // Create astral projection effect at current position
       this.createAstralProjectionEffect(player.position);
       
       // Get spawn position (dungeon entrance)
       const spawnPosition = this.getSpawnPosition();
       
       // Create arrival effect at spawn position
       this.createAstralArrivalEffect(spawnPosition);
       
       // Teleport player with fade effect
       this.teleportPlayerWithEffect(player, spawnPosition);
       
       // Play astral sound effect
       if (this.audioManager) {
           this.audioManager.playSound('magic_teleport', false, 0.8);
       }
       
       console.log('[CardSystem] ✨ Astral projection complete - returned to safety!');
       
       return true;
   }
   ```

### Effect Method Requirements:
- **Validation**: Check for required objects (dungeonHandler, player, etc.)
- **Visual Effects**: Create appropriate visual effects using voxel-style geometry
- **Game Logic**: Implement the actual card effect
- **Audio**: Play appropriate sound effects
- **Return Value**: Return true if successful, false if failed
- **Logging**: Add console logs for debugging

## Step 5: Add Helper Methods (if needed)

Add any helper methods your effect requires:

```javascript
/**
 * Create astral projection effect at departure position
 * @param {THREE.Vector3} position - Position where effect occurs
 */
createAstralProjectionEffect(position) {
    // Implementation using voxel-style geometry
    // Use BoxGeometry, CylinderGeometry with low segment counts
    // Avoid smooth shapes like ConeGeometry and RingGeometry
}

/**
 * Get spawn position (dungeon entrance)
 * @returns {THREE.Vector3} Spawn position
 */
getSpawnPosition() {
    return new THREE.Vector3(0, 0.5, 6); // Default spawn position
}
```

## Step 6: Add Card Artwork Case to Card.js

### Location: `/src/entities/Card.js`

1. **Find the createCardArtwork method** (around line 70)
2. **Find the card type conditions** (around line 200)
3. **Add your card case** before the closing brace:
   ```javascript
   else if (this.cardType === 'gamblers_luck') {
       this.createGamblersLuckArtwork(cardWidth, cardHeight);
   }
   else if (this.cardType === 'astral_recall') {  // <-- Add here
       this.createAstralRecallArtwork(cardWidth, cardHeight);
   }
   }
   ```

## Step 7: Implement Card Artwork Method

### Location: Same file, after existing artwork methods (around line 1177)

1. **Find the end of the last artwork method** (e.g., `createGamblersLuckArtwork`)
2. **Add your artwork method**:
   ```javascript
   /**
    * Create astral voxel artwork for Astral Recall card
    */
   createAstralRecallArtwork(cardWidth, cardHeight) {
       const voxelSize = 0.08;
       
       // Define colors
       const astralPurple = 0x9370DB;
       const spiritBlue = 0x4169E1;
       const etherealWhite = 0xFFFFFF;
       // ... more colors
       
       // Create materials
       const astralPurpleMaterial = new THREE.MeshLambertMaterial({
           color: astralPurple,
           emissive: astralPurple,
           emissiveIntensity: 0.9,
           transparent: true,
           opacity: 0.8
       });
       // ... more materials
       
       // Create animated groups
       this.astralPortalGroup = new THREE.Group();
       this.astralStarsGroup = new THREE.Group();
       // ... more groups
       
       // Define voxel patterns
       const astralPortalPattern = [
           [0, 0], [0.08, 0], [-0.08, 0], [0, 0.08], [0, -0.08],
           // ... more coordinates
       ];
       
       // Create voxels from patterns
       astralPortalPattern.forEach((voxel, index) => {
           const [x, y] = voxel;
           const mesh = new THREE.Mesh(
               new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
               material.clone()
           );
           mesh.position.set(x, y, 0.06);
           mesh.name = 'portalVoxel';
           this.astralPortalGroup.add(mesh);
       });
       
       // Add groups to card
       this.cardModel.add(this.astralPortalGroup);
       // ... add all groups
       
       // Store animation properties
       this.astralRecallAnimationPhase = 0;
       this.portalSpin = 0;
       // ... more animation properties
       
       console.log(`[Card] Created Astral Recall artwork with dimensional portal, orbiting stars, astral rings, and mystic runes`);
   }
   ```

### Artwork Method Requirements:
- **Voxel Size**: Use consistent `voxelSize = 0.08`
- **Colors**: Define thematic color palette
- **Materials**: Use `MeshLambertMaterial` with emissive properties
- **Groups**: Create separate groups for different animated elements
- **Patterns**: Define voxel positions as coordinate arrays
- **Voxel Creation**: Use `BoxGeometry` for all voxels (avoid smooth shapes)
- **Positioning**: Place voxels at `z = 0.06` to appear on card surface
- **Animation Properties**: Initialize animation variables
- **Naming**: Give meaningful names to groups and meshes

## Step 8: Add Card Animation

### Location: Same file, updateAnimation method (around line 11600)

1. **Find the card animation section** in the `updateAnimation` method
2. **Find the last card animation** (e.g., gamblers_luck)
3. **Add your animation** after the closing brace:
   ```javascript
   // Gambler's Luck animation
   if (this.cardType === 'gamblers_luck' && this.gamblersLuckAnimationPhase !== undefined) {
       // ... existing animation
   }
   
   // Astral Recall animation  // <-- Add here
   if (this.cardType === 'astral_recall' && this.astralRecallAnimationPhase !== undefined) {
       this.astralRecallAnimationPhase += deltaTime * 3.0; // Astral activity speed
       this.portalSpin += deltaTime * 4.0; // Portal spinning speed
       // ... more animation variables
       
       const astralTime = this.astralRecallAnimationPhase;
       const portalSpin = this.portalSpin;
       // ... get animation variables
       
       // Animate astral portal (center vortex spinning)
       const astralPortalGroup = this.cardModel.getObjectByName('astralPortal');
       if (astralPortalGroup) {
           astralPortalGroup.rotation.z = portalSpin;
           
           // Portal energy pulsing
           astralPortalGroup.children.forEach((mesh, index) => {
               const portalPhase = index * 0.2; // Stagger per voxel
               const portalPhaseTime = portalSpin + portalPhase;
               
               // Animate properties
               const portalIntensity = 0.7 + Math.sin(portalPhaseTime * 3.0) * 0.4;
               
               if (mesh.material) {
                   mesh.material.emissiveIntensity = portalIntensity;
               }
           });
       }
       
       // ... animate other groups
       
       // Overall card presence
       const astralPulse = 1 + Math.sin(astralTime * 2.5) * 0.08;
       const astralRecallScale = this.cardModel.scale.x;
       this.cardModel.scale.setScalar(astralRecallScale * astralPulse);
   }
   ```

### Animation Requirements:
- **Phase Variables**: Update animation counters with deltaTime
- **Group Access**: Use `getObjectByName()` to access groups
- **Staggered Effects**: Use index-based phase offsets for variety
- **Smooth Motion**: Use sine/cosine functions for natural movement
- **Property Animation**: Animate emissiveIntensity, opacity, rotation, position
- **Overall Presence**: Add subtle card-wide effects

## Step 9: Testing

1. **Open the game** in browser at `localhost:8000`
2. **Enter dungeon mode**
3. **Use console command**: `give('astral_recall')`
4. **Test card functionality**:
   - Verify card appears in hand with proper artwork
   - Check card animations are working
   - Drag and release card to test 3D flight animation
   - Verify card effect executes properly
   - Check visual effects appear correctly

## Important Design Guidelines

### Voxel Art Style
- **Use BoxGeometry only** for voxels - no smooth shapes
- **Consistent voxel size** - always use 0.08
- **Layered patterns** - create depth with multiple z-levels
- **Emissive materials** - make voxels glow appropriately

### Animation Principles
- **Smooth motion** - use sine/cosine functions
- **Staggered timing** - offset animations per voxel
- **Subtle effects** - don't overdo the animation
- **Performance conscious** - avoid expensive operations

### Code Standards
- **Consistent naming** - use descriptive variable names
- **Proper logging** - add console.log statements for debugging
- **Error handling** - validate required objects exist
- **Documentation** - add JSDoc comments for methods

## Common Pitfalls

1. **Forgetting to add the constant** in ITEM_TYPES
2. **Missing the case** in the switch statement
3. **Using smooth geometry** instead of voxel BoxGeometry
4. **Not initializing animation variables** in artwork method
5. **Incorrect z-positioning** of voxels (should be 0.06)
6. **Missing group names** for animation access
7. **Not checking if groups exist** before animating

## File Structure Summary

```
src/
├── entities/
│   ├── ItemTypes.js      # Step 1-2: Add constant and data
│   └── Card.js           # Step 6-8: Add artwork and animation
└── systems/
    └── CardSystem.js     # Step 3-5: Add effect implementation
```

Follow this guide precisely for consistent, working card implementations that integrate properly with all game systems.