<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Fixes Verification</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .comparison {
            display: flex;
            gap: 30px;
            margin: 20px 0;
            align-items: flex-start;
        }
        
        .minimap-container {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            min-width: 300px;
        }
        
        .minimap-container h4 {
            margin: 0 0 10px 0;
            color: #fff;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #333;
            border-radius: 8px;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #555;
            color: #fff;
            border: 1px solid #777;
            cursor: pointer;
            border-radius: 4px;
        }
        
        button:hover { background: #666; }
    </style>
</head>
<body>
    <h1>🔧 Connection Fixes Verification</h1>
    <p class="success">✅ Applied fixes for L_SHAPE, T_SHAPE, and CROSS_SHAPE positioning</p>
    
    <div style="text-align: center; margin: 20px 0;">
        <button onclick="testBeforeAfter()">Before/After Comparison</button>
        <button onclick="testLShapeConnections()">L-Shape Tests</button>
        <button onclick="testTShapeConnections()">T-Shape Tests</button>
        <button onclick="testCrossShapeConnections()">Cross-Shape Tests</button>
        <button onclick="testComplexLayout()">Complex Layout</button>
    </div>
    
    <div id="test-results"></div>
    
    <script>
        // FIXED positioning logic
        function calculatePositionFixed(shapeKey, baseX, baseY, cellSize) {
            let gridX = baseX;
            let gridY = baseY;
            
            if (shapeKey === 'SQUARE_1X1') {
                gridX = baseX;
                gridY = baseY;
            } else if (shapeKey === 'L_SHAPE') {
                // FIXED: Inner corner aligns with connection point
                gridX = baseX - cellSize; // Inner corner at (cellSize, cellSize)
                gridY = baseY - cellSize; // Align inner corner to grid point
            } else if (shapeKey === 'T_SHAPE') {
                // FIXED: Top center stem connection at grid point
                gridX = baseX - cellSize * 1.5; // Center the 3-wide shape so center is at baseX
                gridY = baseY; // Top edge at baseY (stem connection point)
            } else if (shapeKey === 'CROSS_SHAPE') {
                // FIXED: Center at grid point for 4-way connections
                gridX = baseX - cellSize * 1.5; // Center the 3-wide shape at baseX
                gridY = baseY - cellSize * 1.5; // Center the 3-tall shape at baseY
            } else if (shapeKey === 'U_SHAPE_DOWN') {
                gridX = baseX - cellSize * 1.5;
                gridY = baseY - cellSize * 2;
            } else if (shapeKey === 'U_SHAPE_UP') {
                gridX = baseX - cellSize * 1.5;
                gridY = baseY;
            } else if (shapeKey === 'U_SHAPE_LEFT') {
                gridX = baseX;
                gridY = baseY - cellSize * 1.5;
            } else if (shapeKey === 'U_SHAPE_RIGHT') {
                gridX = baseX - cellSize * 2;
                gridY = baseY - cellSize * 1.5;
            } else if (shapeKey === 'RECT_3X1') {
                gridX = baseX - cellSize;
                gridY = baseY - cellSize * 0.5;
            } else if (shapeKey === 'RECT_1X2') {
                gridX = baseX - cellSize * 0.5;
                gridY = baseY;
            } else if (shapeKey === 'RECT_1X3') {
                gridX = baseX - cellSize * 0.5;
                gridY = baseY - cellSize;
            } else if (shapeKey === 'RECT_2X1') {
                gridX = baseX;
                gridY = baseY - cellSize * 0.5;
            } else if (shapeKey === 'SQUARE_2X2') {
                gridX = baseX - cellSize * 0.5;
                gridY = baseY - cellSize * 0.5;
            }
            
            return { gridX, gridY };
        }
        
        // OLD positioning logic
        function calculatePositionOld(shapeKey, baseX, baseY, cellSize) {
            let gridX = baseX;
            let gridY = baseY;
            
            if (shapeKey === 'L_SHAPE') {
                gridX = baseX; // OLD: Corner at grid point
                gridY = baseY;
            } else if (shapeKey === 'T_SHAPE') {
                gridX = baseX - cellSize; // OLD: Wrong centering
                gridY = baseY - cellSize; // OLD: Wrong vertical position
            } else if (shapeKey === 'CROSS_SHAPE') {
                gridX = baseX - cellSize; // OLD: Wrong centering
                gridY = baseY - cellSize;
            }
            // ... other shapes same as fixed version
            
            return { gridX, gridY };
        }
        
        function createRoomShapeSVG(shapeKey, cellSize = 16) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'L_SHAPE':
                    width = cellSize * 2;
                    height = cellSize * 2;
                    svgContent = `<path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${height} L0,${height} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'T_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `<path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${height} L0,${height} L0,${cellSize} L${cellSize},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'CROSS_SHAPE':
                    width = cellSize * 3;
                    height = cellSize * 3;
                    svgContent = `<path d="M${cellSize},0 L${cellSize * 2},0 L${cellSize * 2},${cellSize} L${width},${cellSize} L${width},${cellSize * 2} L${cellSize * 2},${cellSize * 2} L${cellSize * 2},${height} L${cellSize},${height} L${cellSize},${cellSize * 2} L0,${cellSize * 2} L0,${cellSize} L${cellSize},${cellSize} Z" fill="currentColor" stroke="none"/>`;
                    break;
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function createMinimap(rooms, title, useFixedPositioning = true) {
            const cellSize = 18;
            const gridUnit = cellSize * 1.8;
            
            // Find bounds
            let minX = Math.min(...rooms.map(r => r.coords.x));
            let maxX = Math.max(...rooms.map(r => r.coords.x));
            let minY = Math.min(...rooms.map(r => r.coords.y));
            let maxY = Math.max(...rooms.map(r => r.coords.y));
            
            const mapWidth = (maxX - minX + 1) * gridUnit + cellSize * 6;
            const mapHeight = (maxY - minY + 1) * gridUnit + cellSize * 6;
            
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)';
            svg.style.background = '#333';
            
            // Add grid lines
            for (let x = 0; x <= maxX - minX; x++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridX = x * gridUnit + cellSize * 2;
                line.setAttribute('x1', gridX);
                line.setAttribute('y1', 0);
                line.setAttribute('x2', gridX);
                line.setAttribute('y2', mapHeight);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.3');
                svg.appendChild(line);
            }
            
            for (let y = 0; y <= maxY - minY; y++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridY = y * gridUnit + cellSize * 2;
                line.setAttribute('x1', 0);
                line.setAttribute('y1', gridY);
                line.setAttribute('x2', mapWidth);
                line.setAttribute('y2', gridY);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.3');
                svg.appendChild(line);
            }
            
            // Add rooms
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, cellSize);
                
                const baseX = (room.coords.x - minX) * gridUnit + cellSize * 2;
                const baseY = (maxY - room.coords.y) * gridUnit + cellSize * 2;
                
                const { gridX, gridY } = useFixedPositioning ? 
                    calculatePositionFixed(shapeKey, baseX, baseY, cellSize) :
                    calculatePositionOld(shapeKey, baseX, baseY, cellSize);
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                let roomColor = room.type === 'Start' ? '#4CAF50' : '#8a8a8a';
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                // Add connection point markers for visualization
                const connectionPoint = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                connectionPoint.setAttribute('cx', width/2);
                connectionPoint.setAttribute('cy', height/2);
                connectionPoint.setAttribute('r', '2');
                connectionPoint.setAttribute('fill', '#ffff00');
                connectionPoint.setAttribute('stroke', '#000');
                connectionPoint.setAttribute('stroke-width', '1');
                roomGroup.appendChild(connectionPoint);
                
                svg.appendChild(roomGroup);
            });
            
            return svg;
        }
        
        function testBeforeAfter() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🔄 Before/After Connection Fixes</h2>';
            
            const testRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 0, y: 1 } },
                { id: 2, type: 'Normal', shapeKey: 'T_SHAPE', coords: { x: 1, y: 0 } },
                { id: 3, type: 'Normal', shapeKey: 'CROSS_SHAPE', coords: { x: 2, y: 1 } }
            ];
            
            const comparison = document.createElement('div');
            comparison.className = 'comparison';
            
            const beforeContainer = document.createElement('div');
            beforeContainer.className = 'minimap-container';
            beforeContainer.innerHTML = '<h4>Before <span class="error">❌ Gaps</span></h4>';
            beforeContainer.appendChild(createMinimap(testRooms, 'Before', false));
            
            const afterContainer = document.createElement('div');
            afterContainer.className = 'minimap-container';
            afterContainer.innerHTML = '<h4>After <span class="success">✅ Fixed</span></h4>';
            afterContainer.appendChild(createMinimap(testRooms, 'After', true));
            
            comparison.appendChild(beforeContainer);
            comparison.appendChild(afterContainer);
            results.appendChild(comparison);
        }
        
        function testLShapeConnections() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>📐 L-Shape Connection Tests</h2>';
            
            const lShapeTests = [
                [
                    { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 1 } },
                    { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 0, y: 1 } }
                ],
                [
                    { id: 0, type: 'Start', shapeKey: 'L_SHAPE', coords: { x: 1, y: 1 } },
                    { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 1 } },
                    { id: 2, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 } }
                ]
            ];
            
            lShapeTests.forEach((rooms, index) => {
                const container = document.createElement('div');
                container.className = 'minimap-container';
                container.innerHTML = `<h4>L-Shape Test ${index + 1}</h4>`;
                container.appendChild(createMinimap(rooms, `L-Test ${index + 1}`, true));
                results.appendChild(container);
            });
        }
        
        function testTShapeConnections() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🔀 T-Shape Connection Tests</h2>';
            
            const tShapeRooms = [
                { id: 0, type: 'Start', shapeKey: 'T_SHAPE', coords: { x: 1, y: 1 } },
                { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 } }, // Top connection
                { id: 2, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 1 } }, // Left connection
                { id: 3, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 1 } }  // Right connection
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            container.innerHTML = '<h4>T-Shape Hub Connections</h4>';
            container.appendChild(createMinimap(tShapeRooms, 'T-Shape Hub', true));
            results.appendChild(container);
        }
        
        function testCrossShapeConnections() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>✚ Cross-Shape Connection Tests</h2>';
            
            const crossRooms = [
                { id: 0, type: 'Start', shapeKey: 'CROSS_SHAPE', coords: { x: 2, y: 2 } },
                { id: 1, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 1 } }, // Top
                { id: 2, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 2 } }, // Right
                { id: 3, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 3 } }, // Bottom
                { id: 4, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 2 } }  // Left
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            container.innerHTML = '<h4>Cross-Shape 4-Way Hub</h4>';
            container.appendChild(createMinimap(crossRooms, 'Cross Hub', true));
            results.appendChild(container);
        }
        
        function testComplexLayout() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h2>🌟 Complex Layout Test</h2>';
            
            const complexRooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 3 } },
                { id: 1, type: 'Normal', shapeKey: 'L_SHAPE', coords: { x: 2, y: 3 } },
                { id: 2, type: 'Normal', shapeKey: 'T_SHAPE', coords: { x: 3, y: 2 } },
                { id: 3, type: 'Normal', shapeKey: 'CROSS_SHAPE', coords: { x: 4, y: 3 } },
                { id: 4, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 3 } },
                { id: 5, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 1 } },
                { id: 6, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 4 } },
                { id: 7, type: 'Normal', shapeKey: 'SQUARE_1X1', coords: { x: 5, y: 3 } }
            ];
            
            const container = document.createElement('div');
            container.className = 'minimap-container';
            container.innerHTML = '<h4>Complex Multi-Shape Layout</h4>';
            container.appendChild(createMinimap(complexRooms, 'Complex Layout', true));
            results.appendChild(container);
            
            const summary = document.createElement('div');
            summary.className = 'test-section';
            summary.innerHTML = `
                <h3>✅ Connection Fixes Applied</h3>
                <ul>
                    <li><strong>L_SHAPE:</strong> Inner corner now aligns with connection points</li>
                    <li><strong>T_SHAPE:</strong> Top center stem properly aligns with grid points</li>
                    <li><strong>CROSS_SHAPE:</strong> All four connection points centered on grid intersections</li>
                </ul>
                <p class="success"><strong>Result:</strong> All shapes should now connect seamlessly without gaps!</p>
            `;
            results.appendChild(summary);
        }
        
        // Start with before/after comparison
        testBeforeAfter();
    </script>
</body>
</html>