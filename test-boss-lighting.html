<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss Room Lighting Test</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-info {
            background-color: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .highlight {
            color: #4a90e2;
            font-weight: bold;
        }
        .error {
            color: #ff4444;
        }
        .success {
            color: #44ff44;
        }
        .warning {
            color: #ffaa00;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #357abd;
        }
        #game-container {
            width: 800px;
            height: 600px;
            border: 2px solid #444;
            margin: 20px 0;
            position: relative;
        }
        #output {
            background-color: #111;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Boss Room Lighting Test</h1>
    <p>This test checks if boss room static brightness is working correctly.</p>
    
    <div>
        <button onclick="startTest()">Start Test</button>
        <button onclick="teleportToBossRoom()">Teleport to Boss Room</button>
        <button onclick="checkLighting()">Check Current Lighting</button>
    </div>
    
    <div id="game-container"></div>
    
    <div id="output"></div>
    
    <script type="module">
        import * as THREE from 'three';
        import { STATE } from './src/constants.js';
        import SceneManager from './src/core/SceneManager.js';
        
        let sceneManager;
        let dungeonHandler;
        
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const typeClass = type === 'error' ? 'error' : 
                            type === 'success' ? 'success' : 
                            type === 'warning' ? 'warning' : '';
            output.innerHTML += `<div class="${typeClass}">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
            console.log(`[BossLightingTest] ${message}`);
        }
        
        window.startTest = async function() {
            log('Starting boss room lighting test...');
            
            // Create scene manager
            const container = document.getElementById('game-container');
            sceneManager = new SceneManager(container);
            
            // Skip character creation for testing
            window.debugSkipCharacterCreation = true;
            window.debugSkipDungeonDialogue = true;
            
            // Initialize scene manager
            await sceneManager.init();
            
            // Go directly to dungeon
            log('Changing state to DUNGEON...');
            sceneManager.changeState(STATE.DUNGEON);
            
            // Wait for dungeon to load
            setTimeout(() => {
                dungeonHandler = sceneManager.currentHandler;
                if (dungeonHandler) {
                    log('Dungeon loaded successfully', 'success');
                    
                    // Check if boss room exists
                    const bossRooms = Array.from(dungeonHandler.floorLayout.values())
                        .filter(room => room.type === 'Boss');
                    
                    if (bossRooms.length > 0) {
                        log(`Found ${bossRooms.length} boss room(s)`, 'success');
                        bossRooms.forEach(room => {
                            log(`Boss Room ${room.id}:`);
                            log(`  - Name: ${room.bossRoomName || 'Unknown'}`);
                            log(`  - Has bossRoomData: ${!!room.bossRoomData}`);
                            if (room.bossRoomData) {
                                log(`  - staticBrightness: ${room.bossRoomData.staticBrightness}`);
                                log(`  - lighting config: ${JSON.stringify(room.bossRoomData.lighting)}`);
                            }
                        });
                    } else {
                        log('No boss rooms found in dungeon', 'error');
                    }
                } else {
                    log('Failed to get dungeon handler', 'error');
                }
            }, 2000);
        };
        
        window.teleportToBossRoom = function() {
            if (!dungeonHandler) {
                log('Dungeon not loaded yet', 'error');
                return;
            }
            
            const bossRooms = Array.from(dungeonHandler.floorLayout.values())
                .filter(room => room.type === 'Boss');
            
            if (bossRooms.length === 0) {
                log('No boss rooms found', 'error');
                return;
            }
            
            const bossRoom = bossRooms[0];
            log(`Teleporting to boss room ${bossRoom.id}...`);
            
            // Use the internal transition method
            dungeonHandler._transitionToRoom(bossRoom.id, 'north');
            
            // Check lighting after transition
            setTimeout(() => {
                checkLighting();
            }, 1000);
        };
        
        window.checkLighting = function() {
            if (!dungeonHandler) {
                log('Dungeon not loaded yet', 'error');
                return;
            }
            
            log('=== Current Lighting Status ===');
            log(`Current Room ID: ${dungeonHandler.currentRoomId}`);
            
            const currentRoom = dungeonHandler.floorLayout.get(dungeonHandler.currentRoomId);
            if (currentRoom) {
                log(`Room Type: ${currentRoom.type}`);
                log(`Has bossRoomData: ${!!currentRoom.bossRoomData}`);
            }
            
            log(`currentBossRoomData exists: ${!!dungeonHandler.currentBossRoomData}`);
            if (dungeonHandler.currentBossRoomData) {
                log(`Boss Room Name: ${dungeonHandler.currentBossRoomData.name}`);
                log(`staticBrightness: ${dungeonHandler.currentBossRoomData.staticBrightness}`);
                log(`lighting config: ${JSON.stringify(dungeonHandler.currentBossRoomData.lighting)}`);
            }
            
            if (dungeonHandler.ambientLight) {
                log(`Ambient Light Intensity: ${dungeonHandler.ambientLight.intensity.toFixed(3)}`);
                log(`Ambient Light Color: 0x${dungeonHandler.ambientLight.color.getHexString()}`);
                
                // Calculate what the intensity should be for staticBrightness: 2
                const MIN_AMBIENT_INTENSITY = 0.2;
                const MAX_AMBIENT_INTENSITY = 4.0;
                const expectedIntensity = THREE.MathUtils.mapLinear(2, 1, 10, MIN_AMBIENT_INTENSITY, MAX_AMBIENT_INTENSITY);
                log(`Expected intensity for staticBrightness=2: ${expectedIntensity.toFixed(3)}`);
                
                const difference = Math.abs(dungeonHandler.ambientLight.intensity - expectedIntensity);
                if (difference < 0.1) {
                    log('✅ Static brightness is working correctly!', 'success');
                } else {
                    log(`❌ Static brightness not applied correctly (difference: ${difference.toFixed(3)})`, 'error');
                }
            } else {
                log('No ambient light found', 'error');
            }
            
            // Check player health
            if (dungeonHandler.playerController) {
                const health = dungeonHandler.playerController.currentHealth;
                const maxHealth = dungeonHandler.playerController.maxHealth;
                log(`Player Health: ${health}/${maxHealth}`);
            }
            
            log('=== End Lighting Status ===');
        };
    </script>
</body>
</html>