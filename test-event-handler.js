// Test script to manually trigger event room handler creation
window.testEventHandler = async function() {
    const dungeonHandler = window.dungeonHandler || window.sceneManager?.currentHandler;
    if (\!dungeonHandler || \!dungeonHandler.eventRoomManager) {
        console.error("No dungeon handler or event room manager found");
        return;
    }
    
    const currentRoomId = dungeonHandler.currentRoomId;
    const roomData = dungeonHandler.floorLayout?.get(currentRoomId);
    
    if (\!roomData || \!roomData.eventRoomData) {
        console.error("Current room is not an event room");
        return;
    }
    
    console.log("=== MANUALLY TRIGGERING EVENT ROOM HANDLER ===");
    console.log("Room ID:", currentRoomId);
    console.log("Room type:", roomData.type);
    console.log("Event room:", roomData.eventRoomData.name);
    
    try {
        // Force call onEventRoomEnter
        await dungeonHandler.eventRoomManager.onEventRoomEnter(currentRoomId, roomData);
        console.log("✅ Event room handler creation completed");
        
        // Check if handler was created
        const hasHandler = \!\!dungeonHandler.eventRoomManager.activeRoomHandler;
        console.log("Has active handler:", hasHandler);
        
        if (hasHandler) {
            console.log("Handler class:", dungeonHandler.eventRoomManager.activeRoomHandler.constructor.name);
        }
    } catch (error) {
        console.error("❌ Failed to create event room handler:", error);
    }
};

// Auto-run
testEventHandler();
