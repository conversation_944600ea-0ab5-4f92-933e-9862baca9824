# Single Pattern Timeline System

This document explains how to use the new single pattern timeline system that ensures the boss uses exactly one pattern at a time, with precise control over when patterns switch.

## What's New

I've implemented several improvements to ensure the boss uses only one pattern at a time:

1. **Single Pattern Timelines**: Created two new timelines that specify exactly one pattern at a time
2. **Pattern Exclusivity Flag**: Added a `useOnlyTimelinePatterns` flag that ensures only patterns from the timeline are used
3. **Precise Time Control**: Patterns switch at exact time points specified in the timeline

## Available Timelines

### 1. "precise_switch" Timeline

A simple timeline with just two patterns:

- **0-19 seconds**: ONLY petal spread pattern
- **19-60 seconds**: ONLY laser grid pattern (switches exactly at 19 seconds)

### 2. "single_patterns" Timeline

A more complex timeline with five different patterns:

- **0-19 seconds**: ONLY petal spread pattern
- **19-35 seconds**: ONLY laser grid pattern
- **35-50 seconds**: ONLY spiral wave pattern
- **50-65 seconds**: ONLY hellburst pattern
- **65-80 seconds**: ONLY inhale exhale pattern

## How to Use

### In-Game Activation

To use these timelines in the game:

1. Start a boss battle
2. Open the developer console (F12 in most browsers)
3. Run one of these commands:

```javascript
// For the simple two-pattern timeline with switch at 19 seconds
game.dungeonHandler.currentRoom.boss.bossController.loadTimeline("precise_switch");

// OR, for the more complex timeline with five different patterns
game.dungeonHandler.currentRoom.boss.bossController.loadTimeline("single_patterns");
```

### Verifying It Works

To verify the boss is using only one pattern at a time:

1. Enable debug mode to see timeline information
2. Watch for the pattern switch at exactly 19 seconds
3. Confirm that only one pattern is being used at a time

The debug overlay will show:
- Current timeline progress
- Current music time
- Current timeline section description
- Current pattern being used

## How It Works

The system works by:

1. Setting the `useOnlyTimelinePatterns` flag to true when loading a timeline
2. This flag prevents the normal pattern selection logic from running
3. The timeline system triggers exactly one pattern at a time based on the current music time
4. When the music reaches a time boundary (e.g., 19 seconds), the pattern switches instantly

## Troubleshooting

### If you're still seeing multiple patterns or projectile types:

The issue has been fixed in the latest update. The changes include:

1. Fixed the `_spawnPattern` method to directly spawn the pattern specified in the timeline
2. Modified the `_selectProjectileType` method to use consistent projectile types in timeline mode
3. Updated the `triggerPattern` method to store the current timeline pattern

These changes ensure that:
- Only one pattern is used at a time
- The same projectile type is always used for a given pattern
- No random pattern selection occurs in timeline mode

If you're still having issues:

1. Make sure you've loaded the timeline correctly using the console command
2. Check that the music is playing (the timeline is synced to music time)
3. Try restarting the boss battle after loading the timeline
4. Verify in the debug overlay that the timeline is active and showing the correct section

If needed, you can manually set the flag to use only timeline patterns:

```javascript
game.dungeonHandler.currentRoom.boss.bossController.patternManager.useOnlyTimelinePatterns = true;
```
