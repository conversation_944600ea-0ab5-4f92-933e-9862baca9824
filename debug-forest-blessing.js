// Debug script for Forest Blessing card testing
// Run this in browser console when in dungeon scene

function debugForestBlessing() {
    console.log('=== Forest Blessing Debug ===');
    
    // Check if we're in a dungeon scene
    if (!window.sceneManager || !window.sceneManager.currentHandler) {
        console.error('Not in dungeon scene or sceneManager not available');
        return;
    }
    
    const dungeonHandler = window.sceneManager.currentHandler;
    
    // Check player controller
    if (!dungeonHandler.playerController) {
        console.error('PlayerController not found');
        return;
    }
    
    const pc = dungeonHandler.playerController;
    console.log('Player Controller found:', pc);
    console.log('Player health properties:', {
        actualHealth: pc.actualHealth,
        currentHealth: pc.currentHealth,
        health: pc.health,
        maxHealth: pc.maxHealth,
        userData: pc.userData,
        hasHealMethod: typeof pc.heal === 'function'
    });
    
    // Check card system
    if (!dungeonHandler.cardSystem) {
        console.error('CardSystem not found');
        return;
    }
    
    console.log('CardSystem found:', dungeonHandler.cardSystem);
    
    // Check for enemies
    console.log('Active enemies:', dungeonHandler.activeEnemies);
    
    // Check scene for enemy objects
    let enemyCount = 0;
    if (dungeonHandler.scene) {
        dungeonHandler.scene.traverse((object) => {
            if (object.userData && (object.userData.isEnemy || object.userData.type === 'enemy')) {
                enemyCount++;
                console.log('Found enemy:', {
                    name: object.name,
                    position: object.position,
                    health: object.userData.health,
                    speed: object.userData.speed,
                    isEnemy: object.userData.isEnemy,
                    type: object.userData.type
                });
            }
        });
    }
    console.log(`Found ${enemyCount} enemy objects in scene`);
    
    // Test forest blessing effect directly
    console.log('Testing Forest Blessing effect...');
    
    const mockTargetPosition = { x: window.innerWidth / 2, y: window.innerHeight / 2 };
    
    try {
        dungeonHandler.cardSystem.executeForestBlessing(
            { itemData: { effect: 'forest_entangle' } },
            mockTargetPosition
        );
        console.log('✅ Forest Blessing executed successfully');
        
        // Check if health changed
        setTimeout(() => {
            console.log('Post-healing health:', {
                actualHealth: pc.actualHealth,
                currentHealth: pc.currentHealth,
                expectedIncrease: '5 health points'
            });
        }, 100);
        
    } catch (error) {
        console.error('❌ Forest Blessing execution failed:', error);
    }
}

// Quick command to give forest blessing card
function giveForestBlessing() {
    if (window.sceneManager?.currentHandler?.cardSystem) {
        window.sceneManager.currentHandler.cardSystem.addCard('forest_blessing');
        console.log('✅ Added Forest Blessing card to hand');
    } else {
        console.error('❌ CardSystem not available');
    }
}

// Quick command to test soul potion
function testSoulPotion() {
    if (window.give) {
        const oldHealth = window.sceneManager?.currentHandler?.playerController?.actualHealth || 'unknown';
        console.log(`Current health: ${oldHealth}`);
        
        const result = window.give('soul_potion');
        if (result) {
            console.log(`✅ Soul potion added to card inventory! Drag it to use and heal 6 health.`);
            console.log(`💡 The soul potion should now appear in your card inventory bar.`);
        } else {
            console.error('❌ Failed to add soul potion to inventory');
        }
    } else {
        console.error('❌ Give command not available');
    }
}

// Quick command to damage player for testing
function damagePlayer(amount = 5) {
    if (window.sceneManager?.currentHandler?.playerController) {
        const pc = window.sceneManager.currentHandler.playerController;
        const oldHealth = pc.actualHealth;
        pc.takeDamage(amount);
        console.log(`💔 Player took ${amount} damage: ${oldHealth} → ${pc.actualHealth}`);
    } else {
        console.error('❌ PlayerController not available');
    }
}

// Add to window for easy access
window.debugForestBlessing = debugForestBlessing;
window.giveForestBlessing = giveForestBlessing;
window.testSoulPotion = testSoulPotion;
window.damagePlayer = damagePlayer;

console.log('Debug script loaded. Available commands:');
console.log('- debugForestBlessing() - Test forest blessing effect');
console.log('- giveForestBlessing() - Add forest blessing card to hand');
console.log('- testSoulPotion() - Test soul potion healing');
console.log('- damagePlayer(amount) - Damage player for testing');