{"permissions": {"allow": ["Bash(rg:*)", "Bash(grep:*)", "Bash(git checkout:*)", "Bash(find:*)", "Bash(node:*)", "Bash(open index.html)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(./run-simple-test.sh:*)", "Bash(./run-boss-test.sh:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(python3:*)", "Bash(rm:*)", "<PERSON><PERSON>(open:*)", "Bash(ls:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(python3:*)", "Bash(npm init:*)", "Bash(npm install:*)", "WebFetch(domain:cdn.jsdelivr.net)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:github.ngxson.com)", "Bash(cp:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(do if ! grep -q \"availableConnections\" \"$file\")", "Bash(then echo \"Missing availableConnections: $file\")", "Bash(fi)"], "deny": []}}