# Music Timeline System

## Overview

The Music Timeline System allows for precise control over boss patterns based on specific time points in the music. This system enables you to define exactly which patterns should play during specific time ranges, creating a perfectly choreographed boss battle experience.

## Key Features

1. **Time-Based Pattern Control**: Define which patterns play at specific time points in the music
2. **Multiple Timeline Support**: Create different timelines for different boss battles or music tracks
3. **Dynamic Intensity and Speed**: Set custom intensity and speed multipliers for each timeline segment
4. **Seamless Integration**: Works alongside the existing music analysis system
5. **Performance Optimized**: Uses efficient pitch detection with throttling and downsampling

## How to Use

### Creating a Timeline

Timelines are defined in `src/data/bossTimelines.js`. Each timeline is an array of entries with the following properties:

```javascript
{
    startTime: 0,         // Start time in seconds
    endTime: 10,          // End time in seconds
    patterns: ["pattern_name"], // Array of pattern names to use
    intensity: 0.5,       // Pattern intensity (0-1)
    speedMultiplier: 1.0, // Speed multiplier for projectiles
    triggerIntervalMin: 1.0, // Minimum time between pattern triggers
    triggerIntervalMax: 2.0, // Maximum time between pattern triggers
    description: "Section description" // Optional description
}
```

### Loading a Timeline

To load a timeline, call the `loadTimeline` method on the BossController:

```javascript
bossController.loadTimeline("timeline_name");
```

Where "timeline_name" is the key in the bossTimelines object.

### Example Timeline

Here's an example timeline that creates a complete boss battle experience:

```javascript
"boss1": [
    {
        startTime: 0,
        endTime: 15,
        patterns: ["petal_spread"],
        intensity: 0.3,
        speedMultiplier: 0.8,
        description: "Intro section - gentle patterns"
    },
    {
        startTime: 15,
        endTime: 30,
        patterns: ["circle_ripple", "spiral_wave"],
        intensity: 0.5,
        speedMultiplier: 0.9,
        description: "Verse 1 - building intensity"
    },
    {
        startTime: 30,
        endTime: 45,
        patterns: ["laser_grid", "lightning_chain"],
        intensity: 0.7,
        speedMultiplier: 1.1,
        description: "Pre-chorus - more intense patterns"
    },
    {
        startTime: 45,
        endTime: 60,
        patterns: ["hellburst", "boomerang_arcs"],
        intensity: 0.8,
        speedMultiplier: 1.2,
        description: "Chorus - peak intensity"
    }
]
```

## Technical Implementation

The timeline system consists of three main components:

1. **BossMusicTimeline**: Manages the timeline and triggers patterns at the appropriate times
2. **OptimizedPitchDetector**: An improved version of the pitch detector with performance optimizations
3. **Timeline Configuration**: JSON-based timeline definitions in bossTimelines.js

### Performance Optimizations

The system includes several performance optimizations:

1. **Throttled Updates**: Pitch detection is throttled to reduce CPU usage
2. **Audio Buffer Downsampling**: Audio data is downsampled for faster processing
3. **Similarity Detection**: Similar audio buffers are detected to skip redundant processing
4. **Web Audio API Integration**: Uses the Web Audio API's built-in analyzer when available

## Integration with Existing Systems

The timeline system works alongside the existing music analysis system:

1. The timeline system provides precise, time-based pattern control
2. The music analysis system continues to provide real-time responsiveness to musical features
3. Both systems can be used together for a combination of choreographed and reactive boss behavior

## Debug Information

When debug mode is enabled, the following timeline information is displayed:

- Current timeline progress (percentage)
- Current music time (seconds)
- Current timeline section description
- Current timeline patterns
- Last triggered pattern from timeline

## Customization

You can create custom timelines for specific music tracks by adding new entries to the bossTimelines object. This allows for precise choreography of boss battles to match the music perfectly.
