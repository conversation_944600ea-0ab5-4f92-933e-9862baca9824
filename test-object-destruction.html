<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Object Destruction Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #333;
        }
        .success { background: #2a4a2a; }
        .error { background: #4a2a2a; }
        .info { background: #2a2a4a; }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover { background: #5aa0f2; }
        #console-output {
            background: #000;
            color: #0f0;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Object Destruction Debug Test</h1>
    
    <div class="status info">
        <strong>Instructions:</strong>
        <ol>
            <li>Click "Open Game" to start the main game</li>
            <li>Navigate to a dungeon room with destructible objects (stone vases)</li>
            <li>Shoot at the objects and check console output below</li>
            <li>Use the test buttons to simulate destruction</li>
        </ol>
    </div>
    
    <button onclick="openGame()">Open Game</button>
    <button onclick="testDestructionFlow()">Test Destruction Logic</button>
    <button onclick="clearConsole()">Clear Console</button>
    
    <div id="console-output"></div>
    
    <script>
        const consoleOutput = document.getElementById('console-output');
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f44' : type === 'warn' ? '#fa4' : '#0f0';
            consoleOutput.innerHTML += `<span style="color: #888">[${timestamp}]</span> <span style="color: ${color}">${message}</span>\n`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function openGame() {
            window.open('http://localhost:8000/index.html', '_blank');
        }
        
        function testDestructionFlow() {
            console.log('🧪 Testing object destruction flow...');
            
            // Simulate object destruction logic
            const mockObjectUserData = {
                isDestructible: true,
                objectType: 'stone_vase',
                originalVoxels: [
                    { x: 0, y: 0, z: 0, c: '#8B4513' },
                    { x: 1, y: 0, z: 0, c: '#A0522D' },
                    { x: 0, y: 1, z: 0, c: '#CD853F' },
                    { x: 1, y: 1, z: 0, c: '#DEB887' }
                ],
                voxelScale: 1.0
            };
            
            console.log('✅ Object is destructible:', mockObjectUserData.isDestructible);
            console.log('✅ Has voxel data:', Boolean(mockObjectUserData.originalVoxels));
            console.log('✅ Voxel count:', mockObjectUserData.originalVoxels.length);
            console.log('✅ Object type:', mockObjectUserData.objectType);
            
            // Test debris creation logic
            const voxelStep = mockObjectUserData.objectType === 'stone_vase' ? 8 : 4;
            const maxDebrisPieces = Math.ceil(mockObjectUserData.originalVoxels.length / voxelStep);
            console.log('🔥 Debris pieces to create:', maxDebrisPieces);
            
            let debrisCount = 0;
            mockObjectUserData.originalVoxels.forEach((voxelData, index) => {
                if (index % voxelStep === 0 && debrisCount < maxDebrisPieces) {
                    debrisCount++;
                    console.log(`   ✨ Creating debris piece ${debrisCount}: color ${voxelData.c}`);
                }
            });
            
            console.log('🎯 Expected result: Should create', debrisCount, 'debris pieces');
            
            if (debrisCount > 0) {
                console.log('✅ Destruction logic test PASSED - debris should appear!');
            } else {
                console.error('❌ Destruction logic test FAILED - no debris created!');
            }
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        // Initial message
        console.log('🚀 Object Destruction Debug Test Ready');
        console.log('📋 Follow the instructions above to test object destruction');
    </script>
</body>
</html>