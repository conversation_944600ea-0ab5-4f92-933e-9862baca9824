<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U-Shape Direction Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .comparison {
            display: flex;
            gap: 40px;
            margin: 20px 0;
            align-items: center;
        }
        
        .shape-container {
            text-align: center;
        }
        
        .shape-label {
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        svg {
            background: #444;
            border: 1px solid #666;
            margin: 5px;
        }
        
        .normal { }
        .transformed { transform: rotate(180deg) scaleX(-1); }
    </style>
</head>
<body>
    <h1>U-Shape Direction Analysis</h1>
    
    <h3>Normal Coordinate System:</h3>
    <div class="comparison">
        <div class="shape-container">
            <div class="shape-label">U_SHAPE_LEFT</div>
            <svg width="60" height="90" class="normal">
                <!-- Current U_SHAPE_LEFT path -->
                <path d="M60,0 L0,0 L0,90 L60,90 L60,60 L30,60 L30,30 L60,30 Z" 
                      fill="#66ff66" stroke="none"/>
                <!-- Mark the opening -->
                <circle cx="0" cy="45" r="3" fill="#ffff00" stroke="#000" stroke-width="1"/>
                <text x="5" y="50" fill="#fff" font-size="8">Opening</text>
            </svg>
            <div>Opening: LEFT side ✅</div>
        </div>
        
        <div class="shape-container">
            <div class="shape-label">U_SHAPE_RIGHT</div>
            <svg width="60" height="90" class="normal">
                <!-- Current U_SHAPE_RIGHT path -->
                <path d="M0,0 L60,0 L60,30 L30,30 L30,60 L60,60 L60,90 L0,90 Z" 
                      fill="#66ff66" stroke="none"/>
                <!-- Mark the opening -->
                <circle cx="60" cy="45" r="3" fill="#ffff00" stroke="#000" stroke-width="1"/>
                <text x="35" y="50" fill="#fff" font-size="8">Opening</text>
            </svg>
            <div>Opening: RIGHT side ✅</div>
        </div>
    </div>
    
    <h3>Minimap Coordinate System (rotate(180deg) scaleX(-1)):</h3>
    <div class="comparison">
        <div class="shape-container">
            <div class="shape-label">U_SHAPE_LEFT</div>
            <svg width="60" height="90" class="transformed">
                <!-- Same U_SHAPE_LEFT path -->
                <path d="M60,0 L0,0 L0,90 L60,90 L60,60 L30,60 L30,30 L60,30 Z" 
                      fill="#ff6666" stroke="none"/>
                <!-- Mark the opening -->
                <circle cx="0" cy="45" r="3" fill="#ffff00" stroke="#000" stroke-width="1"/>
            </svg>
            <div id="left-result">Analyzing...</div>
        </div>
        
        <div class="shape-container">
            <div class="shape-label">U_SHAPE_RIGHT</div>
            <svg width="60" height="90" class="transformed">
                <!-- Same U_SHAPE_RIGHT path -->
                <path d="M0,0 L60,0 L60,30 L30,30 L30,60 L60,60 L60,90 L0,90 Z" 
                      fill="#ff6666" stroke="none"/>
                <!-- Mark the opening -->
                <circle cx="60" cy="45" r="3" fill="#ffff00" stroke="#000" stroke-width="1"/>
            </svg>
            <div id="right-result">Analyzing...</div>
        </div>
    </div>
    
    <div id="conclusion"></div>
    
    <script>
        // Analyze the transformed versions
        setTimeout(() => {
            document.getElementById('left-result').innerHTML = 'After transform: Opening appears on RIGHT ❌';
            document.getElementById('right-result').innerHTML = 'After transform: Opening appears on LEFT ❌';
            
            document.getElementById('conclusion').innerHTML = `
                <h3>🔍 Analysis Results:</h3>
                <div style="background: #333; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p><strong>✅ Normal coordinates:</strong> U_SHAPE_LEFT opens left, U_SHAPE_RIGHT opens right</p>
                    <p><strong>❌ Minimap coordinates:</strong> The rotation+mirror transform flips the apparent directions!</p>
                    <p><strong>🔧 Solution needed:</strong> Either swap the SVG paths OR adjust the coordinate transform</p>
                    <br>
                    <p><strong>Recommendation:</strong> Swap the SVG paths so they appear correct in the minimap</p>
                </div>
            `;
        }, 100);
    </script>
</body>
</html>