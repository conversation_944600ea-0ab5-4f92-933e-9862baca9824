
var ortWasm = (() => {
  var _scriptDir = import.meta.url;
  
  return (
async function(moduleArg = {}) {

var e=moduleArg,aa,l;e.ready=new Promise((a,b)=>{aa=a;l=b});"use strict";e.mountExternalData=(a,b)=>{(e.gb||(e.gb=new Map)).set(a,b)};e.unmountExternalData=()=>{delete e.gb};
let ca=()=>{const a=(c,d,f)=>(...h)=>{const k=q,m=d?.();h=c(...h);const p=d?.();m!==p&&(c=p,f(m),d=f=null);return q!=k?ba():h},b=c=>async(...d)=>{try{if(e.fb)throw Error("Session already started");const f=e.fb={Eb:d[0],errors:[]},h=await c(...d);if(e.fb!==f)throw Error("Session mismatch");e.ob?.flush();const k=f.errors;if(0<k.length){let m=await Promise.all(k);m=m.filter(p=>p);if(0<m.length)throw Error(m.join("\n"));}return h}finally{e.fb=null}};e._OrtCreateSession=a(e._OrtCreateSession,()=>e._OrtCreateSession,
c=>e._OrtCreateSession=c);e._OrtRun=b(a(e._OrtRun,()=>e._OrtRun,c=>e._OrtRun=c));e._OrtRunWithBinding=b(a(e._OrtRunWithBinding,()=>e._OrtRunWithBinding,c=>e._OrtRunWithBinding=c));e._OrtBindInput=a(e._OrtBindInput,()=>e._OrtBindInput,c=>e._OrtBindInput=c);ca=void 0};
e.jsepInit=(a,b)=>{ca?.();if("webgpu"===a){[e.ob,e.wb,e.Ab,e.pb,e.zb,e.Sa,e.Bb,e.Db,e.xb,e.yb,e.Cb]=b;const c=e.ob;e.jsepRegisterBuffer=(d,f,h,k)=>c.registerBuffer(d,f,h,k);e.jsepGetBuffer=d=>c.getBuffer(d);e.jsepCreateDownloader=(d,f,h)=>c.createDownloader(d,f,h);e.jsepOnReleaseSession=d=>{c.onReleaseSession(d)};e.jsepOnRunStart=d=>c.onRunStart(d)}};
var da=Object.assign({},e),ea="./this.program",x=(a,b)=>{throw b;},fa="object"==typeof window,y="function"==typeof importScripts,ha="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,z="",ia,A,B;
if(ha){const {createRequire:a}=await import("module");var require=a(import.meta.url),fs=require("fs"),ja=require("path");y?z=ja.dirname(z)+"/":z=require("url").fileURLToPath(new URL("./",import.meta.url));ia=(b,c)=>{b=ka(b)?new URL(b):ja.normalize(b);return fs.readFileSync(b,c?void 0:"utf8")};B=b=>{b=ia(b,!0);b.buffer||(b=new Uint8Array(b));return b};A=(b,c,d,f=!0)=>{b=ka(b)?new URL(b):ja.normalize(b);fs.readFile(b,f?void 0:"utf8",(h,k)=>{h?d(h):c(f?k.buffer:k)})};
!e.thisProgram&&1<process.argv.length&&(ea=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);x=(b,c)=>{process.exitCode=b;throw c;};e.inspect=()=>"[Emscripten Module object]"}else if(fa||y)y?z=self.location.href:"undefined"!=typeof document&&document.currentScript&&(z=document.currentScript.src),_scriptDir&&(z=_scriptDir),0!==z.indexOf("blob:")?z=z.substr(0,z.replace(/[?#].*/,"").lastIndexOf("/")+1):z="",ia=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},
y&&(B=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),A=(a,b,c)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?b(d.response):c()};d.onerror=c;d.send(null)};var la=console.log.bind(console),C=console.error.bind(console);Object.assign(e,da);da=null;"object"!=typeof WebAssembly&&D("no native wasm support detected");
var ma,E=!1,na,F,G,H,oa,I,J,pa,qa,ra,sa;function ta(){var a=ma.buffer;e.HEAP8=F=new Int8Array(a);e.HEAP16=H=new Int16Array(a);e.HEAPU8=G=new Uint8Array(a);e.HEAPU16=oa=new Uint16Array(a);e.HEAP32=I=new Int32Array(a);e.HEAPU32=J=new Uint32Array(a);e.HEAPF32=pa=new Float32Array(a);e.HEAPF64=sa=new Float64Array(a);e.HEAP64=qa=new BigInt64Array(a);e.HEAPU64=ra=new BigUint64Array(a)}var ua=[],va=[],wa=[],K=0,xa=null,L=null;
function D(a){a="Aborted("+a+")";C(a);E=!0;na=1;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");l(a);throw a;}var ya=a=>a.startsWith("data:application/octet-stream;base64,"),ka=a=>a.startsWith("file://"),N;if(e.locateFile){if(N="ort-wasm-simd.jsep.wasm",!ya(N)){var za=N;N=e.locateFile?e.locateFile(za,z):z+za}}else N=(new URL("ort-wasm-simd.jsep.wasm",import.meta.url)).href;
function Aa(a){if(B)return B(a);throw"both async and sync fetching of the wasm failed";}function Ba(a){if(fa||y){if("function"==typeof fetch&&!ka(a))return fetch(a,{credentials:"same-origin"}).then(b=>{if(!b.ok)throw"failed to load wasm binary file at '"+a+"'";return b.arrayBuffer()}).catch(()=>Aa(a));if(A)return new Promise((b,c)=>{A(a,d=>b(new Uint8Array(d)),c)})}return Promise.resolve().then(()=>Aa(a))}
function Ca(a,b,c){return Ba(a).then(d=>WebAssembly.instantiate(d,b)).then(d=>d).then(c,d=>{C(`failed to asynchronously prepare wasm: ${d}`);D(d)})}function Da(a,b){var c=N;return"function"!=typeof WebAssembly.instantiateStreaming||ya(c)||ka(c)||ha||"function"!=typeof fetch?Ca(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(f){C(`wasm streaming compile failed: ${f}`);C("falling back to ArrayBuffer instantiation");return Ca(c,a,b)}))}
var Ea={809440:(a,b,c,d)=>{if("undefined"==typeof e||!e.gb)return 1;a=O(a>>>0);a.startsWith("./")&&(a=a.substring(2));a=e.gb.get(a);if(!a)return 2;b>>>=0;c>>>=0;if(b+c>a.byteLength)return 3;try{return G.set(a.subarray(b,b+c),d>>>0>>>0),0}catch{return 4}},809941:()=>{e.xb()},809972:()=>{e.yb()},810001:()=>{e.Cb()},810026:a=>e.wb(a),810059:a=>e.Ab(a),810091:(a,b,c)=>{e.pb(a,b,c,!0)},810130:(a,b,c)=>{e.pb(a,b,c)},810163:a=>{e.Sa("Abs",a,void 0)},810214:a=>{e.Sa("Neg",a,void 0)},810265:a=>{e.Sa("Floor",
a,void 0)},810318:a=>{e.Sa("Ceil",a,void 0)},810370:a=>{e.Sa("Reciprocal",a,void 0)},810428:a=>{e.Sa("Sqrt",a,void 0)},810480:a=>{e.Sa("Exp",a,void 0)},810531:a=>{e.Sa("Erf",a,void 0)},810582:a=>{e.Sa("Sigmoid",a,void 0)},810637:(a,b,c)=>{e.Sa("HardSigmoid",a,{alpha:b,beta:c})},810716:a=>{e.Sa("Log",a,void 0)},810767:a=>{e.Sa("Sin",a,void 0)},810818:a=>{e.Sa("Cos",a,void 0)},810869:a=>{e.Sa("Tan",a,void 0)},810920:a=>{e.Sa("Asin",a,void 0)},810972:a=>{e.Sa("Acos",a,void 0)},811024:a=>{e.Sa("Atan",
a,void 0)},811076:a=>{e.Sa("Sinh",a,void 0)},811128:a=>{e.Sa("Cosh",a,void 0)},811180:a=>{e.Sa("Asinh",a,void 0)},811233:a=>{e.Sa("Acosh",a,void 0)},811286:a=>{e.Sa("Atanh",a,void 0)},811339:a=>{e.Sa("Tanh",a,void 0)},811391:a=>{e.Sa("Not",a,void 0)},811442:(a,b,c)=>{e.Sa("Clip",a,{min:b,max:c})},811511:a=>{e.Sa("Clip",a,void 0)},811563:(a,b)=>{e.Sa("Elu",a,{alpha:b})},811621:a=>{e.Sa("Relu",a,void 0)},811673:(a,b)=>{e.Sa("LeakyRelu",a,{alpha:b})},811737:(a,b)=>{e.Sa("ThresholdedRelu",a,{alpha:b})},
811807:(a,b)=>{e.Sa("Cast",a,{to:b})},811865:a=>{e.Sa("Add",a,void 0)},811916:a=>{e.Sa("Sub",a,void 0)},811967:a=>{e.Sa("Mul",a,void 0)},812018:a=>{e.Sa("Div",a,void 0)},812069:a=>{e.Sa("Pow",a,void 0)},812120:a=>{e.Sa("Equal",a,void 0)},812173:a=>{e.Sa("Greater",a,void 0)},812228:a=>{e.Sa("GreaterOrEqual",a,void 0)},812290:a=>{e.Sa("Less",a,void 0)},812342:a=>{e.Sa("LessOrEqual",a,void 0)},812401:(a,b,c,d,f)=>{e.Sa("ReduceMean",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>
0,f>>>0)):[]})},812560:(a,b,c,d,f)=>{e.Sa("ReduceMax",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},812718:(a,b,c,d,f)=>{e.Sa("ReduceMin",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},812876:(a,b,c,d,f)=>{e.Sa("ReduceProd",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},813035:(a,b,c,d,f)=>{e.Sa("ReduceSum",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>
0,f>>>0)):[]})},813193:(a,b,c,d,f)=>{e.Sa("ReduceL1",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},813350:(a,b,c,d,f)=>{e.Sa("ReduceL2",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},813507:(a,b,c,d,f)=>{e.Sa("ReduceLogSum",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},813668:(a,b,c,d,f)=>{e.Sa("ReduceSumSquare",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>
0,f>>>0)):[]})},813832:(a,b,c,d,f)=>{e.Sa("ReduceLogSumExp",a,{keepDims:!!b,noopWithEmptyAxes:!!c,axes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},813996:a=>{e.Sa("Where",a,void 0)},814049:(a,b,c)=>{e.Sa("Transpose",a,{perm:b?Array.from(I.subarray(b>>>0,c>>>0)):[]})},814157:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t)=>{e.Sa("ConvTranspose",a,{format:p?"NHWC":"NCHW",autoPad:b,dilations:[c],group:d,kernelShape:[f],pads:[h,k],strides:[m],wIsConst:()=>!!F[n>>>0],outputPadding:r?Array.from(I.subarray(r>>>0,u>>>0)):
[],outputShape:w?Array.from(I.subarray(w>>>0,g>>>0)):[],activation:O(t)})},814558:(a,b,c,d,f,h,k,m,p,n,r,u,w,g)=>{e.Sa("ConvTranspose",a,{format:m?"NHWC":"NCHW",autoPad:b,dilations:Array.from(I.subarray(c>>>0,(c>>>0)+2>>>0)),group:d,kernelShape:Array.from(I.subarray(f>>>0,(f>>>0)+2>>>0)),pads:Array.from(I.subarray(h>>>0,(h>>>0)+4>>>0)),strides:Array.from(I.subarray(k>>>0,(k>>>0)+2>>>0)),wIsConst:()=>!!F[p>>>0],outputPadding:n?Array.from(I.subarray(n>>>0,r>>>0)):[],outputShape:u?Array.from(I.subarray(u>>>
0,w>>>0)):[],activation:O(g)})},815123:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t)=>{e.Sa("ConvTranspose",a,{format:p?"NHWC":"NCHW",autoPad:b,dilations:[c],group:d,kernelShape:[f],pads:[h,k],strides:[m],wIsConst:()=>!!F[n>>>0],outputPadding:r?Array.from(I.subarray(r>>>0,u>>>0)):[],outputShape:w?Array.from(I.subarray(w>>>0,g>>>0)):[],activation:O(t)})},815524:(a,b,c,d,f,h,k,m,p,n,r,u,w,g)=>{e.Sa("ConvTranspose",a,{format:m?"NHWC":"NCHW",autoPad:b,dilations:Array.from(I.subarray(c>>>0,(c>>>0)+2>>>0)),group:d,kernelShape:Array.from(I.subarray(f>>>
0,(f>>>0)+2>>>0)),pads:Array.from(I.subarray(h>>>0,(h>>>0)+4>>>0)),strides:Array.from(I.subarray(k>>>0,(k>>>0)+2>>>0)),wIsConst:()=>!!F[p>>>0],outputPadding:n?Array.from(I.subarray(n>>>0,r>>>0)):[],outputShape:u?Array.from(I.subarray(u>>>0,w>>>0)):[],activation:O(g)})},816089:(a,b)=>{e.Sa("GlobalAveragePool",a,{format:b?"NHWC":"NCHW"})},816180:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t,v)=>{e.Sa("AveragePool",a,{format:v?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,storage_order:f,dilations:[h,k],
kernel_shape:[m,p],pads:[n,r,u,w],strides:[g,t]})},816464:(a,b)=>{e.Sa("GlobalAveragePool",a,{format:b?"NHWC":"NCHW"})},816555:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t,v)=>{e.Sa("AveragePool",a,{format:v?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,storage_order:f,dilations:[h,k],kernel_shape:[m,p],pads:[n,r,u,w],strides:[g,t]})},816839:(a,b)=>{e.Sa("GlobalMaxPool",a,{format:b?"NHWC":"NCHW"})},816926:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t,v)=>{e.Sa("MaxPool",a,{format:v?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,
count_include_pad:d,storage_order:f,dilations:[h,k],kernel_shape:[m,p],pads:[n,r,u,w],strides:[g,t]})},817206:(a,b)=>{e.Sa("GlobalMaxPool",a,{format:b?"NHWC":"NCHW"})},817293:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t,v)=>{e.Sa("MaxPool",a,{format:v?"NHWC":"NCHW",auto_pad:b,ceil_mode:c,count_include_pad:d,storage_order:f,dilations:[h,k],kernel_shape:[m,p],pads:[n,r,u,w],strides:[g,t]})},817573:(a,b,c,d,f)=>{e.Sa("Gemm",a,{alpha:b,beta:c,transA:d,transB:f})},817677:a=>{e.Sa("MatMul",a,void 0)},817731:(a,b,c,d)=>
{e.Sa("ArgMax",a,{keepDims:!!b,selectLastIndex:!!c,axis:d})},817839:(a,b,c,d)=>{e.Sa("ArgMin",a,{keepDims:!!b,selectLastIndex:!!c,axis:d})},817947:(a,b)=>{e.Sa("Softmax",a,{axis:b})},818010:(a,b)=>{e.Sa("Concat",a,{axis:b})},818070:(a,b,c,d,f)=>{e.Sa("Split",a,{axis:b,numOutputs:c,splitSizes:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},818210:a=>{e.Sa("Expand",a,void 0)},818264:(a,b)=>{e.Sa("Gather",a,{axis:Number(b)})},818335:(a,b)=>{e.Sa("GatherElements",a,{axis:Number(b)})},818414:(a,b,c,d,f,h,
k,m,p,n,r)=>{e.Sa("Resize",a,{antialias:b,axes:c?Array.from(I.subarray(c>>>0,d>>>0)):[],coordinateTransformMode:O(f),cubicCoeffA:h,excludeOutside:k,extrapolationValue:m,keepAspectRatioPolicy:O(p),mode:O(n),nearestMode:O(r)})},818760:(a,b,c,d,f,h,k)=>{e.Sa("Slice",a,{starts:b?Array.from(I.subarray(b>>>0,c>>>0)):[],ends:d?Array.from(I.subarray(d>>>0,f>>>0)):[],axes:h?Array.from(I.subarray(h>>>0,k>>>0)):[]})},818976:a=>{e.Sa("Tile",a,void 0)},819028:(a,b,c)=>{e.Sa("LayerNormalization",a,{axis:Number(b),
epsilon:Number(c)})},819135:(a,b,c)=>{e.Sa("InstanceNormalization",a,{epsilon:b,format:c?"NHWC":"NCHW"})},819249:(a,b,c)=>{e.Sa("InstanceNormalization",a,{epsilon:b,format:c?"NHWC":"NCHW"})},819363:a=>{e.Sa("Range",a,void 0)},819416:(a,b)=>{e.Sa("Einsum",a,{equation:O(b)})},819497:(a,b,c,d,f)=>{e.Sa("Pad",a,{mode:b,value:c,pads:d?Array.from(I.subarray(d>>>0,f>>>0)):[]})},819624:(a,b,c,d,f,h)=>{e.Sa("BatchNormalization",a,{epsilon:b,momentum:c,spatial:!!f,trainingMode:!!d,format:h?"NHWC":"NCHW"})},
819793:(a,b,c,d,f,h)=>{e.Sa("BatchNormalization",a,{epsilon:b,momentum:c,spatial:!!f,trainingMode:!!d,format:h?"NHWC":"NCHW"})},819962:(a,b,c)=>{e.Sa("CumSum",a,{exclusive:Number(b),reverse:Number(c)})},820059:(a,b,c,d,f,h,k,m,p)=>{e.Sa("Attention",a,{numHeads:b,isUnidirectional:c,maskFilterValue:d,scale:f,doRotary:h,qkvHiddenSizes:k?Array.from(I.subarray(Number(m)>>>0,Number(m)+k>>>0)):[],pastPresentShareBuffer:!!p})},820331:a=>{e.Sa("BiasAdd",a,void 0)},820386:a=>{e.Sa("BiasSplitGelu",a,void 0)},
820447:a=>{e.Sa("FastGelu",a,void 0)},820503:(a,b,c,d,f,h,k,m,p,n,r,u,w)=>{e.Sa("Conv",a,{format:p?"NHWC":"NCHW",auto_pad:b,dilations:[c],group:d,kernel_shape:[f],pads:h?Array.from(I.subarray(h>>>0,k>>>0)):[],strides:[m],w_is_const:()=>!!F[n>>>0],activation:O(r),activation_params:u?Array.from(pa.subarray(u>>>0,w>>>0)):[]})},820873:(a,b,c,d,f,h,k,m,p,n,r,u,w,g,t,v)=>{e.Sa("Conv",a,{format:u?"NHWC":"NCHW",auto_pad:b,dilations:[c,d],group:f,kernel_shape:[h,k],pads:m?Array.from(I.subarray(m>>>0,p>>>0)):
[],strides:[n,r],w_is_const:()=>!!F[w>>>0],activation:O(g),activation_params:t?Array.from(pa.subarray(t>>>0,v>>>0)):[]})},821264:a=>{e.Sa("Gelu",a,void 0)},821316:(a,b,c,d,f,h)=>{e.Sa("MatMulNBits",a,{k:b,n:c,accuracyLevel:d,bits:f,blockSize:h})},821443:(a,b,c,d,f,h)=>{e.Sa("MultiHeadAttention",a,{numHeads:b,isUnidirectional:c,maskFilterValue:d,scale:f,doRotary:h})},821602:(a,b,c,d,f)=>{e.Sa("RotaryEmbedding",a,{interleaved:!!b,numHeads:c,rotaryEmbeddingDim:d,scale:f})},821741:(a,b)=>{e.Sa("SkipLayerNormalization",
a,{epsilon:b})},821822:a=>{e.Bb(a)},821856:(a,b)=>e.Db(a,b,e.fb.Eb,e.fb.errors)};function Fa(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}function Ga(a){this.cb=a-24;this.sb=function(b){J[this.cb+4>>>2>>>0]=b};this.rb=function(b){J[this.cb+8>>>2>>>0]=b};this.kb=function(b,c){this.jb();this.sb(b);this.rb(c)};this.jb=function(){J[this.cb+16>>>2>>>0]=0}}
var Ha=0,Ia=0,Ja="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,Ka=(a,b,c)=>{b>>>=0;var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&Ja)return Ja.decode(a.subarray(b,c));for(d="";b<c;){var f=a[b++];if(f&128){var h=a[b++]&63;if(192==(f&224))d+=String.fromCharCode((f&31)<<6|h);else{var k=a[b++]&63;f=224==(f&240)?(f&15)<<12|h<<6|k:(f&7)<<18|h<<12|k<<6|a[b++]&63;65536>f?d+=String.fromCharCode(f):(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else d+=String.fromCharCode(f)}return d},
O=(a,b)=>(a>>>=0)?Ka(G,a,b):"",La=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},Ma=(a,b,c,d)=>{c>>>=0;if(!(0<d))return 0;var f=c;d=c+d-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var m=a.charCodeAt(++h);k=65536+((k&1023)<<10)|m&1023}if(127>=k){if(c>=d)break;b[c++>>>0]=k}else{if(2047>=k){if(c+1>=d)break;b[c++>>>0]=192|k>>6}else{if(65535>=k){if(c+2>=d)break;b[c++>>>0]=224|k>>12}else{if(c+
3>=d)break;b[c++>>>0]=240|k>>18;b[c++>>>0]=128|k>>12&63}b[c++>>>0]=128|k>>6&63}b[c++>>>0]=128|k&63}}b[c>>>0]=0;return c-f},Na=a=>{if(null===a)return"null";var b=typeof a;return"object"===b||"array"===b||"function"===b?a.toString():""+a},Oa,P=a=>{for(var b="";G[a>>>0];)b+=Oa[G[a++>>>0]];return b},Pa={},Qa={},Ra={},Q;
function Sa(a,b,c={}){var d=b.name;if(!a)throw new Q(`type "${d}" must have a positive integer typeid pointer`);if(Qa.hasOwnProperty(a)){if(c.ub)return;throw new Q(`Cannot register type '${d}' twice`);}Qa[a]=b;delete Ra[a];Pa.hasOwnProperty(a)&&(b=Pa[a],delete Pa[a],b.forEach(f=>f()))}function R(a,b,c={}){if(!("argPackAdvance"in b))throw new TypeError("registerType registeredInstance requires argPackAdvance");Sa(a,b,c)}
var Ta=(a,b,c)=>{switch(b){case 1:return c?d=>F[d>>>0>>>0]:d=>G[d>>>0>>>0];case 2:return c?d=>H[d>>>1>>>0]:d=>oa[d>>>1>>>0];case 4:return c?d=>I[d>>>2>>>0]:d=>J[d>>>2>>>0];case 8:return c?d=>qa[d>>>3]:d=>ra[d>>>3];default:throw new TypeError(`invalid integer width (${b}): ${a}`);}};function Ua(){this.$a=[void 0];this.nb=[]}var S=new Ua;function Va(a){a>>>=0;a>=S.cb&&0===--S.get(a).qb&&S.jb(a)}
var T=a=>{if(!a)throw new Q("Cannot use deleted val. handle = "+a);return S.get(a).value},U=a=>{switch(a){case void 0:return 1;case null:return 2;case !0:return 3;case !1:return 4;default:return S.kb({qb:1,value:a})}};function Wa(a){return this.fromWireType(I[a>>>2>>>0])}var Xa=(a,b)=>{switch(b){case 4:return function(c){return this.fromWireType(pa[c>>>2>>>0])};case 8:return function(c){return this.fromWireType(sa[c>>>3>>>0])};default:throw new TypeError(`invalid float width (${b}): ${a}`);}};
function Ya(a){return this.fromWireType(J[a>>>2>>>0])}
var Za="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,$a=(a,b)=>{var c=a>>1;for(var d=c+b/2;!(c>=d)&&oa[c>>>0];)++c;c<<=1;if(32<c-a&&Za)return Za.decode(G.subarray(a>>>0,c>>>0));c="";for(d=0;!(d>=b/2);++d){var f=H[a+2*d>>>1>>>0];if(0==f)break;c+=String.fromCharCode(f)}return c},ab=(a,b,c)=>{c??=2147483647;if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var f=0;f<c;++f)H[b>>>1>>>0]=a.charCodeAt(f),b+=2;H[b>>>1>>>0]=0;return b-d},bb=a=>2*a.length,cb=(a,b)=>{for(var c=
0,d="";!(c>=b/4);){var f=I[a+4*c>>>2>>>0];if(0==f)break;++c;65536<=f?(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023)):d+=String.fromCharCode(f)}return d},db=(a,b,c)=>{b>>>=0;c??=2147483647;if(4>c)return 0;var d=b;c=d+c-4;for(var f=0;f<a.length;++f){var h=a.charCodeAt(f);if(55296<=h&&57343>=h){var k=a.charCodeAt(++f);h=65536+((h&1023)<<10)|k&1023}I[b>>>2>>>0]=h;b+=4;if(b+4>c)break}I[b>>>2>>>0]=0;return b-d},eb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&
++c;b+=4}return b},gb=(a,b)=>{var c=Qa[a];if(void 0===c)throw a=fb(a),c=P(a),V(a),new Q(b+" has unknown type "+c);return c},hb=(a,b,c)=>{var d=[];a=a.toWireType(d,c);d.length&&(J[b>>>2>>>0]=U(d));return a},ib=a=>{try{a()}catch(b){D(b)}};function jb(){var a=W,b={};for(let [c,d]of Object.entries(a))b[c]="function"==typeof d?function(){kb.push(c);try{return d.apply(null,arguments)}finally{E||(kb.pop(),q&&1===X&&0===kb.length&&(X=0,ib(lb),"undefined"!=typeof Fibers&&Fibers.Kb()))}}:d;return b}
var X=0,q=null,mb=0,kb=[],nb={},ob={},pb=0,qb=null,rb=[];function ba(){return new Promise((a,b)=>{qb={resolve:a,reject:b}})}function sb(){var a=Y(65548),b=a+12;J[a>>>2>>>0]=b;J[a+4>>>2>>>0]=b+65536;b=kb[0];var c=nb[b];void 0===c&&(c=pb++,nb[b]=c,ob[c]=b);I[a+8>>>2>>>0]=c;return a}
function tb(a){if(!E){if(0===X){var b=!1,c=!1;a((d=0)=>{if(!E&&(mb=d,b=!0,c)){X=2;ib(()=>vb(q));"undefined"!=typeof Browser&&Browser.lb.tb&&Browser.lb.resume();d=!1;try{var f=(0,W[ob[I[q+8>>>2>>>0]]])()}catch(m){f=m,d=!0}var h=!1;if(!q){var k=qb;k&&(qb=null,(d?k.reject:k.resolve)(f),h=!0)}if(d&&!h)throw f;}});c=!0;b||(X=1,q=sb(),"undefined"!=typeof Browser&&Browser.lb.tb&&Browser.lb.pause(),ib(()=>wb(q)))}else 2===X?(X=0,ib(xb),V(q),q=null,rb.forEach(d=>{if(!E)try{d();try{na=na=d=na,e.onExit?.(d),
E=!0,x(d,new Fa(d))}catch(f){f instanceof Fa||"unwind"==f||x(1,f)}}catch(f){f instanceof Fa||"unwind"==f||x(1,f)}})):D(`invalid state: ${X}`);return mb}}function yb(a){return tb(b=>{a().then(b)})}
var zb=[],Ab={},Bb=a=>{var b=Ab[a];return void 0===b?P(a):b},Cb=()=>"object"==typeof globalThis?globalThis:Function("return this")(),Db=a=>{var b=zb.length;zb.push(a);return b},Eb=(a,b)=>{for(var c=Array(a),d=0;d<a;++d)c[d]=gb(J[b+4*d>>>2>>>0],"parameter "+d);return c},Fb=(a,b)=>Object.defineProperty(b,"name",{value:a});
function Gb(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=Fb(b.name||"unknownFunctionName",function(){});c.prototype=b.prototype;c=new c;a=b.apply(c,a);return a instanceof Object?a:c}
var Z=a=>0===a%4&&(0!==a%100||0===a%400),Hb=[0,31,60,91,121,152,182,213,244,274,305,335],Ib=[0,31,59,90,120,151,181,212,243,273,304,334],Jb=a=>{var b=La(a)+1,c=Y(b);c&&Ma(a,G,c,b);return c},Kb=[],Lb=(a,b)=>{Kb.length=0;for(var c;c=G[a++>>>0];){var d=105!=c;d&=112!=c;b+=d&&b%8?4:0;Kb.push(112==c?J[b>>>2>>>0]:106==c?qa[b>>>3]:105==c?I[b>>>2>>>0]:sa[b>>>3>>>0]);b+=d?8:4}return Kb},Mb={},Ob=()=>{if(!Nb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&
navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:ea||"./this.program"},b;for(b in Mb)void 0===Mb[b]?delete a[b]:a[b]=Mb[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);Nb=c}return Nb},Nb,Pb=[null,[],[]],Qb=[31,29,31,30,31,30,31,31,30,31,30,31],Rb=[31,28,31,30,31,30,31,31,30,31,30,31];function Sb(a){var b=Array(La(a)+1);Ma(a,b,0,b.length);return b}
function Tb(a,b,c,d){function f(g,t,v){for(g="number"==typeof g?g.toString():g||"";g.length<t;)g=v[0]+g;return g}function h(g,t){return f(g,t,"0")}function k(g,t){function v(ub){return 0>ub?-1:0<ub?1:0}var M;0===(M=v(g.getFullYear()-t.getFullYear()))&&0===(M=v(g.getMonth()-t.getMonth()))&&(M=v(g.getDate()-t.getDate()));return M}function m(g){switch(g.getDay()){case 0:return new Date(g.getFullYear()-1,11,29);case 1:return g;case 2:return new Date(g.getFullYear(),0,3);case 3:return new Date(g.getFullYear(),
0,2);case 4:return new Date(g.getFullYear(),0,1);case 5:return new Date(g.getFullYear()-1,11,31);case 6:return new Date(g.getFullYear()-1,11,30)}}function p(g){var t=g.ab;for(g=new Date((new Date(g.bb+1900,0,1)).getTime());0<t;){var v=g.getMonth(),M=(Z(g.getFullYear())?Qb:Rb)[v];if(t>M-g.getDate())t-=M-g.getDate()+1,g.setDate(1),11>v?g.setMonth(v+1):(g.setMonth(0),g.setFullYear(g.getFullYear()+1));else{g.setDate(g.getDate()+t);break}}v=new Date(g.getFullYear()+1,0,4);t=m(new Date(g.getFullYear(),
0,4));v=m(v);return 0>=k(t,g)?0>=k(v,g)?g.getFullYear()+1:g.getFullYear():g.getFullYear()-1}a>>>=0;b>>>=0;c>>>=0;d>>>=0;var n=J[d+40>>>2>>>0];d={Hb:I[d>>>2>>>0],Gb:I[d+4>>>2>>>0],hb:I[d+8>>>2>>>0],mb:I[d+12>>>2>>>0],ib:I[d+16>>>2>>>0],bb:I[d+20>>>2>>>0],Wa:I[d+24>>>2>>>0],ab:I[d+28>>>2>>>0],Jb:I[d+32>>>2>>>0],Fb:I[d+36>>>2>>>0],Ib:n?O(n):""};c=O(c);n={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S",
"%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var r in n)c=c.replace(new RegExp(r,"g"),n[r]);var u="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),w="January February March April May June July August September October November December".split(" ");n={"%a":g=>u[g.Wa].substring(0,3),"%A":g=>u[g.Wa],"%b":g=>
w[g.ib].substring(0,3),"%B":g=>w[g.ib],"%C":g=>h((g.bb+1900)/100|0,2),"%d":g=>h(g.mb,2),"%e":g=>f(g.mb,2," "),"%g":g=>p(g).toString().substring(2),"%G":g=>p(g),"%H":g=>h(g.hb,2),"%I":g=>{g=g.hb;0==g?g=12:12<g&&(g-=12);return h(g,2)},"%j":g=>{for(var t=0,v=0;v<=g.ib-1;t+=(Z(g.bb+1900)?Qb:Rb)[v++]);return h(g.mb+t,3)},"%m":g=>h(g.ib+1,2),"%M":g=>h(g.Gb,2),"%n":()=>"\n","%p":g=>0<=g.hb&&12>g.hb?"AM":"PM","%S":g=>h(g.Hb,2),"%t":()=>"\t","%u":g=>g.Wa||7,"%U":g=>h(Math.floor((g.ab+7-g.Wa)/7),2),"%V":g=>
{var t=Math.floor((g.ab+7-(g.Wa+6)%7)/7);2>=(g.Wa+371-g.ab-2)%7&&t++;if(t)53==t&&(v=(g.Wa+371-g.ab)%7,4==v||3==v&&Z(g.bb)||(t=1));else{t=52;var v=(g.Wa+7-g.ab-1)%7;(4==v||5==v&&Z(g.bb%400-1))&&t++}return h(t,2)},"%w":g=>g.Wa,"%W":g=>h(Math.floor((g.ab+7-(g.Wa+6)%7)/7),2),"%y":g=>(g.bb+1900).toString().substring(2),"%Y":g=>g.bb+1900,"%z":g=>{g=g.Fb;var t=0<=g;g=Math.abs(g)/60;return(t?"+":"-")+String("0000"+(g/60*100+g%60)).slice(-4)},"%Z":g=>g.Ib,"%%":()=>"%"};c=c.replace(/%%/g,"\x00\x00");for(r in n)c.includes(r)&&
(c=c.replace(new RegExp(r,"g"),n[r](d)));c=c.replace(/\0\0/g,"%");r=Sb(c);if(r.length>b)return 0;F.set(r,a>>>0);return r.length-1}for(var Ub=Array(256),Vb=0;256>Vb;++Vb)Ub[Vb]=String.fromCharCode(Vb);Oa=Ub;Q=e.BindingError=class extends Error{constructor(a){super(a);this.name="BindingError"}};e.InternalError=class extends Error{constructor(a){super(a);this.name="InternalError"}};
Object.assign(Ua.prototype,{get(a){return this.$a[a]},has(a){return void 0!==this.$a[a]},kb(a){var b=this.nb.pop()||this.$a.length;this.$a[b]=a;return b},jb(a){this.$a[a]=void 0;this.nb.push(a)}});S.$a.push({value:void 0},{value:null},{value:!0},{value:!1});S.cb=S.$a.length;e.count_emval_handles=()=>{for(var a=0,b=S.cb;b<S.$a.length;++b)void 0!==S.$a[b]&&++a;return a};
var Xb={ia:function(a,b,c){return yb(async()=>{await e.zb(a,b,c)})},a:function(a,b,c){a>>>=0;(new Ga(a)).kb(b>>>0,c>>>0);Ha=a;Ia++;throw Ha;},x:function(){return 0},ba:function(){},O:function(){},Q:function(){},ca:function(){return 0},$:function(){},W:function(){},_:function(){},D:function(){},P:function(){},M:function(){},aa:function(){},N:function(){},G:function(a,b,c,d,f){b>>>=0;b=P(b);var h=-1!=b.indexOf("u");h&&(f=(1n<<64n)-1n);R(a>>>0,{name:b,fromWireType:k=>k,toWireType:function(k,m){if("bigint"!=
typeof m&&"number"!=typeof m)throw new TypeError(`Cannot convert "${Na(m)}" to ${this.name}`);if(m<d||m>f)throw new TypeError(`Passing a number "${Na(m)}" from JS side to C/C++ side to an argument of type "${b}", which is outside the valid range [${d}, ${f}]!`);return m},argPackAdvance:8,readValueFromPointer:Ta(b,c>>>0,!h),eb:null})},ga:function(a,b,c,d){b=P(b>>>0);R(a>>>0,{name:b,fromWireType:function(f){return!!f},toWireType:function(f,h){return h?c:d},argPackAdvance:8,readValueFromPointer:function(f){return this.fromWireType(G[f>>>
0])},eb:null})},fa:function(a,b){b=P(b>>>0);R(a>>>0,{name:b,fromWireType:c=>{var d=T(c);Va(c);return d},toWireType:(c,d)=>U(d),argPackAdvance:8,readValueFromPointer:Wa,eb:null})},F:function(a,b,c){b=P(b>>>0);R(a>>>0,{name:b,fromWireType:d=>d,toWireType:(d,f)=>f,argPackAdvance:8,readValueFromPointer:Xa(b,c>>>0),eb:null})},s:function(a,b,c,d,f){a>>>=0;c>>>=0;b=P(b>>>0);-1===f&&(f=4294967295);f=m=>m;if(0===d){var h=32-8*c;f=m=>m<<h>>>h}var k=b.includes("unsigned")?function(m,p){return p>>>0}:function(m,
p){return p};R(a,{name:b,fromWireType:f,toWireType:k,argPackAdvance:8,readValueFromPointer:Ta(b,c,0!==d),eb:null})},n:function(a,b,c){function d(h){return new f(F.buffer,J[h+4>>>2>>>0],J[h>>>2>>>0])}var f=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array,BigInt64Array,BigUint64Array][b];c=P(c>>>0);R(a>>>0,{name:c,fromWireType:d,argPackAdvance:8,readValueFromPointer:d},{ub:!0})},H:function(a,b){b=P(b>>>0);var c="std::string"===b;R(a>>>0,{name:b,fromWireType:function(d){var f=
J[d>>>2>>>0],h=d+4;if(c)for(var k=h,m=0;m<=f;++m){var p=h+m;if(m==f||0==G[p>>>0]){k=O(k,p-k);if(void 0===n)var n=k;else n+=String.fromCharCode(0),n+=k;k=p+1}}else{n=Array(f);for(m=0;m<f;++m)n[m]=String.fromCharCode(G[h+m>>>0]);n=n.join("")}V(d);return n},toWireType:function(d,f){f instanceof ArrayBuffer&&(f=new Uint8Array(f));var h="string"==typeof f;if(!(h||f instanceof Uint8Array||f instanceof Uint8ClampedArray||f instanceof Int8Array))throw new Q("Cannot pass non-string to std::string");var k=
c&&h?La(f):f.length;var m=Y(4+k+1),p=m+4;J[m>>>2>>>0]=k;if(c&&h)Ma(f,G,p,k+1);else if(h)for(h=0;h<k;++h){var n=f.charCodeAt(h);if(255<n)throw V(p),new Q("String has UTF-16 code units that do not fit in 8 bits");G[p+h>>>0]=n}else for(h=0;h<k;++h)G[p+h>>>0]=f[h];null!==d&&d.push(V,m);return m},argPackAdvance:8,readValueFromPointer:Ya,eb(d){V(d)}})},z:function(a,b,c){b>>>=0;c>>>=0;c=P(c);if(2===b){var d=$a;var f=ab;var h=bb;var k=()=>oa;var m=1}else 4===b&&(d=cb,f=db,h=eb,k=()=>J,m=2);R(a>>>0,{name:c,
fromWireType:p=>{for(var n=J[p>>>2>>>0],r=k(),u,w=p+4,g=0;g<=n;++g){var t=p+4+g*b;if(g==n||0==r[t>>>m])w=d(w,t-w),void 0===u?u=w:(u+=String.fromCharCode(0),u+=w),w=t+b}V(p);return u},toWireType:(p,n)=>{if("string"!=typeof n)throw new Q(`Cannot pass non-string to C++ string type ${c}`);var r=h(n),u=Y(4+r+b);J[u>>>2]=r>>m;f(n,u+4,r+b);null!==p&&p.push(V,u);return u},argPackAdvance:8,readValueFromPointer:Wa,eb(p){V(p)}})},ha:function(a,b){b=P(b>>>0);R(a>>>0,{vb:!0,name:b,argPackAdvance:0,fromWireType:()=>
{},toWireType:()=>{}})},da:()=>1,u:function(a,b,c){b>>>=0;c>>>=0;a=T(a>>>0);b=gb(b,"emval::as");return hb(b,c,a)},w:function(a){a>>>=0;return yb(()=>{a=T(a);return a.then(U)})},o:function(a,b,c,d){c>>>=0;d>>>=0;a=zb[a>>>0];b=T(b>>>0);return a(null,b,c,d)},k:function(a,b,c,d,f){c>>>=0;d>>>=0;f>>>=0;a=zb[a>>>0];b=T(b>>>0);c=Bb(c);return a(b,b[c],d,f)},b:Va,A:function(a,b){b>>>=0;a=T(a>>>0);b=T(b);return a==b},m:function(a){a>>>=0;if(0===a)return U(Cb());a=Bb(a);return U(Cb()[a])},i:function(a,b,c){b=
Eb(a,b>>>0);var d=b.shift();a--;var f="return function (obj, func, destructorsRef, args) {\n",h=0,k=[];0===c&&k.push("obj");for(var m=["retType"],p=[d],n=0;n<a;++n)k.push("arg"+n),m.push("argType"+n),p.push(b[n]),f+=`  var arg${n} = argType${n}.readValueFromPointer(args${h?"+"+h:""});\n`,h+=b[n].argPackAdvance;f+=`  var rv = ${1===c?"new func":"func.call"}(${k.join(", ")});\n`;for(n=0;n<a;++n)b[n].deleteObject&&(f+=`  argType${n}.deleteObject(arg${n});\n`);d.vb||(m.push("emval_returnValue"),p.push(hb),
f+="  return emval_returnValue(retType, destructorsRef, rv);\n");m.push(f+"};\n");a=Gb(m).apply(null,p);c=`methodCaller<(${b.map(r=>r.name).join(", ")}) => ${d.name}>`;return Db(Fb(c,a))},r:function(a,b){b>>>=0;a=T(a>>>0);b=T(b);return U(a[b])},e:function(a){a>>>=0;4<a&&(S.get(a).qb+=1)},t:function(){return U([])},l:function(a){a=T(a>>>0);for(var b=Array(a.length),c=0;c<a.length;c++)b[c]=a[c];return U(b)},f:function(a){return U(Bb(a>>>0))},j:function(){return U({})},h:function(a){a>>>=0;for(var b=
T(a);b.length;){var c=b.pop();b.pop()(c)}Va(a)},g:function(a,b,c){b>>>=0;c>>>=0;a=T(a>>>0);b=T(b);c=T(c);a[b]=c},c:function(a,b){b>>>=0;a=gb(a>>>0,"_emval_take_value");a=a.readValueFromPointer(b);return U(a)},T:function(a,b){a=-9007199254740992>a||9007199254740992<a?NaN:Number(a);b>>>=0;a=new Date(1E3*a);I[b>>>2>>>0]=a.getUTCSeconds();I[b+4>>>2>>>0]=a.getUTCMinutes();I[b+8>>>2>>>0]=a.getUTCHours();I[b+12>>>2>>>0]=a.getUTCDate();I[b+16>>>2>>>0]=a.getUTCMonth();I[b+20>>>2>>>0]=a.getUTCFullYear()-1900;
I[b+24>>>2>>>0]=a.getUTCDay();I[b+28>>>2>>>0]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},U:function(a,b){a=-9007199254740992>a||9007199254740992<a?NaN:Number(a);b>>>=0;a=new Date(1E3*a);I[b>>>2>>>0]=a.getSeconds();I[b+4>>>2>>>0]=a.getMinutes();I[b+8>>>2>>>0]=a.getHours();I[b+12>>>2>>>0]=a.getDate();I[b+16>>>2>>>0]=a.getMonth();I[b+20>>>2>>>0]=a.getFullYear()-1900;I[b+24>>>2>>>0]=a.getDay();I[b+28>>>2>>>0]=(Z(a.getFullYear())?Hb:Ib)[a.getMonth()]+a.getDate()-1|0;I[b+36>>>2>>>0]=
-(60*a.getTimezoneOffset());var c=(new Date(a.getFullYear(),6,1)).getTimezoneOffset(),d=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();I[b+32>>>2>>>0]=(c!=d&&a.getTimezoneOffset()==Math.min(d,c))|0},V:function(a){a>>>=0;var b=new Date(I[a+20>>>2>>>0]+1900,I[a+16>>>2>>>0],I[a+12>>>2>>>0],I[a+8>>>2>>>0],I[a+4>>>2>>>0],I[a>>>2>>>0],0),c=I[a+32>>>2>>>0],d=b.getTimezoneOffset(),f=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),h=(new Date(b.getFullYear(),0,1)).getTimezoneOffset(),k=Math.min(h,
f);0>c?I[a+32>>>2>>>0]=Number(f!=h&&k==d):0<c!=(k==d)&&(f=Math.max(h,f),b.setTime(b.getTime()+6E4*((0<c?k:f)-d)));I[a+24>>>2>>>0]=b.getDay();I[a+28>>>2>>>0]=(Z(b.getFullYear())?Hb:Ib)[b.getMonth()]+b.getDate()-1|0;I[a>>>2>>>0]=b.getSeconds();I[a+4>>>2>>>0]=b.getMinutes();I[a+8>>>2>>>0]=b.getHours();I[a+12>>>2>>>0]=b.getDate();I[a+16>>>2>>>0]=b.getMonth();I[a+20>>>2>>>0]=b.getYear();a=b.getTime();isNaN(a)?(I[Wb()>>>2>>>0]=61,a=-1):a/=1E3;return BigInt(a)},R:function(){return-52},S:function(){},K:function(a,
b,c){function d(p){return(p=p.toTimeString().match(/\(([A-Za-z ]+)\)$/))?p[1]:"GMT"}c>>>=0;var f=(new Date).getFullYear(),h=new Date(f,0,1),k=new Date(f,6,1);f=h.getTimezoneOffset();var m=k.getTimezoneOffset();J[a>>>0>>>2>>>0]=60*Math.max(f,m);I[b>>>0>>>2>>>0]=Number(f!=m);a=d(h);b=d(k);a=Jb(a);b=Jb(b);m<f?(J[c>>>2>>>0]=a,J[c+4>>>2>>>0]=b):(J[c>>>2>>>0]=b,J[c+4>>>2>>>0]=a)},v:()=>{D("")},d:function(a,b,c){a>>>=0;b=Lb(b>>>0,c>>>0);return Ea[a].apply(null,b)},I:function(a,b,c){a>>>=0;b=Lb(b>>>0,c>>>
0);return Ea[a].apply(null,b)},E:()=>Date.now(),L:function(){return 4294901760},q:()=>performance.now(),J:function(a){a>>>=0;var b=G.length;if(4294901760<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);var f=Math;d=Math.max(a,d);a:{f=(f.min.call(f,4294901760,d+(65536-d%65536)%65536)-ma.buffer.byteLength+65535)/65536;try{ma.grow(f);ta();var h=1;break a}catch(k){}h=void 0}if(h)return!0}return!1},Y:function(a,b){a>>>=0;b>>>=0;var c=0;Ob().forEach((d,f)=>{var h=b+c;f=J[a+
4*f>>>2>>>0]=h;for(h=0;h<d.length;++h)F[f++>>>0>>>0]=d.charCodeAt(h);F[f>>>0>>>0]=0;c+=d.length+1});return 0},Z:function(a,b){a>>>=0;b>>>=0;var c=Ob();J[a>>>2>>>0]=c.length;var d=0;c.forEach(f=>d+=f.length+1);J[b>>>2>>>0]=d;return 0},y:()=>52,C:function(){return 52},X:function(){return 70},B:function(a,b,c,d){b>>>=0;c>>>=0;d>>>=0;for(var f=0,h=0;h<c;h++){var k=J[b>>>2>>>0],m=J[b+4>>>2>>>0];b+=8;for(var p=0;p<m;p++){var n=G[k+p>>>0],r=Pb[a];0===n||10===n?((1===a?la:C)(Ka(r,0)),r.length=0):r.push(n)}f+=
m}J[d>>>2>>>0]=f;return 0},ea:Tb,p:function(a,b,c,d){return Tb(a>>>0,b>>>0,c>>>0,d>>>0)}},W=function(){function a(c){W=c.exports;W=jb();W=Yb();ma=W.ja;ta();va.unshift(W.ka);K--;0==K&&(null!==xa&&(clearInterval(xa),xa=null),L&&(c=L,L=null,c()));return W}var b={a:Xb};K++;if(e.instantiateWasm)try{return e.instantiateWasm(b,a)}catch(c){C(`Module.instantiateWasm callback failed with error: ${c}`),l(c)}Da(b,function(c){a(c.instance)}).catch(l);return{}}();e._OrtInit=(a,b)=>(e._OrtInit=W.la)(a,b);
e._OrtGetLastError=(a,b)=>(e._OrtGetLastError=W.ma)(a,b);e._OrtCreateSessionOptions=(a,b,c,d,f,h,k,m,p,n)=>(e._OrtCreateSessionOptions=W.na)(a,b,c,d,f,h,k,m,p,n);e._OrtAppendExecutionProvider=(a,b)=>(e._OrtAppendExecutionProvider=W.oa)(a,b);e._OrtAddFreeDimensionOverride=(a,b,c)=>(e._OrtAddFreeDimensionOverride=W.pa)(a,b,c);e._OrtAddSessionConfigEntry=(a,b,c)=>(e._OrtAddSessionConfigEntry=W.qa)(a,b,c);e._OrtReleaseSessionOptions=a=>(e._OrtReleaseSessionOptions=W.ra)(a);
e._OrtCreateSession=(a,b,c)=>(e._OrtCreateSession=W.sa)(a,b,c);e._OrtReleaseSession=a=>(e._OrtReleaseSession=W.ta)(a);e._OrtGetInputOutputCount=(a,b,c)=>(e._OrtGetInputOutputCount=W.ua)(a,b,c);e._OrtGetInputName=(a,b)=>(e._OrtGetInputName=W.va)(a,b);e._OrtGetOutputName=(a,b)=>(e._OrtGetOutputName=W.wa)(a,b);e._OrtFree=a=>(e._OrtFree=W.xa)(a);e._OrtCreateTensor=(a,b,c,d,f,h)=>(e._OrtCreateTensor=W.ya)(a,b,c,d,f,h);e._OrtGetTensorData=(a,b,c,d,f)=>(e._OrtGetTensorData=W.za)(a,b,c,d,f);
e._OrtReleaseTensor=a=>(e._OrtReleaseTensor=W.Aa)(a);e._OrtCreateRunOptions=(a,b,c,d)=>(e._OrtCreateRunOptions=W.Ba)(a,b,c,d);e._OrtAddRunConfigEntry=(a,b,c)=>(e._OrtAddRunConfigEntry=W.Ca)(a,b,c);e._OrtReleaseRunOptions=a=>(e._OrtReleaseRunOptions=W.Da)(a);e._OrtCreateBinding=a=>(e._OrtCreateBinding=W.Ea)(a);e._OrtBindInput=(a,b,c)=>(e._OrtBindInput=W.Fa)(a,b,c);e._OrtBindOutput=(a,b,c,d)=>(e._OrtBindOutput=W.Ga)(a,b,c,d);e._OrtClearBoundOutputs=a=>(e._OrtClearBoundOutputs=W.Ha)(a);
e._OrtReleaseBinding=a=>(e._OrtReleaseBinding=W.Ia)(a);e._OrtRunWithBinding=(a,b,c,d,f)=>(e._OrtRunWithBinding=W.Ja)(a,b,c,d,f);e._OrtRun=(a,b,c,d,f,h,k,m)=>(e._OrtRun=W.Ka)(a,b,c,d,f,h,k,m);e._OrtEndProfiling=a=>(e._OrtEndProfiling=W.La)(a);e._JsepOutput=(a,b,c)=>(e._JsepOutput=W.Ma)(a,b,c);e._JsepGetNodeName=a=>(e._JsepGetNodeName=W.Na)(a);
var Wb=()=>(Wb=W.Oa)(),Y=e._malloc=a=>(Y=e._malloc=W.Pa)(a),V=e._free=a=>(V=e._free=W.Qa)(a),fb=a=>(fb=W.Ra)(a),Zb=()=>(Zb=W.Ta)(),$b=a=>($b=W.Ua)(a),ac=a=>(ac=W.Va)(a),wb=a=>(wb=W.Xa)(a),lb=()=>(lb=W.Ya)(),vb=a=>(vb=W.Za)(a),xb=()=>(xb=W._a)();e.___start_em_js=821968;e.___stop_em_js=822129;function Yb(){var a=W;a=Object.assign({},a);var b=d=>()=>d()>>>0,c=d=>f=>d(f)>>>0;a.Oa=b(a.Oa);a.Pa=c(a.Pa);a.Ra=c(a.Ra);a.Ta=b(a.Ta);a.Va=c(a.Va);return a}e.stackAlloc=ac;e.stackSave=Zb;e.stackRestore=$b;
e.UTF8ToString=O;e.stringToUTF8=(a,b,c)=>Ma(a,G,b,c);e.lengthBytesUTF8=La;var bc;L=function cc(){bc||dc();bc||(L=cc)};function dc(){if(!(0<K)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;){var a=e.preRun.shift();ua.unshift(a)}for(;0<ua.length;)ua.shift()(e);if(!(0<K||bc||(bc=!0,e.calledRun=!0,E))){for(;0<va.length;)va.shift()(e);for(aa(e);0<wa.length;)wa.shift()(e)}}}dc();


  return moduleArg.ready
}
);
})();
;
export default ortWasm;