<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss Room Door Configuration Test</title>
    <style>
        body {
            font-family: monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
        }
        pre {
            background: #000;
            padding: 10px;
            border: 1px solid #00ff00;
            overflow-x: auto;
        }
        .highlight {
            color: #ff0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Boss Room Door Configuration Test</h1>
    <div id="output"></div>

    <script type="module">
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
        import { BossRoomManager } from './src/systems/BossRoomManager.js';
        
        const output = document.getElementById('output');
        
        function log(message, isHighlight = false) {
            const div = document.createElement('div');
            if (isHighlight) {
                div.className = 'highlight';
            }
            div.textContent = message;
            output.appendChild(div);
        }
        
        function testBossRoomConfiguration() {
            log('=== Testing Boss Room Door Configuration ===', true);
            
            // Create a boss room manager
            const bossRoomManager = new BossRoomManager(null, null);
            
            // Get the catacombs boss room config
            const config = bossRoomManager.getBossRoomConfig('catacombs');
            
            log('\nBoss Room Configuration:');
            log(`Name: ${config.name}`);
            log(`Type: ${config.type}`);
            log(`Shape: ${config.shape}`);
            log(`Primary Entrance: ${config.primaryEntrance}`);
            
            log('\nAvailable Connections:', true);
            for (const [dir, available] of Object.entries(config.availableConnections)) {
                log(`  ${dir}: ${available} ${available ? '(can enter from this direction)' : '(no door)'}`);
            }
            
            log('\nDoor Positions:', true);
            for (const [dir, pos] of Object.entries(config.doorPositions)) {
                if (pos) {
                    log(`  ${dir}: (${pos.x}, ${pos.y}, ${pos.z})`);
                } else {
                    log(`  ${dir}: null (no door)`);
                }
            }
            
            // Test dungeon generation
            log('\n\n=== Testing Dungeon Generation with Boss Room ===', true);
            
            const dungeonGenerator = new DungeonGenerator();
            dungeonGenerator.bossRoomManager = bossRoomManager;
            
            // Generate a simple test layout
            const areaData = {
                id: 'catacombs',
                name: 'Catacombs',
                enemies: [{ type: 'zombie', weight: 1 }],
                allowedShapes: ['SQUARE_1X1']
            };
            
            const layout = dungeonGenerator.generateGraph(areaData);
            
            // Find the boss room
            let bossRoom = null;
            for (const [id, room] of layout) {
                if (room.type === 'Boss') {
                    bossRoom = room;
                    break;
                }
            }
            
            if (bossRoom) {
                log('\nBoss Room Found:', true);
                log(`Room ID: ${bossRoom.id}`);
                log(`Type: ${bossRoom.type}`);
                log(`Shape: ${bossRoom.shapeKey}`);
                log(`Coordinates: (${bossRoom.coords.x}, ${bossRoom.coords.y})`);
                
                log('\nBoss Room Connections:', true);
                const connections = [];
                for (const [dir, neighborId] of Object.entries(bossRoom.connections)) {
                    if (neighborId !== null) {
                        connections.push(`${dir} -> Room ${neighborId}`);
                    }
                }
                
                if (connections.length === 0) {
                    log('  No connections (isolated room - ERROR!)');
                } else if (connections.length === 1) {
                    log(`  Single connection: ${connections[0]} (CORRECT - dead end)`);
                    
                    // Check if it's from the south
                    if (bossRoom.connections.s !== null) {
                        log('  ✅ Boss room has south connection (south door) - CORRECT!', true);
                    } else {
                        log('  ❌ Boss room does NOT have south connection - INCORRECT!', true);
                    }
                } else {
                    log(`  Multiple connections: ${connections.join(', ')} (ERROR - should be dead end)`);
                }
                
                // Verify the entrance direction
                if (bossRoom.entranceDirection) {
                    log(`\nEntrance Direction: ${bossRoom.entranceDirection}`);
                }
            } else {
                log('\n❌ No boss room found in generated dungeon!', true);
            }
            
            // Generate ASCII map
            log('\n\n=== Dungeon ASCII Map ===', true);
            const asciiMap = dungeonGenerator.generateASCIIMap();
            const mapPre = document.createElement('pre');
            mapPre.textContent = asciiMap;
            output.appendChild(mapPre);
        }
        
        // Run the test
        testBossRoomConfiguration();
    </script>
</body>
</html>