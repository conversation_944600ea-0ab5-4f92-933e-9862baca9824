// Test script to debug catacomb floor scanning issues

import { setupThreeJS } from './src/core/threeSetup.js';
import SceneManager from './src/core/SceneManager.js';
import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
import { AREA_THEMES } from './src/generators/AreaThemes.js';
import { generateRoomVisuals } from './src/scenes/roomGenerator.js';

// Create minimal test environment
const { scene, camera, renderer } = setupThreeJS();
global.window = { dungeonHandler: null };

// Create a catacomb room
const roomData = {
    id: 1,
    x: 0,
    y: 0,
    type: 'NORMAL',
    shapeKey: 'SQUARE_1X1',
    connections: {},
    bounds: {
        width: 14,
        depth: 14,
        minX: -7,
        maxX: 7,
        minZ: -7,
        maxZ: 7
    }
};

console.log("=== Testing Catacomb Floor Scanning ===");
console.log("Creating catacomb room...");

// Get catacomb theme data
const catacombTheme = AREA_THEMES.CATACUMBS;
console.log("Catacomb theme:", catacombTheme.name);

// Generate room visuals
const result = generateRoomVisuals(roomData, catacombTheme);
console.log("\nRoom generation result:");
console.log("- Room group children:", result.roomGroup.children.length);
console.log("- Collision meshes:", result.collisionMeshes.length);

// Manually scan the room group for floor meshes
console.log("\n=== Manual Floor Scan ===");
let floorCount = 0;
let floorGroupCount = 0;

result.roomGroup.traverse(child => {
    if (child.name?.toLowerCase().includes('floor')) {
        console.log(`Found object with 'floor' in name: ${child.name}, type: ${child.constructor.name}`);
        if (child.isGroup) {
            floorGroupCount++;
            console.log(`  - Floor group with ${child.children.length} children`);
            child.children.forEach((subChild, idx) => {
                if (subChild.isMesh) {
                    console.log(`    - Child ${idx}: ${subChild.name}, isFloor: ${subChild.userData?.isFloor}`);
                }
            });
        } else if (child.isMesh) {
            floorCount++;
            console.log(`  - Floor mesh, isFloor: ${child.userData?.isFloor}`);
        }
    }
});

console.log(`\nFloor scan summary:`);
console.log(`- Floor groups: ${floorGroupCount}`);
console.log(`- Floor meshes: ${floorCount}`);

// Check userData.isFloor specifically
console.log("\n=== Checking userData.isFloor ===");
let isFloorCount = 0;
result.roomGroup.traverse(child => {
    if (child.isMesh && child.userData?.isFloor) {
        isFloorCount++;
        console.log(`Mesh with isFloor=true: ${child.name || 'unnamed'}`);
    }
});
console.log(`Total meshes with isFloor=true: ${isFloorCount}`);

process.exit(0);