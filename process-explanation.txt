Process Explanation for "Get Isekai'd" Website

Project Goal: Develop an intuitive and immersive 3D website using HTML, JavaScript, and Three.js for creating and revealing a custom hero card, focusing on user experience.

UX Considerations & Development Process:

1.  **Hero Page - Immediate Engagement & Clarity:**
    *   **Objective:** Create a welcoming and clear starting point that immediately draws the user in.
    *   **Implementation:**
        *   **Title:** A large, bold "Get Isekai'd" title rendered as 3D text (`TextGeometry`) with a glowing material (`MeshStandardMaterial` with `emissive` property) is centered prominently.
        *   **Start Button:** A clear "Start" button, also 3D text, is positioned below the title. It uses a distinct glowing material and features a subtle pulse animation (scaling sine wave in the `animate` loop) to invite interaction and signify it's clickable.
        *   **Interaction:** Raycasting (`THREE.Raycaster`) detects clicks specifically on the button mesh. Hover effects (scaling up) provide immediate visual feedback, managed in the `onMouseMove` handler and `animate` loop.
        *   **Accessibility:** While 3D text makes direct DOM focus tricky, the hover effect provides visual focus. Keyboard accessibility (Enter key) would ideally map to the click handler if a focus management system were added.
        *   **Transition:** Clicking "Start" initiates a fade-to-black transition (`fadeTransition` function using a CSS-animated overlay) accompanied by a "whoosh" sound (`HTMLAudioElement`), providing a smooth and non-disorienting progression to the next stage.

2.  **3D Questionnaire Environment - Balancing Immersion & Usability:**
    *   **Objective:** Create an engaging night-time setting that enhances the fantasy theme without hindering the questionnaire process or performance.
    *   **Implementation:**
        *   **Atmosphere:** A dark blue background (`scene.background`), soft ambient lighting (`AmbientLight`), a point light for the campfire (`PointLight`) with flickering effect (intensity/opacity modulation in `animate`), and a particle-based starfield (`Points` with `BufferGeometry`) establish the night mood.
        *   **Low-Poly Assets:** Simple geometry (cones/cylinders for trees - `ConeGeometry`, `CylinderGeometry`; sphere for fire - `SphereGeometry`; plane for ground - `PlaneGeometry`) is used to ensure quick loading and good performance across devices.
        *   **Layout:** Trees are placed randomly around a central area using polar coordinates, leaving the center clear for interaction. The camera (`PerspectiveCamera`) is positioned slightly elevated, looking towards the center.
        *   **Animation:** Gentle tree sway (sine wave rotation in `animate`) and campfire flicker add life without being distracting.
        *   **Sound:** Looping ambient night sounds (`HTMLAudioElement`) play during this scene (managed via `playAudio`/`stopAudio` and the mute state) to enhance immersion.
        *   **Modularity:** Environment elements (trees, ground, stars, fire) are added to the scene in `createQuestionnaireScene` and stored in `environmentObjects` array for easy cleanup in `clearScene`, facilitating future additions or changes.

3.  **Questionnaire Flow - Scalability & Accessibility:**
    *   **Objective:** Guide the user through questions smoothly, manage answers effectively, and ensure clear interaction.
    *   **Implementation:**
        *   **Linear Flow:** Questions are presented one at a time (`displayQuestion` function), managed by `currentQuestionIndex`. This avoids overwhelming the user.
        *   **Data Structure:** Questions and options are stored in a simple array of objects (`questions` array), making it easy to add, remove, or modify questions later.
        *   **3D Text:** Questions and options are rendered as floating 3D text (`TextGeometry` with `MeshStandardMaterial`). Glow effects (`emissive`) help readability against the dark background.
        *   **Ergonomic Placement:** The question text appears centrally above the options. Options are arranged in a slight arc (`optionAngleSpread`, `optionRadius`) around the campfire focal point, facing the camera (`lookAt(camera.position)`) for better readability.
        *   **Interaction:** Options are added to the `interactableObjects` array. Raycasting handles clicks (`onClick` triggering `selectOption`) and hover (scaling effect in `animate`/`onMouseMove`).
        *   **Feedback & Progress:** Clicking an option stores the answer (`userAnswers` array), plays a subtle sound (`ding`), and automatically displays the next question or the reveal button. A 3D text progress indicator (`Question X/Y`) is displayed in the upper area (`questionMeshes.progress`).
        *   **Accessibility:** Options scale on hover, providing focus feedback. Keyboard navigation (arrow keys/Enter) would require implementing a focus index and keydown listeners to cycle through `interactableObjects` and trigger `selectOption`.
        *   **Reveal Button:** After the last question, a prominent "Reveal Your Card" button (glowing 3D text) appears, clearly signaling the next step.

4.  **Card Reveal - Satisfying Payoff & Readability:**
    *   **Objective:** Deliver an exciting reveal moment and present the generated hero card clearly.
    *   **Implementation:**
        *   **Cinematic Transition:** Clicking "Reveal" triggers the fade transition (`fadeTransition`) with a distinct sound (`ding`).
        *   **Reveal Animation:** The card (`cardGroup`) starts scaled down and rotated, then animates into place (interpolating rotation and scale over time - `revealAnimation` object and logic in `animate`), creating a dynamic entrance.
        *   **Card Layout:** A plane (`PlaneGeometry`) serves as the card base. Content is organized logically:
            *   **Image:** A placeholder image (`TextureLoader`, `PlaneGeometry`, `MeshBasicMaterial`) is placed prominently at the top.
            *   **Text Elements:** Name, Stats, Description, and Rarity are generated based on `userAnswers` and displayed using 3D text (`TextGeometry`). Different materials (`MeshStandardMaterial`) and sizes are used for hierarchy (e.g., larger bold name, smaller description).
            *   **Readability:** Text uses white or rarity-specific colors with slight emissive properties for contrast against the dark card background and scene.
            *   **Rarity Indication:** "Epic" rarity text uses a glowing purple material (`emissive`), and the card border (`Line` with `LineBasicMaterial`) uses the same color for visual reinforcement.
        *   **Positioning:** Elements are positioned relative to the card dimensions and each other (e.g., stats below name, rarity at bottom).
        *   **Lighting:** Specific lighting (`AmbientLight`, `SpotLight`) focuses on the card during the reveal scene.

5.  **General UX Considerations:**
    *   **Responsiveness:** The use of `window.innerWidth / window.innerHeight` for camera aspect and renderer size ensures the scene adapts to different screen ratios. Text/element sizing is currently fixed in world units, which might require adjustments for very small/large or unusually shaped screens (e.g., dynamic scaling based on aspect ratio).
    *   **Performance:** Low-poly models, efficient Three.js practices (geometry/material disposal in `clearScene`), and limiting complex animations aim for smooth performance.
    *   **Audio Control:** A visible mute button allows users to disable audio cues easily.
    *   **Scalability:** Code is organized into functions for different scenes (`createHeroPage`, `createQuestionnaireScene`, `createCardRevealScene`) and logic (`displayQuestion`, `selectOption`, `fadeTransition`). State management (`currentState`, `STATE` object) helps control the flow. The question data structure and environment cleanup are designed for future expansion. 