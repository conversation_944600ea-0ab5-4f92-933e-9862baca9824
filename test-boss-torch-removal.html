<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Boss Room Torch Removal Test</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .room-info {
            background-color: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .highlight {
            color: #4a90e2;
            font-weight: bold;
        }
        .torch-count {
            color: #ffaa00;
            font-size: 1.2em;
        }
        .boss-room {
            border-color: #8b0000;
            background-color: #2a1a1a;
        }
        .log-section {
            background-color: #111;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #357abd;
        }
    </style>
</head>
<body>
    <h1>Boss Room Torch Removal Test</h1>
    <p>This test verifies that boss rooms with removeTorches=true have no torches placed.</p>
    
    <div>
        <button onclick="runTest()">Run Test</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>
    
    <script type="module">
        import * as THREE from 'three';
        import { DungeonGenerator } from './src/generators/DungeonGenerator.js';
        import { generateRoomVisuals } from './src/scenes/roomGenerator.js';
        import { getAreaData } from './src/gameData/areas.js';
        import { BossRoomManager } from './src/systems/BossRoomManager.js';
        
        window.runTest = async function() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Running test...</p>';
            
            // Create a minimal dungeon handler mock
            const mockDungeonHandler = {
                floorLayout: new Map(),
                audioManager: {},
                scene: new THREE.Scene()
            };
            
            // Create boss room manager
            const bossRoomManager = new BossRoomManager(mockDungeonHandler, mockDungeonHandler.audioManager);
            
            // Test configuration
            const testRuns = 5;
            const results = [];
            
            for (let run = 0; run < testRuns; run++) {
                console.log(`\n========== TEST RUN ${run + 1} ==========\n`);
                
                // Create a simple dungeon with a boss room
                const generator = new DungeonGenerator();
                const dungeonData = generator.generate({
                    numRooms: 10,
                    seed: Math.random() * 10000,
                    floorLevel: 1,
                    areaKey: 'catacombs'
                });
                
                // Find the boss room
                let bossRoom = null;
                for (const [roomId, roomData] of dungeonData.rooms) {
                    if (roomData.type === 'Boss') {
                        bossRoom = roomData;
                        // Apply boss room configuration
                        bossRoomManager.applyBossRoomConfig(roomData, 'catacombs');
                        break;
                    }
                }
                
                if (!bossRoom) {
                    results.push({
                        run: run + 1,
                        error: 'No boss room found in dungeon'
                    });
                    continue;
                }
                
                // Generate room visuals
                const roomGroup = new THREE.Group();
                const collisionMeshes = [];
                const lights = [];
                
                const areaData = getAreaData('catacombs');
                generateRoomVisuals(roomGroup, collisionMeshes, lights, bossRoom, areaData);
                
                // Count torches in the room
                let torchCount = 0;
                let torchLogs = [];
                
                roomGroup.traverse((child) => {
                    if (child.userData?.objectType === 'torch' || 
                        child.name?.toLowerCase().includes('torch')) {
                        torchCount++;
                        torchLogs.push(`Found torch: ${child.name || 'unnamed'} at (${child.position.x.toFixed(1)}, ${child.position.y.toFixed(1)}, ${child.position.z.toFixed(1)})`);
                    }
                });
                
                // Count torch lights
                let torchLightCount = 0;
                lights.forEach(light => {
                    if (light.name === 'torchLight') {
                        torchLightCount++;
                    }
                });
                
                results.push({
                    run: run + 1,
                    roomId: bossRoom.id,
                    roomType: bossRoom.type,
                    bossRoomName: bossRoom.bossRoomData?.name || 'Unknown',
                    removeTorches: bossRoom.bossRoomData?.lighting?.removeTorches || false,
                    torchCount: torchCount,
                    torchLightCount: torchLightCount,
                    torchLogs: torchLogs,
                    objectCount: roomGroup.children.length,
                    lightCount: lights.length
                });
            }
            
            // Display results
            let html = '<h2>Test Results</h2>';
            
            results.forEach(result => {
                const isBossRoom = result.roomType === 'Boss';
                const shouldRemoveTorches = result.removeTorches === true;
                const passed = shouldRemoveTorches ? (result.torchCount === 0) : true;
                
                html += `<div class="room-info ${isBossRoom ? 'boss-room' : ''}">`;
                html += `<h3>Run ${result.run} - Room ${result.roomId || 'N/A'}</h3>`;
                
                if (result.error) {
                    html += `<p style="color: #ff4444;">Error: ${result.error}</p>`;
                } else {
                    html += `<p>Room Type: <span class="highlight">${result.roomType}</span></p>`;
                    html += `<p>Boss Room: <span class="highlight">${result.bossRoomName}</span></p>`;
                    html += `<p>Remove Torches: <span class="highlight">${result.removeTorches}</span></p>`;
                    html += `<p>Torch Count: <span class="torch-count">${result.torchCount}</span></p>`;
                    html += `<p>Torch Light Count: <span class="torch-count">${result.torchLightCount}</span></p>`;
                    html += `<p>Total Objects: ${result.objectCount}</p>`;
                    html += `<p>Total Lights: ${result.lightCount}</p>`;
                    
                    if (result.torchLogs.length > 0) {
                        html += '<div class="log-section">';
                        html += '<strong>Torch Locations:</strong><br>';
                        result.torchLogs.forEach(log => {
                            html += log + '<br>';
                        });
                        html += '</div>';
                    }
                    
                    html += `<p style="font-size: 1.2em; margin-top: 10px;">`;
                    if (passed) {
                        html += `<span style="color: #44ff44;">✓ PASSED</span>`;
                        if (shouldRemoveTorches) {
                            html += ' - Boss room correctly has no torches';
                        }
                    } else {
                        html += `<span style="color: #ff4444;">✗ FAILED</span>`;
                        html += ` - Boss room should have no torches but found ${result.torchCount}`;
                    }
                    html += '</p>';
                }
                
                html += '</div>';
            });
            
            // Summary
            const passedCount = results.filter(r => !r.error && (r.removeTorches ? r.torchCount === 0 : true)).length;
            const totalCount = results.filter(r => !r.error).length;
            
            html += '<div class="room-info">';
            html += '<h3>Summary</h3>';
            html += `<p>Tests Passed: <span class="highlight">${passedCount}/${totalCount}</span></p>`;
            html += '</div>';
            
            resultsDiv.innerHTML = html;
            
            console.log('\n========== TEST COMPLETE ==========\n');
            console.log('Results:', results);
        };
        
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };
    </script>
</body>
</html>