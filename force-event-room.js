// Debug command to force event room generation in current dungeon

window.forceEventRoom = async function() {
    console.log('🎪 FORCE EVENT ROOM GENERATION');
    
    if (!window.dungeonHandler) {
        console.error('❌ Must be in dungeon to force event room');
        return;
    }
    
    const dungeonGen = window.dungeonHandler.dungeonGenerator;
    if (!dungeonGen) {
        console.error('❌ No dungeon generator found');
        return;
    }
    
    // Get normal rooms
    const normalRooms = [];
    for (const [roomId, roomData] of dungeonGen.floorLayout) {
        if (roomData.type === 'Normal' && roomId !== 0 && !roomData.hasSecretRoom) {
            normalRooms.push({ roomId, roomData });
        }
    }
    
    console.log(`Found ${normalRooms.length} eligible normal rooms`);
    
    if (normalRooms.length === 0) {
        console.error('❌ No normal rooms available for conversion');
        return;
    }
    
    try {
        // Import event room system
        const { getAvailableEventRooms, getEventRoom } = await import('./src/gameData/eventRooms/index.js');
        
        // Get available event rooms
        const usedEventRooms = dungeonGen.usedEventRooms || new Set();
        const availableEventRooms = getAvailableEventRooms().filter(id => !usedEventRooms.has(id));
        
        console.log(`Available event rooms: ${availableEventRooms.length}`);
        console.log('Available rooms:', availableEventRooms);
        
        if (availableEventRooms.length === 0) {
            console.error('❌ No available event rooms');
            return;
        }
        
        // Select random room and event
        const selectedRoom = normalRooms[Math.floor(Math.random() * normalRooms.length)];
        const randomEventId = availableEventRooms[Math.floor(Math.random() * availableEventRooms.length)];
        const eventData = getEventRoom(randomEventId);
        
        console.log(`Converting room ${selectedRoom.roomId} to event room: ${eventData.name}`);
        
        // Update room data
        selectedRoom.roomData.type = 'EVENT';
        selectedRoom.roomData.eventRoomName = eventData.name;
        selectedRoom.roomData.eventRoomId = eventData.id;
        selectedRoom.roomData.eventRoomData = eventData;
        
        // Update in generator's layout
        const room = dungeonGen.layout.get(selectedRoom.roomId);
        if (room) {
            room.type = 'EVENT';
            room.roomData = selectedRoom.roomData;
        }
        
        // Mark as used
        dungeonGen.usedEventRooms.add(eventData.id);
        
        console.log('✅ Event room conversion complete!');
        console.log('Reload the room to see changes: refreshCurrentRoom()');
        
        // Update the map
        showDungeonMap();
        
    } catch (error) {
        console.error('❌ Error forcing event room:', error);
    }
};

console.log('🎪 Force event room command loaded. Use: forceEventRoom()');