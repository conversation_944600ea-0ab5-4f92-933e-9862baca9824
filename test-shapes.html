<!DOCTYPE html>
<html>
<head>
    <title>Room Shape Test</title>
    <style>
        body { font-family: monospace; background: #1a1a1a; color: #00ff00; padding: 20px; }
        .output { background: #000; padding: 15px; white-space: pre-wrap; }
        button { background: #333; color: #00ff00; border: 1px solid #555; padding: 10px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🏗️ Room Shape Variety Test</h1>
    <button onclick="testShapes()">Test Room Shapes</button>
    <div class="output" id="output"></div>

    <script type="module">
        import { DungeonGeneratorCore } from './src/generators/DungeonGeneratorCore.js';
        
        function log(message) {
            document.getElementById('output').textContent += message + '\n';
        }
        
        window.testShapes = async () => {
            document.getElementById('output').textContent = '';
            log('🏗️ Testing Room Shape Variety...\n');
            
            const generator = new DungeonGeneratorCore();
            
            // Test with area config (simulating web worker)
            const areaConfig = {
                id: 'catacombs',
                data: {
                    allowedShapes: [
                        'SQUARE_1X1', 'RECTANGULAR', 'L_SHAPE', 'T_SHAPE', 'CROSS_SHAPE',
                        'RECT_2X1', 'RECT_1X2', 'RECT_3X1', 'RECT_1X3',
                        'U_SHAPE_DOWN', 'U_SHAPE_UP', 'U_SHAPE_LEFT', 'U_SHAPE_RIGHT',
                        'CORRIDOR_LONG', 'CORRIDOR_SHORT', 'SQUARE_2X2'
                    ]
                }
            };
            
            try {
                const result = await generator.generateLayout(areaConfig);
                const rooms = Object.values(result.rooms);
                
                log(`Generated ${result.roomCount} rooms\n`);
                
                // Count shapes
                const shapeCount = {};
                rooms.forEach(room => {
                    shapeCount[room.shapeKey] = (shapeCount[room.shapeKey] || 0) + 1;
                });
                
                log('🏗️ Shape Distribution:');
                Object.entries(shapeCount).sort((a, b) => b[1] - a[1]).forEach(([shape, count]) => {
                    const percentage = ((count / rooms.length) * 100).toFixed(1);
                    log(`  ${shape.padEnd(15)} : ${count.toString().padStart(2)} rooms (${percentage}%)`);
                });
                
                log('\n📊 Shape Variety Analysis:');
                const uniqueShapes = Object.keys(shapeCount).length;
                const expectedShapes = areaConfig.data.allowedShapes.length;
                log(`  Unique shapes used: ${uniqueShapes}/${expectedShapes}`);
                log(`  Variety percentage: ${((uniqueShapes / expectedShapes) * 100).toFixed(1)}%`);
                
                // Check room types
                log('\n🏠 Room Type Distribution:');
                const typeCount = {};
                rooms.forEach(room => {
                    typeCount[room.type] = (typeCount[room.type] || 0) + 1;
                });
                Object.entries(typeCount).forEach(([type, count]) => {
                    log(`  ${type}: ${count} rooms`);
                });
                
                // Show some individual room examples  
                log('\n🎯 Sample Rooms:');
                rooms.slice(0, 10).forEach(room => {
                    log(`  Room ${room.id}: ${room.type} - ${room.shapeKey}`);
                });
                
                log('\n✅ Test Complete!');
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`);
                console.error(error);
            }
        };
    </script>
</body>
</html>