// Quick test script to check event room generation
import { DungeonGenerator } from './src/generators/DungeonGenerator.js';

async function testEventRoomGeneration() {
    console.log('🎪 Testing Event Room Generation...\n');
    
    for (let i = 0; i < 5; i++) {
        console.log(`--- Test ${i + 1} ---`);
        const generator = new DungeonGenerator();
        generator.currentFloorNumber = 1;
        
        try {
            const layout = await generator.generateLayout('catacombs');
            
            // Count event rooms
            let eventRoomCount = 0;
            let eventRoomDetails = [];
            
            for (const [id, room] of layout) {
                if (room.type === 'EVENT') {
                    eventRoomCount++;
                    eventRoomDetails.push({
                        id: id,
                        name: room.eventRoomName || 'Unknown',
                        eventId: room.eventRoomId || 'Unknown'
                    });
                }
            }
            
            if (eventRoomCount > 0) {
                console.log(`✅ ${eventRoomCount} event room(s) generated!`);
                eventRoomDetails.forEach(detail => {
                    console.log(`   Room ${detail.id}: ${detail.name} (${detail.eventId})`);
                });
            } else {
                console.log('❌ No event rooms generated!');
            }
            
        } catch (error) {
            console.error('❌ Error:', error.message);
        }
        
        console.log('');
    }
}

// Run the test
testEventRoomGeneration();