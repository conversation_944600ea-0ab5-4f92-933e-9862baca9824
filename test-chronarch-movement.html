<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chronarch Boss Movement Test</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
            font-family: Arial, sans-serif;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 100;
        }
        #controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>Chronarch Boss Movement Test</h3>
        <p>Testing smooth movement with BossCombatAI</p>
        <p>Phase: <span id="phase">1</span></p>
        <p>Health: <span id="health">750/750</span></p>
        <p>Boss Position: <span id="position">0, 0, 0</span></p>
        <p>Movement Speed: <span id="speed">5.5</span></p>
        <p>Distance to Player: <span id="distance">0</span></p>
    </div>
    <div id="controls">
        <p>WASD - Move player</p>
        <p>Space - Attack boss</p>
        <p>1/2/3 - Force phase change</p>
        <p>R - Reset boss position</p>
    </div>

    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.156.1/build/three.module.js",
            "three/examples/jsm/": "https://unpkg.com/three@0.156.1/examples/jsm/"
        }
    }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

        // Scene setup
        const scene = new THREE.Scene();
        scene.fog = new THREE.Fog(0x000000, 10, 50);

        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 15, 20);
        camera.lookAt(0, 0, 0);

        const renderer = new THREE.WebGLRenderer();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);

        const controls = new OrbitControls(camera, renderer.domElement);

        // Lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // Floor
        const floorGeometry = new THREE.PlaneGeometry(40, 40);
        const floorMaterial = new THREE.MeshStandardMaterial({ 
            color: 0x333333,
            roughness: 0.8
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.receiveShadow = true;
        scene.add(floor);

        // Add grid for movement reference
        const gridHelper = new THREE.GridHelper(40, 20, 0x444444, 0x222222);
        scene.add(gridHelper);

        // Player (simple green cube)
        const playerGeometry = new THREE.BoxGeometry(1, 2, 1);
        const playerMaterial = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
        const player = new THREE.Mesh(playerGeometry, playerMaterial);
        player.position.set(0, 1, 10);
        player.castShadow = true;
        scene.add(player);

        // Mock Chronarch boss (purple floating sphere)
        const bossGroup = new THREE.Group();
        
        // Boss body
        const bossGeometry = new THREE.SphereGeometry(1.5, 16, 16);
        const bossMaterial = new THREE.MeshStandardMaterial({ 
            color: 0x9900ff,
            emissive: 0x440088,
            emissiveIntensity: 0.3
        });
        const bossMesh = new THREE.Mesh(bossGeometry, bossMaterial);
        bossMesh.castShadow = true;
        bossGroup.add(bossMesh);
        
        // Boss aura effect
        const auraGeometry = new THREE.SphereGeometry(2, 16, 16);
        const auraMaterial = new THREE.MeshBasicMaterial({ 
            color: 0x9900ff,
            transparent: true,
            opacity: 0.2,
            side: THREE.BackSide
        });
        const auraMesh = new THREE.Mesh(auraGeometry, auraMaterial);
        bossGroup.add(auraMesh);
        
        bossGroup.position.set(0, 3, 0);
        bossGroup.scale.set(3.5, 3.5, 3.5); // Chronarch scale
        scene.add(bossGroup);

        // Mock enemy data
        const enemyData = {
            health: 750,
            baseSpeed: 5.5,
            speed: 5.5,
            preferredRange: 6.0,
            attackRange: 8.0,
            maxRange: 12.0,
            enemyType: 'chronarch'
        };

        // Mock boss state
        const bossState = {
            currentPhase: 1,
            currentHealth: 750,
            maxHealth: 750,
            isActive: true,
            floatHeight: 2.0,
            homePosition: bossGroup.position.clone()
        };

        // Movement tracking
        let targetPosition = new THREE.Vector3();
        let moveSpeed = enemyData.baseSpeed;

        // Player controls
        const keys = {};
        window.addEventListener('keydown', (e) => keys[e.key.toLowerCase()] = true);
        window.addEventListener('keyup', (e) => keys[e.key.toLowerCase()] = false);

        // Update loop
        const clock = new THREE.Clock();
        
        function animate() {
            requestAnimationFrame(animate);
            
            const deltaTime = clock.getDelta();
            const currentTime = clock.getElapsedTime();
            
            // Player movement
            const playerSpeed = 10;
            if (keys['w']) player.position.z -= playerSpeed * deltaTime;
            if (keys['s']) player.position.z += playerSpeed * deltaTime;
            if (keys['a']) player.position.x -= playerSpeed * deltaTime;
            if (keys['d']) player.position.x += playerSpeed * deltaTime;
            
            // Boss damage
            if (keys[' '] && !keys.spacePressed) {
                keys.spacePressed = true;
                bossState.currentHealth = Math.max(0, bossState.currentHealth - 50);
                
                // Update phase based on health
                const healthPercent = bossState.currentHealth / bossState.maxHealth;
                if (healthPercent <= 0.25) bossState.currentPhase = 3;
                else if (healthPercent <= 0.6) bossState.currentPhase = 2;
                
                // Flash boss red when hit
                bossMaterial.emissive = new THREE.Color(0xff0000);
                setTimeout(() => bossMaterial.emissive = new THREE.Color(0x440088), 100);
            }
            if (!keys[' ']) keys.spacePressed = false;
            
            // Phase changes
            if (keys['1']) bossState.currentPhase = 1;
            if (keys['2']) bossState.currentPhase = 2;
            if (keys['3']) bossState.currentPhase = 3;
            
            // Reset position
            if (keys['r']) {
                bossGroup.position.x = 0;
                bossGroup.position.z = 0;
            }
            
            // Calculate distance to player
            const distanceToPlayer = bossGroup.position.distanceTo(player.position);
            
            // Boss AI movement (simulating BossCombatAI behavior)
            if (distanceToPlayer > enemyData.preferredRange + 1) {
                // Move towards player
                const direction = new THREE.Vector3()
                    .subVectors(player.position, bossGroup.position)
                    .normalize();
                direction.y = 0; // Keep movement horizontal
                
                // Apply smooth movement
                const movement = direction.multiplyScalar(moveSpeed * deltaTime);
                bossGroup.position.add(movement);
                
                // Face player
                const lookAtPos = player.position.clone();
                lookAtPos.y = bossGroup.position.y;
                bossGroup.lookAt(lookAtPos);
            } else if (distanceToPlayer < enemyData.preferredRange - 1) {
                // Move away from player
                const direction = new THREE.Vector3()
                    .subVectors(bossGroup.position, player.position)
                    .normalize();
                direction.y = 0;
                
                const movement = direction.multiplyScalar(moveSpeed * deltaTime * 0.5);
                bossGroup.position.add(movement);
            }
            
            // Floating animation (from ChronarchBossController)
            const floatY = bossState.homePosition.y + bossState.floatHeight + 
                Math.sin(currentTime * 1.5) * 0.3;
            bossGroup.position.y = THREE.MathUtils.lerp(bossGroup.position.y, floatY, 0.1);
            
            // Aura pulsing effect
            auraMesh.scale.setScalar(1 + Math.sin(currentTime * 2) * 0.1);
            auraMesh.material.opacity = 0.2 + Math.sin(currentTime * 3) * 0.1;
            
            // Update UI
            document.getElementById('phase').textContent = bossState.currentPhase;
            document.getElementById('health').textContent = `${bossState.currentHealth}/${bossState.maxHealth}`;
            document.getElementById('position').textContent = 
                `${bossGroup.position.x.toFixed(1)}, ${bossGroup.position.y.toFixed(1)}, ${bossGroup.position.z.toFixed(1)}`;
            document.getElementById('speed').textContent = moveSpeed.toFixed(1);
            document.getElementById('distance').textContent = distanceToPlayer.toFixed(1);
            
            // Phase-based speed adjustment
            moveSpeed = enemyData.baseSpeed * (1 + bossState.currentPhase * 0.2);
            
            controls.update();
            renderer.render(scene, camera);
        }

        animate();

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>