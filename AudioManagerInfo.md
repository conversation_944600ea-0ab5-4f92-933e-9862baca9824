# Audio Manager Reference Guide

This document provides a comprehensive reference for using the AudioManager and MusicConductor systems in the game.

## Table of Contents

1. [Basic Audio Commands](#basic-audio-commands)
2. [Music System Commands](#music-system-commands)
3. [Combat and Player State](#combat-and-player-state)
4. [Debugging Tools](#debugging-tools)
5. [Advanced Usage](#advanced-usage)
6. [MP3 Naming Convention](#mp3-naming-convention)
7. [Implementation Details](#implementation-details)

## Basic Audio Commands

These commands control basic sound effects and ambient audio.

```javascript
// Play a sound effect (once)
audioManager.playSound('sound_key');

// Play a looping sound
audioManager.playSound('sound_key', true);

// Play a sound with custom volume
audioManager.playSound('sound_key', false, 0.5); // 50% volume

// Stop a sound
audioManager.stopSound('sound_key');

// Fade a sound to a target volume
audioManager.fadeVolume('sound_key', 0.2, 1000); // Fade to 20% volume over 1 second

// Stop all sounds
audioManager.stopAllSounds();

// Toggle mute
audioManager.toggleMute();
```

Available sound keys:
- `'chat'` - Chat/dialogue blip sound
- `'footstep'` - Player footstep sound
- `'bg_ambient_surround'` - Background ambient sound
- `'bg_ambient_music'` - Background music track
- `'reveal'` - Reveal sound effect
- `'select'` - Selection sound effect
- `'start_button'` - Start button sound
- `'creepy_noise'` - Creepy ambient noise
- `'flicker_laugh'` - Flicker laugh sound
- `'main_theme'` - Main theme music for start screen

## Music System Commands

These commands control the adaptive music system.

```javascript
// Initialize the music system (must be called after user interaction)
await audioManager.initMusicSystem();

// Start playing music for an area
await audioManager.startAreaMusic('catacombs');

// Transition to a new area with proper musical transition
await audioManager.transitionAreaMusic('lava_tubes');

// Enter a special room within the current area
await audioManager.enterSpecialRoomMusic('catacombs', 'shop');

// Exit a special room and return to the main area music
await audioManager.exitSpecialRoomMusic('catacombs', 'shop');

// Set the global tempo (BPM)
audioManager.musicConductor.setTempo(120);

// Set tempo stretch factor (changes speed without affecting pitch)
audioManager.musicConductor.setTempoStretch(1.25); // 25% faster

// Activate or deactivate a leitmotif layer
audioManager.musicConductor.setLeitmotifActive('mother', true, 2); // name, active, fadeTime(seconds)
audioManager.musicConductor.setLeitmotifActive('guilt', false, 1); // Fade out over 1 second

// Play a stinger sound (one-shot)
audioManager.musicConductor.triggerStinger('player_hit');
```

Available area IDs:
- `'catacombs'` - The Catacombs starting area
- `'lava_tubes'` - Lava Tubes area

Available special room types:
- `'shop'` - Shop room
- `'altar'` - Altar room

Available leitmotifs:
- `'mother'` - Mother-related story elements
- `'guilt'` - Guilt-related story moments

Available stingers:
- `'player_hit'` - Player takes damage
- `'player_death'` - Player dies
- `'enemy_death'` - Enemy is defeated
- `'boss_appear'` - Boss appears

## Combat and Player State

These commands update the music system based on combat and player state.

```javascript
// Update combat state based on enemy count and player health
audioManager.updateCombatState(enemyCount, currentHealth, maxHealth);

// Handle player being hit (plays hit stinger and applies temporary effects)
audioManager.onPlayerHit();

// Update health-based filtering only
audioManager.musicConductor.updateHealth(currentHealth, maxHealth);
```

### Health-Based Filtering

The music system applies a low-pass filter based on player health:

- **Health 10 or above**: Filter fully open (20kHz) - normal sound
- **Health below 10**: Filter gradually closes as health decreases
- **Health 0**: Filter heavily closed (100Hz) - extremely muffled sound

This creates an immersive effect where the music becomes more muffled as the player's health decreases, adding tension to low-health situations.

## Debugging Tools

These commands help with debugging the music system.

```javascript
// Toggle debug mode for the music system
audioManager.toggleMusicDebug();

// Force an immediate transition to a new area (no smooth transition)
await audioManager.musicConductor.forceTransition('catacombs');

// Apply an effects preset
audioManager.musicConductor.effects.applyEffectsPreset('underwater');
```

Available effects presets:
- `'default'` - Normal sound
- `'underwater'` - Underwater effect
- `'nightmare'` - Nightmare/horror effect
- `'boss'` - Boss fight effect

## Advanced Usage

These examples show how to use the music system in specific game scenarios.

```javascript
// Example: Transitioning between areas during a room change
function changeRoom(targetRoomId, targetAreaId) {
    // First transition the music if area is changing
    if (currentAreaId !== targetAreaId) {
        await audioManager.transitionAreaMusic(targetAreaId);
    }

    // Then load the new room
    loadRoom(targetRoomId);
}

// Example: Updating combat state during enemy spawning
function spawnEnemy(enemyType) {
    // Create and add the enemy
    const enemy = createEnemy(enemyType);
    activeEnemies.push(enemy);

    // Update music system with new enemy count
    const enemyCount = activeEnemies.length;
    audioManager.updateCombatState(
        enemyCount,
        playerController.currentHealth,
        playerController.maxHealth
    );
}

// Example: Activating a leitmotif during a story event
function triggerStoryEvent(eventId) {
    if (eventId === 'mother_appears') {
        // Fade in the mother leitmotif over 3 seconds
        audioManager.musicConductor.setLeitmotifActive('mother', true, 3.0);

        // Show the mother character
        showCharacter('mother');

        // Start dialogue
        startDialogue('mother_encounter');
    }
}
```

## Implementation Details

The audio system consists of several components:

1. **AudioManager** (`src/utils/audioManager.js`)
   - Main interface for playing sounds and controlling music
   - Manages HTML audio elements for basic sounds
   - Integrates with MusicConductor for advanced music features

2. **MusicConductor** (`src/audio/MusicConductor.js`)
   - Core class for the adaptive music system
   - Handles music transitions, leitmotifs, and real-time audio effects
   - Uses Tone.js for advanced audio processing

3. **MusicData** (`src/audio/musicData.js`)
   - Configuration for music assets and metadata
   - Defines areas, transitions, leitmotifs, and stingers

4. **MusicEffects** (`src/audio/MusicEffects.js`)
   - Creates and manages audio effects chains
   - Provides presets for different game situations

5. **MusicDebugHUD** (`src/audio/MusicDebugHUD.js`)
   - Debug overlay for visualizing music system state
   - Shows current area, tempo, effects, and waveform

The system is integrated with:
- `DungeonHandler.js` - For area transitions and combat state updates
- `PlayerController.js` - For player damage effects

Press 'M' in-game to toggle the music debug HUD.

## MP3 Naming Convention

The music system expects specific file names in specific locations. Here's a comprehensive guide:

### Main Area Loops

```
assets/music/[area_id]/loop.mp3
```

Examples:
- `assets/music/catacombs/loop.mp3` (100 BPM)
- `assets/music/lava_tubes/loop.mp3` (110 BPM)

### Transitions Between Areas

```
assets/music/[source_area_id]/transitions/to_[target_area_id].mp3
```

Examples:
- `assets/music/catacombs/transitions/to_lava_tubes.mp3`
- `assets/music/lava_tubes/transitions/to_catacombs.mp3`

### Leitmotifs (Layered Themes)

```
assets/music/[area_id]/leitmotifs/[leitmotif_name]_layer.mp3
```

Examples:
- `assets/music/catacombs/leitmotifs/mother_layer.mp3`
- `assets/music/catacombs/leitmotifs/guilt_layer.mp3`

### Special Room Music

```
assets/music/[area_id]/specialRooms/[room_type]/loop.mp3
assets/music/[area_id]/specialRooms/[room_type]/enter.mp3
assets/music/[area_id]/specialRooms/[room_type]/exit.mp3
```

Examples:
- `assets/music/catacombs/specialRooms/shop/loop.mp3`
- `assets/music/catacombs/specialRooms/shop/enter.mp3`
- `assets/music/catacombs/specialRooms/shop/exit.mp3`
- `assets/music/catacombs/specialRooms/altar/loop.mp3`

### Stingers (One-Shot Music Events)

```
assets/music/stingers/[stinger_name].mp3
```

Examples:
- `assets/music/stingers/player_hit.mp3`
- `assets/music/stingers/player_death.mp3`
- `assets/music/stingers/enemy_death.mp3`
- `assets/music/stingers/boss_appear.mp3`

### Important Notes

1. **File Names Must Match Configuration**: The file names and paths must exactly match what's defined in `src/audio/musicData.js`.

2. **Area IDs**: Use lowercase with underscores for area IDs (e.g., `catacombs`, `lava_tubes`).

3. **Room Types**: Use lowercase with underscores for room types (e.g., `shop`, `altar`).

4. **Leitmotif Names**: Use lowercase with underscores and add `_layer` suffix (e.g., `mother_layer`, `guilt_layer`).

5. **Stinger Names**: Use lowercase with underscores (e.g., `player_hit`, `boss_appear`).

6. **File Format**: While the system is designed for MP3 files, you can also use other formats supported by browsers (OGG, WAV) by changing the file extensions and paths in `musicData.js`.

7. **Loop Points**: For looping tracks, ensure they have clean loop points with no audible gaps or clicks at the transition point.

8. **BPM Information**: Each area has a specific tempo in beats per minute (BPM):
   - Catacombs: 100 BPM
   - Lava Tubes: 110 BPM

   Make sure your music files match these tempos for proper synchronization.
