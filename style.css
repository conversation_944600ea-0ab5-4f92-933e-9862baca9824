body {
    margin: 0;
    overflow: hidden; /* Prevent scrollbars */
    background-color: #000;
    color: #fff;
    font-family: sans-serif;
}

canvas {
    display: block; /* Remove extra space below canvas */
}

/* Dialogue visual effects animations */
@keyframes static-flicker {
    0% { opacity: 0.8; transform: translateX(0px); }
    10% { opacity: 0.2; transform: translateX(2px); }
    20% { opacity: 0.9; transform: translateX(-1px); }
    30% { opacity: 0.1; transform: translateX(1px); }
    40% { opacity: 0.7; transform: translateX(-2px); }
    50% { opacity: 0.3; transform: translateX(1px); }
    60% { opacity: 0.8; transform: translateX(0px); }
    70% { opacity: 0.1; transform: translateX(-1px); }
    80% { opacity: 0.6; transform: translateX(2px); }
    90% { opacity: 0.4; transform: translateX(-1px); }
    100% { opacity: 0.8; transform: translateX(0px); }
}

@keyframes distortion-wave {
    0% { transform: skewX(0deg) scaleX(1); filter: hue-rotate(0deg); }
    25% { transform: skewX(2deg) scaleX(1.02); filter: hue-rotate(90deg); }
    50% { transform: skewX(-1deg) scaleX(0.98); filter: hue-rotate(180deg); }
    75% { transform: skewX(1deg) scaleX(1.01); filter: hue-rotate(270deg); }
    100% { transform: skewX(0deg) scaleX(1); filter: hue-rotate(360deg); }
}

@keyframes glitch-shift {
    0% { transform: translateX(0px) translateY(0px); }
    10% { transform: translateX(-2px) translateY(1px); }
    20% { transform: translateX(2px) translateY(-1px); }
    30% { transform: translateX(-1px) translateY(2px); }
    40% { transform: translateX(1px) translateY(-2px); }
    50% { transform: translateX(-2px) translateY(-1px); }
    60% { transform: translateX(2px) translateY(1px); }
    70% { transform: translateX(-1px) translateY(-2px); }
    80% { transform: translateX(1px) translateY(2px); }
    90% { transform: translateX(-2px) translateY(1px); }
    100% { transform: translateX(0px) translateY(0px); }
}