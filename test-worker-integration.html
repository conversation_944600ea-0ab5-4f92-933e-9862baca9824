<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Worker Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-success {
            background: #2e7d32;
            border: 1px solid #4caf50;
        }
        .test-error {
            background: #c62828;
            border: 1px solid #f44336;
        }
        .test-warning {
            background: #f57c00;
            border: 1px solid #ff9800;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        pre {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #4CAF50; }
        .status-error { background: #f44336; }
        .status-warning { background: #ff9800; }
        .status-unknown { background: #666; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Worker Integration Test Suite</h1>
        <p>This page tests the worker thread integration system to ensure all components are working correctly.</p>
        
        <div class="test-section">
            <h2>🚀 Quick Status Check</h2>
            <button onclick="checkStatus()">Check Worker Status</button>
            <button onclick="runQuickTests()">Run Quick Tests</button>
            <button onclick="runFullTests()">Run Full Test Suite</button>
            <div id="status-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📊 System Information</h2>
            <div id="system-info"></div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Test Results</h2>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📈 Performance Metrics</h2>
            <button onclick="showPerformanceMetrics()">Show Performance Data</button>
            <div id="performance-results"></div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Debug Console</h2>
            <button onclick="clearDebugLog()">Clear Log</button>
            <pre id="debug-log"></pre>
        </div>
    </div>

    <script type="module">
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const debugElement = document.getElementById('debug-log');
            if (debugElement) {
                debugElement.textContent = debugLog.slice(-50).join('\n'); // Keep last 50 entries
                debugElement.scrollTop = debugElement.scrollHeight;
            }
            
            console.log(logEntry);
        }
        
        function addResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            const result = document.createElement('div');
            result.className = `test-result test-${type}`;
            
            const indicator = document.createElement('span');
            indicator.className = `status-indicator status-${type}`;
            
            result.appendChild(indicator);
            result.appendChild(document.createTextNode(message));
            container.appendChild(result);
        }
        
        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = '';
            }
        }
        
        window.checkStatus = async function() {
            clearResults('status-results');
            log('Checking worker integration status...');
            
            try {
                // Check if worker integration is available
                if (typeof window.workerIntegration === 'undefined') {
                    addResult('status-results', 'Worker integration not available globally', 'error');
                    return;
                }
                
                addResult('status-results', 'Worker integration available globally ✓', 'success');
                
                // Check if status checker is available
                if (typeof window.workerIntegrationStatus !== 'undefined') {
                    const status = window.workerIntegrationStatus.getStatus();
                    const message = window.workerIntegrationStatus.getStatusMessage();
                    addResult('status-results', message, status.overall === 'fully_functional' ? 'success' : 'warning');
                    
                    // Show component status
                    if (status.components) {
                        Object.entries(status.components).forEach(([name, component]) => {
                            const componentStatus = component.available || component.initialized ? 'success' : 'error';
                            addResult('status-results', `${name}: ${componentStatus === 'success' ? 'OK' : 'FAILED'}`, componentStatus);
                        });
                    }
                } else {
                    addResult('status-results', 'Status checker not available', 'warning');
                }
                
            } catch (error) {
                addResult('status-results', `Status check failed: ${error.message}`, 'error');
                log(`Status check error: ${error.message}`, 'error');
            }
        };
        
        window.runQuickTests = async function() {
            clearResults('test-results');
            log('Running quick tests...');

            const tests = [
                {
                    name: 'Worker Integration Available',
                    test: () => typeof window.workerIntegration !== 'undefined'
                },
                {
                    name: 'Worker Manager Available',
                    test: () => window.workerIntegration && typeof window.workerIntegration.workerManager !== 'undefined'
                },
                {
                    name: 'Performance Monitor Available',
                    test: () => window.workerExample && typeof window.workerExample.performanceMonitor !== 'undefined'
                },
                {
                    name: 'Test Results Available',
                    test: () => typeof window.workerTestResults !== 'undefined'
                },
                {
                    name: 'Bullet Pattern Worker Fix',
                    test: async () => {
                        try {
                            const pattern = await window.workerIntegration.generateBulletPattern({
                                patternType: 'spiral',
                                position: { x: 0, y: 1, z: 0 },
                                intensity: 0.5,
                                speedMultiplier: 1.0
                            });
                            return pattern && pattern.bullets && pattern.bullets.length > 0;
                        } catch (error) {
                            log(`Bullet pattern test error: ${error.message}`, 'error');
                            return false;
                        }
                    }
                },
                {
                    name: 'Mesh Processing Worker Fix',
                    test: async () => {
                        try {
                            const result = await window.workerIntegration.processMesh({
                                voxels: [{
                                    position: { x: 0, y: 0, z: 0 },
                                    color: { r: 1, g: 0, b: 0 },
                                    size: 1
                                }],
                                voxelScale: 1.0
                            }, 'processVoxelMesh');
                            return result && result.meshData && result.meshData.length > 0;
                        } catch (error) {
                            log(`Mesh processing test error: ${error.message}`, 'error');
                            return false;
                        }
                    }
                }
            ];
            
            for (const test of tests) {
                try {
                    const result = await test.test();
                    addResult('test-results', `${test.name}: ${result ? 'PASS' : 'FAIL'}`, result ? 'success' : 'error');
                    log(`Quick test ${test.name}: ${result ? 'PASS' : 'FAIL'}`);
                } catch (error) {
                    addResult('test-results', `${test.name}: ERROR - ${error.message}`, 'error');
                    log(`Quick test ${test.name} error: ${error.message}`, 'error');
                }
            }
        };
        
        window.runFullTests = async function() {
            clearResults('test-results');
            log('Running full test suite...');
            
            try {
                if (typeof window.workerIntegrationTest !== 'undefined') {
                    addResult('test-results', 'Starting comprehensive test suite...', 'success');
                    const results = await window.workerIntegrationTest.runAllTests();
                    
                    if (results.error) {
                        addResult('test-results', `Test suite failed: ${results.error}`, 'error');
                    } else {
                        addResult('test-results', `Tests completed: ${results.passedTests}/${results.totalTests} passed (${results.successRate.toFixed(1)}%)`, 
                                results.successRate > 80 ? 'success' : 'warning');
                        
                        // Show individual test results
                        results.results.forEach(result => {
                            addResult('test-results', `${result.testName}: ${result.success ? 'PASS' : 'FAIL'} - ${result.message}`, 
                                    result.success ? 'success' : 'error');
                        });
                    }
                } else {
                    addResult('test-results', 'Full test suite not available - tests may still be loading', 'warning');
                }
            } catch (error) {
                addResult('test-results', `Full test suite failed: ${error.message}`, 'error');
                log(`Full test suite error: ${error.message}`, 'error');
            }
        };
        
        window.showPerformanceMetrics = async function() {
            clearResults('performance-results');
            log('Gathering performance metrics...');
            
            try {
                // System information
                const systemInfo = {
                    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                    deviceMemory: navigator.deviceMemory || 'unknown',
                    userAgent: navigator.userAgent.substring(0, 100) + '...'
                };
                
                addResult('performance-results', `CPU Cores: ${systemInfo.hardwareConcurrency}`, 'success');
                addResult('performance-results', `Device Memory: ${systemInfo.deviceMemory}GB`, 'success');
                
                // Memory usage
                if (performance.memory) {
                    const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                    const totalMB = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
                    addResult('performance-results', `Memory Usage: ${memoryMB}MB / ${totalMB}MB`, 'success');
                }
                
                // Worker metrics
                if (window.workerIntegration) {
                    const metrics = await window.workerIntegration.getPerformanceMetrics();
                    if (metrics && !metrics.error) {
                        addResult('performance-results', `Worker Metrics Available: ${Object.keys(metrics.workers || {}).length} worker types`, 'success');
                    }
                }
                
            } catch (error) {
                addResult('performance-results', `Performance metrics failed: ${error.message}`, 'error');
                log(`Performance metrics error: ${error.message}`, 'error');
            }
        };
        
        window.clearDebugLog = function() {
            debugLog = [];
            document.getElementById('debug-log').textContent = '';
        };
        
        // Auto-populate system info on load
        window.addEventListener('load', () => {
            const systemInfoDiv = document.getElementById('system-info');
            systemInfoDiv.innerHTML = `
                <p><strong>Browser:</strong> ${navigator.userAgent.split(' ').pop()}</p>
                <p><strong>CPU Cores:</strong> ${navigator.hardwareConcurrency || 'unknown'}</p>
                <p><strong>Device Memory:</strong> ${navigator.deviceMemory || 'unknown'}GB</p>
                <p><strong>Platform:</strong> ${navigator.platform}</p>
                <p><strong>Language:</strong> ${navigator.language}</p>
            `;
            
            log('Worker Integration Test Suite loaded');
            
            // Auto-check status after a short delay
            setTimeout(() => {
                checkStatus();
            }, 2000);
        });
    </script>
</body>
</html>
