// Debug script to check tomb guardian spawning
import enemySpawnManager from './src/systems/EnemySpawnManager.js';
import { getEnemyData } from './src/entities/EnemyTypes.js';
import { ENEMY_TYPES } from './src/entities/EnemyTypes.js';

console.log('=== TOMB GUARDIAN DEBUG ===');

// Check if tomb_guardian exists in enemy types
console.log('ENEMY_TYPES.TOMB_GUARDIAN:', ENEMY_TYPES.TOMB_GUARDIAN);

// Check if we can get enemy data
const tombGuardianData = getEnemyData('tomb_guardian');
console.log('Tomb Guardian Data:', tombGuardianData);

// Check enemy pools
const catacombsPools = enemySpawnManager.getEnemySpawnTable('catacombs');
console.log('Catacombs Enemy Pools:', catacombsPools);
console.log('Heavy enemies in catacombs:', catacombsPools.heavy);

// Test enemy group selection multiple times
console.log('\n=== Testing Enemy Group Selection ===');
enemySpawnManager.setDebugMode(true);

for (let i = 0; i < 10; i++) {
    console.log(`\nTest ${i + 1}:`);
    const enemyGroup = enemySpawnManager.chooseEnemyGroup({
        biome: 'catacombs',
        roomType: 'normal',
        floorLevel: 5,
        seed: Date.now() + i
    });
    console.log('Selected enemies:', enemyGroup);
}

// Test specifically with room types that use heavy enemies
console.log('\n=== Testing Elite/Mini-Boss Rooms ===');
const specialRoomTypes = ['elite', 'mini_boss'];
specialRoomTypes.forEach(roomType => {
    console.log(`\nTesting ${roomType} room:`);
    const group = enemySpawnManager.chooseEnemyGroup({
        biome: 'catacombs',
        roomType: roomType,
        floorLevel: 5,
        seed: Date.now()
    });
    console.log('Selected enemies:', group);
});