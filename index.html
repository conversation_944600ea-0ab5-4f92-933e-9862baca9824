<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Soulpath</title>

    <!-- CONTINUE WHEN TABBED OUT: Override page visibility to keep game running -->
    <script>
        window.gameRunsInBackground = true;

        // Override document.hidden to always return false (game never pauses)
        Object.defineProperty(document, 'hidden', {
            get: function() { return false; },
            configurable: true
        });

        // Override document.visibilityState to always return 'visible'
        Object.defineProperty(document, 'visibilityState', {
            get: function() { return 'visible'; },
            configurable: true
        });

        console.log('[Background Mode] Game will continue running when tabbed out');

        // HARD REFRESH SYSTEM: Save game state and reload with fresh assets
        window.refreshCurrentRoom = function() {
            console.log('💾 SAVE & RELOAD: Saving game state and performing hard refresh...');

            // Find dungeon handler
            let dungeonHandler = window.dungeonHandler;
            if (!dungeonHandler && window.sceneManager?.currentHandler) {
                const currentHandler = window.sceneManager.currentHandler;
                if (currentHandler.constructor.name === 'DungeonHandler') {
                    dungeonHandler = currentHandler;
                }
            }

            if (!dungeonHandler || (!dungeonHandler.currentRoomId && dungeonHandler.currentRoomId !== 0)) {
                console.error('❌ Must be in dungeon to use refreshCurrentRoom()');
                console.log('💡 Enter the dungeon first, then use this command');
                return;
            }

            // SAVE COMPLETE GAME STATE
            const gameState = {
                roomId: dungeonHandler.currentRoomId,
                position: {
                    x: dungeonHandler.player?.position.x || 0,
                    y: dungeonHandler.player?.position.y || 0,
                    z: dungeonHandler.player?.position.z || 0
                },
                rotation: {
                    x: dungeonHandler.player?.rotation.x || 0,
                    y: dungeonHandler.player?.rotation.y || 0,
                    z: dungeonHandler.player?.rotation.z || 0
                },
                floorNumber: dungeonHandler.currentFloorNumber || 1,
                timestamp: Date.now()
            };

            console.log('💾 Saving complete game state:', gameState);
            sessionStorage.setItem('devRefreshSave', JSON.stringify(gameState));

            // Update cache buster for fresh assets
            window.assetCacheBuster = Date.now();
            console.log(`🔄 Updated cache buster: ${window.assetCacheBuster}`);

            // HARD RELOAD with cache busting
            console.log('🚀 Performing hard reload...');
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('cb', window.assetCacheBuster);
            currentUrl.searchParams.set('devReload', '1'); // Flag for auto-restoration
            window.location.href = currentUrl.toString();
        };

        // ASSET CACHE BUSTING: Add timestamp to asset URLs in development
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
            window.assetCacheBuster = Date.now();

            // Override fetch to add cache busting for assets
            const originalFetch = window.fetch;
            window.fetch = function(url, options) {
                if (typeof url === 'string' && (
                    url.includes('.jpg') || url.includes('.png') ||
                    url.includes('.mp3') || url.includes('.wav') ||
                    url.includes('.glb') || url.includes('.gltf')
                )) {
                    const separator = url.includes('?') ? '&' : '?';
                    url += `${separator}cb=${window.assetCacheBuster}`;
                }
                return originalFetch.call(this, url, options);
            };

            console.log('[Asset Refresh] Cache busting enabled for development');

            // Command to manually refresh cache buster (now optional since refreshCurrentRoom() does it automatically)
            window.refreshAssets = function() {
                window.assetCacheBuster = Date.now();
                console.log(`🔄 Updated asset cache buster to: ${window.assetCacheBuster}`);
                console.log('💡 Note: refreshCurrentRoom() now does this automatically');
            };

            // HARD REFRESH: Force complete page reload (use when soft refresh doesn't work)
            window.hardRefreshRoom = function() {
                console.log('🚀 Performing HARD refresh with complete page reload...');

                // Find dungeon handler
                let dungeonHandler = window.dungeonHandler;
                if (!dungeonHandler && window.sceneManager?.currentHandler) {
                    const currentHandler = window.sceneManager.currentHandler;
                    if (currentHandler.constructor.name === 'DungeonHandler') {
                        dungeonHandler = currentHandler;
                    }
                }

                if (!dungeonHandler || (!dungeonHandler.currentRoomId && dungeonHandler.currentRoomId !== 0)) {
                    console.error('❌ Must be in dungeon to use hardRefreshRoom()');
                    return;
                }

                const currentRoomId = dungeonHandler.currentRoomId;
                const playerPosition = dungeonHandler.player?.position.clone();
                const playerRotation = dungeonHandler.player?.rotation.clone();

                // Update cache buster
                window.assetCacheBuster = Date.now();

                // Save player state for restoration
                if (playerPosition && playerRotation) {
                    sessionStorage.setItem('refreshPlayerState', JSON.stringify({
                        roomId: currentRoomId,
                        position: { x: playerPosition.x, y: playerPosition.y, z: playerPosition.z },
                        rotation: { x: playerRotation.x, y: playerRotation.y, z: playerRotation.z }
                    }));
                    console.log('💾 Saved player state for hard refresh');
                }

                // Force complete reload
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('cb', window.assetCacheBuster);
                window.location.href = currentUrl.toString();
            };

            console.log('🛠️ Development commands available:');
            console.log('  - refreshCurrentRoom() - Save state, hard refresh, auto-restore position');
            console.log('  - restoreFromSave() - Manual restore if auto-restore fails');
            console.log('  - refreshAssets() - Manually update cache buster (optional)');
            console.log('  - startChessGame() - Start Devil\'s Chess Game from anywhere in dungeon');
            console.log('  - testSceneSwitch() - Test scene switching with green background');

            // Add manual restore command that's always available
            window.restoreFromSave = () => {
                const savedState = sessionStorage.getItem('devRefreshSave');
                if (!savedState) {
                    console.log('❌ No saved state found. Use refreshCurrentRoom() first.');
                    return;
                }

                if (!window.dungeonHandler) {
                    console.log('❌ Must be in dungeon to restore. Enter dungeon first.');
                    return;
                }

                try {
                    const state = JSON.parse(savedState);
                    console.log('🔄 Manually restoring saved state:', state);

                    window.dungeonHandler.loadRoom(state.roomId, 'refresh');
                    setTimeout(() => {
                        if (window.dungeonHandler.player) {
                            window.dungeonHandler.player.position.set(state.position.x, state.position.y, state.position.z);
                            window.dungeonHandler.player.rotation.set(state.rotation.x, state.rotation.y, state.rotation.z);
                            console.log('✅ Manual restoration complete!');
                            sessionStorage.removeItem('devRefreshSave');
                        }
                    }, 1000);
                } catch (error) {
                    console.error('❌ Failed to restore:', error);
                }
            };

            // Add simple scene test command
            window.testSceneSwitch = async () => {
                console.log('Testing scene switch...');
                const THREE = await import('three');
                
                const testScene = new THREE.Scene();
                testScene.background = new THREE.Color(0x00ff00); // Bright green
                
                // Add a big cube
                const geometry = new THREE.BoxGeometry(10, 10, 10);
                const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });
                const cube = new THREE.Mesh(geometry, material);
                testScene.add(cube);
                
                window.sceneManager.setRenderScene(testScene);
                console.log('Scene switched to test scene with green background');
            };

            // Add chess game command
            window.startChessGame = async (difficulty = 'normal') => {
                if (!window.sceneManager) {
                    console.log('❌ Scene manager not available. Wait for game to load.');
                    return;
                }

                // More flexible dungeon detection
                const isDungeon = window.sceneManager?.currentScene === 'DUNGEON' || 
                                window.dungeonHandler || 
                                window.sceneManager?.sceneHandlers?.DungeonHandler;
                
                if (!isDungeon) {
                    console.log('❌ Must be in dungeon to start chess game. Enter dungeon first.');
                    console.log('Debug - currentScene:', window.sceneManager?.currentScene);
                    console.log('Debug - dungeonHandler:', !!window.dungeonHandler);
                    console.log('Debug - sceneHandlers:', Object.keys(window.sceneManager?.sceneHandlers || {}));
                    return;
                }

                const validDifficulties = ['easy', 'normal', 'hard', 'expert'];
                if (!validDifficulties.includes(difficulty)) {
                    console.log(`❌ Invalid difficulty. Use: ${validDifficulties.join(', ')}`);
                    return;
                }

                try {
                    console.log(`♟️ Starting 3D Chess Minigame with ${difficulty} difficulty...`);
                    
                    // Import ChessGame class
                    const { ChessGame } = await import('./src/systems/ChessGame.js');
                    
                    // Create and start chess game (now uses 3D minigame system)
                    const chessGame = new ChessGame(window.sceneManager, difficulty, window.dungeonHandler);
                    const gameResult = await chessGame.startGame();
                    
                    console.log('♟️ Chess game completed. Result:', gameResult);
                    
                    if (gameResult === 'won') {
                        console.log('🎉 You defeated the Devil at chess!');
                    } else if (gameResult === 'lost') {
                        console.log('😈 The Devil won this round...');
                    } else {
                        console.log('🤝 The game ended in a draw.');
                    }
                    
                } catch (error) {
                    console.error('❌ Failed to start chess game:', error);
                    console.error('Error details:', error.stack);
                }
            };

            // AUTO-RESTORE SYSTEM: Load saved game state after refresh
            window.addEventListener('load', () => {
                // Check if this is a dev reload
                const urlParams = new URLSearchParams(window.location.search);
                const isDevReload = urlParams.get('devReload') === '1';
                const savedState = sessionStorage.getItem('devRefreshSave');

                if (isDevReload && savedState) {
                    console.log('🎮 DEV RELOAD DETECTED - Setting up smart restore...');

                    // Clean up URL
                    const cleanUrl = new URL(window.location);
                    cleanUrl.searchParams.delete('devReload');
                    cleanUrl.searchParams.delete('cb');
                    window.history.replaceState({}, '', cleanUrl.toString());

                    // Parse saved state
                    let gameState;
                    try {
                        gameState = JSON.parse(savedState);
                        console.log('💾 Loaded saved game state:', gameState);
                    } catch (error) {
                        console.error('❌ Failed to parse saved state:', error);
                        sessionStorage.removeItem('devRefreshSave');
                        return;
                    }

                    // SMART RESTORE: Create a restore function that works when game is ready
                    console.log('🎯 Creating smart restore function...');
                    console.log('💡 The game will show a restore button when ready, or you can run: quickRestore()');

                    // Create immediate restore function
                    window.quickRestore = () => {
                        console.log('🔄 Quick restore triggered...');

                        // Try to find dungeon handler
                        let dungeonHandler = window.dungeonHandler;
                        if (!dungeonHandler && window.sceneManager?.currentHandler?.constructor?.name === 'DungeonHandler') {
                            dungeonHandler = window.sceneManager.currentHandler;
                        }

                        if (dungeonHandler) {
                            console.log('✅ DungeonHandler found, restoring...');
                            dungeonHandler.loadRoom(gameState.roomId, 'refresh');
                            setTimeout(() => {
                                if (dungeonHandler.player) {
                                    dungeonHandler.player.position.set(gameState.position.x, gameState.position.y, gameState.position.z);
                                    dungeonHandler.player.rotation.set(gameState.rotation.x, gameState.rotation.y, gameState.rotation.z);
                                    console.log('🎉 QUICK RESTORE COMPLETE!');
                                    console.log(`📍 Room: ${gameState.roomId}, Position: (${gameState.position.x.toFixed(2)}, ${gameState.position.y.toFixed(2)}, ${gameState.position.z.toFixed(2)})`);
                                    sessionStorage.removeItem('devRefreshSave');
                                }
                            }, 1000);
                        } else {
                            console.log('❌ DungeonHandler not ready. Enter dungeon first, then run: quickRestore()');
                        }
                    };

                    // Try auto-restore after game loads
                    setTimeout(() => {
                        console.log('🔄 Attempting auto-restore...');
                        if (window.sceneManager?.currentHandler?.constructor?.name === 'HeroPageHandler') {
                            console.log('🚪 On hero page - enter dungeon manually and run: quickRestore()');
                            console.log('💡 Or click the "RESTORE SAVED POSITION" button that will appear');
                        } else {
                            window.quickRestore();
                        }
                    }, 8000);
                }
            });
        }
    </script>

    <!-- Favicon Links -->
    <link rel="apple-touch-icon" sizes="180x180" href="favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon/favicon-16x16.png">
    <link rel="manifest" href="favicon/site.webmanifest?v=2">
    <link rel="shortcut icon" href="favicon/favicon.ico">

    <!-- PWA and Mobile Meta Tags -->
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Soulpath">
    <meta name="application-name" content="Soulpath">
    <meta name="msapplication-TileColor" content="#000000">
    <meta name="msapplication-config" content="favicon/browserconfig.xml">

    <!-- Prevent Safari Reader Mode -->
    <meta name="format-detection" content="telephone=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">

    <!-- Force web app mode -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black-fullscreen">

    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="src/styles/modern-minimap.css">
    <style>
        /* Basic styles to ensure canvas fills screen and hide initial elements */
        body {
            margin: 0;
            overflow: hidden;
            background-color: #000;
            color: #fff;
            font-family: sans-serif;
            /* MOBILE FIX: Prevent browser zoom and touch behaviors */
            touch-action: manipulation;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }
        canvas { display: block; width: 100%; height: 100%; } /* Ensure canvas fills body */
        #ui-container {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            pointer-events: none; /* Allow clicks to pass through to canvas by default */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .clickable {
             pointer-events: auto; /* Make specific UI elements clickable */
             cursor: pointer;
        }
        /* Simple mute button style */
        #mute-button {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border: 1px solid #fff;
            border-radius: 5px;
            color: #fff;
            pointer-events: auto;
            cursor: pointer;
            z-index: 100;
        }
         #mute-button:hover {
            background-color: rgba(255, 255, 255, 0.4);
         }

        /* Fade Overlay */
        #fade-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            opacity: 0;
            pointer-events: none; /* Allow clicks through when invisible */
            z-index: 2000; /* Ensure it's above the dialogue */
            transition: opacity 0.2s ease-in-out; /* Reduced from 1s for instant transitions */
            display: none; /* Start hidden */
        }
        #fade-overlay.active {
            opacity: 1;
            pointer-events: auto; /* Block clicks during fade */
        }

        /* Dialogue Box Styles */
        #dialogue-container {
            position: absolute;
            bottom: 10%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            max-width: 600px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace; /* Pixel-style font */
            font-size: 20px;
            line-height: 1.4;
            z-index: 1000;
            opacity: 1;
            transition: opacity 0.5s ease-out;
        }
        #dialogue-container.hidden {
            opacity: 0;
            pointer-events: none;
        }
        #dialogue-text {
            margin: 0;
            padding: 0;
            white-space: pre-wrap; /* Preserve line breaks */
        }
        #dialogue-text::after {
            content: ''; /* Changed from '_' to empty */
            /* animation: blink 1s step-end infinite; */ /* Optionally remove blink */
        }
        @keyframes blink {
            50% { opacity: 0; }
        }

        #dialogue-options {
            margin-top: 15px;
            display: flex;
            flex-direction: column; /* Stack options vertically */
            align-items: flex-start; /* Align options left */
        }

        .dialogue-option-button {
            background: none;
            border: 2px solid #fff;
            border-radius: 3px;
            color: #ffff00; /* Yellow options like Undertale? */
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            padding: 5px 10px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: left;
            transition: background-color 0.2s, color 0.2s;
        }
        .dialogue-option-button:hover,
        .dialogue-option-button:focus {
            background-color: #fff;
            color: #000;
            outline: none;
        }
        /* Add this rule for keyboard highlighting */
        .dialogue-option-button.highlighted {
             background-color: #fff;
             color: #000;
             outline: none;
        }
         #dialogue-progress {
             position: absolute;
             top: 10px;
             right: 15px;
             font-size: 14px;
             color: #aaa;
         }

        /* Hide Canvas Initially */
        #webgl-canvas {
             position: absolute;
             top: 0;
             left: 0;
             width: 100%;
             height: 100%;
             z-index: 1; /* Behind UI */
             /* Start hidden */
             /* opacity: 0; */
             /* transition: opacity 0.5s ease-in; */
        }
        /* #webgl-canvas.visible { */
             /* opacity: 1; */
        /* } */

        /* --- Video Overlay Styles --- */
        #video-overlay {
            display: none; /* Initially hidden */
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.85); /* Semi-transparent black background */
            z-index: 1000; /* Ensure it's above canvas, maybe below dialogue */
            opacity: 0;
            transition: opacity 0.5s ease-in-out; /* Fade transition */
            justify-content: center; /* Center content horizontally */
            align-items: center; /* Center content vertically */
            /* MOBILE FIX: Enable touch events */
            pointer-events: auto;
            cursor: pointer;
        }

        #video-overlay.visible {
            display: flex; /* Use flexbox for centering when visible */
        }

        #video-overlay.fading-in {
            opacity: 1;
        }

        #video-container {
            padding: 5px; /* <<< Keep slim padding */
            background-color: white; /* <<< Keep background */
            border: 2px solid #fff; /* <<< Keep slim border */
            border-radius: 5px; /* <<< Keep slight radius */
            /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3); */
            /* Adjust max-width/max-height if needed to constrain size */
            /* max-width: 60%; */ /* <<< REMOVED */
            /* max-height: 60%; */ /* <<< REMOVED */
            display: inline-flex; /* <<< Changed to inline-flex to shrink-wrap */
            /* justify-content: center; */ /* No longer needed with inline-flex */
            /* align-items: center; */ /* No longer needed with inline-flex */
            /* Ensure it respects overlay centering */
            max-width: 90%; /* Added fallback max-width for container */
            max-height: 90%; /* Added fallback max-height for container */
        }

        /* Apply sizing to both videos using a common class */
        .video-player {
            display: block; /* Prevents extra space below video */
            max-width: 60vw; /* <<< Applied max-width directly using viewport units */
            max-height: 60vh; /* <<< Applied max-height directly using viewport units */
            height: auto; /* Maintain aspect ratio */
            width: auto; /* Maintain aspect ratio */
            object-fit: contain; /* Ensure video scales nicely within bounds */
        }

        /* Fullscreen video style for endintro */
        .video-player.fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            max-width: 100vw !important;
            max-height: 100vh !important;
            object-fit: cover !important;
            z-index: 10000 !important;
            background-color: black !important;
        }
        /* --- End Video Overlay Styles --- */

        /* --- Minimap Styles --- */
        #minimap-container {
            display: block; /* Show minimap container */
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 9999;
            pointer-events: none; /* Allow clicking through container */
        }
        #minimap-grid {
            /* SVG-based minimap container - NO BLACK BOX */
            position: fixed !important;
            top: 10px !important;
            right: 10px !important;
            background: none !important;
            background-color: transparent !important;
            border: none !important;
            border-radius: 0 !important;
            padding: 0 !important;
            margin: 0 !important;
            opacity: 0.95 !important;
            z-index: 9999 !important;
            max-width: 300px;
            max-height: 300px;
            display: block !important;
            visibility: visible !important;
            box-shadow: none !important;
        }
        
        #minimap-grid svg {
            display: block;
            max-width: 100%;
            max-height: 100%;
        }
        
        /* Legacy room styles - kept for backwards compatibility but not used in SVG version */
        .minimap-room {
            width: 10px;
            height: 10px;
            background-color: #333;
            border: 1px solid #222;
            margin: 0;
            padding: 0;
        }
        .minimap-room.visited {
            background-color: #666;
            border-color: #444;
        }
        .minimap-room.current {
            background-color: #fff;
            border-color: #aaa;
            box-shadow: 0 0 3px 1px #fff;
        }
        .minimap-room.boss {
            background-color: #ff4444;
            border-color: #cc2222;
            box-shadow: 0 0 2px 1px #ff4444;
        }
        .minimap-room.event {
            background-color: #44aaff;
            border-color: #2288cc;
            box-shadow: 0 0 2px 1px #44aaff;
        }
        .minimap-room.start {
            background-color: #44ff44;
            border-color: #22cc22;
            box-shadow: 0 0 2px 1px #44ff44;
        }
        /* --- End Minimap Styles --- */

        /* --- Health Orbs Styles --- */
        #health-orbs-container {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1500; /* Above most UI */
            pointer-events: none; /* Allow clicking through */
        }

        @keyframes pulse {
            0% { opacity: 0.4; }
            50% { opacity: 0.8; }
            100% { opacity: 0.4; }
        }
        /* --- End Health Orbs Styles --- */

        /* --- Boss Health Bar Styles --- */
        #boss-health-container {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 400px;
            max-width: 80vw;
            z-index: 1500; /* Above most UI */
            pointer-events: none; /* Allow clicking through */
            opacity: 0; /* Start hidden */
            visibility: hidden; /* Start hidden */
            transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
        }

        #boss-health-container.visible {
            opacity: 1;
            visibility: visible;
        }

        #boss-name {
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 8px 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        #boss-health-bar {
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-top: none;
            border-radius: 0 0 5px 5px;
            height: 20px;
            position: relative;
            overflow: hidden;
        }

        #boss-health-fill {
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, #ff0040, #ff6060);
            transition: width 0.3s ease-out;
            border-radius: 0 0 2px 2px;
            box-shadow: 0 0 10px rgba(255, 0, 64, 0.5);
        }

        /* Boss health bar color variants */
        #boss-health-container.boss-color-red #boss-health-fill {
            background: linear-gradient(90deg, #ff0040, #ff6060);
            box-shadow: 0 0 10px rgba(255, 0, 64, 0.5);
        }
        #boss-health-container.boss-color-magenta #boss-health-fill {
            background: linear-gradient(90deg, #ff0080, #ff60a0);
            box-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
        }
        #boss-health-container.boss-color-purple #boss-health-fill {
            background: linear-gradient(90deg, #8000ff, #a060ff);
            box-shadow: 0 0 10px rgba(128, 0, 255, 0.5);
        }
        #boss-health-container.boss-color-blue #boss-health-fill {
            background: linear-gradient(90deg, #0080ff, #60a0ff);
            box-shadow: 0 0 10px rgba(0, 128, 255, 0.5);
        }
        #boss-health-container.boss-color-green #boss-health-fill {
            background: linear-gradient(90deg, #00ff40, #60ff80);
            box-shadow: 0 0 10px rgba(0, 255, 64, 0.5);
        }
        #boss-health-container.boss-color-orange #boss-health-fill {
            background: linear-gradient(90deg, #ff8000, #ffa060);
            box-shadow: 0 0 10px rgba(255, 128, 0, 0.5);
        }
        #boss-health-container.boss-color-yellow #boss-health-fill {
            background: linear-gradient(90deg, #ffff00, #ffff60);
            box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
        }

        /* Adjust for mobile landscape */
        @media (orientation: landscape) and (pointer: coarse), (orientation: landscape) and (hover: none) {
            #boss-health-container {
                width: 300px;
                top: 15px;
            }
            #boss-name {
                font-size: 14px;
                padding: 6px 12px;
            }
            #boss-health-bar {
                height: 16px;
            }
        }
        /* --- End Boss Health Bar Styles --- */

        #area-name-display {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          /* Styling */
          padding: 15px 30px;
          background-color: rgba(0, 0, 0, 0.75);
          color: white;
          font-size: 2.5em;
          font-family: 'Arial', sans-serif;
          font-weight: bold;
          text-align: center;
          border-radius: 8px;

          /* Layering */
          z-index: 1500; /* Same as minimap, above dialogue/canvas */

          /* Initial State & Transition */
          opacity: 0; /* Start hidden */
          visibility: hidden; /* Start not visible */
          transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;

          /* Interaction */
          pointer-events: none; /* Don't block clicks */
        }

        #total-room-count-display {
          display: none; /* Hide total room count display box */
          opacity: 0; /* Start hidden */
          visibility: hidden; /* Start hidden */
          transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
          pointer-events: none;
        }

        /* --- Joystick Styles --- */
        .joystick-zone {
            display: none; /* Hidden by default */
            position: fixed; /* MOBILE FIX: Use fixed positioning */
            bottom: 5vh; /* More space from bottom */
            width: 120px; /* MOBILE FIX: Fixed pixel size for consistency */
            height: 120px;
            background-color: rgba(128, 128, 128, 0.4); /* Slightly more visible grey background */
            border-radius: 50%; /* Make it circular */
            z-index: 10000; /* Above blur overlay (9998) for microphone permission dialogue */
            pointer-events: auto; /* Allow touch */
            /* MOBILE FIX: Prevent browser behaviors */
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            /* MOBILE FIX: Ensure proper container for nipple positioning */
            overflow: visible;
            box-sizing: border-box;
        }

        #joystick-left {
            left: 10vw; /* MOBILE FIX: Move further from edge to avoid Safari gestures */
        }

        #joystick-right {
            right: 10vw; /* MOBILE FIX: Move further from edge to avoid Safari gestures */
        }

        /* NippleJS specific styles (can be customized further) */
        .nipple .front {
             background-color: rgba(100, 100, 100, 0.9) !important; /* More visible gray handle */
             width: 40px !important; /* Smaller size for better fit in 80px joystick */
             height: 40px !important;
             border-radius: 50% !important;
             border: 2px solid rgba(220, 220, 220, 1.0) !important; /* More visible border */
             /* MOBILE FIX: Ensure centered positioning */
             z-index: 10001 !important; /* Above joystick zone and blur overlay */
             position: absolute !important;
             /* The nipple library handles positioning, but ensure it's centered when at rest */
        }
        .nipple .back {
             background-color: transparent !important; /* Make back transparent to show grey circle */
             border-radius: 50% !important;
             border: none !important;
             /* MOBILE FIX: Let nipple library handle back positioning */
             z-index: 1700 !important;
        }

        /* MOBILE FIX: Ensure nipple container stays on top and prevents zoom */
        .nipple {
            z-index: 1700 !important;
            position: absolute !important;
            touch-action: none !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            user-select: none !important;
            -webkit-tap-highlight-color: transparent !important;
            /* Prevent double-tap zoom */
            -ms-touch-action: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            user-select: none !important;
        }

        /* Show joysticks only on landscape touch devices */
        @media (orientation: landscape) and (pointer: coarse) {
            .joystick-zone {
                display: block;
            }
        }
         @media (orientation: landscape) and (hover: none) {
             /* Alternative for devices that don't report pointer: coarse reliably */
             .joystick-zone {
                 display: block;
             }
         }

        /* --- Portrait Mode Touch Zones (Invisible Virtual Joysticks) --- */
        .portrait-touch-zone {
            display: none; /* Hidden by default */
            position: fixed;
            top: 0;
            width: 50vw; /* Half screen width */
            height: 100vh; /* Full screen height */
            background-color: transparent; /* Invisible */
            z-index: 1600; /* Above other UI */
            pointer-events: auto; /* Enable touch */
            /* MOBILE FIX: Prevent browser behaviors */
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        #portrait-touch-left {
            left: 0; /* Left half of screen */
        }

        #portrait-touch-right {
            right: 0; /* Right half of screen */
        }

        /* Show portrait touch zones only on portrait touch devices AND only during dungeon gameplay */
        /* These will be controlled by JavaScript to only show when needed */
        @media (orientation: portrait) and (pointer: coarse) {
            .portrait-touch-zone.dungeon-active {
                display: block;
            }
            /* Hide landscape joysticks in portrait */
            .joystick-zone {
                display: none !important;
            }
        }
        @media (orientation: portrait) and (hover: none) {
            /* Alternative for devices that don't report pointer: coarse reliably */
            .portrait-touch-zone.dungeon-active {
                display: block;
            }
            /* Hide landscape joysticks in portrait */
            .joystick-zone {
                display: none !important;
            }
        }

        /* --- Center Touch Zone for Camera Toggle --- */
        #center-touch-zone {
            position: fixed;
            top: 15%; /* Positioned to avoid joystick areas in landscape */
            left: 15%; /* Positioned to avoid joystick areas in landscape */
            width: 70%; /* Large enough to cover center area */
            height: 70%; /* Large enough to cover center area */
            background-color: transparent; /* Invisible */
            z-index: 1650; /* Higher than joysticks (1600) to ensure it's on top */
            pointer-events: auto; /* Enable touch */
            /* MOBILE FIX: Prevent browser behaviors */
            touch-action: none;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;
            /* Only show in dungeon state - will be controlled by JavaScript */
            display: none;
        }
        /* --- End Center Touch Zone --- */

        /* --- End Joystick Styles --- */

        /* --- Background Blur Overlay --- */
        #background-blur-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 9998;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            pointer-events: none;
        }
        #background-blur-overlay.active {
            opacity: 1;
            pointer-events: auto;
        }
        /* --- End Background Blur Overlay --- */

        /* --- Rotate Message Style (Dialogue Design) --- */
        #rotate-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 600px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 20px;
            line-height: 1.4;
            text-align: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
            pointer-events: none;
        }
        #rotate-message.active {
            opacity: 1;
            pointer-events: auto;
        }
        /* --- End Rotate Message Style --- */

        /* --- Adjust Dialogue for Mobile Landscape --- */
        @media (orientation: landscape) and (pointer: coarse), (orientation: landscape) and (hover: none) {
            #dialogue-container {
                width: 65%; /* Reduced from 80% */
                max-width: 500px; /* Reduced max-width */
                bottom: 18%; /* Raised from 10% to clear joysticks more */
                padding: 15px; /* Slightly smaller padding */
                font-size: 16px; /* Slightly smaller font */
            }
            .dialogue-option-button {
                 font-size: 14px; /* Smaller option font */
                 padding: 4px 8px;
            }
            #dialogue-progress {
                 font-size: 12px; /* Smaller progress font */
            }
        }
        /* --- End Dialogue Adjustment --- */

        /* After the #fade-overlay element, add the loading message element */
        #loading-message {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            z-index: 2000;
            display: none;
            pointer-events: none;
        }

    </style>
</head>
<body onclick="initAudioContext()">
    <div id="dialogue-container" class="hidden">
        <span id="dialogue-progress"></span>
        <p id="dialogue-text"></p>
        <div id="dialogue-options"></div>
    </div>

    <div id="ui-container">
        <!-- UI elements like progress indicators might go here if not fully 3D -->

        <!-- Health Orbs Container -->
        <div id="health-orbs-container"></div>

        <!-- Boss Health Bar Container -->
        <div id="boss-health-container">
            <div id="boss-name">Boss Name</div>
            <div id="boss-health-bar">
                <div id="boss-health-fill"></div>
            </div>
        </div>

        <!-- Area Name Display Element -->
        <div id="area-name-display"></div>

        <!-- Total Room Count Display (for ESP) -->
        <div id="total-room-count-display"></div>

        <!-- Minimap Container -->
        <div id="minimap-container">
            <div id="minimap-grid"></div>
        </div>

        <!-- Other UI elements? -->
    </div>

    <div id="fade-overlay"></div>

    <!-- Video Overlay Structure -->
    <div id="video-overlay">
        <div id="video-container">
            <!-- Flicker Video -->
            <video id="intro-video" class="video-player" width="640" height="480" muted playsinline loop style="display: none;"> <!-- Added class -->
                <source src="assets/textures/characters/flicker.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <!-- Reborn Video -->
            <video id="reborn-video" class="video-player" width="640" height="480" muted playsinline style="display: none;"> <!-- Added class -->
                 <source src="assets/textures/animations/reborn.mp4" type="video/mp4">
                 Your browser does not support the video tag.
             </video>
            <!-- End Intro Video -->
            <video id="endintro-video" class="video-player" width="640" height="480" muted playsinline preload="auto" loop style="display: none;">
                <source src="assets/textures/animations/endintro.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <!-- Canvas for Three.js -->
    <canvas id="webgl-canvas"></canvas>

    <!-- Audio Element for Chat Sound -->
    <audio id="chat-sound" src="assets/sounds/blip.wav" preload="auto"></audio>
    <!-- Audio Element for Footstep Sound -->
    <audio id="footstep-sound" src="assets/sounds/footstep.wav" preload="auto"></audio>
    <!-- Audio Element for Background Music -->
    <audio id="bg-music" src="assets/sounds/ambient_surround.wav" preload="auto" loop></audio>
    <!-- Audio Element for Second Music Track -->
    <audio id="ambient-music-track" src="assets/sounds/ambient_music.mp3" preload="metadata" loop></audio>
    <!-- Audio Element for Reveal Sound -->
    <audio id="reveal-sound" src="assets/sounds/reveal.wav" preload="auto"></audio>
    <!-- Audio Element for Selection Sound -->
    <audio id="select-sound" src="assets/sounds/select.wav" preload="auto"></audio>
    <!-- Audio Element for Start Button -->
    <audio id="start-button-sound" src="assets/sounds/button_start.wav" preload="auto"></audio>
    <!-- NEW Audio Element for Creepy Noise -->
    <audio id="creepy-noise-sound" src="assets/sounds/creepy_noise.wav" preload="auto"></audio>
    <!-- NEW Audio Element for Stuck In My Dreams -->
    <audio id="stuckinmydreams-sound" src="assets/sounds/stuckinmydreams.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Erasure Sound -->
    <audio id="erasure-sound" src="assets/sounds/erasure.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Erasure2 Sound -->
    <audio id="erasure2-sound" src="assets/sounds/erasure2.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Flicker Laugh -->
    <audio id="flicker_laugh_sound" src="assets/sounds/flicker_laugh.wav" preload="auto"></audio>
    <!-- NEW Audio Element for Flicker Laugh 2 -->
    <audio id="flicker_laugh2_sound" src="assets/sounds/flicker_laugh2.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Flicker Craft -->
    <audio id="flicker_craft_sound" src="assets/sounds/flicker_craft.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Drink -->
    <audio id="drink_sound" src="assets/sounds/drink.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Flicker Command -->
    <audio id="flicker_command_sound" src="assets/sounds/flicker_command.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Opening -->
    <audio id="opening_sound" src="assets/sounds/opening.mp3" preload="auto"></audio>
    <!-- NEW Audio Element for Main Theme -->
    <audio id="main-theme-sound" src="assets/music/main/maintheme.wav" preload="auto" loop autoplay volume="0.7"></audio>


    <!-- Joystick Zones -->
    <div id="joystick-left" class="joystick-zone"></div>
    <div id="joystick-right" class="joystick-zone"></div>

    <!-- Portrait Mode Touch Zones (Invisible Virtual Joysticks) -->
    <div id="portrait-touch-left" class="portrait-touch-zone"></div>
    <div id="portrait-touch-right" class="portrait-touch-zone"></div>

    <!-- Center Screen Touch Zone for Camera Toggle -->
    <div id="center-touch-zone"></div>

    <!-- Background Blur Overlay for Mobile Portrait -->
    <div id="background-blur-overlay"></div>

    <!-- Message for Mobile Portrait -->
    <div id="rotate-message">Please rotate your device to landscape mode to play.</div>

    <div id="loading-message">Preparing dungeon...</div>

    <script type="importmap">
        {
            "imports": {
                "three": "./libs/three/build/three.module.js",
                "three/addons/": "./libs/three/examples/jsm/"
            }
        }
    </script>
    <!-- Audio Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tone/14.8.49/Tone.js"></script>
    <!-- Using a different version of SoundTouch that doesn't use ES modules -->
    <script src="https://unpkg.com/soundtouchjs@0.1.25/dist/soundtouch.min.js"></script>
    
    <!-- ONNX.js for Local AI - CDN Version for Better Compatibility -->
    <script src="https://cdn.jsdelivr.net/npm/onnxruntime-web@1.16.3/dist/ort.min.js"></script>
    <script>
        // Initialize ONNX.js for local model loading
        console.log('[ONNX] 🚀 Loading ONNX.js library...');
        
        // Wait for library to load and configure environment
        async function initializeONNX() {
            try {
                if (typeof ort !== 'undefined') {
                    console.log('[ONNX] ✅ ONNX.js found, configuring environment...');
                    
                    // Configure ONNX.js environment for maximum compatibility
                    ort.env.wasm.numThreads = 1; // Single thread for stability
                    ort.env.wasm.simd = false; // Disable SIMD for compatibility
                    ort.env.logLevel = 'warning';
                    
                    // Set fallback to CPU only for maximum compatibility
                    ort.env.webgl.contextId = 'webgl2'; // Prefer WebGL2
                    ort.env.webgl.matmulMaxBatchSize = 128;
                    ort.env.webgl.textureCacheMode = 'initializerOnly';
                    
                    console.log('[ONNX] ✅ Environment configured for maximum compatibility');
                    
                    // Make ONNX runtime globally available
                    window.onnxRuntime = ort;
                    
                    // Trigger custom event to notify services
                    window.dispatchEvent(new CustomEvent('onnx-ready', { 
                        detail: { onnxRuntime: ort } 
                    }));
                    
                    // Also dispatch the old event name for backwards compatibility
                    window.dispatchEvent(new CustomEvent('transformers-ready', { 
                        detail: { onnxRuntime: ort } 
                    }));
                    
                    console.log('[ONNX] ✅ ONNX.js ready for local model loading');
                } else {
                    console.error('[ONNX] ❌ ONNX.js not loaded');
                }
            } catch (error) {
                console.error('[ONNX] ❌ ONNX.js initialization error:', error);
            }
        }
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeONNX);
        } else {
            initializeONNX();
        }
    </script>

    <script>
        // Mobile audio unlock system
        let audioContextInitialized = false;
        let htmlAudioUnlocked = false;

        function initAudioContext() {
            if (!audioContextInitialized && typeof Tone !== 'undefined') {
                console.log("Initializing Tone.js AudioContext from user interaction");
                Tone.start().then(() => {
                    console.log("Tone.js AudioContext started successfully");
                    audioContextInitialized = true;
                }).catch(error => {
                    console.error("Failed to start Tone.js AudioContext:", error);
                });
            }
        }

        function unlockHTMLAudio() {
            if (htmlAudioUnlocked) return;

            console.log("Unlocking HTML audio context for mobile...");

            // Create a silent audio buffer to unlock the audio context
            // This is more reliable than trying to play actual audio files
            try {
                // Create a minimal silent audio element
                const silentAudio = document.createElement('audio');
                silentAudio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                silentAudio.volume = 0;
                silentAudio.preload = 'auto';

                const playPromise = silentAudio.play();
                if (playPromise !== undefined) {
                    playPromise.then(() => {
                        silentAudio.pause();
                        silentAudio.remove();
                        console.log("[Mobile Audio] Audio context unlocked with silent buffer");
                    }).catch(error => {
                        console.warn("[Mobile Audio] Silent unlock failed:", error);
                        silentAudio.remove();
                    });
                }
            } catch (error) {
                console.warn("[Mobile Audio] Could not create silent audio:", error);
            }

            htmlAudioUnlocked = true;
            console.log("HTML audio unlock completed");
        }

        function initAllAudio() {
            initAudioContext();
            unlockHTMLAudio();
            setupMobileLargeAudioFiles();
        }

        function setupMobileLargeAudioFiles() {
            // Detect mobile
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           ('ontouchstart' in window) ||
                           (navigator.maxTouchPoints > 0);

            if (!isMobile) return;

            console.log("Setting up large audio files for mobile...");

            // Handle large music files differently on mobile
            const largeAudioFiles = ['main-theme-sound', 'ambient-music-track'];
            const mainThemeAudio = document.getElementById('main-theme-sound');

            // AUDIO STATE MONITORING: Check if audio is actually playing during logo sequence
            if (mainThemeAudio) {
                console.log('[Mobile] Setting up audio state monitoring');

                // Set up audio properties
                mainThemeAudio.volume = 0.7;
                mainThemeAudio.loop = true;

                // Comprehensive audio state monitoring
                const logAudioState = (context) => {
                    console.log(`[Mobile Audio State - ${context}]:`);
                    console.log(`  - Paused: ${mainThemeAudio.paused}`);
                    console.log(`  - Muted: ${mainThemeAudio.muted}`);
                    console.log(`  - Volume: ${mainThemeAudio.volume}`);
                    console.log(`  - Current Time: ${mainThemeAudio.currentTime}`);
                    console.log(`  - Duration: ${mainThemeAudio.duration}`);
                    console.log(`  - Ready State: ${mainThemeAudio.readyState}`);
                    console.log(`  - Network State: ${mainThemeAudio.networkState}`);
                    console.log(`  - Autoplay: ${mainThemeAudio.autoplay}`);
                    console.log(`  - Loop: ${mainThemeAudio.loop}`);
                };

                // Monitor audio events
                mainThemeAudio.addEventListener('loadstart', () => {
                    console.log('[Mobile] Audio loadstart event');
                    logAudioState('loadstart');
                });

                mainThemeAudio.addEventListener('canplay', () => {
                    console.log('[Mobile] Audio canplay event');
                    logAudioState('canplay');
                });

                mainThemeAudio.addEventListener('play', () => {
                    console.log('[Mobile] Audio PLAY event - audio is playing!');
                    logAudioState('play');
                });

                mainThemeAudio.addEventListener('pause', () => {
                    console.log('[Mobile] Audio PAUSE event - audio stopped');
                    logAudioState('pause');
                });

                mainThemeAudio.addEventListener('timeupdate', () => {
                    // Only log occasionally to avoid spam
                    if (Math.floor(mainThemeAudio.currentTime) % 5 === 0 && mainThemeAudio.currentTime > 0) {
                        console.log(`[Mobile] Audio playing - time: ${mainThemeAudio.currentTime.toFixed(1)}s`);
                    }
                });

                mainThemeAudio.addEventListener('volumechange', () => {
                    console.log(`[Mobile] Volume changed - Volume: ${mainThemeAudio.volume}, Muted: ${mainThemeAudio.muted}`);
                });

                // Try basic autoplay with detailed monitoring
                const tryBasicAutoplay = () => {
                    console.log('[Mobile] Attempting basic autoplay');
                    logAudioState('before play attempt');

                    mainThemeAudio.play().then(() => {
                        console.log('[Mobile] SUCCESS! play() promise resolved');
                        logAudioState('after successful play');
                        window.mobileAudioBridgeActive = true;

                        // Check if it's actually playing after a moment
                        setTimeout(() => {
                            logAudioState('1 second after play');
                            if (!mainThemeAudio.paused && mainThemeAudio.currentTime > 0) {
                                console.log('[Mobile] ✅ CONFIRMED: Audio is actually playing and progressing!');
                            } else if (!mainThemeAudio.paused && mainThemeAudio.currentTime === 0) {
                                console.log('[Mobile] ⚠️ Audio claims to be playing but time is not progressing');
                            } else {
                                console.log('[Mobile] ❌ Audio is not actually playing despite promise resolution');
                            }
                        }, 1000);

                    }).catch(e => {
                        console.log('[Mobile] play() promise rejected:', e.message);
                        logAudioState('after failed play');
                        console.log('[Mobile] Will wait for user interaction');

                        // Simple fallback: wait for any user interaction
                        const playOnTouch = () => {
                            console.log('[Mobile] User touched screen, playing audio');
                            logAudioState('before touch play');

                            mainThemeAudio.play().then(() => {
                                console.log('[Mobile] Audio started after user interaction');
                                logAudioState('after touch play');
                                window.mobileAudioBridgeActive = true;
                                document.removeEventListener('touchstart', playOnTouch);
                                document.removeEventListener('click', playOnTouch);
                            }).catch(e => {
                                console.log('[Mobile] Audio failed even after interaction:', e.message);
                                logAudioState('after failed touch play');
                            });
                        };

                        // Add simple interaction listeners
                        document.addEventListener('touchstart', playOnTouch, { once: true, passive: true });
                        document.addEventListener('click', playOnTouch, { once: true, passive: true });
                    });
                };

                // Log initial state
                logAudioState('initial');

                // Try immediately
                setTimeout(() => {
                    tryBasicAutoplay();
                }, 100);

                // Periodic state monitoring during logo sequence
                const stateMonitor = setInterval(() => {
                    if (window.mobileAudioBridgeActive) {
                        clearInterval(stateMonitor);
                        return;
                    }

                    console.log('[Mobile] Periodic audio state check:');
                    logAudioState('periodic check');

                    // Check if HTML autoplay started it
                    if (!mainThemeAudio.paused && !window.mobileAudioBridgeActive) {
                        console.log('[Mobile] 🎵 DETECTED: HTML autoplay is working! Audio is playing silently');
                        window.mobileAudioBridgeActive = true;
                        clearInterval(stateMonitor);
                    }
                }, 2000);

                // Stop monitoring after 30 seconds
                setTimeout(() => {
                    clearInterval(stateMonitor);
                    console.log('[Mobile] Stopped audio state monitoring');
                }, 30000);

                // Also try when page becomes visible (in case loaded in background)
                document.addEventListener('visibilitychange', () => {
                    if (!document.hidden && mainThemeAudio.paused && !window.mobileAudioBridgeActive) {
                        console.log('[Mobile] Page visible, trying autoplay again');
                        logAudioState('page visible');
                        tryBasicAutoplay();
                    }
                });
            }

            largeAudioFiles.forEach(id => {
                const audio = document.getElementById(id);
                if (audio && id !== 'main-theme-sound') {
                    // Remove preload for other large files to prevent mobile loading issues
                    audio.preload = 'none';

                    // Add basic loading handlers
                    audio.addEventListener('loadstart', () => {
                        console.log(`[Mobile] ${id} loading started`);
                    });

                    audio.addEventListener('canplay', () => {
                        console.log(`[Mobile] ${id} can play`);
                    });

                    audio.addEventListener('error', (e) => {
                        console.error(`[Mobile] ${id} error:`, e);
                        // Retry loading after a delay
                        setTimeout(() => {
                            console.log(`[Mobile] Retrying ${id} load`);
                            audio.load();
                        }, 1000);
                    });
                }
            });
        }

        // Initialize mobile audio bridge status flag
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);

        if (isMobile) {
            window.mobileAudioBridgeActive = false;
            console.log('[Mobile] Mobile audio bridge status initialized');
        }

        // MOBILE FIX: Global touch event prevention
        document.addEventListener('DOMContentLoaded', function() {
            // SAFARI FIX: Prevent edge swipe gestures
            document.addEventListener('touchstart', function(event) {
                // Prevent Safari edge swipes near screen edges
                const touch = event.touches[0];
                const screenWidth = window.innerWidth;
                const edgeThreshold = 50; // pixels from edge

                if (touch.clientX < edgeThreshold || touch.clientX > screenWidth - edgeThreshold) {
                    // Check if this is a joystick area
                    const target = event.target;
                    const isJoystick = target.closest('.joystick-zone') ||
                                     target.closest('.nipple') ||
                                     target.classList.contains('nipple') ||
                                     target.classList.contains('front') ||
                                     target.classList.contains('back');

                    if (isJoystick) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                }
            }, { passive: false });

            // Prevent double-tap zoom globally
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    // Check if this is a game control element
                    const target = event.target;
                    const isGameControl = target.closest('.joystick-zone') ||
                                        target.closest('.portrait-touch-zone') ||
                                        target.closest('#dialogue-container') ||
                                        target.closest('#video-overlay') ||
                                        target.closest('.nipple') ||
                                        target.classList.contains('nipple') ||
                                        target.classList.contains('front') ||
                                        target.classList.contains('back');

                    // MOBILE FIX: Allow double-tap on center touch zone for camera toggle
                    const isCenterTouchZone = target.id === 'center-touch-zone' || target.closest('#center-touch-zone');

                    // MOBILE FIX: Prevent zoom on game controls, but allow center touch zone double-tap
                    if (isGameControl && !isCenterTouchZone) {
                        event.preventDefault();
                        event.stopPropagation();
                        event.stopImmediatePropagation();
                    } else if (!isCenterTouchZone) {
                        // Also prevent zoom on non-game areas (but not center touch zone)
                        event.preventDefault();
                    }
                    // Center touch zone double-taps are allowed to pass through for camera toggle
                }
                lastTouchEnd = now;
            }, { passive: false });

            // Prevent context menu on long press
            document.addEventListener('contextmenu', function(event) {
                event.preventDefault();
            }, false);

            // Prevent text selection on touch
            document.addEventListener('selectstart', function(event) {
                event.preventDefault();
            }, false);
        });
    </script>

    <script type="module" src="./main.js"></script>
    <!-- NippleJS Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/nipplejs/0.10.1/nipplejs.min.js"></script>
    <!-- Boss Debug Overlay -->
    <script src="./ultra-debug.js"></script>
    <!-- Teleport Debug Commands -->
    <script type="module" src="./src/debug/teleport-commands.js"></script>
    <!-- Event Room Debug Commands -->
    <script src="./src/debug/event-room-debug.js"></script>
    <!-- Spawn Debug Commands -->
    <script type="module" src="./src/debug/spawn-commands.js"></script>
    <!-- Event Room Door Testing -->
    <script type="module" src="./test-event-room-doors.js"></script>
    <!-- Event Room Door Debug -->
    <script type="module" src="./debug-event-room-doors.js"></script>
    <!-- Force Event Room -->
    <script type="module" src="./force-event-room.js"></script>
</body>
</html>
