// Test script for event room door configurations
// This script verifies that event rooms are properly selected based on door requirements
// and that only the defined doors are created in the room

window.testEventRoomDoors = function() {
    console.log('🚪 TESTING EVENT ROOM DOOR SYSTEM 🚪');
    console.log('=====================================');
    
    // Get all available event rooms
    const eventRooms = window.getAvailableEventRooms();
    console.log(`Found ${eventRooms.length} event rooms available`);
    
    // Test each entrance direction
    const directions = ['north', 'south', 'east', 'west'];
    
    directions.forEach(dir => {
        console.log(`\n🧭 Testing entrance from ${dir.toUpperCase()}:`);
        
        // Get event rooms that support this entrance
        const compatibleRooms = eventRooms.filter(roomKey => {
            if (roomKey === 'handler') return false;
            const roomData = window.getEventRoom(roomKey);
            return roomData && roomData.availableConnections && roomData.availableConnections[dir];
        });
        
        console.log(`  ✅ ${compatibleRooms.length} rooms support entrance from ${dir}:`, compatibleRooms);
        
        // Show door configurations for each compatible room
        compatibleRooms.forEach(roomKey => {
            const roomData = window.getEventRoom(roomKey);
            console.log(`    - ${roomKey}:`, roomData.availableConnections);
        });
    });
    
    // Test specific scenarios
    console.log('\n🔬 TESTING SPECIFIC SCENARIOS:');
    console.log('==============================');
    
    // Scenario 1: Room with only north entrance
    const northOnlyRooms = eventRooms.filter(roomKey => {
        if (roomKey === 'handler') return false;
        const roomData = window.getEventRoom(roomKey);
        if (!roomData || !roomData.availableConnections) return false;
        
        const connections = roomData.availableConnections;
        return connections.north && !connections.south && !connections.east && !connections.west;
    });
    
    console.log(`\n1️⃣ Rooms with ONLY north entrance: ${northOnlyRooms.length}`);
    northOnlyRooms.forEach(room => console.log(`   - ${room}`));
    
    // Scenario 2: Rooms with all entrances
    const allEntranceRooms = eventRooms.filter(roomKey => {
        if (roomKey === 'handler') return false;
        const roomData = window.getEventRoom(roomKey);
        if (!roomData || !roomData.availableConnections) return false;
        
        const connections = roomData.availableConnections;
        return connections.north && connections.south && connections.east && connections.west;
    });
    
    console.log(`\n2️⃣ Rooms with ALL entrances: ${allEntranceRooms.length}`);
    allEntranceRooms.forEach(room => console.log(`   - ${room}`));
    
    // Test the new selectEventRoomForFloor with entrance direction
    console.log('\n🎲 TESTING EVENT ROOM SELECTION WITH ENTRANCE DIRECTION:');
    console.log('========================================================');
    
    if (window.dungeonHandler && window.dungeonHandler.eventRoomManager) {
        const eventRoomManager = window.dungeonHandler.eventRoomManager;
        
        // Save current state
        const savedUsedRooms = new Set(eventRoomManager.usedEventRooms);
        
        // Test selection for each direction
        directions.forEach(dir => {
            // Clear used rooms for testing
            eventRoomManager.usedEventRooms.clear();
            
            console.log(`\n🎯 Selecting room for entrance from ${dir.toUpperCase()}:`);
            const selectedRoom = eventRoomManager.selectEventRoomForFloor(99, dir);
            
            if (selectedRoom) {
                const roomData = window.getEventRoom(selectedRoom);
                console.log(`  ✅ Selected: ${selectedRoom}`);
                console.log(`  📍 Door config:`, roomData.availableConnections);
                console.log(`  🚪 Supports ${dir} entrance:`, roomData.availableConnections[dir]);
            } else {
                console.log(`  ❌ No room available for entrance from ${dir}`);
            }
        });
        
        // Restore state
        eventRoomManager.usedEventRooms = savedUsedRooms;
    } else {
        console.log('❌ Event room manager not available for testing');
    }
    
    console.log('\n✅ EVENT ROOM DOOR SYSTEM TEST COMPLETE');
};

// Add command to help
window.testEventRoomDoors.description = "Test event room door configurations and selection";

console.log('🚪 Event room door test loaded. Run testEventRoomDoors() to test.');