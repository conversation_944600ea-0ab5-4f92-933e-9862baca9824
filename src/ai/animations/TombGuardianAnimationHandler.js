/**
 * Tomb Guardian Animation Handler
 * Handles animations for tomb guardian enemies - stone statues with sword and shield
 */
import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class TombGuardianAnimationHandler {
    /**
     * Create a tomb guardian animation handler
     * @param {THREE.Group} tombGuardianModel - The tomb guardian model to animate
     */
    constructor(tombGuardianModel) {
        this.tombGuardianModel = tombGuardianModel;

        // Get body parts - search directly in the model
        this.bodyGroup = tombGuardianModel.getObjectByName('body') || tombGuardianModel.getObjectByName('core');
        this.headGroup = tombGuardianModel.getObjectByName('head');
        this.leftArmGroup = tombGuardianModel.getObjectByName('leftArm');
        this.rightArmGroup = tombGuardianModel.getObjectByName('rightArm');
        this.leftLegGroup = tombGuardianModel.getObjectByName('leftLeg');
        this.rightLegGroup = tombGuardianModel.getObjectByName('rightLeg');

        // Debug output to check if all parts are found
        console.log('TombGuardianAnimationHandler initialized with parts:', {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup
        });

        // Get animation data from model or use defaults
        this.animationData = tombGuardianModel.userData.animationData || {
            walkSpeed: 0.3,              // Very slow walk speed for stone statue
            walkAmplitude: Math.PI / 12, // Small amplitude for heavy movement
            armSwingAmplitude: Math.PI / 16, // Minimal arm swing
            bodySwayAmplitude: 0.02,     // Minimal body sway
            bodySwaySpeed: 0.5,          // Slow sway
            attackAnimationDuration: 1.2, // Slower attack animation
            shieldBashDuration: 0.8,     // Shield bash animation duration
            crumbleDuration: 1.5         // Death crumble animation duration
        };

        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackAnimationProgress = 0;
        this.shieldBashProgress = 0;
        this.crumbleProgress = 0;
        this.isAttacking = false;
        this.isShieldBashing = false;
        this.isCrumbling = false;
        this.lastHitTime = 0;

        // Store original positions and rotations
        this.originalPositions = {};
        this.originalRotations = {};

        this.storeOriginalTransforms();

        // Add transition blending
        this.previousState = null;
        this.transitionProgress = 0;
        this.transitionDuration = 0.2; // 200ms transition
        this.previousAnimationPose = null;
        this.currentAnimationPose = null;
    }

    /**
     * Store original positions and rotations of all body parts
     */
    storeOriginalTransforms() {
        const groups = [
            'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                this.originalPositions[groupName] = group.position.clone();
                this.originalRotations[groupName] = group.rotation.clone();
            }
        }
    }

    /**
     * Reset all body parts to original positions
     */
    resetPositions() {
        const groups = [
            'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                if (this.originalPositions[groupName]) {
                    group.position.copy(this.originalPositions[groupName]);
                }
                if (this.originalRotations[groupName]) {
                    group.rotation.copy(this.originalRotations[groupName]);
                }
            }
        }
    }

    /**
     * Reset all transforms to prepare for a new animation
     */
    resetTransforms() {
        this.resetPositions();
    }

    /**
     * Trigger shield bash animation
     */
    triggerShieldBash() {
        this.isShieldBashing = true;
        this.shieldBashProgress = 0;
    }

    /**
     * Trigger death crumble animation
     */
    triggerCrumble() {
        this.isCrumbling = true;
        this.crumbleProgress = 0;
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.tombGuardianModel) return;

        // Handle special animations first
        if (this.isCrumbling) {
            this.crumbleProgress += deltaTime / this.animationData.crumbleDuration;
            if (this.crumbleProgress >= 1.0) {
                this.crumbleProgress = 1.0;
                // Animation complete - enemy should be destroyed
            }
            this._applyCrumbleAnimation(this.crumbleProgress);
            return;
        }

        if (this.isShieldBashing) {
            this.shieldBashProgress += deltaTime / this.animationData.shieldBashDuration;
            if (this.shieldBashProgress >= 1.0) {
                this.shieldBashProgress = 0;
                this.isShieldBashing = false;
            }
            this._applyShieldBashAnimation(this.shieldBashProgress);
            return;
        }

        // Handle state transition
        if (this.currentState !== state) {
            this.previousState = this.currentState;
            this.currentState = state;
            this.transitionProgress = 0;

            // Reset attack progress when entering attack state
            if (state === AIStates.ATTACKING) {
                this.isAttacking = true;
                this.attackAnimationProgress = 0;
            }
            // Reset attack state when leaving attack state
            else if (this.previousState === AIStates.ATTACKING) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            }
            // Set lastHitTime when entering hit reaction state
            else if (state === AIStates.HIT_REACTING) {
                this.lastHitTime = globalTime;
            }
        }

        // Update transition progress
        if (this.transitionProgress < this.transitionDuration) {
            this.transitionProgress += deltaTime;
        }

        // Handle attack animation progress
        if (state === AIStates.ATTACKING) {
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;
            if (this.attackAnimationProgress >= 1.0) {
                this.attackAnimationProgress = 0;
            }
        }

        // Store base Y position if not already set
        if (typeof this.tombGuardianModel.userData.basePositionY === 'undefined') {
            this.tombGuardianModel.userData.basePositionY = this.tombGuardianModel.position.y;
        }

        // Reset ONLY rotation and scale - DO NOT reset position
        this.tombGuardianModel.rotation.set(0, 0, 0);
        this.tombGuardianModel.scale.set(1, 1, 1);

        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                this._applyIdleAnimation(globalTime);
                break;

            case AIStates.MOVING:
            case AIStates.STRAFING:
                this._applyWalkingAnimation(globalTime);
                break;

            case AIStates.ATTACKING:
                this._applyAttackingAnimation(globalTime);
                break;

            case AIStates.HIT_REACTING:
                this._applyHitReactionAnimation(globalTime);
                break;

            case AIStates.BLOCKING:
                this._applyBlockingAnimation(globalTime);
                break;

            default:
                this._applyIdleAnimation(globalTime);
                break;
        }
    }

    /**
     * Apply idle animation - minimal movement for stone statue
     * @param {number} time - Global time
     * @private
     */
    _applyIdleAnimation(time) {
        this.resetTransforms();

        // Very subtle stone statue movement
        const swaySpeed = this.animationData.bodySwaySpeed;
        const swayAmplitude = this.animationData.bodySwayAmplitude;

        // Body barely sways - like settling stone
        if (this.bodyGroup) {
            // Minimal side-to-side sway
            this.bodyGroup.rotation.z = Math.sin(time * swaySpeed) * swayAmplitude * 0.5;
            
            // Very slight forward/back motion
            this.bodyGroup.rotation.x = Math.sin(time * swaySpeed * 0.7) * swayAmplitude * 0.3;
        }

        // Head movement - almost imperceptible
        if (this.headGroup) {
            // Glowing eyes pulse effect could be added here
            this.headGroup.rotation.y = Math.sin(time * swaySpeed * 0.5) * swayAmplitude * 0.2;
        }

        // Shield arm slightly shifts weight
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.z = Math.sin(time * swaySpeed * 0.8) * swayAmplitude;
        }

        // Sword arm remains mostly static
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * swaySpeed * 0.6) * swayAmplitude * 0.5;
        }
    }

    /**
     * Apply walking animation - heavy, ponderous steps
     * @param {number} time - Global time
     * @private
     */
    _applyWalkingAnimation(time) {
        const walkSpeed = this.animationData.walkSpeed;
        const walkAmplitude = this.animationData.walkAmplitude;

        // Heavy, stomping leg movement
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
        }

        // Minimal arm swing - statue is stiff
        if (this.leftArmGroup) {
            // Shield arm barely moves
            this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.2);
        }
        if (this.rightArmGroup) {
            // Sword arm has slightly more movement
            this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.3);
        }

        // Body rocks slightly with heavy steps
        if (this.bodyGroup) {
            this.bodyGroup.rotation.z = Math.sin(time * walkSpeed * 2) * 0.02;
            this.bodyGroup.rotation.x = Math.abs(Math.sin(time * walkSpeed)) * 0.05;
        }

        // Ground shake effect - heavy footsteps
        if (this.tombGuardianModel) {
            if (this.tombGuardianModel.userData.basePositionY === undefined) {
                this.tombGuardianModel.userData.basePositionY = this.tombGuardianModel.position.y;
            }
            // Step impact creates small bounce
            const stepImpact = Math.abs(Math.sin(time * walkSpeed)) < 0.1 ? 0.02 : 0;
            this.tombGuardianModel.position.y = this.tombGuardianModel.userData.basePositionY + stepImpact;
        }
    }

    /**
     * Apply attacking animation - powerful sword swing
     * @param {number} time - Global time
     * @private
     */
    _applyAttackingAnimation(time) {
        this.resetTransforms();

        const attackProgress = this.attackAnimationProgress;

        // Three phases: wind up (0-0.4), strike (0.4-0.7), recover (0.7-1.0)
        const windUpPhase = attackProgress < 0.4;
        const strikePhase = attackProgress >= 0.4 && attackProgress < 0.7;
        const recoverPhase = attackProgress >= 0.7;

        // Calculate phase-specific progress
        const windUpProgress = windUpPhase ? (attackProgress / 0.4) : 1.0;
        const strikeProgress = strikePhase ? ((attackProgress - 0.4) / 0.3) : (recoverPhase ? 1.0 : 0.0);
        const recoverProgress = recoverPhase ? ((attackProgress - 0.7) / 0.3) : 0.0;

        // Body movement - lean into attack
        if (this.bodyGroup) {
            let bodyLean = 0;
            let bodySway = 0;

            if (windUpPhase) {
                // Lean back and rotate for wind up
                bodyLean = -0.2 * windUpProgress;
                bodySway = -0.3 * windUpProgress;
            } else if (strikePhase) {
                // Powerful forward lean
                bodyLean = -0.2 + (0.5 * strikeProgress);
                bodySway = -0.3 + (0.6 * strikeProgress);
            } else if (recoverPhase) {
                // Return to neutral
                bodyLean = 0.3 - (0.3 * recoverProgress);
                bodySway = 0.3 - (0.3 * recoverProgress);
            }

            this.bodyGroup.rotation.x = bodyLean;
            this.bodyGroup.rotation.y = bodySway;
        }

        // Sword arm animation
        if (this.rightArmGroup) {
            if (windUpPhase) {
                // Raise sword high
                this.rightArmGroup.rotation.x = -Math.PI/2 * windUpProgress;
                this.rightArmGroup.rotation.y = -0.5 * windUpProgress;
                this.rightArmGroup.rotation.z = -0.8 * windUpProgress;
            } else if (strikePhase) {
                // Powerful downward swing
                const strikeX = -Math.PI/2 + (Math.PI * 1.5 * strikeProgress);
                this.rightArmGroup.rotation.x = strikeX;
                this.rightArmGroup.rotation.y = -0.5 + (0.5 * strikeProgress);
                this.rightArmGroup.rotation.z = -0.8 + (0.8 * strikeProgress);
            } else if (recoverPhase) {
                // Return to ready position
                const recoveryX = Math.PI - (Math.PI * recoverProgress);
                this.rightArmGroup.rotation.x = recoveryX;
                this.rightArmGroup.rotation.y = 0;
                this.rightArmGroup.rotation.z = 0;
            }
        }

        // Shield arm provides balance
        if (this.leftArmGroup) {
            if (windUpPhase) {
                // Pull shield back
                this.leftArmGroup.rotation.x = 0.3 * windUpProgress;
                this.leftArmGroup.rotation.z = 0.5 * windUpProgress;
            } else if (strikePhase) {
                // Shield forward for balance
                this.leftArmGroup.rotation.x = 0.3 - (0.5 * strikeProgress);
                this.leftArmGroup.rotation.z = 0.5 - (0.3 * strikeProgress);
            } else if (recoverPhase) {
                // Return to guard position
                this.leftArmGroup.rotation.x = -0.2 + (0.2 * recoverProgress);
                this.leftArmGroup.rotation.z = 0.2 - (0.2 * recoverProgress);
            }
        }

        // Legs provide stable base
        if (this.leftLegGroup && this.rightLegGroup) {
            if (windUpPhase) {
                // Widen stance
                const stanceWidth = 0.2 * windUpProgress;
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;
                
                // Bend knees slightly
                this.leftLegGroup.rotation.x = 0.1 * windUpProgress;
                this.rightLegGroup.rotation.x = 0.1 * windUpProgress;
            } else if (strikePhase) {
                // Step forward with strike
                const stanceWidth = 0.2;
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;
                
                // Right leg forward
                this.rightLegGroup.rotation.x = 0.1 + (0.3 * strikeProgress);
                this.leftLegGroup.rotation.x = 0.1 - (0.1 * strikeProgress);
            } else if (recoverPhase) {
                // Return to neutral stance
                const stanceWidth = 0.2 * (1 - recoverProgress);
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;
                
                this.rightLegGroup.rotation.x = 0.4 - (0.4 * recoverProgress);
                this.leftLegGroup.rotation.x = 0;
            }
        }
    }

    /**
     * Apply shield bash animation
     * @param {number} progress - Animation progress (0-1)
     * @private
     */
    _applyShieldBashAnimation(progress) {
        this.resetTransforms();

        // Two phases: bash (0-0.6), recover (0.6-1.0)
        const bashPhase = progress < 0.6;
        const recoverPhase = progress >= 0.6;

        const bashProgress = bashPhase ? (progress / 0.6) : 1.0;
        const recoverProgress = recoverPhase ? ((progress - 0.6) / 0.4) : 0.0;

        // Body movement - thrust forward with shield
        if (this.bodyGroup) {
            if (bashPhase) {
                // Thrust forward
                this.bodyGroup.rotation.x = 0.3 * bashProgress;
                this.bodyGroup.rotation.y = 0.4 * bashProgress;
                this.bodyGroup.position.z = -0.5 * bashProgress;
            } else if (recoverPhase) {
                // Pull back
                this.bodyGroup.rotation.x = 0.3 - (0.3 * recoverProgress);
                this.bodyGroup.rotation.y = 0.4 - (0.4 * recoverProgress);
                this.bodyGroup.position.z = -0.5 + (0.5 * recoverProgress);
            }
        }

        // Shield arm - powerful forward thrust
        if (this.leftArmGroup) {
            if (bashPhase) {
                // Thrust shield forward
                this.leftArmGroup.rotation.x = -0.8 * bashProgress;
                this.leftArmGroup.rotation.y = 0.6 * bashProgress;
                this.leftArmGroup.rotation.z = 0.3 * bashProgress;
            } else if (recoverPhase) {
                // Pull back to guard
                this.leftArmGroup.rotation.x = -0.8 + (0.8 * recoverProgress);
                this.leftArmGroup.rotation.y = 0.6 - (0.6 * recoverProgress);
                this.leftArmGroup.rotation.z = 0.3 - (0.3 * recoverProgress);
            }
        }

        // Sword arm pulls back for balance
        if (this.rightArmGroup) {
            if (bashPhase) {
                this.rightArmGroup.rotation.x = 0.4 * bashProgress;
                this.rightArmGroup.rotation.z = -0.5 * bashProgress;
            } else if (recoverPhase) {
                this.rightArmGroup.rotation.x = 0.4 - (0.4 * recoverProgress);
                this.rightArmGroup.rotation.z = -0.5 + (0.5 * recoverProgress);
            }
        }

        // Legs provide powerful stance
        if (this.leftLegGroup && this.rightLegGroup) {
            if (bashPhase) {
                // Left leg forward for shield bash
                this.leftLegGroup.rotation.x = 0.4 * bashProgress;
                this.rightLegGroup.rotation.x = -0.2 * bashProgress;
                
                // Wide stance
                this.leftLegGroup.rotation.z = -0.3 * bashProgress;
                this.rightLegGroup.rotation.z = 0.3 * bashProgress;
            } else if (recoverPhase) {
                // Return to neutral
                this.leftLegGroup.rotation.x = 0.4 - (0.4 * recoverProgress);
                this.rightLegGroup.rotation.x = -0.2 + (0.2 * recoverProgress);
                
                this.leftLegGroup.rotation.z = -0.3 + (0.3 * recoverProgress);
                this.rightLegGroup.rotation.z = 0.3 - (0.3 * recoverProgress);
            }
        }
    }

    /**
     * Apply hit reaction animation - stone statue barely flinches
     * @param {number} time - Global time
     * @private
     */
    _applyHitReactionAnimation(time) {
        this.resetTransforms();

        // Calculate hit reaction progress
        const hitProgress = Math.min(1.0, (time - this.lastHitTime) * 3.0);
        const initialImpact = Math.max(0, 1.0 - hitProgress * 4.0);

        // Stone statues barely react to hits
        const reactionScale = 0.3; // Much less reaction than organic enemies

        // Slight backward lean
        if (this.bodyGroup) {
            const backwardLean = -0.1 * initialImpact * reactionScale;
            this.bodyGroup.rotation.x = backwardLean;
            
            // Very slight wobble
            const wobble = Math.sin(time * 20) * 0.02 * (1.0 - hitProgress);
            this.bodyGroup.rotation.z = wobble;
        }

        // Shield arm tightens slightly
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.z = 0.2 * initialImpact * reactionScale;
        }

        // Head barely moves
        if (this.headGroup) {
            this.headGroup.rotation.x = -0.05 * initialImpact * reactionScale;
        }
    }

    /**
     * Apply blocking animation - shield raised
     * @param {number} time - Global time
     * @private
     */
    _applyBlockingAnimation(time) {
        this.resetTransforms();

        // Defensive stance
        if (this.bodyGroup) {
            // Slight crouch
            this.bodyGroup.rotation.x = 0.1;
            // Turn body to present shield
            this.bodyGroup.rotation.y = 0.2;
        }

        // Shield arm raised and forward
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = -0.6;
            this.leftArmGroup.rotation.y = 0.4;
            this.leftArmGroup.rotation.z = 0.3;
        }

        // Sword arm ready but pulled back
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = 0.2;
            this.rightArmGroup.rotation.z = -0.3;
        }

        // Stable defensive stance
        if (this.leftLegGroup && this.rightLegGroup) {
            // Wide stance
            this.leftLegGroup.rotation.z = -0.2;
            this.rightLegGroup.rotation.z = 0.2;
            
            // Knees slightly bent
            this.leftLegGroup.rotation.x = 0.1;
            this.rightLegGroup.rotation.x = 0.1;
        }
    }

    /**
     * Apply crumble animation - death effect
     * @param {number} progress - Animation progress (0-1)
     * @private
     */
    _applyCrumbleAnimation(progress) {
        // All parts fall apart and shake
        const shakeAmount = 0.3 * (1 - progress);
        const fallProgress = progress * progress; // Accelerating fall

        // Random shake for each part
        const groups = [this.bodyGroup, this.headGroup, this.leftArmGroup, 
                       this.rightArmGroup, this.leftLegGroup, this.rightLegGroup];

        groups.forEach((group, index) => {
            if (group) {
                // Random shake that decreases over time
                group.rotation.x = (Math.random() - 0.5) * shakeAmount;
                group.rotation.y = (Math.random() - 0.5) * shakeAmount;
                group.rotation.z = (Math.random() - 0.5) * shakeAmount;
                
                // Parts fall and separate
                group.position.y -= fallProgress * 0.5;
                
                // Parts drift apart slightly
                const driftX = (Math.random() - 0.5) * fallProgress * 0.2;
                const driftZ = (Math.random() - 0.5) * fallProgress * 0.2;
                group.position.x += driftX;
                group.position.z += driftZ;
            }
        });

        // Main body falls faster
        if (this.tombGuardianModel) {
            const baseY = this.tombGuardianModel.userData.basePositionY || 0;
            this.tombGuardianModel.position.y = baseY - (fallProgress * 1.0);
            
            // Fade out effect could be added here by adjusting material opacity
        }
    }
}