import * as THREE from 'three';

/**
 * VoidLordAnimationHandler
 * Handles animations for the Void Lord boss including phase-based animations,
 * energy effects, and black hole distortions
 */
export class VoidLordAnimationHandler {
    constructor(enemy) {
        this.enemy = enemy;
        this.enemyMesh = enemy;
        
        // Get body parts
        this.body = this.findBodyPart('body');
        this.head = this.findBodyPart('head');
        this.leftArm = this.findBodyPart('leftArm');
        this.rightArm = this.findBodyPart('rightArm');
        this.leftLeg = this.findBodyPart('leftLeg');
        this.rightLeg = this.findBodyPart('rightLeg');
        
        // Animation states
        this.currentState = 'IDLE';
        this.previousState = null;
        this.stateStartTime = Date.now();
        this.animationSpeed = 1.0;
        
        // Phase-specific properties
        this.currentPhase = 1;
        this.phaseTransitioning = false;
        this.phaseTransitionStart = 0;
        
        // Energy effects
        this.energyStrains = [];
        this.energyOrbs = [];
        this.blackHoleDistortion = 0;
        
        // Animation parameters
        this.floatOffset = 0;
        this.energyPulse = 0;
        this.attackWindup = 0;
        this.limbSwayPhase = 0;
        
        // Store original positions
        this.originalPositions = {
            body: this.body ? this.body.position.clone() : new THREE.Vector3(),
            head: this.head ? this.head.position.clone() : new THREE.Vector3(),
            leftArm: this.leftArm ? this.leftArm.position.clone() : new THREE.Vector3(),
            rightArm: this.rightArm ? this.rightArm.position.clone() : new THREE.Vector3(),
            leftLeg: this.leftLeg ? this.leftLeg.position.clone() : new THREE.Vector3(),
            rightLeg: this.rightLeg ? this.rightLeg.position.clone() : new THREE.Vector3()
        };
        
        // Store original rotations
        this.originalRotations = {
            body: this.body ? this.body.rotation.clone() : new THREE.Euler(),
            head: this.head ? this.head.rotation.clone() : new THREE.Euler(),
            leftArm: this.leftArm ? this.leftArm.rotation.clone() : new THREE.Euler(),
            rightArm: this.rightArm ? this.rightArm.rotation.clone() : new THREE.Euler(),
            leftLeg: this.leftLeg ? this.leftLeg.rotation.clone() : new THREE.Euler(),
            rightLeg: this.rightLeg ? this.rightLeg.rotation.clone() : new THREE.Euler()
        };
        
        console.log('[VoidLordAnimationHandler] Initialized with body parts:', {
            body: !!this.body,
            head: !!this.head,
            leftArm: !!this.leftArm,
            rightArm: !!this.rightArm,
            leftLeg: !!this.leftLeg,
            rightLeg: !!this.rightLeg
        });
    }
    
    /**
     * Find body part in the enemy model hierarchy
     */
    findBodyPart(name) {
        // First check direct children
        let part = this.enemyMesh.getObjectByName(name);
        
        // If not found, check in visualModel group
        if (!part && this.enemyMesh.getObjectByName('visualModel')) {
            const visualModel = this.enemyMesh.getObjectByName('visualModel');
            part = visualModel.getObjectByName(name);
        }
        
        // If still not found, traverse entire hierarchy
        if (!part) {
            this.enemyMesh.traverse(child => {
                if (child.name === name) {
                    part = child;
                }
            });
        }
        
        return part;
    }
    
    /**
     * Set animation state
     */
    setState(newState) {
        if (this.currentState !== newState) {
            this.previousState = this.currentState;
            this.currentState = newState;
            this.stateStartTime = Date.now();
            console.log(`[VoidLordAnimationHandler] State changed: ${this.previousState} -> ${this.currentState}`);
        }
    }
    
    /**
     * Set current phase
     */
    setPhase(phase) {
        if (this.currentPhase !== phase) {
            console.log(`[VoidLordAnimationHandler] Phase transition: ${this.currentPhase} -> ${phase}`);
            this.currentPhase = phase;
            this.phaseTransitioning = true;
            this.phaseTransitionStart = Date.now();
            
            // Trigger phase-specific effects
            this.triggerPhaseEffects(phase);
        }
    }
    
    /**
     * Trigger phase-specific visual effects
     */
    triggerPhaseEffects(phase) {
        switch (phase) {
            case 2:
                // Phase 2: Increase energy intensity
                this.animationSpeed = 1.5;
                console.log('[VoidLordAnimationHandler] Phase 2: Energy intensified');
                break;
                
            case 3:
                // Phase 3: Maximum chaos
                this.animationSpeed = 2.0;
                console.log('[VoidLordAnimationHandler] Phase 3: MAXIMUM VOID POWER!');
                break;
        }
    }
    
    /**
     * Main update function
     */
    update(deltaTime, state = null) {
        if (state) {
            this.setState(state);
        }
        
        const time = Date.now() * 0.001;
        const stateTime = (Date.now() - this.stateStartTime) * 0.001;
        
        // Update base animations
        this.updateFloatingAnimation(time);
        this.updateEnergyPulse(time);
        this.updateBlackHoleDistortion(time);
        
        // Update state-specific animations
        switch (this.currentState) {
            case 'IDLE':
                this.updateIdleAnimation(time, stateTime);
                break;
                
            case 'WALKING':
            case 'CHASING':
                this.updateMovementAnimation(time, stateTime);
                break;
                
            case 'ATTACKING':
                this.updateAttackAnimation(time, stateTime);
                break;
                
            case 'SPECIAL_ATTACK':
                this.updateSpecialAttackAnimation(time, stateTime);
                break;
                
            case 'PHASE_TRANSITION':
                this.updatePhaseTransitionAnimation(time, stateTime);
                break;
                
            case 'DYING':
                this.updateDeathAnimation(time, stateTime);
                break;
        }
        
        // Update limb sway
        this.updateLimbSway(time);
        
        // Update phase transition if active
        if (this.phaseTransitioning) {
            this.updatePhaseTransition(time);
        }
    }
    
    /**
     * Update floating animation
     */
    updateFloatingAnimation(time) {
        this.floatOffset = Math.sin(time * 1.5 * this.animationSpeed) * 0.3;
        
        if (this.body) {
            this.body.position.y = this.originalPositions.body.y + this.floatOffset;
        }
    }
    
    /**
     * Update energy pulse effect
     */
    updateEnergyPulse(time) {
        this.energyPulse = (Math.sin(time * 3.0 * this.animationSpeed) + 1) * 0.5;
        
        // Apply pulsing to energy materials
        if (this.enemyMesh) {
            this.enemyMesh.traverse(child => {
                if (child.isMesh && child.material) {
                    if (child.material.emissive && child.material.emissiveIntensity !== undefined) {
                        // Pulse energy materials
                        const baseIntensity = 0.5;
                        child.material.emissiveIntensity = baseIntensity + this.energyPulse * 0.5;
                    }
                }
            });
        }
    }
    
    /**
     * Update black hole distortion effect
     */
    updateBlackHoleDistortion(time) {
        if (!this.head) return;
        
        // Rotate the black hole head
        this.head.rotation.y = time * 0.5 * this.animationSpeed;
        
        // Add subtle warping effect
        const warpScale = 1.0 + Math.sin(time * 2.0) * 0.05;
        this.head.scale.setScalar(warpScale);
        
        // Calculate distortion intensity based on phase
        this.blackHoleDistortion = 0.1 * this.currentPhase;
    }
    
    /**
     * Update idle animation
     */
    updateIdleAnimation(time, stateTime) {
        // Gentle swaying
        if (this.body) {
            this.body.rotation.z = Math.sin(time * 0.8) * 0.02;
        }
        
        // Arms float independently
        if (this.leftArm) {
            this.leftArm.rotation.z = Math.sin(time * 1.2 + 1) * 0.05;
        }
        if (this.rightArm) {
            this.rightArm.rotation.z = Math.sin(time * 1.2 - 1) * -0.05;
        }
    }
    
    /**
     * Update movement animation
     */
    updateMovementAnimation(time, stateTime) {
        const moveSpeed = this.currentState === 'CHASING' ? 2.0 : 1.0;
        
        // Body lean forward
        if (this.body) {
            this.body.rotation.x = 0.1 * moveSpeed;
            this.body.rotation.z = Math.sin(time * 4 * moveSpeed) * 0.05;
        }
        
        // Leg movement
        const legSwing = Math.sin(time * 6 * moveSpeed) * 0.3;
        if (this.leftLeg) {
            this.leftLeg.rotation.x = legSwing;
        }
        if (this.rightLeg) {
            this.rightLeg.rotation.x = -legSwing;
        }
        
        // Arm swing
        const armSwing = Math.sin(time * 6 * moveSpeed) * 0.2;
        if (this.leftArm) {
            this.leftArm.rotation.x = -armSwing * 0.5;
        }
        if (this.rightArm) {
            this.rightArm.rotation.x = armSwing * 0.5;
        }
    }
    
    /**
     * Update attack animation
     */
    updateAttackAnimation(time, stateTime) {
        const attackProgress = Math.min(stateTime / 0.8, 1.0);
        
        // Wind up and strike
        if (attackProgress < 0.3) {
            // Wind up
            this.attackWindup = attackProgress / 0.3;
            if (this.rightArm) {
                this.rightArm.rotation.x = -Math.PI * 0.6 * this.attackWindup;
                this.rightArm.rotation.z = -Math.PI * 0.3 * this.attackWindup;
            }
        } else if (attackProgress < 0.6) {
            // Strike
            const strikeProgress = (attackProgress - 0.3) / 0.3;
            if (this.rightArm) {
                this.rightArm.rotation.x = -Math.PI * 0.6 + Math.PI * 0.8 * strikeProgress;
                this.rightArm.rotation.z = -Math.PI * 0.3 + Math.PI * 0.4 * strikeProgress;
            }
            if (this.body) {
                this.body.rotation.x = 0.2 * strikeProgress;
            }
        } else {
            // Recovery
            const recoveryProgress = (attackProgress - 0.6) / 0.4;
            if (this.rightArm) {
                this.rightArm.rotation.x = Math.PI * 0.2 * (1 - recoveryProgress);
                this.rightArm.rotation.z = Math.PI * 0.1 * (1 - recoveryProgress);
            }
            if (this.body) {
                this.body.rotation.x = 0.2 * (1 - recoveryProgress);
            }
        }
    }
    
    /**
     * Update special attack animation (energy strain attacks)
     */
    updateSpecialAttackAnimation(time, stateTime) {
        // Both arms raised channeling energy
        const chargeProgress = Math.min(stateTime / 1.0, 1.0);
        
        if (this.leftArm) {
            this.leftArm.rotation.x = -Math.PI * 0.7 * chargeProgress;
            this.leftArm.rotation.z = Math.PI * 0.2 * chargeProgress;
        }
        if (this.rightArm) {
            this.rightArm.rotation.x = -Math.PI * 0.7 * chargeProgress;
            this.rightArm.rotation.z = -Math.PI * 0.2 * chargeProgress;
        }
        
        // Energy gathering effect
        if (this.body) {
            this.body.scale.setScalar(1.0 + Math.sin(time * 10) * 0.05 * chargeProgress);
        }
    }
    
    /**
     * Update phase transition animation
     */
    updatePhaseTransitionAnimation(time, stateTime) {
        // Dramatic pose during transition
        const transitionProgress = Math.min(stateTime / 2.0, 1.0);
        
        // Arms spread wide
        if (this.leftArm) {
            this.leftArm.rotation.z = Math.PI * 0.5 * transitionProgress;
        }
        if (this.rightArm) {
            this.rightArm.rotation.z = -Math.PI * 0.5 * transitionProgress;
        }
        
        // Head thrown back
        if (this.head) {
            this.head.rotation.x = -Math.PI * 0.3 * transitionProgress;
        }
        
        // Body pulses with energy
        if (this.body) {
            const pulseMagnitude = 0.1 + 0.1 * this.currentPhase;
            this.body.scale.setScalar(1.0 + Math.sin(time * 20) * pulseMagnitude * transitionProgress);
        }
    }
    
    /**
     * Update death animation
     */
    updateDeathAnimation(time, stateTime) {
        // Collapse animation
        const deathProgress = Math.min(stateTime / 3.0, 1.0);
        
        // Black hole destabilizes
        if (this.head) {
            this.head.rotation.x = Math.random() * 0.2 - 0.1;
            this.head.rotation.y = time * 10;
            this.head.rotation.z = Math.random() * 0.2 - 0.1;
            this.head.scale.setScalar(1.0 - deathProgress * 0.5);
        }
        
        // Body collapses
        if (this.body) {
            this.body.position.y = this.originalPositions.body.y * (1 - deathProgress);
            this.body.rotation.x = Math.PI * 0.5 * deathProgress;
        }
        
        // Limbs go limp
        const limpness = deathProgress;
        if (this.leftArm) {
            this.leftArm.rotation.z = Math.PI * 0.4 * limpness;
            this.leftArm.rotation.x = Math.PI * 0.3 * limpness;
        }
        if (this.rightArm) {
            this.rightArm.rotation.z = -Math.PI * 0.4 * limpness;
            this.rightArm.rotation.x = Math.PI * 0.3 * limpness;
        }
    }
    
    /**
     * Update limb sway for organic movement
     */
    updateLimbSway(time) {
        this.limbSwayPhase += 0.016 * this.animationSpeed; // ~60fps
        
        // Don't sway during certain animations
        if (this.currentState === 'ATTACKING' || 
            this.currentState === 'SPECIAL_ATTACK' ||
            this.currentState === 'DYING') {
            return;
        }
        
        // Subtle arm sway
        const armSway = Math.sin(this.limbSwayPhase * 1.5) * 0.02;
        if (this.leftArm && this.currentState !== 'PHASE_TRANSITION') {
            this.leftArm.rotation.z += armSway;
        }
        if (this.rightArm && this.currentState !== 'PHASE_TRANSITION') {
            this.rightArm.rotation.z -= armSway;
        }
        
        // Subtle leg adjustment
        const legAdjust = Math.sin(this.limbSwayPhase * 0.8) * 0.01;
        if (this.leftLeg) {
            this.leftLeg.rotation.z = legAdjust;
        }
        if (this.rightLeg) {
            this.rightLeg.rotation.z = -legAdjust;
        }
    }
    
    /**
     * Update phase transition
     */
    updatePhaseTransition(time) {
        const transitionDuration = 2.0;
        const elapsed = (Date.now() - this.phaseTransitionStart) * 0.001;
        
        if (elapsed >= transitionDuration) {
            this.phaseTransitioning = false;
            console.log('[VoidLordAnimationHandler] Phase transition complete');
        }
    }
    
    /**
     * Trigger attack animation
     */
    triggerAttack(attackType = 'melee') {
        console.log(`[VoidLordAnimationHandler] Triggering ${attackType} attack`);
        
        if (attackType === 'energy_strain' || attackType === 'special') {
            this.setState('SPECIAL_ATTACK');
        } else {
            this.setState('ATTACKING');
        }
    }
    
    /**
     * Reset to idle state
     */
    resetToIdle() {
        this.setState('IDLE');
        this.attackWindup = 0;
        
        // Reset rotations smoothly would be done over time
        // For now, just mark as idle
    }
    
    /**
     * Clean up resources
     */
    cleanup() {
        // Clean up any additional resources if needed
        this.energyStrains = [];
        this.energyOrbs = [];
    }
}