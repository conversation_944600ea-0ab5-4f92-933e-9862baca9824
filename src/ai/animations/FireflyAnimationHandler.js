import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

/**
 * Animation handler for firefly enemies
 * Handles wing flapping and body movement for small flying fireflies
 */
export class FireflyAnimationHandler {
    /**
     * Create a firefly animation handler
     * @param {THREE.Group} fireflyModel - The firefly model to animate
     */
    constructor(fireflyModel) {
        this.fireflyModel = fireflyModel;
        this.bodyGroup = fireflyModel.getObjectByName('body');
        this.leftWingGroup = fireflyModel.getObjectByName('leftWing');
        this.rightWingGroup = fireflyModel.getObjectByName('rightWing');

        // Get animation data from model
        this.animationData = fireflyModel.userData.animationData || {
            wingFlapSpeed: 8.0,         // Very fast wing flapping for firefly
            wingFlapAmplitude: Math.PI / 4, // Moderate wing flap amplitude
            bodyBobAmplitude: 0.08,     // Small body bobbing
            bodyBobSpeed: 3.0,          // Fast bobbing for nervous movement
            attackAnimationDuration: 0.3 // Quick attack animation
        };

        // Store original positions
        this.originalBodyPosition = this.bodyGroup ? this.bodyGroup.position.clone() : new THREE.Vector3();
        this.originalLeftWingRotation = this.leftWingGroup ? this.leftWingGroup.rotation.clone() : new THREE.Euler();
        this.originalRightWingRotation = this.rightWingGroup ? this.rightWingGroup.rotation.clone() : new THREE.Euler();

        // Animation state
        this.currentState = AIStates.IDLE;
        this.isAttacking = false;
        this.attackAnimationProgress = 0;

        console.log('FireflyAnimationHandler initialized');
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.fireflyModel || !this.bodyGroup) return;

        // Update current state
        this.currentState = state;

        // Handle attack animation progress
        if (this.isAttacking) {
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;
            if (this.attackAnimationProgress >= 1.0) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            }
        }

        // Apply animations based on state
        switch (state) {
            case AIStates.HOVERING:
            case AIStates.IDLE:
            case AIStates.MOVING:
            case AIStates.FLEEING: // Fireflies flee, so animate during fleeing
                this._applyFlyingAnimation(globalTime, state === AIStates.FLEEING ? 1.5 : 1.0);
                break;

            case AIStates.ATTACKING:
                if (!this.isAttacking) {
                    this.isAttacking = true;
                    this.attackAnimationProgress = 0;
                }
                this._applyAttackingAnimation(globalTime);
                break;

            case AIStates.STUNNED:
                this._applyStunnedAnimation(globalTime);
                break;

            default:
                this._applyFlyingAnimation(globalTime, 1.0);
                break;
        }
    }

    /**
     * Apply flying animation (wing flapping and body bobbing)
     * @param {number} time - Global time
     * @param {number} intensity - Animation intensity multiplier
     * @private
     */
    _applyFlyingAnimation(time, intensity = 1.0) {
        // Wing flapping with very fast speed for firefly
        if (this.leftWingGroup && this.rightWingGroup) {
            const wingFlapSpeed = this.animationData.wingFlapSpeed * intensity;
            const wingFlapAmplitude = this.animationData.wingFlapAmplitude;

            // Calculate wing flap angle
            const flapAngle = Math.sin(time * wingFlapSpeed) * wingFlapAmplitude;

            // Apply wing rotations (opposite directions for realistic flapping)
            this.leftWingGroup.rotation.z = this.originalLeftWingRotation.z + flapAngle;
            this.rightWingGroup.rotation.z = this.originalRightWingRotation.z - flapAngle;

            // Add slight forward/backward wing motion for more realism
            const wingForwardMotion = Math.cos(time * wingFlapSpeed * 0.5) * 0.1;
            this.leftWingGroup.rotation.x = this.originalLeftWingRotation.x + wingForwardMotion;
            this.rightWingGroup.rotation.x = this.originalRightWingRotation.x + wingForwardMotion;
        }

        // Body bobbing with nervous firefly movement
        if (this.bodyGroup) {
            const bodyBobSpeed = this.animationData.bodyBobSpeed * intensity;
            const bodyBobAmplitude = this.animationData.bodyBobAmplitude;

            // Sinusoidal body bobbing
            const bodyOffset = Math.sin(time * bodyBobSpeed) * bodyBobAmplitude;

            // Add secondary motion for more natural movement
            const secondaryBodyMotion = Math.sin(time * bodyBobSpeed * 1.7) * (bodyBobAmplitude * 0.4);

            // Apply combined motion
            this.bodyGroup.position.y = this.originalBodyPosition.y + bodyOffset + secondaryBodyMotion;

            // Add slight rotation to body for more natural movement
            this.bodyGroup.rotation.x = Math.sin(time * bodyBobSpeed * 0.8) * 0.05;
            this.bodyGroup.rotation.z = Math.cos(time * bodyBobSpeed * 0.6) * 0.03;
        }

        // Animate the cyan light with firefly-like pulsing
        const fireflyLight = this.fireflyModel.userData.light;
        if (fireflyLight) {
            // Create realistic firefly light pulsing pattern
            const pulseSpeed = 4.0 * intensity; // Faster pulsing when fleeing
            const basePulse = Math.sin(time * pulseSpeed) * 0.3 + 0.7; // 0.4 to 1.0 range

            // Add secondary flicker for more realism
            const flicker = Math.sin(time * pulseSpeed * 3.7) * 0.1; // High frequency flicker

            // Combine pulses for natural firefly effect
            const finalIntensity = Math.max(0.2, basePulse + flicker) * intensity;

            fireflyLight.intensity = finalIntensity;

            // Slightly vary the light distance for more organic feel
            const baseDistance = 15; // 3x normal radius
            const distanceVariation = Math.sin(time * pulseSpeed * 0.5) * 2; // ±2 units
            fireflyLight.distance = baseDistance + distanceVariation;
        }
    }

    /**
     * Apply attacking animation
     * @param {number} time - Global time
     * @private
     */
    _applyAttackingAnimation(time) {
        // Quick darting motion during attack
        const attackIntensity = Math.sin(this.attackAnimationProgress * Math.PI);
        
        // Faster wing flapping during attack
        this._applyFlyingAnimation(time, 2.0);

        // Quick forward lunge
        if (this.bodyGroup) {
            const lungeDistance = attackIntensity * 0.3;
            this.bodyGroup.position.z = this.originalBodyPosition.z + lungeDistance;
        }
    }

    /**
     * Apply stunned animation (erratic movement)
     * @param {number} time - Global time
     * @private
     */
    _applyStunnedAnimation(time) {
        // Erratic wing flapping
        if (this.leftWingGroup && this.rightWingGroup) {
            const erraticFlap1 = Math.sin(time * 12.0) * Math.PI / 6;
            const erraticFlap2 = Math.cos(time * 15.0) * Math.PI / 8;

            this.leftWingGroup.rotation.z = this.originalLeftWingRotation.z + erraticFlap1;
            this.rightWingGroup.rotation.z = this.originalRightWingRotation.z + erraticFlap2;
        }

        // Erratic body movement
        if (this.bodyGroup) {
            const erraticX = Math.sin(time * 8.0) * 0.1;
            const erraticY = Math.cos(time * 10.0) * 0.1;

            this.bodyGroup.position.x = this.originalBodyPosition.x + erraticX;
            this.bodyGroup.position.y = this.originalBodyPosition.y + erraticY;
        }
    }

    /**
     * Reset animation to default state
     */
    reset() {
        if (this.bodyGroup) {
            this.bodyGroup.position.copy(this.originalBodyPosition);
            this.bodyGroup.rotation.set(0, 0, 0);
        }

        if (this.leftWingGroup) {
            this.leftWingGroup.rotation.copy(this.originalLeftWingRotation);
        }

        if (this.rightWingGroup) {
            this.rightWingGroup.rotation.copy(this.originalRightWingRotation);
        }

        this.isAttacking = false;
        this.attackAnimationProgress = 0;
    }

    /**
     * Cleanup resources
     */
    dispose() {
        // No specific cleanup needed for this handler
        console.log('FireflyAnimationHandler disposed');
    }
}
