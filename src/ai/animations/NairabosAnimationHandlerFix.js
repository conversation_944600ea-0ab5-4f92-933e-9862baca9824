import { NairabosAnimationHandler } from './NairabosAnimationHandler.js';

/**
 * Fix <PERSON>os animation handler issues
 * @param {Object} enemy - The Nairabos enemy object
 * @returns {Boolean} - True if fixed, false otherwise
 */
export function fixNairabosAnimationHandler(enemy) {
    // Check if this is a Nairabos enemy
    const isNairabos = enemy.name.includes('nairabos') ||
                      enemy.name.includes('Nairabos') ||
                      (enemy.userData && enemy.userData.type === 'nairabos');

    if (!isNairabos) {
        console.log(`[NairabosAnimationHandlerFix] Not a Nairabos enemy: ${enemy.name}`);
        return false;
    }

    // Check if animation handler already exists
    const hasAnimHandler = enemy.userData && enemy.userData.animationHandler;

    if (hasAnimHandler) {
        console.log(`[NairabosAnimationHandlerFix] Animation handler already exists for ${enemy.name}`);
        return false;
    }

    // Verify the enemy has the required body parts for animation
    const bodyParts = {
        body: enemy.getObjectByName('body'),
        head: enemy.getObjectByName('head'),
        leftArm: enemy.getObjectByName('leftArm'),
        rightArm: enemy.getObjectByName('rightArm'),
        leftLeg: enemy.getObjectByName('leftLeg'),
        rightLeg: enemy.getObjectByName('rightLeg')
    };

    console.log(`[NairabosAnimationHandlerFix] Body parts found for ${enemy.name}:`, {
        body: !!bodyParts.body,
        head: !!bodyParts.head,
        leftArm: !!bodyParts.leftArm,
        rightArm: !!bodyParts.rightArm,
        leftLeg: !!bodyParts.leftLeg,
        rightLeg: !!bodyParts.rightLeg
    });

    // Ensure userData exists
    if (!enemy.userData) {
        enemy.userData = {};
    }

    // Create and attach the animation handler
    try {
        // Create the Nairabos animation handler
        console.log(`[NairabosAnimationHandlerFix] Creating handler for enemy:`, enemy.name);
        console.log(`[NairabosAnimationHandlerFix] Enemy object type:`, enemy.constructor.name);
        console.log(`[NairabosAnimationHandlerFix] Enemy has getObjectByName:`, typeof enemy.getObjectByName);

        enemy.userData.animationHandler = new NairabosAnimationHandler(enemy);
        console.log(`[NairabosAnimationHandlerFix] ✅ Successfully created NairabosAnimationHandler for ${enemy.name}`);
        return true;
    } catch (error) {
        console.error(`[NairabosAnimationHandlerFix] ❌ Error creating animation handler:`, error);
        console.error(`[NairabosAnimationHandlerFix] Enemy object:`, enemy);
        console.error(`[NairabosAnimationHandlerFix] Stack trace:`, error.stack);
        return false;
    }
}

/**
 * Apply Nairabos animation handler fix to all Nairabos enemies in the scene
 * @param {THREE.Scene} scene - The scene to search for Nairabos enemies
 * @returns {Number} - Number of enemies fixed
 */
export function applyNairabosAnimationHandlerFix(scene) {
    let fixedCount = 0;
    
    if (!scene) {
        console.warn('[NairabosAnimationHandlerFix] No scene provided');
        return fixedCount;
    }

    // Search for Nairabos enemies in the scene
    scene.traverse((object) => {
        // Check if this object is a Nairabos enemy
        const isNairabos = object.name && (
            object.name.includes('nairabos') ||
            object.name.includes('Nairabos') ||
            (object.userData && object.userData.type === 'nairabos')
        );

        if (isNairabos) {
            console.log(`[NairabosAnimationHandlerFix] Found Nairabos enemy: ${object.name}`);
            
            if (fixNairabosAnimationHandler(object)) {
                fixedCount++;
            }
        }
    });

    if (fixedCount > 0) {
        console.log(`[NairabosAnimationHandlerFix] ✅ Fixed ${fixedCount} Nairabos enemies`);
    } else {
        console.log(`[NairabosAnimationHandlerFix] No Nairabos enemies found or all already have animation handlers`);
    }

    return fixedCount;
}
