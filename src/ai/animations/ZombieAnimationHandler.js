/**
 * Zombie Animation Handler
 * Handles animations for zombie enemies
 */
import * as THREE from 'three';
import { AIStates } from '../AIStates.js';

export class ZombieAnimationHandler {
    /**
     * Create a zombie animation handler
     * @param {THREE.Group} zombieModel - The zombie model to animate
     */
    constructor(zombieModel) {
        this.zombieModel = zombieModel;

        // Get body parts - search directly in the model
        this.bodyGroup = zombieModel.getObjectByName('body') || zombieModel.getObjectByName('core');
        this.headGroup = zombieModel.getObjectByName('head');
        this.leftArmGroup = zombieModel.getObjectByName('leftArm');
        this.rightArmGroup = zombieModel.getObjectByName('rightArm');
        this.leftLegGroup = zombieModel.getObjectByName('leftLeg');
        this.rightLegGroup = zombieModel.getObjectByName('rightLeg');

        // Debug output to check if all parts are found
        console.log('ZombieAnimationHandler initialized with parts:', {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup
        });

        // Get animation data from model or use defaults
        this.animationData = zombieModel.userData.animationData || {
            walkSpeed: 1.5,              // Slower walk speed for shambling effect
            walkAmplitude: Math.PI / 6,  // Larger amplitude for exaggerated movement
            armSwingAmplitude: Math.PI / 4, // Larger arm swing
            bodySwayAmplitude: 0.1,      // Body sway amount
            bodySwaySpeed: 1.2,          // Body sway speed
            attackAnimationDuration: 0.6, // REDUCED: Much faster attack animation (was 2.4s)
            headBobAmplitude: 0.05,      // Head bobbing amount
            headBobSpeed: 1.8            // Head bobbing speed
        };

        // Animation state
        this.currentState = AIStates.IDLE;
        this.attackAnimationProgress = 0;
        this.isAttacking = false;
        this.lastHitTime = 0; // Track when the zombie was last hit

        // Store original positions and rotations
        this.originalPositions = {};
        this.originalRotations = {};

        this.storeOriginalTransforms();

        // Add transition blending
        this.previousState = null;
        this.transitionProgress = 0;
        this.transitionDuration = 0.15; // 150ms transition
        this.previousAnimationPose = null;
        this.currentAnimationPose = null;
    }

    /**
     * Store original positions and rotations of all body parts
     */
    storeOriginalTransforms() {
        const groups = [
            'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                this.originalPositions[groupName] = group.position.clone();
                this.originalRotations[groupName] = group.rotation.clone();
            }
        }
    }

    /**
     * Reset all body parts to original positions
     */
    resetPositions() {
        const groups = [
            'bodyGroup', 'headGroup',
            'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                if (this.originalPositions[groupName]) {
                    group.position.copy(this.originalPositions[groupName]);
                }
                if (this.originalRotations[groupName]) {
                    group.rotation.copy(this.originalRotations[groupName]);
                }
            }
        }
    }

    /**
     * Reset all transforms to prepare for a new animation
     * This ensures animations don't build on top of each other
     */
    resetTransforms() {
        // Reset positions first
        this.resetPositions();

        // Additional reset for any dynamic properties
        const groups = [
            'bodyGroup', 'headGroup', 'leftArmGroup', 'rightArmGroup', 'leftLegGroup', 'rightLegGroup'
        ];

        for (const groupName of groups) {
            const group = this[groupName];
            if (group) {
                // Reset any additional properties that might have been set
                if (group.userData.resetProperties) {
                    for (const prop of group.userData.resetProperties) {
                        if (group[prop] !== undefined) {
                            group[prop] = 0;
                        }
                    }
                }
            }
        }
    }

    /**
     * Store the current pose for blending
     */
    _storePose() {
        if (!this.previousAnimationPose) {
            this.previousAnimationPose = {
                body: this.bodyGroup ? {
                    rotation: this.bodyGroup.rotation.clone(),
                    position: this.bodyGroup.position.clone()
                } : null,
                head: this.headGroup ? {
                    rotation: this.headGroup.rotation.clone(),
                    position: this.headGroup.position.clone()
                } : null,
                leftArm: this.leftArmGroup ? {
                    rotation: this.leftArmGroup.rotation.clone(),
                    position: this.leftArmGroup.position.clone()
                } : null,
                rightArm: this.rightArmGroup ? {
                    rotation: this.rightArmGroup.rotation.clone(),
                    position: this.rightArmGroup.position.clone()
                } : null,
                leftLeg: this.leftLegGroup ? {
                    rotation: this.leftLegGroup.rotation.clone(),
                    position: this.leftLegGroup.position.clone()
                } : null,
                rightLeg: this.rightLegGroup ? {
                    rotation: this.rightLegGroup.rotation.clone(),
                    position: this.rightLegGroup.position.clone()
                } : null
            };
        }
    }

    /**
     * Blend between previous and current pose
     * @param {number} blendFactor - 0 to 1 blend factor
     */
    _blendPoses(blendFactor) {
        if (!this.previousAnimationPose || !this.currentAnimationPose) return;

        const blend = (current, previous, factor) => {
            if (!current || !previous) return;
            current.rotation.lerp(previous.rotation, 1 - factor);
            current.position.lerp(previous.position, 1 - factor);
        };

        // Blend each body part
        if (this.bodyGroup) blend(this.bodyGroup, this.previousAnimationPose.body, blendFactor);
        if (this.headGroup) blend(this.headGroup, this.previousAnimationPose.head, blendFactor);
        if (this.leftArmGroup) blend(this.leftArmGroup, this.previousAnimationPose.leftArm, blendFactor);
        if (this.rightArmGroup) blend(this.rightArmGroup, this.previousAnimationPose.rightArm, blendFactor);
        if (this.leftLegGroup) blend(this.leftLegGroup, this.previousAnimationPose.leftLeg, blendFactor);
        if (this.rightLegGroup) blend(this.rightLegGroup, this.previousAnimationPose.rightLeg, blendFactor);
    }

    /**
     * Update animations based on state and time
     * @param {string} state - Current AI state
     * @param {number} deltaTime - Time since last update
     * @param {number} globalTime - Global time for continuous animations
     */
    update(state, deltaTime, globalTime) {
        if (!this.zombieModel) return;

        // Handle state transition
        if (this.currentState !== state) {
            this._storePose();
            this.previousState = this.currentState;
            this.currentState = state;
            this.transitionProgress = 0;

            // Reset attack progress when entering attack state
            if (state === AIStates.ATTACKING) {
                this.isAttacking = true;
                this.attackAnimationProgress = 0;
            }
            // Reset attack state when leaving attack state
            else if (this.previousState === AIStates.ATTACKING) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            }
            // Set lastHitTime when entering hit reaction state
            else if (state === AIStates.HIT_REACTING) {
                this.lastHitTime = globalTime;
                console.log(`[ZombieAnimationHandler] Hit reaction at time ${globalTime}`);
            }
        }

        // Update transition progress
        if (this.transitionProgress < this.transitionDuration) {
            this.transitionProgress += deltaTime;
        }

        // Handle attack animation progress
        if (state === AIStates.ATTACKING) {
            // Always increment progress while in attack state
            this.attackAnimationProgress += deltaTime / this.animationData.attackAnimationDuration;

            // Loop the animation
            if (this.attackAnimationProgress >= 1.0) {
                this.attackAnimationProgress = 0;
            }
        }

        // Store base Y position if not already set
        if (typeof this.zombieModel.userData.basePositionY === 'undefined') {
            this.zombieModel.userData.basePositionY = this.zombieModel.position.y;
        }

        // Reset ONLY rotation and scale - DO NOT reset position
        this.zombieModel.rotation.set(0, 0, 0);
        this.zombieModel.scale.set(1, 1, 1);

        // Body bob - only apply Y offset RELATIVE to base position
        const baseY = this.zombieModel.userData.basePositionY;
        const bob = Math.sin(this.animationTime * this.walkSpeed) * this.walkAmplitude;
        this.zombieModel.position.y = baseY + bob;  // ONLY modify Y

        // Apply animations based on state
        switch (state) {
            case AIStates.IDLE:
                this._applyIdleAnimation(globalTime);
                break;

            case AIStates.MOVING:
            case AIStates.STRAFING:
            case AIStates.FLEEING:
                this._applyWalkingAnimation(globalTime);
                break;

            case AIStates.ATTACKING:
                this._applyAttackingAnimation(globalTime);
                break;

            case AIStates.HIT_REACTING:
                this._applyHitReactionAnimation(globalTime);
                break;

            default:
                this._applyIdleAnimation(globalTime);
                break;
        }

        // Store current pose for next frame's blending
        this.currentAnimationPose = {
            body: this.bodyGroup ? {
                rotation: this.bodyGroup.rotation.clone(),
                position: this.bodyGroup.position.clone()
            } : null,
            head: this.headGroup ? {
                rotation: this.headGroup.rotation.clone(),
                position: this.headGroup.position.clone()
            } : null,
            leftArm: this.leftArmGroup ? {
                rotation: this.leftArmGroup.rotation.clone(),
                position: this.leftArmGroup.position.clone()
            } : null,
            rightArm: this.rightArmGroup ? {
                rotation: this.rightArmGroup.rotation.clone(),
                position: this.rightArmGroup.position.clone()
            } : null,
            leftLeg: this.leftLegGroup ? {
                rotation: this.leftLegGroup.rotation.clone(),
                position: this.leftLegGroup.position.clone()
            } : null,
            rightLeg: this.rightLegGroup ? {
                rotation: this.rightLegGroup.rotation.clone(),
                position: this.rightLegGroup.position.clone()
            } : null
        };

        // Apply transition blending if in transition
        if (this.transitionProgress < this.transitionDuration) {
            const blendFactor = Math.min(this.transitionProgress / this.transitionDuration, 1);
            this._blendPoses(blendFactor);
        }
    }

    /**
     * Apply idle animation
     * @param {number} time - Global time
     * @private
     */
    _applyIdleAnimation(time) {
        // Reset transforms first to ensure consistent starting pose
        this.resetTransforms();

        // Subtle swaying motion with zombie-like characteristics
        const swaySpeed = this.animationData.bodySwaySpeed * 0.7; // Slower for idle
        const swayAmplitude = this.animationData.bodySwayAmplitude * 0.8; // Slightly increased for more visible movement

        // Body sway - more pronounced and asymmetric
        if (this.bodyGroup) {
            // Combine multiple sine waves for more organic movement
            const primarySway = Math.sin(time * swaySpeed) * swayAmplitude;
            const secondarySway = Math.sin(time * swaySpeed * 1.3) * (swayAmplitude * 0.4);
            const bodySway = primarySway + secondarySway;

            // Apply side-to-side sway with slight asymmetry
            this.bodyGroup.rotation.z = bodySway * 1.2;

            // Forward lean with subtle variation
            const baseLean = 0.15; // Constant forward lean
            const forwardSway = Math.sin(time * swaySpeed * 0.8) * (swayAmplitude * 0.6);
            this.bodyGroup.rotation.x = baseLean + forwardSway;

            // Subtle twist
            this.bodyGroup.rotation.y = Math.sin(time * swaySpeed * 0.5) * (swayAmplitude * 0.2);
        }

        // Head movement - more zombie-like with irregular twitches
        if (this.headGroup) {
            // Base head position - slightly tilted and looking down
            const baseHeadTilt = 0.1;
            const baseHeadNod = 0.2; // Looking slightly down

            // Regular slow movement
            const headBobSpeed = this.animationData.headBobSpeed * 0.8;
            const headBobAmplitude = this.animationData.headBobAmplitude * 0.9;

            // Add occasional twitches
            const twitchFactor = Math.sin(time * 7.3) * Math.sin(time * 3.7) * 0.15;
            const randomTwitch = (Math.sin(time * 13.5) > 0.8) ? twitchFactor : 0;

            // Combine movements
            this.headGroup.rotation.z = baseHeadTilt + Math.sin(time * headBobSpeed) * headBobAmplitude * 0.7 + randomTwitch; // Head tilt
            this.headGroup.rotation.x = baseHeadNod + Math.sin(time * headBobSpeed * 1.2) * headBobAmplitude * 0.5 + randomTwitch * 0.5; // Head nod
            this.headGroup.rotation.y = Math.sin(time * headBobSpeed * 0.7) * headBobAmplitude * 0.3 + randomTwitch * 0.3; // Head turn
        }

        // Arms hanging with zombie-like positioning
        if (this.leftArmGroup) {
            // Base arm position - arms slightly forward and out
            const baseArmForward = 0.3; // Arms slightly forward
            const baseArmOut = 0.4; // Left arm out from body

            // Slow swaying motion
            const armSway = Math.sin(time * swaySpeed * 1.1) * (swayAmplitude * 1.0);
            const armSwayVertical = Math.sin(time * swaySpeed * 0.9) * (swayAmplitude * 0.7);

            // Apply combined movement
            this.leftArmGroup.rotation.x = baseArmForward + armSwayVertical;
            this.leftArmGroup.rotation.z = baseArmOut + armSway * 0.5;
            this.leftArmGroup.rotation.y = Math.sin(time * swaySpeed * 0.7) * (swayAmplitude * 0.3);
        }

        if (this.rightArmGroup) {
            // Base arm position - asymmetric from left arm
            const baseArmForward = 0.25; // Slightly different from left arm
            const baseArmOut = -0.45; // Right arm out from body

            // Slow swaying motion (out of phase with left arm)
            const armSway = Math.sin(time * swaySpeed * 1.1 + Math.PI) * (swayAmplitude * 1.0);
            const armSwayVertical = Math.sin(time * swaySpeed * 0.9 + Math.PI) * (swayAmplitude * 0.7);

            // Apply combined movement
            this.rightArmGroup.rotation.x = baseArmForward + armSwayVertical;
            this.rightArmGroup.rotation.z = baseArmOut + armSway * 0.5;
            this.rightArmGroup.rotation.y = Math.sin(time * swaySpeed * 0.7 + Math.PI) * (swayAmplitude * 0.3);
        }

        // Legs with subtle weight shifting and bent knees
        if (this.leftLegGroup && this.rightLegGroup) {
            // Base leg position - slightly bent knees and feet apart
            const baseKneeBend = 0.2; // Bent knees
            const baseFeetApart = 0.15; // Feet slightly apart

            // Weight shifting
            const weightShift = Math.sin(time * swaySpeed * 0.8);
            const leftLegWeight = Math.max(0, weightShift) * (swayAmplitude * 0.5);
            const rightLegWeight = Math.max(0, -weightShift) * (swayAmplitude * 0.5);

            // Apply to legs
            this.leftLegGroup.rotation.x = baseKneeBend + leftLegWeight;
            this.rightLegGroup.rotation.x = baseKneeBend + rightLegWeight;

            this.leftLegGroup.rotation.z = -baseFeetApart - (weightShift * 0.1);
            this.rightLegGroup.rotation.z = baseFeetApart + (weightShift * 0.1);

            // Subtle foot rotation
            this.leftLegGroup.rotation.y = Math.sin(time * swaySpeed * 0.6) * 0.05;
            this.rightLegGroup.rotation.y = Math.sin(time * swaySpeed * 0.6 + Math.PI) * 0.05;
        }

        // Overall body vertical movement
        if (this.zombieModel) {
            // Store base Y position if not already set
            if (this.zombieModel.userData.basePositionY === undefined) {
                this.zombieModel.userData.basePositionY = this.zombieModel.position.y;
            }

            // Subtle up/down movement as zombie shifts weight
            const bodyBob = Math.sin(time * swaySpeed * 0.8) * 0.05;
            this.zombieModel.position.y = this.zombieModel.userData.basePositionY + bodyBob;
        }
    }

    /**
     * Apply walking animation
     * @param {number} time - Global time
     * @private
     */
    _applyWalkingAnimation(time) {
        // Walking animation
        const walkSpeed = this.animationData.walkSpeed;
        const walkAmplitude = this.animationData.walkAmplitude;

        // Leg movement
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
        }

        // Arm swing
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.5);
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed) * (walkAmplitude * 0.5);
        }

        // Body bob - only apply Y offset RELATIVE to base position
        if (this.zombieModel) {
            if (this.zombieModel.userData.basePositionY === undefined) {
                this.zombieModel.userData.basePositionY = this.zombieModel.position.y;
            }
            const walkBob = Math.abs(Math.sin(time * walkSpeed)) * 0.05;
            this.zombieModel.position.y = this.zombieModel.userData.basePositionY + walkBob;
        }
    }

    /**
     * Apply attacking animation
     * @param {number} time - Global time
     * @private
     */
    _applyAttackingAnimation(time) {
        // Reset transforms first to prevent pose sticking
        this.resetTransforms();

        const attackProgress = this.attackAnimationProgress;

        // Three-phase attack animation (wind up, strike, and recover)
        const windUpPhase = attackProgress < 0.3;
        const strikePhase = attackProgress >= 0.3 && attackProgress < 0.6;
        const recoverPhase = attackProgress >= 0.6;

        // Calculate phase-specific progress
        const windUpProgress = windUpPhase ? (attackProgress / 0.3) : 1.0;
        const strikeProgress = strikePhase ? ((attackProgress - 0.3) / 0.3) : (recoverPhase ? 1.0 : 0.0);
        const recoverProgress = recoverPhase ? ((attackProgress - 0.6) / 0.4) : 0.0;

        // Easing functions for smoother animation
        const easeInWindUp = Math.pow(windUpProgress, 2);
        const easeOutStrike = strikePhase ? (1 - Math.pow(1 - strikeProgress, 2)) : 0;
        const easeInOutRecover = recoverPhase ? (1 - Math.pow(1 - recoverProgress, 2)) : 0;

        // Body movement
        if (this.bodyGroup) {
            // Wind up: lean back, Strike: lunge forward, Recover: return to neutral
            let bodyLean = 0;

            if (windUpPhase) {
                // Wind up: lean back
                bodyLean = 0.3 * easeInWindUp;
            } else if (strikePhase) {
                // Strike: lunge forward
                bodyLean = 0.3 - (0.6 * strikeProgress);
            } else if (recoverPhase) {
                // Recover: return to neutral
                bodyLean = -0.3 + (0.3 * recoverProgress);
            }

            this.bodyGroup.rotation.x = bodyLean;

            // Side-to-side sway for more zombie-like movement
            this.bodyGroup.rotation.z = Math.sin(time * 8) * 0.05;

            // Forward movement during strike
            if (strikePhase) {
                // this.bodyGroup.position.z = -0.4 * strikeProgress; // Keep this if bodyGroup is child
            } else if (recoverPhase) {
                this.bodyGroup.position.z = -0.4 + (0.4 * recoverProgress);
            } else {
                this.bodyGroup.position.z = 0;
            }
        }

        // Arms attack animation
        if (this.leftArmGroup && this.rightArmGroup) {
            // Determine which arm is attacking (alternate between left and right)
            const isLeftArmAttack = Math.floor(time) % 2 === 0;

            // Get attacking and supporting arms
            const attackingArm = isLeftArmAttack ? this.leftArmGroup : this.rightArmGroup;
            const supportingArm = isLeftArmAttack ? this.rightArmGroup : this.leftArmGroup;

            if (windUpPhase) {
                // Wind up: raise attacking arm back and up, supporting arm forward for balance
                attackingArm.rotation.x = -Math.PI/2 * easeInWindUp; // Raise arm higher
                attackingArm.rotation.y = (isLeftArmAttack ? -0.3 : 0.3) * easeInWindUp; // Rotate slightly inward
                attackingArm.rotation.z = (isLeftArmAttack ? 0.7 : -0.7) * easeInWindUp; // Wider swing

                supportingArm.rotation.x = Math.PI/4 * easeInWindUp; // Raise supporting arm forward
                supportingArm.rotation.y = (isLeftArmAttack ? 0.2 : -0.2) * easeInWindUp; // Rotate slightly outward
                supportingArm.rotation.z = (isLeftArmAttack ? -0.4 : 0.4) * easeInWindUp; // Out from body
            } else if (strikePhase) {
                // Strike: swing attacking arm forward and down in an arc
                const strikeX = -Math.PI/2 + (Math.PI * 1.2 * strikeProgress); // Full forward swing
                const strikeZ = (isLeftArmAttack ? 0.7 : -0.7) * (1 - strikeProgress * 0.8); // Maintain some outward position

                attackingArm.rotation.x = strikeX;
                attackingArm.rotation.y = (isLeftArmAttack ? -0.3 : 0.3) * (1 - strikeProgress * 0.5); // Maintain some inward rotation
                attackingArm.rotation.z = strikeZ;

                // Supporting arm follows through with counter-balance
                supportingArm.rotation.x = Math.PI/4 + (Math.PI/8 * strikeProgress); // Raise slightly higher
                supportingArm.rotation.y = (isLeftArmAttack ? 0.2 : -0.2) * (1 - strikeProgress * 0.5); // Maintain outward rotation
                supportingArm.rotation.z = (isLeftArmAttack ? -0.4 : 0.4) * (1 - strikeProgress * 0.3); // Keep arm out
            } else if (recoverPhase) {
                // Recovery: return arms to neutral position
                const recoveryX = Math.PI * 0.7 - (Math.PI * 0.7 * recoverProgress); // Return from forward position
                const recoveryZ = (isLeftArmAttack ? 0.14 : -0.14) * (1 - recoverProgress); // Return from outward position

                attackingArm.rotation.x = recoveryX;
                attackingArm.rotation.y = (isLeftArmAttack ? -0.15 : 0.15) * (1 - recoverProgress);
                attackingArm.rotation.z = recoveryZ;

                // Supporting arm returns to neutral
                supportingArm.rotation.x = (Math.PI/4 + Math.PI/8) * (1 - recoverProgress);
                supportingArm.rotation.y = (isLeftArmAttack ? 0.1 : -0.1) * (1 - recoverProgress);
                supportingArm.rotation.z = (isLeftArmAttack ? -0.28 : 0.28) * (1 - recoverProgress);
            }
        }

        // Head follows the attack motion
        if (this.headGroup) {
            if (windUpPhase) {
                // Look up and tilt during windup
                this.headGroup.rotation.x = -0.3 * easeInWindUp;
                this.headGroup.rotation.y = (isLeftArmAttack ? -0.2 : 0.2) * easeInWindUp; // Turn head toward attacking arm
                this.headGroup.rotation.z = (isLeftArmAttack ? 0.15 : -0.15) * easeInWindUp; // Tilt head
            } else if (strikePhase) {
                // Look down and forward during strike
                this.headGroup.rotation.x = -0.3 + (0.6 * strikeProgress); // From looking up to looking down
                this.headGroup.rotation.y = (isLeftArmAttack ? -0.2 : 0.2) * (1 - strikeProgress * 0.7); // Keep some rotation
                this.headGroup.rotation.z = (isLeftArmAttack ? 0.15 : -0.15) * (1 - strikeProgress * 0.5); // Keep some tilt
            } else if (recoverPhase) {
                // Return head to neutral position
                this.headGroup.rotation.x = 0.3 - (0.3 * recoverProgress);
                this.headGroup.rotation.y = (isLeftArmAttack ? -0.06 : 0.06) * (1 - recoverProgress);
                this.headGroup.rotation.z = (isLeftArmAttack ? 0.075 : -0.075) * (1 - recoverProgress);
            }

            // Add subtle creepy head movement
            const creepFactor = 0.05;
            this.headGroup.rotation.x += Math.sin(time * 3.7) * creepFactor;
            this.headGroup.rotation.y += Math.sin(time * 2.9) * creepFactor * 0.5;
            this.headGroup.rotation.z += Math.sin(time * 4.3) * creepFactor * 0.7;
        }

        // Legs provide stable base with zombie-like movement
        if (this.leftLegGroup && this.rightLegGroup) {
            if (windUpPhase) {
                // Prepare stance - widen and shift weight
                const stanceWidth = 0.25 * easeInWindUp;
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;

                // Slight bend in knees to prepare
                const prepBend = 0.15 * easeInWindUp;
                this.leftLegGroup.rotation.x = prepBend;
                this.rightLegGroup.rotation.x = prepBend;

                // Shift weight to back leg (opposite of attacking arm)
                const weightShift = 0.1 * easeInWindUp;
                if (isLeftArmAttack) {
                    // Right leg takes weight
                    this.rightLegGroup.rotation.x += weightShift;
                    this.leftLegGroup.rotation.x -= weightShift * 0.5;
                } else {
                    // Left leg takes weight
                    this.leftLegGroup.rotation.x += weightShift;
                    this.rightLegGroup.rotation.x -= weightShift * 0.5;
                }
            } else if (strikePhase) {
                // Maintain wide stance
                const stanceWidth = 0.25;
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;

                // Bend knees more during strike for power
                const strikeBend = 0.15 + (0.2 * strikeProgress);
                this.leftLegGroup.rotation.x = strikeBend;
                this.rightLegGroup.rotation.x = strikeBend;

                // Shift weight forward as strike progresses
                const forwardShift = 0.15 * strikeProgress;
                this.leftLegGroup.rotation.x += forwardShift;
                this.rightLegGroup.rotation.x += forwardShift;

                // Rotate slightly for more natural movement
                const rotateAmount = 0.1 * strikeProgress;
                if (isLeftArmAttack) {
                    this.leftLegGroup.rotation.y = rotateAmount;
                    this.rightLegGroup.rotation.y = -rotateAmount * 0.5;
                } else {
                    this.rightLegGroup.rotation.y = rotateAmount;
                    this.leftLegGroup.rotation.y = -rotateAmount * 0.5;
                }
            } else if (recoverPhase) {
                // Return to neutral stance gradually
                const stanceWidth = 0.25 * (1 - recoverProgress);
                this.leftLegGroup.rotation.z = -stanceWidth;
                this.rightLegGroup.rotation.z = stanceWidth;

                // Straighten legs
                const recoverBend = (0.35) * (1 - recoverProgress);
                this.leftLegGroup.rotation.x = recoverBend;
                this.rightLegGroup.rotation.x = recoverBend;

                // Return rotation to neutral
                const rotateAmount = 0.1 * (1 - recoverProgress);
                if (isLeftArmAttack) {
                    this.leftLegGroup.rotation.y = rotateAmount;
                    this.rightLegGroup.rotation.y = -rotateAmount * 0.5;
                } else {
                    this.rightLegGroup.rotation.y = rotateAmount;
                    this.leftLegGroup.rotation.y = -rotateAmount * 0.5;
                }
            }

            // Add subtle shambling motion throughout
            const shambleAmount = 0.05;
            const shambleSpeed = 6.0;
            this.leftLegGroup.rotation.y += Math.sin(time * shambleSpeed) * shambleAmount;
            this.rightLegGroup.rotation.y += Math.sin(time * shambleSpeed + Math.PI) * shambleAmount;
        }
    }

    /**
     * Apply hit reaction animation
     * @param {number} time - Global time
     * @private
     */
    _applyHitReactionAnimation(time) {
        // Reset transforms first to ensure consistent starting pose
        this.resetTransforms();

        // Calculate hit reaction progress based on time
        // This creates a more dynamic reaction that settles over time
        const hitProgress = Math.min(1.0, (time - this.lastHitTime) * 2.0);
        const initialShock = Math.max(0, 1.0 - hitProgress * 3.0); // Strong at start, quickly fades
        const recovery = Math.min(1.0, Math.max(0, hitProgress - 0.3) * 1.5); // Starts after initial shock

        // Easing functions for smoother animation
        const easeOutShock = 1 - Math.pow(1 - initialShock, 3); // Strong at start, quickly fades
        const easeInRecovery = Math.pow(recovery, 2); // Gradual recovery

        // High-frequency wobble that decreases over time
        const wobbleFrequency = 15.0;
        const wobbleAmplitude = 0.2 * (1.0 - recovery);
        const wobble = Math.sin(time * wobbleFrequency) * wobbleAmplitude;

        // Stagger backwards with dynamic recovery
        if (this.bodyGroup) {
            // Initial backward lean that recovers over time
            const backwardLean = -0.4 * easeOutShock + wobble * 0.5;
            this.bodyGroup.rotation.x = backwardLean;

            // Side-to-side wobble that decreases over time
            const sideWobble = Math.sin(time * 12) * 0.2 * (1.0 - recovery);
            this.bodyGroup.rotation.z = sideWobble;

            // Slight twist
            this.bodyGroup.rotation.y = Math.sin(time * 8) * 0.15 * (1.0 - recovery);

            // Backward movement that recovers
            const backwardStagger = 0.2 * easeOutShock;
            this.bodyGroup.position.z = backwardStagger;
        }

        // Arms flail wildly then settle
        if (this.leftArmGroup) {
            // Raise arm up in shock
            const armRaise = Math.PI/4 * easeOutShock;
            this.leftArmGroup.rotation.x = armRaise + wobble;

            // Arm flings outward
            const armOut = 0.6 * easeOutShock;
            this.leftArmGroup.rotation.z = armOut + Math.sin(time * 14) * 0.3 * (1.0 - recovery);

            // Arm rotates
            this.leftArmGroup.rotation.y = Math.sin(time * 10) * 0.4 * (1.0 - recovery);
        }

        if (this.rightArmGroup) {
            // Raise arm up in shock (slightly asymmetric from left)
            const armRaise = Math.PI/4 * easeOutShock * 0.9;
            this.rightArmGroup.rotation.x = armRaise + wobble * 0.9;

            // Arm flings outward
            const armOut = -0.65 * easeOutShock;
            this.rightArmGroup.rotation.z = armOut - Math.sin(time * 13) * 0.3 * (1.0 - recovery);

            // Arm rotates
            this.rightArmGroup.rotation.y = -Math.sin(time * 11) * 0.4 * (1.0 - recovery);
        }

        // Head jerks back dramatically then recovers
        if (this.headGroup) {
            // Head jerks back
            const headJerk = -0.5 * easeOutShock;
            this.headGroup.rotation.x = headJerk + Math.sin(time * 18) * 0.15 * (1.0 - recovery);

            // Head tilts with wobble
            this.headGroup.rotation.z = Math.sin(time * 15) * 0.25 * (1.0 - recovery);

            // Head turns slightly
            this.headGroup.rotation.y = Math.sin(time * 12) * 0.2 * (1.0 - recovery);
        }

        // Legs buckle and recover
        if (this.leftLegGroup && this.rightLegGroup) {
            // Knees buckle
            const kneeBuckle = -0.2 * easeOutShock + 0.3 * easeInRecovery; // Negative is backward, positive is forward

            // Apply to legs with slight asymmetry
            this.leftLegGroup.rotation.x = kneeBuckle - 0.05 + wobble * 0.3;
            this.rightLegGroup.rotation.x = kneeBuckle + 0.05 + wobble * 0.3;

            // Legs spread slightly for balance
            const legSpread = 0.2 * easeOutShock;
            this.leftLegGroup.rotation.z = -legSpread + Math.sin(time * 10) * 0.1 * (1.0 - recovery);
            this.rightLegGroup.rotation.z = legSpread + Math.sin(time * 10 + Math.PI) * 0.1 * (1.0 - recovery);

            // Feet rotate slightly
            this.leftLegGroup.rotation.y = Math.sin(time * 8) * 0.15 * (1.0 - recovery);
            this.rightLegGroup.rotation.y = -Math.sin(time * 8) * 0.15 * (1.0 - recovery);
        }

        // Overall body movement
        if (this.zombieModel) {
            // Store base Y position if not already set
            if (this.zombieModel.userData.basePositionY === undefined) {
                this.zombieModel.userData.basePositionY = this.zombieModel.position.y;
            }

            // Body drops slightly then recovers
            const bodyDrop = -0.1 * easeOutShock + 0.05 * easeInRecovery;
            this.zombieModel.position.y = this.zombieModel.userData.basePositionY + bodyDrop;
        }
    }
}
