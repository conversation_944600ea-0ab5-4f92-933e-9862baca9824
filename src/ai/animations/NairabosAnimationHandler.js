import { AIStates } from '../AIStates.js';

/**
 * Animation handler for Nairabos shadow creature
 * Handles terrifying flying poses, limb movements, and shadow effects
 */
export class NairabosAnimationHandler {
    constructor(enemy) {
        this.enemy = enemy;
        // The enemy object IS the mesh in this case
        this.mesh = enemy.mesh || enemy;
        
        // Add geometry validation utility
        this.validateNumber = (value, fallback = 0) => {
            return (isFinite(value) && !isNaN(value)) ? value : fallback;
        };
        
        this.validateScale = (value, fallback = 1.0) => {
            return (isFinite(value) && !isNaN(value) && value > 0) ? value : fallback;
        };

        console.log(`[NairabosAnimationHandler] Initializing for enemy:`, enemy.name);
        console.log(`[NairabosAnimationHandler] Mesh object:`, this.mesh);

        // Get body parts - handle new visual group structure
        // First try to find the visual group, then look for body parts within it
        const visualGroup = this.mesh.getObjectByName('visualModel');
        const searchRoot = visualGroup || this.mesh;

        this.bodyGroup = searchRoot.getObjectByName('body');
        this.headGroup = searchRoot.getObjectByName('head');
        this.leftArmGroup = searchRoot.getObjectByName('leftArm');
        this.rightArmGroup = searchRoot.getObjectByName('rightArm');
        this.leftLegGroup = searchRoot.getObjectByName('leftLeg');
        this.rightLegGroup = searchRoot.getObjectByName('rightLeg');

        console.log(`[NairabosAnimationHandler] Visual group found:`, !!visualGroup);
        console.log(`[NairabosAnimationHandler] Search root:`, searchRoot.name);

        console.log(`[NairabosAnimationHandler] Body parts found:`, {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup
        });
        
        // CRITICAL FIX: Enhanced debugging and fallback search for missing body parts
        const missingParts = [];
        if (!this.bodyGroup) missingParts.push('body');
        if (!this.headGroup) missingParts.push('head');
        if (!this.leftArmGroup) missingParts.push('leftArm');
        if (!this.rightArmGroup) missingParts.push('rightArm');
        if (!this.leftLegGroup) missingParts.push('leftLeg');
        if (!this.rightLegGroup) missingParts.push('rightLeg');
        
        if (missingParts.length > 0) {
            console.error(`[NairabosAnimationHandler] CRITICAL: Missing body parts: ${missingParts.join(', ')}`);
            console.log(`[NairabosAnimationHandler] Performing exhaustive search...`);
            
            // Complete hierarchy dump for debugging
            let allChildren = [];
            this.mesh.traverse(child => {
                if (child.name) {
                    allChildren.push({
                        name: child.name,
                        type: child.type,
                        hasChildren: child.children.length > 0,
                        parent: child.parent ? child.parent.name : 'root'
                    });
                }
            });
            console.log(`[NairabosAnimationHandler] Complete hierarchy:`, allChildren);
            
            // Try alternative search strategies
            missingParts.forEach(partName => {
                // Try case-insensitive search
                let found = null;
                this.mesh.traverse(child => {
                    if (child.name && child.name.toLowerCase() === partName.toLowerCase()) {
                        found = child;
                    }
                });
                
                if (found) {
                    console.log(`[NairabosAnimationHandler] Found ${partName} with case mismatch: ${found.name}`);
                    // Assign to correct property
                    this[partName + 'Group'] = found;
                } else {
                    console.error(`[NairabosAnimationHandler] ${partName} not found even with fallback search`);
                }
            });
        }

        // Store original positions for reference
        this.originalPositions = {};
        if (this.bodyGroup) this.originalPositions.body = this.bodyGroup.position.clone();
        if (this.headGroup) this.originalPositions.head = this.headGroup.position.clone();
        if (this.leftArmGroup) this.originalPositions.leftArm = this.leftArmGroup.position.clone();
        if (this.rightArmGroup) this.originalPositions.rightArm = this.rightArmGroup.position.clone();
        if (this.leftLegGroup) this.originalPositions.leftLeg = this.leftLegGroup.position.clone();
        if (this.rightLegGroup) this.originalPositions.rightLeg = this.rightLegGroup.position.clone();

        // Animation data from enemy userData
        this.animationData = this.mesh.userData.animationData || {
            shadowFloatSpeed: 2.0,
            shadowFloatAmplitude: 0.3,
            limbSwaySpeed: 1.5,
            limbSwayAmplitude: Math.PI / 12,
            attackAnimationDuration: 0.8,
            jumpHeight: 5.0,
            jumpDuration: 1.2,
            shadowPulseSpeed: 3.0,
            shadowPulseAmplitude: 0.2
        };

        // Animation state
        this.isAttacking = false;
        this.attackAnimationProgress = 0;
        this.isJumping = false;
        this.jumpProgress = 0;
        this.currentAttackType = null;
        this.attackStartTime = 0;
        
        console.log('NairabosAnimationHandler initialized for terrifying shadow creature');
    }

    /**
     * Update animations based on AI state
     */
    update(deltaTime, aiState) {
        if (!this.mesh) {
            console.warn(`[NairabosAnimationHandler] No mesh available for animation`);
            return;
        }

        // CRITICAL: Validate deltaTime to prevent NaN propagation
        const safeDeltaTime = (isFinite(deltaTime) && !isNaN(deltaTime) && deltaTime >= 0) ? deltaTime : 0.016;
        if (safeDeltaTime !== deltaTime) {
            console.warn(`[NairabosAnimationHandler] Invalid deltaTime ${deltaTime}, using ${safeDeltaTime}`);
        }

        const globalTime = Date.now() * 0.001; // Convert to seconds
        
        // Validate globalTime
        if (!isFinite(globalTime) || isNaN(globalTime)) {
            console.error('[NairabosAnimationHandler] Invalid globalTime, aborting animation update');
            return;
        }

        // Update attack animation progress with NaN protection
        if (this.isAttacking) {
            const duration = Math.max(this.animationData.attackAnimationDuration || 0.8, 0.001);
            const progressIncrement = deltaTime / duration;
            
            // Validate against NaN/Infinity
            if (isFinite(progressIncrement) && !isNaN(progressIncrement)) {
                this.attackAnimationProgress += progressIncrement;
            } else {
                console.warn('[NairabosAnimationHandler] Invalid progress increment, using fallback');
                this.attackAnimationProgress += deltaTime / 0.8; // Safe fallback
            }
            
            if (this.attackAnimationProgress >= 1.0) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
                this.currentAttackType = null;
                
                // CRITICAL FIX: Reset body position and rotation after attacks to fix torso offset
                if (this.bodyGroup && this.originalPositions.body) {
                    this.bodyGroup.scale.set(1, 1, 1);
                    this.bodyGroup.position.copy(this.originalPositions.body);
                    this.bodyGroup.rotation.set(0, 0, 0);
                    console.log('[NairabosAnimationHandler] 🔧 Body position reset to original after attack');
                }
            }
            
            // Play the attack animation
            if (this.currentAttackType) {
                this.playAttackAnimation(this.currentAttackType, this.attackAnimationProgress);
                return; // Skip other animations during attack
            }
        }

        // Normalize AI state (handle both string and constant)
        const normalizedState = typeof aiState === 'string' ? aiState.toUpperCase() : aiState;

        // Calculate distance to player for behavior switching
        const playerPosition = this._getPlayerPosition();
        const distanceToPlayer = playerPosition ? this.mesh.position.distanceTo(playerPosition) : 999;
        const isCloseToPlayer = distanceToPlayer <= 6.0; // Within 6 units = walking mode

        console.log(`[NairabosAnimationHandler] State: ${normalizedState}, Distance to player: ${distanceToPlayer.toFixed(2)}, Close: ${isCloseToPlayer}`);

        // Apply animations based on state and distance to player
        switch (normalizedState) {
            case 'HOVERING':
            case 'IDLE':
            case AIStates.HOVERING:
            case AIStates.IDLE:
                this._applyWalkingAnimation(globalTime); // Always walk for BossAI
                break;

            case 'STRAFING': // Flying AI uses this for circling
            case 'MOVING':
            case 'CHASING':
            case AIStates.STRAFING:
            case AIStates.MOVING:
            case AIStates.CHASING:
                this._applyWalkingAnimation(globalTime); // Always walk for BossAI
                break;

            case 'SWOOPING':
            case AIStates.SWOOPING:
                this._applySwoopingAnimation(globalTime);
                break;

            case 'ASCENDING':
            case AIStates.ASCENDING:
                this._applyFlyingAnimation(globalTime); // Always flying when ascending
                break;

            case 'ATTACKING':
            case AIStates.ATTACKING:
                if (!this.isAttacking) {
                    this.isAttacking = true;
                    this.attackAnimationProgress = 0;
                }
                this._applyAttackingAnimation(globalTime);
                break;

            default:
                // Default based on distance to player
                if (isCloseToPlayer) {
                    this._applyWalkingAnimation(globalTime);
                } else {
                    this._applyFloatingAnimation(globalTime);
                }
                break;
        }

        // Always apply shadow effects (includes proper scaling)
        this._applyShadowEffects(globalTime);

        // REMOVED: Scale restoration that was fighting with shadow effects
        // The shadow effects method now handles all scaling properly
    }

    /**
     * Apply floating animation for idle/hovering
     */
    _applyFloatingAnimation(time) {
        try {
            const floatSpeed = this.animationData.shadowFloatSpeed;
            const floatAmplitude = this.animationData.shadowFloatAmplitude;

            // Gentle floating motion for body
            if (this.bodyGroup && this.originalPositions.body) {
                const bodyFloat = Math.sin(time * floatSpeed) * floatAmplitude;
                this.bodyGroup.position.y = this.originalPositions.body.y + bodyFloat;

                // Slight swaying
                this.bodyGroup.rotation.z = Math.sin(time * floatSpeed * 0.7) * 0.05;
            }

            // Head slight bobbing
            if (this.headGroup && this.originalPositions.head) {
                const headBob = Math.sin(time * floatSpeed * 1.2) * (floatAmplitude * 0.3);
                this.headGroup.position.y = this.originalPositions.head.y + headBob;
            }

            // Arms hanging menacingly with slight sway
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = Math.sin(time * floatSpeed * 0.8) * 0.1;
                this.leftArmGroup.rotation.z = 0.2 + Math.sin(time * floatSpeed * 0.6) * 0.05;
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.x = Math.sin(time * floatSpeed * 0.8 + Math.PI) * 0.1;
                this.rightArmGroup.rotation.z = -0.2 - Math.sin(time * floatSpeed * 0.6) * 0.05;
            }

            // Legs in floating position - slightly bent and spread
            if (this.leftLegGroup) {
                this.leftLegGroup.rotation.x = 0.3 + Math.sin(time * floatSpeed * 0.9) * 0.1;
                this.leftLegGroup.rotation.z = -0.1;
            }
            if (this.rightLegGroup) {
                this.rightLegGroup.rotation.x = 0.3 + Math.sin(time * floatSpeed * 0.9 + Math.PI) * 0.1;
                this.rightLegGroup.rotation.z = 0.1;
            }
        } catch (error) {
            console.error(`[NairabosAnimationHandler] Error in floating animation:`, error);
        }
    }

    /**
     * Apply flying animation for movement
     */
    _applyFlyingAnimation(time) {
        try {
            const floatSpeed = this.animationData.shadowFloatSpeed * 1.5;
            const floatAmplitude = this.animationData.shadowFloatAmplitude * 0.8;

            console.log(`[NairabosAnimationHandler] Applying flying animation - time: ${time}`);

            // More dynamic body movement during flight
            if (this.bodyGroup && this.originalPositions.body) {
                const bodyFloat = Math.sin(time * floatSpeed) * floatAmplitude;
                this.bodyGroup.position.y = this.originalPositions.body.y + bodyFloat;

                // Forward lean during flight
                this.bodyGroup.rotation.x = -0.2;
                this.bodyGroup.rotation.z = Math.sin(time * floatSpeed * 0.8) * 0.08;
            }

            // Head tracking forward
            if (this.headGroup) {
                this.headGroup.rotation.x = -0.1;
            }

            // Arms spread wide and back for aerodynamics
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = -0.3 + Math.sin(time * floatSpeed * 1.2) * 0.15;
                this.leftArmGroup.rotation.z = 0.8 + Math.sin(time * floatSpeed * 0.7) * 0.1;
                this.leftArmGroup.rotation.y = -0.2;
                console.log(`[NairabosAnimationHandler] Left arm rotation:`, this.leftArmGroup.rotation);
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.x = -0.3 + Math.sin(time * floatSpeed * 1.2 + Math.PI) * 0.15;
                this.rightArmGroup.rotation.z = -0.8 - Math.sin(time * floatSpeed * 0.7) * 0.1;
                this.rightArmGroup.rotation.y = 0.2;
                console.log(`[NairabosAnimationHandler] Right arm rotation:`, this.rightArmGroup.rotation);
            }

            // CRITICAL: Legs tucked up and back for flying pose - THIS SHOULD FIX THE LEG ISSUE
            if (this.leftLegGroup) {
                this.leftLegGroup.rotation.x = -0.8 + Math.sin(time * floatSpeed * 1.1) * 0.2;
                this.leftLegGroup.rotation.z = -0.2;
                this.leftLegGroup.rotation.y = -0.1;
                console.log(`[NairabosAnimationHandler] Left leg rotation:`, this.leftLegGroup.rotation);
            } else {
                console.warn(`[NairabosAnimationHandler] Left leg group not found!`);
            }
            if (this.rightLegGroup) {
                this.rightLegGroup.rotation.x = -0.8 + Math.sin(time * floatSpeed * 1.1 + Math.PI) * 0.2;
                this.rightLegGroup.rotation.z = 0.2;
                this.rightLegGroup.rotation.y = 0.1;
                console.log(`[NairabosAnimationHandler] Right leg rotation:`, this.rightLegGroup.rotation);
            } else {
                console.warn(`[NairabosAnimationHandler] Right leg group not found!`);
            }
        } catch (error) {
            console.error(`[NairabosAnimationHandler] Error in flying animation:`, error);
        }
    }

    /**
     * Apply swooping animation for aggressive movement
     */
    _applySwoopingAnimation(time) {
        // More aggressive version of flying animation
        this._applyFlyingAnimation(time);

        // Additional swooping effects
        if (this.bodyGroup) {
            this.bodyGroup.rotation.x = -0.4; // More forward lean
            this.bodyGroup.rotation.z = Math.sin(time * 8) * 0.15; // More dramatic sway
        }

        // Arms pulled back more aggressively
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = -0.6;
            this.leftArmGroup.rotation.z = 1.2;
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = -0.6;
            this.rightArmGroup.rotation.z = -1.2;
        }

        // Legs tucked up tight
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = -1.2;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = -1.2;
        }
    }

    /**
     * Apply attacking animation
     */
    _applyAttackingAnimation(time) {
        const progress = this.attackAnimationProgress;

        if (progress < 0.3) {
            // Lunge forward
            const lungeProgress = progress / 0.3;
            
            if (this.bodyGroup) {
                this.bodyGroup.rotation.x = -0.6 * lungeProgress;
                // DISABLED: Body forward movement to fix torso offset issue
                // this.bodyGroup.position.z = this.originalPositions.body.z + lungeProgress * 1.5;
            }

            // Arms reaching forward
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = -1.0 * lungeProgress;
                this.leftArmGroup.rotation.z = 0.5 * lungeProgress;
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.x = -1.0 * lungeProgress;
                this.rightArmGroup.rotation.z = -0.5 * lungeProgress;
            }

            // Legs pulled up for attack
            if (this.leftLegGroup) {
                this.leftLegGroup.rotation.x = -0.5 * lungeProgress;
            }
            if (this.rightLegGroup) {
                this.rightLegGroup.rotation.x = -0.5 * lungeProgress;
            }

        } else if (progress < 0.7) {
            // Strike phase - maintain lunge position with trembling
            const trembleAmount = Math.sin(time * 20) * 0.05;
            
            if (this.bodyGroup) {
                this.bodyGroup.rotation.x = -0.6 + trembleAmount;
            }

        } else {
            // Return to normal with NaN protection
            const returnProgress = Math.max(0, Math.min(1, (progress - 0.7) / 0.3));
            
            // Protect against Math.pow producing NaN
            const baseValue = Math.max(0, Math.min(1, 1 - returnProgress));
            const easeOut = 1 - Math.pow(baseValue, 3);
            
            // Validate easeOut before using
            const safeEaseOut = isFinite(easeOut) && !isNaN(easeOut) ? easeOut : 0;
            
            if (this.bodyGroup) {
                this.bodyGroup.rotation.x = -0.6 * (1 - safeEaseOut);
                // DISABLED: Body forward movement to fix torso offset issue
                // if (this.originalPositions.body) {
                //     this.bodyGroup.position.z = this.originalPositions.body.z + 1.5 * (1 - safeEaseOut);
                // }
            }
        }
    }

    /**
     * Apply shadow effects and material animations with NaN protection
     */
    _applyShadowEffects(time) {
        // This could be expanded to animate materials, opacity, etc.
        // For now, just subtle body effects

        if (this.mesh) {
            // Validate input time to prevent NaN propagation
            const safeTime = isFinite(time) && !isNaN(time) ? time : 0;
            const safeSpeed = isFinite(this.animationData.shadowPulseSpeed) && !isNaN(this.animationData.shadowPulseSpeed) 
                ? this.animationData.shadowPulseSpeed : 3.0;
            
            // CRITICAL FIX: Apply subtle pulsing effect while maintaining the 3.5 base scale
            // Use much smaller pulse amplitude to avoid positioning conflicts
            const pulseInput = safeTime * safeSpeed;
            const rawPulse = Math.sin(pulseInput);
            const pulse = isFinite(rawPulse) && !isNaN(rawPulse) ? 1.0 + rawPulse * 0.005 : 1.0;
            
            const baseScale = 3.5; // Nairabos base scale set by DungeonHandler
            const finalScale = baseScale * pulse;
            
            // Additional validation before applying scale
            if (isFinite(finalScale) && !isNaN(finalScale) && finalScale > 0) {
                this.mesh.scale.set(finalScale, finalScale, finalScale);
            } else {
                console.warn('[NairabosAnimationHandler] Invalid scale detected, using safe default');
                this.mesh.scale.set(3.5, 3.5, 3.5);
            }

            // CRITICAL: Runtime NaN detection in all child geometries
            this.mesh.traverse(child => {
                if (child.isMesh && child.geometry && child.geometry.attributes.position) {
                    const positions = child.geometry.attributes.position.array;
                    let hasNaN = false;
                    
                    // Check for NaN in position attributes
                    for (let i = 0; i < positions.length && i < 30; i += 3) { // Sample first 10 vertices
                        if (!isFinite(positions[i]) || !isFinite(positions[i+1]) || !isFinite(positions[i+2]) ||
                            isNaN(positions[i]) || isNaN(positions[i+1]) || isNaN(positions[i+2])) {
                            console.error(`🚨 RUNTIME NaN DETECTED in ${child.name || 'unnamed'} geometry UUID: ${child.geometry.uuid}`);
                            console.error(`NaN at vertex ${i/3}: (${positions[i]}, ${positions[i+1]}, ${positions[i+2]})`);
                            console.error('Child transform:', {
                                position: child.position.toArray(),
                                scale: child.scale.toArray(),
                                rotation: [child.rotation.x, child.rotation.y, child.rotation.z]
                            });
                            hasNaN = true;
                            break;
                        }
                    }
                    
                    if (hasNaN) {
                        // Emergency fix: Reset geometry to prevent crash
                        console.error(`🔧 EMERGENCY: Resetting corrupted geometry ${child.geometry.uuid}`);
                        child.visible = false; // Hide to prevent rendering crash
                    }
                }
            });

            // Only log occasionally to reduce console spam
            if (Math.floor(safeTime * 10) % 50 === 0) {
                console.log(`🔥 SHADOW EFFECTS - Base: ${baseScale}, Pulse: ${pulse.toFixed(3)}, Final: ${finalScale.toFixed(3)}`);
            }
        }
    }

    /**
     * Apply walking animation for ground movement
     */
    _applyWalkingAnimation(time) {
        try {
            const walkSpeed = 3.0; // Walking speed
            const walkAmplitude = Math.PI / 8; // Leg swing amplitude

            console.log(`[NairabosAnimationHandler] Applying walking animation - time: ${time}`);

            // Body movement during walking - less floating, more grounded
            if (this.bodyGroup && this.originalPositions.body) {
                // Subtle body bob during walking
                const bodyBob = Math.sin(time * walkSpeed * 2) * 0.1;
                this.bodyGroup.position.y = this.originalPositions.body.y + bodyBob;

                // Slight forward lean while walking
                this.bodyGroup.rotation.x = -0.1;
                this.bodyGroup.rotation.z = Math.sin(time * walkSpeed) * 0.03;
            }

            // Head movement during walking
            if (this.headGroup) {
                this.headGroup.rotation.x = -0.05; // Slight forward look
            }

            // Arms swinging during walk
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude * 0.5;
                this.leftArmGroup.rotation.z = 0.1;
                this.leftArmGroup.rotation.y = 0;
                console.log(`[NairabosAnimationHandler] Left arm walking rotation:`, this.leftArmGroup.rotation);
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude * 0.5;
                this.rightArmGroup.rotation.z = -0.1;
                this.rightArmGroup.rotation.y = 0;
                console.log(`[NairabosAnimationHandler] Right arm walking rotation:`, this.rightArmGroup.rotation);
            }

            // CRITICAL: Legs walking animation - alternating steps with fallback
            if (this.leftLegGroup) {
                this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed) * walkAmplitude;
                this.leftLegGroup.rotation.z = -0.05;
                this.leftLegGroup.rotation.y = 0;
                console.log(`[NairabosAnimationHandler] Left leg walking rotation:`, this.leftLegGroup.rotation);
            } else {
                console.warn(`[NairabosAnimationHandler] Left leg group not found for walking! Creating emergency animation...`);
                // Emergency fallback: animate the entire body to simulate walking
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.z = Math.sin(time * walkSpeed) * (walkAmplitude * 0.1);
                }
            }
            if (this.rightLegGroup) {
                this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * walkAmplitude;
                this.rightLegGroup.rotation.z = 0.05;
                this.rightLegGroup.rotation.y = 0;
                console.log(`[NairabosAnimationHandler] Right leg walking rotation:`, this.rightLegGroup.rotation);
            } else {
                console.warn(`[NairabosAnimationHandler] Right leg group not found for walking! Using body fallback...`);
                // Emergency fallback: animate the entire body
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.x = Math.sin(time * walkSpeed + Math.PI) * (walkAmplitude * 0.1);
                }
            }
        } catch (error) {
            console.error(`[NairabosAnimationHandler] Error in walking animation:`, error);
        }
    }

    /**
     * Get player position for distance calculations
     */
    _getPlayerPosition() {
        try {
            // Try to get player position from various possible sources
            if (window.gameInstance?.dungeonHandler?.player?.position) {
                return window.gameInstance.dungeonHandler.player.position;
            }
            if (window.dungeonHandler?.player?.position) {
                return window.dungeonHandler.player.position;
            }
            if (this.enemy?.userData?.aiBrain?.player?.position) {
                return this.enemy.userData.aiBrain.player.position;
            }
            return null;
        } catch (error) {
            console.warn(`[NairabosAnimationHandler] Could not get player position:`, error);
            return null;
        }
    }

    /**
     * Trigger attack animation with specific type
     */
    triggerAttack(attackType = 'default') {
        this.isAttacking = true;
        this.attackAnimationProgress = 0;
        this.currentAttackType = attackType;
        this.attackStartTime = Date.now() * 0.001;
        
        console.log(`[NairabosAnimationHandler] Triggering ${attackType} attack animation`);
    }
    
    /**
     * Play specific attack animations based on attack type
     */
    playAttackAnimation(attackType, progress) {
        const t = progress; // 0 to 1
        
        switch(attackType) {
            case 'pulse_wave':
                // Arms spread wide, then push forward
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.8 * (1 - t);
                    this.leftArmGroup.rotation.x = -Math.PI * 0.3 * t;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.8 * (1 - t);
                    this.rightArmGroup.rotation.x = -Math.PI * 0.3 * t;
                }
                // Body pulses with NaN protection
                if (this.bodyGroup) {
                    const pulse = this.validateNumber(Math.sin(t * Math.PI * 4) * 0.1, 0);
                    const scaleX = this.validateScale(1 + pulse, 1.0);
                    const scaleZ = this.validateScale(1 + pulse, 1.0);
                    this.bodyGroup.scale.x = scaleX;
                    this.bodyGroup.scale.z = scaleZ;
                }
                break;
                
            case 'shadow_minion':
                // Raise arms dramatically
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.7 * t;
                    this.leftArmGroup.rotation.x = -Math.PI * 0.5 * t;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.7 * t;
                    this.rightArmGroup.rotation.x = -Math.PI * 0.5 * t;
                }
                // Head looks up
                if (this.headGroup) {
                    this.headGroup.rotation.x = -Math.PI * 0.3 * t;
                }
                break;
                
            case 'spiral_death':
                // Spin entire body
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.y = Math.PI * 4 * t;
                }
                // Arms out spinning
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.5;
                    this.leftArmGroup.rotation.y = Math.PI * 2 * t;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.5;
                    this.rightArmGroup.rotation.y = -Math.PI * 2 * t;
                }
                break;
                
            case 'floating_circles':
                // Wave arms in circular motions
                const waveTime = t * Math.PI * 2;
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.4 + Math.sin(waveTime) * 0.3;
                    this.leftArmGroup.rotation.x = Math.cos(waveTime) * 0.3;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.4 - Math.sin(waveTime + Math.PI) * 0.3;
                    this.rightArmGroup.rotation.x = Math.cos(waveTime + Math.PI) * 0.3;
                }
                break;
                
            case 'bullet_corridor':
                // Arms spread wide, then clap together to create corridors
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.9 * (1 - t);
                    this.leftArmGroup.rotation.x = -Math.PI * 0.2 * t;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.9 * (1 - t);
                    this.rightArmGroup.rotation.x = -Math.PI * 0.2 * t;
                }
                // Body leans forward slightly
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.x = -Math.PI * 0.1 * t;
                }
                break;
                
            case 'sweeping_wave':
                // Dramatic arm sweep across body
                const sweepAngle = Math.PI * 1.5 * t;
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.3 + Math.sin(sweepAngle) * 0.8;
                    this.leftArmGroup.rotation.y = Math.cos(sweepAngle) * 0.6;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.3 - Math.sin(sweepAngle + Math.PI) * 0.8;
                    this.rightArmGroup.rotation.y = -Math.cos(sweepAngle + Math.PI) * 0.6;
                }
                // Body follows the sweep
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.y = Math.sin(sweepAngle * 0.5) * 0.3;
                }
                break;
                
            case 'zigzag_maze':
                // Sharp, angular arm movements like weaving
                const zigzagTime = t * Math.PI * 6; // Fast angular motions
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.5 + Math.sign(Math.sin(zigzagTime)) * 0.4;
                    this.leftArmGroup.rotation.x = -Math.PI * 0.3 * Math.abs(Math.sin(zigzagTime));
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.5 - Math.sign(Math.sin(zigzagTime + Math.PI)) * 0.4;
                    this.rightArmGroup.rotation.x = -Math.PI * 0.3 * Math.abs(Math.sin(zigzagTime + Math.PI));
                }
                // Head moves sharply side to side
                if (this.headGroup) {
                    this.headGroup.rotation.y = Math.sign(Math.sin(zigzagTime)) * 0.2;
                }
                break;
                
            case 'single_laser':
                // Point dramatically at target, then lean back for laser with spinning arms
                if (this.leftArmGroup && this.rightArmGroup) {
                    if (t < 0.4) {
                        // Charging phase - arms spinning while pointing
                        const spinSpeed = t * Math.PI * 8; // Spinning motion during charge
                        this.leftArmGroup.rotation.x = -Math.PI * 0.4 * t;
                        this.leftArmGroup.rotation.z = Math.PI * 0.2 * t + Math.sin(spinSpeed) * 0.3;
                        this.leftArmGroup.rotation.y = Math.cos(spinSpeed) * 0.2;
                        this.rightArmGroup.rotation.x = -Math.PI * 0.4 * t;
                        this.rightArmGroup.rotation.z = -Math.PI * 0.2 * t - Math.sin(spinSpeed) * 0.3;
                        this.rightArmGroup.rotation.y = -Math.cos(spinSpeed) * 0.2;
                    } else {
                        // Firing phase - arms locked in position
                        this.leftArmGroup.rotation.x = -Math.PI * 0.4;
                        this.leftArmGroup.rotation.z = Math.PI * 0.2;
                        this.leftArmGroup.rotation.y = 0;
                        this.rightArmGroup.rotation.x = -Math.PI * 0.4;
                        this.rightArmGroup.rotation.z = -Math.PI * 0.2;
                        this.rightArmGroup.rotation.y = 0;
                    }
                }
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.x = -Math.PI * 0.2 * t; // Lean back for power
                }
                if (this.headGroup) {
                    this.headGroup.rotation.x = -Math.PI * 0.1 * t; // Head up
                }
                break;
                
            case 'cross_laser':
                // Arms spread wide in X formation with spinning charge motion
                if (this.leftArmGroup) {
                    if (t < 0.5) {
                        // Charging phase - arms spinning while spreading
                        const spinSpeed = t * Math.PI * 6;
                        this.leftArmGroup.rotation.z = Math.PI * 0.7 * t + Math.sin(spinSpeed) * 0.2;
                        this.leftArmGroup.rotation.y = Math.PI * 0.3 * t + Math.cos(spinSpeed) * 0.15;
                        this.leftArmGroup.rotation.x = Math.sin(spinSpeed * 1.5) * 0.1;
                    } else {
                        // Firing phase - arms locked in X position
                        this.leftArmGroup.rotation.z = Math.PI * 0.7;
                        this.leftArmGroup.rotation.y = Math.PI * 0.3;
                        this.leftArmGroup.rotation.x = 0;
                    }
                }
                if (this.rightArmGroup) {
                    if (t < 0.5) {
                        // Charging phase - arms spinning while spreading
                        const spinSpeed = t * Math.PI * 6;
                        this.rightArmGroup.rotation.z = -Math.PI * 0.7 * t - Math.sin(spinSpeed) * 0.2;
                        this.rightArmGroup.rotation.y = -Math.PI * 0.3 * t - Math.cos(spinSpeed) * 0.15;
                        this.rightArmGroup.rotation.x = Math.sin(spinSpeed * 1.5) * 0.1;
                    } else {
                        // Firing phase - arms locked in X position
                        this.rightArmGroup.rotation.z = -Math.PI * 0.7;
                        this.rightArmGroup.rotation.y = -Math.PI * 0.3;
                        this.rightArmGroup.rotation.x = 0;
                    }
                }
                if (this.bodyGroup) {
                    const pulse = this.validateNumber(Math.sin(t * Math.PI * 8) * 0.05, 0);
                    const scaleX = this.validateScale(1 + pulse, 1.0);
                    const scaleZ = this.validateScale(1 + pulse, 1.0);
                    this.bodyGroup.scale.x = scaleX;
                    this.bodyGroup.scale.z = scaleZ;
                }
                break;
                
            case 'rotating_laser':
                // Spinning motion with arms extended
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.y = Math.PI * 4 * t; // Spin the whole body
                }
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.5;
                    this.leftArmGroup.rotation.x = -Math.PI * 0.3;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.5;
                    this.rightArmGroup.rotation.x = -Math.PI * 0.3;
                }
                if (this.headGroup) {
                    this.headGroup.rotation.y = Math.PI * 2 * t; // Head spins too
                }
                break;
                
            case 'combination_chaos':
                // Chaotic movements - multiple rapid animations
                const chaosTime = t * Math.PI * 12; // Very fast movements
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.6 + Math.sin(chaosTime) * 0.4;
                    this.leftArmGroup.rotation.x = Math.cos(chaosTime * 1.3) * 0.3;
                    this.leftArmGroup.rotation.y = Math.sin(chaosTime * 0.7) * 0.2;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.6 - Math.sin(chaosTime + Math.PI) * 0.4;
                    this.rightArmGroup.rotation.x = Math.cos(chaosTime * 1.3 + Math.PI) * 0.3;
                    this.rightArmGroup.rotation.y = -Math.sin(chaosTime * 0.7) * 0.2;
                }
                if (this.bodyGroup) {
                    // Body oscillates rapidly
                    this.bodyGroup.rotation.y = Math.sin(chaosTime * 0.5) * 0.2;
                    this.bodyGroup.rotation.x = Math.cos(chaosTime * 0.8) * 0.1;
                    const rawScale = Math.sin(chaosTime * 2) * 0.1;
                    const scale = this.validateScale(1 + this.validateNumber(rawScale, 0), 1.0);
                    this.bodyGroup.scale.set(scale, scale, scale);
                }
                if (this.headGroup) {
                    // Head moves erratically
                    this.headGroup.rotation.y = Math.sin(chaosTime * 1.1) * 0.3;
                    this.headGroup.rotation.x = Math.cos(chaosTime * 0.9) * 0.2;
                }
                break;
                
            case 'floor_hazards':
                // Stomp and pound the ground
                if (this.leftArmGroup && this.rightArmGroup) {
                    // Arms pound downward
                    this.leftArmGroup.rotation.x = -Math.PI * 0.8 * t;
                    this.leftArmGroup.rotation.z = Math.PI * 0.2;
                    this.rightArmGroup.rotation.x = -Math.PI * 0.8 * t;
                    this.rightArmGroup.rotation.z = -Math.PI * 0.2;
                }
                if (this.bodyGroup) {
                    // Body leans down for ground corruption
                    this.bodyGroup.rotation.x = -Math.PI * 0.4 * t;
                    this.bodyGroup.position.y = this.originalPositions.body.y - t * 0.5;
                }
                if (this.leftLegGroup && this.rightLegGroup) {
                    // Stomp motion
                    const stomp = Math.sin(t * Math.PI * 4) * 0.3;
                    this.leftLegGroup.rotation.x = stomp;
                    this.rightLegGroup.rotation.x = -stomp;
                }
                break;
                
            case 'arena_shrink':
                // Dramatic spreading arms outward, then pulling inward
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.8 * (1 - t);
                    this.leftArmGroup.rotation.x = -Math.PI * 0.3 * t;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.8 * (1 - t);
                    this.rightArmGroup.rotation.x = -Math.PI * 0.3 * t;
                }
                if (this.bodyGroup) {
                    // Body expands then contracts
                    const rawScaleEffect = Math.sin(t * Math.PI) * 0.2;
                    const scaleEffect = this.validateScale(1 + this.validateNumber(rawScaleEffect, 0), 1.0);
                    this.bodyGroup.scale.set(scaleEffect, scaleEffect, scaleEffect);
                    this.bodyGroup.rotation.y = Math.sin(t * Math.PI * 2) * 0.1;
                }
                if (this.headGroup) {
                    this.headGroup.rotation.x = -Math.PI * 0.2 * t;
                }
                break;
                
            case 'desperation_mode':
                // Extremely violent and erratic movements for final 10% health
                const desperationTime = t * Math.PI * 20; // Very fast movements
                if (this.leftArmGroup) {
                    this.leftArmGroup.rotation.z = Math.PI * 0.9 + Math.sin(desperationTime) * 0.6;
                    this.leftArmGroup.rotation.x = Math.cos(desperationTime * 1.7) * 0.5;
                    this.leftArmGroup.rotation.y = Math.sin(desperationTime * 0.3) * 0.4;
                }
                if (this.rightArmGroup) {
                    this.rightArmGroup.rotation.z = -Math.PI * 0.9 - Math.sin(desperationTime + Math.PI) * 0.6;
                    this.rightArmGroup.rotation.x = Math.cos(desperationTime * 1.7 + Math.PI) * 0.5;
                    this.rightArmGroup.rotation.y = -Math.sin(desperationTime * 0.3) * 0.4;
                }
                if (this.bodyGroup) {
                    // Violent body convulsions
                    this.bodyGroup.rotation.y = Math.sin(desperationTime * 0.7) * 0.4;
                    this.bodyGroup.rotation.x = Math.cos(desperationTime * 1.1) * 0.2;
                    const rawScale = Math.sin(desperationTime * 3) * 0.2;
                    const scale = this.validateScale(1 + this.validateNumber(rawScale, 0), 1.0);
                    this.bodyGroup.scale.set(scale, scale, scale);
                }
                if (this.headGroup) {
                    // Head thrashing wildly
                    this.headGroup.rotation.y = Math.sin(desperationTime * 1.3) * 0.5;
                    this.headGroup.rotation.x = Math.cos(desperationTime * 0.9) * 0.3;
                    this.headGroup.rotation.z = Math.sin(desperationTime * 2.1) * 0.2;
                }
                if (this.leftLegGroup && this.rightLegGroup) {
                    // Legs kicking frantically
                    this.leftLegGroup.rotation.x = Math.sin(desperationTime * 2) * 0.4;
                    this.rightLegGroup.rotation.x = Math.sin(desperationTime * 2 + Math.PI) * 0.4;
                }
                break;
                
            default:
                // Default attack - lunge forward
                if (this.bodyGroup) {
                    this.bodyGroup.rotation.x = -Math.PI * 0.2 * Math.sin(t * Math.PI);
                }
                if (this.leftArmGroup && this.rightArmGroup) {
                    const swing = Math.sin(t * Math.PI);
                    this.leftArmGroup.rotation.x = -Math.PI * 0.4 * swing;
                    this.rightArmGroup.rotation.x = -Math.PI * 0.4 * swing;
                }
                break;
        }
    }

    /**
     * Reset all body parts to their original positions and rotations
     * CRITICAL: Fixes torso offset issues by ensuring clean state
     */
    resetToOriginalPositions() {
        if (this.bodyGroup && this.originalPositions.body) {
            this.bodyGroup.position.copy(this.originalPositions.body);
            this.bodyGroup.rotation.set(0, 0, 0);
            this.bodyGroup.scale.set(1, 1, 1);
        }
        
        if (this.headGroup && this.originalPositions.head) {
            this.headGroup.position.copy(this.originalPositions.head);
            this.headGroup.rotation.set(0, 0, 0);
            this.headGroup.scale.set(1, 1, 1);
        }
        
        if (this.leftArmGroup && this.originalPositions.leftArm) {
            this.leftArmGroup.position.copy(this.originalPositions.leftArm);
            this.leftArmGroup.rotation.set(0, 0, 0);
            this.leftArmGroup.scale.set(1, 1, 1);
        }
        
        if (this.rightArmGroup && this.originalPositions.rightArm) {
            this.rightArmGroup.position.copy(this.originalPositions.rightArm);
            this.rightArmGroup.rotation.set(0, 0, 0);
            this.rightArmGroup.scale.set(1, 1, 1);
        }
        
        if (this.leftLegGroup && this.originalPositions.leftLeg) {
            this.leftLegGroup.position.copy(this.originalPositions.leftLeg);
            this.leftLegGroup.rotation.set(0, 0, 0);
            this.leftLegGroup.scale.set(1, 1, 1);
        }
        
        if (this.rightLegGroup && this.originalPositions.rightLeg) {
            this.rightLegGroup.position.copy(this.originalPositions.rightLeg);
            this.rightLegGroup.rotation.set(0, 0, 0);
            this.rightLegGroup.scale.set(1, 1, 1);
        }
        
        console.log('[NairabosAnimationHandler] ✅ All body parts reset to original positions');
    }
}
