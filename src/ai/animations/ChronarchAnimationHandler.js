import { AIStates } from '../AIStates.js';
import { animationIntegration } from '../../utils/AnimationIntegration.js';

/**
 * Animation handler for Chronarch Temporal Alchemist boss
 * Handles mystical floating poses, spellcasting movements, and alchemical effects
 */
export class ChronarchAnimationHandler {
    constructor(enemy) {
        this.enemy = enemy;
        // The enemy object IS the mesh in this case
        this.mesh = enemy.mesh || enemy;
        
        // Add geometry validation utility
        this.validateNumber = (value, fallback = 0) => {
            return (isFinite(value) && !isNaN(value)) ? value : fallback;
        };
        
        this.validateScale = (value, fallback = 1.0) => {
            return (isFinite(value) && !isNaN(value) && value > 0) ? value : fallback;
        };

        console.log(`[ChronarchAnimationHandler] Initializing for enemy:`, enemy.name);
        console.log(`[ChronarchAnimationHandler] Mesh object:`, this.mesh);

        // Get body parts - handle visual group structure
        const visualGroup = this.mesh.getObjectByName('visualModel');
        const searchRoot = visualGroup || this.mesh;

        this.bodyGroup = searchRoot.getObjectByName('body');
        this.headGroup = searchRoot.getObjectByName('head');
        this.leftArmGroup = searchRoot.getObjectByName('leftArm');
        this.rightArmGroup = searchRoot.getObjectByName('rightArm');
        this.leftLegGroup = searchRoot.getObjectByName('leftLeg');
        this.rightLegGroup = searchRoot.getObjectByName('rightLeg');
        
        // Chronarch-specific parts
        this.staffGroup = searchRoot.getObjectByName('staff');
        this.potionBeltGroup = searchRoot.getObjectByName('potionBelt');
        this.floatingPotionsGroup = searchRoot.getObjectByName('floatingPotions');

        console.log(`[ChronarchAnimationHandler] Visual group found:`, !!visualGroup);
        console.log(`[ChronarchAnimationHandler] Body parts found:`, {
            body: !!this.bodyGroup,
            head: !!this.headGroup,
            leftArm: !!this.leftArmGroup,
            rightArm: !!this.rightArmGroup,
            leftLeg: !!this.leftLegGroup,
            rightLeg: !!this.rightLegGroup,
            staff: !!this.staffGroup,
            potionBelt: !!this.potionBeltGroup,
            floatingPotions: !!this.floatingPotionsGroup
        });

        // Enhanced animation parameters (epic boss level sophistication)
        this.animationData = this.mesh.userData.animationData || {
            // Floating parameters (smoother and more majestic)
            shadowFloatSpeed: 1.5, // Slower for more regal movement
            shadowFloatAmplitude: 0.3, // Reduced for stability
            limbSwaySpeed: 1.0, // Gentler limb movement
            limbSwayAmplitude: Math.PI / 20, // Subtler sway
            
            // Attack parameters (more dramatic)  
            attackAnimationDuration: 1.8, // Longer for epic attacks
            spellcastDuration: 2.0, // Extended casting time
            windUpDuration: 1.5, // Wind-up time for major attacks
            
            // Mystical effects (enhanced)
            potionOrbitSpeed: 2.0, // Smoother potion movement
            potionOrbitRadius: 3.2, // Larger orbit for epic scale
            staffGlowSpeed: 2.5, // More stable glow
            shadowPulseSpeed: 2.0, // Slower pulse for stability
            shadowPulseAmplitude: 0.08, // Much smaller for smoothness
            
            // Movement parameters (enhanced epic scale)
            hoverHeight: 1.5, // Higher hover for imposing presence
            approachSpeed: 1.8, // Smoother approach
            robeWaveSpeed: 1.2, // Slower robe waves
            robeWaveAmplitude: 0.35, // Larger robe movement
            
            // New animation refinement parameters
            transitionSmoothing: 0.95, // Smooth transitions between states
            rotationDamping: 0.9, // Prevent rotation overshoot
            positionDamping: 0.85, // Smooth position changes
            scaleStability: 0.98 // Prevent scale flickering
        };

        // Animation state
        this.isAttacking = false;
        this.attackAnimationProgress = 0;
        this.isFloating = true;
        this.floatProgress = 0;
        this.currentAttackType = null;
        this.attackStartTime = 0;
        this.spellcastProgress = 0;
        this.potionOrbitTime = 0;

        // Store original positions for reference
        this.originalPositions = {};
        if (this.bodyGroup) this.originalPositions.body = this.bodyGroup.position.clone();
        if (this.headGroup) this.originalPositions.head = this.headGroup.position.clone();
        if (this.leftArmGroup) this.originalPositions.leftArm = this.leftArmGroup.position.clone();
        if (this.rightArmGroup) this.originalPositions.rightArm = this.rightArmGroup.position.clone();
        if (this.leftLegGroup) this.originalPositions.leftLeg = this.leftLegGroup.position.clone();
        if (this.rightLegGroup) this.originalPositions.rightLeg = this.rightLegGroup.position.clone();
        if (this.staffGroup) this.originalPositions.staff = this.staffGroup.position.clone();
        if (this.potionBeltGroup) this.originalPositions.potionBelt = this.potionBeltGroup.position.clone();
        if (this.floatingPotionsGroup) this.originalPositions.floatingPotions = this.floatingPotionsGroup.position.clone();
        
        console.log('ChronarchAnimationHandler initialized for mystical alchemist boss');

        // Store original update method for fallback
        this.originalUpdate = this._originalUpdate.bind(this);
    }

    /**
     * Update animations based on AI state
     */
    async update(deltaTime, aiState) {
        if (!this.mesh) {
            console.warn(`[ChronarchAnimationHandler] No mesh available for animation`);
            return;
        }

        // CRITICAL: Validate deltaTime to prevent NaN propagation
        const safeDeltaTime = (isFinite(deltaTime) && !isNaN(deltaTime) && deltaTime >= 0) ? deltaTime : 0.016;

        // Try worker-enhanced animation processing
        if (window.workerIntegration) {
            try {
                await animationIntegration.enhanceAnimationHandler(this, safeDeltaTime, aiState);
                return; // Successfully used worker
            } catch (error) {
                console.warn('[ChronarchAnimationHandler] Worker animation failed, using original method:', error);
                // Continue with original method below
            }
        }
        
        const globalTime = Date.now() * 0.001; // Convert to seconds

        // Update attack animation progress with NaN protection
        if (this.isAttacking) {
            const duration = Math.max(this.animationData.attackAnimationDuration || 1.0, 0.001);
            const progressIncrement = safeDeltaTime / duration;
            
            // Validate against NaN/Infinity
            if (isFinite(progressIncrement) && !isNaN(progressIncrement)) {
                this.attackAnimationProgress += progressIncrement;
            } else {
                console.warn('[ChronarchAnimationHandler] Invalid progress increment, using fallback');
                this.attackAnimationProgress += safeDeltaTime / 1.0; // Safe fallback
            }
            
            if (this.attackAnimationProgress >= 1.0) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
                this.currentAttackType = null;
                
                // Reset body position after attacks
                if (this.bodyGroup && this.originalPositions.body) {
                    this.bodyGroup.scale.set(1, 1, 1);
                    this.bodyGroup.position.copy(this.originalPositions.body);
                    this.bodyGroup.rotation.set(0, 0, 0);
                }
            }
            
            // Play the attack animation
            if (this.currentAttackType) {
                this.playAttackAnimation(this.currentAttackType, this.attackAnimationProgress);
                // CRITICAL FIX: NO effects during attacks to prevent twitching
                // Only apply floating potions (independent of body parts)
                this._updateFloatingPotions(globalTime);
                return; // Skip ALL other animations during attack to prevent conflicts
            }
        }

        // Normalize AI state
        const normalizedState = typeof aiState === 'string' ? aiState.toUpperCase() : aiState;

        // Calculate distance to player for behavior switching
        const playerPosition = this._getPlayerPosition();
        const distanceToPlayer = playerPosition ? this.mesh.position.distanceTo(playerPosition) : 999;

        // CRITICAL FIX: Skip ALL movement animations during attacks
        if (this.isAttacking && this.currentAttackType) {
            // During attacks, ONLY run floating potions (completely independent)
            this._updateFloatingPotions(globalTime);
            return; // Prevent ANY other animations from interfering
        }

        // Distance-based movement switching (like Nairabos)
        const isCloseToPlayer = distanceToPlayer <= 6.0; // Within 6 units = grounded mode
        const isMidRange = distanceToPlayer > 6.0 && distanceToPlayer <= 12.0; // Mid range = hovering  
        const isFarRange = distanceToPlayer > 12.0; // Far range = floating

        // Apply smooth state transitions with interpolation (only when not attacking)
        const targetAnimationType = this._determineTargetAnimation(normalizedState, isCloseToPlayer, isMidRange, isFarRange);
        this._smoothTransitionToAnimation(targetAnimationType, globalTime, safeDeltaTime);

        // Apply non-conflicting effects
        this._applyMysticalEffects(globalTime);
        this._updateFloatingPotions(globalTime);
        this._updateStaffEffects(globalTime);
        
        // Light stabilization only (removed aggressive stabilization)
        this._lightStabilizeAnimations();
    }

    /**
     * Enhanced smooth animation transition system
     */
    _determineTargetAnimation(state, isClose, isMid, isFar) {
        if (this.isAttacking) return 'attacking';
        
        switch (state) {
            case 'HOVERING':
            case 'IDLE':
            case AIStates.HOVERING:
            case AIStates.IDLE:
                if (isClose) return 'grounded';
                if (isMid) return 'hovering';
                return 'floating';
                
            case 'STRAFING':
            case 'MOVING':
            case 'CHASING':
            case 'APPROACHING':
            case AIStates.STRAFING:
            case AIStates.MOVING:
            case AIStates.CHASING:
            case AIStates.APPROACHING:
                return isClose ? 'grounded' : 'flying';
                
            case 'SWOOPING':
            case AIStates.SWOOPING:
                return 'swooping';
                
            case 'ASCENDING':
            case AIStates.ASCENDING:
                return 'flying';
                
            case 'ATTACKING':
            case AIStates.ATTACKING:
                return 'attacking';
                
            default:
                if (isClose) return 'grounded';
                if (isMid) return 'hovering';
                return 'floating';
        }
    }
    
    /**
     * Smooth transition system to prevent animation glitches
     */
    _smoothTransitionToAnimation(targetType, time, deltaTime) {
        // Store current animation type and smoothly transition
        if (!this.currentAnimationType) {
            this.currentAnimationType = targetType;
            this.animationBlendWeight = 1.0;
        }
        
        if (this.currentAnimationType !== targetType) {
            // Start blending to new animation
            this.previousAnimationType = this.currentAnimationType;
            this.currentAnimationType = targetType;
            this.animationBlendWeight = 0.0;
            this.isTransitioning = true;
        }
        
        // Update blend weight for smooth transitions
        if (this.isTransitioning) {
            const blendSpeed = 1.5; // Slower transition speed to prevent twitching
            this.animationBlendWeight = Math.min(1.0, this.animationBlendWeight + deltaTime * blendSpeed);
            
            if (this.animationBlendWeight >= 1.0) {
                this.isTransitioning = false;
                this.previousAnimationType = null;
            }
        }
        
        // Apply current animation
        this._applyAnimationType(this.currentAnimationType, time);
        
        // Blend with previous if transitioning
        if (this.isTransitioning && this.previousAnimationType) {
            this._blendAnimations(this.previousAnimationType, this.currentAnimationType, this.animationBlendWeight, time);
        }
    }
    
    /**
     * Apply specific animation type with enhanced smoothness
     */
    _applyAnimationType(animationType, time) {
        switch (animationType) {
            case 'floating':
                this._applyFloatingAnimation(time);
                break;
            case 'grounded':
                this._applyGroundedAnimation(time);
                break;
            case 'hovering':
                this._applyHoveringAnimation(time);
                break;
            case 'flying':
                this._applyFlyingAnimation(time);
                break;
            case 'swooping':
                this._applySwoopingAnimation(time);
                break;
            case 'attacking':
                this._applyAttackingAnimation(time);
                break;
            default:
                this._applyFloatingAnimation(time);
        }
    }
    
    /**
     * Apply distant floating animation - serene mystical hovering (far range)
     */
    _applyFloatingAnimation(time) {
        try {
            const floatSpeed = this.animationData.shadowFloatSpeed;
            const floatAmplitude = this.animationData.shadowFloatAmplitude;

            // Gentle floating motion for body with enhanced smoothness
            if (this.bodyGroup && this.originalPositions.body) {
                const bodyFloat = Math.sin(time * floatSpeed) * floatAmplitude;
                const targetY = this.originalPositions.body.y + bodyFloat;
                
                // Smooth position interpolation
                this.bodyGroup.position.y = this._smoothInterpolate(
                    this.bodyGroup.position.y, 
                    targetY, 
                    this.animationData.positionDamping
                );

                // Subtle mystical swaying with damping
                const targetRotZ = Math.sin(time * floatSpeed * 0.7) * 0.06; // Reduced amplitude
                const targetRotY = Math.sin(time * floatSpeed * 0.4) * 0.08; // Reduced amplitude
                
                this.bodyGroup.rotation.z = this._smoothInterpolate(
                    this.bodyGroup.rotation.z, 
                    targetRotZ, 
                    this.animationData.rotationDamping
                );
                this.bodyGroup.rotation.y = this._smoothInterpolate(
                    this.bodyGroup.rotation.y, 
                    targetRotY, 
                    this.animationData.rotationDamping
                );
            }

            // Head gentle meditation movement
            if (this.headGroup && this.originalPositions.head) {
                const headBob = Math.sin(time * floatSpeed * 1.3) * (floatAmplitude * 0.3);
                this.headGroup.position.y = this.originalPositions.head.y + headBob;
                this.headGroup.rotation.y = Math.sin(time * floatSpeed * 0.6) * 0.2;
                this.headGroup.rotation.x = Math.cos(time * floatSpeed * 0.5) * 0.05;
            }

            // Arms in mystical meditation pose
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = Math.sin(time * floatSpeed * 0.8) * 0.15 - 0.1;
                this.leftArmGroup.rotation.z = 0.4 + Math.sin(time * floatSpeed * 0.6) * 0.1;
                this.leftArmGroup.rotation.y = Math.cos(time * floatSpeed * 0.7) * 0.08;
            }
            if (this.rightArmGroup) {
                // Right arm holds staff with gentle mystical motion
                this.rightArmGroup.rotation.x = Math.sin(time * floatSpeed * 0.8 + Math.PI) * 0.12 - 0.2;
                this.rightArmGroup.rotation.z = -0.3 - Math.sin(time * floatSpeed * 0.6) * 0.08;
                this.rightArmGroup.rotation.y = -Math.cos(time * floatSpeed * 0.7) * 0.06;
            }

            // Staff gentle mystical floating
            if (this.staffGroup && this.originalPositions.staff) {
                this.staffGroup.rotation.z = Math.sin(time * floatSpeed * 0.5) * 0.15;
                this.staffGroup.rotation.y = Math.cos(time * floatSpeed * 0.3) * 0.1;
                this.staffGroup.position.y = this.originalPositions.staff.y + Math.sin(time * floatSpeed * 1.1) * (floatAmplitude * 0.4);
            }

            // Legs barely visible under mystical robes - gentle hovering
            if (this.leftLegGroup && this.originalPositions.leftLeg) {
                this.leftLegGroup.rotation.x = 0.2 + Math.sin(time * floatSpeed * 0.9) * 0.08;
                this.leftLegGroup.rotation.z = -0.05;
                this.leftLegGroup.position.y = this.originalPositions.leftLeg.y + Math.sin(time * floatSpeed * 1.2) * (floatAmplitude * 0.2);
            }
            if (this.rightLegGroup && this.originalPositions.rightLeg) {
                this.rightLegGroup.rotation.x = 0.2 + Math.sin(time * floatSpeed * 0.9 + Math.PI) * 0.08;
                this.rightLegGroup.rotation.z = 0.05;
                this.rightLegGroup.position.y = this.originalPositions.rightLeg.y + Math.sin(time * floatSpeed * 1.2 + Math.PI) * (floatAmplitude * 0.2);
            }
        } catch (error) {
            console.error(`[ChronarchAnimationHandler] Error in floating animation:`, error);
        }
    }

    /**
     * Apply grounded animation - mystical standing pose when close to player
     */
    _applyGroundedAnimation(time) {
        try {
            const walkSpeed = 2.5; // Mystical movement speed
            const walkAmplitude = Math.PI / 12; // Gentle robe sway

            console.log(`[ChronarchAnimationHandler] Applying grounded animation - time: ${time}`);

            // Body movement during grounded state - less floating, more mystical presence
            if (this.bodyGroup && this.originalPositions.body) {
                // Subtle mystical breathing
                const mysticalBob = Math.sin(time * walkSpeed * 0.8) * 0.15;
                this.bodyGroup.position.y = this.originalPositions.body.y + mysticalBob;

                // Mystical robe sway
                this.bodyGroup.rotation.x = -0.05; // Slight forward lean for casting
                this.bodyGroup.rotation.z = Math.sin(time * walkSpeed * 0.6) * 0.04;
                this.bodyGroup.rotation.y = Math.sin(time * walkSpeed * 0.3) * 0.08;
            }

            // Head tracking player when grounded
            if (this.headGroup && this.originalPositions.head) {
                this.headGroup.rotation.x = -0.1; // Slight downward gaze
                this.headGroup.rotation.y = Math.sin(time * walkSpeed * 0.7) * 0.2;
                this.headGroup.position.y = this.originalPositions.head.y + Math.sin(time * walkSpeed * 0.9) * 0.1;
            }

            // Arms in prepared spellcasting pose
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = Math.sin(time * walkSpeed * 0.8) * walkAmplitude * 0.3 - 0.2;
                this.leftArmGroup.rotation.z = 0.5 + Math.sin(time * walkSpeed * 0.5) * 0.1;
                this.leftArmGroup.rotation.y = Math.cos(time * walkSpeed * 0.6) * 0.05;
            }
            if (this.rightArmGroup) {
                // Right arm ready with staff
                this.rightArmGroup.rotation.x = Math.sin(time * walkSpeed * 0.8 + Math.PI) * walkAmplitude * 0.3 - 0.3;
                this.rightArmGroup.rotation.z = -0.4 - Math.sin(time * walkSpeed * 0.5) * 0.08;
                this.rightArmGroup.rotation.y = -Math.cos(time * walkSpeed * 0.6) * 0.04;
            }

            // Staff in ready position
            if (this.staffGroup && this.originalPositions.staff) {
                this.staffGroup.rotation.x = -0.2; // Angled for casting
                this.staffGroup.rotation.z = Math.sin(time * walkSpeed * 0.4) * 0.1;
                this.staffGroup.position.y = this.originalPositions.staff.y + Math.sin(time * walkSpeed * 0.7) * 0.2;
            }

            // Legs stable but with mystical energy
            if (this.leftLegGroup && this.originalPositions.leftLeg) {
                this.leftLegGroup.rotation.x = Math.sin(time * walkSpeed * 0.6) * walkAmplitude * 0.2;
                this.leftLegGroup.rotation.z = -0.02;
                this.leftLegGroup.position.y = this.originalPositions.leftLeg.y + Math.sin(time * walkSpeed * 0.8) * 0.1;
            }
            if (this.rightLegGroup && this.originalPositions.rightLeg) {
                this.rightLegGroup.rotation.x = Math.sin(time * walkSpeed * 0.6 + Math.PI) * walkAmplitude * 0.2;
                this.rightLegGroup.rotation.z = 0.02;
                this.rightLegGroup.position.y = this.originalPositions.rightLeg.y + Math.sin(time * walkSpeed * 0.8 + Math.PI) * 0.1;
            }
        } catch (error) {
            console.error(`[ChronarchAnimationHandler] Error in grounded animation:`, error);
        }
    }

    /**
     * Apply hovering animation - active mystical hovering for mid-range
     */
    _applyHoveringAnimation(time) {
        try {
            const hoverSpeed = this.animationData.shadowFloatSpeed * 1.3;
            const hoverAmplitude = this.animationData.shadowFloatAmplitude * 0.9;

            console.log(`[ChronarchAnimationHandler] Applying hovering animation - time: ${time}`);

            // More dynamic body movement during hovering
            if (this.bodyGroup && this.originalPositions.body) {
                const bodyFloat = Math.sin(time * hoverSpeed) * hoverAmplitude;
                this.bodyGroup.position.y = this.originalPositions.body.y + bodyFloat;

                // Active mystical preparation
                this.bodyGroup.rotation.x = -0.15; // More forward lean
                this.bodyGroup.rotation.z = Math.sin(time * hoverSpeed * 0.8) * 0.12;
                this.bodyGroup.rotation.y = Math.sin(time * hoverSpeed * 0.5) * 0.15;
            }

            // Head more alert and tracking
            if (this.headGroup && this.originalPositions.head) {
                const headBob = Math.sin(time * hoverSpeed * 1.2) * (hoverAmplitude * 0.4);
                this.headGroup.position.y = this.originalPositions.head.y + headBob;
                this.headGroup.rotation.y = Math.sin(time * hoverSpeed * 0.8) * 0.25;
                this.headGroup.rotation.x = -0.08; // Focused forward
            }

            // Arms more active and ready
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = Math.sin(time * hoverSpeed * 1.1) * 0.2 - 0.25;
                this.leftArmGroup.rotation.z = 0.6 + Math.sin(time * hoverSpeed * 0.7) * 0.15;
                this.leftArmGroup.rotation.y = Math.cos(time * hoverSpeed * 0.9) * 0.1;
            }
            if (this.rightArmGroup) {
                // Right arm more dynamic with staff
                this.rightArmGroup.rotation.x = Math.sin(time * hoverSpeed * 1.1 + Math.PI) * 0.18 - 0.35;
                this.rightArmGroup.rotation.z = -0.5 - Math.sin(time * hoverSpeed * 0.7) * 0.12;
                this.rightArmGroup.rotation.y = -Math.cos(time * hoverSpeed * 0.9) * 0.08;
            }

            // Staff more active mystical energy
            if (this.staffGroup && this.originalPositions.staff) {
                this.staffGroup.rotation.z = Math.sin(time * hoverSpeed * 0.6) * 0.2;
                this.staffGroup.rotation.y = Math.cos(time * hoverSpeed * 0.4) * 0.15;
                this.staffGroup.rotation.x = -0.1 + Math.sin(time * hoverSpeed * 0.8) * 0.1;
                this.staffGroup.position.y = this.originalPositions.staff.y + Math.sin(time * hoverSpeed * 1.3) * (hoverAmplitude * 0.5);
            }

            // Legs more dynamic hovering
            if (this.leftLegGroup && this.originalPositions.leftLeg) {
                this.leftLegGroup.rotation.x = 0.3 + Math.sin(time * hoverSpeed * 1.0) * 0.12;
                this.leftLegGroup.rotation.z = -0.08;
                this.leftLegGroup.position.y = this.originalPositions.leftLeg.y + Math.sin(time * hoverSpeed * 1.4) * (hoverAmplitude * 0.3);
            }
            if (this.rightLegGroup && this.originalPositions.rightLeg) {
                this.rightLegGroup.rotation.x = 0.3 + Math.sin(time * hoverSpeed * 1.0 + Math.PI) * 0.12;
                this.rightLegGroup.rotation.z = 0.08;
                this.rightLegGroup.position.y = this.originalPositions.rightLeg.y + Math.sin(time * hoverSpeed * 1.4 + Math.PI) * (hoverAmplitude * 0.3);
            }
        } catch (error) {
            console.error(`[ChronarchAnimationHandler] Error in hovering animation:`, error);
        }
    }

    /**
     * Apply flying animation - dynamic movement through air
     */
    _applyFlyingAnimation(time) {
        try {
            const flySpeed = this.animationData.shadowFloatSpeed * 1.8;
            const flyAmplitude = this.animationData.shadowFloatAmplitude * 1.1;

            console.log(`[ChronarchAnimationHandler] Applying flying animation - time: ${time}`);

            // Dynamic body movement during flight
            if (this.bodyGroup && this.originalPositions.body) {
                const bodyFloat = Math.sin(time * flySpeed) * flyAmplitude;
                this.bodyGroup.position.y = this.originalPositions.body.y + bodyFloat;

                // Aerodynamic lean during flight
                this.bodyGroup.rotation.x = -0.3;
                this.bodyGroup.rotation.z = Math.sin(time * flySpeed * 0.9) * 0.15;
                this.bodyGroup.rotation.y = Math.sin(time * flySpeed * 0.4) * 0.2;
            }

            // Head tracking forward during flight
            if (this.headGroup && this.originalPositions.head) {
                this.headGroup.rotation.x = -0.2;
                this.headGroup.rotation.y = Math.sin(time * flySpeed * 0.6) * 0.15;
                this.headGroup.position.y = this.originalPositions.head.y + Math.sin(time * flySpeed * 1.5) * (flyAmplitude * 0.4);
            }

            // Arms spread for mystical flight
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.x = -0.4 + Math.sin(time * flySpeed * 1.3) * 0.2;
                this.leftArmGroup.rotation.z = 0.9 + Math.sin(time * flySpeed * 0.8) * 0.15;
                this.leftArmGroup.rotation.y = -0.2 + Math.cos(time * flySpeed * 0.7) * 0.1;
            }
            if (this.rightArmGroup) {
                // Right arm extended with staff for flight control
                this.rightArmGroup.rotation.x = -0.5 + Math.sin(time * flySpeed * 1.3 + Math.PI) * 0.18;
                this.rightArmGroup.rotation.z = -0.8 - Math.sin(time * flySpeed * 0.8) * 0.12;
                this.rightArmGroup.rotation.y = 0.2 - Math.cos(time * flySpeed * 0.7) * 0.08;
            }

            // Staff extended for flight navigation
            if (this.staffGroup && this.originalPositions.staff) {
                this.staffGroup.rotation.x = -0.4 + Math.sin(time * flySpeed * 0.9) * 0.15;
                this.staffGroup.rotation.z = Math.sin(time * flySpeed * 0.5) * 0.2;
                this.staffGroup.rotation.y = Math.cos(time * flySpeed * 0.3) * 0.1;
                this.staffGroup.position.y = this.originalPositions.staff.y + Math.sin(time * flySpeed * 1.6) * (flyAmplitude * 0.6);
            }

            // Legs tucked for aerodynamic flight
            if (this.leftLegGroup && this.originalPositions.leftLeg) {
                this.leftLegGroup.rotation.x = -0.6 + Math.sin(time * flySpeed * 1.2) * 0.2;
                this.leftLegGroup.rotation.z = -0.15;
                this.leftLegGroup.rotation.y = -0.1;
                this.leftLegGroup.position.y = this.originalPositions.leftLeg.y + Math.sin(time * flySpeed * 1.7) * (flyAmplitude * 0.4);
            }
            if (this.rightLegGroup && this.originalPositions.rightLeg) {
                this.rightLegGroup.rotation.x = -0.6 + Math.sin(time * flySpeed * 1.2 + Math.PI) * 0.2;
                this.rightLegGroup.rotation.z = 0.15;
                this.rightLegGroup.rotation.y = 0.1;
                this.rightLegGroup.position.y = this.originalPositions.rightLeg.y + Math.sin(time * flySpeed * 1.7 + Math.PI) * (flyAmplitude * 0.4);
            }
        } catch (error) {
            console.error(`[ChronarchAnimationHandler] Error in flying animation:`, error);
        }
    }

    /**
     * Apply swooping animation - aggressive mystical dive
     */
    _applySwoopingAnimation(time) {
        // More aggressive version of flying animation
        this._applyFlyingAnimation(time);

        // Additional swooping effects
        if (this.bodyGroup) {
            this.bodyGroup.rotation.x = -0.5; // More aggressive lean
            this.bodyGroup.rotation.z = Math.sin(time * 10) * 0.2; // More dramatic sway
        }

        // Arms pulled back more aggressively for dive
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.x = -0.7;
            this.leftArmGroup.rotation.z = 1.3;
        }
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.x = -0.8;
            this.rightArmGroup.rotation.z = -1.2;
        }

        // Staff angled for dive attack
        if (this.staffGroup) {
            this.staffGroup.rotation.x = -0.8;
            this.staffGroup.rotation.z = Math.sin(time * 8) * 0.3;
        }

        // Legs tucked tight for aggressive dive
        if (this.leftLegGroup) {
            this.leftLegGroup.rotation.x = -1.0;
        }
        if (this.rightLegGroup) {
            this.rightLegGroup.rotation.x = -1.0;
        }
    }

    /**
     * Apply approaching animation - more aggressive floating
     */
    _applyApproachingAnimation(globalTime) {
        // More intense floating
        const floatTime = globalTime * (this.animationData.floatSpeed * 1.5);
        const floatOffset = Math.sin(floatTime) * (this.animationData.floatAmplitude * 1.2);

        if (this.bodyGroup) {
            this.bodyGroup.position.y = this.originalPositions.body.y + floatOffset;
            // Forward lean
            this.bodyGroup.rotation.x = -0.2;
            this.bodyGroup.rotation.y = Math.sin(globalTime * 1.2) * 0.15;
        }

        // Head tracking player
        if (this.headGroup) {
            this.headGroup.rotation.y = Math.sin(globalTime * 1.0) * 0.3;
            this.headGroup.position.y = this.originalPositions.head.y + floatOffset * 0.6;
        }

        // Arms preparing for combat
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.z = Math.sin(globalTime * 1.5) * 0.3 + 0.5;
            this.leftArmGroup.rotation.x = -0.3;
        }
        
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.z = -0.3;
            this.rightArmGroup.rotation.x = -0.2;
        }

        // Staff ready position
        if (this.staffGroup) {
            this.staffGroup.rotation.x = -0.3;
            this.staffGroup.position.y = this.originalPositions.staff.y + floatOffset * 0.4;
        }
    }

    /**
     * Apply attacking animation - intense spellcasting
     */
    _applyAttackingAnimation(globalTime) {
        const attackTime = globalTime * 3.0; // Fast animation during attacks
        
        if (this.bodyGroup) {
            // Dramatic pose during attack
            this.bodyGroup.rotation.x = -0.4;
            this.bodyGroup.scale.y = 1 + Math.sin(attackTime) * 0.1;
        }

        // Head focused on target
        if (this.headGroup) {
            this.headGroup.rotation.x = -0.2;
        }

        // Arms in powerful casting pose
        if (this.leftArmGroup) {
            this.leftArmGroup.rotation.z = 0.8;
            this.leftArmGroup.rotation.x = -0.5;
        }
        
        if (this.rightArmGroup) {
            this.rightArmGroup.rotation.z = -0.6;
            this.rightArmGroup.rotation.x = -0.4;
        }

        // Staff pointing forward
        if (this.staffGroup) {
            this.staffGroup.rotation.x = -0.6;
            this.staffGroup.rotation.z = Math.sin(attackTime) * 0.2;
        }
    }

    /**
     * Enhanced mystical effects with ultra-smooth animations and stability
     */
    _applyMysticalEffects(time) {
        // Enhanced mystical scaling pulse effect with maximum smoothness
        if (this.mesh) {
            // Validate input time to prevent NaN propagation
            const safeTime = isFinite(time) && !isNaN(time) ? time : 0;
            const safeSpeed = isFinite(this.animationData.shadowPulseSpeed) && !isNaN(this.animationData.shadowPulseSpeed) 
                ? this.animationData.shadowPulseSpeed : 2.0;
            
            // ULTRA-SMOOTH: Apply extremely subtle pulsing for stability
            const pulseInput = safeTime * safeSpeed;
            const rawPulse = Math.sin(pulseInput);
            const pulse = isFinite(rawPulse) && !isNaN(rawPulse) ? 1.0 + rawPulse * this.animationData.shadowPulseAmplitude : 1.0;
            
            const baseScale = this.mesh.userData.scale || 3.5; // Use stored scale or default
            const targetScale = baseScale * pulse;
            
            // Smooth scale interpolation to prevent sudden jumps
            const currentScale = this.mesh.scale.x;
            const smoothScale = this._smoothInterpolate(currentScale, targetScale, this.animationData.scaleStability);
            
            // Additional validation before applying scale
            if (isFinite(smoothScale) && !isNaN(smoothScale) && smoothScale > 0) {
                this.mesh.scale.set(smoothScale, smoothScale, smoothScale);
            } else {
                console.warn('[ChronarchAnimationHandler] Invalid scale detected, using safe default');
                this.mesh.scale.set(baseScale, baseScale, baseScale);
            }
        }

        // Enhanced staff crystal glow animation
        if (this.staffGroup) {
            const glowSpeed = this.animationData.staffGlowSpeed || 3.5;
            const glowIntensity = (Math.sin(time * glowSpeed) + 1) * 0.5;
            
            this.staffGroup.traverse(child => {
                if (child.isMesh && child.material && child.material.emissive) {
                    // Animate crystal glow with more intensity
                    const baseEmissive = 0.4;
                    const glowVariation = glowIntensity * 0.6;
                    child.material.emissiveIntensity = baseEmissive + glowVariation;
                    
                    // Pulsing crystal opacity
                    if (child.material.transparent) {
                        const baseOpacity = 0.8;
                        const opacityVariation = Math.sin(time * glowSpeed * 1.3) * 0.15;
                        child.material.opacity = Math.max(0.3, baseOpacity + opacityVariation);
                    }
                }
            });
        }

        // Mystical robe shimmer and energy
        if (this.bodyGroup) {
            const shimmerSpeed = 1.8;
            const shimmerIntensity = (Math.sin(time * shimmerSpeed) + 1) * 0.5;
            
            this.bodyGroup.traverse(child => {
                if (child.isMesh && child.material) {
                    // Robe mystical shimmer
                    if (child.material.emissive) {
                        const baseEmissive = 0.1;
                        const shimmerVariation = shimmerIntensity * 0.2;
                        child.material.emissiveIntensity = baseEmissive + shimmerVariation;
                    }
                    
                    // Mystical transparency effects
                    if (child.material.transparent && child.material.opacity) {
                        const pulseEffect = Math.sin(time * shimmerSpeed * 0.7) * 0.1;
                        child.material.opacity = Math.max(0.7, child.material.opacity + pulseEffect);
                    }
                }
            });
        }

        // Floating potions mystical energy
        if (this.floatingPotionsGroup) {
            const potionGlowSpeed = 2.5;
            
            this.floatingPotionsGroup.traverse(child => {
                if (child.isMesh && child.material && child.material.emissive) {
                    // Each potion pulses with different timing
                    const offset = child.position.x * 0.1; // Use position as offset for variety
                    const potionPulse = Math.sin(time * potionGlowSpeed + offset);
                    const glowIntensity = 0.5 + potionPulse * 0.4;
                    child.material.emissiveIntensity = Math.max(0.2, glowIntensity);
                    
                    // Potion transparency effects
                    if (child.material.transparent) {
                        const opacityPulse = Math.sin(time * potionGlowSpeed * 1.2 + offset) * 0.2;
                        child.material.opacity = Math.max(0.4, 0.7 + opacityPulse);
                    }
                }
            });
        }

        // CRITICAL: Runtime NaN detection in all child geometries (like Nairabos)
        this.mesh.traverse(child => {
            if (child.isMesh && child.geometry && child.geometry.attributes.position) {
                const positions = child.geometry.attributes.position.array;
                let hasNaN = false;
                
                // Check for NaN in position attributes (sample first 10 vertices)
                for (let i = 0; i < positions.length && i < 30; i += 3) {
                    if (!isFinite(positions[i]) || !isFinite(positions[i+1]) || !isFinite(positions[i+2]) ||
                        isNaN(positions[i]) || isNaN(positions[i+1]) || isNaN(positions[i+2])) {
                        console.error(`🚨 RUNTIME NaN DETECTED in Chronarch ${child.name || 'unnamed'} geometry UUID: ${child.geometry.uuid}`);
                        console.error(`NaN at vertex ${i/3}: (${positions[i]}, ${positions[i+1]}, ${positions[i+2]})`);
                        hasNaN = true;
                        break;
                    }
                }
                
                if (hasNaN) {
                    // Emergency fix: Reset geometry to prevent crash
                    console.error(`🔧 EMERGENCY: Resetting corrupted Chronarch geometry ${child.geometry.uuid}`);
                    child.visible = false; // Hide to prevent rendering crash
                }
            }
        });
    }

    /**
     * Update floating potions orbital animation
     */
    _updateFloatingPotions(globalTime) {
        if (!this.floatingPotionsGroup) return;

        this.potionOrbitTime = globalTime * this.animationData.potionOrbitSpeed;

        // Animate floating potions in orbital patterns
        this.floatingPotionsGroup.children.forEach((potion, index) => {
            if (potion.isMesh) {
                const offset = (index / this.floatingPotionsGroup.children.length) * Math.PI * 2;
                const orbitTime = this.potionOrbitTime + offset;
                
                // Orbital movement
                const radius = this.animationData.potionOrbitRadius + Math.sin(orbitTime * 0.5) * 0.5;
                potion.position.x = Math.cos(orbitTime) * radius;
                potion.position.z = Math.sin(orbitTime) * radius;
                potion.position.y = Math.sin(orbitTime * 2) * 0.8 + 1.0;
                
                // Rotation
                potion.rotation.y = orbitTime;
                potion.rotation.x = Math.sin(orbitTime * 1.5) * 0.3;
            }
        });
    }

    /**
     * Trigger a specific attack animation
     */
    triggerAttack(attackType) {
        this.isAttacking = true;
        this.attackAnimationProgress = 0;
        this.currentAttackType = attackType;
        this.attackStartTime = Date.now() * 0.001;
        
        console.log(`[ChronarchAnimationHandler] Triggering ${attackType} attack animation`);
    }
    
    /**
     * Play specific attack animations based on attack type (enhanced like Nairabos)
     */
    playAttackAnimation(attackType, progress) {
        const t = progress; // 0 to 1
        
        switch(attackType) {
            // === PHASE 1: THE UNRAVELING (100% - 60% Health) - Environmental Control ===
            case 'crystalline_eruption':
                this._animateCrystallineEruption(t);
                break;
                
            case 'shard_volley':
                this._animateShardVolley(t);
                break;
                
            case 'temporal_device_assault':
                this._animateTemporalDeviceAssault(t);
                break;
                
            case 'pillar_slam':
                this._animatePillarSlam(t);
                break;
                
            case 'crystal_barrier':
                this._animateCrystalBarrier(t);
                break;
                
            // === PHASE 2: BENDING TIME (60% - 25% Health) - Time Manipulation ===
            case 'time_dilation_field':
                this._animateTimeDilationField(t);
                break;
                
            case 'echoes_of_the_past':
                this._animateEchoesOfThePast(t);
                break;
                
            case 'chronostatic_prison':
                this._animateChronostaticPrison(t);
                break;
                
            case 'temporal_rift_pulse':
                this._animateTemporalRiftPulse(t);
                break;
                
            case 'reality_fracture':
                this._animateRealityFracture(t);
                break;
                
            // === PHASE 3: REALITY COLLAPSE (25% - 0% Health) - Ultimate Chaos ===
            case 'rift_collapse_ultimate':
                this._animateRiftCollapseUltimate(t);
                break;
                
            case 'platform_shard_volley':
                this._animatePlatformShardVolley(t);
                break;
                
            case 'crystal_cascade':
                this._animateCrystalCascade(t);
                break;
                
            case 'temporal_storm':
                this._animateTemporalStorm(t);
                break;
                
            case 'final_transmutation':
                this._animateFinalTransmutation(t);
                break;
                
            default:
                this._animateEnvironmentalCasting(t);
                break;
        }
    }

    /**
     * Animate potion barrage attack
     */
    _animatePotionBarrage(t) {
        // Arms throwing motion
        if (t < 0.5) {
            // Wind up
            const windUp = t * 2;
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.z = 1.5 * windUp;
                this.leftArmGroup.rotation.x = -0.8 * windUp;
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.z = -1.2 * windUp;
                this.rightArmGroup.rotation.x = -0.6 * windUp;
            }
        } else {
            // Throw
            const throwProgress = (t - 0.5) * 2;
            if (this.leftArmGroup) {
                this.leftArmGroup.rotation.z = 1.5 - throwProgress * 2.0;
                this.leftArmGroup.rotation.x = -0.8 + throwProgress * 0.4;
            }
            if (this.rightArmGroup) {
                this.rightArmGroup.rotation.z = -1.2 + throwProgress * 1.8;
                this.rightArmGroup.rotation.x = -0.6 + throwProgress * 0.2;
            }
        }

        // Body recoil
        if (this.bodyGroup) {
            this.bodyGroup.rotation.x = Math.sin(t * Math.PI) * -0.3;
        }
    }

    /**
     * Animate crystal prison attack
     */
    _animateCrystalPrison(t) {
        // Arms creating geometric patterns
        if (this.leftArmGroup && this.rightArmGroup) {
            const angle = t * Math.PI * 4; // Multiple rotations
            this.leftArmGroup.rotation.y = Math.sin(angle) * 0.8;
            this.rightArmGroup.rotation.y = Math.cos(angle) * 0.8;
            
            this.leftArmGroup.rotation.z = 0.5 + Math.cos(angle * 2) * 0.3;
            this.rightArmGroup.rotation.z = -0.5 - Math.cos(angle * 2) * 0.3;
        }

        // Staff creating containment circle
        if (this.staffGroup) {
            this.staffGroup.rotation.y = t * Math.PI * 2;
            this.staffGroup.position.y = this.originalPositions.staff.y + Math.sin(t * Math.PI) * 0.5;
        }
    }

    /**
     * Animate time warp attack
     */
    _animateTimeWarp(t) {
        // Slow, deliberate movements for time manipulation
        const slowTime = t * 0.5; // Slow animation
        
        if (this.bodyGroup) {
            this.bodyGroup.rotation.y = Math.sin(slowTime * Math.PI * 2) * 0.4;
            this.bodyGroup.scale.set(
                1 + Math.sin(slowTime * Math.PI) * 0.1,
                1 + Math.cos(slowTime * Math.PI) * 0.1,
                1 + Math.sin(slowTime * Math.PI) * 0.1
            );
        }

        // Arms in time manipulation pose
        if (this.leftArmGroup && this.rightArmGroup) {
            this.leftArmGroup.rotation.z = Math.sin(slowTime * Math.PI) * 0.6 + 0.3;
            this.rightArmGroup.rotation.z = -Math.sin(slowTime * Math.PI) * 0.6 - 0.3;
        }
    }

    /**
     * Animate alchemical explosion attack
     */
    _animateAlchemicalExplosion(t) {
        if (t < 0.3) {
            // Charge up - arms gathering energy
            const charge = t / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                this.leftArmGroup.rotation.z = charge * 1.2;
                this.rightArmGroup.rotation.z = -charge * 1.2;
                this.leftArmGroup.rotation.x = -charge * 0.8;
                this.rightArmGroup.rotation.x = -charge * 0.8;
            }
            if (this.bodyGroup) {
                this.bodyGroup.scale.set(1 + charge * 0.2, 1, 1 + charge * 0.2);
            }
        } else {
            // Explosive release
            const explosion = (t - 0.3) / 0.7;
            if (this.leftArmGroup && this.rightArmGroup) {
                this.leftArmGroup.rotation.z = 1.2 - explosion * 1.8;
                this.rightArmGroup.rotation.z = -1.2 + explosion * 1.8;
            }
            if (this.bodyGroup) {
                this.bodyGroup.scale.set(1.2 - explosion * 0.2, 1, 1.2 - explosion * 0.2);
                this.bodyGroup.rotation.x = -explosion * 0.5;
            }
        }
    }

    /**
     * Animate reagent shield attack
     */
    _animateReagentShield(t) {
        // Defensive circular motion
        const shieldAngle = t * Math.PI * 3; // Multiple rotations for shield
        
        if (this.leftArmGroup && this.rightArmGroup) {
            this.leftArmGroup.rotation.y = Math.sin(shieldAngle) * 0.5;
            this.rightArmGroup.rotation.y = Math.cos(shieldAngle) * 0.5;
            
            this.leftArmGroup.rotation.z = 0.8;
            this.rightArmGroup.rotation.z = -0.8;
        }

        // Staff creating protective barrier
        if (this.staffGroup) {
            this.staffGroup.rotation.y = shieldAngle;
            this.staffGroup.rotation.x = Math.sin(t * Math.PI) * 0.3;
        }

        // Body stable but alert
        if (this.bodyGroup) {
            this.bodyGroup.rotation.y = Math.sin(t * Math.PI * 2) * 0.1;
        }
    }

    /**
     * Default mystical casting animation
     */
    _animateMysticalCast(t) {
        // Generic spellcasting animation
        if (this.leftArmGroup && this.rightArmGroup) {
            this.leftArmGroup.rotation.z = Math.sin(t * Math.PI) * 0.8 + 0.3;
            this.rightArmGroup.rotation.z = -Math.sin(t * Math.PI) * 0.6 - 0.2;
            
            this.leftArmGroup.rotation.x = -0.3;
            this.rightArmGroup.rotation.x = -0.2;
        }

        if (this.staffGroup) {
            this.staffGroup.rotation.x = -0.4;
            this.staffGroup.rotation.z = Math.sin(t * Math.PI * 2) * 0.2;
        }

        if (this.bodyGroup) {
            this.bodyGroup.rotation.x = -0.2;
            this.bodyGroup.scale.y = 1 + Math.sin(t * Math.PI) * 0.1;
        }
    }

    /**
     * Get player position for targeting
     */
    _getPlayerPosition() {
        // Try to get player position from various sources
        if (typeof window !== 'undefined' && window.sceneManager) {
            const dungeonHandler = window.sceneManager.currentHandler || window.sceneManager.activeSceneHandler;
            if (dungeonHandler && dungeonHandler.player) {
                return dungeonHandler.player.position;
            }
        }
        return null;
    }

    // === EPIC ENVIRONMENTAL ATTACK ANIMATIONS ===
    // 🌋 PHASE 1: THE UNRAVELING (100% - 60% Health) - Environmental Control

    /**
     * ⚡ Crystalline Eruption - Massive time crystals erupt from floor in formations
     */
    _animateCrystallineEruption(t) {
        if (t < 0.4) {
            // Summoning phase - arms gathering temporal energy
            const summonProgress = t / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms raised high, channeling energy downward
                this.leftArmGroup.rotation.z = summonProgress * 1.8; // Higher raise
                this.leftArmGroup.rotation.x = -summonProgress * 0.8;
                this.rightArmGroup.rotation.z = -summonProgress * 1.8;
                this.rightArmGroup.rotation.x = -summonProgress * 0.8;
                // Fingers spread to channel energy
                this.leftArmGroup.rotation.y = -summonProgress * 0.3;
                this.rightArmGroup.rotation.y = summonProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Lean forward for downward channeling
                this.bodyGroup.rotation.x = summonProgress * 0.3;
                this.bodyGroup.scale.set(1 + summonProgress * 0.15, 1, 1 + summonProgress * 0.15);
            }
            if (this.headGroup) {
                // Look down at eruption points
                this.headGroup.rotation.x = summonProgress * 0.4;
            }
        } else if (t < 0.7) {
            // Eruption phase - violent ground breaking
            const eruptProgress = (t - 0.4) / 0.3;
            const shakeIntensity = eruptProgress * 0.2;
            if (this.bodyGroup) {
                // Violent shaking as crystals break through
                this.bodyGroup.position.x = (Math.random() - 0.5) * shakeIntensity;
                this.bodyGroup.position.z = (Math.random() - 0.5) * shakeIntensity;
                this.bodyGroup.rotation.y = Math.sin(eruptProgress * Math.PI * 8) * 0.1;
            }
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms thrust down with force
                this.leftArmGroup.rotation.z = 1.8 - eruptProgress * 0.5;
                this.rightArmGroup.rotation.z = -1.8 + eruptProgress * 0.5;
                this.leftArmGroup.rotation.x = -0.8 + eruptProgress * 0.4;
                this.rightArmGroup.rotation.x = -0.8 + eruptProgress * 0.4;
            }
        } else {
            // Completion phase - arms spread in triumph
            const completeProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spread wide in victory
                this.leftArmGroup.rotation.z = 1.3 + completeProgress * 0.8;
                this.rightArmGroup.rotation.z = -1.3 - completeProgress * 0.8;
                this.leftArmGroup.rotation.x = -0.4 - completeProgress * 0.2;
                this.rightArmGroup.rotation.x = -0.4 - completeProgress * 0.2;
            }
            if (this.bodyGroup) {
                // Return to normal with satisfaction
                this.bodyGroup.rotation.x = 0.3 - completeProgress * 0.3;
                this.bodyGroup.scale.set(1.15 - completeProgress * 0.15, 1, 1.15 - completeProgress * 0.15);
            }
            if (this.headGroup) {
                // Look up in triumph
                this.headGroup.rotation.x = 0.4 - completeProgress * 0.6;
            }
        }
    }

    /**
     * 💎 Shard Volley - Summons room object, shatters it into large projectile shards
     */
    _animateShardVolley(t) {
        if (t < 0.3) {
            // Object summoning phase - reaching out to grab room object
            const summonProgress = t / 0.3;
            if (this.leftArmGroup) {
                // Left arm reaches out to summon object
                this.leftArmGroup.rotation.z = summonProgress * 0.8;
                this.leftArmGroup.rotation.x = -summonProgress * 0.5;
                this.leftArmGroup.rotation.y = -summonProgress * 0.8; // Reach across body
            }
            if (this.rightArmGroup) {
                // Right arm assists in summoning
                this.rightArmGroup.rotation.z = -summonProgress * 0.5;
                this.rightArmGroup.rotation.x = -summonProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Turn toward summoned object
                this.bodyGroup.rotation.y = -summonProgress * 0.4;
            }
        } else if (t < 0.6) {
            // Shattering phase - crushing the object with telekinesis
            const shatterProgress = (t - 0.3) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Both arms clench as object shatters
                this.leftArmGroup.rotation.z = 0.8 + shatterProgress * 0.4;
                this.rightArmGroup.rotation.z = -0.5 - shatterProgress * 0.3;
                // Rapid clenching motion
                const clenchMotion = Math.sin(shatterProgress * Math.PI * 6) * 0.2;
                this.leftArmGroup.rotation.x = -0.5 + clenchMotion;
                this.rightArmGroup.rotation.x = -0.3 + clenchMotion;
            }
            if (this.bodyGroup) {
                // Strain from effort
                this.bodyGroup.scale.set(1 + shatterProgress * 0.1, 1, 1 + shatterProgress * 0.1);
            }
        } else {
            // Launch phase - hurling shards at target
            const launchProgress = (t - 0.6) / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Both arms thrust forward to launch shards
                this.leftArmGroup.rotation.z = 1.2 - launchProgress * 1.5;
                this.rightArmGroup.rotation.z = -0.8 + launchProgress * 1.3;
                this.leftArmGroup.rotation.x = -0.3 + launchProgress * 0.2;
                this.rightArmGroup.rotation.x = -0.1 + launchProgress * 0.1;
                this.leftArmGroup.rotation.y = -0.8 + launchProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Follow through with body
                this.bodyGroup.rotation.y = -0.4 + launchProgress * 0.6;
                this.bodyGroup.rotation.x = launchProgress * 0.2;
                this.bodyGroup.scale.set(1.1 - launchProgress * 0.1, 1, 1.1 - launchProgress * 0.1);
            }
        }
    }

    /**
     * 🔧 Temporal Device Assault - Hijacks room devices to attack player
     */
    _animateTemporalDeviceAssault(t) {
        if (t < 0.4) {
            // Device hijacking phase - reaching out with temporal energy
            const hijackProgress = t / 0.4;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff points at devices to hijack them
                this.rightArmGroup.rotation.z = -hijackProgress * 1.2;
                this.rightArmGroup.rotation.x = -hijackProgress * 0.6;
                this.rightArmGroup.rotation.y = hijackProgress * 0.5;
                this.staffGroup.rotation.x = -hijackProgress * 0.4;
                this.staffGroup.rotation.y = hijackProgress * 0.3;
            }
            if (this.leftArmGroup) {
                // Left arm gestures to control devices
                this.leftArmGroup.rotation.z = hijackProgress * 0.9;
                this.leftArmGroup.rotation.x = -hijackProgress * 0.4;
                this.leftArmGroup.rotation.y = -hijackProgress * 0.6;
            }
            if (this.bodyGroup) {
                // Turn to face devices
                this.bodyGroup.rotation.y = hijackProgress * 0.3;
            }
        } else if (t < 0.7) {
            // Control phase - manipulating hijacked devices
            const controlProgress = (t - 0.4) / 0.3;
            const controlMotion = Math.sin(controlProgress * Math.PI * 4) * 0.3;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff makes controlling gestures
                this.rightArmGroup.rotation.z = -1.2 + controlMotion;
                this.staffGroup.rotation.y = 0.3 + controlMotion;
                this.staffGroup.rotation.z = Math.sin(controlProgress * Math.PI * 6) * 0.2;
            }
            if (this.leftArmGroup) {
                // Left arm conducts device movements
                this.leftArmGroup.rotation.z = 0.9 + Math.cos(controlProgress * Math.PI * 3) * 0.3;
                this.leftArmGroup.rotation.x = -0.4 + controlMotion * 0.5;
            }
            if (this.bodyGroup) {
                // Subtle swaying while controlling
                this.bodyGroup.rotation.y = 0.3 + Math.sin(controlProgress * Math.PI * 2) * 0.1;
            }
        } else {
            // Attack phase - devices launch their attacks
            const attackProgress = (t - 0.7) / 0.3;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff points aggressively at target
                this.rightArmGroup.rotation.z = -1.2 + attackProgress * 0.5;
                this.rightArmGroup.rotation.x = -0.6 + attackProgress * 0.4;
                this.staffGroup.rotation.x = -0.4 + attackProgress * 0.6;
            }
            if (this.leftArmGroup) {
                // Left arm commands final attack
                this.leftArmGroup.rotation.z = 0.9 - attackProgress * 0.4;
                this.leftArmGroup.rotation.x = -0.4 + attackProgress * 0.2;
                this.leftArmGroup.rotation.y = -0.6 + attackProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Lean into the attack
                this.bodyGroup.rotation.x = attackProgress * 0.2;
                this.bodyGroup.rotation.y = 0.3 - attackProgress * 0.1;
            }
        }
    }

    /**
     * 🗿 Pillar Slam - Levitates and slams worn pillars at player location
     */
    _animatePillarSlam(t) {
        if (t < 0.5) {
            // Pillar lifting phase - telekinetic raising of heavy stone
            const liftProgress = t / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Both arms slowly raise with great effort
                this.leftArmGroup.rotation.z = liftProgress * 1.6;
                this.rightArmGroup.rotation.z = -liftProgress * 1.6;
                this.leftArmGroup.rotation.x = -liftProgress * 0.6;
                this.rightArmGroup.rotation.x = -liftProgress * 0.6;
                // Strain from lifting heavy object
                this.leftArmGroup.rotation.y = -Math.sin(liftProgress * Math.PI * 3) * 0.1;
                this.rightArmGroup.rotation.y = Math.sin(liftProgress * Math.PI * 3) * 0.1;
            }
            if (this.bodyGroup) {
                // Lean back from effort
                this.bodyGroup.rotation.x = -liftProgress * 0.3;
                this.bodyGroup.scale.set(1 + liftProgress * 0.2, 1, 1 + liftProgress * 0.2);
            }
            if (this.headGroup) {
                // Focus intensely on the pillar
                this.headGroup.rotation.x = liftProgress * 0.2;
            }
        } else {
            // Slam phase - violent downward strike
            const slamProgress = (t - 0.5) / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms violently thrust down
                this.leftArmGroup.rotation.z = 1.6 - slamProgress * 2.0;
                this.rightArmGroup.rotation.z = -1.6 + slamProgress * 2.0;
                this.leftArmGroup.rotation.x = -0.6 + slamProgress * 1.0;
                this.rightArmGroup.rotation.x = -0.6 + slamProgress * 1.0;
                this.leftArmGroup.rotation.y = -0.1 + slamProgress * 0.1;
                this.rightArmGroup.rotation.y = 0.1 - slamProgress * 0.1;
            }
            if (this.bodyGroup) {
                // Follow through with violent motion
                this.bodyGroup.rotation.x = -0.3 + slamProgress * 0.8;
                this.bodyGroup.scale.set(1.2 - slamProgress * 0.2, 1, 1.2 - slamProgress * 0.2);
                // Shake from impact
                if (slamProgress > 0.7) {
                    const shakeIntensity = (slamProgress - 0.7) / 0.3 * 0.15;
                    this.bodyGroup.position.x = (Math.random() - 0.5) * shakeIntensity;
                    this.bodyGroup.position.z = (Math.random() - 0.5) * shakeIntensity;
                }
            }
            if (this.headGroup) {
                // Look down at impact
                this.headGroup.rotation.x = 0.2 + slamProgress * 0.3;
            }
        }
    }

    /**
     * 🛡️ Crystal Barrier - Creates defensive crystal walls that reshape arena
     */
    _animateCrystalBarrier(t) {
        if (t < 0.4) {
            // Barrier creation phase - wide protective gestures
            const createProgress = t / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spread wide to create protective barrier
                this.leftArmGroup.rotation.z = createProgress * 1.4;
                this.rightArmGroup.rotation.z = -createProgress * 1.4;
                this.leftArmGroup.rotation.y = -createProgress * 0.8;
                this.rightArmGroup.rotation.y = createProgress * 0.8;
                this.leftArmGroup.rotation.x = -createProgress * 0.3;
                this.rightArmGroup.rotation.x = -createProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Expand chest for protective stance
                this.bodyGroup.scale.set(1 + createProgress * 0.15, 1, 1 + createProgress * 0.15);
            }
        } else if (t < 0.7) {
            // Shaping phase - molding the crystal walls
            const shapeProgress = (t - 0.4) / 0.3;
            const moldingMotion = Math.sin(shapeProgress * Math.PI * 4) * 0.2;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms move in sculpting motions
                this.leftArmGroup.rotation.z = 1.4 + moldingMotion;
                this.rightArmGroup.rotation.z = -1.4 - moldingMotion;
                this.leftArmGroup.rotation.y = -0.8 + Math.cos(shapeProgress * Math.PI * 3) * 0.2;
                this.rightArmGroup.rotation.y = 0.8 - Math.cos(shapeProgress * Math.PI * 3) * 0.2;
            }
            if (this.bodyGroup) {
                // Gentle swaying while shaping
                this.bodyGroup.rotation.y = Math.sin(shapeProgress * Math.PI * 2) * 0.1;
            }
        } else {
            // Solidification phase - final barrier strengthening
            const solidProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms clench to solidify barriers
                this.leftArmGroup.rotation.z = 1.4 - solidProgress * 0.6;
                this.rightArmGroup.rotation.z = -1.4 + solidProgress * 0.6;
                this.leftArmGroup.rotation.x = -0.3 - solidProgress * 0.2;
                this.rightArmGroup.rotation.x = -0.3 - solidProgress * 0.2;
                this.leftArmGroup.rotation.y = -0.8 + solidProgress * 0.4;
                this.rightArmGroup.rotation.y = 0.8 - solidProgress * 0.4;
            }
            if (this.bodyGroup) {
                // Return to strong stance
                this.bodyGroup.scale.set(1.15 - solidProgress * 0.15, 1, 1.15 - solidProgress * 0.15);
                this.bodyGroup.rotation.y = 0.1 - solidProgress * 0.1;
            }
        }
    }

    // ⏰ PHASE 2: BENDING TIME (60% - 25% Health) - Time Manipulation

    /**
     * ⏰ Time Dilation Field - Creates large sphere that slows everything inside
     */
    _animateTimeDilationField(t) {
        if (t < 0.5) {
            // Field generation phase - gathering temporal energy
            const gatherProgress = t / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms draw energy from surroundings
                this.leftArmGroup.rotation.z = gatherProgress * 0.8;
                this.rightArmGroup.rotation.z = -gatherProgress * 0.8;
                this.leftArmGroup.rotation.x = -gatherProgress * 0.5;
                this.rightArmGroup.rotation.x = -gatherProgress * 0.5;
                // Circular energy gathering motion
                const gatherMotion = gatherProgress * Math.PI * 2;
                this.leftArmGroup.rotation.y = -Math.sin(gatherMotion) * 0.3;
                this.rightArmGroup.rotation.y = Math.sin(gatherMotion) * 0.3;
            }
            if (this.bodyGroup) {
                // Concentrate energy in body
                this.bodyGroup.scale.set(1 + gatherProgress * 0.1, 1, 1 + gatherProgress * 0.1);
            }
            if (this.staffGroup) {
                // Staff glows with temporal energy
                this.staffGroup.rotation.y = gatherProgress * Math.PI * 2;
                this.staffGroup.rotation.z = Math.sin(gatherProgress * Math.PI * 3) * 0.1;
            }
        } else {
            // Field deployment phase - releasing time distortion
            const deployProgress = (t - 0.5) / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spread to release field
                this.leftArmGroup.rotation.z = 0.8 + deployProgress * 0.6;
                this.rightArmGroup.rotation.z = -0.8 - deployProgress * 0.6;
                this.leftArmGroup.rotation.x = -0.5 - deployProgress * 0.3;
                this.rightArmGroup.rotation.x = -0.5 - deployProgress * 0.3;
                this.leftArmGroup.rotation.y = -0.3 - deployProgress * 0.5;
                this.rightArmGroup.rotation.y = 0.3 + deployProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Lean back as field expands
                this.bodyGroup.rotation.x = -deployProgress * 0.2;
                this.bodyGroup.scale.set(1.1 - deployProgress * 0.1, 1, 1.1 - deployProgress * 0.1);
            }
            if (this.staffGroup) {
                // Staff points at field center
                this.staffGroup.rotation.x = -deployProgress * 0.4;
                this.staffGroup.rotation.y = Math.PI * 2 - deployProgress * Math.PI;
            }
        }
    }

    /**
     * 👻 Echoes of the Past - Summons ghostly temporal echoes that repeat attacks  
     */
    _animateEchoesOfThePast(t) {
        if (t < 0.6) {
            // Echo summoning phase - calling forth temporal duplicates
            const summonProgress = t / 0.6;
            const echoRipple = Math.sin(summonProgress * Math.PI * 6) * 0.2;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms create rippling temporal gestures
                this.leftArmGroup.rotation.z = summonProgress * 1.0 + echoRipple;
                this.rightArmGroup.rotation.z = -summonProgress * 1.0 - echoRipple;
                this.leftArmGroup.rotation.x = -summonProgress * 0.4 + echoRipple * 0.5;
                this.rightArmGroup.rotation.x = -summonProgress * 0.4 + echoRipple * 0.5;
                // Alternating gestures for echo creation
                this.leftArmGroup.rotation.y = -Math.sin(summonProgress * Math.PI * 4) * 0.4;
                this.rightArmGroup.rotation.y = Math.sin(summonProgress * Math.PI * 4) * 0.4;
            }
            if (this.bodyGroup) {
                // Body ripples with temporal distortion
                this.bodyGroup.rotation.y = Math.sin(summonProgress * Math.PI * 5) * 0.15;
                this.bodyGroup.scale.set(
                    1 + echoRipple * 0.1,
                    1,
                    1 + echoRipple * 0.1
                );
            }
            if (this.headGroup) {
                // Head turns to observe echoes
                this.headGroup.rotation.y = Math.sin(summonProgress * Math.PI * 3) * 0.3;
            }
        } else {
            // Echo command phase - directing temporal duplicates
            const commandProgress = (t - 0.6) / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms point to direct echoes
                this.leftArmGroup.rotation.z = 1.0 - commandProgress * 0.3;
                this.rightArmGroup.rotation.z = -1.0 + commandProgress * 0.3;
                this.leftArmGroup.rotation.x = -0.4 + commandProgress * 0.2;
                this.rightArmGroup.rotation.x = -0.4 + commandProgress * 0.2;
                this.leftArmGroup.rotation.y = -0.4 + commandProgress * 0.6;
                this.rightArmGroup.rotation.y = 0.4 - commandProgress * 0.6;
            }
            if (this.bodyGroup) {
                // Turn to oversee echo attacks
                this.bodyGroup.rotation.y = 0.15 - commandProgress * 0.3;
                this.bodyGroup.scale.set(1, 1, 1); // Return to normal
            }
            if (this.headGroup) {
                // Focus on echo coordination
                this.headGroup.rotation.y = 0.3 - commandProgress * 0.5;
                this.headGroup.rotation.x = commandProgress * 0.2;
            }
        }
    }

    /**
     * ❄️ Chronostatic Prison - Freezes floating objects then releases as weapons
     */
    _animateChronostaticPrison(t) {
        if (t < 0.4) {
            // Object freezing phase - temporal stasis gesture
            const freezeProgress = t / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms slowly extend to freeze objects in time
                this.leftArmGroup.rotation.z = freezeProgress * 1.0;
                this.rightArmGroup.rotation.z = -freezeProgress * 1.0;
                this.leftArmGroup.rotation.x = -freezeProgress * 0.5;
                this.rightArmGroup.rotation.x = -freezeProgress * 0.5;
                // Precise pointing to freeze specific objects
                this.leftArmGroup.rotation.y = -freezeProgress * 0.6;
                this.rightArmGroup.rotation.y = freezeProgress * 0.6;
            }
            if (this.bodyGroup) {
                // Concentrate for precise temporal control
                this.bodyGroup.scale.set(1 + freezeProgress * 0.08, 1, 1 + freezeProgress * 0.08);
            }
            if (this.headGroup) {
                // Focus on objects being frozen
                this.headGroup.rotation.x = freezeProgress * 0.3;
            }
        } else if (t < 0.7) {
            // Suspended animation phase - objects frozen in time
            const suspendProgress = (t - 0.4) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms hold steady, maintaining temporal lock
                this.leftArmGroup.rotation.z = 1.0;
                this.rightArmGroup.rotation.z = -1.0;
                this.leftArmGroup.rotation.x = -0.5;
                this.rightArmGroup.rotation.x = -0.5;
                this.leftArmGroup.rotation.y = -0.6;
                this.rightArmGroup.rotation.y = 0.6;
                // Slight tremor from maintaining stasis
                const tremor = Math.sin(suspendProgress * Math.PI * 8) * 0.05;
                this.leftArmGroup.rotation.z += tremor;
                this.rightArmGroup.rotation.z -= tremor;
            }
            if (this.bodyGroup) {
                // Strain from holding temporal lock
                this.bodyGroup.scale.set(1.08 + Math.sin(suspendProgress * Math.PI * 6) * 0.02, 1, 1.08 + Math.sin(suspendProgress * Math.PI * 6) * 0.02);
            }
        } else {
            // Release phase - launching frozen objects as weapons
            const releaseProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms thrust forward to launch objects
                this.leftArmGroup.rotation.z = 1.0 - releaseProgress * 1.3;
                this.rightArmGroup.rotation.z = -1.0 + releaseProgress * 1.3;
                this.leftArmGroup.rotation.x = -0.5 + releaseProgress * 0.7;
                this.rightArmGroup.rotation.x = -0.5 + releaseProgress * 0.7;
                this.leftArmGroup.rotation.y = -0.6 + releaseProgress * 0.4;
                this.rightArmGroup.rotation.y = 0.6 - releaseProgress * 0.4;
            }
            if (this.bodyGroup) {
                // Follow through with release
                this.bodyGroup.rotation.x = releaseProgress * 0.2;
                this.bodyGroup.scale.set(1.08 - releaseProgress * 0.08, 1, 1.08 - releaseProgress * 0.08);
            }
            if (this.headGroup) {
                // Track projectiles
                this.headGroup.rotation.x = 0.3 - releaseProgress * 0.1;
            }
        }
    }

    /**
     * 🌊 Temporal Rift Pulse - Central rift sends out expanding energy waves
     */
    _animateTemporalRiftPulse(t) {
        if (t < 0.3) {
            // Rift connection phase - linking to central temporal rift
            const connectProgress = t / 0.3;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff points directly at rift
                this.rightArmGroup.rotation.z = -connectProgress * 0.8;
                this.rightArmGroup.rotation.x = -connectProgress * 0.6;
                this.staffGroup.rotation.x = -connectProgress * 0.5;
                this.staffGroup.rotation.y = connectProgress * 0.3;
            }
            if (this.leftArmGroup) {
                // Left arm channels energy to rift
                this.leftArmGroup.rotation.z = connectProgress * 0.6;
                this.leftArmGroup.rotation.x = -connectProgress * 0.4;
                this.leftArmGroup.rotation.y = -connectProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Lean toward rift
                this.bodyGroup.rotation.x = connectProgress * 0.2;
            }
        } else if (t < 0.6) {
            // Energy buildup phase - power accumulating in rift
            const buildupProgress = (t - 0.3) / 0.3;
            const energyPulse = Math.sin(buildupProgress * Math.PI * 6) * 0.3;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff trembles with energy buildup
                this.rightArmGroup.rotation.z = -0.8 + energyPulse * 0.1;
                this.staffGroup.rotation.y = 0.3 + energyPulse * 0.2;
                this.staffGroup.rotation.z = energyPulse * 0.15;
            }
            if (this.leftArmGroup) {
                // Left arm feeds more energy
                this.leftArmGroup.rotation.z = 0.6 + buildupProgress * 0.4;
                this.leftArmGroup.rotation.x = -0.4 - buildupProgress * 0.2;
            }
            if (this.bodyGroup) {
                // Body resonates with rift energy
                this.bodyGroup.scale.set(
                    1 + energyPulse * 0.05,
                    1,
                    1 + energyPulse * 0.05
                );
            }
        } else {
            // Pulse release phase - rift explodes with energy waves
            const pulseProgress = (t - 0.6) / 0.4;
            if (this.rightArmGroup && this.staffGroup) {
                // Staff and arm recoil from massive energy release
                this.rightArmGroup.rotation.z = -0.8 + pulseProgress * 1.2;
                this.rightArmGroup.rotation.x = -0.6 + pulseProgress * 0.8;
                this.staffGroup.rotation.x = -0.5 + pulseProgress * 0.7;
                this.staffGroup.rotation.y = 0.3 - pulseProgress * 0.5;
            }
            if (this.leftArmGroup) {
                // Left arm thrown back by recoil
                this.leftArmGroup.rotation.z = 1.0 + pulseProgress * 0.5;
                this.leftArmGroup.rotation.x = -0.6 + pulseProgress * 0.4;
                this.leftArmGroup.rotation.y = -0.3 - pulseProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Body rocks back from energy release
                this.bodyGroup.rotation.x = 0.2 - pulseProgress * 0.4;
                this.bodyGroup.scale.set(1, 1, 1); // Return to normal
            }
        }
    }

    /**
     * ⚡ Reality Fracture - Cracks appear in space, creating hazardous zones
     */
    _animateRealityFracture(t) {
        if (t < 0.5) {
            // Reality tearing phase - ripping holes in space-time
            const tearProgress = t / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms tear reality apart with violent motions
                this.leftArmGroup.rotation.z = tearProgress * 1.2;
                this.rightArmGroup.rotation.z = -tearProgress * 1.2;
                this.leftArmGroup.rotation.x = -tearProgress * 0.7;
                this.rightArmGroup.rotation.x = -tearProgress * 0.7;
                // Violent tearing gestures
                this.leftArmGroup.rotation.y = -tearProgress * 0.9;
                this.rightArmGroup.rotation.y = tearProgress * 0.9;
            }
            if (this.bodyGroup) {
                // Strain from tearing reality
                this.bodyGroup.rotation.x = -tearProgress * 0.3;
                this.bodyGroup.scale.set(1 + tearProgress * 0.15, 1, 1 + tearProgress * 0.15);
                // Violent shaking from reality distortion
                const distortion = Math.sin(tearProgress * Math.PI * 8) * tearProgress * 0.1;
                this.bodyGroup.position.x = distortion;
                this.bodyGroup.position.z = distortion * 0.5;
            }
            if (this.headGroup) {
                // Intense focus on reality manipulation
                this.headGroup.rotation.x = tearProgress * 0.4;
                this.headGroup.rotation.y = Math.sin(tearProgress * Math.PI * 4) * 0.2;
            }
        } else {
            // Fracture spreading phase - cracks propagate through space
            const spreadProgress = (t - 0.5) / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms guide spreading fractures
                this.leftArmGroup.rotation.z = 1.2 - spreadProgress * 0.4;
                this.rightArmGroup.rotation.z = -1.2 + spreadProgress * 0.4;
                this.leftArmGroup.rotation.x = -0.7 + spreadProgress * 0.3;
                this.rightArmGroup.rotation.x = -0.7 + spreadProgress * 0.3;
                this.leftArmGroup.rotation.y = -0.9 + spreadProgress * 0.5;
                this.rightArmGroup.rotation.y = 0.9 - spreadProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Body settles as fractures stabilize
                this.bodyGroup.rotation.x = -0.3 + spreadProgress * 0.3;
                this.bodyGroup.scale.set(1.15 - spreadProgress * 0.15, 1, 1.15 - spreadProgress * 0.15);
                // Reduce distortion as fractures stabilize
                const stabilization = (1 - spreadProgress) * 0.1;
                this.bodyGroup.position.x = Math.sin(spreadProgress * Math.PI * 3) * stabilization;
                this.bodyGroup.position.z = Math.cos(spreadProgress * Math.PI * 3) * stabilization * 0.5;
            }
            if (this.headGroup) {
                // Observe spreading cracks
                this.headGroup.rotation.x = 0.4 - spreadProgress * 0.2;
                this.headGroup.rotation.y = Math.sin(spreadProgress * Math.PI * 2) * 0.15;
            }
        }
    }

    // 🌟 PHASE 3: REALITY COLLAPSE (25% - 0% Health) - Ultimate Environmental Chaos

    /**
     * 💥 Rift Collapse Ultimate - Floor breaks into floating platforms, becomes platformer
     * Most dramatic attack - reality itself collapses around the boss
     */
    _animateRiftCollapseUltimate(t) {
        if (t < 0.3) {
            // Preparation phase - gathering reality-tearing power
            const gatherProgress = t / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms raised high, channeling ultimate power
                this.leftArmGroup.rotation.z = gatherProgress * 2.2;
                this.leftArmGroup.rotation.x = -gatherProgress * 0.8;
                this.rightArmGroup.rotation.z = -gatherProgress * 2.2;
                this.rightArmGroup.rotation.x = -gatherProgress * 0.8;
                this.leftArmGroup.rotation.y = -gatherProgress * 0.4;
                this.rightArmGroup.rotation.y = gatherProgress * 0.4;
            }
            if (this.bodyGroup) {
                // Body growing with power
                this.bodyGroup.rotation.x = -gatherProgress * 0.6;
                this.bodyGroup.scale.set(1 + gatherProgress * 0.25, 1, 1 + gatherProgress * 0.25);
            }
            if (this.headGroup) {
                // Head thrown back in concentration
                this.headGroup.rotation.x = -gatherProgress * 0.5;
            }
            if (this.staffGroup) {
                // Staff glowing with ultimate power
                this.staffGroup.rotation.x = -gatherProgress * 0.7;
                this.staffGroup.scale.set(1 + gatherProgress * 0.3, 1 + gatherProgress * 0.3, 1 + gatherProgress * 0.3);
            }
        } else if (t < 0.7) {
            // Reality tearing phase - the world breaks apart
            const tearProgress = (t - 0.3) / 0.4;
            const chaosTime = tearProgress * Math.PI * 4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms controlling the chaos, reality bends to will
                this.leftArmGroup.rotation.z = 2.2 - tearProgress * 0.5;
                this.leftArmGroup.rotation.x = -0.8 + Math.sin(chaosTime) * 0.3;
                this.rightArmGroup.rotation.z = -2.2 + tearProgress * 0.5;
                this.rightArmGroup.rotation.x = -0.8 + Math.cos(chaosTime) * 0.3;
                // Weaving reality like fabric
                this.leftArmGroup.rotation.y = -0.4 + Math.sin(chaosTime * 0.7) * 0.6;
                this.rightArmGroup.rotation.y = 0.4 - Math.cos(chaosTime * 0.7) * 0.6;
            }
            if (this.bodyGroup) {
                // Body stable as center of the chaos
                this.bodyGroup.rotation.x = -0.6 + tearProgress * 0.3;
                this.bodyGroup.scale.set(1.25, 1, 1.25);
                // Slight sway as reality warps around
                this.bodyGroup.position.x = Math.sin(chaosTime * 0.3) * 0.1;
                this.bodyGroup.position.z = Math.cos(chaosTime * 0.3) * 0.1;
            }
            if (this.headGroup) {
                // Watching the world tear apart
                this.headGroup.rotation.x = -0.5 + tearProgress * 0.3;
                this.headGroup.rotation.y = Math.sin(chaosTime * 0.5) * 0.2;
            }
            if (this.staffGroup) {
                // Staff as conduit for reality manipulation
                this.staffGroup.rotation.x = -0.7;
                this.staffGroup.rotation.y = chaosTime;
                this.staffGroup.scale.set(1.3, 1.3, 1.3);
            }
        } else {
            // Collapse phase - everything falls apart
            const collapseProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spread wide as reality collapses
                this.leftArmGroup.rotation.z = 1.7 - collapseProgress * 0.5;
                this.leftArmGroup.rotation.x = -0.5 + collapseProgress * 1.2;
                this.rightArmGroup.rotation.z = -1.7 + collapseProgress * 0.5;
                this.rightArmGroup.rotation.x = -0.5 + collapseProgress * 1.2;
                this.leftArmGroup.rotation.y = 0.2 - collapseProgress * 0.6;
                this.rightArmGroup.rotation.y = -0.2 + collapseProgress * 0.6;
            }
            if (this.bodyGroup) {
                // Body settling as the ultimate power is released
                this.bodyGroup.rotation.x = -0.3 + collapseProgress * 0.3;
                this.bodyGroup.scale.set(1.25 - collapseProgress * 0.15, 1, 1.25 - collapseProgress * 0.15);
                this.bodyGroup.position.x = 0;
                this.bodyGroup.position.z = 0;
            }
            if (this.headGroup) {
                // Head slowly lowering as the attack completes
                this.headGroup.rotation.x = -0.2 + collapseProgress * 0.4;
                this.headGroup.rotation.y = 0;
            }
            if (this.staffGroup) {
                // Staff returns to normal as reality settles
                this.staffGroup.rotation.x = -0.7 + collapseProgress * 0.7;
                this.staffGroup.rotation.y = 0;
                this.staffGroup.scale.set(1.3 - collapseProgress * 0.3, 1.3 - collapseProgress * 0.3, 1.3 - collapseProgress * 0.3);
            }
        }
    }

    /**
     * 🗿 Platform Shard Volley - Launches floating platform chunks as projectiles
     */
    _animatePlatformShardVolley(t) {
        if (t < 0.3) {
            // Summoning phase - calling platform chunks from the void
            const summonProgress = t / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms raised, summoning floating chunks
                this.leftArmGroup.rotation.z = summonProgress * 1.6;
                this.leftArmGroup.rotation.x = -summonProgress * 0.5;
                this.rightArmGroup.rotation.z = -summonProgress * 1.6;
                this.rightArmGroup.rotation.x = -summonProgress * 0.5;
                this.leftArmGroup.rotation.y = -summonProgress * 0.3;
                this.rightArmGroup.rotation.y = summonProgress * 0.3;
            }
            if (this.bodyGroup) {
                // Body leaning back with summoning effort
                this.bodyGroup.rotation.x = -summonProgress * 0.4;
                this.bodyGroup.scale.set(1 + summonProgress * 0.1, 1, 1 + summonProgress * 0.1);
            }
            if (this.staffGroup) {
                // Staff pointing upward to summon chunks
                this.staffGroup.rotation.x = -summonProgress * 0.8;
            }
        } else if (t < 0.7) {
            // Control phase - directing the floating chunks
            const controlProgress = (t - 0.3) / 0.4;
            const orchestraTime = controlProgress * Math.PI * 3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms conducting the floating chunks like an orchestra
                this.leftArmGroup.rotation.z = 1.6 - controlProgress * 0.4;
                this.leftArmGroup.rotation.x = -0.5 + Math.sin(orchestraTime) * 0.3;
                this.rightArmGroup.rotation.z = -1.6 + controlProgress * 0.4;
                this.rightArmGroup.rotation.x = -0.5 + Math.cos(orchestraTime * 1.2) * 0.3;
                // Sweeping motions to guide chunks
                this.leftArmGroup.rotation.y = -0.3 + Math.sin(orchestraTime * 0.8) * 0.4;
                this.rightArmGroup.rotation.y = 0.3 - Math.cos(orchestraTime * 0.8) * 0.4;
            }
            if (this.bodyGroup) {
                // Body swaying with the conducting motions
                this.bodyGroup.rotation.x = -0.4 + controlProgress * 0.2;
                this.bodyGroup.rotation.y = Math.sin(orchestraTime * 0.4) * 0.15;
                this.bodyGroup.scale.set(1.1, 1, 1.1);
            }
            if (this.headGroup) {
                // Head tracking the floating chunks
                this.headGroup.rotation.x = 0.2 + Math.sin(orchestraTime * 0.6) * 0.2;
                this.headGroup.rotation.y = Math.cos(orchestraTime * 0.5) * 0.3;
            }
            if (this.staffGroup) {
                // Staff as conductor's baton
                this.staffGroup.rotation.x = -0.8 + Math.sin(orchestraTime * 1.1) * 0.3;
                this.staffGroup.rotation.y = Math.cos(orchestraTime * 0.9) * 0.2;
            }
        } else {
            // Launch phase - hurling chunks at player
            const launchProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Powerful throwing motions
                this.leftArmGroup.rotation.z = 1.2 - launchProgress * 1.8;
                this.leftArmGroup.rotation.x = 0.2 + launchProgress * 0.6;
                this.rightArmGroup.rotation.z = -1.2 + launchProgress * 1.8;
                this.rightArmGroup.rotation.x = 0.2 + launchProgress * 0.6;
                this.leftArmGroup.rotation.y = 0.1 - launchProgress * 0.5;
                this.rightArmGroup.rotation.y = -0.1 + launchProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Body follow-through with the throws
                this.bodyGroup.rotation.x = -0.2 + launchProgress * 0.6;
                this.bodyGroup.rotation.y = 0;
                this.bodyGroup.scale.set(1.1 - launchProgress * 0.1, 1, 1.1 - launchProgress * 0.1);
            }
            if (this.headGroup) {
                // Head watching the projectiles fly
                this.headGroup.rotation.x = 0.4;
                this.headGroup.rotation.y = 0;
            }
            if (this.staffGroup) {
                // Staff pointing at target
                this.staffGroup.rotation.x = -0.5 + launchProgress * 0.8;
                this.staffGroup.rotation.y = 0;
            }
        }
    }

    /**
     * 💎 Crystal Cascade - Creates massive crystal waterfall effect
     */
    _animateCrystalCascade(t) {
        if (t < 0.4) {
            // Summoning phase - gathering crystal energy
            const summonProgress = t / 0.4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms gathering energy upward
                this.leftArmGroup.rotation.z = summonProgress * 1.8;
                this.leftArmGroup.rotation.x = -summonProgress * 0.7;
                this.rightArmGroup.rotation.z = -summonProgress * 1.8;
                this.rightArmGroup.rotation.x = -summonProgress * 0.7;
            }
            if (this.bodyGroup) {
                // Body channeling energy
                this.bodyGroup.rotation.x = -summonProgress * 0.3;
                this.bodyGroup.scale.set(1 + summonProgress * 0.15, 1, 1 + summonProgress * 0.15);
            }
            if (this.headGroup) {
                // Head looking up at forming crystals
                this.headGroup.rotation.x = -summonProgress * 0.6;
            }
            if (this.staffGroup) {
                // Staff channeling crystal power
                this.staffGroup.rotation.x = -summonProgress * 0.9;
                this.staffGroup.scale.set(1 + summonProgress * 0.2, 1 + summonProgress * 0.2, 1 + summonProgress * 0.2);
            }
        } else if (t < 0.7) {
            // Cascade phase - crystals begin falling
            const cascadeProgress = (t - 0.4) / 0.3;
            const fallTime = cascadeProgress * Math.PI * 2;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms directing the cascade
                this.leftArmGroup.rotation.z = 1.8 - cascadeProgress * 0.6;
                this.leftArmGroup.rotation.x = -0.7 + cascadeProgress * 0.9;
                this.rightArmGroup.rotation.z = -1.8 + cascadeProgress * 0.6;
                this.rightArmGroup.rotation.x = -0.7 + cascadeProgress * 0.9;
                // Flowing motions like conducting a waterfall
                this.leftArmGroup.rotation.y = Math.sin(fallTime) * 0.3;
                this.rightArmGroup.rotation.y = -Math.sin(fallTime) * 0.3;
            }
            if (this.bodyGroup) {
                // Body swaying with the crystal flow
                this.bodyGroup.rotation.x = -0.3 + cascadeProgress * 0.2;
                this.bodyGroup.rotation.y = Math.sin(fallTime * 0.5) * 0.1;
                this.bodyGroup.scale.set(1.15, 1, 1.15);
            }
            if (this.headGroup) {
                // Head following the cascading crystals
                this.headGroup.rotation.x = -0.6 + cascadeProgress * 0.8;
            }
            if (this.staffGroup) {
                // Staff glowing with crystal energy
                this.staffGroup.rotation.x = -0.9 + cascadeProgress * 0.4;
                this.staffGroup.rotation.y = Math.sin(fallTime * 1.3) * 0.2;
                this.staffGroup.scale.set(1.2, 1.2, 1.2);
            }
        } else {
            // Impact phase - crystals crashing down
            const impactProgress = (t - 0.7) / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spread wide as crystals impact
                this.leftArmGroup.rotation.z = 1.2 - impactProgress * 0.4;
                this.leftArmGroup.rotation.x = 0.2 + impactProgress * 0.3;
                this.rightArmGroup.rotation.z = -1.2 + impactProgress * 0.4;
                this.rightArmGroup.rotation.x = 0.2 + impactProgress * 0.3;
                this.leftArmGroup.rotation.y = 0;
                this.rightArmGroup.rotation.y = 0;
            }
            if (this.bodyGroup) {
                // Body settling as cascade completes
                this.bodyGroup.rotation.x = -0.1 + impactProgress * 0.1;
                this.bodyGroup.rotation.y = 0;
                this.bodyGroup.scale.set(1.15 - impactProgress * 0.15, 1, 1.15 - impactProgress * 0.15);
            }
            if (this.headGroup) {
                // Head observing the crystal destruction
                this.headGroup.rotation.x = 0.2 - impactProgress * 0.2;
            }
            if (this.staffGroup) {
                // Staff returning to normal
                this.staffGroup.rotation.x = -0.5 + impactProgress * 0.5;
                this.staffGroup.rotation.y = 0;
                this.staffGroup.scale.set(1.2 - impactProgress * 0.2, 1.2 - impactProgress * 0.2, 1.2 - impactProgress * 0.2);
            }
        }
    }

    /**
     * ⛈️ Temporal Storm - Creates chaotic time distortion storms
     */
    _animateTemporalStorm(t) {
        if (t < 0.3) {
            // Gathering phase - collecting temporal energy
            const gatherProgress = t / 0.3;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms drawing in temporal energy
                this.leftArmGroup.rotation.z = gatherProgress * 1.4;
                this.leftArmGroup.rotation.x = -gatherProgress * 0.6;
                this.rightArmGroup.rotation.z = -gatherProgress * 1.4;
                this.rightArmGroup.rotation.x = -gatherProgress * 0.6;
                this.leftArmGroup.rotation.y = -gatherProgress * 0.5;
                this.rightArmGroup.rotation.y = gatherProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Body growing with temporal power
                this.bodyGroup.rotation.x = -gatherProgress * 0.4;
                this.bodyGroup.scale.set(1 + gatherProgress * 0.2, 1, 1 + gatherProgress * 0.2);
            }
            if (this.headGroup) {
                // Head concentrating on the storm
                this.headGroup.rotation.x = -gatherProgress * 0.3;
            }
            if (this.staffGroup) {
                // Staff as focal point for temporal energy
                this.staffGroup.rotation.x = -gatherProgress * 0.7;
                this.staffGroup.scale.set(1 + gatherProgress * 0.3, 1 + gatherProgress * 0.3, 1 + gatherProgress * 0.3);
            }
        } else if (t < 0.8) {
            // Storm phase - chaotic temporal distortions
            const stormProgress = (t - 0.3) / 0.5;
            const chaosTime = stormProgress * Math.PI * 6; // Very chaotic
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms controlling chaotic temporal forces
                this.leftArmGroup.rotation.z = 1.4 + Math.sin(chaosTime * 1.7) * 0.6;
                this.leftArmGroup.rotation.x = -0.6 + Math.cos(chaosTime * 1.3) * 0.4;
                this.rightArmGroup.rotation.z = -1.4 - Math.sin(chaosTime * 1.7) * 0.6;
                this.rightArmGroup.rotation.x = -0.6 + Math.sin(chaosTime * 1.1) * 0.4;
                // Chaotic rotations as time itself distorts
                this.leftArmGroup.rotation.y = -0.5 + Math.sin(chaosTime * 0.9) * 0.8;
                this.rightArmGroup.rotation.y = 0.5 - Math.cos(chaosTime * 0.7) * 0.8;
            }
            if (this.bodyGroup) {
                // Body at center of temporal storm
                this.bodyGroup.rotation.x = -0.4 + Math.sin(chaosTime * 0.5) * 0.2;
                this.bodyGroup.rotation.y = Math.cos(chaosTime * 0.3) * 0.15;
                this.bodyGroup.rotation.z = Math.sin(chaosTime * 0.4) * 0.1;
                this.bodyGroup.scale.set(1.2, 1, 1.2);
                // Slight position distortion from temporal effects
                this.bodyGroup.position.x = Math.sin(chaosTime * 0.8) * 0.05;
                this.bodyGroup.position.z = Math.cos(chaosTime * 0.6) * 0.05;
            }
            if (this.headGroup) {
                // Head buffeted by temporal winds
                this.headGroup.rotation.x = -0.3 + Math.sin(chaosTime * 1.5) * 0.3;
                this.headGroup.rotation.y = Math.cos(chaosTime * 1.2) * 0.25;
            }
            if (this.staffGroup) {
                // Staff as eye of the temporal storm
                this.staffGroup.rotation.x = -0.7 + Math.sin(chaosTime * 2.1) * 0.4;
                this.staffGroup.rotation.y = chaosTime * 0.5; // Spinning in the storm
                this.staffGroup.rotation.z = Math.cos(chaosTime * 1.8) * 0.3;
                this.staffGroup.scale.set(1.3, 1.3, 1.3);
            }
        } else {
            // Dissipation phase - storm calming
            const dissipateProgress = (t - 0.8) / 0.2;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms slowly lowering as storm subsides
                this.leftArmGroup.rotation.z = 1.4 - dissipateProgress * 1.4;
                this.leftArmGroup.rotation.x = -0.6 + dissipateProgress * 0.6;
                this.rightArmGroup.rotation.z = -1.4 + dissipateProgress * 1.4;
                this.rightArmGroup.rotation.x = -0.6 + dissipateProgress * 0.6;
                this.leftArmGroup.rotation.y = -0.5 + dissipateProgress * 0.5;
                this.rightArmGroup.rotation.y = 0.5 - dissipateProgress * 0.5;
            }
            if (this.bodyGroup) {
                // Body returning to normal as temporal effects fade
                this.bodyGroup.rotation.x = -0.4 + dissipateProgress * 0.4;
                this.bodyGroup.rotation.y = 0;
                this.bodyGroup.rotation.z = 0;
                this.bodyGroup.scale.set(1.2 - dissipateProgress * 0.2, 1, 1.2 - dissipateProgress * 0.2);
                this.bodyGroup.position.x = 0;
                this.bodyGroup.position.z = 0;
            }
            if (this.headGroup) {
                // Head clearing as storm ends
                this.headGroup.rotation.x = -0.3 + dissipateProgress * 0.3;
                this.headGroup.rotation.y = 0;
            }
            if (this.staffGroup) {
                // Staff settling as temporal power fades
                this.staffGroup.rotation.x = -0.7 + dissipateProgress * 0.7;
                this.staffGroup.rotation.y = 0;
                this.staffGroup.rotation.z = 0;
                this.staffGroup.scale.set(1.3 - dissipateProgress * 0.3, 1.3 - dissipateProgress * 0.3, 1.3 - dissipateProgress * 0.3);
            }
        }
    }

    /**
     * 🌟 Final Transmutation - Ultimate transformation attack that changes the boss itself
     */
    _animateFinalTransmutation(t) {
        if (t < 0.5) {
            // Transformation phase - boss undergoes dramatic change
            const transmuteProgress = t / 0.5;
            const pulseTime = transmuteProgress * Math.PI * 4;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spreading wide as transformation energy flows through
                this.leftArmGroup.rotation.z = transmuteProgress * 2.0;
                this.leftArmGroup.rotation.x = -transmuteProgress * 0.8;
                this.rightArmGroup.rotation.z = -transmuteProgress * 2.0;
                this.rightArmGroup.rotation.x = -transmuteProgress * 0.8;
                this.leftArmGroup.rotation.y = -transmuteProgress * 0.6;
                this.rightArmGroup.rotation.y = transmuteProgress * 0.6;
                // Pulsing with transformation energy
                const armPulse = 1 + Math.sin(pulseTime) * 0.1;
                this.leftArmGroup.scale.set(armPulse, armPulse, armPulse);
                this.rightArmGroup.scale.set(armPulse, armPulse, armPulse);
            }
            if (this.bodyGroup) {
                // Body growing and pulsing with transformation
                this.bodyGroup.rotation.x = -transmuteProgress * 0.5;
                const bodyScale = 1 + transmuteProgress * 0.3;
                const bodyPulse = bodyScale + Math.sin(pulseTime * 1.3) * 0.15;
                this.bodyGroup.scale.set(bodyPulse, bodyScale, bodyPulse);
                // Energy distortion effects
                this.bodyGroup.position.y = Math.sin(pulseTime * 0.7) * 0.1;
            }
            if (this.headGroup) {
                // Head thrown back in transformation ecstasy
                this.headGroup.rotation.x = -transmuteProgress * 0.7;
                const headPulse = 1 + Math.sin(pulseTime * 1.1) * 0.08;
                this.headGroup.scale.set(headPulse, headPulse, headPulse);
            }
            if (this.staffGroup) {
                // Staff as conduit for transformation energy
                this.staffGroup.rotation.x = -transmuteProgress * 1.0;
                this.staffGroup.rotation.y = pulseTime * 0.3; // Slowly rotating
                const staffScale = 1 + transmuteProgress * 0.4;
                const staffPulse = staffScale + Math.sin(pulseTime * 1.5) * 0.2;
                this.staffGroup.scale.set(staffPulse, staffPulse, staffPulse);
            }
        } else {
            // Completion phase - transformation stabilizing
            const stabilizeProgress = (t - 0.5) / 0.5;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms slowly returning to ready position, but transformed
                this.leftArmGroup.rotation.z = 2.0 - stabilizeProgress * 1.5;
                this.leftArmGroup.rotation.x = -0.8 + stabilizeProgress * 0.5;
                this.rightArmGroup.rotation.z = -2.0 + stabilizeProgress * 1.5;
                this.rightArmGroup.rotation.x = -0.8 + stabilizeProgress * 0.5;
                this.leftArmGroup.rotation.y = -0.6 + stabilizeProgress * 0.4;
                this.rightArmGroup.rotation.y = 0.6 - stabilizeProgress * 0.4;
                // Arms maintain slight enlargement from transformation
                const finalArmScale = 1.1;
                this.leftArmGroup.scale.set(finalArmScale, finalArmScale, finalArmScale);
                this.rightArmGroup.scale.set(finalArmScale, finalArmScale, finalArmScale);
            }
            if (this.bodyGroup) {
                // Body stabilizing at new transformed size
                this.bodyGroup.rotation.x = -0.5 + stabilizeProgress * 0.3;
                const finalBodyScale = 1.2; // Permanently larger after transformation
                this.bodyGroup.scale.set(finalBodyScale, 1.1, finalBodyScale);
                this.bodyGroup.position.y = 0;
            }
            if (this.headGroup) {
                // Head returning to normal but with enhanced presence
                this.headGroup.rotation.x = -0.7 + stabilizeProgress * 0.5;
                const finalHeadScale = 1.05; // Slightly larger, more imposing
                this.headGroup.scale.set(finalHeadScale, finalHeadScale, finalHeadScale);
            }
            if (this.staffGroup) {
                // Staff maintaining enhanced power
                this.staffGroup.rotation.x = -1.0 + stabilizeProgress * 0.7;
                this.staffGroup.rotation.y = 0;
                const finalStaffScale = 1.3; // Permanently enhanced after transmutation
                this.staffGroup.scale.set(finalStaffScale, finalStaffScale, finalStaffScale);
            }
        }
    }

    /**
     * 💨 Toxic Cloud Burst - Brews and releases spreading poison gas
     */
    _animateToxicCloudBurst(t) {
        if (t < 0.4) {
            // Brewing toxic mixture phase
            const brewProgress = t / 0.4;
            const bubbleTime = brewProgress * Math.PI * 6; // Bubbling motion
            if (this.leftArmGroup) {
                // Left arm holding vial, shaking it
                this.leftArmGroup.rotation.z = 0.8 + Math.sin(bubbleTime) * 0.3;
                this.leftArmGroup.rotation.x = -0.4 + Math.cos(bubbleTime * 1.3) * 0.2;
                this.leftArmGroup.rotation.y = Math.sin(bubbleTime * 0.7) * 0.15;
            }
            if (this.rightArmGroup && this.staffGroup) {
                // Staff adding magical catalyst
                this.rightArmGroup.rotation.z = -0.6;
                this.rightArmGroup.rotation.x = -0.3;
                this.staffGroup.rotation.x = -0.4 + Math.sin(bubbleTime * 1.1) * 0.2;
            }
            if (this.bodyGroup) {
                // Body leaning forward to examine the brew
                this.bodyGroup.rotation.x = -brewProgress * 0.3;
            }
        } else {
            // Gas release phase - explosive outward motion
            const releaseProgress = (t - 0.4) / 0.6;
            if (this.leftArmGroup && this.rightArmGroup) {
                // Arms spreading wide to release toxic cloud
                this.leftArmGroup.rotation.z = 0.8 + releaseProgress * 0.8;
                this.leftArmGroup.rotation.x = -0.4 + releaseProgress * 0.6;
                this.rightArmGroup.rotation.z = -0.6 - releaseProgress * 0.8;
                this.rightArmGroup.rotation.x = -0.3 + releaseProgress * 0.6;
            }
            if (this.bodyGroup) {
                // Body recoiling from toxic release
                this.bodyGroup.rotation.x = -0.3 + releaseProgress * 0.5;
                this.bodyGroup.rotation.y = Math.sin(releaseProgress * Math.PI * 4) * 0.2;
            }
            if (this.headGroup) {
                // Head turning away from toxic fumes
                this.headGroup.rotation.x = releaseProgress * 0.4;
                this.headGroup.rotation.y = -releaseProgress * 0.3;
            }
            if (this.staffGroup) {
                // Staff pointing away as cloud spreads
                this.staffGroup.rotation.x = -0.4 + releaseProgress * 0.8;
                this.staffGroup.rotation.y = releaseProgress * 0.5;
            }
        }
    }

    /**
     * Original update method for fallback when workers fail
     * @private
     */
    _originalUpdate(deltaTime, aiState) {
        // This would contain the original update logic
        // For now, we'll use a simplified version that calls the main update logic
        const globalTime = Date.now() * 0.001;

        // Update attack animation progress with NaN protection
        if (this.isAttacking) {
            const duration = Math.max(this.animationData.attackAnimationDuration || 1.0, 0.001);
            const progressIncrement = deltaTime / duration;

            // Validate against NaN/Infinity
            if (isFinite(progressIncrement) && !isNaN(progressIncrement)) {
                this.attackAnimationProgress += progressIncrement;
            } else {
                console.warn('[ChronarchAnimationHandler] Invalid progress increment, using fallback');
                this.attackAnimationProgress += 0.016; // Fallback increment
            }

            if (this.attackAnimationProgress >= 1.0) {
                this.isAttacking = false;
                this.attackAnimationProgress = 0;
            }
        }

        // Continue with state-based animations
        this._updateStateBasedAnimations(aiState, globalTime, deltaTime);
    }
}
