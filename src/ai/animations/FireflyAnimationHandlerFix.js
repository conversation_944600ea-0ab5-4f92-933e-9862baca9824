import { FireflyAnimationHandler } from './FireflyAnimationHandler.js';

/**
 * Fix firefly animation handler issues
 * @param {Object} enemy - The firefly enemy object
 * @returns {Boolean} - True if fixed, false otherwise
 */
export function fixFireflyAnimationHandler(enemy) {
    // Check if this is a firefly enemy
    const isFirefly = enemy.name.includes('firefly') ||
                     enemy.name.includes('Firefly') ||
                     (enemy.userData && enemy.userData.type === 'firefly');

    if (!isFirefly) {
        console.log(`[FireflyAnimationHandlerFix] Not a firefly enemy: ${enemy.name}`);
        return false;
    }

    // Check if animation handler already exists
    const hasAnimHandler = enemy.userData && enemy.userData.animationHandler;

    if (hasAnimHandler) {
        console.log(`[FireflyAnimationHandlerFix] Animation handler already exists for ${enemy.name}`);
        return false;
    }

    // Validate enemy structure
    if (!enemy.userData) {
        console.error(`[FireflyAnimationHandlerFix] Enemy userData missing for ${enemy.name}`);
        return false;
    }

    // Check if enemy has the required structure for animation
    const hasBodyGroup = enemy.getObjectByName && enemy.getObjectByName('body');
    const hasWingGroups = enemy.getObjectByName && 
                         enemy.getObjectByName('leftWing') && 
                         enemy.getObjectByName('rightWing');

    if (!hasBodyGroup || !hasWingGroups) {
        console.warn(`[FireflyAnimationHandlerFix] Enemy ${enemy.name} missing required animation groups (body, wings)`);
        console.log(`[FireflyAnimationHandlerFix] Body group:`, hasBodyGroup);
        console.log(`[FireflyAnimationHandlerFix] Wing groups:`, hasWingGroups);
        // Continue anyway - the animation handler will handle missing groups gracefully
    }

    // Create and attach the animation handler
    try {
        // Create the firefly animation handler
        enemy.userData.animationHandler = new FireflyAnimationHandler(enemy);
        console.log(`[FireflyAnimationHandlerFix] ✅ Successfully created FireflyAnimationHandler for ${enemy.name}`);
        return true;
    } catch (error) {
        console.error(`[FireflyAnimationHandlerFix] ❌ Error creating animation handler:`, error);
        return false;
    }
}

/**
 * Fix all firefly animation handlers in the scene
 * @param {THREE.Scene} scene - The scene to search for firefly enemies
 * @returns {Number} - Number of fireflies fixed
 */
export function fixAllFireflyAnimationHandlers(scene) {
    if (!scene) {
        console.error('[FireflyAnimationHandlerFix] No scene provided');
        return 0;
    }

    let fixedCount = 0;

    // Search for firefly enemies in the scene
    scene.traverse((object) => {
        // Check if this object is a firefly enemy
        const isFirefly = object.name && (
            object.name.includes('firefly') ||
            object.name.includes('Firefly') ||
            (object.userData && object.userData.type === 'firefly')
        );

        if (isFirefly) {
            console.log(`[FireflyAnimationHandlerFix] Found firefly enemy: ${object.name}`);
            
            if (fixFireflyAnimationHandler(object)) {
                fixedCount++;
            }
        }
    });

    if (fixedCount > 0) {
        console.log(`[FireflyAnimationHandlerFix] ✅ Fixed ${fixedCount} firefly enemies`);
    } else {
        console.log(`[FireflyAnimationHandlerFix] No firefly enemies found or all already have animation handlers`);
    }

    return fixedCount;
}
