/**
 * Shadow Minion Animation Handler
 * Handles animation for shadow minions that crawl out of the ground
 * Features emergence animation, scuttling movement, and wispy effects
 */
import * as THREE from 'three';

export class ShadowMinionAnimationHandler {
    constructor(enemy) {
        this.enemy = enemy;
        this.animationStartTime = performance.now();
        this.currentState = 'EMERGING';
        
        // Animation parameters
        this.scuttleSpeed = 8.0;  // Fast spider-like movement
        this.legWaveSpeed = 12.0; // Quick leg movement
        this.wispSpeed = 3.0;     // Slow ethereal wisps
        this.emergenceTime = 2000; // 2 seconds to emerge
        
        // Get references to body parts
        this.bodyParts = {
            body: enemy.getObjectByName('body'),
            head: enemy.getObjectByName('head'),
            tail: enemy.getObjectByName('tail'),
            legs: [
                enemy.getObjectByName('frontLeftLeg'),
                enemy.getObjectByName('frontRightLeg'),
                enemy.getObjectByName('midFrontLeftLeg'),
                enemy.getObjectByName('midFrontRightLeg'),
                enemy.getObjectByName('midBackLeftLeg'),
                enemy.getObjectByName('midBackRightLeg'),
                enemy.getObjectByName('backLeftLeg'),
                enemy.getObjectByName('backRightLeg')
            ].filter(leg => leg !== undefined)
        };
        
        // Store original positions
        this.originalY = enemy.position.y;
        this.emergenceStartY = this.originalY - 3.0; // Start 3 units underground
        
        // Set initial emergence position
        if (enemy.userData.emergingFromGround) {
            enemy.position.y = this.emergenceStartY;
        }
        
        console.log('[ShadowMinionAnimationHandler] Initialized for emergence animation');
    }
    
    /**
     * Set animation state
     */
    setState(newState) {
        if (this.currentState !== newState) {
            console.log(`[ShadowMinionAnimationHandler] State: ${this.currentState} → ${newState}`);
            this.currentState = newState;
            this.animationStartTime = performance.now();
        }
    }
    
    /**
     * Update animation each frame
     */
    update(deltaTime) {
        if (!this.enemy) return;
        
        const currentTime = performance.now();
        const elapsed = currentTime - this.animationStartTime;
        
        switch (this.currentState) {
            case 'EMERGING':
                this.updateEmergenceAnimation(elapsed, deltaTime);
                break;
            case 'SCUTTLING':
                this.updateScuttlingAnimation(elapsed, deltaTime);
                break;
            case 'ATTACKING':
                this.updateAttackAnimation(elapsed, deltaTime);
                break;
            case 'DYING':
                this.updateDeathAnimation(elapsed, deltaTime);
                break;
        }
        
        // Always update wispy effects
        this.updateWispyEffects(currentTime, deltaTime);
    }
    
    /**
     * Emergence animation - crawling out of the ground
     */
    updateEmergenceAnimation(elapsed, deltaTime) {
        const progress = Math.min(elapsed / this.emergenceTime, 1.0);
        const easeProgress = this.easeOutCubic(progress);
        
        // Rise from underground
        this.enemy.position.y = THREE.MathUtils.lerp(this.emergenceStartY, this.originalY, easeProgress);
        
        // Shake/struggle effect while emerging
        const struggle = Math.sin(elapsed * 0.01) * (1.0 - progress) * 0.1;
        this.enemy.rotation.z = struggle;
        
        // Legs scrambling effect
        this.bodyParts.legs.forEach((leg, index) => {
            if (!leg) return;
            const legPhase = index * 0.5;
            const scramble = Math.sin((elapsed + legPhase) * 0.02) * (1.0 - progress) * 0.3;
            leg.rotation.y = scramble;
        });
        
        // Head looking around as it emerges
        if (this.bodyParts.head) {
            const headTurn = Math.sin(elapsed * 0.008) * 0.4;
            this.bodyParts.head.rotation.y = headTurn;
        }
        
        // Transition to scuttling when emergence is complete
        if (progress >= 1.0) {
            this.setState('SCUTTLING');
        }
    }
    
    /**
     * Scuttling movement animation - spider-like locomotion
     */
    updateScuttlingAnimation(elapsed, deltaTime) {
        // Body bobbing motion
        if (this.bodyParts.body) {
            const bob = Math.sin(elapsed * this.scuttleSpeed * 0.001) * 0.05;
            this.bodyParts.body.position.y = bob;
        }
        
        // Head scanning motion
        if (this.bodyParts.head) {
            const scan = Math.sin(elapsed * 0.003) * 0.6;
            this.bodyParts.head.rotation.y = scan;
        }
        
        // Leg wave motion (spider-like gait)
        this.bodyParts.legs.forEach((leg, index) => {
            if (!leg) return;
            
            // Create wave pattern through legs
            const legPhase = (index / this.bodyParts.legs.length) * Math.PI * 2;
            const wave = Math.sin((elapsed * this.legWaveSpeed * 0.001) + legPhase);
            
            // Alternating leg movement (spider gait)
            const lift = Math.max(0, wave) * 0.2;
            const reach = wave * 0.3;
            
            leg.position.y = lift;
            leg.rotation.x = reach;
        });
        
        // Tail swaying
        if (this.bodyParts.tail) {
            const sway = Math.sin(elapsed * 0.004) * 0.3;
            this.bodyParts.tail.rotation.y = sway;
        }
    }
    
    /**
     * Attack animation - rearing up and striking
     */
    updateAttackAnimation(elapsed, deltaTime) {
        const attackDuration = 1000; // 1 second attack
        const progress = Math.min(elapsed / attackDuration, 1.0);
        
        if (progress < 0.3) {
            // Wind-up: rear up
            const rearUp = progress / 0.3;
            this.enemy.rotation.x = -rearUp * 0.4; // Rear back
            
            // Front legs up
            this.bodyParts.legs.slice(0, 4).forEach(leg => {
                if (leg) leg.rotation.x = -rearUp * 0.8;
            });
            
        } else if (progress < 0.6) {
            // Strike: lunge forward
            const strike = (progress - 0.3) / 0.3;
            this.enemy.rotation.x = -0.4 + strike * 0.8; // Forward lunge
            
        } else {
            // Recovery: return to normal
            const recovery = (progress - 0.6) / 0.4;
            this.enemy.rotation.x = 0.4 - recovery * 0.4;
            
            // Reset leg positions
            this.bodyParts.legs.forEach(leg => {
                if (leg) leg.rotation.x = THREE.MathUtils.lerp(leg.rotation.x, 0, recovery);
            });
        }
        
        // Return to scuttling after attack
        if (progress >= 1.0) {
            this.setState('SCUTTLING');
        }
    }
    
    /**
     * Death animation - fading away like shadow
     */
    updateDeathAnimation(elapsed, deltaTime) {
        const deathDuration = 1500; // 1.5 seconds to fade
        const progress = Math.min(elapsed / deathDuration, 1.0);
        
        // Collapse and fade
        this.enemy.scale.setScalar(1.0 - progress);
        this.enemy.position.y = THREE.MathUtils.lerp(this.originalY, this.originalY - 1.0, progress);
        
        // Fade materials
        this.enemy.traverse(child => {
            if (child.material) {
                child.material.opacity = 1.0 - progress;
                child.material.transparent = true;
            }
        });
        
        // Spinning wisp effect
        this.enemy.rotation.y += deltaTime * 0.005 * (1.0 - progress);
    }
    
    /**
     * Wispy ethereal effects - always active
     */
    updateWispyEffects(currentTime, deltaTime) {
        // Subtle floating motion
        const float = Math.sin(currentTime * this.wispSpeed * 0.001) * 0.02;
        this.enemy.position.y += float * deltaTime;
        
        // Tail wisp motion
        if (this.bodyParts.tail) {
            const wispFlow = Math.sin(currentTime * 0.002) * 0.2;
            this.bodyParts.tail.rotation.x = wispFlow;
            
            // Fade tail segments (ethereal effect)
            this.bodyParts.tail.traverse(child => {
                if (child.material) {
                    const pulse = (Math.sin(currentTime * 0.004) + 1) * 0.5;
                    child.material.opacity = 0.7 + pulse * 0.3;
                    child.material.transparent = true;
                }
            });
        }
    }
    
    /**
     * Easing function for smooth emergence
     */
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
    
    /**
     * Cleanup animation resources
     */
    cleanup() {
        // Reset transformations
        if (this.enemy) {
            this.enemy.rotation.set(0, 0, 0);
            this.enemy.scale.setScalar(1);
            this.enemy.position.y = this.originalY;
        }
        
        console.log('[ShadowMinionAnimationHandler] Cleaned up animation resources');
    }
}