/**
 * NPC Dialogue Service - Modular AI system for event room conversations
 * Based on the working wllama implementation from working-ai-test.html
 */

export class NPCDialogueService {
    constructor() {
        this.wllama = null;
        this.isReady = false;
        this.isInitializing = false;
        this.conversationHistories = new Map(); // Character-specific conversation histories
        
        // Wllama configuration
        this.WasmFromCDN = {
            'single-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@2.3.2/esm/single-thread/wllama.wasm',
            'multi-thread/wllama.wasm': 'https://cdn.jsdelivr.net/npm/@wllama/wllama@2.3.2/esm/multi-thread/wllama.wasm',
        };
        
        this.modelConfig = {
            n_ctx: 2048,        // Context size for processing
            n_batch: 64,        // Accept the minimum the library enforces (GGML_KQ_MASK_PAD)
            n_threads: 1,       // Single thread (browser limitation)
            low_vram: true,     // Enable low VRAM mode for speed
            f16_kv: true,       // Enable f16 for speed
            use_mmap: true,     // Keep memory mapping
            use_mlock: false,   // Disable mlock for browser compatibility
            n_ubatch: 64,       // Match n_batch for consistency
            n_gpu_layers: 0,    // Force CPU (browser limitation)
        };
    }
    
    /**
     * Force complete reinitialization of the AI service
     */
    async forceReinitialize() {
        console.log('[NPCDialogueService] 🔄 Force reinitializing wllama...');
        
        // Reset all state
        this.isReady = false;
        this.isInitializing = false;
        this.wllama = null;
        
        // Clear conversation histories to prevent memory buildup
        this.conversationHistories.clear();
        
        // Reinitialize with minimal config
        return await this.initialize();
    }
    
    /**
     * Initialize the AI service
     */
    async initialize() {
        if (this.isReady) {
            console.log('[NPCDialogueService] Already initialized, skipping...');
            return true;
        }
        
        if (this.isInitializing) {
            console.log('[NPCDialogueService] Already initializing, waiting...');
            // Wait for current initialization to complete
            while (this.isInitializing) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return this.isReady;
        }
        
        this.isInitializing = true;
        console.log('[NPCDialogueService] 🤖 Initializing wllama for NPC conversations...');
        
        try {
            // Import wllama dynamically
            const { Wllama } = await import('https://cdn.jsdelivr.net/npm/@wllama/wllama@2.3.2/esm/index.js');
            
            this.wllama = new Wllama(this.WasmFromCDN);
            
            // Load the model  
            const modelUrl = `${window.location.protocol}//${window.location.host}/llm/tinydolphin-2.8-1.1b.Q4_0.gguf`;
            console.log('[NPCDialogueService] Loading model from:', modelUrl);
            
            await this.wllama.loadModelFromUrl(modelUrl, this.modelConfig);
            
            this.isReady = true;
            this.isInitializing = false;
            console.log('[NPCDialogueService] ✅ wllama ready! Model loaded successfully.');
            return true;
            
        } catch (error) {
            console.error('[NPCDialogueService] Failed to initialize:', error);
            this.isInitializing = false;
            return false;
        }
    }
    
    /**
     * Generate NPC response using streaming
     */
    async generateResponse(npcId, npcData, userMessage, streamingCallbacks = null) {
        if (!this.isReady || !this.wllama) {
            throw new Error('NPCDialogueService not ready. Call initialize() first.');
        }
        
        // Get conversation history for this NPC
        const history = this.conversationHistories.get(npcId) || [];
        
        // Build context prompt
        let contextPrompt = npcData.prompt;
        if (history.length > 0) {
            // Keep only last 1 message (half conversation) for minimal context
            const recentHistory = history.slice(-1);
            if (recentHistory.length > 0) {
                contextPrompt += "\n\nRECENT:";
                for (const msg of recentHistory) {
                    contextPrompt += `\n${msg.sender}: ${msg.message}`;
                }
            }
        }
        
        // Use direct completion format to prevent multi-turn generation
        const fullPrompt = `${contextPrompt}\n\nTraveler: "${userMessage}"\nKnight: "`;
        console.log('[NPCDialogueService] Generating response for:', npcData.name);
        console.log('[NPCDialogueService] 📋 Complete prompt being sent to LLM:');
        console.log('='.repeat(80));
        console.log(fullPrompt);
        console.log('='.repeat(80));
        console.log(`[NPCDialogueService] Prompt length: ${fullPrompt.length} characters`);
        
        // Stop sequences optimized for simple dialogue completion
        const stopSequences = [
            '"',            // Stop at closing quote to end response
            '"\n',          // Quote followed by newline
            'Traveler:',    // Prevent dialogue switching
            'Knight:',      // Prevent knight repetition
            'Human:',       // Prevent human dialogue
            'truth knight', // Prevent identity revelation
            'lie knight',   // Prevent identity revelation
        ];
        
        let fullResponse = '';
        let previousText = '';
        let isFirstToken = true;
        
        // Call onStart callback if provided
        if (streamingCallbacks?.onStart) {
            streamingCallbacks.onStart();
        }
        
        // Generate response with streaming - ULTRA CONSERVATIVE parameters to prevent player dialogue
        try {
            await this.wllama.createCompletion(fullPrompt, {
                nPredict: 15,        // Allow complete short sentences
                max_tokens: 15,      // Allow complete short sentences
                temperature: 0.4,    // Slightly more creative for natural responses
                topP: 0.8,          // Balanced variety in word choice
                topK: 30,           // Focused options for natural speech
                repeatPenalty: 1.1, // Prevent repetition
                repeat_penalty: 1.1, // Alternative parameter name for repeat penalty
                nBatch: 64,         // Match model config for consistency
            onNewToken: (token, piece, currentText) => {
                // Extract just the new token by comparing with previous text (exact same as working test)
                const newTokenText = currentText.substring(previousText.length);
                console.log('🔥 New token:', token, 'newText:', `"${newTokenText}"`, 'fullText:', `"${currentText}"`);
                
                // Handle first token
                if (isFirstToken) {
                    if (streamingCallbacks?.onFirstToken) {
                        streamingCallbacks.onFirstToken();
                    }
                    fullResponse = ''; // Clear any initial content (exact same as working test)
                    isFirstToken = false;
                    console.log('✅ First token - stopped thinking animation');
                }
                
                // Check for stop sequences - truncate response if found with ULTRA AGGRESSIVE checking
                let displayText = currentText;
                let shouldStop = false;
                
                // ULTRA AGGRESSIVE: Check every character for player dialogue patterns
                for (const stopSeq of stopSequences) {
                    if (currentText.includes(stopSeq)) {
                        console.log('🛑 Stop sequence detected:', stopSeq);
                        const stopIndex = currentText.indexOf(stopSeq);
                        displayText = currentText.substring(0, stopIndex).trim();
                        shouldStop = true;
                        
                        // ADDITIONAL SAFETY: If we detect player dialogue, also check for sentence completion
                        if (stopSeq.includes('PLAYER:') || stopSeq.includes('You:') || stopSeq.includes('Player:')) {
                            console.log('🚨 PLAYER DIALOGUE DETECTED - Applying aggressive truncation');
                            // Find the last complete sentence before the player dialogue
                            const sentences = displayText.split(/[.!?]/);
                            if (sentences.length > 1) {
                                displayText = sentences.slice(0, -1).join('.') + '.';
                                console.log('🔪 Truncated to last complete sentence:', displayText);
                            }
                        }
                        
                        // IDENTITY REVELATION SAFETY: If we detect truth/lie mentions, truncate aggressively
                        if (stopSeq.includes('truth') || stopSeq.includes('lie') || stopSeq.includes('honest') || stopSeq.includes('deceive')) {
                            console.log('🚨 IDENTITY REVELATION DETECTED - Applying aggressive truncation');
                            // Find the last complete sentence before the identity revelation
                            const sentences = displayText.split(/[.!?]/);
                            if (sentences.length > 1) {
                                displayText = sentences.slice(0, -1).join('.') + '.';
                                console.log('🔪 Truncated identity revelation to last complete sentence:', displayText);
                            }
                            // Let the AI handle it naturally - no fallback responses
                        }
                        break;
                    }
                }
                
                // EXTRA SAFETY: Check if the response ends with problematic patterns
                const problematicEndings = [' no', ' yes', ' No', ' Yes', 'no', 'yes'];
                for (const ending of problematicEndings) {
                    if (displayText.endsWith(ending)) {
                        console.log('🚨 PROBLEMATIC ENDING DETECTED:', ending);
                        displayText = displayText.substring(0, displayText.length - ending.length).trim();
                        // Let the AI handle it naturally - no fallback responses
                        console.log('🔪 Cleaned response:', displayText);
                        break;
                    }
                }
                
                // Update display with potentially truncated text
                fullResponse = displayText;
                previousText = currentText; // Update for next comparison
                
                // Call the streaming callback if provided
                if (streamingCallbacks?.onToken) {
                    streamingCallbacks.onToken(newTokenText, fullResponse);
                }
                
                // Note: wllama doesn't seem to respect return false, so we just truncate display
            }
        });
        
        } catch (error) {
            // Check if this is a batch assertion error or context cache error
            if (error.message && (error.message.includes('llama_batch size exceeded') || error.message.includes('abort signal') || error.message.includes('Running out of context cache'))) {
                console.log('[NPCDialogueService] 🔄 Critical error detected, force reinitializing...');
                
                // Force complete reinitialization for batch assertion errors
                try {
                    await this.forceReinitialize();
                    console.log('[NPCDialogueService] ✅ Model reinitialized successfully');
                    
                    // Retry the generation with reset context - ULTRA CONSERVATIVE parameters
                    console.log('[NPCDialogueService] 🔄 Retrying generation after context reset...');
                    await this.wllama.createCompletion(fullPrompt, {
                        nPredict: 15,        // Allow complete short sentences
                        max_tokens: 15,      // Allow complete short sentences
                        temperature: 0.4,    // Slightly more creative for natural responses
                        topP: 0.8,          // Balanced variety in word choice
                        topK: 30,           // Focused options for natural speech
                        repeatPenalty: 1.1, // Prevent repetition
                        repeat_penalty: 1.1, // Alternative parameter name for repeat penalty
                        nBatch: 64,
                        onNewToken: (token, piece, currentText) => {
                            const newTokenText = currentText.substring(previousText.length);
                            console.log('🔥 New token (retry):', token, 'newText:', `"${newTokenText}"`, 'fullText:', `"${currentText}"`);
                            
                            if (isFirstToken) {
                                if (streamingCallbacks?.onFirstToken) {
                                    streamingCallbacks.onFirstToken();
                                }
                                fullResponse = '';
                                isFirstToken = false;
                                console.log('✅ First token (retry) - stopped thinking animation');
                            }
                            
                            let displayText = currentText;
                            let shouldStop = false;
                            
                            // ULTRA AGGRESSIVE: Check every character for player dialogue patterns (retry)
                            for (const stopSeq of stopSequences) {
                                if (currentText.includes(stopSeq)) {
                                    console.log('🛑 Stop sequence detected (retry):', stopSeq);
                                    const stopIndex = currentText.indexOf(stopSeq);
                                    displayText = currentText.substring(0, stopIndex).trim();
                                    shouldStop = true;
                                    
                                    // ADDITIONAL SAFETY: If we detect player dialogue, also check for sentence completion
                                    if (stopSeq.includes('PLAYER:') || stopSeq.includes('You:') || stopSeq.includes('Player:')) {
                                        console.log('🚨 PLAYER DIALOGUE DETECTED (retry) - Applying aggressive truncation');
                                        // Find the last complete sentence before the player dialogue
                                        const sentences = displayText.split(/[.!?]/);
                                        if (sentences.length > 1) {
                                            displayText = sentences.slice(0, -1).join('.') + '.';
                                            console.log('🔪 Truncated to last complete sentence (retry):', displayText);
                                        }
                                    }
                                    break;
                                }
                            }
                            
                            // EXTRA SAFETY: Check if the response ends with problematic patterns (retry)
                            const problematicEndings = [' no', ' yes', ' No', ' Yes', 'no', 'yes'];
                            for (const ending of problematicEndings) {
                                if (displayText.endsWith(ending)) {
                                    console.log('🚨 PROBLEMATIC ENDING DETECTED (retry):', ending);
                                    displayText = displayText.substring(0, displayText.length - ending.length).trim();
                                    // Let the AI handle it naturally - no fallback responses
                                    console.log('🔪 Cleaned response (retry):', displayText);
                                    break;
                                }
                            }
                            
                            fullResponse = displayText;
                            previousText = currentText;
                            
                            if (streamingCallbacks?.onToken) {
                                streamingCallbacks.onToken(newTokenText, fullResponse);
                            }
                        }
                    });
                    
                } catch (resetError) {
                    console.error('[NPCDialogueService] Failed to reset context or retry:', resetError);
                    throw resetError;
                }
            } else {
                // Re-throw other errors
                throw error;
            }
        }
        
        const cleanResponse = fullResponse.trim();
        
        // Call onComplete callback if provided
        if (streamingCallbacks?.onComplete) {
            streamingCallbacks.onComplete(cleanResponse);
        }
        
        // Update conversation history
        this.addToHistory(npcId, 'PLAYER', userMessage);
        this.addToHistory(npcId, npcData.name, cleanResponse);
        
        return cleanResponse;
    }
    
    /**
     * Reset the model context to free up cache
     * Safe to do since we manage conversation history in the prompt
     */
    async resetModelContext() {
        if (!this.wllama) {
            throw new Error('Wllama not initialized');
        }
        
        console.log('[NPCDialogueService] 🔄 Resetting model context cache...');
        
        try {
            // Clear the model's internal context cache
            // This is safe because we maintain conversation history in our prompt system
            
            // Try different wllama reset methods
            if (this.wllama.samplingReset) {
                await this.wllama.samplingReset();
                console.log('[NPCDialogueService] ✅ Used samplingReset()');
            } else if (this.wllama.reset) {
                await this.wllama.reset();
                console.log('[NPCDialogueService] ✅ Used reset()');
            } else if (this.wllama.clearContext) {
                await this.wllama.clearContext();
                console.log('[NPCDialogueService] ✅ Used clearContext()');
            } else {
                // If no reset method exists, we can still continue - the error handling will work
                console.log('[NPCDialogueService] ⚠️ No reset method found, but conversation history is preserved in prompt');
            }
            
            console.log('[NPCDialogueService] ✅ Model context reset complete');
        } catch (error) {
            console.error('[NPCDialogueService] Error resetting model context:', error);
            // Don't throw the error - we can still retry with the same context
            console.log('[NPCDialogueService] 💡 Context reset failed, but retrying anyway since history is in prompt');
        }
    }
    
    /**
     * Add message to conversation history
     */
    addToHistory(npcId, sender, message) {
        if (!this.conversationHistories.has(npcId)) {
            this.conversationHistories.set(npcId, []);
        }
        
        const history = this.conversationHistories.get(npcId);
        history.push({ sender, message });
        
        // Keep history minimal (last 2 messages only)
        if (history.length > 2) {
            history.splice(0, history.length - 2);
        }
    }
    
    /**
     * Get conversation history for an NPC
     */
    getConversationHistory(npcId) {
        return this.conversationHistories.get(npcId) || [];
    }
    
    /**
     * Clear conversation history for an NPC
     */
    clearHistory(npcId) {
        this.conversationHistories.delete(npcId);
    }
    
    /**
     * Clear all conversation histories
     */
    clearAllHistories() {
        this.conversationHistories.clear();
    }
}

// Create singleton instance
export const npcDialogueService = new NPCDialogueService();