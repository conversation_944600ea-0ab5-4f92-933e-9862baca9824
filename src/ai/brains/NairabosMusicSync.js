/**
 * Nairabos Music Sync System
 * Simple but precise beat-detection system for music-reactive boss attacks
 * Uses existing BossMusicAnalyzer for real-time analysis
 */

export class NairabosMusicSync {
    constructor(audioManager, bossController) {
        this.audioManager = audioManager;
        this.bossController = bossController;
        this.isActive = false;
        
        // Beat detection settings
        this.beatThreshold = 1.4; // Minimum beat strength to trigger attack
        this.lastBeatTime = 0;
        this.minBeatInterval = 300; // Minimum 300ms between beat-triggered attacks
        
        // Intensity tracking
        this.currentIntensity = 0;
        this.intensityThreshold = 0.6; // Trigger special attacks above this intensity
        this.lastIntensityTime = 0;
        
        // Music feature tracking
        this.lastFeatureTime = 0;
        this.featureCooldown = 2000; // 2 seconds between feature-triggered attacks
        
        // Phase-based beat scaling
        this.beatScaling = {
            1: 0.7, // Phase 1: Only respond to stronger beats (easier)
            2: 0.9, // Phase 2: Respond to most beats
            3: 1.2  // Phase 3: Respond to all beats, even weak ones (more frequent)
        };
        
        console.log('[NairabosMusicSync] Initialized simple beat-detection system');
    }
    
    /**
     * Start music synchronization
     */
    start() {
        if (!this.audioManager) {
            console.warn('[NairabosMusicSync] No audio manager available');
            return;
        }
        
        this.isActive = true;
        
        // Set up beat detection callback
        if (this.audioManager.musicAnalyzer && this.audioManager.musicAnalyzer.setBeatCallback) {
            this.audioManager.musicAnalyzer.setBeatCallback((beatStrength, confidence) => {
                this.onBeatDetected(beatStrength, confidence);
            });
        }
        
        // Set up intensity change callback
        if (this.audioManager.musicAnalyzer && this.audioManager.musicAnalyzer.setIntensityCallback) {
            this.audioManager.musicAnalyzer.setIntensityCallback((intensity, deltaIntensity) => {
                this.onIntensityChange(intensity, deltaIntensity);
            });
        }
        
        // Set up musical feature callbacks
        if (this.audioManager.musicAnalyzer && this.audioManager.musicAnalyzer.setFeatureCallback) {
            this.audioManager.musicAnalyzer.setFeatureCallback((features) => {
                this.onMusicalFeatures(features);
            });
        }
        
        console.log('[NairabosMusicSync] Music sync started');
    }
    
    /**
     * Stop music synchronization
     */
    stop() {
        this.isActive = false;
        
        // Clear callbacks
        if (this.audioManager.musicAnalyzer) {
            if (this.audioManager.musicAnalyzer.setBeatCallback) {
                this.audioManager.musicAnalyzer.setBeatCallback(null);
            }
            if (this.audioManager.musicAnalyzer.setIntensityCallback) {
                this.audioManager.musicAnalyzer.setIntensityCallback(null);
            }
            if (this.audioManager.musicAnalyzer.setFeatureCallback) {
                this.audioManager.musicAnalyzer.setFeatureCallback(null);
            }
        }
        
        console.log('[NairabosMusicSync] Music sync stopped');
    }
    
    /**
     * Handle beat detection
     */
    onBeatDetected(beatStrength, confidence) {
        if (!this.isActive || !this.bossController) return;
        
        const currentTime = performance.now();
        const timeSinceLastBeat = currentTime - this.lastBeatTime;
        
        // Apply phase-based scaling to beat threshold
        const currentPhase = this.bossController.currentPhase || 1;
        const scaledThreshold = this.beatThreshold * this.beatScaling[currentPhase];
        
        // Check if beat is strong enough and enough time has passed
        if (beatStrength >= scaledThreshold && timeSinceLastBeat >= this.minBeatInterval) {
            // Don't attack if shielded
            if (this.bossController.isShielded) return;
            
            console.log(`[NairabosMusicSync] Beat detected! Strength: ${beatStrength.toFixed(2)}, Confidence: ${confidence.toFixed(2)}`);
            
            // Calculate intensity based on beat strength and confidence
            const musicIntensity = Math.min(1.0, (beatStrength * confidence) / 2.0);
            
            // Trigger attack pattern
            this.bossController.triggerAttackPattern(musicIntensity);
            this.lastBeatTime = currentTime;
        }
    }
    
    /**
     * Handle intensity changes (buildups, drops, etc.)
     */
    onIntensityChange(intensity, deltaIntensity) {
        if (!this.isActive || !this.bossController) return;
        
        this.currentIntensity = intensity;
        const currentTime = performance.now();
        
        // Trigger special attacks on significant intensity changes
        if (Math.abs(deltaIntensity) > 0.3 && currentTime - this.lastIntensityTime > 1000) {
            if (this.bossController.isShielded) return;
            
            if (deltaIntensity > 0.3) {
                // Intensity spike (drop/climax) - trigger powerful attack
                console.log(`[NairabosMusicSync] Intensity spike detected! ${intensity.toFixed(2)} (+${deltaIntensity.toFixed(2)})`);
                this.triggerIntensityAttack('spike', intensity);
            } else if (deltaIntensity < -0.3) {
                // Intensity drop (buildup end) - trigger defensive pattern
                console.log(`[NairabosMusicSync] Intensity drop detected! ${intensity.toFixed(2)} (${deltaIntensity.toFixed(2)})`);
                this.triggerIntensityAttack('drop', intensity);
            }
            
            this.lastIntensityTime = currentTime;
        }
    }
    
    /**
     * Handle musical features (arpeggios, staccato, etc.)
     */
    onMusicalFeatures(features) {
        if (!this.isActive || !this.bossController) return;
        
        const currentTime = performance.now();
        if (currentTime - this.lastFeatureTime < this.featureCooldown) return;
        if (this.bossController.isShielded) return;
        
        // Trigger specific patterns for musical features
        if (features.arpeggio && features.arpeggio.confidence > 0.7) {
            console.log(`[NairabosMusicSync] Arpeggio detected! Triggering spiral pattern`);
            this.triggerFeatureAttack('arpeggio', features.arpeggio.confidence);
            this.lastFeatureTime = currentTime;
        } else if (features.staccato && features.staccato.detected) {
            console.log(`[NairabosMusicSync] Staccato detected! Triggering burst pattern`);
            this.triggerFeatureAttack('staccato', 0.8);
            this.lastFeatureTime = currentTime;
        } else if (features.buildUp && features.buildUp.detected) {
            console.log(`[NairabosMusicSync] Buildup detected! Preparing intense attack`);
            this.triggerFeatureAttack('buildup', features.buildUp.confidence || 0.9);
            this.lastFeatureTime = currentTime;
        }
    }
    
    /**
     * Trigger attack based on intensity changes
     */
    triggerIntensityAttack(type, intensity) {
        if (!this.bossController.attackPatterns) return;
        
        const currentPhase = this.bossController.currentPhase || 1;
        
        try {
            switch (type) {
                case 'spike':
                    // Intensity spike - trigger powerful directed attack
                    if (currentPhase >= 2) {
                        // Use more powerful patterns in later phases
                        const patterns = currentPhase === 3 ? ['hellburst_aimed', 'desperation_barrage'] : ['predictive_shots', 'area_denial'];
                        const pattern = patterns[Math.floor(Math.random() * patterns.length)];
                        console.log(`[NairabosMusicSync] Intensity spike attack: ${pattern}`);
                        this.bossController.attackPatterns.triggerPattern(currentPhase, intensity);
                    } else {
                        // Phase 1 - use gentler spike attacks
                        this.bossController.attackPatterns.triggerPattern(currentPhase, Math.min(0.6, intensity));
                    }
                    break;
                    
                case 'drop':
                    // Intensity drop - trigger area control
                    console.log(`[NairabosMusicSync] Intensity drop attack`);
                    this.bossController.attackPatterns.triggerPattern(currentPhase, intensity * 0.8);
                    break;
            }
        } catch (error) {
            console.error('[NairabosMusicSync] Error triggering intensity attack:', error);
        }
    }
    
    /**
     * Trigger attack based on musical features
     */
    triggerFeatureAttack(feature, confidence) {
        if (!this.bossController.attackPatterns) return;
        
        const currentPhase = this.bossController.currentPhase || 1;
        
        try {
            switch (feature) {
                case 'arpeggio':
                    // Arpeggios get spiral/rotating patterns
                    console.log(`[NairabosMusicSync] Arpeggio feature attack`);
                    if (currentPhase >= 3) {
                        this.bossController.attackPatterns.spiralDeath(this.bossController.enemy.position, confidence);
                    } else if (currentPhase >= 2) {
                        this.bossController.attackPatterns.rotatingBurst(this.bossController.enemy.position, confidence);
                    } else {
                        this.bossController.attackPatterns.gentleSpiral(this.bossController.enemy.position, confidence * 0.7);
                    }
                    break;
                    
                case 'staccato':
                    // Staccato gets burst patterns
                    console.log(`[NairabosMusicSync] Staccato feature attack`);
                    this.bossController.attackPatterns.triggerPattern(currentPhase, confidence);
                    break;
                    
                case 'buildup':
                    // Buildups trigger escalating patterns
                    console.log(`[NairabosMusicSync] Buildup feature attack`);
                    this.bossController.attackPatterns.triggerPattern(currentPhase, Math.min(1.0, confidence * 1.2));
                    break;
            }
        } catch (error) {
            console.error('[NairabosMusicSync] Error triggering feature attack:', error);
        }
    }
    
    /**
     * Get current music sync status
     */
    getStatus() {
        return {
            isActive: this.isActive,
            currentIntensity: this.currentIntensity,
            lastBeatTime: this.lastBeatTime,
            hasAudioManager: !!this.audioManager,
            hasMusicAnalyzer: !!(this.audioManager && this.audioManager.musicAnalyzer)
        };
    }
    
    /**
     * Update beat detection sensitivity for current phase
     */
    updatePhaseSettings(phase) {
        // Adjust beat detection based on phase
        switch (phase) {
            case 1:
                this.beatThreshold = 1.6; // Higher threshold - only strong beats
                this.minBeatInterval = 400; // Slower response
                break;
            case 2:
                this.beatThreshold = 1.2; // Medium threshold
                this.minBeatInterval = 300; // Medium response
                break;
            case 3:
                this.beatThreshold = 0.9; // Lower threshold - more responsive
                this.minBeatInterval = 200; // Faster response
                break;
        }
        
        console.log(`[NairabosMusicSync] Updated settings for Phase ${phase}: threshold=${this.beatThreshold}, interval=${this.minBeatInterval}ms`);
    }
}