/**
 * SHADOW TORMENT LASER SYSTEM
 * Creates ominous void energy warnings before shadow torment beam attacks
 * Manages shadow beam rendering with reality distortion effects
 * THEMATIC: Purple void energy beams fitting "The Fallen Tormentor"
 */
import * as THREE from 'three';

export class LaserWarningSystem {
    constructor(scene, dungeonHandler) {
        this.scene = scene;
        this.dungeonHandler = dungeonHandler;
        this.activeWarnings = [];
        this.activeLasers = [];
        this.bossEntity = null; // Reference to boss for position following
        
        // ENHANCED WARNING: Multi-layered dramatic warning line
        this.warningMaterial = new THREE.LineBasicMaterial({
            color: 0x8a3c8a, // Bright purple void
            transparent: true,
            opacity: 0.9,
            linewidth: 3 // Thicker line for more presence
        });
        
        // Secondary warning material for inner glow
        this.warningGlowMaterial = new THREE.LineBasicMaterial({
            color: 0xff44ff, // Bright magenta glow
            transparent: true,
            opacity: 0.6,
            linewidth: 2
        });
        
        // TORMENT BEAM: Void energy beam (visible purple with reality distortion)
        this.laserMaterial = new THREE.MeshBasicMaterial({
            color: 0x5a3c5a, // Visible purple void (no longer dark)
            transparent: true,
            opacity: 0.95,
            emissive: 0x6a2c6a, // Purple void glow
            emissiveIntensity: 2.5 // Intense shadow energy
        });
        
        // ENHANCED: Void tear material for maximum intensity beams
        this.voidTearMaterial = new THREE.MeshBasicMaterial({
            color: 0x6a2c6a, // Visible purple void (no longer black)
            transparent: true,
            opacity: 0.98,
            emissive: 0x8a3c8a, // Bright purple void
            emissiveIntensity: 3.0 // Reality-tearing intensity
        });
        
        console.log('[LaserWarningSystem] Initialized');
    }
    
    /**
     * Set boss entity reference for position following
     */
    setBossEntity(bossEntity) {
        this.bossEntity = bossEntity;
        console.log('[LaserWarningSystem] Boss entity reference set for position following');
    }
    
    /**
     * Create shadow warning that shows where torment beam will tear reality
     * THEMATIC: Purple shadow creeping across ground
     */
    createWarning(startPos, endPos, duration = 2000, warningText = 'VOID ENERGY BUILDING!') {
        console.log('[LaserWarningSystem] 💀 Shadow energy building - void tear incoming!');
        
        // FIXED: Use same position calculation as fireLaser for alignment
        let finalStartPos = startPos.clone();
        let finalEndPos = endPos.clone();
        
        if (this.bossEntity && this.bossEntity.position) {
            // Always use boss center as warning origin (same as laser)
            finalStartPos = this.bossEntity.position.clone();
            finalStartPos.y += 2.0; // Offset to boss center height
            
            // Maintain original direction and length from boss center
            const originalDirection = new THREE.Vector3().subVectors(endPos, startPos).normalize();
            const originalLength = startPos.distanceTo(endPos);
            finalEndPos = finalStartPos.clone().add(originalDirection.multiplyScalar(originalLength));
        }
        
        // ENHANCED: Create dramatic warning with multiple visual elements using final positions
        const warningGroup = new THREE.Group();
        
        // ENHANCED: Chain animation dots using correct positions
        const chainDots = this.createChainAnimationDots(finalStartPos, finalEndPos, duration);
        warningGroup.add(chainDots);
        
        // 2. Add pulsing energy particles along warning line using correct positions
        const warningParticles = this.createWarningParticles(finalStartPos, finalEndPos);
        warningGroup.add(warningParticles);
        
        // 3. DISABLED: Void energy aura effect (was creating unwanted black cylinders)
        // const auraEffect = this.createVoidAura(finalStartPos, finalEndPos);
        // warningGroup.add(auraEffect);
        
        // Add pulsing effect and boss following
        const warning = {
            chainDots: chainDots,
            group: warningGroup,
            particles: warningParticles,
            aura: null, // Disabled - was creating black cylinders
            startTime: performance.now(),
            duration: duration,
            startPos: finalStartPos.clone(), // Use final positions for alignment
            endPos: finalEndPos.clone(),
            originalStartPos: startPos.clone(), // Keep original for calculations
            originalEndPos: endPos.clone(),
            originalDirection: new THREE.Vector3().subVectors(endPos, startPos).normalize(),
            originalLength: startPos.distanceTo(endPos),
            active: true,
            pulsePhase: 0,
            followsBoss: true
        };
        
        this.scene.add(warningGroup);
        this.activeWarnings.push(warning);
        
        // Screen shake for emphasis
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.1, 200);
        }
        
        return warning;
    }
    
    /**
     * Fire shadow torment beam that tears through reality
     * THEMATIC: Purple void energy beam with reality distortion
     */
    fireLaser(startPos, endPos, duration = 1000, damage = 2) {
        console.log('[LaserWarningSystem] 🌊 FIRING VOID TORMENT BEAM - reality tearing!');
        
        // Calculate laser direction and length
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        // ENHANCED: Create dramatic laser material with multiple layers
        const isHighDamage = damage > 2;
        const beamRadius = isHighDamage ? 0.3 : 0.25; // Slightly thinner but still imposing
        
        // Create enhanced laser material with inner/outer glow
        const enhancedLaserMaterial = new THREE.MeshBasicMaterial({
            color: isHighDamage ? 0x6a2c6a : 0x5a3c5a, // Visible purple core instead of black
            transparent: true,
            opacity: 0.95,
            emissive: isHighDamage ? 0x8a3c8a : 0x6a2c6a, // Brighter purple glow
            emissiveIntensity: isHighDamage ? 4.0 : 3.0 // Much more intense
        });
        
        // Create shadow torment beam geometry (cylinder with more segments for smoother void effect)
        const beamGeometry = new THREE.CylinderGeometry(beamRadius, beamRadius, length, 16);
        const laserBeam = new THREE.Mesh(beamGeometry, enhancedLaserMaterial);
        
        // DRAMATIC ENHANCEMENT: Add outer glow layer for more impressive visual
        const glowRadius = beamRadius * 1.8;
        const glowGeometry = new THREE.CylinderGeometry(glowRadius, glowRadius, length, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: isHighDamage ? 0x6a3c6a : 0x5a2c5a, // Lighter purple glow colors
            transparent: true,
            opacity: 0.3,
            emissive: isHighDamage ? 0x6a2c6a : 0x4a1a5a,
            emissiveIntensity: 1.5,
            side: THREE.DoubleSide
        });
        const glowLayer = new THREE.Mesh(glowGeometry, glowMaterial);
        laserBeam.add(glowLayer);
        
        // ENHANCED: Create pulsing energy particle system around laser beam
        const particleSystem = this.createLaserParticles(length, beamRadius, isHighDamage);
        laserBeam.add(particleSystem);
        
        // SHADOW ENHANCEMENT: Mark as void energy for special effects
        laserBeam.userData = {
            isLaserBeam: true,
            isVoidEnergy: true,
            shadowTorment: true,
            realityTear: isHighDamage
        };
        
        // SIMPLIFIED: Use boss center directly if boss entity exists
        let finalStartPos = startPos.clone();
        let finalEndPos = endPos.clone();
        
        if (this.bossEntity && this.bossEntity.position) {
            // Always use boss center as laser origin
            finalStartPos = this.bossEntity.position.clone();
            finalStartPos.y += 2.0; // Offset to boss center height
            
            // Maintain original direction and length from boss center
            const originalDirection = new THREE.Vector3().subVectors(endPos, startPos).normalize();
            const originalLength = startPos.distanceTo(endPos);
            finalEndPos = finalStartPos.clone().add(originalDirection.multiplyScalar(originalLength));
        }
        
        // Position and orient the laser with final positions
        const midPoint = new THREE.Vector3().addVectors(finalStartPos, finalEndPos).multiplyScalar(0.5);
        laserBeam.position.copy(midPoint);
        
        // Align laser with final direction
        const finalDirection = new THREE.Vector3().subVectors(finalEndPos, finalStartPos).normalize();
        const up = new THREE.Vector3(0, 1, 0);
        const quaternion = new THREE.Quaternion().setFromUnitVectors(up, finalDirection);
        laserBeam.quaternion.copy(quaternion);
        
        const laser = {
            beam: laserBeam,
            startTime: performance.now(),
            duration: duration,
            startPos: finalStartPos.clone(),
            endPos: finalEndPos.clone(),
            originalDirection: new THREE.Vector3().subVectors(endPos, startPos).normalize(),
            originalLength: startPos.distanceTo(endPos),
            damage: damage,
            active: true,
            hasDealtDamage: false,
            followsBoss: true // Enable boss following
        };
        
        this.scene.add(laserBeam);
        this.activeLasers.push(laser);
        
        // Immediate damage check
        this.checkLaserCollision(laser);
        
        // Screen shake for laser impact
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.3, 300);
        }
        
        // Anime-style lens distortion for laser (Dragon Ball Z style)
        if (this.dungeonHandler.sceneManager && 
            this.dungeonHandler.sceneManager.crtEffect && 
            this.dungeonHandler.sceneManager.crtEffect.manager) {
            this.dungeonHandler.sceneManager.crtEffect.manager.triggerLaserDistortion();
        }
        
        return laser;
    }
    
    /**
     * Update warning and laser systems
     */
    update(deltaTime) {
        const currentTime = performance.now();
        
        // Update warnings
        this.activeWarnings.forEach(warning => {
            if (!warning.active) return;
            
            // CRITICAL: Check if boss is still alive - deactivate warning if boss is dead
            if (!this.bossEntity || !this.bossEntity.parent || this.bossEntity.userData.health <= 0) {
                console.log('[LaserWarningSystem] 🚫 WARNING DEACTIVATED: Boss is dead');
                warning.active = false;
                if (warning.group && warning.group.parent) {
                    this.scene.remove(warning.group);
                }
                return;
            }
            
            const elapsed = currentTime - warning.startTime;
            const progress = elapsed / warning.duration;
            
            if (progress >= 1.0) {
                // Warning expired - remove it
                warning.active = false;
                if (warning.group && warning.group.parent) {
                    this.scene.remove(warning.group);
                }
            } else {
                // BOSS FOLLOWING: Update warning position to follow boss movement
                if (warning.followsBoss && this.bossEntity) {
                    // Update start position to current boss center
                    warning.startPos.copy(this.bossEntity.position);
                    warning.startPos.y += 2.0; // Boss center height
                    
                    // Update end position maintaining original direction and length
                    warning.endPos.copy(warning.startPos).add(
                        warning.originalDirection.clone().multiplyScalar(warning.originalLength)
                    );
                    
                    // Update chain dots to follow boss movement
                    this.updateChainDots(warning.chainDots, warning.startPos, warning.endPos);
                    
                    // Update warning particles and aura to follow boss
                    this.updateWarningEffects(warning, warning.startPos, warning.endPos);
                }
                
                // Update pulsing effect for chain dots and warning effects
                warning.pulsePhase += deltaTime * 0.008;
                const pulse = Math.sin(warning.pulsePhase) * 0.3 + 0.7;
                
                // Update chain dots opacity and color intensity
                if (warning.chainDots && warning.chainDots.children) {
                    warning.chainDots.children.forEach(dot => {
                        if (dot.userData && dot.userData.isChainDot && dot.userData.isVisible && dot.material) {
                            // Apply pulsing to visible dots
                            const basePulse = pulse * (1.0 - progress * 0.3);
                            dot.material.opacity = Math.min(dot.userData.maxOpacity, basePulse);
                            
                            // Increase void intensity as shadow energy builds
                            const voidIntensity = progress * 2;
                            const purpleIntensity = Math.min(1, voidIntensity);
                            // Shift from red to bright purple as energy builds
                            const redComponent = 1.0 - purpleIntensity * 0.7; // Start red, fade to purple
                            const blueComponent = purpleIntensity * 0.8; // Increase blue for purple
                            
                            // Safe material property access
                            if (dot.material.color) {
                                dot.material.color.setRGB(redComponent, 0.1, blueComponent);
                            }
                            if (dot.material.emissive) {
                                dot.material.emissive.setRGB(redComponent, 0.1, blueComponent);
                            }
                        }
                    });
                }
            }
        });
        
        // Update lasers
        this.activeLasers.forEach(laser => {
            if (!laser.active) return;
            
            // CRITICAL: Check if boss is still alive - deactivate laser if boss is dead
            if (!this.bossEntity || !this.bossEntity.parent || this.bossEntity.userData.health <= 0) {
                console.log('[LaserWarningSystem] 🚫 LASER DEACTIVATED: Boss is dead');
                laser.active = false;
                if (laser.beam.parent) {
                    this.scene.remove(laser.beam);
                }
                return;
            }
            
            const elapsed = currentTime - laser.startTime;
            const progress = elapsed / laser.duration;
            
            if (progress >= 1.0) {
                // Laser expired - remove it
                laser.active = false;
                if (laser.beam.parent) {
                    this.scene.remove(laser.beam);
                }
            } else {
                // SIMPLIFIED BOSS FOLLOWING: Always use boss center as origin
                if (laser.followsBoss && this.bossEntity) {
                    // Update start position to current boss center
                    laser.startPos.copy(this.bossEntity.position);
                    laser.startPos.y += 2.0; // Boss center height
                    
                    // Update end position maintaining original direction and length
                    laser.endPos.copy(laser.startPos).add(
                        laser.originalDirection.clone().multiplyScalar(laser.originalLength)
                    );
                    
                    // Update beam position and orientation
                    const midPoint = new THREE.Vector3().addVectors(laser.startPos, laser.endPos).multiplyScalar(0.5);
                    laser.beam.position.copy(midPoint);
                    
                    const up = new THREE.Vector3(0, 1, 0);
                    const quaternion = new THREE.Quaternion().setFromUnitVectors(up, laser.originalDirection);
                    laser.beam.quaternion.copy(quaternion);
                }
                
                // Update laser intensity (bright start, fade out)
                const intensity = Math.max(0.3, 1.0 - progress);
                laser.beam.material.opacity = intensity;
                laser.beam.material.emissiveIntensity = intensity * 2;
                
                // ENHANCED: Animate laser particles for pulsing energy effect
                this.updateLaserParticles(laser.beam, deltaTime, intensity);
                
                // Continue checking collision with updated positions
                if (!laser.hasDealtDamage) {
                    this.checkLaserCollision(laser);
                }
            }
        });
        
        // Clean up inactive warnings and lasers
        this.activeWarnings = this.activeWarnings.filter(w => w.active);
        this.activeLasers = this.activeLasers.filter(l => l.active);
    }
    
    /**
     * Check if laser hits player
     */
    checkLaserCollision(laser) {
        if (!this.dungeonHandler.player || laser.hasDealtDamage) return;
        
        const playerPos = this.dungeonHandler.player.position;
        const playerRadius = 0.5; // Player collision radius
        
        // Calculate distance from player to laser line
        const laserDir = new THREE.Vector3().subVectors(laser.endPos, laser.startPos).normalize();
        const toPlayer = new THREE.Vector3().subVectors(playerPos, laser.startPos);
        
        // Project player position onto laser line
        const projectedDistance = toPlayer.dot(laserDir);
        const projectedPoint = laser.startPos.clone().add(laserDir.clone().multiplyScalar(projectedDistance));
        
        // Check if projection is within laser bounds
        const laserLength = laser.startPos.distanceTo(laser.endPos);
        if (projectedDistance >= 0 && projectedDistance <= laserLength) {
            // Check distance from player to projected point
            const distanceToLaser = playerPos.distanceTo(projectedPoint);
            
            if (distanceToLaser <= playerRadius + 0.2) { // 0.2 = laser beam radius
                // Player hit by laser!
                console.log('[LaserWarningSystem] Laser hit player!');
                laser.hasDealtDamage = true;
                
                // Apply damage
                if (this.dungeonHandler.damagePlayer) {
                    this.dungeonHandler.damagePlayer(laser.damage);
                }
                
                // Visual feedback
                this.addCameraShake(0.5, 500);
            }
        }
    }
    
    /**
     * Add camera shake effect
     */
    addCameraShake(intensity, duration) {
        if (!this.dungeonHandler.camera) return;
        
        const camera = this.dungeonHandler.camera;
        const originalPosition = camera.position.clone();
        const shakeStart = performance.now();
        
        const shakeEffect = () => {
            const elapsed = performance.now() - shakeStart;
            if (elapsed >= duration) {
                camera.position.copy(originalPosition);
                return;
            }
            
            const progress = elapsed / duration;
            const currentIntensity = intensity * (1 - progress);
            
            const shakeX = (Math.random() - 0.5) * currentIntensity;
            const shakeY = (Math.random() - 0.5) * currentIntensity;
            const shakeZ = (Math.random() - 0.5) * currentIntensity;
            
            camera.position.copy(originalPosition);
            camera.position.x += shakeX;
            camera.position.y += shakeY;
            camera.position.z += shakeZ;
            
            requestAnimationFrame(shakeEffect);
        };
        
        shakeEffect();
    }
    
    /**
     * Clean up all warnings and lasers - ENHANCED CLEANUP
     */
    cleanup() {
        console.log(`[LaserWarningSystem] 🧹 CLEANUP: Removing ${this.activeWarnings.length} warnings and ${this.activeLasers.length} laser beams`);
        
        // Remove all warning groups (contains chain dots, particles, aura)
        this.activeWarnings.forEach((warning, index) => {
            try {
                if (warning.group && warning.group.parent) {
                    this.scene.remove(warning.group);
                    console.log(`[LaserWarningSystem] ✅ Removed warning group ${index + 1}`);
                }
                // Force deactivate
                warning.active = false;
            } catch (error) {
                console.warn(`[LaserWarningSystem] Error removing warning group ${index + 1}:`, error);
            }
        });
        
        // Remove all laser beams
        this.activeLasers.forEach((laser, index) => {
            try {
                if (laser.beam && laser.beam.parent) {
                    this.scene.remove(laser.beam);
                    console.log(`[LaserWarningSystem] ✅ Removed laser beam ${index + 1}`);
                }
                // Force deactivate
                laser.active = false;
            } catch (error) {
                console.warn(`[LaserWarningSystem] Error removing laser beam ${index + 1}:`, error);
            }
        });
        
        // Aggressive cleanup - scan scene for any remaining laser-related objects
        const laserObjects = [];
        this.scene.traverse(child => {
            if (child.userData && (
                child.userData.isLaserBeam ||
                child.userData.isVoidEnergy ||
                child.userData.shadowTorment ||
                child.userData.realityTear
            )) {
                laserObjects.push(child);
            }
        });
        
        if (laserObjects.length > 0) {
            console.log(`[LaserWarningSystem] 🚨 AGGRESSIVE CLEANUP: Found ${laserObjects.length} orphaned laser objects`);
            laserObjects.forEach((obj, index) => {
                try {
                    if (obj.parent) {
                        obj.parent.remove(obj);
                        console.log(`[LaserWarningSystem] ✅ Removed orphaned laser object ${index + 1}`);
                    }
                } catch (error) {
                    console.warn(`[LaserWarningSystem] Error removing orphaned object ${index + 1}:`, error);
                }
            });
        }
        
        this.activeWarnings = [];
        this.activeLasers = [];
        this.bossEntity = null; // Clear boss reference
        
        console.log('[LaserWarningSystem] ✅ CLEANUP COMPLETE: All laser warnings, beams, and orphaned objects removed');
    }

    /**
     * Create animated energy particles around laser beam
     * THEMATIC: Pulsing void energy particles that spiral around the beam
     */
    createLaserParticles(beamLength, beamRadius, isHighDamage = false) {
        const particleCount = isHighDamage ? 80 : 60; // More particles for high damage beams
        const particleGeometry = new THREE.BufferGeometry();
        
        // Create particle positions along the beam
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        
        for (let i = 0; i < particleCount; i++) {
            // Position particles in a spiral around the beam
            const t = i / particleCount; // 0 to 1 along beam length
            const angle = t * Math.PI * 8 + Math.random() * Math.PI; // Spiral with randomness
            const spiralRadius = (beamRadius + 0.1) + Math.sin(t * Math.PI * 4) * 0.15; // Pulsing radius
            
            // Position along beam (Y is beam direction)
            positions[i * 3] = Math.cos(angle) * spiralRadius;
            positions[i * 3 + 1] = (t - 0.5) * beamLength; // -length/2 to +length/2
            positions[i * 3 + 2] = Math.sin(angle) * spiralRadius;
            
            // Purple void energy colors with intensity variation
            const intensity = 0.5 + Math.random() * 0.5;
            if (isHighDamage) {
                // Brighter purple for high damage
                colors[i * 3] = 0.8 * intensity;     // Red
                colors[i * 3 + 1] = 0.3 * intensity; // Green  
                colors[i * 3 + 2] = 0.9 * intensity; // Blue
            } else {
                // Standard purple
                colors[i * 3] = 0.6 * intensity;     // Red
                colors[i * 3 + 1] = 0.2 * intensity; // Green
                colors[i * 3 + 2] = 0.7 * intensity; // Blue
            }
            
            // Varying particle sizes
            sizes[i] = 0.02 + Math.random() * 0.03;
        }
        
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        // Create particle material with void energy glow
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending, // Glowing effect
            depthWrite: false
        });
        
        const particles = new THREE.Points(particleGeometry, particleMaterial);
        
        // Add animation data for pulsing effect
        particles.userData = {
            isLaserParticles: true,
            animationPhase: Math.random() * Math.PI * 2,
            originalPositions: positions.slice(), // Store original positions for animation
            spiralSpeed: isHighDamage ? 0.008 : 0.005 // Faster spiral for high damage
        };
        
        return particles;
    }
    
    /**
     * Update laser particle animations for pulsing energy effect
     * THEMATIC: Spiraling void energy that pulses with laser intensity
     */
    updateLaserParticles(laserBeam, deltaTime, intensity) {
        // Find particle systems in the laser beam
        laserBeam.traverse(child => {
            if (child.userData && child.userData.isLaserParticles) {
                const particles = child;
                const userData = particles.userData;
                
                // Update animation phase
                userData.animationPhase += deltaTime * userData.spiralSpeed;
                
                // Get particle position attribute
                const positions = particles.geometry.attributes.position;
                const originalPositions = userData.originalPositions;
                const particleCount = positions.count;
                
                // Animate particles with spiraling and pulsing motion
                for (let i = 0; i < particleCount; i++) {
                    const i3 = i * 3;
                    const t = i / particleCount;
                    
                    // Original spiral position
                    const originalX = originalPositions[i3];
                    const originalZ = originalPositions[i3 + 2];
                    const originalY = originalPositions[i3 + 1];
                    
                    // Add spiraling motion
                    const spiralAngle = userData.animationPhase + t * Math.PI * 8;
                    const spiralRadius = Math.sqrt(originalX * originalX + originalZ * originalZ);
                    
                    // Pulsing effect based on laser intensity and time
                    const pulsePhase = userData.animationPhase * 3 + t * Math.PI * 2;
                    const pulse = Math.sin(pulsePhase) * 0.1 * intensity;
                    
                    // Update positions with spiral and pulse
                    positions.array[i3] = Math.cos(spiralAngle) * (spiralRadius + pulse);
                    positions.array[i3 + 1] = originalY; // Keep Y position along beam
                    positions.array[i3 + 2] = Math.sin(spiralAngle) * (spiralRadius + pulse);
                }
                
                // Mark position attribute as needing update
                positions.needsUpdate = true;
                
                // Update particle opacity based on laser intensity
                particles.material.opacity = 0.6 * intensity + 0.2;
            }
        });
    }
    
    /**
     * Create warning particles along laser path for dramatic effect
     * THEMATIC: Void energy crackling along future laser path
     */
    createWarningParticles(startPos, endPos) {
        const particleCount = 40;
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        const particleGeometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            const t = i / (particleCount - 1); // 0 to 1 along path
            const basePos = startPos.clone().add(direction.clone().multiplyScalar(t * length));
            
            // Add random spread around the line
            const spread = 0.3;
            positions[i * 3] = basePos.x + (Math.random() - 0.5) * spread;
            positions[i * 3 + 1] = basePos.y + (Math.random() - 0.5) * spread;
            positions[i * 3 + 2] = basePos.z + (Math.random() - 0.5) * spread;
            
            // Crackling void energy colors
            const intensity = 0.7 + Math.random() * 0.3;
            colors[i * 3] = 0.5 * intensity;     // Red
            colors[i * 3 + 1] = 0.1 * intensity; // Green
            colors[i * 3 + 2] = 0.7 * intensity; // Blue
        }
        
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.08,
            vertexColors: true,
            transparent: true,
            opacity: 0.7,
            blending: THREE.AdditiveBlending
        });
        
        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.userData = {
            isWarningParticles: true,
            animationPhase: 0,
            originalPositions: positions.slice()
        };
        
        return particles;
    }
    
    /**
     * Create void energy aura around warning line
     * THEMATIC: Dark energy field showing laser danger zone
     */
    createVoidAura(startPos, endPos) {
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        // Create tube geometry for aura
        const curve = new THREE.LineCurve3(startPos, endPos);
        const tubeGeometry = new THREE.TubeGeometry(curve, 20, 0.4, 8, false);
        
        const auraMaterial = new THREE.MeshBasicMaterial({
            color: 0x6a2c6a, // Changed from dark 0x2d1b3d to visible purple
            transparent: true,
            opacity: 0.15, // Reduced opacity to make it more subtle
            emissive: 0x4a1a5a,
            emissiveIntensity: 0.8, // Increased emissive to make it glow purple
            side: THREE.DoubleSide
        });
        
        const aura = new THREE.Mesh(tubeGeometry, auraMaterial);
        aura.userData = {
            isWarningAura: true,
            pulsePhase: Math.random() * Math.PI * 2
        };
        
        return aura;
    }
    
    /**
     * Create chain animation dots that appear from boss outward
     * THEMATIC: Red glowing dots that animate in sequence from boss body
     */
    createChainAnimationDots(startPos, endPos, duration) {
        const dotsGroup = new THREE.Group();
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        const dotCount = 12; // Number of dots in chain
        const dotSize = 0.12;
        
        for (let i = 0; i < dotCount; i++) {
            const t = i / (dotCount - 1); // 0 to 1 along path
            const dotPos = startPos.clone().add(direction.clone().multiplyScalar(t * length));
            
            // Create glowing red dot
            const dotGeometry = new THREE.SphereGeometry(dotSize, 8, 6);
            const dotMaterial = new THREE.MeshBasicMaterial({
                color: 0xff3333, // Bright red
                transparent: true,
                opacity: 0.0, // Start invisible
                emissive: 0xff3333,
                emissiveIntensity: 3.0
            });
            
            const dot = new THREE.Mesh(dotGeometry, dotMaterial);
            dot.position.copy(dotPos);
            
            // Animation data for chain effect
            dot.userData = {
                isChainDot: true,
                chainIndex: i,
                basePosition: dotPos.clone(),
                animationDelay: i * 150, // 150ms delay between dots
                animationPhase: 0,
                maxOpacity: 0.9,
                isVisible: false
            };
            
            dotsGroup.add(dot);
        }
        
        dotsGroup.userData = {
            isChainDots: true,
            startTime: performance.now(),
            duration: duration,
            animationPhase: 0
        };
        
        return dotsGroup;
    }
    
    /**
     * Update chain dots animation - dots appear in sequence from boss outward
     * THEMATIC: Red energy building up along laser path
     */
    updateChainDots(dotsGroup, startPos, endPos) {
        if (!dotsGroup || !dotsGroup.userData.isChainDots) return;
        
        const currentTime = performance.now();
        const elapsed = currentTime - dotsGroup.userData.startTime;
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        dotsGroup.userData.animationPhase += 0.04;
        const globalPhase = dotsGroup.userData.animationPhase;
        
        dotsGroup.children.forEach((dot, index) => {
            if (dot.userData.isChainDot) {
                // Update position along new path
                const t = index / (dotsGroup.children.length - 1);
                const newPos = startPos.clone().add(direction.clone().multiplyScalar(t * length));
                dot.position.copy(newPos);
                dot.userData.basePosition.copy(newPos);
                
                // Chain animation - each dot appears with delay
                const animationStart = dot.userData.animationDelay;
                const dotElapsed = elapsed - animationStart;
                
                if (dotElapsed >= 0) {
                    dot.userData.isVisible = true;
                    
                    // Fade in animation
                    const fadeInDuration = 300; // 300ms fade in
                    const fadeProgress = Math.min(1.0, dotElapsed / fadeInDuration);
                    dot.material.opacity = dot.userData.maxOpacity * fadeProgress;
                    
                    // Pulsing glow effect
                    const pulsePhase = globalPhase * 2 + index * 0.5;
                    const pulse = Math.sin(pulsePhase) * 0.3 + 0.7;
                    dot.material.emissiveIntensity = 2.0 + pulse * 1.5;
                    
                    // Subtle scale pulsing
                    const scalePulse = Math.sin(pulsePhase * 1.5) * 0.2 + 1.0;
                    dot.scale.setScalar(scalePulse);
                } else {
                    // Not yet time for this dot to appear
                    dot.material.opacity = 0.0;
                    dot.userData.isVisible = false;
                }
            }
        });
    }
    
    /**
     * Update warning particle and aura effects
     * THEMATIC: Animate crackling void energy and pulsing aura
     */
    updateWarningEffects(warning, startPos, endPos) {
        if (!warning.particles) return; // Only check particles since aura is disabled
        
        // Update warning particles along new path
        const direction = new THREE.Vector3().subVectors(endPos, startPos);
        const length = direction.length();
        direction.normalize();
        
        const positions = warning.particles.geometry.attributes.position;
        const particleCount = positions.count;
        
        warning.particles.userData.animationPhase += 0.02;
        const animPhase = warning.particles.userData.animationPhase;
        
        for (let i = 0; i < particleCount; i++) {
            const t = i / (particleCount - 1);
            const basePos = startPos.clone().add(direction.clone().multiplyScalar(t * length));
            
            // Add crackling motion and random spread
            const crackle = Math.sin(animPhase * 3 + i * 0.5) * 0.1;
            const spread = 0.3;
            
            positions.array[i * 3] = basePos.x + (Math.random() - 0.5) * spread + crackle;
            positions.array[i * 3 + 1] = basePos.y + (Math.random() - 0.5) * spread;
            positions.array[i * 3 + 2] = basePos.z + (Math.random() - 0.5) * spread + crackle;
        }
        
        positions.needsUpdate = true;
        
        // DISABLED: Aura pulsing effect (aura is now disabled)
        // warning.aura.userData.pulsePhase += 0.02;
        // const pulse = Math.sin(warning.aura.userData.pulsePhase) * 0.2 + 0.8;
        // warning.aura.material.emissiveIntensity = 0.3 * pulse;
        // warning.aura.scale.set(pulse, pulse, pulse);
    }
}