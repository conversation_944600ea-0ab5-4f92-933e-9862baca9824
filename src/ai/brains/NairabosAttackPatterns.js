/**
 * Nairabos Attack Patterns
 * Advanced bullet hell patterns with organic movement and grouping systems
 * Features floating swaying clusters, rotating pairs, and shadow minions
 */
import * as THREE from 'three';
import { LaserWarningSystem } from './LaserWarningSystem.js';
import { ArenaHazardManager } from './ArenaHazardManager.js';

/**
 * Advanced Bullet Group - Handles organic movement patterns
 */
class BulletGroup {
    constructor(origin, dungeon<PERSON><PERSON><PERSON>, owner) {
        this.origin = origin.clone();
        this.dungeonHandler = dungeonHandler;
        this.owner = owner;
        this.bullets = [];
        this.startTime = performance.now();
        this.active = true;
        
        // Movement parameters
        this.swayAmplitude = 2.0;
        this.swayFrequency = 0.003;
        this.rotationSpeed = 0.002;
        this.pulseAmplitude = 0.5;
        this.pulseFrequency = 0.004;
        
        // Group center movement
        this.groupVelocity = new THREE.Vector3();
        this.groupPosition = this.origin.clone();
    }
    
    addBullet(projectileData, localPosition, phase = 0) {
        const worldPosition = this.groupPosition.clone().add(localPosition);
        
        const projectile = this.dungeonHandler.spawnProjectile(worldPosition, new THREE.Vector3(0, 0, 1), projectileData);
        
        if (projectile) {
            // Add visual enhancements to the projectile
            if (projectile.material) {
                projectile.material.emissiveIntensity = 0.5;
            }
            
            this.bullets.push({
                projectile: projectile,
                localPosition: localPosition.clone(),
                initialPosition: localPosition.clone(),
                phase: phase,
                rotationPhase: Math.random() * Math.PI * 2,
                glowIntensity: 1.5 + Math.random() * 0.5
            });
        }
        return projectile;
    }
    
    update(deltaTime) {
        if (!this.active) return;
        
        // OPTIMIZATION: Update counter for reduced calculations
        this.updateCounter = (this.updateCounter || 0) + 1;
        const skipExpensiveUpdates = this.updateCounter % 2 !== 0; // Skip every other frame
        
        const currentTime = performance.now();
        const elapsedTime = currentTime - this.startTime;
        
        // Update group center position
        this.groupPosition.add(this.groupVelocity.clone().multiplyScalar(deltaTime));
        
        // OPTIMIZATION: Pre-calculate shared values
        const shouldUpdateVisuals = !skipExpensiveUpdates && this.bullets.length < 20;
        const glowPulse = shouldUpdateVisuals ? Math.sin(elapsedTime * 0.005) * 0.3 + 0.7 : 1.0;
        
        // Update each bullet's position with organic movement
        this.bullets.forEach((bullet, index) => {
            if (!bullet.projectile || !bullet.projectile.parent) {
                // Bullet was destroyed, mark for removal
                bullet.destroyed = true;
                return;
            }
            
            // OPTIMIZATION: Simplified movement for high projectile counts
            if (this.bullets.length > 15 || skipExpensiveUpdates) {
                // Simple linear movement
                const simpleOffset = bullet.initialPosition.clone();
                const newPosition = this.groupPosition.clone().add(simpleOffset);
                bullet.projectile.position.copy(newPosition);
            } else {
                // Full organic movement only for smaller groups
                // Sine wave sway
                const swayX = Math.sin(elapsedTime * this.swayFrequency + bullet.phase) * this.swayAmplitude;
                const swayZ = Math.cos(elapsedTime * this.swayFrequency * 0.7 + bullet.phase) * this.swayAmplitude * 0.5;
                
                // Rotation around local center
                const rotationAngle = elapsedTime * this.rotationSpeed + bullet.rotationPhase;
                const rotatedX = bullet.initialPosition.x * Math.cos(rotationAngle) - bullet.initialPosition.z * Math.sin(rotationAngle);
                const rotatedZ = bullet.initialPosition.x * Math.sin(rotationAngle) + bullet.initialPosition.z * Math.cos(rotationAngle);
                
                // Pulsing effect
                const pulseScale = 1.0 + Math.sin(elapsedTime * this.pulseFrequency + bullet.phase) * this.pulseAmplitude;
                
                // Combine all effects
                const newPosition = this.groupPosition.clone();
                newPosition.x += (rotatedX + swayX) * pulseScale;
                newPosition.y += bullet.initialPosition.y;
                newPosition.z += (rotatedZ + swayZ) * pulseScale;
                
                bullet.projectile.position.copy(newPosition);
                bullet.localPosition.set(
                    (rotatedX + swayX) * pulseScale,
                    bullet.initialPosition.y,
                    (rotatedZ + swayZ) * pulseScale
                );
            }
            
            // OPTIMIZATION: Update visual effects less frequently
            if (shouldUpdateVisuals && bullet.projectile.material) {
                bullet.projectile.material.emissiveIntensity = bullet.glowIntensity * glowPulse;
            }
        });
        
        // Clean up destroyed bullets
        this.bullets = this.bullets.filter(bullet => !bullet.destroyed);
        
        // Deactivate if no bullets left
        if (this.bullets.length === 0) {
            this.active = false;
        }
    }
    
    setGroupVelocity(velocity) {
        this.groupVelocity.copy(velocity);
    }
    
    setSwayParameters(amplitude, frequency) {
        this.swayAmplitude = amplitude;
        this.swayFrequency = frequency;
    }
    
    setRotationSpeed(speed) {
        this.rotationSpeed = speed;
    }
    
    setPulseParameters(amplitude, frequency) {
        this.pulseAmplitude = amplitude;
        this.pulseFrequency = frequency;
    }
    
    cleanup() {
        // Clean up all bullets
        this.bullets.forEach(bullet => {
            if (bullet.projectile && bullet.projectile.parent) {
                bullet.projectile.parent.remove(bullet.projectile);
            }
        });
        this.bullets = [];
        this.active = false;
    }
}

export class NairabosAttackPatterns {
    constructor(enemy, dungeonHandler, player) {
        this.enemy = enemy;
        this.dungeonHandler = dungeonHandler;
        this.player = player;
        
        // Pattern timing
        this.lastPatternTime = 0;
        this.activeProjectiles = [];
        this.currentPhase = 1;
        
        // Desperation mode (final 10% health)
        this.isDesperationMode = false;
        this.desperationMultiplier = 1.1; // EMERGENCY FIX: Reduced from 1.2x to 1.1x bullet density
        
        // EMERGENCY: Hard projectile limit per attack to prevent crashes
        this.maxProjectilesPerAttack = 30;
        this.currentAttackProjectileCount = 0;
        
        // Advanced pattern management
        this.activeBulletGroups = [];
        this.shadowMinions = [];
        this.windUpIndicators = [];
        
        // Laser warning system
        this.laserSystem = new LaserWarningSystem(dungeonHandler.scene, dungeonHandler);
        this.laserSystem.setBossEntity(enemy); // Set boss reference for position following
        
        // Arena hazard system
        this.hazardManager = new ArenaHazardManager(dungeonHandler.scene, dungeonHandler);
        this.hazardManager.setBossEntity(enemy); // Set boss reference for state checking
        
        // THEMATIC PROJECTILE SYSTEM: Shadow Tormentor themed projectiles
        this.projectileTypes = ['torment_orb', 'bone_spear', 'void_tear', 'shadow_chain', 'screamer_skulls', 'soul_thread'];
        this.currentProjectileIndex = 0;
        
        // Phase-based pattern pools (enhanced with skull patterns for visual clarity)
        this.phase1Patterns = ['aimed_triple', 'cross_pattern', 'floating_swaying_circles', 'bullet_corridor']; // 4 easy patterns
        this.phase2Patterns = ['predictive_shots', 'rotating_bullet_pairs', 'sweeping_death_wave', 'cross_laser_beams', 'floor_danger_zones', 'skull_wave', 'shadow_hands']; // 7 medium patterns
        this.phase3Patterns = ['spiral_death', 'shadow_minion_assault', 'rotating_laser_sweep', 'arena_shrinking', 'skull_ring', 'skull_spiral']; // 6 hard patterns
        
        // OPTIMIZATION: Combination attack projectile tracking for performance
        this.combinationProjectileCount = undefined; // Set to 0 during combination attacks, undefined otherwise
        
        // CRITICAL: Track all timeouts for proper cleanup
        this._activeTimeouts = [];
        
        // OPTIMIZATION: Smart attack queue system
        this.attackQueue = [];
        this.frameProjectileBudget = 8; // REDUCED: Max projectiles spawned per frame
        this.queueProcessing = false;
        this.lastQueueProcess = 0;
        
        // PERFORMANCE: Track spawns this frame
        this.spawnsThisFrame = 0;
        this.lastFrameTime = 0;
        
        console.log('[NairabosAttackPatterns] Initialized with SHADOW TORMENTOR themed projectiles and enhanced visual effects');
    }
    
    /**
     * Clean up old projectiles that are likely expired
     * @private
     */
    _cleanupOldProjectiles() {
        const now = performance.now();
        let cleaned = 0;
        
        // Remove projectiles older than 5 seconds from tracking
        this.activeProjectiles = this.activeProjectiles.filter(projectile => {
            if (!projectile || !projectile.userData || !projectile.userData.spawnTime) {
                cleaned++;
                return false;
            }
            
            const age = now - projectile.userData.spawnTime;
            if (age > 5000) { // 5 seconds
                cleaned++;
                return false;
            }
            
            return true;
        });
        
        if (cleaned > 0) {
            console.log(`[NairabosAttackPatterns] Cleaned up ${cleaned} old projectile references`);
        }
    }

    /**
     * Get thematic visual enhancements for each shadow/torment projectile type
     * CRITICAL: Creates unique visual identity for "The Fallen Tormentor"
     * @param {string} projectileType - The thematic projectile type
     * @returns {Object} Enhanced visual properties
     */
    _getShadowTormentEnhancements(projectileType) {
        switch (projectileType) {
            case 'torment_orb':
                return {
                    // Black spheres with swirling purple void energy
                    primaryColor: 0x1a0d1a, // Dark purple-black
                    secondaryColor: 0x4a1a4a, // Purple void
                    emissiveColor: 0x6a2c6a, // Purple glow
                    emissiveIntensity: 1.2,
                    scale: 1.3, // Larger for intimidation
                    glowRadius: 2.0,
                    trailColor: 0x2d1b3d, // Dark purple trail
                    visualEffects: ['void_swirl', 'screaming_faces', 'dark_tendrils'],
                    soundEffect: 'torment_whisper',
                    description: 'Orbs of pure torment energy'
                };

            case 'bone_spear':
                return {
                    // Jagged bone projectiles with dried blood stains
                    primaryColor: 0xf5f5dc, // Bone white
                    secondaryColor: 0x722f37, // Dried blood
                    emissiveColor: 0x8b4b6b, // Scar pink glow
                    emissiveIntensity: 0.8,
                    scale: 1.4, // Long and menacing
                    rotationSpeed: 3.0, // Spinning end-over-end
                    trailColor: 0x4a2c2a, // Decay brown trail
                    visualEffects: ['bone_fragments', 'blood_drips', 'spinning_motion'],
                    soundEffect: 'bone_crack',
                    description: 'Bones of tormented victims'
                };

            case 'void_tear':
                return {
                    // Rips in reality showing pure darkness
                    primaryColor: 0x000000, // Void black
                    secondaryColor: 0x301934, // Shadow purple
                    emissiveColor: 0x4a1a5a, // Deep purple
                    emissiveIntensity: 2.0, // Very bright void energy
                    scale: 1.2,
                    trailColor: 0x1a0a1a, // Reality distortion
                    visualEffects: ['reality_distortion', 'void_crack', 'space_tear'],
                    soundEffect: 'reality_rip',
                    specialEffect: 'realityDistortion',
                    description: 'Tears in the fabric of reality'
                };

            case 'shadow_chain':
                return {
                    // Dark metal chains with glowing purple links
                    primaryColor: 0x2a2a2a, // Dark metal
                    secondaryColor: 0x5a2d5a, // Purple glow
                    emissiveColor: 0x7a3d7a, // Bright purple links
                    emissiveIntensity: 1.5,
                    scale: 1.1,
                    length: 2.0, // Extended chain links
                    trailColor: 0x3a1a3a, // Metal spark trail
                    visualEffects: ['chain_links', 'metal_sparks', 'whip_motion'],
                    soundEffect: 'chain_clank',
                    description: 'Chains that bound tortured souls'
                };

            case 'screamer_skulls':
                return {
                    // Bone white skull projectiles (ENHANCED)
                    primaryColor: 0xf5f5dc, // Bone white
                    secondaryColor: 0xff4500, // Demon orange eyes
                    emissiveColor: 0xffd700, // Hell yellow glow
                    emissiveIntensity: 1.8, // Bright glowing eyes
                    scale: 1.5, // Larger skulls
                    trailColor: 0x666666, // Ghostly trail
                    visualEffects: ['glowing_eyes', 'jaw_movement', 'ghostly_wail'],
                    soundEffect: 'soul_scream',
                    description: 'Screaming skulls of the damned'
                };

            case 'soul_thread':
                return {
                    // Glowing purple/white threads of captured souls
                    primaryColor: 0xe6e6fa, // Lavender (soul essence)
                    secondaryColor: 0x9370db, // Medium purple
                    emissiveColor: 0xdda0dd, // Plum glow
                    emissiveIntensity: 1.6,
                    scale: 0.8, // Thin but long
                    length: 3.0, // Extended thread
                    trailColor: 0xc8a2c8, // Soul essence trail
                    visualEffects: ['soul_faces', 'ethereal_wisp', 'spirit_energy'],
                    soundEffect: 'soul_whisper',
                    description: 'Threads woven from tortured souls'
                };

            default:
                // Fallback to torment_orb
                return this._getShadowTormentEnhancements('torment_orb');
        }
    }
    
    /**
     * Tracked setTimeout to ensure proper cleanup
     * CRITICAL: All timeouts must use this method for proper boss death cleanup
     */
    _setTimeout(callback, delay) {
        const timeoutId = setTimeout(() => {
            // Remove from tracking array when executed
            const index = this._activeTimeouts.indexOf(timeoutId);
            if (index > -1) {
                this._activeTimeouts.splice(index, 1);
            }
            
            // CRITICAL: Check if boss is still alive before executing callback
            if (!this.enemy || !this.enemy.parent || this.enemy.userData.health <= 0) {
                console.log('[NairabosAttackPatterns] 🚫 TIMEOUT BLOCKED: Boss is dead, skipping attack execution');
                return;
            }
            
            callback();
        }, delay);
        
        this._activeTimeouts.push(timeoutId);
        return timeoutId;
    }
    
    /**
     * Set desperation mode (called from boss controller)
     */
    setDesperationMode(isActive) {
        this.isDesperationMode = isActive;
        console.log(`[NairabosAttackPatterns] Desperation mode ${isActive ? 'ACTIVATED' : 'DEACTIVATED'} - ${this.desperationMultiplier}x bullet density`);
    }
    
    /**
     * Get patterns for current phase
     */
    getPatternsForPhase(phase) {
        switch (phase) {
            case 1: return this.phase1Patterns;
            case 2: return this.phase2Patterns;
            case 3: return this.phase3Patterns;
            default: return this.phase1Patterns;
        }
    }
    
    /**
     * Trigger a pattern based on phase and difficulty
     */
    triggerPattern(phase, intensity = 0.5, desperationMode = false) {
        // OPTIMIZATION: Throttle attack frequency in desperation mode
        if (this.isDesperationMode) {
            const now = performance.now();
            const timeSinceLastAttack = now - (this.lastAttackTriggerTime || 0);
            const minAttackInterval = 600; // INCREASED: Minimum 600ms between attacks in desperation
            
            if (timeSinceLastAttack < minAttackInterval) {
                console.log('[NairabosAttackPatterns] Attack throttled in desperation mode');
                return;
            }
            this.lastAttackTriggerTime = now;
        }
        
        this.currentPhase = phase; // Track current phase for projectile selection
        
        // EMERGENCY: Reset per-attack projectile counter for each new attack
        this.currentAttackProjectileCount = 0;
        
        // Update desperation mode if provided
        if (desperationMode !== this.isDesperationMode) {
            this.setDesperationMode(desperationMode);
        }
        
        // Apply desperation mode effects to intensity
        let finalIntensity = intensity;
        if (this.isDesperationMode) {
            finalIntensity = Math.min(2.0, intensity * this.desperationMultiplier); // Allow higher intensity in desperation
            console.log(`[NairabosAttackPatterns] DESPERATION MODE: Intensity boosted from ${intensity.toFixed(2)} to ${finalIntensity.toFixed(2)}`);
        }
        
        // DESPERATION NOVA: Ultimate attack for final 5% health
        if (this.isDesperationMode && this.enemy && this.enemy.userData.aiBrain) {
            const healthPercent = this.enemy.userData.aiBrain.currentHealth / this.enemy.userData.aiBrain.maxHealth;
            if (healthPercent <= 0.05 && Math.random() < 0.3) { // 30% chance at 5% health
                console.log(`[NairabosAttackPatterns] 🌟 DESPERATION NOVA TRIGGERED - THE FINAL GAMBIT!`);
                this.desperationNova(finalIntensity);
                return;
            }
        }
        
        // Phase 3: Pattern Overlapping System (reduced chance in desperation mode)
        const combinationChance = this.isDesperationMode ? 0.3 : 0.2; // REDUCED: 30% chance in desperation, 20% normal
        if (phase === 3 && Math.random() < combinationChance) {
            console.log(`[NairabosAttackPatterns] Triggering COMBINATION ATTACK (Phase ${phase}, Intensity ${finalIntensity.toFixed(2)}, Desperation: ${this.isDesperationMode})`);
            this.triggerCombinationAttack(finalIntensity);
            return;
        }
        
        const patterns = this.getPatternsForPhase(phase);
        const patternName = patterns[Math.floor(Math.random() * patterns.length)];
        
        console.log(`[NairabosAttackPatterns] Triggering ${patternName} (Phase ${phase}, Intensity ${finalIntensity.toFixed(2)}, Desperation: ${this.isDesperationMode})`);
        
        // Get player position for targeting
        const playerPos = this.getPlayerPosition();
        const bossPos = this.enemy.position.clone();
        // Don't get projectile type here - each pattern will cycle when it spawns
        
        // Execute pattern
        switch (patternName) {
            case 'aimed_triple':
                this.aimTripleShot(bossPos, playerPos, finalIntensity);
                break;
            case 'cross_pattern':
                this.crossPattern(bossPos, playerPos, finalIntensity);
                break;
            case 'gentle_spiral':
                this.gentleSpiral(bossPos, finalIntensity);
                break;
            case 'predictive_shots':
                this.predictiveShots(bossPos, playerPos, finalIntensity);
                break;
            case 'area_denial':
                this.areaDenial(bossPos, playerPos, finalIntensity);
                break;
            case 'rotating_burst':
                this.rotatingBurst(bossPos, playerPos, finalIntensity);
                break;
            case 'player_trap':
                this.playerTrap(bossPos, playerPos, finalIntensity);
                break;
            case 'hellburst_aimed':
                this.hellburstAimed(bossPos, playerPos, finalIntensity);
                break;
            case 'spiral_death':
                this.spiralDeath(bossPos, finalIntensity);
                break;
            case 'bullet_wall':
                this.bulletWall(bossPos, playerPos, finalIntensity);
                break;
            case 'desperation_barrage':
                this.desperationBarrage(bossPos, playerPos, finalIntensity);
                break;
            case 'floating_swaying_circles':
                this.floatingSwayingCircles(bossPos, playerPos, finalIntensity);
                break;
            case 'rotating_bullet_pairs':
                this.rotatingBulletPairs(bossPos, playerPos, finalIntensity);
                break;
            case 'circular_area_attack':
                this.circularAreaAttack(bossPos, playerPos, finalIntensity);
                break;
            case 'shadow_minion_assault':
                this.shadowMinionAssault(bossPos, playerPos, finalIntensity);
                break;
            case 'bullet_corridor':
                this.bulletCorridor(bossPos, playerPos, finalIntensity);
                break;
            case 'sweeping_death_wave':
                this.sweepingDeathWave(bossPos, playerPos, finalIntensity);
                break;
            case 'zigzag_maze':
                this.zigzagMaze(bossPos, playerPos, finalIntensity);
                break;
            case 'single_laser_beam':
                this.singleLaserBeam(bossPos, playerPos, finalIntensity);
                break;
            case 'cross_laser_beams':
                this.crossLaserBeams(bossPos, playerPos, finalIntensity);
                break;
            case 'rotating_laser_sweep':
                this.rotatingLaserSweep(bossPos, playerPos, finalIntensity);
                break;
            case 'floor_danger_zones':
                this.floorDangerZones(bossPos, playerPos, finalIntensity);
                break;
            case 'arena_shrinking':
                this.arenaShrinking(bossPos, playerPos, finalIntensity);
                break;
            case 'skull_wave':
                this.skullWave(bossPos, playerPos, finalIntensity);
                break;
            case 'shadow_hands':
                this.shadowHands(bossPos, playerPos, finalIntensity);
                break;
            case 'skull_ring':
                this.skullRing(bossPos, playerPos, finalIntensity);
                break;
            case 'skull_spiral':
                this.skullSpiral(bossPos, playerPos, finalIntensity);
                break;
        }
    }
    
    /**
     * Process attack queue with frame budget
     * @private
     */
    _processAttackQueue() {
        if (this.attackQueue.length === 0 || this.queueProcessing) return;
        
        const now = performance.now();
        if (now - this.lastQueueProcess < 16) return; // Limit to ~60fps
        
        this.queueProcessing = true;
        let projectilesThisFrame = 0;
        
        // Process attacks until we hit frame budget
        while (this.attackQueue.length > 0 && projectilesThisFrame < this.frameProjectileBudget) {
            const attack = this.attackQueue[0];
            
            // Check if we can spawn this attack's projectiles
            if (projectilesThisFrame + attack.projectileCount <= this.frameProjectileBudget) {
                // Execute the attack
                attack.execute();
                projectilesThisFrame += attack.projectileCount;
                this.attackQueue.shift(); // Remove from queue
            } else {
                // Can't fit this attack in current frame, wait for next frame
                break;
            }
        }
        
        this.lastQueueProcess = now;
        this.queueProcessing = false;
    }
    
    /**
     * Queue an attack instead of immediate execution
     * @private
     */
    _queueAttack(attackFunction, projectileCount, priority = 0) {
        // Merge similar attacks if possible
        const existingIndex = this.attackQueue.findIndex(a => a.type === attackFunction.name);
        if (existingIndex !== -1 && this.attackQueue[existingIndex].mergeable) {
            // Merge by increasing projectile count slightly instead of duplicating
            this.attackQueue[existingIndex].projectileCount += Math.floor(projectileCount * 0.5);
            return;
        }
        
        this.attackQueue.push({
            execute: attackFunction,
            projectileCount: projectileCount,
            priority: priority,
            type: attackFunction.name,
            mergeable: true,
            timestamp: performance.now()
        });
        
        // Sort by priority (higher priority first)
        this.attackQueue.sort((a, b) => b.priority - a.priority);
    }
    
    /**
     * Update all active bullet groups (call this from boss controller)
     */
    update(deltaTime) {
        // Process attack queue first
        this._processAttackQueue();
        
        // OPTIMIZATION: Periodic cleanup of old projectiles
        if (this.updateCounter % 60 === 0) { // Every second at 60fps
            this._cleanupOldProjectiles();
        }
        
        // OPTIMIZATION: Throttle update frequency for non-critical systems
        this.updateCounter = (this.updateCounter || 0) + 1;
        
        // Update bullet groups every frame (critical for smooth movement)
        this.activeBulletGroups.forEach(group => group.update(deltaTime));
        this.activeBulletGroups = this.activeBulletGroups.filter(group => group.active);
        
        // OPTIMIZATION: Update shadow minions every 3rd frame (reduces ~60% of minion overhead)
        if (this.updateCounter % 3 === 0) {
            this.shadowMinions.forEach(minion => {
                if (minion.update) minion.update(deltaTime * 3); // Compensate for skipped frames
            });
            this.shadowMinions = this.shadowMinions.filter(minion => minion.active);
        }
        
        // Update wind-up indicators every 2nd frame
        if (this.updateCounter % 2 === 0) {
            this.windUpIndicators.forEach(indicator => {
                if (indicator.update) indicator.update(deltaTime * 2);
            });
            this.windUpIndicators = this.windUpIndicators.filter(indicator => indicator.active);
        }
        
        // Update laser warning system every frame (critical for player safety)
        if (this.laserSystem) {
            this.laserSystem.update(deltaTime);
        }
        
        // OPTIMIZATION: Update arena hazard system every 4th frame
        if (this.hazardManager && this.updateCounter % 4 === 0) {
            this.hazardManager.update(deltaTime * 4);
        }
    }
    
    /**
     * Get current player position with fallback
     */
    getPlayerPosition() {
        if (this.player && this.player.position) {
            return this.player.position.clone();
        }
        if (this.dungeonHandler.player && this.dungeonHandler.player.position) {
            return this.dungeonHandler.player.position.clone();
        }
        return new THREE.Vector3(0, 0, 0);
    }
    
    /**
     * Get current projectile type and cycle to next
     */
    getNextProjectileType() {
        const projectileType = this.projectileTypes[this.currentProjectileIndex];
        this.currentProjectileIndex = (this.currentProjectileIndex + 1) % this.projectileTypes.length;
        console.log(`[NairabosAttackPatterns] Using projectile type: ${projectileType}`);
        return projectileType;
    }
    
    /**
     * Get player velocity for prediction
     */
    getPlayerVelocity() {
        // Simple velocity estimation based on player controller
        if (this.dungeonHandler.playerController) {
            const controller = this.dungeonHandler.playerController;
            const velocity = new THREE.Vector3();
            
            // Estimate movement from input keys
            if (controller.keys) {
                if (controller.keys.forward) velocity.z -= 1;
                if (controller.keys.backward) velocity.z += 1;
                if (controller.keys.left) velocity.x -= 1;
                if (controller.keys.right) velocity.x += 1;
            }
            
            return velocity.normalize().multiplyScalar(3.0); // Assume 3 units/sec movement
        }
        return new THREE.Vector3(0, 0, 0);
    }
    
    /**
     * Predict where player will be in timeSeconds
     */
    predictPlayerPosition(timeSeconds = 0.5) {
        const currentPos = this.getPlayerPosition();
        const velocity = this.getPlayerVelocity();
        return currentPos.clone().add(velocity.clone().multiplyScalar(timeSeconds));
    }
    
    /**
     * Create smart targeting direction with spread options for dodgeability
     */
    createSmartDirection(bossPos, playerPos, options = {}) {
        const {
            prediction = 0.8,        // How far ahead to predict (seconds)
            accuracy = 0.9,          // 0.0 = random, 1.0 = perfect aim
            spreadAngle = 0.2,       // Maximum random spread in radians
            leadTarget = true        // Whether to lead the target
        } = options;
        
        let targetPos;
        if (leadTarget) {
            targetPos = this.predictPlayerPosition(prediction);
        } else {
            targetPos = playerPos.clone();
        }
        
        // Base direction toward target
        const baseDirection = new THREE.Vector3().subVectors(targetPos, bossPos).normalize();
        
        // Add inaccuracy for dodgeability
        if (accuracy < 1.0) {
            const inaccuracy = (1.0 - accuracy) * spreadAngle;
            const randomSpread = (Math.random() - 0.5) * inaccuracy;
            baseDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), randomSpread);
        }
        
        return baseDirection;
    }
    
    /**
     * Create spread pattern around player position
     */
    createSpreadPattern(bossPos, playerPos, count, options = {}) {
        const {
            prediction = 0.6,
            totalSpread = Math.PI / 3,  // 60 degree cone
            accuracy = 0.8
        } = options;
        
        const centerDirection = this.createSmartDirection(bossPos, playerPos, {
            prediction,
            accuracy: accuracy * 0.8  // Center shot less accurate
        });
        
        const directions = [];
        for (let i = 0; i < count; i++) {
            const spreadAngle = -totalSpread/2 + (i / Math.max(1, count - 1)) * totalSpread;
            const direction = centerDirection.clone();
            direction.applyAxisAngle(new THREE.Vector3(0, 1, 0), spreadAngle);
            directions.push(direction);
        }
        
        return directions;
    }
    
    /**
     * Check minimum spacing between spawn positions
     */
    validateSpawnPosition(newPos, minDistance = 1.0) {
        for (const existingProjectile of this.activeProjectiles) {
            if (existingProjectile && existingProjectile.position) {
                const distance = newPos.distanceTo(existingProjectile.position);
                if (distance < minDistance) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * Find safe spawn position with minimum spacing
     */
    findSafeSpawnPosition(basePos, minDistance = 1.0, maxAttempts = 5) {
        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            let testPos = basePos.clone();
            
            if (attempt > 0) {
                // Add small random offset for subsequent attempts
                const offset = new THREE.Vector3(
                    (Math.random() - 0.5) * 2.0,
                    (Math.random() - 0.5) * 1.0,
                    (Math.random() - 0.5) * 2.0
                );
                testPos.add(offset);
            }
            
            if (this.validateSpawnPosition(testPos, minDistance)) {
                return testPos;
            }
        }
        
        // If no safe position found, use base position
        return basePos;
    }
    
    /**
     * Add wind-up indicator for dramatic attack telegraphing
     */
    addWindUpIndicator(message, duration = 2000) {
        console.log(`[NairabosAttackPatterns] 💀 ${message}`);
        
        // Store the indicator for potential UI display
        const indicator = {
            message: message,
            startTime: performance.now(),
            duration: duration,
            active: true
        };
        
        this.windUpIndicators.push(indicator);
        
        // Clean up expired indicators
        this.windUpIndicators = this.windUpIndicators.filter(ind => 
            (performance.now() - ind.startTime) < ind.duration
        );
        
        // Limit active indicators to prevent spam
        if (this.windUpIndicators.length > 3) {
            this.windUpIndicators.shift(); // Remove oldest
        }
    }
    
    /**
     * Add camera shake effect for dramatic impact
     */
    addCameraShake(intensity = 0.1, duration = 500) {
        console.log(`[NairabosAttackPatterns] 📹 Camera shake: intensity ${intensity}, duration ${duration}ms`);
        
        // Try to access camera shake through dungeonHandler
        if (this.dungeonHandler && this.dungeonHandler.addCameraShake) {
            this.dungeonHandler.addCameraShake(intensity, duration);
        } else if (this.dungeonHandler && this.dungeonHandler.camera) {
            // Fallback: Direct camera manipulation if available
            const camera = this.dungeonHandler.camera;
            const originalPosition = camera.position.clone();
            
            // Store shake parameters for potential animation
            if (!camera.userData.shake) {
                camera.userData.shake = {
                    active: false,
                    intensity: 0,
                    duration: 0,
                    startTime: 0,
                    originalPosition: originalPosition
                };
            }
            
            // Set shake parameters
            camera.userData.shake.active = true;
            camera.userData.shake.intensity = intensity;
            camera.userData.shake.duration = duration;
            camera.userData.shake.startTime = performance.now();
            camera.userData.shake.originalPosition = originalPosition;
        } else {
            // Log warning if camera shake system not available
            console.warn('[NairabosAttackPatterns] Camera shake requested but no camera system available');
        }
    }
    
    // ===== PHASE 1 PATTERNS (Easy Introduction) =====
    
    /**
     * TORMENTOR'S JUDGMENT - Divine punishment with void energy buildup
     * ENHANCED: Was aimed triple shot, now a dramatic judgment with boss speech and void energy gathering
     */
    aimTripleShot(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] ⚖️ THE TORMENTOR PASSES JUDGMENT - void energy gathers for divine punishment!');
        
        // Dramatic buildup with boss speech and energy gathering
        this.addWindUpIndicator("⚖️ 'YOU ARE JUDGED... AND FOUND WANTING!' - VOID ENERGY GATHERING!", 2000);
        
        // Calculate judgment direction
        const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
        
        // THEMATIC: Force void_tear projectiles for maximum judgment effect
        const judgmentProjectileType = 'void_tear';
        
        // Enhanced judgment sequence - escalating punishment
        const judgmentPhases = 3; // Three phases of increasing severity
        const baseSpread = 0.15; // Tighter spread for precision
        
        // BOSS GLOWING: Make boss glow purple during aim for dramatic effect
        if (this.enemy) {
            console.log('[NairabosAttackPatterns] 💜 The Tormentor\'s eyes burn with purple void energy...');
            // Add visual glow effect indicator
            this._setTimeout(() => {
                console.log('[NairabosAttackPatterns] ✨ Void energy radiates from The Fallen Tormentor');
            }, 500);
        }
        
        for (let phase = 0; phase < judgmentPhases; phase++) {
            this._setTimeout(() => {
                const phaseIntensity = intensity + phase * 0.5; // Escalating intensity
                const spread = baseSpread + phase * 0.1; // Slightly wider spread each phase
                
                // Judgment speech for each phase
                const judgmentMessages = [
                    "⚖️ 'FIRST JUDGMENT: GUILTY OF DEFIANCE!'",
                    "⚖️ 'SECOND JUDGMENT: GUILTY OF RESISTANCE!'", 
                    "⚖️ 'FINAL JUDGMENT: GUILTY OF EXISTENCE!'"
                ];
                console.log(`[NairabosAttackPatterns] ${judgmentMessages[phase]}`);
                
                // Enhanced triple shot with void energy
                const angles = [-spread, 0, spread]; // Precise judgment spread
                angles.forEach((angle, shotIndex) => {
                    const shotDir = direction.clone();
                    shotDir.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);
                    
                    // Spawn from boss body position with slight height offset
                    const judgmentHeight = 0.5 + phase * 0.1; // Small height offset from boss body
                    const judgmentPos = bossPos.clone().add(new THREE.Vector3(0, judgmentHeight, 0));
                    
                    // Escalating speed and power
                    const judgmentSpeed = 7.0 + phaseIntensity * 2 + phase;
                    
                    // Add slight homing effect for divine precision
                    const homingDirection = shotDir.clone();
                    if (shotIndex === 1) { // Center shot has slight prediction
                        const predictedPos = this.predictPlayerPosition(0.3);
                        const predictiveDir = new THREE.Vector3().subVectors(predictedPos, judgmentPos).normalize();
                        homingDirection.lerp(predictiveDir, 0.3); // 30% prediction blend
                    }
                    
                    this.spawnProjectile(judgmentPos, homingDirection.normalize(), judgmentSpeed, judgmentProjectileType);
                });
                
                // Reality distortion at each judgment impact point
                this.createJudgmentDistortion(playerPos);
                
                // Screen shake intensifies with each phase
                if (this.dungeonHandler.camera) {
                    this.addCameraShake(0.1 + phase * 0.05, 400);
                }
                
            }, phase * 800); // Stagger phases for dramatic buildup
        }
        
        // FINAL JUDGMENT EFFECT: Reality distortion where judgment lands
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 🌀 "THE JUDGMENT IS RENDERED... REALITY BENDS TO MY WILL!"');
            if (this.dungeonHandler.camera) {
                this.addCameraShake(0.2, 1000); // Final impact distortion
            }
            
            // Final judgment effect removed - keeping distortion only for big laser charges
        }, 2500);
    }
    
    /**
     * Create reality distortion at judgment impact points
     */
    createJudgmentDistortion(targetPosition) {
        // Visual effect showing void energy distorting reality at impact points
        console.log('[NairabosAttackPatterns] 🌀 Void energy tears reality at judgment impact point');
        
        // Enhanced visual feedback for divine judgment
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.08, 300);
        }
    }
    
    /**
     * Pulse Wave Pattern - Expanding energy waves that travel outward
     */
    crossPattern(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Pulse Wave Attack');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('pulse_wave');
        }
        
        // OPTIMIZED: Smart wave distribution for visual impact with fewer projectiles
        const waveCount = 2 + Math.floor(intensity * 1); // OPTIMIZED: 2-3 waves (down from 3-5)
        const waveDelay = 250; // Slightly faster for intensity illusion
        
        for (let wave = 0; wave < waveCount; wave++) {
            this._setTimeout(() => {
                const group = new BulletGroup(bossPos.clone().add(new THREE.Vector3(0, 1, 0)), this.dungeonHandler, this.enemy);
                
                // Configure pulse wave
                group.swayAmplitude = 0; // No sway for clean waves
                group.rotationSpeed = 0.001; // Slight rotation
                group.pulseAmplitude = 0.8; // Strong pulsing
                group.pulseFrequency = 0.008;
                
                // OPTIMIZED: Strategic projectile distribution for visual density
                const projectileCount = 4 + wave * 1; // REDUCED: 4-6 projectiles (was 6-8)
                const baseRadius = 0.4 + wave * 0.4; // Larger radius spread for coverage
                const projectileType = 'shadow_bolt'; // Pure black bullet pattern
                
                // VISUAL ENHANCEMENT: Larger projectiles with enhanced effects
                const projectileScale = 1.2 + wave * 0.1; // Growing projectiles per wave
                const glowMultiplier = 1.3 + wave * 0.2; // Enhanced glow per wave
                
                for (let i = 0; i < projectileCount; i++) {
                    const angle = (i / projectileCount) * Math.PI * 2;
                    const localPos = new THREE.Vector3(
                        Math.cos(angle) * baseRadius,
                        Math.sin(angle * 3) * 0.3, // Slight vertical wave
                        Math.sin(angle) * baseRadius
                    );
                    
                    const projectileData = {
                        projectileType: projectileType,
                        projectileSpeed: 0, // Group handles movement
                        disableTrails: false,
                        type: 'nairabos',
                        health: 1,
                        isProjectile: true,
                        aiType: this.enemy.userData.aiType || 'flying',
                        owner: this.enemy,
                        // VISUAL ENHANCEMENT: Larger, glowing projectiles for visual impact
                        scale: projectileScale,
                        glowIntensity: glowMultiplier,
                        emissiveIntensity: 0.6 + wave * 0.2 // Brighter projectiles in outer waves
                    };
                    
                    group.addBullet(projectileData, localPos, angle);
                }
                
                // Set expansion velocity for pulse wave
                const expansionSpeed = 8.0 + intensity * 4 - wave * 1.5; // Inner waves faster
                group.groupVelocity.set(0, 0, 0); // Start stationary
                
                // Set each bullet to move outward
                group.bullets.forEach((bullet, i) => {
                    if (bullet.projectile) {
                        const angle = (i / projectileCount) * Math.PI * 2;
                        const expandDirection = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
                        
                        // Set projectile velocity directly
                        if (bullet.projectile.userData) {
                            bullet.projectile.userData.velocity = expandDirection.clone().multiplyScalar(expansionSpeed);
                        }
                    }
                });
                
                this.activeBulletGroups.push(group);
            }, wave * waveDelay);
        }
    }
    
    /**
     * TORMENT VORTEX - Swirling void portal with soul energy
     * ENHANCED: Was gentle spiral, now a terrifying vortex of souls
     */
    gentleSpiral(bossPos, intensity) {
        console.log('[NairabosAttackPatterns] 🌀 UNLEASHING TORMENT VORTEX - souls emerge from the void!');
        
        // Wind-up indicator for dramatic effect
        this.addWindUpIndicator("💀 TORMENT VORTEX FORMING - SOULS EMERGING FROM THE VOID!", 1500);
        
        // THEMATIC: Force void_tear projectiles for maximum void effect
        const voidProjectileType = 'void_tear';
        
        // Create expanding vortex effect with 3 rings
        const rings = 3;
        const baseProjectileCount = Math.max(3, Math.floor(2 + intensity * 2)); // 2-4 projectiles per ring
        
        for (let ring = 0; ring < rings; ring++) {
            this._setTimeout(() => {
                const radius = 2 + ring * 1.5; // Expanding rings
                const projectileCount = baseProjectileCount + ring; // More projectiles in outer rings
                const baseAngle = (Date.now() * 0.001 * (1 + ring * 0.5)) % (Math.PI * 2); // Faster rotation in outer rings
                
                // VORTEX CENTER: Create void portal effect with reality distortion
                if (ring === 0) {
                    // Add screen distortion effect
                    if (this.dungeonHandler.camera) {
                        this.addCameraShake(0.2, 800);
                    }
                    
                    // Create void center visual (purple energy sphere)
                    this.createVoidPortalCenter(bossPos);
                }
                
                for (let i = 0; i < projectileCount; i++) {
                    const angle = baseAngle + (i / projectileCount) * Math.PI * 2;
                    
                    // Vortex spiral motion - projectiles emerge from center and spiral outward
                    const spiralOffset = ring * 0.3; // Each ring starts closer to center
                    const startRadius = 0.5 + spiralOffset;
                    const direction = new THREE.Vector3(
                        Math.cos(angle + spiralOffset) * startRadius,
                        Math.sin(ring * 0.5) * 0.2, // Slight vertical spiral
                        Math.sin(angle + spiralOffset) * startRadius
                    ).normalize();
                    
                    // Enhanced projectile with void energy
                    const speed = 3.0 + intensity + ring * 0.5; // Outer rings faster
                    const voidPos = bossPos.clone().add(new THREE.Vector3(0, 0.7, 0)); // Boss body spawn point
                    
                    this.spawnProjectile(voidPos, direction, speed, voidProjectileType);
                }
            }, ring * 400); // Stagger ring emergence for dramatic effect
        }
        
        // SOUL WHISPERS: Add ghostly audio effect
        console.log('[NairabosAttackPatterns] 👻 "The void calls... souls of the tormented emerge..."');
    }
    
    /**
     * Create void portal center effect
     */
    createVoidPortalCenter(position) {
        // This would create a visual void portal at the center
        // For now, log the effect - could be enhanced with particle systems later
        console.log('[NairabosAttackPatterns] 🕳️ Void portal manifests at:', position);
        
        // Screen shake for reality distortion
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.15, 1000);
        }
    }
    
    // ===== PHASE 2 PATTERNS (Medium Difficulty) =====
    
    /**
     * Predictive Shots - Aim where player will be
     */
    predictiveShots(bossPos, playerPos, intensity) {
        const shotCount = 4;
        
        // Create converging pattern on predicted player position
        const directions = this.createSpreadPattern(bossPos, playerPos, shotCount, {
            prediction: 0.8,           // Strong prediction
            totalSpread: Math.PI / 6,  // Tight 30 degree cone
            accuracy: 0.85             // High accuracy but not perfect
        });
        
        directions.forEach((direction, i) => {
            this._setTimeout(() => {
                const spawnPos = this.findSafeSpawnPosition(
                    bossPos.clone().add(new THREE.Vector3(0, 1, 0)),
                    1.3
                );
                this.spawnProjectile(spawnPos, direction, 8.0 + intensity * 2, 'shadow_bolt');
            }, i * 150); // Quick succession but with spacing
        });
    }
    
    /**
     * SOUL BARRIER MANIFESTATION - Spectral soul barriers with screaming faces
     * ENHANCED: Was area denial, now manifestation of trapped souls forming protective barriers
     */
    areaDenial(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] 👻 SOUL BARRIER MANIFESTATION - tormented spirits form protective walls!');
        
        // Wind-up indicator for dramatic buildup
        this.addWindUpIndicator("💀 SOULS OF THE DAMNED MANIFESTING - SPECTRAL BARRIERS FORMING!", 2000);
        
        // Calculate barrier formation direction
        const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
        
        // THEMATIC: Force screamer_skulls for maximum soul horror effect
        const soulProjectileType = 'screamer_skulls';
        
        // Enhanced soul barrier formation - multiple waves of souls
        const barrierWaves = 2; // Two waves of soul barriers for extra protection
        const baseProjectileCount = Math.max(2, Math.floor(2 + intensity * 1)); // 2-3 souls per wave
        
        for (let wave = 0; wave < barrierWaves; wave++) {
            this._setTimeout(() => {
                const spread = Math.PI / 3 + wave * 0.2; // Wider spread for outer wave
                const projectileCount = baseProjectileCount + wave; // More souls in outer wave
                
                // SOUL MANIFESTATION: Souls emerge from the void with increasing intensity
                console.log(`[NairabosAttackPatterns] Wave ${wave + 1}: ${projectileCount} tormented souls emerge!`);
                
                for (let i = 0; i < projectileCount; i++) {
                    const angle = -spread/2 + (i / Math.max(1, projectileCount - 1)) * spread;
                    const shotDir = direction.clone();
                    shotDir.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);
                    
                    // Soul barriers move slower but create persistent blocking presence
                    const soulSpeed = 4.0 + intensity + wave * 0.5; // Slower for barrier effect
                    const soulHeight = 1.5 + wave * 0.3; // Higher spawn for outer wave
                    const soulPos = bossPos.clone().add(new THREE.Vector3(0, soulHeight, 0));
                    
                    // Add undulating movement for soul-like behavior
                    const soulDirection = shotDir.clone();
                    soulDirection.y += Math.sin(i * 0.5) * 0.1; // Slight vertical undulation
                    
                    this.spawnProjectile(soulPos, soulDirection.normalize(), soulSpeed, soulProjectileType);
                }
                
                // Screen shake for soul manifestation
                if (this.dungeonHandler.camera) {
                    this.addCameraShake(0.15 + wave * 0.05, 600);
                }
                
            }, wave * 800); // Stagger waves for dramatic buildup
        }
        
        // SPECTRAL VOICES: Add haunting audio effect
        console.log('[NairabosAttackPatterns] 👻 "We guard our master... none shall pass..."');
        
        // Add purple soul energy connecting the barriers
        this.createSoulBarrierConnections(bossPos, direction, baseProjectileCount);
    }
    
    /**
     * Create visual connections between soul barriers
     */
    createSoulBarrierConnections(position, direction, barrierCount) {
        // Visual effect showing soul energy connecting the barriers
        console.log('[NairabosAttackPatterns] 🔮 Soul energy threads connect the spectral barriers');
        
        // Enhanced screen effect for soul energy manifestation
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.1, 1200);
        }
    }
    
    /**
     * Rotating Burst - Fast rotating spray (optimized)
     */
    rotatingBurst(bossPos, playerPos, intensity) {
        const projectileCount = 3;
        const burstCount = 2;
        
        for (let burst = 0; burst < burstCount; burst++) {
            this._setTimeout(() => {
                // First shot aims at player, others spread around
                const playerDirection = this.createSmartDirection(bossPos, playerPos, {
                    prediction: 0.6,
                    accuracy: 0.7,
                    spreadAngle: 0.2
                });
                
                const baseAngle = Math.atan2(playerDirection.z, playerDirection.x);
                
                for (let i = 0; i < projectileCount; i++) {
                    const angle = baseAngle + (i / projectileCount) * Math.PI * 2;
                    const direction = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
                    
                    const spawnPos = this.findSafeSpawnPosition(
                        bossPos.clone().add(new THREE.Vector3(0, 1, 0)),
                        1.0
                    );
                    
                    this.spawnProjectile(spawnPos, direction, 7.0 + intensity * 2);
                }
            }, burst * 400);
        }
    }
    
    /**
     * Player Trap - Surround player position
     */
    playerTrap(bossPos, playerPos, intensity) {
        // Delay before firing to give warning
        this._setTimeout(() => {
            const directions = [];
            const circleCount = 8;
            
            // Create circle around player's current position
            for (let i = 0; i < circleCount; i++) {
                const angle = (i / circleCount) * Math.PI * 2;
                const direction = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
                directions.push(direction);
            }
            
            // Fire from multiple points around the room toward player position
            directions.forEach((dir, index) => {
                const startPos = playerPos.clone().add(dir.clone().multiplyScalar(15)); // Start 15 units away
                const shotDir = dir.clone().negate(); // Shoot toward center
                
                this._setTimeout(() => {
                    this.spawnProjectile(startPos.add(new THREE.Vector3(0, 1, 0)), shotDir, 8.0 + intensity * 2);
                }, index * 100); // Rapid succession
            });
        }, 800); // Warning delay
    }
    
    // ===== PHASE 3 PATTERNS (Maximum Intensity) =====
    
    /**
     * Hellburst Aimed - Intense burst at player
     */
    hellburstAimed(bossPos, playerPos, intensity) {
        const projectileCount = 4;
        
        // Smart spread pattern targeting player with dodgeability
        const directions = this.createSpreadPattern(bossPos, playerPos, projectileCount, {
            prediction: 0.7,          // Predict 0.7s ahead
            totalSpread: Math.PI / 3, // 60 degree cone
            accuracy: 0.75            // 75% accuracy for dodgeability
        });
        
        directions.forEach((direction, i) => {
            this._setTimeout(() => {
                const spawnPos = this.findSafeSpawnPosition(
                    bossPos.clone().add(new THREE.Vector3(0, 1, 0)),
                    1.5 // Minimum spacing
                );
                this.spawnProjectile(spawnPos, direction, 10.0 + intensity * 3);
            }, i * 100); // Slightly slower succession for dodgeability
        });
    }
    
    /**
     * Spiral Death - Optimized spiral pattern
     */
    spiralDeath(bossPos, intensity) {
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('spiral_death');
        }
        
        const waves = Math.min(2, 1 + Math.floor(intensity * 0.5)); // 1-2 waves based on intensity
        const projectilesPerWave = 4; // REDUCED: From 6 to 4 projectiles
        
        for (let wave = 0; wave < waves; wave++) {
            this._setTimeout(() => {
                const baseAngle = (Date.now() * 0.008 + wave * 0.8) % (Math.PI * 2);
                
                for (let i = 0; i < projectilesPerWave; i++) {
                    const angle = baseAngle + (i / projectilesPerWave) * Math.PI * 2;
                    const direction = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
                    this.spawnProjectile(bossPos.clone().add(new THREE.Vector3(0, 0.5, 0)), direction, 6.0 + intensity * 2);
                }
                
                // Spiral distortion effect removed - keeping distortion only for big laser charges
            }, wave * 400); // INCREASED: Longer delay between waves
        }
    }
    
    /**
     * WALL OF THE DAMNED - Screaming souls forming an impenetrable wall of death
     * ENHANCED: Was bullet wall, now a terrifying wall of tormented souls with screaming faces
     */
    bulletWall(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] 💀 WALL OF THE DAMNED RISING - souls of the tormented form an impenetrable barrier!');
        
        // Dramatic wind-up with boss speech
        this.addWindUpIndicator("💀 BEHOLD THE WALL OF THE DAMNED! SOULS OF MY VICTIMS BLOCK YOUR PATH!", 2500);
        
        // Calculate wall formation
        const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
        const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x); // Perpendicular vector
        
        // THEMATIC: Force screamer_skulls for maximum horror effect
        const damnedSoulType = 'screamer_skulls';
        
        // Enhanced wall parameters - multiple layers of souls
        const wallLayers = 2; // Two layers of damned souls for impenetrable wall
        const wallWidth = 16; // Slightly narrower but more concentrated
        const baseProjectileCount = Math.max(3, Math.floor(2 + intensity * 1)); // 2-3 souls per layer
        
        for (let layer = 0; layer < wallLayers; layer++) {
            this._setTimeout(() => {
                const layerOffset = layer * 2; // Second layer behind first
                const projectileCount = baseProjectileCount + (layer * 1); // More souls in back layer
                
                // WALL FORMATION: Souls manifest in coordinated wall formation
                console.log(`[NairabosAttackPatterns] Layer ${layer + 1}: ${projectileCount} damned souls manifest!`);
                
                for (let i = 0; i < projectileCount; i++) {
                    const offset = (-wallWidth/2 + (i / Math.max(1, projectileCount - 1)) * wallWidth);
                    const startPos = bossPos.clone().add(perpendicular.clone().multiplyScalar(offset));
                    
                    // Stagger heights for more imposing wall effect
                    const soulHeight = 1.2 + Math.sin(i * 0.8) * 0.4 + layer * 0.2; // Undulating heights
                    startPos.y += soulHeight;
                    
                    // Position layer behind previous layer
                    startPos.add(direction.clone().multiplyScalar(-layerOffset));
                    
                    // Soul wall moves as unified horror - slower but more menacing
                    const wallSpeed = 5.0 + intensity + layer * 0.3; // Back layer slightly faster
                    
                    // Add slight convergence toward center for intimidating formation
                    const wallDirection = direction.clone();
                    const centerBias = (i - (projectileCount - 1) / 2) * 0.02; // Slight angle toward center
                    wallDirection.add(perpendicular.clone().multiplyScalar(-centerBias));
                    
                    // Queue wall projectiles in desperation mode
                    if (this.isDesperationMode) {
                        this._queueAttack(
                            () => this.spawnProjectile(startPos, wallDirection.normalize(), wallSpeed, damnedSoulType),
                            1, // Single projectile
                            3  // Lower priority for wall
                        );
                    } else {
                        this.spawnProjectile(startPos, wallDirection.normalize(), wallSpeed, damnedSoulType);
                    }
                }
                
                // Screen shake intensifies with each layer
                if (this.dungeonHandler.camera) {
                    this.addCameraShake(0.2 + layer * 0.1, 800);
                }
                
            }, layer * 600); // Stagger layer formation for dramatic buildup
        }
        
        // VOICES OF THE DAMNED: Terrifying audio effect
        console.log('[NairabosAttackPatterns] 👻 "We are legion... we are eternal... none escape our wrath..."');
        
        // Soul energy connecting the wall segments
        this.createDamnedWallConnections(bossPos, direction, perpendicular, wallWidth);
        
        // Add reality distortion effect for the wall of souls
        if (this.dungeonHandler.camera) {
            this._setTimeout(() => {
                this.addCameraShake(0.15, 1500); // Extended reality distortion
            }, 1000);
        }
    }
    
    /**
     * Create soul energy connections between wall segments
     */
    createDamnedWallConnections(position, direction, perpendicular, wallWidth) {
        // Visual effect showing purple soul energy connecting wall segments
        console.log('[NairabosAttackPatterns] 🔗 Chains of soul energy bind the wall together in eternal torment');
        
        // Enhanced atmospheric effect for wall manifestation
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.1, 2000); // Long atmospheric distortion
        }
    }
    
    /**
     * Desperation Barrage - Optimized chaos
     */
    desperationBarrage(bossPos, playerPos, intensity) {
        const barrageCount = 8;
        
        for (let i = 0; i < barrageCount; i++) {
            this._setTimeout(() => {
                // Smart targeting with decreasing accuracy for challenge
                const shotAccuracy = 0.8 - (i * 0.05); // Gets less accurate over time
                const direction = this.createSmartDirection(bossPos, playerPos, {
                    prediction: 0.5,
                    accuracy: Math.max(0.4, shotAccuracy),
                    spreadAngle: 0.3,
                    leadTarget: true
                });
                
                const spawnPos = this.findSafeSpawnPosition(
                    bossPos.clone().add(new THREE.Vector3(0, 1, 0)),
                    1.2
                );
                
                this.spawnProjectile(spawnPos, direction, 9.0 + intensity * 2);
            }, i * 200);
        }
    }
    
    // ===== ADVANCED ORGANIC PATTERNS =====
    
    /**
     * Floating Swaying Circles - Organic cluster movement
     */
    floatingSwayingCircles(bossPos, playerPos, intensity, projectileType = null) {
        // Pure black bullet pattern for visual clarity
        projectileType = 'shadow_bolt';
        console.log('[NairabosAttackPatterns] Floating Swaying Circles');
        this.createWindUpIndicator(bossPos, 1000, 'Mystical circles forming...');
        
        this._setTimeout(() => {
            const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
            const circleCount = 1; // Single circle to reduce spam
            
            for (let circle = 0; circle < circleCount; circle++) {
                const group = new BulletGroup(bossPos.clone().add(new THREE.Vector3(0, 0.6, 0)), this.dungeonHandler, this.enemy);
                
                // Configure organic movement
                group.setSwayParameters(3.0 + intensity * 2, 0.004);
                group.setRotationSpeed(0.003 + intensity * 0.002);
                group.setPulseParameters(0.3, 0.005);
                group.setGroupVelocity(direction.clone().multiplyScalar(2.0 + intensity));
                
                // Create circle of bullets
                const bulletsInCircle = 3; // Further reduced from 4 for less spam
                const radius = 2.0 + circle * 1.5;
                
                for (let i = 0; i < bulletsInCircle; i++) {
                    const angle = (i / bulletsInCircle) * Math.PI * 2;
                    const localPos = new THREE.Vector3(
                        Math.cos(angle) * radius,
                        0,
                        Math.sin(angle) * radius
                    );
                    
                    const projectileData = {
                        projectileType: projectileType,
                        projectileSpeed: 0, // Movement handled by group
                        disableTrails: false,
                        type: 'nairabos',
                        health: 1,
                        isProjectile: true,
                        aiType: this.enemy.userData.aiType || 'flying',
                        owner: this.enemy
                    };
                    
                    group.addBullet(projectileData, localPos, i * 0.5); // Phase offset for wave effect
                }
                
                this.activeBulletGroups.push(group);
                
                // Stagger circle creation
                if (circle < circleCount - 1) {
                    this._setTimeout(() => {}, circle * 300);
                }
            }
        }, 1000);
    }
    
    /**
     * Rotating Bullet Pairs - S-wave formation
     */
    rotatingBulletPairs(bossPos, playerPos, intensity, projectileType = null) {
        // Pure black bullet pattern for visual clarity
        projectileType = 'shadow_bolt';
        console.log('[NairabosAttackPatterns] Rotating Bullet Pairs');
        this.createWindUpIndicator(bossPos, 800, 'Paired orbs awakening...');
        
        this._setTimeout(() => {
            const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
            const pairCount = 2 + Math.floor(intensity * 1); // Drastically reduced
            
            for (let pair = 0; pair < pairCount; pair++) {
                const group = new BulletGroup(bossPos.clone().add(new THREE.Vector3(0, 1, 0)), this.dungeonHandler, this.enemy);
                
                // Configure pair rotation
                group.setRotationSpeed(0.008 + intensity * 0.004);
                group.setSwayParameters(1.5, 0.006);
                group.setGroupVelocity(direction.clone().multiplyScalar(3.0 + intensity * 2));
                
                // Create bullet pair
                const separation = 1.5;
                const positions = [
                    new THREE.Vector3(-separation, 0, 0),
                    new THREE.Vector3(separation, 0, 0)
                ];
                
                positions.forEach((pos, index) => {
                    const projectileData = {
                        projectileType: projectileType,
                        projectileSpeed: 0,
                        disableTrails: false,
                        type: 'nairabos',
                        health: 1,
                        isProjectile: true,
                        aiType: this.enemy.userData.aiType || 'flying',
                        owner: this.enemy
                    };
                    
                    group.addBullet(projectileData, pos, index * Math.PI); // Opposite phases
                });
                
                this.activeBulletGroups.push(group);
                
                // Stagger pair creation for S-wave effect
                this._setTimeout(() => {}, pair * 200);
            }
        }, 800);
    }
    
    /**
     * Circular Area Attack - Expanding rings with knockback edges
     */
    circularAreaAttack(bossPos, playerPos, intensity, projectileType = null) {
        // Use thematic projectile type if not specified
        projectileType = projectileType || this.getNextProjectileType();
        console.log('[NairabosAttackPatterns] Circular Area Attack');
        this.createWindUpIndicator(bossPos, 1200, 'Gathering dark energy...');
        
        this._setTimeout(() => {
            const rings = 3;
            const ringDelay = 400;
            
            for (let ring = 0; ring < rings; ring++) {
                this._setTimeout(() => {
                    const group = new BulletGroup(bossPos.clone().add(new THREE.Vector3(0, 1, 0)), this.dungeonHandler, this.enemy);
                    
                    // Configure expanding ring
                    group.setRotationSpeed(0.002);
                    group.setGroupVelocity(new THREE.Vector3(0, 0, 0));
                    
                    // Create ring of bullets
                    const bulletCount = 8; // Reduced from 16 for performance
                    const radius = 3.0 + ring * 2.0;
                    
                    for (let i = 0; i < bulletCount; i++) {
                        const angle = (i / bulletCount) * Math.PI * 2;
                        const localPos = new THREE.Vector3(
                            Math.cos(angle) * radius,
                            0,
                            Math.sin(angle) * radius
                        );
                        
                        // Use consistent projectile type for all bullets
                        const bulletType = projectileType;
                        
                        const projectileData = {
                            projectileType: bulletType,
                            projectileSpeed: 4.0 + intensity * 2,
                            disableTrails: false,
                            type: 'nairabos',
                            health: 1,
                            isProjectile: true,
                            aiType: this.enemy.userData.aiType || 'flying',
                            owner: this.enemy
                        };
                        
                        group.addBullet(projectileData, localPos, 0);
                        
                        // Set expansion velocity
                        const expandDirection = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
                        group.setGroupVelocity(expandDirection.multiplyScalar(2.0 + intensity));
                    }
                    
                    this.activeBulletGroups.push(group);
                }, ring * ringDelay);
            }
        }, 1200);
    }
    
    /**
     * Shadow Minion Assault - Spawn shadow minions that attack
     */
    shadowMinionAssault(bossPos, playerPos, intensity, projectileType = null) {
        // Use thematic projectile type if not specified
        projectileType = projectileType || this.getNextProjectileType();
        console.log('[NairabosAttackPatterns] Shadow Minion Assault');
        
        // LIMIT: Check current minion count and limit to 3
        if (this.shadowMinions.length >= 3) {
            console.log('[NairabosAttackPatterns] 🚫 Shadow Minion limit reached (3), removing oldest minion');
            // Remove oldest minion
            const oldestMinion = this.shadowMinions.shift();
            if (oldestMinion && oldestMinion.cleanup) {
                oldestMinion.cleanup();
            }
        }
        
        this.createWindUpIndicator(bossPos, 1500, 'Calling forth shadow servants...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('shadow_minion');
        }
        
        this._setTimeout(() => {
            const minionCount = 1 + Math.floor(intensity * 1); // Max 2 minions
            
            for (let i = 0; i < minionCount; i++) {
                // Spawn positions around the room
                const angle = (i / minionCount) * Math.PI * 2;
                const spawnRadius = 8.0 + Math.random() * 4.0;
                const spawnPos = bossPos.clone().add(new THREE.Vector3(
                    Math.cos(angle) * spawnRadius,
                    0,
                    Math.sin(angle) * spawnRadius
                ));
                
                this.spawnShadowMinion(spawnPos, intensity);
                
                // Stagger spawning
                this._setTimeout(() => {}, i * 300);
            }
        }, 1500);
    }
    
    /**
     * Create wind-up indicator for attack telegraphing
     */
    createWindUpIndicator(position, duration, message) {
        console.log(`[NairabosAttackPatterns] Wind-up: ${message}`);
        
        // Create visual indicator (simple for now)
        const indicator = {
            position: position.clone(),
            startTime: performance.now(),
            duration: duration,
            message: message,
            active: true,
            update: function(deltaTime) {
                const elapsed = performance.now() - this.startTime;
                if (elapsed >= this.duration) {
                    this.active = false;
                }
                // TODO: Add visual effects (particle system, etc.)
            }
        };
        
        this.windUpIndicators.push(indicator);
    }
    
    /**
     * Spawn a shadow minion enemy
     */
    spawnShadowMinion(position, intensity) {
        // LIMIT: Check current minion count and limit to 5
        if (this.shadowMinions.length >= 5) {
            console.log('[NairabosAttackPatterns] 🚫 Shadow Minion limit reached (5/5), removing oldest minion');
            // Remove oldest minion
            const oldestMinion = this.shadowMinions.shift();
            if (oldestMinion && oldestMinion.entity) {
                this.removeShadowMinion(oldestMinion.entity);
            }
        }
        
        try {
            // Spawn through dungeon handler using the registered shadow_minion type
            const minion = this.dungeonHandler._spawnEnemy('shadow_minion', position);
            
            if (minion) {
                // Set up boss minion properties
                minion.userData.lifetime = performance.now() + 15000; // 15 seconds
                minion.userData.attackCooldown = 3000; // OPTIMIZATION: Increased to 3 seconds to reduce projectile spam
                minion.userData.lastAttackTime = performance.now() - 1000; // Start attacks after 2 seconds
                minion.userData.isBossMinion = true;
                minion.userData.parentBoss = this.enemy;
                minion.userData.intensity = intensity;
                
                // Set emergence animation state
                if (minion.userData.animationHandler) {
                    minion.userData.animationHandler.setState('EMERGING');
                }
                
                this.shadowMinions.push({
                    entity: minion,
                    active: true,
                    spawnTime: performance.now(),
                    update: (deltaTime) => {
                        // Check lifetime
                        if (performance.now() > minion.userData.lifetime) {
                            this.removeShadowMinion(minion);
                            return;
                        }
                        
                        // OPTIMIZATION: Simplified AI with staggered attacks
                        const currentTime = performance.now();
                        // Add minion index offset to prevent all minions attacking simultaneously
                        const minionIndex = this.shadowMinions.findIndex(m => m.entity === minion);
                        const attackOffset = minionIndex * 400; // Stagger attacks by 400ms
                        
                        if (currentTime - minion.userData.lastAttackTime > minion.userData.attackCooldown + attackOffset) {
                            this.shadowMinionAttack(minion);
                            minion.userData.lastAttackTime = currentTime;
                        }
                    }
                });
                
                console.log(`[NairabosAttackPatterns] Shadow minion spawned and emerging from ground (${this.shadowMinions.length}/3 total minions)`);
                return minion;
            }
        } catch (error) {
            console.error('[NairabosAttackPatterns] Failed to spawn shadow minion:', error);
        }
        return null;
    }
    
    /**
     * Shadow minion attack pattern
     */
    shadowMinionAttack(minion) {
        const playerPos = this.getPlayerPosition();
        const minionPos = minion.position.clone();
        const direction = new THREE.Vector3().subVectors(playerPos, minionPos).normalize();
        
        // OPTIMIZATION: Reduced to double shot for performance
        for (let i = 0; i < 2; i++) {
            const angle = (i - 0.5) * 0.4; // Wider spread for 2 shots
            const shotDir = direction.clone();
            shotDir.applyAxisAngle(new THREE.Vector3(0, 1, 0), angle);
            
            const projectileData = {
                projectileType: 'green_flame_bullet',
                projectileSpeed: 7.0, // Slightly faster to maintain threat
                disableTrails: true, // PERFORMANCE: Disable trails
                type: 'shadow_minion',
                health: 1,
                isProjectile: true,
                aiType: 'shadow_minion',
                owner: minion
            };
            
            this._setTimeout(() => {
                this.dungeonHandler.spawnProjectile(minionPos.clone().add(new THREE.Vector3(0, 1, 0)), shotDir, projectileData);
            }, i * 150); // Slightly longer delay
        }
    }
    
    /**
     * Remove shadow minion
     */
    removeShadowMinion(minion) {
        if (minion && minion.parent) {
            minion.parent.remove(minion);
        }
        this.shadowMinions = this.shadowMinions.filter(m => m.entity !== minion);
    }
    
    /**
     * Spawn a projectile using the dungeon handler
     */
    spawnProjectile(position, direction, speed = 6.0, projectileType = null) {
        // PERFORMANCE: Frame-based spawn limiting
        const now = performance.now();
        if (now - this.lastFrameTime > 16) { // New frame (~60fps)
            this.spawnsThisFrame = 0;
            this.lastFrameTime = now;
        }
        
        if (this.spawnsThisFrame >= this.frameProjectileBudget) {
            return null; // Silently skip to prevent console spam
        }
        
        // EMERGENCY: Hard projectile limits to prevent crashes
        if (this.activeProjectiles.length >= 50) { // REDUCED from 75
            console.warn('[NairabosAttackPatterns] 🚨 EMERGENCY: Global projectile limit reached (50), skipping spawn');
            return null;
        }
        
        if (this.currentAttackProjectileCount >= this.maxProjectilesPerAttack) {
            console.warn('[NairabosAttackPatterns] 🚨 EMERGENCY: Per-attack limit reached (20), skipping spawn');
            return null;
        }
        
        this.spawnsThisFrame++;
        
        // OPTIMIZATION: Combination projectile limit for multi-pattern attacks
        if (this.combinationProjectileCount !== undefined && this.combinationProjectileCount >= 25) {
            console.warn('[NairabosAttackPatterns] 🎯 OPTIMIZATION: Combination projectile limit reached (25), skipping spawn for performance');
            return null;
        }
        
        if (!this.dungeonHandler || typeof this.dungeonHandler.spawnProjectile !== 'function') {
            console.warn('[NairabosAttackPatterns] Cannot spawn projectile: dungeonHandler.spawnProjectile not available');
            return;
        }
        
        // Increment attack counter
        this.currentAttackProjectileCount++;
        
        // OPTIMIZATION: Increment combination counter if we're in combination attack
        if (this.combinationProjectileCount !== undefined) {
            this.combinationProjectileCount++;
        }
        
        // Use provided projectile type or default to torment_orb
        const finalProjectileType = projectileType || 'torment_orb';
        
        // THEMATIC ENHANCEMENT: Add shadow/torment visual effects based on projectile type
        const shadowEnhancements = this._getShadowTormentEnhancements(finalProjectileType);
        
        // Create enhanced projectile data with thematic properties
        const projectileData = {
            projectileType: finalProjectileType,
            projectileSpeed: speed,
            disableTrails: true, // PERFORMANCE: Disable trails for Nairabos projectiles
            type: 'shadow_bolt', // CRITICAL FIX: Use shadow_bolt instead of 'nairabos'
            health: 1,
            isProjectile: true,
            aiType: this.enemy.userData.aiType || 'flying',
            owner: this.enemy,
            
            // THEMATIC VISUAL ENHANCEMENTS
            ...shadowEnhancements,
            
            // Nairabos signature effects
            shadowAura: true,
            tormentTheme: true,
            corruptionTrail: true,
            voidEnergy: finalProjectileType === 'void_tear',
            
            // OPTIMIZATION: Minimal visual effects for performance
            glowIntensity: 0.8, // REDUCED
            glowColor: shadowEnhancements.emissiveColor || 0x6a2c6a,
            orbitalParticles: false, // PERFORMANCE: Disable particles
            animatedShader: false, // PERFORMANCE: Disable animated shaders
            pointLight: false, // PERFORMANCE: Disable ALL point lights during combat
            pointLightColor: shadowEnhancements.emissiveColor || 0x6a2c6a,
            pointLightIntensity: 0.3, // PERFORMANCE: Reduced intensity
            pointLightDistance: 2.0 // PERFORMANCE: Reduced distance
        };
        
        try {
            const projectile = this.dungeonHandler.spawnProjectile(position, direction, projectileData);
            if (projectile) {
                // Mark spawn time for cleanup
                if (!projectile.userData) projectile.userData = {};
                projectile.userData.spawnTime = performance.now();
                
                this.activeProjectiles.push(projectile);
                
                // Clean up projectile reference after it's likely to be gone
                this._setTimeout(() => {
                    const index = this.activeProjectiles.indexOf(projectile);
                    if (index > -1) {
                        this.activeProjectiles.splice(index, 1);
                    }
                }, 5000); // REDUCED: 5 second cleanup
            }
            return projectile;
        } catch (error) {
            console.error('[NairabosAttackPatterns] Error spawning projectile:', error);
            return null;
        }
    }
    
    // ===== NEW BULLET HELL PATTERNS =====
    
    /**
     * Bullet Corridor - Creates narrow safe corridors player must navigate through
     */
    bulletCorridor(bossPos, playerPos, intensity, projectileType = null) {
        // Pure black bullet pattern for visual clarity
        projectileType = 'shadow_bolt';
        console.log('[NairabosAttackPatterns] Bullet Corridor Attack');
        this.createWindUpIndicator(bossPos, 1500, 'Creating deadly corridors...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('bullet_corridor');
        }
        
        this._setTimeout(() => {
            const corridorCount = 1 + Math.floor(intensity * 0.5); // 1-2 corridors max
            const arenaWidth = 10; // OPTIMIZED: Further reduced for performance
            // Desperation mode: reduce safe corridor width by 30% 
            const baseCorridorWidth = 2.5; // Player can fit through
            const corridorWidth = this.isDesperationMode ? baseCorridorWidth * 0.7 : baseCorridorWidth;
            const wallDensity = this.isDesperationMode ? 4 : 3; // OPTIMIZED: Reduced from 8/6 to 4/3
            
            // SMART OPTIMIZATION: Use larger, more visible projectiles for visual density
            const projectileScale = 1.5; // 50% larger projectiles
            const projectileSpeed = 2.5 + intensity; // Slower for better visibility
            
            for (let corridor = 0; corridor < corridorCount; corridor++) {
                // Calculate corridor position (evenly spaced)
                const corridorZ = -arenaWidth/2 + (corridor + 1) * (arenaWidth / (corridorCount + 1));
                
                // Create walls on both sides of corridor
                for (let side = -1; side <= 1; side += 2) {
                    const wallStart = corridorZ + side * (corridorWidth/2);
                    const wallEnd = side > 0 ? arenaWidth/2 : -arenaWidth/2;
                    
                    for (let z = wallStart; side > 0 ? z <= wallEnd : z >= wallEnd; z += side * (3/wallDensity)) { // OPTIMIZED: 3x spacing for better performance
                        // FIXED: Single line instead of grid - spawn only at center X position
                        for (let x = 0; x <= 0; x += 1) { // Single line at x=0
                            const projectileData = {
                                projectileType: projectileType,
                                projectileSpeed: projectileSpeed,
                                disableTrails: true, // PERFORMANCE: Disable trails for Nairabos projectiles
                                type: 'nairabos',
                                health: 1,
                                isProjectile: true,
                                aiType: this.enemy.userData.aiType || 'flying',
                                owner: this.enemy,
                                // VISUAL ENHANCEMENT: Larger projectiles for maintained visual impact
                                scale: projectileScale,
                                glowIntensity: 1.3 // Enhanced glow for visibility
                            };
                            
                            const spawnPos = new THREE.Vector3(x, 1, z);
                            const direction = new THREE.Vector3(0, 0, side);
                            
                            this.spawnProjectile(spawnPos, direction, projectileSpeed, projectileType);
                        }
                    }
                }
            }
        }, 1500);
    }
    
    /**
     * Sweeping Death Wave - Directional wave that sweeps across arena
     */
    sweepingDeathWave(bossPos, playerPos, intensity, projectileType = null) {
        // Pure black bullet pattern for visual clarity
        projectileType = 'shadow_bolt';
        console.log('[NairabosAttackPatterns] Sweeping Death Wave Attack');
        this.createWindUpIndicator(bossPos, 1200, 'Summoning death wave...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('sweeping_wave');
        }
        
        this._setTimeout(() => {
            // Choose random edge as origin
            const edges = ['north', 'south', 'east', 'west'];
            const chosenEdge = edges[Math.floor(Math.random() * edges.length)];
            
            let originPos, sweepDirection, waveWidth;
            const arenaSize = 15;
            
            switch(chosenEdge) {
                case 'north':
                    originPos = new THREE.Vector3(-arenaSize, 1, -arenaSize);
                    sweepDirection = new THREE.Vector3(1, 0, 1).normalize();
                    break;
                case 'south':
                    originPos = new THREE.Vector3(arenaSize, 1, arenaSize);
                    sweepDirection = new THREE.Vector3(-1, 0, -1).normalize();
                    break;
                case 'east':
                    originPos = new THREE.Vector3(arenaSize, 1, -arenaSize);
                    sweepDirection = new THREE.Vector3(-1, 0, 1).normalize();
                    break;
                case 'west':
                    originPos = new THREE.Vector3(-arenaSize, 1, arenaSize);
                    sweepDirection = new THREE.Vector3(1, 0, -1).normalize();
                    break;
            }
            
            waveWidth = 6 + intensity * 1; // Width of the wave - PERFORMANCE FIX: Reduced
            const waveDensity = 3 + intensity * 1; // Bullets per unit - PERFORMANCE FIX: Reduced from 6 + intensity * 2
            
            // Create the sweeping wave - EMERGENCY FIX: Drastically reduced step count
            for (let step = 0; step < 8; step++) { // EMERGENCY: Reduced from 20 to 8 steps to prevent crashes
                this._setTimeout(() => {
                    const currentPos = originPos.clone().add(sweepDirection.clone().multiplyScalar(step * 1.5));
                    
                    // Create perpendicular line of bullets
                    const perpDirection = new THREE.Vector3(-sweepDirection.z, 0, sweepDirection.x);
                    
                    for (let i = 0; i < waveDensity; i++) {
                        const bulletOffset = (i - waveDensity/2) * (waveWidth / waveDensity);
                        const bulletPos = currentPos.clone().add(perpDirection.clone().multiplyScalar(bulletOffset));
                        
                        const projectileData = {
                            projectileType: projectileType,
                            projectileSpeed: 4.0 + intensity,
                            disableTrails: false,
                            type: 'nairabos',
                            health: 1,
                            isProjectile: true,
                            aiType: this.enemy.userData.aiType || 'flying',
                            owner: this.enemy
                        };
                        
                        this.spawnProjectile(bulletPos, sweepDirection.clone(), 4.0 + intensity, projectileType);
                    }
                }, step * 100); // 100ms between wave steps
            }
        }, 1200);
    }
    
    /**
     * Zigzag Maze - Creates zigzag pattern requiring sharp directional changes
     */
    zigzagMaze(bossPos, playerPos, intensity, projectileType = null) {
        // Use thematic projectile type if not specified
        projectileType = projectileType || this.getNextProjectileType();
        console.log('[NairabosAttackPatterns] Zigzag Maze Attack');
        this.createWindUpIndicator(bossPos, 1800, 'Weaving a deadly maze...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('zigzag_maze');
        }
        
        this._setTimeout(() => {
            const segments = 4 + Math.floor(intensity); // 4-5 segments
            const segmentLength = 6;
            const gapWidth = 3; // Width of safe passage
            
            for (let segment = 0; segment < segments; segment++) {
                this._setTimeout(() => {
                    const isEvenSegment = segment % 2 === 0;
                    const baseZ = -10 + segment * (20 / segments);
                    
                    // Determine which side is blocked this segment
                    const leftBlocked = isEvenSegment;
                    const rightBlocked = !isEvenSegment;
                    
                    // Create diagonal walls
                    for (let x = -10; x <= 10; x += 0.8) {
                        for (let z = baseZ; z < baseZ + (20 / segments); z += 0.8) {
                            let shouldSpawn = false;
                            
                            if (leftBlocked && x < gapWidth) {
                                shouldSpawn = true;
                            } else if (rightBlocked && x > -gapWidth) {
                                shouldSpawn = true;
                            }
                            
                            if (shouldSpawn) {
                                const projectileData = {
                                    projectileType: projectileType,
                                    projectileSpeed: 2.0 + intensity * 0.5,
                                    disableTrails: true, // PERFORMANCE: Disable trails for Nairabos projectiles
                                    type: 'nairabos',
                                    health: 1,
                                    isProjectile: true,
                                    aiType: this.enemy.userData.aiType || 'flying',
                                    owner: this.enemy
                                };
                                
                                const spawnPos = new THREE.Vector3(x, 1, z);
                                const direction = new THREE.Vector3(
                                    leftBlocked ? 1 : -1,
                                    0,
                                    0.3
                                ).normalize();
                                
                                this.spawnProjectile(spawnPos, direction, 2.0 + intensity * 0.5, projectileType);
                            }
                        }
                    }
                    console.log(`[NairabosAttackPatterns] Zigzag segment ${segment} spawned ${projectilesInSegment} projectiles`);
                }, segment * 600); // Stagger segments
            }
        }, 1800);
    }
    
    // ===== PATTERN OVERLAPPING SYSTEM =====
    
    /**
     * Trigger combination attacks for Phase 3 chaos
     * OPTIMIZED: Hard limit of 25 projectiles total across all patterns
     */
    triggerCombinationAttack(intensity) {
        const playerPos = this.getPlayerPosition();
        const bossPos = this.enemy.position.clone();
        
        // OPTIMIZATION: Initialize combination projectile counter
        this.combinationProjectileCount = 0;
        const COMBINATION_PROJECTILE_LIMIT = 25; // Hard cap for performance
        
        console.log(`[NairabosAttackPatterns] 🎯 COMBINATION ATTACK: Starting with ${COMBINATION_PROJECTILE_LIMIT} projectile budget`);
        
        // Define combination attack types with optimized intensities
        const combinationTypes = [
            'spiral_plus_laser',
            'corridor_plus_minions', 
            'sweep_plus_bullets',
            'laser_cross_plus_maze'
        ];
        
        const comboType = combinationTypes[Math.floor(Math.random() * combinationTypes.length)];
        console.log(`[NairabosAttackPatterns] Executing OPTIMIZED combination: ${comboType}`);
        
        // Show combination wind-up
        this.createWindUpIndicator(bossPos, 3000, 'UNLEASHING CHAOS!');
        
        // Trigger animation
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('combination_chaos');
        }
        
        // OPTIMIZATION: Reduced intensities to stay within projectile budget while maintaining challenge
        switch (comboType) {
            case 'spiral_plus_laser':
                // OPTIMIZED: ~12 projectiles from spiral + lasers for visual impact
                console.log(`[NairabosAttackPatterns] 🌀 Spiral + Laser combo (Budget: ${COMBINATION_PROJECTILE_LIMIT})`);
                this.spiralDeath(bossPos, intensity * 0.4); // Reduced from 0.7 for projectile optimization
                this._setTimeout(() => {
                    if (this.combinationProjectileCount < COMBINATION_PROJECTILE_LIMIT) {
                        this.crossLaserBeams(bossPos, playerPos, intensity * 0.6); // Reduced from 0.8
                    } else {
                        console.log('[NairabosAttackPatterns] ⚡ OPTIMIZATION: Skipping laser beams - projectile limit reached');
                    }
                }, 1500);
                break;
                
            case 'corridor_plus_minions':
                // OPTIMIZED: ~15 projectiles from corridors + minions for challenge
                console.log(`[NairabosAttackPatterns] 🏃 Corridor + Minions combo (Budget: ${COMBINATION_PROJECTILE_LIMIT})`);
                this.bulletCorridor(bossPos, playerPos, intensity * 0.5); // Reduced from 0.8 
                this._setTimeout(() => {
                    if (this.combinationProjectileCount < COMBINATION_PROJECTILE_LIMIT) {
                        this.shadowMinionAssault(bossPos, playerPos, intensity * 0.4); // Reduced from 0.6
                    } else {
                        console.log('[NairabosAttackPatterns] 👻 OPTIMIZATION: Skipping minion assault - projectile limit reached');
                    }
                }, 2000);
                break;
                
            case 'sweep_plus_bullets':
                // OPTIMIZED: ~18 projectiles from wave + pairs for coverage
                console.log(`[NairabosAttackPatterns] 🌊 Sweep + Bullets combo (Budget: ${COMBINATION_PROJECTILE_LIMIT})`);
                this.sweepingDeathWave(bossPos, playerPos, intensity * 0.4); // Reduced from 0.7
                this._setTimeout(() => {
                    if (this.combinationProjectileCount < COMBINATION_PROJECTILE_LIMIT) {
                        this.rotatingBulletPairs(bossPos, playerPos, intensity * 0.4); // Reduced from 0.6
                    } else {
                        console.log('[NairabosAttackPatterns] 🔄 OPTIMIZATION: Skipping bullet pairs - projectile limit reached'); 
                    }
                }, 1000);
                break;
                
            case 'laser_cross_plus_maze':
                // OPTIMIZED: Laser-focused combination with minimal projectiles for precision challenge
                console.log(`[NairabosAttackPatterns] ✨ Laser + Maze combo (Budget: ${COMBINATION_PROJECTILE_LIMIT})`);
                this.crossLaserBeams(bossPos, playerPos, intensity * 0.5); // Reduced from 0.6
                this._setTimeout(() => {
                    if (this.combinationProjectileCount < COMBINATION_PROJECTILE_LIMIT) {
                        this.zigzagMaze(bossPos, playerPos, intensity * 0.4); // Reduced from 0.7
                    } else {
                        console.log('[NairabosAttackPatterns] 🧩 OPTIMIZATION: Skipping zigzag maze - projectile limit reached');
                    }
                }, 2500);
                break;
        }
        
        // OPTIMIZATION: Enhanced visual feedback to compensate for fewer projectiles
        if (this.laserSystem) {
            this.laserSystem.addCameraShake(0.3, 1500); // Increased shake intensity and duration for impact
        }
        
        // OPTIMIZATION: Log final projectile usage for monitoring
        this._setTimeout(() => {
            console.log(`[NairabosAttackPatterns] 📊 COMBINATION COMPLETE: Used ${this.combinationProjectileCount}/${COMBINATION_PROJECTILE_LIMIT} projectiles`);
            this.combinationProjectileCount = 0; // Reset counter
        }, 4000);
    }
    
    // ===== LASER ATTACK PATTERNS =====
    
    /**
     * Single Laser Beam - Aimed at player with clear warning
     */
    singleLaserBeam(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Single Laser Beam Attack');
        this.createWindUpIndicator(bossPos, 2500, 'Charging devastating laser...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('single_laser');
        }
        
        // Calculate laser path (from boss to player position)
        const laserStart = bossPos.clone().add(new THREE.Vector3(0, 2, 0)); // Elevated start
        const predictedPlayerPos = this.predictPlayerPosition(2.0); // Predict where player will be
        
        // Extend laser beyond player to hit arena wall
        const direction = new THREE.Vector3().subVectors(predictedPlayerPos, laserStart).normalize();
        const laserEnd = laserStart.clone().add(direction.multiplyScalar(25));
        
        // Create warning 2 seconds before firing
        this._setTimeout(() => {
            this.laserSystem.createWarning(laserStart, laserEnd, 2000, 'LASER TARGETING!');
            
            // Fire laser after warning
            this._setTimeout(() => {
                const damage = 2 + Math.floor(intensity * 2); // 2-4 damage based on intensity
                this.laserSystem.fireLaser(laserStart, laserEnd, 1500, damage);
            }, 2000);
            
        }, 500);
    }
    
    /**
     * Cross Laser Beams - X-pattern centered on boss
     */
    crossLaserBeams(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Cross Laser Beams Attack');
        this.createWindUpIndicator(bossPos, 3000, 'Preparing cross laser array...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('cross_laser');
        }
        
        this._setTimeout(() => {
            const laserStart = bossPos.clone().add(new THREE.Vector3(0, 2.0, 0)); // Match LaserWarningSystem offset
            const laserLength = 20;
            
            // Four laser directions forming an X
            const laserDirections = [
                new THREE.Vector3(1, 0, 1).normalize(),   // Northeast
                new THREE.Vector3(-1, 0, 1).normalize(),  // Northwest  
                new THREE.Vector3(1, 0, -1).normalize(),  // Southeast
                new THREE.Vector3(-1, 0, -1).normalize()  // Southwest
            ];
            
            // OPTIMIZATION: Reduced intensity distortion for performance
            this._setTimeout(() => {
                if (this.dungeonHandler.sceneManager && 
                    this.dungeonHandler.sceneManager.crtEffect && 
                    this.dungeonHandler.sceneManager.crtEffect.manager) {
                    // PERFORMANCE: Reduced distortion intensity from 0.7 to 0.4
                    this.dungeonHandler.sceneManager.crtEffect.manager.triggerLensDistortion(0.4, 600, 1);
                }
            }, 2000 + (laserDirections.length * 200) - 600);
            
            // Create warnings for all beams
            const warnings = [];
            laserDirections.forEach((dir, index) => {
                const laserEnd = laserStart.clone().add(dir.clone().multiplyScalar(laserLength));
                
                this._setTimeout(() => {
                    const warning = this.laserSystem.createWarning(laserStart, laserEnd, 2000, `CROSS LASER ${index + 1}!`);
                    warnings.push({ warning, dir, end: laserEnd });
                }, index * 200); // Stagger warnings slightly
            });
            
            // Fire all lasers simultaneously after warnings
            this._setTimeout(() => {
                warnings.forEach(({ dir, end }) => {
                    const damage = 1 + Math.floor(intensity); // 1-2 damage (lower since multiple beams)
                    this.laserSystem.fireLaser(laserStart, end, 2000, damage);
                });
            }, 2000 + (laserDirections.length * 200));
            
        }, 1000);
    }
    
    /**
     * Rotating Laser Sweep - Laser beam that rotates around boss
     */
    rotatingLaserSweep(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Rotating Laser Sweep Attack');
        this.createWindUpIndicator(bossPos, 2000, 'Initiating laser sweep protocol...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('rotating_laser');
        }
        
        this._setTimeout(() => {
            // OPTIMIZATION: Reduced intensity distortion for performance
            this._setTimeout(() => {
                if (this.dungeonHandler.sceneManager && 
                    this.dungeonHandler.sceneManager.crtEffect && 
                    this.dungeonHandler.sceneManager.crtEffect.manager) {
                    // PERFORMANCE: Reduced distortion intensity from 0.6 to 0.3
                    this.dungeonHandler.sceneManager.crtEffect.manager.triggerLensDistortion(0.3, 600, 1);
                }
            }, 1500 - 600); // Adjusted timing
            const laserStart = bossPos.clone().add(new THREE.Vector3(0, 2.0, 0)); // Match LaserWarningSystem offset
            const sweepRadius = 15;
            const rotationSpeed = 0.04 + intensity * 0.02; // Faster with higher intensity
            const totalDuration = 4000; // 4 seconds of sweeping
            const steps = 15; // OPTIMIZATION: Reduced from 30 to 15 for better performance
            
            // Start angle (towards player)
            const toPlayer = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
            let currentAngle = Math.atan2(toPlayer.z, toPlayer.x);
            
            // Create initial warning
            const initialDir = new THREE.Vector3(Math.cos(currentAngle), 0, Math.sin(currentAngle));
            const initialEnd = laserStart.clone().add(initialDir.multiplyScalar(sweepRadius));
            this.laserSystem.createWarning(laserStart, initialEnd, 1500, 'ROTATING LASER!');
            
            // Start the sweep after warning
            this._setTimeout(() => {
                // OPTIMIZATION: Create continuous sweep instead of individual segments
                let lastLaserEnd = null;
                for (let step = 0; step < steps; step++) {
                    this._setTimeout(() => {
                        // Calculate current laser position
                        const stepAngle = currentAngle + (step * rotationSpeed * 2); // Double rotation to cover same area
                        const direction = new THREE.Vector3(Math.cos(stepAngle), 0, Math.sin(stepAngle));
                        const laserEnd = laserStart.clone().add(direction.multiplyScalar(sweepRadius));
                        
                        // OPTIMIZATION: Create wider laser beams to maintain coverage with fewer segments
                        const damage = 1 + Math.floor(intensity * 0.5); // 1-2 damage
                        this.laserSystem.fireLaser(laserStart, laserEnd, 800, damage); // Longer duration for continuity
                        
                        // Store for potential optimization
                        lastLaserEnd = laserEnd;
                    }, step * (totalDuration / steps));
                }
            }, 1500);
            
        }, 500);
    }
    
    // ===== ARENA HAZARD PATTERNS =====
    
    /**
     * Floor Danger Zones - Creates multiple warning zones that become dangerous
     */
    floorDangerZones(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Floor Danger Zones Attack');
        this.createWindUpIndicator(bossPos, 2000, 'Corrupting the ground...');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('floor_hazards');
        }
        
        this._setTimeout(() => {
            const zoneCount = 3 + Math.floor(intensity * 2); // 3-5 zones
            this.hazardManager.createRandomCorruptionZones(zoneCount, 12);
            
            // Add screen shake for ground corruption
            if (this.laserSystem) {
                this.laserSystem.addCameraShake(0.15, 800);
            }
        }, 2000);
    }
    
    /**
     * Arena Shrinking - Gradually reduces arena size (Phase 3 only)
     */
    arenaShrinking(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] Arena Shrinking Attack');
        this.createWindUpIndicator(bossPos, 3000, 'THE WALLS ARE CLOSING IN!');
        
        // Trigger animation on the boss
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('arena_shrink');
        }
        
        this._setTimeout(() => {
            // Calculate target size based on intensity
            const minSize = 8; // Minimum arena size
            const maxSize = 16; // Starting size for shrinking
            const targetSize = maxSize - (intensity * (maxSize - minSize));
            
            this.hazardManager.startRealityCollapse(targetSize, 0.08 + intensity * 0.04);
            
            // Add dramatic screen shake
            if (this.laserSystem) {
                this.laserSystem.addCameraShake(0.4, 2000);
            }
            
            // Show warning message
            console.log(`[NairabosAttackPatterns] Arena shrinking to ${targetSize.toFixed(1)} units!`);
            
        }, 3000);
    }
    
    // ===== NEW SKULL PATTERNS (Enhanced Visual Clarity) =====
    
    /**
     * SKULL WAVE - Wave of screamer skulls moving across arena (Phase 2)
     */
    skullWave(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] 🌊💀 SKULL WAVE - A tide of tormented souls sweeps across the battlefield!');
        
        this.addWindUpIndicator("💀 'BEHOLD THE TIDE OF THE DAMNED!' - Skull wave forming!", 2000);
        
        this._setTimeout(() => {
            const waveLength = 10 + Math.floor(intensity * 4); // 10-14 skulls
            const waveWidth = 3.0; // Width of wave formation
            
            // Create undulating wave formation
            for (let i = 0; i < waveLength; i++) {
                this._setTimeout(() => {
                    const progress = i / (waveLength - 1); // 0 to 1
                    
                    // Wave undulation
                    const undulation = Math.sin(progress * Math.PI * 3) * waveWidth;
                    
                    // Position perpendicular to boss-player line
                    const direction = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
                    const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x);
                    
                    const skullPos = bossPos.clone()
                        .add(perpendicular.clone().multiplyScalar(undulation))
                        .add(new THREE.Vector3(0, 0.5, 0));
                    
                    const waveDirection = direction.clone();
                    const speed = 4.0 + intensity;
                    
                    this.spawnProjectile(skullPos, waveDirection, speed, 'screamer_skulls');
                }, i * 100); // Stagger skull spawning
            }
            
            this.addCameraShake(0.1, 1200);
        }, 2000);
    }
    
    /**
     * SHADOW HANDS - Two large hand formations that chase the player (Phase 2)
     */
    shadowHands(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] 👐💀 SHADOW HANDS - Dark hands reach from the void to grasp the living!');
        
        this.addWindUpIndicator("👐 'THE HANDS OF THE VOID REACH FOR YOU!' - Shadow manifestation!", 2500);
        
        this._setTimeout(() => {
            // Create two hand formations
            for (let hand = 0; hand < 2; hand++) {
                const handSide = hand === 0 ? -1 : 1; // Left and right hands
                
                // Hand formation: 5 skulls in finger pattern
                const fingerPositions = [
                    { x: 0, z: 3 },     // Thumb
                    { x: 1, z: 2 },     // Index
                    { x: 1.5, z: 0.5 }, // Middle  
                    { x: 1, z: -1 },    // Ring
                    { x: 0, z: -2 }     // Pinky
                ];
                
                const handGroup = [];
                
                // Spawn skulls in hand formation
                fingerPositions.forEach((finger, fingerIndex) => {
                    this._setTimeout(() => {
                        const handCenter = bossPos.clone().add(new THREE.Vector3(handSide * 8, 1.0, 0));
                        const fingerPos = handCenter.clone().add(new THREE.Vector3(
                            finger.x * handSide, 
                            0, 
                            finger.z
                        ));
                        
                        // Store hand data for chasing behavior
                        const skullData = {
                            handSide: handSide,
                            fingerIndex: fingerIndex,
                            handCenter: handCenter,
                            chaseStartTime: performance.now() + fingerIndex * 100,
                            chaseDuration: 8000 // 8 seconds of chasing
                        };
                        
                        const projectile = this.spawnProjectile(fingerPos, new THREE.Vector3(0, 0, 0), 0, 'screamer_skulls');
                        if (projectile) {
                            projectile.userData.handData = skullData;
                            projectile.userData.isHandSkull = true;
                            handGroup.push(projectile);
                        }
                    }, fingerIndex * 150); // Stagger finger spawning
                });
                
                // Implement chasing logic
                this._setTimeout(() => {
                    const chaseInterval = setInterval(() => {
                        const currentTime = performance.now();
                        const currentPlayerPos = this.getPlayerPosition();
                        
                        handGroup.forEach(skull => {
                            if (!skull || !skull.parent || !skull.userData.handData) return;
                            
                            const handData = skull.userData.handData;
                            const elapsed = currentTime - handData.chaseStartTime;
                            
                            if (elapsed > 0 && elapsed < handData.chaseDuration) {
                                // Chase player with grasping motion
                                const chaseDirection = new THREE.Vector3()
                                    .subVectors(currentPlayerPos, skull.position)
                                    .normalize();
                                
                                const chaseSpeed = 3.0 + intensity;
                                skull.position.add(chaseDirection.multiplyScalar(chaseSpeed * 0.016)); // ~60fps
                                
                                // Grasping motion - hands slowly close
                                const closeProgress = elapsed / handData.chaseDuration;
                                const closeFactor = closeProgress * 0.3; // Hands close 30% over time
                                
                                // Move fingers toward center of hand
                                const toCenter = new THREE.Vector3()
                                    .subVectors(handData.handCenter, skull.position)
                                    .multiplyScalar(closeFactor * 0.5);
                                skull.position.add(toCenter);
                            }
                        });
                        
                        // Clean up after chase duration
                        if (currentTime - handGroup[0]?.userData?.handData?.chaseStartTime > 8000) {
                            clearInterval(chaseInterval);
                        }
                    }, 16); // ~60fps updates
                }, 1000); // Start chasing 1 second after spawning
            }
            
            this.addCameraShake(0.15, 2000);
        }, 2500);
    }
    
    /**
     * SKULL RING - Circle of skulls that contracts toward center (Phase 3)
     */
    skullRing(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] ⭕💀 SKULL RING - Circle of the damned closes in!');
        
        this.addWindUpIndicator("⭕ 'THE CIRCLE OF TORMENT CLOSES!' - Ring formation!", 2000);
        
        this._setTimeout(() => {
            const ringRadius = 12 + intensity * 2;
            const skullCount = 12 + Math.floor(intensity * 3); // 12-15 skulls
            
            for (let i = 0; i < skullCount; i++) {
                const angle = (i / skullCount) * Math.PI * 2;
                const skullPos = bossPos.clone().add(new THREE.Vector3(
                    Math.cos(angle) * ringRadius,
                    0.5,
                    Math.sin(angle) * ringRadius
                ));
                
                // Direction toward center (contracting)
                const contractDirection = new THREE.Vector3().subVectors(bossPos, skullPos).normalize();
                const speed = 2.0 + intensity * 0.5;
                
                this.spawnProjectile(skullPos, contractDirection, speed, 'screamer_skulls');
            }
            
            this.addCameraShake(0.2, 1500);
        }, 2000);
    }
    
    /**
     * SKULL SPIRAL - Expanding spiral of skulls from boss (Phase 3)
     */
    skullSpiral(bossPos, playerPos, intensity) {
        console.log('[NairabosAttackPatterns] 🌀💀 SKULL SPIRAL - Souls spiral outward in tormented formation!');
        
        this.addWindUpIndicator("🌀 'WITNESS THE SPIRAL OF ETERNAL TORMENT!' - Soul manifestation!", 2500);
        
        this._setTimeout(() => {
            const spiralArms = 2; // REDUCED: Two arms
            const skullsPerArm = 3 + Math.floor(intensity * 1); // REDUCED: 3-4 skulls per arm
            
            for (let arm = 0; arm < spiralArms; arm++) {
                const baseAngle = (arm / spiralArms) * Math.PI * 2;
                
                for (let skull = 0; skull < skullsPerArm; skull++) {
                    this._setTimeout(() => {
                        const spiralProgress = skull / (skullsPerArm - 1);
                        const spiralAngle = baseAngle + spiralProgress * Math.PI * 2 * 1.5; // 1.5 rotations
                        const distance = 2 + spiralProgress * 10; // Expanding outward
                        
                        const skullPos = bossPos.clone().add(new THREE.Vector3(
                            Math.cos(spiralAngle) * distance,
                            0.5,
                            Math.sin(spiralAngle) * distance
                        ));
                        
                        const spiralDirection = new THREE.Vector3(
                            Math.cos(spiralAngle),
                            0,
                            Math.sin(spiralAngle)
                        ).normalize();
                        
                        const speed = 5.0 + intensity + skull * 0.5; // Increasing speed
                        
                        this.spawnProjectile(skullPos, spiralDirection, speed, 'screamer_skulls');
                    }, (arm * skullsPerArm + skull) * 150); // Stagger spawning
                }
            }
            
            this.addCameraShake(0.25, 2000);
        }, 2500);
    }
    
    /**
     * DESPERATION NOVA - Ultimate signature attack for final 5% health
     * OPTIMIZATION: Staged execution to prevent frame drops
     */
    desperationNova(intensity) {
        console.log('[NairabosAttackPatterns] 🌟💀 DESPERATION NOVA INITIATED - STAGED EXECUTION!');
        
        // Epic buildup with boss speech
        this.addWindUpIndicator("🌟 'I WILL DRAG YOU TO THE VOID! WITNESS MY TRUE POWER!' - REALITY UNRAVELING!", 3500);
        
        const bossPos = this.enemy.position.clone();
        const playerPos = this.getPlayerPosition();
        
        // PHASE 1: Boss rises into air (visual indicator)
        console.log('[NairabosAttackPatterns] 🚁 The Fallen Tormentor RISES INTO THE AIR - void energy swirling!');
        if (this.dungeonHandler.camera) {
            this.addCameraShake(0.3, 2000); // Intense buildup shake
        }
        
        // OPTIMIZATION: Clear attack queue for nova priority
        this.attackQueue = [];
        
        // STAGE 1: Orbital projectiles (0-1s) - Max 15 active
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 🌀 STAGE 1: ORBITAL PROJECTILES');
            
            const orbitRadius = 4.0;
            const orbitLayers = 3;
            const projectilesPerLayer = 5; // 15 total (3x5)
            
            // Queue orbital projectiles in batches
            for (let layer = 0; layer < orbitLayers; layer++) {
                this._setTimeout(() => {
                    const layerRadius = orbitRadius + layer * 1.5;
                    const layerHeight = 2.0 + layer * 0.8;
                    
                    // Create batch function for this layer
                    const spawnLayerBatch = () => {
                        for (let i = 0; i < projectilesPerLayer; i++) {
                            const angle = (i / projectilesPerLayer) * Math.PI * 2 + layer * 0.5;
                            const projectileType = this.projectileTypes[i % this.projectileTypes.length];
                            
                            const orbitPos = bossPos.clone().add(new THREE.Vector3(
                                Math.cos(angle) * layerRadius,
                                layerHeight,
                                Math.sin(angle) * layerRadius
                            ));
                            
                            const orbitVelocity = new THREE.Vector3(
                                -Math.sin(angle),
                                0,
                                Math.cos(angle)
                            ).normalize().multiplyScalar(3.0 + layer);
                            
                            this.spawnProjectile(orbitPos, orbitVelocity, 2.0, projectileType);
                        }
                    };
                    
                    // Queue this layer
                    this._queueAttack(spawnLayerBatch, projectilesPerLayer, 10 - layer);
                }, layer * 300); // Stagger layers
            }
        }, 1000);
        
        // STAGE 2: Wave projectiles (1-2s) - Max 15 active
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 💥 STAGE 2: WAVE PROJECTILES');
            
            const waveCount = 3; // Reduced from 4
            for (let wave = 0; wave < waveCount; wave++) {
                this._setTimeout(() => {
                    const waveProjectiles = 5; // 15 total over 3 waves
                    
                    // Create batch function for this wave
                    const spawnWaveBatch = () => {
                        const waveRadius = 2 + wave * 1.5;
                        for (let i = 0; i < waveProjectiles; i++) {
                            const angle = (i / waveProjectiles) * Math.PI * 2 + wave * 0.3;
                            const waveDir = new THREE.Vector3(
                                Math.cos(angle),
                                Math.sin(wave * 0.2) * 0.1,
                                Math.sin(angle)
                            ).normalize();
                            
                            const mixedType = this.projectileTypes[(wave * waveProjectiles + i) % this.projectileTypes.length];
                            const waveSpeed = 4.0 + intensity + wave * 0.5;
                            const wavePos = bossPos.clone().add(new THREE.Vector3(0, 3, 0));
                            
                            this.spawnProjectile(wavePos, waveDir, waveSpeed, mixedType);
                        }
                    };
                    
                    // Queue this wave with lower priority than orbital
                    this._queueAttack(spawnWaveBatch, waveProjectiles, 5 - wave);
                    
                    // Screen shake
                    if (this.dungeonHandler.camera) {
                        this.addCameraShake(0.15 + wave * 0.05, 600);
                    }
                }, wave * 600); // Increased delay between waves
            }
        }, 2000);
        
        // STAGE 3: Corruption zones (2-3s)
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 🌀 STAGE 3: CORRUPTION ZONES');
            
            // Limited corruption zones
            if (this.hazardManager) {
                this.hazardManager.createRandomCorruptionZones(3, 10); // Reduced from 5
            }
            
            // Moderate screen shake
            if (this.dungeonHandler.camera) {
                this.addCameraShake(0.25, 1500);
            }
        }, 3000);
        
        // STAGE 4: Final laser (3-4s)
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 💀 STAGE 4: FINAL LASER - REALITY TEARS!');
            
            // Final laser sweep without overwhelming effects
            if (this.laserSystem) {
                const novaDirection = new THREE.Vector3().subVectors(playerPos, bossPos).normalize();
                this.laserSystem.createWarning(bossPos, playerPos, 2000, 'REALITY TEARS APART!');
                this._setTimeout(() => {
                    this.laserSystem.fireLaser(bossPos, playerPos, 2000, 3);
                }, 2200);
            }
        }, 4000);
        
        // FINAL MESSAGE: Boss ultimate speech
        this._setTimeout(() => {
            console.log('[NairabosAttackPatterns] 💀 "IF I CANNOT RULE... THEN ALL SHALL PERISH IN THE VOID!"');
        }, 6000);
    }
    
    /**
     * Clean up all active projectiles, hazards, and visual effects
     * CRITICAL: Called when Nairabos dies to remove ALL remaining attack patterns
     */
    cleanup() {
        console.log('[NairabosAttackPatterns] 🧹 COMPREHENSIVE CLEANUP: Removing all remaining boss attack elements');
        
        // Clean up active projectiles
        if (this.activeProjectiles) {
            console.log(`[NairabosAttackPatterns] Removing ${this.activeProjectiles.length} active projectiles`);
            this.activeProjectiles.forEach(projectile => {
                if (projectile && projectile.parent) {
                    projectile.parent.remove(projectile);
                }
            });
            this.activeProjectiles = [];
        }
        
        // Clean up bullet groups (floating, swaying patterns)
        if (this.activeBulletGroups) {
            console.log(`[NairabosAttackPatterns] Removing ${this.activeBulletGroups.length} active bullet groups`);
            this.activeBulletGroups.forEach(group => {
                if (group && group.cleanup) {
                    group.cleanup();
                }
            });
            this.activeBulletGroups = [];
        }
        
        // Clean up shadow minions
        if (this.shadowMinions) {
            console.log(`[NairabosAttackPatterns] Removing ${this.shadowMinions.length} shadow minions`);
            this.shadowMinions.forEach(minion => {
                try {
                    this.removeShadowMinion(minion.entity);
                } catch (error) {
                    console.warn('[NairabosAttackPatterns] Error removing shadow minion:', error);
                }
            });
            this.shadowMinions = [];
        }
        
        // Clean up wind-up indicators
        if (this.windUpIndicators) {
            console.log(`[NairabosAttackPatterns] Removing ${this.windUpIndicators.length} wind-up indicators`);
            this.windUpIndicators = [];
        }
        
        // CRITICAL: Clean up laser system (removes laser beams and warnings)
        if (this.laserSystem) {
            console.log('[NairabosAttackPatterns] 🔥 Cleaning up laser system (beams, warnings, effects)');
            this.laserSystem.cleanup();
        }
        
        // CRITICAL: Clean up hazard system (removes floor zones, arena effects)
        if (this.hazardManager) {
            console.log('[NairabosAttackPatterns] 🌊 Cleaning up hazard manager (floor zones, arena shrinking)');
            this.hazardManager.cleanup();
        }
        
        // CRITICAL: Clear all pending timeouts and intervals that might spawn more projectiles
        if (this._activeTimeouts) {
            console.log(`[NairabosAttackPatterns] Clearing ${this._activeTimeouts.length} pending attack timeouts`);
            this._activeTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            this._activeTimeouts = [];
        }
        
        // Reset attack counters
        this.currentAttackProjectileCount = 0;
        this.combinationProjectileCount = undefined;
        
        // CRITICAL: Stop desperation mode effects
        if (this.isDesperationMode) {
            console.log('[NairabosAttackPatterns] 💀 Ending desperation mode effects');
            this.isDesperationMode = false;
            
            // Clear desperation visual effects from scene
            if (this.dungeonHandler && this.dungeonHandler.scene && this.dungeonHandler.scene.userData.desperationMode) {
                this.dungeonHandler.scene.userData.desperationMode.active = false;
                delete this.dungeonHandler.scene.userData.desperationMode;
            }
        }
        
        console.log('[NairabosAttackPatterns] ✅ CLEANUP COMPLETE: All boss attack patterns, projectiles, hazards, and effects removed');
    }
}