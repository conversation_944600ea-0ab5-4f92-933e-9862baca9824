/**
 * Chronarch Boss Controller
 * Specialized boss controller for "Chrona<PERSON> the Temporal Alchemist"
 * Implements alchemical attack patterns with potion throwing, crystal manipulation, and time magic
 */
import * as THREE from 'three';
import { 
    createCrystallineEruption, 
    createShardProjectile, 
    createTimeDilationField, 
    createTemporal<PERSON>cho, 
    createRealityFracture 
} from '../../generators/prefabs/chronarchAttackObjects.js';

export class ChronarchBossController {
    constructor(enemy, dungeonHandler, audioManager) {
        this.enemy = enemy;
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;
        
        // Boss state (5x increased health)
        this.maxHealth = enemy.userData.health || 750;
        this.currentHealth = enemy.userData.health || 750;
        this.currentPhase = 1;
        this.isActive = false;
        this.isShielded = false;
        this.lastPhaseChangeTime = 0;
        
        // Phase thresholds (HP percentages) - Epic environmental boss design
        this.phaseThresholds = {
            phase1: 1.0,    // 100% - 60% (THE UNRAVELING - Environmental Control)
            phase2: 0.6,    // 60% - 25% (BENDING TIME - Time Manipulation)  
            phase3: 0.25,   // 25% - 0% (REALITY COLLAPSE - Ultimate Chaos)
        };
        
        // Attack pattern timing (balanced like Nairabos)
        this.lastAttackTime = 0;
        this.attackCooldowns = {
            1: 4.5,  // Phase 1: 4.5 seconds (more forgiving start, like Nairabos)
            2: 3.0,  // Phase 2: 3.0 seconds (matches Nairabos Phase 1)  
            3: 1.8   // Phase 3: 1.8 seconds (close to Nairabos Phase 3 of 1.4s)
        };
        
        // EPIC ENVIRONMENTAL ATTACK PATTERNS - Large-scale reality manipulation!
        this.attackPatterns = {
            // 🔮 PHASE 1: THE UNRAVELING (100% - 60% Health) - Environmental Control
            phase1: [
                'crystalline_eruption',  // Massive time crystals erupt from floor in formation
                'shard_volley',          // Summons room object, shatters it into large projectile shards
                'temporal_device_assault', // Hijacks room devices to attack player
                'pillar_slam',           // Levitates and slams worn pillars at player location
                'crystal_barrier'        // Creates defensive crystal walls that reshape arena
            ],
            // 🔮 PHASE 2: BENDING TIME (60% - 25% Health) ⏰ - Time Manipulation
            phase2: [
                'time_dilation_field',   // Creates large sphere that slows everything inside
                'echoes_of_the_past',    // Summons ghostly temporal echoes that repeat attacks
                'chronostatic_prison',   // Freezes floating objects then releases as weapons
                'temporal_rift_pulse',   // Central rift sends out expanding energy waves
                'reality_fracture'       // Cracks appear in space, creating hazardous zones
            ],
            // 🔮 PHASE 3: REALITY COLLAPSE (25% - 0% Health) 🌟 - Ultimate Environmental Chaos
            phase3: [
                'rift_collapse_ultimate', // Floor breaks into floating platforms, becomes platformer
                'platform_shard_volley',  // Fast shard attacks while player jumps platforms
                'crystal_cascade',        // Crystals rain from above onto platforms
                'temporal_storm',         // Multiple time effects active simultaneously
                'final_transmutation'     // Last-ditch reality warping attack
            ]
        };
        
        this.currentAttackPattern = 0;
        
        // Shield system
        this.shieldDuration = 4.0; // 4 seconds of protection
        this.shieldCooldown = 20.0; // 20 seconds between shields
        this.lastShieldTime = 0;
        this.shieldStartTime = 0;
        
        // Movement handled by BossCombatAI parent class
        // Only keep floating animation properties
        this.floatHeight = 2.0;
        this.homePosition = enemy.position.clone();
        
        // Mark as floating enemy to prevent floor adaptation conflicts
        enemy.userData.isFloating = true;
        
        // Player tracking
        this.playerPosition = null;
        this.playerDistance = 999;
        this.optimalRange = 6.0; // Preferred distance from player
        this.minRange = 3.0; // Minimum distance before backing away
        this.maxRange = 10.0; // Maximum attack range
        
        // EPIC ENVIRONMENTAL ATTACK SYSTEM
        this.environmentalObjects = []; // Track room objects for manipulation
        this.activeTimeDilationFields = []; // Track active time dilation spheres
        this.temporalEchoes = []; // Track ghostly boss echoes
        this.floatingPlatforms = []; // Track platform pieces during ultimate
        this.activeEnvironmentalEffects = []; // Track all environmental attacks
        
        // Large-scale attack properties
        this.crystalEruptionSize = 3.0; // Massive crystal size (3 meters)
        this.shardProjectileSize = 2.0; // Large shard size (2 meters)
        this.timeDilationRadius = 8.0; // 8 meter radius dilation field
        this.platformChunkSize = 4.0; // Platform chunk size for ultimate
        
        // Animation system integration
        this.animationHandler = null;
        this.currentAnimationState = 'HOVERING';
        
        // Attack pattern repetition system (less predictable like Nairabos)
        this.attackRepeatCount = 0;
        this.attacksPerPattern = 1; // Only 1 repeat per pattern (more variety, less predictable)
        
        // Ultimate phase state tracking
        this.ultimatePhaseActive = false;
        this.originalFloorHeight = 0;
        this.platformJumpingActive = false;
        
        // Wind-up/telegraphing system for major attacks (like Nairabos)
        this.isWindingUp = false;
        this.windUpStartTime = 0;
        this.windUpDuration = 1500; // 1.5 seconds wind-up for major attacks
        this.pendingAttack = null;
        this.currentExecutingAttack = null;
        this.attackStartTime = 0;
        
        // Major attacks that require wind-up (like Nairabos does)
        this.majorAttacks = new Set([
            'giant_potion_brew', 'toxic_cloud_burst'
        ]);
        
        // Speech bubble system - Unique alchemical dialogue
        this.speechBubble = null;
        this.speechTexts = {
            start: "Behold the master of time and alchemy!",
            phase1To2: "My temporal powers awaken... time bends to my will!",
            phase2To3: "Reality itself shall be transmuted!",
            lowHealth: "Impossible! My perfect formulas cannot fail!",
            shieldActivate: "Protective reagents, shield me!",
            // Phase 1 - Brewing Master
            giant_potion_brew: "Witness my grandest creation!",
            toxic_cloud_burst: "Breathe deep my toxic perfection!",
            liquid_wave: "Let the alchemical tides consume you!",
            reagent_shower: "Taste my rainbow of destruction!",
            transmutation_circle: "The very ground serves my will!",
            // Phase 2 - Temporal Manipulator  
            crystalline_eruption: "Crystal spires pierce the void!",
            shard_volley: "Temporal shards rain from above!",
            time_dilation_field: "Time itself bends to my will!",
            reality_fracture: "Reality cracks under my power!",
            defeat: "No... my perfect philosophy... cannot... be... wrong..."
        };
        
        console.log('[ChronarchBossController] Initialized for "Chronarch the Temporal Alchemist"');
    }
    
    /**
     * Start the boss fight
     */
    start() {
        console.log('[ChronarchBossController] 🔮 Boss fight started!');
        console.log(`[ChronarchBossController] 🔮 Initial state - currentPhase: ${this.currentPhase}, maxRange: ${this.maxRange}`);
        console.log(`[ChronarchBossController] 🔮 Attack patterns:`, this.attackPatterns);
        
        this.isActive = true;
        this.lastAttackTime = Date.now() * 0.001;
        
        // Set initial floating position
        this.enemy.position.y = this.homePosition.y + this.floatHeight;
        
        console.log(`[ChronarchBossController] 🔮 Controller activated - isActive: ${this.isActive}`);
        
        // Show initial speech bubble
        this.showSpeechBubble(this.speechTexts.start, 3000);
        
        // Play boss music if available
        if (this.audioManager && this.audioManager.playSound) {
            this.audioManager.playSound('boss_music');
        }
        
        // Set initial phase
        this.setPhase(1);
        
        console.log(`[ChronarchBossController] 🔮 Boss started with phase ${this.currentPhase}`);
        
        // Let base AI handle positioning - removed custom position override
    }
    
    /**
     * Update boss AI
     */
    update(deltaTime) {
        if (!this.isActive) {
            console.log(`[ChronarchBossController] 🔮 update() called but controller not active`);
            return;
        }
        
        const currentTime = Date.now() * 0.001;
        
        // Update player tracking
        this.updatePlayerTracking();
        
        // Update phase based on health
        this.updatePhase();
        
        // Movement is handled by BossCombatAI parent class
        // Only update floating animation
        this.updateFloatingAnimation(currentTime);
        
        // Update shield state
        this.updateShield(currentTime);
        
        // Enable internal attack timing like Nairabos - this ensures attacks happen regardless of BossCombatAI
        this.updateAttacks(currentTime);
        
        // Update wind-up system
        this.updateWindUp(currentTime);
        
        // Update animation state
        this.updateAnimations();
        
        // Update speech bubbles
        this.updateSpeechBubbles(deltaTime);
    }
    
    /**
     * Update wind-up system (like Nairabos)
     */
    updateWindUp(currentTime) {
        if (this.isWindingUp) {
            const windUpElapsed = (currentTime - this.windUpStartTime) * 1000; // Convert to ms
            const progress = windUpElapsed / this.windUpDuration;
            
            // Visual indicator could be added here (like Nairabos shadow aura)
            // For now, just ensure the wind-up completes properly
            
            if (progress >= 1.0) {
                this.isWindingUp = false;
                this.executePendingAttack();
            }
        }
    }
    
    /**
     * Update player position tracking
     */
    updatePlayerTracking() {
        if (this.dungeonHandler && this.dungeonHandler.player) {
            this.playerPosition = this.dungeonHandler.player.position;
            this.playerDistance = this.enemy.position.distanceTo(this.playerPosition);
        }
    }
    
    /**
     * Update boss phase based on health
     */
    updatePhase() {
        const healthPercentage = this.currentHealth / this.maxHealth;
        let newPhase = 1;
        
        console.log(`[ChronarchBossController] 🔮 Health check - ${this.currentHealth}/${this.maxHealth} = ${(healthPercentage * 100).toFixed(1)}%`);
        
        if (healthPercentage <= this.phaseThresholds.phase3) {
            newPhase = 3;
        } else if (healthPercentage <= this.phaseThresholds.phase2) {
            newPhase = 2;
        }
        
        if (newPhase !== this.currentPhase) {
            console.log(`[ChronarchBossController] 🔮 PHASE CHANGE - ${this.currentPhase} → ${newPhase}`);
            this.setPhase(newPhase);
        }
    }
    
    /**
     * Set boss phase
     */
    setPhase(phase) {
        console.log(`[ChronarchBossController] 🔮 Entering Phase ${phase}`);
        this.currentPhase = phase;
        this.lastPhaseChangeTime = Date.now() * 0.001;
        this.currentAttackPattern = 0; // Reset attack pattern rotation
        this.attackRepeatCount = 0; // Reset repeat counter for new phase
        
        // Phase-specific setup with speech bubbles
        switch(phase) {
            case 1:
                this.showPhaseMessage("The Temporal Alchemist awakens!");
                break;
            case 2:
                this.showSpeechBubble(this.speechTexts.phase1To2, 2500);
                this.activateShield(); // Shield on phase transition
                break;
            case 3:
                this.showSpeechBubble(this.speechTexts.phase2To3, 2500);
                this.activateShield(); // Shield on phase transition
                break;
        }
    }
    
    /**
     * Update floating animation only - movement handled by BossCombatAI
     */
    updateFloatingAnimation(currentTime) {
        // Update home position to follow boss's current X/Z position
        // This ensures floating animation works with BossCombatAI movement
        this.homePosition.x = this.enemy.position.x;
        this.homePosition.z = this.enemy.position.z;
        
        // Apply smooth floating animation to Y position
        const targetY = this.homePosition.y + this.floatHeight + 
            Math.sin(currentTime * 1.5) * 0.3; // Gentle floating
        
        // Use smaller lerp factor for smoother, less jarring transitions
        this.enemy.position.y = THREE.MathUtils.lerp(
            this.enemy.position.y, 
            targetY, 
            0.05 // Reduced lerp factor for smoother animation
        );
    }
    
    /**
     * Update shield state
     */
    updateShield(currentTime) {
        if (this.isShielded) {
            const shieldDuration = currentTime - this.shieldStartTime;
            if (shieldDuration >= this.shieldDuration) {
                this.deactivateShield();
            }
        }
    }
    
    /**
     * Update attack patterns
     */
    updateAttacks(currentTime) {
        console.log(`[ChronarchBossController] 🔮 updateAttacks() - isShielded: ${this.isShielded}, playerPosition: ${this.playerPosition ? 'exists' : 'null'}`);
        
        if (this.isShielded || !this.playerPosition) {
            if (this.isShielded) console.log(`[ChronarchBossController] 🔮 Skipping attack - boss is shielded`);
            if (!this.playerPosition) console.log(`[ChronarchBossController] 🔮 Skipping attack - no player position`);
            return;
        }
        
        const timeSinceLastAttack = currentTime - this.lastAttackTime;
        const cooldown = this.attackCooldowns[this.currentPhase];
        
        console.log(`[ChronarchBossController] 🔮 Attack timing - timeSinceLastAttack: ${timeSinceLastAttack.toFixed(2)}s, cooldown: ${cooldown}s, playerDistance: ${this.playerDistance.toFixed(2)}, maxRange: ${this.maxRange}`);
        
        if (timeSinceLastAttack >= cooldown && this.playerDistance <= this.maxRange) {
            console.log(`[ChronarchBossController] 🔮 ATTACK CONDITIONS MET - calling executeAttack()`);
            this.executeAttack();
            this.lastAttackTime = currentTime;
        } else {
            console.log(`[ChronarchBossController] 🔮 Attack conditions not met - waiting...`);
        }
    }
    
    /**
     * Execute an attack based on current phase (with wind-up system like Nairabos)
     */
    executeAttack() {
        const currentTime = Date.now() * 0.001;
        console.log(`[ChronarchBossController] 🔮 executeAttack() called externally - Phase: ${this.currentPhase}, Time: ${currentTime.toFixed(2)}`);
        
        // Check if we're currently winding up an attack
        if (this.isWindingUp) {
            const windUpElapsed = currentTime - this.windUpStartTime;
            if (windUpElapsed >= this.windUpDuration / 1000) {
                // Wind-up complete, execute the pending attack
                this.isWindingUp = false;
                this.executePendingAttack();
            }
            return; // Still winding up
        }
        
        // Check if enough time has passed since last attack to prevent spam
        const timeSinceLastAttack = currentTime - this.lastAttackTime;
        const cooldown = this.attackCooldowns[this.currentPhase];
        
        if (timeSinceLastAttack < cooldown) {
            console.log(`[ChronarchBossController] 🔮 Attack called too soon - ${timeSinceLastAttack.toFixed(2)}s < ${cooldown}s cooldown`);
            return; // Still on cooldown
        }
        
        const phaseAttacks = this.attackPatterns[`phase${this.currentPhase}`];
        console.log(`[ChronarchBossController] 🔮 Phase attacks for phase ${this.currentPhase}:`, phaseAttacks);
        
        if (!phaseAttacks || phaseAttacks.length === 0) {
            console.warn(`[ChronarchBossController] 🔮 No attacks found for phase ${this.currentPhase}`);
            return;
        }
        
        // Select current attack pattern
        const attackType = phaseAttacks[this.currentAttackPattern];
        console.log(`[ChronarchBossController] 🔮 Selected attack type: ${attackType} (pattern ${this.currentAttackPattern}/${phaseAttacks.length}, repeat ${this.attackRepeatCount + 1}/${this.attacksPerPattern})`);
        
        // Increment repeat counter
        this.attackRepeatCount++;
        
        // Switch to next attack pattern only after repeating current attack enough times
        if (this.attackRepeatCount >= this.attacksPerPattern) {
            this.currentAttackPattern = (this.currentAttackPattern + 1) % phaseAttacks.length;
            this.attackRepeatCount = 0; // Reset repeat counter for new attack
            console.log(`[ChronarchBossController] 🔮 Switching to next attack pattern: ${phaseAttacks[this.currentAttackPattern]}`);
        }
        
        // Check if this is a major attack that requires wind-up
        if (this.majorAttacks.has(attackType)) {
            console.log(`[ChronarchBossController] 🔮 Starting wind-up for major attack: ${attackType}`);
            this.startWindUp(attackType);
            return; // Will execute after wind-up
        }
        
        // Execute minor attacks immediately
        this.lastAttackTime = currentTime;
        this.executeAttackLogic(attackType);
    }
    
    /**
     * Start wind-up for a major attack (like Nairabos)
     */
    startWindUp(attackType) {
        this.isWindingUp = true;
        this.windUpStartTime = Date.now() * 0.001;
        this.pendingAttack = attackType;
        
        // Show wind-up speech bubble
        if (this.speechTexts[attackType]) {
            this.showSpeechBubble(`Preparing... ${this.speechTexts[attackType]}`, this.windUpDuration);
        }
        
        // Trigger wind-up animation
        if (this.animationHandler) {
            this.animationHandler.triggerWindUp(attackType);
        }
        
        console.log(`[ChronarchBossController] 🔮 Wind-up started for ${attackType} (${this.windUpDuration}ms)`);
    }
    
    /**
     * Execute the pending attack after wind-up completes
     */
    executePendingAttack() {
        if (!this.pendingAttack) return;
        
        const attackType = this.pendingAttack;
        this.pendingAttack = null;
        
        // Record attack time
        this.lastAttackTime = Date.now() * 0.001;
        
        console.log(`[ChronarchBossController] 🔮 Wind-up complete! Executing ${attackType} attack`);
        this.executeAttackLogic(attackType);
    }
    
    /**
     * Execute the actual attack logic
     */
    executeAttackLogic(attackType) {
        console.log(`[ChronarchBossController] 🔮 Executing ${attackType} attack`);
        
        // Track current executing attack
        this.currentExecutingAttack = attackType;
        this.attackStartTime = Date.now();
        
        // Clear the executing attack after a reasonable duration (3 seconds)
        setTimeout(() => {
            if (this.currentExecutingAttack === attackType) {
                this.currentExecutingAttack = null;
                this.attackStartTime = 0;
            }
        }, 3000);
        
        // Trigger animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttack(attackType);
        }

        console.log(`[ChronarchBossController] 🔮 Entering switch statement for attack type: ${attackType}`);
        
        // Execute attack logic based on EPIC ENVIRONMENTAL PATTERNS
        switch(attackType) {
            // 🔮 PHASE 1: THE UNRAVELING (100% - 60% Health) - Environmental Control
            case 'crystalline_eruption':
                console.log(`[ChronarchBossController] ⚡ EPIC ATTACK: Crystalline Eruption - Massive crystal spikes!`);
                this.executeCrystallineEruption();
                break;
            case 'shard_volley':
                console.log(`[ChronarchBossController] 💎 EPIC ATTACK: Shard Volley - Large projectile shards!`);
                this.executeShardVolley();
                break;
            case 'temporal_device_assault':
                console.log(`[ChronarchBossController] 🔧 EPIC ATTACK: Temporal Device Assault - Hijacked room devices!`);
                this.executeTemporalDeviceAssault();
                break;
            case 'pillar_slam':
                console.log(`[ChronarchBossController] 🗿 EPIC ATTACK: Pillar Slam - Levitated pillars!`);
                this.executePillarSlam();
                break;
            case 'crystal_barrier':
                console.log(`[ChronarchBossController] 🛡️ EPIC ATTACK: Crystal Barrier - Defensive walls!`);
                this.executeCrystalBarrier();
                break;
                
            // 🔮 PHASE 2: BENDING TIME (60% - 25% Health) ⏰ - Time Manipulation
            case 'time_dilation_field':
                console.log(`[ChronarchBossController] ⏰ EPIC ATTACK: Time Dilation Field - Slowdown sphere!`);
                this.executeTimeDilationField();
                break;
            case 'echoes_of_the_past':
                console.log(`[ChronarchBossController] 👻 EPIC ATTACK: Echoes of the Past - Temporal echoes!`);
                this.executeEchoesOfThePast();
                break;
            case 'chronostatic_prison':
                console.log(`[ChronarchBossController] ⏸️ EPIC ATTACK: Chronostatic Prison - Time freeze!`);
                this.executeChronostaticPrison();
                break;
            case 'temporal_rift_pulse':
                console.log(`[ChronarchBossController] 💥 EPIC ATTACK: Temporal Rift Pulse - Energy waves!`);
                this.executeTemporalRiftPulse();
                break;
            case 'reality_fracture':
                console.log(`[ChronarchBossController] ⚡ EPIC ATTACK: Reality Fracture - Space cracks!`);
                this.executeRealityFracture();
                break;
                
            // 🔮 PHASE 3: REALITY COLLAPSE (25% - 0% Health) 🌟 - Ultimate Environmental Chaos
            case 'rift_collapse_ultimate':
                console.log(`[ChronarchBossController] 🌟 ULTIMATE ATTACK: Rift Collapse - Floating platforms!`);
                this.executeRiftCollapseUltimate();
                break;
            case 'platform_shard_volley':
                console.log(`[ChronarchBossController] 🗿 ULTIMATE ATTACK: Platform Shard Volley - Flying chunks!`);
                this.executePlatformShardVolley();
                break;
            case 'crystal_cascade':
                console.log(`[ChronarchBossController] 💎 ULTIMATE ATTACK: Crystal Cascade - Crystal waterfall!`);
                this.executeCrystalCascade();
                break;
            case 'temporal_storm':
                console.log(`[ChronarchBossController] ⛈️ ULTIMATE ATTACK: Temporal Storm - Time chaos!`);
                this.executeTemporalStorm();
                break;
            case 'final_transmutation':
                console.log(`[ChronarchBossController] 🌟 ULTIMATE ATTACK: Final Transmutation - Reality warping!`);
                this.executeFinalTransmutation();
                break;
                
            // Legacy attack patterns (fallback compatibility)
                
            // Fallback for any missing patterns
            default:
                console.warn(`[ChronarchBossController] ⚠️ UNKNOWN ATTACK: ${attackType} - using basic potion fallback`);
                this.executePotionBarrage();
                break;
        }
    }
    
    /**
     * Execute potion barrage attack
     */
    executePotionBarrage() {
        if (!this.playerPosition) return;
        
        const potionCount = this.currentPhase === 1 ? 3 : (this.currentPhase === 2 ? 4 : 5);
        
        for (let i = 0; i < potionCount; i++) {
            setTimeout(() => {
                this.throwPotion(i * 0.3); // Stagger timing
            }, i * 200);
        }
    }
    
    /**
     * Throw a single potion projectile with advanced targeting
     */
    throwPotion(delay = 0) {
        if (!this.playerPosition || !this.dungeonHandler.spawnProjectile) return;
        
        // Cycle through projectile types for variety
        const projectileType = this.projectileTypes[this.currentProjectileType];
        this.currentProjectileType = (this.currentProjectileType + 1) % this.projectileTypes.length;
        this.projectileSpawnCount++;
        
        // Calculate smart direction with player prediction
        const targetPosition = this.playerPosition.clone();
        
        // Add intelligent spread based on phase
        const spreadAmount = this.currentPhase === 1 ? 1.5 : (this.currentPhase === 2 ? 1.0 : 0.5);
        targetPosition.x += (Math.random() - 0.5) * spreadAmount;
        targetPosition.z += (Math.random() - 0.5) * spreadAmount;
        
        const direction = new THREE.Vector3()
            .subVectors(targetPosition, this.enemy.position)
            .normalize();
        
        // Create projectile data object using correct format
        const projectileData = {
            projectileType: projectileType,
            projectileSpeed: 12.0 + (this.currentPhase * 3.0), // Speed increases with phase
            disableTrails: false,
            type: 'chronarch',
            health: 1,
            isProjectile: true,
            aiType: 'flying',
            owner: this.enemy,
            damage: (this.enemy.userData.damage || 25) + (this.currentPhase * 5),
            // Alchemical visual effects
            glowIntensity: 1.5,
            emissiveIntensity: 0.8,
            alchemicalAura: true,
            temporalTheme: true,
            scale: 1.2
        };
        
        // Spawn projectile with correct signature
        const projectile = this.dungeonHandler.spawnProjectile(
            this.enemy.position.clone(),
            direction,
            projectileData
        );
        
        if (projectile) {
            // Enhanced projectile properties based on type
            switch(projectileType) {
                case 'alchemical_symbols':
                    projectile.userData.isExplosive = true;
                    projectile.userData.explosionRadius = 2.5;
                    break;
                case 'poison_cloud':
                    projectile.userData.isPoisonous = true;
                    projectile.userData.cloudRadius = 3.0;
                    break;
                case 'rotating_sigils':
                    projectile.userData.isHoming = true;
                    projectile.userData.homingStrength = 0.05;
                    break;
                case 'sacred_geometry':
                    projectile.userData.isExplosive = true;
                    projectile.userData.explosionRadius = 3.0;
                    projectile.userData.damage *= 1.5;
                    break;
            }
            
            // Add alchemical glowing effect for magical projectiles
            if (projectile.material) {
                projectile.material.emissive = new THREE.Color(0x8A2BE2);
                projectile.material.emissiveIntensity = 0.3;
            }
        }
        
        console.log(`[ChronarchBossController] 🔮 Launched ${projectileType} projectile (${this.projectileSpawnCount})`);
    }
    
    /**
     * Execute crystal prison attack
     */
    executeCrystalPrison() {
        if (!this.playerPosition) return;
        
        console.log(`[ChronarchBossController] 🔮 Creating crystal prison around player`);
        
        // First, throw some projectiles as part of the attack
        this.throwPotion(); // Immediate projectile
        setTimeout(() => this.throwPotion(), 300); // Second projectile
        setTimeout(() => this.throwPotion(), 600); // Third projectile
        
        // Create crystal barriers around player position (visual effect)
        const crystalPositions = [
            { x: 2, z: 0 }, { x: -2, z: 0 }, { x: 0, z: 2 }, { x: 0, z: -2 },
            { x: 1.5, z: 1.5 }, { x: -1.5, z: -1.5 }, { x: 1.5, z: -1.5 }, { x: -1.5, z: 1.5 }
        ];
        
        crystalPositions.forEach((offset, index) => {
            setTimeout(() => {
                const crystalPos = new THREE.Vector3(
                    this.playerPosition.x + offset.x,
                    this.playerPosition.y,
                    this.playerPosition.z + offset.z
                );
                
                // Spawn crystal barrier (temporary obstacle)
                this.spawnCrystalBarrier(crystalPos);
            }, index * 100);
        });
    }
    
    /**
     * Spawn a temporary crystal barrier
     */
    spawnCrystalBarrier(position) {
        // This would spawn a temporary crystal obstacle
        // For now, just create a visual effect
        console.log(`[ChronarchBossController] 🔮 Spawning crystal barrier at`, position);
        
        // TODO: Create actual crystal barrier object with collision
        // This would need integration with the dungeon's obstacle system
    }
    
    /**
     * Execute time warp attack
     */
    executeTimeWarp() {
        console.log(`[ChronarchBossController] 🔮 Casting time warp - slowing player`);
        
        // Fire temporal projectiles as part of the attack
        this.throwPotion(); // Immediate projectile
        setTimeout(() => this.throwPotion(), 400); // Delayed projectile
        
        // Apply time slow effect to player
        if (this.dungeonHandler.player && this.dungeonHandler.player.userData) {
            const originalSpeed = this.dungeonHandler.player.userData.moveSpeed || 1.0;
            this.dungeonHandler.player.userData.moveSpeed = originalSpeed * 0.3; // 70% slow
            
            // Remove effect after duration
            setTimeout(() => {
                if (this.dungeonHandler.player && this.dungeonHandler.player.userData) {
                    this.dungeonHandler.player.userData.moveSpeed = originalSpeed;
                    console.log(`[ChronarchBossController] 🔮 Time warp effect ended`);
                }
            }, 4000); // 4 second duration
        }
    }
    
    /**
     * Execute alchemical explosion attack
     */
    executeAlchemicalExplosion() {
        if (!this.playerPosition) return;
        
        console.log(`[ChronarchBossController] 🔮 Preparing alchemical explosion`);
        
        // Fire explosive projectiles in multiple directions
        for (let i = 0; i < 5; i++) {
            setTimeout(() => this.throwPotion(), i * 200);
        }
        
        // Warning phase - show danger zone
        const explosionCenter = this.playerPosition.clone();
        
        // After 1.5 second delay, explode
        setTimeout(() => {
            const distance = this.playerPosition.distanceTo(explosionCenter);
            const explosionRadius = 3.5;
            
            if (distance <= explosionRadius) {
                // Player is in explosion range
                const damage = (this.enemy.userData.damage || 25) * 1.5; // 50% more damage
                console.log(`[ChronarchBossController] 🔮 Player caught in alchemical explosion!`);
                
                // Apply damage (would need damage system integration)
                // this.dungeonHandler.playerController?.takeDamage(damage);
            }
        }, 1500);
    }
    
    /**
     * Execute reagent shield attack
     */
    executeReagentShield() {
        console.log(`[ChronarchBossController] 🔮 Activating reagent shield`);
        this.activateShield();
    }
    
    /**
     * Activate shield
     */
    activateShield() {
        const currentTime = Date.now() * 0.001;
        
        if (currentTime - this.lastShieldTime < this.shieldCooldown) {
            return; // Shield on cooldown
        }
        
        this.isShielded = true;
        this.shieldStartTime = currentTime;
        this.lastShieldTime = currentTime;
        
        // Show shield activation speech
        this.showSpeechBubble(this.speechTexts.shieldActivate, 2000);
        
        console.log(`[ChronarchBossController] 🔮 Shield activated for ${this.shieldDuration} seconds`);
        
        // Visual shield effect would be added here
    }
    
    /**
     * Deactivate shield
     */
    deactivateShield() {
        this.isShielded = false;
        console.log(`[ChronarchBossController] 🔮 Shield deactivated`);
    }
    
    /**
     * Update animation states
     */
    updateAnimations() {
        let newState = 'HOVERING';
        
        if (this.isShielded) {
            newState = 'SHIELDED';
        } else if (this.playerDistance <= this.maxRange) {
            newState = 'APPROACHING';
        }
        
        if (newState !== this.currentAnimationState) {
            this.currentAnimationState = newState;
            if (this.animationHandler) {
                // Animation handler will pick up the state in its update
            }
        }
    }
    
    /**
     * Take damage
     */
    takeDamage(damage) {
        if (this.isShielded) {
            console.log(`[ChronarchBossController] 🔮 Damage blocked by shield`);
            return false;
        }
        
        this.currentHealth = Math.max(0, this.currentHealth - damage);
        console.log(`[ChronarchBossController] 🔮 Health: ${this.currentHealth}/${this.maxHealth}`);
        
        // Update enemy health
        if (this.enemy.userData) {
            this.enemy.userData.health = this.currentHealth;
        }
        
        if (this.currentHealth <= 0) {
            this.defeat();
        }
        
        return true;
    }
    
    /**
     * Boss defeat
     */
    defeat() {
        console.log(`[ChronarchBossController] 🔮 Chronarch has been defeated!`);
        this.isActive = false;
        
        // Show defeat speech bubble
        this.showSpeechBubble(this.speechTexts.defeat, 4000);
        
        // Play victory music
        if (this.audioManager && this.audioManager.playSound) {
            this.audioManager.playSound('victory_music');
        }
        
        // Clean up speech bubble after delay
        setTimeout(() => {
            this.removeSpeechBubble();
        }, 5000);
        
        // Boss death would be handled by the dungeon system
    }
    
    /**
     * Show phase transition message
     */
    showPhaseMessage(message) {
        console.log(`[ChronarchBossController] 🔮 ${message}`);
        // This would show a speech bubble or UI message
    }
    
    /**
     * Set animation handler reference
     */
    setAnimationHandler(handler) {
        this.animationHandler = handler;
    }
    
    /**
     * Get current state for external systems
     */
    getState() {
        return {
            phase: this.currentPhase,
            health: this.currentHealth,
            maxHealth: this.maxHealth,
            isShielded: this.isShielded,
            isActive: this.isActive,
            animationState: this.currentAnimationState
        };
    }

    /**
     * Show speech bubble above boss
     */
    showSpeechBubble(text, duration = 3000) {
        // Remove existing speech bubble first
        this.removeSpeechBubble();
        
        // Create new speech bubble
        this.speechBubble = document.createElement('div');
        this.speechBubble.className = 'boss-speech-bubble';
        this.speechBubble.innerHTML = `
            <div class="boss-speech-content">
                <div class="boss-speech-text">${text}</div>
            </div>
        `;

        // Style the speech bubble (retro/terminal style)
        this.speechBubble.style.cssText = `
            position: absolute;
            z-index: 2000;
            pointer-events: none;
            opacity: 0;
            transform: translateX(-50%);
            transition: opacity 0.5s ease-in-out;
        `;

        const speechContent = this.speechBubble.querySelector('.boss-speech-content');
        speechContent.style.cssText = `
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            padding: 10px 15px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 1px;
        `;

        // Add to document
        document.body.appendChild(this.speechBubble);

        // Position the speech bubble
        this.updateSpeechBubblePosition();

        // Fade in
        setTimeout(() => {
            if (this.speechBubble) {
                this.speechBubble.style.opacity = '1';
            }
        }, 50);

        // Auto remove after duration
        setTimeout(() => {
            this.removeSpeechBubble();
        }, duration);

        console.log(`[ChronarchBossController] 🔮 Speech bubble: "${text}"`);
    }

    /**
     * Update speech bubble position to follow boss
     */
    updateSpeechBubblePosition() {
        if (!this.speechBubble || !this.enemy) return;

        try {
            // Get camera from dungeonHandler
            const camera = this.dungeonHandler?.camera;
            if (!camera) return;

            // Calculate boss head position (add offset above boss)
            const enemyPosition = this.enemy.position.clone();
            enemyPosition.y += 6.0; // Position above boss head

            // Project 3D position to 2D screen coordinates
            const vector = enemyPosition.clone();
            vector.project(camera);

            // Convert to screen coordinates using window dimensions (matches Nairabos)
            const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
            const y = (vector.y * -0.5 + 0.5) * window.innerHeight;

            // Update speech bubble position
            this.speechBubble.style.left = x + 'px';
            this.speechBubble.style.top = y + 'px';
        } catch (error) {
            console.warn('[ChronarchBossController] Error updating speech bubble position:', error);
        }
    }

    /**
     * Update speech bubbles each frame
     */
    updateSpeechBubbles(deltaTime) {
        if (this.speechBubble) {
            this.updateSpeechBubblePosition();
        }
    }

    /**
     * Remove speech bubble
     */
    removeSpeechBubble() {
        if (this.speechBubble) {
            // Fade out
            this.speechBubble.style.opacity = '0';
            
            // Remove from DOM after fade
            setTimeout(() => {
                if (this.speechBubble && this.speechBubble.parentNode) {
                    this.speechBubble.parentNode.removeChild(this.speechBubble);
                }
                this.speechBubble = null;
            }, 500);
        }
    }

    // ===================================================================
    // 🔮 UNIQUE ALCHEMICAL ATTACK IMPLEMENTATIONS 🔮
    // ===================================================================

    /**
     * PHASE 1: Giant Potion Brew - Creates massive slow-moving explosive projectile
     */
    executeGiantPotionBrew() {
        console.log(`[ChronarchBossController] 🔮 Executing Giant Potion Brew - creating 3D voxel potion bottle`);
        
        if (!this.playerPosition) {
            console.error(`[ChronarchBossController] 🔮 ABORT: No player position`);
            return;
        }
        
        // Create the 3D voxel potion bottle
        const giantPotionBottle = this.createGiantPotionBottle();
        
        // Position it at the boss's hand/casting position
        const spawnPosition = this.enemy.position.clone();
        spawnPosition.y += 2.0; // Raise it above the boss
        spawnPosition.x += Math.cos(Date.now() * 0.001) * 0.5; // Slight horizontal offset
        giantPotionBottle.position.copy(spawnPosition);
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(giantPotionBottle);
        }
        
        // Calculate throw direction
        const direction = new THREE.Vector3()
            .subVectors(this.playerPosition, spawnPosition)
            .normalize();
        
        // Animate the bottle throw
        this.animateBottleThrow(giantPotionBottle, direction);
        
        console.log(`[ChronarchBossController] 🔮 SUCCESS: Giant Potion Bottle created and thrown`);
    }

    /**
     * Create a 3D voxel potion bottle
     */
    createGiantPotionBottle() {
        console.log(`[ChronarchBossController] 🔮 Creating 3D voxel potion bottle`);
        
        const VOXEL_SIZE = 0.05; // Match game's voxel size
        const potionScale = 6.0; // Much larger like crystal cave objects (was 2.5)
        const voxelSize = VOXEL_SIZE * potionScale;
        
        // Define potion bottle voxel structure
        const bottleVoxels = [
            // Bottle base (3x3 foundation)
            { x: 0, y: 0, z: 0, color: 0x4a7c59 }, // Dark green glass
            { x: 1, y: 0, z: 0, color: 0x4a7c59 },
            { x: 2, y: 0, z: 0, color: 0x4a7c59 },
            { x: 0, y: 0, z: 1, color: 0x4a7c59 },
            { x: 1, y: 0, z: 1, color: 0x4a7c59 },
            { x: 2, y: 0, z: 1, color: 0x4a7c59 },
            { x: 0, y: 0, z: 2, color: 0x4a7c59 },
            { x: 1, y: 0, z: 2, color: 0x4a7c59 },
            { x: 2, y: 0, z: 2, color: 0x4a7c59 },
            
            // Bottle body (liquid-filled sections)
            { x: 0, y: 1, z: 0, color: 0x4a7c59 }, { x: 2, y: 1, z: 0, color: 0x4a7c59 },
            { x: 0, y: 1, z: 1, color: 0x4a7c59 }, { x: 2, y: 1, z: 1, color: 0x4a7c59 },
            { x: 0, y: 1, z: 2, color: 0x4a7c59 }, { x: 2, y: 1, z: 2, color: 0x4a7c59 },
            { x: 0, y: 2, z: 0, color: 0x4a7c59 }, { x: 2, y: 2, z: 0, color: 0x4a7c59 },
            { x: 0, y: 2, z: 1, color: 0x4a7c59 }, { x: 2, y: 2, z: 1, color: 0x4a7c59 },
            { x: 0, y: 2, z: 2, color: 0x4a7c59 }, { x: 2, y: 2, z: 2, color: 0x4a7c59 },
            { x: 0, y: 3, z: 0, color: 0x4a7c59 }, { x: 2, y: 3, z: 0, color: 0x4a7c59 },
            { x: 0, y: 3, z: 1, color: 0x4a7c59 }, { x: 2, y: 3, z: 1, color: 0x4a7c59 },
            { x: 0, y: 3, z: 2, color: 0x4a7c59 }, { x: 2, y: 3, z: 2, color: 0x4a7c59 },
            
            // Liquid content (bright green)
            { x: 1, y: 1, z: 1, color: 0x00ff41 }, // Bright green liquid center
            { x: 1, y: 2, z: 1, color: 0x00ff41 },
            { x: 1, y: 3, z: 1, color: 0x00ff41 },
            
            // Bottle neck
            { x: 1, y: 4, z: 1, color: 0x4a7c59 },
            { x: 1, y: 5, z: 1, color: 0x4a7c59 },
            
            // Cork/stopper
            { x: 1, y: 6, z: 1, color: 0x8B4513 }, // Brown cork
        ];
        
        // Create the bottle group
        const bottleGroup = new THREE.Group();
        bottleGroup.name = 'GiantPotionBottle';
        
        // Create voxel geometries grouped by material
        const geometriesByMaterial = {};
        const tempMatrix = new THREE.Matrix4();
        
        bottleVoxels.forEach(voxel => {
            const colorHex = voxel.color;
            
            if (!geometriesByMaterial[colorHex]) {
                geometriesByMaterial[colorHex] = [];
            }
            
            // Create and position voxel geometry
            const voxelGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            tempMatrix.makeTranslation(
                voxel.x * voxelSize,
                voxel.y * voxelSize,
                voxel.z * voxelSize
            );
            voxelGeometry.applyMatrix4(tempMatrix);
            
            geometriesByMaterial[colorHex].push(voxelGeometry);
        });
        
        // Create merged meshes for each material
        Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
            if (geometries.length > 0) {
                // Import BufferGeometryUtils if needed
                const mergedGeometry = THREE.BufferGeometryUtils ? 
                    THREE.BufferGeometryUtils.mergeGeometries(geometries) :
                    geometries[0]; // Fallback to first geometry if merge not available
                
                // Create material
                const material = new THREE.MeshStandardMaterial({
                    color: parseInt(colorHex),
                    roughness: colorHex == 0x00ff41 ? 0.1 : 0.3, // Liquid is more reflective
                    metalness: 0.1,
                    emissive: colorHex == 0x00ff41 ? new THREE.Color(0x004411) : new THREE.Color(0x000000),
                    emissiveIntensity: colorHex == 0x00ff41 ? 0.3 : 0.0,
                    transparent: colorHex == 0x4a7c59, // Make glass slightly transparent
                    opacity: colorHex == 0x4a7c59 ? 0.8 : 1.0
                });
                
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                bottleGroup.add(mesh);
            }
        });
        
        // Center the bottle
        bottleGroup.position.set(-voxelSize * 1.5, 0, -voxelSize * 1.5);
        
        // Add particle effects
        this.addPotionParticles(bottleGroup);
        
        // Store metadata
        bottleGroup.userData = {
            type: 'giant_potion_bottle',
            damage: (this.enemy.userData.damage || 25) * 1.5,
            explosionRadius: 4.0,
            isExplosive: true,
            owner: this.enemy,
            creationTime: Date.now()
        };
        
        return bottleGroup;
    }

    /**
     * Add particle effects around the potion bottle
     */
    addPotionParticles(bottleGroup) {
        // Create glowing magical particles with multiple layers
        const particleCount = 12; // More particles for richer effect
        
        // Layer 1: Core energy particles (bright green)
        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.03, 0.03, 0.03);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0x00ff41,
                emissive: 0x00ff41,
                emissiveIntensity: 0.8,
                transparent: true,
                opacity: 0.9
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Orbital positioning
            const angle = (i / particleCount) * Math.PI * 2;
            const radius = 0.25 + Math.random() * 0.1;
            const height = Math.random() * 0.6 + 0.1;
            
            particle.position.set(
                Math.cos(angle) * radius,
                height,
                Math.sin(angle) * radius
            );
            
            // Store animation data
            particle.userData = {
                orbitAngle: angle,
                orbitSpeed: 0.015 + Math.random() * 0.02,
                orbitRadius: radius,
                bobOffset: Math.random() * Math.PI * 2,
                bobSpeed: 0.025 + Math.random() * 0.015,
                baseHeight: height,
                particleType: 'core'
            };
            
            bottleGroup.add(particle);
        }
        
        // Layer 2: Wispy trail particles (darker green)
        for (let i = 0; i < 8; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.02, 0.02, 0.02);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0x004411,
                emissive: 0x004411,
                emissiveIntensity: 0.4,
                transparent: true,
                opacity: 0.6
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Random positioning for wispy effect
            const angle = Math.random() * Math.PI * 2;
            const radius = 0.35 + Math.random() * 0.15;
            
            particle.position.set(
                Math.cos(angle) * radius,
                Math.random() * 0.4 + 0.3,
                Math.sin(angle) * radius
            );
            
            particle.userData = {
                orbitAngle: angle,
                orbitSpeed: 0.008 + Math.random() * 0.012,
                orbitRadius: radius,
                bobOffset: Math.random() * Math.PI * 2,
                bobSpeed: 0.02 + Math.random() * 0.01,
                particleType: 'wisp'
            };
            
            bottleGroup.add(particle);
        }
        
        // Layer 3: Sparkle effects (bright white/yellow)
        for (let i = 0; i < 6; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.015, 0.015, 0.015);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: 0xffff88,
                emissive: 0xffff88,
                emissiveIntensity: 1.0,
                transparent: true,
                opacity: 0.8
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Close to bottle for sparkle effect
            const angle = Math.random() * Math.PI * 2;
            const radius = 0.15 + Math.random() * 0.1;
            
            particle.position.set(
                Math.cos(angle) * radius,
                Math.random() * 0.8 + 0.1,
                Math.sin(angle) * radius
            );
            
            particle.userData = {
                orbitAngle: angle,
                orbitSpeed: 0.03 + Math.random() * 0.02,
                orbitRadius: radius,
                bobOffset: Math.random() * Math.PI * 2,
                bobSpeed: 0.04 + Math.random() * 0.02,
                sparklePhase: Math.random() * Math.PI * 2,
                particleType: 'sparkle'
            };
            
            bottleGroup.add(particle);
        }
    }

    /**
     * Animate the bottle being thrown toward the player
     */
    animateBottleThrow(bottle, direction) {
        const startTime = Date.now();
        const throwDuration = 2000; // 2 seconds flight time
        const startPos = bottle.position.clone();
        const targetPos = this.playerPosition.clone();
        targetPos.y += 1.0; // Aim slightly above ground
        
        // Add some arc to the throw
        const midPoint = startPos.clone().lerp(targetPos, 0.5);
        midPoint.y += 3.0; // Arc height
        
        const animateFrame = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / throwDuration, 1.0);
            
            if (progress >= 1.0) {
                // Bottle reached target - explode
                this.explodePotionBottle(bottle);
                return;
            }
            
            // Bezier curve for arced trajectory
            const t = progress;
            const invT = 1 - t;
            const pos = new THREE.Vector3()
                .copy(startPos).multiplyScalar(invT * invT)
                .add(midPoint.clone().multiplyScalar(2 * invT * t))
                .add(targetPos.clone().multiplyScalar(t * t));
            
            bottle.position.copy(pos);
            
            // Rotate bottle during flight
            bottle.rotation.x += 0.1;
            bottle.rotation.z += 0.05;
            
            // Animate particles with enhanced effects
            bottle.children.forEach(child => {
                if (child.userData.orbitAngle !== undefined) {
                    child.userData.orbitAngle += child.userData.orbitSpeed;
                    
                    // Update orbital position
                    const radius = child.userData.orbitRadius || 0.3;
                    child.position.x = Math.cos(child.userData.orbitAngle) * radius;
                    child.position.z = Math.sin(child.userData.orbitAngle) * radius;
                    
                    // Vertical bobbing animation
                    const baseHeight = child.userData.baseHeight || 0.3;
                    const bobAmount = child.userData.particleType === 'sparkle' ? 0.15 : 0.08;
                    child.position.y = baseHeight + Math.sin(Date.now() * child.userData.bobSpeed + child.userData.bobOffset) * bobAmount;
                    
                    // Special sparkle effects
                    if (child.userData.particleType === 'sparkle') {
                        child.userData.sparklePhase += 0.1;
                        child.material.opacity = 0.4 + Math.sin(child.userData.sparklePhase) * 0.4;
                        
                        // Random sparkle repositioning
                        if (Math.random() < 0.01) {
                            const newAngle = Math.random() * Math.PI * 2;
                            const newRadius = 0.15 + Math.random() * 0.1;
                            child.userData.orbitAngle = newAngle;
                            child.userData.orbitRadius = newRadius;
                        }
                    }
                    
                    // Wispy particles trail effect
                    if (child.userData.particleType === 'wisp') {
                        child.material.opacity = 0.3 + Math.sin(Date.now() * 0.003 + child.userData.bobOffset) * 0.3;
                    }
                }
            });
            
            // Continue animation
            requestAnimationFrame(animateFrame);
        };
        
        animateFrame();
    }

    /**
     * Create explosion effect when bottle hits target
     */
    explodePotionBottle(bottle) {
        console.log(`[ChronarchBossController] 🔮 Giant Potion Bottle exploded!`);
        
        // Create explosion effect (simple particle burst)
        const explosionGroup = new THREE.Group();
        explosionGroup.position.copy(bottle.position);
        
        // Create explosion particles
        for (let i = 0; i < 20; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.5 ? 0x00ff41 : 0x4a7c59,
                emissive: 0x004411,
                emissiveIntensity: 0.5
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            particle.position.set(
                (Math.random() - 0.5) * 2,
                Math.random() * 1,
                (Math.random() - 0.5) * 2
            );
            
            explosionGroup.add(particle);
        }
        
        // Add explosion to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(explosionGroup);
        }
        
        // Damage player if in range
        const distance = bottle.position.distanceTo(this.playerPosition);
        if (distance <= bottle.userData.explosionRadius) {
            console.log(`[ChronarchBossController] 🔮 Player caught in Giant Potion explosion!`);
            // Damage would be applied by dungeon handler
        }
        
        // Clean up bottle
        if (bottle.parent) {
            bottle.parent.remove(bottle);
        }
        
        // Clean up explosion after delay
        setTimeout(() => {
            if (explosionGroup.parent) {
                explosionGroup.parent.remove(explosionGroup);
            }
        }, 3000);
    }

    /**
     * PHASE 1: Toxic Cloud Burst - Area denial gas attack with 3D voxel gas generator
     */
    executeToxicCloudBurst() {
        console.log(`[ChronarchBossController] 🔮 Executing Toxic Cloud Burst - creating 3D voxel gas generator`);
        
        if (!this.playerPosition) {
            console.error(`[ChronarchBossController] 🔮 ABORT: No player position`);
            return;
        }
        
        // Create a 3D voxel gas generator device
        const gasGenerator = this.createToxicCloudGenerator();
        
        // Position it between boss and player
        const midPoint = this.enemy.position.clone().lerp(this.playerPosition, 0.3);
        midPoint.y += 1.5; // Hover above ground
        gasGenerator.position.copy(midPoint);
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(gasGenerator);
        }
        
        // Animate the gas generator and cloud release
        this.animateGasGenerator(gasGenerator);
        
        console.log(`[ChronarchBossController] 🔮 SUCCESS: Toxic Gas Generator created and activated`);
    }

    /**
     * Create a 3D voxel toxic gas generator device
     */
    createToxicCloudGenerator() {
        console.log(`[ChronarchBossController] 🔮 Creating 3D voxel gas generator`);
        
        const VOXEL_SIZE = 0.05;
        const generatorScale = 2.0;
        const voxelSize = VOXEL_SIZE * generatorScale;
        
        // Define gas generator voxel structure (alchemical apparatus)
        const generatorVoxels = [
            // Base platform (3x3)
            { x: 0, y: 0, z: 0, color: 0x444444 }, { x: 1, y: 0, z: 0, color: 0x444444 }, { x: 2, y: 0, z: 0, color: 0x444444 },
            { x: 0, y: 0, z: 1, color: 0x444444 }, { x: 1, y: 0, z: 1, color: 0x444444 }, { x: 2, y: 0, z: 1, color: 0x444444 },
            { x: 0, y: 0, z: 2, color: 0x444444 }, { x: 1, y: 0, z: 2, color: 0x444444 }, { x: 2, y: 0, z: 2, color: 0x444444 },
            
            // Central gas chamber
            { x: 1, y: 1, z: 1, color: 0x228B22 }, // Dark green gas chamber
            { x: 1, y: 2, z: 1, color: 0x228B22 },
            { x: 1, y: 3, z: 1, color: 0x228B22 },
            
            // Support pillars
            { x: 0, y: 1, z: 0, color: 0x666666 }, { x: 2, y: 1, z: 0, color: 0x666666 },
            { x: 0, y: 1, z: 2, color: 0x666666 }, { x: 2, y: 1, z: 2, color: 0x666666 },
            { x: 0, y: 2, z: 0, color: 0x666666 }, { x: 2, y: 2, z: 0, color: 0x666666 },
            { x: 0, y: 2, z: 2, color: 0x666666 }, { x: 2, y: 2, z: 2, color: 0x666666 },
            
            // Gas vents (emitting points)
            { x: 0, y: 3, z: 1, color: 0x32CD32 }, // Bright green vents
            { x: 2, y: 3, z: 1, color: 0x32CD32 },
            { x: 1, y: 3, z: 0, color: 0x32CD32 },
            { x: 1, y: 3, z: 2, color: 0x32CD32 },
            
            // Top crystalline activator
            { x: 1, y: 4, z: 1, color: 0x9400D3 }, // Purple crystal
        ];
        
        // Create the generator group
        const generatorGroup = new THREE.Group();
        generatorGroup.name = 'ToxicCloudGenerator';
        
        // Create voxel geometries grouped by material
        const geometriesByMaterial = {};
        const tempMatrix = new THREE.Matrix4();
        
        generatorVoxels.forEach(voxel => {
            const colorHex = voxel.color;
            
            if (!geometriesByMaterial[colorHex]) {
                geometriesByMaterial[colorHex] = [];
            }
            
            // Create and position voxel geometry
            const voxelGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            tempMatrix.makeTranslation(
                voxel.x * voxelSize,
                voxel.y * voxelSize,
                voxel.z * voxelSize
            );
            voxelGeometry.applyMatrix4(tempMatrix);
            
            geometriesByMaterial[colorHex].push(voxelGeometry);
        });
        
        // Create merged meshes for each material
        Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
            if (geometries.length > 0) {
                const mergedGeometry = THREE.BufferGeometryUtils ? 
                    THREE.BufferGeometryUtils.mergeGeometries(geometries) :
                    geometries[0];
                
                // Create material with special effects for gas components
                const material = new THREE.MeshStandardMaterial({
                    color: parseInt(colorHex),
                    roughness: colorHex == 0x228B22 || colorHex == 0x32CD32 ? 0.1 : 0.7,
                    metalness: colorHex == 0x444444 || colorHex == 0x666666 ? 0.8 : 0.1,
                    emissive: colorHex == 0x32CD32 ? new THREE.Color(0x004400) : 
                             colorHex == 0x9400D3 ? new THREE.Color(0x440044) :
                             new THREE.Color(0x000000),
                    emissiveIntensity: colorHex == 0x32CD32 || colorHex == 0x9400D3 ? 0.4 : 0.0,
                    transparent: colorHex == 0x228B22,
                    opacity: colorHex == 0x228B22 ? 0.7 : 1.0
                });
                
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                generatorGroup.add(mesh);
            }
        });
        
        // Center the generator
        generatorGroup.position.set(-voxelSize * 1.0, 0, -voxelSize * 1.0);
        
        // Add gas particle effects
        this.addGasGeneratorParticles(generatorGroup);
        
        // Store metadata
        generatorGroup.userData = {
            type: 'toxic_gas_generator',
            duration: 5000, // 5 seconds of gas emission
            gasEmissionRate: 0.3, // Emit gas every 300ms
            damage: (this.enemy.userData.damage || 25) * 0.4,
            gasRadius: 3.0,
            owner: this.enemy,
            creationTime: Date.now()
        };
        
        return generatorGroup;
    }

    /**
     * Add particle effects around the gas generator
     */
    addGasGeneratorParticles(generatorGroup) {
        // Create toxic gas particles
        for (let i = 0; i < 15; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.025, 0.025, 0.025);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.7 ? 0x32CD32 : 0x228B22,
                emissive: 0x004400,
                emissiveIntensity: 0.3,
                transparent: true,
                opacity: 0.5 + Math.random() * 0.3
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Position around gas vents
            const ventPositions = [
                { x: 0, y: 3, z: 1 },
                { x: 2, y: 3, z: 1 },
                { x: 1, y: 3, z: 0 },
                { x: 1, y: 3, z: 2 }
            ];
            const vent = ventPositions[i % ventPositions.length];
            
            particle.position.set(
                vent.x * 0.1 + (Math.random() - 0.5) * 0.2,
                vent.y * 0.1 + Math.random() * 0.3,
                vent.z * 0.1 + (Math.random() - 0.5) * 0.2
            );
            
            particle.userData = {
                isGasParticle: true,
                ventIndex: i % ventPositions.length,
                riseSpeed: 0.01 + Math.random() * 0.02,
                swayAmount: Math.random() * 0.1,
                swaySpeed: 0.02 + Math.random() * 0.03,
                swayOffset: Math.random() * Math.PI * 2,
                maxHeight: 2.0 + Math.random() * 1.0
            };
            
            generatorGroup.add(particle);
        }
    }

    /**
     * Animate the gas generator and emit toxic clouds
     */
    animateGasGenerator(generator) {
        const startTime = Date.now();
        const duration = generator.userData.duration;
        let lastEmissionTime = 0;
        
        const animateFrame = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1.0) {
                // Generator finished - create final explosion and clean up
                this.explodeGasGenerator(generator);
                return;
            }
            
            // Animate gas particles
            generator.children.forEach(child => {
                if (child.userData.isGasParticle) {
                    // Rising gas effect
                    child.position.y += child.userData.riseSpeed;
                    
                    // Swaying motion
                    child.position.x += Math.sin(Date.now() * child.userData.swaySpeed + child.userData.swayOffset) * child.userData.swayAmount * 0.1;
                    child.position.z += Math.cos(Date.now() * child.userData.swaySpeed + child.userData.swayOffset) * child.userData.swayAmount * 0.1;
                    
                    // Fade out as gas rises
                    const heightProgress = child.position.y / child.userData.maxHeight;
                    child.material.opacity = Math.max(0.1, 0.8 - heightProgress);
                    
                    // Reset particle if it rises too high
                    if (child.position.y > child.userData.maxHeight) {
                        const ventPositions = [
                            { x: 0, y: 3, z: 1 },
                            { x: 2, y: 3, z: 1 },
                            { x: 1, y: 3, z: 0 },
                            { x: 1, y: 3, z: 2 }
                        ];
                        const vent = ventPositions[child.userData.ventIndex];
                        child.position.set(
                            vent.x * 0.1 + (Math.random() - 0.5) * 0.1,
                            vent.y * 0.1,
                            vent.z * 0.1 + (Math.random() - 0.5) * 0.1
                        );
                    }
                }
            });
            
            // Emit gas clouds periodically
            if (elapsed - lastEmissionTime >= generator.userData.gasEmissionRate * 1000) {
                this.emitToxicGasCloud(generator);
                lastEmissionTime = elapsed;
            }
            
            // Continue animation
            requestAnimationFrame(animateFrame);
        };
        
        animateFrame();
    }

    /**
     * Emit a single toxic gas cloud from the generator
     */
    emitToxicGasCloud(generator) {
        if (!this.dungeonHandler.spawnProjectile) return;
        
        // Random direction with slight bias toward player
        const randomAngle = Math.random() * Math.PI * 2;
        const toPlayer = new THREE.Vector3().subVectors(this.playerPosition, generator.position).normalize();
        const randomDir = new THREE.Vector3(
            Math.cos(randomAngle),
            0,
            Math.sin(randomAngle)
        );
        
        // Blend random direction with player direction
        const direction = randomDir.lerp(toPlayer, 0.3).normalize();
        
        const toxicCloudData = {
            projectileType: 'poison_cloud',
            projectileSpeed: 4.0,
            disableTrails: false,
            type: 'chronarch',
            health: 1,
            isProjectile: true,
            aiType: 'flying',
            owner: this.enemy,
            damage: generator.userData.damage,
            scale: 1.2,
            isPoisonous: true,
            cloudRadius: generator.userData.gasRadius,
            lingeringEffect: true,
            glowIntensity: 1.0,
            emissiveIntensity: 0.6
        };
        
        try {
            this.dungeonHandler.spawnProjectile(
                generator.position.clone(),
                direction,
                toxicCloudData
            );
        } catch (error) {
            console.error(`[ChronarchBossController] 🔮 Gas cloud emission error:`, error);
        }
    }

    /**
     * Create explosion effect when gas generator finishes
     */
    explodeGasGenerator(generator) {
        console.log(`[ChronarchBossController] 🔮 Toxic Gas Generator explosion!`);
        
        // Create final gas burst explosion
        const explosionGroup = new THREE.Group();
        explosionGroup.position.copy(generator.position);
        
        // Create larger explosion particles
        for (let i = 0; i < 30; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.08, 0.08, 0.08);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.5 ? 0x32CD32 : 0x228B22,
                emissive: 0x004400,
                emissiveIntensity: 0.4
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            particle.position.set(
                (Math.random() - 0.5) * 4,
                Math.random() * 2,
                (Math.random() - 0.5) * 4
            );
            
            explosionGroup.add(particle);
        }
        
        // Add explosion to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(explosionGroup);
        }
        
        // Clean up generator
        if (generator.parent) {
            generator.parent.remove(generator);
        }
        
        // Clean up explosion after delay
        setTimeout(() => {
            if (explosionGroup.parent) {
                explosionGroup.parent.remove(explosionGroup);
            }
        }, 4000);
    }

    /**
     * PHASE 1: Liquid Wave - Sequential wave pattern projectiles
     */
    executeLiquidWave() {
        console.log(`[ChronarchBossController] 🔮 Executing Liquid Wave - wave formation attack`);
        
        if (!this.playerPosition || !this.dungeonHandler.spawnProjectile) return;
        
        const waveProjectiles = 6;
        const waveWidth = Math.PI / 2; // 90 degree wave
        
        for (let i = 0; i < waveProjectiles; i++) {
            setTimeout(() => {
                // Calculate wave formation
                const waveProgress = i / (waveProjectiles - 1); // 0 to 1
                const angle = (waveProgress - 0.5) * waveWidth; // -45 to +45 degrees
                
                const baseDirection = new THREE.Vector3()
                    .subVectors(this.playerPosition, this.enemy.position)
                    .normalize();
                
                // Create wave direction
                const waveDirection = baseDirection.clone();
                waveDirection.x = baseDirection.x * Math.cos(angle) - baseDirection.z * Math.sin(angle);
                waveDirection.z = baseDirection.x * Math.sin(angle) + baseDirection.z * Math.cos(angle);
                
                const liquidWaveData = {
                    projectileType: 'alchemical_symbols',
                    projectileSpeed: 8.0,
                    disableTrails: false,
                    type: 'chronarch',
                    health: 1,
                    isProjectile: true,
                    aiType: 'flying',
                    owner: this.enemy,
                    damage: (this.enemy.userData.damage || 25) * 0.8, // Slightly reduced damage per wave projectile
                    // Liquid wave properties
                    scale: 1.2,
                    liquidTrail: true,
                    waveFormation: true,
                    glowIntensity: 1.3,
                    emissiveIntensity: 0.9
                };
                
                this.dungeonHandler.spawnProjectile(
                    this.enemy.position.clone(),
                    waveDirection,
                    liquidWaveData
                );
            }, i * 150); // Wave timing
        }
        
        console.log(`[ChronarchBossController] 🔮 Liquid Wave - spawned ${waveProjectiles} projectiles in wave formation`);
    }

    // ===================================================================
    // 🔮 PHASE 2: TEMPORAL MANIPULATOR ATTACKS ⏰
    // ===================================================================

    /**
     * PHASE 2: Time Warp - Temporal manipulation attacks
     */
    executeTimeWarp() {
        console.log(`[ChronarchBossController] 🔮 Executing Time Warp - temporal manipulation`);
        
        if (!this.playerPosition || !this.dungeonHandler.spawnProjectile) return;
        
        // Create temporal projectiles with time distortion effects
        const temporalCount = 5;
        const temporalSpread = Math.PI / 4; // 45 degree spread
        
        for (let i = 0; i < temporalCount; i++) {
            setTimeout(() => {
                const angle = (i - temporalCount/2) * (temporalSpread / temporalCount);
                const direction = new THREE.Vector3()
                    .subVectors(this.playerPosition, this.enemy.position)
                    .normalize();
                
                // Rotate direction by spread angle
                const rotatedDirection = direction.clone();
                rotatedDirection.x = direction.x * Math.cos(angle) - direction.z * Math.sin(angle);
                rotatedDirection.z = direction.x * Math.sin(angle) + direction.z * Math.cos(angle);
                
                const temporalData = {
                    projectileType: 'rotating_sigils',
                    projectileSpeed: 10.0,
                    disableTrails: false,
                    type: 'chronarch',
                    health: 1,
                    isProjectile: true,
                    aiType: 'flying',
                    owner: this.enemy,
                    damage: (this.enemy.userData.damage || 25) * 0.9,
                    // Temporal effects
                    scale: 1.4,
                    isHoming: true,
                    homingStrength: 0.08,
                    temporalDistortion: true,
                    glowIntensity: 2.0,
                    emissiveIntensity: 1.2,
                    // Special temporal projectile effects
                    timeWarpEffect: true,
                    chronalAura: true
                };
                
                try {
                    this.dungeonHandler.spawnProjectile(
                        this.enemy.position.clone(),
                        rotatedDirection,
                        temporalData
                    );
                } catch (error) {
                    console.error(`[ChronarchBossController] 🔮 Temporal projectile spawn error:`, error);
                }
            }, i * 250); // Stagger timing for dramatic effect
        }
        
        console.log(`[ChronarchBossController] 🔮 Time Warp - spawned ${temporalCount} temporal distortion projectiles`);
    }

    /**
     * PHASE 2: Crystal Prison - Spawns crystal towers using proper enemy system
     */
    executeCrystalPrison() {
        console.log(`[ChronarchBossController] 🔮 Executing Crystal Prison - spawning crystal towers`);
        
        if (!this.playerPosition || !this.dungeonHandler._spawnEnemy) {
            console.error(`[ChronarchBossController] 🔮 ABORT: Missing player position or spawn method`);
            return;
        }
        
        // Spawn crystal towers around the player using proper enemy system
        const crystalCount = 6;
        const circleRadius = 5.0; // Distance from player
        
        for (let i = 0; i < crystalCount; i++) {
            setTimeout(() => {
                // Calculate positions in a circle around the player
                const angle = (i / crystalCount) * Math.PI * 2;
                const towerPosition = this.playerPosition.clone();
                towerPosition.x += Math.cos(angle) * circleRadius;
                towerPosition.z += Math.sin(angle) * circleRadius;
                
                // Spawn crystal tower using the proper enemy system (like Nairabos does)
                const crystalTower = this.dungeonHandler._spawnEnemy('crystal_tower', towerPosition);
                
                if (crystalTower) {
                    // Set up crystal tower properties (like shadow_minion)
                    crystalTower.userData.lifetime = performance.now() + 8000; // 8 seconds
                    crystalTower.userData.isBossMinion = true;
                    crystalTower.userData.parentBoss = this.enemy;
                    crystalTower.userData.towerIndex = i;
                    crystalTower.userData.spawnTime = performance.now();
                    
                    // Replace the model with our custom 3D voxel tower
                    this.replaceTowerModel(crystalTower);
                    
                    // Set up tower AI for firing projectiles
                    if (crystalTower.userData.ai) {
                        crystalTower.userData.ai.isStructure = true;
                        crystalTower.userData.ai.attackTarget = null; // Will target player
                    }
                    
                    console.log(`[ChronarchBossController] 🔮 Crystal Tower ${i+1}/${crystalCount} spawned at position:`, towerPosition);
                } else {
                    console.error(`[ChronarchBossController] 🔮 Failed to spawn crystal tower ${i+1}`);
                }
            }, i * 300); // Stagger the tower creation
        }
        
        console.log(`[ChronarchBossController] 🔮 Crystal Prison - spawning ${crystalCount} crystal towers`);
    }

    /**
     * Replace the default enemy model with our custom 3D voxel tower
     */
    replaceTowerModel(towerEntity) {
        // Remove the default enemy model if it exists
        if (towerEntity.children.length > 0) {
            // Clear existing children (default model)
            while (towerEntity.children.length > 0) {
                towerEntity.remove(towerEntity.children[0]);
            }
        }
        
        // Create and add our custom 3D voxel tower model
        const customTower = this.createCrystalTower();
        towerEntity.add(customTower);
        
        // Store reference for animations
        towerEntity.userData.voxelModel = customTower;
        
        console.log(`[ChronarchBossController] 🔮 Replaced tower model with custom voxel design`);
    }

    /**
     * Create a 3D voxel crystal tower
     */
    createCrystalTower() {
        console.log(`[ChronarchBossController] 🔮 Creating 3D voxel crystal tower`);
        
        const VOXEL_SIZE = 0.05;
        const towerScale = 2.2;
        const voxelSize = VOXEL_SIZE * towerScale;
        
        // Define crystal tower voxel structure (mystical crystal formation)
        const towerVoxels = [
            // Base foundation (3x3)
            { x: 0, y: 0, z: 0, color: 0x4169E1 }, { x: 1, y: 0, z: 0, color: 0x4169E1 }, { x: 2, y: 0, z: 0, color: 0x4169E1 },
            { x: 0, y: 0, z: 1, color: 0x4169E1 }, { x: 1, y: 0, z: 1, color: 0x4169E1 }, { x: 2, y: 0, z: 1, color: 0x4169E1 },
            { x: 0, y: 0, z: 2, color: 0x4169E1 }, { x: 1, y: 0, z: 2, color: 0x4169E1 }, { x: 2, y: 0, z: 2, color: 0x4169E1 },
            
            // Tower body (tapering crystal structure)
            // Level 1
            { x: 0, y: 1, z: 0, color: 0x6495ED }, { x: 2, y: 1, z: 0, color: 0x6495ED },
            { x: 0, y: 1, z: 1, color: 0x6495ED }, { x: 1, y: 1, z: 1, color: 0x87CEEB }, { x: 2, y: 1, z: 1, color: 0x6495ED },
            { x: 0, y: 1, z: 2, color: 0x6495ED }, { x: 2, y: 1, z: 2, color: 0x6495ED },
            
            // Level 2
            { x: 0, y: 2, z: 1, color: 0x87CEEB }, { x: 1, y: 2, z: 0, color: 0x87CEEB }, 
            { x: 1, y: 2, z: 1, color: 0xB0E0E6 }, { x: 1, y: 2, z: 2, color: 0x87CEEB }, { x: 2, y: 2, z: 1, color: 0x87CEEB },
            
            // Level 3
            { x: 1, y: 3, z: 1, color: 0xB0E0E6 },
            { x: 0, y: 3, z: 1, color: 0x87CEEB }, { x: 2, y: 3, z: 1, color: 0x87CEEB },
            { x: 1, y: 3, z: 0, color: 0x87CEEB }, { x: 1, y: 3, z: 2, color: 0x87CEEB },
            
            // Level 4
            { x: 1, y: 4, z: 1, color: 0xB0E0E6 },
            
            // Crystal tip (glowing energy focus)
            { x: 1, y: 5, z: 1, color: 0xFFFFFF }, // Bright white crystal tip
            
            // Energy cores (scattered through tower)
            { x: 1, y: 1, z: 0, color: 0xADD8E6 }, { x: 1, y: 1, z: 2, color: 0xADD8E6 },
            { x: 0, y: 2, z: 0, color: 0xADD8E6 }, { x: 2, y: 2, z: 2, color: 0xADD8E6 },
        ];
        
        // Create the tower group
        const towerGroup = new THREE.Group();
        towerGroup.name = 'CrystalTower';
        
        // Create voxel geometries grouped by material
        const geometriesByMaterial = {};
        const tempMatrix = new THREE.Matrix4();
        
        towerVoxels.forEach(voxel => {
            const colorHex = voxel.color;
            
            if (!geometriesByMaterial[colorHex]) {
                geometriesByMaterial[colorHex] = [];
            }
            
            // Create and position voxel geometry
            const voxelGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            tempMatrix.makeTranslation(
                voxel.x * voxelSize,
                voxel.y * voxelSize,
                voxel.z * voxelSize
            );
            voxelGeometry.applyMatrix4(tempMatrix);
            
            geometriesByMaterial[colorHex].push(voxelGeometry);
        });
        
        // Create merged meshes for each material
        Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
            if (geometries.length > 0) {
                const mergedGeometry = THREE.BufferGeometryUtils ? 
                    THREE.BufferGeometryUtils.mergeGeometries(geometries) :
                    geometries[0];
                
                // Create material with crystal effects
                const material = new THREE.MeshStandardMaterial({
                    color: parseInt(colorHex),
                    roughness: colorHex == 0xFFFFFF ? 0.05 : 0.2, // Crystal tip is most reflective
                    metalness: 0.1,
                    emissive: colorHex == 0xFFFFFF ? new THREE.Color(0x4444FF) : 
                             colorHex == 0xADD8E6 ? new THREE.Color(0x002266) :
                             new THREE.Color(0x001133),
                    emissiveIntensity: colorHex == 0xFFFFFF ? 0.6 : 
                                      colorHex == 0xADD8E6 ? 0.4 : 0.2,
                    transparent: true,
                    opacity: colorHex == 0xFFFFFF ? 0.9 : 0.8
                });
                
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                towerGroup.add(mesh);
            }
        });
        
        // Center the tower
        towerGroup.position.set(-voxelSize * 1.0, 0, -voxelSize * 1.0);
        
        // Add crystal particle effects
        this.addCrystalTowerParticles(towerGroup);
        
        // Store metadata
        towerGroup.userData = {
            type: 'crystal_tower',
            duration: 8000, // 8 seconds of activity
            projectileFireRate: 1.0, // Fire every 1 second
            damage: (this.enemy.userData.damage || 25) * 0.6,
            owner: this.enemy,
            creationTime: Date.now(),
            lastFireTime: 0
        };
        
        return towerGroup;
    }

    /**
     * Add particle effects around the crystal tower
     */
    addCrystalTowerParticles(towerGroup) {
        // Create mystical crystal energy particles
        for (let i = 0; i < 10; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.02, 0.02, 0.02);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.5 ? 0x87CEEB : 0xB0E0E6,
                emissive: 0x003366,
                emissiveIntensity: 0.5,
                transparent: true,
                opacity: 0.7 + Math.random() * 0.3
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Position around crystal tower
            const angle = (i / 10) * Math.PI * 2;
            const radius = 0.4 + Math.random() * 0.2;
            const height = Math.random() * 0.8 + 0.2;
            
            particle.position.set(
                Math.cos(angle) * radius,
                height,
                Math.sin(angle) * radius
            );
            
            particle.userData = {
                isCrystalParticle: true,
                orbitAngle: angle,
                orbitSpeed: 0.02 + Math.random() * 0.015,
                orbitRadius: radius,
                bobOffset: Math.random() * Math.PI * 2,
                bobSpeed: 0.03 + Math.random() * 0.02,
                baseHeight: height,
                pulsePhase: Math.random() * Math.PI * 2
            };
            
            towerGroup.add(particle);
        }
    }

    /**
     * Animate the crystal tower and make it fire projectiles
     */
    animateCrystalTower(tower, towerIndex) {
        const startTime = Date.now();
        const duration = tower.userData.duration;
        let lastProjectileTime = 0;
        
        const animateFrame = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1.0) {
                // Tower finished - create crystal explosion and clean up
                this.explodeCrystalTower(tower);
                return;
            }
            
            // Rising animation (first 1 second)
            if (progress < 0.125) { // 1/8 of duration for rising
                const riseProgress = progress / 0.125;
                tower.scale.y = riseProgress;
                tower.position.y = (1 - riseProgress) * -2; // Rise from underground
            } else {
                tower.scale.y = 1;
                tower.position.y = 0;
            }
            
            // Animate crystal particles
            tower.children.forEach(child => {
                if (child.userData.isCrystalParticle) {
                    // Orbital motion
                    child.userData.orbitAngle += child.userData.orbitSpeed;
                    child.position.x = Math.cos(child.userData.orbitAngle) * child.userData.orbitRadius;
                    child.position.z = Math.sin(child.userData.orbitAngle) * child.userData.orbitRadius;
                    
                    // Vertical bobbing
                    child.position.y = child.userData.baseHeight + Math.sin(Date.now() * child.userData.bobSpeed + child.userData.bobOffset) * 0.1;
                    
                    // Pulsing glow
                    child.userData.pulsePhase += 0.05;
                    child.material.opacity = 0.5 + Math.sin(child.userData.pulsePhase) * 0.3;
                }
            });
            
            // Fire projectiles periodically
            if (elapsed - lastProjectileTime >= tower.userData.projectileFireRate * 1000 && progress > 0.125) {
                this.fireCrystalProjectile(tower, towerIndex);
                lastProjectileTime = elapsed;
            }
            
            // Tower energy pulsing
            const pulseFactor = 1.0 + Math.sin(Date.now() * 0.005) * 0.1;
            tower.children.forEach(child => {
                if (child.material && child.material.emissive) {
                    child.material.emissiveIntensity = (child.material.emissiveIntensity || 0.2) * pulseFactor;
                }
            });
            
            // Continue animation
            requestAnimationFrame(animateFrame);
        };
        
        animateFrame();
    }

    /**
     * Fire a crystal projectile from the tower toward the player
     */
    fireCrystalProjectile(tower, towerIndex) {
        if (!this.dungeonHandler.spawnProjectile || !this.playerPosition) return;
        
        // Calculate direction to player
        const direction = new THREE.Vector3()
            .subVectors(this.playerPosition, tower.position)
            .normalize();
        
        // Add slight spread for variety
        const spread = 0.2;
        direction.x += (Math.random() - 0.5) * spread;
        direction.z += (Math.random() - 0.5) * spread;
        direction.normalize();
        
        const crystalProjectileData = {
            projectileType: 'sacred_geometry',
            projectileSpeed: 8.0,
            disableTrails: false,
            type: 'chronarch',
            health: 1,
            isProjectile: true,
            aiType: 'flying',
            owner: this.enemy,
            damage: tower.userData.damage,
            scale: 1.3,
            isExplosive: true,
            explosionRadius: 1.5,
            crystallineEnergy: true,
            glowIntensity: 2.0,
            emissiveIntensity: 1.0
        };
        
        try {
            const spawnPos = tower.position.clone();
            spawnPos.y += 3.0; // Fire from crystal tip
            this.dungeonHandler.spawnProjectile(
                spawnPos,
                direction,
                crystalProjectileData
            );
        } catch (error) {
            console.error(`[ChronarchBossController] 🔮 Crystal tower projectile error:`, error);
        }
    }

    /**
     * Create explosion effect when crystal tower finishes
     */
    explodeCrystalTower(tower) {
        console.log(`[ChronarchBossController] 🔮 Crystal Tower explosion!`);
        
        // Create crystal shatter explosion
        const explosionGroup = new THREE.Group();
        explosionGroup.position.copy(tower.position);
        
        // Create crystal shard particles
        for (let i = 0; i < 25; i++) {
            const shardGeometry = new THREE.BoxGeometry(0.06, 0.06, 0.06);
            const shardMaterial = new THREE.MeshBasicMaterial({
                color: Math.random() > 0.6 ? 0x87CEEB : 0x6495ED,
                emissive: 0x003366,
                emissiveIntensity: 0.4
            });
            
            const shard = new THREE.Mesh(shardGeometry, shardMaterial);
            shard.position.set(
                (Math.random() - 0.5) * 6,
                Math.random() * 3,
                (Math.random() - 0.5) * 6
            );
            
            explosionGroup.add(shard);
        }
        
        // Add explosion to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(explosionGroup);
        }
        
        // Clean up tower
        if (tower.parent) {
            tower.parent.remove(tower);
        }
        
        // Clean up explosion after delay
        setTimeout(() => {
            if (explosionGroup.parent) {
                explosionGroup.parent.remove(explosionGroup);
            }
        }, 5000);
    }

    // ===================================================================
    // 🔮 PHASE 3: MASTER TRANSMUTER ATTACKS 🌟
    // ===================================================================

    /**
     * PHASE 3: Alchemical Explosion - Spawns transmutation reactors using proper enemy system
     */
    executeAlchemicalExplosion() {
        console.log(`[ChronarchBossController] 🔮 Executing Alchemical Explosion - spawning transmutation reactors`);
        
        if (!this.playerPosition || !this.dungeonHandler._spawnEnemy) {
            console.error(`[ChronarchBossController] 🔮 ABORT: Missing player position or spawn method`);
            return;
        }
        
        // Spawn transmutation reactors for massive finale attack
        const reactorCount = 4;
        const placementRadius = 6.0;
        
        for (let i = 0; i < reactorCount; i++) {
            setTimeout(() => {
                // Create reactor positions in strategic locations
                let reactorPosition;
                if (i === 0) {
                    // First reactor near player
                    reactorPosition = this.playerPosition.clone();
                    reactorPosition.x += (Math.random() - 0.5) * 4;
                    reactorPosition.z += (Math.random() - 0.5) * 4;
                } else {
                    // Other reactors in circle around battlefield
                    const angle = ((i - 1) / (reactorCount - 1)) * Math.PI * 2;
                    reactorPosition = this.enemy.position.clone();
                    reactorPosition.x += Math.cos(angle) * placementRadius;
                    reactorPosition.z += Math.sin(angle) * placementRadius;
                }
                
                // Spawn transmutation reactor using the proper enemy system
                const transmutationReactor = this.dungeonHandler._spawnEnemy('transmutation_reactor', reactorPosition);
                
                if (transmutationReactor) {
                    // Set up reactor properties
                    transmutationReactor.userData.lifetime = performance.now() + 8000; // 8 seconds
                    transmutationReactor.userData.isBossMinion = true;
                    transmutationReactor.userData.parentBoss = this.enemy;
                    transmutationReactor.userData.reactorIndex = i;
                    transmutationReactor.userData.spawnTime = performance.now();
                    transmutationReactor.userData.patternIndex = Math.floor(Math.random() * 4); // Random pattern
                    
                    // Replace the model with our custom 3D voxel reactor
                    this.replaceReactorModel(transmutationReactor);
                    
                    // Set up reactor AI for pattern firing
                    if (transmutationReactor.userData.ai) {
                        transmutationReactor.userData.ai.isStructure = true;
                        transmutationReactor.userData.ai.attackTarget = null; // Will use pattern system
                        transmutationReactor.userData.ai.usePatterns = true;
                    }
                    
                    console.log(`[ChronarchBossController] 🔮 Transmutation Reactor ${i+1}/${reactorCount} spawned at position:`, reactorPosition);
                } else {
                    console.error(`[ChronarchBossController] 🔮 Failed to spawn transmutation reactor ${i+1}`);
                }
            }, i * 500); // Stagger creation for dramatic buildup
        }
        
        console.log(`[ChronarchBossController] 🔮 Alchemical Explosion - spawning ${reactorCount} transmutation reactors`);
    }

    /**
     * Replace the default enemy model with our custom 3D voxel reactor
     */
    replaceReactorModel(reactorEntity) {
        // Remove the default enemy model if it exists
        if (reactorEntity.children.length > 0) {
            // Clear existing children (default model)
            while (reactorEntity.children.length > 0) {
                reactorEntity.remove(reactorEntity.children[0]);
            }
        }
        
        // Create and add our custom 3D voxel reactor model
        const customReactor = this.createTransmutationReactor();
        reactorEntity.add(customReactor);
        
        // Store reference for animations
        reactorEntity.userData.voxelModel = customReactor;
        
        console.log(`[ChronarchBossController] 🔮 Replaced reactor model with custom voxel design`);
    }

    /**
     * Create a 3D voxel transmutation reactor
     */
    createTransmutationReactor() {
        console.log(`[ChronarchBossController] 🔮 Creating 3D voxel transmutation reactor`);
        
        const VOXEL_SIZE = 0.05;
        const reactorScale = 2.8;
        const voxelSize = VOXEL_SIZE * reactorScale;
        
        // Define transmutation reactor voxel structure (ultimate alchemical device)
        const reactorVoxels = [
            // Base containment chamber (4x4)
            { x: 0, y: 0, z: 0, color: 0x8B0000 }, { x: 1, y: 0, z: 0, color: 0x8B0000 }, { x: 2, y: 0, z: 0, color: 0x8B0000 }, { x: 3, y: 0, z: 0, color: 0x8B0000 },
            { x: 0, y: 0, z: 1, color: 0x8B0000 }, { x: 1, y: 0, z: 1, color: 0x8B0000 }, { x: 2, y: 0, z: 1, color: 0x8B0000 }, { x: 3, y: 0, z: 1, color: 0x8B0000 },
            { x: 0, y: 0, z: 2, color: 0x8B0000 }, { x: 1, y: 0, z: 2, color: 0x8B0000 }, { x: 2, y: 0, z: 2, color: 0x8B0000 }, { x: 3, y: 0, z: 2, color: 0x8B0000 },
            { x: 0, y: 0, z: 3, color: 0x8B0000 }, { x: 1, y: 0, z: 3, color: 0x8B0000 }, { x: 2, y: 0, z: 3, color: 0x8B0000 }, { x: 3, y: 0, z: 3, color: 0x8B0000 },
            
            // Reactor core chamber walls
            { x: 0, y: 1, z: 0, color: 0xFF4500 }, { x: 3, y: 1, z: 0, color: 0xFF4500 }, { x: 0, y: 1, z: 3, color: 0xFF4500 }, { x: 3, y: 1, z: 3, color: 0xFF4500 },
            { x: 0, y: 2, z: 0, color: 0xFF4500 }, { x: 3, y: 2, z: 0, color: 0xFF4500 }, { x: 0, y: 2, z: 3, color: 0xFF4500 }, { x: 3, y: 2, z: 3, color: 0xFF4500 },
            
            // Central transmutation core (pulsing energy)
            { x: 1, y: 1, z: 1, color: 0xFF6347 }, { x: 2, y: 1, z: 1, color: 0xFF6347 },
            { x: 1, y: 1, z: 2, color: 0xFF6347 }, { x: 2, y: 1, z: 2, color: 0xFF6347 },
            { x: 1, y: 2, z: 1, color: 0xFFD700 }, { x: 2, y: 2, z: 1, color: 0xFFD700 },
            { x: 1, y: 2, z: 2, color: 0xFFD700 }, { x: 2, y: 2, z: 2, color: 0xFFD700 },
            
            // Energy focus points (corner reactors)
            { x: 0, y: 3, z: 0, color: 0xFF0000 }, { x: 3, y: 3, z: 0, color: 0xFF0000 },
            { x: 0, y: 3, z: 3, color: 0xFF0000 }, { x: 3, y: 3, z: 3, color: 0xFF0000 },
            
            // Central energy conduits
            { x: 1, y: 3, z: 1, color: 0xFFFF00 }, { x: 2, y: 3, z: 1, color: 0xFFFF00 },
            { x: 1, y: 3, z: 2, color: 0xFFFF00 }, { x: 2, y: 3, z: 2, color: 0xFFFF00 },
            
            // Transmutation apex (reality bender)
            { x: 1, y: 4, z: 1, color: 0xFFFFFF }, { x: 2, y: 4, z: 1, color: 0xFFFFFF },
            { x: 1, y: 4, z: 2, color: 0xFFFFFF }, { x: 2, y: 4, z: 2, color: 0xFFFFFF },
            
            // Ultimate power core
            { x: 1, y: 5, z: 1, color: 0xFF00FF }, { x: 2, y: 5, z: 1, color: 0xFF00FF },
            { x: 1, y: 5, z: 2, color: 0xFF00FF }, { x: 2, y: 5, z: 2, color: 0xFF00FF },
        ];
        
        // Create the reactor group
        const reactorGroup = new THREE.Group();
        reactorGroup.name = 'TransmutationReactor';
        
        // Create voxel geometries grouped by material
        const geometriesByMaterial = {};
        const tempMatrix = new THREE.Matrix4();
        
        reactorVoxels.forEach(voxel => {
            const colorHex = voxel.color;
            
            if (!geometriesByMaterial[colorHex]) {
                geometriesByMaterial[colorHex] = [];
            }
            
            // Create and position voxel geometry
            const voxelGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            tempMatrix.makeTranslation(
                voxel.x * voxelSize,
                voxel.y * voxelSize,
                voxel.z * voxelSize
            );
            voxelGeometry.applyMatrix4(tempMatrix);
            
            geometriesByMaterial[colorHex].push(voxelGeometry);
        });
        
        // Create merged meshes for each material
        Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
            if (geometries.length > 0) {
                const mergedGeometry = THREE.BufferGeometryUtils ? 
                    THREE.BufferGeometryUtils.mergeGeometries(geometries) :
                    geometries[0];
                
                // Create material with explosive energy effects
                const material = new THREE.MeshStandardMaterial({
                    color: parseInt(colorHex),
                    roughness: colorHex == 0xFFFFFF || colorHex == 0xFF00FF ? 0.0 : 0.4,
                    metalness: colorHex == 0x8B0000 || colorHex == 0xFF4500 ? 0.9 : 0.3,
                    emissive: colorHex == 0xFFFFFF ? new THREE.Color(0x666666) :
                             colorHex == 0xFF00FF ? new THREE.Color(0x440044) :
                             colorHex == 0xFFD700 ? new THREE.Color(0x332200) :
                             colorHex == 0xFFFF00 ? new THREE.Color(0x444400) :
                             colorHex == 0xFF0000 ? new THREE.Color(0x330000) :
                             new THREE.Color(0x220000),
                    emissiveIntensity: colorHex == 0xFFFFFF ? 0.8 :
                                      colorHex == 0xFF00FF ? 0.7 :
                                      colorHex == 0xFFD700 ? 0.6 :
                                      colorHex == 0xFFFF00 ? 0.5 :
                                      colorHex == 0xFF0000 ? 0.4 : 0.2,
                    transparent: true,
                    opacity: colorHex == 0xFFFFFF ? 0.95 : 0.9
                });
                
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                reactorGroup.add(mesh);
            }
        });
        
        // Center the reactor
        reactorGroup.position.set(-voxelSize * 1.5, 0, -voxelSize * 1.5);
        
        // Add transmutation particle effects
        this.addTransmutationParticles(reactorGroup);
        
        // Store metadata
        reactorGroup.userData = {
            type: 'transmutation_reactor',
            duration: 8000, // 8 seconds of escalating power (reduced from 10)
            explosionWaves: 3,
            projectileFireRate: 1.5, // Fire every 1.5 seconds (slower than 0.8)
            damage: (this.enemy.userData.damage || 25) * 1.5, // Maximum damage for finale
            owner: this.enemy,
            creationTime: Date.now(),
            lastFireTime: 0,
            energyLevel: 0,
            patternIndex: Math.floor(Math.random() * 4) // Add pattern variety
        };
        
        return reactorGroup;
    }

    /**
     * Add particle effects around the transmutation reactor
     */
    addTransmutationParticles(reactorGroup) {
        // Create chaotic transmutation energy particles
        for (let i = 0; i < 20; i++) {
            const particleGeometry = new THREE.BoxGeometry(0.03, 0.03, 0.03);
            const particleColor = [0xFF0000, 0xFFD700, 0xFF00FF, 0xFFFF00, 0xFFFFFF][Math.floor(Math.random() * 5)];
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: particleColor,
                emissive: particleColor,
                emissiveIntensity: 0.8,
                transparent: true,
                opacity: 0.8
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            
            // Position around reactor in chaotic patterns
            const angle = Math.random() * Math.PI * 2;
            const radius = 0.6 + Math.random() * 0.4;
            const height = Math.random() * 1.2 + 0.3;
            
            particle.position.set(
                Math.cos(angle) * radius,
                height,
                Math.sin(angle) * radius
            );
            
            particle.userData = {
                isTransmutationParticle: true,
                chaosAngle: angle,
                chaosSpeed: 0.03 + Math.random() * 0.04,
                chaosRadius: radius,
                energyPulse: Math.random() * Math.PI * 2,
                energySpeed: 0.06 + Math.random() * 0.04,
                baseHeight: height,
                chaosIntensity: Math.random()
            };
            
            reactorGroup.add(particle);
        }
    }

    /**
     * Animate the transmutation reactor with escalating power
     */
    animateTransmutationReactor(reactor, reactorIndex) {
        const startTime = Date.now();
        const duration = reactor.userData.duration;
        let lastProjectileTime = 0;
        let wavesFired = 0;
        
        const animateFrame = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1.0) {
                // Reactor finished - create ultimate explosion and clean up
                this.explodeTransmutationReactor(reactor);
                return;
            }
            
            // Energy buildup animation (first 20% of duration)
            if (progress < 0.2) {
                const buildupProgress = progress / 0.2;
                reactor.scale.set(buildupProgress, buildupProgress, buildupProgress);
            } else {
                reactor.scale.set(1, 1, 1);
            }
            
            // Update energy level for escalating effects
            reactor.userData.energyLevel = progress;
            
            // Animate transmutation particles with increasing chaos
            reactor.children.forEach(child => {
                if (child.userData.isTransmutationParticle) {
                    // Chaotic orbital motion that increases with energy
                    child.userData.chaosAngle += child.userData.chaosSpeed * (1 + progress * 2);
                    const chaosOffset = Math.sin(Date.now() * 0.01) * progress * 0.3;
                    
                    child.position.x = Math.cos(child.userData.chaosAngle) * (child.userData.chaosRadius + chaosOffset);
                    child.position.z = Math.sin(child.userData.chaosAngle) * (child.userData.chaosRadius + chaosOffset);
                    
                    // Vertical energy surges
                    child.userData.energyPulse += child.userData.energySpeed;
                    child.position.y = child.userData.baseHeight + Math.sin(child.userData.energyPulse) * 0.3 * progress;
                    
                    // Pulsing intensity based on energy level
                    child.material.opacity = 0.5 + Math.sin(child.userData.energyPulse) * 0.4 * progress;
                    child.material.emissiveIntensity = 0.8 + Math.sin(child.userData.energyPulse) * 0.7 * progress;
                }
            });
            
            // Fire explosive projectile waves periodically
            if (elapsed - lastProjectileTime >= reactor.userData.projectileFireRate * 1000 && progress > 0.2) {
                this.fireTransmutationWave(reactor, wavesFired);
                lastProjectileTime = elapsed;
                wavesFired++;
            }
            
            // Reactor core pulsing with increasing intensity
            const pulseIntensity = 1.0 + Math.sin(Date.now() * 0.008) * 0.3 * progress;
            reactor.children.forEach(child => {
                if (child.material && child.material.emissive) {
                    const basePower = child.material.emissiveIntensity || 0.2;
                    child.material.emissiveIntensity = basePower * pulseIntensity;
                }
            });
            
            // Continue animation
            requestAnimationFrame(animateFrame);
        };
        
        animateFrame();
    }

    /**
     * Fire a wave of transmutation projectiles from the reactor with alchemical patterns
     */
    fireTransmutationWave(reactor, waveIndex) {
        if (!this.dungeonHandler.spawnProjectile) return;
        
        const energyLevel = reactor.userData.energyLevel;
        const patternType = reactor.userData.patternIndex;
        
        // Different alchemical patterns based on reactor and energy level
        switch(patternType) {
            case 0: // Pentagram Pattern - 5 pointed star (classic alchemy)
                this.firePentagramPattern(reactor, energyLevel, waveIndex);
                break;
            case 1: // Spiral Pattern - Golden ratio spiral (transmutation)
                this.fireSpiralPattern(reactor, energyLevel, waveIndex);
                break;
            case 2: // Cross Pattern - Four elements (earth, air, fire, water)
                this.fireCrossPattern(reactor, energyLevel, waveIndex);
                break;
            case 3: // Hexagon Pattern - Philosopher's stone (6 sides of perfection)
                this.fireHexagonPattern(reactor, energyLevel, waveIndex);
                break;
        }
    }

    /**
     * Fire projectiles in a pentagram (5-pointed star) pattern
     */
    firePentagramPattern(reactor, energyLevel, waveIndex) {
        const points = 5;
        const raysPerPoint = 3; // 3 projectiles per star point
        
        for (let point = 0; point < points; point++) {
            for (let ray = 0; ray < raysPerPoint; ray++) {
                setTimeout(() => {
                    // Pentagram angles: 0°, 72°, 144°, 216°, 288°
                    const baseAngle = (point * 72) * (Math.PI / 180);
                    const raySpread = (ray - 1) * 0.2; // Small spread per ray
                    const finalAngle = baseAngle + raySpread;
                    
                    const direction = new THREE.Vector3(
                        Math.cos(finalAngle),
                        0,
                        Math.sin(finalAngle)
                    ).normalize();
                    
                    this.spawnTransmutationProjectile(reactor, direction, energyLevel, waveIndex);
                }, (point * raysPerPoint + ray) * 80); // Stagger timing
            }
        }
    }

    /**
     * Fire projectiles in a spiral pattern (golden ratio)
     */
    fireSpiralPattern(reactor, energyLevel, waveIndex) {
        const spiralTurns = 2; // 2 full rotations
        const projectilesPerTurn = 8;
        const totalProjectiles = spiralTurns * projectilesPerTurn;
        
        for (let i = 0; i < totalProjectiles; i++) {
            setTimeout(() => {
                // Golden ratio spiral: each projectile slightly further out and rotated
                const progress = i / totalProjectiles;
                const angle = progress * spiralTurns * Math.PI * 2;
                const radius = 0.3 + (progress * 0.7); // Expanding spiral
                
                const direction = new THREE.Vector3(
                    Math.cos(angle) * radius,
                    0,
                    Math.sin(angle) * radius
                ).normalize();
                
                this.spawnTransmutationProjectile(reactor, direction, energyLevel, waveIndex);
            }, i * 100); // Spiral timing
        }
    }

    /**
     * Fire projectiles in a cross pattern (four elements)
     */
    fireCrossPattern(reactor, energyLevel, waveIndex) {
        const arms = 4; // North, South, East, West
        const projectilesPerArm = 4;
        
        for (let arm = 0; arm < arms; arm++) {
            for (let proj = 0; proj < projectilesPerArm; proj++) {
                setTimeout(() => {
                    // Cross arms: 0°, 90°, 180°, 270°
                    const baseAngle = arm * (Math.PI / 2);
                    const armSpread = (proj - 1.5) * 0.3; // Spread along arm
                    const finalAngle = baseAngle + armSpread;
                    
                    const direction = new THREE.Vector3(
                        Math.cos(finalAngle),
                        0,
                        Math.sin(finalAngle)
                    ).normalize();
                    
                    this.spawnTransmutationProjectile(reactor, direction, energyLevel, waveIndex);
                }, (arm * projectilesPerArm + proj) * 75); // Cross timing
            }
        }
    }

    /**
     * Fire projectiles in a hexagon pattern (philosopher's stone)
     */
    fireHexagonPattern(reactor, energyLevel, waveIndex) {
        const sides = 6; // Perfect hexagon
        const layersPerSide = 3; // Multiple hexagon layers
        
        for (let layer = 0; layer < layersPerSide; layer++) {
            for (let side = 0; side < sides; side++) {
                setTimeout(() => {
                    // Hexagon angles: 0°, 60°, 120°, 180°, 240°, 300°
                    const angle = side * (Math.PI / 3);
                    const layerRadius = 0.8 + (layer * 0.4); // Expanding layers
                    
                    const direction = new THREE.Vector3(
                        Math.cos(angle) * layerRadius,
                        0,
                        Math.sin(angle) * layerRadius
                    ).normalize();
                    
                    this.spawnTransmutationProjectile(reactor, direction, energyLevel, waveIndex);
                }, (layer * sides + side) * 90); // Hexagon timing
            }
        }
    }

    /**
     * Helper method to spawn a transmutation projectile
     */
    spawnTransmutationProjectile(reactor, direction, energyLevel, waveIndex) {
        const transmutationData = {
            projectileType: waveIndex % 3 === 0 ? 'alchemical_symbols' : 
                          waveIndex % 3 === 1 ? 'sacred_geometry' : 'rotating_sigils',
            projectileSpeed: 8.0 + (energyLevel * 6.0), // Slightly slower than before
            disableTrails: false,
            type: 'chronarch',
            health: 1,
            isProjectile: true,
            aiType: 'flying',
            owner: this.enemy,
            damage: reactor.userData.damage,
            scale: 1.2 + (energyLevel * 0.5),
            isExplosive: true,
            explosionRadius: 2.0 + energyLevel,
            transmutationPower: true,
            realityWarping: true,
            glowIntensity: 1.8 + energyLevel,
            emissiveIntensity: 1.0 + energyLevel
        };
        
        try {
            const spawnPos = reactor.position.clone();
            spawnPos.y += 2.5; // Fire from reactor core
            this.dungeonHandler.spawnProjectile(
                spawnPos,
                direction,
                transmutationData
            );
        } catch (error) {
            console.error(`[ChronarchBossController] 🔮 Transmutation projectile error:`, error);
        }
    }

    /**
     * Create ultimate explosion when transmutation reactor reaches critical mass
     */
    explodeTransmutationReactor(reactor) {
        console.log(`[ChronarchBossController] 🔮 Transmutation Reactor ULTIMATE EXPLOSION!`);
        
        // Create the most massive explosion in the game
        const explosionGroup = new THREE.Group();
        explosionGroup.position.copy(reactor.position);
        
        // Create reality-warping explosion particles
        for (let i = 0; i < 50; i++) {
            const shardGeometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const shardColor = [0xFF0000, 0xFFD700, 0xFF00FF, 0xFFFF00, 0xFFFFFF][Math.floor(Math.random() * 5)];
            const shardMaterial = new THREE.MeshBasicMaterial({
                color: shardColor,
                emissive: shardColor,
                emissiveIntensity: 1.0
            });
            
            const shard = new THREE.Mesh(shardGeometry, shardMaterial);
            shard.position.set(
                (Math.random() - 0.5) * 12,
                Math.random() * 6,
                (Math.random() - 0.5) * 12
            );
            
            explosionGroup.add(shard);
        }
        
        // Add explosion to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(explosionGroup);
        }
        
        // Clean up reactor
        if (reactor.parent) {
            reactor.parent.remove(reactor);
        }
        
        // Clean up explosion after extended duration (ultimate finale)
        setTimeout(() => {
            if (explosionGroup.parent) {
                explosionGroup.parent.remove(explosionGroup);
            }
        }, 8000); // 8 seconds for ultimate impact
    }

    // =========================================================================
    // EPIC ENVIRONMENTAL ATTACK EXECUTION METHODS
    // =========================================================================

    /**
     * ⚡ PHASE 1: Crystalline Eruption - Massive crystal spikes erupt from floor
     */
    executeCrystallineEruption() {
        console.log(`[ChronarchBossController] ⚡ EXECUTING: Crystalline Eruption - Massive crystal formation!`);
        
        if (!this.playerPosition) return;
        
        // Create eruption near player position
        const targetPosition = {
            x: this.playerPosition.x + (Math.random() - 0.5) * 4,
            y: 0,
            z: this.playerPosition.z + (Math.random() - 0.5) * 4
        };
        
        // Create the massive crystalline eruption
        const eruption = createCrystallineEruption({
            seed: Math.random()
        });
        
        eruption.position.set(targetPosition.x, targetPosition.y, targetPosition.z);
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(eruption);
            console.log(`[ChronarchBossController] ⚡ Crystalline Eruption spawned at position:`, targetPosition);
        }
        
        // Damage player if within range
        const distanceToPlayer = Math.sqrt(
            Math.pow(targetPosition.x - this.playerPosition.x, 2) + 
            Math.pow(targetPosition.z - this.playerPosition.z, 2)
        );
        
        if (distanceToPlayer < eruption.userData.damageRadius) {
            console.log(`[ChronarchBossController] ⚡ Player caught in Crystalline Eruption blast!`);
            if (this.dungeonHandler.playerController && this.dungeonHandler.playerController.takeDamage) {
                this.dungeonHandler.playerController.takeDamage(60, targetPosition); // Heavy damage for epic attack
            }
        }
        
        // Clean up after duration
        setTimeout(() => {
            if (eruption.parent) {
                eruption.parent.remove(eruption);
                console.log(`[ChronarchBossController] ⚡ Crystalline Eruption expired`);
            }
        }, eruption.userData.duration || 15000);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('crystalline_eruption', 1.8);
        }
    }

    /**
     * 💎 PHASE 1: Shard Volley - Large projectile shards from destroyed objects
     */
    executeShardVolley() {
        console.log(`[ChronarchBossController] 💎 EXECUTING: Shard Volley - Large crystal projectiles!`);
        
        if (!this.playerPosition) return;
        
        const shardCount = 3 + this.currentPhase; // More shards in later phases
        
        for (let i = 0; i < shardCount; i++) {
            setTimeout(() => {
                // Create shard projectile
                const shard = createShardProjectile({
                    seed: Math.random() + i
                });
                
                // Position near boss
                const angleOffset = (i / shardCount) * Math.PI * 2;
                const spawnDistance = 3;
                shard.position.set(
                    this.enemy.position.x + Math.cos(angleOffset) * spawnDistance,
                    this.enemy.position.y + 1,
                    this.enemy.position.z + Math.sin(angleOffset) * spawnDistance
                );
                
                // Calculate trajectory to player
                const direction = new THREE.Vector3(
                    this.playerPosition.x - shard.position.x,
                    this.playerPosition.y - shard.position.y + 1,
                    this.playerPosition.z - shard.position.z
                ).normalize();
                
                // Add to scene
                if (this.dungeonHandler.scene) {
                    this.dungeonHandler.scene.add(shard);
                }
                
                // Animate shard movement
                const shardSpeed = shard.userData.speed || 8.0;
                const moveInterval = setInterval(() => {
                    shard.position.add(direction.clone().multiplyScalar(shardSpeed * 0.016));
                    shard.rotation.x += 0.1;
                    shard.rotation.z += 0.05;
                    
                    // Check collision with player
                    const distanceToPlayer = shard.position.distanceTo(this.playerPosition);
                    if (distanceToPlayer < 1.5) {
                        console.log(`[ChronarchBossController] 💎 Shard hit player!`);
                        this.dungeonHandler.playerController?.takeDamage(shard.userData.damage || 35, shard.position);
                        
                        // Remove shard
                        clearInterval(moveInterval);
                        if (shard.parent) {
                            shard.parent.remove(shard);
                        }
                    }
                    
                    // Remove if too far
                    if (shard.position.length() > 50) {
                        clearInterval(moveInterval);
                        if (shard.parent) {
                            shard.parent.remove(shard);
                        }
                    }
                }, 16);
                
            }, i * 300); // Stagger shard launches
        }
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('shard_volley', 1.5);
        }
    }

    /**
     * 🔧 PHASE 1: Temporal Device Assault - Hijacked room devices attack player
     */
    executeTemporalDeviceAssault() {
        console.log(`[ChronarchBossController] 🔧 EXECUTING: Temporal Device Assault - Hijacking room devices!`);
        
        // Find temporal devices in the room
        const roomObjects = [];
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.traverse((object) => {
                if (object.userData && object.userData.objectType === 'temporal_device') {
                    roomObjects.push(object);
                }
            });
        }
        
        if (roomObjects.length === 0) {
            console.log(`[ChronarchBossController] 🔧 No temporal devices found, using crystal eruption instead`);
            this.executeCrystallineEruption();
            return;
        }
        
        // Hijack up to 3 devices
        const devicesToHijack = roomObjects.slice(0, 3);
        
        devicesToHijack.forEach((device, index) => {
            setTimeout(() => {
                // Store original position
                if (!device.userData.originalPosition) {
                    device.userData.originalPosition = device.position.clone();
                }
                
                // Animate device becoming hostile
                device.userData.hijacked = true;
                
                // Create red lightning bolt from device to player
                this.createLightningBolt(device.position, this.playerPosition, {
                    color: 0xFF0000,
                    duration: 2000,
                    damage: 25
                });
                
                console.log(`[ChronarchBossController] 🔧 Device ${index + 1} hijacked and firing!`);
                
                // Return device to normal after attack
                setTimeout(() => {
                    device.userData.hijacked = false;
                }, 3000);
                
            }, index * 800); // Stagger device attacks
        });
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('temporal_device_assault', 2.0);
        }
    }

    /**
     * 🗿 PHASE 1: Pillar Slam - Levitate and slam worn pillars at player
     */
    executePillarSlam() {
        console.log(`[ChronarchBossController] 🗿 EXECUTING: Pillar Slam - Levitating pillars!`);
        
        // Find time-worn pillars in the room
        const pillars = [];
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.traverse((object) => {
                if (object.userData && object.userData.objectType === 'time_worn_pillar') {
                    pillars.push(object);
                }
            });
        }
        
        if (pillars.length === 0) {
            console.log(`[ChronarchBossController] 🗿 No pillars found, using shard volley instead`);
            this.executeShardVolley();
            return;
        }
        
        // Use the first pillar found
        const pillar = pillars[0];
        
        // Store original position if not already stored
        if (!pillar.userData.originalPosition) {
            pillar.userData.originalPosition = pillar.position.clone();
        }
        
        // Levitate pillar
        const levitateHeight = 4;
        const levitateTime = 1500;
        
        // Animate levitation
        const startY = pillar.position.y;
        const targetY = startY + levitateHeight;
        const levitateStart = Date.now();
        
        const levitateInterval = setInterval(() => {
            const elapsed = Date.now() - levitateStart;
            const progress = Math.min(elapsed / levitateTime, 1);
            
            pillar.position.y = startY + (levitateHeight * progress);
            pillar.rotation.y += 0.02;
            
            if (progress >= 1) {
                clearInterval(levitateInterval);
                
                // Slam pillar down at player position
                setTimeout(() => {
                    this.slamPillar(pillar, this.playerPosition);
                }, 500);
            }
        }, 16);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('pillar_slam', 2.5);
        }
    }

    /**
     * Helper method to slam pillar at target position
     */
    slamPillar(pillar, targetPosition) {
        console.log(`[ChronarchBossController] 🗿 SLAM! Pillar crashing down!`);
        
        // Move pillar to target position
        pillar.position.x = targetPosition.x;
        pillar.position.z = targetPosition.z;
        
        // Animate slam down
        const slamSpeed = 0.5;
        const slamInterval = setInterval(() => {
            pillar.position.y -= slamSpeed;
            
            // Check if pillar hits ground level
            if (pillar.position.y <= pillar.userData.originalPosition.y) {
                pillar.position.y = pillar.userData.originalPosition.y;
                clearInterval(slamInterval);
                
                // Check damage to player
                const distanceToPlayer = Math.sqrt(
                    Math.pow(pillar.position.x - targetPosition.x, 2) + 
                    Math.pow(pillar.position.z - targetPosition.z, 2)
                );
                
                if (distanceToPlayer < 2.5) {
                    console.log(`[ChronarchBossController] 🗿 Player crushed by falling pillar!`);
                    this.dungeonHandler.playerController?.takeDamage(80, pillar.position); // Heavy damage
                }
                
                // Return pillar to original position after delay
                setTimeout(() => {
                    this.returnPillarToPosition(pillar);
                }, 3000);
            }
        }, 16);
    }

    /**
     * Helper method to return pillar to original position
     */
    returnPillarToPosition(pillar) {
        if (!pillar.userData.originalPosition) return;
        
        const returnInterval = setInterval(() => {
            pillar.position.lerp(pillar.userData.originalPosition, 0.05);
            
            if (pillar.position.distanceTo(pillar.userData.originalPosition) < 0.1) {
                pillar.position.copy(pillar.userData.originalPosition);
                clearInterval(returnInterval);
                console.log(`[ChronarchBossController] 🗿 Pillar returned to original position`);
            }
        }, 16);
    }

    /**
     * 🛡️ PHASE 1: Crystal Barrier - Create defensive crystal walls
     */
    executeCrystalBarrier() {
        console.log(`[ChronarchBossController] 🛡️ EXECUTING: Crystal Barrier - Defensive walls!`);
        
        // Create barrier around boss
        const barrierRadius = 4;
        const segmentCount = 8;
        const barriers = [];
        
        for (let i = 0; i < segmentCount; i++) {
            const angle = (i / segmentCount) * Math.PI * 2;
            const barrierPosition = {
                x: this.enemy.position.x + Math.cos(angle) * barrierRadius,
                y: this.enemy.position.y,
                z: this.enemy.position.z + Math.sin(angle) * barrierRadius
            };
            
            // Create small crystalline eruption as barrier segment
            const barrier = createCrystallineEruption({
                seed: Math.random() + i
            });
            
            barrier.position.set(barrierPosition.x, barrierPosition.y, barrierPosition.z);
            barrier.scale.set(0.3, 0.6, 0.3); // Smaller barrier segments
            barrier.userData.isBarrier = true;
            
            barriers.push(barrier);
            
            if (this.dungeonHandler.scene) {
                this.dungeonHandler.scene.add(barrier);
            }
        }
        
        // Remove barriers after duration
        setTimeout(() => {
            barriers.forEach(barrier => {
                if (barrier.parent) {
                    barrier.parent.remove(barrier);
                }
            });
            console.log(`[ChronarchBossController] 🛡️ Crystal Barrier expired`);
        }, 10000);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('crystal_barrier', 1.2);
        }
    }

    /**
     * ⏰ PHASE 2: Time Dilation Field - Create slowdown sphere
     */
    executeTimeDilationField() {
        console.log(`[ChronarchBossController] ⏰ EXECUTING: Time Dilation Field - Slowdown sphere!`);
        
        if (!this.playerPosition) return;
        
        // Create dilation field at player position
        const field = createTimeDilationField({
            radius: 8.0
        });
        
        field.position.set(
            this.playerPosition.x,
            this.playerPosition.y,
            this.playerPosition.z
        );
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(field);
            console.log(`[ChronarchBossController] ⏰ Time Dilation Field spawned`);
        }
        
        // Apply slowdown effect to player
        if (this.dungeonHandler.player) {
            const originalSpeed = this.dungeonHandler.player.userData.moveSpeed || 1.0;
            this.dungeonHandler.player.userData.moveSpeed = originalSpeed * field.userData.slowdownFactor;
            console.log(`[ChronarchBossController] ⏰ Player movement slowed to ${field.userData.slowdownFactor * 100}%`);
            
            // Restore speed after field expires
            setTimeout(() => {
                this.dungeonHandler.player.userData.moveSpeed = originalSpeed;
                console.log(`[ChronarchBossController] ⏰ Player movement speed restored`);
            }, field.userData.duration);
        }
        
        // Clean up field
        setTimeout(() => {
            if (field.parent) {
                field.parent.remove(field);
                console.log(`[ChronarchBossController] ⏰ Time Dilation Field expired`);
            }
        }, field.userData.duration);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('time_dilation_field', 2.0);
        }
    }

    /**
     * 👻 PHASE 2: Echoes of the Past - Ghostly temporal echoes
     */
    executeEchoesOfThePast() {
        console.log(`[ChronarchBossController] 👻 EXECUTING: Echoes of the Past - Temporal echoes!`);
        
        // Create temporal echo of the boss
        const echo = createTemporalEcho(this.enemy, {
            echoDelay: 2000,
            lifetime: 8000,
            attacksToEcho: ['crystalline_eruption', 'shard_volley']
        });
        
        // Position echo slightly offset from boss
        echo.position.set(
            this.enemy.position.x + 3,
            this.enemy.position.y,
            this.enemy.position.z + 3
        );
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(echo);
            console.log(`[ChronarchBossController] 👻 Temporal Echo spawned`);
        }
        
        // Echo will repeat boss attacks with delay
        setTimeout(() => {
            console.log(`[ChronarchBossController] 👻 Echo begins repeating attacks`);
            
            // Echo performs a weaker version of recent attacks
            echo.userData.attacksToEcho.forEach((attackType, index) => {
                setTimeout(() => {
                    if (attackType === 'crystalline_eruption') {
                        // Smaller eruption from echo
                        this.createEchoAttack(echo.position, 'crystalline_eruption');
                    } else if (attackType === 'shard_volley') {
                        // Fewer shards from echo
                        this.createEchoAttack(echo.position, 'shard_volley');
                    }
                }, index * 1500);
            });
        }, echo.userData.echoDelay);
        
        // Clean up echo
        setTimeout(() => {
            if (echo.parent) {
                echo.parent.remove(echo);
                console.log(`[ChronarchBossController] 👻 Temporal Echo faded away`);
            }
        }, echo.userData.lifetime);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('echoes_of_the_past', 1.8);
        }
    }

    /**
     * Helper method to create echo attacks (weaker versions)
     */
    createEchoAttack(echoPosition, attackType) {
        if (attackType === 'crystalline_eruption') {
            const smallEruption = createCrystallineEruption({ seed: Math.random() });
            smallEruption.position.copy(echoPosition);
            smallEruption.scale.set(0.5, 0.5, 0.5); // Half size
            
            if (this.dungeonHandler.scene) {
                this.dungeonHandler.scene.add(smallEruption);
            }
            
            setTimeout(() => {
                if (smallEruption.parent) {
                    smallEruption.parent.remove(smallEruption);
                }
            }, 8000);
        }
    }

    /**
     * ⏸️ PHASE 2: Chronostatic Prison - Time freeze imprisonment
     */
    executeChronostaticPrison() {
        console.log(`[ChronarchBossController] ⏸️ EXECUTING: Chronostatic Prison - Time freeze!`);
        
        // Freeze player temporarily
        if (this.dungeonHandler.player) {
            console.log(`[ChronarchBossController] ⏸️ Player frozen in time!`);
            
            // Store original controls
            const originalControls = this.dungeonHandler.player.userData.controlsEnabled;
            this.dungeonHandler.player.userData.controlsEnabled = false;
            
            // Visual effect - freeze all floating objects in room
            if (this.dungeonHandler.scene) {
                this.dungeonHandler.scene.traverse((object) => {
                    if (object.userData && object.userData.isFloating) {
                        object.userData.frozenInTime = true;
                    }
                });
            }
            
            // Release after duration
            setTimeout(() => {
                this.dungeonHandler.player.userData.controlsEnabled = originalControls;
                
                // Unfreeze objects and launch them as weapons
                if (this.dungeonHandler.scene) {
                    this.dungeonHandler.scene.traverse((object) => {
                        if (object.userData && object.userData.frozenInTime) {
                            object.userData.frozenInTime = false;
                            
                            // Launch object toward player
                            this.launchFrozenObjectAtPlayer(object);
                        }
                    });
                }
                
                console.log(`[ChronarchBossController] ⏸️ Time prison released - objects launched!`);
            }, 3000); // 3 second freeze
        }
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('chronostatic_prison', 2.2);
        }
    }

    /**
     * Helper method to launch frozen objects at player
     */
    launchFrozenObjectAtPlayer(object) {
        if (!this.playerPosition) return;
        
        const direction = new THREE.Vector3(
            this.playerPosition.x - object.position.x,
            this.playerPosition.y - object.position.y + 1,
            this.playerPosition.z - object.position.z
        ).normalize();
        
        const launchSpeed = 3.0;
        const launchInterval = setInterval(() => {
            object.position.add(direction.clone().multiplyScalar(launchSpeed * 0.016));
            object.rotation.x += 0.05;
            object.rotation.y += 0.03;
            
            // Check collision with player
            const distanceToPlayer = object.position.distanceTo(this.playerPosition);
            if (distanceToPlayer < 1.2) {
                console.log(`[ChronarchBossController] ⏸️ Frozen object hit player!`);
                this.dungeonHandler.playerController?.takeDamage(20, object.position);
                clearInterval(launchInterval);
                
                // Return object to original position
                if (object.userData.originalPosition) {
                    object.position.copy(object.userData.originalPosition);
                }
            }
            
            // Stop if object goes too far
            if (object.position.length() > 30) {
                clearInterval(launchInterval);
                if (object.userData.originalPosition) {
                    object.position.copy(object.userData.originalPosition);
                }
            }
        }, 16);
    }

    /**
     * 💥 PHASE 2: Temporal Rift Pulse - Energy waves from central rift
     */
    executeTemporalRiftPulse() {
        console.log(`[ChronarchBossController] 💥 EXECUTING: Temporal Rift Pulse - Energy waves!`);
        
        // Find the temporal rift in the room
        let rift = null;
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.traverse((object) => {
                if (object.userData && object.userData.objectType === 'temporal_rift') {
                    rift = object;
                }
            });
        }
        
        if (!rift) {
            console.log(`[ChronarchBossController] 💥 No rift found, using center of room`);
            rift = { position: { x: 0, y: 1, z: 0 } };
        }
        
        // Create expanding energy waves
        const waveCount = 3;
        
        for (let i = 0; i < waveCount; i++) {
            setTimeout(() => {
                this.createEnergyWave(rift.position, i);
            }, i * 1000);
        }
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('temporal_rift_pulse', 2.5);
        }
    }

    /**
     * Helper method to create expanding energy wave
     */
    createEnergyWave(centerPosition, waveIndex) {
        console.log(`[ChronarchBossController] 💥 Creating energy wave ${waveIndex + 1}`);
        
        const waveGeometry = new THREE.RingGeometry(0.1, 1, 16);
        const waveMaterial = new THREE.MeshBasicMaterial({
            color: 0x8A2BE2,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
        
        const wave = new THREE.Mesh(waveGeometry, waveMaterial);
        wave.position.set(centerPosition.x, centerPosition.y + 0.1, centerPosition.z);
        wave.rotation.x = -Math.PI / 2; // Lay flat on ground
        
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(wave);
        }
        
        // Animate wave expansion
        let waveRadius = 1;
        const maxRadius = 12;
        const expansionSpeed = 0.3;
        
        const waveInterval = setInterval(() => {
            waveRadius += expansionSpeed;
            wave.scale.set(waveRadius, waveRadius, 1);
            wave.material.opacity = 0.6 * (1 - waveRadius / maxRadius);
            
            // Check damage to player
            if (this.playerPosition) {
                const distanceToPlayer = Math.sqrt(
                    Math.pow(centerPosition.x - this.playerPosition.x, 2) + 
                    Math.pow(centerPosition.z - this.playerPosition.z, 2)
                );
                
                if (Math.abs(distanceToPlayer - waveRadius) < 0.5) {
                    console.log(`[ChronarchBossController] 💥 Energy wave hit player!`);
                    this.dungeonHandler.playerController?.takeDamage(30, new THREE.Vector3(centerPosition.x, centerPosition.y, centerPosition.z));
                }
            }
            
            // Remove wave when fully expanded
            if (waveRadius >= maxRadius) {
                clearInterval(waveInterval);
                if (wave.parent) {
                    wave.parent.remove(wave);
                }
            }
        }, 16);
    }

    /**
     * ⚡ PHASE 2: Reality Fracture - Cracks appear in space
     */
    executeRealityFracture() {
        console.log(`[ChronarchBossController] ⚡ EXECUTING: Reality Fracture - Space cracking!`);
        
        if (!this.playerPosition) return;
        
        // Create fracture near player
        const fracture = createRealityFracture({
            length: 10.0
        });
        
        fracture.position.set(
            this.playerPosition.x + (Math.random() - 0.5) * 6,
            this.playerPosition.y,
            this.playerPosition.z + (Math.random() - 0.5) * 6
        );
        
        // Random rotation for crack direction
        fracture.rotation.y = Math.random() * Math.PI * 2;
        
        // Add to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(fracture);
            console.log(`[ChronarchBossController] ⚡ Reality Fracture spawned`);
        }
        
        // Damage player over time if in crack zone
        const damageInterval = setInterval(() => {
            if (this.playerPosition) {
                const distanceToFracture = fracture.position.distanceTo(this.playerPosition);
                if (distanceToFracture < 3.0) {
                    console.log(`[ChronarchBossController] ⚡ Player caught in reality fracture!`);
                    this.dungeonHandler.playerController?.takeDamage(fracture.userData.damagePerSecond || 10, fracture.position);
                }
            }
        }, 1000);
        
        // Clean up fracture
        setTimeout(() => {
            clearInterval(damageInterval);
            if (fracture.parent) {
                fracture.parent.remove(fracture);
                console.log(`[ChronarchBossController] ⚡ Reality Fracture healed`);
            }
        }, fracture.userData.duration);
        
        // Start animation
        if (this.animationHandler) {
            this.animationHandler.triggerAttackAnimation('reality_fracture', 2.0);
        }
    }

    // PHASE 3 ULTIMATE ATTACKS - TO BE IMPLEMENTED
    // These are the most complex attacks requiring room transformation
    

    /**
     * Helper method to create realistic red lightning bolt between two positions
     */
    createLightningBolt(startPos, endPos, options = {}) {
        const lightningGroup = new THREE.Group();
        const totalDistance = startPos.distanceTo(endPos);
        const segments = Math.floor(totalDistance / 0.4) + 5; // More segments for detailed lightning
        
        // Create lightning path with random jitter
        const points = [];
        const direction = new THREE.Vector3().subVectors(endPos, startPos).normalize();
        const perpendicular1 = new THREE.Vector3().crossVectors(direction, new THREE.Vector3(0, 1, 0)).normalize();
        const perpendicular2 = new THREE.Vector3().crossVectors(direction, perpendicular1).normalize();
        
        for (let i = 0; i <= segments; i++) {
            const progress = i / segments;
            const basePoint = new THREE.Vector3().lerpVectors(startPos, endPos, progress);
            
            // Add random jitter for lightning zigzag effect
            if (i > 0 && i < segments) {
                const jitterStrength = Math.sin(progress * Math.PI) * 0.8; // Stronger jitter in middle
                const jitterX = (Math.random() - 0.5) * jitterStrength;
                const jitterY = (Math.random() - 0.5) * jitterStrength;
                
                basePoint.add(perpendicular1.clone().multiplyScalar(jitterX));
                basePoint.add(perpendicular2.clone().multiplyScalar(jitterY));
            }
            
            points.push(basePoint);
        }
        
        // Create lightning segments with multiple layers for glow effect
        const colors = [
            { color: 0xFF0000, size: 0.08, opacity: 1.0, intensity: 1.5 },    // Bright red core
            { color: 0xFF4444, size: 0.12, opacity: 0.8, intensity: 1.0 },   // Medium red glow
            { color: 0xFF6666, size: 0.16, opacity: 0.6, intensity: 0.8 }    // Outer red glow
        ];
        
        colors.forEach(layer => {
            for (let i = 0; i < points.length - 1; i++) {
                const start = points[i];
                const end = points[i + 1];
                const segmentDistance = start.distanceTo(end);
                
                // Create lightning segment
                const segmentGeometry = new THREE.CylinderGeometry(layer.size, layer.size, segmentDistance, 4);
                const segmentMaterial = new THREE.MeshBasicMaterial({
                    color: layer.color,
                    emissive: layer.color,
                    emissiveIntensity: layer.intensity,
                    transparent: true,
                    opacity: layer.opacity
                });
                
                const segment = new THREE.Mesh(segmentGeometry, segmentMaterial);
                
                // Position and orient segment
                const midPoint = new THREE.Vector3().addVectors(start, end).multiplyScalar(0.5);
                segment.position.copy(midPoint);
                segment.lookAt(end);
                segment.rotateX(Math.PI / 2);
                
                segment.castShadow = false;
                segment.receiveShadow = false;
                lightningGroup.add(segment);
            }
        });
        
        // Add branching lightning effects
        if (points.length > 4) {
            const branchCount = 2 + Math.floor(Math.random() * 3);
            for (let b = 0; b < branchCount; b++) {
                const branchStartIndex = Math.floor(Math.random() * (points.length - 2)) + 1;
                const branchStart = points[branchStartIndex];
                const branchLength = totalDistance * (0.2 + Math.random() * 0.3);
                
                // Create random branch direction
                const randomDir = new THREE.Vector3(
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2,
                    (Math.random() - 0.5) * 2
                ).normalize().multiplyScalar(branchLength);
                
                const branchEnd = branchStart.clone().add(randomDir);
                const branchDistance = branchStart.distanceTo(branchEnd);
                
                // Create branch segment (smaller and dimmer)
                const branchGeometry = new THREE.CylinderGeometry(0.04, 0.04, branchDistance, 4);
                const branchMaterial = new THREE.MeshBasicMaterial({
                    color: 0xFF0000,
                    emissive: 0xFF0000,
                    emissiveIntensity: 1.2,
                    transparent: true,
                    opacity: 0.8
                });
                
                const branch = new THREE.Mesh(branchGeometry, branchMaterial);
                const branchMidPoint = new THREE.Vector3().addVectors(branchStart, branchEnd).multiplyScalar(0.5);
                branch.position.copy(branchMidPoint);
                branch.lookAt(branchEnd);
                branch.rotateX(Math.PI / 2);
                
                branch.castShadow = false;
                branch.receiveShadow = false;
                lightningGroup.add(branch);
            }
        }
        
        // Add lightning group to scene
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(lightningGroup);
        }
        
        // Create bright flash effect for lightning
        const flashLight = new THREE.PointLight(0xFF0000, 3.0, 8.0);
        flashLight.position.copy(startPos);
        lightningGroup.add(flashLight);
        
        // Animate flash intensity
        let flashIntensity = 3.0;
        const flashAnimation = setInterval(() => {
            flashIntensity *= 0.85;
            flashLight.intensity = flashIntensity;
            if (flashIntensity < 0.1) {
                clearInterval(flashAnimation);
            }
        }, 50);
        
        // Damage player if lightning hits
        if (options.damage && this.playerPosition) {
            const distanceToLightning = startPos.distanceTo(this.playerPosition);
            if (distanceToLightning < 2.0) {
                this.dungeonHandler.playerController?.takeDamage(options.damage, startPos);
            }
        }
        
        // Remove lightning after duration
        setTimeout(() => {
            if (lightningGroup.parent) {
                lightningGroup.parent.remove(lightningGroup);
            }
        }, options.duration || 2000);
        
        return lightningGroup;
    }
}