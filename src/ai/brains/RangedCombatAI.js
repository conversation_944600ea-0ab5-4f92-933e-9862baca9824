/**
 * Ranged Combat AI implementation
 * Completely rebuilt for reliability with skeleton archers
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates } from '../AIStates.js';
import { groupCoordinator } from '../GroupCoordinator.js';
import { PathfindingSystem } from '../PathfindingSystem.js';

export class RangedCombatAI extends AIBrain {
    /**
     * Constructor for Ranged Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Ranged specific parameters
        this.preferredRange = enemyData.preferredRange || 8.0;
        this.minRange = enemyData.moveAwayRange || 2.0;
        this.maxRange = enemyData.maxRange || 30.0;

        // Log the actual ranges being used
        console.log(`[RangedCombatAI] Enemy ${enemy.name} ranges: preferred=${this.preferredRange.toFixed(1)}, min=${this.minRange.toFixed(1)}, max=${this.maxRange.toFixed(1)}`);

        // Movement parameters
        this.moveSpeed = enemyData.baseSpeed || 2.5;
        this.moveTimer = 0;
        this.moveDuration = 1.0 + Math.random() * 1.0; // 1-2 seconds of movement
        this.moveDirection = new THREE.Vector3();

        // Shooting parameters
        this.shootCooldown = enemyData.attackCooldown || 1.2;
        this.shootTimer = 0;
        this.aimDuration = 0.1; // Very short aiming time
        this.aimTimer = 0;
        this.isAiming = false;

        // Register with group coordinator
        groupCoordinator.registerEnemy(enemy, this);

        // Simple stuck detection - much lighter weight
        this.stuckDetection = {
            lastPosition: new THREE.Vector3(),
            stuckTimer: 0,
            maxStuckTime: 2.0,
            checkInterval: 0.5, // Only check every 0.5 seconds
            lastCheckTime: 0
        };

        // Initialize pathfinding system
        this.pathfindingSystem = new PathfindingSystem(this);
        this.currentPath = [];
        this.currentWaypoint = null;

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Simple stuck detection - only check periodically
     * @param {Number} deltaTime - Time since last update
     * @returns {Boolean} True if enemy is stuck
     * @private
     */
    _detectStuckBehavior(deltaTime) {
        this.stuckDetection.lastCheckTime += deltaTime;

        // Only check every 0.5 seconds to reduce performance impact
        if (this.stuckDetection.lastCheckTime < this.stuckDetection.checkInterval) {
            return false;
        }

        this.stuckDetection.lastCheckTime = 0;
        const currentPos = this.enemy.position.clone();

        // Check if we've moved since last check
        const distanceMoved = currentPos.distanceTo(this.stuckDetection.lastPosition);

        if (distanceMoved < 0.2) { // Very little movement
            this.stuckDetection.stuckTimer += this.stuckDetection.checkInterval;

            if (this.stuckDetection.stuckTimer >= this.stuckDetection.maxStuckTime) {
                console.log(`[RangedCombatAI] ${this.enemy.name} detected as stuck`);
                this.stuckDetection.stuckTimer = 0; // Reset to prevent constant triggering
                this.stuckDetection.lastPosition.copy(currentPos);
                return true;
            }
        } else {
            // Reset stuck timer if we're moving
            this.stuckDetection.stuckTimer = 0;
        }

        this.stuckDetection.lastPosition.copy(currentPos);
        return false;
    }



    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update timers
        if (this.moveTimer > 0) this.moveTimer -= deltaTime;
        if (this.shootTimer > 0) this.shootTimer -= deltaTime;
        if (this.isAiming) this.aimTimer += deltaTime;

        // Check for stuck behavior (lightweight check)
        const isStuck = this._detectStuckBehavior(deltaTime);

        // Use existing simple line of sight check
        const hasLineOfSight = this._canSeePlayer();

        // Log state occasionally
        if (Math.random() < 0.02) { // Reduced logging frequency
            console.log(`[RangedCombatAI] ${this.enemy.name} - State: ${this.currentState}, Distance: ${distanceToPlayer.toFixed(1)}, LOS: ${hasLineOfSight}`);
        }

        // If stuck, use enhanced unstuck behavior
        if (isStuck) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - Stuck, attempting unstuck maneuver`);
            this._performUnstuckManeuver();
            this.setState(AIStates.MOVING);
            this.moveTimer = 1.5; // Move for longer to get out of stuck position
            return;
        }

        // SIMPLIFIED STATE MACHINE
        switch (this.currentState) {
            case AIStates.IDLE:
                // PRIMARY GOAL: Attack the player
                if (distanceToPlayer <= this.maxRange) {
                    // If we can shoot AND have line of sight, start aiming
                    if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange && hasLineOfSight) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Starting to aim at player at distance ${distanceToPlayer.toFixed(1)}`);
                        this.setState(AIStates.AIMING);
                    }
                    // If player is too close, move away
                    else if (distanceToPlayer < this.minRange) {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Player too close, moving away`);
                        this.setState(AIStates.FLEEING);
                    }
                    // If no line of sight or can't shoot yet, move to get better position
                    else {
                        console.log(`[RangedCombatAI] ${this.enemy.name} - Moving to attack position (LOS: ${hasLineOfSight})`);
                        this.setState(AIStates.MOVING);
                    }
                }
                // If player is out of range, move closer to attack
                else {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Player out of range, moving closer to attack`);
                    this.setState(AIStates.MOVING);
                }
                break;

            case AIStates.MOVING:
                // If we can shoot AND have line of sight, start aiming
                if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange && distanceToPlayer <= this.maxRange && hasLineOfSight) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Can shoot while moving, starting to aim`);
                    this.setState(AIStates.AIMING);
                }
                // If player is too close, move away
                else if (distanceToPlayer < this.minRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Player too close while moving, fleeing`);
                    this.setState(AIStates.FLEEING);
                }
                // If move timer expired, go back to idle to reassess
                else if (this.moveTimer <= 0) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Move complete, reassessing attack position`);
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.AIMING:
                // If aiming is complete, shoot
                if (this.aimTimer >= this.aimDuration) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Aim complete, shooting`);
                    this._executeShoot();
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.FLEEING:
                // If we've moved far enough away, go back to idle
                if (distanceToPlayer >= this.preferredRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Reached safe distance, going idle`);
                    this.setState(AIStates.IDLE);
                }
                // If we can shoot while fleeing, do it
                else if (this.shootTimer <= 0 && distanceToPlayer >= this.minRange) {
                    console.log(`[RangedCombatAI] ${this.enemy.name} - Can shoot while fleeing, starting to aim`);
                    this.setState(AIStates.AIMING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless fleeing
        if (this.currentState !== AIStates.FLEEING && this.player) {
            this._faceTarget(this.player.position);
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Just stand still
                break;

            case AIStates.MOVING:
                this._executeMoveToPreferredRange(deltaTime, distanceToPlayer, directionToPlayer);
                break;

            case AIStates.AIMING:
                // Just stand still while aiming
                break;

            case AIStates.FLEEING:
                this._executeFleeingBehavior(deltaTime, directionToPlayer);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        console.log(`[RangedCombatAI] ${this.enemy.name} - Entering state: ${state}`);

        switch (state) {
            case AIStates.IDLE:
                // No special initialization
                break;

            case AIStates.MOVING:
                // Set move timer
                this.moveTimer = this.moveDuration;
                // Choose random move direction
                this._chooseRandomMoveDirection();
                break;

            case AIStates.AIMING:
                // Reset aim timer
                this.aimTimer = 0;
                this.isAiming = true;
                break;

            case AIStates.FLEEING:
                // No special initialization
                break;
        }
    }

    /**
     * Choose a movement direction prioritizing attack positioning
     * @private
     */
    _chooseRandomMoveDirection() {
        if (!this.player) return;

        // Get direction to player
        const dirToPlayer = this.player.position.clone().sub(this.enemy.position).normalize();

        // Calculate distance to player
        const distanceToPlayer = this.enemy.position.distanceTo(this.player.position);

        // Simple movement logic - prioritize getting into attack position
        if (distanceToPlayer < this.preferredRange - 1.0) {
            // Too close, move away slightly
            this.moveDirection = dirToPlayer.clone().multiplyScalar(-1);
            // Add slight randomness
            this.moveDirection.x += (Math.random() - 0.5) * 0.3;
            this.moveDirection.z += (Math.random() - 0.5) * 0.3;
        } else if (distanceToPlayer > this.preferredRange + 1.0) {
            // Too far, move closer to attack
            this.moveDirection = dirToPlayer.clone();
            // Add slight randomness
            this.moveDirection.x += (Math.random() - 0.5) * 0.3;
            this.moveDirection.z += (Math.random() - 0.5) * 0.3;
        } else {
            // At good distance, strafe perpendicular to player for better attack angle
            const perpDirection = new THREE.Vector3(-dirToPlayer.z, 0, dirToPlayer.x);
            this.moveDirection = perpDirection.normalize();
            // Randomly choose direction
            if (Math.random() < 0.5) {
                this.moveDirection.multiplyScalar(-1);
            }
        }

        // Normalize the direction
        this.moveDirection.normalize();
    }

    /**
     * Execute movement to preferred range
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeMoveToPreferredRange(deltaTime, distanceToPlayer, directionToPlayer) {
        if (!this.player) return;

        // Calculate move amount
        const moveAmount = this.moveSpeed * deltaTime;
        
        // Use pathfinding to navigate around obstacles
        if (!this.currentPath || this.currentPath.length === 0 || 
            (Date.now() - (this.lastPathUpdate || 0) > 500)) {
            // Update path every 500ms or when no path exists
            const obstacles = this._getObstaclesForPathfinding();
            
            // For ranged enemies, find path to preferred range, not directly to player
            const targetPos = this._calculatePreferredPosition(distanceToPlayer, directionToPlayer);
            this.currentPath = this.pathfindingSystem.findPath(targetPos, obstacles);
            this.lastPathUpdate = Date.now();
        }
        
        // Get next waypoint
        this.currentWaypoint = this.pathfindingSystem.getNextWaypoint();
        
        let moveVector;
        let newPosition;
        
        if (this.currentWaypoint) {
            // Move towards waypoint
            const directionToWaypoint = new THREE.Vector3()
                .copy(this.currentWaypoint)
                .sub(this.enemy.position)
                .normalize();
            
            // Get steering force to avoid dynamic obstacles
            const steeringForce = this.pathfindingSystem.getSteeringForce(
                directionToWaypoint, 
                this._getDynamicObstacles()
            );
            
            // Combine waypoint direction with steering
            const finalDirection = directionToWaypoint.add(steeringForce).normalize();
            moveVector = finalDirection.multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        } else {
            // Fallback to using moveDirection if no path
            moveVector = this.moveDirection.clone().multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        }

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Selective collision detection - skip floor objects but check walls/obstacles
        // Use the same filtering logic as PlayerController
        let canMove = true;
        
        // Skip collision detection entirely if we have a valid pathfinding waypoint
        if (this.currentWaypoint) {
            canMove = true; // Trust pathfinding
        } else if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a bounding box for the enemy at the test position
            // CRITICAL FIX: Use current Y position for collision check, only test X/Z movement
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, this.enemy.position.y, newPosition.z);
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with non-floor objects only
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL: Use same floor filtering logic as PlayerController
                // Check userData.isFloor first (most reliable method)
                const isFloorByUserData = obj.userData?.isFloor === true;

                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                const hasFloorMaterial = obj.material && (
                    obj.material.name?.includes('CaveFloorMaterial') ||
                    obj.material.name?.includes('caveFloor') ||
                    (obj.material.materials && obj.material.materials.some(mat =>
                        mat.name?.includes('CaveFloorMaterial') || mat.name?.includes('caveFloor')))
                );

                // Skip ALL types of floor objects - they should not block enemy movement
                if (isFloorByUserData || isFloor || hasFloorMaterial) {
                    continue; // Skip floor collision for enemy movement
                }

                try {
                    // Check collision with walls and objects (not floors)
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    // Continue if collision check fails
                    continue;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // CRITICAL FIX: Copy approach from MeleeCombatAI - only update X and Z coordinates
            // Don't modify Y position to avoid floor collision issues
            this.enemy.position.x = newPosition.x;
            this.enemy.position.z = newPosition.z;

            // CRITICAL FIX: Apply floor curvature adaptation (inherited from AIBrain)
            this._adaptToFloorHeight();
        } else {
            // If collision, just choose a new direction - keep it simple
            this._chooseRandomMoveDirection();
        }
    }

    /**
     * Execute fleeing behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeFleeingBehavior(deltaTime, directionToPlayer) {
        if (!this.player) return;

        // Move away from player
        const fleeDirection = directionToPlayer.clone().multiplyScalar(-1);

        // Add some randomness to fleeing direction
        const randomAngle = (Math.random() - 0.5) * Math.PI / 6; // +/- 30 degrees
        const randomizedDirection = new THREE.Vector3(
            fleeDirection.x * Math.cos(randomAngle) - fleeDirection.z * Math.sin(randomAngle),
            0,
            fleeDirection.x * Math.sin(randomAngle) + fleeDirection.z * Math.cos(randomAngle)
        );

        // Calculate move amount
        const fleeSpeed = this.moveSpeed * 1.2; // Slightly faster when fleeing
        const moveAmount = fleeSpeed * deltaTime;

        // Calculate new position
        const moveVector = randomizedDirection.normalize().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Selective collision detection - skip floor objects but check walls/obstacles
        // Use the same filtering logic as PlayerController
        let canMove = true;
        
        // Skip collision detection entirely if we have a valid pathfinding waypoint
        if (this.currentWaypoint) {
            canMove = true; // Trust pathfinding
        } else if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a bounding box for the enemy at the test position
            // CRITICAL FIX: Use current Y position for collision check, only test X/Z movement
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, this.enemy.position.y, newPosition.z);
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with non-floor objects only
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL: Use same floor filtering logic as PlayerController
                // Check userData.isFloor first (most reliable method)
                const isFloorByUserData = obj.userData?.isFloor === true;

                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                const hasFloorMaterial = obj.material && (
                    obj.material.name?.includes('CaveFloorMaterial') ||
                    obj.material.name?.includes('caveFloor') ||
                    (obj.material.materials && obj.material.materials.some(mat =>
                        mat.name?.includes('CaveFloorMaterial') || mat.name?.includes('caveFloor')))
                );

                // Skip ALL types of floor objects - they should not block enemy movement
                if (isFloorByUserData || isFloor || hasFloorMaterial) {
                    continue; // Skip floor collision for enemy movement
                }

                try {
                    // Check collision with walls and objects (not floors)
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    // Continue if collision check fails
                    continue;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // CRITICAL FIX: Copy approach from MeleeCombatAI - only update X and Z coordinates
            // Don't modify Y position to avoid floor collision issues
            this.enemy.position.x = newPosition.x;
            this.enemy.position.z = newPosition.z;

            // CRITICAL FIX: Apply floor curvature adaptation (inherited from AIBrain)
            this._adaptToFloorHeight();

            // Face away from player when fleeing
            this._faceTarget(this.enemy.position.clone().add(randomizedDirection));
        } else {
            // If collision while fleeing, try a simple perpendicular direction
            const perpendicularDir = new THREE.Vector3(-randomizedDirection.z, 0, randomizedDirection.x);
            const altMoveVector = perpendicularDir.normalize().multiplyScalar(moveAmount * 0.5);
            const altPosition = this.enemy.position.clone().add(altMoveVector);

            // Apply alternative movement without further collision checks to prevent getting stuck
            // CRITICAL FIX: Copy approach from MeleeCombatAI - only update X and Z coordinates
            this.enemy.position.x = altPosition.x;
            this.enemy.position.z = altPosition.z;

            // CRITICAL FIX: Apply floor curvature adaptation for alternative movement
            this._adaptToFloorHeight();

            this._faceTarget(this.enemy.position.clone().add(perpendicularDir));
        }
    }

    /**
     * Execute shooting behavior
     * @private
     */
    _executeShoot() {
        console.log(`[RangedCombatAI] ${this.enemy.name} - Executing shoot`);

        // Reset shooting cooldown
        this.shootTimer = this.shootCooldown;
        this.isAiming = false;

        if (!this.player) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - Cannot shoot: player is null`);
            return;
        }

        // Calculate direction to player
        const shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            console.log(`[RangedCombatAI] ${this.enemy.name} - Calling onShoot with direction: ${shootDirection.x.toFixed(2)},${shootDirection.y.toFixed(2)},${shootDirection.z.toFixed(2)}`);
            this.enemy.userData.onShoot(shootDirection);
        } else {
            console.log(`[RangedCombatAI] ${this.enemy.name} - No onShoot handler found`);
        }

        // Force a priority update
        if (this.enemy.userData) {
            this.enemy.userData.priorityUpdate = true;
        }
    }

    /**
     * Perform unstuck maneuver when enemy is stuck
     * @private
     */
    _performUnstuckManeuver() {
        console.log(`[RangedCombatAI] ${this.enemy.name} - Performing unstuck maneuver`);
        
        // Reset stuck detection
        this.stuckDetection.stuckTimer = 0;
        this.stuckDetection.lastPosition.copy(this.enemy.position);
        
        // Choose a random direction away from current position
        const randomAngle = Math.random() * Math.PI * 2;
        const moveDistance = 2.0 + Math.random() * 2.0; // Move 2-4 units
        
        // Calculate new position
        const moveVector = new THREE.Vector3(
            Math.cos(randomAngle) * moveDistance,
            0,
            Math.sin(randomAngle) * moveDistance
        );
        
        // Apply the movement directly without collision checks to unstuck
        this.enemy.position.x += moveVector.x;
        this.enemy.position.z += moveVector.z;
        
        // Adapt to floor height
        this._adaptToFloorHeight();
        
        // Face the new direction
        this._faceTarget(this.enemy.position.clone().add(moveVector));
        
        // Reset movement timer to continue moving
        this.moveTimer = 1.5;
        this.moveDirection = moveVector.normalize();
    }
    
    /**
     * Calculate preferred position for ranged combat
     * @param {Number} distanceToPlayer - Current distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @returns {THREE.Vector3} Target position
     * @private
     */
    _calculatePreferredPosition(distanceToPlayer, directionToPlayer) {
        // Calculate position at preferred range from player
        const offsetFromPlayer = directionToPlayer.clone().multiplyScalar(-this.preferredRange);
        return this.player.position.clone().add(offsetFromPlayer);
    }
    
    /**
     * Get obstacles for pathfinding (static objects like walls, vases, etc.)
     * @returns {Array} Array of obstacle objects
     * @private
     */
    _getObstaclesForPathfinding() {
        const obstacles = [];
        
        if (this.collisionObjects) {
            for (const obj of this.collisionObjects) {
                // Skip floors
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                
                if (isFloorByUserData || isFloor) {
                    continue;
                }
                
                // Include all non-floor objects as obstacles
                // This includes walls, vases, pillars, etc.
                if (obj.position) {
                    obstacles.push({
                        position: obj.position.clone(),
                        radius: obj.userData?.size || 1.0,
                        type: obj.userData?.type || 'unknown'
                    });
                }
            }
        }
        
        return obstacles;
    }
    
    /**
     * Get dynamic obstacles (other enemies, debris, etc.)
     * @returns {Array} Array of dynamic obstacle objects
     * @private
     */
    _getDynamicObstacles() {
        const obstacles = [];
        
        // Add nearby enemies as obstacles
        if (this.environmentalData && this.environmentalData.nearbyEnemies) {
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance > 0.1) { // Skip self
                    obstacles.push({
                        position: enemyData.position.clone(),
                        radius: 1.0,
                        type: 'enemy'
                    });
                }
            }
        }
        
        // Add nearby debris
        if (this.environmentalData && this.environmentalData.nearbyDebris) {
            for (const debrisData of this.environmentalData.nearbyDebris) {
                obstacles.push({
                    position: debrisData.position.clone(),
                    radius: 0.5,
                    type: 'debris'
                });
            }
        }
        
        return obstacles;
    }
}
