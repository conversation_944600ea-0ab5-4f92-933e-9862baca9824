/**
 * SHADOW CORRUPTION HAZARD MANAGER
 * Manages void corruption and shadow torment hazards during "The Fallen Tormentor" boss fight
 * Including corrupted ground zones, reality tears, and shadow barriers
 * THEMATIC: Purple void corruption effects with shadow energy
 */
import * as THREE from 'three';

export class ArenaHazardManager {
    constructor(scene, dungeonHandler) {
        this.scene = scene;
        this.dungeonHandler = dungeonHandler;
        this.activeCorruptionZones = [];
        this.shadowBarriers = [];
        this.originalArenaSize = 20; // Assume 20x20 arena
        this.currentArenaSize = this.originalArenaSize;
        this.shrinkingActive = false;
        this.shrinkRate = 0.05; // Units per second
        
        // CRITICAL: Timeout tracking for proper cleanup
        this._activeTimeouts = [];
        this.bossEntity = null; // Reference to boss for state checking
        
        // SHADOW CORRUPTION: Materials for void hazards
        this.corruptionZoneMaterial = new THREE.MeshBasicMaterial({
            color: 0x4a1a5a, // Deep purple corruption
            transparent: true,
            opacity: 0.7,
            emissive: 0x2d1b3d, // Dark void glow
            emissiveIntensity: 0.8,
            side: THREE.DoubleSide
        });
        
        this.shadowBarrierMaterial = new THREE.MeshBasicMaterial({
            color: 0x2d1b3d, // Dark void barrier
            transparent: true,
            opacity: 0.85,
            emissive: 0x4a1a5a, // Purple shadow glow
            emissiveIntensity: 1.2
        });
        
        this.voidWarningMaterial = new THREE.MeshBasicMaterial({
            color: 0x6a2c6a, // Purple void warning
            transparent: true,
            opacity: 0.5,
            emissive: 0x4a1a5a, // Shadow energy buildup
            emissiveIntensity: 0.6,
            side: THREE.DoubleSide
        });
        
        console.log('[ArenaHazardManager] Initialized');
    }
    
    /**
     * Set boss entity reference for state checking
     */
    setBossEntity(bossEntity) {
        this.bossEntity = bossEntity;
        console.log('[ArenaHazardManager] Boss entity reference set for state checking');
    }
    
    /**
     * Tracked setTimeout to ensure proper cleanup
     * CRITICAL: All timeouts must use this method for proper boss death cleanup
     */
    _setTimeout(callback, delay) {
        const timeoutId = setTimeout(() => {
            // Remove from tracking array when executed
            const index = this._activeTimeouts.indexOf(timeoutId);
            if (index > -1) {
                this._activeTimeouts.splice(index, 1);
            }
            
            // CRITICAL: Check if boss is still alive before executing callback
            if (!this.bossEntity || !this.bossEntity.parent || this.bossEntity.userData.health <= 0) {
                console.log('[ArenaHazardManager] 🚫 TIMEOUT BLOCKED: Boss is dead, skipping hazard execution');
                return;
            }
            
            callback();
        }, delay);
        
        this._activeTimeouts.push(timeoutId);
        return timeoutId;
    }
    
    /**
     * Create corrupted ground zone that drains life force on contact
     * THEMATIC: Purple void corruption spreading across the ground
     */
    createCorruptionZone(position, radius = 2.5, warningTime = 2000, damageTime = 3000, damage = 1) {
        console.log('[ArenaHazardManager] 💀 Creating void corruption zone at', position);
        
        // Create void energy warning first
        const warningGeometry = new THREE.CircleGeometry(radius, 16);
        const voidWarning = new THREE.Mesh(warningGeometry, this.voidWarningMaterial);
        voidWarning.rotation.x = -Math.PI / 2; // Lay flat on ground
        voidWarning.position.copy(position);
        voidWarning.position.y = 0.01; // Slightly above ground
        
        this.scene.add(voidWarning);
        
        const hazard = {
            voidWarning: voidWarning,
            corruptionZone: null,
            position: position.clone(),
            radius: radius,
            damage: damage,
            startTime: performance.now(),
            warningTime: warningTime,
            damageTime: damageTime,
            active: true,
            isVoidWarning: true,
            isCorrupting: false,
            hasDealtDamage: false,
            pulsePhase: 0
        };
        
        this.activeCorruptionZones.push(hazard);
        
        // Transition to corruption zone after void warning
        this._setTimeout(() => {
            if (!hazard.active) return;
            
            // Remove void warning
            this.scene.remove(voidWarning);
            
            // Create actual corruption zone
            const corruptionGeometry = new THREE.CircleGeometry(radius, 16);
            const corruptionZone = new THREE.Mesh(corruptionGeometry, this.corruptionZoneMaterial);
            corruptionZone.rotation.x = -Math.PI / 2;
            corruptionZone.position.copy(position);
            corruptionZone.position.y = 0.02; // Slightly above warning level
            
            this.scene.add(corruptionZone);
            
            hazard.corruptionZone = corruptionZone;
            hazard.isVoidWarning = false;
            hazard.isCorrupting = true;
            
            // Remove corruption zone after damage time
            this._setTimeout(() => {
                if (hazard.active) {
                    hazard.active = false;
                    this.scene.remove(corruptionZone);
                }
            }, damageTime);
            
        }, warningTime);
        
        return hazard;
    }
    
    /**
     * Create multiple random corruption zones across the arena
     * THEMATIC: Void corruption spreading randomly through the battlefield
     */
    createRandomCorruptionZones(count, arenaRadius = 10) {
        console.log(`[ArenaHazardManager] 💀 Creating ${count} random void corruption zones`);
        
        for (let i = 0; i < count; i++) {
            // Random position within arena
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * arenaRadius * 0.8; // Keep away from edges
            
            const position = new THREE.Vector3(
                Math.cos(angle) * distance,
                0,
                Math.sin(angle) * distance
            );
            
            // Stagger corruption spread times
            this._setTimeout(() => {
                this.createCorruptionZone(position, 2.0 + Math.random() * 1.5, 2000, 4000, 1);
            }, i * 800);
        }
    }
    
    /**
     * Start reality collapse - shadow barriers closing in
     * THEMATIC: The void is collapsing the arena, forcing the player into closer combat
     * ENHANCED: 10-second duration limit with automatic cleanup
     */
    startRealityCollapse(targetSize = 12, shrinkRate = 0.03) {
        // FIXED: Only allow one active barrier set at a time
        if (this.shrinkingActive) {
            console.log('[ArenaHazardManager] 🚫 Reality collapse already active - skipping');
            return;
        }
        
        console.log(`[ArenaHazardManager] 🌀 Starting 10-second reality collapse from ${this.currentArenaSize} to ${targetSize}`);
        this.shrinkingActive = true;
        this.shrinkRate = shrinkRate;
        this.targetArenaSize = targetSize;
        this.realityCollapseStartTime = performance.now();
        
        // Create visible shadow barriers
        this.createShadowBarriers();
        
        // FIXED: Auto-cleanup after 10 seconds
        this._setTimeout(() => {
            console.log('[ArenaHazardManager] ⏰ 10-second reality collapse completed - barriers vanishing');
            this.endRealityCollapse();
        }, 10000);
    }
    
    /**
     * Create visible shadow barriers around the arena
     * THEMATIC: Purple void energy barriers that damage on contact
     */
    createShadowBarriers() {
        const barrierHeight = 4;
        const barrierThickness = 0.5;
        
        // Create four shadow barriers (north, south, east, west)
        const barrierPositions = [
            { x: 0, z: this.currentArenaSize / 2, rotY: 0 },      // North
            { x: 0, z: -this.currentArenaSize / 2, rotY: 0 },     // South  
            { x: this.currentArenaSize / 2, z: 0, rotY: Math.PI / 2 }, // East
            { x: -this.currentArenaSize / 2, z: 0, rotY: Math.PI / 2 } // West
        ];
        
        barrierPositions.forEach((pos, index) => {
            const barrierGeometry = new THREE.BoxGeometry(this.currentArenaSize, barrierHeight, barrierThickness);
            const shadowBarrier = new THREE.Mesh(barrierGeometry, this.shadowBarrierMaterial);
            
            shadowBarrier.position.set(pos.x, barrierHeight / 2, pos.z);
            shadowBarrier.rotation.y = pos.rotY;
            
            // Add void energy userData for identification
            shadowBarrier.userData = {
                isShadowBarrier: true,
                voidEnergy: true,
                dealsDamage: true
            };
            
            this.scene.add(shadowBarrier);
            this.shadowBarriers.push(shadowBarrier);
        });
    }
    
    /**
     * End reality collapse - remove shadow barriers
     * THEMATIC: The void energy dissipates and barriers vanish
     */
    endRealityCollapse() {
        if (!this.shrinkingActive) return;
        
        console.log('[ArenaHazardManager] 🌀 Ending reality collapse - shadow barriers dissipating');
        
        // Remove all shadow barriers
        this.shadowBarriers.forEach(barrier => {
            if (barrier.parent) {
                this.scene.remove(barrier);
            }
        });
        
        // Reset state
        this.shadowBarriers = [];
        this.shrinkingActive = false;
        this.currentArenaSize = this.originalArenaSize;
        
        console.log('[ArenaHazardManager] ✅ Reality collapse ended - arena restored to original size');
    }
    
    /**
     * Update shadow barrier positions during reality collapse
     * THEMATIC: Void barriers closing in with intensifying energy
     */
    updateShadowBarriers() {
        if (this.shadowBarriers.length !== 4) return;
        
        const halfSize = this.currentArenaSize / 2;
        
        // Update shadow barrier positions
        this.shadowBarriers[0].position.z = halfSize;   // North
        this.shadowBarriers[1].position.z = -halfSize;  // South
        this.shadowBarriers[2].position.x = halfSize;   // East
        this.shadowBarriers[3].position.x = -halfSize;  // West
        
        // Update barrier geometry and intensify glow as they close in
        const collapseProgress = 1.0 - (this.currentArenaSize / this.originalArenaSize);
        this.shadowBarriers.forEach(barrier => {
            barrier.scale.x = this.currentArenaSize / this.originalArenaSize;
            // Intensify void energy as barriers close in
            barrier.material.emissiveIntensity = 1.2 + collapseProgress * 0.8;
        });
    }
    
    /**
     * Check if player touches shadow barriers
     * THEMATIC: Void energy barriers deal high damage and push back
     */
    checkShadowBarrierCollision() {
        if (!this.dungeonHandler.player) return;
        
        const playerPos = this.dungeonHandler.player.position;
        const halfSize = this.currentArenaSize / 2;
        
        // Check if player touches shadow barriers
        if (Math.abs(playerPos.x) > halfSize || Math.abs(playerPos.z) > halfSize) {
            // Player touched void barrier - apply corruption damage and push back
            console.log('[ArenaHazardManager] 💀 Player touched shadow barrier - void corruption!');
            
            if (this.dungeonHandler.damagePlayer) {
                this.dungeonHandler.damagePlayer(2); // High damage for void energy contact
            }
            
            // Push player back towards center
            const toCenterX = playerPos.x > 0 ? -1 : 1;
            const toCenterZ = playerPos.z > 0 ? -1 : 1;
            
            playerPos.x = Math.max(-halfSize + 0.5, Math.min(halfSize - 0.5, playerPos.x + toCenterX));
            playerPos.z = Math.max(-halfSize + 0.5, Math.min(halfSize - 0.5, playerPos.z + toCenterZ));
        }
    }
    
    /**
     * Update all shadow corruption hazards
     * THEMATIC: Void energy pulsing with malevolent corruption
     */
    update(deltaTime) {
        const currentTime = performance.now();
        
        // Update corruption zones
        this.activeCorruptionZones.forEach(hazard => {
            if (!hazard.active) return;
            
            // Update void energy pulsing effect
            hazard.pulsePhase += deltaTime * 0.008;
            
            if (hazard.isVoidWarning && hazard.voidWarning) {
                // Void warning pulse (intensifies as corruption builds)
                const elapsed = currentTime - hazard.startTime;
                const progress = elapsed / hazard.warningTime;
                const voidIntensity = Math.min(1, progress * 2);
                const pulse = Math.sin(hazard.pulsePhase * (1 + voidIntensity * 3)) * 0.3 + 0.7;
                hazard.voidWarning.material.opacity = pulse * 0.6;
                // Intensify emissive as corruption builds
                hazard.voidWarning.material.emissiveIntensity = 0.6 + voidIntensity * 0.4;
            }
            
            if (hazard.isCorrupting && hazard.corruptionZone) {
                // Corruption zone pulse with malevolent energy
                const pulse = Math.sin(hazard.pulsePhase * 2) * 0.2 + 0.8;
                hazard.corruptionZone.material.opacity = pulse * 0.8;
                hazard.corruptionZone.material.emissiveIntensity = 0.8 + pulse * 0.4;
                
                // Check collision with player
                this.checkCorruptionZoneCollision(hazard);
            }
        });
        
        // Clean up inactive corruption zones
        this.activeCorruptionZones = this.activeCorruptionZones.filter(hazard => hazard.active);
        
        // Update reality collapse
        if (this.shrinkingActive && this.currentArenaSize > this.targetArenaSize) {
            this.currentArenaSize -= this.shrinkRate * deltaTime;
            this.currentArenaSize = Math.max(this.targetArenaSize, this.currentArenaSize);
            
            this.updateShadowBarriers();
            this.checkShadowBarrierCollision();
            
            if (this.currentArenaSize <= this.targetArenaSize) {
                console.log('[ArenaHazardManager] 🌀 Reality collapse complete - void has consumed the arena');
                this.shrinkingActive = false;
            }
        }
    }
    
    /**
     * Check if player is in corruption zone
     * THEMATIC: Void corruption drains life force from those who enter
     */
    checkCorruptionZoneCollision(hazard) {
        if (!this.dungeonHandler.player || hazard.hasDealtDamage) return;
        
        const playerPos = this.dungeonHandler.player.position;
        const distance = playerPos.distanceTo(hazard.position);
        
        if (distance <= hazard.radius) {
            // Player is being corrupted by void energy
            console.log('[ArenaHazardManager] 💀 Player caught in void corruption - life force draining!');
            hazard.hasDealtDamage = true;
            
            if (this.dungeonHandler.damagePlayer) {
                this.dungeonHandler.damagePlayer(hazard.damage);
            }
            
            // Enhanced visual feedback - corruption effect
            if (this.dungeonHandler.camera) {
                this.addCorruptionShake(0.3, 400);
            }
        }
    }
    
    /**
     * Add corruption camera shake effect
     * THEMATIC: Void energy creates reality distortion
     */
    addCorruptionShake(intensity, duration) {
        if (!this.dungeonHandler.camera) return;
        
        const camera = this.dungeonHandler.camera;
        const originalPosition = camera.position.clone();
        const shakeStart = performance.now();
        
        const shakeEffect = () => {
            const elapsed = performance.now() - shakeStart;
            if (elapsed >= duration) {
                camera.position.copy(originalPosition);
                return;
            }
            
            const progress = elapsed / duration;
            const currentIntensity = intensity * (1 - progress);
            
            const shakeX = (Math.random() - 0.5) * currentIntensity;
            const shakeY = (Math.random() - 0.5) * currentIntensity;
            const shakeZ = (Math.random() - 0.5) * currentIntensity;
            
            camera.position.copy(originalPosition);
            camera.position.x += shakeX;
            camera.position.y += shakeY;
            camera.position.z += shakeZ;
            
            requestAnimationFrame(shakeEffect);
        };
        
        shakeEffect();
    }
    
    /**
     * Clean up all shadow corruption hazards
     * THEMATIC: Banish the void corruption and restore reality
     */
    cleanup() {
        console.log(`[ArenaHazardManager] 🧹 CLEANUP: Clearing ${this._activeTimeouts.length} pending timeouts`);
        
        // CRITICAL: Clear all pending timeouts first
        if (this._activeTimeouts) {
            this._activeTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            this._activeTimeouts = [];
        }
        
        // End any active reality collapse
        if (this.shrinkingActive) {
            this.endRealityCollapse();
        }
        
        // Remove all corruption zones
        this.activeCorruptionZones.forEach(hazard => {
            if (hazard.voidWarning && hazard.voidWarning.parent) {
                this.scene.remove(hazard.voidWarning);
            }
            if (hazard.corruptionZone && hazard.corruptionZone.parent) {
                this.scene.remove(hazard.corruptionZone);
            }
        });
        
        // Clear arrays
        this.activeCorruptionZones = [];
        this.bossEntity = null; // Clear boss reference
        
        console.log('[ArenaHazardManager] 🌀 Cleansed all void corruption - reality restored!');
    }
}