/**
 * <PERSON><PERSON><PERSON> Boss Controller
 * Specialized boss controller for "Nairabos The Fallen Tormentor" 
 * Implements 3-phase bullet hell combat with shields, speech bubbles, strategic patterns, and music sync
 */
import * as THREE from 'three';
import { NairabosAttackPatterns } from './NairabosAttackPatterns.js';
import { NairabosMusicSync } from './NairabosMusicSync.js';
import { ShaderOptimizer } from '../../utils/ShaderOptimizer.js';

export class NairabosBossController {
    constructor(enemy, dungeonHandler, audioManager) {
        this.enemy = enemy;
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;
        
        // Boss state
        this.maxHealth = enemy.userData.health;
        this.currentHealth = enemy.userData.health;
        this.currentPhase = 1;
        this.isActive = false;
        this.isShielded = false;
        this.lastPhaseChangeTime = 0;
        
        // Phase thresholds (HP percentages)
        this.phaseThresholds = {
            phase1: 1.0,    // 100% - 70%
            phase2: 0.7,    // 70% - 30%  
            phase3: 0.3,    // 30% - 0%
            desperation: 0.1 // 10% - Final desperation mode
        };
        
        // Desperation mode state
        this.isDesperationMode = false;
        this.desperationStartTime = 0;
        
        // Attack pattern system
        console.log('[NairabosBossController] Initializing attack patterns...');
        this.attackPatterns = new NairabosAttackPatterns(enemy, dungeonHandler, dungeonHandler.player);
        
        // Music sync system
        console.log('[NairabosBossController] Initializing music sync...');
        this.musicSync = new NairabosMusicSync(audioManager, this);
        
        // Fallback timing for when music sync isn't available
        this.lastAttackTime = 0;
        this.fallbackCooldowns = {
            1: 3.0,  // Phase 1: 3.0 seconds between attacks (more forgiving)
            2: 2.5,  // Phase 2: 2.5 seconds between attacks (increased from 2.0)
            3: 2.0   // Phase 3: 2.0 seconds between attacks (increased from 1.4)
        };
        
        // Shield system
        this.shieldMesh = null;
        this.shieldDuration = 5.0; // 5 seconds of invulnerability
        this.shieldCooldown = 15.0; // 15 seconds between shields
        this.lastShieldTime = 0;
        this.shieldStartTime = 0;
        
        // Speech bubble system
        this.speechBubble = null;
        this.speechTexts = {
            phase1To2: "You are weak...",
            phase2To3: "Be ready for pain!",
            lowHealth: "I will not fall!",
            shieldActivate: "Face my wrath!",
            defeat: "Impossible..."
        };
        
        // Animation states
        this.animationStates = {
            IDLE: 'shadow_floating',
            ATTACKING: 'shadow_striking', 
            SHIELDED: 'shadow_floating', // Will add glow effect
            CASTING: 'shadow_striking'
        };
        
        // Performance tracking
        this.lastUpdateTime = 0;
        this.performanceMode = 'high'; // Will be set by performance detection
        
        // SIGNATURE SHADOW AURA SYSTEM
        this.shadowAura = null;
        this.voidCorruptionField = null;
        this.realityDistortionActive = false;
        this.auraIntensity = 0.5;
        
        // CRITICAL: Shader optimization for uniform overflow
        this.shaderOptimizer = null;
        if (dungeonHandler.renderer && dungeonHandler.scene) {
            this.shaderOptimizer = new ShaderOptimizer(dungeonHandler.renderer, dungeonHandler.scene);
        }
        
        console.log('[NairabosBossController] Initialized for "Nairabos The Fallen Tormentor"');
    }
    
    /**
     * Start the boss fight
     */
    start() {
        if (this.isActive) return;
        
        // CRITICAL: Apply shader optimization before boss fight starts
        if (this.shaderOptimizer) {
            const usage = this.shaderOptimizer.getUniformUsage();
            console.log(`[NairabosBossController] Pre-optimization uniform usage: ${usage.percentage.toFixed(1)}% (${usage.estimated}/${usage.maxFragment})`);
            
            if (usage.percentage > 70) {
                console.log('[NairabosBossController] 🚨 High uniform usage detected, applying optimization');
                this.shaderOptimizer.optimizeForBossFight();
            }
        }
        
        this.isActive = true;
        this.currentPhase = 1;
        this.lastPhaseChangeTime = performance.now();
        this.lastAttackTime = performance.now();
        
        // Show initial speech bubble
        this.showSpeechBubble("Mortal... you dare disturb my slumber?", 3000);
        
        // Start music sync and attack patterns after initial speech
        setTimeout(() => {
            this.lastAttackTime = performance.now(); // Reset timer after speech
            
            // Start music synchronization
            if (this.musicSync) {
                this.musicSync.start();
                console.log('[NairabosBossController] Music sync activated');
            }
            
            // SIGNATURE EFFECT: Create persistent shadow aura
            this.createShadowAura();
        }, 3500);
        
        console.log('[NairabosBossController] Boss fight started - Phase 1');
    }
    
    /**
     * Update boss controller each frame
     */
    update(deltaTime) {
        if (!this.isActive) return;
        
        const currentTime = performance.now();
        this.lastUpdateTime = currentTime;
        
        // Update health from AI brain
        if (this.enemy.userData.aiBrain && this.enemy.userData.aiBrain.currentHealth !== undefined) {
            this.currentHealth = this.enemy.userData.aiBrain.currentHealth;
        }
        
        // Check for phase transitions
        this.checkPhaseTransitions();
        
        // Update shield system
        this.updateShield(deltaTime);
        
        // Update attack patterns
        this.updateAttackPatterns(deltaTime);
        
        // Update advanced bullet groups and minions
        if (this.attackPatterns && this.attackPatterns.update) {
            this.attackPatterns.update(deltaTime);
            
            // Update wind-up indicators (create visual effects)
            this.updateWindUpIndicators(deltaTime);
        }
        
        // Update speech bubbles
        this.updateSpeechBubbles(deltaTime);
        
        // Update animations based on state
        this.updateAnimations();
        
        // Update signature shadow aura
        this.updateShadowAura(deltaTime);
    }
    
    /**
     * Check if we need to transition to a new phase
     */
    checkPhaseTransitions() {
        const healthPercent = this.currentHealth / this.maxHealth;
        let newPhase = this.currentPhase;
        
        // Check for desperation mode (final 10% health)
        if (healthPercent <= this.phaseThresholds.desperation && !this.isDesperationMode) {
            this.activateDesperationMode();
        }
        
        if (healthPercent <= this.phaseThresholds.phase3 && this.currentPhase < 3) {
            newPhase = 3;
        } else if (healthPercent <= this.phaseThresholds.phase2 && this.currentPhase < 2) {
            newPhase = 2;
        }
        
        if (newPhase !== this.currentPhase) {
            this.transitionToPhase(newPhase);
        }
    }
    
    /**
     * Activate desperation mode for final 10% health
     */
    activateDesperationMode() {
        if (this.isDesperationMode) return;
        
        console.log('[NairabosBossController] DESPERATION MODE ACTIVATED! Final 10% health reached!');
        
        this.isDesperationMode = true;
        this.desperationStartTime = performance.now();
        
        // Dramatic speech bubble
        this.showSpeechBubble("IMPOSSIBLE! I WILL NOT BE DEFEATED!", 3000);
        
        // Intense screen shake
        if (this.dungeonHandler.camera) {
            this.addIntenseScreenShake(1.0, 2000);
        }
        
        // Red tint effect - add to scene userData for main.js to pick up
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.userData.desperationMode = {
                active: true,
                startTime: this.desperationStartTime,
                intensity: 0.3
            };
        }
        
        // Boss visual enhancement - make him glow red
        if (this.enemy) {
            this.enemy.traverse(child => {
                if (child.isMesh && child.material) {
                    child.material.emissive.setHex(0x440000); // Dark red glow
                    child.material.emissiveIntensity = 0.8;
                }
            });
        }
        
        // Update attack patterns for desperation
        if (this.attackPatterns) {
            this.attackPatterns.setDesperationMode(true);
        }
        
        // Trigger continuous desperation animation
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('desperation_mode');
        }
        
        // PERFORMANCE: Only slightly reduce cooldowns in desperation (1.2x speed instead of 1.5x)
        this.fallbackCooldowns[1] *= 0.83; // ~2.5s
        this.fallbackCooldowns[2] *= 0.83; // ~2.1s
        this.fallbackCooldowns[3] *= 0.83; // ~1.7s
        
        console.log('[NairabosBossController] Desperation mechanics: 1.5x speed, 1.5x density, visual effects active');
    }
    
    /**
     * Add intense screen shake for desperation mode
     */
    addIntenseScreenShake(intensity, duration) {
        if (!this.dungeonHandler.camera) return;
        
        const camera = this.dungeonHandler.camera;
        const originalPosition = camera.position.clone();
        const shakeStart = performance.now();
        
        const shakeEffect = () => {
            const elapsed = performance.now() - shakeStart;
            if (elapsed >= duration) {
                camera.position.copy(originalPosition);
                return;
            }
            
            const progress = elapsed / duration;
            const currentIntensity = intensity * (1 - progress * 0.5); // Gradual fade
            
            const shakeX = (Math.random() - 0.5) * currentIntensity;
            const shakeY = (Math.random() - 0.5) * currentIntensity;
            const shakeZ = (Math.random() - 0.5) * currentIntensity;
            
            camera.position.copy(originalPosition);
            camera.position.x += shakeX;
            camera.position.y += shakeY;
            camera.position.z += shakeZ;
            
            requestAnimationFrame(shakeEffect);
        };
        
        shakeEffect();
    }
    
    /**
     * Transition to a new combat phase
     */
    transitionToPhase(newPhase) {
        console.log(`[NairabosBossController] Transitioning from Phase ${this.currentPhase} to Phase ${newPhase}`);
        
        const oldPhase = this.currentPhase;
        this.currentPhase = newPhase;
        this.lastPhaseChangeTime = performance.now();
        
        // Activate shield during phase transition
        this.activateShield();
        
        // Show phase transition speech
        if (newPhase === 2) {
            this.showSpeechBubble(this.speechTexts.phase1To2, 2500);
        } else if (newPhase === 3) {
            this.showSpeechBubble(this.speechTexts.phase2To3, 2500);
        }
        
        // Update pattern cooldowns based on phase (no method needed, just log the transition)
        
        // Update music sync settings for new phase
        if (this.musicSync) {
            this.musicSync.updatePhaseSettings(newPhase);
        }
        
        // Visual effects for phase change
        this.triggerPhaseChangeEffects();
    }
    
    /**
     * Update attack pattern timing - music sync with fallback system
     */
    updateAttackPatterns(deltaTime) {
        if (this.isShielded) return; // No attacks during shield
        
        // Check if music sync is active and working
        const musicSyncStatus = this.musicSync ? this.musicSync.getStatus() : null;
        const usingMusicSync = musicSyncStatus && musicSyncStatus.isActive && musicSyncStatus.hasMusicAnalyzer;
        
        if (this.isActive && performance.now() % 5000 < 100) { // Log every 5 seconds
            console.log('[NairabosBossController] Music sync status:', {
                hasMusicSync: !!this.musicSync,
                status: musicSyncStatus,
                usingMusicSync: usingMusicSync,
                isShielded: this.isShielded,
                currentPhase: this.currentPhase
            });
        }
        
        if (!usingMusicSync) {
            // Fallback to timer-based attacks when music sync isn't working
            const currentTime = performance.now();
            const timeSinceLastAttack = (currentTime - this.lastAttackTime) / 1000; // Convert to seconds
            const attackCooldown = this.fallbackCooldowns[this.currentPhase] || 2.0;
            
            if (timeSinceLastAttack >= attackCooldown) {
                console.log(`[NairabosBossController] Fallback attack triggered! Cooldown: ${attackCooldown}s, Time since last: ${timeSinceLastAttack.toFixed(2)}s`);
                this.triggerAttackPattern();
                this.lastAttackTime = currentTime;
            }
        }
        // Note: When music sync is active, attacks are triggered by beat detection callbacks
    }
    
    /**
     * Trigger an attack pattern based on current phase
     */
    triggerAttackPattern(musicIntensity = null) {
        if (!this.attackPatterns) {
            console.error('[NairabosBossController] Attack patterns not available');
            return;
        }
        
        // Calculate intensity based on health percentage and music input
        const healthPercent = this.currentHealth / this.maxHealth;
        const baseIntensity = {
            1: 0.36, // Phase 1: Easier (10% reduction from 0.4)
            2: 0.7,  // Phase 2: Medium (unchanged)
            3: 1.0   // Phase 3: Hard (unchanged)
        };
        
        // Use music intensity if provided, otherwise calculate from health
        let intensity;
        if (musicIntensity !== null) {
            // Music-driven intensity with phase scaling
            intensity = Math.min(1.0, musicIntensity * baseIntensity[this.currentPhase]);
            console.log(`[NairabosBossController] Music-driven attack (Music: ${musicIntensity.toFixed(2)}, Final: ${intensity.toFixed(2)})`);
        } else {
            // Health-driven intensity (fallback)
            const phaseMultiplier = 1.0 + (1.0 - healthPercent) * 0.3;
            intensity = Math.min(1.0, baseIntensity[this.currentPhase] * phaseMultiplier);
            console.log(`[NairabosBossController] Health-driven attack (Phase ${this.currentPhase}, Intensity ${intensity.toFixed(2)})`);
        }
        
        // Apply desperation mode multiplier (reduced to 1.2x bullet density for performance)
        if (this.isDesperationMode) {
            intensity = Math.min(1.3, intensity * 1.2); // REDUCED: Was 1.5x, now 1.2x
            console.log(`[NairabosBossController] DESPERATION MODE: Enhanced intensity ${intensity.toFixed(2)}`);
        }
        
        try {
            this.attackPatterns.triggerPattern(this.currentPhase, intensity, this.isDesperationMode);
        } catch (error) {
            console.error(`[NairabosBossController] Failed to trigger attack pattern:`, error);
        }
    }
    
    /**
     * Activate protective shield
     */
    activateShield() {
        if (this.isShielded) return;
        
        const currentTime = performance.now();
        if (currentTime - this.lastShieldTime < this.shieldCooldown * 1000) return;
        
        this.isShielded = true;
        this.shieldStartTime = currentTime;
        this.lastShieldTime = currentTime;
        
        // Create shield visual
        this.createShieldMesh();
        
        // Show shield speech
        this.showSpeechBubble(this.speechTexts.shieldActivate, 2000);
        
        console.log('[NairabosBossController] Shield activated');
    }
    
    /**
     * Create the visual shield around Nairabos
     */
    createShieldMesh() {
        if (this.shieldMesh) {
            this.dungeonHandler.scene.remove(this.shieldMesh);
        }
        
        // Create a sphere around Nairabos
        const shieldGeometry = new THREE.SphereGeometry(3.0, 16, 12);
        const shieldMaterial = new THREE.MeshBasicMaterial({
            color: 0xff0000,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide
        });
        
        this.shieldMesh = new THREE.Mesh(shieldGeometry, shieldMaterial);
        this.shieldMesh.position.copy(this.enemy.position);
        
        // Add pulsing animation
        this.shieldMesh.userData = {
            startTime: performance.now(),
            baseOpacity: 0.3
        };
        
        this.dungeonHandler.scene.add(this.shieldMesh);
    }
    
    /**
     * Update shield system
     */
    updateShield(deltaTime) {
        if (!this.isShielded) return;
        
        const currentTime = performance.now();
        const shieldDuration = currentTime - this.shieldStartTime;
        
        // Update shield position to follow Nairabos
        if (this.shieldMesh) {
            this.shieldMesh.position.copy(this.enemy.position);
            this.shieldMesh.position.y += 2.0; // Center on Nairabos
            
            // Pulsing animation
            const pulseSpeed = 3.0;
            const pulse = Math.sin(currentTime * 0.005 * pulseSpeed);
            this.shieldMesh.material.opacity = 0.2 + pulse * 0.1;
            this.shieldMesh.rotation.y += deltaTime * 2.0;
        }
        
        // Remove shield after duration
        if (shieldDuration > this.shieldDuration * 1000) {
            this.deactivateShield();
        }
    }
    
    /**
     * Deactivate shield
     */
    deactivateShield() {
        this.isShielded = false;
        
        if (this.shieldMesh) {
            this.dungeonHandler.scene.remove(this.shieldMesh);
            this.shieldMesh = null;
        }
        
        console.log('[NairabosBossController] Shield deactivated');
    }
    
    /**
     * Show speech bubble with text
     */
    showSpeechBubble(text, duration = 3000) {
        // Remove existing speech bubble
        if (this.speechBubble) {
            this.removeSpeechBubble();
        }
        
        // Create speech bubble container
        this.speechBubble = document.createElement('div');
        this.speechBubble.className = 'boss-speech-bubble';
        this.speechBubble.innerHTML = `
            <div class="boss-speech-content">
                <div class="boss-speech-text">${text}</div>
            </div>
        `;
        
        // Add styles
        this.speechBubble.style.cssText = `
            position: absolute;
            z-index: 2000;
            pointer-events: none;
            opacity: 0;
            transform: translateX(-50%);
            transition: opacity 0.5s ease-in-out;
        `;
        
        // Speech bubble content styling (fishing rod style)
        const speechContent = this.speechBubble.querySelector('.boss-speech-content');
        speechContent.style.cssText = `
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            padding: 10px 15px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 1px;
        `;
        
        document.body.appendChild(this.speechBubble);
        
        // Position above Nairabos
        this.updateSpeechBubblePosition();
        
        // Fade in
        setTimeout(() => {
            if (this.speechBubble) {
                this.speechBubble.style.opacity = '1';
            }
        }, 100);
        
        // Auto-remove after duration
        setTimeout(() => {
            this.removeSpeechBubble();
        }, duration);
        
        console.log(`[NairabosBossController] Speech: "${text}"`);
    }
    
    /**
     * Update speech bubble position to follow Nairabos
     */
    updateSpeechBubblePosition() {
        if (!this.speechBubble || !this.enemy) return;
        
        // Get Nairabos world position
        const enemyPosition = this.enemy.position.clone();
        enemyPosition.y += 6.0; // Position above Nairabos head
        
        // Project to screen coordinates
        const vector = enemyPosition.clone();
        vector.project(this.dungeonHandler.camera);
        
        const screenX = (vector.x * 0.5 + 0.5) * window.innerWidth;
        const screenY = (vector.y * -0.5 + 0.5) * window.innerHeight;
        
        this.speechBubble.style.left = `${screenX}px`;
        this.speechBubble.style.top = `${screenY}px`;
    }
    
    /**
     * Update speech bubbles
     */
    updateSpeechBubbles(deltaTime) {
        if (this.speechBubble) {
            this.updateSpeechBubblePosition();
        }
    }
    
    /**
     * Remove speech bubble
     */
    removeSpeechBubble() {
        if (this.speechBubble) {
            this.speechBubble.style.opacity = '0';
            setTimeout(() => {
                if (this.speechBubble && this.speechBubble.parentNode) {
                    this.speechBubble.parentNode.removeChild(this.speechBubble);
                }
                this.speechBubble = null;
            }, 500);
        }
    }
    
    /**
     * Update wind-up indicators with visual effects
     */
    updateWindUpIndicators(deltaTime) {
        if (!this.attackPatterns || !this.attackPatterns.windUpIndicators) return;
        
        this.attackPatterns.windUpIndicators.forEach(indicator => {
            if (!indicator.active) return;
            
            // Create simple particle effect for wind-up
            const elapsed = performance.now() - indicator.startTime;
            const progress = elapsed / indicator.duration;
            
            if (progress < 1.0) {
                // Create energy buildup effect around boss
                const intensity = Math.sin(elapsed * 0.01) * (1.0 - progress);
                
                // Screen shake for major attacks
                if (indicator.message.includes('dark energy') || indicator.message.includes('shadow servants')) {
                    if (this.dungeonHandler.camera && Math.random() < 0.1) {
                        const shakeIntensity = intensity * 0.1;
                        this.dungeonHandler.camera.position.x += (Math.random() - 0.5) * shakeIntensity;
                        this.dungeonHandler.camera.position.z += (Math.random() - 0.5) * shakeIntensity;
                    }
                }
                
                // Boss glow effect during wind-up
                if (this.enemy) {
                    this.enemy.traverse(child => {
                        if (child.isMesh && child.material) {
                            child.material.emissiveIntensity = intensity * 0.3;
                        }
                    });
                }
            }
        });
    }
    
    /**
     * Update animations based on current state
     */
    updateAnimations() {
        if (!this.enemy.userData.animationHandler) {
            console.warn('[NairabosBossController] No animation handler found');
            return;
        }
        
        let targetState = this.animationStates.IDLE;
        
        if (this.isShielded) {
            targetState = this.animationStates.SHIELDED;
        } else if (performance.now() - this.lastAttackTime < 1000) {
            targetState = this.animationStates.ATTACKING;
        }
        
        // CRITICAL FIX: Call update method instead of setState
        try {
            const deltaTime = 0.016; // 60fps fallback
            // Force walking animation for boss movement
            this.enemy.userData.animationHandler.update(deltaTime, 'CHASING');
            console.log(`[NairabosBossController] Forcing CHASING animation state for movement`);
        } catch (error) {
            console.error('[NairabosBossController] Animation update error:', error);
        }
    }
    
    /**
     * ENHANCED PHASE TRANSITION VISUAL OVERHAUL
     * THEMATIC: Reality tears, void eruption, boss transformation, environmental corruption
     */
    triggerPhaseChangeEffects() {
        console.log('[NairabosBossController] 🌀💀 PHASE TRANSITION - REALITY TEARS ASUNDER!');
        
        // EFFECT 1: Reality tears opening around boss
        this.createRealityTears();
        
        // EFFECT 2: Void energy erupting from ground
        this.createVoidEruption();
        
        // EFFECT 3: All projectiles freeze momentarily
        this.freezeProjectilesEffect();
        
        // EFFECT 4: Boss model enhanced corruption glow
        this.enhanceBossCorruption();
        
        // EFFECT 5: Screen reality distortion effect
        this.createRealityDistortion();
        
        // EFFECT 6: Environmental corruption spread
        this.spreadEnvironmentalCorruption();
        
        // EFFECT 7: Intensify shadow aura
        if (this.shadowAura) {
            this.intensifyShadowAura(4000); // Extended 4 second intensification
        }
        
        // EFFECT 8: Ultimate phase transition speech
        setTimeout(() => {
            const phaseMessages = {
                2: "💀 'WITNESS MY GROWING POWER! THE VOID HUNGERS!'",
                3: "🌀 'NOW YOU FACE MY TRUE FURY! REALITY BENDS TO MY WILL!'",
                desperation: "💥 'IMPOSSIBLE! I AM ETERNAL! I WILL NOT BE DEFEATED!'"
            };
            const message = phaseMessages[this.currentPhase] || phaseMessages.desperation;
            console.log(`[NairabosBossController] ${message}`);
        }, 1000);
    }
    
    /**
     * Create reality tears around the boss
     */
    createRealityTears() {
        console.log('[NairabosBossController] 🌀 Reality tears opening around The Fallen Tormentor');
        
        // Multiple tears at different positions
        const tearCount = 6;
        for (let i = 0; i < tearCount; i++) {
            const angle = (i / tearCount) * Math.PI * 2;
            const radius = 8 + Math.random() * 4;
            const tearPos = this.enemy.position.clone().add(new THREE.Vector3(
                Math.cos(angle) * radius,
                2 + Math.random() * 3,
                Math.sin(angle) * radius
            ));
            
            // Log tear position for visual system
            console.log(`[NairabosBossController] 🕳️ Reality tear ${i + 1} manifests at:`, tearPos);
        }
        
        // Intense screen shake for reality tearing
        if (this.dungeonHandler.camera) {
            this.addIntenseScreenShake(0.6, 2500);
        }
    }
    
    /**
     * Create void energy erupting from ground
     */
    createVoidEruption() {
        console.log('[NairabosBossController] 💥 Void energy ERUPTS from the corrupted ground!');
        
        // Multiple eruption points in a circle around boss
        const eruptionCount = 8;
        const baseRadius = 6;
        
        for (let i = 0; i < eruptionCount; i++) {
            setTimeout(() => {
                const angle = (i / eruptionCount) * Math.PI * 2;
                const radius = baseRadius + (i % 2) * 2; // Alternating distances
                const eruptionPos = this.enemy.position.clone().add(new THREE.Vector3(
                    Math.cos(angle) * radius,
                    0,
                    Math.sin(angle) * radius
                ));
                
                console.log(`[NairabosBossController] 🌋 Void eruption ${i + 1} at:`, eruptionPos);
                
                // Screen shake for each eruption
                if (this.dungeonHandler.camera) {
                    this.addIntenseScreenShake(0.2, 300);
                }
            }, i * 150); // Stagger eruptions
        }
    }
    
    /**
     * Freeze all projectiles momentarily for dramatic effect
     */
    freezeProjectilesEffect() {
        console.log('[NairabosBossController] ⏸️ TIME FREEZES - all projectiles suspended in void energy!');
        
        // This creates a dramatic pause in the action
        // Visual system would handle actual projectile freezing
        setTimeout(() => {
            console.log('[NairabosBossController] ⏯️ Time resumes - projectiles unleashed with renewed fury!');
        }, 800);
    }
    
    /**
     * Enhanced boss corruption visual effects
     */
    enhanceBossCorruption() {
        console.log('[NairabosBossController] 💜 The Fallen Tormentor radiates INTENSE void corruption!');
        
        if (this.enemy) {
            this.enemy.traverse(child => {
                if (child.isMesh && child.material && child.material.emissive) {
                    // More intense and longer-lasting corruption effect
                    child.material.emissiveIntensity = 1.2; // Brighter
                    child.material.emissive.setHex(0x4a1a5a); // Purple corruption
                    
                    // Gradual fade over 3 seconds
                    const fadeStart = performance.now();
                    const fadeEffect = () => {
                        const elapsed = performance.now() - fadeStart;
                        const progress = elapsed / 3000; // 3 second fade
                        
                        if (progress >= 1.0) {
                            child.material.emissiveIntensity = 0.2; // Keep slight glow
                            return;
                        }
                        
                        const intensity = 1.2 * (1 - progress) + 0.2; // Fade to 0.2
                        child.material.emissiveIntensity = intensity;
                        
                        requestAnimationFrame(fadeEffect);
                    };
                    
                    fadeEffect();
                }
            });
        }
    }
    
    /**
     * Create screen reality distortion effect
     */
    createRealityDistortion() {
        console.log('[NairabosBossController] 🌀 Reality DISTORTS around The Fallen Tormentor!');
        
        // Add distortion data to scene for main.js to pick up
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.userData.phaseTransition = {
                active: true,
                startTime: performance.now(),
                intensity: 0.8,
                distortionType: 'void_corruption',
                duration: 3000
            };
        }
        
        // Extended reality distortion screen shake
        if (this.dungeonHandler.camera) {
            this.addIntenseScreenShake(0.4, 3000);
        }
    }
    
    /**
     * Spread environmental corruption
     */
    spreadEnvironmentalCorruption() {
        console.log('[NairabosBossController] 🟣 CORRUPTION spreads across the battlefield!');
        
        // Trigger environmental effects through attack patterns
        if (this.attackPatterns && this.attackPatterns.hazardManager) {
            // Create corruption zones during phase transition
            this.attackPatterns.hazardManager.createRandomCorruptionZones(3, 12);
        }
        
        // Add purple corruption lighting effect
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.userData.corruptionSpread = {
                active: true,
                startTime: performance.now(),
                intensity: this.currentPhase / 3, // Stronger each phase
                duration: 5000
            };
        }
    }
    
    /**
     * Create signature shadow aura around "The Fallen Tormentor"
     * THEMATIC: Persistent void energy emanating from the boss
     */
    createShadowAura() {
        if (this.shadowAura) return; // Already exists
        
        console.log('[NairabosBossController] 🌟 Creating signature shadow aura');
        
        // Create large sphere around Nairabos for shadow aura
        const auraGeometry = new THREE.SphereGeometry(5.0, 24, 16);
        const auraMaterial = new THREE.MeshBasicMaterial({
            color: 0x2d1b3d, // Dark purple void
            transparent: true,
            opacity: 0.15,
            side: THREE.BackSide, // Render from inside
            emissive: 0x4a1a5a, // Purple shadow energy
            emissiveIntensity: this.auraIntensity
        });
        
        this.shadowAura = new THREE.Mesh(auraGeometry, auraMaterial);
        this.shadowAura.position.copy(this.enemy.position);
        this.shadowAura.userData = {
            isShadowAura: true,
            voidEnergy: true,
            startTime: performance.now()
        };
        
        this.dungeonHandler.scene.add(this.shadowAura);
        
        // Create void corruption field (ground effect)
        const corruptionGeometry = new THREE.CircleGeometry(8.0, 32);
        const corruptionMaterial = new THREE.MeshBasicMaterial({
            color: 0x1a0d1a, // Very dark purple
            transparent: true,
            opacity: 0.3,
            emissive: 0x2d1b3d, // Dark void glow
            emissiveIntensity: 0.4,
            side: THREE.DoubleSide
        });
        
        this.voidCorruptionField = new THREE.Mesh(corruptionGeometry, corruptionMaterial);
        this.voidCorruptionField.rotation.x = -Math.PI / 2; // Lay flat
        this.voidCorruptionField.position.copy(this.enemy.position);
        this.voidCorruptionField.position.y = 0.05; // Slightly above ground
        this.voidCorruptionField.userData = {
            isCorruptionField: true,
            voidEnergy: true
        };
        
        this.dungeonHandler.scene.add(this.voidCorruptionField);
        
        console.log('[NairabosBossController] Shadow aura and corruption field created');
    }
    
    /**
     * Intensify shadow aura for dramatic effect
     */
    intensifyShadowAura(duration = 2000) {
        if (!this.shadowAura) return;
        
        const startTime = performance.now();
        const originalIntensity = this.auraIntensity;
        const maxIntensity = originalIntensity * 2.5;
        
        const intensifyEffect = () => {
            const elapsed = performance.now() - startTime;
            const progress = elapsed / duration;
            
            if (progress >= 1.0) {
                // Restore original intensity
                this.auraIntensity = originalIntensity;
                if (this.shadowAura) {
                    this.shadowAura.material.emissiveIntensity = originalIntensity;
                }
                return;
            }
            
            // Pulse effect with gradual fade
            const pulse = Math.sin(elapsed * 0.01) * 0.3 + 0.7;
            const intensity = originalIntensity + (maxIntensity - originalIntensity) * (1 - progress) * pulse;
            
            this.auraIntensity = intensity;
            if (this.shadowAura) {
                this.shadowAura.material.emissiveIntensity = intensity;
            }
            
            requestAnimationFrame(intensifyEffect);
        };
        
        intensifyEffect();
    }
    
    /**
     * Update shadow aura to follow boss and add pulsing effects
     * THEMATIC: Void energy constantly emanating from "The Fallen Tormentor"
     */
    updateShadowAura(deltaTime) {
        if (!this.shadowAura || !this.enemy) return;
        
        const currentTime = performance.now();
        
        // Update aura position to follow Nairabos
        this.shadowAura.position.copy(this.enemy.position);
        this.shadowAura.position.y += 2.0; // Center on boss
        
        if (this.voidCorruptionField) {
            this.voidCorruptionField.position.copy(this.enemy.position);
            this.voidCorruptionField.position.y = 0.05; // Keep on ground
        }
        
        // Subtle pulsing effect
        const pulsePhase = currentTime * 0.003; // Slow pulse
        const pulse = Math.sin(pulsePhase) * 0.15 + 0.85; // 0.7 to 1.0
        
        // Apply pulse to aura
        this.shadowAura.material.opacity = 0.15 * pulse;
        this.shadowAura.scale.setScalar(0.95 + pulse * 0.1); // Subtle size variation
        
        // Slow rotation for mystical effect
        this.shadowAura.rotation.y += deltaTime * 0.5;
        
        // Update corruption field
        if (this.voidCorruptionField) {
            this.voidCorruptionField.material.opacity = 0.3 * pulse;
            this.voidCorruptionField.rotation.z += deltaTime * 0.3; // Slow rotation
        }
        
        // Enhance effects during desperation mode
        if (this.isDesperationMode) {
            const desperationPulse = Math.sin(currentTime * 0.008) * 0.3 + 0.7; // Faster pulse
            this.shadowAura.material.emissiveIntensity = this.auraIntensity * desperationPulse * 1.5;
            
            if (this.voidCorruptionField) {
                this.voidCorruptionField.material.emissiveIntensity = 0.4 * desperationPulse * 1.3;
            }
        }
    }
    
    /**
     * Clean up all shadow aura effects
     * THEMATIC: Banish the void corruption when boss is defeated
     */
    cleanupShadowEffects() {
        // Remove shadow aura
        if (this.shadowAura) {
            if (this.shadowAura.parent) {
                this.dungeonHandler.scene.remove(this.shadowAura);
            }
            this.shadowAura = null;
        }
        
        // Remove void corruption field
        if (this.voidCorruptionField) {
            if (this.voidCorruptionField.parent) {
                this.dungeonHandler.scene.remove(this.voidCorruptionField);
            }
            this.voidCorruptionField = null;
        }
        
        // Clear desperation mode effects from scene
        if (this.dungeonHandler.scene && this.dungeonHandler.scene.userData.desperationMode) {
            this.dungeonHandler.scene.userData.desperationMode = null;
        }
        
        console.log('[NairabosBossController] 🌟 Shadow effects cleansed - void corruption banished');
    }
    
    /**
     * Handle boss taking damage
     */
    takeDamage(damage) {
        if (this.isShielded) {
            console.log('[NairabosBossController] Damage blocked by shield');
            return false; // Damage blocked
        }
        
        this.currentHealth -= damage;
        
        // Show low health speech
        const healthPercent = this.currentHealth / this.maxHealth;
        if (healthPercent < 0.2 && Math.random() < 0.3) {
            this.showSpeechBubble(this.speechTexts.lowHealth, 2000);
        }
        
        return true; // Damage applied
    }
    
    /**
     * Check if Nairabos can be damaged
     */
    canTakeDamage() {
        return !this.isShielded;
    }
    
    /**
     * Stop the boss controller
     */
    stop() {
        this.isActive = false;
        
        // CRITICAL: Restore shader optimization after boss fight
        if (this.shaderOptimizer) {
            this.shaderOptimizer.restoreOptimization();
        }
        
        // Clean up shield
        this.deactivateShield();
        
        // Clean up speech bubble
        this.removeSpeechBubble();
        
        // Clean up signature shadow effects
        this.cleanupShadowEffects();
        
        // Stop music synchronization
        if (this.musicSync) {
            this.musicSync.stop();
        }
        
        // Stop attack patterns and clear projectiles
        if (this.attackPatterns) {
            this.attackPatterns.cleanup();
        }
        
        console.log('[NairabosBossController] Boss fight ended');
    }
    
    /**
     * Clean up resources
     */
    cleanup() {
        this.stop();
        
        if (this.musicSync) {
            this.musicSync.stop();
            this.musicSync = null;
        }
        
        if (this.attackPatterns) {
            this.attackPatterns.cleanup();
            this.attackPatterns = null;
        }
    }
}