import * as THREE from 'three';
import { ProjectileTypes } from '../../projectiles/ProjectileTypes.js';
import { createVoidLordEnergyStrain, createVoidLordEnergyOrb } from '../../generators/prefabs/voidLordEnemy.js';

/**
 * VoidLordAttackPatterns
 * Manages the Void Lord's energy strain attacks and phase-based patterns
 */
export class VoidLordAttackPatterns {
    constructor(enemy, dungeonHandler, player) {
        this.enemy = enemy;
        this.dungeonHandler = dungeonHandler;
        this.player = player;
        this.scene = dungeonHandler.scene;
        
        // Pattern configuration
        this.patterns = {
            phase1: ['energy_burst', 'void_beam', 'energy_rain', 'ground_tentacles'],
            phase2: ['energy_spiral', 'void_prison', 'chaos_storm', 'ground_tentacles'],
            phase3: ['reality_tear', 'annihilation_wave', 'black_hole_collapse', 'ground_tentacles']
        };
        
        // Active effects
        this.activeEnergyStrains = [];
        this.activeEnergyOrbs = [];
        this.activeProjectiles = [];
        
        // Pattern cooldowns
        this.lastPatternTime = 0;
        this.patternCooldown = 2000; // 2 seconds between patterns
        
        // Pattern usage tracking
        this.patternUsageCount = {};
        this.currentPattern = null;
        
        // Special attack state
        this.chargingSpecialAttack = false;
        this.specialAttackChargeTime = 0;
        
        console.log('[VoidLordAttackPatterns] Initialized attack system');
    }
    
    /**
     * Trigger an attack pattern based on phase and intensity
     */
    triggerPattern(phase, intensity = 0.5, isDesperationMode = false) {
        const currentTime = Date.now();
        
        // Check cooldown
        if (currentTime - this.lastPatternTime < this.patternCooldown) {
            return;
        }
        
        // Get available patterns for current phase
        const availablePatterns = this.patterns[`phase${phase}`];
        if (!availablePatterns || availablePatterns.length === 0) {
            console.warn(`[VoidLordAttackPatterns] No patterns available for phase ${phase}`);
            return;
        }
        
        // Select pattern randomly with intensity influencing preference for harder attacks
        // Random selection ensures all patterns get used
        const randomValue = Math.random();
        let patternIndex;
        
        if (intensity > 0.7) {
            // High intensity - prefer later patterns (harder attacks)
            patternIndex = Math.floor(randomValue * availablePatterns.length);
            if (randomValue > 0.5) {
                // 50% chance to use the hardest patterns
                patternIndex = Math.max(patternIndex, availablePatterns.length - 2);
            }
        } else if (intensity > 0.4) {
            // Medium intensity - any pattern
            patternIndex = Math.floor(randomValue * availablePatterns.length);
        } else {
            // Low intensity - prefer earlier patterns
            patternIndex = Math.floor(randomValue * availablePatterns.length);
            if (randomValue > 0.5) {
                // 50% chance to use easier patterns
                patternIndex = Math.min(patternIndex, 1);
            }
        }
        
        patternIndex = Math.min(Math.max(0, patternIndex), availablePatterns.length - 1);
        const selectedPattern = availablePatterns[patternIndex];
        
        // Track pattern usage
        if (!this.patternUsageCount[selectedPattern]) {
            this.patternUsageCount[selectedPattern] = 0;
        }
        this.patternUsageCount[selectedPattern]++;
        
        // Store current pattern for display
        this.currentPattern = selectedPattern;
        
        console.log(`[VoidLordAttackPatterns] Executing pattern: ${selectedPattern} (Phase ${phase}, Intensity ${intensity.toFixed(2)}, Usage #${this.patternUsageCount[selectedPattern]})`);
        
        // Log pattern usage stats periodically
        if (Object.keys(this.patternUsageCount).length > 0 && 
            Object.values(this.patternUsageCount).reduce((a, b) => a + b, 0) % 10 === 0) {
            console.log('[VoidLordAttackPatterns] Pattern usage stats:', this.patternUsageCount);
        }
        
        // Execute the selected pattern
        this.executePattern(selectedPattern, intensity, isDesperationMode);
        
        this.lastPatternTime = currentTime;
    }
    
    /**
     * Execute a specific attack pattern
     */
    executePattern(patternName, intensity, isDesperationMode) {
        // Trigger animation
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.triggerAttack('energy_strain');
        }
        
        switch (patternName) {
            // Phase 1 Patterns
            case 'energy_burst':
                this.executeEnergyBurst(intensity, isDesperationMode);
                break;
            case 'void_beam':
                this.executeVoidBeam(intensity, isDesperationMode);
                break;
            case 'energy_rain':
                this.executeEnergyRain(intensity, isDesperationMode);
                break;
                
            // Phase 2 Patterns
            case 'energy_spiral':
                this.executeEnergySpiral(intensity, isDesperationMode);
                break;
            case 'void_prison':
                this.executeVoidPrison(intensity, isDesperationMode);
                break;
            case 'chaos_storm':
                this.executeChaosStorm(intensity, isDesperationMode);
                break;
                
            // Phase 3 Patterns
            case 'reality_tear':
                this.executeRealityTear(intensity, isDesperationMode);
                break;
            case 'annihilation_wave':
                this.executeAnnihilationWave(intensity, isDesperationMode);
                break;
            case 'black_hole_collapse':
                this.executeBlackHoleCollapse(intensity, isDesperationMode);
                break;
                
            // All Phases Pattern
            case 'ground_tentacles':
                this.executeGroundTentacles(intensity, isDesperationMode);
                break;
                
            default:
                console.warn(`[VoidLordAttackPatterns] Unknown pattern: ${patternName}`);
        }
    }
    
    /**
     * PHASE 1: Energy Burst - Radial burst of energy projectiles
     */
    executeEnergyBurst(intensity, isDesperationMode) {
        const projectileCount = Math.floor(8 + intensity * 8);
        const baseSpeed = 3 + intensity * 2;
        const speedMultiplier = isDesperationMode ? 1.5 : 1.0;
        
        const bossPos = this.enemy.position.clone();
        bossPos.y += 2; // Fire from chest height
        
        for (let i = 0; i < projectileCount; i++) {
            const angle = (i / projectileCount) * Math.PI * 2;
            const direction = new THREE.Vector3(
                Math.cos(angle),
                0,
                Math.sin(angle)
            );
            
            this.fireEnergyProjectile(
                bossPos.clone(),
                direction,
                baseSpeed * speedMultiplier,
                0xff0000 // Red energy
            );
        }
        
        // Add screen shake
        this.addScreenShake(0.3, 500);
    }
    
    /**
     * PHASE 1: Void Beam - Energy tentacle beam attack
     */
    executeVoidBeam(intensity, isDesperationMode) {
        const beamDuration = 2000 + intensity * 1000;
        const tentacleCount = 3 + Math.floor(intensity * 2);
        
        // Create multiple tentacle beams
        const beamTentacles = [];
        
        for (let i = 0; i < tentacleCount; i++) {
            const startPos = this.enemy.position.clone();
            startPos.y += 6; // Start higher up, away from boss body
            
            // Create a very wide arc with huge spacing
            const angle = (i / (tentacleCount - 1)) * Math.PI * 2.5 - Math.PI * 1.25; // Wide 225° arc
            const distance = 20; // Very far from boss
            startPos.x += Math.sin(angle) * distance;
            startPos.z += Math.cos(angle) * distance - 15; // Big offset forward
            
            const tentacle = this.createBeamTentacle(startPos, this.player.position, 0xff0000);
            this.scene.add(tentacle);
            this.activeEnergyStrains.push(tentacle);
            beamTentacles.push(tentacle);
        }
        
        // Track tentacles to player
        const updateTentacles = () => {
            beamTentacles.forEach((tentacle, index) => {
                if (!tentacle.parent) return;
                
                // Update tentacle to track player
                const toPlayer = this.player.position.clone().sub(tentacle.position);
                toPlayer.normalize();
                
                // Animate tentacle segments
                tentacle.children.forEach((segment, segIndex) => {
                    const t = segIndex / tentacle.children.length;
                    const time = Date.now() * 0.001;
                    
                    // Wave motion
                    segment.rotation.x = Math.sin(time * 3 + segIndex * 0.5) * 0.2 * t;
                    segment.rotation.z = Math.cos(time * 2.5 + segIndex * 0.5) * 0.2 * t;
                    
                    // Point towards player
                    segment.lookAt(this.player.position);
                });
            });
        };
        
        // Update tentacle positions
        const tentacleInterval = setInterval(updateTentacles, 50);
        
        // Remove tentacles after duration
        setTimeout(() => {
            clearInterval(tentacleInterval);
            beamTentacles.forEach(tentacle => {
                this.removeEnergyStrain(tentacle);
            });
        }, beamDuration);
        
        // Damage zone
        this.createBeamDamageZone(this.enemy.position, this.player.position, 2, beamDuration);
    }
    
    /**
     * Create a beam tentacle
     */
    createBeamTentacle(startPos, targetPos, color = 0xff0000) {
        const tentacleGroup = new THREE.Group();
        const segmentCount = 10; // Back to original count
        const totalLength = startPos.distanceTo(targetPos);
        const segmentLength = totalLength / segmentCount;
        
        const direction = targetPos.clone().sub(startPos).normalize();
        
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1);
            // Proper tapering - thicker at base (bottom), thinner at top
            const radius = 0.8 - t * 0.5; // Taper from 0.8 at base to 0.3 at tip
            
            // Clean single voxel design
            const voxelSize = radius * 2;
            const geometry = new THREE.BoxGeometry(voxelSize, segmentLength, voxelSize);
            
            // Gradient effect through material
            const material = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.7 - t * 0.3, // Semi-transparent energy beam
                emissive: 0xff0000,
                emissiveIntensity: 1.0 + t * 0.5
            });
            
            const segment = new THREE.Mesh(geometry, material);
            
            // Position along line to target
            segment.position.copy(direction.clone().multiplyScalar(i * segmentLength));
            segment.lookAt(targetPos);
            
            tentacleGroup.add(segment);
        }
        
        tentacleGroup.position.copy(startPos);
        tentacleGroup.userData.isBeamTentacle = true;
        
        return tentacleGroup;
    }
    
    /**
     * PHASE 1: Energy Rain - Projectiles fall from above
     */
    executeEnergyRain(intensity, isDesperationMode) {
        const dropCount = Math.floor(15 + intensity * 20);
        const dropDelay = isDesperationMode ? 50 : 100;
        
        for (let i = 0; i < dropCount; i++) {
            setTimeout(() => {
                const randomOffset = new THREE.Vector3(
                    (Math.random() - 0.5) * 20,
                    15,
                    (Math.random() - 0.5) * 20
                );
                
                const startPos = this.player.position.clone().add(randomOffset);
                const direction = new THREE.Vector3(0, -1, 0);
                
                // Create warning marker
                this.createGroundWarning(
                    new THREE.Vector3(startPos.x, 0, startPos.z),
                    1.0,
                    500
                );
                
                // Fire projectile after warning
                setTimeout(() => {
                    this.fireEnergyProjectile(
                        startPos,
                        direction,
                        5,
                        0xff0000
                    );
                }, 500);
            }, i * dropDelay);
        }
    }
    
    /**
     * PHASE 2: Energy Spiral - Spiraling projectile pattern
     */
    executeEnergySpiral(intensity, isDesperationMode) {
        const spiralArms = 3 + Math.floor(intensity * 2);
        const projectilesPerArm = 10;
        const spiralSpeed = isDesperationMode ? 0.15 : 0.1;
        
        const bossPos = this.enemy.position.clone();
        bossPos.y += 2;
        
        let spiralAngle = 0;
        let spiralRadius = 1;
        
        const spiralInterval = setInterval(() => {
            for (let arm = 0; arm < spiralArms; arm++) {
                const armAngle = spiralAngle + (arm / spiralArms) * Math.PI * 2;
                const direction = new THREE.Vector3(
                    Math.cos(armAngle),
                    0,
                    Math.sin(armAngle)
                );
                
                this.fireEnergyProjectile(
                    bossPos.clone(),
                    direction,
                    3,
                    arm % 2 === 0 ? 0xff0000 : 0xff69b4 // Alternating red and pink
                );
            }
            
            spiralAngle += spiralSpeed;
            spiralRadius += 0.1;
            
            if (spiralRadius > 5) {
                clearInterval(spiralInterval);
            }
        }, 100);
    }
    
    /**
     * PHASE 2: Void Prison - Trap player in energy cage with tentacles
     */
    executeVoidPrison(intensity, isDesperationMode) {
        const prisonRadius = 5 - intensity * 2; // Smaller = harder
        const tentacleCount = 6 + Math.floor(intensity * 4);
        const prisonDuration = 3000 + intensity * 2000;
        
        const playerPos = this.player.position.clone();
        
        // Create energy tentacle prison
        for (let i = 0; i < tentacleCount; i++) {
            const angle = (i / tentacleCount) * Math.PI * 2;
            const tentaclePos = playerPos.clone().add(
                new THREE.Vector3(
                    Math.cos(angle) * prisonRadius,
                    0,
                    Math.sin(angle) * prisonRadius
                )
            );
            
            // Create tentacle that curves inward
            const tentacle = this.createAttackTentacle(tentaclePos, playerPos, 0xff0000);
            this.scene.add(tentacle);
            this.activeEnergyStrains.push(tentacle);
            
            // Animate tentacle closing in
            const closeInterval = setInterval(() => {
                if (!tentacle.parent) {
                    clearInterval(closeInterval);
                    return;
                }
                
                // Move tentacle closer to player
                const toPlayer = playerPos.clone().sub(tentacle.position);
                toPlayer.y = 0; // Keep on horizontal plane
                toPlayer.normalize().multiplyScalar(0.1);
                tentacle.position.add(toPlayer);
            }, 50);
            
            // Remove after duration
            setTimeout(() => {
                clearInterval(closeInterval);
                this.removeEnergyStrain(tentacle);
            }, prisonDuration);
        }
        
        // Create closing walls if in desperation mode
        if (isDesperationMode) {
            this.createClosingWalls(playerPos, prisonRadius, prisonDuration);
        }
    }
    
    /**
     * Create an attack tentacle
     */
    createAttackTentacle(startPos, targetPos, color = 0xff0000) {
        const tentacleGroup = new THREE.Group();
        const segmentCount = 8; // Back to original
        const segmentLength = 0.8;
        
        // Calculate direction to target
        const direction = targetPos.clone().sub(startPos).normalize();
        
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1);
            // Proper tapering - thick at base, thin at top
            const radius = 0.6 - t * 0.4; // Already correct: 0.6 at base to 0.2 at tip
            
            // Use voxel boxes
            const voxelSize = radius * 2;
            const geometry = new THREE.BoxGeometry(voxelSize, segmentLength, voxelSize);
            const material = new THREE.MeshBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.9 - t * 0.3, // Original opacity
                emissive: color,
                emissiveIntensity: 0.8 // Original intensity
            });
            
            const segment = new THREE.Mesh(geometry, material);
            
            // Position segments along curve towards target
            const curveOffset = Math.sin(t * Math.PI) * 2; // Arc shape
            segment.position.x = direction.x * i * segmentLength + direction.z * curveOffset;
            segment.position.y = i * segmentLength * 0.7 + curveOffset;
            segment.position.z = direction.z * i * segmentLength - direction.x * curveOffset;
            
            segment.userData.segmentIndex = i;
            tentacleGroup.add(segment);
        }
        
        tentacleGroup.position.copy(startPos);
        tentacleGroup.userData.isAttackTentacle = true;
        tentacleGroup.userData.timeOffset = Math.random() * Math.PI * 2;
        
        return tentacleGroup;
    }
    
    /**
     * PHASE 2: Chaos Storm - Random projectiles everywhere
     */
    executeChaosStorm(intensity, isDesperationMode) {
        const stormDuration = 5000;
        const projectileRate = isDesperationMode ? 50 : 100; // ms between projectiles
        
        const stormInterval = setInterval(() => {
            // Random position around arena
            const randomPos = new THREE.Vector3(
                (Math.random() - 0.5) * 30,
                Math.random() * 10 + 5,
                (Math.random() - 0.5) * 30
            );
            
            // Random direction
            const randomDir = new THREE.Vector3(
                Math.random() - 0.5,
                Math.random() - 0.5,
                Math.random() - 0.5
            ).normalize();
            
            // Random color
            const colors = [0xff0000, 0xff69b4, 0xff4500];
            const color = colors[Math.floor(Math.random() * colors.length)];
            
            this.fireEnergyProjectile(
                randomPos,
                randomDir,
                2 + Math.random() * 3,
                color
            );
        }, projectileRate);
        
        // Stop storm after duration
        setTimeout(() => {
            clearInterval(stormInterval);
        }, stormDuration);
        
        // Add continuous screen shake
        this.addContinuousScreenShake(0.2, stormDuration);
    }
    
    /**
     * PHASE 3: Reality Tear - Create damaging rifts in space
     */
    executeRealityTear(intensity, isDesperationMode) {
        const tearCount = 3 + Math.floor(intensity * 2);
        
        for (let i = 0; i < tearCount; i++) {
            setTimeout(() => {
                const tearPos = this.player.position.clone().add(
                    new THREE.Vector3(
                        (Math.random() - 0.5) * 10,
                        0,
                        (Math.random() - 0.5) * 10
                    )
                );
                
                // Warning
                this.createGroundWarning(tearPos, 3, 1000);
                
                // Create tear after warning
                setTimeout(() => {
                    this.createRealityTearEffect(tearPos, intensity, isDesperationMode);
                }, 1000);
            }, i * 500);
        }
    }
    
    /**
     * PHASE 3: Annihilation Wave - Massive expanding wave
     */
    executeAnnihilationWave(intensity, isDesperationMode) {
        const waveSpeed = 5 + intensity * 5;
        const waveCount = isDesperationMode ? 3 : 1;
        
        for (let wave = 0; wave < waveCount; wave++) {
            setTimeout(() => {
                // Create expanding ring of projectiles
                let radius = 1;
                const expandInterval = setInterval(() => {
                    const projectileCount = Math.floor(radius * 4);
                    
                    for (let i = 0; i < projectileCount; i++) {
                        const angle = (i / projectileCount) * Math.PI * 2;
                        const pos = this.enemy.position.clone().add(
                            new THREE.Vector3(
                                Math.cos(angle) * radius,
                                1,
                                Math.sin(angle) * radius
                            )
                        );
                        
                        // Create energy orb at position
                        const orb = createVoidLordEnergyOrb(0.5, 0xff0000);
                        orb.position.copy(pos);
                        this.scene.add(orb);
                        this.activeEnergyOrbs.push(orb);
                        
                        // Remove after short time
                        setTimeout(() => {
                            this.removeEnergyOrb(orb);
                        }, 1000);
                    }
                    
                    radius += waveSpeed * 0.1;
                    
                    if (radius > 20) {
                        clearInterval(expandInterval);
                    }
                }, 100);
            }, wave * 1500);
        }
        
        // Epic screen shake
        this.addScreenShake(0.5, 2000);
    }
    
    /**
     * PHASE 3: Black Hole Collapse - Ultimate attack
     */
    executeBlackHoleCollapse(intensity, isDesperationMode) {
        console.log('[VoidLordAttackPatterns] BLACK HOLE COLLAPSE - ULTIMATE ATTACK!');
        
        // Charge up time
        const chargeTime = 3000;
        const collapseRadius = 15;
        
        // Create charging effect
        const chargePos = this.enemy.position.clone();
        chargePos.y += 5;
        
        const chargeOrb = createVoidLordEnergyOrb(1, 0x000000);
        chargeOrb.position.copy(chargePos);
        this.scene.add(chargeOrb);
        
        // Grow the orb during charge
        const chargeInterval = setInterval(() => {
            chargeOrb.scale.multiplyScalar(1.05);
            
            // Pull in debris
            if (Math.random() < 0.3) {
                const debrisPos = chargePos.clone().add(
                    new THREE.Vector3(
                        (Math.random() - 0.5) * 20,
                        (Math.random() - 0.5) * 10,
                        (Math.random() - 0.5) * 20
                    )
                );
                
                const debris = createVoidLordEnergyOrb(0.3, 0xff0000);
                debris.position.copy(debrisPos);
                this.scene.add(debris);
                
                // Animate debris to black hole
                const pullInterval = setInterval(() => {
                    debris.position.lerp(chargePos, 0.1);
                    if (debris.position.distanceTo(chargePos) < 1) {
                        this.scene.remove(debris);
                        clearInterval(pullInterval);
                    }
                }, 50);
            }
        }, 100);
        
        // Warning for players
        this.createGroundWarning(this.enemy.position, collapseRadius, chargeTime);
        
        // Execute collapse
        setTimeout(() => {
            clearInterval(chargeInterval);
            
            // Remove charge orb
            this.scene.remove(chargeOrb);
            
            // Create massive damage burst
            this.createCollapseExplosion(this.enemy.position, collapseRadius, intensity);
            
            // Epic screen shake
            this.addScreenShake(1.0, 1000);
        }, chargeTime);
    }
    
    /**
     * Fire a basic energy projectile
     */
    fireEnergyProjectile(position, direction, speed, color) {
        // Use the dungeon handler's projectile system
        if (this.dungeonHandler.fireProjectile) {
            const projectileData = {
                position: position,
                direction: direction,
                speed: speed,
                damage: 1,
                type: 'enemy',
                owner: this.enemy,
                color: color,
                size: 0.3,
                glow: true
            };
            
            this.dungeonHandler.fireProjectile(projectileData);
        }
    }
    
    /**
     * Create ground warning indicator
     */
    createGroundWarning(position, radius, duration) {
        // Create voxel-style warning using boxes in a circle pattern
        const warningGroup = new THREE.Group();
        const voxelSize = 0.3;
        const voxelCount = Math.floor(radius * 8); // More voxels for larger radius
        
        for (let i = 0; i < voxelCount; i++) {
            const angle = (i / voxelCount) * Math.PI * 2;
            const voxelRadius = radius - 0.2;
            
            const geometry = new THREE.BoxGeometry(voxelSize, voxelSize * 0.2, voxelSize);
            const material = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.5,
                emissive: 0xff0000,
                emissiveIntensity: 0.3
            });
            
            const voxel = new THREE.Mesh(geometry, material);
            voxel.position.x = Math.cos(angle) * voxelRadius;
            voxel.position.z = Math.sin(angle) * voxelRadius;
            voxel.position.y = 0.1;
            
            warningGroup.add(voxel);
        }
        
        warningGroup.position.copy(position);
        
        this.scene.add(warningGroup);
        
        // Pulse animation for all voxels
        const pulseInterval = setInterval(() => {
            const opacity = 0.5 + Math.sin(Date.now() * 0.01) * 0.3;
            warningGroup.children.forEach(voxel => {
                voxel.material.opacity = opacity;
                voxel.material.emissiveIntensity = 0.3 + Math.sin(Date.now() * 0.01) * 0.2;
            });
        }, 50);
        
        // Remove after duration
        setTimeout(() => {
            clearInterval(pulseInterval);
            this.scene.remove(warningGroup);
            // Clean up geometry and materials
            warningGroup.children.forEach(voxel => {
                voxel.geometry.dispose();
                voxel.material.dispose();
            });
        }, duration);
    }
    
    /**
     * Create beam damage zone
     */
    createBeamDamageZone(startPos, endPos, width, duration) {
        // This would integrate with the dungeon's damage system
        // For now, just log it
        console.log(`[VoidLordAttackPatterns] Beam damage zone created`);
    }
    
    /**
     * Create closing walls for void prison
     */
    createClosingWalls(center, startRadius, duration) {
        const wallCount = 4;
        const walls = [];
        
        for (let i = 0; i < wallCount; i++) {
            const angle = (i / wallCount) * Math.PI * 2;
            const wallPos = center.clone();
            
            // Create energy wall
            const wall = createVoidLordEnergyOrb(3, 0xff0000);
            wall.scale.set(0.2, 3, startRadius * 2);
            wall.position.copy(wallPos);
            wall.rotation.y = angle;
            
            this.scene.add(wall);
            walls.push(wall);
        }
        
        // Animate walls closing in
        const closeInterval = setInterval(() => {
            walls.forEach(wall => {
                wall.scale.z *= 0.95;
            });
        }, 100);
        
        // Remove walls after duration
        setTimeout(() => {
            clearInterval(closeInterval);
            walls.forEach(wall => {
                this.scene.remove(wall);
            });
        }, duration);
    }
    
    /**
     * Create reality tear effect
     */
    createRealityTearEffect(position, intensity, isDesperationMode) {
        const tearDuration = 5000;
        const damageRadius = 3 + intensity * 2;
        
        // Create visual tear
        const tear = createVoidLordEnergyOrb(damageRadius, 0x000000);
        tear.position.copy(position);
        tear.position.y = 0.1;
        tear.scale.y = 0.1; // Flat on ground
        
        this.scene.add(tear);
        
        // Warping effect
        const warpInterval = setInterval(() => {
            tear.scale.x = damageRadius * (1 + Math.sin(Date.now() * 0.01) * 0.2);
            tear.scale.z = damageRadius * (1 + Math.cos(Date.now() * 0.01) * 0.2);
            tear.material.opacity = 0.5 + Math.random() * 0.3;
        }, 50);
        
        // Remove after duration
        setTimeout(() => {
            clearInterval(warpInterval);
            this.scene.remove(tear);
        }, tearDuration);
    }
    
    /**
     * Create collapse explosion
     */
    createCollapseExplosion(position, radius, intensity) {
        // Create expanding shockwave
        const shockwave = createVoidLordEnergyOrb(1, 0xff0000);
        shockwave.position.copy(position);
        shockwave.scale.y = 0.1;
        
        this.scene.add(shockwave);
        
        // Expand shockwave
        const expandInterval = setInterval(() => {
            shockwave.scale.x *= 1.2;
            shockwave.scale.z *= 1.2;
            shockwave.material.opacity *= 0.9;
            
            if (shockwave.scale.x > radius * 2) {
                clearInterval(expandInterval);
                this.scene.remove(shockwave);
            }
        }, 50);
        
        // Create damage in radius
        console.log(`[VoidLordAttackPatterns] Collapse explosion at ${position} with radius ${radius}`);
    }
    
    /**
     * Add screen shake effect
     */
    addScreenShake(intensity, duration) {
        if (this.dungeonHandler.camera) {
            const camera = this.dungeonHandler.camera;
            const originalPosition = camera.position.clone();
            const shakeStart = Date.now();
            
            const shakeInterval = setInterval(() => {
                const elapsed = Date.now() - shakeStart;
                if (elapsed >= duration) {
                    camera.position.copy(originalPosition);
                    clearInterval(shakeInterval);
                    return;
                }
                
                const decay = 1 - (elapsed / duration);
                camera.position.x = originalPosition.x + (Math.random() - 0.5) * intensity * decay;
                camera.position.z = originalPosition.z + (Math.random() - 0.5) * intensity * decay;
            }, 16);
        }
    }
    
    /**
     * Add continuous screen shake
     */
    addContinuousScreenShake(intensity, duration) {
        const shakeCount = Math.floor(duration / 200);
        for (let i = 0; i < shakeCount; i++) {
            setTimeout(() => {
                this.addScreenShake(intensity, 150);
            }, i * 200);
        }
    }
    
    /**
     * Remove energy strain from scene
     */
    removeEnergyStrain(strain) {
        const index = this.activeEnergyStrains.indexOf(strain);
        if (index > -1) {
            this.activeEnergyStrains.splice(index, 1);
        }
        
        if (strain.parent) {
            strain.parent.remove(strain);
        }
        
        if (strain.geometry) strain.geometry.dispose();
        if (strain.material) strain.material.dispose();
    }
    
    /**
     * Remove energy orb from scene
     */
    removeEnergyOrb(orb) {
        const index = this.activeEnergyOrbs.indexOf(orb);
        if (index > -1) {
            this.activeEnergyOrbs.splice(index, 1);
        }
        
        if (orb.parent) {
            orb.parent.remove(orb);
        }
        
        if (orb.geometry) orb.geometry.dispose();
        if (orb.material) orb.material.dispose();
    }
    
    /**
     * Update active effects
     */
    update(deltaTime) {
        // Update energy orb animations
        this.activeEnergyOrbs.forEach(orb => {
            if (orb.userData.pulseSpeed) {
                const scale = orb.userData.baseScale || 1;
                const pulse = 1 + Math.sin(Date.now() * 0.001 * orb.userData.pulseSpeed) * 0.2;
                orb.scale.setScalar(scale * pulse);
            }
        });
    }
    
    /**
     * Ground Tentacles - Tentacles emerge from ground around boss
     */
    executeGroundTentacles(intensity, isDesperationMode) {
        const tentacleCount = 8 + Math.floor(intensity * 4);
        const radius = 10 + intensity * 5; // Distance from boss
        const emergeDuration = 1500; // How long tentacles take to emerge
        
        console.log(`[VoidLordAttackPatterns] Summoning ${tentacleCount} ground tentacles`);
        
        // Create warning zones
        const warningPositions = [];
        for (let i = 0; i < tentacleCount; i++) {
            const angle = (i / tentacleCount) * Math.PI * 2;
            const distance = radius + (Math.random() - 0.5) * 4; // Some randomness
            const pos = new THREE.Vector3(
                this.enemy.position.x + Math.cos(angle) * distance,
                0,
                this.enemy.position.z + Math.sin(angle) * distance
            );
            warningPositions.push(pos);
            
            // Create warning
            this.createGroundWarning(pos, 2, emergeDuration);
        }
        
        // After warning, create tentacles
        setTimeout(() => {
            warningPositions.forEach((pos, index) => {
                // Stagger tentacle emergence slightly for more organic feel
                setTimeout(() => {
                    const tentacle = this.createEmergingGroundTentacle(pos);
                    this.scene.add(tentacle);
                    this.activeEnergyStrains.push(tentacle);
                    
                    // Remove after full animation completes (emerge + attack + retract)
                    // Total duration: 1000ms emerge + 3000ms attack + 800ms retract = 4800ms
                    setTimeout(() => {
                        // Cancel any ongoing animation
                        if (tentacle.userData.animationFrame) {
                            cancelAnimationFrame(tentacle.userData.animationFrame);
                        }
                        
                        // Clean up geometry and materials for each segment
                        tentacle.children.forEach(segment => {
                            if (segment.geometry) segment.geometry.dispose();
                            if (segment.material) segment.material.dispose();
                        });
                        
                        this.removeEnergyStrain(tentacle);
                    }, 4800 + 200); // Add small buffer to ensure animation completes
                }, index * 100); // Stagger by 100ms per tentacle
            });
        }, emergeDuration);
    }
    
    /**
     * Create a tentacle that emerges from the ground
     */
    createEmergingGroundTentacle(position) {
        const tentacleGroup = new THREE.Group();
        const segmentCount = 10;
        const segmentLength = 1.2;
        
        // Create tentacle segments
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1);
            const radius = 0.8 - t * 0.5; // Taper from 0.8 at base to 0.3 at tip
            
            // Clean voxel design for ground tentacles
            const voxelSize = radius * 2;
            const geometry = new THREE.BoxGeometry(voxelSize, segmentLength, voxelSize);
            
            // Create material with nice gradient
            const material = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.9 - t * 0.2,
                emissive: 0xff0000,
                emissiveIntensity: 0.8 + t * 0.4
            });
            
            const segment = new THREE.Mesh(geometry, material);
            segment.position.y = i * segmentLength - 2; // Start underground
            segment.userData.segmentIndex = i;
            segment.userData.originalY = i * segmentLength - 2;
            
            // Add slight rotation for organic feel
            segment.rotation.x = Math.sin(i * 0.5) * 0.1;
            segment.rotation.z = Math.cos(i * 0.5) * 0.1;
            
            tentacleGroup.add(segment);
        }
        
        tentacleGroup.position.copy(position);
        tentacleGroup.userData.startY = position.y;
        tentacleGroup.userData.timeOffset = Math.random() * Math.PI * 2;
        tentacleGroup.userData.animationPhase = 'emerging'; // Track animation state
        
        const emergeDuration = 1000; // 1 second to emerge
        const attackDuration = 3000; // 3 seconds attacking
        const retractDuration = 800; // 0.8 seconds to retract
        
        let animationFrame;
        
        // Combined animation for emerge, attack, and retract
        const animateTentacle = () => {
            const elapsed = performance.now() - startTime;
            
            if (elapsed < emergeDuration) {
                // Emergence phase
                const progress = elapsed / emergeDuration;
                tentacleGroup.position.y = position.y + progress * 2; // Rise up
                
                // Wave motion during emergence
                tentacleGroup.children.forEach((segment, index) => {
                    const t = index / tentacleGroup.children.length;
                    const wave = Math.sin(elapsed * 0.003 + index * 0.5) * 0.3 * t;
                    segment.rotation.x = Math.sin(index * 0.5) * 0.1 + wave;
                    segment.rotation.z = Math.cos(index * 0.5) * 0.1 + Math.cos(elapsed * 0.002 + index * 0.3) * 0.2 * t;
                    
                    // Pulse effect
                    if (segment.material) {
                        segment.material.emissiveIntensity = 0.8 + Math.sin(elapsed * 0.004 + index * 0.3) * 0.4;
                    }
                });
            } else if (elapsed < emergeDuration + attackDuration) {
                // Attack phase - full height with aggressive motion
                tentacleGroup.position.y = position.y + 2;
                
                // More aggressive wave motion during attack
                tentacleGroup.children.forEach((segment, index) => {
                    const t = index / tentacleGroup.children.length;
                    const attackTime = elapsed - emergeDuration;
                    const wave = Math.sin(attackTime * 0.005 + index * 0.5) * 0.5 * t;
                    segment.rotation.x = Math.sin(index * 0.5) * 0.1 + wave;
                    segment.rotation.z = Math.cos(index * 0.5) * 0.1 + Math.cos(attackTime * 0.004 + index * 0.3) * 0.3 * t;
                    
                    // Stronger pulse during attack
                    if (segment.material) {
                        segment.material.emissiveIntensity = 1.0 + Math.sin(attackTime * 0.006 + index * 0.3) * 0.5;
                    }
                });
            } else if (elapsed < emergeDuration + attackDuration + retractDuration) {
                // Retraction phase
                const retractProgress = (elapsed - emergeDuration - attackDuration) / retractDuration;
                tentacleGroup.position.y = position.y + 2 * (1 - retractProgress); // Sink back down
                
                // Segments retract with delay for organic feel
                tentacleGroup.children.forEach((segment, index) => {
                    const t = index / tentacleGroup.children.length;
                    const segmentDelay = t * 0.3; // Top segments retract first
                    const segmentProgress = Math.max(0, Math.min(1, (retractProgress - segmentDelay) / (1 - segmentDelay)));
                    
                    // Move segment down
                    segment.position.y = segment.userData.originalY + (segmentLength * index) * (1 - segmentProgress);
                    
                    // Reduce rotation as it retracts
                    segment.rotation.x *= (1 - segmentProgress * 0.8);
                    segment.rotation.z *= (1 - segmentProgress * 0.8);
                    
                    // Fade out
                    if (segment.material) {
                        segment.material.opacity = (0.9 - t * 0.2) * (1 - segmentProgress * 0.5);
                        segment.material.emissiveIntensity = (0.8 + t * 0.4) * (1 - segmentProgress);
                    }
                });
            } else {
                // Animation complete
                cancelAnimationFrame(animationFrame);
                return;
            }
            
            animationFrame = requestAnimationFrame(animateTentacle);
        };
        
        const startTime = performance.now();
        animateTentacle();
        
        // Store animation frame reference for cleanup
        tentacleGroup.userData.animationFrame = animationFrame;
        
        return tentacleGroup;
    }
    
    /**
     * Clean up all active effects
     */
    cleanup() {
        // Remove all energy strains
        [...this.activeEnergyStrains].forEach(strain => {
            this.removeEnergyStrain(strain);
        });
        
        // Remove all energy orbs
        [...this.activeEnergyOrbs].forEach(orb => {
            this.removeEnergyOrb(orb);
        });
        
        this.activeEnergyStrains = [];
        this.activeEnergyOrbs = [];
        this.activeProjectiles = [];
    }
}