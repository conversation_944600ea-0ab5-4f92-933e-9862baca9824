/**
 * Melee Combat AI implementation
 * Specializes in getting close to the player and using melee attacks
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';
import { PathfindingSystem } from '../PathfindingSystem.js';

export class MeleeCombatAI extends AIBrain {
    /**
     * Constructor for Melee Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // AGGRESSIVE: Remove attack cooldown for zombies and magma golems - rely on player invincibility only
        const enemyType = enemyData.type;
        if (enemyType === 'zombie' || enemyType === 'magma_golem') {
            this.attackCooldown = 0.0; // No cooldown - attack constantly!
            this.timeSinceLastAttack = 0.0; // Always ready to attack
        } else {
            // Other enemies keep their normal cooldowns
            this.attackCooldown = enemyData.attackCooldown || 2.0;
            this.timeSinceLastAttack = this.attackCooldown; // Start ready to attack
        }

        // Melee specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 2.0);
        this.attackRange = this._getScaledValue(enemyData.attackRange || 2.5);
        this.chargeRange = this._getScaledValue(enemyData.chargeRange || 6.0);

        // Charging behavior
        this.isCharging = false;
        this.chargeTimer = 0;
        this.chargeDuration = this._getScaledValue(1.0, 0.5, 1.5);
        this.chargeSpeed = this._getScaledValue(enemyData.speed * 1.5, enemyData.speed * 1.2, enemyData.speed * 2.0);
        this.chargeChance = this._getScaledValue(0.4, 0.2, 0.6); // Higher with difficulty

        // Blocking behavior
        this.isBlocking = false;
        this.blockTimer = 0;
        this.blockDuration = this._getScaledValue(1.0, 0.5, 1.5);
        this.blockChance = this._getScaledValue(0.3, 0.1, 0.5); // Higher with difficulty
        
        // Special Attack Parameters (Shield Bash for Tomb Guardian)
        this.hasSpecialAttack = enemyData.hasShieldBash || false;
        this.specialAttackCooldown = enemyData.shieldBashCooldown || 5.0;
        this.timeSinceLastSpecialAttack = this.specialAttackCooldown; // Start ready
        this.specialAttackRange = enemyData.shieldBashRange || 4.0;
        this.specialAttackDuration = 0.8; // Duration of shield bash animation
        this.specialAttackTimer = 0;
        this.isPerformingSpecialAttack = false;
        this.hasExecutedSpecialAttack = false;
        
        // Initialize pathfinding system
        this.pathfindingSystem = new PathfindingSystem(this);
        this.currentPath = [];
        this.currentWaypoint = null;
        this.specialAttackChance = this._getScaledValue(0.4, 0.2, 0.6); // Higher with difficulty

        // Circling behavior
        this.isCircling = false;
        this.circleDirection = 1; // 1 for clockwise, -1 for counterclockwise
        this.circleTimer = 0;
        this.circleDuration = this._getScaledValue(2.0, 1.0, 3.0);
        this.circleChance = this._getScaledValue(0.5, 0.3, 0.7); // Higher with difficulty

        // Attack execution tracking
        this.hasExecutedAttack = false;

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Get attack duration based on enemy type
     * @returns {Number} - Attack animation duration
     * @private
     */
    _getAttackDuration() {
        const enemyType = this.enemy.userData.type;
        if (enemyType === 'zombie' || enemyType === 'magma_golem') {
            return 0.2; // Very fast attack animation for aggressive enemies
        } else {
            return Math.max(this.attackCooldown * 0.8, 0.3); // Normal duration for others
        }
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update special attack cooldown
        if (this.hasSpecialAttack) {
            this.timeSinceLastSpecialAttack += deltaTime;
        }
        // Handle state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                // AGGRESSIVE: Always check for attack opportunities in idle
                if (this._canSeePlayer()) {
                    // PRIORITY 1: If player is in attack range and cooldown ready, attack immediately
                    if (distanceToPlayer <= this.attackRange &&
                        this.timeSinceLastAttack >= this.attackCooldown) {
                        this.setState(AIStates.ATTACKING);
                        // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                        const attackDuration = this._getAttackDuration();
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                        this.attackStateTimer = this.attackStateDuration;
                    }
                    // PRIORITY 1.5: Check for special attack opportunity (shield bash)
                    else if (this.hasSpecialAttack &&
                             distanceToPlayer <= this.specialAttackRange &&
                             this.timeSinceLastSpecialAttack >= this.specialAttackCooldown &&
                             Math.random() < this.specialAttackChance) {
                        this.setState(AIStates.SPECIAL_ATTACK);
                    }
                    // PRIORITY 2: If player is in charge range, consider charging
                    else if (distanceToPlayer <= this.chargeRange &&
                        distanceToPlayer > this.attackRange &&
                        Math.random() < this.chargeChance) {
                        this.setState(AIStates.CHARGING);
                    }
                    // PRIORITY 3: If player is too far, move closer
                    else if (distanceToPlayer > this.preferredRange) {
                        this.setState(AIStates.MOVING);
                    }
                    // PRIORITY 4: Otherwise, consider circling (but prefer attacking)
                    else if (Math.random() < this.circleChance * 0.5) { // Reduced circle chance to favor attacking
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    }
                }
                break;

            case AIStates.MOVING:
                // If player is in charge range, consider charging
                if (distanceToPlayer <= this.chargeRange &&
                    distanceToPlayer > this.attackRange &&
                    Math.random() < this.chargeChance) {
                    this.setState(AIStates.CHARGING);
                }
                // If player is in attack range, attack
                else if (distanceToPlayer <= this.attackRange &&
                         this.timeSinceLastAttack >= this.attackCooldown) {
                    this.setState(AIStates.ATTACKING);
                    // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                    const attackDuration = this._getAttackDuration();
                    this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                    this.attackStateTimer = this.attackStateDuration;
                }
                // Check for special attack opportunity while moving
                else if (this.hasSpecialAttack &&
                         distanceToPlayer <= this.specialAttackRange &&
                         this.timeSinceLastSpecialAttack >= this.specialAttackCooldown &&
                         Math.random() < this.specialAttackChance * 0.5) { // Lower chance while moving
                    this.setState(AIStates.SPECIAL_ATTACK);
                }
                // If we've reached a good position, go idle or circle
                else if (distanceToPlayer <= this.preferredRange) {
                    if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.CHARGING:
                // If charge timer expired, go to attacking if in range
                if (this.chargeTimer <= 0) {
                    if (distanceToPlayer <= this.attackRange) {
                        this.setState(AIStates.ATTACKING);
                        // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                        const attackDuration = this._getAttackDuration();
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                        this.attackStateTimer = this.attackStateDuration;
                    } else {
                        this.setState(AIStates.MOVING);
                    }
                }
                // If we've reached attack range during charge, attack
                else if (distanceToPlayer <= this.attackRange) {
                    this.setState(AIStates.ATTACKING);
                    // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                    const attackDuration = this._getAttackDuration();
                    this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                    this.attackStateTimer = this.attackStateDuration;
                }
                break;

            case AIStates.ATTACKING:
                // Stay in ATTACKING state for the full animation duration
                this.attackStateTimer -= deltaTime;
                if (this.attackStateTimer <= 0) {
                    // Only transition after animation completes
                    this.timeSinceLastAttack = 0;
                    if (distanceToPlayer > this.preferredRange) {
                        this.setState(AIStates.MOVING);
                    } else if (Math.random() < this.blockChance) {
                        this.setState(AIStates.BLOCKING);
                    } else if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                // CRITICAL: If player moves too far away during attack, cancel and chase
                else if (distanceToPlayer > this.attackRange * 1.5) {
                    this.setState(AIStates.MOVING);
                    this.attackStateTimer = 0;
                }
                break;

            case AIStates.BLOCKING:
                // After blocking duration, go back to idle/circling
                if (this.blockTimer <= 0) {
                    if (Math.random() < this.circleChance) {
                        this.setState(AIStates.STRAFING); // Use strafing for circling
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.STRAFING: // Used for circling
                // If circling timer expired, go back to idle
                if (this.circleTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                // If player is in attack range while circling, consider attacking
                else if (distanceToPlayer <= this.attackRange &&
                         this.timeSinceLastAttack >= this.attackCooldown) {
                    // Chance to attack while circling based on difficulty
                    const attackWhileCirclingChance = this._getScaledValue(0.6, 0.4, 0.8);
                    if (Math.random() < attackWhileCirclingChance) {
                        this.setState(AIStates.ATTACKING);
                        // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                        const attackDuration = this._getAttackDuration();
                        this.attackStateDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                        this.attackStateTimer = this.attackStateDuration;
                    }
                }
                // If player moves too far while circling, chase
                else if (distanceToPlayer > this.preferredRange * 1.5) {
                    this.setState(AIStates.MOVING);
                }
                break;
                
            case AIStates.SPECIAL_ATTACK:
                // Stay in special attack state until animation completes
                this.specialAttackTimer -= deltaTime;
                if (this.specialAttackTimer <= 0) {
                    // Reset special attack cooldown
                    this.timeSinceLastSpecialAttack = 0;
                    this.setState(AIStates.IDLE);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless circling
        if (this.currentState !== AIStates.STRAFING) {
            // Make sure all enemies face the player, especially zombies and magma golems
            this._faceTarget(this.player.position);

            // Force zombies and magma golems to face the player
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // EMERGENCY FIX: Only update rotation if not frozen
                if (!this.enemy.userData.freezeRotation) {
                    // Force rotation update to ensure they face the player
                    const direction = this.player.position.clone().sub(this.enemy.position);
                    direction.y = 0; // Keep upright
                    this.enemy.lookAt(this.enemy.position.clone().add(direction));

                    // IMPORTANT: Store the direction to player for animation handlers to use
                    if (!this.enemy.userData.lastMoveDirection) {
                        this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                    }
                    this.enemy.userData.lastMoveDirection.copy(direction);
                }
            }
        }

        // IMPORTANT: If we're hit reacting, make sure we set the state to HIT_REACTING
        // This ensures the animation handler plays the hit animation
        if (this.isHitReacting && this.currentState !== AIStates.HIT_REACTING) {
            this.setState(AIStates.HIT_REACTING);
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.MOVING:
                // Move towards player
                this._moveTowardsPlayer(deltaTime, directionToPlayer);
                break;

            case AIStates.CHARGING:
                // Charge towards player
                this._executeChargeBehavior(deltaTime, directionToPlayer);
                break;

            case AIStates.ATTACKING:
                // IMPROVED: Play attack animation first, then execute damage
                this.attackStateTimer -= deltaTime;

                // Execute damage at the right moment during animation
                const animationDuration = this.enemy.userData.animationData?.attackAnimationDuration || 0.6;
                const damagePoint = animationDuration * 0.4; // EARLIER: Execute damage 40% through animation (was 60%)
                const timeElapsed = animationDuration - this.attackStateTimer;

                // FIXED: Execute attack earlier and with more forgiving range
                if (!this.hasExecutedAttack && timeElapsed >= damagePoint) {
                    // Use more generous range for damage execution
                    const damageRange = Math.max(this.attackRange * 2.0, 6.0); // Much more forgiving
                    if (distanceToPlayer <= damageRange) {
                        this._executeMeleeAttack();
                        this.hasExecutedAttack = true;
                    }
                }

                // Transition out when animation completes
                if (this.attackStateTimer <= 0) {
                    this.hasExecutedAttack = false; // Reset for next attack
                    this.timeSinceLastAttack = 0; // Reset cooldown

                    if (distanceToPlayer > this.preferredRange) {
                        this.setState(AIStates.MOVING);
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                break;

            case AIStates.BLOCKING:
                // Execute blocking behavior
                this._executeBlockingBehavior(deltaTime);
                break;

            case AIStates.STRAFING:
                // Circle around player
                this._executeCirclingBehavior(deltaTime, directionToPlayer);
                break;
                
            case AIStates.SPECIAL_ATTACK:
                // Execute special attack (shield bash)
                this._executeSpecialAttack(deltaTime, distanceToPlayer);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.CHARGING:
                // Initialize charging
                this.chargeTimer = this.chargeDuration;
                this.isCharging = true;
                break;

            case AIStates.ATTACKING:
                // Initialize attack animation and timer
                this.hasExecutedAttack = false;
                // AGGRESSIVE: Very short animation for zombies and magma golems with no cooldown
                const attackDuration = this._getAttackDuration();
                const animationDuration = this.enemy.userData.animationData?.attackAnimationDuration || attackDuration;
                this.attackStateTimer = animationDuration;

                // Set attack animation state
                if (this.enemy.userData.animationState !== undefined) {
                    this.enemy.userData.animationState = 'attacking';
                }
                break;

            case AIStates.BLOCKING:
                // Initialize blocking
                this.blockTimer = this.blockDuration;
                this.isBlocking = true;
                break;

            case AIStates.STRAFING:
                // Initialize circling
                this.circleTimer = this.circleDuration;
                this.isCircling = true;
                // 50% chance to change circle direction
                if (Math.random() < 0.5) {
                    this.circleDirection *= -1;
                }
                break;
                
            case AIStates.SPECIAL_ATTACK:
                // Initialize special attack (shield bash)
                this.specialAttackTimer = this.specialAttackDuration;
                this.isPerformingSpecialAttack = true;
                this.hasExecutedSpecialAttack = false;
                
                // Trigger shield bash animation if tomb guardian
                if (this.enemy.userData.type === 'tomb_guardian' && 
                    this.enemy.userData.animationHandler && 
                    this.enemy.userData.animationHandler.triggerShieldBash) {
                    this.enemy.userData.animationHandler.triggerShieldBash();
                }
                break;
        }
    }

    /**
     * Move towards player
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _moveTowardsPlayer(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Calculate move amount
        const moveSpeed = this.enemyData.speed;
        const moveAmount = moveSpeed * deltaTime;
        
        // Use pathfinding system to navigate around obstacles
        if (!this.currentPath || this.currentPath.length === 0 || 
            (Date.now() - (this.lastPathUpdate || 0) > 500)) {
            // Update path every 500ms or when no path exists
            const obstacles = this._getObstaclesForPathfinding();
            this.currentPath = this.pathfindingSystem.findPath(this.player.position, obstacles);
            this.lastPathUpdate = Date.now();
            
            if (this.enemy.userData.debugPathfinding) {
                console.log(`[MeleeCombatAI] Updated path for ${this.enemy.name}, waypoints: ${this.currentPath.length}`);
            }
        }
        
        // Get next waypoint
        this.currentWaypoint = this.pathfindingSystem.getNextWaypoint();
        
        let moveVector;
        let newPosition;
        
        if (this.currentWaypoint) {
            // Move towards waypoint
            const directionToWaypoint = new THREE.Vector3()
                .copy(this.currentWaypoint)
                .sub(this.enemy.position)
                .normalize();
            
            // Get steering force to avoid dynamic obstacles
            const steeringForce = this.pathfindingSystem.getSteeringForce(
                directionToWaypoint, 
                this._getDynamicObstacles()
            );
            
            // Combine waypoint direction with steering
            const finalDirection = directionToWaypoint.add(steeringForce).normalize();
            moveVector = finalDirection.multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        } else {
            // Fallback to direct movement if no path
            moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        }

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Selective collision detection - skip floor objects but check walls/obstacles
        // Use the same filtering logic as PlayerController
        let canMove = true;
        
        // Skip collision detection entirely if we have a valid pathfinding waypoint
        if (this.currentWaypoint) {
            canMove = true; // Trust pathfinding
        } else if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a bounding box for the enemy at the test position
            // CRITICAL FIX: Use current Y position for collision check, only test X/Z movement
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, this.enemy.position.y, newPosition.z);
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with non-floor objects only
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL: Use same floor filtering logic as PlayerController
                // Check userData.isFloor first (most reliable method)
                const isFloorByUserData = obj.userData?.isFloor === true;

                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                const hasFloorMaterial = obj.material && (
                    obj.material.name?.includes('CaveFloor') ||
                    obj.material.name?.includes('caveFloor') ||
                    (obj.material.materials && obj.material.materials.some(mat =>
                        mat.name?.includes('CaveFloor') || mat.name?.includes('caveFloor')))
                );

                // Skip ALL types of floor objects - they should not block enemy movement
                if (isFloorByUserData || isFloor || hasFloorMaterial) {
                    continue; // Skip floor collision for enemy movement
                }

                try {
                    // Check collision with walls and objects (not floors)
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        // If we have a waypoint from pathfinding and hit a destructible, trust the pathfinding
                        if (this.currentWaypoint && (obj.userData?.isDestructible || obj.userData?.parentDestructible)) {
                            // Debug log but don't block movement
                            if (this.enemy?.userData?.debugPathfinding) {
                                const objName = obj.name || obj.userData?.type || 'unknown';
                                console.log(`[MeleeCombatAI] Ignoring destructible for pathfinding: ${objName}`);
                            }
                            continue; // Don't block movement for destructibles when pathfinding
                        }
                        
                        // Debug log collision detection
                        if (this.enemy?.userData?.debugPathfinding) {
                            const objName = obj.name || obj.userData?.type || 'unknown';
                            console.log(`[MeleeCombatAI] Collision detected with: ${objName}, destructible: ${obj.userData?.isDestructible}`);
                        }
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    // Continue if collision check fails
                    continue;
                }
            }
        }

        // Check separation from other enemies
        if (canMove && this.environmentalData && this.environmentalData.nearbyEnemies) {
            const minEnemyDistance = 1.5; // Minimum distance between enemies
            
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance < 0.1) continue; // Skip self
                
                // Calculate distance at new position
                const distAtNewPos = newPosition.distanceTo(enemyData.position);
                
                // If too close, prevent movement or adjust it
                if (distAtNewPos < minEnemyDistance) {
                    // Calculate push direction away from other enemy
                    const pushDir = new THREE.Vector3()
                        .subVectors(newPosition, enemyData.position)
                        .normalize();
                    
                    // Adjust position to maintain minimum distance
                    const adjustment = pushDir.multiplyScalar(minEnemyDistance - distAtNewPos);
                    newPosition.add(adjustment);
                    
                    // Re-check bounds after adjustment
                    if (this.floorBounds) {
                        const minX = this.floorBounds.min.x + 1.0;
                        const maxX = this.floorBounds.max.x - 1.0;
                        const minZ = this.floorBounds.min.z + 1.0;
                        const maxZ = this.floorBounds.max.z - 1.0;
                        
                        newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                        newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
                    }
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (directionToPlayer.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(directionToPlayer.x, 0, directionToPlayer.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(directionToPlayer);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }
    }

    /**
     * Execute charge behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeChargeBehavior(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Update charge timer
        this.chargeTimer -= deltaTime;

        // Calculate move amount (faster when charging)
        const moveAmount = this.chargeSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Selective collision detection - skip floor objects but check walls/obstacles
        // Use the same filtering logic as PlayerController
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a bounding box for the enemy at the test position
            // CRITICAL FIX: Use current Y position for collision check, only test X/Z movement
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, this.enemy.position.y, newPosition.z);
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with non-floor objects only
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL: Use same floor filtering logic as PlayerController
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                const hasFloorMaterial = obj.material && (
                    obj.material.name?.includes('CaveFloor') ||
                    obj.material.name?.includes('caveFloor') ||
                    (obj.material.materials && obj.material.materials.some(mat =>
                        mat.name?.includes('CaveFloor') || mat.name?.includes('caveFloor')))
                );

                // Skip ALL types of floor objects
                if (isFloorByUserData || isFloor || hasFloorMaterial) {
                    continue;
                }

                try {
                    // Check collision with walls and objects (not floors)
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
        }

        // Check separation from other enemies during charge
        if (canMove && this.environmentalData && this.environmentalData.nearbyEnemies) {
            const minEnemyDistance = 1.2; // Slightly smaller during charge
            
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance < 0.1) continue; // Skip self
                
                // Calculate distance at new position
                const distAtNewPos = newPosition.distanceTo(enemyData.position);
                
                // If too close, adjust position
                if (distAtNewPos < minEnemyDistance) {
                    const pushDir = new THREE.Vector3()
                        .subVectors(newPosition, enemyData.position)
                        .normalize();
                    
                    const adjustment = pushDir.multiplyScalar(minEnemyDistance - distAtNewPos);
                    newPosition.add(adjustment);
                    
                    // Re-check bounds after adjustment
                    if (this.floorBounds) {
                        const minX = this.floorBounds.min.x + 1.0;
                        const maxX = this.floorBounds.max.x - 1.0;
                        const minZ = this.floorBounds.min.z + 1.0;
                        const maxZ = this.floorBounds.max.z - 1.0;
                        
                        newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                        newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
                    }
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (directionToPlayer.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(directionToPlayer.x, 0, directionToPlayer.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(directionToPlayer);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get attack data from enemy model if available
        const attackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Emit attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }

    /**
     * Execute blocking behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeBlockingBehavior(deltaTime) {
        // Update block timer
        this.blockTimer -= deltaTime;

        // No movement while blocking, just face player
        // Could add slight backward movement if desired
    }

    /**
     * Execute circling behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeCirclingBehavior(deltaTime, directionToPlayer) {
        // CRITICAL FIX: Don't move if knocked back
        if (this.isKnockedBack) return;

        // Update circle timer
        this.circleTimer -= deltaTime;

        // Calculate perpendicular direction (for circling)
        const perpDirection = new THREE.Vector3(-directionToPlayer.z, 0, directionToPlayer.x);
        perpDirection.normalize().multiplyScalar(this.circleDirection);

        // Add a slight inward component to maintain distance
        const inwardDirection = directionToPlayer.clone().multiplyScalar(0.2);
        const circleDirection = perpDirection.clone().add(inwardDirection).normalize();

        // Calculate move amount
        const circleSpeed = this.enemyData.speed * 0.8; // Slightly slower when circling
        const moveAmount = circleSpeed * deltaTime;

        // Calculate new position
        const moveVector = circleDirection.multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Selective collision detection - skip floor objects but check walls/obstacles
        // Use the same filtering logic as PlayerController
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a bounding box for the enemy at the test position
            // CRITICAL FIX: Use current Y position for collision check, only test X/Z movement
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, this.enemy.position.y, newPosition.z);
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with non-floor objects only
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // CRITICAL: Use same floor filtering logic as PlayerController
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                const hasFloorMaterial = obj.material && (
                    obj.material.name?.includes('CaveFloorMaterial') ||
                    obj.material.name?.includes('caveFloor') ||
                    (obj.material.materials && obj.material.materials.some(mat =>
                        mat.name?.includes('CaveFloorMaterial') || mat.name?.includes('caveFloor')))
                );

                // Skip ALL types of floor objects
                if (isFloorByUserData || isFloor || hasFloorMaterial) {
                    continue;
                }

                try {
                    // Check collision with walls and objects (not floors)
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
        }

        // Check separation from other enemies during circling
        if (canMove && this.environmentalData && this.environmentalData.nearbyEnemies) {
            const minEnemyDistance = 1.5; // Normal distance during circling
            
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance < 0.1) continue; // Skip self
                
                // Calculate distance at new position
                const distAtNewPos = newPosition.distanceTo(enemyData.position);
                
                // If too close, adjust position
                if (distAtNewPos < minEnemyDistance) {
                    const pushDir = new THREE.Vector3()
                        .subVectors(newPosition, enemyData.position)
                        .normalize();
                    
                    const adjustment = pushDir.multiplyScalar(minEnemyDistance - distAtNewPos);
                    newPosition.add(adjustment);
                    
                    // Re-check bounds after adjustment
                    if (this.floorBounds) {
                        const minX = this.floorBounds.min.x + 1.0;
                        const maxX = this.floorBounds.max.x - 1.0;
                        const minZ = this.floorBounds.min.z + 1.0;
                        const maxZ = this.floorBounds.max.z - 1.0;
                        
                        newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                        newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
                    }
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // Special handling for zombies and magma golems
            const enemyType = this.enemy.userData.type;
            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // IMPORTANT: Make sure the enemy faces the direction of movement BEFORE moving
                // This ensures the model is properly oriented
                if (circleDirection.lengthSq() > 0.001 && !this.enemy.userData.freezeRotation) {
                    // EMERGENCY FIX: Only update rotation if not frozen
                    // Create a normalized direction vector in the XZ plane
                    const moveDir = new THREE.Vector3(circleDirection.x, 0, circleDirection.z).normalize();
                    // Calculate the target position to look at
                    const targetPos = this.enemy.position.clone().add(moveDir);
                    // Make the enemy look at the target position
                    this.enemy.lookAt(targetPos);
                }

                // Force position update for these enemy types
                // This ensures the position change takes effect regardless of animation handlers
                this.enemy.position.x = newPosition.x;
                this.enemy.position.z = newPosition.z;
                // Don't modify Y position to avoid floor collision issues

                // Store the position in userData to prevent animation handlers from overriding it
                if (!this.enemy.userData.lastAIPosition) {
                    this.enemy.userData.lastAIPosition = new THREE.Vector3();
                }
                this.enemy.userData.lastAIPosition.copy(this.enemy.position);

                // Store the movement direction for animation handlers to use
                if (!this.enemy.userData.lastMoveDirection) {
                    this.enemy.userData.lastMoveDirection = new THREE.Vector3();
                }
                this.enemy.userData.lastMoveDirection.copy(circleDirection);
            } else {
                // Normal position update for other enemy types
                this.enemy.position.copy(newPosition);
            }
        }

        // Face slightly ahead of movement direction for more natural look
        const lookDirection = circleDirection.clone().add(directionToPlayer).normalize();
        this._faceTarget(this.enemy.position.clone().add(lookDirection));
    }

    /**
     * Predict player movement for attack timing
     * @returns {THREE.Vector3} - Predicted player position
     * @private
     */
    _predictPlayerMovement() {
        if (!this.player) return null;

        // Simple prediction based on current velocity
        const predictedPosition = this.player.position.clone();

        // If player has velocity, predict movement
        if (this.player.velocity) {
            const predictionTime = this._getScaledValue(0.3, 0.1, 0.5); // Shorter for melee
            predictedPosition.add(this.player.velocity.clone().multiplyScalar(predictionTime));
        }

        return predictedPosition;
    }
    
    /**
     * Execute special attack (shield bash for tomb guardian)
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _executeSpecialAttack(deltaTime, distanceToPlayer) {
        this.specialAttackTimer -= deltaTime;
        
        // Calculate animation progress
        const animationProgress = 1 - (this.specialAttackTimer / this.specialAttackDuration);
        const damagePoint = 0.4; // Execute damage 40% through animation
        
        // Execute damage at the right moment
        if (!this.hasExecutedSpecialAttack && animationProgress >= damagePoint) {
            // Use generous range for shield bash
            const shieldBashRange = this.specialAttackRange * 1.5;
            if (distanceToPlayer <= shieldBashRange) {
                this._executeShieldBash();
                this.hasExecutedSpecialAttack = true;
            }
        }
        
        // Reset flags when animation completes
        if (this.specialAttackTimer <= 0) {
            this.isPerformingSpecialAttack = false;
            this.hasExecutedSpecialAttack = false;
        }
    }
    
    /**
     * Execute shield bash attack
     * @private
     */
    _executeShieldBash() {
        if (!this.player) return;
        
        // Get shield bash data from enemy model if available
        const shieldBashHitbox = this.enemy.userData.shieldBashHitbox || {
            radius: this.specialAttackRange,
            damage: this.enemyData.shieldBashDamage || 10,
            knockback: this.enemyData.shieldBashKnockback || 20,
            stunDuration: this.enemyData.shieldBashStunDuration || 1.5
        };
        
        // Emit shield bash event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShieldBash) {
            this.enemy.userData.onShieldBash(shieldBashHitbox);
        }
        
        // Alternatively, emit a special attack event with type
        if (this.enemy.userData.onSpecialAttack) {
            this.enemy.userData.onSpecialAttack({
                type: 'shield_bash',
                hitbox: shieldBashHitbox
            });
        }
    }
    
    /**
     * Check if path between two points collides with walls or obstacles
     * @param {THREE.Vector3} from - Start position
     * @param {THREE.Vector3} to - End position
     * @returns {boolean} True if collision detected
     * @private
     */
    _checkWallCollision(from, to) {
        if (!this.collisionObjects || this.collisionObjects.length === 0) {
            return false;
        }
        
        // Create a ray from current position to target
        // Start the ray from enemy's center height to detect obstacles at various heights
        const fromCenter = from.clone();
        fromCenter.y += 0.5; // Raise the ray origin to enemy's approximate center
        
        const toCenter = to.clone();
        toCenter.y += 0.5; // Keep the ray level
        
        const direction = toCenter.clone().sub(fromCenter).normalize();
        const distance = fromCenter.distanceTo(toCenter);
        const raycaster = new THREE.Raycaster(fromCenter, direction, 0, distance);
        
        // Check for intersections with walls and obstacles
        for (const obj of this.collisionObjects) {
            // Skip floor objects
            if (obj.userData?.isFloor || 
                (obj.name && obj.name.toLowerCase().includes('floor'))) {
                continue;
            }
            
            // Check if this is a wall, obstacle, or destructible object
            const intersects = raycaster.intersectObject(obj, true);
            if (intersects.length > 0) {
                // Debug log what we hit
                if (this.enemy?.userData?.debugPathfinding) {
                    const objName = obj.name || obj.userData?.type || 'unknown';
                    const isDestructible = obj.userData?.isDestructible || obj.userData?.parentDestructible;
                    console.log(`[MeleeCombatAI] Ray hit object: ${objName}, destructible: ${isDestructible}, type: ${obj.type}`);
                }
                
                // Additional check for destructible objects like vases
                if (obj.userData?.isDestructible || obj.userData?.parentDestructible) {
                    return true; // Path is blocked by destructible object
                }
                return true; // Path is blocked
            }
        }
        
        return false;
    }
    
    /**
     * Get alternative movement directions when direct path is blocked
     * @param {THREE.Vector3} preferredDirection - Preferred movement direction
     * @param {number} moveAmount - Movement distance
     * @returns {Array<THREE.Vector3>} Array of alternative movement vectors
     * @private
     */
    _getAlternativeMoves(preferredDirection, moveAmount) {
        const alternatives = [];
        
        // Try perpendicular directions first (for corner navigation)
        const perpendicular1 = new THREE.Vector3(-preferredDirection.z, 0, preferredDirection.x).normalize();
        const perpendicular2 = new THREE.Vector3(preferredDirection.z, 0, -preferredDirection.x).normalize();
        
        // Add perpendicular movements
        alternatives.push(perpendicular1.clone().multiplyScalar(moveAmount));
        alternatives.push(perpendicular2.clone().multiplyScalar(moveAmount));
        
        // Try diagonal movements (45 degree angles)
        const angles = [Math.PI/4, -Math.PI/4, Math.PI/8, -Math.PI/8];
        for (const angle of angles) {
            const rotated = new THREE.Vector3();
            rotated.x = preferredDirection.x * Math.cos(angle) - preferredDirection.z * Math.sin(angle);
            rotated.z = preferredDirection.x * Math.sin(angle) + preferredDirection.z * Math.cos(angle);
            rotated.y = 0;
            rotated.normalize();
            alternatives.push(rotated.multiplyScalar(moveAmount));
        }
        
        return alternatives;
    }
    
    /**
     * Get obstacles for pathfinding (static objects like walls, vases, etc.)
     * @returns {Array} Array of obstacle objects
     * @private
     */
    _getObstaclesForPathfinding() {
        const obstacles = [];
        
        if (this.collisionObjects) {
            for (const obj of this.collisionObjects) {
                // Skip floors
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                
                if (isFloorByUserData || isFloor) {
                    continue;
                }
                
                // Include all non-floor objects as obstacles
                // This includes walls, vases, pillars, etc.
                if (obj.position) {
                    // Get actual world position for objects that might be children
                    const worldPos = new THREE.Vector3();
                    obj.getWorldPosition(worldPos);
                    
                    obstacles.push({
                        position: worldPos,
                        radius: obj.userData?.size || 1.0,
                        type: obj.userData?.type || obj.name || 'unknown'
                    });
                }
            }
        }
        
        if (this.enemy.userData.debugPathfinding && obstacles.length > 0) {
            console.log(`[MeleeCombatAI] Found ${obstacles.length} obstacles for pathfinding`);
        }
        
        return obstacles;
    }
    
    /**
     * Get dynamic obstacles (other enemies, debris, etc.)
     * @returns {Array} Array of dynamic obstacle objects
     * @private
     */
    _getDynamicObstacles() {
        const obstacles = [];
        
        // Add nearby enemies as obstacles
        if (this.environmentalData && this.environmentalData.nearbyEnemies) {
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance > 0.1) { // Skip self
                    obstacles.push({
                        position: enemyData.position.clone(),
                        radius: 1.0,
                        type: 'enemy'
                    });
                }
            }
        }
        
        // Add nearby debris
        if (this.environmentalData && this.environmentalData.nearbyDebris) {
            for (const debrisData of this.environmentalData.nearbyDebris) {
                obstacles.push({
                    position: debrisData.position.clone(),
                    radius: 0.5,
                    type: 'debris'
                });
            }
        }
        
        return obstacles;
    }
}
