/**
 * Boss Combat AI base class
 * Provides framework for creating complex boss behaviors with phases
 * Integrates with BossController for music-reactive bullet patterns
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';
import { <PERSON><PERSON><PERSON>roller } from './BossController.js';
import { NairabosBossController } from './NairabosBossController.js';
import { ChronarchBossController } from './ChronarchBossController.js';
import { VoidLordBossController } from './VoidLordBossController.js';

export class BossCombatAI extends AIBrain {
    /**
     * Constructor for Boss Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Boss specific parameters
        this.maxHealth = enemyData.health;
        this.currentHealth = enemyData.health;
        this.phaseThresholds = [0.7, 0.4, 0.15]; // Health percentages for phase transitions
        this.currentPhase = 0; // Start at phase 0

        // Boss controller for music-reactive patterns
        this.bossController = null;
        this.isMusicReactive = enemyData.musicReactive !== false; // Enable by default

        // Phase transition
        this.isTransitioning = false;
        this.transitionTimer = 0;
        this.transitionDuration = this._getScaledValue(2.0, 1.5, 3.0);

        // Special attack
        this.specialAttackCooldown = this._getScaledValue(15.0, 20.0, 10.0); // Shorter with higher difficulty
        this.specialAttackCooldownTimer = this.specialAttackCooldown * 0.5; // Start halfway through cooldown
        this.specialAttackDuration = this._getScaledValue(3.0, 2.0, 4.0);
        this.specialAttackTimer = 0;

        // Boss movement
        this.teleportCooldown = this._getScaledValue(20.0, 30.0, 15.0); // Shorter with higher difficulty
        this.teleportCooldownTimer = this.teleportCooldown * 0.5; // Start halfway through cooldown
        this.teleportDuration = this._getScaledValue(1.0, 0.7, 1.5);
        this.teleportTimer = 0;
        
        // CRITICAL FIX: Initialize moveSpeed for boss movement
        // Ensure baseSpeed is available for phase scaling
        if (!enemyData.baseSpeed && enemyData.speed) {
            enemyData.baseSpeed = enemyData.speed;
        }
        this.moveSpeed = enemyData.baseSpeed || enemyData.speed || 3.0;
        
        // Ensure enemyData.speed is initialized for movement calculations
        if (!enemyData.speed) {
            enemyData.speed = this.moveSpeed;
        }
        
        console.log(`[BossCombatAI] Initialized movement for ${enemy.name || 'boss'}: moveSpeed=${this.moveSpeed}, enemyData.speed=${enemyData.speed}, baseSpeed=${enemyData.baseSpeed}`);

        // Flying escape system for when boss gets stuck
        this.stuckTimer = 0;
        this.lastTeleportTime = 0;
        this.teleportCooldownDuration = 3000; // 3 seconds between teleports
        
        // Smooth flying movement system
        this.isFlying = false;
        this.flyingStartPos = null;
        this.flyingTargetPos = null;
        this.flyingStartTime = 0;
        this.flyingDuration = 2000; // 2 seconds flight time

        // Initialize state
        this.setState(AIStates.IDLE);

        // Initialize boss controller if music reactive
        if (this.isMusicReactive && scene.userData.dungeonHandler && scene.userData.audioManager) {
            this._initBossController(scene.userData.dungeonHandler, scene.userData.audioManager);
        }
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Ensure boss controller is started
        if (this.bossController && !this.bossController.active) {
            console.log("[BossCombatAI] Boss controller not active, starting it...");
            this._startBossController();
        }
        // Update cooldown timers
        if (this.specialAttackCooldownTimer > 0) {
            this.specialAttackCooldownTimer -= deltaTime;
        }
        if (this.teleportCooldownTimer > 0) {
            this.teleportCooldownTimer -= deltaTime;
        }

        // Check for phase transitions
        this._checkPhaseTransition();

        // Handle state transitions
        if (this.isTransitioning) {
            // During transition, only update transition timer
            this.transitionTimer -= deltaTime;
            if (this.transitionTimer <= 0) {
                this.isTransitioning = false;
                this._onPhaseTransitionComplete();
            }
            return;
        }

        // State transitions based on current state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Debug log for state transitions
                console.log(`[BossCombatAI] IDLE state - Distance to player: ${distanceToPlayer.toFixed(2)}, Preferred range: ${this._getPreferredRange().toFixed(2)}`);
                // Transition based on distance
                if (distanceToPlayer <= this._getPreferredRange()) {
                    // Choose between different attack types
                    console.log('[BossCombatAI] Player in range - choosing attack state');
                    this._chooseAttackState();
                } else {
                    console.log('[BossCombatAI] Player too far - switching to MOVING state');
                    this.setState(AIStates.MOVING);
                }
                break;

            case AIStates.MOVING:
                // If reached preferred range, attack
                if (distanceToPlayer <= this._getPreferredRange()) {
                    this._chooseAttackState();
                }
                // Consider teleporting if cooldown is ready
                else if (distanceToPlayer > this._getPreferredRange() * 1.5 &&
                         this.teleportCooldownTimer <= 0) {
                    this.setState(AIStates.PHASE_CHANGE); // Use PHASE_CHANGE for teleport
                }
                break;

            case AIStates.ATTACKING:
            case AIStates.SHOOTING:
                // After attack, go back to idle
                this.setState(AIStates.IDLE);
                break;

            case AIStates.SPECIAL_ATTACK:
                // Update special attack timer
                this.specialAttackTimer -= deltaTime;
                if (this.specialAttackTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Update teleport timer
                this.teleportTimer -= deltaTime;
                if (this.teleportTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Initialize boss controller if not already initialized
        if (this.isMusicReactive && !this.bossController && this.scene && this.scene.userData.dungeonHandler && this.scene.userData.audioManager) {
            console.log("[BossCombatAI] Boss controller not initialized, initializing it now...");
            this._initBossController(this.scene.userData.dungeonHandler, this.scene.userData.audioManager);
        }

        // Update boss controller if active
        this._updateBossController(deltaTime);

        // PRIORITY: Handle smooth flying movement first
        if (this.isFlying) {
            this._updateFlyingMovement(deltaTime);
            return; // Skip normal behavior while flying
        }

        // If transitioning between phases, execute transition behavior
        if (this.isTransitioning) {
            this._executePhaseTransition(deltaTime);
            return;
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                // Idle behavior (slight random movement)
                this._executeIdleBehavior(deltaTime);
                break;

            case AIStates.MOVING:
                // Move towards player with boss-specific movement
                this._executeBossMovement(deltaTime, directionToPlayer, distanceToPlayer);
                break;

            case AIStates.ATTACKING:
                // Execute melee attack
                this._executeMeleeAttack();
                break;

            case AIStates.SHOOTING:
                // Execute ranged attack
                this._executeRangedAttack();
                break;

            case AIStates.SPECIAL_ATTACK:
                // Execute special attack
                this._executeSpecialAttack(deltaTime);
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Execute teleport
                this._executeTeleport(deltaTime);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.ATTACKING:
            case AIStates.SHOOTING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;

            case AIStates.SPECIAL_ATTACK:
                // Initialize special attack
                this.specialAttackTimer = this.specialAttackDuration;
                this.specialAttackCooldownTimer = this.specialAttackCooldown;
                break;

            case AIStates.PHASE_CHANGE: // Used for teleport
                // Initialize teleport
                this.teleportTimer = this.teleportDuration;
                this.teleportCooldownTimer = this.teleportCooldown;
                break;
        }
    }

    /**
     * Take damage and check for phase transitions
     * @param {Number} damage - Amount of damage taken
     */
    takeDamage(damage) {
        this.currentHealth -= damage;

        // Clamp health to 0
        if (this.currentHealth < 0) {
            this.currentHealth = 0;
        }

        // Check for phase transition
        this._checkPhaseTransition();
    }

    /**
     * Check if should transition to next phase
     * @private
     */
    _checkPhaseTransition() {
        if (this.isTransitioning) return;

        // Calculate health percentage
        const healthPercentage = this.currentHealth / this.maxHealth;

        // Check if crossed a phase threshold
        for (let i = this.currentPhase; i < this.phaseThresholds.length; i++) {
            if (healthPercentage <= this.phaseThresholds[i]) {
                // Transition to next phase
                this.currentPhase = i + 1;
                this._startPhaseTransition();
                break;
            }
        }
    }

    /**
     * Start phase transition
     * @private
     */
    _startPhaseTransition() {
        this.isTransitioning = true;
        this.transitionTimer = this.transitionDuration;

        // Cancel any current actions
        this.setState(AIStates.IDLE);

        // Emit phase change event (to be handled by DungeonHandler)
        if (this.enemy.userData.onPhaseChange) {
            this.enemy.userData.onPhaseChange(this.currentPhase);
        }
    }

    /**
     * Execute phase transition behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executePhaseTransition(deltaTime) {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: become invulnerable and play an animation

        // Make enemy partially transparent
        const opacity = 0.5 + 0.5 * Math.sin(this.transitionTimer * 5);
        this._setEnemyVisibility(opacity);
    }

    /**
     * Called when phase transition is complete
     * @private
     */
    _onPhaseTransitionComplete() {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: restore visibility and update parameters

        // Restore visibility
        this._setEnemyVisibility(1.0);

        // Update parameters based on phase
        this._updatePhaseParameters();

        // Trigger special attack pattern for phase transition
        if (this.bossController && this.bossController.active) {
            // Select pattern based on phase
            let patternName;
            switch (this.currentPhase) {
                case 1: patternName = "spiral_wave"; break;
                case 2: patternName = "laser_grid"; break;
                case 3: patternName = "hellburst"; break;
                default: patternName = "circle_ripple";
            }

            // Trigger pattern with high intensity
            this.bossController.patternManager.triggerPattern(patternName, 0.8 + (this.currentPhase * 0.05));
        }
    }

    /**
     * Update parameters based on current phase
     * @private
     */
    _updatePhaseParameters() {
        // This is a base implementation that should be overridden by subclasses
        // Default behavior: increase aggression and speed with each phase

        // Scale parameters based on phase
        const phaseMultiplier = 1.0 + this.currentPhase * 0.2; // 20% increase per phase

        // Update speed
        this.enemyData.speed = this.enemyData.baseSpeed * phaseMultiplier;
        
        // CRITICAL FIX: Also update moveSpeed to stay in sync with enemyData.speed
        this.moveSpeed = this.enemyData.speed;

        // Update attack cooldown (shorter)
        this.attackCooldown = this._getScaledValue(2.0) / phaseMultiplier;

        // Update special attack cooldown (shorter)
        this.specialAttackCooldown = this._getScaledValue(15.0, 20.0, 10.0) / phaseMultiplier;
    }

    /**
     * Choose an attack state based on distance and cooldowns
     * @private
     */
    _chooseAttackState() {
        console.log(`[BossCombatAI] _chooseAttackState() called`);
        
        // Check if special attack is ready
        if (this.specialAttackCooldownTimer <= 0) {
            // Higher chance of special attack at higher phases
            const specialAttackChance = 0.3 + (this.currentPhase * 0.1);
            console.log(`[BossCombatAI] Special attack chance: ${specialAttackChance.toFixed(2)}`);
            if (Math.random() < specialAttackChance) {
                console.log(`[BossCombatAI] Choosing SPECIAL_ATTACK state`);
                this.setState(AIStates.SPECIAL_ATTACK);
                return;
            }
        }

        // Choose between melee and ranged based on distance
        const distanceToPlayer = this._getDistanceToPlayer();
        const meleeRange = this._getScaledValue(3.0);

        console.log(`[BossCombatAI] Distance to player: ${distanceToPlayer.toFixed(2)}, melee range: ${meleeRange.toFixed(2)}`);

        if (distanceToPlayer <= meleeRange) {
            console.log(`[BossCombatAI] Choosing ATTACKING (melee) state`);
            this.setState(AIStates.ATTACKING);
        } else {
            console.log(`[BossCombatAI] Choosing SHOOTING (ranged) state`);
            this.setState(AIStates.SHOOTING);
        }
    }

    /**
     * Get preferred range based on phase
     * @returns {Number} - Preferred range
     * @private
     */
    _getPreferredRange() {
        // Base preferred range
        const baseRange = this._getScaledValue(5.0);

        // Adjust based on phase
        switch (this.currentPhase) {
            case 0: // First phase
                return baseRange;
            case 1: // Second phase
                return baseRange * 0.8; // Closer
            case 2: // Third phase
                return baseRange * 0.6; // Even closer
            default: // Final phase
                return baseRange * 0.5; // Very close
        }
    }

    /**
     * Execute move towards player behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeMoveTowardsPlayer(deltaTime, directionToPlayer) {
        // Calculate move amount
        const moveSpeed = this.enemyData.speed;
        const moveAmount = moveSpeed * deltaTime;

        // Calculate new position
        const moveVector = directionToPlayer.clone().multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls
        let canMove = true;
        if (this.collisionObjects) {
            const enemyRadius = this.enemyData.size || 0.5;
            for (const obj of this.collisionObjects) {
                const objBox = new THREE.Box3().setFromObject(obj);
                const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                    canMove = false;
                    break;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Face player
        this._faceTarget(this.player.position);
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        console.log(`[BossCombatAI] _executeMeleeAttack() called`);
        
        // Check if we have a specialized boss controller and delegate to it
        if (this.bossController && (this.bossController instanceof ChronarchBossController || this.bossController instanceof NairabosBossController)) {
            console.log(`[BossCombatAI] Delegating melee attack to specialized boss controller`);
            
            // For ChronarchBossController, call its executeAttack method
            if (this.bossController instanceof ChronarchBossController && this.bossController.executeAttack) {
                console.log(`[BossCombatAI] Calling ChronarchBossController.executeAttack() for melee`);
                this.bossController.executeAttack();
                return;
            }
            
            // For NairabosBossController, call its executeAttack method  
            if (this.bossController instanceof NairabosBossController && this.bossController.executeAttack) {
                console.log(`[BossCombatAI] Calling NairabosBossController.executeAttack() for melee`);
                this.bossController.executeAttack();
                return;
            }
        }

        console.log(`[BossCombatAI] Using default melee attack behavior`);

        // Get attack data from enemy model if available
        const baseAttackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: this.enemyData.meleeDamage || 4,
            knockback: 5.0
        };

        // Scale damage based on phase
        const damageMultiplier = 1.0 + (this.currentPhase * 0.2); // 20% increase per phase

        // Apply phase multiplier to damage
        const attackHitbox = {
            ...baseAttackHitbox,
            damage: baseAttackHitbox.damage * damageMultiplier
        };

        // Emit attack event with modified hitbox
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        }
    }

    /**
     * Execute ranged attack
     * @private
     */
    _executeRangedAttack() {
        if (!this.player) return;

        console.log(`[BossCombatAI] _executeRangedAttack() called`);
        
        // Check if we have a specialized boss controller and delegate to it
        if (this.bossController && (this.bossController instanceof ChronarchBossController || this.bossController instanceof NairabosBossController)) {
            console.log(`[BossCombatAI] Delegating attack to specialized boss controller`);
            
            // For ChronarchBossController, call its executeAttack method
            if (this.bossController instanceof ChronarchBossController && this.bossController.executeAttack) {
                console.log(`[BossCombatAI] Calling ChronarchBossController.executeAttack()`);
                this.bossController.executeAttack();
                return;
            }
            
            // For NairabosBossController, call its executeAttack method  
            if (this.bossController instanceof NairabosBossController && this.bossController.executeAttack) {
                console.log(`[BossCombatAI] Calling NairabosBossController.executeAttack()`);
                this.bossController.executeAttack();
                return;
            }
        }

        console.log(`[BossCombatAI] Using default ranged attack behavior`);

        // Get shoot direction
        let shootDirection;
        if (this.difficulty >= 3 || this.currentPhase >= 1) {
            // Use prediction for higher difficulties or later phases
            const predictedPosition = this._predictPlayerMovement();
            shootDirection = predictedPosition.clone().sub(this.enemy.position).normalize();
        } else {
            // Direct shooting for lower difficulties
            shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();
        }

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            // Scale projectile count based on phase
            const projectileCount = 1 + Math.floor(this.currentPhase / 2); // More projectiles in later phases
            this.enemy.userData.onShoot(shootDirection, projectileCount);
        }
    }

    /**
     * Execute special attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeSpecialAttack(deltaTime) {
        // If using music-reactive boss controller, delegate to it
        if (this.bossController && this.bossController.active) {
            // Use music intensity to determine attack pattern
            const intensity = this.bossController.currentIntensity;

            // Calculate attack progress (0 to 1)
            const attackProgress = 1 - (this.specialAttackTimer / this.specialAttackDuration);

            // Select pattern based on intensity and phase
            let patternName;
            if (intensity < 30) {
                patternName = "petal_spread";
            } else if (intensity < 50) {
                patternName = "circle_ripple";
            } else if (intensity < 70) {
                patternName = "spiral_wave";
            } else {
                patternName = "hellburst";
            }

            // Trigger pattern at specific points in the progress
            if (attackProgress >= 0.2 && attackProgress < 0.25) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            } else if (attackProgress >= 0.5 && attackProgress < 0.55) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            } else if (attackProgress >= 0.8 && attackProgress < 0.85) {
                this.bossController.patternManager.triggerPattern(patternName, intensity / 100);
            }

            return;
        }

        // Fallback to default behavior if no boss controller
        // Calculate attack progress (0 to 1)
        const attackProgress = 1 - (this.specialAttackTimer / this.specialAttackDuration);

        // Execute attack at specific points in the progress
        if (attackProgress >= 0.2 && attackProgress < 0.25) {
            this._executeSpecialAttackWave(0);
        } else if (attackProgress >= 0.5 && attackProgress < 0.55) {
            this._executeSpecialAttackWave(1);
        } else if (attackProgress >= 0.8 && attackProgress < 0.85) {
            this._executeSpecialAttackWave(2);
        }
    }

    /**
     * Execute a wave of the special attack
     * @param {Number} waveIndex - Index of the wave
     * @private
     */
    _executeSpecialAttackWave(waveIndex) {
        if (!this.player) return;

        // Number of projectiles based on phase and wave
        const baseCount = 4 + this.currentPhase * 2;
        const projectileCount = baseCount + waveIndex;

        // Emit special attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onSpecialAttack) {
            this.enemy.userData.onSpecialAttack(waveIndex, projectileCount);
        }
    }

    /**
     * Execute teleport behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeTeleport(deltaTime) {
        // Calculate teleport progress (0 to 1)
        const teleportProgress = 1 - (this.teleportTimer / this.teleportDuration);

        if (teleportProgress < 0.5) {
            // First half: fade out
            const opacity = 1 - (teleportProgress * 2);
            this._setEnemyVisibility(opacity);
        } else {
            // Second half: fade in at new position
            if (teleportProgress === 0.5) {
                // At exactly halfway, move to new position
                this._teleportToNewPosition();
            }

            // Fade in
            const opacity = (teleportProgress - 0.5) * 2;
            this._setEnemyVisibility(opacity);
        }
    }

    /**
     * Teleport to a new position
     * @private
     */
    _teleportToNewPosition() {
        if (!this.player) return;

        // Choose teleport position based on phase
        let teleportPosition;

        if (this.currentPhase >= 2) {
            // Later phases: teleport behind player
            const playerForward = new THREE.Vector3(0, 0, -1);
            if (this.player.quaternion) {
                playerForward.applyQuaternion(this.player.quaternion);
            }

            const behindDistance = 3.0;
            teleportPosition = this.player.position.clone().add(
                playerForward.clone().multiplyScalar(-behindDistance)
            );
        } else {
            // Earlier phases: teleport to preferred range
            const preferredRange = this._getPreferredRange();
            const randomAngle = Math.random() * Math.PI * 2;

            teleportPosition = this.player.position.clone().add(
                new THREE.Vector3(
                    Math.cos(randomAngle) * preferredRange,
                    0,
                    Math.sin(randomAngle) * preferredRange
                )
            );
        }

        // Apply teleport
        this.enemy.position.copy(teleportPosition);

        // Face player
        this._faceTarget(this.player.position);
    }

    /**
     * Set enemy visibility
     * @param {Number} opacity - Opacity value (0-1)
     * @private
     */
    _setEnemyVisibility(opacity) {
        // Apply opacity to all meshes in enemy
        this.enemy.traverse(child => {
            if (child.isMesh && child.material) {
                // Handle array of materials
                if (Array.isArray(child.material)) {
                    child.material.forEach(mat => {
                        mat.transparent = opacity < 1;
                        mat.opacity = opacity;
                    });
                } else {
                    child.material.transparent = opacity < 1;
                    child.material.opacity = opacity;
                }
            }
        });
    }

    /**
     * Initialize boss controller for music-reactive patterns
     * @param {Object} dungeonHandler - Reference to the DungeonHandler
     * @param {Object} audioManager - Reference to the AudioManager
     * @private
     */
    _initBossController(dungeonHandler, audioManager) {
        console.log("[BossCombatAI] _initBossController called");
        console.log("[BossCombatAI] DungeonHandler:", dungeonHandler ? "exists" : "null");
        console.log("[BossCombatAI] AudioManager:", audioManager ? "exists" : "null");
        console.log("[BossCombatAI] Enemy:", this.enemy ? "exists" : "null");
        if (this.enemy) {
            console.log("[BossCombatAI] Enemy position:", this.enemy.position ? "exists" : "null");
            if (this.enemy.position) {
                console.log(`[BossCombatAI] Enemy position: ${this.enemy.position.x.toFixed(2)}, ${this.enemy.position.y.toFixed(2)}, ${this.enemy.position.z.toFixed(2)}`);
            }
        }
        try {
            // Determine boss type and create appropriate controller
            const bossType = this.enemy.userData.type || this.enemy.name || 'unknown';
            console.log(`[BossCombatAI] Detected boss type: ${bossType}`);
            console.log(`[BossCombatAI] Enemy name: ${this.enemy.name}`);
            console.log(`[BossCombatAI] Enemy userData.type: ${this.enemy.userData.type}`);
            console.log(`[BossCombatAI] Enemy userData.name: ${this.enemy.userData.name}`);
            console.log(`[BossCombatAI] Enemy userData:`, this.enemy.userData);
            
            if (bossType === 'nairabos' || this.enemy.name === 'Nairabos' || 
                (this.enemy.userData.name && this.enemy.userData.name.includes('Nairabos'))) {
                // Use specialized Nairabos controller
                console.log("[BossCombatAI] Creating NairabosBossController for Nairabos boss");
                this.bossController = new NairabosBossController(this.enemy, dungeonHandler, audioManager);
                
                // Store reference to AI controller in enemy userData
                this.enemy.userData.aiController = this;
                
                // Start Nairabos boss controller directly (no timeline loading needed)
                this._startBossController();
                
            } else if (bossType === 'chronarch' || this.enemy.name === 'Chronarch' || 
                       (this.enemy.userData.name && this.enemy.userData.name.includes('Chronarch'))) {
                // Use specialized Chronarch controller
                console.log("[BossCombatAI] Creating ChronarchBossController for Chronarch boss");
                this.bossController = new ChronarchBossController(this.enemy, dungeonHandler, audioManager);
                
                // Connect animation handler
                if (this.enemy.userData.animationHandler) {
                    this.bossController.setAnimationHandler(this.enemy.userData.animationHandler);
                    console.log("[BossCombatAI] Connected ChronarchAnimationHandler to boss controller");
                }
                
                // Store reference to AI controller in enemy userData
                this.enemy.userData.aiController = this;
                
                // CRITICAL FIX: Store boss controller where DungeonHandler expects it
                this.enemy.userData.chronarchBossController = this.bossController;
                console.log("[BossCombatAI] Connected ChronarchBossController to enemy.userData for DungeonHandler access");
                
                // Start Chronarch boss controller directly
                this._startBossController();
                
            } else if (bossType === 'void_lord' || this.enemy.name === 'VoidLord' || 
                       (this.enemy.userData.name && this.enemy.userData.name.includes('VoidLord')) ||
                       (this.enemy.userData.enemyType && this.enemy.userData.enemyType === 'void_lord')) {
                // Use specialized Void Lord controller
                console.log("[BossCombatAI] Creating VoidLordBossController for Void Lord boss");
                this.bossController = new VoidLordBossController(this.enemy, dungeonHandler, audioManager);
                
                // Connect animation handler
                if (this.enemy.userData.animationHandler) {
                    this.bossController.setAnimationHandler(this.enemy.userData.animationHandler);
                    console.log("[BossCombatAI] Connected VoidLordAnimationHandler to boss controller");
                }
                
                // Store reference to AI controller in enemy userData
                this.enemy.userData.aiController = this;
                
                // CRITICAL FIX: Store boss controller where DungeonHandler expects it
                this.enemy.userData.voidLordBossController = this.bossController;
                console.log("[BossCombatAI] Connected VoidLordBossController to enemy.userData for DungeonHandler access");
                
                // Start Void Lord boss controller directly
                this._startBossController();
                
            } else {
                // Use generic boss controller for other bosses
                console.log("[BossCombatAI] Creating generic BossController");
                this.bossController = new BossController(
                    this.enemy,
                    dungeonHandler,
                    audioManager,
                    {
                        visualEffects: true,
                        screenShake: true,
                        debugMode: true
                    }
                );

                // Initialize boss controller
                this.bossController.init().then(success => {
                    if (success) {
                        console.log("[BossCombatAI] Boss controller initialized successfully");

                        // Store reference to AI controller in enemy userData
                        this.enemy.userData.aiController = this;

                        // Load the appropriate timeline for the boss type
                        if (bossType === 'catacombs_overlord') {
                            console.log("[BossCombatAI] Setting up catacomb_overlord timeline for catacomb boss");
                            const success = this.bossController.loadTimeline("catacomb_overlord", false); // false = integrated mode
                            console.log(`[BossCombatAI] Timeline load ${success ? 'successful' : 'failed'}`);
                        } else {
                            console.log("[BossCombatAI] Enemy type not recognized, using default timeline");
                            const success = this.bossController.loadTimeline("catacomb_overlord", false); // Use catacomb_overlord as default
                            console.log(`[BossCombatAI] Default timeline load ${success ? 'successful' : 'failed'}`);
                        }

                        // Start boss controller when entering combat
                        this._startBossController();
                    } else {
                        console.error("[BossCombatAI] Failed to initialize boss controller");
                    }
                });
            }
        } catch (error) {
            console.error("[BossCombatAI] Error initializing boss controller:", error);
        }
    }

    /**
     * Start boss controller
     * @private
     */
    _startBossController() {
        console.log("[BossCombatAI] _startBossController called");
        if (this.bossController) {
            console.log("[BossCombatAI] Boss controller exists, active state:", this.bossController.active || this.bossController.isActive);
            
            // Check if already active (different property names for different controllers)
            const isActive = this.bossController.active || this.bossController.isActive;
            
            if (!isActive) {
                console.log("[BossCombatAI] Starting boss controller...");
                
                // Check if it's a specialized boss controller (synchronous) or generic BossController (asynchronous)
                if (this.bossController instanceof NairabosBossController) {
                    // Synchronous start for NairabosBossController
                    try {
                        this.bossController.start();
                        console.log("[BossCombatAI] NairabosBossController started successfully");
                    } catch (error) {
                        console.error("[BossCombatAI] Error starting NairabosBossController:", error);
                    }
                } else if (this.bossController instanceof ChronarchBossController) {
                    // Synchronous start for ChronarchBossController
                    try {
                        this.bossController.start();
                        console.log("[BossCombatAI] ChronarchBossController started successfully");
                    } catch (error) {
                        console.error("[BossCombatAI] Error starting ChronarchBossController:", error);
                    }
                } else if (this.bossController instanceof VoidLordBossController) {
                    // Synchronous start for VoidLordBossController
                    try {
                        this.bossController.start();
                        console.log("[BossCombatAI] VoidLordBossController started successfully");
                    } catch (error) {
                        console.error("[BossCombatAI] Error starting VoidLordBossController:", error);
                    }
                } else {
                    // Asynchronous start for generic BossController
                    this.bossController.start().then(success => {
                        if (success) {
                            console.log("[BossCombatAI] Boss controller started successfully");
                        } else {
                            console.error("[BossCombatAI] Failed to start boss controller");
                        }
                    }).catch(error => {
                        console.error("[BossCombatAI] Error starting boss controller:", error);
                    });
                }
            } else {
                console.log("[BossCombatAI] Boss controller already active");
            }
        } else {
            console.error("[BossCombatAI] Cannot start boss controller - not initialized");
        }
    }

    /**
     * Update boss controller
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateBossController(deltaTime) {
        // Check both active and isActive properties for different controller types
        if (this.bossController && (this.bossController.active || this.bossController.isActive)) {
            this.bossController.update(deltaTime);
        }
    }
    
    /**
     * Execute idle behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeIdleBehavior(deltaTime) {
        // Slight random movement
        if (!this.idleTarget) {
            const randomOffset = new THREE.Vector3(
                (Math.random() - 0.5) * 4,
                0,
                (Math.random() - 0.5) * 4
            );
            this.idleTarget = this.enemy.position.clone().add(randomOffset);
        }

        // Move towards idle target
        const toTarget = new THREE.Vector3().subVectors(this.idleTarget, this.enemy.position);
        const distance = toTarget.length();

        if (distance > 0.5) {
            toTarget.normalize();
            this._applyMovement(toTarget, this.moveSpeed * 0.3, deltaTime);
        } else {
            // Reached target, pick new one
            this.idleTarget = null;
        }
    }
    
    /**
     * Execute boss-specific movement behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _executeBossMovement(deltaTime, directionToPlayer, distanceToPlayer) {
        if (!directionToPlayer || !this.player) return;
        
        // Debug log to verify movement execution is being called
        console.log(`[BossCombatAI] _executeBossMovement called: distance=${distanceToPlayer.toFixed(2)}, moveSpeed=${this.moveSpeed}`);
        
        // Determine target position based on preferred range
        const preferredRange = this._getPreferredRange();
        const currentDistance = distanceToPlayer;
        
        // Calculate movement direction
        let moveDirection = new THREE.Vector3();
        
        if (currentDistance > preferredRange * 1.2) {
            // Too far - move towards player
            moveDirection.copy(directionToPlayer);
        } else if (currentDistance < preferredRange * 0.8) {
            // Too close - move away from player
            moveDirection.copy(directionToPlayer).negate();
        } else {
            // In range - strafe around player
            const strafeDirection = new THREE.Vector3(-directionToPlayer.z, 0, directionToPlayer.x);
            const strafeAmount = Math.sin(Date.now() * 0.001) > 0 ? 1 : -1;
            moveDirection.copy(strafeDirection).multiplyScalar(strafeAmount);
        }
        
        // Apply movement with boss speed
        if (moveDirection.lengthSq() > 0) {
            moveDirection.normalize();
            console.log(`[BossCombatAI] Applying movement - Direction: (${moveDirection.x.toFixed(2)}, ${moveDirection.y.toFixed(2)}, ${moveDirection.z.toFixed(2)}), Speed: ${this.moveSpeed}, DeltaTime: ${deltaTime.toFixed(3)}`);
            this._applyMovement(moveDirection, this.moveSpeed, deltaTime);
            this._faceTarget(this.player.position);
        } else {
            console.log(`[BossCombatAI] No movement - moveDirection length too small or zero`);
        }
    }

    /**
     * Apply movement to the boss with collision detection and bounds checking
     * @param {THREE.Vector3} direction - Normalized movement direction
     * @param {Number} speed - Movement speed
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _applyMovement(direction, speed, deltaTime) {
        if (this.isKnockedBack) return;

        // Ensure we have a valid direction
        if (!direction || direction.lengthSq() < 0.001) return;

        // Normalize direction
        const normalizedDirection = direction.clone().normalize();

        // Calculate movement vector
        const movement = normalizedDirection.multiplyScalar(speed * deltaTime);

        // Calculate new position
        const newPosition = this.enemy.position.clone().add(movement);

        // Check floor bounds to prevent boss from walking off the floor
        if (this.floorBounds) {
            const buffer = 2.0; // Keep boss away from edges (larger buffer for boss)
            const minX = this.floorBounds.min.x + buffer;
            const maxX = this.floorBounds.max.x - buffer;
            const minZ = this.floorBounds.min.z + buffer;
            const maxZ = this.floorBounds.max.z - buffer;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check collision with walls and objects
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            const bossRadius = this.enemyData.size || 1.5; // Boss is larger
            const bossBox = new THREE.Box3().setFromCenterAndSize(
                newPosition,
                new THREE.Vector3(bossRadius * 2, bossRadius * 2, bossRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry
                }

                // Skip floor objects
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                
                // Check for small ground objects that shouldn't block movement
                const isSmallGroundObject = obj.userData?.isGroundDecoration === true || 
                                          obj.userData?.isSmallObject === true ||
                                          obj.userData?.isFloorDecoration === true;
                
                // Check for specific object types that are small ground decorations
                const isDebrisOrSmallDecor = obj.name && (
                    obj.name.toLowerCase().includes('debris') ||
                    obj.name.toLowerCase().includes('rubble') ||
                    obj.name.toLowerCase().includes('candle') ||
                    obj.name.toLowerCase().includes('bottle') ||
                    obj.name.toLowerCase().includes('potion') ||
                    obj.name.toLowerCase().includes('book') ||
                    obj.name.toLowerCase().includes('scroll') ||
                    obj.name.toLowerCase().includes('crystal_small') ||
                    obj.name.toLowerCase().includes('stone_small') ||
                    obj.name.toLowerCase().includes('vase') ||
                    obj.name.toLowerCase().includes('moss')
                );

                const objBox = new THREE.Box3().setFromObject(obj);
                const objHeight = objBox.max.y - objBox.min.y;
                const isAtFeetLevel = objBox.max.y <= (this.enemy.position.y + 0.1);
                const isHorizontalPlane = objHeight < 0.5;
                const isLowObject = objHeight < 1.5 && objBox.max.y < (this.enemy.position.y + 1.0);
                const isProbablyFloor = isFloor || (isHorizontalPlane && isAtFeetLevel);

                // Skip floor objects and small ground decorations
                if (isProbablyFloor || isSmallGroundObject || isDebrisOrSmallDecor || isLowObject) {
                    continue; // Skip these objects
                }
                
                // Special case: Skip pillars if boss is Chronarch (floating boss)
                const isPillar = obj.name && obj.name.toLowerCase().includes('pillar');
                if (isPillar && this.enemy.userData.isFloating) {
                    // Chronarch floats above pillars
                    continue;
                }

                try {
                    // Check for collision
                    if (bossBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`[BossCombatAI] Collision check error:`, error);
                }
            }
        }

        // Apply movement only if no collision
        if (canMove) {
            console.log(`[BossCombatAI] Movement successful - Old position: (${this.enemy.position.x.toFixed(2)}, ${this.enemy.position.y.toFixed(2)}, ${this.enemy.position.z.toFixed(2)}), New position: (${newPosition.x.toFixed(2)}, ${newPosition.y.toFixed(2)}, ${newPosition.z.toFixed(2)})`);
            this.enemy.position.copy(newPosition);
            
            // Reset stuck timer on successful movement
            if (this.stuckTimer) {
                this.stuckTimer = 0;
            }
            
            // Apply floor curvature adaptation for boss (skip for floating bosses like Chronarch)
            if (!this.enemy.userData.isFloating) {
                this._adaptToFloorHeight();
            }
        } else {
            // CRITICAL FIX: Boss is blocked - try alternative movement
            console.log('[BossCombatAI] Boss movement blocked by collision, trying alternative movement');
            
            // Try moving with smaller step size
            const smallerStep = direction.clone().multiplyScalar(speed * 0.3); // 30% of original movement
            const alternativePosition = this.enemy.position.clone().add(smallerStep);
            
            // Quick collision check for smaller movement
            const smallerBossBox = new THREE.Box3().setFromObject(this.enemy);
            smallerBossBox.translate(smallerStep);
            
            let alternativeCanMove = true;
            if (this.scene.userData.dungeonHandler && this.scene.userData.dungeonHandler.collisionObjects) {
                for (const obj of this.scene.userData.dungeonHandler.collisionObjects) {
                    if (!obj || !obj.geometry) continue;
                    
                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (smallerBossBox.intersectsBox(objBox)) {
                        alternativeCanMove = false;
                        break;
                    }
                }
            }
            
            if (alternativeCanMove) {
                this.enemy.position.copy(alternativePosition);
                // Skip floor adaptation for floating bosses
                if (!this.enemy.userData.isFloating) {
                    this._adaptToFloorHeight();
                }
                console.log('[BossCombatAI] Used alternative smaller movement');
            } else {
                // CRITICAL FIX: Boss is completely stuck - initiate flying escape
                console.log('[BossCombatAI] 🚁 Boss completely stuck - initiating flying escape!');
                
                // Track how long boss has been stuck
                if (!this.stuckTimer) {
                    this.stuckTimer = 0;
                }
                this.stuckTimer += deltaTime;
                
                // If stuck for more than 0.5 seconds, activate flying escape immediately
                if (this.stuckTimer > 0.5) {
                    // Check teleport cooldown to prevent rapid teleporting
                    const currentTime = Date.now();
                    if (currentTime - this.lastTeleportTime > this.teleportCooldownDuration) {
                        console.log('[BossCombatAI] 🌊 FLYING ESCAPE ACTIVATED - teleporting to safe position!');
                        this._performFlyingEscape();
                        this.lastTeleportTime = currentTime;
                        this.stuckTimer = 0; // Reset stuck timer
                    } else {
                        console.log('[BossCombatAI] ⏳ Flying escape on cooldown, waiting...');
                    }
                }
            }
        }

        // Update animation state if boss has animation handler
        if (this.enemy.userData && this.enemy.userData.animationHandler) {
            if (speed > 0.1 && canMove) {
                // Boss is moving - ensure appropriate animation
                if (this.enemy.userData.animationState !== 'moving') {
                    this.enemy.userData.animationState = 'moving';
                }
            }
        }

        // Face movement direction if not frozen and can move
        if (!this.enemy.userData.freezeRotation && canMove) {
            this._faceDirection(normalizedDirection);
        }
    }

    /**
     * Face a specific direction smoothly
     * @param {THREE.Vector3} direction - Direction to face
     * @private
     */
    _faceDirection(direction) {
        if (this.enemy.userData.freezeRotation || this.isKnockedBack) return;

        if (direction && direction.lengthSq() > 0.001) {
            const targetPos = this.enemy.position.clone().add(direction);
            this.enemy.lookAt(targetPos);
        }
    }

    /**
     * Perform flying escape when boss is stuck
     * Teleports boss to a safe position with flying animation
     * @private
     */
    _performFlyingEscape() {
        console.log('[BossCombatAI] 🚁 FLYING ESCAPE: Finding safe teleport position...');
        
        // Trigger flying animation immediately
        if (this.enemy.userData && this.enemy.userData.animationHandler) {
            console.log('[BossCombatAI] 🌊 Activating flying animation for escape');
            this.enemy.userData.animationHandler.triggerAttack('flying');
        }
        
        // Find a safe position around the player
        const playerPos = this.player ? this.player.position.clone() : new THREE.Vector3(0, 0, 0);
        const safePositions = [];
        
        // Generate potential safe positions in a circle around player
        const radius = 8.0; // Safe distance from player
        const numPositions = 8;
        
        for (let i = 0; i < numPositions; i++) {
            const angle = (i / numPositions) * Math.PI * 2;
            const testPos = new THREE.Vector3(
                playerPos.x + Math.cos(angle) * radius,
                playerPos.y + 3.5, // Floating height
                playerPos.z + Math.sin(angle) * radius
            );
            
            // Check if position is within floor bounds AND not colliding with walls
            if (this.floorBounds) {
                const buffer = 4.0; // Increased buffer to stay well away from walls
                const inBounds = testPos.x >= (this.floorBounds.min.x + buffer) &&
                               testPos.x <= (this.floorBounds.max.x - buffer) &&
                               testPos.z >= (this.floorBounds.min.z + buffer) &&
                               testPos.z <= (this.floorBounds.max.z - buffer);
                
                if (inBounds) {
                    // Additional wall collision check
                    const isWallFree = this._checkPositionForWallCollision(testPos);
                    if (isWallFree) {
                        safePositions.push(testPos);
                        console.log(`[BossCombatAI] ✅ Safe position found: (${testPos.x.toFixed(2)}, ${testPos.z.toFixed(2)})`);
                    } else {
                        console.log(`[BossCombatAI] ❌ Position blocked by walls: (${testPos.x.toFixed(2)}, ${testPos.z.toFixed(2)})`);
                    }
                }
            } else {
                // No floor bounds available, use basic wall check
                const isWallFree = this._checkPositionForWallCollision(testPos);
                if (isWallFree) {
                    safePositions.push(testPos);
                }
            }
        }
        
        // Choose a random safe position
        if (safePositions.length > 0) {
            const targetPos = safePositions[Math.floor(Math.random() * safePositions.length)];
            console.log(`[BossCombatAI] 🎯 Starting smooth flight to position: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)}, ${targetPos.z.toFixed(2)})`);
            
            // Start smooth flying movement
            this.isFlying = true;
            this.flyingStartPos = this.enemy.position.clone();
            this.flyingTargetPos = targetPos.clone();
            this.flyingStartTime = performance.now();
            
            console.log('[BossCombatAI] 🌊 Smooth flying movement initiated!');
        } else {
            // Fallback 1: Try closer positions near arena center
            console.log('[BossCombatAI] ⚠️ No safe perimeter positions found, trying center area');
            const centerSafePositions = [];
            
            // Try positions closer to center with smaller radius
            const smallRadius = 4.0;
            for (let i = 0; i < 4; i++) {
                const angle = (i / 4) * Math.PI * 2;
                const testPos = new THREE.Vector3(
                    Math.cos(angle) * smallRadius,
                    3.5, // Floating height
                    Math.sin(angle) * smallRadius
                );
                
                if (this._checkPositionForWallCollision(testPos)) {
                    centerSafePositions.push(testPos);
                }
            }
            
            if (centerSafePositions.length > 0) {
                const targetPos = centerSafePositions[Math.floor(Math.random() * centerSafePositions.length)];
                console.log(`[BossCombatAI] 🎯 Using center position: (${targetPos.x.toFixed(2)}, ${targetPos.y.toFixed(2)}, ${targetPos.z.toFixed(2)})`);
                
                this.isFlying = true;
                this.flyingStartPos = this.enemy.position.clone();
                this.flyingTargetPos = targetPos.clone();
                this.flyingStartTime = performance.now();
            } else {
                // Fallback 2: Emergency center position (should always be safe)
                console.log('[BossCombatAI] 🚨 EMERGENCY: Flying to absolute center position');
                const centerPos = new THREE.Vector3(0, 3.5, 0);
                
                this.isFlying = true;
                this.flyingStartPos = this.enemy.position.clone();
                this.flyingTargetPos = centerPos.clone();
                this.flyingStartTime = performance.now();
            }
        }
    }

    /**
     * Update smooth flying movement
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateFlyingMovement(deltaTime) {
        if (!this.isFlying || !this.flyingStartPos || !this.flyingTargetPos) {
            this.isFlying = false;
            return;
        }

        const currentTime = performance.now();
        const elapsed = currentTime - this.flyingStartTime;
        const progress = Math.min(elapsed / this.flyingDuration, 1.0);

        // Use smooth easing function for natural flying motion
        const easeInOutQuad = (t) => {
            return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        };
        const smoothProgress = easeInOutQuad(progress);

        // Interpolate position with arc motion for flying effect
        const currentPos = new THREE.Vector3();
        currentPos.lerpVectors(this.flyingStartPos, this.flyingTargetPos, smoothProgress);
        
        // Add vertical arc for realistic flying motion
        const arcHeight = 2.0; // Height of arc above straight line
        const arcProgress = Math.sin(smoothProgress * Math.PI); // Sine wave for arc
        currentPos.y += arcHeight * arcProgress;

        // Update boss position
        this.enemy.position.copy(currentPos);

        // Face movement direction during flight
        if (progress < 0.95) { // Stop rotating near the end
            const direction = new THREE.Vector3().subVectors(this.flyingTargetPos, this.flyingStartPos).normalize();
            if (direction.lengthSq() > 0.001) {
                const targetLookAt = this.enemy.position.clone().add(direction);
                this.enemy.lookAt(targetLookAt);
            }
        }

        // Check if flying is complete
        if (progress >= 1.0) {
            console.log('[BossCombatAI] ✅ Flying movement complete - boss landed at target position!');
            
            // Ensure final position is exactly the target
            this.enemy.position.copy(this.flyingTargetPos);
            
            // Update base position for animation system
            if (this.enemy.userData) {
                this.enemy.userData.basePositionY = this.flyingTargetPos.y;
            }
            
            // Face the player after landing
            if (this.player) {
                this._faceTarget(this.player.position);
            }
            
            // Reset flying state
            this.isFlying = false;
            this.flyingStartPos = null;
            this.flyingTargetPos = null;
            this.flyingStartTime = 0;
            
            console.log('[BossCombatAI] 🏁 Boss ready for normal combat behavior');
        }
    }

    /**
     * Check if a position is free from wall collisions
     * @param {THREE.Vector3} position - Position to check
     * @returns {boolean} - True if position is wall-free
     * @private
     */
    _checkPositionForWallCollision(position) {
        if (!this.collisionObjects || this.collisionObjects.length === 0) {
            return true; // No collision objects to check
        }

        // Create a bounding box for the boss at the test position
        const bossRadius = 2.5; // Boss is large
        const bossBox = new THREE.Box3().setFromCenterAndSize(
            position,
            new THREE.Vector3(bossRadius * 2, bossRadius * 2, bossRadius * 2)
        );

        // Check collision with all objects
        for (const obj of this.collisionObjects) {
            if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                continue; // Skip objects without geometry
            }

            // Skip floor objects - boss should float above them
            const isFloor = obj.userData?.isFloor === true || 
                           obj.name?.toLowerCase().includes('floor') ||
                           obj.name?.toLowerCase().includes('ground') ||
                           obj.name?.toLowerCase().includes('terrain');

            // Check for small ground objects
            const isSmallGroundObject = obj.userData?.isGroundDecoration === true || 
                                      obj.userData?.isSmallObject === true ||
                                      obj.userData?.isFloorDecoration === true;
            
            // Check for specific small decorations
            const isDebrisOrSmallDecor = obj.name && (
                obj.name.toLowerCase().includes('debris') ||
                obj.name.toLowerCase().includes('rubble') ||
                obj.name.toLowerCase().includes('candle') ||
                obj.name.toLowerCase().includes('bottle') ||
                obj.name.toLowerCase().includes('potion') ||
                obj.name.toLowerCase().includes('book') ||
                obj.name.toLowerCase().includes('scroll') ||
                obj.name.toLowerCase().includes('crystal_small') ||
                obj.name.toLowerCase().includes('stone_small') ||
                obj.name.toLowerCase().includes('vase') ||
                obj.name.toLowerCase().includes('moss')
            );

            if (isFloor || isSmallGroundObject || isDebrisOrSmallDecor) {
                continue; // Skip floor and small decoration objects
            }

            try {
                const objBox = new THREE.Box3().setFromObject(obj);
                
                // Check height - only consider objects that could block flying boss
                const objHeight = objBox.max.y - objBox.min.y;
                const isAtFeetLevel = objBox.max.y <= (position.y - 1.0); // Below flying height
                const isHorizontalPlane = objHeight < 0.5;
                const isLowObject = objHeight < 1.5 && objBox.max.y < (position.y + 1.0);
                const isProbablyFloor = isFloor || (isHorizontalPlane && isAtFeetLevel);

                if (isProbablyFloor || isLowObject) {
                    continue; // Skip floor-like and low objects
                }
                
                // Special case: Skip pillars for floating bosses like Chronarch
                const isPillar = obj.name && obj.name.toLowerCase().includes('pillar');
                if (isPillar && this.enemy.userData.isFloating) {
                    continue;
                }

                // Check for collision with walls/obstacles
                if (bossBox.intersectsBox(objBox)) {
                    console.log(`[BossCombatAI] Wall collision detected with: ${obj.name || 'unnamed'}`);
                    return false; // Collision detected
                }
            } catch (error) {
                console.warn(`[BossCombatAI] Error checking collision with object:`, error);
            }
        }

        return true; // No collisions detected
    }

    /**
     * Clean up resources
     */
    dispose() {
        // Dispose boss controller
        if (this.bossController) {
            this.bossController.dispose();
            this.bossController = null;
        }

        // Call parent dispose if it exists
        if (super.dispose) {
            super.dispose();
        }
    }
}
