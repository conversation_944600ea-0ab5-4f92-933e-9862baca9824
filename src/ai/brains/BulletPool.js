/**
 * Bullet Pool System
 * Pre-allocates bullet objects to improve performance by avoiding
 * constant creation and destruction of projectiles during intense boss fights
 */
import * as THREE from 'three';

export class BulletPool {
    constructor(poolSize = 1000, dungeonHandler) {
        this.poolSize = poolSize;
        this.dungeonHandler = dungeonHandler;
        this.available = [];
        this.active = [];
        this.totalCreated = 0;
        this.totalReused = 0;
        this.peakActive = 0;
        
        // Pre-allocate bullets
        this.initializePool();
        
        console.log(`[BulletPool] Initialized with ${poolSize} pre-allocated bullets`);
    }
    
    /**
     * Pre-allocate bullet objects
     */
    initializePool() {
        for (let i = 0; i < this.poolSize; i++) {
            const bullet = this.createBulletObject();
            this.available.push(bullet);
        }
        console.log(`[BulletPool] Pre-allocated ${this.poolSize} bullet objects`);
    }
    
    /**
     * Create a single bullet object
     */
    createBulletObject() {
        const bullet = {
            // Visual components
            mesh: null,
            geometry: null,
            material: null,
            
            // Physics properties
            position: new THREE.Vector3(),
            velocity: new THREE.Vector3(),
            acceleration: new THREE.Vector3(),
            
            // Lifecycle properties
            isActive: false,
            spawnTime: 0,
            lifetime: 10000, // 10 seconds default
            health: 1,
            damage: 1,
            
            // Behavior properties
            projectileType: 'shadow_bolt',
            owner: null,
            speed: 6.0,
            hasHitTarget: false,
            trailPositions: [],
            
            // Visual effects
            glowIntensity: 1.0,
            pulsePhase: 0,
            rotationSpeed: 0,
            
            // Pool management
            poolId: this.totalCreated++
        };
        
        return bullet;
    }
    
    /**
     * Get a bullet from the pool
     */
    acquire(position, velocity, projectileData) {
        let bullet;
        
        if (this.available.length > 0) {
            // Reuse existing bullet
            bullet = this.available.pop();
            this.totalReused++;
        } else {
            // Pool exhausted - create new bullet (should be rare)
            bullet = this.createBulletObject();
            console.warn(`[BulletPool] Pool exhausted! Created new bullet (${this.totalCreated})`);
        }
        
        // Initialize bullet properties
        this.initializeBullet(bullet, position, velocity, projectileData);
        
        // Add to active list
        this.active.push(bullet);
        this.peakActive = Math.max(this.peakActive, this.active.length);
        
        return bullet;
    }
    
    /**
     * Initialize bullet with new data
     */
    initializeBullet(bullet, position, velocity, projectileData) {
        // Reset position and movement
        bullet.position.copy(position);
        bullet.velocity.copy(velocity);
        bullet.acceleration.set(0, 0, 0);
        
        // Reset lifecycle
        bullet.isActive = true;
        bullet.spawnTime = performance.now();
        bullet.hasHitTarget = false;
        bullet.trailPositions = [];
        
        // Apply projectile data
        if (projectileData) {
            bullet.projectileType = projectileData.projectileType || 'shadow_bolt';
            bullet.health = projectileData.health || 1;
            bullet.damage = projectileData.damage || 1;
            bullet.speed = projectileData.projectileSpeed || 6.0;
            bullet.owner = projectileData.owner || null;
            bullet.lifetime = projectileData.lifetime || 10000;
        }
        
        // Create or update visual mesh
        this.updateBulletVisuals(bullet);
        
        // Reset visual effects
        bullet.glowIntensity = 1.0 + Math.random() * 0.5;
        bullet.pulsePhase = Math.random() * Math.PI * 2;
        bullet.rotationSpeed = (Math.random() - 0.5) * 0.1;
    }
    
    /**
     * Update bullet's visual representation
     */
    updateBulletVisuals(bullet) {
        // Create mesh if it doesn't exist
        if (!bullet.mesh) {
            bullet.geometry = new THREE.SphereGeometry(0.15, 8, 6);
            bullet.material = new THREE.MeshBasicMaterial({
                color: this.getProjectileColor(bullet.projectileType),
                emissive: this.getProjectileColor(bullet.projectileType),
                emissiveIntensity: 0.5,
                transparent: true,
                opacity: 0.9
            });
            bullet.mesh = new THREE.Mesh(bullet.geometry, bullet.material);
            bullet.mesh.userData.bullet = bullet; // Reference back to bullet data
        } else {
            // Update existing mesh
            const color = this.getProjectileColor(bullet.projectileType);
            bullet.material.color.set(color);
            bullet.material.emissive.set(color);
        }
        
        // Update position
        bullet.mesh.position.copy(bullet.position);
        
        // Add to scene if not already added
        if (!bullet.mesh.parent && this.dungeonHandler.scene) {
            this.dungeonHandler.scene.add(bullet.mesh);
        }
    }
    
    /**
     * Get color for projectile type
     */
    getProjectileColor(projectileType) {
        const colors = {
            'shadow_bolt': 0x4a0080,
            'ice_spike': 0x00aaff,
            'energy_blast': 0xffaa00,
            'lightning_bolt': 0xffff00,
            'screamer_skulls': 0xff0040,
            'blood_threads': 0x880000,
            'plasma_bolt': 0x00ff88
        };
        return colors[projectileType] || 0x4a0080;
    }
    
    /**
     * Return bullet to pool
     */
    release(bullet) {
        if (!bullet || !bullet.isActive) return;
        
        // Mark as inactive
        bullet.isActive = false;
        
        // Remove from scene
        if (bullet.mesh && bullet.mesh.parent) {
            this.dungeonHandler.scene.remove(bullet.mesh);
        }
        
        // Remove from active list
        const activeIndex = this.active.indexOf(bullet);
        if (activeIndex > -1) {
            this.active.splice(activeIndex, 1);
        }
        
        // Return to available pool
        this.available.push(bullet);
    }
    
    /**
     * Update all active bullets
     */
    update(deltaTime) {
        const currentTime = performance.now();
        const bulletsToRelease = [];
        
        this.active.forEach(bullet => {
            if (!bullet.isActive) {
                bulletsToRelease.push(bullet);
                return;
            }
            
            // Check lifetime
            if (currentTime - bullet.spawnTime > bullet.lifetime) {
                bulletsToRelease.push(bullet);
                return;
            }
            
            // Update position
            bullet.velocity.add(bullet.acceleration.clone().multiplyScalar(deltaTime));
            bullet.position.add(bullet.velocity.clone().multiplyScalar(deltaTime));
            
            // Update mesh position
            if (bullet.mesh) {
                bullet.mesh.position.copy(bullet.position);
                
                // Update visual effects
                bullet.pulsePhase += deltaTime * 0.005;
                const pulse = Math.sin(bullet.pulsePhase) * 0.3 + 0.7;
                bullet.material.emissiveIntensity = bullet.glowIntensity * pulse;
                
                // Rotation
                bullet.mesh.rotation.x += bullet.rotationSpeed * deltaTime;
                bullet.mesh.rotation.y += bullet.rotationSpeed * deltaTime * 0.7;
            }
            
            // Simple collision check with arena bounds
            if (Math.abs(bullet.position.x) > 25 || Math.abs(bullet.position.z) > 25 || bullet.position.y < -5 || bullet.position.y > 15) {
                bulletsToRelease.push(bullet);
            }
        });
        
        // Release expired bullets
        bulletsToRelease.forEach(bullet => this.release(bullet));
    }
    
    /**
     * Get pool statistics
     */
    getStats() {
        return {
            totalCreated: this.totalCreated,
            totalReused: this.totalReused,
            available: this.available.length,
            active: this.active.length,
            peakActive: this.peakActive,
            poolEfficiency: this.totalReused / Math.max(1, this.totalCreated + this.totalReused)
        };
    }
    
    /**
     * Log performance statistics
     */
    logStats() {
        const stats = this.getStats();
        console.log('[BulletPool] Performance Stats:', {
            'Pool Efficiency': `${(stats.poolEfficiency * 100).toFixed(1)}%`,
            'Active Bullets': stats.active,
            'Available Bullets': stats.available,
            'Peak Active': stats.peakActive,
            'Total Created': stats.totalCreated,
            'Total Reused': stats.totalReused
        });
    }
    
    /**
     * Clean up all bullets and reset pool
     */
    cleanup() {
        // Release all active bullets
        [...this.active].forEach(bullet => this.release(bullet));
        
        // Dispose of all geometries and materials
        this.available.forEach(bullet => {
            if (bullet.geometry) {
                bullet.geometry.dispose();
            }
            if (bullet.material) {
                bullet.material.dispose();
            }
        });
        
        this.available = [];
        this.active = [];
        
        console.log('[BulletPool] Cleaned up and reset pool');
    }
}