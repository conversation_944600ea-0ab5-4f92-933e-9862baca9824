/**
 * Void<PERSON>ord Boss Controller
 * Specialized boss controller for the Void Lord - a cosmic horror with black hole head
 * and energy strain attacks across 3 phases
 */
import * as THREE from 'three';
import { VoidLordAttackPatterns } from './VoidLordAttackPatterns.js';

export class VoidLordBossController {
    constructor(enemy, dungeonHandler, audioManager) {
        this.enemy = enemy;
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;
        
        // Boss state
        this.maxHealth = enemy.userData.health || 100;
        this.currentHealth = this.maxHealth;
        this.currentPhase = 1;
        this.isActive = false;
        this.isDead = false;
        
        // Phase thresholds
        this.phaseThresholds = {
            phase1: 1.0,    // 100% - 70%
            phase2: 0.7,    // 70% - 35%  
            phase3: 0.35,   // 35% - 0%
            desperation: 0.15 // 15% - Final desperation
        };
        
        // Desperation mode
        this.isDesperationMode = false;
        this.desperationStartTime = 0;
        
        // Phase transition tracking
        this.phaseTransitioning = false;
        
        // Attack system
        console.log('[VoidLordBossController] Initializing attack patterns...');
        this.attackPatterns = new VoidLordAttackPatterns(enemy, dungeonHand<PERSON>, dungeonHandler.player);
        
        // Attack timing
        this.lastAttackTime = 0;
        this.attackCooldowns = {
            1: 3.5,  // Phase 1: 3.5 seconds between attacks
            2: 2.5,  // Phase 2: 2.5 seconds
            3: 1.8   // Phase 3: 1.8 seconds
        };
        this.currentAttackName = null; // Track current attack for display
        
        // Special mechanics
        this.voidShield = null;
        this.isShielded = false;
        this.shieldHealth = 20;
        this.energyAura = null;
        
        // Speech system
        this.speechBubble = null;
        this.speeches = {
            intro: "Your reality ends here, mortal...",
            phase1To2: "The void hungers for your soul!",
            phase2To3: "I am beyond your comprehension!",
            desperation: "IMPOSSIBLE! THE VOID IS ETERNAL!",
            shieldUp: "Energy shields activated!",
            defeat: "The void... consumes... all..."
        };
        
        // Visual effects
        this.blackHoleParticles = [];
        this.realityDistortion = 0;
        
        console.log('[VoidLordBossController] Initialized for Void Lord boss');
    }
    
    /**
     * Start the boss fight
     */
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.currentPhase = 1;
        this.lastAttackTime = performance.now();
        
        // Sync initial phase with AI brain
        if (this.enemy.userData.aiBrain) {
            this.enemy.userData.aiBrain.currentPhase = 1;
        }
        
        // Initial speech
        this.showSpeechBubble(this.speeches.intro, 3000);
        
        // Create energy aura effect
        this.createEnergyAura();
        
        // Start combat after intro
        setTimeout(() => {
            this.lastAttackTime = performance.now();
            console.log('[VoidLordBossController] Boss fight started - Phase 1');
        }, 3500);
    }
    
    /**
     * Update controller each frame
     */
    update(deltaTime) {
        if (!this.isActive || this.isDead) return;
        
        const currentTime = performance.now();
        
        // Update health from AI brain
        if (this.enemy.userData.aiBrain && this.enemy.userData.aiBrain.currentHealth !== undefined) {
            this.currentHealth = this.enemy.userData.aiBrain.currentHealth;
            this.maxHealth = this.enemy.userData.aiBrain.maxHealth || this.maxHealth;
        }
        
        // Check phase transitions
        this.checkPhaseTransitions();
        
        // Update attack patterns
        this.updateAttackPatterns(deltaTime);
        
        // Update shield if active
        this.updateShield(deltaTime);
        
        // Update visual effects
        this.updateVisualEffects(deltaTime);
        
        // Update attack patterns system
        if (this.attackPatterns) {
            this.attackPatterns.update(deltaTime);
        }
        
        // Update animation state
        if (this.enemy.userData.animationHandler) {
            // Determine actual state based on what the boss is doing
            let aiState = 'IDLE';
            if (this.attackPatterns && this.attackPatterns.currentPattern) {
                aiState = 'ATTACKING';
            } else if (this.phaseTransitioning) {
                aiState = 'PHASE_TRANSITION';
            } else if (this.enemy.userData.aiBrain?.currentState) {
                aiState = this.enemy.userData.aiBrain.currentState;
            }
            this.enemy.userData.animationHandler.update(deltaTime, aiState);
        }
    }
    
    /**
     * Check for phase transitions
     */
    checkPhaseTransitions() {
        const healthPercent = this.currentHealth / this.maxHealth;
        let newPhase = this.currentPhase;
        
        // Check desperation mode
        if (healthPercent <= this.phaseThresholds.desperation && !this.isDesperationMode) {
            this.activateDesperationMode();
        }
        
        // Check phase transitions
        if (healthPercent <= this.phaseThresholds.phase3 && this.currentPhase < 3) {
            newPhase = 3;
        } else if (healthPercent <= this.phaseThresholds.phase2 && this.currentPhase < 2) {
            newPhase = 2;
        }
        
        if (newPhase !== this.currentPhase) {
            this.transitionToPhase(newPhase);
        }
    }
    
    /**
     * Transition to new phase
     */
    transitionToPhase(newPhase) {
        console.log(`[VoidLordBossController] Phase transition: ${this.currentPhase} -> ${newPhase}`);
        
        this.phaseTransitioning = true;
        this.currentPhase = newPhase;
        
        // Sync phase with AI brain (BossCombatAI uses 0-based phases)
        if (this.enemy.userData.aiBrain) {
            this.enemy.userData.aiBrain.currentPhase = newPhase;
        }
        
        // Update animation handler
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.setPhase(newPhase);
            this.enemy.userData.animationHandler.setState('PHASE_TRANSITION');
        }
        
        // Show phase speech
        if (newPhase === 2) {
            this.showSpeechBubble(this.speeches.phase1To2, 2500);
            // Activate shield in phase 2
            this.activateVoidShield();
        } else if (newPhase === 3) {
            this.showSpeechBubble(this.speeches.phase2To3, 2500);
            // Increase reality distortion in phase 3
            this.increaseRealityDistortion();
        }
        
        // Visual effects
        this.triggerPhaseTransitionEffects(newPhase);
        
        // Clear phase transitioning flag after 2 seconds
        setTimeout(() => {
            this.phaseTransitioning = false;
        }, 2000);
    }
    
    /**
     * Activate desperation mode
     */
    activateDesperationMode() {
        console.log('[VoidLordBossController] DESPERATION MODE ACTIVATED!');
        
        this.isDesperationMode = true;
        this.desperationStartTime = performance.now();
        
        // Dramatic speech
        this.showSpeechBubble(this.speeches.desperation, 3000);
        
        // Intense effects
        this.addIntenseScreenShake(0.8, 2000);
        
        // Update attack cooldowns
        this.attackCooldowns[1] *= 0.7;
        this.attackCooldowns[2] *= 0.7;
        this.attackCooldowns[3] *= 0.7;
        
        // Enhance visual effects on tentacles
        if (this.energyTentacles) {
            this.energyTentacles.forEach(tentacle => {
                tentacle.children.forEach(segment => {
                    if (segment.material) {
                        segment.material.emissiveIntensity = 1.5;
                        segment.material.color.setHex(0xff0000); // Bright red in desperation
                    }
                });
            });
        }
    }
    
    /**
     * Update attack patterns
     */
    updateAttackPatterns(deltaTime) {
        if (this.isShielded) return; // No attacks while regenerating shield
        
        const currentTime = performance.now();
        const timeSinceLastAttack = (currentTime - this.lastAttackTime) / 1000;
        const attackCooldown = this.attackCooldowns[this.currentPhase] || 2.0;
        
        if (timeSinceLastAttack >= attackCooldown) {
            this.triggerAttackPattern();
            this.lastAttackTime = currentTime;
        }
    }
    
    /**
     * Trigger attack pattern
     */
    triggerAttackPattern() {
        if (!this.attackPatterns) return;
        
        // Calculate intensity based on health
        const healthPercent = this.currentHealth / this.maxHealth;
        const baseIntensity = 1.0 - healthPercent;
        const phaseMultiplier = this.currentPhase * 0.3;
        const intensity = Math.min(1.0, baseIntensity + phaseMultiplier);
        
        // Trigger pattern
        this.attackPatterns.triggerPattern(this.currentPhase, intensity, this.isDesperationMode);
        
        // Update current attack name from attack patterns
        if (this.attackPatterns.currentPattern) {
            this.currentAttackName = this.attackPatterns.currentPattern;
        }
    }
    
    /**
     * Activate void shield
     */
    activateVoidShield() {
        if (this.isShielded) return;
        
        this.isShielded = true;
        this.shieldHealth = 20;
        
        // Create shield visual
        this.createShieldVisual();
        
        // Show speech
        this.showSpeechBubble(this.speeches.shieldUp, 2000);
        
        console.log('[VoidLordBossController] Void shield activated');
    }
    
    /**
     * Create shield visual effect
     */
    createShieldVisual() {
        if (this.voidShield) {
            this.dungeonHandler.scene.remove(this.voidShield);
        }
        
        // Create dark energy shield
        const shieldGeometry = new THREE.SphereGeometry(4.0, 32, 24);
        const shieldMaterial = new THREE.MeshBasicMaterial({
            color: 0x000000,
            transparent: true,
            opacity: 0.4,
            side: THREE.DoubleSide,
            emissive: 0xff0000,
            emissiveIntensity: 0.5
        });
        
        this.voidShield = new THREE.Mesh(shieldGeometry, shieldMaterial);
        this.voidShield.position.copy(this.enemy.position);
        
        this.dungeonHandler.scene.add(this.voidShield);
    }
    
    /**
     * Update shield
     */
    updateShield(deltaTime) {
        if (!this.isShielded || !this.voidShield) return;
        
        // Update position
        this.voidShield.position.copy(this.enemy.position);
        this.voidShield.position.y += 2;
        
        // Pulsing effect
        const time = performance.now() * 0.001;
        this.voidShield.material.opacity = 0.3 + Math.sin(time * 3) * 0.1;
        this.voidShield.rotation.y += deltaTime * 1.5;
        
        // Check if shield broken
        if (this.shieldHealth <= 0) {
            this.deactivateShield();
        }
    }
    
    /**
     * Deactivate shield
     */
    deactivateShield() {
        this.isShielded = false;
        
        if (this.voidShield) {
            this.dungeonHandler.scene.remove(this.voidShield);
            this.voidShield = null;
        }
        
        console.log('[VoidLordBossController] Shield deactivated');
    }
    
    /**
     * Create energy aura with tentacles
     */
    createEnergyAura() {
        // Create energy tentacles that emerge from boss's back
        this.energyTentacles = [];
        const tentacleCount = 6; // Fewer tentacles for cleaner look
        
        for (let i = 0; i < tentacleCount; i++) {
            // Create arc behind the boss (from back)
            const angle = (i / (tentacleCount - 1)) * Math.PI - Math.PI/2; // -90° to +90°
            const tentacle = this.createBackEnergyTentacle(angle);
            this.energyTentacles.push(tentacle);
            this.dungeonHandler.scene.add(tentacle);
        }
        
        console.log('[VoidLordBossController] Created energy tentacles from back');
    }
    
    /**
     * Create energy tentacle from boss's back
     */
    createBackEnergyTentacle(baseAngle) {
        const tentacleGroup = new THREE.Group();
        const segmentCount = 8; // Shorter tentacles
        const voxelSize = 0.5; // Voxel unit size
        
        // Create tentacle segments using clean voxel design
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1);
            
            // Simple tapering - one clean voxel per segment
            const baseSize = 1.5;
            const tipSize = 0.4;
            const segmentSize = (baseSize - t * (baseSize - tipSize)) * voxelSize;
            
            // Single clean voxel per segment
            const geometry = new THREE.BoxGeometry(segmentSize, voxelSize * 1.2, segmentSize);
            const material = new THREE.MeshBasicMaterial({
                color: 0xff0000,
                transparent: true,
                opacity: 0.6 - t * 0.2, // Semi-transparent energy field
                emissive: 0xff0000,
                emissiveIntensity: 0.6 + t * 0.4 // Brighter at tips
            });
            
            const segment = new THREE.Mesh(geometry, material);
            segment.position.y = i * voxelSize * 1.1; // Slight spacing between voxels
            segment.userData.segmentIndex = i;
            
            tentacleGroup.add(segment);
        }
        
        // Position deeper into the ground
        tentacleGroup.position.copy(this.enemy.position);
        tentacleGroup.position.y = -1.5; // Sink into the ground
        tentacleGroup.userData.baseAngle = baseAngle;
        tentacleGroup.userData.timeOffset = Math.random() * Math.PI * 2;
        
        return tentacleGroup;
    }
    
    /**
     * Create ground energy tentacle (for attack pattern)
     */
    createGroundEnergyTentacle(baseAngle, radius) {
        const tentacleGroup = new THREE.Group();
        const segmentCount = 12;
        const segmentLength = 1.0;
        
        // Create tentacle segments
        for (let i = 0; i < segmentCount; i++) {
            const t = i / (segmentCount - 1);
            const segmentRadius = 0.5 - t * 0.3; // Taper towards end
            
            const geometry = new THREE.CylinderGeometry(segmentRadius, segmentRadius * 0.8, segmentLength, 8);
            const material = new THREE.MeshBasicMaterial({
                color: i % 2 === 0 ? 0xff0000 : 0x990000,
                transparent: true,
                opacity: 0.8 - t * 0.3,
                emissive: 0xff0000,
                emissiveIntensity: 0.5 + t * 0.5
            });
            
            const segment = new THREE.Mesh(geometry, material);
            segment.position.y = i * segmentLength;
            segment.userData.segmentIndex = i;
            segment.userData.baseAngle = baseAngle;
            
            tentacleGroup.add(segment);
        }
        
        // Position tentacle away from boss center
        const offsetX = Math.cos(baseAngle) * radius;
        const offsetZ = Math.sin(baseAngle) * radius;
        tentacleGroup.position.set(
            this.enemy.position.x + offsetX,
            this.enemy.position.y + 2, // Start a bit above ground
            this.enemy.position.z + offsetZ
        );
        
        tentacleGroup.userData.baseAngle = baseAngle;
        tentacleGroup.userData.radius = radius;
        tentacleGroup.userData.timeOffset = Math.random() * Math.PI * 2;
        
        return tentacleGroup;
    }
    
    /**
     * Update visual effects
     */
    updateVisualEffects(deltaTime) {
        const time = performance.now() * 0.001;
        
        // Update energy tentacles
        if (this.energyTentacles) {
            this.energyTentacles.forEach((tentacle, index) => {
                // Update tentacle position to follow boss
                const baseAngle = tentacle.userData.baseAngle;
                
                // Ground tentacles - attached to boss position but sunk into ground
                tentacle.position.x = this.enemy.position.x;
                tentacle.position.z = this.enemy.position.z;
                tentacle.position.y = -1.5; // Keep sunk into ground
                
                // Animate tentacle movement
                const timeOffset = tentacle.userData.timeOffset;
                
                // Wave motion for tentacle
                tentacle.children.forEach((segment, segIndex) => {
                    const t = segIndex / tentacle.children.length;
                    
                    // Curve forward from back
                    const forwardCurve = t * 4; // How much to curve forward
                    const sideSway = Math.sin(baseAngle) * 2; // Side offset based on angle
                    
                    // Sinuous movement
                    const wavePhase = time * 2 + timeOffset + segIndex * 0.3;
                    const waveAmplitude = 0.5 + t * 1; // Less movement, more controlled
                    
                    segment.position.x = sideSway + Math.sin(wavePhase) * waveAmplitude * t;
                    segment.position.z = -forwardCurve + Math.cos(wavePhase * 0.8) * waveAmplitude * t;
                    
                    // Rotation to point forward
                    segment.rotation.x = -0.3 - t * 0.5 + Math.sin(wavePhase * 1.5) * 0.2 * t;
                    segment.rotation.z = Math.sin(baseAngle) * 0.3 + Math.cos(wavePhase * 1.2) * 0.2 * t;
                    
                    // Pulsing glow
                    if (segment.material && segment.material.emissive) {
                        const pulse = 0.5 + Math.sin(time * 3 + segIndex * 0.5) * 0.3;
                        segment.material.emissiveIntensity = 0.6 + pulse * 0.4;
                    }
                });
                
                // Overall tentacle rotation
                tentacle.rotation.y = baseAngle + Math.sin(time + timeOffset) * 0.2;
            });
        }
        
        // Update reality distortion
        if (this.realityDistortion > 0) {
            // Add to scene userData for post-processing
            if (this.dungeonHandler.scene) {
                this.dungeonHandler.scene.userData.voidLordDistortion = {
                    active: true,
                    intensity: this.realityDistortion,
                    position: this.enemy.position.clone()
                };
            }
        }
    }
    
    /**
     * Increase reality distortion for phase 3
     */
    increaseRealityDistortion() {
        this.realityDistortion = 0.5;
        console.log('[VoidLordBossController] Reality distortion increased');
    }
    
    /**
     * Trigger phase transition effects
     */
    triggerPhaseTransitionEffects(phase) {
        // Screen shake
        this.addIntenseScreenShake(0.6, 1500);
        
        // Flash effect
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.userData.phaseFlash = {
                active: true,
                startTime: performance.now(),
                duration: 500,
                color: phase === 3 ? 0xff0000 : 0xff69b4
            };
        }
    }
    
    /**
     * Show speech bubble
     */
    showSpeechBubble(text, duration = 3000) {
        if (this.speechBubble) {
            this.removeSpeechBubble();
        }
        
        this.speechBubble = document.createElement('div');
        this.speechBubble.className = 'boss-speech-bubble void-lord-speech';
        this.speechBubble.innerHTML = `
            <div class="boss-speech-content">
                <div class="boss-speech-text">${text}</div>
            </div>
        `;
        
        // Custom styling for Void Lord
        this.speechBubble.style.cssText = `
            position: absolute;
            z-index: 2000;
            pointer-events: none;
            opacity: 0;
            transform: translateX(-50%);
            transition: opacity 0.5s ease-in-out;
        `;
        
        const speechContent = this.speechBubble.querySelector('.boss-speech-content');
        speechContent.style.cssText = `
            background-color: rgba(0, 0, 0, 0.9);
            border: 3px solid #ff0000;
            border-radius: 5px;
            padding: 12px 20px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            font-weight: bold;
            color: #ff0000;
            text-align: center;
            white-space: nowrap;
            text-transform: uppercase;
            letter-spacing: 2px;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
        `;
        
        document.body.appendChild(this.speechBubble);
        this.updateSpeechBubblePosition();
        
        setTimeout(() => {
            if (this.speechBubble) {
                this.speechBubble.style.opacity = '1';
            }
        }, 100);
        
        setTimeout(() => {
            this.removeSpeechBubble();
        }, duration);
    }
    
    /**
     * Update speech bubble position
     */
    updateSpeechBubblePosition() {
        if (!this.speechBubble || !this.enemy) return;
        
        const enemyPosition = this.enemy.position.clone();
        enemyPosition.y += 8;
        
        const vector = enemyPosition.clone();
        vector.project(this.dungeonHandler.camera);
        
        const screenX = (vector.x * 0.5 + 0.5) * window.innerWidth;
        const screenY = (vector.y * -0.5 + 0.5) * window.innerHeight;
        
        this.speechBubble.style.left = `${screenX}px`;
        this.speechBubble.style.top = `${screenY}px`;
    }
    
    /**
     * Remove speech bubble
     */
    removeSpeechBubble() {
        if (this.speechBubble) {
            this.speechBubble.style.opacity = '0';
            setTimeout(() => {
                if (this.speechBubble && this.speechBubble.parentNode) {
                    this.speechBubble.parentNode.removeChild(this.speechBubble);
                }
                this.speechBubble = null;
            }, 500);
        }
    }
    
    /**
     * Add screen shake
     */
    addIntenseScreenShake(intensity, duration) {
        if (!this.dungeonHandler.camera) return;
        
        const camera = this.dungeonHandler.camera;
        const originalPosition = camera.position.clone();
        const shakeStart = performance.now();
        
        const shakeEffect = () => {
            const elapsed = performance.now() - shakeStart;
            if (elapsed >= duration) {
                camera.position.copy(originalPosition);
                return;
            }
            
            const progress = elapsed / duration;
            const currentIntensity = intensity * (1 - progress * 0.5);
            
            camera.position.x = originalPosition.x + (Math.random() - 0.5) * currentIntensity;
            camera.position.y = originalPosition.y + (Math.random() - 0.5) * currentIntensity * 0.5;
            camera.position.z = originalPosition.z + (Math.random() - 0.5) * currentIntensity;
            
            requestAnimationFrame(shakeEffect);
        };
        
        shakeEffect();
    }
    
    /**
     * Handle taking damage
     */
    takeDamage(damage) {
        if (this.isShielded && this.voidShield) {
            // Damage goes to shield first
            this.shieldHealth -= damage;
            console.log(`[VoidLordBossController] Shield took ${damage} damage, ${this.shieldHealth} remaining`);
            
            // Visual feedback
            if (this.voidShield) {
                this.voidShield.material.emissiveIntensity = 1.0;
                setTimeout(() => {
                    if (this.voidShield) {
                        this.voidShield.material.emissiveIntensity = 0.5;
                    }
                }, 100);
            }
            
            return false; // No damage to boss
        }
        
        this.currentHealth -= damage;
        return true;
    }
    
    /**
     * Check if can take damage
     */
    canTakeDamage() {
        return true; // Can always be hit, but shield absorbs damage
    }
    
    /**
     * Handle death
     */
    die() {
        if (this.isDead) return;
        
        this.isDead = true;
        this.isActive = false;
        
        // Death speech
        this.showSpeechBubble(this.speeches.defeat, 3000);
        
        // Trigger death animation
        if (this.enemy.userData.animationHandler) {
            this.enemy.userData.animationHandler.setState('DYING');
        }
        
        // Clean up effects after delay
        setTimeout(() => {
            this.cleanup();
        }, 4000);
        
        console.log('[VoidLordBossController] Void Lord defeated!');
    }
    
    /**
     * Stop the controller
     */
    stop() {
        this.isActive = false;
        
        // Clean up effects
        this.deactivateShield();
        this.removeSpeechBubble();
        
        if (this.attackPatterns) {
            this.attackPatterns.cleanup();
        }
        
        console.log('[VoidLordBossController] Boss fight ended');
    }
    
    /**
     * Get current boss state for debug display
     */
    getCurrentState() {
        if (this.isDead) return 'DEAD';
        if (!this.isActive) return 'INACTIVE';
        if (this.phaseTransitioning) return 'PHASE_TRANSITION';
        if (this.attackPatterns && this.attackPatterns.currentPattern) {
            return 'ATTACKING';
        }
        if (this.isShielded) return 'SHIELDED';
        if (this.isDesperationMode) return 'DESPERATION';
        
        // Check animation state
        const animHandler = this.enemy.userData.animationHandler;
        if (animHandler && animHandler.currentState) {
            const animState = animHandler.currentState;
            if (animState === 'ATTACK' || animState === 'ATTACKING') {
                return 'ATTACKING';
            }
        }
        
        // Default to AI brain state or IDLE
        if (this.enemy.userData.aiBrain?.currentState) {
            return this.enemy.userData.aiBrain.currentState;
        }
        return 'IDLE';
    }
    
    /**
     * Clean up resources
     */
    cleanup() {
        this.stop();
        
        // Remove energy tentacles
        if (this.energyTentacles) {
            this.energyTentacles.forEach(tentacle => {
                if (tentacle.parent) {
                    tentacle.parent.remove(tentacle);
                }
                // Clean up geometry and materials
                tentacle.children.forEach(segment => {
                    if (segment.geometry) segment.geometry.dispose();
                    if (segment.material) segment.material.dispose();
                });
            });
            this.energyTentacles = [];
        }
        
        // Clear scene effects
        if (this.dungeonHandler.scene) {
            this.dungeonHandler.scene.userData.voidLordDistortion = null;
        }
        
        if (this.attackPatterns) {
            this.attackPatterns.cleanup();
            this.attackPatterns = null;
        }
    }
    
    /**
     * Get current state for debug display
     */
    getCurrentState() {
        if (!this.isActive) return 'INACTIVE';
        
        // If currently attacking, show the attack name
        if (this.currentAttackName) {
            // Format attack name from snake_case to readable format
            const attackName = this.currentAttackName
                .replace(/_/g, ' ')
                .split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');
            return `ATTACKING: ${attackName}`;
        }
        
        // Other states
        if (this.phaseTransitioning) return 'PHASE TRANSITION';
        if (this.isShielded) return 'SHIELDED';
        if (this.isDesperationMode) return 'DESPERATION MODE';
        
        // Default to movement state
        return 'PURSUING';
    }
    
    /**
     * Getter for health (for debug compatibility)
     */
    get health() {
        return this.currentHealth;
    }
}