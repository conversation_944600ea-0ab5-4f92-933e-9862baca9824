/**
 * Fleeing Combat AI implementation
 * Specializes in running away from the player
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';
import { PathfindingSystem } from '../PathfindingSystem.js';

export class FleeingCombatAI extends AIBrain {
    /**
     * Constructor for Fleeing Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Fleeing specific parameters
        this.fleeThreshold = this._getScaledValue(enemyData.fleeThreshold || 8.0);
        this.panicThreshold = this._getScaledValue(enemyData.panicThreshold || 5.0);
        this.safeDistance = this._getScaledValue(enemyData.safeDistance || 12.0);

        // Fleeing behavior
        this.isFleeing = false;
        this.fleeTimer = 0;
        this.fleeDuration = this._getScaledValue(5.0, 3.0, 7.0);
        this.fleeSpeed = this._getScaledValue(enemyData.speed * 1.5, enemyData.speed * 1.2, enemyData.speed * 1.8);

        // Panic behavior
        this.isPanicking = false;
        this.panicTimer = 0;
        this.panicDuration = this._getScaledValue(2.0, 1.0, 3.0);
        this.panicSpeed = this._getScaledValue(enemyData.speed * 0.5, enemyData.speed * 0.3, enemyData.speed * 0.7);
        this.panicDirectionChangeInterval = this._getScaledValue(0.5, 0.3, 0.7);
        this.panicDirectionChangeTimer = 0;
        this.panicDirection = new THREE.Vector3();

        // Hiding behavior
        this.isHiding = false;
        this.hideTimer = 0;
        this.hideDuration = this._getScaledValue(3.0, 2.0, 5.0);
        this.hideSpot = null;
        
        // Initialize pathfinding system
        this.pathfindingSystem = new PathfindingSystem(this);
        this.currentPath = [];
        this.currentWaypoint = null;

        // Initialize state
        this.setState(AIStates.IDLE);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Handle state transitions
        switch (this.currentState) {
            case AIStates.IDLE:
                // If player gets too close, start fleeing
                if (distanceToPlayer <= this.fleeThreshold) {
                    this.setState(AIStates.FLEEING);
                }
                break;

            case AIStates.FLEEING:
                // If player gets too close while fleeing, panic
                if (distanceToPlayer <= this.panicThreshold) {
                    this.setState(AIStates.STUNNED); // Use STUNNED for panic
                }
                // If reached safe distance, go back to idle or hiding
                else if (distanceToPlayer >= this.safeDistance) {
                    // Chance to hide based on difficulty
                    const hideChance = this._getScaledValue(0.7, 0.5, 0.9);
                    if (Math.random() < hideChance) {
                        this.setState(AIStates.IDLE); // Use IDLE for hiding
                    } else {
                        this.setState(AIStates.IDLE);
                    }
                }
                // If flee timer expired, go back to idle
                else if (this.fleeTimer <= 0) {
                    this.setState(AIStates.IDLE);
                }
                break;

            case AIStates.STUNNED: // Used for panic
                // After panic duration, go back to fleeing
                if (this.panicTimer <= 0) {
                    this.setState(AIStates.FLEEING);
                }
                break;

            case AIStates.IDLE: // Used for hiding
                // If player gets too close while hiding, flee
                if (distanceToPlayer <= this.fleeThreshold) {
                    this.setState(AIStates.FLEEING);
                }
                // If hide timer expired, go back to idle
                else if (this.isHiding && this.hideTimer <= 0) {
                    this.isHiding = false;
                    this.setState(AIStates.IDLE);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.IDLE:
                if (this.isHiding) {
                    // Hiding behavior
                    this._executeHidingBehavior(deltaTime);
                } else {
                    // Idle behavior (slight random movement)
                    this._executeIdleBehavior(deltaTime);
                }
                break;

            case AIStates.FLEEING:
                // Fleeing behavior
                this._executeFleeingBehavior(deltaTime, directionToPlayer, collisionObjects);
                break;

            case AIStates.STUNNED:
                // Panic behavior
                this._executePanicBehavior(deltaTime);
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.FLEEING:
                // Initialize fleeing
                this.isFleeing = true;
                this.fleeTimer = this.fleeDuration;
                break;

            case AIStates.STUNNED:
                // Initialize panic
                this.isPanicking = true;
                this.panicTimer = this.panicDuration;
                this.panicDirectionChangeTimer = 0;
                this._chooseRandomPanicDirection();
                break;

            case AIStates.IDLE:
                // Check if should hide
                if (this.currentState === AIStates.FLEEING && !this.isHiding) {
                    const hideChance = this._getScaledValue(0.7, 0.5, 0.9);
                    if (Math.random() < hideChance) {
                        this.isHiding = true;
                        this.hideTimer = this.hideDuration;
                        this._findHideSpot();
                    }
                }
                break;
        }
    }

    /**
     * Execute fleeing behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @private
     */
    _executeFleeingBehavior(deltaTime, directionToPlayer, collisionObjects) {
        // Update flee timer
        this.fleeTimer -= deltaTime;

        // Calculate flee direction (away from player)
        const fleeDirection = directionToPlayer.clone().multiplyScalar(-1);

        // Add some randomness to direction
        const randomAngle = (Math.random() - 0.5) * Math.PI / 6; // +/- 30 degrees
        const randomizedDirection = new THREE.Vector3(
            fleeDirection.x * Math.cos(randomAngle) - fleeDirection.z * Math.sin(randomAngle),
            0,
            fleeDirection.x * Math.sin(randomAngle) + fleeDirection.z * Math.cos(randomAngle)
        );

        // Calculate move amount
        const moveAmount = this.fleeSpeed * deltaTime;
        
        // Calculate flee target position (some distance away from player)
        const fleeDistance = this.safeDistance || 12.0;
        const fleeTargetPos = this.enemy.position.clone().add(randomizedDirection.normalize().multiplyScalar(fleeDistance));
        
        // Use pathfinding to navigate to flee position
        if (!this.currentPath || this.currentPath.length === 0 || 
            (Date.now() - (this.lastPathUpdate || 0) > 300)) { // Update more frequently when fleeing
            // Update path every 300ms or when no path exists
            const obstacles = this._getObstaclesForPathfinding();
            this.currentPath = this.pathfindingSystem.findPath(fleeTargetPos, obstacles);
            this.lastPathUpdate = Date.now();
        }
        
        // Get next waypoint
        this.currentWaypoint = this.pathfindingSystem.getNextWaypoint();
        
        let moveVector;
        let newPosition;
        
        if (this.currentWaypoint) {
            // Move towards waypoint
            const directionToWaypoint = new THREE.Vector3()
                .copy(this.currentWaypoint)
                .sub(this.enemy.position)
                .normalize();
            
            // Get steering force to avoid dynamic obstacles
            const steeringForce = this.pathfindingSystem.getSteeringForce(
                directionToWaypoint, 
                this._getDynamicObstacles()
            );
            
            // Combine waypoint direction with steering
            const finalDirection = directionToWaypoint.add(steeringForce).normalize();
            moveVector = finalDirection.multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        } else {
            // Fallback to direct movement if no path
            moveVector = randomizedDirection.normalize().multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        }

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls
        let canMove = true;
        
        // Skip collision detection if we're following a pathfinding waypoint
        if (this.currentWaypoint) {
            canMove = true; // Trust pathfinding
        } else if (this.collisionObjects) {
            const enemyRadius = this.enemyData.size || 0.5;
            for (const obj of this.collisionObjects) {
                // FISH POND FIX: Allow fish to move through pond collision boxes
                if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                    continue; // Skip pond collision for fish
                }

                const objBox = new THREE.Box3().setFromObject(obj);
                const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                    canMove = false;
                    break;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Face away from player
        this._faceTarget(this.enemy.position.clone().add(randomizedDirection));

        // Occasionally look back at player
        if (Math.random() < 0.1) {
            this._faceTarget(this.player.position);
        }
    }

    /**
     * Execute panic behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executePanicBehavior(deltaTime) {
        // Update panic timer
        this.panicTimer -= deltaTime;

        // Update direction change timer
        this.panicDirectionChangeTimer -= deltaTime;
        if (this.panicDirectionChangeTimer <= 0) {
            this._chooseRandomPanicDirection();
            this.panicDirectionChangeTimer = this.panicDirectionChangeInterval;
            
            // Clear current path when changing direction
            this.currentPath = [];
            this.currentWaypoint = null;
        }

        // Calculate move amount (slower when panicking)
        const moveAmount = this.panicSpeed * deltaTime;
        
        // Calculate panic target position
        const panicDistance = 5.0; // Distance to run in panic direction
        const panicTargetPos = this.enemy.position.clone().add(this.panicDirection.clone().multiplyScalar(panicDistance));
        
        // Use pathfinding to navigate to panic position
        if (!this.currentPath || this.currentPath.length === 0 || 
            (Date.now() - (this.lastPathUpdate || 0) > 200)) { // Update frequently when panicking
            // Update path every 200ms or when no path exists
            const obstacles = this._getObstaclesForPathfinding();
            this.currentPath = this.pathfindingSystem.findPath(panicTargetPos, obstacles);
            this.lastPathUpdate = Date.now();
        }
        
        // Get next waypoint
        this.currentWaypoint = this.pathfindingSystem.getNextWaypoint();
        
        let moveVector;
        let newPosition;
        
        if (this.currentWaypoint) {
            // Move towards waypoint
            const directionToWaypoint = new THREE.Vector3()
                .copy(this.currentWaypoint)
                .sub(this.enemy.position)
                .normalize();
            
            // Get steering force to avoid dynamic obstacles
            const steeringForce = this.pathfindingSystem.getSteeringForce(
                directionToWaypoint, 
                this._getDynamicObstacles()
            );
            
            // Combine waypoint direction with steering
            const finalDirection = directionToWaypoint.add(steeringForce).normalize();
            moveVector = finalDirection.multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        } else {
            // Fallback to direct movement if no path
            moveVector = this.panicDirection.clone().multiplyScalar(moveAmount);
            newPosition = this.enemy.position.clone().add(moveVector);
        }

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls
        let canMove = true;
        
        // Skip collision detection if we're following a pathfinding waypoint
        if (this.currentWaypoint) {
            canMove = true; // Trust pathfinding
        } else if (this.collisionObjects) {
            const enemyRadius = this.enemyData.size || 0.5;
            for (const obj of this.collisionObjects) {
                // FISH POND FIX: Allow fish to move through pond collision boxes
                if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                    continue; // Skip pond collision for fish
                }

                const objBox = new THREE.Box3().setFromObject(obj);
                const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                    canMove = false;
                    break;
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Face in movement direction
        this._faceTarget(this.enemy.position.clone().add(this.panicDirection));
    }

    /**
     * Execute hiding behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeHidingBehavior(deltaTime) {
        // Update hide timer
        this.hideTimer -= deltaTime;

        // If we have a hide spot, move towards it
        if (this.hideSpot) {
            // Calculate direction to hide spot
            const directionToHideSpot = this.hideSpot.clone().sub(this.enemy.position);
            const distanceToHideSpot = directionToHideSpot.length();
            directionToHideSpot.normalize();

            // If not at hide spot yet, move towards it
            if (distanceToHideSpot > 0.5) {
                // Calculate move amount
                const moveSpeed = this.enemyData.speed * 0.7; // Slower when hiding
                const moveAmount = moveSpeed * deltaTime;
                
                // Use pathfinding to navigate to hide spot
                if (!this.currentPath || this.currentPath.length === 0 || 
                    (Date.now() - (this.lastPathUpdate || 0) > 500)) {
                    // Update path every 500ms or when no path exists
                    const obstacles = this._getObstaclesForPathfinding();
                    this.currentPath = this.pathfindingSystem.findPath(this.hideSpot, obstacles);
                    this.lastPathUpdate = Date.now();
                }
                
                // Get next waypoint
                this.currentWaypoint = this.pathfindingSystem.getNextWaypoint();
                
                let moveVector;
                let newPosition;
                
                if (this.currentWaypoint) {
                    // Move towards waypoint
                    const directionToWaypoint = new THREE.Vector3()
                        .copy(this.currentWaypoint)
                        .sub(this.enemy.position)
                        .normalize();
                    
                    // Get steering force to avoid dynamic obstacles
                    const steeringForce = this.pathfindingSystem.getSteeringForce(
                        directionToWaypoint, 
                        this._getDynamicObstacles()
                    );
                    
                    // Combine waypoint direction with steering
                    const finalDirection = directionToWaypoint.add(steeringForce).normalize();
                    moveVector = finalDirection.multiplyScalar(moveAmount);
                    newPosition = this.enemy.position.clone().add(moveVector);
                } else {
                    // Fallback to direct movement if no path
                    moveVector = directionToHideSpot.multiplyScalar(Math.min(moveAmount, distanceToHideSpot));
                    newPosition = this.enemy.position.clone().add(moveVector);
                }

                // Check if new position is within floor bounds
                if (this.floorBounds) {
                    const minX = this.floorBounds.min.x + 1.0; // Add buffer
                    const maxX = this.floorBounds.max.x - 1.0;
                    const minZ = this.floorBounds.min.z + 1.0;
                    const maxZ = this.floorBounds.max.z - 1.0;

                    // Clamp position to floor bounds
                    newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
                    newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
                }

                // Check for collisions with walls
                let canMove = true;
                if (this.collisionObjects) {
                    const enemyRadius = this.enemyData.size || 0.5;
                    for (const obj of this.collisionObjects) {
                        // FISH POND FIX: Allow fish to move through pond collision boxes
                        if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                            continue; // Skip pond collision for fish
                        }

                        const objBox = new THREE.Box3().setFromObject(obj);
                        const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

                        // Simple collision check: if enemy's bounding sphere intersects with object's bounding box
                        if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                            canMove = false;
                            break;
                        }
                    }
                }

                // Apply movement if no collision
                if (canMove) {
                    this.enemy.position.copy(newPosition);
                }

                // Face towards hide spot
                this._faceTarget(this.hideSpot);
            } else {
                // At hide spot, face away from player
                if (this.player) {
                    const directionToPlayer = this.player.position.clone().sub(this.enemy.position);
                    this._faceTarget(this.enemy.position.clone().sub(directionToPlayer));
                }
            }
        } else {
            // No hide spot, just stand still
            // Occasionally look around
            if (Math.random() < 0.05) {
                const randomAngle = Math.random() * Math.PI * 2;
                const randomDirection = new THREE.Vector3(
                    Math.cos(randomAngle),
                    0,
                    Math.sin(randomAngle)
                );
                this._faceTarget(this.enemy.position.clone().add(randomDirection));
            }
        }
    }

    /**
     * Choose a random panic direction
     * @private
     */
    _chooseRandomPanicDirection() {
        const randomAngle = Math.random() * Math.PI * 2;
        this.panicDirection.set(
            Math.cos(randomAngle),
            0,
            Math.sin(randomAngle)
        );
    }

    /**
     * Find a hide spot
     * @private
     */
    _findHideSpot() {
        if (!this.player) {
            this.hideSpot = null;
            return;
        }

        // Simple implementation: just pick a point away from player
        const directionFromPlayer = this.enemy.position.clone().sub(this.player.position).normalize();
        const hideDistance = this._getScaledValue(10, 8, 15);

        // Add some randomness
        const randomAngle = (Math.random() - 0.5) * Math.PI / 2; // +/- 45 degrees
        const randomizedDirection = new THREE.Vector3(
            directionFromPlayer.x * Math.cos(randomAngle) - directionFromPlayer.z * Math.sin(randomAngle),
            0,
            directionFromPlayer.x * Math.sin(randomAngle) + directionFromPlayer.z * Math.cos(randomAngle)
        );

        // Calculate hide spot
        this.hideSpot = this.player.position.clone().add(
            randomizedDirection.multiplyScalar(hideDistance)
        );

        // TODO: Implement more sophisticated hide spot finding
        // Could use raycasting to find spots behind obstacles
    }
    
    /**
     * Get obstacles for pathfinding (static objects like walls, vases, etc.)
     * @returns {Array} Array of obstacle objects
     * @private
     */
    _getObstaclesForPathfinding() {
        const obstacles = [];
        
        if (this.collisionObjects) {
            for (const obj of this.collisionObjects) {
                // Skip floors
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                
                if (isFloorByUserData || isFloor) {
                    continue;
                }
                
                // Include all non-floor objects as obstacles
                if (obj.position) {
                    const worldPos = new THREE.Vector3();
                    obj.getWorldPosition(worldPos);
                    
                    obstacles.push({
                        position: worldPos,
                        radius: obj.userData?.size || 1.0,
                        type: obj.userData?.type || obj.name || 'unknown'
                    });
                }
            }
        }
        
        return obstacles;
    }
    
    /**
     * Get dynamic obstacles (other enemies, debris, etc.)
     * @returns {Array} Array of dynamic obstacle objects
     * @private
     */
    _getDynamicObstacles() {
        const obstacles = [];
        
        // Add nearby enemies as obstacles
        if (this.environmentalData && this.environmentalData.nearbyEnemies) {
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance > 0.1) { // Skip self
                    obstacles.push({
                        position: enemyData.position.clone(),
                        radius: 1.0,
                        type: 'enemy'
                    });
                }
            }
        }
        
        // Add nearby debris
        if (this.environmentalData && this.environmentalData.nearbyDebris) {
            for (const debrisData of this.environmentalData.nearbyDebris) {
                obstacles.push({
                    position: debrisData.position.clone(),
                    radius: 0.5,
                    type: 'debris'
                });
            }
        }
        
        return obstacles;
    }
}
