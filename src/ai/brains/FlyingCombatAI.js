/**
 * Flying Combat AI implementation
 * Specializes in aerial movement and attacks
 */
import * as THREE from 'three';
import { AIBrain } from '../AIBrain.js';
import { AIStates, TransitionConditions, checkCondition } from '../AIStates.js';
import { PathfindingSystem } from '../PathfindingSystem.js';

export class FlyingCombatAI extends AIBrain {
    /**
     * Constructor for Flying Combat AI
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Flying specific parameters
        this.preferredRange = this._getScaledValue(enemyData.preferredRange || 6.0);
        this.attackRange = this._getScaledValue(enemyData.attackRange || 4.5); // Increased from 3.0 to 4.5 to match the new hitbox radius
        this.minHoverHeight = this._getScaledValue(enemyData.minHoverHeight || 2.0);
        this.maxHoverHeight = this._getScaledValue(enemyData.maxHoverHeight || 4.0);
        this.currentHoverHeight = this.minHoverHeight + Math.random() * (this.maxHoverHeight - this.minHoverHeight);

        // Hovering behavior
        this.isHovering = true;
        this.hoverTimer = 0;
        this.hoverDuration = this._getScaledValue(3.0, 2.0, 5.0);
        this.hoverSpeed = this._getScaledValue(enemyData.speed * 0.5, enemyData.speed * 0.3, enemyData.speed * 0.7);
        this.hoverDirection = 1; // 1 for up, -1 for down

        // Swooping behavior
        this.isSwooping = false;
        this.swoopTimer = 0;
        this.swoopDuration = this._getScaledValue(0.8, 0.6, 1.0); // Faster swooping duration
        this.swoopSpeed = this._getScaledValue(enemyData.speed * 3.0, enemyData.speed * 2.5, enemyData.speed * 3.5); // Much faster swooping
        // Use enemy definition values if available, otherwise use defaults
        this.swoopChance = enemyData.swoopChance || this._getScaledValue(0.3, 0.2, 0.5); // Higher with difficulty
        this.swoopCooldown = enemyData.swoopCooldown || this._getScaledValue(5.0, 7.0, 3.0); // Shorter with higher difficulty
        this.swoopCooldownTimer = 0;

        // Circling behavior
        this.isCircling = false;
        this.circleTimer = 0;
        this.circleDuration = this._getScaledValue(4.0, 3.0, 6.0);
        this.circleRadius = this._getScaledValue(5.0, 4.0, 7.0);
        this.circleSpeed = this._getScaledValue(enemyData.speed * 0.8, enemyData.speed * 0.6, enemyData.speed * 1.0);
        this.circleAngle = Math.random() * Math.PI * 2;
        this.circleDirection = Math.random() < 0.5 ? 1 : -1; // 1 for clockwise, -1 for counterclockwise

        // Set flying flag
        this.isFlying = true;
        
        // Initialize pathfinding system
        this.pathfindingSystem = new PathfindingSystem(this);
        this.currentPath = [];
        this.currentWaypoint = null;

        // Initialize state
        this.setState(AIStates.HOVERING);
    }

    /**
     * Update the AI state based on current conditions
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Update cooldown timers
        if (this.swoopCooldownTimer > 0) {
            this.swoopCooldownTimer -= deltaTime;
        }

        // SIMPLIFIED STATE TRANSITIONS FOR BATS
        // Bats should primarily hover and swoop, with minimal circling

        switch (this.currentState) {
            case AIStates.HOVERING:
                // If we're at a good distance and swoop is ready, swoop at the player
                if (distanceToPlayer <= this.preferredRange * 2 &&
                    this.swoopCooldownTimer <= 0) {
                    // Very high chance to swoop when in range
                    this.setState(AIStates.SWOOPING);
                }
                // Rarely circle when far from player
                else if (distanceToPlayer > this.preferredRange * 2 &&
                         this.hoverTimer <= 0 &&
                         Math.random() < 0.2) {
                    this.setState(AIStates.STRAFING); // Use STRAFING for circling
                }
                break;

            case AIStates.STRAFING: // Used for circling
                // If circle timer expired or player is close, transition to hovering
                if (this.circleTimer <= 0 || distanceToPlayer <= this.preferredRange) {
                    this.setState(AIStates.HOVERING);
                }
                // If player is in range and swoop is ready, always swoop
                else if (distanceToPlayer <= this.preferredRange * 1.5 &&
                         this.swoopCooldownTimer <= 0) {
                    this.setState(AIStates.SWOOPING);
                }
                break;

            case AIStates.SWOOPING:
                // If swoop timer expired, transition to ascending
                if (this.swoopTimer <= 0) {
                    this.setState(AIStates.ASCENDING);
                }
                break;

            case AIStates.ASCENDING:
                // If reached hover height, transition to hovering
                if (this.enemy.position.y >= this.currentHoverHeight) {
                    this.setState(AIStates.HOVERING);
                }
                break;

            case AIStates.ATTACKING:
                // After attacking, immediately go back to hovering
                this.setState(AIStates.HOVERING);
                break;

            case AIStates.SHOOTING:
                // After shooting, go back to previous state
                if (this.previousState === AIStates.STRAFING) {
                    this.setState(AIStates.STRAFING);
                } else {
                    this.setState(AIStates.HOVERING);
                }
                break;
        }
    }

    /**
     * Execute behavior based on current state
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Always face the player unless swooping
        if (this.currentState !== AIStates.SWOOPING) {
            this._faceTarget(this.player.position);
        }

        // Execute behavior based on state
        switch (this.currentState) {
            case AIStates.HOVERING:
                // Hovering behavior
                this._executeHoveringBehavior(deltaTime);
                break;

            case AIStates.STRAFING: // Used for circling
                // Circling behavior
                this._executeCirclingBehavior(deltaTime);
                break;

            case AIStates.SWOOPING:
                // Swooping behavior
                this._executeSwoopingBehavior(deltaTime, directionToPlayer);
                break;

            case AIStates.ASCENDING:
                // Ascending behavior
                this._executeAscendingBehavior(deltaTime);
                break;

            case AIStates.ATTACKING:
                // REVERTED: Back to immediate attack execution like before - bats were perfect!
                // Just trigger the animation - actual attack is handled in _executeMeleeAttack
                break;

            case AIStates.SHOOTING:
                // Shooting behavior
                this._executeShootingBehavior();
                break;
        }
    }

    /**
     * Called when entering a new state
     * @param {String} state - The state being entered
     * @override
     */
    onStateEnter(state) {
        switch (state) {
            case AIStates.HOVERING:
                // Initialize hovering
                this.isHovering = true;
                this.hoverTimer = this.hoverDuration;
                // Choose new hover height
                this.currentHoverHeight = this.minHoverHeight + Math.random() * (this.maxHoverHeight - this.minHoverHeight);
                break;

            case AIStates.STRAFING: // Used for circling
                // Initialize circling
                this.isCircling = true;
                this.circleTimer = this.circleDuration;
                // Choose new circle parameters
                this.circleRadius = this._getScaledValue(5.0, 4.0, 7.0);
                // 50% chance to change circle direction
                if (Math.random() < 0.5) {
                    this.circleDirection *= -1;
                }
                break;

            case AIStates.SWOOPING:
                // Initialize swooping
                this.isSwooping = true;
                this.swoopTimer = this.swoopDuration;
                this.swoopCooldownTimer = this.swoopCooldown;
                break;

            case AIStates.ASCENDING:
                // No special initialization needed
                break;

            case AIStates.ATTACKING:
                // REVERTED: No special initialization needed for immediate attacks
                break;

            case AIStates.SHOOTING:
                // Reset attack timer
                this.timeSinceLastAttack = 0;
                break;
        }
    }

    /**
     * Execute hovering behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeHoveringBehavior(deltaTime) {
        if (!this.player) return;

        // Update hover timer
        this.hoverTimer -= deltaTime;

        // DIRECT APPROACH: Always move directly toward the player
        // Calculate distance to player
        const distanceToPlayer = this.enemy.position.distanceTo(this.player.position);

        // Get direction to player
        const dirToPlayer = this.player.position.clone().sub(this.enemy.position).normalize();

        // Determine if we should attack
        if (distanceToPlayer <= this.attackRange && this.timeSinceLastAttack >= this.attackCooldown) {
            // REVERTED: Back to immediate attack execution - bats were perfect!
            this._executeMeleeAttack();
            this.timeSinceLastAttack = 0;

            // After attacking, move slightly away
            const moveAwayDir = dirToPlayer.clone().negate();
            moveAwayDir.y = 0; // Keep horizontal
            const moveAwayAmount = this.hoverSpeed * deltaTime * 0.5;
            const moveAwayVector = moveAwayDir.multiplyScalar(moveAwayAmount);
            this.enemy.position.add(moveAwayVector);

            // Force state transition to ensure we keep moving
            this.setState(AIStates.HOVERING);
        } else {
            // We need to move toward the player
            // Adjust speed based on distance
            let moveSpeed = this.hoverSpeed;

            // Move MUCH faster when further away
            if (distanceToPlayer > this.preferredRange * 3) {
                moveSpeed *= 5.0; // Extremely fast when very far
            } else if (distanceToPlayer > this.preferredRange * 2) {
                moveSpeed *= 4.0; // Very fast when far
            } else if (distanceToPlayer > this.preferredRange) {
                moveSpeed *= 3.0; // Fast when approaching
            }

            // Calculate movement vector
            const moveAmount = moveSpeed * deltaTime;

            // Create a copy of direction to player that we can modify
            const moveDir = dirToPlayer.clone();

            // If we're close to preferred range, reduce vertical movement
            if (distanceToPlayer < this.preferredRange * 1.2) {
                moveDir.y *= 0.3; // Reduce vertical component
            }

            const moveVector = moveDir.normalize().multiplyScalar(moveAmount);

            // Apply movement
            this.enemy.position.add(moveVector);

            // Force velocity update to ensure AI system knows we're moving
            if (this.velocity) {
                this.velocity.copy(moveVector.clone().divideScalar(deltaTime));
            }
        }

        // Ensure we're not below minimum hover height
        if (this.enemy.position.y < this.minHoverHeight) {
            this.enemy.position.y = this.minHoverHeight;
        }

        // Ensure we're not above maximum hover height
        if (this.enemy.position.y > this.maxHoverHeight) {
            this.enemy.position.y = this.maxHoverHeight;
        }

        // Store current position for collision checks
        const newPosition = this.enemy.position.clone();

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Create invisible bottom boundary at minZ
            // This prevents bats from flying through the bottom wall
            if (newPosition.z > maxZ - 1.0) {
                console.log('Bat tried to fly through bottom boundary - preventing');
                newPosition.z = maxZ - 1.0;
            }

            // Clamp position to floor bounds (X and Z only, Y is controlled by hover)
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls - IMPROVED COLLISION DETECTION
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            // Use collisionRadius if available, otherwise fall back to size
            const enemyRadius = this.enemyData.collisionRadius || this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                try {
                    const objBox = new THREE.Box3().setFromObject(obj);

                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`Bat collision detected with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`Bat point collision detected with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`Error checking collision with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            // Preserve the Y position that was set earlier
            const currentY = this.enemy.position.y;
            this.enemy.position.copy(newPosition);
            this.enemy.position.y = currentY;
        }
    }

    /**
     * Execute circling behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeCirclingBehavior(deltaTime) {
        if (!this.player) return;

        // Update circle timer
        this.circleTimer -= deltaTime;

        // Update circle angle
        const angleChange = this.circleSpeed * deltaTime / this.circleRadius;
        this.circleAngle += angleChange * this.circleDirection;

        // Calculate target position
        const targetX = this.player.position.x + Math.cos(this.circleAngle) * this.circleRadius;
        const targetZ = this.player.position.z + Math.sin(this.circleAngle) * this.circleRadius;
        const targetY = this.currentHoverHeight;

        // Calculate direction to target
        const targetPosition = new THREE.Vector3(targetX, targetY, targetZ);
        const directionToTarget = targetPosition.clone().sub(this.enemy.position);
        const distanceToTarget = directionToTarget.length();
        directionToTarget.normalize();

        // Move towards target position
        const moveAmount = Math.min(distanceToTarget, this.circleSpeed * deltaTime);
        const moveVector = directionToTarget.multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Create invisible bottom boundary at minZ
            // This prevents bats from flying through the bottom wall
            if (newPosition.z > maxZ - 1.0) {
                console.log('Bat tried to fly through bottom boundary during circling - preventing');
                newPosition.z = maxZ - 1.0;
            }

            // Clamp position to floor bounds (X and Z only, Y can be above floor)
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls - IMPROVED COLLISION DETECTION
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            // Use collisionRadius if available, otherwise fall back to size
            const enemyRadius = this.enemyData.collisionRadius || this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // FISH POND FIX: Allow fish to move through pond collision boxes
                if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                    continue; // Skip pond collision for fish
                }

                try {
                    const objBox = new THREE.Box3().setFromObject(obj);

                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`Flying enemy collision detected during circling with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`Flying enemy point collision detected during circling with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`Error checking collision during circling with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }
    }

    /**
     * Execute swooping behavior
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeSwoopingBehavior(deltaTime, directionToPlayer) {
        // Update swoop timer
        this.swoopTimer -= deltaTime;

        // Calculate swoop progress (0 to 1)
        const swoopProgress = 1 - (this.swoopTimer / this.swoopDuration);

        // ALWAYS target player's current position, not initial position
        const targetPosition = this.player.position.clone();

        // Calculate direction to target
        const directionToTarget = targetPosition.clone().sub(this.enemy.position);
        directionToTarget.normalize();

        // Calculate move amount (faster in middle of swoop)
        // Enhanced speed curve for more aggressive swooping
        const swoopSpeedCurve = 5 * swoopProgress * (1 - swoopProgress) + 0.3; // Parabolic curve peaking at 0.5 with minimum of 0.3
        const moveAmount = this.swoopSpeed * 1.8 * swoopSpeedCurve * deltaTime; // 80% faster swooping

        // Calculate new position
        const moveVector = directionToTarget.multiplyScalar(moveAmount);
        const newPosition = this.enemy.position.clone().add(moveVector);

        // Force velocity update to ensure AI system knows we're moving
        if (this.velocity) {
            this.velocity.copy(moveVector.clone().divideScalar(deltaTime));
        }

        // Check if new position is within floor bounds
        if (this.floorBounds) {
            const minX = this.floorBounds.min.x + 1.0; // Add buffer
            const maxX = this.floorBounds.max.x - 1.0;
            const minZ = this.floorBounds.min.z + 1.0;
            const maxZ = this.floorBounds.max.z - 1.0;

            // Create invisible bottom boundary at minZ
            // This prevents bats from flying through the bottom wall
            if (newPosition.z > maxZ - 1.0) {
                console.log('Bat tried to fly through bottom boundary during swooping - preventing');
                newPosition.z = maxZ - 1.0;
            }

            // Clamp position to floor bounds (X and Z only, Y is controlled by swoop)
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // Check for collisions with walls - IMPROVED COLLISION DETECTION
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            // Use collisionRadius if available, otherwise fall back to size
            const enemyRadius = this.enemyData.collisionRadius || this.enemyData.size || 0.5;
            const enemyPos = new THREE.Vector3(newPosition.x, newPosition.y, newPosition.z);

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // FISH POND FIX: Allow fish to move through pond collision boxes
                if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                    continue; // Skip pond collision for fish
                }

                try {
                    const objBox = new THREE.Box3().setFromObject(obj);

                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`Flying enemy collision detected during swooping with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`Flying enemy point collision detected during swooping with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`Error checking collision during swooping with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }

        // Adjust height based on swoop progress
        const startHeight = this.currentHoverHeight;
        const lowestHeight = 1.5; // Increased lowest point of swoop to avoid ground clipping

        // Parabolic height curve
        const heightCurve = 1 - 4 * swoopProgress * (1 - swoopProgress); // 1 at start/end, 0 at middle
        const targetHeight = startHeight - (startHeight - lowestHeight) * (1 - heightCurve);

        // Apply height adjustment with safety check
        this.enemy.position.y = Math.max(targetHeight, this.minHoverHeight * 0.75);

        // Face in movement direction
        this._faceTarget(this.enemy.position.clone().add(directionToTarget));

        // Check if close enough to player for melee attack
        const distanceToPlayer = this.enemy.position.distanceTo(this.player.position);
        if (distanceToPlayer <= this.attackRange && this.timeSinceLastAttack >= this.attackCooldown) {
            // Execute melee attack
            this._executeMeleeAttack();
            this.timeSinceLastAttack = 0;
        }
    }

    /**
     * Execute ascending behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeAscendingBehavior(deltaTime) {
        // Calculate ascend speed
        const ascendSpeed = this.swoopSpeed * 0.7;

        // Calculate move amount
        const moveAmount = ascendSpeed * deltaTime;

        // Store current position for collision check
        const currentPosition = this.enemy.position.clone();
        const newPosition = currentPosition.clone();

        // Calculate vertical movement
        newPosition.y += moveAmount;

        // Add slight backward movement
        if (this.player) {
            const directionFromPlayer = this.enemy.position.clone().sub(this.player.position).normalize();
            const backwardMove = directionFromPlayer.multiplyScalar(moveAmount * 0.5);
            backwardMove.y = 0; // Keep vertical movement separate
            newPosition.add(backwardMove);
        }

        // Clamp height to max hover height
        if (newPosition.y > this.currentHoverHeight) {
            newPosition.y = this.currentHoverHeight;
        }

        // Check for collisions with walls - IMPROVED COLLISION DETECTION
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            // Create a sphere representing the enemy's collision volume
            // Use collisionRadius if available, otherwise fall back to size
            const enemyRadius = this.enemyData.collisionRadius || this.enemyData.size || 0.5;
            const enemyPos = newPosition.clone();

            // Create a box3 for more accurate collision detection
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                enemyPos,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry or children
                }

                // FISH POND FIX: Allow fish to move through pond collision boxes
                if (this.enemyData.type === 'fish' && obj.userData?.allowFish === true) {
                    continue; // Skip pond collision for fish
                }

                try {
                    const objBox = new THREE.Box3().setFromObject(obj);

                    // More accurate collision check using box intersection
                    if (enemyBox.intersectsBox(objBox)) {
                        console.log(`Flying enemy collision detected during ascending with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }

                    // Additional point-based check for thin walls
                    if (objBox.distanceToPoint(enemyPos) < enemyRadius) {
                        console.log(`Flying enemy point collision detected during ascending with object: ${obj.name || 'unnamed'}`);
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`Error checking collision during ascending with object: ${obj.name || 'unnamed'}`, error);
                }
            }
        }

        // Apply movement if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        }
    }

    /**
     * Execute shooting behavior
     * @private
     */
    _executeShootingBehavior() {
        if (!this.player) return;

        // Get shoot direction
        let shootDirection;
        if (this.difficulty >= 3) {
            // Use prediction for higher difficulties
            const predictedPosition = this._predictPlayerMovement();
            shootDirection = predictedPosition.clone().sub(this.enemy.position).normalize();
        } else {
            // Direct shooting for lower difficulties
            shootDirection = this.player.position.clone().sub(this.enemy.position).normalize();
        }

        // Emit shoot event (to be handled by DungeonHandler)
        if (this.enemy.userData.onShoot) {
            this.enemy.userData.onShoot(shootDirection);
        }
    }

    /**
     * Execute melee attack
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get attack data from enemy model if available
        const attackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: 1, // FIXED: Always deal 1 damage regardless of enemy type
            knockback: 5.0
        };

        console.log(`Bat executing melee attack! Attack range: ${this.attackRange}, hitbox radius: ${attackHitbox.radius}`);
        console.log(`Distance to player: ${this.enemy.position.distanceTo(this.player.position).toFixed(2)}`);

        // Set state to ATTACKING for animation
        this.setState(AIStates.ATTACKING);

        // Reset attack timer
        this.timeSinceLastAttack = 0;

        // Emit attack event (to be handled by DungeonHandler)
        if (this.enemy.userData.onMeleeAttack) {
            console.log(`Calling onMeleeAttack handler with hitbox:`, attackHitbox);
            try {
                this.enemy.userData.onMeleeAttack(attackHitbox);
                console.log(`Successfully called onMeleeAttack handler`);
            } catch (error) {
                console.error(`Error calling onMeleeAttack handler:`, error);
            }
        } else {
            console.error(`No onMeleeAttack handler found for enemy ${this.enemy.name}`);
        }

        // Apply damage to player directly if no handler
        if (!this.enemy.userData.onMeleeAttack && this.player.userData) {
            // Check if player has takeDamage method
            if (this.player.userData.takeDamage) {
                this.player.userData.takeDamage(attackHitbox.damage, this.enemy.position);
            }

            // Apply knockback to player
            if (this.player.userData.applyKnockback) {
                const knockbackDirection = this.player.position.clone().sub(this.enemy.position).normalize();
                this.player.userData.applyKnockback(knockbackDirection, attackHitbox.knockback);
            }
        }
    }
    
    /**
     * Get obstacles for pathfinding (static objects like walls, vases, etc.)
     * @returns {Array} Array of obstacle objects
     * @private
     */
    _getObstaclesForPathfinding() {
        const obstacles = [];
        
        if (this.collisionObjects) {
            for (const obj of this.collisionObjects) {
                // Skip floors
                const isFloorByUserData = obj.userData?.isFloor === true;
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );
                
                if (isFloorByUserData || isFloor) {
                    continue;
                }
                
                // Include all non-floor objects as obstacles
                if (obj.position) {
                    const worldPos = new THREE.Vector3();
                    obj.getWorldPosition(worldPos);
                    
                    obstacles.push({
                        position: worldPos,
                        radius: obj.userData?.size || 1.0,
                        type: obj.userData?.type || obj.name || 'unknown'
                    });
                }
            }
        }
        
        return obstacles;
    }
    
    /**
     * Get dynamic obstacles (other enemies, debris, etc.)
     * @returns {Array} Array of dynamic obstacle objects
     * @private
     */
    _getDynamicObstacles() {
        const obstacles = [];
        
        // Add nearby enemies as obstacles
        if (this.environmentalData && this.environmentalData.nearbyEnemies) {
            for (const enemyData of this.environmentalData.nearbyEnemies) {
                if (enemyData.distance > 0.1) { // Skip self
                    obstacles.push({
                        position: enemyData.position.clone(),
                        radius: 1.0,
                        type: 'enemy'
                    });
                }
            }
        }
        
        // Add nearby debris
        if (this.environmentalData && this.environmentalData.nearbyDebris) {
            for (const debrisData of this.environmentalData.nearbyDebris) {
                obstacles.push({
                    position: debrisData.position.clone(),
                    radius: 0.5,
                    type: 'debris'
                });
            }
        }
        
        return obstacles;
    }
}
