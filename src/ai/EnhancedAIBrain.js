/**
 * Enhanced AI Brain - Advanced AI system with intelligent behavior
 * Provides sophisticated combat intelligence, autonomous behavior, and group coordination
 */
import * as THREE from 'three';
import { AIBrain } from './AIBrain.js';
import { AIStates } from './AIStates.js';
import { AIMemory } from './AIMemory.js';
import { PathfindingSystem } from './PathfindingSystem.js';
import { TacticalDecisionMaker } from './TacticalDecisionMaker.js';

// GroupCoordinator is optional - will be loaded dynamically if available
let GroupCoordinator = null;

export class EnhancedAIBrain extends AIBrain {
    /**
     * Constructor for Enhanced AI Brain
     * @param {Object} enemy - The enemy object this brain controls
     * @param {Object} enemyData - The enemy data (from userData)
     * @param {Object} scene - The scene object
     * @param {Object} player - The player object
     * @param {Number} difficulty - Difficulty level (1-5)
     */
    constructor(enemy, enemyData, scene, player, difficulty = 1) {
        super(enemy, enemyData, scene, player, difficulty);

        // Enhanced AI Systems
        this.memory = new AIMemory(this);
        this.pathfinding = new PathfindingSystem(this);
        this.tacticalDecision = new TacticalDecisionMaker(this);

        // Personality System
        this.personality = this._generatePersonality();

        // Combat Intelligence
        this.combatIntelligence = {
            adaptationLevel: 0,
            playerPatterns: new Map(),
            lastPlayerActions: [],
            predictiveTargeting: true,
            environmentalAwareness: true
        };

        // Autonomous Behavior (reduced delays for more responsive combat)
        this.autonomousBehavior = {
            reactionTime: this._getScaledValue(0.1, 0.05, 0.3), // Faster reactions
            decisionDelay: this._getScaledValue(0.05, 0.02, 0.2), // Faster decisions
            situationalAwareness: this._getScaledValue(0.8, 0.6, 0.95), // Better awareness
            lastDecisionTime: 0,
            pendingDecision: null
        };

        // Group Coordination
        this.groupBehavior = {
            isGroupMember: false,
            groupRole: 'assault', // assault, support, flanker, defender
            groupId: null,
            coordinationLevel: this._getScaledValue(0.6, 0.3, 0.9),
            lastGroupUpdate: 0
        };

        // Advanced Combat States with proper timing
        this.combatStates = {
            current: 'neutral',
            aggressive: { threshold: 0.7, duration: 0 },
            defensive: { threshold: 0.3, duration: 0 },
            flanking: { threshold: 0.5, duration: 0 },
            retreating: { threshold: 0.2, duration: 0 }
        };

        // State Management (simplified - no complex attack tracking)
        this.stateManagement = {
            currentStateStartTime: 0,
            lastStateChangeTime: 0,
            forceStateUntil: 0 // Force current state until this time
        };

        // Environmental Awareness
        this.environmentalData = {
            nearbyEnemies: [],
            nearbyDebris: [],
            coverPositions: [],
            hazards: [],
            lastScan: 0,
            scanInterval: 1000 // ms
        };

        // Predictive Systems
        this.prediction = {
            playerVelocity: new THREE.Vector3(),
            playerAcceleration: new THREE.Vector3(),
            lastPlayerPosition: new THREE.Vector3(),
            predictedPlayerPosition: new THREE.Vector3(),
            predictionAccuracy: 0.5
        };

        // Idle Behavior System
        this.idleBehavior = {
            patrolPoints: [],
            currentPatrolIndex: 0,
            patrolTimer: 0,
            patrolSpeed: this.enemyData.speed * 0.3,
            investigationTarget: null,
            curiosityLevel: Math.random()
        };

        // Initialize state management
        this.stateManagement.currentStateStartTime = Date.now();
        this.stateManagement.lastStateChangeTime = Date.now();

        // Enhanced speed for specific enemy types
        this._applyEnemyTypeEnhancements();

        // Initialize systems (non-async parts first)
        this._initializeEnhancedSystems();
    }

    /**
     * Generate unique personality for this enemy
     * @returns {Object} Personality traits
     * @private
     */
    _generatePersonality() {
        // Create personality traits first
        const personality = {
            aggression: Math.random() * 0.6 + 0.2, // 0.2 - 0.8
            caution: Math.random() * 0.6 + 0.2,   // 0.2 - 0.8
            intelligence: Math.random() * 0.4 + 0.3 + (this.difficulty * 0.1), // 0.3 - 0.9
            cooperation: Math.random() * 0.6 + 0.2, // 0.2 - 0.8
            adaptability: Math.random() * 0.4 + 0.3 + (this.difficulty * 0.1), // 0.3 - 0.9
            riskTolerance: Math.random() * 0.8 + 0.1, // 0.1 - 0.9
            preferredRange: this._getScaledValue(
                this.enemyData.preferredRange || 3.0,
                2.0,
                6.0
            ) * (0.8 + Math.random() * 0.4) // ±20% variation
        };

        // Now select combat style based on the personality traits
        personality.combatStyle = this._selectCombatStyle(personality);

        return personality;
    }

    /**
     * Apply enemy type specific enhancements
     * @private
     */
    _applyEnemyTypeEnhancements() {
        const enemyType = this.enemy.userData?.type;

        if (enemyType === 'zombie') {
            // Make zombies faster but attack slower
            this.enemyData.speed *= 1.4; // 40% speed increase
            console.log(`[Enhanced AI] Zombie speed enhanced: ${this.enemyData.speed}`);

            // Adjust attack range for faster zombies
            this.attackRange *= 1.1; // Slightly longer attack range

            // FIXED: Increase attack cooldown for slower attacks
            this.attackCooldown *= 1.5; // 50% slower attacks (was 0.8 = faster)

        } else if (enemyType === 'magma_golem') {
            // Magma golems are already powerful, just slight enhancements
            this.enemyData.speed *= 1.1; // 10% speed increase
            this.attackRange *= 1.05; // Slightly longer reach

            // FIXED: Increase attack cooldown for slower attacks
            this.attackCooldown *= 1.3; // 30% slower attacks
            console.log(`[Enhanced AI] Magma golem speed enhanced: ${this.enemyData.speed}`);
        }
    }

    /**
     * Select combat style based on enemy type and personality
     * @param {Object} personality - Personality traits (optional, uses this.personality if not provided)
     * @returns {String} Combat style
     * @private
     */
    _selectCombatStyle(personality = null) {
        // Use provided personality or fallback to this.personality
        const traits = personality || this.personality;

        // Safety check
        if (!traits) {
            console.warn('[EnhancedAIBrain] No personality traits available, using default combat style');
            return 'aggressive';
        }

        const styles = ['aggressive', 'defensive', 'tactical', 'opportunistic', 'berserker'];
        const weights = [
            traits.aggression,
            traits.caution,
            traits.intelligence,
            traits.adaptability,
            traits.riskTolerance
        ];

        // Select style based on highest weighted trait
        const maxIndex = weights.indexOf(Math.max(...weights));
        return styles[maxIndex];
    }

    /**
     * Initialize enhanced AI systems
     * @private
     */
    _initializeEnhancedSystems() {
        // Group coordination is disabled for now due to import issues
        // This can be re-enabled later when GroupCoordinator is properly integrated
        console.log('[EnhancedAIBrain] Group coordination temporarily disabled');
        this.groupBehavior.isGroupMember = false;

        // Initialize patrol points for idle behavior
        this._generatePatrolPoints();

        // Set initial prediction data
        if (this.player && this.player.position) {
            this.prediction.lastPlayerPosition.copy(this.player.position);
        }
    }

    /**
     * Generate patrol points around spawn area
     * @private
     */
    _generatePatrolPoints() {
        const spawnPos = this.enemy.position.clone();
        const patrolRadius = 5.0;
        const numPoints = 3 + Math.floor(Math.random() * 3); // 3-5 points

        for (let i = 0; i < numPoints; i++) {
            const angle = (i / numPoints) * Math.PI * 2;
            const radius = patrolRadius * (0.5 + Math.random() * 0.5);
            const point = new THREE.Vector3(
                spawnPos.x + Math.cos(angle) * radius,
                spawnPos.y,
                spawnPos.z + Math.sin(angle) * radius
            );
            this.idleBehavior.patrolPoints.push(point);
        }
    }

    /**
     * Enhanced update method with advanced AI systems
     * @param {Number} deltaTime - Time since last update
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @param {Function} calculateFloorCurvatureHeight - Function to calculate floor curvature height
     * @returns {Object} - Updated state data
     * @override
     */
    update(deltaTime, collisionObjects, floorBounds, calculateFloorCurvatureHeight = null) {
        // CRITICAL FIX: Ensure curvature calculation function is stored before calling parent
        this.calculateFloorCurvatureHeight = calculateFloorCurvatureHeight;

        // Call parent update first with curvature calculation function
        const baseResult = super.update(deltaTime, collisionObjects, floorBounds, calculateFloorCurvatureHeight);

        // Skip enhanced processing during hit reaction or knockback
        if (this.isHitReacting || this.isKnockedBack) {
            return baseResult;
        }

        // Update enhanced AI systems (simplified for better performance)
        this._updateEnhancedSystems(deltaTime);

        // Update memory system
        this.memory.update(deltaTime);

        // Update environmental awareness (less frequently)
        if (Date.now() - this.environmentalData.lastScan > 1000) { // PERFORMANCE FIX: Every 1000ms instead of 500ms
            this._updateEnvironmentalAwareness(deltaTime);
        }

        // Update predictive targeting (simplified)
        this._updatePredictiveTargeting(deltaTime);

        // Group coordination is disabled
        // this._updateGroupCoordination(deltaTime);

        // Combat adaptation (simplified)
        this._updateCombatAdaptation(deltaTime);

        // Debug state management
        this._debugStateManagement();

        return {
            ...baseResult,
            personality: this.personality,
            combatState: this.combatStates.current,
            groupRole: this.groupBehavior.groupRole,
            adaptationLevel: this.combatIntelligence.adaptationLevel
        };
    }

    /**
     * Update enhanced AI systems
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateEnhancedSystems(deltaTime) {
        // Update decision delays for realistic reaction times
        this.autonomousBehavior.lastDecisionTime += deltaTime;

        // Process pending decisions with delay
        if (this.autonomousBehavior.pendingDecision &&
            this.autonomousBehavior.lastDecisionTime >= this.autonomousBehavior.decisionDelay) {
            this._processPendingDecision();
        }

        // Update combat state durations
        Object.keys(this.combatStates).forEach(state => {
            if (state !== 'current' && this.combatStates[state].duration > 0) {
                this.combatStates[state].duration -= deltaTime;
            }
        });
    }

    /**
     * Update environmental awareness system
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateEnvironmentalAwareness(deltaTime) {
        const now = Date.now();
        if (now - this.environmentalData.lastScan < this.environmentalData.scanInterval) {
            return;
        }

        this.environmentalData.lastScan = now;
        this.environmentalData.nearbyEnemies = [];
        this.environmentalData.nearbyDebris = [];
        this.environmentalData.coverPositions = [];
        this.environmentalData.hazards = [];

        // Scan for nearby objects
        if (this.scene && this.scene.children) {
            const scanRadius = 10.0;
            const enemyPos = this.enemy.position;

            this.scene.children.forEach(obj => {
                const distance = obj.position.distanceTo(enemyPos);
                if (distance > scanRadius) return;

                // Identify object types
                if (obj.userData?.type === 'enemy' && obj !== this.enemy) {
                    this.environmentalData.nearbyEnemies.push({
                        object: obj,
                        distance: distance,
                        position: obj.position.clone()
                    });
                } else if (obj.userData?.isDebrisPiece || obj.userData?.isBonePiece) {
                    this.environmentalData.nearbyDebris.push({
                        object: obj,
                        distance: distance,
                        position: obj.position.clone()
                    });
                } else if (this._isCoverObject(obj)) {
                    this.environmentalData.coverPositions.push({
                        object: obj,
                        distance: distance,
                        position: obj.position.clone(),
                        coverValue: this._calculateCoverValue(obj)
                    });
                }
            });
        }

        // React to environmental changes
        this._reactToEnvironmentalChanges();
    }

    /**
     * Update predictive targeting system
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updatePredictiveTargeting(deltaTime) {
        if (!this.player || !this.player.position) return;

        const currentPlayerPos = this.player.position.clone();

        // Calculate player velocity
        this.prediction.playerVelocity.copy(currentPlayerPos)
            .sub(this.prediction.lastPlayerPosition)
            .divideScalar(deltaTime);

        // Calculate player acceleration
        const previousVelocity = this.prediction.playerVelocity.clone();
        this.prediction.playerAcceleration.copy(this.prediction.playerVelocity)
            .sub(previousVelocity)
            .divideScalar(deltaTime);

        // Predict future player position
        const predictionTime = this._calculatePredictionTime();
        this.prediction.predictedPlayerPosition.copy(currentPlayerPos)
            .add(this.prediction.playerVelocity.clone().multiplyScalar(predictionTime))
            .add(this.prediction.playerAcceleration.clone().multiplyScalar(0.5 * predictionTime * predictionTime));

        // Update prediction accuracy based on success rate
        this._updatePredictionAccuracy();

        // Store current position for next frame
        this.prediction.lastPlayerPosition.copy(currentPlayerPos);
    }

    /**
     * Update group coordination
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateGroupCoordination(deltaTime) {
        // Group coordination is temporarily disabled
        if (!this.groupBehavior.isGroupMember) return;

        // This method is currently a no-op until GroupCoordinator is properly integrated
        // Individual AI behavior will still work without group coordination
    }

    /**
     * Update combat adaptation system
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _updateCombatAdaptation(deltaTime) {
        // Record player actions for pattern analysis
        this._recordPlayerActions();

        // Analyze patterns and adapt
        if (this.combatIntelligence.lastPlayerActions.length >= 10) {
            this._analyzePlayerPatterns();
            this._adaptTactics();
        }

        // Update adaptation level based on encounter success
        this._updateAdaptationLevel();
    }

    /**
     * Enhanced state update with tactical decision making
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @override
     */
    _updateState(deltaTime, distanceToPlayer, directionToPlayer) {
        // Check if we're currently forced to stay in a state
        const now = Date.now();
        if (now < this.stateManagement.forceStateUntil) {
            return; // Don't change state yet
        }

        // FIXED: Use much smaller attack range to ensure enemies get close enough to hit
        // The attack hitbox radius is only ~1.925 units, so enemies need to be very close
        const closeAttackRange = 1.5; // REDUCED: Must be within hit radius
        const aggressionBonus = this.personality.aggression * 0.2; // Reduced bonus
        const effectiveRange = closeAttackRange + aggressionBonus;

        // More conservative state transitions with proper checks
        if (distanceToPlayer <= effectiveRange &&
            this.timeSinceLastAttack >= this.attackCooldown &&
            this.currentState !== AIStates.ATTACKING) {
            // Attack when in range, ready, and not already attacking
            this.setState(AIStates.ATTACKING);
        } else if (distanceToPlayer > this.preferredRange * 1.5 &&
                   this.currentState !== AIStates.ATTACKING) {
            // Chase when too far and not attacking
            this.setState(AIStates.MOVING);
        } else if (distanceToPlayer <= this.preferredRange &&
                   this.currentState !== AIStates.ATTACKING &&
                   this.timeSinceLastAttack < this.attackCooldown) {
            // Strafe when in good position but not ready to attack
            if (Math.random() < 0.3 + this.personality.intelligence * 0.2) {
                this.setState(AIStates.STRAFING);
            } else {
                this.setState(AIStates.IDLE);
            }
        }

        // Update combat state based on conditions
        this._updateCombatState(distanceToPlayer);
    }

    /**
     * Enhanced behavior execution with autonomous systems
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @param {Array} collisionObjects - Objects to check for collision
     * @param {Object} floorBounds - Bounds of the floor
     * @override
     */
    _executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds) {
        // Execute behavior based on current state with enhanced logic
        switch (this.currentState) {
            case AIStates.IDLE:
                this._executeEnhancedIdleBehavior(deltaTime);
                break;

            case AIStates.MOVING:
                this._executeEnhancedMovement(deltaTime, directionToPlayer);
                break;

            case AIStates.ATTACKING:
                this._executeEnhancedAttack(deltaTime, distanceToPlayer);
                break;

            case AIStates.STRAFING:
                this._executeEnhancedStrafing(deltaTime, directionToPlayer);
                break;

            case AIStates.FLEEING:
                this._executeEnhancedFleeing(deltaTime, directionToPlayer);
                break;

            default:
                // Fall back to parent behavior
                super._executeBehavior(deltaTime, distanceToPlayer, directionToPlayer, collisionObjects, floorBounds);
                break;
        }

        // Always update facing direction unless specifically overridden
        if (!this._shouldSkipFacing()) {
            this._updateFacing(directionToPlayer);
        }
    }

    /**
     * Enhanced idle behavior with patrol and investigation
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeEnhancedIdleBehavior(deltaTime) {
        // Ensure idle animation is playing when truly idle
        this._ensureIdleAnimation();

        // Update patrol timer
        this.idleBehavior.patrolTimer += deltaTime;

        // Check for investigation targets
        if (this.idleBehavior.investigationTarget) {
            this._investigateTarget(deltaTime);
            return;
        }

        // Patrol behavior
        if (this.idleBehavior.patrolPoints.length > 0 && this.idleBehavior.patrolTimer > 3.0) {
            this._executePatrolBehavior(deltaTime);
        } else {
            // Subtle idle movement
            this._executeSubtleIdleMovement(deltaTime);
        }

        // Random investigation chance
        if (Math.random() < 0.01 && this.environmentalData.nearbyDebris.length > 0) {
            this._startInvestigation();
        }
    }

    /**
     * Ensure idle animation is playing when not moving
     * @private
     */
    _ensureIdleAnimation() {
        if (this.enemy.userData && this.enemy.userData.type) {
            const enemyType = this.enemy.userData.type;

            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // Set idle animation if not already set
                if (this.enemy.userData.animationState !== 'idle') {
                    this.enemy.userData.animationState = 'idle';
                    console.log(`[Enhanced AI] Setting ${enemyType} to idle animation`);
                }
            }
        }
    }

    /**
     * Ensure attack animation is triggered when attacking
     * @private
     */
    _ensureAttackAnimation() {
        if (this.enemy.userData && this.enemy.userData.type) {
            const enemyType = this.enemy.userData.type;

            if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                // Set attack animation
                this.enemy.userData.animationState = 'attacking';
                console.log(`[Enhanced AI] Setting ${enemyType} to attack animation`);
            }
        }
    }

    /**
     * Enhanced movement with pathfinding and obstacle avoidance
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeEnhancedMovement(deltaTime, directionToPlayer) {
        if (this.isKnockedBack) return;

        // SIMPLIFIED: More direct and aggressive movement
        // Complex pathfinding was making enemies too slow and hesitant

        // Use slight predictive targeting for more challenge
        let targetDirection = directionToPlayer.clone();

        if (this.combatIntelligence.predictiveTargeting && this.player.velocity) {
            // Simple prediction: add a fraction of player velocity
            const predictionFactor = 0.3 * this.personality.intelligence;
            const predictedOffset = this.player.velocity.clone().multiplyScalar(predictionFactor);
            const predictedPosition = this.player.position.clone().add(predictedOffset);
            targetDirection = predictedPosition.sub(this.enemy.position);
            targetDirection.y = 0;
            targetDirection.normalize();
        }

        // Apply personality-based speed modification
        const speedMultiplier = 1.0 + (this.personality.aggression * 0.3) - (this.personality.caution * 0.2);
        const enhancedSpeed = this.enemyData.speed * speedMultiplier;

        // Simple obstacle avoidance - just check for nearby debris
        const avoidanceForce = this._getSimpleAvoidanceForce();
        if (avoidanceForce.length() > 0) {
            targetDirection.add(avoidanceForce.multiplyScalar(0.5));
            targetDirection.normalize();
        }

        // Apply movement with enhanced speed
        this._applyMovement(deltaTime, targetDirection, enhancedSpeed);
    }

    /**
     * Enhanced attack with predictive targeting
     * @param {Number} deltaTime - Time since last update
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _executeEnhancedAttack(deltaTime, distanceToPlayer) {
        // FIXED: Continue moving toward player if still too far away
        const optimalAttackDistance = 1.5; // REDUCED: Must be within hit radius (1.925)

        if (distanceToPlayer > optimalAttackDistance) {
            // Move closer to player while in attack state
            const directionToPlayer = this.player.position.clone().sub(this.enemy.position);
            directionToPlayer.y = 0;
            directionToPlayer.normalize();

            // Move at reduced speed while attacking
            const attackMoveSpeed = this.enemyData.speed * 0.6;
            this._applyMovement(deltaTime, directionToPlayer, attackMoveSpeed);
        }

        // Face the player directly for better accuracy
        this._faceTarget(this.player.position);

        // Apply personality-based attack modifications (FIXED: Make attacks slower overall)
        const attackSpeedMultiplier = 0.8 + (this.personality.aggression * 0.2) - (this.personality.caution * 0.1);
        const modifiedCooldown = this.attackCooldown / attackSpeedMultiplier;

        // Execute attack if ready and close enough - SIMPLIFIED LIKE BAT SYSTEM
        if (this.timeSinceLastAttack >= modifiedCooldown && distanceToPlayer <= optimalAttackDistance) {
            console.log(`[Enhanced AI] ${this.enemy.userData?.type || 'Enemy'} executing attack at distance ${distanceToPlayer.toFixed(2)}`);

            // Ensure attack animation is triggered
            this._ensureAttackAnimation();

            // IMMEDIATE ATTACK EXECUTION - like bat system
            this._executeMeleeAttack();

            // Reset attack timer immediately
            this.timeSinceLastAttack = 0;

            // Add slight unpredictability based on intelligence
            if (Math.random() < this.personality.intelligence * 0.3) {
                // Intelligent enemies might delay slightly to throw off player timing
                this.timeSinceLastAttack -= 0.1;
            }
        }
    }

    /**
     * Enhanced strafing with environmental awareness
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeEnhancedStrafing(deltaTime, directionToPlayer) {
        if (this.isKnockedBack) return;

        // Calculate strafe direction based on personality and environment
        const strafeDirection = this._calculateStrafeDirection(directionToPlayer);

        // Apply steering forces
        const obstacles = this._getPathfindingObstacles();
        const steeringForce = this.pathfinding.getSteeringForce(strafeDirection, obstacles);
        const finalDirection = strafeDirection.add(steeringForce);
        finalDirection.normalize();

        // Apply movement
        const strafeSpeed = this.enemyData.speed * 0.7; // Slower when strafing
        this._applyMovement(deltaTime, finalDirection, strafeSpeed);

        // Maintain facing towards player
        this._faceTarget(this.player.position);
    }

    /**
     * Enhanced fleeing with intelligent escape routes
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _executeEnhancedFleeing(deltaTime, directionToPlayer) {
        if (this.isKnockedBack) return;

        // Calculate escape direction
        const escapeDirection = this._calculateEscapeDirection(directionToPlayer);

        // Use pathfinding to find escape route
        const escapeTarget = this.enemy.position.clone().add(escapeDirection.multiplyScalar(10.0));
        const obstacles = this._getPathfindingObstacles();
        const path = this.pathfinding.findPath(escapeTarget, obstacles);

        // Get next waypoint
        const waypoint = this.pathfinding.getNextWaypoint();

        if (waypoint) {
            const waypointDirection = waypoint.clone().sub(this.enemy.position);
            waypointDirection.y = 0;
            waypointDirection.normalize();

            // Apply movement at increased speed
            const fleeSpeed = this.enemyData.speed * 1.3;
            this._applyMovement(deltaTime, waypointDirection, fleeSpeed);
        } else {
            // Direct escape
            const fleeSpeed = this.enemyData.speed * 1.3;
            this._applyMovement(deltaTime, escapeDirection, fleeSpeed);
        }
    }



    /**
     * Update facing direction with smooth rotation
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @private
     */
    _updateFacing(directionToPlayer) {
        if (this.enemy.userData.freezeRotation || this.isKnockedBack) return;

        // Smooth rotation towards target
        const targetDirection = directionToPlayer.clone();
        targetDirection.y = 0;
        targetDirection.normalize();

        if (targetDirection.lengthSq() > 0.001) {
            const targetPosition = this.enemy.position.clone().add(targetDirection);
            this.enemy.lookAt(targetPosition);
        }
    }

    /**
     * Check if facing update should be skipped
     * @returns {Boolean} True if should skip facing
     * @private
     */
    _shouldSkipFacing() {
        return this.isKnockedBack ||
               this.enemy.userData.freezeRotation ||
               this.currentState === AIStates.FLEEING;
    }

    // ===== HELPER METHODS =====

    /**
     * Get pathfinding obstacles from environment
     * @returns {Array} Array of obstacles
     * @private
     */
    _getPathfindingObstacles() {
        const obstacles = [];

        // Add collision objects
        if (this.collisionObjects) {
            obstacles.push(...this.collisionObjects);
        }

        // Add other enemies as obstacles
        if (this.environmentalData.nearbyEnemies) {
            this.environmentalData.nearbyEnemies.forEach(enemyData => {
                obstacles.push(enemyData.object);
            });
        }

        // Add large debris as obstacles
        if (this.environmentalData.nearbyDebris) {
            this.environmentalData.nearbyDebris.forEach(debrisData => {
                if (debrisData.distance < 2.0) { // Only close debris
                    obstacles.push(debrisData.object);
                }
            });
        }

        return obstacles;
    }

    /**
     * Calculate strafe direction based on personality and environment
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @returns {THREE.Vector3} Strafe direction
     * @private
     */
    _calculateStrafeDirection(directionToPlayer) {
        // Base strafe direction (perpendicular to player direction)
        const strafeDirection = new THREE.Vector3(-directionToPlayer.z, 0, directionToPlayer.x);

        // Apply circling direction
        if (this.circleData && this.circleData.circleDirection) {
            strafeDirection.multiplyScalar(this.circleData.circleDirection);
        } else {
            strafeDirection.multiplyScalar(Math.random() < 0.5 ? 1 : -1);
        }

        // Adjust based on environmental factors
        if (this.environmentalData.coverPositions.length > 0) {
            const nearestCover = this.environmentalData.coverPositions[0];
            const coverDirection = nearestCover.position.clone().sub(this.enemy.position);
            coverDirection.y = 0;
            coverDirection.normalize();

            // Blend strafe direction with cover direction
            strafeDirection.lerp(coverDirection, 0.3);
        }

        strafeDirection.normalize();
        return strafeDirection;
    }

    /**
     * Calculate escape direction for fleeing
     * @param {THREE.Vector3} directionToPlayer - Direction to player
     * @returns {THREE.Vector3} Escape direction
     * @private
     */
    _calculateEscapeDirection(directionToPlayer) {
        // Base escape direction (away from player)
        const escapeDirection = directionToPlayer.clone().negate();
        escapeDirection.y = 0;

        // Adjust based on environmental factors
        if (this.environmentalData.coverPositions.length > 0) {
            // Find cover that's away from player
            const validCover = this.environmentalData.coverPositions.filter(cover => {
                const coverToPlayer = this.player.position.clone().sub(cover.position);
                const enemyToPlayer = this.player.position.clone().sub(this.enemy.position);
                return coverToPlayer.dot(enemyToPlayer) < 0; // Cover is on opposite side
            });

            if (validCover.length > 0) {
                const bestCover = validCover.reduce((best, current) =>
                    current.coverValue > best.coverValue ? current : best
                );

                const coverDirection = bestCover.position.clone().sub(this.enemy.position);
                coverDirection.y = 0;
                coverDirection.normalize();

                // Blend escape direction with cover direction
                escapeDirection.lerp(coverDirection, 0.6);
            }
        }

        escapeDirection.normalize();
        return escapeDirection;
    }

    /**
     * Schedule a decision with reaction time delay
     * @param {String} decision - The decision to schedule
     * @private
     */
    _scheduleDecision(decision) {
        this.autonomousBehavior.pendingDecision = decision;
        this.autonomousBehavior.lastDecisionTime = 0;
    }

    /**
     * Process pending decision
     * @private
     */
    _processPendingDecision() {
        if (this.autonomousBehavior.pendingDecision) {
            this.setState(this.autonomousBehavior.pendingDecision);
            this.autonomousBehavior.pendingDecision = null;
        }
    }

    /**
     * Update combat state based on conditions
     * @param {Number} distanceToPlayer - Distance to player
     * @private
     */
    _updateCombatState(distanceToPlayer) {
        const healthRatio = this.enemyData.health / (this.enemyData.maxHealth || 100);
        const threatLevel = this._calculateThreatLevel(distanceToPlayer);

        // Determine combat state
        let newCombatState = 'neutral';

        if (healthRatio < 0.2 && this.personality.caution > 0.5) {
            newCombatState = 'retreating';
        } else if (threatLevel > 0.7 && this.personality.aggression > 0.6) {
            newCombatState = 'aggressive';
        } else if (threatLevel > 0.5 && this.personality.caution > 0.6) {
            newCombatState = 'defensive';
        } else if (distanceToPlayer > this.preferredRange && this.personality.intelligence > 0.6) {
            newCombatState = 'flanking';
        }

        // Update combat state
        if (newCombatState !== this.combatStates.current) {
            this.combatStates.current = newCombatState;
            // Safely set duration only if the state object exists
            if (this.combatStates[newCombatState]) {
                this.combatStates[newCombatState].duration = 5.0; // State lasts 5 seconds
            }
        }
    }

    /**
     * Calculate threat level
     * @param {Number} distanceToPlayer - Distance to player
     * @returns {Number} Threat level (0-1)
     * @private
     */
    _calculateThreatLevel(distanceToPlayer) {
        let threatLevel = 0;

        // Distance-based threat
        const maxThreatDistance = 10.0;
        threatLevel += Math.max(0, 1 - (distanceToPlayer / maxThreatDistance)) * 0.5;

        // Health-based threat
        const healthRatio = this.enemyData.health / (this.enemyData.maxHealth || 100);
        threatLevel += (1 - healthRatio) * 0.3;

        // Environmental threat
        if (this.environmentalData.nearbyEnemies.length === 0) {
            threatLevel += 0.2; // Higher threat when isolated
        }

        return Math.min(1.0, threatLevel);
    }

    /**
     * Check if object provides cover
     * @param {Object} obj - Object to check
     * @returns {Boolean} True if object provides cover
     * @private
     */
    _isCoverObject(obj) {
        if (!obj || !obj.geometry) return false;

        // Check if object is large enough to provide cover
        const bbox = new THREE.Box3().setFromObject(obj);
        const size = bbox.getSize(new THREE.Vector3());

        return size.x > 1.0 || size.z > 1.0 || size.y > 1.5;
    }

    /**
     * Calculate cover value of an object
     * @param {Object} obj - Object to evaluate
     * @returns {Number} Cover value (0-1)
     * @private
     */
    _calculateCoverValue(obj) {
        if (!obj || !obj.geometry) return 0;

        const bbox = new THREE.Box3().setFromObject(obj);
        const size = bbox.getSize(new THREE.Vector3());

        // Larger objects provide better cover
        const sizeScore = Math.min(1.0, (size.x + size.z + size.y) / 10.0);

        // Objects closer to player provide better cover
        const distanceToPlayer = obj.position.distanceTo(this.player.position);
        const distanceScore = Math.max(0, 1 - (distanceToPlayer / 15.0));

        return (sizeScore + distanceScore) / 2;
    }

    // ===== MISSING METHOD IMPLEMENTATIONS =====

    /**
     * React to environmental changes
     * @private
     */
    _reactToEnvironmentalChanges() {
        // React to nearby enemies
        if (this.environmentalData.nearbyEnemies.length > 2) {
            // Avoid clustering
            this.groupBehavior.coordinationLevel = Math.min(0.9, this.groupBehavior.coordinationLevel + 0.1);
        }

        // React to debris
        if (this.environmentalData.nearbyDebris.length > 3) {
            // Be more cautious in debris-heavy areas
            this.personality.caution = Math.min(0.9, this.personality.caution + 0.1);
        }
    }

    /**
     * Calculate prediction time for targeting
     * @returns {Number} Prediction time in seconds
     * @private
     */
    _calculatePredictionTime() {
        const baseTime = 0.5; // Base prediction time
        const intelligenceModifier = this.personality.intelligence * 0.3;
        const adaptationModifier = this.combatIntelligence.adaptationLevel * 0.2;

        return baseTime + intelligenceModifier + adaptationModifier;
    }

    /**
     * Update prediction accuracy based on success rate
     * @private
     */
    _updatePredictionAccuracy() {
        // Simplified accuracy update - would track actual hit/miss in full implementation
        const targetAccuracy = 0.5 + (this.personality.intelligence * 0.3);
        this.prediction.predictionAccuracy = THREE.MathUtils.lerp(
            this.prediction.predictionAccuracy,
            targetAccuracy,
            0.1
        );
    }

    /**
     * Check if engaged with player
     * @returns {Boolean} True if engaged
     * @private
     */
    _isEngagedWithPlayer() {
        const distanceToPlayer = this.enemy.position.distanceTo(this.player.position);
        return distanceToPlayer < (this.preferredRange * 2) && this.currentState !== AIStates.IDLE;
    }

    /**
     * Execute group command
     * @param {Object} command - Group command to execute
     * @private
     */
    _executeGroupCommand(command) {
        if (!command) return;

        switch (command.type) {
            case 'coordinate_attack':
                if (this.currentState === AIStates.IDLE) {
                    this.setState(AIStates.MOVING);
                }
                break;
            case 'spread_out':
                this.groupBehavior.coordinationLevel = Math.max(0.3, this.groupBehavior.coordinationLevel - 0.2);
                break;
            case 'retreat':
                this.setState(AIStates.FLEEING);
                break;
        }
    }

    /**
     * Record player actions for pattern analysis
     * @private
     */
    _recordPlayerActions() {
        // Simplified implementation - would track actual player actions
        const playerVelocity = this.prediction.playerVelocity.length();

        if (playerVelocity > 2.0) {
            this.combatIntelligence.lastPlayerActions.push({
                type: 'move',
                timestamp: Date.now(),
                velocity: playerVelocity
            });
        }

        // Limit action history
        if (this.combatIntelligence.lastPlayerActions.length > 20) {
            this.combatIntelligence.lastPlayerActions.shift();
        }
    }

    /**
     * Analyze player patterns
     * @private
     */
    _analyzePlayerPatterns() {
        const actions = this.combatIntelligence.lastPlayerActions;
        if (actions.length < 5) return;

        // Analyze movement patterns
        const avgVelocity = actions.reduce((sum, action) => sum + (action.velocity || 0), 0) / actions.length;

        if (avgVelocity > 3.0) {
            this.combatIntelligence.playerPatterns.set('movement', 'highly_mobile');
        } else if (avgVelocity > 1.5) {
            this.combatIntelligence.playerPatterns.set('movement', 'moderately_mobile');
        } else {
            this.combatIntelligence.playerPatterns.set('movement', 'stationary');
        }
    }

    /**
     * Adapt tactics based on analysis
     * @private
     */
    _adaptTactics() {
        const movementPattern = this.combatIntelligence.playerPatterns.get('movement');

        if (movementPattern === 'highly_mobile') {
            // Increase prediction time for mobile players
            this.combatIntelligence.predictiveTargeting = true;
            this.personality.adaptability = Math.min(0.9, this.personality.adaptability + 0.1);
        } else if (movementPattern === 'stationary') {
            // Be more aggressive against stationary players
            this.personality.aggression = Math.min(0.9, this.personality.aggression + 0.1);
        }
    }

    /**
     * Update adaptation level
     * @private
     */
    _updateAdaptationLevel() {
        // Gradually increase adaptation level over time
        this.combatIntelligence.adaptationLevel = Math.min(1.0,
            this.combatIntelligence.adaptationLevel + 0.01
        );
    }

    /**
     * Investigate target
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _investigateTarget(deltaTime) {
        if (!this.idleBehavior.investigationTarget) return;

        const target = this.idleBehavior.investigationTarget;
        const distance = this.enemy.position.distanceTo(target);

        if (distance < 2.0) {
            // Reached investigation target
            this.idleBehavior.investigationTarget = null;
            this.idleBehavior.patrolTimer = 0; // Reset patrol timer
        } else {
            // Move towards investigation target
            const direction = target.clone().sub(this.enemy.position);
            direction.y = 0;
            direction.normalize();

            this._applyMovement(deltaTime, direction, this.idleBehavior.patrolSpeed);
        }
    }

    /**
     * Execute patrol behavior
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executePatrolBehavior(deltaTime) {
        if (this.idleBehavior.patrolPoints.length === 0) return;

        const currentTarget = this.idleBehavior.patrolPoints[this.idleBehavior.currentPatrolIndex];
        const distance = this.enemy.position.distanceTo(currentTarget);

        if (distance < 1.5) {
            // Reached patrol point, move to next
            this.idleBehavior.currentPatrolIndex =
                (this.idleBehavior.currentPatrolIndex + 1) % this.idleBehavior.patrolPoints.length;
            this.idleBehavior.patrolTimer = 0;
        } else {
            // Move towards patrol point
            const direction = currentTarget.clone().sub(this.enemy.position);
            direction.y = 0;
            direction.normalize();

            this._applyMovement(deltaTime, direction, this.idleBehavior.patrolSpeed);
        }
    }

    /**
     * Execute subtle idle movement
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeSubtleIdleMovement(deltaTime) {
        // Small random movements to appear alive
        if (Math.random() < 0.02) { // 2% chance per frame
            const randomDirection = new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                0,
                (Math.random() - 0.5) * 2
            ).normalize();

            this._applyMovement(deltaTime, randomDirection, this.idleBehavior.patrolSpeed * 0.5);
        }
    }

    /**
     * Start investigation of nearby debris
     * @private
     */
    _startInvestigation() {
        if (this.environmentalData.nearbyDebris.length === 0) return;

        // Pick closest debris to investigate
        const closestDebris = this.environmentalData.nearbyDebris.reduce((closest, current) =>
            current.distance < closest.distance ? current : closest
        );

        this.idleBehavior.investigationTarget = closestDebris.position.clone();
    }

    /**
     * Face target position
     * @param {THREE.Vector3} targetPosition - Position to face
     * @private
     */
    _faceTarget(targetPosition) {
        if (this.enemy.userData.freezeRotation || this.isKnockedBack) return;

        const direction = targetPosition.clone().sub(this.enemy.position);
        direction.y = 0;
        direction.normalize();

        if (direction.lengthSq() > 0.001) {
            const targetPos = this.enemy.position.clone().add(direction);
            this.enemy.lookAt(targetPos);
        }
    }

    /**
     * Execute aggressive attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeAggressiveAttack(deltaTime) {
        // Aggressive attacks are faster and more direct
        this._executeStandardAttack(deltaTime);
    }

    /**
     * Execute tactical attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeTacticalAttack(deltaTime) {
        // Tactical attacks consider positioning and timing
        this._executeStandardAttack(deltaTime);
    }

    /**
     * Execute opportunistic attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeOpportunisticAttack(deltaTime) {
        // Opportunistic attacks wait for the right moment
        this._executeStandardAttack(deltaTime);
    }

    /**
     * Execute standard attack
     * @param {Number} deltaTime - Time since last update
     * @private
     */
    _executeStandardAttack(deltaTime) {
        // Call the parent class's _executeAttack method directly
        // This ensures the actual damage-dealing logic is executed
        super._executeAttack(deltaTime);
    }

    /**
     * Get simple obstacle avoidance force
     * @returns {THREE.Vector3} Avoidance force
     * @private
     */
    _getSimpleAvoidanceForce() {
        const avoidanceForce = new THREE.Vector3();
        const avoidanceRadius = 2.0;
        const enemyPos = this.enemy.position;

        // Check nearby debris for simple avoidance
        if (this.environmentalData.nearbyDebris) {
            this.environmentalData.nearbyDebris.forEach(debris => {
                const distance = debris.distance;
                if (distance < avoidanceRadius) {
                    // Calculate avoidance direction (away from debris)
                    const avoidDirection = enemyPos.clone().sub(debris.position);
                    avoidDirection.y = 0;
                    avoidDirection.normalize();

                    // Stronger avoidance for closer objects
                    const strength = (avoidanceRadius - distance) / avoidanceRadius;
                    avoidanceForce.add(avoidDirection.multiplyScalar(strength));
                }
            });
        }

        return avoidanceForce;
    }

    /**
     * Execute enhanced melee attack with personality-based modifications
     * @private
     */
    _executeMeleeAttack() {
        if (!this.player) return;

        // Get base attack data from enemy model - FIXED: Always use 1 damage
        const baseAttackHitbox = this.enemy.userData.attackHitbox || {
            radius: this.attackRange,
            damage: 1, // Fixed to always deal 1 damage
            knockback: 5.0
        };

        // Create attack hitbox with fixed damage
        const attackHitbox = {
            ...baseAttackHitbox,
            damage: 1 // Ensure damage is always 1 regardless of enemy type
        };

        // Apply personality-based modifications (but keep damage at 1)
        // Aggressive personalities have slightly better knockback
        if (this.personality.aggression > 0.7) {
            attackHitbox.knockback *= 1.1; // Reduced from 1.2
        }

        // Intelligent personalities have slightly better accuracy
        if (this.personality.intelligence > 0.7) {
            attackHitbox.radius *= 1.05; // Reduced from 1.15
        }

        // Cautious personalities have slightly better accuracy
        if (this.personality.caution > 0.7) {
            attackHitbox.radius *= 1.05; // Reduced and no damage reduction
        }

        console.log(`[Enhanced AI] ${this.enemy.userData?.type || 'Enemy'} executing melee attack - Damage: ${attackHitbox.damage}`);

        // Emit attack event to be handled by DungeonHandler
        if (this.enemy.userData.onMeleeAttack) {
            this.enemy.userData.onMeleeAttack(attackHitbox);
        } else {
            console.warn(`[Enhanced AI] ${this.enemy.userData?.type || 'Enemy'} has no onMeleeAttack handler!`);
        }
    }

    /**
     * Override _applyMovement to ensure proper collision detection and animation triggering
     * @param {Number} deltaTime - Time since last update
     * @param {THREE.Vector3} direction - Direction to move
     * @param {Number} speed - Movement speed (optional)
     * @override
     */
    _applyMovement(deltaTime, direction, speed = null) {
        if (this.isKnockedBack) return;

        const moveSpeed = speed || this.enemyData.speed;

        // Ensure we have a valid direction
        if (!direction || direction.lengthSq() < 0.001) return;

        // Normalize direction
        const normalizedDirection = direction.clone().normalize();

        // Calculate movement vector
        const movement = normalizedDirection.multiplyScalar(moveSpeed * deltaTime);

        // Calculate new position
        const newPosition = this.enemy.position.clone().add(movement);

        // CRITICAL FIX: Check floor bounds to prevent walking off the floor
        if (this.floorBounds) {
            const buffer = 1.0; // Keep enemies away from edges
            const minX = this.floorBounds.min.x + buffer;
            const maxX = this.floorBounds.max.x - buffer;
            const minZ = this.floorBounds.min.z + buffer;
            const maxZ = this.floorBounds.max.z - buffer;

            // Clamp position to floor bounds
            newPosition.x = Math.max(minX, Math.min(maxX, newPosition.x));
            newPosition.z = Math.max(minZ, Math.min(maxZ, newPosition.z));
        }

        // CRITICAL FIX: Check collision with walls and objects
        let canMove = true;
        if (this.collisionObjects && this.collisionObjects.length > 0) {
            const enemyRadius = this.enemyData.size || 0.5;
            const enemyBox = new THREE.Box3().setFromCenterAndSize(
                newPosition,
                new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
            );

            // Check collision with all objects
            for (const obj of this.collisionObjects) {
                if (!obj.geometry && (!obj.children || obj.children.length === 0)) {
                    continue; // Skip objects without geometry
                }

                // Skip floor objects (same logic as base AIBrain)
                const isFloor = obj.name && (
                    obj.name.toLowerCase().includes('floor') ||
                    obj.name.toLowerCase().includes('ground') ||
                    obj.name.toLowerCase().includes('terrain')
                );

                const objBox = new THREE.Box3().setFromObject(obj);
                const objHeight = objBox.max.y - objBox.min.y;
                const isAtFeetLevel = objBox.max.y <= (this.enemy.position.y + 0.1);
                const isHorizontalPlane = objHeight < 0.5;
                const isProbablyFloor = isFloor || (isHorizontalPlane && isAtFeetLevel);

                if (isProbablyFloor) {
                    continue; // Skip floor objects
                }

                try {
                    // Check for collision
                    if (enemyBox.intersectsBox(objBox)) {
                        canMove = false;
                        break;
                    }
                } catch (error) {
                    console.error(`[Enhanced AI] Collision check error:`, error);
                }
            }
        }

        // Apply movement only if no collision
        if (canMove) {
            this.enemy.position.copy(newPosition);
        } else {
            // If collision detected, try to find alternative direction
            this._handleCollisionAvoidance(normalizedDirection, deltaTime, moveSpeed);
        }

        // IMPORTANT: Trigger walk animation for zombies and other enemies
        if (this.enemy.userData && this.enemy.userData.type) {
            const enemyType = this.enemy.userData.type;

            // Ensure walk animation is playing for moving enemies
            if (moveSpeed > 0.1 && canMove) { // Only if actually moving and not blocked
                if (enemyType === 'zombie' || enemyType === 'magma_golem') {
                    // Force walk animation state
                    if (this.enemy.userData.animationState !== 'walking') {
                        this.enemy.userData.animationState = 'walking';
                        console.log(`[Enhanced AI] Setting ${enemyType} to walking animation`);
                    }
                }
            }
        }

        // Face movement direction if not frozen and can move
        if (!this.enemy.userData.freezeRotation && canMove) {
            this._faceDirection(normalizedDirection);
        }
    }

    /**
     * Handle collision avoidance when blocked
     * @param {THREE.Vector3} originalDirection - Original movement direction
     * @param {Number} deltaTime - Time since last update
     * @param {Number} moveSpeed - Movement speed
     * @private
     */
    _handleCollisionAvoidance(originalDirection, deltaTime, moveSpeed) {
        // Try alternative directions when blocked
        const alternatives = [
            originalDirection.clone().applyAxisAngle(new THREE.Vector3(0, 1, 0), Math.PI / 4), // 45 degrees right
            originalDirection.clone().applyAxisAngle(new THREE.Vector3(0, 1, 0), -Math.PI / 4), // 45 degrees left
            originalDirection.clone().applyAxisAngle(new THREE.Vector3(0, 1, 0), Math.PI / 2), // 90 degrees right
            originalDirection.clone().applyAxisAngle(new THREE.Vector3(0, 1, 0), -Math.PI / 2), // 90 degrees left
        ];

        for (const altDirection of alternatives) {
            const altMovement = altDirection.multiplyScalar(moveSpeed * deltaTime * 0.5); // Slower when avoiding
            const altPosition = this.enemy.position.clone().add(altMovement);

            // Check floor bounds for alternative position
            if (this.floorBounds) {
                const buffer = 1.0;
                const minX = this.floorBounds.min.x + buffer;
                const maxX = this.floorBounds.max.x - buffer;
                const minZ = this.floorBounds.min.z + buffer;
                const maxZ = this.floorBounds.max.z - buffer;

                if (altPosition.x < minX || altPosition.x > maxX ||
                    altPosition.z < minZ || altPosition.z > maxZ) {
                    continue; // Skip this alternative if it goes out of bounds
                }
            }

            // Check collision for alternative position
            let altCanMove = true;
            if (this.collisionObjects && this.collisionObjects.length > 0) {
                const enemyRadius = this.enemyData.size || 0.5;
                const enemyBox = new THREE.Box3().setFromCenterAndSize(
                    altPosition,
                    new THREE.Vector3(enemyRadius * 2, enemyRadius * 2, enemyRadius * 2)
                );

                for (const obj of this.collisionObjects) {
                    if (!obj.geometry && (!obj.children || obj.children.length === 0)) continue;

                    const objBox = new THREE.Box3().setFromObject(obj);
                    if (enemyBox.intersectsBox(objBox)) {
                        altCanMove = false;
                        break;
                    }
                }
            }

            // Use this alternative if it's clear
            if (altCanMove) {
                this.enemy.position.copy(altPosition);
                this._faceDirection(altDirection);
                return;
            }
        }

        // If no alternative works, just stay in place
        console.log(`[Enhanced AI] ${this.enemy.userData?.type || 'Enemy'} blocked by collision, staying in place`);
    }

    /**
     * Face a specific direction
     * @param {THREE.Vector3} direction - Direction to face
     * @private
     */
    _faceDirection(direction) {
        if (!direction || direction.lengthSq() < 0.001) return;

        const targetPosition = this.enemy.position.clone().add(direction);
        this.enemy.lookAt(targetPosition);
    }

    /**
     * Debug state management (temporary for testing)
     * @private
     */
    _debugStateManagement() {
        // Only log occasionally to avoid spam
        if (Math.random() < 0.01) { // 1% chance per frame
            const now = Date.now();
            const timeInState = now - this.stateManagement.currentStateStartTime;
            const timeSinceLastChange = now - this.stateManagement.lastStateChangeTime;
            const forceStateRemaining = Math.max(0, this.stateManagement.forceStateUntil - now);

            console.log(`[Enhanced AI Debug] ${this.enemy.userData?.type || 'Enemy'}:
                State: ${this.currentState},
                Time in state: ${timeInState}ms,
                Time since last change: ${timeSinceLastChange}ms,
                Force state remaining: ${forceStateRemaining}ms,
                Attack cooldown remaining: ${Math.max(0, this.attackCooldown - this.timeSinceLastAttack)}s`);
        }
    }

    /**
     * Override setState to implement proper state management with cooldowns
     * @param {String} newState - The new state to transition to
     * @override
     */
    setState(newState) {
        const now = Date.now();

        // Check if we're forced to stay in current state
        if (now < this.stateManagement.forceStateUntil) {
            return; // Cannot change state yet
        }

        // Allow state change - simplified like bat system
        if (newState !== this.currentState) {
            console.log(`[Enhanced AI] ${this.enemy.userData?.type || 'Enemy'} changing state: ${this.currentState} -> ${newState}`);

            // Call parent setState
            super.setState(newState);

            // Update state management
            this.stateManagement.currentStateStartTime = now;
            this.stateManagement.lastStateChangeTime = now;
        }
    }
}
