import * as THREE from 'three';

/**
 * Weapon Display Overlay - Shows current weapon as 3D model in HTML container
 */
export class WeaponDisplayOverlay {
    constructor(weaponSystem) {
        console.log('[WeaponDisplayOverlay] Constructor called with:', { weaponSystem });

        this.weaponSystem = weaponSystem;

        // Track current weapon to detect changes
        this.lastWeaponId = null;

        // Create HTML overlay element
        this.createHTMLOverlay();

        // Create Three.js scene for weapon rendering
        this.create3DScene();

        // Force initial weapon display
        this.updateWeaponDisplay();

        console.log('[WeaponDisplayOverlay] Initialized weapon display overlay successfully');
    }

    /**
     * Create HTML overlay element
     */
    createHTMLOverlay() {
        // Detect if we're on mobile device
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        // Remove any existing weapon display overlay to prevent duplicates
        const existingOverlay = document.getElementById('weapon-display-overlay');
        if (existingOverlay) {
            console.log('[WeaponDisplayOverlay] Removing existing weapon display overlay');
            existingOverlay.remove();
        }

        // Create main container
        this.overlayElement = document.createElement('div');
        this.overlayElement.id = 'weapon-display-overlay';

        // Different positioning and sizing for mobile vs desktop
        const mobileStyles = isMobile ? `
            bottom: 20px;
            right: 195px; /* Moved back halfway to the right (220px - 25px) */
            width: 75px; /* 25% smaller (100px * 0.75) */
            height: 75px;
        ` : `
            bottom: 20px;
            right: 20px;
            width: 90px; /* 25% smaller (120px * 0.75) */
            height: 90px;
        `;

        this.overlayElement.style.cssText = `
            position: absolute;
            ${mobileStyles}
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            z-index: 1000;
            pointer-events: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
            color: white;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
        `;

        // Create canvas for 3D weapon rendering (adjust size for mobile)
        this.weaponCanvas = document.createElement('canvas');
        const canvasWidth = isMobile ? 60 : 75; /* 25% smaller */
        const canvasHeight = isMobile ? 48 : 60; /* 25% smaller */
        this.weaponCanvas.width = canvasWidth;
        this.weaponCanvas.height = canvasHeight;
        this.weaponCanvas.style.cssText = `
            width: ${canvasWidth}px;
            height: ${canvasHeight}px;
            border-radius: 4px;
        `;

        // Create weapon name label
        this.weaponLabel = document.createElement('div');
        this.weaponLabel.style.cssText = `
            font-size: 10px;
            font-weight: bold;
            margin-top: 4px;
            text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
        `;

        this.overlayElement.appendChild(this.weaponCanvas);
        this.overlayElement.appendChild(this.weaponLabel);

        // Add to document body
        document.body.appendChild(this.overlayElement);

        console.log('[WeaponDisplayOverlay] Created HTML overlay element with canvas');
    }

    /**
     * Create Three.js scene for weapon rendering
     */
    create3DScene() {
        // Get canvas dimensions (already set based on mobile/desktop)
        const canvasWidth = this.weaponCanvas.width;
        const canvasHeight = this.weaponCanvas.height;

        // Create renderer for the small canvas
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.weaponCanvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(canvasWidth, canvasHeight);
        this.renderer.setClearColor(0x000000, 0); // Transparent background

        // Create scene
        this.scene = new THREE.Scene();

        // Create camera with aspect ratio based on canvas dimensions
        this.camera = new THREE.PerspectiveCamera(45, canvasWidth/canvasHeight, 0.1, 100);
        this.camera.position.set(2, 1, 3);
        this.camera.lookAt(0, 0, 0);

        // Add lighting
        this.setupLighting();

        // Animation properties
        this.rotationSpeed = 0.02;
        this.currentWeaponModel = null;

        console.log('[WeaponDisplayOverlay] Created 3D scene for weapon rendering');
    }

    /**
     * Setup lighting for weapon display
     */
    setupLighting() {
        // Ambient light for overall illumination
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.scene.add(ambientLight);

        // Directional light for definition
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(2, 2, 2);
        this.scene.add(directionalLight);

        // Additional light from opposite side
        const fillLight = new THREE.DirectionalLight(0xffffff, 0.4);
        fillLight.position.set(-2, 1, -1);
        this.scene.add(fillLight);
    }

    /**
     * Update weapon display based on current weapon
     */
    updateWeaponDisplay() {
        const currentWeapon = this.weaponSystem.getCurrentWeapon();

        if (!currentWeapon) {
            this.clearWeaponDisplay();
            return;
        }

        // Check if weapon changed
        if (this.lastWeaponId !== currentWeapon.id) {
            console.log('[WeaponDisplayOverlay] Weapon changed to:', currentWeapon.name);
            this.lastWeaponId = currentWeapon.id;
            this.loadWeaponDisplay(currentWeapon);
        }
    }
    
    /**
     * Load weapon display into overlay
     */
    loadWeaponDisplay(weapon) {
        // Clear existing weapon display
        this.clearWeaponDisplay();

        // Update weapon label
        this.weaponLabel.textContent = weapon.name;

        if (weapon.type === 'ranged') {
            // For Soul Blast, create a magical orb effect
            this.createSoulBlastDisplay(weapon);
        } else if (weapon.type === 'melee' && weapon.model) {
            // For melee weapons, use the actual 3D model
            this.createMeleeWeaponDisplay(weapon);
        }

        console.log(`[WeaponDisplayOverlay] Loaded weapon display: ${weapon.name}`);
    }

    /**
     * Create Soul Blast display (magical orb effect)
     */
    createSoulBlastDisplay(weapon) {
        // Create glowing orb geometry
        const orbGeometry = new THREE.SphereGeometry(0.3, 16, 16);

        // Create glowing material
        const orbMaterial = new THREE.MeshBasicMaterial({
            color: 0x4444ff,
            transparent: true,
            opacity: 0.8
        });

        // Create orb mesh
        const orbMesh = new THREE.Mesh(orbGeometry, orbMaterial);
        orbMesh.name = 'soulBlastDisplay';

        // Add pulsing effect data
        orbMesh.userData.pulsePhase = 0;

        // Add to scene
        this.scene.add(orbMesh);
        this.currentWeaponModel = orbMesh;

        // Create outer glow effect
        const glowGeometry = new THREE.SphereGeometry(0.4, 16, 16);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x2222ff,
            transparent: true,
            opacity: 0.3
        });

        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.name = 'soulBlastGlow';
        orbMesh.add(glowMesh);

        console.log('[WeaponDisplayOverlay] Created Soul Blast 3D display');
    }

    /**
     * Create melee weapon display using actual 3D model
     */
    createMeleeWeaponDisplay(weapon) {
        if (!weapon.model) {
            console.warn('[WeaponDisplayOverlay] No 3D model available for weapon:', weapon.name);
            return;
        }

        // Clone the actual weapon model
        this.currentWeaponModel = weapon.model.clone();
        this.currentWeaponModel.name = 'weaponDisplay';

        // Scale weapon for display
        this.currentWeaponModel.scale.setScalar(0.6);

        // Position weapon nicely in view
        this.currentWeaponModel.position.set(0, 0, 0);
        this.currentWeaponModel.rotation.set(0, 0, 0);

        // Add to scene
        this.scene.add(this.currentWeaponModel);

        console.log('[WeaponDisplayOverlay] Created melee weapon 3D display with actual model');
    }

    /**
     * Clear current weapon display
     */
    clearWeaponDisplay() {
        if (this.currentWeaponModel) {
            this.scene.remove(this.currentWeaponModel);
            this.currentWeaponModel = null;
        }
    }
    
    /**
     * Add CSS animations to document
     */
    addPulseAnimation() {
        if (!document.getElementById('weapon-pulse-animation')) {
            const style = document.createElement('style');
            style.id = 'weapon-pulse-animation';
            style.textContent = `
                @keyframes pulse {
                    0% { transform: scale(1); opacity: 0.8; }
                    50% { transform: scale(1.1); opacity: 1; }
                    100% { transform: scale(1); opacity: 0.8; }
                }
            `;
            document.head.appendChild(style);
        }
    }

    addSpinAnimation() {
        if (!document.getElementById('weapon-spin-animation')) {
            const style = document.createElement('style');
            style.id = 'weapon-spin-animation';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Update weapon display (called from game loop)
     */
    update() {
        // Update weapon display if needed
        this.updateWeaponDisplay();

        // Update 3D animations
        this.updateAnimation();

        // Render the 3D scene
        this.render3D();
    }

    /**
     * Update 3D animations
     */
    updateAnimation() {
        if (!this.currentWeaponModel) return;

        // Rotate weapon for spinning effect
        this.currentWeaponModel.rotation.y += this.rotationSpeed;

        // Special effects for Soul Blast
        if (this.currentWeaponModel.name === 'soulBlastDisplay') {
            // Pulsing effect
            this.currentWeaponModel.userData.pulsePhase += 0.05;
            const pulseScale = 1 + Math.sin(this.currentWeaponModel.userData.pulsePhase) * 0.1;
            this.currentWeaponModel.scale.setScalar(pulseScale);

            // Floating effect
            this.currentWeaponModel.position.y = Math.sin(this.currentWeaponModel.userData.pulsePhase * 0.7) * 0.1;
        }
    }

    /**
     * Render the 3D scene to canvas
     */
    render3D() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    /**
     * Resize overlay when window resizes
     */
    onWindowResize() {
        // Detect if we're on mobile device
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) ||
                         (navigator.maxTouchPoints > 0);

        // Adjust overlay size for very small screens
        if (window.innerWidth < 600 || window.innerHeight < 400) {
            if (isMobile) {
                // Extra small mobile size (25% smaller)
                this.overlayElement.style.width = '60px';
                this.overlayElement.style.height = '60px';
                this.overlayElement.style.right = '175px'; // Moved back halfway (200px - 25px)
            } else {
                // Small desktop size (25% smaller)
                this.overlayElement.style.width = '60px';
                this.overlayElement.style.height = '60px';
                this.overlayElement.style.right = '20px';
            }
        } else {
            if (isMobile) {
                // Normal mobile size (25% smaller)
                this.overlayElement.style.width = '75px';
                this.overlayElement.style.height = '75px';
                this.overlayElement.style.right = '195px'; // Moved back halfway (220px - 25px)
            } else {
                // Normal desktop size (25% smaller)
                this.overlayElement.style.width = '90px';
                this.overlayElement.style.height = '90px';
                this.overlayElement.style.right = '20px';
            }
        }
    }

    /**
     * Show/hide overlay
     */
    setVisible(visible) {
        this.visible = visible;
        if (this.overlayElement) {
            this.overlayElement.style.display = visible ? 'flex' : 'none';
        }
    }

    /**
     * Check if overlay should be rendered
     */
    isVisible() {
        return this.visible !== false; // Default to visible
    }

    /**
     * Cleanup
     */
    dispose() {
        // Clear weapon display
        this.clearWeaponDisplay();

        // Dispose of 3D scene objects
        if (this.scene) {
            this.scene.traverse(object => {
                if (object.geometry) object.geometry.dispose();
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            });
        }

        // Dispose of renderer
        if (this.renderer) {
            this.renderer.dispose();
        }

        // Remove HTML element
        if (this.overlayElement && this.overlayElement.parentNode) {
            this.overlayElement.parentNode.removeChild(this.overlayElement);
        }

        // Remove CSS animations
        const pulseStyle = document.getElementById('weapon-pulse-animation');
        if (pulseStyle) pulseStyle.remove();

        const spinStyle = document.getElementById('weapon-spin-animation');
        if (spinStyle) spinStyle.remove();

        console.log('[WeaponDisplayOverlay] Disposed weapon display overlay');
    }
}
