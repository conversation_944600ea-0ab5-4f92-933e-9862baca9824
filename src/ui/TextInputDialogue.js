/**
 * Text Input Dialogue System
 * Allows free text input for AI chat interactions with knights
 * Integrates with NPCDialogueService for natural language conversations
 */

import { npcDialogueService } from '../ai/NPCDialogueService.js';
import { getNPCData } from '../ai/NPCDatabase.js';

export class TextInputDialogue {
    constructor() {
        this.isActive = false;
        this.currentContext = null;
        this.conversationHistory = [];
        this.onResponseCallback = null;
        this.onCloseCallback = null;
        
        console.log('[TextInputDialogue] ✅ Initialized text input dialogue system');
    }
    
    /**
     * Show text input dialogue for knight interaction
     * @param {Object} context - Chat context including knight info
     * @param {Function} onResponse - Callback for when response is received
     * @param {Function} onClose - Callback for when dialogue is closed
     * @returns {Promise} - Resolves when dialogue is complete
     */
    async showKnightChat(context, onResponse = null, onClose = null) {
        console.log('[TextInputDialogue] 🎬 Starting knight chat with context:', context);
        
        if (this.isActive) {
            console.warn('[TextInputDialogue] Chat already active');
            return;
        }
        
        this.isActive = true;
        this.currentContext = context;
        this.onResponseCallback = onResponse;
        this.onCloseCallback = onClose;
        
        console.log('[TextInputDialogue] 🎭 Creating chat interface...');
        
        return new Promise((resolve) => {
            this.createChatInterface(resolve);
        });
    }
    
    /**
     * Create the chat interface as exact copy of normal dialogue box
     * @param {Function} resolve - Promise resolve function
     */
    createChatInterface(resolve) {
        // Create main container - exactly like normal dialogue (NO full screen overlay)
        const chatContainer = document.createElement('div');
        chatContainer.id = 'knight-chat-container';
        chatContainer.style.cssText = `
            position: absolute;
            bottom: 10%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            max-width: 600px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 20px;
            line-height: 1.4;
            z-index: 1000;
            opacity: 1;
        `;
        
        // Create conversation display area (for showing chat history)
        const conversationArea = document.createElement('div');
        conversationArea.id = 'conversation-area';
        conversationArea.style.cssText = `
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
            padding-right: 5px;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        `;
        
        // Create input area section
        const inputSection = document.createElement('div');
        inputSection.style.cssText = `
            border-top: 1px solid #fff;
            padding-top: 15px;
        `;
        
        // Create input prompt text 
        const inputPrompt = document.createElement('p');
        const { knightType, knightSide } = this.currentContext;
        const knightTitle = 'Knight';
        inputPrompt.style.cssText = `
            margin: 0 0 10px 0;
            padding: 0;
            white-space: pre-wrap;
        `;
        inputPrompt.textContent = `What do you wish to ask the ${knightTitle}?`;
        
        // Create text input (exactly like dialogue but as input field)
        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.id = 'knight-chat-input';
        textInput.placeholder = 'Type your question here...';
        textInput.style.cssText = `
            width: 100%;
            box-sizing: border-box;
            background: none;
            border: 2px solid #fff;
            border-radius: 3px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            padding: 8px 12px;
            margin-bottom: 10px;
            outline: none;
        `;
        
        // Create options container (exactly like dialogue options)
        const optionsContainer = document.createElement('div');
        optionsContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        `;
        
        // Create send button (exactly like dialogue option)
        const sendButton = document.createElement('button');
        sendButton.textContent = 'Send Message';
        sendButton.style.cssText = `
            background: none;
            border: 2px solid #fff;
            border-radius: 3px;
            color: #ffff00;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            padding: 5px 10px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: left;
            transition: background-color 0.2s, color 0.2s;
            outline: none;
        `;
        
        // Create close button (exactly like dialogue option)
        const closeButton = document.createElement('button');
        closeButton.textContent = 'End Conversation';
        closeButton.style.cssText = `
            background: none;
            border: 2px solid #fff;
            border-radius: 3px;
            color: #ffff00;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            padding: 5px 10px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: left;
            transition: background-color 0.2s, color 0.2s;
            outline: none;
        `;
        
        // Add initial knight greeting (AI-enhanced if available)
        const greeting = this.getEnhancedInitialGreeting();
        this.addMessageToConversation(conversationArea, 'knight', greeting);
        
        // Add AI status message
        this.addAIStatusMessage(conversationArea);
        
        // Event handlers
        const sendMessage = async () => {
            const message = textInput.value.trim();
            if (!message) return;
            
            // 1. IMMEDIATELY show user message and clear input
            this.addMessageToConversation(conversationArea, 'user', message);
            textInput.value = '';
            
            // 2. Disable input and show loading state
            textInput.disabled = true;
            sendButton.disabled = true;
            
            // 3. Show animated thinking dots in button
            this.startButtonThinkingAnimation(sendButton);
            
            // 4. Create streaming message container 
            let streamingMessageId = null;
            let thinkingInterval = null;
            
            // 5. Give the browser time to render before starting AI processing
            console.log('[TextInputDialogue] 📝 Message sent, starting streaming response...');
            
            // Use setTimeout to ensure UI updates render first
            setTimeout(async () => {
                try {
                    console.log('[TextInputDialogue] 🤖 Starting AI processing...');
                    
                    // 6. Initialize AI service if not ready
                    if (!npcDialogueService.isReady) {
                        await npcDialogueService.initialize();
                    }
                    
                    // 7. Get NPC data for the current knight
                    const npcId = this.currentContext.knightType === 'lie' ? 'lie_knight' : 'truth_knight';
                    const npcData = getNPCData(npcId);
                    
                    if (!npcData) {
                        throw new Error(`NPC data not found for: ${npcId}`);
                    }
                    
                    // Add context-specific information to the NPC prompt
                    const contextualNPC = {
                        ...npcData,
                        prompt: this.buildContextualPrompt(npcData.prompt)
                    };
                    
                    // 8. Setup streaming callbacks
                    const streamingCallbacks = {
                        onStart: () => {
                            console.log('[TextInputDialogue] 🎯 Streaming started');
                            // Create streaming message container
                            streamingMessageId = this.addStreamingMessage(conversationArea, contextualNPC.name);
                            
                            // Start thinking animation
                            let dots = 0;
                            thinkingInterval = setInterval(() => {
                                dots = (dots + 1) % 4;
                                this.updateStreamingMessage(streamingMessageId, '.'.repeat(dots + 1));
                            }, 400);
                        },
                        onFirstToken: () => {
                            console.log('[TextInputDialogue] ✅ First token received - stopping thinking animation');
                            // Stop thinking animation when first token arrives
                            if (thinkingInterval) {
                                clearInterval(thinkingInterval);
                                thinkingInterval = null;
                            }
                        },
                        onToken: (newToken, fullText) => {
                            // Update the streaming message with new text in real-time
                            if (streamingMessageId) {
                                this.updateStreamingMessage(streamingMessageId, fullText);
                            }
                        },
                        onComplete: (finalResponse) => {
                            console.log('[TextInputDialogue] 🎉 Streaming complete:', finalResponse);
                            // Remove the blinking cursor when done
                            if (streamingMessageId) {
                                this.finishStreamingMessage(streamingMessageId);
                            }
                            
                            // 9. Call response callback if provided
                            if (this.onResponseCallback) {
                                this.onResponseCallback(message, finalResponse);
                            }
                            
                            // 10. Update conversation history
                            this.conversationHistory.push({ user: message, knight: finalResponse });
                        }
                    };
                    
                    // 9. Get AI response using streaming NPC dialogue service
                    const response = await npcDialogueService.generateResponse(
                        `${npcId}_${this.currentContext.knightSide}`, // Unique ID per knight
                        contextualNPC,
                        message,
                        streamingCallbacks
                    );
                    
                    console.log('[TextInputDialogue] ✅ Streaming response complete:', response);
                    
                } catch (error) {
                    console.error('[TextInputDialogue] Error during streaming response:', error);
                    
                    // Clean up any ongoing animations
                    if (thinkingInterval) {
                        clearInterval(thinkingInterval);
                        thinkingInterval = null;
                    }
                    
                    // Remove streaming message on error
                    if (streamingMessageId) {
                        this.removeStreamingMessage(streamingMessageId);
                    }
                    
                    // Show error message
                    this.addMessageToConversation(conversationArea, 'system', 'The knight\'s consciousness is not yet awakened. Please wait for AI to load, then try again.');
                }
                
                // 11. Re-enable input and stop thinking animation
                textInput.disabled = false;
                sendButton.disabled = false;
                this.stopButtonThinkingAnimation(sendButton);
                textInput.focus();
            }, 50); // Short delay to ensure UI updates
        };
        
        const closeChat = () => {
            // Stop any ongoing thinking animation
            this.stopButtonThinkingAnimation(sendButton);
            
            if (this.onCloseCallback) {
                this.onCloseCallback();
            }
            
            if (chatContainer && chatContainer.parentNode) {
                document.body.removeChild(chatContainer);
            }
            this.isActive = false;
            this.currentContext = null;
            resolve();
        };
        
        // Button hover effects (matching event room dialogue style)
        sendButton.addEventListener('mouseenter', () => {
            sendButton.style.backgroundColor = '#fff';
            sendButton.style.color = '#000';
        });
        sendButton.addEventListener('mouseleave', () => {
            sendButton.style.backgroundColor = 'transparent';
            sendButton.style.color = '#ffff00';
        });
        
        closeButton.addEventListener('mouseenter', () => {
            closeButton.style.backgroundColor = '#fff';
            closeButton.style.color = '#000';
        });
        closeButton.addEventListener('mouseleave', () => {
            closeButton.style.backgroundColor = 'transparent';
            closeButton.style.color = '#ffff00';
        });
        
        // Event listeners
        sendButton.addEventListener('click', sendMessage);
        closeButton.addEventListener('click', closeChat);
        
        textInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            } else if (event.key === 'Escape') {
                event.preventDefault();
                closeChat();
            }
        });
        
        // Handle input focus and gate choice detection
        textInput.addEventListener('focus', () => {
            textInput.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });
        textInput.addEventListener('blur', () => {
            textInput.style.backgroundColor = 'transparent';
        });
        
        textInput.addEventListener('input', () => {
            const message = textInput.value.toLowerCase();
            if (message.includes('i choose') || message.includes('i pick') || message.includes('left gate') || message.includes('right gate')) {
                textInput.style.borderColor = '#ff0';
                textInput.title = 'This might trigger a gate choice!';
            } else {
                textInput.style.borderColor = '#fff';
                textInput.title = '';
            }
        });
        
        // Assemble interface (exactly like dialogue structure)
        optionsContainer.appendChild(sendButton);
        optionsContainer.appendChild(closeButton);
        
        inputSection.appendChild(inputPrompt);
        inputSection.appendChild(textInput);
        inputSection.appendChild(optionsContainer);
        
        chatContainer.appendChild(conversationArea);
        chatContainer.appendChild(inputSection);
        
        document.body.appendChild(chatContainer);
        
        // Focus input
        setTimeout(() => {
            textInput.focus();
        }, 100);
    }
    
    /**
     * Add a message to the conversation display (matching event room dialogue text style)
     * @param {HTMLElement} conversationArea - Container element
     * @param {string} sender - 'user', 'knight', or 'system'
     * @param {string} message - Message content
     */
    addMessageToConversation(conversationArea, sender, message) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            margin-bottom: 15px;
            padding: 0;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 20px;
            line-height: 1.4;
            white-space: normal;
            width: 100%;
            box-sizing: border-box;
            word-wrap: break-word;
            overflow-wrap: break-word;
        `;
        
        // Create sender prefix and message text in same style as event dialogue
        let fullText = '';
        if (sender === 'user') {
            fullText = `> ${message}`;
            messageDiv.style.color = '#ffff00'; // Yellow for user input like dialogue options
        } else if (sender === 'knight') {
            const knightName = 'Knight';
            fullText = `${knightName}: "${message}"`;
            messageDiv.style.color = '#fff'; // White for knight response like dialogue text
        } else {
            fullText = `[${message}]`;
            messageDiv.style.color = '#ccc'; // Gray for system messages
            messageDiv.style.fontStyle = 'italic';
        }
        
        // Clean the text to remove extra whitespace and normalize spacing
        const cleanFullText = fullText
            .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
            .trim();               // Remove leading/trailing whitespace
            
        messageDiv.textContent = cleanFullText;
        conversationArea.appendChild(messageDiv);
        
        // Auto-scroll to bottom
        conversationArea.scrollTop = conversationArea.scrollHeight;
    }
    
    /**
     * Get enhanced initial greeting
     * @returns {string} - Initial greeting message
     */
    getEnhancedInitialGreeting() {
        const npcId = this.currentContext.knightType === 'lie' ? 'lie_knight' : 'truth_knight';
        const npcData = getNPCData(npcId);
        
        if (npcData) {
            // Return character-specific greeting
            if (this.currentContext.knightType === 'lie') {
                return "One of us speaks truth, one of us speaks falsehood. One path brings great riches, one brings certain doom. Ask me anything about these gates, and I shall answer.";
            } else {
                return "One of us lies, one of us tells the truth. One door leads to unbelievable treasure, one to your safe death. You may ask me any question about these gates or our sacred duty.";
            }
        }
        
        return "The knight stands ready to speak with you.";
    }

    /**
     * Get initial greeting based on knight type (fallback)
     * @returns {string} - Initial greeting message
     */
    getInitialGreeting() {
        const { knightType } = this.currentContext;
        
        if (knightType === 'lie') {
            return "One of us speaks truth, one of us speaks falsehood. One path brings great riches, one brings certain doom. Ask me about the gates, and I shall answer.";
        } else {
            return "One of us lies, one of us tells the truth. One door leads to unbelievable treasure, one to your safe death. Ask me about the gates or our duty.";
        }
    }
    
    /**
     * Check if dialogue is currently active
     * @returns {boolean} - Whether dialogue is active
     */
    isDialogueActive() {
        return this.isActive;
    }
    
    /**
     * Close the dialogue programmatically
     */
    close() {
        const container = document.getElementById('knight-chat-container');
        if (container && container.parentNode) {
            // Stop any ongoing thinking animation
            const sendButton = container.querySelector('button');
            if (sendButton) {
                this.stopButtonThinkingAnimation(sendButton);
            }
            
            document.body.removeChild(container);
            this.isActive = false;
            this.currentContext = null;
        }
    }
    
    /**
     * Get conversation history
     * @returns {Array} - Conversation history
     */
    getConversationHistory() {
        return [...this.conversationHistory];
    }
    
    /**
     * Clear conversation history
     */
    clearHistory() {
        this.conversationHistory = [];
        console.log('[TextInputDialogue] Conversation history cleared');
    }
    
    /**
     * Build contextual prompt with game state information
     */
    buildContextualPrompt(basePrompt) {
        const context = this.currentContext;
        let contextualPrompt = basePrompt;
        
        // Add explicit gate information based on knight type
        const knightGuardsTreasure = (context.knightSide === context.treasureGateSide);
        
        if (context.knightType === 'truth') {
            // Truth knight gets correct information
            contextualPrompt += `\n\nGATE INFORMATION:`;
            contextualPrompt += `\nYour gate leads to ${knightGuardsTreasure ? 'treasure' : 'death'}.`;
            contextualPrompt += `\nThe other gate leads to ${knightGuardsTreasure ? 'death' : 'treasure'}.`;
        } else {
            // Lie knight gets correct information but will lie about it (as instructed in their base prompt)
            contextualPrompt += `\n\nGATE INFORMATION:`;
            contextualPrompt += `\nYour gate leads to ${knightGuardsTreasure ? 'treasure' : 'death'}.`;
            contextualPrompt += `\nThe other gate leads to ${knightGuardsTreasure ? 'death' : 'treasure'}.`;
        }
        
        return contextualPrompt;
    }
    
    /**
     * Add AI status message to show current AI mode
     */
    addAIStatusMessage(conversationArea) {
        if (npcDialogueService.isReady) {
            // Don't show status when AI is ready - knights speak naturally
            return;
        } else if (npcDialogueService.isInitializing) {
            this.addMessageToConversation(conversationArea, 'system', "⏳ The knight's consciousness awakens with ancient AI magic... Enhanced responses incoming.");
        } else {
            this.addMessageToConversation(conversationArea, 'system', "⚠️ The knight will awaken with AI magic on first message. Please be patient during initialization...");
        }
    }
    
    /**
     * Add streaming message container (matching event room dialogue style)
     * @param {HTMLElement} conversationArea - Container element
     * @param {string} senderName - Name of the character speaking
     * @returns {string} - Message ID for updates
     */
    addStreamingMessage(conversationArea, senderName) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            margin-bottom: 15px;
            padding: 0;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 20px;
            line-height: 1.4;
            white-space: normal;
            word-wrap: break-word;
            overflow-wrap: break-word;
        `;
        
        const knightName = 'Knight';
        
        // Create the text content with streaming placeholder
        const textContent = document.createElement('div');
        textContent.style.cssText = `
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        `;
        textContent.innerHTML = `${knightName}: "<span class="streaming-text"></span><span class="cursor" style="color: #0f0; font-weight: bold; animation: blink 1s infinite;">|</span>"`;
        messageDiv.appendChild(textContent);
        
        // Add CSS animation for cursor blinking
        if (!document.getElementById('cursor-blink-style')) {
            const style = document.createElement('style');
            style.id = 'cursor-blink-style';
            style.textContent = `
                @keyframes blink {
                    0%, 50% { opacity: 1; }
                    51%, 100% { opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        // Generate unique ID
        const messageId = `streaming-${Date.now()}`;
        messageDiv.id = messageId;
        
        conversationArea.appendChild(messageDiv);
        
        // Auto-scroll to bottom
        conversationArea.scrollTop = conversationArea.scrollHeight;
        
        return messageId;
    }
    
    /**
     * Update streaming message with new text (like in working-ai-test.html)
     * @param {string} messageId - Message ID to update
     * @param {string} text - New text content
     */
    updateStreamingMessage(messageId, text) {
        const messageDiv = document.getElementById(messageId);
        if (messageDiv) {
            const textSpan = messageDiv.querySelector('.streaming-text');
            if (textSpan) {
                // Clean the text to remove extra whitespace and normalize spacing
                const cleanText = text
                    .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
                    .trim();               // Remove leading/trailing whitespace
                    
                textSpan.textContent = cleanText;
                
                // Ensure text container respects width bounds
                const parentWidth = messageDiv.parentElement.offsetWidth;
                if (parentWidth > 0) {
                    textSpan.parentElement.style.maxWidth = '100%';
                    textSpan.parentElement.style.width = '100%';
                }
                
                // Auto-scroll to keep the streaming message visible
                messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }
        }
    }
    
    /**
     * Finish streaming message by removing cursor (like in working-ai-test.html)
     * @param {string} messageId - Message ID to finish
     */
    finishStreamingMessage(messageId) {
        const messageDiv = document.getElementById(messageId);
        if (messageDiv) {
            const cursor = messageDiv.querySelector('.cursor');
            if (cursor) {
                cursor.remove(); // Remove blinking cursor when done
            }
        }
    }
    
    /**
     * Remove streaming message (for error handling)
     * @param {string} messageId - Message ID to remove
     */
    removeStreamingMessage(messageId) {
        const messageDiv = document.getElementById(messageId);
        if (messageDiv && messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }
    
    /**
     * Start animated thinking dots in send button
     * @param {HTMLElement} button - Button element to animate
     */
    startButtonThinkingAnimation(button) {
        // Store original button text
        button.dataset.originalText = button.textContent;
        
        let dots = 0;
        button.dataset.thinkingInterval = setInterval(() => {
            dots = (dots + 1) % 4;
            button.textContent = '•'.repeat(dots + 1);
        }, 400);
    }
    
    /**
     * Stop animated thinking dots and restore original button text
     * @param {HTMLElement} button - Button element to restore
     */
    stopButtonThinkingAnimation(button) {
        // Clear the animation interval
        if (button.dataset.thinkingInterval) {
            clearInterval(parseInt(button.dataset.thinkingInterval));
            delete button.dataset.thinkingInterval;
        }
        
        // Restore original button text
        button.textContent = button.dataset.originalText || 'Send Message';
        delete button.dataset.originalText;
    }
}

// Create singleton instance
export const textInputDialogue = new TextInputDialogue();

// Export default instance
export default textInputDialogue;