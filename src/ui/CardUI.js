import * as THREE from 'three';

/**
 * CardUI - Manages the card hand display overlay
 */
export class CardUI {
    constructor(cardSystem, scene) {
        this.cardSystem = cardSystem;
        this.scene = scene;
        
        // UI state
        this.visible = true;
        this.selectedCardIndex = -1;
        
        // Mobile detection
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);
        
        // Card display properties
        this.cardSpacing = 120; // Pixels between cards
        this.cardWidth = 100;
        this.cardHeight = 140;
        this.handYPosition = 20; // Same height as weapon info container
        
        // 3D rendering for cards
        this.cardRenderer = null;
        this.cardScene = new THREE.Scene();
        this.cardCamera = null;
        this.cardCanvases = new Map(); // cardId -> canvas element
        
        // HTML elements
        this.overlayElement = null;
        this.cardContainer = null;
        this.descriptionElement = null;
        
        // Input handling
        this.isDragging = false;
        this.dragStartPos = null;
        this.dragCardId = null;

        // Mobile touch handling
        this.activeTouchCard = null;
        this.touchStartTime = 0;
        this.touchMoved = false;
        this.startTouchPos = null;
        this.holdTimer = null;
        this.holdInfoShown = false;
        
        this.createUIElements();
        this.setupCardRenderer();
        this.setupEventListeners();
        this.setupResizeHandler();

        console.log('[CardUI] Initialized card UI overlay');
        
        // Add a manual trigger for debugging
        window.debugCardUI = this;
    }
    
    /**
     * Create the main UI elements
     */
    createUIElements() {
        // Remove any existing card UI
        const existingOverlay = document.getElementById('card-ui-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }
        
        // Create main overlay container
        this.overlayElement = document.createElement('div');
        this.overlayElement.id = 'card-ui-overlay';

        // Different positioning for mobile vs desktop (similar to weapon info)
        const mobileStyles = this.isMobile ? `
            bottom: ${this.handYPosition}px;
            left: 195px; /* Position next to left joystick, mirroring weapon info positioning */
        ` : `
            bottom: ${this.handYPosition}px;
            left: 20px; /* Desktop positioning */
        `;

        // Match weapon info container height (25% smaller)
        const overlayHeight = this.isMobile ? 75 : 90;

        this.overlayElement.style.cssText = `
            position: fixed;
            ${mobileStyles}
            width: auto;
            height: ${overlayHeight}px;
            pointer-events: none;
            z-index: 2000;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        `;
        
        // Create card container
        this.cardContainer = document.createElement('div');
        this.cardContainer.id = 'card-container';
        // Match weapon info container height (25% smaller)
        const containerHeight = this.isMobile ? 75 : 90;

        this.cardContainer.style.cssText = `
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            gap: 10px;
            height: ${containerHeight}px;
            pointer-events: auto;
            z-index: 2001;
            position: relative;
        `;
        
        // Create description container (dialogue-style overlay)
        this.descriptionElement = document.createElement('div');
        this.descriptionElement.id = 'card-description';
        this.descriptionElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            max-width: 500px;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.8);
            border: 4px solid #fff;
            border-radius: 5px;
            color: #fff;
            font-family: 'Courier New', Courier, monospace;
            font-size: 18px;
            line-height: 1.4;
            text-align: left;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 2000;
        `;
        
        this.overlayElement.appendChild(this.cardContainer);
        this.overlayElement.appendChild(this.descriptionElement);
        document.body.appendChild(this.overlayElement);
    }
    
    /**
     * Setup 3D renderer for card previews
     */
    setupCardRenderer() {
        // Create camera for card rendering (1:1 aspect ratio for cube)
        this.cardCamera = new THREE.PerspectiveCamera(45, 1, 0.1, 100);
        this.cardCamera.position.set(0, 0, 2.5); // Adjusted distance for better size
        this.cardCamera.lookAt(0, 0, 0);
        
        // Setup lighting for card scene
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        this.cardScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        this.cardScene.add(directionalLight);
    }
    
    /**
     * Setup resize handler for responsive positioning
     */
    setupResizeHandler() {
        window.addEventListener('resize', this.onWindowResize.bind(this));
    }

    /**
     * Handle window resize to adjust positioning
     */
    onWindowResize() {
        if (!this.overlayElement) return;

        // Store previous mobile state
        const wasMobile = this.isMobile;

        // Update mobile detection
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);

        // If mobile state changed, re-setup event listeners
        if (wasMobile !== this.isMobile) {
            this.removeEventListeners();
            this.setupEventListeners();
        }

        // Adjust positioning and sizing for different screen sizes (25% smaller)
        if (window.innerWidth < 600 || window.innerHeight < 400) {
            if (this.isMobile) {
                // Extra small mobile - move closer to edge and smaller size
                this.overlayElement.style.left = '175px';
                this.overlayElement.style.height = '60px';
                this.cardContainer.style.height = '60px';
            } else {
                // Small desktop
                this.overlayElement.style.left = '20px';
                this.overlayElement.style.height = '60px';
                this.cardContainer.style.height = '60px';
            }
        } else {
            if (this.isMobile) {
                // Normal mobile size
                this.overlayElement.style.left = '195px';
                this.overlayElement.style.height = '75px';
                this.cardContainer.style.height = '75px';
            } else {
                // Normal desktop size
                this.overlayElement.style.left = '20px';
                this.overlayElement.style.height = '90px';
                this.cardContainer.style.height = '90px';
            }
        }
    }

    /**
     * Remove event listeners
     */
    removeEventListeners() {
        if (this.boundHandlers && this.cardContainer) {
            // Remove mouse events
            this.cardContainer.removeEventListener('mousedown', this.boundHandlers.mouseDown);

            // Remove touch events
            this.cardContainer.removeEventListener('touchstart', this.boundHandlers.touchStart);
        }

        if (this.boundHandlers) {
            // Remove document events
            document.removeEventListener('mousemove', this.boundHandlers.mouseMove);
            document.removeEventListener('mouseup', this.boundHandlers.mouseUp);
            document.removeEventListener('touchmove', this.boundHandlers.touchMove);
            document.removeEventListener('touchend', this.boundHandlers.touchEnd);
        }
    }

    /**
     * Setup event listeners for card interactions
     */
    setupEventListeners() {
        // Store bound functions for proper removal
        if (!this.boundHandlers) {
            this.boundHandlers = {
                mouseDown: this.handleMouseDown.bind(this),
                mouseMove: this.handleMouseMove.bind(this),
                mouseUp: this.handleMouseUp.bind(this),
                touchStart: this.handleTouchStart.bind(this),
                touchMove: this.handleTouchMove.bind(this),
                touchEnd: this.handleTouchEnd.bind(this)
            };
        }

        // Mouse events for desktop
        if (!this.isMobile) {
            this.cardContainer.addEventListener('mousedown', this.boundHandlers.mouseDown);
            document.addEventListener('mousemove', this.boundHandlers.mouseMove);
            document.addEventListener('mouseup', this.boundHandlers.mouseUp);
        }

        // Touch events for mobile (always add these for hybrid devices)
        this.cardContainer.addEventListener('touchstart', this.boundHandlers.touchStart, { passive: false });
        document.addEventListener('touchmove', this.boundHandlers.touchMove, { passive: false });
        document.addEventListener('touchend', this.boundHandlers.touchEnd);

        // Note: Individual card hover events are set up in createCardElement
    }
    
    /**
     * Handle mouse down events
     */
    handleMouseDown(event) {
        const cardElement = event.target.closest('.card-element');
        if (cardElement) {
            const cardId = cardElement.dataset.cardId;
            this.startDrag(cardId, { x: event.clientX, y: event.clientY });
            event.preventDefault();
        }
    }
    
    /**
     * Handle mouse move events
     */
    handleMouseMove(event) {
        if (this.isDragging) {
            this.updateDrag({ x: event.clientX, y: event.clientY });
            event.preventDefault();
        }
    }
    
    /**
     * Handle mouse up events
     */
    async handleMouseUp(event) {
        if (this.isDragging) {
            await this.endDrag({ x: event.clientX, y: event.clientY });
            event.preventDefault();
        }
    }
    
    /**
     * Handle touch start events
     */
    handleTouchStart(event) {
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const cardElement = event.target.closest('.card-element');

            if (cardElement) {
                // Store touch start info for both dragging and hold
                this.activeTouchCard = cardElement;
                this.touchStartTime = Date.now();
                this.touchMoved = false;
                this.startTouchPos = { x: touch.clientX, y: touch.clientY };
                this.holdInfoShown = false; // Track if hold info was already shown

                // Set up hold timer for 1 second - opens info immediately when timer fires
                this.holdTimer = setTimeout(() => {
                    if (this.activeTouchCard && !this.touchMoved && !this.isDragging) {
                        // Hold detected - show card info immediately
                        const cardId = this.activeTouchCard.dataset.cardId;
                        const card = this.cardSystem.getCard(cardId);
                        if (card) {
                            this.showCardDescription(card);
                            this.holdInfoShown = true;

                            // Auto-hide after 3 seconds
                            setTimeout(() => {
                                this.hideCardDescription();
                            }, 3000);
                        }
                    }
                }, 1000); // 1 second hold - info opens immediately when this fires

                event.preventDefault();
                event.stopPropagation();
            }
        }
    }
    
    /**
     * Handle touch move events
     */
    handleTouchMove(event) {
        if (event.touches.length === 1 && this.activeTouchCard) {
            const touch = event.touches[0];

            if (this.startTouchPos) {
                const deltaX = touch.clientX - this.startTouchPos.x;
                const deltaY = touch.clientY - this.startTouchPos.y;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                // If moved more than 10 pixels, mark as moved and start dragging
                if (distance > 10) {
                    this.touchMoved = true;

                    // Clear hold timer since we're dragging
                    if (this.holdTimer) {
                        clearTimeout(this.holdTimer);
                        this.holdTimer = null;
                    }

                    // Start dragging if not already dragging
                    if (!this.isDragging) {
                        const cardId = this.activeTouchCard.dataset.cardId;
                        this.startDrag(cardId, this.startTouchPos);
                    }
                }
            }

            // Continue dragging if already started
            if (this.isDragging) {
                this.updateDrag({ x: touch.clientX, y: touch.clientY });
                event.preventDefault();
            }
        }
    }
    
    /**
     * Handle touch end events
     */
    async handleTouchEnd(event) {
        const touch = event.changedTouches[0];

        if (this.isDragging) {
            // End dragging
            await this.endDrag({ x: touch.clientX, y: touch.clientY });
            event.preventDefault();
        } else if (this.activeTouchCard && this.touchStartTime) {
            // Clear hold timer
            if (this.holdTimer) {
                clearTimeout(this.holdTimer);
                this.holdTimer = null;
            }

            // Check for tap (no dragging occurred and hold info wasn't already shown)
            if (!this.touchMoved && !this.holdInfoShown) {
                // Tap detected - show card info
                const cardId = this.activeTouchCard.dataset.cardId;
                const card = this.cardSystem.getCard(cardId);
                if (card) {
                    this.showCardDescription(card);

                    // Auto-hide after 3 seconds on mobile
                    setTimeout(() => {
                        this.hideCardDescription();
                    }, 3000);
                }
                event.preventDefault();
                event.stopPropagation();
            }
        }

        // Clean up touch data
        this.activeTouchCard = null;
        this.touchStartTime = 0;
        this.touchMoved = false;
        this.startTouchPos = null;
        this.holdInfoShown = false;
        if (this.holdTimer) {
            clearTimeout(this.holdTimer);
            this.holdTimer = null;
        }
    }
    
    /**
     * Start dragging a card
     */
    startDrag(cardId, position) {
        this.isDragging = true;
        this.dragCardId = cardId;
        this.dragStartPos = position;

        // Hide card description when dragging starts
        this.hideCardDescription();

        // Notify card system
        this.cardSystem.startDragging(cardId, position);

        // Visual feedback
        const cardElement = this.cardContainer.querySelector(`[data-card-id="${cardId}"]`);
        if (cardElement) {
            cardElement.style.transform = 'scale(1.1) translateY(-10px)';
            cardElement.style.zIndex = '1700';
        }
    }
    
    /**
     * Update drag position
     */
    updateDrag(position) {
        if (!this.isDragging || !this.dragCardId) return;
        
        const cardElement = this.cardContainer.querySelector(`[data-card-id="${this.dragCardId}"]`);
        if (cardElement) {
            const deltaX = position.x - this.dragStartPos.x;
            const deltaY = position.y - this.dragStartPos.y;
            cardElement.style.transform = `scale(1.1) translate(${deltaX}px, ${deltaY - 10}px)`;
        }
    }
    
    /**
     * End dragging
     */
    async endDrag(position) {
        if (!this.isDragging || !this.dragCardId) return;
        
        const cardElement = this.cardContainer.querySelector(`[data-card-id="${this.dragCardId}"]`);
        if (cardElement) {
            cardElement.style.transform = '';
            cardElement.style.zIndex = '';
        }
        
        // Notify card system (now async for 3D animation)
        const wasPlayed = await this.cardSystem.stopDragging(position);
        
        this.isDragging = false;
        this.dragCardId = null;
        this.dragStartPos = null;
        
        // Update display if card was played
        if (wasPlayed) {
            this.updateCardDisplay();
        }
    }
    
    /**
     * Show card description in dialogue style
     */
    showCardDescription(card) {
        const displayData = card.getDisplayData();

        // Create dialogue-style content
        this.descriptionElement.innerHTML = `
            <div style="font-weight: bold; color: #ffd700; margin-bottom: 10px; font-size: 22px;">
                ${displayData.name}
            </div>
            <div style="margin-bottom: 10px; white-space: pre-wrap; text-align: left; padding: 0; text-indent: 0;">${displayData.description}</div>
            <div style="font-size: 14px; color: #ccc; margin-top: 10px; font-style: italic;">
                Drag on screen to cast
            </div>
        `;
        this.descriptionElement.style.opacity = '1';
    }
    
    /**
     * Hide card description
     */
    hideCardDescription() {
        this.descriptionElement.style.opacity = '0';
    }
    
    /**
     * Update the card display
     */
    updateCardDisplay() {
        if (!this.visible) {
            console.log('[CardUI] updateCardDisplay called but UI is not visible');
            return;
        }

        console.log('[CardUI] updateCardDisplay called, visible:', this.visible);

        // Clear existing card elements
        this.cardContainer.innerHTML = '';
        this.cardCanvases.clear();

        // Get cards from card system
        const cards = this.cardSystem.getCardsInOrder();
        console.log('[CardUI] Retrieved cards from system:', cards.length, cards.map(c => c.name));

        if (cards.length === 0) {
            // Hide the entire overlay if no cards
            this.overlayElement.style.display = 'none';
            console.log('[CardUI] No cards, hiding overlay');
            return;
        }

        // Show the overlay if we have cards
        this.overlayElement.style.display = 'flex';
        console.log('[CardUI] Showing overlay with', cards.length, 'cards');

        // Create card elements (no need for complex positioning since we're using flexbox)
        cards.forEach((card, index) => {
            console.log(`[CardUI] Creating card element for: ${card.name} (${card.cardType})`);
            this.createCardElement(card, index, 0); // No x offset needed with flexbox
        });
    }
    
    /**
     * Create a card element
     */
    createCardElement(card, index, xOffset) {
        console.log(`[CardUI] createCardElement called for card: ${card.name} (${card.cardType})`);
        
        const cardElement = document.createElement('div');
        cardElement.className = 'card-element';
        cardElement.dataset.cardId = card.id;

        // Match weapon info container dimensions (cube, 25% smaller)
        const cardSize = this.isMobile ? 75 : 90;

        cardElement.style.cssText = `
            position: relative;
            width: ${cardSize}px;
            height: ${cardSize}px;
            cursor: pointer;
            transition: transform 0.2s ease;
            flex-shrink: 0;
            background-color: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            z-index: 2002;
        `;
        
        console.log(`[CardUI] Created card element with size: ${cardSize}px`);
        
        // Create canvas for 3D card rendering
        const canvas = document.createElement('canvas');
        canvas.width = cardSize;
        canvas.height = cardSize;
        canvas.style.cssText = `
            position: absolute;
            top: 2px;
            left: 2px;
            width: calc(100% - 4px);
            height: calc(100% - 4px);
            border-radius: 6px;
            background-color: transparent;
            pointer-events: none;
            z-index: 1;
        `;

        // Create transparent interaction overlay
        const interactionOverlay = document.createElement('div');
        interactionOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: transparent;
            z-index: 2;
            cursor: pointer;
        `;

        cardElement.appendChild(canvas);
        cardElement.appendChild(interactionOverlay);
        this.cardContainer.appendChild(cardElement);

        // Store canvas for rendering
        this.cardCanvases.set(card.id, canvas);

        // Add hover events to individual card (desktop)
        cardElement.addEventListener('mouseenter', (event) => {
            if (!this.isDragging) {
                this.showCardDescription(card);
            }
        });

        cardElement.addEventListener('mouseleave', (event) => {
            if (!this.isDragging) {
                this.hideCardDescription();
            }
        });

        // Touch handling is now managed at the CardUI level

        // Setup 3D rendering for this card
        this.setupCardRendering(card, canvas);
    }
    
    /**
     * Setup 3D rendering for a specific card
     */
    setupCardRendering(card, canvas) {
        console.log(`[CardUI] setupCardRendering called for card: ${card.name}`);
        console.log(`[CardUI] Card model exists:`, !!card.cardModel);
        
        // Create renderer for this card
        const renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            alpha: true,
            antialias: true
        });

        // Use cube dimensions for rendering
        const cardSize = this.isMobile ? 75 : 90;
        renderer.setSize(cardSize, cardSize);
        renderer.setClearColor(0x000000, 0);
        
        // Clone card model for display
        if (!card.cardModel) {
            console.error(`[CardUI] No card model found for: ${card.name}`);
            return;
        }
        
        const cardModel = card.cardModel.clone();
        cardModel.position.set(0, 0, 0);
        cardModel.rotation.set(0, 0, 0);
        cardModel.scale.set(0.9, 0.9, 0.9); // Smaller models inside containers
        
        console.log(`[CardUI] Cloned card model for rendering: ${card.name}`);
        
        // Create temporary scene for this card
        const tempScene = new THREE.Scene();
        tempScene.add(cardModel);
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
        tempScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        tempScene.add(directionalLight);
        
        // Render the card
        renderer.render(tempScene, this.cardCamera);
        
        // Store for updates
        card.uiRenderer = renderer;
        card.uiScene = tempScene;
        card.uiModel = cardModel;
    }
    
    /**
     * Update card UI (called from game loop)
     */
    update(deltaTime) {
        if (!this.visible) return;
        
        // Update card display if hand changed
        const currentCardCount = this.cardSystem.getCardsInOrder().length;
        const displayedCardCount = this.cardContainer.children.length;
        
        if (currentCardCount !== displayedCardCount) {
            console.log(`[CardUI] Card count changed: ${displayedCardCount} -> ${currentCardCount}, updating display`);
            this.updateCardDisplay();
        }
        
        // Update 3D card renderings
        this.cardSystem.getCardsInOrder().forEach(card => {
            if (card.uiRenderer && card.uiScene && card.uiModel) {
                // Update card model animation
                card.update(deltaTime);
                
                // Re-render if needed
                card.uiRenderer.render(card.uiScene, this.cardCamera);
            }
        });
    }
    
    /**
     * Set UI visibility
     */
    setVisible(visible) {
        this.visible = visible;
        if (this.overlayElement) {
            this.overlayElement.style.display = visible ? 'flex' : 'none';
        }
    }
    
    /**
     * Check if UI is visible
     */
    isVisible() {
        return this.visible;
    }
    
    /**
     * Dispose of the card UI
     */
    dispose() {
        // Clean up event listeners
        this.removeEventListeners();
        window.removeEventListener('resize', this.onWindowResize.bind(this));

        // Clean up renderers
        this.cardSystem.getCardsInOrder().forEach(card => {
            if (card.uiRenderer) {
                card.uiRenderer.dispose();
                card.uiRenderer = null;
            }
            if (card.uiScene) {
                card.uiScene = null;
            }
            if (card.uiModel) {
                card.uiModel = null;
            }
        });

        // Remove HTML elements
        if (this.overlayElement) {
            this.overlayElement.remove();
            this.overlayElement = null;
        }

        this.cardCanvases.clear();

        console.log('[CardUI] Card UI disposed');
    }
}
