/**
 * BossHealthBar.js
 * 
 * Manages the boss health bar UI component that appears at the top center of the screen
 * during boss and miniboss battles. Features customizable colors and smooth animations.
 */

class BossHealthBar {
    constructor() {
        this.container = document.getElementById('boss-health-container');
        this.nameElement = document.getElementById('boss-name');
        this.healthBar = document.getElementById('boss-health-bar');
        this.healthFill = document.getElementById('boss-health-fill');
        
        if (!this.container) {
            console.error('Boss health container not found!');
            return;
        }
        
        this.isVisible = false;
        this.currentHealth = 100;
        this.maxHealth = 100;
        this.currentBoss = null;
        this.currentColor = 'red';
        
        // Animation properties
        this.animationSpeed = 0.3; // seconds
        this.pulseAnimation = null;
    }

    /**
     * Show the boss health bar with specified boss data
     * @param {string} bossName - Display name of the boss
     * @param {number} maxHealth - Maximum health of the boss
     * @param {string} color - Color theme (red, magenta, purple, blue, green, orange, yellow)
     * @param {Object} bossRef - Reference to the boss object for tracking
     */
    show(bossName, maxHealth, color = 'red', bossRef = null) {
        if (!this.container) return;
        
        console.log(`[BossHealthBar] Showing boss health bar for ${bossName} (${maxHealth} HP, ${color} theme)`);
        
        this.currentBoss = bossRef;
        this.maxHealth = maxHealth;
        this.currentHealth = maxHealth;
        this.currentColor = color;
        
        // Set boss name
        this.nameElement.textContent = bossName;
        
        // Set color theme
        this.setColorTheme(color);
        
        // Reset health bar to full
        this.healthFill.style.width = '100%';
        
        // Show the container
        this.container.classList.add('visible');
        this.isVisible = true;
        
        // Add a subtle pulse effect when first shown
        this.addShowAnimation();
    }

    /**
     * Hide the boss health bar
     */
    hide() {
        if (!this.container) return;
        
        console.log('[BossHealthBar] Hiding boss health bar');
        
        this.container.classList.remove('visible');
        this.isVisible = false;
        this.currentBoss = null;
        
        // Clear any animations
        if (this.pulseAnimation) {
            this.pulseAnimation.cancel();
            this.pulseAnimation = null;
        }
    }

    /**
     * Update the health bar with current health
     * @param {number} currentHealth - Current health value
     */
    updateHealth(currentHealth) {
        if (!this.container || !this.isVisible) return;
        
        this.currentHealth = Math.max(0, Math.min(currentHealth, this.maxHealth));
        const healthPercentage = (this.currentHealth / this.maxHealth) * 100;
        
        // Update the fill width with smooth animation
        this.healthFill.style.width = `${healthPercentage}%`;
        
        // Add damage pulse effect when health decreases significantly
        if (currentHealth < this.currentHealth - (this.maxHealth * 0.1)) {
            this.addDamageEffect();
        }
        
        // Change color as health gets low
        if (healthPercentage <= 25 && this.currentColor !== 'red') {
            this.addCriticalHealthEffect();
        }
        
        console.log(`[BossHealthBar] Health updated: ${this.currentHealth}/${this.maxHealth} (${healthPercentage.toFixed(1)}%)`);
    }

    /**
     * Set the color theme of the health bar
     * @param {string} color - Color name (red, magenta, purple, blue, green, orange, yellow)
     */
    setColorTheme(color) {
        if (!this.container) return;
        
        // Remove all existing color classes
        const colorClasses = ['boss-color-red', 'boss-color-magenta', 'boss-color-purple', 
                             'boss-color-blue', 'boss-color-green', 'boss-color-orange', 'boss-color-yellow'];
        colorClasses.forEach(cls => this.container.classList.remove(cls));
        
        // Add the new color class
        this.container.classList.add(`boss-color-${color}`);
        this.currentColor = color;
        
        console.log(`[BossHealthBar] Color theme set to: ${color}`);
    }

    /**
     * Get boss health bar data for external systems
     * @returns {Object} Health bar state
     */
    getHealthData() {
        return {
            isVisible: this.isVisible,
            currentHealth: this.currentHealth,
            maxHealth: this.maxHealth,
            healthPercentage: this.maxHealth > 0 ? (this.currentHealth / this.maxHealth) * 100 : 0,
            currentBoss: this.currentBoss,
            color: this.currentColor
        };
    }

    /**
     * Update method called from the game loop
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        if (!this.isVisible || !this.currentBoss) return;
        
        // Auto-update health if boss reference is available
        if (this.currentBoss.userData && this.currentBoss.userData.aiBrain) {
            const aiBrain = this.currentBoss.userData.aiBrain;
            const userData = this.currentBoss.userData;
            
            // DEBUG: Log health values
            console.log(`[BossHealthBar DEBUG] Health check:`, {
                aiBrainCurrentHealth: aiBrain.currentHealth,
                aiBrainMaxHealth: aiBrain.maxHealth,
                userDataHealth: userData.health,
                currentDisplayedHealth: this.currentHealth
            });
            
            // Try multiple health sources
            let currentBossHealth = aiBrain.currentHealth;
            if (currentBossHealth === undefined) {
                currentBossHealth = userData.health; // Fallback to userData.health
                console.log(`[BossHealthBar DEBUG] Using userData.health as fallback: ${currentBossHealth}`);
            }
            
            if (currentBossHealth !== undefined && currentBossHealth !== this.currentHealth) {
                console.log(`[BossHealthBar DEBUG] Updating health from ${this.currentHealth} to ${currentBossHealth}`);
                this.updateHealth(currentBossHealth);
                
                // Auto-hide when boss is defeated
                if (currentBossHealth <= 0) {
                    setTimeout(() => {
                        this.hide();
                    }, 1000); // Delay to show the empty bar briefly
                }
            }
        }
    }

    /**
     * Add a subtle animation when the health bar is first shown
     * @private
     */
    addShowAnimation() {
        if (!this.container) return;
        
        // Scale animation
        this.container.style.transform = 'translateX(-50%) scale(0.8)';
        this.container.style.transition = 'transform 0.3s ease-out, opacity 0.5s ease-in-out, visibility 0.5s ease-in-out';
        
        // Animate to normal scale
        setTimeout(() => {
            this.container.style.transform = 'translateX(-50%) scale(1.0)';
        }, 50);
        
        // Reset transform after animation
        setTimeout(() => {
            this.container.style.transform = 'translateX(-50%)';
            this.container.style.transition = 'opacity 0.5s ease-in-out, visibility 0.5s ease-in-out';
        }, 350);
    }

    /**
     * Add visual effect when boss takes significant damage
     * @private
     */
    addDamageEffect() {
        if (!this.container) return;
        
        // Flash the border red briefly
        const originalBorderColor = this.container.style.borderColor;
        this.nameElement.style.borderColor = '#ff0000';
        this.healthBar.style.borderColor = '#ff0000';
        
        setTimeout(() => {
            this.nameElement.style.borderColor = originalBorderColor;
            this.healthBar.style.borderColor = originalBorderColor;
        }, 200);
        
        // Shake effect
        if (this.pulseAnimation) {
            this.pulseAnimation.cancel();
        }
        
        this.pulseAnimation = this.container.animate([
            { transform: 'translateX(-50%) translateY(0px)' },
            { transform: 'translateX(-50%) translateY(-2px)' },
            { transform: 'translateX(-50%) translateY(1px)' },
            { transform: 'translateX(-50%) translateY(0px)' }
        ], {
            duration: 200,
            easing: 'ease-out'
        });
    }

    /**
     * Add critical health warning effects
     * @private
     */
    addCriticalHealthEffect() {
        if (!this.container) return;
        
        // Add pulsing red glow to the name
        this.nameElement.style.textShadow = '0 0 10px #ff0000, 0 0 20px #ff0000';
        this.nameElement.style.animation = 'pulse 1s infinite';
        
        console.log('[BossHealthBar] Critical health effects activated');
    }

    /**
     * Determine boss color based on boss type/name
     * @param {string} bossType - Boss type identifier
     * @returns {string} Color name
     */
    static getBossColor(bossType) {
        const colorMap = {
            'nairabos': 'red',
            'sairabos': 'magenta',
            'catacombs_overlord': 'purple',
            'catacomb_overlord': 'purple',
            'magma_golem': 'orange',
            'skeleton_boss': 'blue',
            'bat_boss': 'green',
            'zombie_boss': 'green',
            'firefly_boss': 'yellow'
        };
        
        return colorMap[bossType.toLowerCase()] || 'red';
    }

    /**
     * Get the display name for a boss type
     * @param {string} bossType - Boss type identifier
     * @returns {string} Display name
     */
    static getBossDisplayName(bossType) {
        const nameMap = {
            'nairabos': 'Nairabos The Fallen Tormentor',
            'sairabos': 'Sairabos',
            'catacombs_overlord': 'Catacombs Overlord',
            'catacomb_overlord': 'Catacombs Overlord',
            'magma_golem': 'Magma Golem',
            'skeleton_boss': 'Skeleton Boss',
            'bat_boss': 'Bat Boss',
            'zombie_boss': 'Zombie Boss',
            'firefly_boss': 'Firefly Boss'
        };
        
        return nameMap[bossType.toLowerCase()] || bossType.toUpperCase();
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.hide();
        if (this.pulseAnimation) {
            this.pulseAnimation.cancel();
            this.pulseAnimation = null;
        }
        this.currentBoss = null;
    }
}

export default BossHealthBar;