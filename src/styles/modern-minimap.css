/**
 * Modern Minimap Styles
 * 
 * Stunning visual design with glassmorphism effects, smooth animations,
 * and responsive behavior for the modern minimap system.
 */

/* Main minimap container */
.modern-minimap {
    /* Glassmorphism base */
    background: rgba(10, 10, 15, 0.85);
    backdrop-filter: blur(12px) saturate(1.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    
    /* Enhanced visual effects */
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    
    /* Smooth transitions */
    transition: 
        opacity 0.2s ease-out,
        transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
        box-shadow 0.3s ease-out;
    
    /* Interaction states */
    will-change: transform, opacity;
}

.modern-minimap:hover {
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    
    border-color: rgba(255, 255, 255, 0.15);
}

/* SVG container optimizations */
.modern-minimap svg {
    display: block;
    width: 100%;
    height: 100%;
    overflow: visible;
    
    /* Hardware acceleration */
    transform: translateZ(0);
    will-change: transform;
}

/* Room elements */
.modern-minimap .room-element {
    /* Smooth transitions for all room state changes */
    transition: 
        fill 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        filter 0.3s ease-out;
    
    /* Hardware acceleration */
    will-change: fill, stroke, transform;
}

/* Room type specific styles */
.modern-minimap .room-unvisited {
    fill: rgba(60, 60, 70, 0.3);
    stroke: rgba(60, 60, 70, 0.5);
    stroke-width: 1;
}

.modern-minimap .room-visited {
    fill: rgba(120, 130, 150, 0.8);
    stroke: rgba(140, 150, 170, 0.9);
    stroke-width: 1;
}

.modern-minimap .room-current {
    fill: rgba(255, 255, 255, 0.95);
    stroke: rgba(255, 255, 255, 1.0);
    stroke-width: 2;
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6));
}

.modern-minimap .room-boss {
    fill: rgba(255, 80, 80, 0.9);
    stroke: rgba(255, 100, 100, 1.0);
    stroke-width: 2;
    filter: drop-shadow(0 0 4px rgba(255, 80, 80, 0.5));
}

.modern-minimap .room-event {
    fill: rgba(80, 150, 255, 0.9);
    stroke: rgba(100, 170, 255, 1.0);
    stroke-width: 2;
    filter: drop-shadow(0 0 4px rgba(80, 150, 255, 0.5));
}

.modern-minimap .room-start {
    fill: rgba(80, 255, 120, 0.9);
    stroke: rgba(100, 255, 140, 1.0);
    stroke-width: 2;
    filter: drop-shadow(0 0 4px rgba(80, 255, 120, 0.5));
}

.modern-minimap .room-secret {
    fill: rgba(200, 120, 255, 0.8);
    stroke: rgba(220, 140, 255, 1.0);
    stroke-width: 2;
    filter: drop-shadow(0 0 4px rgba(200, 120, 255, 0.5));
}

/* Player indicator */
.modern-minimap .player-indicator {
    fill: rgba(255, 220, 100, 1.0);
    filter: drop-shadow(0 0 6px rgba(255, 220, 100, 0.8));
    
    /* Pulsing animation */
    animation: player-pulse 2s ease-in-out infinite;
}

@keyframes player-pulse {
    0%, 100% { 
        r: 4;
        fill-opacity: 1.0;
    }
    50% { 
        r: 5;
        fill-opacity: 0.8;
    }
}

/* Connection lines (if used) */
.modern-minimap .connection-line {
    stroke: rgba(120, 130, 150, 0.4);
    stroke-width: 1;
    stroke-dasharray: 2, 2;
    
    transition: stroke 0.3s ease-out;
}

.modern-minimap .connection-line.active {
    stroke: rgba(255, 255, 255, 0.6);
    stroke-dasharray: none;
}

/* Responsive design */
@media (max-width: 768px) {
    .modern-minimap {
        /* Smaller on mobile */
        transform: scale(0.8);
        transform-origin: top right;
    }
}

@media (max-height: 600px) {
    .modern-minimap {
        /* Adjust for landscape mobile */
        transform: scale(0.7);
        transform-origin: top right;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modern-minimap {
        background: rgba(0, 0, 0, 0.9);
        border-color: rgba(255, 255, 255, 0.3);
    }
    
    .modern-minimap .room-visited {
        fill: rgba(200, 200, 200, 0.9);
        stroke: rgba(255, 255, 255, 1.0);
    }
    
    .modern-minimap .room-current {
        fill: rgba(255, 255, 255, 1.0);
        stroke: rgba(255, 255, 255, 1.0);
        stroke-width: 3;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .modern-minimap,
    .modern-minimap .room-element,
    .modern-minimap .player-indicator {
        transition: none;
        animation: none;
    }
    
    .modern-minimap .player-indicator {
        r: 4; /* Fixed size */
    }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    .modern-minimap {
        background: rgba(5, 5, 10, 0.9);
        border-color: rgba(255, 255, 255, 0.08);
    }
}

/* Performance optimizations */
.modern-minimap * {
    /* Enable hardware acceleration for all elements */
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Loading state */
.modern-minimap.loading {
    opacity: 0.5;
    pointer-events: none;
}

.modern-minimap.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: loading-spin 1s linear infinite;
}

@keyframes loading-spin {
    to { transform: rotate(360deg); }
}

/* Error state */
.modern-minimap.error {
    background: rgba(40, 10, 10, 0.9);
    border-color: rgba(255, 80, 80, 0.3);
}

.modern-minimap.error::after {
    content: '⚠';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 80, 80, 0.8);
    font-size: 24px;
    font-weight: bold;
}

/* Hidden state */
.modern-minimap.hidden {
    opacity: 0;
    transform: scale(0.9);
    pointer-events: none;
}

/* Focus indicators for accessibility */
.modern-minimap:focus-within {
    outline: 2px solid rgba(80, 150, 255, 0.8);
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .modern-minimap {
        display: none;
    }
}
