/**
 * Debug commands for testing event room functionality
 */

// Test event room dialogue system
window.testEventDialogue = function() {
    console.log('🧪 Testing event room dialogue system...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error('❌ EventRoomManager not available');
        return;
    }
    
    // Test dialogue data
    const testDialogueData = {
        lines: [
            "This is a test dialogue...",
            "Should I continue with the test?"
        ],
        options: [
            { text: "Yes", value: "yes" },
            { text: "No", value: "no" }
        ],
        onChoice: {
            "yes": {
                spawnEnemies: false,
                removeObject: null
            },
            "no": {
                // Do nothing
            }
        }
    };
    
    console.log('🧪 Calling showEventDialogue...');
    eventRoomManager.showEventDialogue(testDialogueData, dungeonHandler.currentRoomId)
        .then(result => {
            console.log('✅ Test dialogue completed successfully:', result);
        })
        .catch(error => {
            console.error('❌ Test dialogue failed:', error);
        });
};

// Test event room interaction trigger
window.testEventTrigger = function() {
    console.log('🧪 Testing event room trigger...');

    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }

    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error('❌ EventRoomManager not available');
        return;
    }

    const currentRoomId = dungeonHandler.currentRoomId;
    console.log(`🧪 Testing trigger for room ${currentRoomId}...`);

    eventRoomManager.handleEventTrigger(currentRoomId, 'mysterious_fishing_rod', { source: 'debug_test' })
        .then(() => {
            console.log('✅ Test trigger completed successfully');
        })
        .catch(error => {
            console.error('❌ Test trigger failed:', error);
        });
};

// Test fishing rod reel-in animation directly
window.testFishingRodAnimation = function() {
    console.log('🎣 Testing fishing rod reel-in animation...');

    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }

    // Find fishing rod in scene
    let fishingRod = null;
    dungeonHandler.scene.traverse(child => {
        if (child.userData?.rodId === "mysterious_fishing_rod" ||
            child.name === 'improved_fishing_rod' ||
            child.userData?.objectType === 'improved_fishing_rod') {
            fishingRod = child;
            console.log('🎣 Found fishing rod:', child.name, child.userData);
        }
    });

    if (!fishingRod) {
        console.error('❌ No fishing rod found in scene');
        return;
    }

    // Spawn a fish enemy first
    const playerPos = dungeonHandler.player.position.clone();
    const fishPos = playerPos.clone();
    fishPos.x += 0; // At pond center
    fishPos.y = 1.0; // At water level
    fishPos.z += 0; // At pond center

    console.log('🐟 Spawning fish enemy for animation test...');
    const fish = dungeonHandler._spawnEnemy('fish', fishPos);

    if (!fish) {
        console.error('❌ Failed to spawn fish enemy');
        return;
    }

    console.log('✅ Fish spawned, starting reel-in animation...');

    // Wait a moment for fish to be fully added to scene
    setTimeout(() => {
        // Import and run animation
        import('../generators/prefabs/improvedFishingRodObject.js').then(module => {
            module.animateFishingRodReelIn(fishingRod, 2000)
                .then(() => {
                    console.log('✅ Fishing rod animation completed');
                })
                .catch(error => {
                    console.error('❌ Animation failed:', error);
                });
        });
    }, 200); // Small delay to ensure fish is in scene
};

// Test pond collision system
window.testPondCollision = function() {
    console.log('🏊 Testing pond collision system...');

    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }

    // Find pond in scene
    let pond = null;
    dungeonHandler.scene.traverse(child => {
        if (child.userData?.objectType === 'glowing_pond' || child.name === 'glowing_pond') {
            pond = child;
            console.log('🏊 Found pond:', child.name, child.userData);
        }
    });

    if (!pond) {
        console.error('❌ No pond found in scene');
        return;
    }

    // Check collision boxes
    let collisionBoxes = [];
    pond.traverse(child => {
        if (child.userData?.isPondCollision === true) {
            collisionBoxes.push(child);
        }
    });

    console.log(`🏊 Found ${collisionBoxes.length} pond collision boxes`);
    collisionBoxes.forEach((box, index) => {
        console.log(`  Box ${index}: ${box.name}, allowFish: ${box.userData?.allowFish}, blockPlayer: ${box.userData?.blockPlayer}`);
    });

    // Test player position near pond
    const playerPos = dungeonHandler.player.position.clone();
    console.log(`🏊 Player position: (${playerPos.x.toFixed(2)}, ${playerPos.y.toFixed(2)}, ${playerPos.z.toFixed(2)})`);

    // Check if player is near pond collision
    let nearPondCollision = false;
    collisionBoxes.forEach(box => {
        const distance = playerPos.distanceTo(box.position);
        if (distance < 2.0) {
            nearPondCollision = true;
            console.log(`🏊 Player is near pond collision box: ${box.name}, distance: ${distance.toFixed(2)}`);
        }
    });

    if (!nearPondCollision) {
        console.log('🏊 Player is not near pond collision boxes');
    }

    console.log('🏊 Pond collision test completed');
};

// List all interactable objects in current room
window.listInteractableObjects = function() {
    console.log('🧪 Listing all interactable objects in current room...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler || !dungeonHandler.scene) {
        console.error('❌ DungeonHandler or scene not available');
        return;
    }
    
    const interactableObjects = [];
    
    dungeonHandler.scene.traverse(child => {
        if (child.userData && child.userData.isInteractable) {
            interactableObjects.push({
                name: child.name,
                objectType: child.userData.objectType,
                isEventObject: child.userData.isEventObject,
                eventRoomId: child.userData.eventRoomId,
                position: child.position.clone(),
                userData: child.userData
            });
        }
    });
    
    console.log(`📋 Found ${interactableObjects.length} interactable objects:`);
    interactableObjects.forEach((obj, index) => {
        console.log(`  ${index + 1}. ${obj.name} (${obj.objectType})`);
        console.log(`     Position: (${obj.position.x.toFixed(2)}, ${obj.position.y.toFixed(2)}, ${obj.position.z.toFixed(2)})`);
        console.log(`     Event Object: ${obj.isEventObject}, Room ID: ${obj.eventRoomId}`);
        console.log(`     UserData:`, obj.userData);
    });
    
    return interactableObjects;
};

// Force enable player movement (in case it gets stuck)
window.forceEnableMovement = function() {
    console.log('🔧 Force enabling player movement...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    if (dungeonHandler.playerController) {
        dungeonHandler.playerController.enableMovement();
        dungeonHandler.playerController.inputEnabled = true;
        console.log('✅ Player movement force enabled');
    }
    
    if (dungeonHandler.chestInteractionSystem) {
        dungeonHandler.chestInteractionSystem.enablePlayerMovement();
        dungeonHandler.chestInteractionSystem.isAnimating = false;
        console.log('✅ Chest interaction system reset');
    }
};

// Check current room event status
window.checkEventRoomStatus = function() {
    console.log('🧪 Checking current room event status...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const currentRoomId = dungeonHandler.currentRoomId;
    const roomData = dungeonHandler.floorLayout?.get(currentRoomId);
    
    console.log(`📋 Room ${currentRoomId} Status:`);
    console.log(`  Type: ${roomData?.type}`);
    console.log(`  Is Event Room: ${roomData?.type === 'EVENT'}`);
    console.log(`  Event Room Name: ${roomData?.eventRoomName}`);
    console.log(`  Has Event Data: ${!!roomData?.eventRoomData}`);
    console.log(`  Has Event Mechanics: ${!!roomData?.eventMechanics}`);
    
    if (dungeonHandler.eventRoomManager) {
        const eventState = dungeonHandler.eventRoomManager.getEventRoomState(currentRoomId);
        console.log(`  Event State:`, eventState);
    }
    
    return {
        roomId: currentRoomId,
        roomData: roomData,
        eventState: dungeonHandler.eventRoomManager?.getEventRoomState(currentRoomId)
    };
};

// Debug room 18's event room configuration
window.debugRoom18 = function() {
    console.log('🔍 Debugging Room 18 Configuration...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const roomId = 18;
    const roomData = dungeonHandler.floorLayout?.get(roomId);
    
    if (!roomData) {
        console.error(`❌ Room ${roomId} not found in floor layout`);
        console.log('Available rooms:', Array.from(dungeonHandler.floorLayout.keys()).sort((a,b) => a-b));
        return;
    }
    
    console.log(`\n📋 Room ${roomId} Full Configuration:`);
    console.log('========================');
    
    // Basic room info
    console.log('\n1️⃣ Basic Room Info:');
    console.log(`  - Type: ${roomData.type}`);
    console.log(`  - Coordinates: (${roomData.x}, ${roomData.z})`);
    console.log(`  - Entrances:`, roomData.entrances);
    console.log(`  - Is Event Room: ${roomData.type === 'EVENT'}`);
    
    // Event room specific info
    if (roomData.type === 'EVENT') {
        console.log('\n2️⃣ Event Room Configuration:');
        console.log(`  - Event Room Name: ${roomData.eventRoomName || 'NOT SET'}`);
        console.log(`  - Has Event Data: ${!!roomData.eventRoomData}`);
        console.log(`  - Has Event Mechanics: ${!!roomData.eventMechanics}`);
        
        // Available connections from event room data
        if (roomData.eventRoomData) {
            console.log('\n3️⃣ Event Room Data:');
            console.log(`  - Name: ${roomData.eventRoomData.name}`);
            console.log(`  - Available Connections:`, roomData.eventRoomData.availableConnections);
            console.log(`  - Has Custom Door Positions: ${!!roomData.eventRoomData.customDoorPositions}`);
            
            if (roomData.eventRoomData.availableConnections) {
                console.log('\n  Connection Details:');
                Object.entries(roomData.eventRoomData.availableConnections).forEach(([dir, available]) => {
                    console.log(`    - ${dir}: ${available ? '✅ Available' : '❌ Not Available'}`);
                });
            }
        }
        
        // Check event mechanics available connections
        if (roomData.eventMechanics && roomData.eventMechanics.availableConnections) {
            console.log('\n4️⃣ Event Mechanics Available Connections:');
            Object.entries(roomData.eventMechanics.availableConnections).forEach(([dir, available]) => {
                console.log(`    - ${dir}: ${available ? '✅ Available' : '❌ Not Available'}`);
            });
        }
    }
    
    // Actual connections to other rooms
    console.log('\n5️⃣ Actual Room Connections:');
    const connections = {
        north: roomData.north,
        south: roomData.south,
        east: roomData.east,
        west: roomData.west
    };
    
    Object.entries(connections).forEach(([dir, targetRoomId]) => {
        if (targetRoomId !== null && targetRoomId !== undefined) {
            const targetRoom = dungeonHandler.floorLayout?.get(targetRoomId);
            console.log(`  - ${dir}: Room ${targetRoomId} (${targetRoom?.type || 'UNKNOWN'})`);
        } else {
            console.log(`  - ${dir}: No connection`);
        }
    });
    
    // Check for mismatches
    console.log('\n6️⃣ Connection Validation:');
    if (roomData.eventRoomData?.availableConnections) {
        const availableConnections = roomData.eventRoomData.availableConnections;
        Object.entries(connections).forEach(([dir, targetRoomId]) => {
            const isAvailable = availableConnections[dir];
            const hasConnection = targetRoomId !== null && targetRoomId !== undefined;
            
            if (hasConnection && !isAvailable) {
                console.warn(`  ⚠️  ${dir}: Has connection to room ${targetRoomId} but marked as NOT available in event room data!`);
            } else if (!hasConnection && isAvailable) {
                console.log(`  ℹ️  ${dir}: Available for connection but no room connected`);
            } else if (hasConnection && isAvailable) {
                console.log(`  ✅ ${dir}: Properly connected to room ${targetRoomId}`);
            }
        });
    }
    
    // Check entrance directions vs available connections
    console.log('\n7️⃣ Entrance Direction Analysis:');
    if (roomData.entrances && roomData.entrances.length > 0) {
        roomData.entrances.forEach(entrance => {
            console.log(`  - Entrance from: ${entrance}`);
            if (roomData.eventRoomData?.availableConnections) {
                const canEnterFrom = roomData.eventRoomData.availableConnections[entrance];
                console.log(`    Available in event data: ${canEnterFrom ? '✅ Yes' : '❌ No'}`);
            }
        });
    }
    
    // Raw data dump
    console.log('\n8️⃣ Raw Room Data:');
    console.log(JSON.stringify(roomData, null, 2));
    
    return roomData;
};

console.log('🧪 Event Room Debug Commands Loaded:');
console.log('  - testEventDialogue() - Test the dialogue system');
console.log('  - testEventTrigger() - Test event trigger mechanism');
console.log('  - listInteractableObjects() - List all interactable objects');
console.log('  - forceEnableMovement() - Force enable player movement');
console.log('  - checkEventRoomStatus() - Check current room event status');
console.log('  - debugRoom18() - Debug room 18\'s event room configuration');
