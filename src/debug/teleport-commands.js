/**
 * Debug commands for teleporting and item management in the game
 * These can be called from the browser console
 */
import { ITEM_TYPES, getItemData } from '../entities/ItemTypes.js';

/**
 * Helper function to wait for scene manager to be ready
 * @param {number} maxWaitTime - Maximum time to wait in milliseconds
 * @returns {Promise<Object>} Promise that resolves with the dungeon handler
 */
function waitForDungeonHandler(maxWaitTime = 5000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();

        function checkHandler() {
            const sceneManager = window.sceneManager;
            if (sceneManager) {
                const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
                if (dungeonHandler && dungeonHandler.constructor.name === 'DungeonHandler') {
                    resolve(dungeonHandler);
                    return;
                }
            }

            if (Date.now() - startTime > maxWaitTime) {
                reject(new Error('Timeout waiting for dungeon handler'));
                return;
            }

            setTimeout(checkHand<PERSON>, 100);
        }

        checkHandler();
    });
}

/**
 * Teleport the player to the boss room
 * @returns {boolean} True if successful, false otherwise
 */
export function teleportToBoss() {
    console.log("=== DEBUG: Teleport to Boss ===");

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }
    console.log("DungeonHandler found:", !!dungeonHandler);
    
    // Get the player controller
    const playerController = dungeonHandler.playerController;
    if (!playerController) {
        console.error("Player controller not found");
        return false;
    }
    console.log("PlayerController found:", !!playerController);
    
    // Call the teleport method
    try {
        console.log("Calling teleportToBoss method...");
        playerController.teleportToBoss();
        console.log("Teleport command executed");
        return true;
    } catch (error) {
        console.error("Error teleporting to boss:", error);
        return false;
    }
}

/**
 * Teleport directly to a specific room by ID
 * @param {number} roomId - The ID of the room to teleport to
 * @returns {boolean} True if successful, false otherwise
 */
export function teleportToRoom(roomId) {
    console.log(`=== DEBUG: Teleport to Room ${roomId} ===`);

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    // Get room data to determine best entry direction
    const roomData = dungeonHandler.floorLayout?.get(roomId);
    if (!roomData) {
        console.error(`Room data not found for ID ${roomId}`);
        return false;
    }

    // CRITICAL FIX: Check if this is an event room and ensure event room data is applied
    if (roomData.type === 'EVENT' && !roomData.eventRoomData) {
        console.log(`Room ${roomId} is an event room but missing eventRoomData, applying configuration...`);

        // Get the event room manager
        const eventRoomManager = dungeonHandler.eventRoomManager;
        if (!eventRoomManager) {
            console.error(`Event room manager not found, cannot configure event room ${roomId}`);
            return false;
        }

        // Check if this room has an event room key assigned
        if (roomData.eventRoomKey) {
            console.log(`Applying existing event room key '${roomData.eventRoomKey}' to room ${roomId}`);
            eventRoomManager.applyEventRoomConfig(roomData, roomData.eventRoomKey);
        } else {
            // Select and apply a new event room configuration
            console.log(`Selecting new event room configuration for room ${roomId}`);
            const eventRoomKey = eventRoomManager.selectEventRoomForFloor(1); // Assume floor 1 for teleport
            if (eventRoomKey) {
                eventRoomManager.applyEventRoomConfig(roomData, eventRoomKey);
                console.log(`Applied event room configuration '${eventRoomKey}' to room ${roomId}`);
            } else {
                console.error(`Failed to select event room configuration for room ${roomId}`);
                return false;
            }
        }
    }

    // Find the best entry direction (prefer south door if available, as it positions player near entrance)
    let entryDirection = null;
    const connections = roomData.connections || {};

    // Priority order: south (player spawns near north wall), east, west, north
    const directionPriority = ['s', 'e', 'w', 'n'];

    for (const dir of directionPriority) {
        if (connections[dir] !== null && connections[dir] !== undefined) {
            entryDirection = dir;
            break;
        }
    }

    // If no connections found, use null (will spawn at center)
    if (!entryDirection) {
        console.log(`No door connections found for room ${roomId}, spawning at center`);
        entryDirection = null;
    } else {
        console.log(`Using entry direction '${entryDirection}' for room ${roomId} (player will spawn near ${entryDirection} door)`);
    }

    // Call the transition method with appropriate entry direction
    try {
        console.log(`Transitioning to room ${roomId} via ${entryDirection || 'center spawn'}...`);
        dungeonHandler._transitionToRoom(roomId, entryDirection);
        console.log("Teleport command executed");
        return true;
    } catch (error) {
        console.error("Error teleporting to room:", error);
        return false;
    }
}

/**
 * Create and teleport to a specific event room
 * @param {string} eventRoomId - Event room ID (e.g., "mysterious_pond", "ominous_treasure")
 * @returns {boolean} Success status
 */
export function createEvent(eventRoomId) {
    console.log(`=== DEBUG: Create Event Room ${eventRoomId} ===`);

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    // Get the event room manager
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error("Event room manager not found");
        return false;
    }

    // Validate that the event room exists
    const eventRoomData = eventRoomManager.getEventRoomData(eventRoomId);
    if (!eventRoomData) {
        console.error(`Event room '${eventRoomId}' not found. Available event rooms:`, eventRoomManager.getAvailableEventRooms?.() || 'Unable to get list');
        return false;
    }

    console.log(`✅ Found event room data for '${eventRoomId}':`, eventRoomData.name);

    // Find an available room to convert to event room, or create a new one
    let targetRoomId = null;
    const floorLayout = dungeonHandler.floorLayout;

    if (floorLayout) {
        // Look for an existing normal room that's not Room 0
        const availableRooms = Array.from(floorLayout.values()).filter(room =>
            room.type === 'Normal' &&
            room.id !== 0 &&
            !room.isSecret &&
            room.type !== 'Boss'
        );

        if (availableRooms.length > 0) {
            // Use the highest ID room
            const targetRoom = availableRooms.reduce((highest, room) =>
                room.id > highest.id ? room : highest);
            targetRoomId = targetRoom.id;
            console.log(`Converting existing room ${targetRoomId} to event room '${eventRoomId}'`);
        } else {
            // Create a new room ID
            const allRoomIds = Array.from(floorLayout.keys());
            targetRoomId = Math.max(...allRoomIds) + 1;
            console.log(`Creating new room ${targetRoomId} for event room '${eventRoomId}'`);

            // Create basic room data
            const newRoomData = {
                id: targetRoomId,
                type: 'Normal',
                shapeKey: eventRoomData.shape || 'SQUARE_1X1',
                connections: { s: null }, // Add a south door for entry
                coords: { x: 0, y: 0 }, // Place at origin
                visited: false,
                state: {},
                getCenter: function() { return { x: 0, y: 0 }; },
                isDark: false,
                bounds: { minX: -7, maxX: 7, minZ: -7, maxZ: 7 }
            };

            floorLayout.set(targetRoomId, newRoomData);
        }
    } else {
        console.error("No floor layout found");
        return false;
    }

    // Get the target room data
    const targetRoom = floorLayout.get(targetRoomId);
    if (!targetRoom) {
        console.error(`Failed to get room data for ID ${targetRoomId}`);
        return false;
    }

    // Apply event room configuration
    console.log(`Applying event room configuration '${eventRoomId}' to room ${targetRoomId}`);
    eventRoomManager.applyEventRoomConfig(targetRoom, eventRoomId);

    // Verify the configuration was applied
    if (!targetRoom.eventRoomData) {
        console.error(`Failed to apply event room configuration to room ${targetRoomId}`);
        return false;
    }
    
    // The applyEventRoomConfig method now sets the type to 'EVENT'
    console.log(`✅ Room type set to: ${targetRoom.type}`)

    // MUSIC FIX: Ensure the room has proper area context for music system
    if (!targetRoom.state) {
        targetRoom.state = {};
    }
    // Use current area, falling back to previous area, then catacombs
    const currentAreaId = dungeonHandler.currentArea?.id || dungeonHandler.previousAreaId || 'catacombs';
    targetRoom.state.area = currentAreaId;
    console.log(`✅ Set event room area context to: ${targetRoom.state.area}`);

    console.log(`✅ Successfully configured room ${targetRoomId} as event room '${eventRoomId}'`);

    // After applying event room configuration and before teleport
    // Invalidate any cached visuals so the room regenerates as an event room
    if (dungeonHandler.preLoadedRooms && dungeonHandler.preLoadedRooms.has(targetRoomId)) {
        console.log(`Clearing pre-loaded visuals cache for room ${targetRoomId} so it regenerates as event room`);
        const preLoadData = dungeonHandler.preLoadedRooms.get(targetRoomId);
        
        // Properly dispose of the cached room group
        if (preLoadData.roomGroup) {
            preLoadData.roomGroup.traverse(child => {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => mat.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
        }
        
        dungeonHandler.preLoadedRooms.delete(targetRoomId);
    }
    
    // If this is the current room, we need to handle it specially
    if (dungeonHandler.currentRoomId === targetRoomId) {
        console.log(`Target room ${targetRoomId} is the current room - will force reload during teleport`);
        // The teleport will handle the reload, but we ensure the room knows it needs regeneration
        targetRoom.needsRegeneration = true;
    }

    // Now teleport to the room
    console.log(`Teleporting to newly created event room ${targetRoomId}...`);
    return teleportToRoom(targetRoomId);
}

/**
 * Display a list of all available items in the game
 * @returns {string} Success message
 */
export function itemlist() {
    console.group("🎒 === ALL AVAILABLE ITEMS ===");
    
    // Get all item data
    const allItems = Object.keys(ITEM_TYPES).map(key => ITEM_TYPES[key]);
    
    // Group items by category
    const itemsByCategory = {};
    
    allItems.forEach(itemType => {
        const itemData = getItemData(itemType);
        if (itemData) {
            const category = itemData.category || 'uncategorized';
            if (!itemsByCategory[category]) {
                itemsByCategory[category] = [];
            }
            itemsByCategory[category].push({
                type: itemType,
                name: itemData.name,
                rarity: itemData.rarity,
                description: itemData.description
            });
        }
    });
    
    // Display items by category
    Object.keys(itemsByCategory).forEach(category => {
        console.group(`📦 ${category.toUpperCase()}:`);
        itemsByCategory[category].forEach(item => {
            const rarityEmoji = {
                'common': '⚪',
                'rare': '🔵', 
                'epic': '🟣',
                'legendary': '🟠'
            }[item.rarity] || '⚫';
            
            console.log(`${rarityEmoji} ${item.type} - "${item.name}"`);
            console.log(`   └─ ${item.description}`);
        });
        console.groupEnd();
    });
    
    console.groupEnd();
    console.log(`📊 Total items: ${allItems.length}`);
    console.log("💡 Use give('item_type') to add any item to your inventory");
    
    return "📋 Item list displayed above";
}

/**
 * Give an item to the player
 * @param {string} itemType - The item type to give (from itemlist())
 * @returns {boolean} Success status
 */
export async function give(itemType) {
    if (!itemType) {
        console.error("❌ Please specify an item type. Use itemlist() to see available items.");
        console.log("💡 Example: give('forest_blessing')");
        return false;
    }
    
    console.log(`🎁 Attempting to give item: ${itemType}`);
    
    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found - must be in game");
        return false;
    }
    
    // Validate item exists
    const itemData = getItemData(itemType);
    if (!itemData) {
        console.error(`❌ Item type '${itemType}' not found. Use itemlist() to see available items.`);
        return false;
    }
    
    console.log(`✅ Found item: "${itemData.name}" (${itemData.category})`);
    
    try {
        // Handle different item categories
        switch (itemData.category) {
            case 'card':
                if (dungeonHandler.cardSystem) {
                    const success = dungeonHandler.cardSystem.addCard(itemType);
                    if (success) {
                        console.log(`🃏 Added card "${itemData.name}" to your hand!`);
                        return true;
                    } else {
                        console.warn(`⚠️ Could not add card (hand might be full)`);
                        return false;
                    }
                } else {
                    console.error("❌ Card system not available");
                    return false;
                }
                
            case 'weapon':
                if (dungeonHandler.weaponSystem) {
                    // Create 3D model for the weapon if it's a melee weapon with a voxelModel
                    let weaponModel = null;
                    if (itemData.weaponType === 'melee' && itemData.voxelModel) {
                        try {
                            // Map voxel model names to file names and function names
                            const prefabMap = {
                                'hero_sword': {
                                    file: 'heroSwordItem.js',
                                    function: 'createHeroSwordItem'
                                },
                                'mystic_staff': {
                                    file: 'mysticStaffItem.js', 
                                    function: 'createMysticStaffItem'
                                }
                            };
                            
                            const prefabInfo = prefabMap[itemData.voxelModel];
                            if (prefabInfo) {
                                const prefabModule = await import(`../generators/prefabs/${prefabInfo.file}`);
                                const createFunction = prefabModule[prefabInfo.function];
                                if (createFunction) {
                                    weaponModel = createFunction();
                                    console.log(`✅ Created 3D model for ${itemData.name}`);
                                }
                            }
                        } catch (error) {
                            console.warn(`⚠️ Could not load 3D model for ${itemData.name}:`, error);
                        }
                    }
                    
                    // Convert ItemTypes format to WeaponSystem format
                    const weaponData = {
                        id: itemType,
                        name: itemData.name,
                        type: itemData.weaponType || 'melee', // Convert weaponType to type
                        description: itemData.description,
                        damage: itemData.damage || 1,
                        range: itemData.range || 3,
                        attackSpeed: itemData.attackSpeed || 1.0,
                        model: weaponModel, // Attach the 3D model
                        attackPattern: itemData.attackPattern || {
                            type: 'frontal_arc',
                            range: 3.0,
                            angle: 90,
                            description: 'Basic attack'
                        }
                    };
                    
                    dungeonHandler.weaponSystem.equipWeapon(weaponData);
                    console.log(`⚔️ Equipped weapon "${itemData.name}"!`);
                    return true;
                } else {
                    console.error("❌ Weapon system not available");
                    return false;
                }
                
            case 'health':
                if (itemType === 'soul_heart' && dungeonHandler.player) {
                    // Add soul heart directly to player
                    dungeonHandler.player.souls = Math.min((dungeonHandler.player.souls || 0) + 1, dungeonHandler.player.maxSouls || 9);
                    console.log(`💙 Added soul heart! Souls: ${dungeonHandler.player.souls}/${dungeonHandler.player.maxSouls}`);
                    return true;
                } else {
                    console.log(`💊 Health item "${itemData.name}" effect applied`);
                    return true;
                }
                
            case 'consumable':
                if (itemType === 'soul_potion' && dungeonHandler.cardSystem) {
                    // Add soul potion to card inventory for drag-and-drop use
                    const success = dungeonHandler.cardSystem.addCard('soul_potion');
                    if (success) {
                        console.log(`💜 Soul potion added to inventory! Drag it onto the screen to heal.`);
                        return true;
                    } else {
                        console.warn(`💜 Failed to add soul potion - inventory may be full`);
                        return false;
                    }
                } else {
                    console.log(`💊 Consumable item "${itemData.name}" effect applied`);
                    return true;
                }
                
            case 'utility':
            case 'relic':
            case 'soul':
                // For other items, we'll just log that they were given
                // These could be added to an inventory system in the future
                console.log(`📦 Received "${itemData.name}" - ${itemData.description}`);
                console.log("💡 This item type doesn't have specific functionality yet, but it's been 'given' to you!");
                return true;
                
            default:
                console.warn(`⚠️ Unknown item category: ${itemData.category}`);
                console.log(`📦 Received "${itemData.name}" anyway!`);
                return true;
        }
    } catch (error) {
        console.error(`❌ Error giving item: ${error.message}`);
        return false;
    }
}

/**
 * Display help information for console commands
 */
export function help() {
    // Force console to stay open by using console.group
    console.group("🎮 === SOULPATH CONSOLE COMMANDS ===");
    
    console.group("📍 EVENT ROOMS:");
    const eventRooms = [
        'arcade_game',
        'devils_chess_room',
        'mysterious_pond', 
        'ominous_treasure',
        'chronal_anomaly',
        'eye_of_judgment',
        'guardians_of_lies'
    ];
    eventRooms.forEach(room => {
        console.log(`createEvent('${room}')`);
    });
    console.groupEnd();
    
    console.group("🎯 NAVIGATION:");
    console.log("help() - Show this help");
    console.log("listEventRooms() - List all event rooms");
    console.log("showDungeonMap() - Show ASCII map with entrances & door rotations");
    console.log("teleportToRoom(roomId) - Teleport to room");
    console.log("listRooms() - Show all rooms");
    console.log("refreshCurrentRoom() - Refresh room");
    console.log("coords() - Show player position");
    console.log("revealMap() - Show entire dungeon layout");
    console.groupEnd();
    
    console.group("⚔️ PLAYER & COMBAT:");
    console.log("godMode() - Toggle invincibility");
    console.log("noclip() - Toggle wall clipping");
    console.log("heal() - Full heal player");
    console.log("setHealth(amount) - Set player health");
    console.log("speed(multiplier) - Change movement speed");
    console.groupEnd();
    
    console.group("🎒 ITEMS & INVENTORY:");
    console.log("itemlist() - Show all available items");
    console.log("give('item_type') - Give any item to player");
    console.log("addCards(n) - Add random cards");
    console.groupEnd();
    
    console.group("🏰 DUNGEON:");
    console.log("unlockAllDoors() - Open all locked doors");
    console.log("startChessGame('normal') - Start chess game");
    console.log("debugEventRoomDoors() - Debug event room door positions");
    console.log("testEventRoomDoors() - Test event room door selection system");
    console.groupEnd();
    
    console.group("🕹️ MINI-GAMES:");
    console.log("createEvent('arcade_game') - Spawn Rhythmic Challenge (Crypt of NecroDancer-style)");
    console.log("createEvent('devils_chess_room') - Spawn Devil's Chess Room");
    console.groupEnd();
    
    console.log("💡 Examples:");
    console.log("  createEvent('arcade_game') - Rhythm game with WASD/touch controls");
    console.log("  showDungeonMap() - View dungeon layout and event room details");
    console.groupEnd();
    
    return "📖 Help commands displayed in console groups above";
}

/**
 * Toggle god mode (invincibility)
 */
export function godMode() {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    dungeonHandler.player.godMode = !dungeonHandler.player.godMode;
    console.log(`🛡️ God mode: ${dungeonHandler.player.godMode ? 'ON' : 'OFF'}`);
}

/**
 * Toggle no-clip mode (wall clipping)
 */
export function noclip() {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    dungeonHandler.player.noclip = !dungeonHandler.player.noclip;
    console.log(`👻 No-clip mode: ${dungeonHandler.player.noclip ? 'ON' : 'OFF'}`);
}

/**
 * Heal player to full health
 */
export function heal() {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    const player = dungeonHandler.player;
    if (player.actualHealth !== undefined && player.maxHealth !== undefined) {
        player.actualHealth = player.maxHealth;
        player.currentHealth = player.maxHealth;
        
        // Update health bar if it exists
        if (player.healthBar) {
            player.healthBar.updateHealth(player.currentHealth);
        }
        
        console.log(`❤️ Player healed to ${player.actualHealth}/${player.maxHealth} HP`);
    } else {
        console.log("❤️ Health system not found");
    }
}

/**
 * Set player health to specific amount
 */
export function setHealth(amount) {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    if (typeof amount !== 'number' || amount <= 0) {
        console.error("❌ Invalid health amount. Use: setHealth(100)");
        return;
    }

    const player = dungeonHandler.player;
    if (player.actualHealth !== undefined) {
        player.actualHealth = amount;
        player.currentHealth = Math.min(amount, player.maxHealth || amount);
        
        // Update health bar if it exists
        if (player.healthBar) {
            player.healthBar.updateHealth(player.currentHealth);
        }
        
        console.log(`❤️ Player health set to ${player.actualHealth}/${player.maxHealth || 'unlimited'} HP`);
    } else {
        console.log("❤️ Health system not found");
    }
}

/**
 * Add random cards to player
 */
export function addCards(n = 1) {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ Dungeon handler not found");
        return;
    }

    if (typeof n !== 'number' || n <= 0) {
        console.error("❌ Invalid card count. Use: addCards(5)");
        return;
    }

    // Try to access card system
    if (dungeonHandler.cardSystem) {
        const cardTypes = ['attack', 'defense', 'utility', 'healing', 'movement'];
        let addedCards = 0;
        
        for (let i = 0; i < n; i++) {
            const randomType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
            try {
                dungeonHandler.cardSystem.addCard(randomType);
                addedCards++;
            } catch (error) {
                console.warn(`Failed to add card ${i + 1}: ${error.message}`);
            }
        }
        
        console.log(`🃏 Added ${addedCards}/${n} random cards`);
        console.log(`🃏 Total cards: ${dungeonHandler.cardSystem.cards.size}/${dungeonHandler.cardSystem.maxHandSize}`);
    } else {
        console.log(`🃏 Card system not found, would add ${n} cards`);
    }
}

/**
 * Show player coordinates
 */
export function coords() {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    const pos = dungeonHandler.player.position;
    console.log(`📍 Player position: (${pos.x.toFixed(2)}, ${pos.y.toFixed(2)}, ${pos.z.toFixed(2)})`);
    
    if (dungeonHandler.currentRoomId) {
        console.log(`🏠 Current room: ${dungeonHandler.currentRoomId}`);
    }
}

/**
 * Change player movement speed
 */
export function speed(multiplier = 1) {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.player) {
        console.error("❌ Player not found");
        return;
    }

    if (typeof multiplier !== 'number' || multiplier <= 0) {
        console.error("❌ Invalid speed multiplier. Use: speed(2.0)");
        return;
    }

    const player = dungeonHandler.player;
    player.speedMultiplier = multiplier;
    const baseSpeed = player.moveSpeed || 10.08;
    const newSpeed = baseSpeed * multiplier;
    
    console.log(`🏃 Speed multiplier set to ${multiplier}x (${baseSpeed.toFixed(2)} → ${newSpeed.toFixed(2)} units/sec)`);
}

/**
 * Reveal entire dungeon map
 */
export function revealMap() {
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ Dungeon handler not found");
        return;
    }

    if (dungeonHandler.floorLayout) {
        console.log("🗺️ Dungeon Map Revealed:");
        dungeonHandler.floorLayout.forEach((room, roomId) => {
            console.log(`  ${roomId}: ${room.type} (${room.x}, ${room.y})`);
        });
    } else {
        console.log("🗺️ Dungeon layout not available");
    }
}

/**
 * Unlock all doors (dummy command)
 */
export function unlockAllDoors() {
    console.log("🔓 All doors unlocked! (Feature not yet implemented)");
}

/**
 * List all available event rooms
 * @returns {Array} Array of available event room IDs
 */
export function listEventRooms() {
    console.log("=== DEBUG: List Available Event Rooms ===");

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return [];
    }

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return [];
    }

    // Get the event room manager
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error("Event room manager not found");
        return [];
    }

    // Get available event rooms
    const availableRooms = [];
    try {
        // Import the event room functions
        import('../gameData/eventRooms/index.js').then(eventRoomModule => {
            const eventRoomIds = eventRoomModule.getAvailableEventRooms();

            console.log("📋 Available Event Rooms:");
            eventRoomIds.forEach((roomId, index) => {
                const roomData = eventRoomModule.getEventRoom(roomId);
                console.log(`${index + 1}. ${roomId} - ${roomData?.name || 'Unknown'}`);
                availableRooms.push({
                    id: roomId,
                    name: roomData?.name || 'Unknown',
                    shape: roomData?.shape || 'Unknown',
                    description: roomData?.description || 'No description'
                });
            });

            console.log("\n💡 Usage: createEvent('mysterious_pond') or createEvent('ominous_treasure')");
            console.table(availableRooms);
        }).catch(error => {
            console.error("Error loading event room data:", error);
        });
    } catch (error) {
        console.error("Error getting available event rooms:", error);
    }

    return availableRooms;
}

/**
 * Teleport directly to the secret room
 * @returns {boolean} True if successful, false otherwise
 */
export function teleportToSecret() {
    console.log("=== DEBUG: Teleport to Secret Room ===");

    // Debug: Check what's available
    console.log("window.sceneManager exists:", !!window.sceneManager);
    console.log("window.sceneManager:", window.sceneManager);

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found on window object");
        console.log("Available window properties:", Object.keys(window).filter(key => key.includes('scene') || key.includes('Scene')));
        return false;
    }

    console.log("sceneManager.currentHandler exists:", !!sceneManager.currentHandler);
    console.log("sceneManager.activeSceneHandler exists:", !!sceneManager.activeSceneHandler);
    console.log("sceneManager.currentState:", sceneManager.currentState);

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found (tried both currentHandler and activeSceneHandler)");
        return false;
    }

    console.log("DungeonHandler found:", !!dungeonHandler);
    console.log("DungeonHandler type:", dungeonHandler.constructor.name);

    // Check if secret room manager exists
    if (!dungeonHandler.secretRoomManager) {
        console.error("Secret room manager not found");
        console.log("Available dungeonHandler properties:", Object.keys(dungeonHandler).filter(key => key.includes('secret') || key.includes('Secret') || key.includes('room') || key.includes('Room')));
        return false;
    }

    // Get current area name
    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    console.log(`Looking for secret room in area: "${currentArea}"`);

    // Get secret room data for current area
    const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);
    if (!secretRoomData) {
        console.error(`No secret room found for area: "${currentArea}"`);
        console.log("Available secret areas:", Array.from(dungeonHandler.secretRoomManager.secretRooms.keys()));
        return false;
    }

    const secretRoomId = secretRoomData.id;
    console.log(`Found secret room with ID: ${secretRoomId}`);

    // Get secret wall data for proper setup
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);
    if (!secretWallData) {
        console.error("Secret wall data not found");
        return false;
    }

    // CRITICAL FIX: Ensure secret room is registered in floor layout
    if (!dungeonHandler.floorLayout.has(secretRoomId)) {
        console.log(`Secret room ${secretRoomId} not in floor layout, adding it...`);
        dungeonHandler.floorLayout.set(secretRoomId, secretRoomData);
    }

    // Set up state for proper functionality
    secretWallData.isRevealed = true;
    secretWallData.isAnimating = false;
    secretRoomData.isDiscovered = true;
    secretRoomData.visited = true;

    // Update room connections
    const hostRoomId = secretWallData.hostRoom.id;
    const hostRoom = dungeonHandler.floorLayout.get(hostRoomId);
    if (hostRoom) {
        hostRoom.connections = hostRoom.connections || {};
        hostRoom.connections[secretWallData.direction] = secretRoomData.id;
    }
    secretRoomData.connections = secretRoomData.connections || {};
    secretRoomData.connections[secretWallData.entranceDirection] = hostRoomId;

    console.log(`Teleporting directly to secret room ${secretRoomId}...`);

    // CRITICAL FIX: Properly simulate the secret wall activation sequence
    console.log("Step 1: Going to host room to properly activate secret wall...");

    // First, ensure we're in the host room and the secret wall is created
    if (dungeonHandler.currentRoomId !== hostRoomId) {
        dungeonHandler.loadRoom(hostRoomId, 'north');
    }

    // Wait for host room to load, then simulate proper activation
    setTimeout(() => {
        console.log("Step 2: Host room loaded, creating and activating secret wall...");

        // Ensure secret wall exists
        dungeonHandler.secretRoomManager.checkAndCreateSecretWallForRoom(hostRoomId);

        // Wait for wall creation, then simulate activation
        setTimeout(() => {
            console.log("Step 3: Simulating secret wall activation (R key press)...");

            // CRITICAL: Force the secret wall activation bypassing proximity check
            const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';

            // Temporarily set proximity to true for activation
            const originalProximity = dungeonHandler.secretRoomManager.isPlayerNearSecret;
            dungeonHandler.secretRoomManager.isPlayerNearSecret = true;

            console.log("Forcing proximity state and triggering secret wall activation...");

            // This will trigger the proper animation and set up door triggers
            dungeonHandler.secretRoomManager.handleSecretRevealCommand();

            // Restore original proximity state
            dungeonHandler.secretRoomManager.isPlayerNearSecret = originalProximity;

            // Wait for animation to complete, then teleport to secret room
            setTimeout(() => {
                console.log("Step 4: Secret wall activated, teleporting to secret room...");

                try {
                    dungeonHandler.loadRoom(secretRoomId, secretWallData.entranceDirection);
                    console.log("✅ Secret room teleport completed with proper activation sequence");

                    // CRITICAL FIX: Restart proximity monitoring after teleport sequence
                    setTimeout(() => {
                        console.log("Step 5: Restarting proximity monitoring system...");

                        // Reset proximity state completely
                        dungeonHandler.secretRoomManager.isPlayerNearSecret = false;

                        // Stop and restart proximity monitoring to ensure it's working
                        dungeonHandler.secretRoomManager.stopProximityMonitoring();
                        dungeonHandler.secretRoomManager.startProximityMonitoring();

                        console.log("✅ Proximity monitoring restarted - system should work normally now");
                        console.log("🔍 Walk near the secret wall in the host room to test proximity detection");
                    }, 1000);

                } catch (error) {
                    console.error("Error loading secret room:", error);
                    // Fallback: Try transition method
                    dungeonHandler._transitionToRoom(secretRoomId, secretWallData.entranceDirection);

                    // Still restart proximity monitoring even with fallback
                    setTimeout(() => {
                        console.log("Restarting proximity monitoring after fallback...");
                        dungeonHandler.secretRoomManager.isPlayerNearSecret = false;
                        dungeonHandler.secretRoomManager.stopProximityMonitoring();
                        dungeonHandler.secretRoomManager.startProximityMonitoring();
                    }, 2000);
                }
            }, 2000); // Wait for animation to complete

        }, 500); // Wait for wall creation

    }, 500); // Wait for host room loading

    return true;

    return true;
}

/**
 * List all available rooms in the dungeon
 * @returns {Array} Array of room IDs and types
 */
export function listRooms() {
    console.log("=== DEBUG: List Rooms ===");

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return [];
    }

    // Try both currentHandler and activeSceneHandler
    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return [];
    }
    const rooms = [];

    // List all rooms in the floor layout
    if (dungeonHandler.floorLayout) {
        for (const [id, roomData] of dungeonHandler.floorLayout.entries()) {
            rooms.push({
                id,
                type: roomData.type || 'normal',
                visited: roomData.visited || false,
                isCurrent: id === dungeonHandler.currentRoomId,
                isSecret: roomData.isSecret || false,
                isDiscovered: roomData.isDiscovered || false
            });
        }
    }

    console.table(rooms);
    return rooms;
}

/**
 * Alternative async version that waits for the scene manager to be ready
 * @returns {Promise<boolean>} Promise that resolves to true if successful
 */
export async function teleportToSecretAsync() {
    console.log("=== DEBUG: Teleport to Secret Room (Async) ===");

    try {
        console.log("Waiting for dungeon handler to be ready...");
        const dungeonHandler = await waitForDungeonHandler();
        console.log("DungeonHandler found:", !!dungeonHandler);

        // Check if secret room manager exists
        if (!dungeonHandler.secretRoomManager) {
            console.error("Secret room manager not found");
            return false;
        }

        // Get current area name
        const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
        console.log(`Looking for secret room in area: "${currentArea}"`);

        // Get secret room data for current area
        const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);
        if (!secretRoomData) {
            console.error(`No secret room found for area: "${currentArea}"`);
            console.log("Available secret areas:", Array.from(dungeonHandler.secretRoomManager.secretRooms.keys()));
            return false;
        }

        const secretRoomId = secretRoomData.id;
        console.log(`Found secret room with ID: ${secretRoomId}`);

        // Get secret wall data for proper setup
        const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);
        if (!secretWallData) {
            console.error("Secret wall data not found");
            return false;
        }

        // STEP 1: Ensure secret wall is properly revealed for exit detection
        console.log("Setting up secret wall state for proper exit detection...");

        // The key insight: closeSecretDoor() requires isRevealed=true to work
        // So we need to ensure the wall is marked as revealed when we teleport
        secretWallData.isRevealed = true;
        secretWallData.isAnimating = false;
        secretRoomData.isDiscovered = true;
        secretRoomData.visited = true;

        console.log(`DEBUG: Set secret wall isRevealed=true for area ${currentArea} (required for exit detection)`);

        // STEP 2: Update host room connections
        const hostRoom = dungeonHandler.floorLayout.get(secretWallData.hostRoom.id);
        if (hostRoom) {
            hostRoom.connections = hostRoom.connections || {};
            hostRoom.connections[secretWallData.direction] = secretRoomData.id;
        }

        // STEP 3: Ensure the secret room has proper connection back to host
        secretRoomData.connections = secretRoomData.connections || {};
        secretRoomData.connections[secretWallData.entranceDirection] = secretWallData.hostRoom.id;

        // STEP 4: Ensure host room has secret wall, then teleport
        const hostRoomId = secretWallData.hostRoom.id;
        const currentRoomId = dungeonHandler.currentRoomId;

        console.log(`Current room: ${currentRoomId}, Host room: ${hostRoomId}`);
        console.log(`Secret wall wallGroup exists: ${!!secretWallData.wallGroup}`);

        // If secret wall doesn't exist in scene, we need to load the host room first
        if (!secretWallData.wallGroup) {
            console.log("Secret wall not in scene - need to load host room first to create it");

            if (currentRoomId !== hostRoomId) {
                console.log("Going to host room to create secret wall...");
                dungeonHandler._transitionToRoom(hostRoomId, 'north');

                setTimeout(() => {
                    console.log("Host room loaded, secret wall should now exist");
                    console.log(`Secret wall wallGroup now exists: ${!!secretWallData.wallGroup}`);

                    // Now teleport to secret room
                    const entranceDirection = secretWallData.entranceDirection;
                    dungeonHandler._transitionToRoom(secretRoomId, entranceDirection);
                    console.log("Secret room teleport completed with secret wall in host room");
                }, 1000);
            } else {
                console.log("Already in host room but secret wall missing - forcing creation");
                dungeonHandler.secretRoomManager.checkAndCreateSecretWallForRoom(hostRoomId);

                setTimeout(() => {
                    const entranceDirection = secretWallData.entranceDirection;
                    dungeonHandler._transitionToRoom(secretRoomId, entranceDirection);
                    console.log("Secret room teleport completed with forced secret wall creation");
                }, 500);
            }
        } else {
            console.log("Secret wall already exists in scene - direct teleport");
            const entranceDirection = secretWallData.entranceDirection;
            dungeonHandler._transitionToRoom(secretRoomId, entranceDirection);
            console.log("Secret room teleport completed - secret wall already in scene");
        }

        return true;
    } catch (error) {
        console.error("Error teleporting to secret room:", error);
        return false;
    }
}

/**
 * Debug function to show secret room information
 * @returns {Object|null} Secret room debug info
 */
export function debugSecretRoom() {
    console.log("=== DEBUG: Secret Room Information ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return null;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("No dungeon handler or secret room manager found");
        return null;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);

    if (!secretRoomData || !secretWallData) {
        console.log(`No secret room found for area: "${currentArea}"`);
        return null;
    }

    const debugInfo = {
        area: currentArea,
        secretRoom: {
            id: secretRoomData.id,
            coords: secretRoomData.coords,
            shapeKey: secretRoomData.shapeKey,
            isDiscovered: secretRoomData.isDiscovered,
            visited: secretRoomData.visited
        },
        secretWall: {
            hostRoomId: secretWallData.hostRoom.id,
            hostRoomShape: secretWallData.hostRoom.shapeKey,
            direction: secretWallData.direction,
            entranceDirection: secretWallData.entranceDirection,
            offsetX: secretWallData.offsetX,
            offsetY: secretWallData.offsetY,
            isRevealed: secretWallData.isRevealed,
            wallPosition: secretWallData.wallPosition
        }
    };

    console.log("Secret Room Debug Info:");
    console.table(debugInfo.secretRoom);
    console.log("Secret Wall Debug Info:");
    console.table(debugInfo.secretWall);

    return debugInfo;
}

/**
 * Reset secret wall to closed state (for debugging)
 * @returns {boolean} True if successful
 */
export function resetSecretWall() {
    console.log("=== DEBUG: Reset Secret Wall ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("No dungeon handler or secret room manager found");
        return false;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);
    const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);

    if (!secretWallData || !secretRoomData) {
        console.error(`No secret room found for area: "${currentArea}"`);
        return false;
    }

    // Reset secret wall state
    secretWallData.isRevealed = false;
    secretWallData.isAnimating = false;
    secretRoomData.isDiscovered = false;

    // Clear any cooldowns
    const cooldownKey = `${currentArea}_${secretWallData.hostRoom.id}`;
    dungeonHandler.secretRoomManager.cooldowns.delete(cooldownKey);

    console.log("Secret wall reset to closed state");
    console.log("You should now be able to activate it with R key or voice command");

    return true;
}

/**
 * Force proximity check for secret walls (for debugging)
 * @returns {boolean} True if successful
 */
export function forceProximityCheck() {
    console.log("=== DEBUG: Force Proximity Check ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("No dungeon handler or secret room manager found");
        return false;
    }

    // Force a proximity check
    dungeonHandler.secretRoomManager.checkPlayerProximity();

    console.log("Forced proximity check completed");
    console.log("Check console for proximity messages");

    return true;
}

/**
 * Force reload the current room to regenerate secret wall properly
 * @returns {boolean} True if successful
 */
export function reloadRoomForSecretWall() {
    console.log("=== DEBUG: Reload Room for Secret Wall ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    const currentRoomId = dungeonHandler.currentRoomId;
    if (currentRoomId === undefined || currentRoomId === null) {
        console.error("No current room ID found");
        return false;
    }

    console.log(`Reloading room ${currentRoomId} to regenerate secret wall...`);

    // Force reload the current room
    dungeonHandler.loadRoom(currentRoomId, 'north');

    console.log("Room reload initiated - secret wall should now be properly integrated");

    return true;
}

/**
 * Complete secret wall fix sequence
 * @returns {boolean} True if successful
 */
export function fixSecretWall() {
    console.log("=== DEBUG: Complete Secret Wall Fix ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("No dungeon handler or secret room manager found");
        return false;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);

    if (!secretWallData) {
        console.error(`No secret wall data found for area: "${currentArea}"`);
        return false;
    }

    const hostRoomId = secretWallData.hostRoom.id;
    const currentRoomId = dungeonHandler.currentRoomId;

    console.log(`Current room: ${currentRoomId}, Host room: ${hostRoomId}`);

    // Step 1: Ensure we're in the host room
    if (currentRoomId !== hostRoomId) {
        console.log("Step 1: Going to host room...");
        dungeonHandler._transitionToRoom(hostRoomId, 'north');

        setTimeout(() => {
            console.log("Step 2: Now in host room, creating secret wall...");
            dungeonHandler.secretRoomManager.checkAndCreateSecretWallForRoom(hostRoomId);

            setTimeout(() => {
                console.log("Step 3: Testing proximity detection...");
                forceProximityCheck();
                console.log("✅ Secret wall fix complete! Walk near the wall and press R");
            }, 1000);
        }, 1500);
    } else {
        // Already in host room
        console.log("Step 1: Already in host room, creating secret wall...");
        dungeonHandler.secretRoomManager.checkAndCreateSecretWallForRoom(hostRoomId);

        setTimeout(() => {
            console.log("Step 2: Testing proximity detection...");
            forceProximityCheck();
            console.log("✅ Secret wall fix complete! Walk near the wall and press R");
        }, 1000);
    }

    return true;
}

/**
 * Comprehensive room accessibility analysis
 * @returns {Object} Analysis results
 */
export function analyzeRoomAccessibility() {
    console.log("=== COMPREHENSIVE ROOM ACCESSIBILITY ANALYSIS ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return { error: "No scene manager" };
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found");
        return { error: "No dungeon handler" };
    }

    const floorLayout = dungeonHandler.floorLayout;
    if (!floorLayout) {
        console.error("❌ No floor layout found");
        return { error: "No floor layout" };
    }

    console.log(`📊 Total rooms in floor layout: ${floorLayout.size}`);
    console.log(`🏠 Current room: ${dungeonHandler.currentRoomId}`);
    console.log(`🚪 Door triggers in current room: ${dungeonHandler.doorTriggers?.length || 0}`);

    // 1. Analyze all rooms and their connections
    const roomAnalysis = new Map();
    const allRoomIds = Array.from(floorLayout.keys()).sort((a, b) => a - b);

    console.log("\n🏠 DETAILED ROOM INVENTORY:");
    allRoomIds.forEach(roomId => {
        const room = floorLayout.get(roomId);
        const connections = room.connections || {};
        const connectionCount = Object.values(connections).filter(conn => conn !== null && conn !== undefined).length;

        roomAnalysis.set(roomId, {
            id: roomId,
            type: room.type,
            connections: connections,
            connectionCount: connectionCount,
            visited: room.visited || false,
            isSecret: room.isSecret || false,
            eventRoomKey: room.eventRoomKey || null,
            secretConnections: room.secretConnections || null
        });

        console.log(`  Room ${roomId}: ${room.type} | Connections: ${connectionCount} | Visited: ${room.visited || false} | Secret: ${room.isSecret || false}`);
        if (connectionCount > 0) {
            Object.entries(connections).forEach(([dir, targetId]) => {
                if (targetId !== null && targetId !== undefined) {
                    const targetRoom = floorLayout.get(targetId);
                    console.log(`    ${dir.toUpperCase()} → Room ${targetId} (${targetRoom?.type || 'Unknown'})`);
                }
            });
        }
        if (room.secretConnections) {
            console.log(`    SECRET CONNECTIONS:`, room.secretConnections);
        }
    });

    // 2. Build accessibility graph from Room 0
    console.log("\n🚪 ACCESSIBILITY ANALYSIS FROM ROOM 0:");
    const accessibleRooms = new Set();
    const queue = [0]; // Start from Room 0
    accessibleRooms.add(0);

    while (queue.length > 0) {
        const currentRoomId = queue.shift();
        const currentRoom = floorLayout.get(currentRoomId);

        if (currentRoom && currentRoom.connections) {
            Object.values(currentRoom.connections).forEach(targetId => {
                if (targetId !== null && targetId !== undefined && !accessibleRooms.has(targetId)) {
                    accessibleRooms.add(targetId);
                    queue.push(targetId);
                }
            });
        }
    }

    console.log(`✅ Accessible rooms from Room 0: ${accessibleRooms.size}`);
    console.log(`📍 Accessible room IDs: [${Array.from(accessibleRooms).sort((a, b) => a - b).join(', ')}]`);

    // 3. Identify inaccessible rooms
    const inaccessibleRooms = allRoomIds.filter(id => !accessibleRooms.has(id));
    console.log(`❌ Inaccessible rooms: ${inaccessibleRooms.length}`);
    if (inaccessibleRooms.length > 0) {
        console.log(`🚫 Inaccessible room IDs: [${inaccessibleRooms.join(', ')}]`);

        inaccessibleRooms.forEach(roomId => {
            const room = floorLayout.get(roomId);
            console.log(`  Room ${roomId}: ${room.type} | Connections: ${JSON.stringify(room.connections)}`);
        });
    }

    // 4. Check for broken connections (one-way connections)
    console.log("\n🔗 CONNECTION INTEGRITY CHECK:");
    let brokenConnections = 0;
    const brokenConnectionDetails = [];

    allRoomIds.forEach(roomId => {
        const room = floorLayout.get(roomId);
        if (room.connections) {
            Object.entries(room.connections).forEach(([direction, targetId]) => {
                if (targetId !== null && targetId !== undefined) {
                    const targetRoom = floorLayout.get(targetId);
                    if (targetRoom && targetRoom.connections) {
                        // Check if target room has reverse connection
                        const oppositeDir = getOppositeDirection(direction);
                        const reverseConnection = targetRoom.connections[oppositeDir];

                        if (reverseConnection !== roomId) {
                            const detail = `Room ${roomId} → Room ${targetId} (${direction}) | Reverse: ${reverseConnection}`;
                            console.log(`🔴 BROKEN CONNECTION: ${detail}`);
                            brokenConnections++;
                            brokenConnectionDetails.push(detail);
                        }
                    }
                }
            });
        }
    });

    if (brokenConnections === 0) {
        console.log("✅ All connections are bidirectional");
    } else {
        console.log(`❌ Found ${brokenConnections} broken connections`);
    }

    return {
        totalRooms: floorLayout.size,
        accessibleRooms: accessibleRooms.size,
        inaccessibleRooms: inaccessibleRooms.length,
        inaccessibleRoomIds: inaccessibleRooms,
        brokenConnections: brokenConnections,
        brokenConnectionDetails: brokenConnectionDetails,
        roomAnalysis: Array.from(roomAnalysis.values())
    };
}

/**
 * Analyze current room door triggers and connections
 * @returns {Object} Door analysis results
 */
export function analyzeDoorTriggers() {
    console.log("=== DOOR TRIGGER ANALYSIS ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return { error: "No scene manager" };
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found");
        return { error: "No dungeon handler" };
    }

    const currentRoomId = dungeonHandler.currentRoomId;
    const currentRoom = dungeonHandler.floorLayout?.get(currentRoomId);

    console.log(`🏠 Current Room: ${currentRoomId} (${currentRoom?.type || 'Unknown'})`);
    console.log(`🚪 Door triggers: ${dungeonHandler.doorTriggers?.length || 0}`);

    if (!currentRoom) {
        console.error("❌ Current room data not found");
        return { error: "No current room data" };
    }

    // Analyze room connections
    console.log("\n📋 ROOM CONNECTIONS:");
    const connections = currentRoom.connections || {};
    Object.entries(connections).forEach(([dir, targetId]) => {
        if (targetId !== null && targetId !== undefined) {
            const targetRoom = dungeonHandler.floorLayout.get(targetId);
            console.log(`  ${dir.toUpperCase()} → Room ${targetId} (${targetRoom?.type || 'Unknown'})`);
        } else {
            console.log(`  ${dir.toUpperCase()} → No connection`);
        }
    });

    // Analyze door triggers
    console.log("\n🚪 DOOR TRIGGER DETAILS:");
    if (dungeonHandler.doorTriggers && dungeonHandler.doorTriggers.length > 0) {
        dungeonHandler.doorTriggers.forEach((trigger, index) => {
            console.log(`  Trigger ${index}:`);
            console.log(`    Name: ${trigger.name}`);
            console.log(`    Position: (${trigger.position.x.toFixed(2)}, ${trigger.position.y.toFixed(2)}, ${trigger.position.z.toFixed(2)})`);
            console.log(`    Direction: ${trigger.userData?.direction}`);
            console.log(`    Target Room: ${trigger.userData?.targetRoomId}`);
            console.log(`    Visible: ${trigger.material?.visible}`);
            console.log(`    UserData:`, trigger.userData);
        });
    } else {
        console.log("  ❌ No door triggers found in current room");
    }

    // Check player position relative to triggers
    if (dungeonHandler.player && dungeonHandler.doorTriggers?.length > 0) {
        console.log("\n👤 PLAYER POSITION ANALYSIS:");
        console.log(`Player position: (${dungeonHandler.player.position.x.toFixed(2)}, ${dungeonHandler.player.position.y.toFixed(2)}, ${dungeonHandler.player.position.z.toFixed(2)})`);

        dungeonHandler.doorTriggers.forEach((trigger, index) => {
            const triggerBox = new THREE.Box3().setFromObject(trigger);
            const distance = dungeonHandler.player.position.distanceTo(trigger.position);
            const isInside = triggerBox.containsPoint(dungeonHandler.player.position);

            console.log(`  Trigger ${index} distance: ${distance.toFixed(2)} units | Inside: ${isInside}`);
        });
    }

    return {
        currentRoomId: currentRoomId,
        roomType: currentRoom.type,
        connections: connections,
        doorTriggerCount: dungeonHandler.doorTriggers?.length || 0,
        doorTriggers: dungeonHandler.doorTriggers?.map(trigger => ({
            name: trigger.name,
            position: trigger.position,
            direction: trigger.userData?.direction,
            targetRoomId: trigger.userData?.targetRoomId,
            visible: trigger.material?.visible
        })) || []
    };
}

/**
 * Test door trigger functionality by simulating player movement to each trigger
 * @returns {Object} Test results
 */
export function testDoorTriggers() {
    console.log("=== TESTING DOOR TRIGGER FUNCTIONALITY ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return { error: "No scene manager" };
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found");
        return { error: "No dungeon handler" };
    }

    const currentRoomId = dungeonHandler.currentRoomId;
    console.log(`🏠 Testing door triggers in room ${currentRoomId}`);
    console.log(`🚪 Found ${dungeonHandler.doorTriggers?.length || 0} door triggers`);

    if (!dungeonHandler.doorTriggers || dungeonHandler.doorTriggers.length === 0) {
        console.log("❌ No door triggers found to test");
        return { error: "No door triggers" };
    }

    const testResults = [];
    const originalPlayerPosition = dungeonHandler.player.position.clone();

    dungeonHandler.doorTriggers.forEach((trigger, index) => {
        console.log(`\n🔍 Testing trigger ${index}: ${trigger.name}`);
        console.log(`  Direction: ${trigger.userData?.direction}`);
        console.log(`  Target Room: ${trigger.userData?.targetRoomId}`);
        console.log(`  Position: (${trigger.position.x.toFixed(2)}, ${trigger.position.y.toFixed(2)}, ${trigger.position.z.toFixed(2)})`);

        // Test trigger collision detection
        const triggerBox = new THREE.Box3().setFromObject(trigger);
        console.log(`  Trigger bounds: min(${triggerBox.min.x.toFixed(2)}, ${triggerBox.min.y.toFixed(2)}, ${triggerBox.min.z.toFixed(2)}) max(${triggerBox.max.x.toFixed(2)}, ${triggerBox.max.y.toFixed(2)}, ${triggerBox.max.z.toFixed(2)})`);

        // Move player to trigger position for testing
        const testPosition = trigger.position.clone();
        testPosition.y = 0; // Keep player on ground
        dungeonHandler.player.position.copy(testPosition);

        // Test if player is inside trigger
        const isInside = triggerBox.containsPoint(dungeonHandler.player.position);
        console.log(`  Player inside trigger: ${isInside}`);

        // Check target room exists
        const targetRoom = dungeonHandler.floorLayout?.get(trigger.userData?.targetRoomId);
        const targetExists = !!targetRoom;
        console.log(`  Target room exists: ${targetExists} (${targetRoom?.type || 'N/A'})`);

        // Check for secret door requirements
        const requiresReveal = trigger.userData?.requiresReveal || false;
        const isRevealed = trigger.userData?.isRevealed || false;
        const animationComplete = trigger.userData?.animationComplete || false;
        console.log(`  Secret door: requires=${requiresReveal}, revealed=${isRevealed}, animComplete=${animationComplete}`);

        // Check transition cooldown
        const canTransition = dungeonHandler.transitionCooldown <= 0;
        console.log(`  Can transition: ${canTransition} (cooldown: ${dungeonHandler.transitionCooldown})`);

        // Check scene fading
        const notFading = !dungeonHandler.sceneManager.isFading;
        console.log(`  Not fading: ${notFading}`);

        const testResult = {
            triggerIndex: index,
            triggerName: trigger.name,
            direction: trigger.userData?.direction,
            targetRoomId: trigger.userData?.targetRoomId,
            position: trigger.position,
            playerInside: isInside,
            targetExists: targetExists,
            requiresReveal: requiresReveal,
            isRevealed: isRevealed,
            animationComplete: animationComplete,
            canTransition: canTransition,
            notFading: notFading,
            shouldWork: isInside && targetExists && canTransition && notFading &&
                       (!requiresReveal || (isRevealed && animationComplete))
        };

        testResults.push(testResult);

        if (testResult.shouldWork) {
            console.log(`  ✅ Trigger should work correctly`);
        } else {
            console.log(`  ❌ Trigger has issues:`);
            if (!isInside) console.log(`    - Player not inside trigger bounds`);
            if (!targetExists) console.log(`    - Target room doesn't exist`);
            if (!canTransition) console.log(`    - Transition cooldown active`);
            if (!notFading) console.log(`    - Scene is fading`);
            if (requiresReveal && !isRevealed) console.log(`    - Secret door not revealed`);
            if (requiresReveal && !animationComplete) console.log(`    - Secret door animation not complete`);
        }
    });

    // Restore original player position
    dungeonHandler.player.position.copy(originalPlayerPosition);

    const workingTriggers = testResults.filter(r => r.shouldWork).length;
    const totalTriggers = testResults.length;

    console.log(`\n📊 SUMMARY: ${workingTriggers}/${totalTriggers} triggers should work correctly`);

    return {
        currentRoomId: currentRoomId,
        totalTriggers: totalTriggers,
        workingTriggers: workingTriggers,
        testResults: testResults
    };
}

/**
 * Fix room accessibility issues by analyzing and repairing connections
 * @returns {Object} Fix results
 */
export function fixRoomAccessibility() {
    console.log("=== FIXING ROOM ACCESSIBILITY ISSUES ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return { error: "No scene manager" };
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found");
        return { error: "No dungeon handler" };
    }

    const floorLayout = dungeonHandler.floorLayout;
    if (!floorLayout) {
        console.error("❌ No floor layout found");
        return { error: "No floor layout" };
    }

    console.log(`🔧 Analyzing ${floorLayout.size} rooms for accessibility issues...`);

    // 1. First, analyze current state
    const analysis = analyzeRoomAccessibility();
    if (analysis.inaccessibleRooms === 0) {
        console.log("✅ No accessibility issues found!");
        return { success: true, message: "All rooms are accessible" };
    }

    console.log(`🔧 Found ${analysis.inaccessibleRooms} inaccessible rooms. Attempting fixes...`);

    let fixesApplied = 0;

    // 2. Fix broken bidirectional connections
    console.log("\n🔧 FIXING BROKEN CONNECTIONS:");
    const allRoomIds = Array.from(floorLayout.keys());

    allRoomIds.forEach(roomId => {
        const room = floorLayout.get(roomId);
        if (room.connections) {
            Object.entries(room.connections).forEach(([direction, targetId]) => {
                if (targetId !== null && targetId !== undefined) {
                    const targetRoom = floorLayout.get(targetId);
                    if (targetRoom && targetRoom.connections) {
                        const oppositeDir = getOppositeDirection(direction);
                        const reverseConnection = targetRoom.connections[oppositeDir];

                        if (reverseConnection !== roomId) {
                            // Fix the reverse connection
                            console.log(`🔧 Fixing connection: Room ${targetId} ${oppositeDir.toUpperCase()} → Room ${roomId}`);
                            targetRoom.connections[oppositeDir] = roomId;
                            fixesApplied++;
                        }
                    }
                }
            });
        }
    });

    // 3. Reconnect isolated rooms to the main graph
    console.log("\n🔧 RECONNECTING ISOLATED ROOMS:");
    const accessibleRooms = new Set();
    const queue = [0];
    accessibleRooms.add(0);

    // Build accessible rooms set
    while (queue.length > 0) {
        const currentRoomId = queue.shift();
        const currentRoom = floorLayout.get(currentRoomId);

        if (currentRoom && currentRoom.connections) {
            Object.values(currentRoom.connections).forEach(targetId => {
                if (targetId !== null && targetId !== undefined && !accessibleRooms.has(targetId)) {
                    accessibleRooms.add(targetId);
                    queue.push(targetId);
                }
            });
        }
    }

    // Find and reconnect isolated rooms
    const isolatedRooms = allRoomIds.filter(id => !accessibleRooms.has(id));

    isolatedRooms.forEach(isolatedRoomId => {
        const isolatedRoom = floorLayout.get(isolatedRoomId);
        if (!isolatedRoom || isolatedRoom.type === 'EVENT') return; // Skip event rooms

        console.log(`🔧 Reconnecting isolated room ${isolatedRoomId} (${isolatedRoom.type})`);

        // Find the closest accessible room
        let closestRoom = null;
        let closestDistance = Infinity;

        accessibleRooms.forEach(accessibleRoomId => {
            const accessibleRoom = floorLayout.get(accessibleRoomId);
            if (accessibleRoom && accessibleRoom.coords && isolatedRoom.coords) {
                const distance = Math.abs(accessibleRoom.coords.x - isolatedRoom.coords.x) +
                               Math.abs(accessibleRoom.coords.y - isolatedRoom.coords.y);

                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestRoom = accessibleRoom;
                }
            }
        });

        if (closestRoom) {
            // Determine connection direction
            const dx = isolatedRoom.coords.x - closestRoom.coords.x;
            const dy = isolatedRoom.coords.y - closestRoom.coords.y;

            let direction, oppositeDir;
            if (Math.abs(dx) > Math.abs(dy)) {
                direction = dx > 0 ? 'e' : 'w';
                oppositeDir = dx > 0 ? 'w' : 'e';
            } else {
                direction = dy > 0 ? 's' : 'n';
                oppositeDir = dy > 0 ? 'n' : 's';
            }

            // Create bidirectional connection
            closestRoom.connections[direction] = isolatedRoomId;
            isolatedRoom.connections[oppositeDir] = closestRoom.id;

            console.log(`✅ Connected Room ${closestRoom.id} ${direction.toUpperCase()} ↔ Room ${isolatedRoomId} ${oppositeDir.toUpperCase()}`);
            fixesApplied++;
        }
    });

    // 4. Reload current room to refresh door triggers
    console.log("\n🔧 REFRESHING CURRENT ROOM:");
    const currentRoomId = dungeonHandler.currentRoomId;
    console.log(`🔄 Reloading room ${currentRoomId} to refresh door triggers...`);

    // Force reload the current room
    dungeonHandler.loadRoom(currentRoomId, null);

    console.log(`✅ Applied ${fixesApplied} fixes to room accessibility`);

    // 5. Re-analyze to verify fixes
    console.log("\n🔍 VERIFYING FIXES:");
    const postFixAnalysis = analyzeRoomAccessibility();

    return {
        success: true,
        fixesApplied: fixesApplied,
        beforeFix: {
            totalRooms: analysis.totalRooms,
            accessibleRooms: analysis.accessibleRooms,
            inaccessibleRooms: analysis.inaccessibleRooms
        },
        afterFix: {
            totalRooms: postFixAnalysis.totalRooms,
            accessibleRooms: postFixAnalysis.accessibleRooms,
            inaccessibleRooms: postFixAnalysis.inaccessibleRooms
        }
    };
}

/**
 * Debug the event room system to see why no event room was assigned
 * @returns {Object} Event room debug results
 */
export function debugEventRoomSystem() {
    console.log("=== EVENT ROOM SYSTEM DEBUG ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return { error: "No scene manager" };
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("❌ No dungeon handler found");
        return { error: "No dungeon handler" };
    }

    console.log("🔍 Checking EventRoomManager availability...");
    console.log(`EventRoomManager in dungeonHandler: ${!!dungeonHandler.eventRoomManager}`);

    if (dungeonHandler.eventRoomManager) {
        console.log("✅ EventRoomManager found in dungeonHandler");
        console.log(`Event room data available: ${Object.keys(dungeonHandler.eventRoomManager.eventRooms || {}).length} event types`);

        if (dungeonHandler.eventRoomManager.eventRooms) {
            console.log("📋 Available event room types:", Object.keys(dungeonHandler.eventRoomManager.eventRooms));
        }
    } else {
        console.log("❌ No EventRoomManager in dungeonHandler");
    }

    // Check if dungeonGenerator has eventRoomManager
    if (dungeonHandler.dungeonGenerator) {
        console.log(`EventRoomManager in dungeonGenerator: ${!!dungeonHandler.dungeonGenerator.eventRoomManager}`);

        if (dungeonHandler.dungeonGenerator.eventRoomManager) {
            console.log("✅ EventRoomManager found in dungeonGenerator");
        } else {
            console.log("❌ No EventRoomManager in dungeonGenerator - this is the problem!");
        }
    } else {
        console.log("❌ No dungeonGenerator found in dungeonHandler");
    }

    // Check current floor layout for any event room traces
    const floorLayout = dungeonHandler.floorLayout;
    if (floorLayout) {
        console.log("\n🔍 Checking floor layout for event room traces...");

        let eventRoomFound = false;
        let roomsWithEventData = [];

        floorLayout.forEach((room, roomId) => {
            if (room.type === 'EVENT') {
                eventRoomFound = true;
                console.log(`✅ Found EVENT room: ${roomId}`);
            }

            if (room.eventRoomKey || room.eventRoomName) {
                roomsWithEventData.push({
                    id: roomId,
                    type: room.type,
                    eventRoomKey: room.eventRoomKey,
                    eventRoomName: room.eventRoomName
                });
            }
        });

        if (!eventRoomFound) {
            console.log("❌ No EVENT type rooms found in floor layout");
        }

        if (roomsWithEventData.length > 0) {
            console.log("📋 Rooms with event data:", roomsWithEventData);
        } else {
            console.log("❌ No rooms with event data found");
        }
    }

    // Simulate what _getNormalRooms would return
    if (floorLayout) {
        console.log("\n🔍 Simulating _getNormalRooms filter...");
        const allRooms = Array.from(floorLayout.values());
        console.log(`Total rooms: ${allRooms.length}`);

        const normalRooms = allRooms.filter(room =>
            room.type === 'Normal' &&
            room.id !== 0 &&
            !room.isSecret
        );

        console.log(`Normal rooms available for event assignment: ${normalRooms.length}`);
        console.log(`Normal room IDs: [${normalRooms.map(r => r.id).join(', ')}]`);

        if (normalRooms.length === 0) {
            console.log("❌ No normal rooms available - this explains why no event room was assigned!");

            // Debug why no normal rooms
            allRooms.forEach(room => {
                let reason = [];
                if (room.type !== 'Normal') reason.push(`type=${room.type}`);
                if (room.id === 0) reason.push('is Room 0');
                if (room.isSecret) reason.push('is secret');

                if (reason.length > 0) {
                    console.log(`  Room ${room.id}: excluded (${reason.join(', ')})`);
                }
            });
        }
    }

    // CRITICAL: Check if dungeonGenerator.layout exists and compare with floorLayout
    console.log("\n🔍 CHECKING DUNGEON GENERATOR LAYOUT:");
    if (dungeonHandler.dungeonGenerator && dungeonHandler.dungeonGenerator.layout) {
        console.log("✅ DungeonGenerator.layout exists");
        const generatorRooms = Array.from(dungeonHandler.dungeonGenerator.layout.values());
        console.log(`Generator layout rooms: ${generatorRooms.length}`);

        console.log("Generator room types:");
        generatorRooms.forEach(room => {
            console.log(`  Room ${room.id}: ${room.type} (isSecret: ${room.isSecret})`);
        });

        // Test _getNormalRooms on generator layout
        const generatorNormalRooms = generatorRooms.filter(room =>
            room.type === 'Normal' &&
            room.id !== 0 &&
            !room.isSecret
        );

        console.log(`Normal rooms in generator layout: ${generatorNormalRooms.length}`);
        console.log(`Generator normal room IDs: [${generatorNormalRooms.map(r => r.id).join(', ')}]`);

        if (generatorNormalRooms.length === 0) {
            console.log("❌ FOUND THE ISSUE: Generator layout has no normal rooms!");
            console.log("This explains why event room assignment fails.");

            // Check what happened to all the rooms
            const roomTypeCounts = {};
            generatorRooms.forEach(room => {
                roomTypeCounts[room.type] = (roomTypeCounts[room.type] || 0) + 1;
            });
            console.log("Generator room type distribution:", roomTypeCounts);
        }
    } else {
        console.log("❌ DungeonGenerator.layout not found");
    }

    return {
        eventRoomManagerInHandler: !!dungeonHandler.eventRoomManager,
        eventRoomManagerInGenerator: !!dungeonHandler.dungeonGenerator?.eventRoomManager,
        eventRoomFound: false, // Will be updated by the actual check
        normalRoomsAvailable: 0 // Will be updated by the actual check
    };
}

/**
 * Helper function to get opposite direction
 */
function getOppositeDirection(direction) {
    const opposites = {
        'n': 's', 's': 'n',
        'e': 'w', 'w': 'e',
        'north': 'south', 'south': 'north',
        'east': 'west', 'west': 'east'
    };
    return opposites[direction.toLowerCase()] || direction;
}

/**
 * Restart proximity monitoring system (for when it gets stuck)
 * @returns {boolean} True if successful
 */
export function restartProximityMonitoring() {
    console.log("=== DEBUG: Restart Proximity Monitoring ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("❌ No dungeon handler or secret room manager found");
        return false;
    }

    console.log("🔄 Stopping proximity monitoring...");
    dungeonHandler.secretRoomManager.stopProximityMonitoring();

    console.log("🔄 Resetting proximity state...");
    dungeonHandler.secretRoomManager.isPlayerNearSecret = false;

    console.log("🔄 Starting proximity monitoring...");
    dungeonHandler.secretRoomManager.startProximityMonitoring();

    console.log("✅ Proximity monitoring restarted successfully");
    console.log("🔍 Walk near secret walls to test detection");

    return true;
}

/**
 * Quick test to verify secret wall creation and proximity
 * @returns {boolean} True if successful
 */
export function quickTestSecretWall() {
    console.log("=== QUICK SECRET WALL TEST ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("❌ No dungeon handler or secret room manager found");
        return false;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);
    const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);

    console.log("🔍 Secret Wall Status:");
    console.log(`   Area: ${currentArea}`);
    console.log(`   Secret room exists: ${!!secretRoomData}`);
    console.log(`   Secret wall data exists: ${!!secretWallData}`);

    if (secretWallData) {
        console.log(`   Host room: ${secretWallData.hostRoom.id}`);
        console.log(`   Current room: ${dungeonHandler.currentRoomId}`);
        console.log(`   Direction: ${secretWallData.direction}`);
        console.log(`   Wall group exists: ${!!secretWallData.wallGroup}`);
        console.log(`   Is revealed: ${secretWallData.isRevealed}`);

        if (secretWallData.wallGroup) {
            console.log(`   Wall position: (${secretWallData.wallPosition?.x?.toFixed(2) || 'N/A'}, ${secretWallData.wallPosition?.y?.toFixed(2) || 'N/A'}, ${secretWallData.wallPosition?.z?.toFixed(2) || 'N/A'})`);
        }
    }

    // Test proximity if in correct room
    if (secretWallData && dungeonHandler.currentRoomId === secretWallData.hostRoom.id) {
        console.log("🎯 Testing proximity detection...");
        dungeonHandler.secretRoomManager.checkPlayerProximity();

        if (dungeonHandler.player) {
            const playerPos = dungeonHandler.player.position;
            console.log(`   Player position: (${playerPos.x.toFixed(2)}, ${playerPos.y.toFixed(2)}, ${playerPos.z.toFixed(2)})`);
        }
    } else if (secretWallData) {
        console.log(`⚠️ Not in host room (current: ${dungeonHandler.currentRoomId}, host: ${secretWallData.hostRoom.id})`);
        console.log("   Use teleportToSecret() or fixSecretWall() to get to the right room");
    }

    return true;
}

/**
 * Force secret wall to be revealed (for when you're in secret room and need to exit)
 * @returns {boolean} True if successful
 */
export function forceSecretWallRevealed() {
    console.log("=== DEBUG: Force Secret Wall Revealed ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("No dungeon handler or secret room manager found");
        return false;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);

    if (!secretWallData) {
        console.error(`No secret wall found for area: "${currentArea}"`);
        return false;
    }

    // Force secret wall to be revealed
    secretWallData.isRevealed = true;
    secretWallData.isAnimating = false;

    console.log(`✅ Forced secret wall isRevealed=true for area ${currentArea}`);
    console.log("Now try walking out of the secret room");

    return true;
}

/**
 * Comprehensive secret door system test
 * @returns {boolean} True if all tests pass
 */
export function testSecretDoorSystem() {
    console.log("=== COMPREHENSIVE SECRET DOOR SYSTEM TEST ===");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("❌ Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler || !dungeonHandler.secretRoomManager) {
        console.error("❌ No dungeon handler or secret room manager found");
        return false;
    }

    const currentArea = dungeonHandler.currentArea?.name || 'The Catacombs';
    const secretRoomData = dungeonHandler.secretRoomManager.secretRooms.get(currentArea);
    const secretWallData = dungeonHandler.secretRoomManager.secretWalls.get(currentArea);

    console.log("🔍 Testing Secret Door System Components:");

    // Test 1: Secret Room Generation
    console.log("\n1. SECRET ROOM GENERATION:");
    if (secretRoomData) {
        console.log("✅ Secret room exists:", {
            id: secretRoomData.id,
            coords: secretRoomData.coords,
            type: secretRoomData.type,
            isDiscovered: secretRoomData.isDiscovered
        });
    } else {
        console.log("❌ No secret room found for area:", currentArea);
        return false;
    }

    // Test 2: Secret Wall Data
    console.log("\n2. SECRET WALL DATA:");
    if (secretWallData) {
        console.log("✅ Secret wall data exists:", {
            hostRoomId: secretWallData.hostRoom.id,
            direction: secretWallData.direction,
            isRevealed: secretWallData.isRevealed,
            wallGroupExists: !!secretWallData.wallGroup
        });
    } else {
        console.log("❌ No secret wall data found for area:", currentArea);
        return false;
    }

    // Test 3: Wall Integration
    console.log("\n3. WALL INTEGRATION:");
    const hostRoomId = secretWallData.hostRoom.id;
    const currentRoomId = dungeonHandler.currentRoomId;

    if (currentRoomId === hostRoomId) {
        if (secretWallData.wallGroup) {
            console.log("✅ Secret wall is integrated in current room");
            console.log("   Wall group name:", secretWallData.wallGroup.name);
            console.log("   Wall position:", secretWallData.wallGroup.position);
        } else {
            console.log("⚠️ In host room but secret wall not integrated");
            console.log("   Attempting to create secret wall...");
            dungeonHandler.secretRoomManager.checkAndCreateSecretWallForRoom(hostRoomId);

            setTimeout(() => {
                if (secretWallData.wallGroup) {
                    console.log("✅ Secret wall created successfully");
                } else {
                    console.log("❌ Failed to create secret wall");
                }
            }, 500);
        }
    } else {
        console.log("ℹ️ Not in host room (current:", currentRoomId, "host:", hostRoomId, ")");
        console.log("   Use teleportToSecret() to test wall integration");
    }

    // Test 4: Player Proximity Detection
    console.log("\n4. PROXIMITY DETECTION:");
    if (dungeonHandler.player) {
        const playerPos = dungeonHandler.player.position;
        console.log("✅ Player position:", {
            x: playerPos.x.toFixed(2),
            y: playerPos.y.toFixed(2),
            z: playerPos.z.toFixed(2)
        });

        // Force proximity check
        dungeonHandler.secretRoomManager.checkPlayerProximity();
        console.log("✅ Proximity check triggered");
    } else {
        console.log("❌ Player not found");
    }

    // Test 5: Interaction Systems
    console.log("\n5. INTERACTION SYSTEMS:");

    // Voice recognition
    if (dungeonHandler.secretRoomManager.recognition) {
        console.log("✅ Voice recognition initialized");
    } else {
        console.log("⚠️ Voice recognition not available");
    }

    // Keyboard handler
    if (dungeonHandler.secretRoomManager.keyboardHandler) {
        console.log("✅ Keyboard handler (R key) active");
    } else {
        console.log("❌ Keyboard handler not found");
    }

    // Mobile detection
    if (dungeonHandler.secretRoomManager.isMobile) {
        console.log("✅ Mobile device detected - double-tap joystick available");
    } else {
        console.log("ℹ️ Desktop device - voice/keyboard controls available");
    }

    // Test 6: Door Trigger System
    console.log("\n6. DOOR TRIGGER SYSTEM:");
    if (dungeonHandler.doorTriggers) {
        const secretTriggers = dungeonHandler.doorTriggers.filter(trigger =>
            trigger.userData.isSecretDoor === true
        );
        console.log(`✅ Found ${secretTriggers.length} secret door triggers`);

        secretTriggers.forEach((trigger, index) => {
            console.log(`   Trigger ${index + 1}:`, {
                isRevealed: trigger.userData.isRevealed,
                animationComplete: trigger.userData.animationComplete,
                targetRoomId: trigger.userData.targetRoomId
            });
        });
    } else {
        console.log("❌ Door trigger system not found");
    }

    console.log("\n=== TEST SUMMARY ===");
    console.log("✅ Secret door system components verified");
    console.log("📋 Next steps:");
    console.log("   1. Use teleportToSecret() to test full functionality");
    console.log("   2. Walk near secret wall and press R key");
    console.log("   3. Test animation and room transition");
    console.log("   4. Test exit from secret room");

    return true;
}

// Make commands available globally
window.teleportToBoss = teleportToBoss;
window.teleportToRoom = teleportToRoom;
window.teleportToSecret = teleportToSecret;
window.teleportToSecretAsync = teleportToSecretAsync;
window.forceSecretWallRevealed = forceSecretWallRevealed;
window.listRooms = listRooms;
window.testSecretDoorSystem = testSecretDoorSystem;
window.debugSecretRoom = debugSecretRoom;
window.resetSecretWall = resetSecretWall;
window.forceProximityCheck = forceProximityCheck;
window.reloadRoomForSecretWall = reloadRoomForSecretWall;
window.fixSecretWall = fixSecretWall;
window.quickTestSecretWall = quickTestSecretWall;
window.restartProximityMonitoring = restartProximityMonitoring;
window.analyzeRoomAccessibility = analyzeRoomAccessibility;
window.analyzeDoorTriggers = analyzeDoorTriggers;
window.fixRoomAccessibility = fixRoomAccessibility;
window.testDoorTriggers = testDoorTriggers;
window.debugEventRoomSystem = debugEventRoomSystem;
window.createEvent = createEvent;
window.listEventRooms = listEventRooms;
window.help = help;
window.itemlist = itemlist;
window.give = give;
window.godMode = godMode;
window.noclip = noclip;
window.heal = heal;
window.setHealth = setHealth;
window.addCards = addCards;
window.coords = coords;
window.speed = speed;
window.revealMap = revealMap;
window.unlockAllDoors = unlockAllDoors;

console.log("Teleport debug commands loaded. Available commands:");
console.log("- teleportToBoss() - Teleport to the boss room");
console.log("- teleportToRoom(roomId) - Teleport to a specific room by ID");
console.log("- createEvent(eventRoomId) - Create and teleport to specific event room");
console.log("- createEvent('arcade_game') - Spawn arcade machine with rhythm mini-game");
console.log("- listEventRooms() - List all available event rooms");
console.log("- showDungeonMap() - Display ASCII dungeon map with event room details");
console.log("- teleportToSecret() - Teleport to the secret room (fully functional)");
console.log("- teleportToSecretAsync() - Teleport to the secret room (waits for initialization)");
console.log("- forceSecretWallRevealed() - Force secret wall to revealed state (use if exit doesn't work)");
console.log("- listRooms() - List all available rooms");
console.log("- testSecretDoorSystem() - Comprehensive test of secret door functionality");
console.log("- forceProximityCheck() - Force proximity detection check");
console.log("- fixSecretWall() - Complete secret wall fix sequence");
console.log("- quickTestSecretWall() - Quick test of secret wall status and proximity");
console.log("- restartProximityMonitoring() - Restart proximity monitoring system");
console.log("- reloadRoomForSecretWall() - Reload current room to regenerate secret wall");
console.log("");
console.log("🔍 ROOM ACCESSIBILITY DEBUG:");
console.log("- analyzeRoomAccessibility() - Comprehensive room accessibility analysis");
console.log("- analyzeDoorTriggers() - Analyze current room door triggers and connections");
console.log("- testDoorTriggers() - Test door trigger functionality by simulating player movement");
console.log("- fixRoomAccessibility() - Automatically fix room accessibility issues");
