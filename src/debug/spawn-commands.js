/**
 * Debug commands for spawning enemies, items, and testing positioning
 */

/**
 * Spawn a nairabos enemy at the player's current position for testing
 * @returns {boolean} Success status
 */
export function spawnNairabos() {
    console.log("🔥 DEBUG: Spawning Nairabos for positioning test");

    // Get the current scene manager and dungeon handler
    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    if (!dungeonHandler.player) {
        console.error("Player not found");
        return false;
    }

    // Get player position and spawn nairabos nearby
    const playerPos = dungeonHandler.player.position.clone();
    const spawnPos = playerPos.clone();
    spawnPos.x += 3; // Spawn 3 units to the right of player
    spawnPos.z += 3; // Spawn 3 units forward from player
    spawnPos.y = 0; // Let the spawn system handle Y positioning

    console.log(`🔥 Spawning nairabos at position: (${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)})`);

    try {
        const nairabos = dungeonHandler._spawnEnemy('nairabos', spawnPos);
        
        if (nairabos) {
            console.log(`🔥 SUCCESS: Nairabos spawned successfully!`);
            console.log(`🔥 Final position: (${nairabos.position.x.toFixed(2)}, ${nairabos.position.y.toFixed(2)}, ${nairabos.position.z.toFixed(2)})`);
            console.log(`🔥 Scale: (${nairabos.scale.x}, ${nairabos.scale.y}, ${nairabos.scale.z})`);
            
            // Log some debug info about the model
            if (nairabos.userData) {
                console.log(`🔥 Enemy type: ${nairabos.userData.enemyType || 'unknown'}`);
                console.log(`🔥 Base position Y: ${nairabos.userData.basePositionY || 'not set'}`);
            }
            
            return true;
        } else {
            console.error("🔥 FAILED: Could not spawn nairabos");
            return false;
        }
    } catch (error) {
        console.error("🔥 ERROR spawning nairabos:", error);
        return false;
    }
}

/**
 * Spawn any enemy type at the player's position for testing
 * @param {string} enemyType - Type of enemy to spawn
 * @returns {boolean} Success status
 */
export function spawnEnemy(enemyType) {
    console.log(`🧪 DEBUG: Spawning ${enemyType} for testing`);

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    if (!dungeonHandler.player) {
        console.error("Player not found");
        return false;
    }

    const playerPos = dungeonHandler.player.position.clone();
    const spawnPos = playerPos.clone();
    spawnPos.x += 2;
    spawnPos.z += 2;
    spawnPos.y = 0;

    console.log(`🧪 Spawning ${enemyType} at position: (${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)})`);

    try {
        const enemy = dungeonHandler._spawnEnemy(enemyType, spawnPos);

        if (enemy) {
            console.log(`🧪 SUCCESS: ${enemyType} spawned successfully!`);
            if (enemy.mesh) {
                console.log(`🧪 Final position: (${enemy.mesh.position.x.toFixed(2)}, ${enemy.mesh.position.y.toFixed(2)}, ${enemy.mesh.position.z.toFixed(2)})`);
            } else if (enemy.position) {
                console.log(`🧪 Final position: (${enemy.position.x.toFixed(2)}, ${enemy.position.y.toFixed(2)}, ${enemy.position.z.toFixed(2)})`);
            }
            return true;
        } else {
            console.error(`🧪 FAILED: Could not spawn ${enemyType}`);
            return false;
        }
    } catch (error) {
        console.error(`🧪 ERROR spawning ${enemyType}:`, error);
        return false;
    }
}

/**
 * Test nairabos positioning and scaling comprehensively
 * @returns {boolean} Success status
 */
export function testNairabosPositioning() {
    console.log("🧪 COMPREHENSIVE NAIRABOS POSITIONING TEST");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    // Clear existing enemies first
    clearEnemies();

    // Spawn a new nairabos
    console.log("🧪 Step 1: Spawning nairabos...");
    const success = spawnNairabos();

    if (!success) {
        console.error("🧪 FAILED: Could not spawn nairabos for testing");
        return false;
    }

    // Wait a moment for animation handler to initialize
    setTimeout(() => {
        const nairabos = dungeonHandler.activeEnemies.find(enemy =>
            enemy.userData && enemy.userData.enemyType === 'nairabos'
        );

        if (!nairabos) {
            console.error("🧪 FAILED: Nairabos not found after spawn");
            return;
        }

        console.log("🧪 Step 2: Analyzing nairabos positioning...");
        console.log(`🧪 Position: (${nairabos.position.x.toFixed(2)}, ${nairabos.position.y.toFixed(2)}, ${nairabos.position.z.toFixed(2)})`);
        console.log(`🧪 Scale: (${nairabos.scale.x.toFixed(3)}, ${nairabos.scale.y.toFixed(3)}, ${nairabos.scale.z.toFixed(3)})`);

        // Check if animation handler exists
        const hasAnimHandler = nairabos.userData && nairabos.userData.animationHandler;
        console.log(`🧪 Animation Handler: ${hasAnimHandler ? 'Present' : 'Missing'}`);

        // Check body parts
        const bodyParts = {
            body: nairabos.getObjectByName('body'),
            head: nairabos.getObjectByName('head'),
            leftLeg: nairabos.getObjectByName('leftLeg'),
            rightLeg: nairabos.getObjectByName('rightLeg')
        };

        console.log("🧪 Body parts found:", {
            body: !!bodyParts.body,
            head: !!bodyParts.head,
            leftLeg: !!bodyParts.leftLeg,
            rightLeg: !!bodyParts.rightLeg
        });

        // Analyze expected vs actual positioning with visual elevation approach
        const expectedGroundOffset = 15 * 0.06; // 15 voxels * nairabosVoxelSize (collision box at ground)
        const expectedScaledOffset = expectedGroundOffset * 3.5; // After 3.5x scaling
        const expectedFloorOffset = 0.5; // Minimal floor curvature offset
        const expectedCollisionHeight = expectedScaledOffset + expectedFloorOffset;
        const visualElevation = 1.0 * 3.5; // Visual model elevated 1 unit, then scaled
        const expectedVisualHeight = expectedCollisionHeight + visualElevation;

        console.log(`🧪 Expected collision box height: ${expectedCollisionHeight.toFixed(3)} (ground level)`);
        console.log(`🧪 Expected visual elevation: ${visualElevation.toFixed(3)} (visual model offset)`);
        console.log(`🧪 Expected total visual height: ${expectedVisualHeight.toFixed(3)} (collision + visual)`);
        console.log(`🧪 Actual Y position: ${nairabos.position.y.toFixed(3)}`);

        // Check if visual group exists
        const visualGroup = nairabos.getObjectByName('visualModel');
        console.log(`🧪 Visual group found: ${!!visualGroup}`);
        if (visualGroup) {
            console.log(`🧪 Visual group position: (${visualGroup.position.x.toFixed(2)}, ${visualGroup.position.y.toFixed(2)}, ${visualGroup.position.z.toFixed(2)})`);
        }

        const positionDifference = Math.abs(nairabos.position.y - expectedCollisionHeight);
        console.log(`🧪 Position difference from expected collision height: ${positionDifference.toFixed(3)} units`);

        if (positionDifference < 2.0) {
            console.log("🧪 ✅ POSITIONING TEST PASSED - Nairabos collision box correctly positioned");
        } else {
            console.log("🧪 ⚠️ POSITIONING TEST WARNING - Collision box position difference detected");
        }

        // Test scale stability over time
        console.log("🧪 Step 3: Testing scale stability...");
        let scaleChecks = 0;
        const scaleTest = setInterval(() => {
            scaleChecks++;
            const currentScale = nairabos.scale.x;
            console.log(`🧪 Scale check ${scaleChecks}: ${currentScale.toFixed(4)}`);

            if (scaleChecks >= 5) {
                clearInterval(scaleTest);
                console.log("🧪 ✅ SCALE STABILITY TEST COMPLETED");

                // Final assessment for visual elevation approach
                const visualGroup = nairabos.getObjectByName('visualModel');
                if (visualGroup && visualGroup.position.y >= 0.8 && visualGroup.position.y <= 1.2) {
                    console.log("🧪 🎉 OVERALL TEST RESULT: NAIRABOS VISUAL ELEVATION APPEARS CORRECT!");
                    console.log("🧪 The visual model is properly elevated for ground clearance!");
                    console.log("🧪 The nairabos should appear standing properly above the ground!");
                } else if (nairabos.position.y > 3.0) {
                    console.log("🧪 ✅ OVERALL TEST RESULT: NAIRABOS COLLISION BOX POSITIONING IS GOOD");
                    console.log("🧪 The collision box is properly positioned at ground level.");
                    if (!visualGroup) {
                        console.log("🧪 ⚠️ WARNING: Visual group not found - visual elevation may not be working.");
                    }
                } else {
                    console.log("🧪 ❌ OVERALL TEST RESULT: NAIRABOS POSITIONING NEEDS ADJUSTMENT");
                    console.log("🧪 Try using fixNairabosHeight() to adjust manually.");
                }
            }
        }, 1000);

    }, 500); // Wait 500ms for initialization

    return true;
}

/**
 * Manually adjust nairabos height for testing
 * @param {number} yOffset - Amount to move nairabos up (default: 5.0)
 * @returns {boolean} Success status
 */
export function fixNairabosHeight(yOffset = 5.0) {
    console.log(`🔧 DEBUG: Manually adjusting nairabos height by ${yOffset} units`);

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    // Find nairabos enemies
    const nairabosEnemies = dungeonHandler.activeEnemies.filter(enemy =>
        enemy.userData && enemy.userData.enemyType === 'nairabos'
    );

    if (nairabosEnemies.length === 0) {
        console.error("No nairabos enemies found. Spawn one first with spawnNairabos()");
        return false;
    }

    nairabosEnemies.forEach((nairabos, index) => {
        const oldY = nairabos.position.y;
        nairabos.position.y += yOffset;
        console.log(`🔧 Nairabos ${index + 1}: Moved from Y=${oldY.toFixed(2)} to Y=${nairabos.position.y.toFixed(2)}`);

        // Update base position for animation system
        if (nairabos.userData) {
            nairabos.userData.basePositionY = nairabos.position.y;
        }
    });

    console.log(`🔧 SUCCESS: Adjusted ${nairabosEnemies.length} nairabos enemies`);
    return true;
}

/**
 * Clear all enemies in the current room
 * @returns {boolean} Success status
 */
export function clearEnemies() {
    console.log("🧹 DEBUG: Clearing all enemies");

    const sceneManager = window.sceneManager;
    if (!sceneManager) {
        console.error("Scene manager not found");
        return false;
    }

    const dungeonHandler = sceneManager.currentHandler || sceneManager.activeSceneHandler;
    if (!dungeonHandler) {
        console.error("No dungeon handler found");
        return false;
    }

    try {
        const enemyCount = dungeonHandler.activeEnemies.length;
        
        // Remove all enemies from the scene
        dungeonHandler.activeEnemies.forEach(enemy => {
            if (enemy && enemy.parent) {
                enemy.parent.remove(enemy);
            }
        });
        
        // Clear the active enemies array
        dungeonHandler.activeEnemies.length = 0;
        
        console.log(`🧹 SUCCESS: Cleared ${enemyCount} enemies`);
        return true;
    } catch (error) {
        console.error("🧹 ERROR clearing enemies:", error);
        return false;
    }
}

/**
 * Test event room wall rendering by teleporting to different event rooms
 * @returns {boolean} Success status
 */
export function testEventRoomWalls() {
    console.log("🧪 EVENT ROOM WALL RENDERING TEST");
    console.log("🧪 This will teleport you through different event rooms to test wall rendering");
    console.log("🧪 Look for proper wall tops/overhangs when viewing from above");

    const eventRooms = [
        { name: "Mysterious Pond", command: "teleportToRoom('mysteriousPond')" },
        { name: "Ominous Treasure", command: "teleportToRoom('ominousTreasure')" }
    ];

    console.log("🧪 Available event rooms to test:");
    eventRooms.forEach((room, index) => {
        console.log(`🧪 ${index + 1}. ${room.name} - Run: ${room.command}`);
    });

    console.log("🧪 After teleporting, use first-person mode ('C' key) and look down from above to inspect wall tops");
    console.log("🧪 Walls should have proper overhangs and be 'closed off at the top' like catacomb walls");

    return true;
}

/**
 * Emergency command to force clear everything and reset
 * @returns {boolean} Success status
 */
export function emergencyReset() {
    console.log("🚨 EMERGENCY RESET - Clearing everything");

    try {
        const sceneManager = window.sceneManager;
        const dungeonHandler = sceneManager?.currentHandler || sceneManager?.activeSceneHandler;

        if (dungeonHandler) {
            // Clear all enemies
            console.log("🚨 Clearing all active enemies");
            dungeonHandler.activeEnemies.length = 0;

            // Clear all projectiles
            console.log("🚨 Clearing all projectiles");
            dungeonHandler.activeProjectiles.length = 0;

            console.log("🚨 Emergency reset complete");
        }

        return true;
    } catch (error) {
        console.error("🚨 Emergency reset failed:", error);
        return false;
    }
}

/**
 * Test chest spawning in pond room
 */
window.testPondChest = function() {
    console.log('🧪 Testing pond room chest spawning...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error('❌ EventRoomManager not available');
        return;
    }
    
    const currentRoomId = dungeonHandler.currentRoomId;
    const roomHandler = eventRoomManager.roomHandlers.get(currentRoomId);
    
    if (!roomHandler) {
        console.error('❌ No room handler found for current room');
        return;
    }
    
    if (roomHandler.constructor.name !== 'MysteriousPondRoom') {
        console.error('❌ Current room is not a pond room');
        return;
    }
    
    console.log('✅ Found pond room, spawning chest...');
    roomHandler.spawnChestAtPondCenter().then(() => {
        console.log('✅ Chest spawning completed!');
    }).catch(error => {
        console.error('❌ Chest spawning failed:', error);
    });
};

/**
 * Force trigger pond room enemy defeat logic
 */
window.testPondEnemyDefeat = function() {
    console.log('🧪 Testing pond room enemy defeat logic...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (!eventRoomManager) {
        console.error('❌ EventRoomManager not available');
        return;
    }
    
    const currentRoomId = dungeonHandler.currentRoomId;
    const roomHandler = eventRoomManager.roomHandlers.get(currentRoomId);
    
    if (!roomHandler) {
        console.error('❌ No room handler found for current room');
        return;
    }
    
    if (roomHandler.constructor.name !== 'MysteriousPondRoom') {
        console.error('❌ Current room is not a pond room');
        return;
    }
    
    console.log('✅ Found pond room, triggering enemy defeat...');
    roomHandler.handleEnemyDefeat('test_nairabos');
    console.log('✅ Enemy defeat logic triggered!');
};

/**
 * Test the fast dungeon entry debug mode
 */
window.testFastDungeonEntry = function() {
    console.log('🚀 Testing fast dungeon entry debug mode...');
    
    // Check if we're in the character creation scene
    if (!window.gameInstance || !window.gameInstance.sceneManager) {
        console.error('❌ Game instance not available');
        return;
    }
    
    const sceneManager = window.gameInstance.sceneManager;
    if (!sceneManager.currentHandler || sceneManager.currentHandler.constructor.name !== 'CharacterCreationHandler') {
        console.error('❌ Not in character creation scene. This command only works during the campfire scene.');
        return;
    }
    
    console.log('✅ In character creation scene, triggering fast dungeon entry...');
    sceneManager.currentHandler._triggerDebugDungeonTransition();
    console.log('✅ Fast dungeon entry triggered!');
};

/**
 * Test player movement and controls
 */
window.testPlayerMovement = function() {
    console.log('🎮 Testing player movement and controls...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    if (!dungeonHandler.playerController) {
        console.error('❌ PlayerController not available');
        return;
    }
    
    console.log('✅ PlayerController found:', dungeonHandler.playerController.constructor.name);
    console.log('🎮 Controls enabled:', dungeonHandler.playerController.enabled);
    console.log('🏃 Movement enabled:', dungeonHandler.playerController.movementEnabled);
    
    if (!dungeonHandler.playerController.enabled) {
        console.log('🔧 Enabling player controls...');
        dungeonHandler.playerController.enable();
        console.log('✅ Player controls enabled!');
    } else {
        console.log('✅ Player controls already enabled');
    }
    
    // Test basic movement
    if (dungeonHandler.player) {
        console.log('🎯 Player position:', dungeonHandler.player.position);
        console.log('📷 Camera position:', dungeonHandler.camera.position);
    }
};

/**
 * Force start dungeon music
 */
window.forceStartDungeonMusic = function() {
    console.log('🎵 Force starting dungeon music...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    if (!dungeonHandler.audioManager) {
        console.error('❌ AudioManager not available');
        return;
    }
    
    console.log('🎵 Initializing and starting music system...');
    dungeonHandler.audioManager.initMusicSystem().then(success => {
        if (success) {
            console.log('✅ Music system initialized, starting catacombs music');
            dungeonHandler.audioManager.startAreaMusic('catacombs');
            console.log('🎵 Dungeon music started!');
        } else {
            console.error('❌ Music system initialization failed');
        }
    }).catch(error => {
        console.error('❌ Error initializing music system:', error);
    });
};

/**
 * Debug dungeon state
 */
window.debugDungeonState = function() {
    console.log('🔍 Debugging dungeon state...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    console.log('=== DUNGEON DEBUG INFO ===');
    console.log('🏠 Current Room ID:', dungeonHandler.currentRoomId);
    console.log('🎮 Player Controller:', !!dungeonHandler.playerController);
    console.log('🎮 Controls Enabled:', dungeonHandler.playerController?.enabled);
    console.log('🎵 Audio Manager:', !!dungeonHandler.audioManager);
    console.log('🎵 Waiting for Entry Dialogue:', dungeonHandler.waitingForEntryDialogue);
    console.log('🎵 Delayed Music Init:', dungeonHandler.delayedMusicInit);
    console.log('📷 Camera Mode:', dungeonHandler.cameraMode);
    console.log('🚪 Door Triggers:', dungeonHandler.doorTriggers.length);
    console.log('👹 Active Enemies:', dungeonHandler.activeEnemies.length);
    console.log('🔗 Debug Flags:');
    console.log('  - debugSkipDungeonDialogue:', window.debugSkipDungeonDialogue);
    console.log('  - debugStartMusicImmediately:', window.debugStartMusicImmediately);
    console.log('=========================');
};

/**
 * Debug lighting state
 */
window.debugLightingState = function() {
    console.log('💡 Debugging lighting state...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    const BASE_AMBIENT_INTENSITY = 1.2; // Same value as in DungeonHandler.js
    
    console.log('=== LIGHTING DEBUG INFO ===');
    console.log('🏠 Current Room ID:', dungeonHandler.currentRoomId);
    console.log('💡 Ambient Light Intensity:', dungeonHandler.ambientLight?.intensity || 'No ambient light');
    console.log('💡 Base Ambient Intensity (Expected):', BASE_AMBIENT_INTENSITY);
    console.log('🌑 Is Room 0 Dark Atmosphere:', dungeonHandler.isRoom0DarkAtmosphere);
    console.log('🚫 Debug Skip Dialogue:', !!window.debugSkipDungeonDialogue);
    console.log('⏳ Waiting for Entry Dialogue:', dungeonHandler.waitingForEntryDialogue);
    
    // Count all lights in scene
    let lightCount = 0;
    if (dungeonHandler.scene) {
        dungeonHandler.scene.traverse((obj) => {
            if (obj.isLight) {
                lightCount++;
                console.log(`💡 Light: ${obj.name || 'unnamed'} (${obj.type}), intensity: ${obj.intensity}`);
            }
        });
    }
    
    console.log('💡 Total lights in scene:', lightCount);
    console.log('============================');
};

/**
 * Toggle volumetric fog visibility for testing
 */
window.toggleVolumetricFog = function() {
    console.log('🌫️ Toggling volumetric fog visibility...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    if (!dungeonHandler.currentRoomGroup) {
        console.error('❌ No current room group');
        return;
    }
    
    console.log('=== VOLUMETRIC FOG DEBUG INFO ===');
    
    // Check for volumetric fog debug mesh
    let fogDebugMesh = null;
    
    dungeonHandler.currentRoomGroup.traverse(child => {
        if (child.name === 'VolumetricFogDebug') {
            fogDebugMesh = child;
            console.log('🌫️ Found volumetric fog debug mesh:', child.visible ? 'VISIBLE' : 'HIDDEN');
        }
    });
    
    // Toggle debug mesh visibility
    if (fogDebugMesh) {
        fogDebugMesh.visible = !fogDebugMesh.visible;
        console.log(`🌫️ Volumetric fog debug mesh: ${fogDebugMesh.visible ? 'SHOWN' : 'HIDDEN'}`);
    } else {
        console.warn('❌ No volumetric fog debug mesh found');
    }
    
    // Check for event room handler
    const eventRoomManager = dungeonHandler.eventRoomManager;
    if (eventRoomManager) {
        const currentRoomId = dungeonHandler.currentRoomId;
        const roomHandler = eventRoomManager.roomHandlers.get(currentRoomId);
        
        if (roomHandler && roomHandler.constructor.name === 'MysteriousPondRoom') {
            console.log('✅ In mysterious pond room');
            if (roomHandler.fogVolume) {
                console.log('🌫️ Fog volume exists:', roomHandler.fogVolume);
                console.log('🌫️ Fog pass exists:', !!roomHandler.fogPass);
            } else {
                console.warn('❌ No fog volume in pond room handler');
            }
        } else {
            console.warn('❌ Not in mysterious pond room');
        }
    }
    
    console.log('===============================');
};

/**
 * Debug music system state and initialization
 */
window.debugMusicSystem = function() {
    console.log('🎵 Debugging music system...');
    
    const dungeonHandler = window.dungeonHandler;
    if (!dungeonHandler) {
        console.error('❌ DungeonHandler not available');
        return;
    }
    
    if (!dungeonHandler.audioManager) {
        console.error('❌ AudioManager not available');
        return;
    }
    
    const audioManager = dungeonHandler.audioManager;
    
    console.log('=== MUSIC SYSTEM DEBUG INFO ===');
    console.log('🎵 Music Conductor Available:', !!audioManager.musicConductor);
    console.log('🎵 Music Conductor Initialized:', audioManager.musicConductor?.initialized || false);
    console.log('🎵 Current Area Music:', audioManager.currentAreaMusic || 'None');
    console.log('⏳ Waiting for Entry Dialogue:', dungeonHandler.waitingForEntryDialogue);
    console.log('⏰ Delayed Music Init:', dungeonHandler.delayedMusicInit);
    console.log('🚫 Debug Skip Dialogue:', !!window.debugSkipDungeonDialogue);
    console.log('🚀 Debug Start Music Immediately:', !!window.debugStartMusicImmediately);
    console.log('📂 Dungeon Entry Dialogue Exists:', !!window.dungeonEntryDialogue);
    
    // Test if music system can initialize
    console.log('🧪 Testing music system initialization...');
    audioManager.initMusicSystem().then(success => {
        console.log('✅ Music system init test result:', success);
        if (success) {
            console.log('🎵 Attempting to start catacombs music...');
            audioManager.startAreaMusic('catacombs').then(musicSuccess => {
                console.log('🎵 Catacombs music start result:', musicSuccess);
            }).catch(error => {
                console.error('❌ Error starting catacombs music:', error);
            });
        }
    }).catch(error => {
        console.error('❌ Error testing music system init:', error);
    });
    
    console.log('================================');
};

// Make commands available globally
window.spawnNairabos = spawnNairabos;
window.spawnEnemy = spawnEnemy;
window.clearEnemies = clearEnemies;
window.emergencyReset = emergencyReset;
window.fixNairabosHeight = fixNairabosHeight;
window.testNairabosPositioning = testNairabosPositioning;
window.testEventRoomWalls = testEventRoomWalls;

console.log("🔥 Spawn debug commands loaded. Available commands:");
console.log("- spawnNairabos() - Spawn a nairabos enemy for positioning test");
console.log("- spawnEnemy(type) - Spawn any enemy type (e.g., spawnEnemy('zombie'))");
console.log("- clearEnemies() - Clear all enemies in the current room");
console.log("- emergencyReset() - EMERGENCY: Force clear everything if game is stuck");
console.log("- fixNairabosHeight(offset) - Manually adjust nairabos height (default: +5.0)");
console.log("- testNairabosPositioning() - Comprehensive positioning and scaling test");
console.log("- testEventRoomWalls() - Guide for testing event room wall rendering");
console.log("- testPondChest() - Test chest spawning in pond room");
console.log("- testPondEnemyDefeat() - Force trigger pond room enemy defeat logic");
console.log("- testFastDungeonEntry() - Test the fast dungeon entry debug mode");
console.log("- testPlayerMovement() - Test player movement and controls");
console.log("- forceStartDungeonMusic() - Force start dungeon music");
console.log("- debugDungeonState() - Debug dungeon state");
console.log("- debugLightingState() - Debug lighting system");
console.log("- debugMusicSystem() - Debug music system initialization");
console.log("- toggleVolumetricFog() - Toggle volumetric fog visibility in pond room");
