// src/config/workerConfig.js
// Configuration for worker performance optimization

/**
 * Worker performance configuration
 * Adjust these values based on your hardware and performance requirements
 */
export const WORKER_CONFIG = {
    // Global worker settings
    maxWorkersPerType: 4,           // Maximum workers per worker type
    preWarmPools: true,             // Pre-create workers for faster response
    maxConcurrentRequests: 8,       // Maximum concurrent requests across all workers
    workerIdleTimeout: 300000,      // 5 minutes - terminate idle workers
    
    // Memory management
    memoryCleanupInterval: 60000,   // 1 minute - check for cleanup
    maxMemoryPerWorker: 100 * 1024 * 1024, // 100MB per worker
    
    // Performance monitoring
    enablePerformanceMetrics: true,
    metricsRetentionTime: 3600000,  // 1 hour
    
    // Worker-specific configurations
    workerTypes: {
        dungeon: {
            maxWorkers: 2,          // Dungeon generation is CPU intensive
            preWarm: 1,             // Keep 1 ready
            priority: 'normal',
            timeout: 30000,         // 30 seconds
            memoryLimit: 150 * 1024 * 1024 // 150MB
        },
        
        destruction: {
            maxWorkers: 4,          // Physics calculations can be parallelized
            preWarm: 2,             // Keep 2 ready for responsive destruction
            priority: 'high',
            timeout: 10000,         // 10 seconds
            memoryLimit: 50 * 1024 * 1024  // 50MB
        },
        
        ai: {
            maxWorkers: 1,          // AI inference is memory intensive
            preWarm: 0,             // Don't pre-warm due to memory usage
            priority: 'low',
            timeout: 60000,         // 60 seconds
            memoryLimit: 500 * 1024 * 1024 // 500MB
        },

        meshProcessing: {
            maxWorkers: 3,          // Mesh operations can be parallelized
            preWarm: 1,             // Keep 1 ready for responsive mesh operations
            priority: 'high',
            timeout: 15000,         // 15 seconds
            memoryLimit: 200 * 1024 * 1024 // 200MB
        },

        pathfinding: {
            maxWorkers: 2,          // Pathfinding can be CPU intensive
            preWarm: 1,             // Keep 1 ready
            priority: 'normal',
            timeout: 5000,          // 5 seconds
            memoryLimit: 50 * 1024 * 1024  // 50MB
        },

        animation: {
            maxWorkers: 2,          // Animation calculations
            preWarm: 1,             // Keep 1 ready for smooth animations
            priority: 'high',
            timeout: 1000,          // 1 second - animations need to be fast
            memoryLimit: 30 * 1024 * 1024  // 30MB
        },

        bulletPattern: {
            maxWorkers: 2,          // Bullet pattern generation
            preWarm: 1,             // Keep 1 ready for responsive gameplay
            priority: 'high',
            timeout: 2000,          // 2 seconds
            memoryLimit: 40 * 1024 * 1024  // 40MB
        }
    }
};

/**
 * Get optimized configuration based on hardware capabilities
 * @returns {Object} Optimized worker configuration
 */
export function getOptimizedWorkerConfig() {
    const config = { ...WORKER_CONFIG };
    
    // Detect hardware capabilities
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const memoryGB = navigator.deviceMemory || 4; // Fallback to 4GB
    
    // Adjust based on CPU cores
    if (hardwareConcurrency >= 8) {
        // High-end device
        config.maxWorkersPerType = 6;
        config.maxConcurrentRequests = 12;
        config.workerTypes.dungeon.maxWorkers = 3;
        config.workerTypes.destruction.maxWorkers = 6;
        config.workerTypes.meshProcessing.maxWorkers = 4;
        config.workerTypes.pathfinding.maxWorkers = 3;
        config.workerTypes.animation.maxWorkers = 3;
        config.workerTypes.bulletPattern.maxWorkers = 3;
    } else if (hardwareConcurrency <= 2) {
        // Low-end device
        config.maxWorkersPerType = 2;
        config.maxConcurrentRequests = 4;
        config.workerTypes.dungeon.maxWorkers = 1;
        config.workerTypes.destruction.maxWorkers = 2;
        config.workerTypes.meshProcessing.maxWorkers = 1;
        config.workerTypes.pathfinding.maxWorkers = 1;
        config.workerTypes.animation.maxWorkers = 1;
        config.workerTypes.bulletPattern.maxWorkers = 1;
        config.preWarmPools = false; // Don't pre-warm on low-end devices
    }
    
    // Adjust based on available memory
    if (memoryGB >= 8) {
        // High memory device
        config.workerTypes.ai.preWarm = 1;
        config.workerTypes.ai.memoryLimit = 1024 * 1024 * 1024; // 1GB
    } else if (memoryGB <= 2) {
        // Low memory device
        config.workerTypes.ai.maxWorkers = 0; // Disable AI workers
        config.workerTypes.dungeon.memoryLimit = 100 * 1024 * 1024; // 100MB
        config.workerTypes.destruction.memoryLimit = 30 * 1024 * 1024; // 30MB
    }
    
    console.log(`[WorkerConfig] Optimized for ${hardwareConcurrency} cores, ${memoryGB}GB RAM`, config);
    return config;
}

/**
 * Performance monitoring thresholds
 */
export const PERFORMANCE_THRESHOLDS = {
    // Response time thresholds (ms)
    responseTime: {
        good: 100,
        acceptable: 500,
        poor: 1000
    },
    
    // Memory usage thresholds (bytes)
    memoryUsage: {
        warning: 80 * 1024 * 1024,  // 80MB
        critical: 150 * 1024 * 1024 // 150MB
    },
    
    // Error rate thresholds (percentage)
    errorRate: {
        warning: 0.05,  // 5%
        critical: 0.15  // 15%
    },
    
    // Queue length thresholds
    queueLength: {
        warning: 5,
        critical: 10
    }
};

/**
 * Get worker configuration for specific type
 * @param {string} workerType - Type of worker
 * @returns {Object} Worker-specific configuration
 */
export function getWorkerTypeConfig(workerType) {
    const baseConfig = getOptimizedWorkerConfig();
    const typeConfig = baseConfig.workerTypes[workerType];
    
    if (!typeConfig) {
        console.warn(`[WorkerConfig] Unknown worker type: ${workerType}, using defaults`);
        return {
            maxWorkers: baseConfig.maxWorkersPerType,
            preWarm: 1,
            priority: 'normal',
            timeout: 30000,
            memoryLimit: baseConfig.maxMemoryPerWorker
        };
    }
    
    return {
        ...typeConfig,
        // Merge with global settings
        idleTimeout: baseConfig.workerIdleTimeout,
        enableMetrics: baseConfig.enablePerformanceMetrics
    };
}
