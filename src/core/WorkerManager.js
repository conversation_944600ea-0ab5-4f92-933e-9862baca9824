// src/core/WorkerManager.js
// Centralized web worker management and coordination

export class WorkerManager {
    constructor(config = {}) {
        this.workers = new Map();
        this.activeRequests = new Map();
        this.requestCounter = 0;
        this.performanceMetrics = new Map();
        this.errorHandlers = new Map();
        this.debugMode = false;

        // Worker pool configuration
        this.poolConfig = {
            maxWorkersPerType: config.maxWorkersPerType || 4,
            preWarmPools: config.preWarmPools || true,
            maxConcurrentRequests: config.maxConcurrentRequests || 8,
            workerIdleTimeout: config.workerIdleTimeout || 300000, // 5 minutes
            ...config
        };

        // Worker pools by type
        this.workerPools = new Map();
        this.requestQueue = new Map(); // Queue requests when all workers busy

        this._log('WorkerManager initialized with config:', this.poolConfig);

        // Start cleanup interval
        this.startCleanupInterval();
    }

    _log(message, ...args) {
        if (this.debugMode) {
            console.log(`[WorkerManager] ${message}`, ...args);
        }
    }

    /**
     * Create and register a new worker
     * @param {string} type - Worker type identifier
     * @param {string} scriptPath - Path to worker script
     * @param {Object} options - Worker configuration options
     * @returns {string} - Worker ID
     */
    createWorker(type, scriptPath, options = {}) {
        try {
            const worker = new Worker(scriptPath);
            const workerId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            const workerData = {
                worker,
                type,
                scriptPath,
                busy: false,
                lastUsed: Date.now(),
                requestCount: 0,
                createdAt: Date.now(),
                options: { ...options }
            };

            this.workers.set(workerId, workerData);
            this.performanceMetrics.set(workerId, {
                totalRequests: 0,
                totalTime: 0,
                averageTime: 0,
                errors: 0,
                lastError: null
            });

            this.setupWorkerHandlers(workerId);
            this._log(`Created worker: ${workerId} (${type})`);

            return workerId;

        } catch (error) {
            console.error(`Failed to create worker: ${type}`, error);
            throw new Error(`Worker creation failed: ${error.message}`);
        }
    }

    /**
     * Setup event handlers for a worker
     * @param {string} workerId - Worker ID
     */
    setupWorkerHandlers(workerId) {
        const workerData = this.workers.get(workerId);
        if (!workerData) return;

        const { worker } = workerData;

        worker.onmessage = (event) => {
            this.handleWorkerMessage(workerId, event);
        };

        worker.onerror = (error) => {
            this.handleWorkerError(workerId, error);
        };

        worker.onmessageerror = (error) => {
            this.handleWorkerMessageError(workerId, error);
        };
    }

    /**
     * Handle incoming messages from workers
     * @param {string} workerId - Worker ID
     * @param {MessageEvent} event - Message event
     */
    handleWorkerMessage(workerId, event) {
        const { requestId, success, result, error, metadata } = event.data;
        
        if (!requestId) {
            this._log(`Received message without requestId from worker ${workerId}`);
            return;
        }

        const requestData = this.activeRequests.get(requestId);
        if (!requestData) {
            this._log(`Received message for unknown request: ${requestId}`);
            return;
        }

        const { resolve, reject, startTime, workerId: requestWorkerId } = requestData;
        
        // Update worker status
        const workerData = this.workers.get(workerId);
        if (workerData) {
            workerData.busy = false;
            workerData.lastUsed = Date.now();
            workerData.requestCount++;
        }

        // Update performance metrics
        const totalTime = Date.now() - startTime;
        this.updatePerformanceMetrics(workerId, totalTime, !success);

        // Clean up request tracking
        this.activeRequests.delete(requestId);

        // Return worker to pool when request completes
        this.returnWorkerToPool(workerId);

        if (success) {
            this._log(`Request completed: ${requestId} (${totalTime}ms)`);
            resolve({
                result,
                metadata: {
                    ...metadata,
                    workerId,
                    totalTime
                }
            });
        } else {
            this._log(`Request failed: ${requestId} - ${error?.message}`);
            const errorObj = new Error(error?.message || 'Worker request failed');
            errorObj.workerError = error;
            errorObj.workerId = workerId;
            reject(errorObj);
        }
    }

    /**
     * Handle worker errors
     * @param {string} workerId - Worker ID
     * @param {ErrorEvent} error - Error event
     */
    handleWorkerError(workerId, error) {
        console.error(`Worker error (${workerId}):`, error);
        
        const workerData = this.workers.get(workerId);
        if (workerData) {
            workerData.busy = false;
        }

        // Update error metrics
        const metrics = this.performanceMetrics.get(workerId);
        if (metrics) {
            metrics.errors++;
            metrics.lastError = {
                message: error.message,
                filename: error.filename,
                lineno: error.lineno,
                timestamp: Date.now()
            };
        }

        // Call error handler if registered
        const errorHandler = this.errorHandlers.get(workerId);
        if (errorHandler) {
            errorHandler(error, workerId);
        }

        // Restart worker if needed
        if (this.shouldRestartWorker(workerId)) {
            this.restartWorker(workerId);
        }
    }

    /**
     * Handle worker message errors
     * @param {string} workerId - Worker ID
     * @param {MessageEvent} error - Message error event
     */
    handleWorkerMessageError(workerId, error) {
        console.error(`Worker message error (${workerId}):`, error);
        this.handleWorkerError(workerId, error);
    }

    /**
     * Send a message to a worker
     * @param {string} workerId - Worker ID
     * @param {Object} message - Message to send
     * @param {Array} transferable - Transferable objects
     * @param {number} timeout - Request timeout in ms
     * @returns {Promise} - Promise that resolves with worker response
     */
    async sendMessage(workerId, message, transferable = [], timeout = 30000) {
        const workerData = this.workers.get(workerId);
        if (!workerData) {
            throw new Error(`Worker not found: ${workerId}`);
        }

        const { worker } = workerData;
        const requestId = `req_${++this.requestCounter}_${Date.now()}`;
        const startTime = Date.now();

        return new Promise((resolve, reject) => {
            // Set up timeout
            const timeoutId = setTimeout(() => {
                this.activeRequests.delete(requestId);
                workerData.busy = false;
                reject(new Error(`Worker request timeout: ${requestId} (${timeout}ms)`));
            }, timeout);

            // Store request data
            this.activeRequests.set(requestId, {
                resolve: (result) => {
                    clearTimeout(timeoutId);
                    resolve(result);
                },
                reject: (error) => {
                    clearTimeout(timeoutId);
                    reject(error);
                },
                startTime,
                workerId,
                timeoutId
            });

            // Mark worker as busy
            workerData.busy = true;

            // Send message
            try {
                worker.postMessage({
                    ...message,
                    requestId
                }, transferable);

                this._log(`Sent message to worker ${workerId}: ${message.type}`);
            } catch (error) {
                clearTimeout(timeoutId);
                this.activeRequests.delete(requestId);
                workerData.busy = false;
                reject(new Error(`Failed to send message: ${error.message}`));
            }
        });
    }

    /**
     * Get an available worker of specified type
     * @param {string} type - Worker type
     * @returns {string|null} - Worker ID or null if none available
     */
    getAvailableWorker(type) {
        const availableWorkers = Array.from(this.workers.entries())
            .filter(([id, data]) => data.type === type && !data.busy)
            .sort(([, a], [, b]) => a.requestCount - b.requestCount);

        return availableWorkers.length > 0 ? availableWorkers[0][0] : null;
    }

    /**
     * Initialize worker pool for a specific type
     * @param {string} type - Worker type
     * @param {string} scriptPath - Path to worker script
     * @param {Object} options - Worker options
     * @param {number} poolSize - Number of workers to pre-create
     */
    async initializeWorkerPool(type, scriptPath, options = {}, poolSize = null) {
        poolSize = poolSize || this.poolConfig.maxWorkersPerType;

        if (!this.workerPools.has(type)) {
            this.workerPools.set(type, {
                scriptPath,
                options,
                maxSize: poolSize,
                available: [],
                busy: [],
                requestQueue: []
            });
        }

        const pool = this.workerPools.get(type);

        // Pre-warm the pool if enabled
        if (this.poolConfig.preWarmPools) {
            const preWarmCount = Math.min(2, poolSize); // Pre-warm 2 workers or pool size
            for (let i = 0; i < preWarmCount; i++) {
                const workerId = this.createWorker(type, scriptPath, options);
                pool.available.push(workerId);
            }
            this._log(`Pre-warmed ${preWarmCount} workers for type: ${type}`);
        }
    }

    /**
     * Get or create a worker for the specified type with improved pooling
     * @param {string} type - Worker type
     * @param {string} scriptPath - Path to worker script (for creation)
     * @param {Object} options - Worker options (for creation)
     * @returns {Promise<string>} - Worker ID
     */
    async getOrCreateWorker(type, scriptPath, options = {}) {
        // Initialize pool if it doesn't exist
        if (!this.workerPools.has(type)) {
            await this.initializeWorkerPool(type, scriptPath, options);
        }

        const pool = this.workerPools.get(type);

        // Try to get available worker from pool
        if (pool.available.length > 0) {
            const workerId = pool.available.pop();
            pool.busy.push(workerId);
            return workerId;
        }

        // Create new worker if under pool limit
        if (pool.busy.length < pool.maxSize) {
            const workerId = this.createWorker(type, scriptPath, options);
            pool.busy.push(workerId);
            return workerId;
        }

        // All workers busy - queue the request or return null for immediate handling
        this._log(`All ${type} workers busy (${pool.busy.length}/${pool.maxSize})`);
        return null;
    }

    /**
     * Return a worker to the available pool
     * @param {string} workerId - Worker ID to return
     */
    returnWorkerToPool(workerId) {
        const workerData = this.workers.get(workerId);
        if (!workerData) return;

        const pool = this.workerPools.get(workerData.type);
        if (!pool) return;

        // Move from busy to available
        const busyIndex = pool.busy.indexOf(workerId);
        if (busyIndex !== -1) {
            pool.busy.splice(busyIndex, 1);
            pool.available.push(workerId);

            // Update worker state
            workerData.busy = false;
            workerData.lastUsed = Date.now();

            this._log(`Worker ${workerId} returned to ${workerData.type} pool`);
        }
    }

    /**
     * Update performance metrics for a worker
     * @param {string} workerId - Worker ID
     * @param {number} processingTime - Processing time in ms
     * @param {boolean} isError - Whether the request resulted in an error
     */
    updatePerformanceMetrics(workerId, processingTime, isError = false) {
        const metrics = this.performanceMetrics.get(workerId);
        if (!metrics) return;

        metrics.totalRequests++;
        metrics.totalTime += processingTime;
        metrics.averageTime = metrics.totalTime / metrics.totalRequests;

        if (isError) {
            metrics.errors++;
        }
    }

    /**
     * Check if a worker should be restarted
     * @param {string} workerId - Worker ID
     * @returns {boolean} - Whether to restart the worker
     */
    shouldRestartWorker(workerId) {
        const metrics = this.performanceMetrics.get(workerId);
        if (!metrics) return false;

        // Restart if error rate is high
        const errorRate = metrics.errors / Math.max(metrics.totalRequests, 1);
        return errorRate > 0.1 && metrics.totalRequests > 10;
    }

    /**
     * Restart a worker
     * @param {string} workerId - Worker ID
     */
    async restartWorker(workerId) {
        const workerData = this.workers.get(workerId);
        if (!workerData) return;

        this._log(`Restarting worker: ${workerId}`);

        // Terminate old worker
        workerData.worker.terminate();

        // Create new worker with same configuration
        const newWorkerId = this.createWorker(
            workerData.type,
            workerData.scriptPath,
            workerData.options
        );

        // Clean up old worker data
        this.workers.delete(workerId);
        this.performanceMetrics.delete(workerId);
        this.errorHandlers.delete(workerId);

        return newWorkerId;
    }

    /**
     * Register an error handler for a worker
     * @param {string} workerId - Worker ID
     * @param {Function} handler - Error handler function
     */
    setErrorHandler(workerId, handler) {
        this.errorHandlers.set(workerId, handler);
    }

    /**
     * Get performance metrics for all workers
     * @returns {Object} - Performance metrics
     */
    getAllPerformanceMetrics() {
        const metrics = {};
        
        this.performanceMetrics.forEach((workerMetrics, workerId) => {
            const workerData = this.workers.get(workerId);
            metrics[workerId] = {
                ...workerMetrics,
                type: workerData?.type,
                busy: workerData?.busy,
                createdAt: workerData?.createdAt,
                lastUsed: workerData?.lastUsed
            };
        });

        return {
            workers: metrics,
            totalWorkers: this.workers.size,
            activeRequests: this.activeRequests.size,
            timestamp: Date.now()
        };
    }

    /**
     * Terminate a specific worker
     * @param {string} workerId - Worker ID
     */
    terminateWorker(workerId) {
        const workerData = this.workers.get(workerId);
        if (!workerData) return;

        this._log(`Terminating worker: ${workerId}`);

        workerData.worker.terminate();
        this.workers.delete(workerId);
        this.performanceMetrics.delete(workerId);
        this.errorHandlers.delete(workerId);
    }

    /**
     * Terminate all workers
     */
    terminateAllWorkers() {
        this._log('Terminating all workers');

        this.workers.forEach((workerData, workerId) => {
            workerData.worker.terminate();
        });

        this.workers.clear();
        this.performanceMetrics.clear();
        this.errorHandlers.clear();
        this.activeRequests.clear();
        this.workerPools.clear();

        // Clear cleanup interval
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }

    /**
     * Start cleanup interval for idle workers
     */
    startCleanupInterval() {
        this.cleanupInterval = setInterval(() => {
            this.cleanupIdleWorkers();
        }, 60000); // Check every minute
    }

    /**
     * Clean up idle workers to free memory
     */
    cleanupIdleWorkers() {
        const now = Date.now();
        const idleTimeout = this.poolConfig.workerIdleTimeout;

        this.workerPools.forEach((pool, type) => {
            const idleWorkers = [];

            // Find idle workers in available pool
            pool.available.forEach(workerId => {
                const workerData = this.workers.get(workerId);
                if (workerData && (now - workerData.lastUsed) > idleTimeout) {
                    idleWorkers.push(workerId);
                }
            });

            // Keep at least 1 worker per type, terminate others
            const keepCount = 1;
            const toTerminate = idleWorkers.slice(keepCount);

            toTerminate.forEach(workerId => {
                this._log(`Terminating idle worker: ${workerId}`);
                this.terminateWorker(workerId);

                // Remove from pool
                const availableIndex = pool.available.indexOf(workerId);
                if (availableIndex !== -1) {
                    pool.available.splice(availableIndex, 1);
                }
            });

            if (toTerminate.length > 0) {
                this._log(`Cleaned up ${toTerminate.length} idle ${type} workers`);
            }
        });
    }

    /**
     * Get worker pool statistics
     * @returns {Object} - Pool statistics
     */
    getPoolStatistics() {
        const stats = {};

        this.workerPools.forEach((pool, type) => {
            stats[type] = {
                available: pool.available.length,
                busy: pool.busy.length,
                maxSize: pool.maxSize,
                queuedRequests: pool.requestQueue.length,
                utilization: pool.busy.length / pool.maxSize
            };
        });

        return {
            pools: stats,
            totalWorkers: this.workers.size,
            activeRequests: this.activeRequests.size,
            timestamp: Date.now()
        };
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether to enable debug mode
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        this._log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    // ===== DESTRUCTION WORKER SPECIFIC METHODS =====

    /**
     * Initialize the destruction worker for debris processing
     * @returns {Promise<string>} - Worker ID
     */
    async initializeDestructionWorker() {
        try {
            const workerId = this.getOrCreateWorker(
                'destruction', 
                './src/workers/destructionWorker.js',
                { priority: 'high' }
            );
            
            console.log(`[WorkerManager] Destruction worker initialized: ${workerId}`);
            return workerId;
        } catch (error) {
            console.error('[WorkerManager] Failed to initialize destruction worker:', error);
            throw error;
        }
    }

    /**
     * Process enemy destruction in worker thread
     * @param {Object} enemyData - Enemy data including body parts and position
     * @param {string} enemyType - Type of enemy being destroyed
     * @param {THREE.Vector3} projectileVelocity - Velocity of projectile that killed enemy
     * @param {number} groundLevel - Ground level for physics calculations
     * @param {Array} collisionObjects - Objects that debris can collide with
     * @param {number} creationTime - Time when destruction occurred
     * @returns {Promise<Object>} - Promise that resolves with debris data
     */
    async processEnemyDestruction(enemyData, enemyType, projectileVelocity, groundLevel, collisionObjects, creationTime) {
        const workerId = this.getAvailableWorker('destruction');
        if (!workerId) {
            throw new Error('No destruction worker available');
        }

        // Serialize data for worker (remove Three.js objects that can't be transferred)
        const serializedData = {
            enemyData: this._serializeEnemyData(enemyData),
            enemyType,
            projectileVelocity: projectileVelocity ? {
                x: projectileVelocity.x,
                y: projectileVelocity.y,
                z: projectileVelocity.z
            } : null,
            groundLevel,
            collisionObjects: this._serializeCollisionObjects(collisionObjects),
            creationTime
        };

        const response = await this.sendMessage(workerId, {
            type: 'processEnemyDestruction',
            data: serializedData
        });

        return response.result;
    }

    /**
     * Process object destruction in worker thread
     * @param {Object} objectData - Object data including voxels and materials
     * @param {string} objectType - Type of object being destroyed
     * @param {THREE.Vector3} pointOfImpact - Point where object was hit
     * @param {THREE.Vector3} projectileVelocity - Velocity of projectile that hit object
     * @param {Array} collisionObjects - Objects that debris can collide with
     * @returns {Promise<Object>} - Promise that resolves with debris data
     */
    async processObjectDestruction(objectData, objectType, pointOfImpact, projectileVelocity, collisionObjects) {
        const workerId = this.getAvailableWorker('destruction');
        if (!workerId) {
            throw new Error('No destruction worker available');
        }

        // Serialize data for worker
        const serializedData = {
            objectData: this._serializeObjectData(objectData),
            objectType,
            pointOfImpact: pointOfImpact ? {
                x: pointOfImpact.x,
                y: pointOfImpact.y,
                z: pointOfImpact.z
            } : null,
            projectileVelocity: projectileVelocity ? {
                x: projectileVelocity.x,
                y: projectileVelocity.y,
                z: projectileVelocity.z
            } : null,
            collisionObjects: this._serializeCollisionObjects(collisionObjects)
        };

        const response = await this.sendMessage(workerId, {
            type: 'processObjectDestruction',
            data: serializedData
        });

        return response.result;
    }

    /**
     * Check if destruction worker is available
     * @returns {boolean}
     */
    isDestructionWorkerReady() {
        return this.getAvailableWorker('destruction') !== null;
    }

    // ===== SERIALIZATION HELPER METHODS =====

    /**
     * Serialize enemy data for worker transfer
     */
    _serializeEnemyData(enemyData) {
        const serialized = {
            id: enemyData.id,
            bodyPartGroups: []
        };

        if (enemyData.bodyPartGroups) {
            serialized.bodyPartGroups = enemyData.bodyPartGroups.map(group => ({
                name: group.name,
                worldPosition: {
                    x: group.worldPosition.x,
                    y: group.worldPosition.y,
                    z: group.worldPosition.z
                },
                material: this._serializeMaterial(group.material),
                meshes: group.meshes.map(mesh => ({
                    worldPosition: {
                        x: mesh.worldPosition.x,
                        y: mesh.worldPosition.y,
                        z: mesh.worldPosition.z
                    },
                    worldQuaternion: {
                        x: mesh.worldQuaternion.x,
                        y: mesh.worldQuaternion.y,
                        z: mesh.worldQuaternion.z,
                        w: mesh.worldQuaternion.w
                    },
                    material: this._serializeMaterial(mesh.material)
                }))
            }));
        }

        return serialized;
    }

    /**
     * Serialize object data for worker transfer
     */
    _serializeObjectData(objectData) {
        const serialized = {
            id: objectData.id,
            name: objectData.name,
            position: {
                x: objectData.position.x,
                y: objectData.position.y,
                z: objectData.position.z
            },
            material: this._serializeMaterial(objectData.material)
        };

        // Include voxel data if available
        if (objectData.originalVoxels) {
            serialized.originalVoxels = objectData.originalVoxels.map(voxel => ({
                worldPosition: {
                    x: voxel.worldPosition.x,
                    y: voxel.worldPosition.y,
                    z: voxel.worldPosition.z
                },
                material: this._serializeMaterial(voxel.material)
            }));
            serialized.voxelScale = objectData.voxelScale;
        }

        return serialized;
    }

    /**
     * Serialize collision objects for worker transfer
     */
    _serializeCollisionObjects(collisionObjects) {
        if (!Array.isArray(collisionObjects)) return [];
        
        return collisionObjects.map(obj => ({
            box: obj.box ? {
                min: { x: obj.box.min.x, y: obj.box.min.y, z: obj.box.min.z },
                max: { x: obj.box.max.x, y: obj.box.max.y, z: obj.box.max.z }
            } : null,
            center: obj.center ? {
                x: obj.center.x,
                y: obj.center.y,
                z: obj.center.z
            } : null,
            size: obj.size ? {
                x: obj.size.x,
                y: obj.size.y,
                z: obj.size.z
            } : null
        }));
    }

    /**
     * Serialize material data for worker transfer
     */
    _serializeMaterial(material) {
        if (!material) return null;
        
        if (Array.isArray(material)) {
            return material.map(mat => this._serializeMaterial(mat));
        }

        return {
            color: material.color ? material.color.getHex() : 0x888888,
            type: material.type || 'MeshLambertMaterial',
            transparent: material.transparent || false,
            opacity: material.opacity !== undefined ? material.opacity : 1.0
        };
    }
}