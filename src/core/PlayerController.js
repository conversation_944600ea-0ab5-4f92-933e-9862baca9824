import * as THREE from 'three';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js'; // Import VOXEL_SIZE
import { STATE } from '../constants.js'; // <<< Import STATE
import SoulOrbHealthBar from '../ui/SoulOrbHealthBar.js';

// Re-import player constants if needed (or define them here)
const PLAYER_PIXEL_SCALE_XY = 3;
const PLAYER_PIXEL_SCALE_Z = 3;
const PLAYER_VOXEL_WIDTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
const PLAYER_VOXEL_HEIGHT = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
const PLAYER_VOXEL_DEPTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_Z;

// Remove TRAIL_LENGTH constant - will use currentHealth instead
// const TRAIL_LENGTH = 8;
const BASE_MAX_TRAIL_LENGTH = 5;    // Trail length when player is at 100% health
const TRAIL_SPAWN_INTERVAL = 0.12;
const BASE_TRAIL_FADE_DURATION = 0.6; // Renamed: Fade duration at 100% health
const MIN_TRAIL_FADE_DURATION = 0.1; // Minimum fade duration
const TRAIL_LIGHT_INTENSITY = 0.4;
const TRAIL_LIGHT_RANGE = 1.0;
const TRAIL_LIGHT_COLOR = 0x88ddff;

// --- Axes Helper Constants ---
const AXES_HELPER_SIZE = 2.0; // <<< INCREASED SIZE SIGNIFICANTLY for debugging (larger than enemy)
// ---------------------------

// --- NippleJS Constants (Keep for handleMobileInput) ---
const JOYSTICK_THRESHOLD = 0.3; // How far joystick needs to move to register as input
// const JOYSTICK_TAP_THRESHOLD = 0.1; // Tap detection moved to SceneManager
// const JOYSTICK_VERTICAL_THRESHOLD = 0.5; // Dialogue nav moved to SceneManager

const PROJECTILE_GRAVITY = -4.9; // <<< Reduced gravity further

class PlayerController {
    constructor(playerMesh, camera, scene, clock, dungeonHandler, collisionObjects = [], floorBounds = { minX: -Infinity, maxX: Infinity, minZ: -Infinity, maxZ: Infinity }, weaponSystem = null) {
        this.playerMesh = playerMesh;
        this.camera = camera; // Keep for potential future use (e.g., camera relative movement)
        this.scene = scene; // Store scene reference
        this.clock = clock; // Store clock reference
        this.dungeonHandler = dungeonHandler; // <<< Store DungeonHandler reference
        this.weaponSystem = weaponSystem; // <<< Store WeaponSystem reference
        this.collisionObjects = collisionObjects;
        this.floorBounds = floorBounds;

        // Find limb subgroups
        this.body = this.playerMesh.getObjectByName('Body'); // Assuming a main 'Body' group exists
        this.head = this.playerMesh.getObjectByName('Head'); // Assuming 'Head' exists
        this.leftLeg = this.playerMesh.getObjectByName('leftLeg');
        this.rightLeg = this.playerMesh.getObjectByName('rightLeg');
        this.leftArm = this.playerMesh.getObjectByName('leftArm');
        this.rightArm = this.playerMesh.getObjectByName('rightArm');
        this.leftFoot = this.playerMesh.getObjectByName('leftFoot'); // <<< Add feet if they exist
        this.rightFoot = this.playerMesh.getObjectByName('rightFoot'); // <<< Add feet if they exist

        this.moveSpeed = 10.08; // Increased by 5% from 9.6 (9.6 * 1.05 = 10.08) for faster movement
        this.keys = {
            forward: false, backward: false, left: false, right: false,
            shootUp: false, shootDown: false, shootLeft: false, shootRight: false,
            toggleCamera: false, // <<< Add key state for camera toggle
            zoomIn: false,      // <<< Add key state for zoom in
            zoomOut: false,      // <<< Add key state for zoom out
            toggleDebugLight: false, // <<< Add key state for debug light toggle
            shoot: false,       // <<< Add spacebar shooting for desktop first-person mode
            interact: false,    // <<< Add interact key for chest interactions
            enter: false        // <<< Add enter key for chest interactions
        };

        // First-person camera rotation state
        this.firstPersonRotation = {
            yaw: 0,   // Horizontal rotation (Y-axis)
            pitch: 0, // Vertical rotation (X-axis) - now used for mouse look
            sensitivity: 0.002, // Mouse-like sensitivity for WASD rotation
            mouseSensitivity: 0.002, // Mouse sensitivity for pointer lock controls
            smoothing: 0.15,     // Smoothing factor for rotation interpolation
            maxPitch: Math.PI / 2 - 0.1, // FIXED: Maximum pitch angle (nearly 90 degrees up for full vertical range)
            minPitch: -Math.PI / 2 + 0.1 // FIXED: Minimum pitch angle (nearly -90 degrees down, prevent gimbal lock)
        };

        // Mouse controls for first-person mode
        this.mouseControls = {
            isPointerLocked: false,
            isEnabled: false, // Only enabled in desktop first-person mode
            lockElement: null // Will be set to the canvas/document element
        };
        this.inputEnabled = false;

        // Collision detection helpers - Updated counts for VOXEL_SIZE = 0.05
        // const legHeightVoxels = 14; // << Remove old dimension constants
        // const bodyHeightVoxels = 14;
        // const headWidthVoxels = 14;
        // const headHeightVoxels = 10;
        // const hairWidthVoxels = 14;
        // const bodyDepthVoxels = 11;
        // const armWidthVoxels = 5;
        // const armHeightVoxels = 13;
        // const armDepthVoxels = 5;
        // const neckWidthVoxels = 5;
        // const neckHeightVoxels = 3;
        // const neckDepthVoxels = 5;
        // const shoeWidthVoxels = 8;
        // const shoeHeightVoxels = 3;
        // const shoeDepthVoxels = 8;
        // const headDepthVoxels = 10;

        const playerScale = 0.7; // Match the scale applied in DungeonHandler

        // Approximate bounding box based on voxel counts and VOXEL_SIZE
        // --- New Dimensions for Elemental Model (v2 - Macro Voxels) ---
        // const elementalWidthVoxels = 11; // Old base voxel counts
        // const elementalHeightVoxels = 25;
        // const elementalDepthVoxels = 7;

        const elementalMacroWidth = 7;   // Approx max width in macro voxels
        const elementalMacroHeight = 22; // Updated total height (was 21)
        const elementalMacroDepth = PLAYER_PIXEL_SCALE_Z;   // Depth now matches the scale (Should be 3)

        let playerWidth = elementalMacroWidth * PLAYER_VOXEL_WIDTH; // Use macro dimensions
        let playerHeight = elementalMacroHeight * PLAYER_VOXEL_HEIGHT;
        let playerDepth = elementalMacroDepth * PLAYER_VOXEL_DEPTH; // Use macro depth
        // -------------------------------------------------------------

        // Apply scaling to the calculated collision dimensions
        playerWidth *= playerScale;
        playerHeight *= playerScale;
        playerDepth *= playerScale;

        this.playerSize = new THREE.Vector3(playerWidth, playerHeight, playerDepth);
        this.playerBox = new THREE.Box3();
        // --- Add Box Helpers ---
        this.playerBoxHelper = new THREE.Box3Helper(this.playerBox, 0x00ff00); // Green for player
        this.scene.add(this.playerBoxHelper);
        this.wallBoxHelpers = {}; // Store wall helpers temporarily
        // -----------------------
        this._updatePlayerBounds();

        // Bind event listeners
        this.boundKeyDown = this._handleKeyDown.bind(this);
        this.boundKeyUp = this._handleKeyUp.bind(this);
        this.boundMouseMove = this._handleMouseMove.bind(this);
        this.boundPointerLockChange = this._handlePointerLockChange.bind(this);
        this.boundPointerLockError = this._handlePointerLockError.bind(this);
        this.boundClick = this._handleClick.bind(this);

        this.animationTime = 0;
        this.walkCycleSpeed = 15; // Adjust speed of swing
        this.walkCycleMagnitude = Math.PI / 8; // Adjust amount of swing
        // Idle Animation Parameters
        this.idleCycleSpeed = 1.5;
        this.idleCycleMagnitude = Math.PI / 48; // Small magnitude for subtle sway
        this.idleTime = 0; // Timer for idle duration

        // --- Player Health ---
        this.maxHealth = 200; // Set to 200 for UI purposes
        this.actualHealth = 8; // Track actual health separately
        this.currentHealth = 8; // Start with 8 health for UI display
        this.healthBar = new SoulOrbHealthBar(camera, this.maxHealth);
        this.healthBar.updateHealth(this.currentHealth); // Explicitly set the health bar to show 8

        // --- Invincibility System ---
        this.isInvincible = false;
        this.invincibilityDuration = 0.5; // Half a second of invincibility
        this.invincibilityTimer = 0;

        // --- Visual Feedback System ---
        this.isFlashing = false;
        this.flashTimer = 0;
        this.flashFrequency = 5; // 5 flashes per second
        this.flashState = false; // Current flash state (on/off)
        this.originalMaterials = new Map(); // Store original materials for restoration
        this.flashMaterial = null; // White flash material

        // --- Screen Shake System ---
        this.isShaking = false;
        this.shakeTimer = 0;
        this.shakeDuration = 0.25; // 0.25 seconds
        this.shakeIntensity = 0.1; // Moderate shake intensity
        this.originalCameraPosition = new THREE.Vector3();
        this.shakeOffset = new THREE.Vector3();
        // ---------------------

        // --- Player Souls ---
        this.souls = 0; // Start with 0 souls
        this.onSoulsChanged = null; // Callback for when souls change
        // -------------------

        // --- Foot Light Trail ---
        this.footTrailLights = []; // Array to hold trail lights
        this.trailSpawnTimer = 0;  // Timer to control spawn rate
        // --- End Foot Light Trail ---

        // --- ESP Debug State ---
        this.isEspEnabled = false;
        // -----------------------

        // --- Rotation Helpers State ---
        this.isEspRotationViewActive = false; // <<< Separate state for rotation view
        this.rotationHelpersGroup = new THREE.Group(); // <<< Group for helpers
        this.scene.add(this.rotationHelpersGroup);
        this.rotationHelpersGroup.visible = false; // Start hidden
        this.boneHelpers = {}; // Map to store helpers by bone name
        this._createRotationHelpers(); // Create helpers on initialization
        // -----------------------------

        // --- Shooting State ---
        this.shootDirection = new THREE.Vector3();
        this.isShooting = false;
        this.shootCooldown = 0.4; // Cooldown in seconds (2.5 shots per second - more reasonable)
        this.timeSinceLastShot = this.shootCooldown; // Allow shooting immediately

        // Mobile 360-degree shooting
        this.mobileShootVector = { x: 0, y: 0 }; // Raw joystick vector for 360-degree shooting
        this.isMobileShooting = false;
        // ---------------------

        // --- Projectile Stats ---
        this.baseProjectileDamage = 10;
        this.baseProjectileRange = 36.0; // Increased by 20% from 30.0 (30.0 * 1.2 = 36.0) for better shooting range
        this.baseProjectileSize = 0.15;

        this.projectileDamage = this.baseProjectileDamage;
        this.projectileRange = this.baseProjectileRange;
        this.projectileSize = this.baseProjectileSize;
        // ------------------------

        // Store a reference to the DungeonHandler in the player's userData
        // This will help other systems (like CRTEffectManager) find the DungeonHandler
        if (this.playerMesh && this.dungeonHandler) {
            this.playerMesh.userData.dungeonHandler = this.dungeonHandler;
        }

        this._updateRotationHelpersVisibility(); // Ensure helpers are hidden initially

        // Initialize visual feedback systems
        this._initializeVisualFeedback();
    }

    enable() {
        console.log("PlayerController enabled");
        window.addEventListener('keydown', this.boundKeyDown);
        window.addEventListener('keyup', this.boundKeyUp);
        this.inputEnabled = true;

        // Enable mouse controls if appropriate
        this._updateMouseControlsState();

        // Ensure health bar exists and is properly initialized
        if (!this.healthBar) {
            this.healthBar = new SoulOrbHealthBar(this.camera, this.maxHealth);
            this.healthBar.updateHealth(this.currentHealth);
        }
    }

    disable() {
        console.log("PlayerController disabled");
        window.removeEventListener('keydown', this.boundKeyDown);
        window.removeEventListener('keyup', this.boundKeyUp);

        // Disable mouse controls
        this._disableMouseControls();

        this.inputEnabled = false;
        this.keys = { forward: false, backward: false, left: false, right: false,
            shootUp: false, shootDown: false, shootLeft: false, shootRight: false, shoot: false,
            interact: false, enter: false };

        // Cleanup foot trail lights
        this.footTrailLights.forEach(light => {
            if (light && this.scene) {
                this.scene.remove(light);
            }
        });
        this.footTrailLights = [];

        // Do NOT dispose health bar during room transitions
        // if (this.healthBar) {
        //     this.healthBar.dispose();
        // }

        // Cleanup ALL box helpers (player + collision)
        if (this.playerBoxHelper && this.scene) {
            this.scene.remove(this.playerBoxHelper);
        }
        Object.values(this.wallBoxHelpers).forEach(helper => {
            if (helper && this.scene) this.scene.remove(helper);
        });
        this.wallBoxHelpers = {};
    }

    /**
     * Disable only movement (for dialogue, etc.) while keeping other input active
     */
    disableMovement() {
        this.movementDisabled = true;
        // Clear movement keys
        this.keys.forward = false;
        this.keys.backward = false;
        this.keys.left = false;
        this.keys.right = false;
        this.keys.shootUp = false;
        this.keys.shootDown = false;
        this.keys.shootLeft = false;
        this.keys.shootRight = false;
        console.log("PlayerController: Movement disabled");
    }

    /**
     * Re-enable movement
     */
    enableMovement() {
        this.movementDisabled = false;
        console.log("PlayerController: Movement enabled");
    }

    /**
     * Check if weapon system is currently attacking
     */
    isWeaponSystemAttacking() {
        return this.weaponSystem && this.weaponSystem.isAttacking;
    }

    _handleKeyDown(event) {
        let isEspToggle = false;
        // Check for Tab key
        if (event.code === 'Tab') {
             isEspToggle = true;

             // Display room shape name when Tab is pressed
             if (this.dungeonHandler && this.inputEnabled) {
                 this.dungeonHandler.displayRoomShape();
             }
        }

        if (isEspToggle) {
            // Only toggle if input is generally enabled
            if (!this.inputEnabled) return;

            // Toggle both ESP states
            this.isEspEnabled = !this.isEspEnabled;
            this.isEspRotationViewActive = this.isEspEnabled; // Link rotation view to general ESP for now
            console.log(`ESP Toggled: ${this.isEspEnabled}, Rotation View: ${this.isEspRotationViewActive}`);

            // Update visibility of different helpers
            if (this.playerBoxHelper) this.playerBoxHelper.visible = this.isEspEnabled;
            this._updateEspHelpersVisibility(); // Updates collision helpers
            this._updateRotationHelpersVisibility(); // Updates bone rotation helpers

            // --- Call DungeonHandler to update display ---
            if (this.dungeonHandler) {
                 this.dungeonHandler._updateTotalRoomCountDisplay(this.isEspEnabled);
                 this.dungeonHandler.setEspRotationViewActive(this.isEspRotationViewActive); // <<< Notify DungeonHandler
                 // FIXED: Sync ESP state with DungeonHandler for local coordinate system
                 this.dungeonHandler.isEspEnabled = this.isEspEnabled;
            }
            // -------------------------------------------

            event.preventDefault(); // Prevent default Tab behavior (like changing focus)
            return; // Don't process other keys if ESP was toggled
        }

        if (!this.inputEnabled) return; // Ignore other keys if disabled

        switch (event.code) {
            // --- Movement (Arrow Keys) ---
            case 'ArrowUp':    this.keys.forward = true; break;
            case 'ArrowDown':  this.keys.backward = true; break;
            case 'ArrowLeft':  this.keys.left = true; break;
            case 'ArrowRight': this.keys.right = true; break;

            // --- Shooting (WASD) ---
            case 'KeyW': this.keys.shootUp = true; break;
            case 'KeyS': this.keys.shootDown = true; break;
            case 'KeyA': this.keys.shootLeft = true; break;
            case 'KeyD': this.keys.shootRight = true; break;

            // --- Spacebar Shooting (Desktop First-Person) ---
            case 'Space': this.keys.shoot = true; break;

            // --- Interaction Keys ---
            case 'Enter': this.keys.enter = true; this.keys.interact = true; break;

            // --- Camera Toggle ---
            case 'KeyC': // <<< Add case for 'C' key
                if (!this.keys.toggleCamera) { // Prevent repeated calls while key is held
                    this.keys.toggleCamera = true;
                    this.dungeonHandler?.toggleCameraView(); // Call method on DungeonHandler
                }
                break;

            // --- DEBUG: Damage Player ---
            case 'KeyU':
                this.takeDamage(1);
                break;
            // --- DEBUG: Increase Max Health ---
            case 'KeyY':
                this.increaseMaxHealth(1);
                break;
            // --- DEBUG: Increase Projectile Stats ---
            case 'Digit1': this.increaseProjectileSize(0.02); break;
            case 'Digit2': this.increaseProjectileRange(1.0); break;
            case 'Digit3': this.increaseProjectileDamage(5); break;
            // --- DEBUG: Toggle Music Debug HUD --- DISABLED
            // case 'KeyM':
            //     if (this.dungeonHandler && this.dungeonHandler.audioManager) {
            //         this.dungeonHandler.audioManager.toggleMusicDebug();
            //     }
            //     break;

            // --- Camera Zoom ---
            case 'Slash': this.keys.zoomOut = true; break; // Updated code for '-'
            case 'BracketRight': this.keys.zoomIn = true; break; // Updated code for '+' (often '=' key)

            // --- Debug Light Toggle --- DISABLED
            // case 'KeyO':
            //     if (!this.keys.toggleDebugLight) { // Prevent rapid toggles
            //         this.keys.toggleDebugLight = true;
            //         this.dungeonHandler?.toggleDebugLight(); // Call method on DungeonHandler
            //     }
            //     break;



            // --- Teleport to Boss (F10) --- DISABLED
            // case 'F10':
            //     console.log("[PlayerController] F10 key pressed - Teleporting to boss");
            //     console.log("[PlayerController] dungeonHandler exists:", !!this.dungeonHandler);
            //     if (this.dungeonHandler) {
            //         console.log("[PlayerController] Calling teleportToBoss method");
            //         this.teleportToBoss();
            //         // Prevent default F10 behavior
            //         event.preventDefault();
            //         console.log("[PlayerController] After teleportToBoss call");
            //     } else {
            //         console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
            //     }
            //     break;


            // --- Alternative Teleport to Boss (F11 key) --- DISABLED
            // case 'F11':
            //     console.log("[PlayerController] F11 key pressed - Direct teleport to boss");
            //     if (this.dungeonHandler) {
            //         // Direct teleport to boss room using room ID 1 (usually the boss room)
            //         this.dungeonHandler._transitionToRoom(1, 'north');
            //         event.preventDefault(); // Prevent default F11 behavior
            //     } else {
            //         console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
            //     }
            //     break;

            // --- Alternative Teleport to Boss (T key) --- DISABLED
            // case 'KeyT':
            //     console.log("[PlayerController] T key pressed - Teleporting to boss");
            //     if (this.dungeonHandler) {
            //         this.teleportToBoss();
            //     } else {
            //         console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
            //     }
            //     break;

            // --- ESC Key: Exit Pointer Lock ---
            case 'Escape':
                if (this.mouseControls.isPointerLocked) {
                    console.log("[Mouse Controls] ESC pressed, exiting pointer lock");
                    this._exitPointerLock();
                }
                break;
        }
    }

    _handleKeyUp(event) {
        // Always process key up events to prevent stuck keys
        // This ensures keys are properly released even if inputEnabled changes during a key press
        
        // Handle Tab key release to hide displays
        if (event.code === 'Tab') {
            if (this.dungeonHandler && this.inputEnabled) {
                this.dungeonHandler.hideTabDisplays();
            }
            event.preventDefault();
        }
        
        switch (event.code) {
            // --- Movement (Arrow Keys) ---
            case 'ArrowUp':    this.keys.forward = false; break;
            case 'ArrowDown':  this.keys.backward = false; break;
            case 'ArrowLeft':  this.keys.left = false; break;
            case 'ArrowRight': this.keys.right = false; break;

            // --- Shooting (WASD) ---
            case 'KeyW': this.keys.shootUp = false; break;
            case 'KeyS': this.keys.shootDown = false; break;
            case 'KeyA': this.keys.shootLeft = false; break;
            case 'KeyD': this.keys.shootRight = false; break;

            // --- Spacebar Shooting (Desktop First-Person) ---
            case 'Space': this.keys.shoot = false; break;

            // --- Interaction Keys ---
            case 'Enter': this.keys.enter = false; this.keys.interact = false; break;

            // --- Camera Toggle ---
            case 'KeyC': this.keys.toggleCamera = false; break; // <<< Reset toggle key state on key up

            // --- Camera Zoom ---
            case 'Slash': this.keys.zoomOut = false; break; // Updated code for '-'
            case 'BracketRight': this.keys.zoomIn = false; break; // Updated code for '+'

            // --- Debug Light Toggle --- DISABLED
            // case 'KeyO': this.keys.toggleDebugLight = false; break; // Reset flag on key up
        }
    }

    // --- Mouse Event Handlers for First-Person Mode ---
    _handleMouseMove(event) {
        // Only process mouse movement in desktop first-person mode
        if (!this.mouseControls.isEnabled || !this.mouseControls.isPointerLocked) return;

        const isFirstPerson = this.dungeonHandler?.cameraMode === 'firstPerson';
        const isMobile = this.dungeonHandler?.sceneManager?.isMobileDevice || false;

        // Only apply mouse controls in desktop first-person mode
        if (!isFirstPerson || isMobile) return;

        // Apply mouse movement to camera rotation
        const movementX = event.movementX || 0;
        const movementY = event.movementY || 0;

        // Update yaw (horizontal rotation)
        this.firstPersonRotation.yaw -= movementX * this.firstPersonRotation.mouseSensitivity;

        // Update pitch (vertical rotation) with limits
        this.firstPersonRotation.pitch -= movementY * this.firstPersonRotation.mouseSensitivity;
        this.firstPersonRotation.pitch = Math.max(
            this.firstPersonRotation.minPitch,
            Math.min(this.firstPersonRotation.maxPitch, this.firstPersonRotation.pitch)
        );

        // Keep yaw in reasonable range
        while (this.firstPersonRotation.yaw > Math.PI) this.firstPersonRotation.yaw -= 2 * Math.PI;
        while (this.firstPersonRotation.yaw < -Math.PI) this.firstPersonRotation.yaw += 2 * Math.PI;
    }

    _handlePointerLockChange() {
        // Check for pointer lock element with browser compatibility
        const pointerLockElement = document.pointerLockElement ||
                                  document.mozPointerLockElement ||
                                  document.webkitPointerLockElement;

        const isLocked = pointerLockElement === this.mouseControls.lockElement;
        this.mouseControls.isPointerLocked = isLocked;

        if (isLocked) {
            console.log("[Mouse Controls] Pointer lock activated - cursor hidden, infinite mouse movement enabled");
            // Note: Global cursor management is handled by SceneManager
        } else {
            console.log("[Mouse Controls] Pointer lock deactivated");
            // Note: Global cursor management is handled by SceneManager
        }
    }

    _handlePointerLockError() {
        console.error("[Mouse Controls] Pointer lock error occurred");
        this.mouseControls.isPointerLocked = false;
    }

    _handleClick() {
        // Request pointer lock when clicking in desktop first-person mode
        const isFirstPerson = this.dungeonHandler?.cameraMode === 'firstPerson';
        const isMobile = this.dungeonHandler?.sceneManager?.isMobileDevice || false;

        if (isFirstPerson && !isMobile && !this.mouseControls.isPointerLocked) {
            console.log("[Mouse Controls] Click detected, requesting pointer lock");

            // CRITICAL FIX: Add error handling for pointer lock request
            try {
                this._requestPointerLock();
            } catch (error) {
                console.warn("[Mouse Controls] Pointer lock request failed:", error.message);
                // Silently continue - pointer lock is optional for gameplay
            }
        }
    }

    _requestPointerLock() {
        if (this.mouseControls.lockElement) {
            console.log("[Mouse Controls] Requesting pointer lock on element:", this.mouseControls.lockElement);

            // Try different pointer lock methods for browser compatibility
            const element = this.mouseControls.lockElement;
            const requestPointerLock = element.requestPointerLock ||
                                     element.mozRequestPointerLock ||
                                     element.webkitRequestPointerLock;

            if (requestPointerLock) {
                // CRITICAL FIX: Handle both promise-based and callback-based pointer lock APIs
                try {
                    const result = requestPointerLock.call(element);

                    // Handle promise-based API (modern browsers)
                    if (result && typeof result.then === 'function') {
                        result.catch(error => {
                            console.warn("[Mouse Controls] Pointer lock promise rejected:", error.message);
                            // This is expected if user gesture is required - not an error
                        });
                    }

                    console.log("[Mouse Controls] Pointer lock requested");
                } catch (error) {
                    console.warn("[Mouse Controls] Pointer lock request failed:", error.message);
                }
            } else {
                console.warn("[Mouse Controls] Pointer lock not supported by browser");
            }
        } else {
            console.error("[Mouse Controls] No lock element available for pointer lock");
        }
    }

    _exitPointerLock() {
        // Try different exit pointer lock methods for browser compatibility
        const exitPointerLock = document.exitPointerLock ||
                               document.mozExitPointerLock ||
                               document.webkitExitPointerLock;

        if (exitPointerLock) {
            exitPointerLock.call(document);
            console.log("[Mouse Controls] Pointer lock exit requested");
        }

        // Note: Global cursor management is handled by SceneManager
    }

    // --- Mouse Controls Management ---
    _enableMouseControls() {
        const isFirstPerson = this.dungeonHandler?.cameraMode === 'firstPerson';
        const isMobile = this.dungeonHandler?.sceneManager?.isMobileDevice || false;

        // Only enable mouse controls in desktop first-person mode
        if (isFirstPerson && !isMobile) {
            // PERFORMANCE FIX: Only enable if currently disabled to prevent console spam
            if (this.mouseControls.isEnabled) {
                return; // Already enabled, no need to do anything
            }

            this.mouseControls.isEnabled = true;

            // Set lock element (prefer canvas for better pointer lock experience)
            const canvas = this.dungeonHandler?.sceneManager?.renderer?.domElement;
            this.mouseControls.lockElement = canvas || document.body;

            console.log("[Mouse Controls] Lock element set to:", this.mouseControls.lockElement.tagName);

            // Add mouse event listeners with browser compatibility
            document.addEventListener('mousemove', this.boundMouseMove);

            // Add pointer lock change listeners for all browsers
            document.addEventListener('pointerlockchange', this.boundPointerLockChange);
            document.addEventListener('mozpointerlockchange', this.boundPointerLockChange);
            document.addEventListener('webkitpointerlockchange', this.boundPointerLockChange);

            // Add pointer lock error listeners for all browsers
            document.addEventListener('pointerlockerror', this.boundPointerLockError);
            document.addEventListener('mozpointerlockerror', this.boundPointerLockError);
            document.addEventListener('webkitpointerlockerror', this.boundPointerLockError);

            this.mouseControls.lockElement.addEventListener('click', this.boundClick);

            console.log("[Mouse Controls] Mouse controls enabled for desktop first-person mode");
            console.log("[Mouse Controls] Click anywhere to activate pointer lock and hide cursor");

            // Auto-request pointer lock when entering first-person mode for better UX
            setTimeout(() => {
                if (this.mouseControls.isEnabled && !this.mouseControls.isPointerLocked) {
                    console.log("[Mouse Controls] Auto-requesting pointer lock for first-person mode");
                    this._requestPointerLock();
                }
            }, 100); // Small delay to ensure everything is set up
        } else {
            this._disableMouseControls();
        }
    }

    _disableMouseControls() {
        // PERFORMANCE FIX: Only disable if currently enabled to prevent console spam
        if (!this.mouseControls.isEnabled) {
            return; // Already disabled, no need to do anything
        }

        this.mouseControls.isEnabled = false;

        // Exit pointer lock if active
        if (this.mouseControls.isPointerLocked) {
            this._exitPointerLock();
        }

        // Remove mouse event listeners with browser compatibility
        document.removeEventListener('mousemove', this.boundMouseMove);

        // Remove pointer lock change listeners for all browsers
        document.removeEventListener('pointerlockchange', this.boundPointerLockChange);
        document.removeEventListener('mozpointerlockchange', this.boundPointerLockChange);
        document.removeEventListener('webkitpointerlockchange', this.boundPointerLockChange);

        // Remove pointer lock error listeners for all browsers
        document.removeEventListener('pointerlockerror', this.boundPointerLockError);
        document.removeEventListener('mozpointerlockerror', this.boundPointerLockError);
        document.removeEventListener('webkitpointerlockerror', this.boundPointerLockError);

        if (this.mouseControls.lockElement) {
            this.mouseControls.lockElement.removeEventListener('click', this.boundClick);
            // Note: Global cursor management is handled by SceneManager
        }

        console.log("[Mouse Controls] Mouse controls disabled");
    }

    _updateMouseControlsState() {
        // Update mouse controls based on current camera mode and device type
        const isFirstPerson = this.dungeonHandler?.cameraMode === 'firstPerson';
        const isMobile = this.dungeonHandler?.sceneManager?.isMobileDevice || false;

        if (isFirstPerson && !isMobile) {
            this._enableMouseControls();
        } else {
            this._disableMouseControls();
        }
    }

    _updatePlayerBounds() {
        const boxCenter = this.playerMesh.position.clone();
        // --- Restore standard vertical centering ---
        // boxCenter.y = this.playerSize.y * 0.45;   // Previous attempt (slightly lower center)
        boxCenter.y += this.playerSize.y * 0.5;    // Standard center alignment
        // -------------------------------------------
        this.playerBox.setFromCenterAndSize(
            boxCenter,
            this.playerSize
        );
        // Update the helper
        if (this.playerBoxHelper) {
            this.playerBoxHelper.box.copy(this.playerBox); // Use copy to update helper's box
        }
    }

    // Check collision between player's potential bounding box and collision objects
    _checkCollision(potentialPosition) {
        // Check if noclip is enabled
        if (this.noclip) {
            return false; // No collision when noclip is enabled
        }
        
        const potentialPlayerBox = this.playerBox.clone().translate(
             potentialPosition.clone().sub(this.playerMesh.position)
        );

        for (const obj of this.collisionObjects) {
             // CRITICAL FIX: Check for geometry OR children with geometry (for wall groups)
            if (!obj.geometry && !(obj.children && obj.children.length > 0)) continue;

            // CRITICAL FIX: Skip floor objects to prevent player getting stuck
            // Check userData.isFloor first (most reliable method)
            const isFloorByUserData = obj.userData?.isFloor === true;

            const isFloor = obj.name && (
                obj.name.toLowerCase().includes('floor') ||
                obj.name.toLowerCase().includes('ground') ||
                obj.name.toLowerCase().includes('terrain')
            );

            const hasFloorMaterial = obj.material && (
                obj.material.name?.includes('CaveFloor') ||
                obj.material.name?.includes('caveFloor') ||
                (obj.material.materials && obj.material.materials.some(mat =>
                    mat.name?.includes('CaveFloor') || mat.name?.includes('caveFloor')))
            );

            // Skip ALL types of floor objects - they should not block player movement
            if (isFloorByUserData || isFloor || hasFloorMaterial) {
                continue; // Skip floor collision for player movement
            }

            // Use setFromObject for more reliable world bounding box
            const objectBox = new THREE.Box3().setFromObject(obj);

            if (potentialPlayerBox.intersectsBox(objectBox)) {
                // console.log("Collision detected with:", obj.name || obj.uuid);
                return true; // Collision detected
            }
        }
        return false; // No collision
    }

    _isWithinFloorBounds(position) {
         // Use minX/maxX etc. from constructor
         return position.x >= this.floorBounds.minX && position.x <= this.floorBounds.maxX &&
                position.z >= this.floorBounds.minZ && position.z <= this.floorBounds.maxZ;
     }

    /**
     * Adapt player height to floor curvature
     * IMPROVED: Better floor following with raycasting for curved floors
     * @private
     */
    _adaptToFloorHeight() {
        // PERFORMANCE OPTIMIZATION: Only update when player has moved significantly
        const playerPos = this.playerMesh.position;
        const movementThreshold = 0.5; // Only update if player moved more than 0.5 units

        if (this._lastFloorAdaptPosition) {
            const distanceMoved = playerPos.distanceTo(this._lastFloorAdaptPosition);
            if (distanceMoved < movementThreshold) {
                return; // Player hasn't moved enough to warrant an update
            }
        }

        // PERFORMANCE FIX: Use timer-based throttling instead of frame counting
        if (!this._floorHeightTimer) this._floorHeightTimer = 0;

        const currentTime = this.clock ? this.clock.getElapsedTime() : Date.now() / 1000;
        if (!this._lastFloorHeightUpdate) this._lastFloorHeightUpdate = currentTime;

        const timeSinceLastUpdate = currentTime - this._lastFloorHeightUpdate;
        const updateInterval = 0.15; // Increased from 0.1 to 0.15 seconds for better performance

        // Only run every 0.15 seconds for better performance
        if (timeSinceLastUpdate < updateInterval) {
            return;
        }

        this._lastFloorHeightUpdate = currentTime;
        this._lastFloorAdaptPosition = playerPos.clone(); // Store current position

        // Quick exit if no collision objects
        if (!this.collisionObjects || this.collisionObjects.length === 0) {
            return;
        }

        // PERFORMANCE OPTIMIZATION: Use DungeonHandler's cached floor height if available
        if (this.dungeonHandler && this.dungeonHandler._getCachedFloorHeight) {
            const cachedHeight = this.dungeonHandler._getCachedFloorHeight(playerPos.x, playerPos.z);
            if (cachedHeight !== null) {
                // Use cached height with curvature calculation
                let adjustedFloorY = cachedHeight;
                if (this.dungeonHandler._calculateFloorCurvatureHeight) {
                    adjustedFloorY = this.dungeonHandler._calculateFloorCurvatureHeight(playerPos.x, playerPos.z, cachedHeight);
                }

                const targetY = adjustedFloorY + 0.25; // Fixed height above floor
                const heightDifference = Math.abs(playerPos.y - targetY);

                if (heightDifference > 0.05) { // More precise threshold
                    // Faster adaptation for better floor following
                    this.playerMesh.position.y = targetY;
                }
                return; // Exit early using cached data
            }
        }

        // Fallback to raycasting if cache is not available
        const raycaster = new THREE.Raycaster();

        // Cast ray from above player position downward
        raycaster.set(
            new THREE.Vector3(playerPos.x, playerPos.y + 3, playerPos.z),
            new THREE.Vector3(0, -1, 0)
        );

        // Find floor meshes efficiently
        const floorMeshes = this.collisionObjects.filter(obj => obj.userData?.isFloor === true);

        if (floorMeshes.length > 0) {
            const intersects = raycaster.intersectObjects(floorMeshes, true);

            if (intersects.length > 0) {
                const floorY = intersects[0].point.y;

                // FIXED: Use curvature calculation for consistent positioning
                let adjustedFloorY = floorY;
                if (this.dungeonHandler && this.dungeonHandler._calculateFloorCurvatureHeight) {
                    adjustedFloorY = this.dungeonHandler._calculateFloorCurvatureHeight(playerPos.x, playerPos.z, floorY);
                }

                const requiredPlayerY = adjustedFloorY + 0.25; // Standard height above floor
                const heightDifference = Math.abs(this.playerMesh.position.y - requiredPlayerY);

                // Precise height adjustment
                if (heightDifference > 0.05) {
                    this.playerMesh.position.y = requiredPlayerY;
                }
            } else {
                // Fallback: ensure player doesn't fall below base level
                const baseFloorY = 0.25;
                if (this.playerMesh.position.y < baseFloorY) {
                    this.playerMesh.position.y = baseFloorY;
                }
            }
        }
    }

    // New method to resolve ground collision
    _resolveGroundCollision() {
        // We abandon the bounding box approach for ground resolution for now,
        // as the bounds calculation itself seems tied to the mesh origin incorrectly.
        // Instead, we enforce a minimum mesh Y position.

        // Estimate the required offset to lift the mesh origin so the feet are visually on the ground.
        // This value might need tuning based on the model geometry.
        const requiredMeshYOffset = 0.3; // Increased offset from 0.25

        if (this.playerMesh.position.y < requiredMeshYOffset) {
            const correction = requiredMeshYOffset - this.playerMesh.position.y;
            // console.log(`[Ground Resolve] Mesh Y too low (${this.playerMesh.position.y.toFixed(3)}). Target: ${requiredMeshYOffset}. Applying Correction: ${correction.toFixed(3)}`);
            this.playerMesh.position.y += correction; // Apply correction directly to mesh Y

            // Update bounds AFTER correction as the mesh position changed.
            this._updatePlayerBounds();
        }
     }

    // --- NEW: Helper to check collision at a specific position ---
    _checkCollisionAtPosition(potentialPosition) {
        // Check if noclip is enabled
        if (this.noclip) {
            return false; // No collision when noclip is enabled
        }
        
        // --- Check Numerical Bounds FIRST ---
        if (potentialPosition.x < this.floorBounds.minX || potentialPosition.x > this.floorBounds.maxX ||
            potentialPosition.z < this.floorBounds.minZ || potentialPosition.z > this.floorBounds.maxZ) {
            console.log(`[PlayerCollision] BLOCKED by floor bounds:`, {
                position: potentialPosition,
                bounds: this.floorBounds,
                violatesMinX: potentialPosition.x < this.floorBounds.minX,
                violatesMaxX: potentialPosition.x > this.floorBounds.maxX,
                violatesMinZ: potentialPosition.z < this.floorBounds.minZ,
                violatesMaxZ: potentialPosition.z > this.floorBounds.maxZ
            });
            return true;
        }
        // -------------------------------

        const tempBoxCenter = potentialPosition.clone();
        // Align temp box the same way as playerBox
        // tempBoxCenter.y = this.playerSize.y * 0.45; // Using slightly lowered center
        tempBoxCenter.y += this.playerSize.y * 0.5; // Standard center alignment
        const tempPlayerBox = new THREE.Box3();
        tempPlayerBox.setFromCenterAndSize(tempBoxCenter, this.playerSize);

        // -- Collision helpers are now managed in update() --

        let collisionDetected = false; // Flag to track if any collision happened

        // Check against dynamic collision objects
        for (const collisionObject of this.collisionObjects) {
            // CRITICAL FIX: Skip floor objects to prevent player getting stuck on curved floors
            // Check userData.isFloor first (most reliable method)
            const isFloorByUserData = collisionObject.userData?.isFloor === true;

            const isFloor = collisionObject.name && (
                collisionObject.name.toLowerCase().includes('floor') ||
                collisionObject.name.toLowerCase().includes('ground') ||
                collisionObject.name.toLowerCase().includes('terrain')
            );

            const hasFloorMaterial = collisionObject.material && (
                collisionObject.material.name?.includes('caveFloor') ||
                (collisionObject.material.materials && collisionObject.material.materials.some(mat => mat.name?.includes('caveFloor')))
            );

            // Check for floor decorations (like ritual circles, rugs, etc.)
            const isFloorDecoration = collisionObject.userData?.isFloorDecoration === true;
            
            // Check for small ground objects that shouldn't block movement
            const isSmallGroundObject = collisionObject.userData?.isGroundDecoration === true || 
                                      collisionObject.userData?.isSmallObject === true;
            
            // Check for specific object types that are small ground decorations
            const isDebrisOrSmallDecor = collisionObject.name && (
                collisionObject.name.toLowerCase().includes('debris') ||
                collisionObject.name.toLowerCase().includes('rubble') ||
                collisionObject.name.toLowerCase().includes('candle') ||
                collisionObject.name.toLowerCase().includes('bottle') ||
                collisionObject.name.toLowerCase().includes('potion') ||
                collisionObject.name.toLowerCase().includes('book') ||
                collisionObject.name.toLowerCase().includes('scroll') ||
                collisionObject.name.toLowerCase().includes('crystal_small') ||
                collisionObject.name.toLowerCase().includes('stone_small')
            );
            
            // Check object height to filter out small objects
            let isLowObject = false;
            if (collisionObject.geometry || (collisionObject.children && collisionObject.children.length > 0)) {
                const objBox = new THREE.Box3().setFromObject(collisionObject);
                const objHeight = objBox.max.y - objBox.min.y;
                const objTopY = objBox.max.y;
                // Consider it a low object if it's less than 1 unit tall and its top is below player's knee height
                isLowObject = objHeight < 1.0 && objTopY < (this.playerMesh.position.y + 0.5);
            }

            // Skip ALL types of floor objects and small ground decorations - they should not block player movement
            if (isFloorByUserData || isFloor || hasFloorMaterial || isFloorDecoration || 
                isSmallGroundObject || isDebrisOrSmallDecor || isLowObject) {
                continue; // Skip collision for player movement
            }

            let objectBox = new THREE.Box3();
            let collidedWithObject = false;

            // --- Use Standard 3D Check for ALL Collision Objects ---
            // Includes visible walls AND invisible boundary walls
            if (collisionObject.geometry || (collisionObject.children && collisionObject.children.length > 0)) {
                 objectBox.setFromObject(collisionObject);
                 if (tempPlayerBox.intersectsBox(objectBox)) {
                     collidedWithObject = true;
                 }
             }
            // -----------------------------------------------------

            if (collidedWithObject) {
                // CRITICAL FIX: ALL collision objects should block player movement regardless of name or material
                // Wall collision meshes are valid collision objects even if they don't have specific names or materials

                // Optional debug logging (can be disabled for performance)
                if (true) { // Set to true for debugging collision issues
                    console.log(`[PlayerCollision] BLOCKED by object:`, {
                        name: collisionObject.name || 'unnamed',
                        uuid: collisionObject.uuid,
                        position: collisionObject.position,
                        material: collisionObject.material?.name || 'no material',
                        userData: collisionObject.userData,
                        geometry: collisionObject.geometry?.type || 'no geometry',
                        parent: collisionObject.parent?.name || 'no parent',
                        type: collisionObject.type,
                        visible: collisionObject.visible,
                        children: collisionObject.children?.length || 0
                    });
                }

                collisionDetected = true; // Set flag
                break;
            }
        }

        return collisionDetected; // Return true if any collision occurred
    }
    // --- END NEW HELPER ---

    // --- NEW: Helper to manage ESP helper visibility ---
    _updateEspHelpersVisibility() {
        // Update Player Helper
        if (this.playerBoxHelper) {
            this.playerBoxHelper.visible = this.isEspEnabled;
        }

        // Update Collision Object Helpers
        const currentCollisionObjectUUIDs = new Set(this.collisionObjects.map(obj => obj.uuid));

        // Remove helpers for objects that are no longer in collisionObjects
        for (const uuid in this.wallBoxHelpers) {
            if (!currentCollisionObjectUUIDs.has(uuid)) {
                const helper = this.wallBoxHelpers[uuid];
                if (helper && this.scene) {
                    this.scene.remove(helper);
                }
                delete this.wallBoxHelpers[uuid];
            }
        }

        // Add/Update helpers for current collisionObjects
        for (const collisionObject of this.collisionObjects) {
            let helper = this.wallBoxHelpers[collisionObject.uuid];
            const objectBox = new THREE.Box3().setFromObject(collisionObject);
            const objectUUID = collisionObject.uuid;

            // Determine color based on name (visible walls vs invisible boundaries)
            let helperColor;
            if (collisionObject.name.startsWith('invisibleBoundary')) {
                helperColor = 0xffff00; // Yellow for invisible walls
            } else if (collisionObject.name === 'StonebrickWallSegment') {
                helperColor = 0xff0000; // Red for visible walls
            } else {
                helperColor = 0x808080; // Grey for anything else (shouldn't happen often)
            }

            if (!helper) {
                // Create new helper if it doesn't exist
                helper = new THREE.Box3Helper(objectBox, helperColor);
                this.scene.add(helper);
                this.wallBoxHelpers[objectUUID] = helper;
            } else {
                // Update existing helper's box and color (in case object changed)
                helper.box = objectBox;
                helper.material.color.setHex(helperColor);
            }
            // Set visibility based on ESP state
            helper.visible = this.isEspEnabled;

            // --- Remove Floor Child Helper Logic ---
            // We are no longer checking floor children directly for collision
            // or visualizing them separately in ESP mode.
            // Ensure any old child helpers are removed
            for (const uuidKey in this.wallBoxHelpers) {
                if (uuidKey.startsWith(`${objectUUID}_child_`)) {
                    const staleChildHelper = this.wallBoxHelpers[uuidKey];
                    if (staleChildHelper && this.scene) {
                        this.scene.remove(staleChildHelper);
                    }
                    delete this.wallBoxHelpers[uuidKey]; // Remove from dictionary
                }
            }
            // --- End Floor Child Helper Logic ---
        }
    }
    // --- End ESP Helper Update ---

    // --- NEW: Handle Mobile Input (Called by SceneManager) ---
    handleMobileInput(leftStickData, rightStickData) {
        if (!this.inputEnabled) return; // Don't process if controller disabled

        // MOBILE FIX: Properly reset only mobile input keys (don't interfere with keyboard)
        // Since isUsingKeyboard always returns false, we need to reset all keys for mobile
        this.keys.forward = false;
        this.keys.backward = false;
        this.keys.left = false;
        this.keys.right = false;
        this.keys.shootUp = false;
        this.keys.shootDown = false;
        this.keys.shootLeft = false;
        this.keys.shootRight = false;
        this.keys.interact = false; // Reset interaction key for mobile

        // Apply Left Stick (Movement)
        if (leftStickData && leftStickData.active && leftStickData.vector) {
            const vector = leftStickData.vector;

            // MOBILE FIX: Lower threshold for better responsiveness
            const threshold = 0.2; // Reduced from 0.3 to 0.2 for better sensitivity

            // MOBILE FIX: Correct coordinate mapping for movement
            // In THREE.js: forward = negative Z, backward = positive Z, left = negative X, right = positive X
            // FIXED: Joystick Y-axis mapping was backwards
            if (vector.y > threshold) {
                this.keys.forward = true; // FIXED: Positive joystick Y = forward (negative Z)
            }
            if (vector.y < -threshold) {
                this.keys.backward = true; // FIXED: Negative joystick Y = backward (positive Z)
            }
            if (vector.x < -threshold) {
                this.keys.left = true; // Negative joystick X = left (negative X)
            }
            if (vector.x > threshold) {
                this.keys.right = true; // Positive joystick X = right (positive X)
            }
        }

        // Apply Right Stick (Shooting) - 360-degree support
        if (rightStickData && rightStickData.active && rightStickData.vector) {
            const vector = rightStickData.vector;

            // Store raw vector for 360-degree shooting
            this.mobileShootVector.x = vector.x;
            this.mobileShootVector.y = vector.y;

            // Check if joystick is being used for shooting (any direction)
            const vectorMagnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y);
            const threshold = 0.2; // Threshold for shooting activation

            if (vectorMagnitude > threshold) {
                this.isMobileShooting = true;

                // Also set discrete direction keys for backward compatibility
                if (vector.y > threshold) {
                    this.keys.shootUp = true; // Positive joystick Y = shootUp (negative Z)
                }
                if (vector.y < -threshold) {
                    this.keys.shootDown = true; // Negative joystick Y = shootDown (positive Z)
                }
                if (vector.x < -threshold) {
                    this.keys.shootLeft = true; // Negative joystick X = shootLeft (negative X)
                }
                if (vector.x > threshold) {
                    this.keys.shootRight = true; // Positive joystick X = shootRight (positive X)
                }
            } else {
                this.isMobileShooting = false;
            }
        } else {
            // Reset mobile shooting when joystick is not active
            this.isMobileShooting = false;
            this.mobileShootVector.x = 0;
            this.mobileShootVector.y = 0;
        }

        // Check for right joystick tap (interaction)
        if (rightStickData && rightStickData.tapped) {
            this.keys.interact = true;
            console.log('[PlayerController] Right joystick tapped - interaction triggered');
        }
    }

    // Helper to check if a specific key direction is from keyboard (implementation depends on how _handleKeyDown/_handleKeyUp set state)
    // For now, we assume if the key is true, it might be keyboard, so we reset based on joystick status
    // A more robust solution might involve separate state for keyboard vs joystick input.
    isUsingKeyboard(direction) {
        // Simple check: if the corresponding keyboard event flag is true, assume keyboard
        // This needs refinement if mixing inputs is desired differently.
        switch (direction) {
            // Link key flags to actual keyboard event keys if necessary for more complex checks
            // This is a basic implementation assuming direct mapping
            default: return false; // Placeholder - refine if needed
        }
    }
    // --- END NEW METHOD ---

    update(deltaTime) {
        // --- Update ESP Helpers (PERFORMANCE FIX: Only update every 100ms for better responsiveness) ---
        const currentTime = this.clock.getElapsedTime() * 1000; // Convert to milliseconds
        if (!this._lastEspHelpersUpdate || (currentTime - this._lastEspHelpersUpdate) > 100) {
            this._updateEspHelpersVisibility();
            this._lastEspHelpersUpdate = currentTime;
        }
        // ------------------------

        if (!this.inputEnabled) return;

        const time = this.clock.getElapsedTime();
        this.trailSpawnTimer += deltaTime;
        this.timeSinceLastShot += deltaTime;

        // --- Movement Logic (Different for first-person vs normal mode) ---
        const moveDirection = new THREE.Vector3();
        const isFirstPerson = this.dungeonHandler?.cameraMode === 'firstPerson';

        // Check if movement is disabled (for dialogue, etc.)
        const movementAllowed = !this.movementDisabled;

        // Detect if we're on mobile device
        const isMobile = this.dungeonHandler?.sceneManager?.isMobileDevice || false;

        // Update mouse controls state based on current camera mode
        this._updateMouseControlsState();

        if (movementAllowed) {
            if (isFirstPerson) {
                // FIRST-PERSON MODE: Different controls for mobile vs desktop

                if (isMobile) {
                    // MOBILE: Only left stick (forward/backward/left/right) for movement
                    // Right stick is reserved for camera rotation and shooting
                    if (this.keys.forward) moveDirection.z -= 1;   // Left stick up = forward
                    if (this.keys.backward) moveDirection.z += 1;  // Left stick down = backward
                    if (this.keys.left) moveDirection.x -= 1;      // Left stick left = strafe left
                    if (this.keys.right) moveDirection.x += 1;     // Left stick right = strafe right
                } else {
                    // DESKTOP: WASD keys (shootUp/shootDown/shootLeft/shootRight) for movement
                    // Arrow keys will be used for camera rotation and shooting
                    if (this.keys.shootUp) moveDirection.z -= 1;    // W = forward
                    if (this.keys.shootDown) moveDirection.z += 1;  // S = backward
                    if (this.keys.shootLeft) moveDirection.x -= 1;  // A = strafe left
                    if (this.keys.shootRight) moveDirection.x += 1; // D = strafe right
                }
            } else {
                // NORMAL MODE: Different controls for mobile vs desktop

                if (isMobile) {
                    // MOBILE: Use joystick movement keys (forward/backward/left/right)
                    if (this.keys.forward) moveDirection.z -= 1;
                    if (this.keys.backward) moveDirection.z += 1;
                    if (this.keys.left) moveDirection.x -= 1;
                    if (this.keys.right) moveDirection.x += 1;
                } else {
                    // DESKTOP: Use WASD for movement (shootUp/shootDown/shootLeft/shootRight)
                    if (this.keys.shootUp) moveDirection.z -= 1;    // W = forward
                    if (this.keys.shootDown) moveDirection.z += 1;  // S = backward
                    if (this.keys.shootLeft) moveDirection.x -= 1;  // A = left
                    if (this.keys.shootRight) moveDirection.x += 1; // D = right
                }
            }
        }

        let attemptedMove = false;
        let movementBasedQuaternion = this.playerMesh.quaternion.clone(); // Store original quaternion

        if (moveDirection.lengthSq() > 0) {
            // console.log("Processing Movement (Keys/Stick detected)"); // Updated Log
            attemptedMove = true;
            moveDirection.normalize();
            
            // Use flight speed if flying, otherwise normal speed
            let baseSpeed = this.speedMultiplier ? this.moveSpeed * this.speedMultiplier : this.moveSpeed;
            if (this.isFlying) {
                baseSpeed = this.flightSpeed || 8;
            }
            const moveDistance = baseSpeed * deltaTime;

            let moveVector;
            if (isFirstPerson) {
                // FIRST-PERSON: Movement relative to current camera/player facing direction
                // Create rotation matrix from current yaw (horizontal rotation only)
                const yawRotation = new THREE.Quaternion();
                yawRotation.setFromAxisAngle(new THREE.Vector3(0, 1, 0), this.firstPersonRotation.yaw);

                // Transform movement direction by yaw rotation
                moveVector = moveDirection.clone().applyQuaternion(yawRotation).multiplyScalar(moveDistance);
            } else {
                // NORMAL MODE: World-relative movement
                moveVector = moveDirection.multiplyScalar(moveDistance);
            }

            const originalPosition = this.playerMesh.position.clone();
            const finalMove = new THREE.Vector3(0, 0, 0);

            if (this.isFlying) {
                // --- FLIGHT MODE: Free 3D movement with room boundaries ---
                // Allow movement in all three dimensions
                finalMove.copy(moveVector);
                
                // Check horizontal boundaries (X and Z)
                const potentialX = originalPosition.x + finalMove.x;
                const potentialZ = originalPosition.z + finalMove.z;
                
                // Check room boundaries from floorBounds if available
                if (this.floorBounds) {
                    if (potentialX < this.floorBounds.minX + 1 || potentialX > this.floorBounds.maxX - 1) {
                        finalMove.x = 0;
                    }
                    if (potentialZ < this.floorBounds.minZ + 1 || potentialZ > this.floorBounds.maxZ - 1) {
                        finalMove.z = 0;
                    }
                }
                
                // Calculate target flight height (maintain height above ground)
                if (this.groundY !== undefined && this.flightHeight) {
                    this.targetFlightY = this.groundY + this.flightHeight;
                } else {
                    this.targetFlightY = originalPosition.y + 1; // Default hover height
                }
                
                // Smooth transition to flight height
                const heightDiff = this.targetFlightY - originalPosition.y;
                const heightSpeed = 4.0; // units per second
                const maxHeightMove = heightSpeed * deltaTime;
                
                if (Math.abs(heightDiff) > 0.1) {
                    finalMove.y = Math.sign(heightDiff) * Math.min(Math.abs(heightDiff), maxHeightMove);
                } else {
                    finalMove.y = heightDiff; // Snap to target if very close
                }
                
                // Constrain flight height (don't go too high)
                const maxFlightHeight = this.groundY + (this.flightHeight || 3) + 2;
                if (originalPosition.y + finalMove.y > maxFlightHeight) {
                    finalMove.y = maxFlightHeight - originalPosition.y;
                }
                
                // Don't go below ground
                if (originalPosition.y + finalMove.y < this.groundY) {
                    finalMove.y = this.groundY - originalPosition.y;
                }
                
            } else {
                // --- NORMAL MODE: Ground-based movement with collision ---
                // Check X movement
                const potentialPositionX = originalPosition.clone();
                potentialPositionX.x += moveVector.x;
                if (!this._checkCollisionAtPosition(potentialPositionX)) {
                    finalMove.x = moveVector.x;
                }

                // Check Z movement
                const potentialPositionZ = originalPosition.clone();
                potentialPositionZ.z += moveVector.z;
                if (!this._checkCollisionAtPosition(potentialPositionZ)) {
                    finalMove.z = moveVector.z;
                }
            }

            // Apply the allowed movement
            this.playerMesh.position.add(finalMove);

            // Handle floor height adaptation (only for ground movement)
            if (!this.isFlying && finalMove.lengthSq() > 0) {
                this._adaptToFloorHeight();
            }

            this._updatePlayerBounds();

            // Get final effective movement after collision checks
            const actualMove = this.playerMesh.position.clone().sub(originalPosition);

            // --- Player Orientation (Based ONLY on Movement) ---
            // Calculate movement rotation but store it temporarily
            const movementMade = actualMove.clone().setY(0);
            if (movementMade.lengthSq() > 0.0001) {
                let angle = Math.atan2(-movementMade.x, -movementMade.z);
                const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
                let snappedAngle = Math.round(angle / fortyFiveDegrees) * fortyFiveDegrees;
                snappedAngle += Math.PI;
                movementBasedQuaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedAngle);
            }

            // --- Walk Animation ---
            this.animationTime += deltaTime;
            this.idleTime = 0; // Reset idle timer when moving
            const swingAngle = Math.sin(this.animationTime * this.walkCycleSpeed) * this.walkCycleMagnitude;
            if (this.leftLeg) this.leftLeg.rotation.x = swingAngle;
            if (this.rightLeg) this.rightLeg.rotation.x = -swingAngle;
            if (this.leftArm) this.leftArm.rotation.x = -swingAngle;
            // Skip right arm animation during weapon attacks
            if (this.rightArm && !this.isWeaponSystemAttacking()) this.rightArm.rotation.x = swingAngle;

        } else { // Not moving
            attemptedMove = false;
            // --- Idle/Reset Logic ---
            this.idleTime += deltaTime;
            if (this.idleTime <= 0.1) {
                this.animationTime += deltaTime;
                const idleAngle = Math.sin(this.animationTime * this.idleCycleSpeed) * this.idleCycleMagnitude;
                if (this.leftLeg) this.leftLeg.rotation.x = idleAngle;
                if (this.rightLeg) this.rightLeg.rotation.x = idleAngle;
                if (this.leftArm) this.leftArm.rotation.x = -idleAngle;
                // Skip right arm animation during weapon attacks
                if (this.rightArm && !this.isWeaponSystemAttacking()) this.rightArm.rotation.x = -idleAngle;
            } else {
                if (this.leftLeg) this.leftLeg.rotation.x = 0;
                if (this.rightLeg) this.rightLeg.rotation.x = 0;
                if (this.leftArm) this.leftArm.rotation.x = 0;
                // Skip right arm reset during weapon attacks
                if (this.rightArm && !this.isWeaponSystemAttacking()) this.rightArm.rotation.x = 0;
            }
        }
        // --- END of movement logic ---

        // --- First-Person Rotation Logic ---
        if (isFirstPerson) {
            // MOBILE FIX: Different rotation speeds for mobile vs desktop
            const mobileRotationSpeed = 2.5; // Increased for better mobile responsiveness
            const desktopRotationSpeed = 1.5; // Comfortable for desktop

            if (isMobile) {
                // MOBILE: Right joystick left/right controls camera rotation ONLY
                if (this.keys.shootLeft) {
                    this.firstPersonRotation.yaw += mobileRotationSpeed * deltaTime; // Turn left
                }
                if (this.keys.shootRight) {
                    this.firstPersonRotation.yaw -= mobileRotationSpeed * deltaTime; // Turn right
                }
            } else {
                // DESKTOP: Arrow keys left/right control camera rotation (alongside mouse controls)
                // Mouse controls are handled in _handleMouseMove for pointer lock mode
                if (this.keys.left) {
                    this.firstPersonRotation.yaw += desktopRotationSpeed * deltaTime; // Turn left
                }
                if (this.keys.right) {
                    this.firstPersonRotation.yaw -= desktopRotationSpeed * deltaTime; // Turn right
                }

                // DESKTOP: Arrow keys up/down control camera pitch (alongside mouse controls)
                // FIXED: Corrected pitch direction - up arrow should look up (negative pitch)
                if (this.keys.forward) {
                    this.firstPersonRotation.pitch += desktopRotationSpeed * deltaTime; // FIXED: Look up (up arrow = positive pitch change)
                    this.firstPersonRotation.pitch = Math.min(this.firstPersonRotation.maxPitch, this.firstPersonRotation.pitch);
                }
                if (this.keys.backward) {
                    this.firstPersonRotation.pitch -= desktopRotationSpeed * deltaTime; // FIXED: Look down (down arrow = negative pitch change)
                    this.firstPersonRotation.pitch = Math.max(this.firstPersonRotation.minPitch, this.firstPersonRotation.pitch);
                }
            }

            // Keep yaw in reasonable range
            while (this.firstPersonRotation.yaw > Math.PI) this.firstPersonRotation.yaw -= 2 * Math.PI;
            while (this.firstPersonRotation.yaw < -Math.PI) this.firstPersonRotation.yaw += 2 * Math.PI;
        }

        // --- Shooting Logic (Different for first-person vs normal mode) ---
        this.shootDirection.set(0, 0, 0);

        if (isFirstPerson) {
            // FIRST-PERSON MODE: Different shooting controls for mobile vs desktop

            if (isMobile) {
                // MOBILE: Right joystick has dual function
                // - Left/Right: Camera rotation (handled above in rotation logic)
                // - Up/Down: Shooting forward in camera direction
                if (this.keys.shootUp || this.keys.shootDown) {
                    // Shoot forward in the direction the camera is facing
                    this.shootDirection.z = -1; // Always shoot forward in first-person
                }
            } else {
                // DESKTOP: Spacebar for shooting in first-person mode
                // Arrow keys are now used only for camera rotation/pitch control
                if (this.keys.shoot) {
                    // Shoot forward in the direction the camera is facing
                    this.shootDirection.z = -1; // Always shoot forward in first-person
                }
            }
        } else {
            // NORMAL MODE: Different shooting controls for mobile vs desktop

            if (isMobile) {
                // MOBILE: Use 360-degree joystick vector for precise shooting
                if (this.isMobileShooting) {
                    // Convert joystick vector to 3D world coordinates
                    // Joystick X maps to world X, Joystick Y maps to world Z (inverted)
                    this.shootDirection.x = this.mobileShootVector.x;
                    this.shootDirection.z = -this.mobileShootVector.y; // Invert Y to Z mapping
                    this.shootDirection.y = 0; // Keep shooting horizontal

                    // Normalize the direction vector
                    this.shootDirection.normalize();

                    // Debug: Uncomment to see shooting direction
                    // console.log(`[PlayerController] 360° Mobile shooting: joystick(${this.mobileShootVector.x.toFixed(2)}, ${this.mobileShootVector.y.toFixed(2)}) -> world(${this.shootDirection.x.toFixed(2)}, ${this.shootDirection.z.toFixed(2)})`);
                }
            } else {
                // DESKTOP: Use arrow keys for shooting (forward/backward/left/right)
                if (this.keys.forward)    this.shootDirection.z = -1; // Up arrow = shoot up
                if (this.keys.backward)   this.shootDirection.z = 1;  // Down arrow = shoot down
                if (this.keys.left)       this.shootDirection.x = -1; // Left arrow = shoot left
                if (this.keys.right)      this.shootDirection.x = 1;  // Right arrow = shoot right
            }
        }

        // Update isShooting state - include mobile shooting
        this.isShooting = this.shootDirection.lengthSq() > 0 || this.isMobileShooting;

        // --- Apply Rotation Logic (Different for first-person vs normal mode) ---
        if (isFirstPerson) {
            // FIRST-PERSON MODE: Use smooth rotation from firstPersonRotation.yaw
            // Apply the current yaw rotation to the player mesh
            this.playerMesh.quaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), this.firstPersonRotation.yaw);

            // Transform shooting direction by current rotation for projectiles
            if (this.isShooting) {
                this.shootDirection.normalize();
                // Apply yaw rotation to shooting direction
                const yawRotation = new THREE.Quaternion();
                yawRotation.setFromAxisAngle(new THREE.Vector3(0, 1, 0), this.firstPersonRotation.yaw);
                this.shootDirection.applyQuaternion(yawRotation);
            }
        } else {
            // NORMAL MODE: Original rotation system
            if (this.isShooting) {
                // Calculate and apply shooting rotation if WASD is pressed
                this.shootDirection.normalize(); // Normalize here for angle calculation
                let shootAngle = Math.atan2(-this.shootDirection.x, -this.shootDirection.z);
                const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
                let snappedShootAngle = Math.round(shootAngle / fortyFiveDegrees) * fortyFiveDegrees;
                snappedShootAngle += Math.PI;
                this.playerMesh.quaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedShootAngle);
            } else if (attemptedMove) {
                // Apply movement rotation only if NOT shooting but moving
                const movementMade = moveDirection.clone().setY(0);
                if (movementMade.lengthSq() > 0.0001) {
                    let angle = Math.atan2(-movementMade.x, -movementMade.z);
                    const fortyFiveDegrees = Math.PI / 4; // Changed from 90 to 45 degrees
                    let snappedAngle = Math.round(angle / fortyFiveDegrees) * fortyFiveDegrees;
                    snappedAngle += Math.PI;
                    this.playerMesh.quaternion.setFromAxisAngle(new THREE.Vector3(0, 1, 0), snappedAngle);
                }
            }
        }
        // --- End Rotation Logic ---

        // --- Fire Shot/Attack based on Weapon System ---
        if (this.isShooting) {
            // Use weapon system if available
            if (this.weaponSystem) {
                const attackSuccess = this.weaponSystem.attack(this);
                // WeaponSystem now handles all cooldowns
            } else if (this.timeSinceLastShot >= this.shootCooldown) {
                // Fallback to original projectile system with cooldown
                this.timeSinceLastShot = 0;
                // shootDirection is already normalized if isShooting is true

                // --- Spawn Projectile ---
                if (this.dungeonHandler) {
                    const startPos = this.playerMesh.position.clone();
                    startPos.y += this.playerSize.y * 0.5;
                    // --- Pass current projectile stats ---
                    // Create a player projectile data object
                    const playerProjectileData = {
                        damage: this.projectileDamage,
                        range: this.projectileRange,
                        size: this.projectileSize
                    };

                    this.dungeonHandler.spawnProjectile(
                        startPos,
                        this.shootDirection,
                        playerProjectileData
                    );
                } else {
                    console.warn("DungeonHandler reference not found in PlayerController!");
                }
                // -------------------------
            }
        }
        // --- END Shooting Logic ---

        // Resolve ground collision AFTER movement and animation update
        this._resolveGroundCollision(); // Ensure Y pos is correct

        // Calculate allowed trail length based on health percentage
        const allowedTrailLength = Math.round((this.currentHealth / this.maxHealth) * BASE_MAX_TRAIL_LENGTH);
        // Calculate current fade duration based on health percentage
        const currentFadeDuration = MIN_TRAIL_FADE_DURATION + (BASE_TRAIL_FADE_DURATION - MIN_TRAIL_FADE_DURATION) * (this.currentHealth / this.maxHealth);

        // --- Update Foot Light Trail ---
        // Spawn new light if attemptedMove and interval passed (use attemptedMove)
        if (attemptedMove && this.trailSpawnTimer >= TRAIL_SPAWN_INTERVAL) {
            this.trailSpawnTimer = 0; // Reset timer

            let trailLight = null;
            // Check against calculated allowedTrailLength
            if (this.footTrailLights.length >= allowedTrailLength) {
                // Reuse the oldest light (only if allowed length > 0)
                if (allowedTrailLength > 0) {
                     trailLight = this.footTrailLights.shift();
                } else {
                    // If allowed length is 0, don't reuse or spawn
                    trailLight = null;
                }
            } else {
                // Create a new light (if allowed length > 0 and we haven't reached it)
                 if (allowedTrailLength > 0) { // Check allowed length, not current health
                    trailLight = new THREE.PointLight(TRAIL_LIGHT_COLOR, 0, TRAIL_LIGHT_RANGE);
                    this.scene.add(trailLight);
                 } else {
                     trailLight = null;
                 }
            }

            // If we have a light to position (either new or reused)
            if (trailLight) {
                // Position and activate the light
                trailLight.position.set(
                    this.playerMesh.position.x,
                    VOXEL_SIZE * 2, // Keep light near ground
                    this.playerMesh.position.z
                );
                trailLight.intensity = TRAIL_LIGHT_INTENSITY; // Set initial intensity
                trailLight.visible = true;
                trailLight.userData.spawnTime = time; // Store spawn time

                this.footTrailLights.push(trailLight); // Add to the end of the array
            }
        }

        // Update existing trail lights (fade)
        this.footTrailLights.forEach(light => {
            const age = time - light.userData.spawnTime;
            // Use currentFadeDuration for check
            if (age >= currentFadeDuration || currentFadeDuration <= MIN_TRAIL_FADE_DURATION) {
                light.intensity = 0;
                light.visible = false;
            } else {
                // Use currentFadeDuration for ratio calculation
                const fadeRatio = Math.max(0, 1.0 - (age / currentFadeDuration));
                light.intensity = TRAIL_LIGHT_INTENSITY * fadeRatio;
                light.visible = true; // Ensure visible while fading
            }
        });
        // --- End Update Foot Light Trail ---

        // --- Update Rotation Helpers ---
        this._updateRotationHelpers();
        // -----------------------------

        // Update weapon system
        if (this.weaponSystem) {
            this.weaponSystem.update(deltaTime);
        }

        // Update health bar animation
        if (this.healthBar) {
            this.healthBar.update(deltaTime);
        }

        // --- Update Invincibility Timer ---
        if (this.isInvincible) {
            this.invincibilityTimer -= deltaTime;
            if (this.invincibilityTimer <= 0) {
                this.isInvincible = false;
                this.invincibilityTimer = 0;
                this._endFlashEffect(); // End flashing when invincibility ends
                console.log("Player invincibility ended");
            }
        }

        // --- Handle Flight Descent ---
        if (this.descendingFromFlight && !this.isFlying) {
            const descentSpeed = 6.0; // units per second
            const groundDistance = this.playerMesh.position.y - this.groundY;
            
            if (groundDistance > 0.2) {
                // Still descending
                const descentMove = Math.min(descentSpeed * deltaTime, groundDistance);
                this.playerMesh.position.y -= descentMove;
            } else {
                // Reached ground
                this.playerMesh.position.y = this.groundY;
                this.descendingFromFlight = false;
                console.log('[PlayerController] 🪶 Landed safely on the ground');
            }
        }

        // --- Update Visual Feedback Effects ---
        this._updateFlashEffect(deltaTime);
        this._updateScreenShake(deltaTime);
    }

    // --- Health Methods ---
    takeDamage(amount, sourcePosition) {
        // Check if player is in god mode
        if (this.godMode) {
            console.log(`Player is in god mode, ignoring ${amount} damage`);
            return false; // No damage taken, player is not dead
        }
        
        // Check if player is currently invincible
        if (this.isInvincible) {
            console.log(`Player is invincible, ignoring ${amount} damage (${this.invincibilityTimer.toFixed(2)}s remaining)`);
            return false; // No damage taken, player is not dead
        }

        // Check for active crystal barrier
        if (this.activeCrystalBarrier && this.activeCrystalBarrier.userData.isActive && this.activeCrystalBarrier.userData.remainingCrystals > 0) {
            const barrierData = this.activeCrystalBarrier.userData;
            
            // Each hit breaks exactly one crystal, regardless of damage amount
            const crystalIndex = barrierData.activeCrystalIndices.shift(); // Remove next crystal
            barrierData.remainingCrystals--;
            
            // Get reference to CardSystem to call destroyCrystal method
            if (this.dungeonHandler && this.dungeonHandler.cardSystem) {
                this.dungeonHandler.cardSystem.destroyCrystal(this.activeCrystalBarrier, crystalIndex, 'damage');
            }
            
            console.log(`🔷 Crystal shield absorbed hit! Crystal destroyed. Remaining crystals: ${barrierData.remainingCrystals}/${barrierData.maxCrystals}`);
            
            // Check if all crystals are destroyed
            if (barrierData.remainingCrystals <= 0) {
                console.log(`🔷 All crystal shields destroyed! Protection ended.`);
                barrierData.isActive = false;
            }
            
            // Crystal completely absorbed the damage - no damage to player
            return false;
        }

        // Ensure amount is a number
        if (typeof amount !== 'number' || isNaN(amount)) {
            console.error(`Invalid damage amount: ${amount}, using default of 1`);
            amount = 1;
        }

        // Reduce actual health
        this.actualHealth = Math.max(0, this.actualHealth - amount);
        // Update displayed health
        this.currentHealth = Math.min(this.maxHealth, this.actualHealth);

        // Update health bar UI
        if (this.healthBar) {
            this.healthBar.updateHealth(this.currentHealth);
        }

        console.log(`Player took ${amount} damage. Actual health: ${this.actualHealth} (UI shows: ${this.currentHealth}/${this.maxHealth})`);

        // Activate invincibility period
        this.isInvincible = true;
        this.invincibilityTimer = this.invincibilityDuration;
        console.log(`Player is now invincible for ${this.invincibilityDuration} seconds`);

        // Trigger visual feedback effects
        this._startFlashEffect();
        this._startScreenShake();

        // Trigger music effect for player hit
        if (this.dungeonHandler && this.dungeonHandler.audioManager) {
            this.dungeonHandler.audioManager.onPlayerHit();
        }

        return this.actualHealth <= 0;
    }

    heal(amount) {
        // Increase actual health without limit
        this.actualHealth += amount;
        // Show maxHealth in the UI if we're above it
        this.currentHealth = Math.min(this.maxHealth, this.actualHealth);
        if (this.healthBar) {
            this.healthBar.updateHealth(this.currentHealth);
        }
        console.log(`Player healed ${amount}. Actual health: ${this.actualHealth} (UI shows: ${this.currentHealth}/${this.maxHealth})`);
    }

    // Example: Method to increase max health (like finding a heart container)
    increaseMaxHealth(amount) {
        this.maxHealth += amount;
        if (this.healthBar) {
            this.healthBar.dispose();
            this.healthBar = new SoulOrbHealthBar(this.camera, this.maxHealth);
            this.healthBar.updateHealth(this.currentHealth);
        }
        this.currentHealth += amount; // Usually also heal by the amount increased
        console.log(`Player max health increased by ${amount}. Current health: ${this.currentHealth}/${this.maxHealth}`);
        // Trail length will now scale based on the new max health
    }
    // ----------------------

    // --- Projectile Stat Upgrade Methods (Example) ---
    increaseProjectileDamage(amount) {
        this.projectileDamage += amount;
        console.log(`Projectile Damage increased to ${this.projectileDamage}`);
        // Add potential caps or diminishing returns later
    }

    increaseProjectileRange(amount) {
        this.projectileRange += amount;
        console.log(`Projectile Range increased to ${this.projectileRange.toFixed(1)}`);
    }

    increaseProjectileSize(amount) {
        // Add a max size maybe?
        const maxSize = 0.8;
        this.projectileSize = Math.min(maxSize, this.projectileSize + amount);
        console.log(`Projectile Size increased to ${this.projectileSize.toFixed(2)}`);
    }

    /**
     * Shoot projectile method for weapon system
     * Returns true if projectile was fired successfully
     */
    shootProjectile() {
        if (!this.dungeonHandler || !this.isShooting) {
            return false;
        }

        const startPos = this.playerMesh.position.clone();
        startPos.y += this.playerSize.y * 0.5;

        const playerProjectileData = {
            damage: this.projectileDamage,
            range: this.projectileRange,
            size: this.projectileSize
        };

        this.dungeonHandler.spawnProjectile(
            startPos,
            this.shootDirection,
            playerProjectileData
        );

        return true;
    }

    /**
     * Teleport the player to the boss
     * Creates a visual effect and moves the player to the boss's position
     */
    teleportToBoss() {
        console.log("[PlayerController] teleportToBoss method called");
        if (!this.dungeonHandler) {
            console.error("[PlayerController] Cannot teleport: dungeonHandler is null");
            return;
        }

        console.log("[PlayerController] DungeonHandler methods:", Object.keys(this.dungeonHandler));
        console.log("[PlayerController] findBossRoom exists:", typeof this.dungeonHandler.findBossRoom === 'function');
        console.log("[PlayerController] teleportToRoom exists:", typeof this.dungeonHandler.teleportToRoom === 'function');
        console.log("[PlayerController] currentRoom exists:", !!this.dungeonHandler.currentRoom);

        // Find the current room and check if it has a boss
        const currentRoom = this.dungeonHandler.currentRoom;
        console.log("[PlayerController] Current room:", currentRoom ? 'exists' : 'null');

        if (!currentRoom || !currentRoom.boss) {
            console.log("[PlayerController] Current room has no boss, searching for boss room");
            // Try to find a room with a boss
            const bossRoom = this.dungeonHandler.findBossRoom();
            console.log("[PlayerController] findBossRoom result:", bossRoom ? 'found boss room' : 'no boss room found');

            if (bossRoom) {
                // If we found a boss room but we're not in it, teleport to that room first
                if (bossRoom !== currentRoom) {
                    console.log("[PlayerController] Teleporting to boss room...");
                    try {
                        this.dungeonHandler.teleportToRoom(bossRoom);
                        console.log("[PlayerController] teleportToRoom completed");

                        // Wait a short time for the room to load before teleporting to the boss
                        console.log("[PlayerController] Setting timeout for _teleportToBossPosition");
                        setTimeout(() => {
                            console.log("[PlayerController] Timeout triggered, calling _teleportToBossPosition");
                            this._teleportToBossPosition();
                        }, 100);
                        return;
                    } catch (error) {
                        console.error("[PlayerController] Error in teleportToRoom:", error);
                    }
                } else {
                    console.log("[PlayerController] Already in boss room");
                }
            } else {
                console.log("[PlayerController] No boss found in the dungeon");
                return;
            }
        } else {
            console.log("[PlayerController] Current room has a boss");
        }

        // If we're already in the boss room, teleport directly to the boss
        console.log("[PlayerController] Calling _teleportToBossPosition directly");
        this._teleportToBossPosition();
    }

    /**
     * Internal method to handle the actual teleportation to the boss position
     * @private
     */
    _teleportToBossPosition() {
        console.log("[PlayerController] _teleportToBossPosition method called");

        // Find active bosses in the current room
        console.log("[PlayerController] DungeonHandler exists:", !!this.dungeonHandler);
        if (this.dungeonHandler) {
            console.log("[PlayerController] activeBosses exists:", !!this.dungeonHandler.activeBosses);
            if (this.dungeonHandler.activeBosses) {
                console.log("[PlayerController] activeBosses length:", this.dungeonHandler.activeBosses.length);
            }
        }

        if (!this.dungeonHandler || !this.dungeonHandler.activeBosses || this.dungeonHandler.activeBosses.length === 0) {
            console.log("[PlayerController] No active bosses found in the dungeon");

            // Try to find any enemies in the room that might be bosses
            if (this.dungeonHandler && this.dungeonHandler.activeEnemies && this.dungeonHandler.activeEnemies.length > 0) {
                console.log("[PlayerController] Found active enemies:", this.dungeonHandler.activeEnemies.length);

                // Look for enemies that might be bosses based on their userData
                const possibleBoss = this.dungeonHandler.activeEnemies.find(enemy => {
                    if (!enemy || !enemy.userData) return false;
                    return enemy.userData.aiType === 'boss' ||
                           enemy.userData.aiType === 'catacombs_overlord' ||
                           (enemy.userData.aiType && enemy.userData.aiType.includes('boss'));
                });

                if (possibleBoss) {
                    console.log("[PlayerController] Found a possible boss enemy");

                    // Get the boss position
                    const bossPosition = possibleBoss.position.clone();

                    // Add a small offset so we don't teleport directly on top of the boss
                    bossPosition.x -= 2; // Move slightly to the left of the boss

                    console.log(`[PlayerController] Boss found at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

                    // Create a teleport effect at the current position
                    this._createTeleportEffect(this.playerMesh.position.clone());

                    // Move the player to the boss position
                    this.playerMesh.position.copy(bossPosition);

                    // Create a teleport effect at the new position
                    this._createTeleportEffect(bossPosition);

                    console.log(`[PlayerController] Teleported to boss at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);
                    return;
                }
            }

            // If we still can't find a boss, just teleport to the center of the room
            if (this.dungeonHandler && this.dungeonHandler.currentRoom) {
                console.log("[PlayerController] Teleporting to center of room instead");
                const roomCenter = new THREE.Vector3(0, 0, 0);

                // Create a teleport effect at the current position
                this._createTeleportEffect(this.playerMesh.position.clone());

                // Move the player to the room center
                this.playerMesh.position.copy(roomCenter);

                // Create a teleport effect at the new position
                this._createTeleportEffect(roomCenter);

                console.log(`[PlayerController] Teleported to room center at position: ${roomCenter.x.toFixed(2)}, ${roomCenter.y.toFixed(2)}, ${roomCenter.z.toFixed(2)}`);
                return;
            }

            return;
        }

        // Get the first active boss position
        const boss = this.dungeonHandler.activeBosses[0];
        if (!boss) {
            console.log("[PlayerController] Boss reference is invalid");
            return;
        }

        console.log("[PlayerController] Boss object:", boss);
        console.log("[PlayerController] Boss position exists:", !!boss.position);

        // Get the boss position
        const bossPosition = boss.position.clone();

        // Add a small offset so we don't teleport directly on top of the boss
        bossPosition.x -= 2; // Move slightly to the left of the boss

        console.log(`[PlayerController] Boss found at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);

        // Create a teleport effect at the current position
        this._createTeleportEffect(this.playerMesh.position.clone());

        // Move the player to the boss position
        this.playerMesh.position.copy(bossPosition);

        // Create a teleport effect at the new position
        this._createTeleportEffect(bossPosition);

        console.log(`[PlayerController] Teleported to boss at position: ${bossPosition.x.toFixed(2)}, ${bossPosition.y.toFixed(2)}, ${bossPosition.z.toFixed(2)}`);
    }

    /**
     * Create a visual effect for teleportation
     * @param {THREE.Vector3} position - Position to create the effect
     * @private
     */
    _createTeleportEffect(position) {
        // Use the dungeonHandler's particle effect system if available
        if (this.dungeonHandler && this.dungeonHandler.createParticleEffect) {
            // Create a teleport effect using the particle system
            this.dungeonHandler.createParticleEffect(
                'teleport_flash', // Effect type
                position,          // Position
                1.0,               // Scale
                30                 // Particle count
            );
            return;
        }

        // Fallback if particle system is not available
        console.log(`[PlayerController] Created teleport effect at ${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}`);

        // Create a simple light flash if we can't create particles
        if (this.scene) {
            const flashLight = new THREE.PointLight(0x00ffff, 2.0, 5.0);
            flashLight.position.copy(position);
            this.scene.add(flashLight);

            // Remove the light after a short time
            setTimeout(() => {
                if (flashLight && this.scene) {
                    this.scene.remove(flashLight);
                }
            }, 300);
        }
    }
    // -------------------------------------------------

    updateCollisionObjects(newCollisionObjects) {
        this.collisionObjects = newCollisionObjects;
        // PERFORMANCE FIX: Clear cached floor meshes when collision objects change
        this._cachedFloorMeshes = null;
    }

    // --- Internal: Create Rotation Helpers ---
    _createRotationHelpers() {
        console.log("[PlayerController] _createRotationHelpers called.");
        this.clearRotationHelpers(); // Clear existing before creating new
        this.rotationHelpersGroup = new THREE.Group();
        this.rotationHelpersGroup.name = "PlayerRotationHelpers";

        // <<< Target GROUP names, not bone names >>>
        const groupNamesToMatch = ["Body", "leftArm", "rightArm", "leftLeg", "rightLeg"]; // Added Body
        const foundGroupNames = []; // To log names we actually find
        const allObjectInfo = [];

        console.log("[PlayerController] Traversing player mesh for TARGET GROUPS...");
        this.playerMesh.traverse((object) => {
             const info = `Name: '${object.name}', Type: ${object.constructor.name}`;
             allObjectInfo.push(info);

            if (object.isGroup && groupNamesToMatch.includes(object.name)) {
                 console.log(`    -> Found Target Group: '${object.name}'`);

                 // <<< FIND FIRST MESH INSIDE THE GROUP >>>
                 let targetMesh = null;
                 object.traverse((child) => {
                     if (!targetMesh && child.isMesh) { // Find the first Mesh
                         targetMesh = child;
                     }
                 });

                 if (targetMesh) {
                     console.log(`        -> Attaching helper to first Mesh inside Group: '${targetMesh.name || '(unnamed)'}'`);
                     // <<< Create helper attached to the MESH, not the group >>>
                     const helper = new THREE.BoxHelper(targetMesh, 0x00ffff); // Cyan
                     helper.material.linewidth = 2;
                     helper.matrixAutoUpdate = false;
                     this.rotationHelpersGroup.add(helper);
                     // <<< Store helper keyed by the PARENT GROUP's name for the update loop >>>
                     this.boneHelpers[object.name] = helper;
                     foundGroupNames.push(object.name); // Log the group name we matched
                 } else {
                      console.warn(`        -> No Mesh found inside Group: '${object.name}'. Cannot create helper.`);
                  }
                 // <<< END FIND FIRST MESH >>>
            }
        });

        console.log("[PlayerController] Finished traversing. All Objects found count:", allObjectInfo.length);
        console.log("[PlayerController] Target Groups found by name:", foundGroupNames);
        console.log("[PlayerController] Created bone helpers for matched groups:", Object.keys(this.boneHelpers));
        this.scene.add(this.rotationHelpersGroup);
        this._updateRotationHelpersVisibility(); // Set initial visibility
    }

    // --- Internal: Update Rotation Helper Transforms ---
    _updateRotationHelpers() {
        // No update needed if group isn't visible
        if (!this.rotationHelpersGroup || !this.rotationHelpersGroup.visible) return;

        // <<< RE-ADD Temporary variables for position/quaternion copy >>>
        const worldPosition = new THREE.Vector3();
        const worldQuaternion = new THREE.Quaternion();

        // Iterate through helpers keyed by GROUP name now
        for (const groupName in this.boneHelpers) {
            const helper = this.boneHelpers[groupName];
            // <<< Get the GROUP first to find the target MESH >>>
            const group = this.playerMesh.getObjectByName(groupName);
            let targetMesh = null;
            if (group) {
                 group.traverse((child) => {
                     if (!targetMesh && child.isMesh) { targetMesh = child; }
                 });
            }

            // <<< Check if target MESH and helper exist >>>
            if (targetMesh && helper && helper.isBoxHelper) {
                 // <<< Apply position/quaternion from the MESH >>>
                 targetMesh.updateWorldMatrix(true, false); // Update mesh's world matrix

                 targetMesh.getWorldPosition(worldPosition);
                 targetMesh.getWorldQuaternion(worldQuaternion);

                 helper.position.copy(worldPosition);
                 helper.quaternion.copy(worldQuaternion);
            } else {
                 // console.warn(`[PlayerController] Target Mesh for group '${groupName}' not found or helper invalid.`);
            }
        }
    }

    _updateAnimation(deltaTime) {
    }

    // <<< ADD Missing clearRotationHelpers Function >>>
    clearRotationHelpers() {
        console.log("[PlayerController] Clearing existing rotation helpers...");
        if (this.rotationHelpersGroup) {
            // Dispose and remove each helper individually
            Object.values(this.boneHelpers).forEach(helper => {
                if (helper) {
                    if (helper.geometry) helper.geometry.dispose();
                    if (helper.material) helper.material.dispose();
                    this.rotationHelpersGroup.remove(helper);
                }
            });
            // Remove the main group from the scene
            this.scene.remove(this.rotationHelpersGroup);
            console.log("[PlayerController] Removed rotation helpers group from scene.");
        } else {
            console.log("[PlayerController] No rotation helpers group found to clear.");
        }
        // Reset the state variables
        this.rotationHelpersGroup = null;
        this.boneHelpers = {};
    }
    // <<< END Added Function >>>

    // <<< RE-ADD Missing _updateRotationHelpersVisibility Function >>>
    _updateRotationHelpersVisibility() {
        if (this.rotationHelpersGroup) {
            this.rotationHelpersGroup.visible = this.isEspRotationViewActive;
             console.log(`[PlayerController] Setting rotation helpers group visibility to: ${this.rotationHelpersGroup.visible}`);
        } else {
             console.log("[PlayerController] Rotation helpers group not found, cannot set visibility.");
        }
    }
    // <<< END Re-added Function >>>

    /**
     * Add souls to the player
     * @param {number} amount - The amount of souls to add
     */
    addSouls(amount) {
        this.souls += amount;
        console.log(`Player now has ${this.souls} souls`);

        // Update UI if needed
        if (this.onSoulsChanged) {
            this.onSoulsChanged(this.souls);
        }
    }

    /**
     * Set a callback for when souls change
     * @param {function} callback - Function to call when souls change
     */
    setSoulsChangedCallback(callback) {
        this.onSoulsChanged = callback;
    }

    // --- Visual Feedback Methods ---

    /**
     * Initialize visual feedback systems
     * @private
     */
    _initializeVisualFeedback() {
        // Create white flash material for damage feedback
        this.flashMaterial = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.8
        });

        // Store original camera position
        if (this.camera) {
            this.originalCameraPosition.copy(this.camera.position);
        }
    }

    /**
     * Start the flash effect when player takes damage
     * @private
     */
    _startFlashEffect() {
        if (!this.playerMesh) return;

        this.isFlashing = true;
        this.flashTimer = 0;
        this.flashState = false;

        // Store original materials if not already stored
        if (this.originalMaterials.size === 0) {
            this.playerMesh.traverse((child) => {
                if (child.isMesh && child.material) {
                    this.originalMaterials.set(child.uuid, child.material);
                }
            });
        }

        console.log("Started player flash effect");
    }

    /**
     * Update the flash effect
     * @param {number} deltaTime - Time since last frame
     * @private
     */
    _updateFlashEffect(deltaTime) {
        if (!this.isFlashing || !this.playerMesh) return;

        this.flashTimer += deltaTime;
        const flashInterval = 1.0 / this.flashFrequency; // Time between flashes

        // Check if it's time to toggle flash state
        if (this.flashTimer >= flashInterval) {
            this.flashTimer = 0;
            this.flashState = !this.flashState;

            // Apply or remove flash material
            this.playerMesh.traverse((child) => {
                if (child.isMesh && child.material) {
                    if (this.flashState) {
                        // Apply white flash material
                        child.material = this.flashMaterial;
                    } else {
                        // Restore original material
                        const originalMaterial = this.originalMaterials.get(child.uuid);
                        if (originalMaterial) {
                            child.material = originalMaterial;
                        }
                    }
                }
            });
        }
    }

    /**
     * End the flash effect
     * @private
     */
    _endFlashEffect() {
        if (!this.isFlashing || !this.playerMesh) return;

        this.isFlashing = false;
        this.flashTimer = 0;
        this.flashState = false;

        // Restore all original materials
        this.playerMesh.traverse((child) => {
            if (child.isMesh && child.material) {
                const originalMaterial = this.originalMaterials.get(child.uuid);
                if (originalMaterial) {
                    child.material = originalMaterial;
                }
            }
        });

        console.log("Ended player flash effect");
    }

    /**
     * Start screen shake effect when player takes damage
     * @private
     */
    _startScreenShake() {
        if (!this.camera) return;

        this.isShaking = true;
        this.shakeTimer = 0;

        // Store the current camera position as the base position
        this.originalCameraPosition.copy(this.camera.position);

        console.log("Started screen shake effect");
    }

    /**
     * Update screen shake effect
     * @param {number} deltaTime - Time since last frame
     * @private
     */
    _updateScreenShake(deltaTime) {
        if (!this.isShaking || !this.camera) return;

        this.shakeTimer += deltaTime;

        if (this.shakeTimer >= this.shakeDuration) {
            // End shake effect
            this.isShaking = false;
            this.shakeTimer = 0;

            // Reset camera to original position (relative to any player movement)
            this.camera.position.copy(this.originalCameraPosition);
            this.shakeOffset.set(0, 0, 0);

            console.log("Ended screen shake effect");
            return;
        }

        // Calculate shake intensity that decreases over time
        const shakeProgress = this.shakeTimer / this.shakeDuration;
        const currentIntensity = this.shakeIntensity * (1.0 - shakeProgress); // Fade out over time

        // Generate random shake offset
        this.shakeOffset.set(
            (Math.random() - 0.5) * 2 * currentIntensity, // X axis
            0, // Don't shake Y axis to avoid disorientation
            (Math.random() - 0.5) * 2 * currentIntensity  // Z axis
        );

        // Apply shake offset to camera
        this.camera.position.copy(this.originalCameraPosition).add(this.shakeOffset);
    }

}

export default PlayerController;