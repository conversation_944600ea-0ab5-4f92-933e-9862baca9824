import * as THREE from 'three';
import { FontLoader } from 'three/addons/loaders/FontLoader.js'; // Needed if SceneManager loads font
import { STATE } from '../constants.js';
import HeroPageHandler from '../scenes/HeroPageHandler.js';
import CharacterCreationHandler from '../scenes/CharacterCreationHandler.js';
import DungeonHandler from '../scenes/DungeonHandler.js'; // Import DungeonHandler
import AudioManager from '../utils/audioManager.js'; // Import AudioManager
import FrustumCulling from '../utils/FrustumCulling.js'; // Import FrustumCulling
import { initCRTEffect } from '../effects/CRTIntegration.js'; // Import CRT effect
import { initHDRFramerateEffect } from '../effects/HDRFramerateIntegration.js'; // Import HDR and Framerate effect
import { initConsolePresetEffect } from '../effects/ConsolePresetIntegration.js'; // Import Console Preset effect
import { VoxelShadowManager } from '../effects/VoxelShadowManager.js'; // Import Voxel Shadow Manager

// --- NippleJS Constants ---
const JOYSTICK_THRESHOLD = 0.3;
const JOYSTICK_TAP_THRESHOLD = 0.1;
const JOYSTICK_VERTICAL_THRESHOLD = 0.5;
// --------------------------

class SceneManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.font = null; // Font can be loaded once here or by the first handler that needs it
        this.clock = new THREE.Clock();
        this.currentState = STATE.LOADING;
        this.fadeOverlay = document.getElementById('fade-overlay');
        this.activeSceneHandler = null;
        this.isFading = false;
        this.onFadeCompleteCallback = null;

        // HIGH PERFORMANCE: Maintain 30 FPS but prioritize performance allocation
        this.lastFrameTime = 0;
        this.targetFPS = 30; // Keep 30 FPS as requested
        this.minFrameTime = 1000 / this.targetFPS; // ~33.3ms per frame
        this.performanceMode = 'maximum'; // Track performance mode

        this.audioManager = new AudioManager(); // Instantiate AudioManager
        this.frustumCulling = new FrustumCulling(); // Instantiate FrustumCulling
        this.enableFrustumCulling = true; // Flag to enable/disable frustum culling

        // Global cursor management
        this.cursorVisible = false; // Track cursor visibility state
        this._initializeCursorManagement();

        // Camera fix tracking
        this._lastFrameFixedCamera = false;
        this._cameraState = {
            position: null,
            quaternion: null,
            up: null,
            zoom: null,
            isTopDown: false
        };

        // CRT effect will be initialized after renderer is created

        // --- NEW: Enhanced Mobile Input State ---
        this.joystickManagerLeft = null;
        this.joystickManagerRight = null;
        this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
        this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
        this.lastLeftStickY = 0;
        this.lastTapTimeRight = 0;
        this.tapDebounce = 200; // ms
        this.doubleTapWindow = 300; // ms for double-tap detection
        this.rightStickTapCount = 0;
        this.rightStickDoubleTapTimer = null;
        this.rightStickTapFlag = false;
        this.rightStickDoubleTapFlag = false;
        this.isMobileLandscape = false;
        this.isMobilePortrait = false;
        this.isMobileDevice = false;
        this.boundOrientationChange = this._handleOrientationChange.bind(this);
        this.lastProcessedStickY = 0; // Added for dialogue navigation processing

        // Portrait mode touch zones (invisible virtual joysticks)
        this.portraitTouchZones = {
            left: { active: false, startPos: null, currentPos: null },
            right: { active: false, startPos: null, currentPos: null }
        };
        this.boundTouchStart = this._handleTouchStart.bind(this);
        this.boundTouchMove = this._handleTouchMove.bind(this);
        this.boundTouchEnd = this._handleTouchEnd.bind(this);

        // Center touch zone for camera toggle
        this.centerTouchZone = {
            active: false,
            lastTapTime: 0,
            tapCount: 0
        };
        this.boundCenterTouchStart = this._handleCenterTouchStart.bind(this);
        this.boundCenterTouchEnd = this._handleCenterTouchEnd.bind(this);
        
        // Cutscene mode flag
        this.cutsceneMode = false;
        // -----------------------------------------------------

        this._initEngine();
        this._setupEventlisteners();
        this._requestMaximumPerformance();
    }

    _initEngine() {
        console.log("Initializing 3D engine...");
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000000);

        // Default camera, will be adjusted by handlers
        this.camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        this.camera.position.z = 5;

        const canvasElement = document.getElementById('webgl-canvas');
        if (!canvasElement) {
            console.error("CRITICAL: #webgl-canvas element not found!");
            // Maybe display an error message in the HTML
            const errorDiv = document.createElement('div');
            errorDiv.style.color = 'red';
            errorDiv.style.position = 'absolute';
            errorDiv.style.top = '50%';
            errorDiv.style.left = '50%';
            errorDiv.style.transform = 'translate(-50%, -50%)';
            errorDiv.textContent = 'Error: Cannot initialize 3D canvas.';
            document.body.appendChild(errorDiv);
            return; // Stop initialization
        }
        // HIGH PERFORMANCE: Enable maximum GPU utilization
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true, 
            canvas: canvasElement,
            powerPreference: "high-performance", // Request dedicated GPU
            precision: "highp", // Use high precision shaders
            alpha: false, // Disable alpha for better performance
            premultipliedAlpha: false,
            preserveDrawingBuffer: false,
            stencil: false // Disable stencil buffer if not needed
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        // UNLIMITED PIXEL RATIO: Use full device resolution for maximum quality
        this.renderer.setPixelRatio(window.devicePixelRatio);

        // Initialize Voxel Shadow Manager (replaces basic shadow mapping)
        try {
            this.voxelShadowManager = new VoxelShadowManager(this.renderer, this.scene, this.camera);
            console.log("✅ Voxel Shadow Manager initialized successfully.");
        } catch (error) {
            console.error("❌ Failed to initialize Voxel Shadow Manager:", error);
            this.voxelShadowManager = null;
        }

        // Set up tone mapping for better brightness
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 2.5; // Higher exposure for brighter overall lighting
        this.renderer.outputEncoding = THREE.sRGBEncoding;

        // Enhanced lighting for better brightness (can be adjusted by handlers)
        const ambientLight = new THREE.AmbientLight(0xffffff, 1.0); // Doubled ambient light intensity
        ambientLight.name = "globalAmbientLight";
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8); // Increased directional light intensity
        directionalLight.position.set(5, 10, 7.5);
        directionalLight.name = "globalDirectionalLight";
        this.scene.add(directionalLight);

        // Configure the main light for voxel shadows
        this.voxelShadowManager.setMainLight(directionalLight);

        console.log("Engine initialized.");

        // Initialize CRT effect
        this.crtEffect = initCRTEffect(this.renderer, this.scene, this.camera);
        console.log("CRT effect initialized.");

        // Initialize HDR and Framerate effect
        this.hdrFramerateEffect = initHDRFramerateEffect(this.renderer, this.scene, this.camera);
        console.log("HDR and Framerate effect initialized.");

        // Initialize Console Preset effect (with references to other effects for integration)
        this.consolePresetEffect = initConsolePresetEffect(
            this.renderer,
            this.scene,
            this.camera,
            this.hdrFramerateEffect,
            this.crtEffect
        );
        console.log("Console Preset effect initialized.");

        // --- Initialize Mobile Controls Check ---
        this._handleOrientationChange(); // Check initial orientation
        window.addEventListener('orientationchange', this.boundOrientationChange);
        // MOBILE FIX: Also listen for resize events for better orientation detection
        window.addEventListener('resize', () => {
            setTimeout(() => this._handleOrientationChange(), 100);
        });
        // ------------------------------------
    }

    _setupEventlisteners() {
        window.addEventListener('resize', this._onWindowResize.bind(this));
        // Global listeners? If most are scene-specific, add/remove them in handlers.
    }

    _onWindowResize() {
        if (!this.camera) return;
        const aspect = window.innerWidth / window.innerHeight;

        // Check if we're in DungeonHandler
        const isDungeonScene = this.activeSceneHandler instanceof DungeonHandler;

        // Save full camera state before resize (only for non-DungeonHandler scenes)
        if (!isDungeonScene) {
            this._saveCameraState();
        }

        if (this.camera.isPerspectiveCamera) {
            this.camera.aspect = aspect;
        } else if (this.camera.isOrthographicCamera) {
            // For DungeonHandler, let it handle its own camera resizing
            if (isDungeonScene) {
                // Don't modify the camera here, let DungeonHandler's onResize handle it
            } else {
                // For other scenes, use default frustum size
                const frustumSize = 15; // Default size
                this.camera.left = frustumSize * aspect / -2;
                this.camera.right = frustumSize * aspect / 2;
                this.camera.top = frustumSize / 2;
                this.camera.bottom = frustumSize / -2;
            }
        }

        // Restore zoom value from saved state (only for non-DungeonHandler scenes)
        if (!isDungeonScene && this._cameraState.zoom !== null) {
            this.camera.zoom = this._cameraState.zoom;
        }

        // Update projection matrix (DungeonHandler will do this in its onResize)
        if (!isDungeonScene) {
            this.camera.updateProjectionMatrix();
        }

        // Always resize the renderer
        this.renderer.setSize(window.innerWidth, window.innerHeight);

        // Update CRT effect resolution
        if (this.crtEffect && this.crtEffect.manager) {
            this.crtEffect.manager.updateResolution();
            if (!isDungeonScene && this.crtEffect.manager.crtPass && this._cameraState.zoom !== null) {
                this.crtEffect.manager.crtPass.uniforms.cameraZoom.value = this._cameraState.zoom;
            }
        }

        // Handlers might need to adjust layouts on resize too
        // This is especially important for DungeonHandler to handle its own camera
        if (this.activeSceneHandler && typeof this.activeSceneHandler.onResize === 'function') {
            this.activeSceneHandler.onResize();
        }
    }

    // --- Fade Transition ---
    startFade(onComplete) {
        if (this.isFading) return; // Prevent overlapping fades
        console.log("Starting fade out...");
        this.isFading = true;
        this.onFadeCompleteCallback = onComplete;

        if (!this.fadeOverlay) {
            console.error("Fade overlay element not found! Cannot fade.");
            this.isFading = false;
            if (this.onFadeCompleteCallback) this.onFadeCompleteCallback();
            return;
        }

        this.fadeOverlay.style.display = 'block';
        this.fadeOverlay.offsetHeight; // Trigger reflow
        this.fadeOverlay.classList.add('active');

        // Wait for fade-out CSS transition (defined in index.html)
        // Assumes 1s transition now - adjust if CSS changes
        setTimeout(() => {
            console.log("Fade out complete.");
            if (this.onFadeCompleteCallback) {
                this.onFadeCompleteCallback(); // Execute the state change / scene setup
                this.onFadeCompleteCallback = null; // Clear callback
            }
             // Start fade-in *after* the next scene is potentially set up
             this.endFade(); // <<< RESTORED: endFade is called by startFade again
        }, 200); // Match CSS transition duration (Reduced from 1s for instant transitions)
    }

    endFade() {
         if (!this.isFading || !this.fadeOverlay) return;
         console.log("Starting fade in...");

        // Wait a tiny bit for scene setup before fading in
        setTimeout(() => {
             this.fadeOverlay.classList.remove('active');
             // Wait for fade-in CSS transition before hiding overlay
             setTimeout(() => {
                 console.log("Fade in complete.");
                 this.fadeOverlay.style.display = 'none';
                 this.isFading = false;
             }, 200); // Match CSS transition duration (Reduced from 1s for instant transitions)
         }, 20); // Short delay before fade-in (Reduced from 50ms)
    }

    // --- Global Cursor Management ---
    _initializeCursorManagement() {
        // Hide cursor by default for immersive gaming experience
        this._hideCursor();
        console.log("[Cursor Management] Initialized - cursor hidden by default");
    }

    _showCursor() {
        if (!this.cursorVisible) {
            document.body.style.cursor = 'default';
            // Also set on canvas and document for better coverage
            if (this.renderer && this.renderer.domElement) {
                this.renderer.domElement.style.cursor = 'default';
            }
            document.documentElement.style.cursor = 'default';
            this.cursorVisible = true;
            console.log("[Cursor Management] Cursor shown");
        }
    }

    _hideCursor() {
        if (this.cursorVisible || this.cursorVisible === undefined) {
            document.body.style.cursor = 'none';
            // Also set on canvas and document for better coverage
            if (this.renderer && this.renderer.domElement) {
                this.renderer.domElement.style.cursor = 'none';
            }
            document.documentElement.style.cursor = 'none';
            this.cursorVisible = false;
            console.log("[Cursor Management] Cursor hidden");
        }
    }

    // Public methods for scene handlers to control cursor visibility
    showCursor() {
        this._showCursor();
    }

    hideCursor() {
        this._hideCursor();
    }

    // --- State Management ---
    async changeState(newState, params = {}) {
        console.log(`Changing state from ${this.currentState} to ${newState}`, params);

        // Store current camera state if it exists
        const prevCameraState = this.camera ? {
            zoom: this.camera.zoom,
            position: this.camera.position.clone(),
            quaternion: this.camera.quaternion.clone(),
            up: this.camera.up.clone(),
            isOrthographic: this.camera.isOrthographicCamera
        } : null;

        // Reset scene and camera
        if (this.activeSceneHandler) {
            await this.activeSceneHandler.cleanup();
        }

        this.currentState = newState;
        this.activeSceneHandler = null;

        // Reset camera state tracking
        this._lastFrameFixedCamera = false;
        this._cameraState.position = null;

        try {
            switch (newState) {
                case STATE.HERO_PAGE:
                    this.activeSceneHandler = new HeroPageHandler(this);
                    await this.activeSceneHandler.init(this.scene);
                    // Show cursor only on the logo/hero page
                    this._showCursor();
                    break;
                case STATE.CHARACTER_CREATION:
                    this.activeSceneHandler = new CharacterCreationHandler(this);
                    await this.activeSceneHandler.init(this.scene);
                    // Hide cursor for character creation scene
                    this._hideCursor();
                    break;
                case STATE.DUNGEON:
                    // For dungeon, position the camera farther from the floor
                    console.log(`[SceneManager] ===== CREATING DUNGEONHANDLER =====`);
                    console.log(`[SceneManager] About to instantiate DungeonHandler with params:`, params);
                    this.activeSceneHandler = new DungeonHandler(this, params); // Pass params
                    console.log(`[SceneManager] DungeonHandler created:`, !!this.activeSceneHandler);

                    // Always modify DungeonHandler camera settings before init to match user preferences
                    // Position the camera farther from the floor and point more downward
                    // This matches the camera style in the Flicker scene that the user prefers
                    this.activeSceneHandler.originalCameraOffset = new THREE.Vector3(0, 20, 25); // Increased Y and Z
                    this.activeSceneHandler.topDownCameraOffset = new THREE.Vector3(0, 35, 0); // Increased Y

                    await this.activeSceneHandler.init(this.scene);
                    // Hide cursor for dungeon gameplay
                    this._hideCursor();
                    break;
                case STATE.LOADING:
                default:
                    console.log(`Entering ${newState} state.`);
                    // Hide cursor for loading and other states
                    this._hideCursor();
                    // Optionally show a loading indicator in the DOM
                    break;
            }

            // If changing TO dungeon, ensure player controller exists
            if (newState === STATE.DUNGEON && !(this.activeSceneHandler instanceof DungeonHandler)) {
                 console.error("State changed to Dungeon, but handler is not DungeonHandler!");
                 // Handle error appropriately
            }

            console.log(`Successfully initialized state: ${newState}`);

            // MOBILE FIX: Ensure center touch zone is properly initialized when entering dungeon
            if (newState === STATE.DUNGEON && (this.isMobileLandscape || this.isMobilePortrait)) {
                // Small delay to ensure DOM is ready
                setTimeout(() => {
                    this._initCenterTouchZone();
                }, 100);
            }
        } catch (error) {
            console.error(`Failed to initialize state ${newState}:`, error);
            // Handle error, maybe revert to a safe state or show an error message
            this.changeState(STATE.LOADING); // Revert to loading on error
        }

        // Restore camera state if it existed and if the new handler didn't create its own camera
        if (prevCameraState && this.camera) {
            // Only restore zoom and position if the camera type hasn't changed
            // (e.g., don't restore orthographic settings to a perspective camera)
            if (prevCameraState.isOrthographic === this.camera.isOrthographicCamera) {
                this.camera.zoom = prevCameraState.zoom;
                this.camera.updateProjectionMatrix();

                // Update CRT effect with restored zoom
                try {
                    if (this.crtEffect && this.crtEffect.manager && this.crtEffect.manager.crtPass &&
                        this.crtEffect.manager.crtPass.uniforms &&
                        this.crtEffect.manager.crtPass.uniforms.cameraZoom) {
                        this.crtEffect.manager.crtPass.uniforms.cameraZoom.value = prevCameraState.zoom;
                    }
                } catch (error) {
                    console.warn('Error updating CRT effect zoom:', error);
                }

                // Only restore position if we're not in a scene that manages its own camera position
                if (!(this.activeSceneHandler instanceof DungeonHandler) &&
                    !(this.activeSceneHandler instanceof CharacterCreationHandler)) {
                    this.camera.position.copy(prevCameraState.position);
                    this.camera.quaternion.copy(prevCameraState.quaternion);
                    this.camera.up.copy(prevCameraState.up);
                }
            }
        }
    }

    // --- Main Loop ---
    animate() {
        // Use arrow function for binding `this` correctly
        requestAnimationFrame(() => this.animate());

        // CONTINUE WHEN TABBED OUT: Don't pause game when page is hidden
        // Skip the document.hidden check to keep game running in background

        // FPS Cap Implementation: Limit to 30 FPS for stability
        const currentTime = performance.now();
        const targetFrameTime = 1000 / 30; // 30 FPS = ~33.33ms per frame

        if (!this.lastFrameTime) {
            this.lastFrameTime = currentTime;
        }

        const elapsed = currentTime - this.lastFrameTime;
        if (elapsed < targetFrameTime) {
            return; // Skip this frame to maintain 30 FPS cap
        }

        this.lastFrameTime = currentTime;

        const deltaTime = this.clock.getDelta();

        // CRITICAL: Proper deltaTime scaling to maintain consistent game mechanics at 30 FPS
        // This ensures all physics, animations, and timers work exactly the same as 60 FPS
        const targetDeltaTime = 1 / 60; // Game was designed for 60 FPS (16.67ms per frame)

        // Scale deltaTime to compensate for 30 FPS frame rate
        // At 30 FPS, deltaTime will be ~33.33ms, but we want movement to feel like 16.67ms
        let scaledDeltaTime;
        if (deltaTime > targetDeltaTime * 3) {
            // Cap extremely large deltaTime values (e.g., when tab is inactive)
            scaledDeltaTime = targetDeltaTime * 2;
        } else {
            // Maintain the original deltaTime for proper movement scaling
            // This preserves the original movement speed and responsiveness
            scaledDeltaTime = deltaTime;
        }

        // --- Process Mobile Input FIRST ---
        this._processMobileInput(scaledDeltaTime);
        // ---------------------------------

        // Update frustum for culling before scene updates
        if (this.enableFrustumCulling && this.camera) {
            this.frustumCulling.updateFrustum(this.camera);

            // MASSIVE PERFORMANCE GAIN: Cull room objects that are outside camera view
            // Only update/render objects that are visible to the camera
            if (this.activeSceneHandler && this.activeSceneHandler.currentRoomObjects) {
                if (!this.roomObjectCullTimer) this.roomObjectCullTimer = 0;
                this.roomObjectCullTimer += scaledDeltaTime;

                // Update culling every 100ms instead of every frame for better performance
                if (this.roomObjectCullTimer >= 0.1) {
                    this.roomObjectCullTimer = 0;

                    // Cull current room objects
                    this.activeSceneHandler.currentRoomObjects.forEach(obj => {
                        if (obj && obj.visible !== undefined) {
                            const isVisible = this.frustumCulling.isVisible(obj, 2.0); // 2 unit expansion for safety
                            obj.visible = isVisible;

                            // Also disable collision detection for invisible objects
                            if (obj.userData) {
                                obj.userData.culled = !isVisible;
                            }
                        }
                    });
                }
            }
        }

        // Update voxel shadow system
        if (this.voxelShadowManager) {
            this.voxelShadowManager.update();
        }

        // Update logic for the currently active scene handler
        if (this.activeSceneHandler && typeof this.activeSceneHandler.update === 'function') {
            this.activeSceneHandler.update(scaledDeltaTime, this.scene, this.camera);
        }

        // Global animations or updates can go here (e.g., stats display)

        if (this.renderer && this.scene && this.camera) {
             try {
                 // Store camera properties before effects
                 const isTopDownView = this.activeSceneHandler?.isTopDownView;
                 const isDungeonScene = this.activeSceneHandler instanceof DungeonHandler;

                 // Only save camera state if we're not in DungeonHandler
                 // Let DungeonHandler manage its own camera
                 if (!isDungeonScene) {
                     this._saveCameraState();
                 }

                 // Get current time for effects
                 const currentTime = performance.now();

                 // First check if we should render this frame based on game FPS limiting
                 let shouldRenderGameFrame = true;
                 if (this.consolePresetEffect) {
                     shouldRenderGameFrame = this.consolePresetEffect.shouldRenderGameFrame(currentTime);
                 }

                 // Only proceed if we should render this frame based on game FPS
                 if (shouldRenderGameFrame) {
                     // Check if 8-bit code effect is enabled
                     const eightBitCodeEnabled = this.consolePresetEffect && this.consolePresetEffect.eightBitCodeEnabled;

                     // Coordinate HDR and CRT effects
                     if (this.hdrFramerateEffect && this.crtEffect) {
                         // Both effects are available - integrate them

                         // First check if we should render this frame (framerate limiting)
                         let shouldRender = this.hdrFramerateEffect.shouldRenderFrame(currentTime);

                         if (shouldRender) {
                             // Apply HDR settings first (tone mapping, etc.)
                             this.hdrFramerateEffect.applyHDRSettings();

                             // Get current render target
                             const currentRenderTarget = this.renderer.getRenderTarget();

                             // Render scene to a temporary target using CRT effect
                             // This will apply CRT distortion, scanlines, etc.
                             this.crtEffect.update();

                             // Apply bit depth post-processing after CRT
                             // This will simulate lower bit depths and apply dithering
                             this.hdrFramerateEffect.applyBitDepthPostProcessing();

                             // Apply 8-bit code effect if enabled
                             if (eightBitCodeEnabled) {
                                 this.consolePresetEffect.update(currentTime);
                             }

                             // Restore original render target
                             this.renderer.setRenderTarget(currentRenderTarget);
                         }
                     }
                     // HDR effect and 8-bit code effect available
                     else if (this.hdrFramerateEffect && eightBitCodeEnabled) {
                         // First apply HDR effect
                         let shouldRender = this.hdrFramerateEffect.shouldRenderFrame(currentTime);

                         if (shouldRender) {
                             this.hdrFramerateEffect.update(currentTime);

                             // Then apply 8-bit code effect
                             this.consolePresetEffect.update(currentTime);
                         }
                     }
                     // Only HDR effect available
                     else if (this.hdrFramerateEffect) {
                         // HDR effect handles framerate limiting and rendering
                         this.hdrFramerateEffect.update(currentTime);
                     }
                     // Only CRT effect and 8-bit code effect available
                     else if (this.crtEffect && eightBitCodeEnabled) {
                         // First apply CRT effect
                         this.crtEffect.update();

                         // Then apply 8-bit code effect
                         this.consolePresetEffect.update(currentTime);
                     }
                     // Only CRT effect available
                     else if (this.crtEffect) {
                         // Update CRT effect (this handles rendering internally)
                         this.crtEffect.update();
                     }
                     // Only 8-bit code effect available
                     else if (eightBitCodeEnabled) {
                         // Render scene normally first
                         this.renderer.render(this.scene, this.camera);

                         // Then apply 8-bit code effect
                         this.consolePresetEffect.update(currentTime);
                     }
                     // No effects available
                     else {
                         // Fallback to normal rendering
                         this.renderer.render(this.scene, this.camera);
                     }
                 }

                 // Only restore camera state if we're not in DungeonHandler
                 // Let DungeonHandler manage its own camera
                 if (!isDungeonScene) {
                     this._restoreCameraState();
                 }

                 // Track if we fixed the camera this frame
                 this._lastFrameFixedCamera = isTopDownView && !isDungeonScene;
             } catch (error) {
                 console.error("Error during rendering:", error);
                 // Potentially stop the loop or try to recover
                 // For now, just log it to avoid crashing the tab entirely
             }
        } else {
             console.warn("Skipping render: Renderer, Scene, or Camera not ready.");
        }
    }



    async start() {
        console.log("Starting SceneManager...");
        // Optionally load global assets here (e.g., font) if not handled by specific scenes
        // try {
        //     const loader = new FontLoader();
        //     this.font = await loader.loadAsync('assets/fonts/helvetiker_bold.typeface.json');
        //     console.log("Global font loaded by SceneManager.");
        // } catch (error) {
        //     console.error("Failed to load global font:", error);
        //     // Handle font loading failure
        //     return; // Don't start if essential assets fail
        // }

        // Start the animation loop
        this.animate();

        // Initial state transition (after engine init and loop start)
        await this.changeState(STATE.HERO_PAGE); // Start with the hero page
    }

    // --- NEW: Joystick Management Methods (Moved from PlayerController) ---
     _initJoysticks() {
        this._destroyJoysticks(); // Ensure clean state

        // Check if nipplejs is loaded
        if (typeof nipplejs === 'undefined') {
             console.error("nipplejs library not loaded! Cannot initialize joysticks.");
             return;
        }

        const commonOptions = {
            mode: 'static',
            size: 80, // MOBILE FIX: Smaller size to fit better in grey circles
            threshold: 0.1, // Lower threshold for sensitivity
            color: 'grey',
            fadeTime: 150,
            dynamicPage: true,
            restJoystick: true,
            restOpacity: 0.8, // MOBILE FIX: More visible when at rest
            lockX: false,
            lockY: false,
            // MOBILE FIX: Force nipples to start in center position
            restPosition: { x: 0, y: 0 }
        };

        try {
            const zoneLeft = document.getElementById('joystick-left');
            if (zoneLeft) {
                // MOBILE FIX: Position joystick in center of the zone
                this.joystickManagerLeft = nipplejs.create({
                    ...commonOptions,
                    zone: zoneLeft,
                    position: { left: '50%', top: '50%' } // Center within the zone
                });
                this._bindJoystickEvents(this.joystickManagerLeft, 'left');
                console.log("[SceneManager] Left joystick initialized.");
            } else {
                console.warn("[SceneManager] Left joystick zone not found!");
            }

            const zoneRight = document.getElementById('joystick-right');
            if (zoneRight) {
                // MOBILE FIX: Position joystick in center of the zone
                this.joystickManagerRight = nipplejs.create({
                    ...commonOptions,
                    zone: zoneRight,
                    position: { left: '50%', top: '50%' } // Center within the zone
                });
                this._bindJoystickEvents(this.joystickManagerRight, 'right');
                console.log("[SceneManager] Right joystick initialized.");
            } else {
                console.warn("[SceneManager] Right joystick zone not found!");
            }
        } catch (error) {
            console.error("[SceneManager] Error initializing nipplejs:", error);
            this._destroyJoysticks();
        }
    }

    _destroyJoysticks() {
        if (this.joystickManagerLeft) {
            this.joystickManagerLeft.destroy();
            this.joystickManagerLeft = null;
            console.log("[SceneManager] Left joystick destroyed.");
        }
        if (this.joystickManagerRight) {
            this.joystickManagerRight.destroy();
            this.joystickManagerRight = null;
            console.log("[SceneManager] Right joystick destroyed.");
        }
        this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
        this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
    }

    _bindJoystickEvents(manager, side) {
        manager.on('start', (evt, nipple) => {
            // MOBILE FIX: Prevent browser zoom on joystick interaction
            if (evt && evt.preventDefault) {
                evt.preventDefault();
                evt.stopPropagation();
            }

            if (side === 'left') this.leftStickData.active = true;
            if (side === 'right') this.rightStickData.active = true;
            this.lastLeftStickY = 0;
        });

        manager.on('move', (evt, nipple) => {
            // MOBILE FIX: Prevent browser zoom on joystick interaction
            if (evt && evt.preventDefault) {
                evt.preventDefault();
                evt.stopPropagation();
            }

            const vector = nipple.vector || { x: 0, y: 0 }; // Ensure vector exists
            if (side === 'left') {
                this.leftStickData.vector = vector;
                this.lastLeftStickY = vector.y; // Store Y for processing in update
            } else if (side === 'right') {
                this.rightStickData.vector = vector;
            }
        });

        manager.on('end', (evt, nipple) => {
             // MOBILE FIX: Prevent browser zoom on joystick interaction
             if (evt && evt.preventDefault) {
                 evt.preventDefault();
                 evt.stopPropagation();
             }

             const distance = nipple.distance || 0; // Get distance before resetting
             // Reset stick data immediately
             if (side === 'left') {
                 this.leftStickData.active = false;
                 this.leftStickData.vector = { x: 0, y: 0 };
                 this.lastLeftStickY = 0;
             } else if (side === 'right') {
                 this.rightStickData.active = false;
                 this.rightStickData.vector = { x: 0, y: 0 };

                 // Process Tap based on the distance *before* reset
                 if (distance <= JOYSTICK_TAP_THRESHOLD) {
                     const now = performance.now();
                     if (now - this.lastTapTimeRight > this.tapDebounce) {
                         console.log(`[SceneManager] Right joystick tap registered (dist: ${distance.toFixed(2)})`);

                         // Handle double-tap detection for secret rooms
                         const timeSinceLastTap = now - this.lastTapTimeRight;
                         if (timeSinceLastTap < this.doubleTapWindow && this.rightStickTapCount > 0) {
                             // Double-tap detected
                             this.rightStickTapCount = 0;
                             this.rightStickDoubleTapFlag = true;
                             console.log("[SceneManager] Right joystick DOUBLE-TAP detected for secret room");

                             // Clear any existing timer
                             if (this.rightStickDoubleTapTimer) {
                                 clearTimeout(this.rightStickDoubleTapTimer);
                                 this.rightStickDoubleTapTimer = null;
                             }
                         } else {
                             // First tap or too much time passed
                             this.rightStickTapCount = 1;
                             this.rightStickTapFlag = true; // Still set single tap flag

                             // Set timer to reset tap count if no second tap comes
                             if (this.rightStickDoubleTapTimer) {
                                 clearTimeout(this.rightStickDoubleTapTimer);
                             }
                             this.rightStickDoubleTapTimer = setTimeout(() => {
                                 this.rightStickTapCount = 0;
                                 this.rightStickDoubleTapTimer = null;
                             }, this.doubleTapWindow);
                         }

                         this.lastTapTimeRight = now;
                     } else {
                          console.log("[SceneManager] Right joystick tap ignored (debounce).");
                     }
                 }
             }
        });
    }

    _handleOrientationChange() {
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        const isLandscape = window.matchMedia("(orientation: landscape)").matches;
        const isPortrait = window.matchMedia("(orientation: portrait)").matches;

        this.isMobileDevice = likelyMobile;
        this.isMobileLandscape = likelyMobile && isLandscape;
        this.isMobilePortrait = likelyMobile && isPortrait;

        console.log(`[SceneManager] Orientation changed. Mobile: ${this.isMobileDevice}, Landscape: ${this.isMobileLandscape}, Portrait: ${this.isMobilePortrait}`);

        if (this.isMobileLandscape) {
            this._initJoysticks();
            this._destroyPortraitTouchZones();
            this._initCenterTouchZone();
        } else if (this.isMobilePortrait) {
            this._destroyJoysticks();
            this._initPortraitTouchZones();
            this._initCenterTouchZone();
        } else {
            this._destroyJoysticks();
            this._destroyPortraitTouchZones();
            this._destroyCenterTouchZone();
        }
    }

     // --- NEW: Portrait Touch Zone Management ---
     _initPortraitTouchZones() {
         console.log("[SceneManager] Initializing portrait touch zones");

         // Add touch event listeners to portrait touch zones
         const leftZone = document.getElementById('portrait-touch-left');
         const rightZone = document.getElementById('portrait-touch-right');

         console.log("[SceneManager] Portrait zones found:", { leftZone: !!leftZone, rightZone: !!rightZone });

         if (leftZone && rightZone) {
             // MOBILE FIX: Remove existing listeners first
             leftZone.removeEventListener('touchstart', this.boundTouchStart);
             leftZone.removeEventListener('touchmove', this.boundTouchMove);
             leftZone.removeEventListener('touchend', this.boundTouchEnd);

             rightZone.removeEventListener('touchstart', this.boundTouchStart);
             rightZone.removeEventListener('touchmove', this.boundTouchMove);
             rightZone.removeEventListener('touchend', this.boundTouchEnd);

             // Add fresh listeners
             leftZone.addEventListener('touchstart', this.boundTouchStart, { passive: false });
             leftZone.addEventListener('touchmove', this.boundTouchMove, { passive: false });
             leftZone.addEventListener('touchend', this.boundTouchEnd, { passive: false });

             rightZone.addEventListener('touchstart', this.boundTouchStart, { passive: false });
             rightZone.addEventListener('touchmove', this.boundTouchMove, { passive: false });
             rightZone.addEventListener('touchend', this.boundTouchEnd, { passive: false });

             // MOBILE FIX: Make zones visible for debugging (temporarily)
             if (window.location.search.includes('debug')) {
                 leftZone.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
                 rightZone.style.backgroundColor = 'rgba(0, 0, 255, 0.2)';
             }

             console.log("[SceneManager] Portrait touch zones initialized successfully");
         } else {
             console.error("[SceneManager] Portrait touch zone elements not found!", { leftZone, rightZone });
         }
     }

     _destroyPortraitTouchZones() {
         console.log("[SceneManager] Destroying portrait touch zones");

         const leftZone = document.getElementById('portrait-touch-left');
         const rightZone = document.getElementById('portrait-touch-right');

         if (leftZone && rightZone) {
             leftZone.removeEventListener('touchstart', this.boundTouchStart);
             leftZone.removeEventListener('touchmove', this.boundTouchMove);
             leftZone.removeEventListener('touchend', this.boundTouchEnd);

             rightZone.removeEventListener('touchstart', this.boundTouchStart);
             rightZone.removeEventListener('touchmove', this.boundTouchMove);
             rightZone.removeEventListener('touchend', this.boundTouchEnd);
         }

         // Reset touch zone states
         this.portraitTouchZones.left.active = false;
         this.portraitTouchZones.right.active = false;
         this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
         this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
     }

     // --- NEW: Process Mobile Input ---
     _processMobileInput(deltaTime) {
         if (!this.isMobileLandscape && !this.isMobilePortrait) return; // Only process if mobile controls are active
         if (this.cutsceneMode) return; // Disable mobile input during cutscenes

         // Process mobile input without excessive logging

         // --- Handle Right Stick Tap Actions (Non-Dungeon States Only) ---
         if (this.rightStickTapFlag && this.currentState !== STATE.DUNGEON) {
             this.rightStickTapFlag = false; // Consume the flag

             if (this.currentState === STATE.HERO_PAGE && this.activeSceneHandler instanceof HeroPageHandler) {
                 console.log("[SceneManager] Processing Right Tap -> HeroPage Start");
                 this.activeSceneHandler.triggerStartGame();
             } else if (this.currentState === STATE.CHARACTER_CREATION && this.activeSceneHandler instanceof CharacterCreationHandler) {
                 console.log("[SceneManager] Processing Right Tap -> Dialogue Confirmation");
                 this.activeSceneHandler.confirmDialogue();
             } else {
                  console.log("[SceneManager] Right Tap ignored (state not Hero/Creation).");
             }
         }
         // --- End Right Stick Tap ---

         // --- Handle Left Stick Dialogue Navigation ---
         if (this.currentState === STATE.CHARACTER_CREATION && this.activeSceneHandler instanceof CharacterCreationHandler) {
            // Check both vertical and horizontal movement thresholds
            if (this.leftStickData.active) { // Check only if stick is active
                const currentY = this.leftStickData.vector.y;
                const currentX = this.leftStickData.vector.x;

                // Initialize lastProcessedStickX if not exists
                if (this.lastProcessedStickX === undefined) {
                    this.lastProcessedStickX = 0;
                }

                // Check vertical movement (up/down)
                if (currentY > JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickY <= JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick UP detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('up');
                    this.lastProcessedStickY = currentY; // Mark as processed
                }
                else if (currentY < -JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickY >= -JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick DOWN detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('down');
                    this.lastProcessedStickY = currentY; // Mark as processed
                }
                else if (Math.abs(currentY) < JOYSTICK_VERTICAL_THRESHOLD) {
                     this.lastProcessedStickY = currentY;
                }

                // Check horizontal movement (left/right)
                if (currentX > JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickX <= JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick RIGHT detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('right');
                    this.lastProcessedStickX = currentX; // Mark as processed
                }
                else if (currentX < -JOYSTICK_VERTICAL_THRESHOLD && this.lastProcessedStickX >= -JOYSTICK_VERTICAL_THRESHOLD) {
                    console.log("[SceneManager] Left Stick LEFT detected -> Navigating Dialogue");
                    this.activeSceneHandler.navigateDialogue('left');
                    this.lastProcessedStickX = currentX; // Mark as processed
                }
                else if (Math.abs(currentX) < JOYSTICK_VERTICAL_THRESHOLD) {
                     this.lastProcessedStickX = currentX;
                }
            } else {
                 this.lastProcessedStickY = 0; // Reset if stick is not active
                 this.lastProcessedStickX = 0; // Reset if stick is not active
            }
         } else {
             // Reset both axes if not in character creation state
             this.lastProcessedStickY = 0;
             this.lastProcessedStickX = 0;
         }
         // --- End Left Stick Dialogue Nav ---

         // --- Handle Right Stick Double-Tap for Secret Rooms in Dungeon ---
         if (this.rightStickDoubleTapFlag && this.currentState === STATE.DUNGEON && this.activeSceneHandler instanceof DungeonHandler) {
             this.rightStickDoubleTapFlag = false; // Consume the flag

             // Check if secret room manager exists and trigger secret reveal
             const dungeonHandler = this.activeSceneHandler;
             if (dungeonHandler.secretRoomManager && typeof dungeonHandler.secretRoomManager.handleSecretRevealCommand === 'function') {
                 console.log("[SceneManager] Right joystick double-tap -> Triggering secret room reveal");
                 dungeonHandler.secretRoomManager.handleSecretRevealCommand();
             } else {
                 console.log("[SceneManager] Right joystick double-tap detected but no secret room manager available");
             }
         }

         // --- Pass Input to PlayerController if in Dungeon State ---
         if (this.currentState === STATE.DUNGEON && this.activeSceneHandler instanceof DungeonHandler) {
             // Check if any dialogue is active - if so, don't consume the tap flag here
             const dungeonHandler = this.activeSceneHandler;
             const isDungeonDialogueActive = dungeonHandler.mobileInputActive;
             const isChestDialogueActive = dungeonHandler.chestInteractionSystem?.isMobileDialogueActive;
             const isAnyDialogueActive = isDungeonDialogueActive || isChestDialogueActive;

             const playerController = dungeonHandler.playerController;
             if (playerController && typeof playerController.handleMobileInput === 'function') {
                 // Include right stick tap information for dungeon interactions
                 const rightStickDataWithTap = {
                     ...this.rightStickData,
                     tapped: this.rightStickTapFlag && !isAnyDialogueActive // Only pass tap if no dialogue active
                 };
                 playerController.handleMobileInput(this.leftStickData, rightStickDataWithTap);

                 // Only consume the tap flag if no dialogue is active
                 if (this.rightStickTapFlag && !isAnyDialogueActive) {
                     this.rightStickTapFlag = false;
                     console.log("[SceneManager] Right stick tap passed to PlayerController for dungeon interaction");
                 }
             } else {
                 // console.warn("PlayerController or handleMobileInput not found in DungeonHandler!");
             }
         }
         // --- End Pass Input to Player ---
     }

     // --- NEW: Portrait Touch Event Handlers ---
     _handleTouchStart(event) {
         // MOBILE FIX: Prevent all browser defaults
         event.preventDefault();
         event.stopPropagation();

         const touch = event.touches[0];
         if (!touch) return;

         const target = event.target;
         const isLeftZone = target.id === 'portrait-touch-left';
         const isRightZone = target.id === 'portrait-touch-right';

         console.log(`[SceneManager] Touch start detected on ${target.id}`);

         if (isLeftZone) {
             this.portraitTouchZones.left.active = true;
             this.portraitTouchZones.left.startPos = { x: touch.clientX, y: touch.clientY };
             this.portraitTouchZones.left.currentPos = { x: touch.clientX, y: touch.clientY };
             console.log("[SceneManager] Left touch zone activated", this.portraitTouchZones.left.startPos);
         } else if (isRightZone) {
             this.portraitTouchZones.right.active = true;
             this.portraitTouchZones.right.startPos = { x: touch.clientX, y: touch.clientY };
             this.portraitTouchZones.right.currentPos = { x: touch.clientX, y: touch.clientY };

             // Right touch zone activated (no longer handles camera toggle)

             console.log("[SceneManager] Right touch zone activated", this.portraitTouchZones.right.startPos);
         }
     }

     _handleTouchMove(event) {
         // MOBILE FIX: Prevent all browser defaults
         event.preventDefault();
         event.stopPropagation();

         const touch = event.touches[0];
         if (!touch) return;

         const target = event.target;
         const isLeftZone = target.id === 'portrait-touch-left';
         const isRightZone = target.id === 'portrait-touch-right';

         if (isLeftZone && this.portraitTouchZones.left.active) {
             this.portraitTouchZones.left.currentPos = { x: touch.clientX, y: touch.clientY };
             this._updatePortraitStickData('left');
             console.log("[SceneManager] Left touch move", this.leftStickData);
         } else if (isRightZone && this.portraitTouchZones.right.active) {
             this.portraitTouchZones.right.currentPos = { x: touch.clientX, y: touch.clientY };
             this._updatePortraitStickData('right');
             console.log("[SceneManager] Right touch move", this.rightStickData);
         }
     }

     _handleTouchEnd(event) {
         // MOBILE FIX: Prevent all browser defaults
         event.preventDefault();
         event.stopPropagation();

         const target = event.target;
         const isLeftZone = target.id === 'portrait-touch-left';
         const isRightZone = target.id === 'portrait-touch-right';

         console.log(`[SceneManager] Touch end detected on ${target.id}`);

         if (isLeftZone) {
             this.portraitTouchZones.left.active = false;
             this.portraitTouchZones.left.startPos = null;
             this.portraitTouchZones.left.currentPos = null;
             this.leftStickData = { active: false, vector: { x: 0, y: 0 } };
             console.log("[SceneManager] Left touch zone deactivated");
         } else if (isRightZone) {
             this.portraitTouchZones.right.active = false;
             this.portraitTouchZones.right.startPos = null;
             this.portraitTouchZones.right.currentPos = null;
             this.rightStickData = { active: false, vector: { x: 0, y: 0 } };
             console.log("[SceneManager] Right touch zone deactivated");
         }
     }

     _updatePortraitStickData(zone) {
         const touchZone = this.portraitTouchZones[zone];
         if (!touchZone.active || !touchZone.startPos || !touchZone.currentPos) return;

         const deltaX = touchZone.currentPos.x - touchZone.startPos.x;
         const deltaY = touchZone.currentPos.y - touchZone.startPos.y;

         // MOBILE FIX: Direct coordinate conversion without circular transformation
         const maxDistance = 100; // Maximum distance for full deflection

         // Clamp the deltas to maxDistance and normalize directly
         const clampedX = Math.max(-maxDistance, Math.min(maxDistance, deltaX));
         const clampedY = Math.max(-maxDistance, Math.min(maxDistance, deltaY));

         // Convert directly to normalized values (-1 to 1)
         const x = clampedX / maxDistance;
         const y = -clampedY / maxDistance; // Invert Y for screen coordinates (down = positive in game)

         // Portrait touch zone processing complete

         if (zone === 'left') {
             this.leftStickData = { active: true, vector: { x, y } };
         } else if (zone === 'right') {
             this.rightStickData = { active: true, vector: { x, y } };
         }
     }

     _handleDoubleTapCameraToggle() {
         // Only toggle camera in dungeon state
         if (this.currentState === STATE.DUNGEON && this.activeSceneHandler instanceof DungeonHandler) {
             console.log("[SceneManager] Double-tap camera toggle triggered");
             this.activeSceneHandler.toggleCameraView();
         }
     }

     // --- NEW: Center Touch Zone Management ---
     _initCenterTouchZone() {
         const centerZone = document.getElementById('center-touch-zone');
         if (centerZone) {
             // Show the center touch zone only in dungeon state
             if (this.currentState === STATE.DUNGEON) {
                 centerZone.style.display = 'block';
             } else {
                 centerZone.style.display = 'none';
             }

             // Remove existing listeners first
             centerZone.removeEventListener('touchstart', this.boundCenterTouchStart);
             centerZone.removeEventListener('touchend', this.boundCenterTouchEnd);

             // Add fresh listeners
             centerZone.addEventListener('touchstart', this.boundCenterTouchStart, { passive: false });
             centerZone.addEventListener('touchend', this.boundCenterTouchEnd, { passive: false });
         }
     }

     _destroyCenterTouchZone() {
         console.log("[SceneManager] Destroying center touch zone");

         const centerZone = document.getElementById('center-touch-zone');
         if (centerZone) {
             centerZone.style.display = 'none';
             centerZone.removeEventListener('touchstart', this.boundCenterTouchStart);
             centerZone.removeEventListener('touchend', this.boundCenterTouchEnd);
         }

         // Reset center touch zone state
         this.centerTouchZone.active = false;
         this.centerTouchZone.lastTapTime = 0;
         this.centerTouchZone.tapCount = 0;
     }

     _handleCenterTouchStart(event) {
         // MOBILE FIX: Prevent all browser defaults
         event.preventDefault();
         event.stopPropagation();

         // Only process in dungeon state
         if (this.currentState !== STATE.DUNGEON) return;

         this.centerTouchZone.active = true;
     }

     _handleCenterTouchEnd(event) {
         // MOBILE FIX: Prevent all browser defaults
         event.preventDefault();
         event.stopPropagation();

         // Only process in dungeon state
         if (this.currentState !== STATE.DUNGEON || !this.centerTouchZone.active) return;

         this.centerTouchZone.active = false;

         // Handle double-tap detection
         const now = performance.now();
         const timeSinceLastTap = now - this.centerTouchZone.lastTapTime;

         if (timeSinceLastTap < this.doubleTapWindow) {
             this.centerTouchZone.tapCount++;
             if (this.centerTouchZone.tapCount >= 2) {
                 this._handleDoubleTapCameraToggle();
                 this.centerTouchZone.tapCount = 0;
                 return;
             }
         } else {
             this.centerTouchZone.tapCount = 1;
         }

         this.centerTouchZone.lastTapTime = now;
     }
     // --- END CENTER TOUCH ZONE METHODS ---

     // --- END NEW METHODS ---

     // --- Camera State Management Methods ---
     _saveCameraState() {
         if (!this.camera) return;

         // Save camera state
         this._cameraState.position = this.camera.position.clone();
         this._cameraState.quaternion = this.camera.quaternion.clone();
         this._cameraState.up = this.camera.up.clone();
         this._cameraState.zoom = this.camera.zoom;
         this._cameraState.isTopDown = this.activeSceneHandler?.isTopDownView || false;
     }

     _restoreCameraState() {
         if (!this.camera || !this._cameraState.position) return;

         // Don't restore camera state if we're in DungeonHandler
         // Let the DungeonHandler manage its own camera
         if (this.activeSceneHandler instanceof DungeonHandler) {
             return;
         }

         // For other scenes, restore the camera state
         this.camera.position.copy(this._cameraState.position);
         this.camera.quaternion.copy(this._cameraState.quaternion);
         this.camera.up.copy(this._cameraState.up);
         this.camera.zoom = this._cameraState.zoom;
     }

    /**
     * Request maximum performance allocation from browser and system
     */
    _requestMaximumPerformance() {
        console.log("[SceneManager] 🚀 Requesting maximum performance allocation...");
        
        // 1. REQUEST DEDICATED GPU (if available)
        try {
            const canvas = this.renderer.domElement;
            const context = canvas.getContext('webgl2') || canvas.getContext('webgl');
            if (context) {
                console.log("✅ WebGL context acquired with high-performance preference");
            }
        } catch (error) {
            console.warn("⚠️ Could not verify GPU preference:", error);
        }

        // 2. DISABLE BROWSER THROTTLING
        try {
            // Prevent browser from throttling when tab not visible
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'visible') {
                    console.log("🎯 Tab focused - ensuring maximum performance");
                    this._prioritizeGamePerformance();
                }
            });
        } catch (error) {
            console.warn("⚠️ Could not configure browser throttling:", error);
        }

        // 3. REQUEST MAXIMUM CPU PRIORITY
        try {
            // Use scheduler.postTask if available (Chrome 94+)
            if ('scheduler' in window && 'postTask' in window.scheduler) {
                window.scheduler.postTask(() => {
                    console.log("✅ High priority task scheduler enabled");
                }, { priority: 'user-blocking' });
            }
        } catch (error) {
            console.warn("⚠️ Scheduler API not available:", error);
        }

        // 4. MAXIMIZE WEBGL PERFORMANCE
        try {
            const gl = this.renderer.getContext();
            
            // Enable all available extensions for maximum performance
            const extensions = [
                'WEBGL_lose_context',
                'OES_vertex_array_object', 
                'ANGLE_instanced_arrays',
                'WEBGL_compressed_texture_s3tc',
                'EXT_texture_filter_anisotropic'
            ];
            
            extensions.forEach(ext => {
                const extension = gl.getExtension(ext);
                if (extension) {
                    console.log(`✅ WebGL extension enabled: ${ext}`);
                }
            });

            // Display GPU info
            const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
            if (debugInfo) {
                const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                console.log(`🎮 GPU: ${renderer} (${vendor})`);
            }
        } catch (error) {
            console.warn("⚠️ Could not optimize WebGL:", error);
        }

        // 5. MEMORY OPTIMIZATION
        try {
            // Display memory usage if available
            if ('performance' in window && 'memory' in performance) {
                const memory = performance.memory;
                console.log(`💾 Memory: ${(memory.usedJSHeapSize / 1048576).toFixed(1)}MB used of ${(memory.totalJSHeapSize / 1048576).toFixed(1)}MB`);
            }

            // Create performance hints
            this._createPerformanceHints();
        } catch (error) {
            console.warn("⚠️ Could not optimize memory:", error);
        }

        console.log("🚀 Maximum performance mode activated!");
    }

    /**
     * Create performance hints for the browser
     */
    _createPerformanceHints() {
        // Set performance-critical viewport
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.setAttribute('name', 'viewport');
            document.head.appendChild(viewport);
        }
        viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover');
    }

    /**
     * Prioritize game performance when tab becomes active
     */
    _prioritizeGamePerformance() {
        // Reset pixel ratio for maximum quality
        if (this.renderer) {
            this.renderer.setPixelRatio(window.devicePixelRatio);
            console.log("🎯 Reset pixel ratio for maximum quality");
        }
    }

    // --- Scene Switching Methods for Minigames ---
    
    /**
     * Switch to a temporary scene (for minigames)
     * @param {THREE.Scene} newScene - The scene to switch to
     */
    setRenderScene(newScene) {
        if (!this.originalScene) {
            this.originalScene = this.scene;
            this.originalAnimateMethod = this.animate.bind(this);
            this.minigameMode = true;
        }
        this.scene = newScene;
        
        // Override the animate method to render minigame scene with CRT effects
        this.animate = () => {
            requestAnimationFrame(() => this.animate());
            
            const deltaTime = this.clock.getDelta();
            const currentTime = performance.now();
            
            // CRITICAL: Still update the active scene handler (for input, game logic, etc.)
            if (this.activeSceneHandler && this.activeSceneHandler.update) {
                this.activeSceneHandler.update(deltaTime);
            }
            
            // Apply CRT effects to minigame scene if available
            if (this.crtEffect && this.crtEffect.enabled) {
                // Update CRT effect with current scene
                this.crtEffect.scene = this.scene;
                this.crtEffect.update();
            } else {
                // Fallback to direct rendering
                if (this.renderer && this.scene && this.camera) {
                    this.renderer.render(this.scene, this.camera);
                }
            }
        };
        
        console.log('[SceneManager] Switched to temporary scene with override animate');
    }
    
    /**
     * Restore the original scene
     */
    restoreOriginalScene() {
        if (this.originalScene) {
            this.scene = this.originalScene;
            this.originalScene = null;
            
            // Restore original animate method
            if (this.originalAnimateMethod) {
                this.animate = this.originalAnimateMethod;
                this.originalAnimateMethod = null;
            }
            this.minigameMode = false;
            
            console.log('[SceneManager] Restored original scene and animate method');
        }
    }
}

export default SceneManager;
