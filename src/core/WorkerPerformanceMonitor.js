// src/core/WorkerPerformanceMonitor.js
// Advanced performance monitoring for worker threads

import { PERFORMANCE_THRESHOLDS } from '../config/workerConfig.js';

export class WorkerPerformanceMonitor {
    constructor(workerManager) {
        this.workerManager = workerManager;
        this.metrics = new Map();
        this.alerts = [];
        this.isMonitoring = false;
        this.monitoringInterval = null;
        
        // Performance history for trend analysis
        this.performanceHistory = new Map();
        this.maxHistoryLength = 100;
        
        console.log('[WorkerPerformanceMonitor] Initialized');
    }

    /**
     * Start performance monitoring
     * @param {number} interval - Monitoring interval in ms (default: 5000)
     */
    startMonitoring(interval = 5000) {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
            this.analyzePerformance();
        }, interval);
        
        console.log(`[WorkerPerformanceMonitor] Started monitoring (${interval}ms interval)`);
    }

    /**
     * Stop performance monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('[WorkerPerformanceMonitor] Stopped monitoring');
    }

    /**
     * Collect current performance metrics
     */
    collectMetrics() {
        const timestamp = Date.now();
        const poolStats = this.workerManager.getPoolStatistics();
        const allMetrics = this.workerManager.getAllPerformanceMetrics();
        
        // Collect system metrics if available
        const systemMetrics = this.getSystemMetrics();
        
        const currentMetrics = {
            timestamp,
            pools: poolStats.pools,
            workers: allMetrics.workers,
            system: systemMetrics,
            totalWorkers: poolStats.totalWorkers,
            activeRequests: poolStats.activeRequests
        };
        
        // Store in history
        this.addToHistory('overall', currentMetrics);
        
        // Store per worker type
        Object.keys(poolStats.pools).forEach(workerType => {
            this.addToHistory(workerType, {
                timestamp,
                ...poolStats.pools[workerType],
                workers: this.getWorkersByType(allMetrics.workers, workerType)
            });
        });
    }

    /**
     * Get system performance metrics
     * @returns {Object} System metrics
     */
    getSystemMetrics() {
        const metrics = {
            timestamp: Date.now()
        };
        
        // Memory information (if available)
        if (performance.memory) {
            metrics.memory = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit,
                usage: performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize
            };
        }
        
        // CPU information
        metrics.cpu = {
            hardwareConcurrency: navigator.hardwareConcurrency || 4
        };
        
        // Device information
        if (navigator.deviceMemory) {
            metrics.device = {
                memory: navigator.deviceMemory
            };
        }
        
        return metrics;
    }

    /**
     * Analyze performance and generate alerts
     */
    analyzePerformance() {
        const currentTime = Date.now();
        const newAlerts = [];
        
        // Analyze each worker type
        this.performanceHistory.forEach((history, workerType) => {
            if (workerType === 'overall') return;
            
            const recent = history.slice(-5); // Last 5 measurements
            if (recent.length === 0) return;
            
            const latest = recent[recent.length - 1];
            
            // Check utilization
            if (latest.utilization > 0.9) {
                newAlerts.push({
                    type: 'HIGH_UTILIZATION',
                    workerType,
                    severity: 'warning',
                    message: `${workerType} workers at ${(latest.utilization * 100).toFixed(1)}% utilization`,
                    timestamp: currentTime,
                    data: { utilization: latest.utilization }
                });
            }
            
            // Check queue length
            if (latest.queuedRequests > PERFORMANCE_THRESHOLDS.queueLength.warning) {
                const severity = latest.queuedRequests > PERFORMANCE_THRESHOLDS.queueLength.critical ? 'critical' : 'warning';
                newAlerts.push({
                    type: 'HIGH_QUEUE_LENGTH',
                    workerType,
                    severity,
                    message: `${workerType} has ${latest.queuedRequests} queued requests`,
                    timestamp: currentTime,
                    data: { queueLength: latest.queuedRequests }
                });
            }
            
            // Check response times
            if (latest.workers) {
                Object.values(latest.workers).forEach(worker => {
                    if (worker.averageTime > PERFORMANCE_THRESHOLDS.responseTime.poor) {
                        newAlerts.push({
                            type: 'SLOW_RESPONSE',
                            workerType,
                            severity: 'warning',
                            message: `Worker ${worker.type} average response time: ${worker.averageTime.toFixed(0)}ms`,
                            timestamp: currentTime,
                            data: { averageTime: worker.averageTime }
                        });
                    }
                });
            }
        });
        
        // Add new alerts
        this.alerts.push(...newAlerts);
        
        // Keep only recent alerts (last hour)
        const oneHourAgo = currentTime - 3600000;
        this.alerts = this.alerts.filter(alert => alert.timestamp > oneHourAgo);
        
        // Log critical alerts
        newAlerts.filter(alert => alert.severity === 'critical').forEach(alert => {
            console.warn(`[WorkerPerformanceMonitor] CRITICAL: ${alert.message}`, alert.data);
        });
    }

    /**
     * Add metrics to history
     * @param {string} key - History key
     * @param {Object} metrics - Metrics to add
     */
    addToHistory(key, metrics) {
        if (!this.performanceHistory.has(key)) {
            this.performanceHistory.set(key, []);
        }
        
        const history = this.performanceHistory.get(key);
        history.push(metrics);
        
        // Keep only recent history
        if (history.length > this.maxHistoryLength) {
            history.splice(0, history.length - this.maxHistoryLength);
        }
    }

    /**
     * Get workers by type from metrics
     * @param {Object} allWorkers - All worker metrics
     * @param {string} type - Worker type to filter
     * @returns {Object} Filtered workers
     */
    getWorkersByType(allWorkers, type) {
        const filtered = {};
        Object.entries(allWorkers).forEach(([workerId, worker]) => {
            if (worker.type === type) {
                filtered[workerId] = worker;
            }
        });
        return filtered;
    }

    /**
     * Get performance report
     * @returns {Object} Performance report
     */
    getPerformanceReport() {
        const currentTime = Date.now();
        const recentAlerts = this.alerts.filter(alert => 
            currentTime - alert.timestamp < 300000 // Last 5 minutes
        );
        
        return {
            timestamp: currentTime,
            isMonitoring: this.isMonitoring,
            alerts: {
                recent: recentAlerts,
                total: this.alerts.length,
                critical: this.alerts.filter(a => a.severity === 'critical').length,
                warnings: this.alerts.filter(a => a.severity === 'warning').length
            },
            pools: this.workerManager.getPoolStatistics(),
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * Generate performance recommendations
     * @returns {Array} Array of recommendations
     */
    generateRecommendations() {
        const recommendations = [];
        const poolStats = this.workerManager.getPoolStatistics();
        
        Object.entries(poolStats.pools).forEach(([type, stats]) => {
            // High utilization recommendation
            if (stats.utilization > 0.8) {
                recommendations.push({
                    type: 'SCALE_UP',
                    workerType: type,
                    message: `Consider increasing ${type} worker pool size (current: ${stats.busy}/${stats.maxSize})`,
                    priority: 'medium'
                });
            }
            
            // Low utilization recommendation
            if (stats.utilization < 0.1 && stats.maxSize > 1) {
                recommendations.push({
                    type: 'SCALE_DOWN',
                    workerType: type,
                    message: `Consider reducing ${type} worker pool size (utilization: ${(stats.utilization * 100).toFixed(1)}%)`,
                    priority: 'low'
                });
            }
            
            // Queue buildup recommendation
            if (stats.queuedRequests > 0) {
                recommendations.push({
                    type: 'QUEUE_MANAGEMENT',
                    workerType: type,
                    message: `${type} has queued requests, consider optimizing or scaling`,
                    priority: 'high'
                });
            }
        });
        
        return recommendations;
    }

    /**
     * Clear all metrics and alerts
     */
    clearMetrics() {
        this.metrics.clear();
        this.alerts = [];
        this.performanceHistory.clear();
        console.log('[WorkerPerformanceMonitor] Metrics cleared');
    }
}
