import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Blink Strike Card Prefab
 * Teleports the player to target enemy and performs a devastating strike
 */

// Blink Strike specific colors
const BLINK_STRIKE_COLORS = {
    VOID_BLACK: 0x0D0D0D,           // Void darkness
    TELEPORT_BLUE: 0x1E90FF,        // Teleportation energy
    STRIKE_RED: 0xDC143C,           // Strike attack
    SHADOW_PURPLE: 0x4B0082,        // Shadow trail
    LIGHTNING_WHITE: 0xF8F8FF,      // Lightning flash
    ENERGY_CYAN: 0x00FFFF,          // Energy discharge
    PORTAL_MAGENTA: 0xFF00FF,       // Portal magic
    VOID_GRAY: 0x2F2F2F             // Void distortion
};

/**
 * Create a blink strike card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The blink strike card 3D model
 */
export function createBlinkStrikeCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'BlinkStrikeCard';

    // Blink Strike materials
    const voidBlackMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.VOID_BLACK,
        emissive: BLINK_STRIKE_COLORS.VOID_BLACK,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const teleportBlueMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.TELEPORT_BLUE,
        emissive: BLINK_STRIKE_COLORS.TELEPORT_BLUE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const strikeRedMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.STRIKE_RED,
        emissive: BLINK_STRIKE_COLORS.STRIKE_RED,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const shadowPurpleMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.SHADOW_PURPLE,
        emissive: BLINK_STRIKE_COLORS.SHADOW_PURPLE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const lightningWhiteMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.LIGHTNING_WHITE,
        emissive: BLINK_STRIKE_COLORS.LIGHTNING_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.5
    });

    const energyCyanMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.ENERGY_CYAN,
        emissive: BLINK_STRIKE_COLORS.ENERGY_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const portalMagentaMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.PORTAL_MAGENTA,
        emissive: BLINK_STRIKE_COLORS.PORTAL_MAGENTA,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const voidGrayMaterial = new THREE.MeshLambertMaterial({
        color: BLINK_STRIKE_COLORS.VOID_GRAY,
        emissive: BLINK_STRIKE_COLORS.VOID_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Teleport Portal (origin point)
    const teleportPortalVoxels = [
        // Portal rim (circular formation)
        { x: -0.16, y: 0.0, z: 0.0, material: portalMagentaMaterial }, // Left
        { x: 0.16, y: 0.0, z: 0.0, material: portalMagentaMaterial }, // Right
        { x: 0.0, y: 0.16, z: 0.0, material: portalMagentaMaterial }, // Top
        { x: 0.0, y: -0.16, z: 0.0, material: portalMagentaMaterial }, // Bottom
        { x: -0.11, y: 0.11, z: 0.0, material: portalMagentaMaterial }, // Top-left
        { x: 0.11, y: 0.11, z: 0.0, material: portalMagentaMaterial }, // Top-right
        { x: -0.11, y: -0.11, z: 0.0, material: portalMagentaMaterial }, // Bottom-left
        { x: 0.11, y: -0.11, z: 0.0, material: portalMagentaMaterial }, // Bottom-right
        
        // Portal interior
        { x: -0.08, y: 0.0, z: 0.0, material: voidBlackMaterial }, // Inner left
        { x: 0.08, y: 0.0, z: 0.0, material: voidBlackMaterial }, // Inner right
        { x: 0.0, y: 0.08, z: 0.0, material: voidBlackMaterial }, // Inner top
        { x: 0.0, y: -0.08, z: 0.0, material: voidBlackMaterial }, // Inner bottom
        { x: 0.0, y: 0.0, z: 0.0, material: voidBlackMaterial }, // Center void
        
        // Portal depth
        { x: 0.0, y: 0.0, z: -0.04, material: shadowPurpleMaterial }, // Depth layer 1
        { x: 0.0, y: 0.0, z: -0.08, material: voidBlackMaterial } // Depth layer 2
    ];

    // Strike Figure (warrior silhouette)
    const strikeFigureVoxels = [
        // Warrior figure
        { x: 0.0, y: 0.12, z: 0.02, material: voidGrayMaterial }, // Head
        { x: 0.0, y: 0.04, z: 0.02, material: voidGrayMaterial }, // Torso upper
        { x: 0.0, y: -0.04, z: 0.02, material: voidGrayMaterial }, // Torso lower
        
        // Arms in striking pose
        { x: -0.08, y: 0.08, z: 0.02, material: voidGrayMaterial }, // Left shoulder
        { x: -0.12, y: 0.04, z: 0.02, material: voidGrayMaterial }, // Left upper arm
        { x: -0.16, y: 0.0, z: 0.02, material: strikeRedMaterial }, // Left hand/weapon
        { x: 0.08, y: 0.08, z: 0.02, material: voidGrayMaterial }, // Right shoulder
        { x: 0.12, y: 0.04, z: 0.02, material: voidGrayMaterial }, // Right upper arm
        { x: 0.16, y: 0.0, z: 0.02, material: strikeRedMaterial }, // Right hand/weapon
        
        // Legs in motion
        { x: -0.04, y: -0.12, z: 0.02, material: voidGrayMaterial }, // Left thigh
        { x: -0.04, y: -0.20, z: 0.02, material: voidGrayMaterial }, // Left shin
        { x: 0.04, y: -0.12, z: 0.02, material: voidGrayMaterial }, // Right thigh
        { x: 0.04, y: -0.20, z: 0.02, material: voidGrayMaterial }, // Right shin
        
        // Strike energy
        { x: -0.20, y: 0.0, z: 0.04, material: strikeRedMaterial }, // Left strike energy
        { x: 0.20, y: 0.0, z: 0.04, material: strikeRedMaterial }, // Right strike energy
        { x: 0.0, y: 0.16, z: 0.04, material: lightningWhiteMaterial } // Head energy
    ];

    // Teleport Trail (path between portals)
    const teleportTrailVoxels = [
        // Horizontal trail elements
        { x: -0.24, y: 0.04, z: 0.0, material: teleportBlueMaterial },
        { x: -0.20, y: 0.08, z: 0.0, material: energyCyanMaterial },
        { x: -0.16, y: 0.12, z: 0.0, material: teleportBlueMaterial },
        { x: -0.12, y: 0.16, z: 0.0, material: energyCyanMaterial },
        { x: -0.08, y: 0.20, z: 0.0, material: teleportBlueMaterial },
        { x: -0.04, y: 0.24, z: 0.0, material: energyCyanMaterial },
        { x: 0.0, y: 0.28, z: 0.0, material: lightningWhiteMaterial },
        { x: 0.04, y: 0.24, z: 0.0, material: energyCyanMaterial },
        { x: 0.08, y: 0.20, z: 0.0, material: teleportBlueMaterial },
        { x: 0.12, y: 0.16, z: 0.0, material: energyCyanMaterial },
        { x: 0.16, y: 0.12, z: 0.0, material: teleportBlueMaterial },
        { x: 0.20, y: 0.08, z: 0.0, material: energyCyanMaterial },
        { x: 0.24, y: 0.04, z: 0.0, material: teleportBlueMaterial },
        
        // Diagonal trail elements
        { x: -0.28, y: -0.04, z: 0.0, material: shadowPurpleMaterial },
        { x: -0.24, y: -0.08, z: 0.0, material: teleportBlueMaterial },
        { x: -0.20, y: -0.12, z: 0.0, material: shadowPurpleMaterial },
        { x: -0.16, y: -0.16, z: 0.0, material: teleportBlueMaterial },
        { x: -0.12, y: -0.20, z: 0.0, material: shadowPurpleMaterial },
        { x: -0.08, y: -0.24, z: 0.0, material: teleportBlueMaterial },
        { x: -0.04, y: -0.28, z: 0.0, material: shadowPurpleMaterial },
        { x: 0.04, y: -0.28, z: 0.0, material: shadowPurpleMaterial },
        { x: 0.08, y: -0.24, z: 0.0, material: teleportBlueMaterial },
        { x: 0.12, y: -0.20, z: 0.0, material: shadowPurpleMaterial },
        { x: 0.16, y: -0.16, z: 0.0, material: teleportBlueMaterial },
        { x: 0.20, y: -0.12, z: 0.0, material: shadowPurpleMaterial },
        { x: 0.24, y: -0.08, z: 0.0, material: teleportBlueMaterial },
        { x: 0.28, y: -0.04, z: 0.0, material: shadowPurpleMaterial }
    ];

    // Lightning Effects (teleport energy)
    const lightningEffectsVoxels = [
        // Lightning bolts
        { x: -0.32, y: 0.12, z: 0.04, material: lightningWhiteMaterial },
        { x: -0.28, y: 0.16, z: 0.04, material: energyCyanMaterial },
        { x: -0.24, y: 0.20, z: 0.04, material: lightningWhiteMaterial },
        { x: -0.20, y: 0.24, z: 0.04, material: energyCyanMaterial },
        { x: 0.32, y: 0.12, z: 0.04, material: lightningWhiteMaterial },
        { x: 0.28, y: 0.16, z: 0.04, material: energyCyanMaterial },
        { x: 0.24, y: 0.20, z: 0.04, material: lightningWhiteMaterial },
        { x: 0.20, y: 0.24, z: 0.04, material: energyCyanMaterial },
        
        // Energy discharges
        { x: 0.0, y: 0.32, z: 0.04, material: lightningWhiteMaterial },
        { x: -0.04, y: 0.32, z: 0.04, material: energyCyanMaterial },
        { x: 0.04, y: 0.32, z: 0.04, material: energyCyanMaterial },
        { x: 0.0, y: -0.32, z: 0.04, material: lightningWhiteMaterial },
        { x: -0.04, y: -0.32, z: 0.04, material: energyCyanMaterial },
        { x: 0.04, y: -0.32, z: 0.04, material: energyCyanMaterial },
        
        // Floating energy orbs
        { x: -0.36, y: 0.0, z: 0.08, material: teleportBlueMaterial },
        { x: 0.36, y: 0.0, z: 0.08, material: teleportBlueMaterial },
        { x: 0.0, y: 0.36, z: 0.08, material: energyCyanMaterial },
        { x: 0.0, y: -0.36, z: 0.08, material: energyCyanMaterial }
    ];

    // Create teleport portal group
    const teleportPortalGroup = new THREE.Group();
    teleportPortalGroup.name = 'teleportPortalGroup';

    // Add teleport portal voxels
    teleportPortalVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.portalPhase = index * 0.08; // Stagger animation
        teleportPortalGroup.add(mesh);
    });

    // Create strike figure group
    const strikeFigureGroup = new THREE.Group();
    strikeFigureGroup.name = 'strikeFigureGroup';

    // Add strike figure voxels
    strikeFigureVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.strikePhase = index * 0.12; // Stagger animation
        strikeFigureGroup.add(mesh);
    });

    // Create teleport trail group
    const teleportTrailGroup = new THREE.Group();
    teleportTrailGroup.name = 'teleportTrailGroup';

    // Add teleport trail voxels
    teleportTrailVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.trailPhase = index * 0.05; // Stagger animation
        teleportTrailGroup.add(mesh);
    });

    // Create lightning effects group
    const lightningEffectsGroup = new THREE.Group();
    lightningEffectsGroup.name = 'lightningEffectsGroup';

    // Add lightning effects voxels
    lightningEffectsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.lightningPhase = index * 0.1; // Stagger animation
        lightningEffectsGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(teleportPortalGroup);
    cardGroup.add(strikeFigureGroup);
    cardGroup.add(teleportTrailGroup);
    cardGroup.add(lightningEffectsGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        teleportPulse: 0,
        strikeMotion: 0,
        energyFlow: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update blink strike card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateBlinkStrikeCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.teleportPulse += deltaTime * 4.0; // Teleport pulse speed
    cardGroup.userData.strikeMotion += deltaTime * 6.0; // Strike motion speed
    cardGroup.userData.energyFlow += deltaTime * 8.0; // Energy flow speed

    const time = cardGroup.userData.animationTime;
    const teleportPulse = cardGroup.userData.teleportPulse;
    const strikeMotion = cardGroup.userData.strikeMotion;
    const energyFlow = cardGroup.userData.energyFlow;

    // Animate teleport portal (portal vortex)
    const teleportPortalGroup = cardGroup.getObjectByName('teleportPortalGroup');
    if (teleportPortalGroup) {
        // Portal rotation
        const portalRotation = Math.sin(teleportPulse * 2.0) * 0.05;
        teleportPortalGroup.rotation.z = portalRotation;
        
        // Individual portal element animation
        teleportPortalGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.portalPhase !== undefined) {
                const portalTime = teleportPulse + mesh.userData.portalPhase;
                
                // Portal pulsing
                const portalPulse = Math.sin(portalTime * 5.0) * 0.002;
                const vortex = Math.cos(portalTime * 3.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + vortex;
                mesh.position.y = mesh.userData.originalPosition.y + portalPulse;
                
                // Portal glow
                const portalGlow = 1.0 + Math.sin(portalTime * 4.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * portalGlow;
                }
                
                // Portal opacity
                const portalOpacity = 0.7 + Math.sin(portalTime * 6.0) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * portalOpacity;
                }
            }
        });
    }

    // Animate strike figure (warrior in motion)
    const strikeFigureGroup = cardGroup.getObjectByName('strikeFigureGroup');
    if (strikeFigureGroup) {
        strikeFigureGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.strikePhase !== undefined) {
                const strikeTime = strikeMotion + mesh.userData.strikePhase;
                
                // Strike motion
                const strikeForward = Math.sin(strikeTime * 3.0) * 0.003;
                const strikeSway = Math.cos(strikeTime * 4.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + strikeSway;
                mesh.position.z = mesh.userData.originalPosition.z + strikeForward;
                
                // Strike intensity
                const strikeIntensity = 1.0 + Math.sin(strikeTime * 5.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * strikeIntensity;
                }
                
                // Strike flickering
                const strikeFlicker = 0.8 + Math.sin(strikeTime * 7.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * strikeFlicker;
                }
            }
        });
    }

    // Animate teleport trail (energy path)
    const teleportTrailGroup = cardGroup.getObjectByName('teleportTrailGroup');
    if (teleportTrailGroup) {
        teleportTrailGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.trailPhase !== undefined) {
                const trailTime = energyFlow + mesh.userData.trailPhase;
                
                // Trail flowing motion
                const trailFlow = Math.sin(trailTime * 6.0) * 0.004;
                const trailWave = Math.cos(trailTime * 4.0) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + trailFlow;
                mesh.position.y = mesh.userData.originalPosition.y + trailWave;
                
                // Trail energy intensity
                const trailIntensity = 1.0 + Math.sin(trailTime * 8.0) * 0.7;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * trailIntensity;
                }
                
                // Trail scale pulsing
                const trailScale = 0.9 + Math.sin(trailTime * 5.0) * 0.2;
                mesh.scale.setScalar(trailScale);
            }
        });
    }

    // Animate lightning effects (energy discharge)
    const lightningEffectsGroup = cardGroup.getObjectByName('lightningEffectsGroup');
    if (lightningEffectsGroup) {
        lightningEffectsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.lightningPhase !== undefined) {
                const lightningTime = energyFlow + mesh.userData.lightningPhase;
                
                // Lightning crackling
                const lightningCrackle = Math.sin(lightningTime * 10.0) * 0.006;
                const lightningZap = Math.cos(lightningTime * 12.0) * 0.005;
                
                mesh.position.x = mesh.userData.originalPosition.x + lightningCrackle;
                mesh.position.y = mesh.userData.originalPosition.y + lightningZap;
                
                // Lightning intensity fluctuation
                const lightningIntensity = 1.0 + Math.sin(lightningTime * 15.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * lightningIntensity;
                }
                
                // Lightning opacity (flash effect)
                const lightningOpacity = 0.6 + Math.sin(lightningTime * 12.0) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * lightningOpacity;
                }
                
                // Lightning scale variation
                const lightningScale = 0.8 + Math.sin(lightningTime * 8.0) * 0.4;
                mesh.scale.setScalar(lightningScale);
            }
        });
    }

    // Overall blink strike presence (rare teleport magic)
    const blinkPulse = 1 + Math.sin(time * 3.0) * 0.1;
    const blinkShift = Math.cos(time * 8.0) * 0.001;
    cardGroup.scale.setScalar(0.8 * blinkPulse);
    cardGroup.position.x += blinkShift;
    cardGroup.position.z += blinkShift * 1.2;
}

// Export the blink strike card data for the loot system
export const BLINK_STRIKE_CARD_DATA = {
    name: "Blink Strike",
    description: 'Instantly teleports to target enemy and performs a devastating strike, dealing high damage and briefly stunning the target.',
    category: 'card',
    rarity: 'rare',
    effect: 'blink_strike',
    effectValue: 50, // Strike damage
    createFunction: createBlinkStrikeCard,
    updateFunction: updateBlinkStrikeCardAnimation,
    voxelModel: 'blink_strike_card',
    glow: {
        color: 0x1E90FF,
        intensity: 1.3
    }
};

export default createBlinkStrikeCard;