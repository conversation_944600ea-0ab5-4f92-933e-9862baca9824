import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Bone Guardian Card Prefab
 * Summons a skeletal guardian that blocks enemy attacks and retaliates
 */

// Bone Guardian specific colors
const BONE_GUARDIAN_COLORS = {
    BONE_WHITE: 0xF5F5DC,           // Main skeletal structure
    SHADOW_BLACK: 0x2F2F2F,         // Dark necromantic energy
    SOUL_PURPLE: 0x8A2BE2,          // Soul flames
    DEATH_GRAY: 0x696969,           // Death essence
    ANCIENT_BROWN: 0x8B4513,        // Ancient bone coloring
    SPECTRAL_BLUE: 0x4169E1,        // Spectral energy
    NECRO_GREEN: 0x32CD32,          // Necromantic glow
    ETHEREAL_CYAN: 0x00CED1         // Ethereal protection
};

/**
 * Create a bone guardian card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The bone guardian card 3D model
 */
export function createBoneGuardianCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'BoneGuardianCard';

    // Bone Guardian materials
    const boneWhiteMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.BONE_WHITE,
        emissive: BONE_GUARDIAN_COLORS.BONE_WHITE,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const shadowBlackMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.SHADOW_BLACK,
        emissive: BONE_GUARDIAN_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const soulPurpleMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.SOUL_PURPLE,
        emissive: BONE_GUARDIAN_COLORS.SOUL_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const deathGrayMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.DEATH_GRAY,
        emissive: BONE_GUARDIAN_COLORS.DEATH_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.8
    });

    const ancientBrownMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.ANCIENT_BROWN,
        emissive: BONE_GUARDIAN_COLORS.ANCIENT_BROWN,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const spectralBlueMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.SPECTRAL_BLUE,
        emissive: BONE_GUARDIAN_COLORS.SPECTRAL_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const necroGreenMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.NECRO_GREEN,
        emissive: BONE_GUARDIAN_COLORS.NECRO_GREEN,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const etherealCyanMaterial = new THREE.MeshLambertMaterial({
        color: BONE_GUARDIAN_COLORS.ETHEREAL_CYAN,
        emissive: BONE_GUARDIAN_COLORS.ETHEREAL_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Skeletal Guardian (main structure)
    const skeletalGuardianVoxels = [
        // Skull (head)
        { x: 0.0, y: 0.12, z: 0.0, material: boneWhiteMaterial }, // Main skull
        { x: -0.04, y: 0.16, z: 0.0, material: boneWhiteMaterial }, // Skull top
        { x: 0.04, y: 0.16, z: 0.0, material: boneWhiteMaterial }, // Skull top
        { x: -0.02, y: 0.12, z: 0.02, material: shadowBlackMaterial }, // Left eye socket
        { x: 0.02, y: 0.12, z: 0.02, material: shadowBlackMaterial }, // Right eye socket
        { x: 0.0, y: 0.08, z: 0.02, material: ancientBrownMaterial }, // Jaw
        
        // Spine (central column)
        { x: 0.0, y: 0.04, z: 0.0, material: boneWhiteMaterial }, // Neck vertebra
        { x: 0.0, y: -0.04, z: 0.0, material: boneWhiteMaterial }, // Upper spine
        { x: 0.0, y: -0.12, z: 0.0, material: boneWhiteMaterial }, // Lower spine
        { x: 0.0, y: -0.20, z: 0.0, material: boneWhiteMaterial }, // Pelvis
        
        // Ribcage
        { x: -0.08, y: 0.0, z: 0.0, material: boneWhiteMaterial }, // Left ribs
        { x: 0.08, y: 0.0, z: 0.0, material: boneWhiteMaterial }, // Right ribs
        { x: -0.06, y: -0.08, z: 0.0, material: boneWhiteMaterial }, // Left lower ribs
        { x: 0.06, y: -0.08, z: 0.0, material: boneWhiteMaterial }, // Right lower ribs
        
        // Left arm
        { x: -0.12, y: 0.04, z: 0.0, material: boneWhiteMaterial }, // Left shoulder
        { x: -0.16, y: -0.04, z: 0.0, material: boneWhiteMaterial }, // Left upper arm
        { x: -0.20, y: -0.12, z: 0.0, material: boneWhiteMaterial }, // Left forearm
        { x: -0.24, y: -0.16, z: 0.0, material: ancientBrownMaterial }, // Left hand
        
        // Right arm
        { x: 0.12, y: 0.04, z: 0.0, material: boneWhiteMaterial }, // Right shoulder
        { x: 0.16, y: -0.04, z: 0.0, material: boneWhiteMaterial }, // Right upper arm
        { x: 0.20, y: -0.12, z: 0.0, material: boneWhiteMaterial }, // Right forearm
        { x: 0.24, y: -0.16, z: 0.0, material: ancientBrownMaterial }, // Right hand
        
        // Left leg
        { x: -0.04, y: -0.24, z: 0.0, material: boneWhiteMaterial }, // Left thigh
        { x: -0.04, y: -0.32, z: 0.0, material: boneWhiteMaterial }, // Left shin
        { x: -0.04, y: -0.36, z: 0.02, material: ancientBrownMaterial }, // Left foot
        
        // Right leg
        { x: 0.04, y: -0.24, z: 0.0, material: boneWhiteMaterial }, // Right thigh
        { x: 0.04, y: -0.32, z: 0.0, material: boneWhiteMaterial }, // Right shin
        { x: 0.04, y: -0.36, z: 0.02, material: ancientBrownMaterial } // Right foot
    ];

    // Soul Flames (necromantic energy)
    const soulFlamesVoxels = [
        // Eye flames
        { x: -0.02, y: 0.12, z: 0.04, material: soulPurpleMaterial }, // Left eye flame
        { x: 0.02, y: 0.12, z: 0.04, material: soulPurpleMaterial }, // Right eye flame
        
        // Chest soul flame
        { x: 0.0, y: 0.0, z: 0.02, material: soulPurpleMaterial }, // Heart flame
        { x: 0.0, y: 0.04, z: 0.04, material: soulPurpleMaterial }, // Rising flame
        { x: 0.0, y: 0.08, z: 0.06, material: soulPurpleMaterial }, // Flame tip
        
        // Hand flames
        { x: -0.28, y: -0.16, z: 0.0, material: soulPurpleMaterial }, // Left hand flame
        { x: 0.28, y: -0.16, z: 0.0, material: soulPurpleMaterial }, // Right hand flame
        
        // Floating soul orbs
        { x: -0.16, y: 0.20, z: 0.08, material: soulPurpleMaterial },
        { x: 0.16, y: 0.20, z: 0.08, material: soulPurpleMaterial },
        { x: 0.0, y: 0.24, z: 0.0, material: soulPurpleMaterial },
        { x: -0.32, y: -0.08, z: 0.04, material: soulPurpleMaterial },
        { x: 0.32, y: -0.08, z: 0.04, material: soulPurpleMaterial }
    ];

    // Death Aura (protective energy)
    const deathAuraVoxels = [
        // Aura around guardian
        { x: -0.12, y: 0.08, z: -0.04, material: deathGrayMaterial }, // Left aura
        { x: 0.12, y: 0.08, z: -0.04, material: deathGrayMaterial }, // Right aura
        { x: 0.0, y: 0.16, z: -0.06, material: deathGrayMaterial }, // Top aura
        { x: 0.0, y: -0.16, z: -0.04, material: deathGrayMaterial }, // Bottom aura
        
        // Protective barrier
        { x: -0.20, y: 0.0, z: -0.08, material: spectralBlueMaterial }, // Left barrier
        { x: 0.20, y: 0.0, z: -0.08, material: spectralBlueMaterial }, // Right barrier
        { x: 0.0, y: 0.20, z: -0.08, material: spectralBlueMaterial }, // Top barrier
        { x: 0.0, y: -0.20, z: -0.08, material: spectralBlueMaterial }, // Bottom barrier
        
        // Swirling energy
        { x: -0.08, y: 0.16, z: -0.12, material: etherealCyanMaterial },
        { x: 0.08, y: 0.16, z: -0.12, material: etherealCyanMaterial },
        { x: -0.16, y: -0.08, z: -0.12, material: etherealCyanMaterial },
        { x: 0.16, y: -0.08, z: -0.12, material: etherealCyanMaterial }
    ];

    // Necromantic Effects (magical energy)
    const necromanticEffectsVoxels = [
        // Ground necromancy
        { x: 0.0, y: -0.40, z: 0.0, material: necroGreenMaterial }, // Center ground
        { x: -0.08, y: -0.40, z: 0.08, material: necroGreenMaterial }, // Left ground
        { x: 0.08, y: -0.40, z: 0.08, material: necroGreenMaterial }, // Right ground
        { x: 0.0, y: -0.40, z: 0.16, material: necroGreenMaterial }, // Front ground
        
        // Rising necromantic energy
        { x: -0.12, y: -0.36, z: 0.04, material: shadowBlackMaterial },
        { x: 0.12, y: -0.36, z: 0.04, material: shadowBlackMaterial },
        { x: 0.0, y: -0.32, z: 0.08, material: shadowBlackMaterial },
        { x: -0.16, y: -0.28, z: 0.0, material: shadowBlackMaterial },
        { x: 0.16, y: -0.28, z: 0.0, material: shadowBlackMaterial },
        
        // Floating runes
        { x: -0.24, y: 0.12, z: 0.0, material: necroGreenMaterial },
        { x: 0.24, y: 0.12, z: 0.0, material: necroGreenMaterial },
        { x: 0.0, y: 0.28, z: -0.04, material: necroGreenMaterial },
        { x: -0.20, y: -0.24, z: -0.04, material: necroGreenMaterial },
        { x: 0.20, y: -0.24, z: -0.04, material: necroGreenMaterial }
    ];

    // Create skeletal guardian group
    const skeletalGuardianGroup = new THREE.Group();
    skeletalGuardianGroup.name = 'skeletalGuardianGroup';

    // Add skeletal guardian voxels
    skeletalGuardianVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.bonePhase = index * 0.1; // Stagger animation
        skeletalGuardianGroup.add(mesh);
    });

    // Create soul flames group
    const soulFlamesGroup = new THREE.Group();
    soulFlamesGroup.name = 'soulFlamesGroup';

    // Add soul flames voxels
    soulFlamesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.flamePhase = index * 0.12; // Stagger animation
        soulFlamesGroup.add(mesh);
    });

    // Create death aura group
    const deathAuraGroup = new THREE.Group();
    deathAuraGroup.name = 'deathAuraGroup';

    // Add death aura voxels
    deathAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.08; // Stagger animation
        deathAuraGroup.add(mesh);
    });

    // Create necromantic effects group
    const necromanticEffectsGroup = new THREE.Group();
    necromanticEffectsGroup.name = 'necromanticEffectsGroup';

    // Add necromantic effects voxels
    necromanticEffectsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.necroPhase = index * 0.15; // Stagger animation
        necromanticEffectsGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(skeletalGuardianGroup);
    cardGroup.add(soulFlamesGroup);
    cardGroup.add(deathAuraGroup);
    cardGroup.add(necromanticEffectsGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        guardianStance: 0,
        soulFlicker: 0,
        deathPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update bone guardian card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateBoneGuardianCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.guardianStance += deltaTime * 2.0; // Guardian stance speed
    cardGroup.userData.soulFlicker += deltaTime * 5.0; // Soul flame flicker
    cardGroup.userData.deathPulse += deltaTime * 3.0; // Death aura pulse

    const time = cardGroup.userData.animationTime;
    const guardianStance = cardGroup.userData.guardianStance;
    const soulFlicker = cardGroup.userData.soulFlicker;
    const deathPulse = cardGroup.userData.deathPulse;

    // Animate skeletal guardian (guardian stance)
    const skeletalGuardianGroup = cardGroup.getObjectByName('skeletalGuardianGroup');
    if (skeletalGuardianGroup) {
        // Guardian breathing motion
        const guardianBreath = Math.sin(guardianStance * 1.5) * 0.001;
        skeletalGuardianGroup.position.y = guardianBreath;
        
        // Individual bone animation
        skeletalGuardianGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.bonePhase !== undefined) {
                const boneTime = guardianStance + mesh.userData.bonePhase;
                
                // Bone creaking motion
                const creak = Math.sin(boneTime * 1.8) * 0.0003;
                const sway = Math.cos(boneTime * 2.2) * 0.0002;
                
                mesh.position.x = mesh.userData.originalPosition.x + creak;
                mesh.position.z = mesh.userData.originalPosition.z + sway;
                
                // Bone glow
                const boneGlow = 1.0 + Math.sin(boneTime * 1.5) * 0.2;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * boneGlow;
                }
            }
        });
    }

    // Animate soul flames (necromantic fire)
    const soulFlamesGroup = cardGroup.getObjectByName('soulFlamesGroup');
    if (soulFlamesGroup) {
        soulFlamesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.flamePhase !== undefined) {
                const flameTime = soulFlicker + mesh.userData.flamePhase;
                
                // Soul flame dancing
                const flameDance = Math.sin(flameTime * 6.0) * 0.002;
                const flameFlicker = Math.cos(flameTime * 8.0) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + flameDance;
                mesh.position.y = mesh.userData.originalPosition.y + flameFlicker;
                
                // Soul flame intensity
                const flameIntensity = 1.0 + Math.sin(flameTime * 7.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * flameIntensity;
                }
                
                // Soul flame opacity
                const flameOpacity = 0.8 + Math.sin(flameTime * 5.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * flameOpacity;
                }
                
                // Soul flame scale
                const flameScale = 0.9 + Math.sin(flameTime * 6.0) * 0.2;
                mesh.scale.setScalar(flameScale);
            }
        });
    }

    // Animate death aura (protective energy)
    const deathAuraGroup = cardGroup.getObjectByName('deathAuraGroup');
    if (deathAuraGroup) {
        deathAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = deathPulse + mesh.userData.auraPhase;
                
                // Aura pulsing motion
                const auraPulse = Math.sin(auraTime * 4.0) * 0.003;
                const auraRotate = Math.cos(auraTime * 3.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + auraRotate;
                mesh.position.y = mesh.userData.originalPosition.y + auraPulse;
                
                // Aura intensity fluctuation
                const auraIntensity = 1.0 + Math.sin(auraTime * 5.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraIntensity;
                }
                
                // Aura opacity variation
                const auraOpacity = 0.6 + Math.sin(auraTime * 4.0) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * auraOpacity;
                }
                
                // Aura scale pulsing
                const auraScale = 0.8 + Math.sin(auraTime * 3.5) * 0.3;
                mesh.scale.setScalar(auraScale);
            }
        });
    }

    // Animate necromantic effects (magical energy)
    const necromanticEffectsGroup = cardGroup.getObjectByName('necromanticEffectsGroup');
    if (necromanticEffectsGroup) {
        necromanticEffectsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.necroPhase !== undefined) {
                const necroTime = deathPulse * 0.7 + mesh.userData.necroPhase;
                
                // Necromantic energy swirling
                const necroSwirl = Math.sin(necroTime * 4.5) * 0.004;
                const necroRise = Math.cos(necroTime * 3.5) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + necroSwirl;
                mesh.position.y = mesh.userData.originalPosition.y + necroRise;
                
                // Necromantic intensity fluctuation
                const necroIntensity = 1.0 + Math.sin(necroTime * 6.0) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * necroIntensity;
                }
                
                // Necromantic scale variation
                const necroScale = 0.9 + Math.sin(necroTime * 4.0) * 0.2;
                mesh.scale.setScalar(necroScale);
            }
        });
    }

    // Overall guardian presence (epic death magic)
    const guardianPulse = 1 + Math.sin(time * 1.8) * 0.06;
    const guardianSway = Math.cos(time * 4.0) * 0.0006;
    cardGroup.scale.setScalar(0.8 * guardianPulse);
    cardGroup.position.x += guardianSway;
    cardGroup.position.z += guardianSway * 0.7;
}

// Export the bone guardian card data for the loot system
export const BONE_GUARDIAN_CARD_DATA = {
    name: "Bone Guardian",
    description: 'Summons a skeletal guardian that blocks enemy attacks and retaliates with bone magic, providing strong defensive presence.',
    category: 'card',
    rarity: 'epic',
    effect: 'bone_guardian',
    effectValue: 1, // Number of bone guardians summoned
    createFunction: createBoneGuardianCard,
    updateFunction: updateBoneGuardianCardAnimation,
    voxelModel: 'bone_guardian_card',
    glow: {
        color: 0x8A2BE2,
        intensity: 1.5
    }
};

export default createBoneGuardianCard;