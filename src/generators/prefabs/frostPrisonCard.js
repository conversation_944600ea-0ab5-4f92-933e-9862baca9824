import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Frost Prison Card Prefab
 * Creates ice walls that block enemy movement and damage them with frost
 */

// Frost prison specific colors
const FROST_COLORS = {
    ICE_BLUE: 0x87CEEB,           // Main ice wall color
    CRYSTAL_ICE: 0xE0FFFF,        // Bright ice crystals
    FROZEN_WHITE: 0xF0F8FF,       // Frozen highlights
    ARCTIC_BLUE: 0x4682B4,        // Deep ice color
    FROST_CYAN: 0x00FFFF,         // Frost energy
    GLACIAL_TEAL: 0x008B8B,       // Dark ice accents
    DIAMOND_ICE: 0xB0E0E6,        // Shimmering ice
    SNOW_WHITE: 0xFFFAFA          // Snow/frost effects
};

/**
 * Create a frost prison card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The frost prison card 3D model
 */
export function createFrostPrisonCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'FrostPrisonCard';

    // Frost prison materials
    const iceBlueMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.ICE_BLUE,
        emissive: FROST_COLORS.ICE_BLUE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const crystalIceMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.CRYSTAL_ICE,
        emissive: FROST_COLORS.CRYSTAL_ICE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.95
    });

    const frozenWhiteMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.FROZEN_WHITE,
        emissive: FROST_COLORS.FROZEN_WHITE,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 1.0
    });

    const arcticBlueMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.ARCTIC_BLUE,
        emissive: FROST_COLORS.ARCTIC_BLUE,
        emissiveIntensity: 0.35,
        transparent: true,
        opacity: 0.8
    });

    const frostCyanMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.FROST_CYAN,
        emissive: FROST_COLORS.FROST_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const glacialTealMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.GLACIAL_TEAL,
        emissive: FROST_COLORS.GLACIAL_TEAL,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const diamondIceMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.DIAMOND_ICE,
        emissive: FROST_COLORS.DIAMOND_ICE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const snowWhiteMaterial = new THREE.MeshLambertMaterial({
        color: FROST_COLORS.SNOW_WHITE,
        emissive: FROST_COLORS.SNOW_WHITE,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Ice wall structure (prison walls)
    const iceWallVoxels = [
        // Central ice pillar
        { x: 0.0, y: 0.0, z: 0.0, material: crystalIceMaterial },
        { x: 0.0, y: 0.04, z: 0.0, material: iceBlueMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: crystalIceMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: frozenWhiteMaterial },
        { x: 0.0, y: 0.16, z: 0.0, material: crystalIceMaterial },
        { x: 0.0, y: 0.20, z: 0.0, material: iceBlueMaterial },
        
        // Left wall section
        { x: -0.08, y: 0.0, z: 0.0, material: arcticBlueMaterial },
        { x: -0.08, y: 0.04, z: 0.0, material: iceBlueMaterial },
        { x: -0.08, y: 0.08, z: 0.0, material: glacialTealMaterial },
        { x: -0.08, y: 0.12, z: 0.0, material: crystalIceMaterial },
        { x: -0.12, y: 0.06, z: 0.0, material: diamondIceMaterial },
        { x: -0.16, y: 0.08, z: 0.0, material: arcticBlueMaterial },
        
        // Right wall section
        { x: 0.08, y: 0.0, z: 0.0, material: arcticBlueMaterial },
        { x: 0.08, y: 0.04, z: 0.0, material: iceBlueMaterial },
        { x: 0.08, y: 0.08, z: 0.0, material: glacialTealMaterial },
        { x: 0.08, y: 0.12, z: 0.0, material: crystalIceMaterial },
        { x: 0.12, y: 0.06, z: 0.0, material: diamondIceMaterial },
        { x: 0.16, y: 0.08, z: 0.0, material: arcticBlueMaterial },
        
        // Front wall section
        { x: 0.0, y: 0.0, z: 0.08, material: glacialTealMaterial },
        { x: 0.0, y: 0.04, z: 0.12, material: iceBlueMaterial },
        { x: 0.0, y: 0.08, z: 0.16, material: crystalIceMaterial },
        { x: 0.0, y: 0.12, z: 0.08, material: diamondIceMaterial },
        
        // Back wall section
        { x: 0.0, y: 0.0, z: -0.08, material: glacialTealMaterial },
        { x: 0.0, y: 0.04, z: -0.12, material: iceBlueMaterial },
        { x: 0.0, y: 0.08, z: -0.16, material: crystalIceMaterial },
        { x: 0.0, y: 0.12, z: -0.08, material: diamondIceMaterial }
    ];

    // Frost crystals (decorative ice formations)
    const frostCrystalVoxels = [
        // Top crystals
        { x: -0.04, y: 0.24, z: -0.04, material: crystalIceMaterial },
        { x: 0.04, y: 0.24, z: 0.04, material: crystalIceMaterial },
        { x: -0.06, y: 0.28, z: 0.02, material: frozenWhiteMaterial },
        { x: 0.06, y: 0.28, z: -0.02, material: frozenWhiteMaterial },
        { x: 0.0, y: 0.32, z: 0.0, material: diamondIceMaterial },
        
        // Side crystals
        { x: -0.20, y: 0.12, z: 0.04, material: crystalIceMaterial },
        { x: 0.20, y: 0.12, z: -0.04, material: crystalIceMaterial },
        { x: -0.24, y: 0.16, z: 0.08, material: frozenWhiteMaterial },
        { x: 0.24, y: 0.16, z: -0.08, material: frozenWhiteMaterial },
        { x: -0.18, y: 0.20, z: 0.06, material: diamondIceMaterial },
        { x: 0.18, y: 0.20, z: -0.06, material: diamondIceMaterial },
        
        // Base crystals
        { x: -0.12, y: -0.04, z: 0.12, material: glacialTealMaterial },
        { x: 0.12, y: -0.04, z: -0.12, material: glacialTealMaterial },
        { x: -0.08, y: -0.08, z: 0.16, material: arcticBlueMaterial },
        { x: 0.08, y: -0.08, z: -0.16, material: arcticBlueMaterial }
    ];

    // Frost energy (magical cold energy)
    const frostEnergyVoxels = [
        // Inner energy ring
        { x: -0.10, y: 0.14, z: -0.06, material: frostCyanMaterial },
        { x: 0.10, y: 0.14, z: 0.06, material: frostCyanMaterial },
        { x: -0.06, y: 0.18, z: -0.10, material: frostCyanMaterial },
        { x: 0.06, y: 0.18, z: 0.10, material: frostCyanMaterial },
        { x: -0.14, y: 0.10, z: 0.0, material: frostCyanMaterial },
        { x: 0.14, y: 0.10, z: 0.0, material: frostCyanMaterial },
        
        // Middle energy ring
        { x: -0.14, y: 0.18, z: -0.10, material: snowWhiteMaterial },
        { x: 0.14, y: 0.18, z: 0.10, material: snowWhiteMaterial },
        { x: -0.10, y: 0.22, z: -0.14, material: snowWhiteMaterial },
        { x: 0.10, y: 0.22, z: 0.14, material: snowWhiteMaterial },
        { x: -0.18, y: 0.14, z: 0.04, material: snowWhiteMaterial },
        { x: 0.18, y: 0.14, z: -0.04, material: snowWhiteMaterial },
        
        // Outer energy ring
        { x: -0.18, y: 0.22, z: -0.14, material: frostCyanMaterial },
        { x: 0.18, y: 0.22, z: 0.14, material: frostCyanMaterial },
        { x: -0.14, y: 0.26, z: -0.18, material: frostCyanMaterial },
        { x: 0.14, y: 0.26, z: 0.18, material: frostCyanMaterial },
        { x: -0.22, y: 0.18, z: 0.08, material: frostCyanMaterial },
        { x: 0.22, y: 0.18, z: -0.08, material: frostCyanMaterial }
    ];

    // Frost particles (swirling ice particles)
    const frostParticleVoxels = [
        // Particle swirl 1
        { x: -0.26, y: 0.26, z: 0.0, material: snowWhiteMaterial },
        { x: 0.26, y: 0.26, z: 0.0, material: snowWhiteMaterial },
        { x: -0.30, y: 0.22, z: 0.04, material: frostCyanMaterial },
        { x: 0.30, y: 0.22, z: -0.04, material: frostCyanMaterial },
        { x: -0.34, y: 0.18, z: 0.08, material: snowWhiteMaterial },
        { x: 0.34, y: 0.18, z: -0.08, material: snowWhiteMaterial },
        
        // Particle swirl 2
        { x: 0.0, y: 0.30, z: -0.26, material: frostCyanMaterial },
        { x: 0.0, y: 0.30, z: 0.26, material: frostCyanMaterial },
        { x: 0.04, y: 0.34, z: -0.22, material: snowWhiteMaterial },
        { x: -0.04, y: 0.34, z: 0.22, material: snowWhiteMaterial },
        { x: 0.08, y: 0.38, z: -0.18, material: frostCyanMaterial },
        { x: -0.08, y: 0.38, z: 0.18, material: frostCyanMaterial },
        
        // Ground frost
        { x: -0.22, y: -0.12, z: 0.22, material: snowWhiteMaterial },
        { x: 0.22, y: -0.12, z: -0.22, material: snowWhiteMaterial },
        { x: -0.18, y: -0.16, z: 0.26, material: frostCyanMaterial },
        { x: 0.18, y: -0.16, z: -0.26, material: frostCyanMaterial }
    ];

    // Create ice wall group
    const iceWallGroup = new THREE.Group();
    iceWallGroup.name = 'iceWall';

    // Add ice wall voxels
    iceWallVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        iceWallGroup.add(mesh);
    });

    // Create frost crystals group
    const frostCrystalGroup = new THREE.Group();
    frostCrystalGroup.name = 'frostCrystals';

    // Add frost crystal voxels
    frostCrystalVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.crystalPhase = index * 0.15; // Stagger animation
        frostCrystalGroup.add(mesh);
    });

    // Create frost energy group
    const frostEnergyGroup = new THREE.Group();
    frostEnergyGroup.name = 'frostEnergy';

    // Add frost energy voxels
    frostEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.1; // Stagger animation
        frostEnergyGroup.add(mesh);
    });

    // Create frost particles group
    const frostParticleGroup = new THREE.Group();
    frostParticleGroup.name = 'frostParticles';

    // Add frost particle voxels
    frostParticleVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.particlePhase = index * 0.08; // Stagger animation
        frostParticleGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(iceWallGroup);
    cardGroup.add(frostCrystalGroup);
    cardGroup.add(frostEnergyGroup);
    cardGroup.add(frostParticleGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        iceGlow: 0,
        crystalShimmer: 0,
        frostFlow: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update frost prison card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateFrostPrisonCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.iceGlow += deltaTime * 2.0; // Ice glow speed
    cardGroup.userData.crystalShimmer += deltaTime * 2.5; // Crystal shimmer speed
    cardGroup.userData.frostFlow += deltaTime * 3.0; // Frost flow speed

    const time = cardGroup.userData.animationTime;
    const iceGlow = cardGroup.userData.iceGlow;
    const crystalShimmer = cardGroup.userData.crystalShimmer;
    const frostFlow = cardGroup.userData.frostFlow;

    // Animate ice walls (cold pulsing)
    const iceWallGroup = cardGroup.getObjectByName('iceWall');
    if (iceWallGroup) {
        // Ice glow pulsing
        const iceIntensity = 1.0 + Math.sin(iceGlow * 1.5) * 0.3;
        iceWallGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * iceIntensity;
            }
        });
    }

    // Animate frost crystals (sparkling)
    const frostCrystalGroup = cardGroup.getObjectByName('frostCrystals');
    if (frostCrystalGroup) {
        frostCrystalGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.crystalPhase !== undefined) {
                const crystalTime = crystalShimmer + mesh.userData.crystalPhase;
                
                // Crystal sparkling motion
                const sparkle = Math.sin(crystalTime * 4.0) * 0.001;
                const shimmer = Math.cos(crystalTime * 3.0) * 0.0008;
                
                mesh.position.x = mesh.userData.originalPosition.x + sparkle;
                mesh.position.y = mesh.userData.originalPosition.y + shimmer;
                
                // Crystal intensity fluctuation
                const crystalIntensity = 1.0 + Math.sin(crystalTime * 5.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crystalIntensity;
                }
                
                // Crystal scale variation
                const crystalScale = 0.9 + Math.sin(crystalTime * 4.5) * 0.15;
                mesh.scale.setScalar(crystalScale);
            }
        });
    }

    // Animate frost energy (flowing cold energy)
    const frostEnergyGroup = cardGroup.getObjectByName('frostEnergy');
    if (frostEnergyGroup) {
        frostEnergyGroup.rotation.y = time * 0.8; // Slow energy rotation
        
        frostEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = frostFlow + mesh.userData.energyPhase;
                
                // Energy flow motion
                const flow = Math.sin(energyTime * 3.0) * 0.002;
                const pulse = Math.cos(energyTime * 2.5) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + flow;
                mesh.position.z = mesh.userData.originalPosition.z + pulse;
                
                // Energy intensity fluctuation
                const energyIntensity = 1.0 + Math.sin(energyTime * 4.5) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyIntensity;
                }
                
                // Energy scale pulsing
                const energyScale = 0.8 + Math.sin(energyTime * 4.0) * 0.3;
                mesh.scale.setScalar(energyScale);
            }
        });
    }

    // Animate frost particles (swirling particles)
    const frostParticleGroup = cardGroup.getObjectByName('frostParticles');
    if (frostParticleGroup) {
        frostParticleGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.particlePhase !== undefined) {
                const particleTime = frostFlow + mesh.userData.particlePhase;
                
                // Particle swirl motion
                const swirl = Math.sin(particleTime * 5.0) * 0.003;
                const drift = Math.cos(particleTime * 4.5) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + swirl;
                mesh.position.y = mesh.userData.originalPosition.y + drift;
                
                // Particle intensity fluctuation
                const particleIntensity = 1.0 + Math.sin(particleTime * 6.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * particleIntensity;
                }
                
                // Particle scale variation
                const particleScale = 0.7 + Math.sin(particleTime * 5.5) * 0.2;
                mesh.scale.setScalar(particleScale);
            }
        });
    }

    // Overall frost prison pulsing (icy cold)
    const frostPulse = 1 + Math.sin(time * 2.0) * 0.05;
    cardGroup.scale.setScalar(0.8 * frostPulse);
}

// Export the frost prison card data for the loot system
export const FROST_PRISON_CARD_DATA = {
    name: "Frost Prison",
    description: 'Creates ice walls that block enemy movement and projectiles for 40 seconds. Enemies touching walls take frost damage and are slowed.',
    category: 'card',
    rarity: 'epic',
    effect: 'frost_prison',
    effectValue: 40, // Duration in seconds
    createFunction: createFrostPrisonCard,
    updateFunction: updateFrostPrisonCardAnimation,
    voxelModel: 'frost_prison_card',
    glow: {
        color: 0x87CEEB,
        intensity: 1.4
    }
};

export default createFrostPrisonCard;