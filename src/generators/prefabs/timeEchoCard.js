import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Time Echo Card Prefab
 * Creates a temporal shadow copy that repeats your last action
 */

// Time echo specific colors
const ECHO_COLORS = {
    ECHO_PURPLE: 0x9932CC,          // Dark orchid temporal energy
    ECHO_BLUE: 0x4169E1,            // Royal blue time magic
    SHADOW_GRAY: 0x696969,          // Dim gray shadow essence
    TIME_GOLD: 0xDAA520,            // Goldenrod time streams
    PHANTOM_WHITE: 0xF8F8FF,        // Ghost white phantom energy
    TEMPORAL_CYAN: 0x20B2AA         // Light sea green temporal rifts
};

/**
 * Create a time echo card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The time echo card 3D model
 */
export function createTimeEchoCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'TimeEchoCard';

    // Time echo materials
    const echoPurpleMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.ECHO_PURPLE,
        emissive: ECHO_COLORS.ECHO_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const echoBlueMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.ECHO_BLUE,
        emissive: ECHO_COLORS.ECHO_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.SHADOW_GRAY,
        emissive: ECHO_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.6
    });

    const timeGoldMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.TIME_GOLD,
        emissive: ECHO_COLORS.TIME_GOLD,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.9
    });

    const phantomWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.PHANTOM_WHITE,
        emissive: ECHO_COLORS.PHANTOM_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.5
    });

    const temporalCyanMaterial = new THREE.MeshLambertMaterial({
        color: ECHO_COLORS.TEMPORAL_CYAN,
        emissive: ECHO_COLORS.TEMPORAL_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central temporal figure (simplified humanoid shadow)
    const temporalFigureVoxels = [
        // Head
        { x: 0.0, y: 0.20, z: 0, material: shadowGrayMaterial },
        { x: -0.04, y: 0.20, z: 0, material: shadowGrayMaterial },
        { x: 0.04, y: 0.20, z: 0, material: shadowGrayMaterial },
        
        // Torso
        { x: 0.0, y: 0.12, z: 0, material: echoPurpleMaterial },
        { x: 0.0, y: 0.04, z: 0, material: echoPurpleMaterial },
        { x: 0.0, y: -0.04, z: 0, material: echoPurpleMaterial },
        { x: -0.04, y: 0.08, z: 0, material: echoBlueMaterial },
        { x: 0.04, y: 0.08, z: 0, material: echoBlueMaterial },
        
        // Arms
        { x: -0.08, y: 0.08, z: 0, material: shadowGrayMaterial },
        { x: 0.08, y: 0.08, z: 0, material: shadowGrayMaterial },
        { x: -0.12, y: 0.04, z: 0, material: temporalCyanMaterial },
        { x: 0.12, y: 0.04, z: 0, material: temporalCyanMaterial },
        
        // Legs
        { x: -0.04, y: -0.12, z: 0, material: shadowGrayMaterial },
        { x: 0.04, y: -0.12, z: 0, material: shadowGrayMaterial },
        { x: -0.04, y: -0.20, z: 0, material: echoBlueMaterial },
        { x: 0.04, y: -0.20, z: 0, material: echoBlueMaterial }
    ];

    // Time streams (flowing temporal energy around figure)
    const timeStreamsVoxels = [
        // Left time stream
        { x: -0.20, y: 0.16, z: 0.04, material: timeGoldMaterial },
        { x: -0.24, y: 0.08, z: 0.08, material: timeGoldMaterial },
        { x: -0.20, y: 0.0, z: 0.04, material: timeGoldMaterial },
        { x: -0.24, y: -0.08, z: 0.08, material: timeGoldMaterial },
        { x: -0.20, y: -0.16, z: 0.04, material: timeGoldMaterial },
        
        // Right time stream
        { x: 0.20, y: 0.16, z: -0.04, material: timeGoldMaterial },
        { x: 0.24, y: 0.08, z: -0.08, material: timeGoldMaterial },
        { x: 0.20, y: 0.0, z: -0.04, material: timeGoldMaterial },
        { x: 0.24, y: -0.08, z: -0.08, material: timeGoldMaterial },
        { x: 0.20, y: -0.16, z: -0.04, material: timeGoldMaterial },
        
        // Vertical time streams
        { x: 0.0, y: 0.32, z: 0.06, material: temporalCyanMaterial },
        { x: 0.0, y: 0.28, z: 0.12, material: temporalCyanMaterial },
        { x: 0.0, y: -0.28, z: 0.06, material: temporalCyanMaterial },
        { x: 0.0, y: -0.32, z: 0.12, material: temporalCyanMaterial }
    ];

    // Echo phantoms (ghostly duplicates around main figure)
    const echoPhantomVoxels = [
        // Left echo phantom
        { x: -0.16, y: 0.20, z: 0.08, material: phantomWhiteMaterial },
        { x: -0.16, y: 0.12, z: 0.08, material: phantomWhiteMaterial },
        { x: -0.16, y: 0.04, z: 0.08, material: phantomWhiteMaterial },
        { x: -0.16, y: -0.04, z: 0.08, material: phantomWhiteMaterial },
        { x: -0.16, y: -0.12, z: 0.08, material: phantomWhiteMaterial },
        
        // Right echo phantom
        { x: 0.16, y: 0.20, z: -0.08, material: phantomWhiteMaterial },
        { x: 0.16, y: 0.12, z: -0.08, material: phantomWhiteMaterial },
        { x: 0.16, y: 0.04, z: -0.08, material: phantomWhiteMaterial },
        { x: 0.16, y: -0.04, z: -0.08, material: phantomWhiteMaterial },
        { x: 0.16, y: -0.12, z: -0.08, material: phantomWhiteMaterial },
        
        // Back echo phantom
        { x: 0.0, y: 0.16, z: -0.12, material: echoPurpleMaterial },
        { x: 0.0, y: 0.08, z: -0.12, material: echoPurpleMaterial },
        { x: 0.0, y: 0.0, z: -0.12, material: echoPurpleMaterial },
        { x: 0.0, y: -0.08, z: -0.12, material: echoPurpleMaterial },
        
        // Front echo phantom
        { x: 0.0, y: 0.16, z: 0.12, material: echoBlueMaterial },
        { x: 0.0, y: 0.08, z: 0.12, material: echoBlueMaterial },
        { x: 0.0, y: 0.0, z: 0.12, material: echoBlueMaterial },
        { x: 0.0, y: -0.08, z: 0.12, material: echoBlueMaterial }
    ];

    // Temporal ripples (energy waves emanating from figure)
    const temporalRipplesVoxels = [
        // Inner ripple ring
        { x: -0.12, y: 0.12, z: 0.02, material: temporalCyanMaterial },
        { x: 0.12, y: 0.12, z: 0.02, material: temporalCyanMaterial },
        { x: 0.12, y: -0.12, z: 0.02, material: temporalCyanMaterial },
        { x: -0.12, y: -0.12, z: 0.02, material: temporalCyanMaterial },
        
        // Outer ripple ring
        { x: -0.28, y: 0.0, z: 0.04, material: timeGoldMaterial },
        { x: 0.28, y: 0.0, z: 0.04, material: timeGoldMaterial },
        { x: 0.0, y: 0.28, z: 0.04, material: timeGoldMaterial },
        { x: 0.0, y: -0.28, z: 0.04, material: timeGoldMaterial },
        
        // Diagonal ripples
        { x: -0.20, y: 0.20, z: 0.06, material: echoPurpleMaterial },
        { x: 0.20, y: 0.20, z: 0.06, material: echoPurpleMaterial },
        { x: 0.20, y: -0.20, z: 0.06, material: echoPurpleMaterial },
        { x: -0.20, y: -0.20, z: 0.06, material: echoPurpleMaterial },
        
        // Distant ripples
        { x: -0.32, y: 0.16, z: 0.10, material: phantomWhiteMaterial },
        { x: 0.32, y: 0.16, z: 0.10, material: phantomWhiteMaterial },
        { x: 0.32, y: -0.16, z: 0.10, material: phantomWhiteMaterial },
        { x: -0.32, y: -0.16, z: 0.10, material: phantomWhiteMaterial }
    ];

    // Create temporal figure group
    const temporalFigureGroup = new THREE.Group();
    temporalFigureGroup.name = 'temporalFigure';

    // Add temporal figure voxels
    temporalFigureVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        temporalFigureGroup.add(mesh);
    });

    // Create time streams group
    const timeStreamsGroup = new THREE.Group();
    timeStreamsGroup.name = 'timeStreams';

    // Add time streams voxels
    timeStreamsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.streamPhase = index * 0.25; // Stagger animation
        timeStreamsGroup.add(mesh);
    });

    // Create echo phantoms group
    const echoPhantomGroup = new THREE.Group();
    echoPhantomGroup.name = 'echoPhantoms';

    // Add echo phantom voxels
    echoPhantomVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.phantomPhase = index * 0.15; // Stagger animation
        echoPhantomGroup.add(mesh);
    });

    // Create temporal ripples group
    const temporalRipplesGroup = new THREE.Group();
    temporalRipplesGroup.name = 'temporalRipples';

    // Add temporal ripples voxels
    temporalRipplesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.ripplePhase = index * 0.2; // Stagger animation
        temporalRipplesGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(temporalFigureGroup);
    cardGroup.add(timeStreamsGroup);
    cardGroup.add(echoPhantomGroup);
    cardGroup.add(temporalRipplesGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        temporalRotation: 0,
        echoPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update time echo card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateTimeEchoCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.temporalRotation += deltaTime * 1.5; // Temporal rotation speed
    cardGroup.userData.echoPulse += deltaTime * 4.0; // Echo pulse speed

    const time = cardGroup.userData.animationTime;
    const temporalRotation = cardGroup.userData.temporalRotation;
    const echoPulse = cardGroup.userData.echoPulse;

    // Animate temporal figure (subtle pulsing)
    const temporalFigureGroup = cardGroup.getObjectByName('temporalFigure');
    if (temporalFigureGroup) {
        // Figure energy pulsing (shadow glow)
        const figurePulse = 0.4 + Math.sin(echoPulse * 2.0) * 0.4;
        temporalFigureGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * figurePulse;
            }
        });
    }

    // Animate time streams (flowing motion)
    const timeStreamsGroup = cardGroup.getObjectByName('timeStreams');
    if (timeStreamsGroup) {
        timeStreamsGroup.rotation.y = temporalRotation * 0.5;
        
        // Time stream flowing and pulsing
        timeStreamsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.streamPhase !== undefined) {
                const streamTime = echoPulse + mesh.userData.streamPhase;
                
                // Flowing motion along streams
                const flow = Math.sin(streamTime * 3.0) * 0.008;
                mesh.position.y = mesh.userData.originalPosition.y + flow;
                
                // Time stream energy pulsing
                const streamPulse = 0.7 + Math.sin(streamTime * 4.5) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * streamPulse;
                }
            }
        });
    }

    // Animate echo phantoms (ghostly phasing)
    const echoPhantomGroup = cardGroup.getObjectByName('echoPhantoms');
    if (echoPhantomGroup) {
        echoPhantomGroup.rotation.y = -temporalRotation * 0.8; // Counter-rotate
        
        // Echo phantom phasing and floating
        echoPhantomGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.phantomPhase !== undefined) {
                const phantomTime = echoPulse + mesh.userData.phantomPhase;
                
                // Ghostly floating motion
                const floatX = Math.sin(phantomTime * 2.2) * 0.005;
                const floatY = Math.cos(phantomTime * 1.8) * 0.005;
                const floatZ = Math.sin(phantomTime * 2.5) * 0.005;
                
                mesh.position.x = mesh.userData.originalPosition.x + floatX;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                mesh.position.z = mesh.userData.originalPosition.z + floatZ;
                
                // Echo phantom fading
                const phantomFade = 0.3 + Math.sin(phantomTime * 3.5) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * phantomFade;
                }
                
                // Phantom energy pulsing
                const phantomPulse = 0.5 + Math.sin(phantomTime * 5.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * phantomPulse;
                }
            }
        });
    }

    // Animate temporal ripples (expanding waves)
    const temporalRipplesGroup = cardGroup.getObjectByName('temporalRipples');
    if (temporalRipplesGroup) {
        temporalRipplesGroup.rotation.z = temporalRotation * 1.2; // Fast rotation
        
        // Temporal ripple expansion and pulsing
        temporalRipplesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.ripplePhase !== undefined) {
                const rippleTime = echoPulse + mesh.userData.ripplePhase;
                
                // Ripple expansion effect
                const expansion = 1 + Math.sin(rippleTime * 2.8) * 0.1;
                mesh.scale.setScalar(expansion);
                
                // Temporal ripple energy pulsing
                const ripplePulse = 0.6 + Math.sin(rippleTime * 6.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * ripplePulse;
                }
                
                // Ripple wave effect
                const waveOpacity = 0.7 + Math.sin(rippleTime * 4.2) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * waveOpacity;
                }
            }
        });
    }

    // Gentle overall temporal pulsing
    const temporalPulseScale = 1 + Math.sin(time * 2.2) * 0.06;
    cardGroup.scale.setScalar(0.8 * temporalPulseScale);
}

// Export the time echo card data for the loot system
export const TIME_ECHO_CARD_DATA = {
    name: 'Time Echo',
    description: 'Creates a temporal shadow copy that mimics and repeats your last significant action with a slight delay, effectively doubling your presence on the battlefield.',
    category: 'card',
    rarity: 'epic',
    effect: 'time_echo',
    effectValue: 1, // Number of echo shadows created
    createFunction: createTimeEchoCard,
    updateFunction: updateTimeEchoCardAnimation,
    voxelModel: 'time_echo_card',
    glow: {
        color: 0x9932CC,
        intensity: 1.3
    }
};

export default createTimeEchoCard;