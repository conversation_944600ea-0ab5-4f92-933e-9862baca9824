import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a time-worn pillar object - weathered stone showing temporal aging effects
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Time-worn pillar group
 */
export function createTimeWornPillarObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 666);

    // Use consistent voxel size
    const pillarVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('time_worn_pillar_voxel', () =>
        new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize)
    );

    // Weathered stone materials (different levels of aging)
    const oldStoneMaterial = _getMaterialByHex_Cached('5a5a5a', {
        emissive: new THREE.Color(0x0a0a0a),
        emissiveIntensity: 0.02,
        roughness: 0.95,
        metalness: 0.05
    });

    const veryOldStoneMaterial = _getMaterialByHex_Cached('4a4a4a', {
        emissive: new THREE.Color(0x0a0a0a),
        emissiveIntensity: 0.01,
        roughness: 0.98,
        metalness: 0.02
    });

    const ancientStoneMaterial = _getMaterialByHex_Cached('3a3a3a', {
        emissive: new THREE.Color(0x050505),
        emissiveIntensity: 0.005,
        roughness: 1.0,
        metalness: 0.0
    });

    // Temporal erosion material (where time has eaten away at the stone)
    const erodedMaterial = _getMaterialByHex_Cached('2a2a2a', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x1a1a2a),
        emissiveIntensity: 0.05,
        roughness: 1.0,
        metalness: 0.0
    });

    // Base (stable foundation)
    const basePositions = [
        { x: -1, z: -1 }, { x: 0, z: -1 }, { x: 1, z: -1 },
        { x: -1, z: 0 },  { x: 0, z: 0 },  { x: 1, z: 0 },
        { x: -1, z: 1 },  { x: 0, z: 1 },  { x: 1, z: 1 }
    ];

    basePositions.forEach(pos => {
        const baseVoxel = new THREE.Mesh(baseGeometry.clone(), oldStoneMaterial);
        baseVoxel.position.set(
            pos.x * pillarVoxelSize,
            pillarVoxelSize * 0.5,
            pos.z * pillarVoxelSize
        );
        baseVoxel.userData.isFloorObject = true;
        baseVoxel.userData.hasCollision = true;
        baseVoxel.castShadow = true;
        baseVoxel.receiveShadow = true;
        group.add(baseVoxel);
    });

    // Main pillar shaft (with irregular weathering pattern)
    const pillarHeight = 6; // Height in voxels
    
    for (let y = 1; y <= pillarHeight; y++) {
        // Determine weathering level based on height and randomness
        const weatheringFactor = (y / pillarHeight) + (rng() - 0.5) * 0.3;
        
        // Choose material based on weathering
        let material;
        if (weatheringFactor < 0.3) {
            material = oldStoneMaterial;
        } else if (weatheringFactor < 0.6) {
            material = veryOldStoneMaterial;
        } else {
            material = ancientStoneMaterial;
        }

        // Create pillar cross-section with some voxels missing due to erosion
        const pillarPositions = [
            { x: -0.5, z: -0.5, chance: 0.9 }, { x: 0, z: -0.5, chance: 1.0 }, { x: 0.5, z: -0.5, chance: 0.9 },
            { x: -0.5, z: 0, chance: 1.0 },    { x: 0, z: 0, chance: 1.0 },    { x: 0.5, z: 0, chance: 1.0 },
            { x: -0.5, z: 0.5, chance: 0.9 },  { x: 0, z: 0.5, chance: 1.0 },  { x: 0.5, z: 0.5, chance: 0.9 }
        ];

        pillarPositions.forEach(pos => {
            // Some voxels might be eroded away
            if (rng() < pos.chance - (weatheringFactor * 0.2)) {
                const pillarVoxel = new THREE.Mesh(baseGeometry.clone(), material);
                pillarVoxel.position.set(
                    pos.x * pillarVoxelSize,
                    y * pillarVoxelSize,
                    pos.z * pillarVoxelSize
                );
                pillarVoxel.userData.isPillarPart = true;
                pillarVoxel.userData.weatheringLevel = weatheringFactor;
                pillarVoxel.castShadow = true;
                pillarVoxel.receiveShadow = true;
                group.add(pillarVoxel);
            } else if (rng() < 0.3) {
                // Sometimes add an eroded/ghostly piece
                const erodedVoxel = new THREE.Mesh(baseGeometry.clone(), erodedMaterial);
                erodedVoxel.position.set(
                    pos.x * pillarVoxelSize,
                    y * pillarVoxelSize,
                    pos.z * pillarVoxelSize
                );
                erodedVoxel.userData.isErodedPart = true;
                erodedVoxel.castShadow = false;
                erodedVoxel.receiveShadow = true;
                group.add(erodedVoxel);
            }
        });
    }

    // Add some temporal energy traces (showing time damage)
    const traceMaterial = _getMaterialByHex_Cached('6A5ACD', {
        transparent: true,
        opacity: 0.4,
        emissive: new THREE.Color(0x6A5ACD),
        emissiveIntensity: 0.2
    });

    const traceGeometry = getOrCreateGeometry('temporal_trace', () =>
        new THREE.BoxGeometry(pillarVoxelSize * 0.2, pillarVoxelSize * 0.2, pillarVoxelSize * 0.2)
    );

    // Add random temporal traces along the pillar
    for (let i = 0; i < 5; i++) {
        if (rng() < 0.7) {
            const trace = new THREE.Mesh(traceGeometry, traceMaterial);
            trace.position.set(
                (rng() - 0.5) * pillarVoxelSize * 2,
                (1 + rng() * (pillarHeight - 1)) * pillarVoxelSize,
                (rng() - 0.5) * pillarVoxelSize * 2
            );
            trace.userData.isTemporalTrace = true;
            trace.userData.animationPhase = rng() * Math.PI * 2;
            trace.castShadow = false;
            trace.receiveShadow = false;
            group.add(trace);
        }
    }

    // Capital/top (partially crumbled)
    const capitalPositions = [
        { x: -1, z: -1, chance: 0.6 }, { x: 0, z: -1, chance: 0.8 }, { x: 1, z: -1, chance: 0.6 },
        { x: -1, z: 0, chance: 0.8 },  { x: 0, z: 0, chance: 1.0 },  { x: 1, z: 0, chance: 0.8 },
        { x: -1, z: 1, chance: 0.6 },  { x: 0, z: 1, chance: 0.8 },  { x: 1, z: 1, chance: 0.6 }
    ];

    capitalPositions.forEach(pos => {
        if (rng() < pos.chance) {
            const capitalVoxel = new THREE.Mesh(baseGeometry.clone(), veryOldStoneMaterial);
            capitalVoxel.position.set(
                pos.x * pillarVoxelSize,
                (pillarHeight + 1) * pillarVoxelSize,
                pos.z * pillarVoxelSize
            );
            capitalVoxel.userData.isCapitalPart = true;
            capitalVoxel.castShadow = true;
            capitalVoxel.receiveShadow = true;
            group.add(capitalVoxel);
        }
    });

    // Rubble at base (fallen pieces)
    const rubbleGeometry = getOrCreateGeometry('pillar_rubble', () =>
        new THREE.BoxGeometry(pillarVoxelSize * 0.6, pillarVoxelSize * 0.4, pillarVoxelSize * 0.6)
    );

    for (let i = 0; i < 3; i++) {
        if (rng() < 0.8) {
            const rubble = new THREE.Mesh(rubbleGeometry, ancientStoneMaterial);
            const angle = rng() * Math.PI * 2;
            const distance = 1.5 + rng() * 0.5;
            rubble.position.set(
                Math.cos(angle) * distance * pillarVoxelSize,
                pillarVoxelSize * 0.2,
                Math.sin(angle) * distance * pillarVoxelSize
            );
            rubble.rotation.set(
                (rng() - 0.5) * Math.PI * 0.3,
                rng() * Math.PI * 2,
                (rng() - 0.5) * Math.PI * 0.3
            );
            rubble.userData.isRubble = true;
            rubble.castShadow = true;
            rubble.receiveShadow = true;
            group.add(rubble);
        }
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'time_worn_pillar',
        isInteractable: false,
        isEventObject: true,
        objectId: options.userData?.objectId || 'time_worn_pillar',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: 'crumble',
        health: options.health || 3, // More durable than small objects
        isInteriorObject: true,
        voxelScale: pillarVoxelSize,
        hasAnimation: true,
        animationType: 'temporal_flicker'
    };

    // Add collision detection to solid parts only
    group.traverse(child => {
        if (child.isMesh && !child.userData.isTemporalTrace && !child.userData.isErodedPart) {
            child.userData.isFloorObject = true;
            child.userData.hasCollision = true;
            child.userData.objectType = 'time_worn_pillar';
        }
    });

    group.name = 'time_worn_pillar';

    console.log('[TimeWornPillarObject] ✅ Created time-worn pillar with weathering effects');
    return group;
}