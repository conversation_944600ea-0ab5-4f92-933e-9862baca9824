import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Soul Harvest Card Prefab
 * Absorbs souls from defeated enemies to increase damage temporarily
 */

// Soul Harvest specific colors
const SOUL_HARVEST_COLORS = {
    SOUL_BLUE: 0x4169E1,            // Soul essence
    SPECTRAL_WHITE: 0xF8F8FF,       // Spectral energy
    DEATH_BLACK: 0x2F2F2F,          // Death magic
    REAPER_PURPLE: 0x483D8B,        // Reaper energy
    ESSENCE_CYAN: 0x00CED1,         // Soul essence
    SHADOW_GRAY: 0x696969,          // Shadow magic
    HARVEST_GOLD: 0xDAA520,         // Harvest power
    VOID_VIOLET: 0x8A2BE2           // Void energy
};

/**
 * Create a soul harvest card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The soul harvest card 3D model
 */
export function createSoulHarvestCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SoulHarvestCard';

    // Soul Harvest materials
    const soulBlueMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.SOUL_BLUE,
        emissive: SOUL_HARVEST_COLORS.SOUL_BLUE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const spectralWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.SPECTRAL_WHITE,
        emissive: SOUL_HARVEST_COLORS.SPECTRAL_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.7
    });

    const deathBlackMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.DEATH_BLACK,
        emissive: SOUL_HARVEST_COLORS.DEATH_BLACK,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const reaperPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.REAPER_PURPLE,
        emissive: SOUL_HARVEST_COLORS.REAPER_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const essenceCyanMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.ESSENCE_CYAN,
        emissive: SOUL_HARVEST_COLORS.ESSENCE_CYAN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.SHADOW_GRAY,
        emissive: SOUL_HARVEST_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const harvestGoldMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.HARVEST_GOLD,
        emissive: SOUL_HARVEST_COLORS.HARVEST_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const voidVioletMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_HARVEST_COLORS.VOID_VIOLET,
        emissive: SOUL_HARVEST_COLORS.VOID_VIOLET,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Reaper Scythe Core (central harvesting tool)
    const reaperScytheVoxels = [
        // Scythe blade
        { x: 0.0, y: 0.0, z: 0.0, material: spectralWhiteMaterial }, // Center
        { x: -0.04, y: 0.04, z: 0.0, material: deathBlackMaterial }, // Blade left
        { x: 0.04, y: 0.04, z: 0.0, material: deathBlackMaterial }, // Blade right
        { x: -0.06, y: 0.02, z: 0.0, material: shadowGrayMaterial }, // Blade curve left
        { x: 0.06, y: 0.02, z: 0.0, material: shadowGrayMaterial }, // Blade curve right
        { x: -0.08, y: 0.0, z: 0.0, material: deathBlackMaterial }, // Blade tip left
        { x: 0.08, y: 0.0, z: 0.0, material: deathBlackMaterial }, // Blade tip right
        
        // Scythe handle
        { x: 0.0, y: -0.02, z: 0.0, material: shadowGrayMaterial }, // Handle top
        { x: 0.0, y: -0.04, z: 0.0, material: deathBlackMaterial }, // Handle middle
        { x: 0.0, y: -0.06, z: 0.0, material: shadowGrayMaterial }, // Handle bottom
        { x: 0.0, y: -0.08, z: 0.0, material: deathBlackMaterial }, // Handle end
        
        // Scythe energy
        { x: -0.02, y: 0.02, z: 0.02, material: reaperPurpleMaterial }, // Energy left
        { x: 0.02, y: 0.02, z: 0.02, material: reaperPurpleMaterial }, // Energy right
        { x: 0.0, y: 0.02, z: 0.02, material: spectralWhiteMaterial }, // Energy center
        { x: 0.0, y: 0.0, z: 0.02, material: voidVioletMaterial }, // Energy core
        
        // Soul binding points
        { x: -0.04, y: 0.0, z: 0.0, material: harvestGoldMaterial }, // Left binding
        { x: 0.04, y: 0.0, z: 0.0, material: harvestGoldMaterial }, // Right binding
        { x: 0.0, y: 0.06, z: 0.0, material: harvestGoldMaterial } // Top binding
    ];

    // Soul Orbs (collected souls)
    const soulOrbsVoxels = [
        // Inner soul ring
        { x: -0.10, y: 0.0, z: 0.0, material: soulBlueMaterial }, // Left
        { x: 0.10, y: 0.0, z: 0.0, material: soulBlueMaterial }, // Right
        { x: 0.0, y: 0.10, z: 0.0, material: soulBlueMaterial }, // Top
        { x: 0.0, y: -0.10, z: 0.0, material: soulBlueMaterial }, // Bottom
        { x: -0.07, y: 0.07, z: 0.0, material: essenceCyanMaterial }, // Top-left
        { x: 0.07, y: 0.07, z: 0.0, material: essenceCyanMaterial }, // Top-right
        { x: -0.07, y: -0.07, z: 0.0, material: essenceCyanMaterial }, // Bottom-left
        { x: 0.07, y: -0.07, z: 0.0, material: essenceCyanMaterial }, // Bottom-right
        
        // Soul cores
        { x: -0.08, y: 0.04, z: 0.04, material: spectralWhiteMaterial }, // Soul core 1
        { x: 0.08, y: 0.04, z: 0.04, material: spectralWhiteMaterial }, // Soul core 2
        { x: 0.04, y: -0.08, z: 0.04, material: spectralWhiteMaterial }, // Soul core 3
        { x: -0.04, y: -0.08, z: 0.04, material: spectralWhiteMaterial }, // Soul core 4
        { x: 0.0, y: 0.08, z: 0.04, material: spectralWhiteMaterial }, // Soul core 5
        
        // Middle soul ring
        { x: -0.14, y: 0.0, z: 0.0, material: essenceCyanMaterial }, // Far left
        { x: 0.14, y: 0.0, z: 0.0, material: essenceCyanMaterial }, // Far right
        { x: 0.0, y: 0.14, z: 0.0, material: essenceCyanMaterial }, // Far top
        { x: 0.0, y: -0.14, z: 0.0, material: essenceCyanMaterial }, // Far bottom
        { x: -0.10, y: 0.10, z: 0.0, material: soulBlueMaterial }, // Far top-left
        { x: 0.10, y: 0.10, z: 0.0, material: soulBlueMaterial }, // Far top-right
        { x: -0.10, y: -0.10, z: 0.0, material: soulBlueMaterial }, // Far bottom-left
        { x: 0.10, y: -0.10, z: 0.0, material: soulBlueMaterial }, // Far bottom-right
        
        // Outer soul ring
        { x: -0.18, y: 0.0, z: 0.0, material: voidVioletMaterial }, // Outer left
        { x: 0.18, y: 0.0, z: 0.0, material: voidVioletMaterial }, // Outer right
        { x: 0.0, y: 0.18, z: 0.0, material: voidVioletMaterial }, // Outer top
        { x: 0.0, y: -0.18, z: 0.0, material: voidVioletMaterial }, // Outer bottom
        { x: -0.13, y: 0.13, z: 0.0, material: reaperPurpleMaterial }, // Outer top-left
        { x: 0.13, y: 0.13, z: 0.0, material: reaperPurpleMaterial }, // Outer top-right
        { x: -0.13, y: -0.13, z: 0.0, material: reaperPurpleMaterial }, // Outer bottom-left
        { x: 0.13, y: -0.13, z: 0.0, material: reaperPurpleMaterial } // Outer bottom-right
    ];

    // Spectral Energy Streams (soul flow)
    const spectralEnergyVoxels = [
        // Inner energy streams
        { x: -0.12, y: 0.02, z: 0.06, material: spectralWhiteMaterial },
        { x: 0.12, y: -0.02, z: 0.06, material: spectralWhiteMaterial },
        { x: 0.02, y: 0.12, z: 0.06, material: spectralWhiteMaterial },
        { x: -0.02, y: -0.12, z: 0.06, material: spectralWhiteMaterial },
        { x: -0.085, y: 0.085, z: 0.06, material: essenceCyanMaterial },
        { x: 0.085, y: 0.085, z: 0.06, material: essenceCyanMaterial },
        { x: -0.085, y: -0.085, z: 0.06, material: essenceCyanMaterial },
        { x: 0.085, y: -0.085, z: 0.06, material: essenceCyanMaterial },
        
        // Middle energy streams
        { x: -0.16, y: 0.04, z: 0.04, material: harvestGoldMaterial },
        { x: 0.16, y: -0.04, z: 0.04, material: harvestGoldMaterial },
        { x: 0.04, y: 0.16, z: 0.04, material: harvestGoldMaterial },
        { x: -0.04, y: -0.16, z: 0.04, material: harvestGoldMaterial },
        { x: -0.113, y: 0.113, z: 0.04, material: soulBlueMaterial },
        { x: 0.113, y: 0.113, z: 0.04, material: soulBlueMaterial },
        { x: -0.113, y: -0.113, z: 0.04, material: soulBlueMaterial },
        { x: 0.113, y: -0.113, z: 0.04, material: soulBlueMaterial },
        
        // Outer energy streams
        { x: -0.20, y: 0.06, z: 0.02, material: voidVioletMaterial },
        { x: 0.20, y: -0.06, z: 0.02, material: voidVioletMaterial },
        { x: 0.06, y: 0.20, z: 0.02, material: voidVioletMaterial },
        { x: -0.06, y: -0.20, z: 0.02, material: voidVioletMaterial },
        { x: -0.141, y: 0.141, z: 0.02, material: reaperPurpleMaterial },
        { x: 0.141, y: 0.141, z: 0.02, material: reaperPurpleMaterial },
        { x: -0.141, y: -0.141, z: 0.02, material: reaperPurpleMaterial },
        { x: 0.141, y: -0.141, z: 0.02, material: reaperPurpleMaterial },
        
        // Far energy streams
        { x: -0.24, y: 0.08, z: 0.0, material: shadowGrayMaterial },
        { x: 0.24, y: -0.08, z: 0.0, material: shadowGrayMaterial },
        { x: 0.08, y: 0.24, z: 0.0, material: shadowGrayMaterial },
        { x: -0.08, y: -0.24, z: 0.0, material: shadowGrayMaterial },
        { x: -0.17, y: 0.17, z: 0.0, material: deathBlackMaterial },
        { x: 0.17, y: 0.17, z: 0.0, material: deathBlackMaterial },
        { x: -0.17, y: -0.17, z: 0.0, material: deathBlackMaterial },
        { x: 0.17, y: -0.17, z: 0.0, material: deathBlackMaterial }
    ];

    // Death Aura (power enhancement field)
    const deathAuraVoxels = [
        // Inner aura ring
        { x: -0.11, y: 0.03, z: 0.08, material: deathBlackMaterial },
        { x: 0.11, y: -0.03, z: 0.08, material: deathBlackMaterial },
        { x: 0.03, y: 0.11, z: 0.08, material: deathBlackMaterial },
        { x: -0.03, y: -0.11, z: 0.08, material: deathBlackMaterial },
        { x: -0.078, y: 0.078, z: 0.08, material: shadowGrayMaterial },
        { x: 0.078, y: 0.078, z: 0.08, material: shadowGrayMaterial },
        { x: -0.078, y: -0.078, z: 0.08, material: shadowGrayMaterial },
        { x: 0.078, y: -0.078, z: 0.08, material: shadowGrayMaterial },
        
        // Middle aura ring
        { x: -0.15, y: 0.05, z: 0.06, material: reaperPurpleMaterial },
        { x: 0.15, y: -0.05, z: 0.06, material: reaperPurpleMaterial },
        { x: 0.05, y: 0.15, z: 0.06, material: reaperPurpleMaterial },
        { x: -0.05, y: -0.15, z: 0.06, material: reaperPurpleMaterial },
        { x: -0.106, y: 0.106, z: 0.06, material: voidVioletMaterial },
        { x: 0.106, y: 0.106, z: 0.06, material: voidVioletMaterial },
        { x: -0.106, y: -0.106, z: 0.06, material: voidVioletMaterial },
        { x: 0.106, y: -0.106, z: 0.06, material: voidVioletMaterial },
        
        // Outer aura ring
        { x: -0.19, y: 0.07, z: 0.04, material: harvestGoldMaterial },
        { x: 0.19, y: -0.07, z: 0.04, material: harvestGoldMaterial },
        { x: 0.07, y: 0.19, z: 0.04, material: harvestGoldMaterial },
        { x: -0.07, y: -0.19, z: 0.04, material: harvestGoldMaterial },
        { x: -0.134, y: 0.134, z: 0.04, material: essenceCyanMaterial },
        { x: 0.134, y: 0.134, z: 0.04, material: essenceCyanMaterial },
        { x: -0.134, y: -0.134, z: 0.04, material: essenceCyanMaterial },
        { x: 0.134, y: -0.134, z: 0.04, material: essenceCyanMaterial },
        
        // Far aura ring
        { x: -0.23, y: 0.09, z: 0.02, material: spectralWhiteMaterial },
        { x: 0.23, y: -0.09, z: 0.02, material: spectralWhiteMaterial },
        { x: 0.09, y: 0.23, z: 0.02, material: spectralWhiteMaterial },
        { x: -0.09, y: -0.23, z: 0.02, material: spectralWhiteMaterial },
        { x: -0.163, y: 0.163, z: 0.02, material: soulBlueMaterial },
        { x: 0.163, y: 0.163, z: 0.02, material: soulBlueMaterial },
        { x: -0.163, y: -0.163, z: 0.02, material: soulBlueMaterial },
        { x: 0.163, y: -0.163, z: 0.02, material: soulBlueMaterial }
    ];

    // Create reaper scythe group
    const reaperScytheGroup = new THREE.Group();
    reaperScytheGroup.name = 'reaperScytheGroup';

    // Add reaper scythe voxels
    reaperScytheVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.scythePhase = index * 0.07; // Stagger animation
        reaperScytheGroup.add(mesh);
    });

    // Create soul orbs group
    const soulOrbsGroup = new THREE.Group();
    soulOrbsGroup.name = 'soulOrbsGroup';

    // Add soul orbs voxels
    soulOrbsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.orbPhase = index * 0.05; // Stagger animation
        soulOrbsGroup.add(mesh);
    });

    // Create spectral energy group
    const spectralEnergyGroup = new THREE.Group();
    spectralEnergyGroup.name = 'spectralEnergyGroup';

    // Add spectral energy voxels
    spectralEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.04; // Stagger animation
        spectralEnergyGroup.add(mesh);
    });

    // Create death aura group
    const deathAuraGroup = new THREE.Group();
    deathAuraGroup.name = 'deathAuraGroup';

    // Add death aura voxels
    deathAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.09; // Stagger animation
        deathAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(reaperScytheGroup);
    cardGroup.add(soulOrbsGroup);
    cardGroup.add(spectralEnergyGroup);
    cardGroup.add(deathAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        soulCollection: 0,
        reaperMotion: 0,
        powerSurge: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update soul harvest card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSoulHarvestCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.soulCollection += deltaTime * 3.5; // Soul collection speed
    cardGroup.userData.reaperMotion += deltaTime * 2.0; // Reaper motion speed
    cardGroup.userData.powerSurge += deltaTime * 4.5; // Power surge speed

    const time = cardGroup.userData.animationTime;
    const soulCollection = cardGroup.userData.soulCollection;
    const reaperMotion = cardGroup.userData.reaperMotion;
    const powerSurge = cardGroup.userData.powerSurge;

    // Animate reaper scythe (harvesting tool)
    const reaperScytheGroup = cardGroup.getObjectByName('reaperScytheGroup');
    if (reaperScytheGroup) {
        // Scythe swinging motion
        const scytheSwing = Math.sin(reaperMotion * 3.0) * 0.003;
        reaperScytheGroup.position.y = scytheSwing;
        reaperScytheGroup.rotation.z = Math.sin(reaperMotion * 2.5) * 0.04;
        
        // Individual scythe element animation
        reaperScytheGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.scythePhase !== undefined) {
                const scytheTime = reaperMotion + mesh.userData.scythePhase;
                
                // Scythe edge sharpening
                const edgeSharpness = Math.sin(scytheTime * 6.0) * 0.001;
                const bladeGleam = Math.cos(scytheTime * 5.0) * 0.0008;
                
                mesh.position.x = mesh.userData.originalPosition.x + edgeSharpness;
                mesh.position.z = mesh.userData.originalPosition.z + bladeGleam;
                
                // Scythe intensity (death energy)
                const scytheIntensity = 1.0 + Math.sin(scytheTime * 7.0) * 0.7;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * scytheIntensity;
                }
                
                // Scythe scale (reaper presence)
                const scytheScale = 0.9 + Math.sin(scytheTime * 4.0) * 0.25;
                mesh.scale.setScalar(scytheScale);
            }
        });
    }

    // Animate soul orbs (collected souls)
    const soulOrbsGroup = cardGroup.getObjectByName('soulOrbsGroup');
    if (soulOrbsGroup) {
        // Soul orbit motion
        const soulOrbit = Math.sin(soulCollection * 1.5) * 0.04;
        soulOrbsGroup.rotation.z = soulOrbit;
        soulOrbsGroup.rotation.y = soulCollection * 0.3;
        
        soulOrbsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.orbPhase !== undefined) {
                const orbTime = soulCollection + mesh.userData.orbPhase;
                
                // Soul orbital motion
                const orbFloat = Math.sin(orbTime * 4.0) * 0.004;
                const orbPulse = Math.cos(orbTime * 3.5) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + orbPulse;
                mesh.position.y = mesh.userData.originalPosition.y + orbFloat;
                
                // Soul energy glow
                const soulGlow = 1.0 + Math.sin(orbTime * 6.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * soulGlow;
                }
                
                // Soul opacity variation
                const soulOpacity = 0.8 + Math.sin(orbTime * 5.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * soulOpacity;
                }
            }
        });
    }

    // Animate spectral energy (soul flow)
    const spectralEnergyGroup = cardGroup.getObjectByName('spectralEnergyGroup');
    if (spectralEnergyGroup) {
        spectralEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = soulCollection + mesh.userData.energyPhase;
                
                // Spectral energy flowing motion
                const energyFlow = Math.sin(energyTime * 5.0) * 0.006;
                const energyWave = Math.cos(energyTime * 4.0) * 0.005;
                
                mesh.position.x = mesh.userData.originalPosition.x + energyFlow;
                mesh.position.y = mesh.userData.originalPosition.y + energyWave;
                
                // Energy intensity
                const energyIntensity = 1.0 + Math.sin(energyTime * 7.0) * 0.9;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyIntensity;
                }
                
                // Energy scale variation (power flow)
                const energyScale = 0.7 + Math.sin(energyTime * 6.0) * 0.5;
                mesh.scale.setScalar(energyScale);
                
                // Energy opacity (spectral manifestation)
                const energyOpacity = 0.6 + Math.sin(energyTime * 8.0) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * energyOpacity;
                }
            }
        });
    }

    // Animate death aura (power enhancement field)
    const deathAuraGroup = cardGroup.getObjectByName('deathAuraGroup');
    if (deathAuraGroup) {
        deathAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = powerSurge + mesh.userData.auraPhase;
                
                // Death aura expansion and contraction
                const auraExpansion = Math.sin(auraTime * 2.5) * 0.008;
                const auraPower = Math.cos(auraTime * 3.0) * 0.006;
                
                mesh.position.x = mesh.userData.originalPosition.x + auraExpansion;
                mesh.position.y = mesh.userData.originalPosition.y + auraPower;
                
                // Aura power intensity
                const auraPowerIntensity = 1.0 + Math.sin(auraTime * 5.0) * 1.0;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraPowerIntensity;
                }
                
                // Aura scale variation (power surge)
                const auraScale = 0.8 + Math.sin(auraTime * 4.5) * 0.4;
                mesh.scale.setScalar(auraScale);
                
                // Aura opacity (death magic manifestation)
                const auraOpacity = 0.8 + Math.sin(auraTime * 6.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * auraOpacity;
                }
            }
        });
    }

    // Overall soul harvest presence (epic necromantic magic)
    const harvestPulse = 1 + Math.sin(time * 2.8) * 0.1;
    const harvestShift = Math.cos(time * 3.5) * 0.0015;
    cardGroup.scale.setScalar(0.8 * harvestPulse);
    cardGroup.position.x += harvestShift;
    cardGroup.position.z += harvestShift * 0.8;
}

// Export the soul harvest card data for the loot system
export const SOUL_HARVEST_CARD_DATA = {
    name: "Soul Harvest",
    description: 'Absorbs souls from defeated enemies for 15 seconds, increasing damage by 10% per soul (max 100% bonus damage).',
    category: 'card',
    rarity: 'epic',
    effect: 'soul_harvest',
    effectValue: 15, // Duration in seconds
    createFunction: createSoulHarvestCard,
    updateFunction: updateSoulHarvestCardAnimation,
    voxelModel: 'soul_harvest_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.7
    }
};

export default createSoulHarvestCard;