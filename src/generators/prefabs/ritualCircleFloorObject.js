import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create ritual circle floor patterns for the Eye of Judgment room
 * These are concentric circles with mystical runes and patterns
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Ritual circle floor object group
 */
export function createRitualCircleFloorObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888);

    // Use smaller voxel size for detailed floor patterns
    const floorVoxelSize = VOXEL_SIZE * 1.5;
    const baseGeometry = getOrCreateGeometry('ritual_floor_voxel', () =>
        new THREE.BoxGeometry(floorVoxelSize, floorVoxelSize * 0.2, floorVoxelSize)
    );

    // Ritual circle materials (dark mystical tones)
    const outerCircleMaterial = _getMaterialByHex_Cached('2F2F4F', {
        roughness: 0.9,
        metalness: 0.05
    }); // Dark slate gray

    const innerCircleMaterial = _getMaterialByHex_Cached('483D8B', {
        roughness: 0.8,
        metalness: 0.1,
        emissive: new THREE.Color(0x1a1a3a),
        emissiveIntensity: 0.1
    }); // Dark slate blue with subtle glow

    const runeMaterial = _getMaterialByHex_Cached('6A5ACD', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.2,
        roughness: 0.6,
        metalness: 0.2
    }); // Slate blue runes with glow

    const centerMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x8A2BE2),
        emissiveIntensity: 0.3,
        roughness: 0.4,
        metalness: 0.3
    }); // Blue violet center with strong glow

    // Calculate circle positions using mathematical approach
    const centerX = 0;
    const centerZ = 0;

    // Outer circle (radius 6-7 voxels) - reduced size to avoid door collision
    const outerRadius = 6;
    for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 24) { // 48 points
        const x = Math.cos(angle) * outerRadius;
        const z = Math.sin(angle) * outerRadius;
        
        // Add some randomness for organic feel
        const jitterX = (rng() - 0.5) * 0.3;
        const jitterZ = (rng() - 0.5) * 0.3;
        
        const voxel = new THREE.Mesh(baseGeometry.clone(), outerCircleMaterial);
        voxel.position.set(
            (x + jitterX) * floorVoxelSize,
            floorVoxelSize * 0.1,
            (z + jitterZ) * floorVoxelSize
        );
        voxel.userData.isFloorDecoration = true;
        voxel.userData.circleType = 'outer';
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    }

    // Inner circle (radius 4-5 voxels) - reduced size to avoid door collision
    const innerRadius = 4;
    for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 18) { // 36 points
        const x = Math.cos(angle) * innerRadius;
        const z = Math.sin(angle) * innerRadius;
        
        const jitterX = (rng() - 0.5) * 0.2;
        const jitterZ = (rng() - 0.5) * 0.2;
        
        const voxel = new THREE.Mesh(baseGeometry.clone(), innerCircleMaterial);
        voxel.position.set(
            (x + jitterX) * floorVoxelSize,
            floorVoxelSize * 0.15,
            (z + jitterZ) * floorVoxelSize
        );
        voxel.userData.isFloorDecoration = true;
        voxel.userData.circleType = 'inner';
        voxel.castShadow = false; // Emissive doesn't cast shadows
        voxel.receiveShadow = true;
        group.add(voxel);
    }

    // Rune positions on middle ring (radius 5 voxels) - reduced size to avoid door collision
    const runeRadius = 5;
    const runeCount = 8; // 8 cardinal/intercardinal directions
    for (let i = 0; i < runeCount; i++) {
        const angle = (i / runeCount) * Math.PI * 2;
        const x = Math.cos(angle) * runeRadius;
        const z = Math.sin(angle) * runeRadius;
        
        // Create rune clusters (3x3 pattern)
        for (let rx = -1; rx <= 1; rx++) {
            for (let rz = -1; rz <= 1; rz++) {
                // Skip some voxels for rune pattern (create gaps)
                if (Math.abs(rx) + Math.abs(rz) > 1.5 && rng() > 0.3) continue;
                
                const voxel = new THREE.Mesh(
                    getOrCreateGeometry('rune_voxel', () =>
                        new THREE.BoxGeometry(floorVoxelSize * 0.8, floorVoxelSize * 0.3, floorVoxelSize * 0.8)
                    ),
                    runeMaterial
                );
                voxel.position.set(
                    (x + rx * 0.4) * floorVoxelSize,
                    floorVoxelSize * 0.2,
                    (z + rz * 0.4) * floorVoxelSize
                );
                voxel.rotation.y = angle + Math.PI / 2; // Orient runes toward center
                voxel.userData.isFloorDecoration = true;
                voxel.userData.circleType = 'rune';
                voxel.userData.runeIndex = i;
                voxel.castShadow = false;
                voxel.receiveShadow = true;
                group.add(voxel);
            }
        }
    }

    // Central core (radius 2-3 voxels)
    const coreRadius = 2.5;
    for (let x = -3; x <= 3; x++) {
        for (let z = -3; z <= 3; z++) {
            const distance = Math.sqrt(x * x + z * z);
            if (distance <= coreRadius) {
                // Create elevated center with height variation
                const height = Math.max(0.1, (coreRadius - distance) / coreRadius);
                
                const voxel = new THREE.Mesh(
                    getOrCreateGeometry('center_voxel', () =>
                        new THREE.BoxGeometry(floorVoxelSize, floorVoxelSize * height, floorVoxelSize)
                    ),
                    centerMaterial
                );
                voxel.position.set(
                    x * floorVoxelSize,
                    floorVoxelSize * height * 0.5,
                    z * floorVoxelSize
                );
                voxel.userData.isFloorDecoration = true;
                voxel.userData.circleType = 'center';
                voxel.castShadow = false;
                voxel.receiveShadow = true;
                group.add(voxel);
            }
        }
    }

    // Connecting lines between circles (ritual pathways)
    const pathwayMaterial = _getMaterialByHex_Cached('4B0082', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0x2F1B69),
        emissiveIntensity: 0.15
    }); // Indigo pathways

    // Create 4 main pathways (cardinal directions)
    const pathwayDirections = [
        { x: 1, z: 0 }, { x: -1, z: 0 }, { x: 0, z: 1 }, { x: 0, z: -1 }
    ];

    pathwayDirections.forEach(dir => {
        for (let i = 2; i < 5; i++) { // Reduced pathway length to avoid door collision
            const voxel = new THREE.Mesh(
                getOrCreateGeometry('pathway_voxel', () =>
                    new THREE.BoxGeometry(floorVoxelSize * 0.6, floorVoxelSize * 0.1, floorVoxelSize * 0.6)
                ),
                pathwayMaterial
            );
            voxel.position.set(
                dir.x * i * floorVoxelSize,
                floorVoxelSize * 0.05,
                dir.z * i * floorVoxelSize
            );
            voxel.userData.isFloorDecoration = true;
            voxel.userData.circleType = 'pathway';
            voxel.castShadow = false;
            voxel.receiveShadow = true;
            group.add(voxel);
        }
    });

    // Additional decorative elements - small mystical nodes
    const nodeMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x9370DB),
        emissiveIntensity: 0.4
    }); // Medium orchid nodes

    // Place nodes at intercardinal positions
    const nodePositions = [
        { x: 4, z: 4 }, { x: -4, z: 4 }, { x: 4, z: -4 }, { x: -4, z: -4 }
    ];

    nodePositions.forEach(pos => {
        const node = new THREE.Mesh(
            getOrCreateGeometry('mystical_node', () =>
                new THREE.BoxGeometry(floorVoxelSize * 0.5, floorVoxelSize * 0.4, floorVoxelSize * 0.5)
            ),
            nodeMaterial
        );
        node.position.set(
            pos.x * floorVoxelSize,
            floorVoxelSize * 0.25,
            pos.z * floorVoxelSize
        );
        node.userData.isFloorDecoration = true;
        node.userData.circleType = 'node';
        node.userData.hasAnimation = true;
        node.castShadow = false;
        node.receiveShadow = true;
        group.add(node);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'ritual_circle_floor',
        isFloorDecoration: true,
        isEventObject: true,
        objectId: options.userData?.objectId || 'ritual_circle_floor',
        isRitualCircle: true,
        hasAnimation: true,
        animationType: 'ritual_glow',
        voxelScale: floorVoxelSize,
        // Ritual properties
        isActiveCircle: false,
        powerLevel: 0,
        // Animation properties
        glowCycleSpeed: 0.002,
        pulseSpeed: 0.001
    };

    group.position.y = -0.1; // Slightly below ground level
    group.name = 'ritual_circle_floor';

    console.log('[RitualCircleFloor] ✅ Created mystical ritual circle floor pattern');
    return group;
}

/**
 * Animate the ritual circle with pulsing glows and energy flows
 * @param {THREE.Group} circleGroup - The ritual circle group to animate
 * @param {number} deltaTime - Time since last frame
 * @param {number} time - Total elapsed time
 */
export function animateRitualCircle(circleGroup, deltaTime, time) {
    if (!circleGroup || !circleGroup.userData.isRitualCircle) return;

    const { glowCycleSpeed, pulseSpeed, isActiveCircle, powerLevel } = circleGroup.userData;

    // Animate different parts based on their type
    circleGroup.traverse(child => {
        if (!child.userData.circleType) return;

        const { circleType } = child.userData;

        switch (circleType) {
            case 'rune':
                // Runes pulse in sequence
                const runePhase = (child.userData.runeIndex / 8) * Math.PI * 2;
                const runePulse = Math.sin(time * glowCycleSpeed + runePhase);
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.2 + runePulse * 0.1;
                }
                // Runes glow brighter when circle is active
                if (isActiveCircle && child.material.opacity) {
                    child.material.opacity = 0.8 + runePulse * 0.2;
                }
                break;

            case 'center':
                // Central core has strong pulsing
                const centerPulse = Math.sin(time * pulseSpeed) * 0.5 + 0.5;
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.3 + centerPulse * 0.2;
                }
                // Scale pulsing for dramatic effect
                const scale = 1.0 + centerPulse * 0.1;
                child.scale.setScalar(scale);
                break;

            case 'inner':
                // Inner circle has steady glow with slight variation
                const innerPulse = Math.sin(time * glowCycleSpeed * 0.7) * 0.3 + 0.7;
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.1 + innerPulse * 0.05;
                }
                break;

            case 'pathway':
                // Pathways have energy flowing along them
                const pathwayFlow = Math.sin(time * glowCycleSpeed * 2 + 
                    (child.position.x + child.position.z) * 0.1) * 0.5 + 0.5;
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.15 + pathwayFlow * 0.1;
                }
                break;

            case 'node':
                // Nodes have independent pulsing
                const nodePulse = Math.sin(time * pulseSpeed * 1.3 + 
                    (child.position.x * child.position.z) * 0.01) * 0.5 + 0.5;
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.4 + nodePulse * 0.2;
                }
                // Gentle floating animation
                child.position.y = circleGroup.userData.voxelScale * (0.25 + nodePulse * 0.1);
                break;
        }
    });

    // When the circle is judging, increase overall power
    if (isActiveCircle) {
        const judgmentIntensity = powerLevel || 1.0;
        circleGroup.traverse(child => {
            if (child.material && child.material.emissive) {
                child.material.emissiveIntensity *= judgmentIntensity;
            }
        });
    }
}