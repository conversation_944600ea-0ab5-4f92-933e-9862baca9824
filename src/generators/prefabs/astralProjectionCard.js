import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Astral Projection Card Prefab
 * Creates a physical body lying down with an astral body floating above, connected by a silver cord
 */

// Astral projection specific colors
const ASTRAL_COLORS = {
    PHYSICAL_BROWN: 0x8B4513,    // Saddle brown for physical body
    ASTRAL_BLUE: 0x87CEEB,       // Sky blue for astral body
    ASTRAL_WHITE: 0xF0F8FF,      // Alice blue for astral energy
    SILVER_CORD: 0xC0C0C0,       // Silver for the connecting cord
    ETHEREAL_CYAN: 0xE0FFFF,     // Light cyan for ethereal effects
    SOUL_GLOW: 0xB0E0E6          // Powder blue for soul energy
};

/**
 * Create an astral projection card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The astral projection card 3D model
 */
export function createAstralProjectionCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'AstralProjectionCard';

    // Materials
    const physicalBrownMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.PHYSICAL_BROWN,
        emissive: ASTRAL_COLORS.PHYSICAL_BROWN,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const astralBlueMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.ASTRAL_BLUE,
        emissive: ASTRAL_COLORS.ASTRAL_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const astralWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.ASTRAL_WHITE,
        emissive: ASTRAL_COLORS.ASTRAL_WHITE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.5
    });

    const silverCordMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.SILVER_CORD,
        emissive: ASTRAL_COLORS.SILVER_CORD,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.4
    });

    const etherealCyanMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.ETHEREAL_CYAN,
        emissive: ASTRAL_COLORS.ETHEREAL_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.3
    });

    const soulGlowMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.SOUL_GLOW,
        emissive: ASTRAL_COLORS.SOUL_GLOW,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.4
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create physical body voxels (bottom, lying down)
    const physicalBodyVoxels = [
        // Physical body core (lying horizontally)
        { x: 0, y: -4, z: 0, material: physicalBrownMaterial },
        { x: 0, y: -3, z: 0, material: physicalBrownMaterial },
        { x: 0, y: -2, z: 0, material: physicalBrownMaterial },
        
        // Body outline
        { x: -1, y: -3, z: 0, material: physicalBrownMaterial },
        { x: 1, y: -3, z: 0, material: physicalBrownMaterial },
        { x: -2, y: -3, z: 0, material: physicalBrownMaterial },
        { x: 2, y: -3, z: 0, material: physicalBrownMaterial },
        
        // Head and feet
        { x: 0, y: -5, z: 0, material: physicalBrownMaterial },
        { x: -1, y: -2, z: 0, material: physicalBrownMaterial },
        { x: 1, y: -2, z: 0, material: physicalBrownMaterial }
    ];

    // Create astral body voxels (top, floating)
    const astralBodyVoxels = [
        // Astral body core (floating above)
        { x: 0, y: 4, z: 0, material: astralBlueMaterial },
        { x: 0, y: 3, z: 0, material: astralBlueMaterial },
        { x: 0, y: 2, z: 0, material: astralBlueMaterial },
        
        // Astral outline (more ethereal)
        { x: -1, y: 3, z: 0, material: astralWhiteMaterial },
        { x: 1, y: 3, z: 0, material: astralWhiteMaterial },
        { x: -2, y: 3, z: 0, material: astralWhiteMaterial },
        { x: 2, y: 3, z: 0, material: astralWhiteMaterial },
        
        // Astral head and extremities
        { x: 0, y: 5, z: 0, material: astralWhiteMaterial },
        { x: -1, y: 2, z: 0, material: astralBlueMaterial },
        { x: 1, y: 2, z: 0, material: astralBlueMaterial },
        
        // Astral aura
        { x: -3, y: 3, z: 0, material: etherealCyanMaterial },
        { x: 3, y: 3, z: 0, material: etherealCyanMaterial },
        { x: 0, y: 6, z: 0, material: etherealCyanMaterial },
        { x: -1, y: 5, z: 0, material: soulGlowMaterial },
        { x: 1, y: 5, z: 0, material: soulGlowMaterial }
    ];

    // Create silver cord voxels (connecting the two bodies)
    const silverCordVoxels = [
        // Central connection
        { x: 0, y: 1, z: 0, material: silverCordMaterial },
        { x: 0, y: 0, z: 0, material: silverCordMaterial },
        { x: 0, y: -1, z: 0, material: silverCordMaterial },
        
        // Cord undulations
        { x: 0.5, y: 0.5, z: 0, material: silverCordMaterial },
        { x: -0.5, y: 0.5, z: 0, material: silverCordMaterial },
        { x: 0.5, y: -0.5, z: 0, material: silverCordMaterial },
        { x: -0.5, y: -0.5, z: 0, material: silverCordMaterial },
        
        // Cord energy nodes
        { x: 1, y: 0, z: 0, material: soulGlowMaterial },
        { x: -1, y: 0, z: 0, material: soulGlowMaterial }
    ];

    // Create all voxels
    [...physicalBodyVoxels, ...astralBodyVoxels, ...silverCordVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8, // Slightly compact
            voxel.y * VOXEL_SIZE * 0.8, // Slightly compact
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === physicalBrownMaterial ? 'physical' :
                                 voxel.material === astralBlueMaterial ? 'astral' :
                                 voxel.material === astralWhiteMaterial ? 'astral_energy' :
                                 voxel.material === silverCordMaterial ? 'silver_cord' :
                                 voxel.material === soulGlowMaterial ? 'soul_glow' : 'ethereal';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        breathingPhase: 0,
        astralPhase: 0,
        cordPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update astral projection card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateAstralProjectionCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Breathing phase for physical body
    cardGroup.userData.breathingPhase += deltaTime * 1.5;
    const breathingValue = (Math.sin(cardGroup.userData.breathingPhase) + 1) * 0.5; // 0 to 1

    // Astral floating phase
    cardGroup.userData.astralPhase += deltaTime * 2.0;
    const astralValue = (Math.sin(cardGroup.userData.astralPhase) + 1) * 0.5; // 0 to 1

    // Silver cord undulation phase
    cardGroup.userData.cordPhase += deltaTime * 3.0;
    const cordValue = (Math.sin(cardGroup.userData.cordPhase) + 1) * 0.5; // 0 to 1

    // Apply astral projection animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different animation patterns for different body types
            switch (child.userData.voxelType) {
                case 'physical':
                    child.material.opacity = baseOpacity * (0.9 + breathingValue * 0.1);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.3 + breathingValue * 0.2;
                    }
                    // Subtle breathing motion
                    child.position.y += Math.sin(time * 1.5) * 0.002;
                    break;
                case 'astral':
                    child.material.opacity = baseOpacity * (0.6 + astralValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + astralValue * 0.6;
                    }
                    // Astral floating motion
                    child.position.y += Math.sin(time * 2 + child.position.x * 2) * 0.008;
                    child.position.x += Math.cos(time * 1.8 + child.position.y * 1.5) * 0.004;
                    break;
                case 'astral_energy':
                    child.material.opacity = baseOpacity * (0.5 + astralValue * 0.5);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.9 + astralValue * 0.8;
                    }
                    // Ethereal energy movement
                    child.position.y += Math.sin(time * 2.5 + child.position.x * 3) * 0.010;
                    child.position.x += Math.cos(time * 2.2 + child.position.y * 2) * 0.006;
                    break;
                case 'silver_cord':
                    child.material.opacity = baseOpacity * (0.4 + cordValue * 0.6);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.7 + cordValue * 0.5;
                    }
                    // Silver cord undulation
                    child.position.x += Math.sin(time * 4 + child.position.y * 4) * 0.005;
                    child.position.y += Math.cos(time * 3.5 + child.position.x * 3) * 0.003;
                    break;
                case 'soul_glow':
                    child.material.opacity = baseOpacity * (0.4 + astralValue * 0.6);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + astralValue * 0.7;
                    }
                    // Soul energy pulsing
                    const pulseScale = 1.0 + astralValue * 0.2;
                    child.scale.setScalar(pulseScale);
                    break;
                case 'ethereal':
                    child.material.opacity = baseOpacity * (0.3 + astralValue * 0.7);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.6 + astralValue * 0.6;
                    }
                    // Ethereal drifting motion
                    child.position.y += Math.sin(time * 3 + child.position.x * 4) * 0.012;
                    child.position.x += Math.cos(time * 2.8 + child.position.y * 3) * 0.008;
                    break;
            }
        }
    });

    // Overall separation effect
    const separationOffset = Math.sin(time * 1.2) * 0.01;
    cardGroup.children.forEach((child) => {
        if (child.userData.voxelType === 'astral' || child.userData.voxelType === 'astral_energy' || child.userData.voxelType === 'ethereal') {
            child.position.y += separationOffset;
        }
    });
}

// Export the astral projection card data for the loot system
export const ASTRAL_PROJECTION_CARD_DATA = {
    name: 'Astral Projection',
    description: 'Separates your soul from your physical form, allowing you to traverse the astral plane and bypass physical barriers while maintaining a silver cord connection.',
    category: 'card',
    rarity: 'rare',
    effect: 'astral_projection',
    effectValue: 6,
    createFunction: createAstralProjectionCard,
    updateFunction: updateAstralProjectionCardAnimation,
    voxelModel: 'astral_projection_card',
    glow: {
        color: 0x87CEEB,
        intensity: 1.1
    }
};

export default createAstralProjectionCard;