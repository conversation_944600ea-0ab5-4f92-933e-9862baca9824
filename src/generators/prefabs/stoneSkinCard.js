import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Stone Skin Card Prefab
 * Creates stone armor that provides damage reduction
 */

// Stone skin specific colors
const STONE_COLORS = {
    GRANITE_GRAY: 0x696969,           // Main stone armor color
    SLATE_BLUE: 0x6A5ACD,            // Magical stone enhancement
    IRON_GRAY: 0x2F4F4F,             // Dark metal reinforcement
    QUARTZ_WHITE: 0xF5F5DC,          // Light stone highlights
    OBSIDIAN_BLACK: 0x36454F,        // Dark stone shadows
    CRYSTAL_BLUE: 0x4682B4            // Magical crystal accents
};

/**
 * Create a stone skin card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The stone skin card 3D model
 */
export function createStoneSkinCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'StoneSkinCard';

    // Stone skin materials
    const graniteGrayMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.GRANITE_GRAY,
        emissive: STONE_COLORS.GRANITE_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.95
    });

    const slateBlueMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.SLATE_BLUE,
        emissive: STONE_COLORS.SLATE_BLUE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const ironGrayMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.IRON_GRAY,
        emissive: STONE_COLORS.IRON_GRAY,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 1.0
    });

    const quartzWhiteMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.QUARTZ_WHITE,
        emissive: STONE_COLORS.QUARTZ_WHITE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const obsidianBlackMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.OBSIDIAN_BLACK,
        emissive: STONE_COLORS.OBSIDIAN_BLACK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 1.0
    });

    const crystalBlueMaterial = new THREE.MeshLambertMaterial({
        color: STONE_COLORS.CRYSTAL_BLUE,
        emissive: STONE_COLORS.CRYSTAL_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Stone armor core (central protective form)
    const stoneArmorVoxels = [
        // Helmet/head protection
        { x: 0.0, y: 0.20, z: 0.0, material: graniteGrayMaterial },
        { x: -0.04, y: 0.20, z: 0.0, material: graniteGrayMaterial },
        { x: 0.04, y: 0.20, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: 0.20, z: 0.04, material: graniteGrayMaterial },
        { x: 0.0, y: 0.20, z: -0.04, material: graniteGrayMaterial },
        { x: 0.0, y: 0.24, z: 0.0, material: ironGrayMaterial }, // Helmet crown
        
        // Chest armor
        { x: 0.0, y: 0.12, z: 0.0, material: graniteGrayMaterial },
        { x: -0.04, y: 0.12, z: 0.0, material: graniteGrayMaterial },
        { x: 0.04, y: 0.12, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: 0.12, z: 0.04, material: graniteGrayMaterial },
        { x: 0.0, y: 0.16, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: graniteGrayMaterial },
        
        // Shoulder guards
        { x: -0.08, y: 0.16, z: 0.0, material: ironGrayMaterial },
        { x: 0.08, y: 0.16, z: 0.0, material: ironGrayMaterial },
        { x: -0.08, y: 0.12, z: 0.0, material: ironGrayMaterial },
        { x: 0.08, y: 0.12, z: 0.0, material: ironGrayMaterial },
        
        // Torso armor
        { x: 0.0, y: 0.04, z: 0.0, material: graniteGrayMaterial },
        { x: -0.04, y: 0.04, z: 0.0, material: graniteGrayMaterial },
        { x: 0.04, y: 0.04, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: 0.0, z: 0.0, material: graniteGrayMaterial },
        
        // Belt/waist armor
        { x: 0.0, y: -0.04, z: 0.0, material: ironGrayMaterial },
        { x: -0.04, y: -0.04, z: 0.0, material: ironGrayMaterial },
        { x: 0.04, y: -0.04, z: 0.0, material: ironGrayMaterial },
        
        // Leg guards
        { x: 0.0, y: -0.08, z: 0.0, material: graniteGrayMaterial },
        { x: -0.04, y: -0.08, z: 0.0, material: graniteGrayMaterial },
        { x: 0.04, y: -0.08, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: graniteGrayMaterial },
        { x: 0.0, y: -0.16, z: 0.0, material: graniteGrayMaterial }
    ];

    // Stone reinforcements (additional armor plates)
    const stoneReinforcementsVoxels = [
        // Arm guards
        { x: -0.12, y: 0.08, z: 0.0, material: slateBlueMaterial },
        { x: 0.12, y: 0.08, z: 0.0, material: slateBlueMaterial },
        { x: -0.12, y: 0.04, z: 0.0, material: slateBlueMaterial },
        { x: 0.12, y: 0.04, z: 0.0, material: slateBlueMaterial },
        { x: -0.12, y: 0.0, z: 0.0, material: slateBlueMaterial },
        { x: 0.12, y: 0.0, z: 0.0, material: slateBlueMaterial },
        
        // Side armor plates
        { x: -0.08, y: 0.08, z: 0.04, material: obsidianBlackMaterial },
        { x: 0.08, y: 0.08, z: 0.04, material: obsidianBlackMaterial },
        { x: -0.08, y: 0.04, z: 0.04, material: obsidianBlackMaterial },
        { x: 0.08, y: 0.04, z: 0.04, material: obsidianBlackMaterial },
        { x: -0.08, y: 0.08, z: -0.04, material: obsidianBlackMaterial },
        { x: 0.08, y: 0.08, z: -0.04, material: obsidianBlackMaterial },
        { x: -0.08, y: 0.04, z: -0.04, material: obsidianBlackMaterial },
        { x: 0.08, y: 0.04, z: -0.04, material: obsidianBlackMaterial },
        
        // Back armor
        { x: 0.0, y: 0.12, z: -0.08, material: ironGrayMaterial },
        { x: 0.0, y: 0.08, z: -0.08, material: ironGrayMaterial },
        { x: 0.0, y: 0.04, z: -0.08, material: ironGrayMaterial },
        { x: 0.0, y: 0.0, z: -0.08, material: ironGrayMaterial },
        
        // Knee guards
        { x: -0.04, y: -0.12, z: 0.04, material: slateBlueMaterial },
        { x: 0.04, y: -0.12, z: 0.04, material: slateBlueMaterial },
        { x: -0.04, y: -0.16, z: 0.04, material: slateBlueMaterial },
        { x: 0.04, y: -0.16, z: 0.04, material: slateBlueMaterial }
    ];

    // Crystal enchantments (magical stone enhancements)
    const crystalEnchantmentsVoxels = [
        // Chest crystal
        { x: 0.0, y: 0.12, z: 0.08, material: crystalBlueMaterial },
        
        // Helmet crystal
        { x: 0.0, y: 0.24, z: 0.04, material: quartzWhiteMaterial },
        
        // Shoulder crystals
        { x: -0.08, y: 0.16, z: 0.04, material: crystalBlueMaterial },
        { x: 0.08, y: 0.16, z: 0.04, material: crystalBlueMaterial },
        
        // Gauntlet crystals
        { x: -0.12, y: 0.08, z: 0.04, material: quartzWhiteMaterial },
        { x: 0.12, y: 0.08, z: 0.04, material: quartzWhiteMaterial },
        
        // Belt crystal
        { x: 0.0, y: -0.04, z: 0.04, material: crystalBlueMaterial },
        
        // Leg crystals
        { x: -0.04, y: -0.08, z: 0.08, material: quartzWhiteMaterial },
        { x: 0.04, y: -0.08, z: 0.08, material: quartzWhiteMaterial }
    ];

    // Stone aura (protective energy field)
    const stoneAuraVoxels = [
        // Inner aura layer
        { x: -0.16, y: 0.16, z: 0.12, material: slateBlueMaterial },
        { x: 0.16, y: 0.16, z: 0.12, material: slateBlueMaterial },
        { x: -0.16, y: 0.0, z: 0.12, material: slateBlueMaterial },
        { x: 0.16, y: 0.0, z: 0.12, material: slateBlueMaterial },
        { x: -0.16, y: -0.16, z: 0.12, material: slateBlueMaterial },
        { x: 0.16, y: -0.16, z: 0.12, material: slateBlueMaterial },
        
        // Outer aura layer
        { x: -0.20, y: 0.20, z: 0.16, material: crystalBlueMaterial },
        { x: 0.20, y: 0.20, z: 0.16, material: crystalBlueMaterial },
        { x: -0.20, y: 0.0, z: 0.16, material: crystalBlueMaterial },
        { x: 0.20, y: 0.0, z: 0.16, material: crystalBlueMaterial },
        { x: -0.20, y: -0.20, z: 0.16, material: crystalBlueMaterial },
        { x: 0.20, y: -0.20, z: 0.16, material: crystalBlueMaterial },
        
        // Protective field particles
        { x: -0.24, y: 0.12, z: 0.20, material: quartzWhiteMaterial },
        { x: 0.24, y: 0.12, z: 0.20, material: quartzWhiteMaterial },
        { x: -0.12, y: 0.24, z: 0.20, material: quartzWhiteMaterial },
        { x: 0.12, y: 0.24, z: 0.20, material: quartzWhiteMaterial },
        { x: -0.24, y: -0.12, z: 0.20, material: quartzWhiteMaterial },
        { x: 0.24, y: -0.12, z: 0.20, material: quartzWhiteMaterial },
        { x: -0.12, y: -0.24, z: 0.20, material: quartzWhiteMaterial },
        { x: 0.12, y: -0.24, z: 0.20, material: quartzWhiteMaterial },
        
        // Distant protective sparkles
        { x: -0.28, y: 0.28, z: 0.24, material: crystalBlueMaterial },
        { x: 0.28, y: 0.28, z: 0.24, material: crystalBlueMaterial },
        { x: -0.28, y: -0.28, z: 0.24, material: crystalBlueMaterial },
        { x: 0.28, y: -0.28, z: 0.24, material: crystalBlueMaterial }
    ];

    // Create stone armor group
    const stoneArmorGroup = new THREE.Group();
    stoneArmorGroup.name = 'stoneArmor';

    // Add stone armor voxels
    stoneArmorVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        stoneArmorGroup.add(mesh);
    });

    // Create stone reinforcements group
    const stoneReinforcementsGroup = new THREE.Group();
    stoneReinforcementsGroup.name = 'stoneReinforcements';

    // Add stone reinforcements voxels
    stoneReinforcementsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.reinforcementPhase = index * 0.1; // Stagger animation
        stoneReinforcementsGroup.add(mesh);
    });

    // Create crystal enchantments group
    const crystalEnchantmentsGroup = new THREE.Group();
    crystalEnchantmentsGroup.name = 'crystalEnchantments';

    // Add crystal enchantments voxels
    crystalEnchantmentsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.crystalPhase = index * 0.15; // Stagger animation
        crystalEnchantmentsGroup.add(mesh);
    });

    // Create stone aura group
    const stoneAuraGroup = new THREE.Group();
    stoneAuraGroup.name = 'stoneAura';

    // Add stone aura voxels
    stoneAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.05; // Stagger animation
        stoneAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(stoneArmorGroup);
    cardGroup.add(stoneReinforcementsGroup);
    cardGroup.add(crystalEnchantmentsGroup);
    cardGroup.add(stoneAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        stonePulse: 0,
        crystalGlow: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update stone skin card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateStoneSkinCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.stonePulse += deltaTime * 1.5; // Stone pulse speed
    cardGroup.userData.crystalGlow += deltaTime * 3.0; // Crystal glow speed

    const time = cardGroup.userData.animationTime;
    const stonePulse = cardGroup.userData.stonePulse;
    const crystalGlow = cardGroup.userData.crystalGlow;

    // Animate stone armor (solid pulsing)
    const stoneArmorGroup = cardGroup.getObjectByName('stoneArmor');
    if (stoneArmorGroup) {
        // Stone armor steady pulsing (protective strength)
        const stoneIntensity = 1.0 + Math.sin(stonePulse * 1.2) * 0.2;
        stoneArmorGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * stoneIntensity;
            }
        });
    }

    // Animate stone reinforcements (armor shifting)
    const stoneReinforcementsGroup = cardGroup.getObjectByName('stoneReinforcements');
    if (stoneReinforcementsGroup) {
        stoneReinforcementsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.reinforcementPhase !== undefined) {
                const reinforcementTime = stonePulse + mesh.userData.reinforcementPhase;
                
                // Subtle armor plate movement
                const plateShift = Math.sin(reinforcementTime * 2.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + plateShift;
                mesh.position.z = mesh.userData.originalPosition.z + Math.cos(reinforcementTime * 1.8) * 0.001;
                
                // Reinforcement intensity variation
                const reinforcementIntensity = 1.0 + Math.sin(reinforcementTime * 3.0) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * reinforcementIntensity;
                }
            }
        });
    }

    // Animate crystal enchantments (magical glow)
    const crystalEnchantmentsGroup = cardGroup.getObjectByName('crystalEnchantments');
    if (crystalEnchantmentsGroup) {
        crystalEnchantmentsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.crystalPhase !== undefined) {
                const crystalTime = crystalGlow + mesh.userData.crystalPhase;
                
                // Crystal floating motion
                const floatY = Math.sin(crystalTime * 2.5) * 0.002;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                
                // Crystal glow pulsing
                const crystalIntensity = 1.0 + Math.sin(crystalTime * 4.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crystalIntensity;
                }
                
                // Crystal scale twinkling
                const crystalScale = 0.9 + Math.sin(crystalTime * 6.0) * 0.2;
                mesh.scale.setScalar(crystalScale);
            }
        });
    }

    // Animate stone aura (protective field)
    const stoneAuraGroup = cardGroup.getObjectByName('stoneAura');
    if (stoneAuraGroup) {
        stoneAuraGroup.rotation.y = time * 0.5; // Slow rotation
        
        stoneAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = crystalGlow + mesh.userData.auraPhase;
                
                // Aura floating motion (protective barrier movement)
                const auraFloat = Math.sin(auraTime * 3.0) * 0.003;
                const auraOrbit = Math.cos(auraTime * 2.2) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + auraOrbit;
                mesh.position.y = mesh.userData.originalPosition.y + auraFloat;
                
                // Aura intensity pulsing
                const auraIntensity = 0.8 + Math.sin(auraTime * 5.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraIntensity;
                }
                
                // Aura opacity flickering (protective field energy)
                const auraOpacity = 0.7 + Math.sin(auraTime * 7.0) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * auraOpacity;
                }
            }
        });
    }

    // Overall stone skin pulsing (defensive power)
    const defensePulse = 1 + Math.sin(time * 1.8) * 0.05;
    cardGroup.scale.setScalar(0.8 * defensePulse);
}

// Export the stone skin card data for the loot system
export const STONE_SKIN_CARD_DATA = {
    name: 'Stone Skin',
    description: 'Transforms your skin into living stone, providing significant damage reduction and immunity to certain status effects while maintaining mobility.',
    category: 'card',
    rarity: 'epic',
    effect: 'stone_skin',
    effectValue: 50, // Percentage damage reduction
    createFunction: createStoneSkinCard,
    updateFunction: updateStoneSkinCardAnimation,
    voxelModel: 'stone_skin_card',
    glow: {
        color: 0x696969,
        intensity: 1.3
    }
};

export default createStoneSkinCard;