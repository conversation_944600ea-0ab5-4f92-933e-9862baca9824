import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached,
    // Use stone materials for catacombs theme
    stoneGrain1, // 958272
    stoneGrain2, // 9D8A7A
    stoneGrain3, // A59080
    stoneGrain4, // AA9586
    caveFloorCrackMaterial // 4a3a2a (For darker details)
} from './shared.js';

// --- Voxel Data for Large Ominous Angel Statue ---

// Store stone colors in an array for easier selection
const STONE_COLORS = ['958272', '9d8a7a', 'a59080', 'aa9586'];
const DARK_STONE = '4a3a2a'; // Dark color for shadows and details
const LIGHT_STONE = 'aa9586'; // Lighter stone for highlights

// Define a much larger and more detailed angel statue
// Structure: Large Pedestal -> Detailed Angel Body -> Massive Wings -> Head -> Details
const largeAngelStatueShape = [
    // === LARGE PEDESTAL BASE (Y: 0-4) ===
    // Bottom layer (Y=0) - 7x7 square
    ...generateSquareLayer(0, 3, STONE_COLORS[0]),
    // Second layer (Y=1) - 5x5 square
    ...generateSquareLayer(1, 2, STONE_COLORS[1]),
    // Third layer (Y=2) - 5x5 square
    ...generateSquareLayer(2, 2, STONE_COLORS[0]),
    // Fourth layer (Y=3) - 3x3 square
    ...generateSquareLayer(3, 1, STONE_COLORS[1]),
    // Top pedestal layer (Y=4) - 3x3 square
    ...generateSquareLayer(4, 1, STONE_COLORS[0]),

    // === ANGEL FEET AND LEGS (Y: 5-8) ===
    // Feet (Y=5) - wider base
    ...generateRectangleLayer(5, 2, 1, STONE_COLORS[2]),
    // Lower legs (Y=6-7)
    ...generateRectangleLayer(6, 1, 1, STONE_COLORS[2]),
    ...generateRectangleLayer(7, 1, 1, STONE_COLORS[2]),
    // Upper legs (Y=8)
    ...generateRectangleLayer(8, 1, 1, STONE_COLORS[1]),

    // === ANGEL TORSO (Y: 9-15) ===
    // Lower torso (Y=9-11)
    ...generateRectangleLayer(9, 2, 1, STONE_COLORS[1]),
    ...generateRectangleLayer(10, 2, 1, STONE_COLORS[1]),
    ...generateRectangleLayer(11, 2, 1, STONE_COLORS[1]),
    // Mid torso (Y=12-13)
    ...generateRectangleLayer(12, 2, 1, STONE_COLORS[0]),
    ...generateRectangleLayer(13, 2, 1, STONE_COLORS[0]),
    // Upper torso/chest (Y=14-15)
    ...generateRectangleLayer(14, 2, 1, STONE_COLORS[0]),
    ...generateRectangleLayer(15, 2, 1, STONE_COLORS[0]),

    // === MASSIVE WINGS (Y: 10-20) ===
    // Left wing - extending far west
    ...generateWingLayer(10, -3, -6, STONE_COLORS[2]),
    ...generateWingLayer(11, -3, -6, STONE_COLORS[2]),
    ...generateWingLayer(12, -3, -7, STONE_COLORS[2]),
    ...generateWingLayer(13, -3, -7, STONE_COLORS[2]),
    ...generateWingLayer(14, -3, -8, STONE_COLORS[2]),
    ...generateWingLayer(15, -3, -8, STONE_COLORS[2]),
    ...generateWingLayer(16, -3, -7, STONE_COLORS[2]),
    ...generateWingLayer(17, -3, -6, STONE_COLORS[2]),
    ...generateWingLayer(18, -3, -5, STONE_COLORS[2]),
    ...generateWingLayer(19, -3, -4, STONE_COLORS[2]),
    ...generateWingLayer(20, -3, -3, STONE_COLORS[2]),

    // Right wing - extending far east
    ...generateWingLayer(10, 3, 6, STONE_COLORS[2]),
    ...generateWingLayer(11, 3, 6, STONE_COLORS[2]),
    ...generateWingLayer(12, 3, 7, STONE_COLORS[2]),
    ...generateWingLayer(13, 3, 7, STONE_COLORS[2]),
    ...generateWingLayer(14, 3, 8, STONE_COLORS[2]),
    ...generateWingLayer(15, 3, 8, STONE_COLORS[2]),
    ...generateWingLayer(16, 3, 7, STONE_COLORS[2]),
    ...generateWingLayer(17, 3, 6, STONE_COLORS[2]),
    ...generateWingLayer(18, 3, 5, STONE_COLORS[2]),
    ...generateWingLayer(19, 3, 4, STONE_COLORS[2]),
    ...generateWingLayer(20, 3, 3, STONE_COLORS[2]),

    // === ARMS (Y: 12-16) ===
    // Left arm
    { x: -3, y: 12, z: 0, c: STONE_COLORS[1] },
    { x: -3, y: 13, z: 0, c: STONE_COLORS[1] },
    { x: -3, y: 14, z: 0, c: STONE_COLORS[1] },
    { x: -3, y: 15, z: 0, c: STONE_COLORS[1] },
    { x: -3, y: 16, z: 0, c: STONE_COLORS[1] },
    // Right arm
    { x: 3, y: 12, z: 0, c: STONE_COLORS[1] },
    { x: 3, y: 13, z: 0, c: STONE_COLORS[1] },
    { x: 3, y: 14, z: 0, c: STONE_COLORS[1] },
    { x: 3, y: 15, z: 0, c: STONE_COLORS[1] },
    { x: 3, y: 16, z: 0, c: STONE_COLORS[1] },

    // === NECK AND HEAD (Y: 16-20) ===
    // Neck (Y=16-17)
    { x: 0, y: 16, z: 0, c: STONE_COLORS[1] },
    { x: 0, y: 17, z: 0, c: STONE_COLORS[1] },

    // Head base (Y=18) - 3x3 square
    ...generateSquareLayer(18, 1, STONE_COLORS[0]),
    // Head middle (Y=19) - 3x3 square
    ...generateSquareLayer(19, 1, STONE_COLORS[0]),
    // Head top (Y=20)
    { x: 0, y: 20, z: 0, c: STONE_COLORS[0] },

    // === OMINOUS DETAILS ===
    // Dark shadows/cracks on body
    { x: 0, y: 9, z: 1, c: DARK_STONE },   // Lower torso shadow
    { x: 0, y: 12, z: 1, c: DARK_STONE },  // Mid torso shadow
    { x: 0, y: 15, z: 1, c: DARK_STONE },  // Upper torso shadow

    // Eyes (dark voxels on head)
    { x: -1, y: 18, z: 1, c: DARK_STONE }, // Left eye
    { x: 1, y: 18, z: 1, c: DARK_STONE },  // Right eye

    // Wing tips (darker for ominous effect)
    { x: -8, y: 14, z: 0, c: DARK_STONE },
    { x: -8, y: 15, z: 0, c: DARK_STONE },
    { x: 8, y: 14, z: 0, c: DARK_STONE },
    { x: 8, y: 15, z: 0, c: DARK_STONE },

    // Additional ominous details
    { x: 0, y: 18, z: -1, c: DARK_STONE }, // Back of head shadow
    { x: 0, y: 10, z: -1, c: DARK_STONE }, // Back torso shadow
];

// Helper function to generate a square layer
function generateSquareLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate a rectangular layer
function generateRectangleLayer(y, xRadius, zRadius, color) {
    const layer = [];
    for (let x = -xRadius; x <= xRadius; x++) {
        for (let z = -zRadius; z <= zRadius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate wing layers
function generateWingLayer(y, startX, endX, color) {
    const layer = [];
    const minX = Math.min(startX, endX);
    const maxX = Math.max(startX, endX);
    for (let x = minX; x <= maxX; x++) {
        layer.push({ x, y, z: 0, c: color });
    }
    return layer;
}

// --- Main Prefab Function ---
export function createLargeAngelStatueObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 887); // Different multiplier for variation

    // Use a large voxel size for impressive statue (reduced by 36% total)
    const statueVoxelSize = VOXEL_SIZE * 5.12; // 6.4 * 0.8 = 5.12 (another 20% smaller)
    const baseGeometry = getOrCreateGeometry('large_angel_statue_voxel', () =>
        new THREE.BoxGeometry(statueVoxelSize, statueVoxelSize, statueVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the statue shape
    largeAngelStatueShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to stone color for weathered effect
        let finalColor = c;
        if (rng() < 0.2) { // 20% chance for weathering
            const colorIndex = Math.floor(rng() * STONE_COLORS.length);
            finalColor = STONE_COLORS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * statueVoxelSize,
            y * statueVoxelSize,
            z * statueVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'large_angel_statue',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        originalVoxels: largeAngelStatueShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 19 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('Large angel statue created with options:', options);
    console.log('Large angel statue userData:', group.userData);

    return group;
}
