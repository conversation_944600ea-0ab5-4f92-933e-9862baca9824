import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached,
    // Use stone materials for catacombs theme
    stoneGrain1, // 958272
    stoneGrain2, // 9D8A7A
    stoneGrain3, // A59080
    stoneGrain4, // AA9586
    caveFloorCrackMaterial // 4a3a2a (For darker details)
} from './shared.js';

// --- Voxel Data for Ominous Angel Statue ---

// Store stone colors in an array for easier selection
const STONE_COLORS = ['958272', '9d8a7a', 'a59080', 'aa9586'];
const DARK_STONE = '4a3a2a'; // Dark color for shadows and details
const LIGHT_STONE = 'aa9586'; // Lighter stone for highlights

// Define the angel statue shape using voxel coordinates
// Structure: Pedestal base -> Angel body -> Wings -> Head -> Details
const angelStatueShape = [
    // === PEDESTAL BASE (Y: 0-2) ===
    // Base layer (Y=0) - 5x5 square
    ...generateSquareLayer(0, 2, STONE_COLORS[0]),
    // Middle layer (Y=1) - 3x3 square
    ...generateSquareLayer(1, 1, STONE_COLORS[1]),
    // Top layer (Y=2) - 3x3 square
    ...generateSquareLayer(2, 1, STONE_COLORS[0]),

    // === ANGEL BODY (Y: 3-8) ===
    // Feet/base (Y=3) - 3x1 rectangle
    { x: -1, y: 3, z: 0, c: STONE_COLORS[2] },
    { x: 0, y: 3, z: 0, c: STONE_COLORS[2] },
    { x: 1, y: 3, z: 0, c: STONE_COLORS[2] },

    // Lower torso (Y=4-5) - 3x1 rectangle
    { x: -1, y: 4, z: 0, c: STONE_COLORS[1] },
    { x: 0, y: 4, z: 0, c: STONE_COLORS[1] },
    { x: 1, y: 4, z: 0, c: STONE_COLORS[1] },
    { x: -1, y: 5, z: 0, c: STONE_COLORS[1] },
    { x: 0, y: 5, z: 0, c: STONE_COLORS[1] },
    { x: 1, y: 5, z: 0, c: STONE_COLORS[1] },

    // Upper torso (Y=6-7) - 3x1 rectangle
    { x: -1, y: 6, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 6, z: 0, c: STONE_COLORS[0] },
    { x: 1, y: 6, z: 0, c: STONE_COLORS[0] },
    { x: -1, y: 7, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 7, z: 0, c: STONE_COLORS[0] },
    { x: 1, y: 7, z: 0, c: STONE_COLORS[0] },

    // === WINGS (Y: 5-9) ===
    // Left wing (extending west)
    { x: -2, y: 5, z: 0, c: STONE_COLORS[2] },
    { x: -3, y: 5, z: 0, c: STONE_COLORS[2] },
    { x: -2, y: 6, z: 0, c: STONE_COLORS[2] },
    { x: -3, y: 6, z: 0, c: STONE_COLORS[2] },
    { x: -2, y: 7, z: 0, c: STONE_COLORS[2] },
    { x: -3, y: 7, z: 0, c: STONE_COLORS[2] },
    { x: -2, y: 8, z: 0, c: STONE_COLORS[2] },
    { x: -3, y: 8, z: 0, c: STONE_COLORS[2] },
    { x: -2, y: 9, z: 0, c: STONE_COLORS[2] },

    // Right wing (extending east)
    { x: 2, y: 5, z: 0, c: STONE_COLORS[2] },
    { x: 3, y: 5, z: 0, c: STONE_COLORS[2] },
    { x: 2, y: 6, z: 0, c: STONE_COLORS[2] },
    { x: 3, y: 6, z: 0, c: STONE_COLORS[2] },
    { x: 2, y: 7, z: 0, c: STONE_COLORS[2] },
    { x: 3, y: 7, z: 0, c: STONE_COLORS[2] },
    { x: 2, y: 8, z: 0, c: STONE_COLORS[2] },
    { x: 3, y: 8, z: 0, c: STONE_COLORS[2] },
    { x: 2, y: 9, z: 0, c: STONE_COLORS[2] },

    // === HEAD (Y: 8-10) ===
    // Neck (Y=8)
    { x: 0, y: 8, z: 0, c: STONE_COLORS[1] },

    // Head base (Y=9) - 3x3 square
    { x: -1, y: 9, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 9, z: 0, c: STONE_COLORS[0] },
    { x: 1, y: 9, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 9, z: -1, c: STONE_COLORS[0] },
    { x: 0, y: 9, z: 1, c: STONE_COLORS[0] },

    // Head top (Y=10)
    { x: 0, y: 10, z: 0, c: STONE_COLORS[0] },

    // === OMINOUS DETAILS ===
    // Dark shadows/cracks on body
    { x: 0, y: 4, z: 1, c: DARK_STONE }, // Front shadow
    { x: 0, y: 6, z: 1, c: DARK_STONE }, // Chest shadow

    // Eyes (dark voxels on head)
    { x: -1, y: 9, z: 1, c: DARK_STONE }, // Left eye
    { x: 1, y: 9, z: 1, c: DARK_STONE },  // Right eye

    // Wing tips (darker for ominous effect)
    { x: -3, y: 9, z: 0, c: DARK_STONE },
    { x: 3, y: 9, z: 0, c: DARK_STONE },
];

// Helper function to generate a square layer
function generateSquareLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// --- Main Prefab Function ---
export function createOminousAngelStatueObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 773); // Different multiplier for variation

    // Use a much larger voxel size for the statue (10x bigger)
    const statueVoxelSize = VOXEL_SIZE * 20.0; // 10 times bigger than the original 2.0
    const baseGeometry = getOrCreateGeometry('angel_statue_voxel', () =>
        new THREE.BoxGeometry(statueVoxelSize, statueVoxelSize, statueVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the statue shape
    angelStatueShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to stone color for weathered effect
        let finalColor = c;
        if (rng() < 0.3) { // 30% chance for weathering
            const colorIndex = Math.floor(rng() * STONE_COLORS.length);
            finalColor = STONE_COLORS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * statueVoxelSize,
            y * statueVoxelSize,
            z * statueVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'ominous_angel_statue',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        originalVoxels: angelStatueShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 17 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('Ominous angel statue created with options:', options);
    console.log('Angel statue userData:', group.userData);

    return group;
}
