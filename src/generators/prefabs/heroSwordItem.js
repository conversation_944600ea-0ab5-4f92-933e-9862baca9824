import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Voxel Data for Hero Sword ---

// Define sword colors
const BLADE_COLORS = ['C0C0C0', 'D3D3D3', 'E5E5E5', 'F0F0F0']; // Silver blade tones
const HILT_COLORS = ['8B4513', '654321', '5D4037']; // Brown leather grip
const GUARD_COLORS = ['FFD700', 'FFA500', 'DAA520']; // Gold crossguard
const POMMEL_COLORS = ['4A4A4A', '696969', '808080']; // Dark metal pommel
const GEM_COLOR = 'FF0000'; // Red gem

// Define the hero sword shape using voxel coordinates
// Structure: Pommel -> Grip -> Guard -> Blade -> Tip
const heroSwordShape = [
    // === POMMEL (Y: 0-1) ===
    // Pommel base (Y=0)
    { x: 0, y: 0, z: 0, c: POMMEL_COLORS[0] },
    { x: -1, y: 0, z: 0, c: POMMEL_COLORS[1] },
    { x: 1, y: 0, z: 0, c: POMMEL_COLORS[1] },
    { x: 0, y: 0, z: -1, c: POMMEL_COLORS[1] },
    { x: 0, y: 0, z: 1, c: POMMEL_COLORS[1] },
    
    // Pommel top (Y=1) with gem
    { x: 0, y: 1, z: 0, c: GEM_COLOR }, // Central gem
    { x: -1, y: 1, z: 0, c: POMMEL_COLORS[0] },
    { x: 1, y: 1, z: 0, c: POMMEL_COLORS[0] },

    // === GRIP/HANDLE (Y: 2-7) ===
    // Leather-wrapped grip
    { x: 0, y: 2, z: 0, c: HILT_COLORS[0] },
    { x: 0, y: 3, z: 0, c: HILT_COLORS[1] },
    { x: 0, y: 4, z: 0, c: HILT_COLORS[0] },
    { x: 0, y: 5, z: 0, c: HILT_COLORS[2] },
    { x: 0, y: 6, z: 0, c: HILT_COLORS[0] },
    { x: 0, y: 7, z: 0, c: HILT_COLORS[1] },

    // === CROSSGUARD (Y: 8) ===
    // Horizontal crossguard
    { x: -3, y: 8, z: 0, c: GUARD_COLORS[0] },
    { x: -2, y: 8, z: 0, c: GUARD_COLORS[1] },
    { x: -1, y: 8, z: 0, c: GUARD_COLORS[0] },
    { x: 0, y: 8, z: 0, c: GUARD_COLORS[2] },
    { x: 1, y: 8, z: 0, c: GUARD_COLORS[0] },
    { x: 2, y: 8, z: 0, c: GUARD_COLORS[1] },
    { x: 3, y: 8, z: 0, c: GUARD_COLORS[0] },

    // === BLADE (Y: 9-20) ===
    // Blade base (Y=9-10) - full width
    { x: -1, y: 9, z: 0, c: BLADE_COLORS[0] },
    { x: 0, y: 9, z: 0, c: BLADE_COLORS[1] },
    { x: 1, y: 9, z: 0, c: BLADE_COLORS[0] },
    { x: -1, y: 10, z: 0, c: BLADE_COLORS[1] },
    { x: 0, y: 10, z: 0, c: BLADE_COLORS[2] },
    { x: 1, y: 10, z: 0, c: BLADE_COLORS[1] },

    // Blade middle (Y=11-16) - full width
    { x: -1, y: 11, z: 0, c: BLADE_COLORS[0] },
    { x: 0, y: 11, z: 0, c: BLADE_COLORS[3] },
    { x: 1, y: 11, z: 0, c: BLADE_COLORS[0] },
    { x: -1, y: 12, z: 0, c: BLADE_COLORS[1] },
    { x: 0, y: 12, z: 0, c: BLADE_COLORS[2] },
    { x: 1, y: 12, z: 0, c: BLADE_COLORS[1] },
    { x: -1, y: 13, z: 0, c: BLADE_COLORS[0] },
    { x: 0, y: 13, z: 0, c: BLADE_COLORS[3] },
    { x: 1, y: 13, z: 0, c: BLADE_COLORS[0] },
    { x: -1, y: 14, z: 0, c: BLADE_COLORS[1] },
    { x: 0, y: 14, z: 0, c: BLADE_COLORS[2] },
    { x: 1, y: 14, z: 0, c: BLADE_COLORS[1] },
    { x: -1, y: 15, z: 0, c: BLADE_COLORS[0] },
    { x: 0, y: 15, z: 0, c: BLADE_COLORS[3] },
    { x: 1, y: 15, z: 0, c: BLADE_COLORS[0] },
    { x: -1, y: 16, z: 0, c: BLADE_COLORS[1] },
    { x: 0, y: 16, z: 0, c: BLADE_COLORS[2] },
    { x: 1, y: 16, z: 0, c: BLADE_COLORS[1] },

    // Blade taper (Y=17-19) - narrowing
    { x: 0, y: 17, z: 0, c: BLADE_COLORS[3] },
    { x: -1, y: 17, z: 0, c: BLADE_COLORS[1] },
    { x: 1, y: 17, z: 0, c: BLADE_COLORS[1] },
    { x: 0, y: 18, z: 0, c: BLADE_COLORS[2] },
    { x: 0, y: 19, z: 0, c: BLADE_COLORS[3] },

    // === BLADE TIP (Y: 20) ===
    { x: 0, y: 20, z: 0, c: BLADE_COLORS[3] },
];

// --- Main Item Function ---
export function createHeroSwordItem(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 2341); // Different multiplier for variation

    // Use smaller voxel size for items
    const itemVoxelSize = VOXEL_SIZE * 2.0; // Smaller than floor objects
    const baseGeometry = getOrCreateGeometry('hero_sword_voxel', () =>
        new THREE.BoxGeometry(itemVoxelSize, itemVoxelSize, itemVoxelSize)
    );

    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the sword shape
    heroSwordShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to metallic colors for shine effect
        let finalColor = c;
        if (c.startsWith('C0') || c.startsWith('D3') || c.startsWith('E5') || c.startsWith('F0')) {
            // Blade colors - add shine variation
            if (rng() < 0.3) { // 30% chance for shine
                const shineIndex = Math.floor(rng() * BLADE_COLORS.length);
                finalColor = BLADE_COLORS[shineIndex];
            }
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * itemVoxelSize,
            y * itemVoxelSize,
            z * itemVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        itemType: 'hero_sword',
        itemName: 'Hero Sword',
        itemDescription: 'A legendary sword once wielded by a great hero. Its blade gleams with ancient power.',
        itemCategory: 'weapon',
        itemRarity: 'legendary',
        voxelScale: itemVoxelSize,
        
        // Animation properties
        isFloating: false,
        floatSpeed: 1.0,
        rotationSpeed: 1.0,
        
        originalVoxels: heroSwordShape.map(v => {
            const voxelRng = mulberry32(seed * 17 + v.x * 3 + v.y * 5 + v.z * 7);
            return {...v, c: v.c}; // Keep original color
        })
    };

    // Rotate sword to be upright (blade pointing up)
    group.rotation.z = 0; // Sword is already vertical in our voxel design

    console.log('Hero sword item created with options:', options);
    console.log('Hero sword userData:', group.userData);

    return group;
}

// Item metadata for the loot system
export const HERO_SWORD_DATA = {
    id: 'hero_sword',
    name: 'Hero Sword',
    description: 'A legendary sword once wielded by a great hero. Its blade gleams with ancient power.',
    type: 'weapon',
    category: 'weapon',
    rarity: 'legendary',
    createFunction: createHeroSwordItem,
    dropChance: 25, // 25% chance in normal chests
    value: 1000,
    stackable: false,

    // Weapon-specific properties
    weaponType: 'melee',
    damage: 2, // Higher damage than Soul Blast
    range: 4, // Melee range
    attackSpeed: 1.2, // Slightly faster than default

    // Attack pattern for directional combat
    attackPattern: {
        type: 'frontal_arc',
        range: 3.0, // Realistic sword reach
        angle: 90, // 90-degree arc in front of player
        description: 'Sword swing in a frontal arc'
    }
};
