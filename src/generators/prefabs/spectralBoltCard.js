import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Spectral Bolt Card Prefab
 * Creates an ethereal energy bolt with crackling energy for offensive magic
 */

// Spectral bolt specific colors
const SPECTRAL_COLORS = {
    ENERGY_CORE: 0x4DC8FF,      // Bright ethereal blue
    ENERGY_BRIGHT: 0x80E0FF,    // Lighter ethereal blue
    ENERGY_WHITE: 0xFFFFFF,     // Pure white energy
    LIGHTNING: 0xE0F8FF,        // Very light blue-white
    TRAIL: 0x66CCFF,            // Medium blue
    SPARK: 0xB3E5FC             // Light blue sparks
};

/**
 * Create a spectral bolt card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The spectral bolt card 3D model
 */
export function createSpectralBoltCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SpectralBoltCard';

    // Materials
    const energyCoreMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.ENERGY_CORE,
        emissive: SPECTRAL_COLORS.ENERGY_CORE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const energyBrightMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.ENERGY_BRIGHT,
        emissive: SPECTRAL_COLORS.ENERGY_BRIGHT,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const energyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.ENERGY_WHITE,
        emissive: SPECTRAL_COLORS.ENERGY_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const lightningMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.LIGHTNING,
        emissive: SPECTRAL_COLORS.LIGHTNING,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.7
    });

    const trailMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.TRAIL,
        emissive: SPECTRAL_COLORS.TRAIL,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    const sparkMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.SPARK,
        emissive: SPECTRAL_COLORS.SPARK,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create bolt core (main projectile)
    const boltCoreVoxels = [
        // Main bolt body (horizontal arrow shape pointing right)
        { x: 0, y: 0, z: 0, material: energyWhiteMaterial },
        { x: 0.1, y: 0, z: 0, material: energyCoreMaterial },
        { x: 0.2, y: 0, z: 0, material: energyCoreMaterial },
        { x: 0.3, y: 0, z: 0, material: energyWhiteMaterial },
        
        // Arrow head (pointing right)
        { x: 0.4, y: 0, z: 0, material: energyWhiteMaterial },
        { x: 0.3, y: 0.05, z: 0, material: energyCoreMaterial },
        { x: 0.3, y: -0.05, z: 0, material: energyCoreMaterial },
        
        // Core energy intensifiers
        { x: 0.15, y: 0, z: 0, material: energyWhiteMaterial },
        { x: 0.25, y: 0, z: 0, material: energyWhiteMaterial }
    ];

    // Create energy trail (behind the bolt)
    const energyTrailVoxels = [
        // Main trail
        { x: -0.1, y: 0, z: 0, material: trailMaterial },
        { x: -0.2, y: 0, z: 0, material: trailMaterial },
        { x: -0.3, y: 0, z: 0, material: trailMaterial },
        
        // Trail variations
        { x: -0.15, y: 0.05, z: 0, material: energyBrightMaterial },
        { x: -0.15, y: -0.05, z: 0, material: energyBrightMaterial },
        { x: -0.25, y: 0.05, z: 0, material: trailMaterial },
        { x: -0.25, y: -0.05, z: 0, material: trailMaterial }
    ];

    // Create crackling lightning around the bolt
    const lightningVoxels = [
        // Lightning crackling around bolt
        { x: 0.05, y: 0.1, z: 0, material: lightningMaterial },
        { x: 0.15, y: -0.1, z: 0, material: lightningMaterial },
        { x: 0.25, y: 0.1, z: 0, material: lightningMaterial },
        { x: 0.35, y: -0.1, z: 0, material: lightningMaterial },
        
        // Side lightning
        { x: 0.1, y: 0.15, z: 0, material: sparkMaterial },
        { x: 0.2, y: -0.15, z: 0, material: sparkMaterial },
        { x: 0.3, y: 0.15, z: 0, material: sparkMaterial },
        
        // Vertical lightning streaks
        { x: 0.05, y: 0.2, z: 0, material: lightningMaterial },
        { x: 0.25, y: -0.2, z: 0, material: lightningMaterial }
    ];

    // Create energy sparks (floating around)
    const sparkVoxels = [
        // Floating sparks
        { x: -0.1, y: 0.2, z: 0, material: sparkMaterial },
        { x: 0.45, y: 0.1, z: 0, material: sparkMaterial },
        { x: -0.05, y: -0.2, z: 0, material: sparkMaterial },
        { x: 0.35, y: 0.2, z: 0, material: sparkMaterial },
        { x: -0.2, y: 0.15, z: 0, material: sparkMaterial },
        { x: 0.4, y: -0.15, z: 0, material: sparkMaterial },
        
        // Energy fragments
        { x: 0.05, y: 0.25, z: 0, material: energyBrightMaterial },
        { x: 0.3, y: -0.25, z: 0, material: energyBrightMaterial },
        { x: -0.15, y: -0.1, z: 0, material: sparkMaterial }
    ];

    // Create all voxels and organize into groups
    const boltCoreGroup = new THREE.Group();
    const energyTrailGroup = new THREE.Group();
    const lightningGroup = new THREE.Group();
    const sparkGroup = new THREE.Group();

    boltCoreGroup.name = 'boltCore';
    energyTrailGroup.name = 'energyTrail';
    lightningGroup.name = 'lightning';
    sparkGroup.name = 'sparks';

    // Add bolt core voxels
    boltCoreVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        boltCoreGroup.add(mesh);
    });

    // Add energy trail voxels
    energyTrailVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            -0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        energyTrailGroup.add(mesh);
    });

    // Add lightning voxels
    lightningVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.lightningPhase = index * 0.3; // Stagger lightning animation
        lightningGroup.add(mesh);
    });

    // Add spark voxels
    sparkVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.02
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        mesh.userData.sparkPhase = index * 0.4; // Stagger spark animation
        sparkGroup.add(mesh);
    });

    // Add all groups to card
    cardGroup.add(boltCoreGroup);
    cardGroup.add(energyTrailGroup);
    cardGroup.add(lightningGroup);
    cardGroup.add(sparkGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        energyPhase: 0,
        lightningPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update spectral bolt card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSpectralBoltCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.energyPhase += deltaTime * 3; // Energy pulsing speed
    cardGroup.userData.lightningPhase += deltaTime * 8; // Lightning flickering speed

    const time = cardGroup.userData.animationTime;
    const energyPhase = cardGroup.userData.energyPhase;
    const lightningPhase = cardGroup.userData.lightningPhase;

    // Animate bolt core with pulsing energy
    const boltCoreGroup = cardGroup.getObjectByName('boltCore');
    if (boltCoreGroup) {
        const energyPulse = 0.7 + Math.sin(energyPhase) * 0.3; // 0.4 to 1.0
        
        boltCoreGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyPulse;
            }
        });
    }

    // Animate energy trail with flowing effect
    const energyTrailGroup = cardGroup.getObjectByName('energyTrail');
    if (energyTrailGroup) {
        const trailFlow = 0.5 + Math.sin(energyPhase * 1.5) * 0.5; // 0.0 to 1.0
        
        energyTrailGroup.children.forEach(mesh => {
            if (mesh.material) {
                mesh.material.opacity = mesh.userData.originalOpacity * trailFlow;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * trailFlow;
                }
            }
        });
    }

    // Animate lightning with rapid flickering
    const lightningGroup = cardGroup.getObjectByName('lightning');
    if (lightningGroup) {
        lightningGroup.children.forEach(mesh => {
            const lightningFlicker = Math.sin(lightningPhase + mesh.userData.lightningPhase) > 0.3 ? 1.0 : 0.2;
            
            if (mesh.material) {
                mesh.material.opacity = mesh.userData.originalOpacity * lightningFlicker;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * lightningFlicker;
                }
            }
        });
    }

    // Animate sparks with floating motion
    const sparkGroup = cardGroup.getObjectByName('sparks');
    if (sparkGroup) {
        sparkGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparkPhase !== undefined) {
                const sparkTime = time + mesh.userData.sparkPhase;
                const floatX = Math.sin(sparkTime * 2) * 0.01;
                const floatY = Math.cos(sparkTime * 1.5) * 0.01;
                
                mesh.position.x = mesh.userData.originalPosition.x + floatX;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                
                // Twinkling effect
                const twinkle = 0.3 + Math.sin(sparkTime * 4) * 0.7; // 0.3 to 1.0
                if (mesh.material) {
                    mesh.material.opacity = mesh.userData.originalOpacity * twinkle;
                    if (mesh.userData.originalEmissive !== undefined) {
                        mesh.material.emissiveIntensity = mesh.userData.originalEmissive * twinkle;
                    }
                }
            }
        });
    }

    // Gentle overall rotation for dynamic effect
    const rotationSpeed = Math.sin(time * 0.5) * 0.02;
    cardGroup.rotation.z = rotationSpeed;
}

// Export the spectral bolt card data for the loot system
export const SPECTRAL_BOLT_CARD_DATA = {
    name: 'Spectral Bolt',
    description: 'Channels devastating ethereal magic to unleash three consecutive salvos of ten bolts each, raining thirty piercing projectiles in all directions over six seconds of sustained destruction.',
    category: 'card',
    rarity: 'rare',
    effect: 'spectral_projectile',
    effectValue: 25,
    createFunction: createSpectralBoltCard,
    updateFunction: updateSpectralBoltCardAnimation,
    voxelModel: 'spectral_bolt_card',
    glow: {
        color: 0x4DC8FF,
        intensity: 1.3
    }
};

export default createSpectralBoltCard;