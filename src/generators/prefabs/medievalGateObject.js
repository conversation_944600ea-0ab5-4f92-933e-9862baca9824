import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Medieval Gate - Voxel-style imposing gate following devil room aesthetic
 * Can be configured as treasure gate (golden) or death gate (dark)
 */
export function createMedievalGateObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 666);
    
    const gateType = options.gateType || 'treasure'; // 'treasure' or 'death'
    const isDeathGate = gateType === 'death';
    
    // Use consistent voxel size for gate construction
    const gateVoxelSize = VOXEL_SIZE * 3.0; // Large voxels for imposing gates
    const baseGeometry = getOrCreateGeometry('gate_voxel', () =>
        new THREE.BoxGeometry(gateVoxelSize, gateVoxelSize, gateVoxelSize)
    );
    
    // Gate materials using game's hex material system
    let stoneMaterial, metalMaterial, accentMaterial, glowColor;
    
    if (isDeathGate) {
        // Dark death gate with red accents
        stoneMaterial = _getMaterialByHex_Cached('2C2C2C', {
            roughness: 0.8,
            metalness: 0.2,
            emissive: new THREE.Color(0x0A0505),
            emissiveIntensity: 0.05
        });
        metalMaterial = _getMaterialByHex_Cached('1A1A1A', {
            roughness: 0.6,
            metalness: 0.9,
            emissive: new THREE.Color(0x050202),
            emissiveIntensity: 0.1
        });
        accentMaterial = _getMaterialByHex_Cached('8B0000', {
            roughness: 0.3,
            metalness: 0.4,
            emissive: new THREE.Color(0x8B0000),
            emissiveIntensity: 0.3
        });
        glowColor = 0x8B0000;
    } else {
        // Golden treasure gate with blue accents
        stoneMaterial = _getMaterialByHex_Cached('D4AF37', {
            roughness: 0.4,
            metalness: 0.5,
            emissive: new THREE.Color(0x2A2A0A),
            emissiveIntensity: 0.05
        });
        metalMaterial = _getMaterialByHex_Cached('FFD700', {
            roughness: 0.2,
            metalness: 0.9,
            emissive: new THREE.Color(0x2A2A0A),
            emissiveIntensity: 0.1
        });
        accentMaterial = _getMaterialByHex_Cached('00CED1', {
            roughness: 0.1,
            metalness: 0.8,
            emissive: new THREE.Color(0x008B8B),
            emissiveIntensity: 0.2
        });
        glowColor = 0xFFD700;
    }
    
    // Gate structure using voxel positioning arrays (like devil room objects)
    const gateFrame = [
        // Left pillar (tower)
        { x: -3, y: 0, z: 0 }, { x: -3, y: 1, z: 0 }, { x: -3, y: 2, z: 0 }, 
        { x: -3, y: 3, z: 0 }, { x: -3, y: 4, z: 0 }, { x: -3, y: 5, z: 0 },
        { x: -3, y: 6, z: 0 }, { x: -3, y: 7, z: 0 }, { x: -3, y: 8, z: 0 },
        
        // Right pillar (tower)
        { x: 3, y: 0, z: 0 }, { x: 3, y: 1, z: 0 }, { x: 3, y: 2, z: 0 }, 
        { x: 3, y: 3, z: 0 }, { x: 3, y: 4, z: 0 }, { x: 3, y: 5, z: 0 },
        { x: 3, y: 6, z: 0 }, { x: 3, y: 7, z: 0 }, { x: 3, y: 8, z: 0 },
        
        // Top lintel (connecting the towers)
        { x: -2, y: 8, z: 0 }, { x: -1, y: 8, z: 0 }, { x: 0, y: 8, z: 0 }, 
        { x: 1, y: 8, z: 0 }, { x: 2, y: 8, z: 0 },
        
        // Archway support
        { x: -2, y: 7, z: 0 }, { x: 2, y: 7, z: 0 },
        
        // Base supports
        { x: -3, y: 0, z: 1 }, { x: -3, y: 1, z: 1 }, { x: -3, y: 2, z: 1 },
        { x: 3, y: 0, z: 1 }, { x: 3, y: 1, z: 1 }, { x: 3, y: 2, z: 1 }
    ];
    
    // Add main gate frame structure
    gateFrame.forEach(pos => {
        const voxel = new THREE.Mesh(baseGeometry.clone(), stoneMaterial);
        voxel.position.set(
            pos.x * gateVoxelSize,
            pos.y * gateVoxelSize,
            pos.z * gateVoxelSize
        );
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    });
    
    // Gate doors (metal barriers)
    const gateDoors = [
        // Left door
        { x: -2, y: 1, z: 0 }, { x: -2, y: 2, z: 0 }, { x: -2, y: 3, z: 0 }, 
        { x: -2, y: 4, z: 0 }, { x: -2, y: 5, z: 0 }, { x: -2, y: 6, z: 0 },
        { x: -1, y: 1, z: 0 }, { x: -1, y: 2, z: 0 }, { x: -1, y: 3, z: 0 }, 
        { x: -1, y: 4, z: 0 }, { x: -1, y: 5, z: 0 }, { x: -1, y: 6, z: 0 },
        
        // Right door  
        { x: 1, y: 1, z: 0 }, { x: 1, y: 2, z: 0 }, { x: 1, y: 3, z: 0 }, 
        { x: 1, y: 4, z: 0 }, { x: 1, y: 5, z: 0 }, { x: 1, y: 6, z: 0 },
        { x: 2, y: 1, z: 0 }, { x: 2, y: 2, z: 0 }, { x: 2, y: 3, z: 0 }, 
        { x: 2, y: 4, z: 0 }, { x: 2, y: 5, z: 0 }, { x: 2, y: 6, z: 0 }
    ];
    
    gateDoors.forEach(pos => {
        const door = new THREE.Mesh(baseGeometry.clone(), metalMaterial);
        door.position.set(
            pos.x * gateVoxelSize,
            pos.y * gateVoxelSize,
            pos.z * gateVoxelSize - gateVoxelSize * 0.3 // Slightly recessed
        );
        door.scale.set(0.9, 0.9, 0.3); // Thinner doors
        door.castShadow = true;
        door.receiveShadow = true;
        group.add(door);
    });
    
    // Metal reinforcements (cross pattern on doors)
    const reinforcements = [
        // Left door cross pattern
        { x: -1.5, y: 2, z: 0 }, { x: -1.5, y: 4, z: 0 }, { x: -1.5, y: 6, z: 0 },
        // Right door cross pattern
        { x: 1.5, y: 2, z: 0 }, { x: 1.5, y: 4, z: 0 }, { x: 1.5, y: 6, z: 0 },
        // Central seam
        { x: 0, y: 3, z: 0 }, { x: 0, y: 4, z: 0 }, { x: 0, y: 5, z: 0 }
    ];
    
    reinforcements.forEach(pos => {
        const reinforcement = new THREE.Mesh(baseGeometry.clone(), metalMaterial);
        reinforcement.position.set(
            pos.x * gateVoxelSize,
            pos.y * gateVoxelSize,
            pos.z * gateVoxelSize
        );
        reinforcement.scale.set(0.3, 0.3, 0.2); // Thin reinforcement bars
        reinforcement.castShadow = true;
        reinforcement.receiveShadow = true;
        group.add(reinforcement);
    });
    
    // Decorative accent voxels
    if (isDeathGate) {
        // Death gate: Spikes and skulls
        const deathDecor = [
            // Skull motif at top center
            { x: 0, y: 9, z: 0 }, { x: 0, y: 10, z: 0 },
            // Spikes on towers
            { x: -3, y: 9, z: 0 }, { x: 3, y: 9, z: 0 },
            { x: -3, y: 10, z: 0 }, { x: 3, y: 10, z: 0 },
            // Side decorations
            { x: -4, y: 4, z: 0 }, { x: 4, y: 4, z: 0 },
            { x: -4, y: 6, z: 0 }, { x: 4, y: 6, z: 0 }
        ];
        
        deathDecor.forEach(pos => {
            const decor = new THREE.Mesh(baseGeometry.clone(), accentMaterial);
            decor.position.set(
                pos.x * gateVoxelSize,
                pos.y * gateVoxelSize,
                pos.z * gateVoxelSize
            );
            decor.scale.set(0.6, 0.6, 0.6); // Smaller decorative elements
            decor.castShadow = true;
            decor.receiveShadow = true;
            group.add(decor);
        });
    } else {
        // Treasure gate: Gems and orbs
        const treasureDecor = [
            // Central gem at top
            { x: 0, y: 9, z: 0 }, { x: 0, y: 10, z: 0 },
            // Corner gems
            { x: -2, y: 6, z: 1 }, { x: 2, y: 6, z: 1 },
            { x: -2, y: 4, z: 1 }, { x: 2, y: 4, z: 1 },
            // Tower orbs
            { x: -3, y: 9, z: 0 }, { x: 3, y: 9, z: 0 },
            { x: -3, y: 10, z: 0 }, { x: 3, y: 10, z: 0 }
        ];
        
        treasureDecor.forEach(pos => {
            const decor = new THREE.Mesh(baseGeometry.clone(), accentMaterial);
            decor.position.set(
                pos.x * gateVoxelSize,
                pos.y * gateVoxelSize,
                pos.z * gateVoxelSize
            );
            decor.scale.set(0.7, 0.7, 0.7); // Decorative gem elements
            decor.castShadow = true;
            decor.receiveShadow = true;
            group.add(decor);
        });
    }
    
    // Gate lighting (simpler, more focused)
    const gateLight = new THREE.PointLight(
        glowColor, 
        isDeathGate ? 1.2 : 2.0, 
        gateVoxelSize * 25
    );
    gateLight.position.set(0, 8 * gateVoxelSize, 2 * gateVoxelSize);
    gateLight.castShadow = true;
    group.add(gateLight);
    
    // Set up group properties following game conventions
    group.userData = {
        ...(options.userData || {}),
        objectType: isDeathGate ? 'death_gate' : 'treasure_gate',
        isInteractable: true,
        interactionType: 'gate_choice',
        gateType: gateType,
        leadsTo: isDeathGate ? 'death' : 'treasure',
        isDoor: true,
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: gateVoxelSize,
        canOpen: true // For gate opening mechanic
    };
    
    group.name = isDeathGate ? 'death_gate' : 'treasure_gate';
    
    // Store references for interaction
    group.userData.gateLight = gateLight;
    
    console.log(`[MedievalGate] ✅ Created ${gateType} gate with voxel aesthetic`);
    return group;
}

/**
 * Create treasure gate variant
 */
export function createTreasureGateObject(options = {}) {
    return createMedievalGateObject({
        ...options,
        gateType: 'treasure'
    });
}

/**
 * Create death gate variant  
 */
export function createDeathGateObject(options = {}) {
    return createMedievalGateObject({
        ...options,
        gateType: 'death'
    });
}