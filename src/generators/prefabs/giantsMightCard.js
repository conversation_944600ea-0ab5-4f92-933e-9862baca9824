import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Giant's Might Card Prefab
 * Creates titan transformation that grants massive size and overwhelming strength
 */

// Giant's might specific colors
const GIANT_COLORS = {
    TITAN_BRONZE: 0xCD7F32,          // Main titan body/muscles
    EARTH_BROWN: 0x8B4513,           // Earthy giant connection
    MOUNTAIN_GRAY: 0x696969,         // Stone-like strength
    GOLDEN_BRONZE: 0xB8860B,         // Metallic highlights
    DEEP_COPPER: 0xB87333,           // Muscle definition
    GRANITE_SILVER: 0xC0C0C0,        // Stone accents
    AMBER_GOLD: 0xFFBF00,            // Power emanations
    IRON_STEEL: 0x708090             // Metallic strength
};

/**
 * Create a giant's might card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The giant's might card 3D model
 */
export function createGiantsMightCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'GiantsMightCard';

    // Giant's might materials
    const titanBronzeMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.TITAN_BRONZE,
        emissive: GIANT_COLORS.TITAN_BRONZE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.95
    });

    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.EARTH_BROWN,
        emissive: GIANT_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 1.0
    });

    const mountainGrayMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.MOUNTAIN_GRAY,
        emissive: GIANT_COLORS.MOUNTAIN_GRAY,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const goldenBronzeMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.GOLDEN_BRONZE,
        emissive: GIANT_COLORS.GOLDEN_BRONZE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const deepCopperMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.DEEP_COPPER,
        emissive: GIANT_COLORS.DEEP_COPPER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const graniteSilverMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.GRANITE_SILVER,
        emissive: GIANT_COLORS.GRANITE_SILVER,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.8
    });

    const amberGoldMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.AMBER_GOLD,
        emissive: GIANT_COLORS.AMBER_GOLD,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const ironSteelMaterial = new THREE.MeshLambertMaterial({
        color: GIANT_COLORS.IRON_STEEL,
        emissive: GIANT_COLORS.IRON_STEEL,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Giant figure (massive titan form)
    const giantFigureVoxels = [
        // Giant head (proportionally large)
        { x: 0.0, y: 0.20, z: 0.0, material: titanBronzeMaterial },
        { x: -0.06, y: 0.20, z: 0.0, material: deepCopperMaterial },
        { x: 0.06, y: 0.20, z: 0.0, material: deepCopperMaterial },
        { x: 0.0, y: 0.26, z: 0.0, material: goldenBronzeMaterial },
        { x: -0.04, y: 0.24, z: 0.0, material: titanBronzeMaterial },
        { x: 0.04, y: 0.24, z: 0.0, material: titanBronzeMaterial },
        
        // Giant eyes (glowing with power)
        { x: -0.04, y: 0.22, z: 0.06, material: amberGoldMaterial },
        { x: 0.04, y: 0.22, z: 0.06, material: amberGoldMaterial },
        
        // Massive shoulders
        { x: -0.12, y: 0.14, z: 0.0, material: mountainGrayMaterial },
        { x: 0.12, y: 0.14, z: 0.0, material: mountainGrayMaterial },
        { x: -0.16, y: 0.10, z: 0.0, material: ironSteelMaterial },
        { x: 0.16, y: 0.10, z: 0.0, material: ironSteelMaterial },
        
        // Giant torso (broad and powerful)
        { x: 0.0, y: 0.16, z: 0.0, material: titanBronzeMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: deepCopperMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: titanBronzeMaterial },
        { x: 0.0, y: 0.04, z: 0.0, material: deepCopperMaterial },
        { x: -0.08, y: 0.12, z: 0.0, material: titanBronzeMaterial },
        { x: 0.08, y: 0.12, z: 0.0, material: titanBronzeMaterial },
        { x: -0.08, y: 0.08, z: 0.0, material: deepCopperMaterial },
        { x: 0.08, y: 0.08, z: 0.0, material: deepCopperMaterial },
        
        // Massive arms
        { x: -0.20, y: 0.06, z: 0.0, material: mountainGrayMaterial },
        { x: 0.20, y: 0.06, z: 0.0, material: mountainGrayMaterial },
        { x: -0.24, y: 0.02, z: 0.04, material: ironSteelMaterial },
        { x: 0.24, y: 0.02, z: 0.04, material: ironSteelMaterial },
        { x: -0.28, y: -0.02, z: 0.08, material: titanBronzeMaterial },
        { x: 0.28, y: -0.02, z: 0.08, material: titanBronzeMaterial },
        
        // Giant lower body
        { x: 0.0, y: 0.0, z: 0.0, material: earthBrownMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: mountainGrayMaterial },
        { x: 0.0, y: -0.08, z: 0.0, material: earthBrownMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: mountainGrayMaterial },
        
        // Massive legs
        { x: -0.08, y: -0.16, z: 0.0, material: ironSteelMaterial },
        { x: 0.08, y: -0.16, z: 0.0, material: ironSteelMaterial },
        { x: -0.08, y: -0.20, z: 0.0, material: mountainGrayMaterial },
        { x: 0.08, y: -0.20, z: 0.0, material: mountainGrayMaterial },
        { x: -0.08, y: -0.24, z: 0.0, material: earthBrownMaterial },
        { x: 0.08, y: -0.24, z: 0.0, material: earthBrownMaterial }
    ];

    // Strength aura (power emanating from giant)
    const strengthAuraVoxels = [
        // Inner strength ring
        { x: -0.14, y: 0.10, z: -0.06, material: amberGoldMaterial },
        { x: 0.14, y: 0.10, z: -0.06, material: amberGoldMaterial },
        { x: -0.10, y: 0.14, z: -0.06, material: goldenBronzeMaterial },
        { x: 0.10, y: 0.14, z: -0.06, material: goldenBronzeMaterial },
        { x: -0.14, y: 0.00, z: -0.06, material: amberGoldMaterial },
        { x: 0.14, y: 0.00, z: -0.06, material: amberGoldMaterial },
        { x: -0.10, y: -0.04, z: -0.06, material: goldenBronzeMaterial },
        { x: 0.10, y: -0.04, z: -0.06, material: goldenBronzeMaterial },
        
        // Middle strength ring
        { x: -0.18, y: 0.14, z: -0.10, material: goldenBronzeMaterial },
        { x: 0.18, y: 0.14, z: -0.10, material: goldenBronzeMaterial },
        { x: -0.14, y: 0.18, z: -0.10, material: amberGoldMaterial },
        { x: 0.14, y: 0.18, z: -0.10, material: amberGoldMaterial },
        { x: -0.18, y: -0.04, z: -0.10, material: goldenBronzeMaterial },
        { x: 0.18, y: -0.04, z: -0.10, material: goldenBronzeMaterial },
        { x: -0.14, y: -0.08, z: -0.10, material: amberGoldMaterial },
        { x: 0.14, y: -0.08, z: -0.10, material: amberGoldMaterial },
        
        // Outer strength ring
        { x: -0.22, y: 0.18, z: -0.14, material: amberGoldMaterial },
        { x: 0.22, y: 0.18, z: -0.14, material: amberGoldMaterial },
        { x: -0.18, y: 0.22, z: -0.14, material: goldenBronzeMaterial },
        { x: 0.18, y: 0.22, z: -0.14, material: goldenBronzeMaterial },
        { x: -0.22, y: -0.08, z: -0.14, material: amberGoldMaterial },
        { x: 0.22, y: -0.08, z: -0.14, material: amberGoldMaterial },
        { x: -0.18, y: -0.12, z: -0.14, material: goldenBronzeMaterial },
        { x: 0.18, y: -0.12, z: -0.14, material: goldenBronzeMaterial }
    ];

    // Size increase indicators (growth energy)
    const sizeIncreaseVoxels = [
        // Growth energy bursts
        { x: -0.26, y: 0.22, z: 0.0, material: amberGoldMaterial },
        { x: 0.26, y: 0.22, z: 0.0, material: amberGoldMaterial },
        { x: -0.30, y: 0.18, z: 0.04, material: goldenBronzeMaterial },
        { x: 0.30, y: 0.18, z: 0.04, material: goldenBronzeMaterial },
        { x: -0.34, y: 0.14, z: 0.08, material: amberGoldMaterial },
        { x: 0.34, y: 0.14, z: 0.08, material: amberGoldMaterial },
        
        // Expansion waves (top)
        { x: -0.22, y: 0.26, z: 0.08, material: goldenBronzeMaterial },
        { x: 0.22, y: 0.26, z: 0.08, material: goldenBronzeMaterial },
        { x: -0.18, y: 0.30, z: 0.12, material: amberGoldMaterial },
        { x: 0.18, y: 0.30, z: 0.12, material: amberGoldMaterial },
        { x: -0.14, y: 0.34, z: 0.16, material: goldenBronzeMaterial },
        { x: 0.14, y: 0.34, z: 0.16, material: goldenBronzeMaterial },
        
        // Expansion waves (bottom)
        { x: -0.22, y: -0.16, z: 0.08, material: goldenBronzeMaterial },
        { x: 0.22, y: -0.16, z: 0.08, material: goldenBronzeMaterial },
        { x: -0.18, y: -0.20, z: 0.12, material: amberGoldMaterial },
        { x: 0.18, y: -0.20, z: 0.12, material: amberGoldMaterial },
        { x: -0.14, y: -0.24, z: 0.16, material: goldenBronzeMaterial },
        { x: 0.14, y: -0.24, z: 0.16, material: goldenBronzeMaterial },
        
        // Side expansion indicators
        { x: -0.38, y: 0.10, z: 0.12, material: amberGoldMaterial },
        { x: 0.38, y: 0.10, z: 0.12, material: amberGoldMaterial },
        { x: -0.42, y: 0.06, z: 0.16, material: goldenBronzeMaterial },
        { x: 0.42, y: 0.06, z: 0.16, material: goldenBronzeMaterial }
    ];

    // Earth connection (giant's connection to the ground)
    const earthConnectionVoxels = [
        // Ground tremor effects
        { x: -0.12, y: -0.28, z: 0.0, material: earthBrownMaterial },
        { x: 0.12, y: -0.28, z: 0.0, material: earthBrownMaterial },
        { x: -0.16, y: -0.32, z: 0.04, material: mountainGrayMaterial },
        { x: 0.16, y: -0.32, z: 0.04, material: mountainGrayMaterial },
        { x: -0.20, y: -0.36, z: 0.08, material: ironSteelMaterial },
        { x: 0.20, y: -0.36, z: 0.08, material: ironSteelMaterial },
        
        // Rock formations
        { x: -0.24, y: -0.28, z: 0.08, material: graniteSilverMaterial },
        { x: 0.24, y: -0.28, z: 0.08, material: graniteSilverMaterial },
        { x: -0.28, y: -0.24, z: 0.12, material: mountainGrayMaterial },
        { x: 0.28, y: -0.24, z: 0.12, material: mountainGrayMaterial },
        { x: -0.32, y: -0.20, z: 0.16, material: earthBrownMaterial },
        { x: 0.32, y: -0.20, z: 0.16, material: earthBrownMaterial },
        
        // Earth energy rising
        { x: -0.08, y: -0.32, z: -0.04, material: goldenBronzeMaterial },
        { x: 0.08, y: -0.32, z: -0.04, material: goldenBronzeMaterial },
        { x: -0.04, y: -0.36, z: -0.08, material: amberGoldMaterial },
        { x: 0.04, y: -0.36, z: -0.08, material: amberGoldMaterial },
        { x: 0.0, y: -0.40, z: -0.12, material: goldenBronzeMaterial }
    ];

    // Create giant figure group
    const giantFigureGroup = new THREE.Group();
    giantFigureGroup.name = 'giantFigure';

    // Add giant figure voxels
    giantFigureVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        giantFigureGroup.add(mesh);
    });

    // Create strength aura group
    const strengthAuraGroup = new THREE.Group();
    strengthAuraGroup.name = 'strengthAura';

    // Add strength aura voxels
    strengthAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.strengthPhase = index * 0.1; // Stagger animation
        strengthAuraGroup.add(mesh);
    });

    // Create size increase group
    const sizeIncreaseGroup = new THREE.Group();
    sizeIncreaseGroup.name = 'sizeIncrease';

    // Add size increase voxels
    sizeIncreaseVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.growthPhase = index * 0.08; // Stagger animation
        sizeIncreaseGroup.add(mesh);
    });

    // Create earth connection group
    const earthConnectionGroup = new THREE.Group();
    earthConnectionGroup.name = 'earthConnection';

    // Add earth connection voxels
    earthConnectionVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.earthPhase = index * 0.12; // Stagger animation
        earthConnectionGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(giantFigureGroup);
    cardGroup.add(strengthAuraGroup);
    cardGroup.add(sizeIncreaseGroup);
    cardGroup.add(earthConnectionGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        giantPulse: 0,
        strengthFlow: 0,
        growthExpansion: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update giant's might card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateGiantsMightCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.giantPulse += deltaTime * 1.8; // Giant pulse speed
    cardGroup.userData.strengthFlow += deltaTime * 3.0; // Strength flow speed
    cardGroup.userData.growthExpansion += deltaTime * 2.2; // Growth expansion speed

    const time = cardGroup.userData.animationTime;
    const giantPulse = cardGroup.userData.giantPulse;
    const strengthFlow = cardGroup.userData.strengthFlow;
    const growthExpansion = cardGroup.userData.growthExpansion;

    // Animate giant figure (powerful breathing/growing)
    const giantFigureGroup = cardGroup.getObjectByName('giantFigure');
    if (giantFigureGroup) {
        // Giant breathing motion (slow and powerful)
        const giantBreath = Math.sin(giantPulse * 1.0) * 0.001;
        giantFigureGroup.position.y = giantBreath;
        
        // Giant power pulsing
        const giantPower = 1.0 + Math.sin(giantPulse * 1.5) * 0.3;
        giantFigureGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * giantPower;
            }
        });
    }

    // Animate strength aura (power emanation)
    const strengthAuraGroup = cardGroup.getObjectByName('strengthAura');
    if (strengthAuraGroup) {
        strengthAuraGroup.rotation.y = time * 0.6; // Slow powerful rotation
        
        strengthAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.strengthPhase !== undefined) {
                const strengthTime = strengthFlow + mesh.userData.strengthPhase;
                
                // Strength energy pulsing outward
                const strengthPulse = Math.sin(strengthTime * 2.5) * 0.003;
                const strengthWave = Math.cos(strengthTime * 3.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + strengthPulse;
                mesh.position.y = mesh.userData.originalPosition.y + strengthWave;
                
                // Strength intensity fluctuation
                const strengthIntensity = 1.0 + Math.sin(strengthTime * 4.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * strengthIntensity;
                }
                
                // Strength scale variation
                const strengthScale = 0.9 + Math.sin(strengthTime * 3.5) * 0.2;
                mesh.scale.setScalar(strengthScale);
            }
        });
    }

    // Animate size increase (growth expansion)
    const sizeIncreaseGroup = cardGroup.getObjectByName('sizeIncrease');
    if (sizeIncreaseGroup) {
        sizeIncreaseGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.growthPhase !== undefined) {
                const growthTime = growthExpansion + mesh.userData.growthPhase;
                
                // Growth expansion motion (outward expansion)
                const growthExpand = Math.sin(growthTime * 2.8) * 0.004;
                const growthRise = Math.cos(growthTime * 3.2) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + growthExpand;
                mesh.position.y = mesh.userData.originalPosition.y + growthRise;
                
                // Growth energy intensity
                const growthIntensity = 1.0 + Math.sin(growthTime * 5.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * growthIntensity;
                }
                
                // Growth scale pulsing (expansion effect)
                const growthScale = 1.0 + Math.sin(growthTime * 4.5) * 0.3;
                mesh.scale.setScalar(growthScale);
            }
        });
    }

    // Animate earth connection (ground tremor)
    const earthConnectionGroup = cardGroup.getObjectByName('earthConnection');
    if (earthConnectionGroup) {
        earthConnectionGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.earthPhase !== undefined) {
                const earthTime = giantPulse + mesh.userData.earthPhase;
                
                // Earth tremor motion
                const earthShake = Math.sin(earthTime * 4.0) * 0.002;
                const earthRumble = Math.cos(earthTime * 3.5) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + earthShake;
                mesh.position.z = mesh.userData.originalPosition.z + earthRumble;
                
                // Earth energy glow
                const earthGlow = 1.0 + Math.sin(earthTime * 3.8) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * earthGlow;
                }
                
                // Earth stability (slight scale changes)
                const earthStability = 0.95 + Math.sin(earthTime * 2.5) * 0.1;
                mesh.scale.setScalar(earthStability);
            }
        });
    }

    // Overall giant's might pulsing (massive power)
    const titanPulse = 1 + Math.sin(time * 1.8) * 0.07;
    cardGroup.scale.setScalar(0.8 * titanPulse);
}

// Export the giant's might card data for the loot system
export const GIANTS_MIGHT_CARD_DATA = {
    name: "Giant's Might",
    description: 'Transform into a colossal titan, gaining massive size increase, overwhelming physical strength, and earth-shaking power that devastates enemies through sheer force.',
    category: 'card',
    rarity: 'epic',
    effect: 'giants_might',
    effectValue: 50, // Duration in seconds
    createFunction: createGiantsMightCard,
    updateFunction: updateGiantsMightCardAnimation,
    voxelModel: 'giants_might_card',
    glow: {
        color: 0xCD7F32,
        intensity: 1.5
    }
};

export default createGiantsMightCard;