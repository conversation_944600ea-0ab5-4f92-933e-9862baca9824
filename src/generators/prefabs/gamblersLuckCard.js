import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Gambler's Luck Card Prefab
 * Risk/reward utility card with gambling mechanics
 */

// Gambler's Luck specific colors
const GAMBLERS_LUCK_COLORS = {
    LUCK_GREEN: 0x00FF00,            // Lucky green
    RISK_RED: 0xFF0000,              // Risk red
    FORTUNE_GOLD: 0xFFD700,          // Fortune gold
    CHANCE_BLUE: 0x4169E1,           // Chance blue
    JACKPOT_PURPLE: 0x8A2BE2,        // Jackpot purple
    DICE_WHITE: 0xFFFFFF,            // Dice white
    GAMBLE_ORANGE: 0xFF8C00,         // Gamble orange
    ODDS_SILVER: 0xC0C0C0            // Odds silver
};

/**
 * Create a gambler's luck card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The gambler's luck card 3D model
 */
export function createGamblersLuckCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'GamblersLuckCard';

    // Gambler's Luck materials
    const luckGreenMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.LUCK_GREEN,
        emissive: GAMBLERS_LUCK_COLORS.LUCK_GREEN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const riskRedMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.RISK_RED,
        emissive: GAMBLERS_LUCK_COLORS.RISK_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const fortuneGoldMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.FORTUNE_GOLD,
        emissive: GAMBLERS_LUCK_COLORS.FORTUNE_GOLD,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const chanceBlueMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.CHANCE_BLUE,
        emissive: GAMBLERS_LUCK_COLORS.CHANCE_BLUE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    const jackpotPurpleMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.JACKPOT_PURPLE,
        emissive: GAMBLERS_LUCK_COLORS.JACKPOT_PURPLE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const diceWhiteMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.DICE_WHITE,
        emissive: GAMBLERS_LUCK_COLORS.DICE_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const gambleOrangeMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.GAMBLE_ORANGE,
        emissive: GAMBLERS_LUCK_COLORS.GAMBLE_ORANGE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const oddsSilverMaterial = new THREE.MeshLambertMaterial({
        color: GAMBLERS_LUCK_COLORS.ODDS_SILVER,
        emissive: GAMBLERS_LUCK_COLORS.ODDS_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Lucky Dice Core (central gambling element)
    const luckyDiceVoxels = [
        // First die
        { x: -0.04, y: 0.0, z: 0.0, material: diceWhiteMaterial }, // Die 1 center
        { x: -0.05, y: 0.01, z: 0.01, material: riskRedMaterial }, // Die 1 dot
        { x: -0.03, y: -0.01, z: -0.01, material: riskRedMaterial }, // Die 1 dot
        { x: -0.05, y: -0.01, z: 0.01, material: riskRedMaterial }, // Die 1 dot
        { x: -0.03, y: 0.01, z: -0.01, material: riskRedMaterial }, // Die 1 dot
        
        // Second die
        { x: 0.04, y: 0.0, z: 0.0, material: diceWhiteMaterial }, // Die 2 center
        { x: 0.05, y: 0.01, z: 0.01, material: luckGreenMaterial }, // Die 2 dot
        { x: 0.03, y: -0.01, z: -0.01, material: luckGreenMaterial }, // Die 2 dot
        { x: 0.05, y: -0.01, z: 0.01, material: luckGreenMaterial }, // Die 2 dot
        { x: 0.03, y: 0.01, z: -0.01, material: luckGreenMaterial }, // Die 2 dot
        { x: 0.04, y: 0.0, z: 0.02, material: luckGreenMaterial }, // Die 2 dot
        { x: 0.04, y: 0.0, z: -0.02, material: luckGreenMaterial }, // Die 2 dot
        
        // Fortune core
        { x: 0.0, y: 0.0, z: 0.0, material: fortuneGoldMaterial }, // Central fortune
        { x: 0.0, y: 0.02, z: 0.0, material: jackpotPurpleMaterial }, // Fortune top
        { x: 0.0, y: -0.02, z: 0.0, material: jackpotPurpleMaterial }, // Fortune bottom
        { x: 0.0, y: 0.0, z: 0.02, material: chanceBlueMaterial }, // Fortune front
        { x: 0.0, y: 0.0, z: -0.02, material: chanceBlueMaterial }, // Fortune back
        
        // Dice edges
        { x: -0.06, y: 0.02, z: 0.0, material: oddsSilverMaterial }, // Die 1 edge
        { x: -0.02, y: 0.02, z: 0.0, material: oddsSilverMaterial }, // Die 1 edge
        { x: 0.06, y: 0.02, z: 0.0, material: oddsSilverMaterial }, // Die 2 edge
        { x: 0.02, y: 0.02, z: 0.0, material: oddsSilverMaterial }, // Die 2 edge
        { x: -0.06, y: -0.02, z: 0.0, material: oddsSilverMaterial }, // Die 1 edge
        { x: -0.02, y: -0.02, z: 0.0, material: oddsSilverMaterial }, // Die 1 edge
        { x: 0.06, y: -0.02, z: 0.0, material: oddsSilverMaterial }, // Die 2 edge
        { x: 0.02, y: -0.02, z: 0.0, material: oddsSilverMaterial } // Die 2 edge
    ];

    // Fortune Coins (gambling rewards)
    const fortuneCoinsVoxels = [
        // Inner coin ring
        { x: -0.10, y: 0.0, z: 0.0, material: fortuneGoldMaterial }, // Left coin
        { x: 0.10, y: 0.0, z: 0.0, material: fortuneGoldMaterial }, // Right coin
        { x: 0.0, y: 0.10, z: 0.0, material: fortuneGoldMaterial }, // Top coin
        { x: 0.0, y: -0.10, z: 0.0, material: fortuneGoldMaterial }, // Bottom coin
        { x: -0.07, y: 0.07, z: 0.0, material: gambleOrangeMaterial }, // Top-left coin
        { x: 0.07, y: 0.07, z: 0.0, material: gambleOrangeMaterial }, // Top-right coin
        { x: -0.07, y: -0.07, z: 0.0, material: gambleOrangeMaterial }, // Bottom-left coin
        { x: 0.07, y: -0.07, z: 0.0, material: gambleOrangeMaterial }, // Bottom-right coin
        
        // Coin details
        { x: -0.09, y: 0.01, z: 0.01, material: jackpotPurpleMaterial }, // Coin marking
        { x: 0.09, y: 0.01, z: 0.01, material: jackpotPurpleMaterial }, // Coin marking
        { x: 0.01, y: 0.09, z: 0.01, material: jackpotPurpleMaterial }, // Coin marking
        { x: 0.01, y: -0.09, z: 0.01, material: jackpotPurpleMaterial }, // Coin marking
        
        // Middle coin ring
        { x: -0.14, y: 0.0, z: 0.0, material: gambleOrangeMaterial }, // Far left coin
        { x: 0.14, y: 0.0, z: 0.0, material: gambleOrangeMaterial }, // Far right coin
        { x: 0.0, y: 0.14, z: 0.0, material: gambleOrangeMaterial }, // Far top coin
        { x: 0.0, y: -0.14, z: 0.0, material: gambleOrangeMaterial }, // Far bottom coin
        { x: -0.10, y: 0.10, z: 0.0, material: fortuneGoldMaterial }, // Far top-left coin
        { x: 0.10, y: 0.10, z: 0.0, material: fortuneGoldMaterial }, // Far top-right coin
        { x: -0.10, y: -0.10, z: 0.0, material: fortuneGoldMaterial }, // Far bottom-left coin
        { x: 0.10, y: -0.10, z: 0.0, material: fortuneGoldMaterial }, // Far bottom-right coin
        
        // Outer coin ring
        { x: -0.18, y: 0.0, z: 0.0, material: oddsSilverMaterial }, // Outer left coin
        { x: 0.18, y: 0.0, z: 0.0, material: oddsSilverMaterial }, // Outer right coin
        { x: 0.0, y: 0.18, z: 0.0, material: oddsSilverMaterial }, // Outer top coin
        { x: 0.0, y: -0.18, z: 0.0, material: oddsSilverMaterial }, // Outer bottom coin
        { x: -0.13, y: 0.13, z: 0.0, material: chanceBlueMaterial }, // Outer top-left coin
        { x: 0.13, y: 0.13, z: 0.0, material: chanceBlueMaterial }, // Outer top-right coin
        { x: -0.13, y: -0.13, z: 0.0, material: chanceBlueMaterial }, // Outer bottom-left coin
        { x: 0.13, y: -0.13, z: 0.0, material: chanceBlueMaterial } // Outer bottom-right coin
    ];

    // Luck Streaks (chance energy streams)
    const luckStreaksVoxels = [
        // Luck streak lines
        { x: -0.12, y: 0.02, z: 0.06, material: luckGreenMaterial },
        { x: 0.12, y: -0.02, z: 0.06, material: riskRedMaterial },
        { x: 0.02, y: 0.12, z: 0.06, material: luckGreenMaterial },
        { x: -0.02, y: -0.12, z: 0.06, material: riskRedMaterial },
        { x: -0.085, y: 0.085, z: 0.06, material: chanceBlueMaterial },
        { x: 0.085, y: 0.085, z: 0.06, material: chanceBlueMaterial },
        { x: -0.085, y: -0.085, z: 0.06, material: chanceBlueMaterial },
        { x: 0.085, y: -0.085, z: 0.06, material: chanceBlueMaterial },
        
        // Risk streak lines
        { x: -0.16, y: 0.04, z: 0.04, material: riskRedMaterial },
        { x: 0.16, y: -0.04, z: 0.04, material: luckGreenMaterial },
        { x: 0.04, y: 0.16, z: 0.04, material: riskRedMaterial },
        { x: -0.04, y: -0.16, z: 0.04, material: luckGreenMaterial },
        { x: -0.113, y: 0.113, z: 0.04, material: fortuneGoldMaterial },
        { x: 0.113, y: 0.113, z: 0.04, material: fortuneGoldMaterial },
        { x: -0.113, y: -0.113, z: 0.04, material: fortuneGoldMaterial },
        { x: 0.113, y: -0.113, z: 0.04, material: fortuneGoldMaterial },
        
        // Fortune streak lines
        { x: -0.20, y: 0.06, z: 0.02, material: fortuneGoldMaterial },
        { x: 0.20, y: -0.06, z: 0.02, material: fortuneGoldMaterial },
        { x: 0.06, y: 0.20, z: 0.02, material: fortuneGoldMaterial },
        { x: -0.06, y: -0.20, z: 0.02, material: fortuneGoldMaterial },
        { x: -0.141, y: 0.141, z: 0.02, material: jackpotPurpleMaterial },
        { x: 0.141, y: 0.141, z: 0.02, material: jackpotPurpleMaterial },
        { x: -0.141, y: -0.141, z: 0.02, material: jackpotPurpleMaterial },
        { x: 0.141, y: -0.141, z: 0.02, material: jackpotPurpleMaterial },
        
        // Gamble streak lines
        { x: -0.24, y: 0.08, z: 0.0, material: gambleOrangeMaterial },
        { x: 0.24, y: -0.08, z: 0.0, material: gambleOrangeMaterial },
        { x: 0.08, y: 0.24, z: 0.0, material: gambleOrangeMaterial },
        { x: -0.08, y: -0.24, z: 0.0, material: gambleOrangeMaterial },
        { x: -0.17, y: 0.17, z: 0.0, material: oddsSilverMaterial },
        { x: 0.17, y: 0.17, z: 0.0, material: oddsSilverMaterial },
        { x: -0.17, y: -0.17, z: 0.0, material: oddsSilverMaterial },
        { x: 0.17, y: -0.17, z: 0.0, material: oddsSilverMaterial }
    ];

    // Chance Aura (probability field)
    const chanceAuraVoxels = [
        // Inner chance layer
        { x: -0.11, y: 0.03, z: 0.08, material: chanceBlueMaterial },
        { x: 0.11, y: -0.03, z: 0.08, material: chanceBlueMaterial },
        { x: 0.03, y: 0.11, z: 0.08, material: chanceBlueMaterial },
        { x: -0.03, y: -0.11, z: 0.08, material: chanceBlueMaterial },
        { x: -0.078, y: 0.078, z: 0.08, material: luckGreenMaterial },
        { x: 0.078, y: 0.078, z: 0.08, material: luckGreenMaterial },
        { x: -0.078, y: -0.078, z: 0.08, material: luckGreenMaterial },
        { x: 0.078, y: -0.078, z: 0.08, material: luckGreenMaterial },
        
        // Middle chance layer
        { x: -0.15, y: 0.05, z: 0.06, material: jackpotPurpleMaterial },
        { x: 0.15, y: -0.05, z: 0.06, material: jackpotPurpleMaterial },
        { x: 0.05, y: 0.15, z: 0.06, material: jackpotPurpleMaterial },
        { x: -0.05, y: -0.15, z: 0.06, material: jackpotPurpleMaterial },
        { x: -0.106, y: 0.106, z: 0.06, material: riskRedMaterial },
        { x: 0.106, y: 0.106, z: 0.06, material: riskRedMaterial },
        { x: -0.106, y: -0.106, z: 0.06, material: riskRedMaterial },
        { x: 0.106, y: -0.106, z: 0.06, material: riskRedMaterial },
        
        // Outer chance layer
        { x: -0.19, y: 0.07, z: 0.04, material: fortuneGoldMaterial },
        { x: 0.19, y: -0.07, z: 0.04, material: fortuneGoldMaterial },
        { x: 0.07, y: 0.19, z: 0.04, material: fortuneGoldMaterial },
        { x: -0.07, y: -0.19, z: 0.04, material: fortuneGoldMaterial },
        { x: -0.134, y: 0.134, z: 0.04, material: gambleOrangeMaterial },
        { x: 0.134, y: 0.134, z: 0.04, material: gambleOrangeMaterial },
        { x: -0.134, y: -0.134, z: 0.04, material: gambleOrangeMaterial },
        { x: 0.134, y: -0.134, z: 0.04, material: gambleOrangeMaterial },
        
        // Far chance layer
        { x: -0.23, y: 0.09, z: 0.02, material: oddsSilverMaterial },
        { x: 0.23, y: -0.09, z: 0.02, material: oddsSilverMaterial },
        { x: 0.09, y: 0.23, z: 0.02, material: oddsSilverMaterial },
        { x: -0.09, y: -0.23, z: 0.02, material: oddsSilverMaterial },
        { x: -0.163, y: 0.163, z: 0.02, material: diceWhiteMaterial },
        { x: 0.163, y: 0.163, z: 0.02, material: diceWhiteMaterial },
        { x: -0.163, y: -0.163, z: 0.02, material: diceWhiteMaterial },
        { x: 0.163, y: -0.163, z: 0.02, material: diceWhiteMaterial }
    ];

    // Create lucky dice group
    const luckyDiceGroup = new THREE.Group();
    luckyDiceGroup.name = 'luckyDiceGroup';

    // Add lucky dice voxels
    luckyDiceVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.dicePhase = index * 0.07; // Stagger animation
        luckyDiceGroup.add(mesh);
    });

    // Create fortune coins group
    const fortuneCoinsGroup = new THREE.Group();
    fortuneCoinsGroup.name = 'fortuneCoinsGroup';

    // Add fortune coins voxels
    fortuneCoinsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.coinPhase = index * 0.05; // Stagger animation
        fortuneCoinsGroup.add(mesh);
    });

    // Create luck streaks group
    const luckStreaksGroup = new THREE.Group();
    luckStreaksGroup.name = 'luckStreaksGroup';

    // Add luck streaks voxels
    luckStreaksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.streakPhase = index * 0.06; // Stagger animation
        luckStreaksGroup.add(mesh);
    });

    // Create chance aura group
    const chanceAuraGroup = new THREE.Group();
    chanceAuraGroup.name = 'chanceAuraGroup';

    // Add chance aura voxels
    chanceAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.chancePhase = index * 0.08; // Stagger animation
        chanceAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(luckyDiceGroup);
    cardGroup.add(fortuneCoinsGroup);
    cardGroup.add(luckStreaksGroup);
    cardGroup.add(chanceAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        diceRoll: 0,
        fortuneSpin: 0,
        luckFlux: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update gambler's luck card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateGamblersLuckCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.diceRoll += deltaTime * 2.5; // Dice rolling speed
    cardGroup.userData.fortuneSpin += deltaTime * 3.0; // Fortune spinning speed
    cardGroup.userData.luckFlux += deltaTime * 4.0; // Luck flux speed

    const time = cardGroup.userData.animationTime;
    const diceRoll = cardGroup.userData.diceRoll;
    const fortuneSpin = cardGroup.userData.fortuneSpin;
    const luckFlux = cardGroup.userData.luckFlux;

    // Animate lucky dice (gambling tools)
    const luckyDiceGroup = cardGroup.getObjectByName('luckyDiceGroup');
    if (luckyDiceGroup) {
        // Dice rolling motion
        const diceRotation = Math.sin(diceRoll * 2.0) * 0.1;
        luckyDiceGroup.rotation.x = diceRotation;
        luckyDiceGroup.rotation.y = diceRoll * 0.3;
        luckyDiceGroup.rotation.z = Math.cos(diceRoll * 1.8) * 0.08;
        
        // Individual dice element animation
        luckyDiceGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.dicePhase !== undefined) {
                const diceTime = diceRoll + mesh.userData.dicePhase;
                
                // Dice tumbling motion
                const diceTumble = Math.sin(diceTime * 5.0) * 0.002;
                const diceShake = Math.cos(diceTime * 6.0) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + diceTumble;
                mesh.position.y = mesh.userData.originalPosition.y + diceShake;
                
                // Dice intensity (luck energy)
                const diceIntensity = 1.0 + Math.sin(diceTime * 8.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * diceIntensity;
                }
                
                // Dice scale (gambling excitement)
                const diceScale = 0.9 + Math.sin(diceTime * 4.0) * 0.2;
                mesh.scale.setScalar(diceScale);
            }
        });
    }

    // Animate fortune coins (gambling rewards)
    const fortuneCoinsGroup = cardGroup.getObjectByName('fortuneCoinsGroup');
    if (fortuneCoinsGroup) {
        // Coin spinning motion
        const coinSpin = Math.sin(fortuneSpin * 1.5) * 0.06;
        fortuneCoinsGroup.rotation.z = coinSpin;
        fortuneCoinsGroup.rotation.y = fortuneSpin * 0.4;
        
        fortuneCoinsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.coinPhase !== undefined) {
                const coinTime = fortuneSpin + mesh.userData.coinPhase;
                
                // Coin spinning motion
                const coinFloat = Math.sin(coinTime * 3.0) * 0.005;
                const coinGlint = Math.cos(coinTime * 4.0) * 0.004;
                
                mesh.position.x = mesh.userData.originalPosition.x + coinGlint;
                mesh.position.y = mesh.userData.originalPosition.y + coinFloat;
                
                // Coin golden glow
                const coinGlow = 1.0 + Math.sin(coinTime * 7.0) * 0.9;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * coinGlow;
                }
                
                // Coin opacity variation
                const coinOpacity = 0.8 + Math.sin(coinTime * 6.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * coinOpacity;
                }
            }
        });
    }

    // Animate luck streaks (chance energy streams)
    const luckStreaksGroup = cardGroup.getObjectByName('luckStreaksGroup');
    if (luckStreaksGroup) {
        luckStreaksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.streakPhase !== undefined) {
                const streakTime = luckFlux + mesh.userData.streakPhase;
                
                // Luck streak flowing motion
                const streakFlow = Math.sin(streakTime * 6.0) * 0.007;
                const streakPulse = Math.cos(streakTime * 5.0) * 0.006;
                
                mesh.position.x = mesh.userData.originalPosition.x + streakFlow;
                mesh.position.y = mesh.userData.originalPosition.y + streakPulse;
                
                // Streak intensity
                const streakIntensity = 1.0 + Math.sin(streakTime * 9.0) * 1.0;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * streakIntensity;
                }
                
                // Streak scale variation (luck flow)
                const streakScale = 0.6 + Math.sin(streakTime * 7.0) * 0.6;
                mesh.scale.setScalar(streakScale);
                
                // Streak opacity (chance manifestation)
                const streakOpacity = 0.6 + Math.sin(streakTime * 8.0) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * streakOpacity;
                }
            }
        });
    }

    // Animate chance aura (probability field)
    const chanceAuraGroup = cardGroup.getObjectByName('chanceAuraGroup');
    if (chanceAuraGroup) {
        chanceAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.chancePhase !== undefined) {
                const chanceTime = luckFlux + mesh.userData.chancePhase;
                
                // Chance aura fluctuation
                const chanceFlux = Math.sin(chanceTime * 3.5) * 0.009;
                const chanceShift = Math.cos(chanceTime * 4.0) * 0.007;
                
                mesh.position.x = mesh.userData.originalPosition.x + chanceFlux;
                mesh.position.y = mesh.userData.originalPosition.y + chanceShift;
                
                // Chance field intensity
                const chanceIntensity = 1.0 + Math.sin(chanceTime * 6.0) * 0.7;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * chanceIntensity;
                }
                
                // Chance scale variation (probability waves)
                const chanceScale = 0.8 + Math.sin(chanceTime * 5.0) * 0.3;
                mesh.scale.setScalar(chanceScale);
                
                // Chance opacity (probability flux)
                const chanceOpacity = 0.7 + Math.sin(chanceTime * 7.0) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * chanceOpacity;
                }
            }
        });
    }

    // Overall gambler's luck presence (epic risk/reward magic)
    const gamblerPulse = 1 + Math.sin(time * 3.0) * 0.12;
    const luckShift = Math.cos(time * 4.2) * 0.0025;
    cardGroup.scale.setScalar(0.8 * gamblerPulse);
    cardGroup.position.x += luckShift;
    cardGroup.position.z += luckShift * 1.1;
}

// Export the gambler's luck card data for the loot system
export const GAMBLERS_LUCK_CARD_DATA = {
    name: "Gambler's Luck",
    description: 'Roll the dice for risk/reward effects. Can grant powerful rewards or dangerous penalties based on chance.',
    category: 'card',
    rarity: 'epic',
    effect: 'gamblers_luck',
    effectValue: 1, // Number of gambles to make
    createFunction: createGamblersLuckCard,
    updateFunction: updateGamblersLuckCardAnimation,
    voxelModel: 'gamblers_luck_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.6
    }
};

export default createGamblersLuckCard;