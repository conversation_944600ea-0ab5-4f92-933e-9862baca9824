import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Dust Devil Card Prefab
 * Creates a chaotic whirlwind of dust and debris
 */

// Dust devil specific colors
const DUST_COLORS = {
    DUST_BROWN: 0x8B4513,        // Primary dust color
    SAND_TAN: 0xDEB887,          // Light sand particles
    DIRT_BROWN: 0x654321,        // Dark dirt particles
    DEBRIS_GRAY: 0x696969,       // Stone debris
    WIND_WHITE: 0xF5F5DC,        // Wind motion lines
    EARTH_BROWN: 0x8B4513,       // Earth particles
    GRIT_GRAY: 0xA0A0A0          // Gritty particles
};

/**
 * Create a dust devil card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The dust devil card 3D model
 */
export function createDustDevilCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'DustDevilCard';

    // Materials
    const dustBrownMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.DUST_BROWN,
        emissive: DUST_COLORS.DUST_BROWN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const sandTanMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.SAND_TAN,
        emissive: DUST_COLORS.SAND_TAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const dirtBrownMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.DIRT_BROWN,
        emissive: DUST_COLORS.DIRT_BROWN,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const debrisGrayMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.DEBRIS_GRAY,
        emissive: DUST_COLORS.DEBRIS_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    const windWhiteMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.WIND_WHITE,
        emissive: DUST_COLORS.WIND_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.5
    });

    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.EARTH_BROWN,
        emissive: DUST_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const gritGrayMaterial = new THREE.MeshLambertMaterial({
        color: DUST_COLORS.GRIT_GRAY,
        emissive: DUST_COLORS.GRIT_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create dust devil spiral (small tornado-like structure)
    const spiralVoxels = [
        // Core spiral (bottom to top)
        { x: 0, y: -4, z: 0, material: dustBrownMaterial },
        { x: 1, y: -3, z: 0, material: sandTanMaterial },
        { x: 0, y: -2, z: 1, material: dirtBrownMaterial },
        { x: -1, y: -1, z: 0, material: dustBrownMaterial },
        { x: 0, y: 0, z: -1, material: sandTanMaterial },
        { x: 1, y: 1, z: 0, material: dustBrownMaterial },
        { x: 0, y: 2, z: 1, material: dirtBrownMaterial },
        { x: -1, y: 3, z: 0, material: sandTanMaterial },
        { x: 0, y: 4, z: 0, material: dustBrownMaterial },

        // Wider spiral base
        { x: 2, y: -4, z: 0, material: earthBrownMaterial },
        { x: 0, y: -4, z: 2, material: earthBrownMaterial },
        { x: -2, y: -4, z: 0, material: earthBrownMaterial },
        { x: 0, y: -4, z: -2, material: earthBrownMaterial },

        // Mid-level spiral expansion
        { x: 2, y: -1, z: 1, material: gritGrayMaterial },
        { x: 1, y: -1, z: 2, material: gritGrayMaterial },
        { x: -1, y: -1, z: 2, material: gritGrayMaterial },
        { x: -2, y: -1, z: 1, material: gritGrayMaterial },
        { x: -2, y: -1, z: -1, material: gritGrayMaterial },
        { x: -1, y: -1, z: -2, material: gritGrayMaterial },
        { x: 1, y: -1, z: -2, material: gritGrayMaterial },
        { x: 2, y: -1, z: -1, material: gritGrayMaterial }
    ];

    // Create scattered debris particles
    const debrisVoxels = [
        // Flying debris around the spiral
        { x: 3, y: 0, z: 0, material: debrisGrayMaterial },
        { x: 2, y: 2, z: 2, material: debrisGrayMaterial },
        { x: -2, y: 1, z: 2, material: debrisGrayMaterial },
        { x: -3, y: 0, z: 0, material: debrisGrayMaterial },
        { x: -2, y: -2, z: -2, material: debrisGrayMaterial },
        { x: 2, y: -1, z: -2, material: debrisGrayMaterial },

        // Additional chaotic debris
        { x: 4, y: 1, z: 1, material: dirtBrownMaterial },
        { x: 3, y: 3, z: -1, material: dirtBrownMaterial },
        { x: -3, y: 2, z: 1, material: dirtBrownMaterial },
        { x: -4, y: -1, z: -1, material: dirtBrownMaterial },
        { x: 1, y: 4, z: 2, material: dirtBrownMaterial },
        { x: -1, y: -3, z: 3, material: dirtBrownMaterial }
    ];

    // Create swirling dust clouds
    const dustCloudVoxels = [
        // Upper dust cloud
        { x: 1, y: 5, z: 0, material: sandTanMaterial },
        { x: 0, y: 5, z: 1, material: sandTanMaterial },
        { x: -1, y: 5, z: 0, material: sandTanMaterial },
        { x: 0, y: 5, z: -1, material: sandTanMaterial },

        // Lower dust cloud
        { x: 2, y: -5, z: 0, material: earthBrownMaterial },
        { x: 0, y: -5, z: 2, material: earthBrownMaterial },
        { x: -2, y: -5, z: 0, material: earthBrownMaterial },
        { x: 0, y: -5, z: -2, material: earthBrownMaterial },

        // Side dust puffs
        { x: 3, y: 0, z: 3, material: dustBrownMaterial },
        { x: -3, y: 0, z: 3, material: dustBrownMaterial },
        { x: 3, y: 0, z: -3, material: dustBrownMaterial },
        { x: -3, y: 0, z: -3, material: dustBrownMaterial }
    ];

    // Create wind motion lines
    const windLineVoxels = [
        // Motion indicators showing chaotic movement
        { x: 4, y: 0, z: 0, material: windWhiteMaterial },
        { x: 3, y: 1, z: 0, material: windWhiteMaterial },
        { x: 3, y: -1, z: 0, material: windWhiteMaterial },

        { x: -4, y: 0, z: 0, material: windWhiteMaterial },
        { x: -3, y: 1, z: 0, material: windWhiteMaterial },
        { x: -3, y: -1, z: 0, material: windWhiteMaterial },

        { x: 0, y: 0, z: 4, material: windWhiteMaterial },
        { x: 0, y: 1, z: 3, material: windWhiteMaterial },
        { x: 0, y: -1, z: 3, material: windWhiteMaterial },

        { x: 0, y: 0, z: -4, material: windWhiteMaterial },
        { x: 0, y: 1, z: -3, material: windWhiteMaterial },
        { x: 0, y: -1, z: -3, material: windWhiteMaterial }
    ];

    // Create all voxels
    [...spiralVoxels, ...debrisVoxels, ...dustCloudVoxels, ...windLineVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.35, // Compact spacing
            voxel.y * VOXEL_SIZE * 0.35,
            voxel.z * VOXEL_SIZE * 0.35
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (spiralVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'spiral';
        } else if (debrisVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'debris';
        } else if (dustCloudVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'dust';
        } else {
            mesh.userData.voxelType = 'wind';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        spiralRotation: 0,
        debrisFloat: 0,
        dustSwirl: 0,
        chaosOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update dust devil card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateDustDevilCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.spiralRotation += deltaTime * 6.0; // Fast spiral rotation
    cardGroup.userData.debrisFloat += deltaTime * 4.0;
    cardGroup.userData.dustSwirl += deltaTime * 3.5;

    const time = cardGroup.userData.animationTime;
    const spiralRotation = cardGroup.userData.spiralRotation;
    const debrisFloat = cardGroup.userData.debrisFloat;
    const dustSwirl = cardGroup.userData.dustSwirl;
    const chaosOffset = cardGroup.userData.chaosOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'spiral':
                    // Spiral rotates and pulses with chaotic intensity
                    const spiralIntensity = 0.6 + Math.sin(spiralRotation * 2.0 + child.position.y * 0.5) * 0.6;
                    const spiralRotationAngle = spiralRotation + child.position.y * 0.3;
                    const spiralRadius = Math.sqrt(child.position.x * child.position.x + child.position.z * child.position.z);
                    
                    child.material.emissiveIntensity = baseEmissive * spiralIntensity;
                    child.material.opacity = baseOpacity * (0.7 + spiralIntensity * 0.3);
                    
                    // Rotate around Y axis
                    if (spiralRadius > 0) {
                        const newAngle = Math.atan2(child.position.z, child.position.x) + deltaTime * 6.0;
                        child.position.x = Math.cos(newAngle) * spiralRadius;
                        child.position.z = Math.sin(newAngle) * spiralRadius;
                    }
                    break;

                case 'debris':
                    // Debris floats chaotically
                    const debrisIntensity = 0.5 + Math.sin(debrisFloat * 3.0 + chaosOffset) * 0.7;
                    const debrisMotionX = Math.sin(debrisFloat * 2.5 + child.position.y) * 0.015;
                    const debrisMotionY = Math.cos(debrisFloat * 2.0 + child.position.x) * 0.008;
                    const debrisMotionZ = Math.sin(debrisFloat * 3.0 + child.position.z) * 0.012;
                    
                    child.material.emissiveIntensity = baseEmissive * debrisIntensity;
                    child.material.opacity = baseOpacity * debrisIntensity;
                    child.position.x += debrisMotionX;
                    child.position.y += debrisMotionY;
                    child.position.z += debrisMotionZ;
                    break;

                case 'dust':
                    // Dust swirls and expands
                    const dustIntensity = 0.4 + Math.sin(dustSwirl * 4.0 + child.position.x + child.position.z) * 0.8;
                    const dustMotion = Math.sin(dustSwirl * 3.0 + chaosOffset) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * dustIntensity;
                    child.material.opacity = baseOpacity * dustIntensity;
                    child.position.x += dustMotion;
                    child.position.z += dustMotion * 0.7;
                    break;

                case 'wind':
                    // Wind lines show rapid chaotic movement
                    const windIntensity = 0.3 + Math.sin(spiralRotation * 8.0 + child.position.x * 2 + chaosOffset) * 0.9;
                    const windMotion = Math.sin(spiralRotation * 12.0 + chaosOffset) * 0.018;
                    
                    child.material.emissiveIntensity = baseEmissive * windIntensity;
                    child.material.opacity = baseOpacity * windIntensity;
                    child.position.x += windMotion;
                    child.position.z += windMotion * 0.5;
                    break;
            }
        }
    });

    // Overall chaotic card movement
    const chaosMotion = Math.sin(time * 4.0 + chaosOffset) * 0.008;
    cardGroup.position.x += chaosMotion;
    cardGroup.position.z += chaosMotion * 0.6;
    cardGroup.rotation.y += deltaTime * 0.5; // Slight rotation
}

// Export the dust devil card data for the loot system
export const DUST_DEVIL_CARD_DATA = {
    name: 'Dust Devil',
    description: 'Summons a small but fierce whirlwind of dust and debris that spirals erratically across the battlefield, dealing earth damage to enemies in its chaotic path.',
    category: 'card',
    rarity: 'common',
    effect: 'dust_devil',
    effectValue: 45,
    createFunction: createDustDevilCard,
    updateFunction: updateDustDevilCardAnimation,
    voxelModel: 'dust_devil_card',
    glow: {
        color: 0xDEB887,
        intensity: 1.0
    }
};

export default createDustDevilCard;