import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

/**
 * Creates a voxel-based chess king object
 * Kings are distinctive with a cross-topped crown and regal bearing
 * White kings use light stone colors, black kings use dark obsidian colors
 */

// Material cache for performance
const materialCache = new Map();

function _getMaterialByHex_Cached(hexColor, materialProperties = {}) {
    if (materialCache.has(hexColor)) {
        return materialCache.get(hexColor);
    }
    
    const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(`#${hexColor}`),
        roughness: materialProperties.roughness || 0.7,
        metalness: materialProperties.metalness || 0.1,
        ...materialProperties
    });
    
    materialCache.set(hexColor, material);
    return material;
}

/**
 * Create a chess king with specified color
 * @param {Object} options - Configuration options
 * @param {string} options.color - 'white' or 'black'
 * @param {number} options.scale - Scale multiplier (default: 2.4)
 * @param {number} options.seed - Random seed for variation
 * @returns {THREE.Group} - The king 3D object
 */
export function createChessKingObject(options = {}) {
    const { 
        color = 'white', 
        scale = 2.4,
        seed = 0
    } = options;
    
    const kingGroup = new THREE.Group();
    kingGroup.userData = { 
        type: 'chess_king', 
        chessColor: color,
        chessPiece: 'king'
    };
    
    // Color schemes
    const colorSchemes = {
        white: {
            base: ['FFFFFF', 'F0F0F0', 'E8E8E8'],
            accent: ['CCCCCC', 'D0D0D0'],
            detail: ['B0B0B0', 'A0A0A0'],
            crown: ['FFD700', 'FFC000'], // Gold crown
            cross: ['FFFF00'], // Bright gold cross
            jewel: ['0066FF'] // Royal blue jewel
        },
        black: {
            base: ['1A0A0A', '000000', '2A1A1A'],
            accent: ['8B0000', '660000'],
            detail: ['AA0000', 'CC0000'],
            crown: ['FF0000', 'CC0000'], // Blood red crown
            cross: ['FF6600'], // Hellish orange cross
            jewel: ['FF0000'] // Red jewel
        }
    };
    
    const scheme = colorSchemes[color];
    // Higher detail for chess pieces - smaller voxels
    const CHESS_DETAIL_FACTOR = 0.5; // 2x more detailed voxels
    const voxelScale = VOXEL_SIZE * scale * CHESS_DETAIL_FACTOR;
    
    // Simple seeded random for consistent variation
    const rng = (() => {
        let s = seed;
        return () => {
            s = Math.sin(s) * 10000;
            return s - Math.floor(s);
        };
    })();
    
    // King shape: Tall figure with cross-topped crown
    const kingVoxels = [
        // Base layer (y=0) - 3x3 foundation
        { x: -1, y: 0, z: -1, c: scheme.base[0] },
        { x: 0, y: 0, z: -1, c: scheme.base[0] },
        { x: 1, y: 0, z: -1, c: scheme.base[0] },
        { x: -1, y: 0, z: 0, c: scheme.base[0] },
        { x: 0, y: 0, z: 0, c: scheme.base[0] },
        { x: 1, y: 0, z: 0, c: scheme.base[0] },
        { x: -1, y: 0, z: 1, c: scheme.base[0] },
        { x: 0, y: 0, z: 1, c: scheme.base[0] },
        { x: 1, y: 0, z: 1, c: scheme.base[0] },
        
        // Layer 1 (y=1) - Ornate base
        { x: -1, y: 1, z: -1, c: scheme.base[1] },
        { x: 0, y: 1, z: -1, c: scheme.accent[0] },
        { x: 1, y: 1, z: -1, c: scheme.base[1] },
        { x: -1, y: 1, z: 0, c: scheme.accent[0] },
        { x: 0, y: 1, z: 0, c: scheme.crown[0] },
        { x: 1, y: 1, z: 0, c: scheme.accent[0] },
        { x: -1, y: 1, z: 1, c: scheme.base[1] },
        { x: 0, y: 1, z: 1, c: scheme.accent[0] },
        { x: 1, y: 1, z: 1, c: scheme.base[1] },
        
        // Layer 2 (y=2) - Body begins with royal decoration
        { x: -1, y: 2, z: -1, c: scheme.accent[0] },
        { x: 0, y: 2, z: -1, c: scheme.accent[1] },
        { x: 1, y: 2, z: -1, c: scheme.accent[0] },
        { x: -1, y: 2, z: 0, c: scheme.accent[1] },
        { x: 0, y: 2, z: 0, c: scheme.jewel[0] },
        { x: 1, y: 2, z: 0, c: scheme.accent[1] },
        { x: -1, y: 2, z: 1, c: scheme.accent[0] },
        { x: 0, y: 2, z: 1, c: scheme.accent[1] },
        { x: 1, y: 2, z: 1, c: scheme.accent[0] },
        
        // Layer 3 (y=3) - Body continues
        { x: -1, y: 3, z: -1, c: scheme.accent[1] },
        { x: 0, y: 3, z: -1, c: scheme.detail[0] },
        { x: 1, y: 3, z: -1, c: scheme.accent[1] },
        { x: -1, y: 3, z: 0, c: scheme.detail[0] },
        { x: 0, y: 3, z: 0, c: scheme.crown[1] },
        { x: 1, y: 3, z: 0, c: scheme.detail[0] },
        { x: -1, y: 3, z: 1, c: scheme.accent[1] },
        { x: 0, y: 3, z: 1, c: scheme.detail[0] },
        { x: 1, y: 3, z: 1, c: scheme.accent[1] },
        
        // Layer 4 (y=4) - Upper body/shoulders
        { x: -1, y: 4, z: -1, c: scheme.detail[0] },
        { x: 0, y: 4, z: -1, c: scheme.detail[1] },
        { x: 1, y: 4, z: -1, c: scheme.detail[0] },
        { x: -1, y: 4, z: 0, c: scheme.detail[1] },
        { x: 0, y: 4, z: 0, c: scheme.crown[0] },
        { x: 1, y: 4, z: 0, c: scheme.detail[1] },
        { x: -1, y: 4, z: 1, c: scheme.detail[0] },
        { x: 0, y: 4, z: 1, c: scheme.detail[1] },
        { x: 1, y: 4, z: 1, c: scheme.detail[0] },
        
        // Layer 5 (y=5) - Crown base
        { x: -1, y: 5, z: -1, c: scheme.crown[0] },
        { x: 0, y: 5, z: -1, c: scheme.crown[1] },
        { x: 1, y: 5, z: -1, c: scheme.crown[0] },
        { x: -1, y: 5, z: 0, c: scheme.crown[1] },
        { x: 0, y: 5, z: 0, c: scheme.jewel[0] },
        { x: 1, y: 5, z: 0, c: scheme.crown[1] },
        { x: -1, y: 5, z: 1, c: scheme.crown[0] },
        { x: 0, y: 5, z: 1, c: scheme.crown[1] },
        { x: 1, y: 5, z: 1, c: scheme.crown[0] },
        
        // Layer 6 (y=6) - Crown rim
        { x: -1, y: 6, z: -1, c: scheme.crown[1] },
        { x: 0, y: 6, z: -1, c: scheme.crown[0] },
        { x: 1, y: 6, z: -1, c: scheme.crown[1] },
        { x: -1, y: 6, z: 0, c: scheme.crown[0] },
        { x: 0, y: 6, z: 0, c: scheme.crown[1] },
        { x: 1, y: 6, z: 0, c: scheme.crown[0] },
        { x: -1, y: 6, z: 1, c: scheme.crown[1] },
        { x: 0, y: 6, z: 1, c: scheme.crown[0] },
        { x: 1, y: 6, z: 1, c: scheme.crown[1] },
        
        // Layer 7 (y=7) - Crown body with cross base
        { x: 0, y: 7, z: -1, c: scheme.crown[0] },
        { x: -1, y: 7, z: 0, c: scheme.crown[0] },
        { x: 0, y: 7, z: 0, c: scheme.cross[0] },
        { x: 1, y: 7, z: 0, c: scheme.crown[0] },
        { x: 0, y: 7, z: 1, c: scheme.crown[0] },
        
        // Layer 8 (y=8) - Cross vertical beam
        { x: 0, y: 8, z: 0, c: scheme.cross[0] },
        
        // Layer 9 (y=9) - Cross horizontal beam and top
        { x: -1, y: 9, z: 0, c: scheme.cross[0] }, // Left arm
        { x: 0, y: 9, z: 0, c: scheme.cross[0] },  // Center
        { x: 1, y: 9, z: 0, c: scheme.cross[0] },  // Right arm
        { x: 0, y: 9, z: -1, c: scheme.cross[0] }, // Front arm
        { x: 0, y: 9, z: 1, c: scheme.cross[0] },  // Back arm
        
        // Layer 10 (y=10) - Cross top
        { x: 0, y: 10, z: 0, c: scheme.cross[0] },
        
        // Royal jewels on crown
        { x: 0, y: 6, z: -1, c: scheme.jewel[0] }, // Front jewel
        { x: 0, y: 6, z: 1, c: scheme.jewel[0] },  // Back jewel
        { x: -1, y: 6, z: 0, c: scheme.jewel[0] }, // Left jewel
        { x: 1, y: 6, z: 0, c: scheme.jewel[0] },  // Right jewel
        
        // Additional body decorations
        { x: 0, y: 3, z: -1, c: scheme.jewel[0] }, // Front chest jewel
        { x: 0, y: 3, z: 1, c: scheme.jewel[0] },  // Back decoration
    ];
    
    // Group voxels by material for optimization
    const voxelsByMaterial = new Map();
    
    for (const voxel of kingVoxels) {
        if (!voxelsByMaterial.has(voxel.c)) {
            voxelsByMaterial.set(voxel.c, []);
        }
        voxelsByMaterial.get(voxel.c).push(voxel);
    }
    
    // Create merged geometry for each material
    for (const [hexColor, voxels] of voxelsByMaterial) {
        const geometries = [];
        
        for (const voxel of voxels) {
            const geometry = new THREE.BoxGeometry(voxelScale, voxelScale, voxelScale);
            geometry.translate(
                voxel.x * voxelScale,
                voxel.y * voxelScale,
                voxel.z * voxelScale
            );
            geometries.push(geometry);
        }
        
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            
            // Special material properties for different elements
            let materialProps = {
                roughness: color === 'black' ? 0.2 : 0.6,
                metalness: color === 'black' ? 0.3 : 0.1
            };
            
            // Crown elements - highly metallic and shiny
            if (scheme.crown.includes(hexColor)) {
                materialProps = {
                    roughness: 0.05,
                    metalness: 0.9,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: color === 'black' ? 0.15 : 0.08
                };
            }
            
            // Cross elements - very bright and emissive
            if (scheme.cross.includes(hexColor)) {
                materialProps = {
                    roughness: 0.0,
                    metalness: 1.0,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: color === 'black' ? 0.5 : 0.3
                };
            }
            
            // Jewel elements - highly emissive and bright
            if (scheme.jewel.includes(hexColor)) {
                materialProps = {
                    roughness: 0.0,
                    metalness: 0.95,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: color === 'black' ? 0.6 : 0.4
                };
            }
            
            const material = _getMaterialByHex_Cached(hexColor, materialProps);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.layers.set(0); // Ensure piece is on layer 0 for raycasting
            kingGroup.add(mesh);
        }
    }
    
    // Add majestic glow for black pieces (hellish royalty theme)
    if (color === 'black') {
        const glowGeometry = new THREE.SphereGeometry(voxelScale * 4, 12, 12);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x880000,
            transparent: true,
            opacity: 0.2,
            side: THREE.BackSide
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.layers.set(0); // Ensure glow is on layer 0 for raycasting
        glowMesh.position.y = voxelScale * 5;
        kingGroup.add(glowMesh);
        
        // Additional inner glow for extra majesty
        const innerGlowGeometry = new THREE.SphereGeometry(voxelScale * 2.5, 8, 8);
        const innerGlowMaterial = new THREE.MeshBasicMaterial({
            color: 0xAA0000,
            transparent: true,
            opacity: 0.15,
            side: THREE.BackSide
        });
        const innerGlowMesh = new THREE.Mesh(innerGlowGeometry, innerGlowMaterial);
        innerGlowMesh.layers.set(0); // Ensure inner glow is on layer 0 for raycasting
        innerGlowMesh.position.y = voxelScale * 5;
        kingGroup.add(innerGlowMesh);
    }
    
    // Position the king so its base is at y=0
    kingGroup.position.y = 0;
    
    // Ensure the entire group is on layer 0 for raycasting
    kingGroup.layers.set(0);
    
    console.log(`[chessKingObject] Created ${color} chess king with ${kingVoxels.length} voxels`);
    
    return kingGroup;
}

/**
 * Helper function to create white king
 */
export function createWhiteChessKing(options = {}) {
    return createChessKingObject({ ...options, color: 'white' });
}

/**
 * Helper function to create black king
 */
export function createBlackChessKing(options = {}) {
    return createChessKingObject({ ...options, color: 'black' });
}