import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Biohazard Card Prefab
 * Creates a toxic chemical cloud with spreading contamination
 */

// Biohazard specific colors
const BIOHAZARD_COLORS = {
    TOXIC_GREEN: 0x32CD32,       // Primary toxic green
    ACID_YELLOW: 0xFFFF00,       // Acidic yellow
    POISON_LIME: 0x7FFF00,       // Lime poison
    CHEMICAL_ORANGE: 0xFF8C00,   // Chemical warning orange
    HAZARD_RED: 0xFF4500,        // Hazard warning red
    SICKLY_PALE: 0x98FB98,       // Pale sickly green
    RADIOACTIVE: 0x39FF14,       // Bright radioactive green
    CONTAMINATE: 0x228B22        // Dark forest contamination
};

/**
 * Create a biohazard card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The biohazard card 3D model
 */
export function createBiohazardCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'BiohazardCard';

    // Materials
    const toxicGreenMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.TOXIC_GREEN,
        emissive: BIOHAZARD_COLORS.TOXIC_GREEN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const acidYellowMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.ACID_YELLOW,
        emissive: BIOHAZARD_COLORS.ACID_YELLOW,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const poisonLimeMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.POISON_LIME,
        emissive: BIOHAZARD_COLORS.POISON_LIME,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const chemicalOrangeMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.CHEMICAL_ORANGE,
        emissive: BIOHAZARD_COLORS.CHEMICAL_ORANGE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const hazardRedMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.HAZARD_RED,
        emissive: BIOHAZARD_COLORS.HAZARD_RED,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const sicklyPaleMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.SICKLY_PALE,
        emissive: BIOHAZARD_COLORS.SICKLY_PALE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const radioactiveMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.RADIOACTIVE,
        emissive: BIOHAZARD_COLORS.RADIOACTIVE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.9
    });

    const contaminateMaterial = new THREE.MeshLambertMaterial({
        color: BIOHAZARD_COLORS.CONTAMINATE,
        emissive: BIOHAZARD_COLORS.CONTAMINATE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create biohazard trefoil symbol (central hazard icon)
    const trefoilVoxels = [
        // Center circle
        { x: 0, y: 0, z: 0, material: hazardRedMaterial },
        
        // Upper circle
        { x: 0, y: 2, z: 0, material: toxicGreenMaterial },
        { x: -1, y: 2, z: 0, material: toxicGreenMaterial },
        { x: 1, y: 2, z: 0, material: toxicGreenMaterial },
        { x: 0, y: 3, z: 0, material: toxicGreenMaterial },
        
        // Lower left circle
        { x: -2, y: -1, z: 0, material: toxicGreenMaterial },
        { x: -3, y: -1, z: 0, material: toxicGreenMaterial },
        { x: -2, y: -2, z: 0, material: toxicGreenMaterial },
        { x: -1, y: -1, z: 0, material: toxicGreenMaterial },
        
        // Lower right circle
        { x: 2, y: -1, z: 0, material: toxicGreenMaterial },
        { x: 3, y: -1, z: 0, material: toxicGreenMaterial },
        { x: 2, y: -2, z: 0, material: toxicGreenMaterial },
        { x: 1, y: -1, z: 0, material: toxicGreenMaterial },
        
        // Connecting lines
        { x: 0, y: 1, z: 0, material: hazardRedMaterial },
        { x: -1, y: 0, z: 0, material: hazardRedMaterial },
        { x: 1, y: 0, z: 0, material: hazardRedMaterial }
    ];

    // Create toxic cloud particles
    const toxicCloudVoxels = [
        // Upper cloud layer
        { x: -4, y: 4, z: 0, material: acidYellowMaterial },
        { x: -2, y: 5, z: 0, material: poisonLimeMaterial },
        { x: 0, y: 5, z: 0, material: sicklyPaleMaterial },
        { x: 2, y: 5, z: 0, material: poisonLimeMaterial },
        { x: 4, y: 4, z: 0, material: acidYellowMaterial },
        { x: -3, y: 4, z: 0, material: contaminateMaterial },
        { x: 3, y: 4, z: 0, material: contaminateMaterial },
        
        // Mid-level toxic spread
        { x: -5, y: 0, z: 0, material: radioactiveMaterial },
        { x: -4, y: 1, z: 0, material: acidYellowMaterial },
        { x: -4, y: -1, z: 0, material: acidYellowMaterial },
        { x: 5, y: 0, z: 0, material: radioactiveMaterial },
        { x: 4, y: 1, z: 0, material: acidYellowMaterial },
        { x: 4, y: -1, z: 0, material: acidYellowMaterial },
        
        // Lower contamination layer
        { x: -4, y: -4, z: 0, material: acidYellowMaterial },
        { x: -2, y: -5, z: 0, material: poisonLimeMaterial },
        { x: 0, y: -5, z: 0, material: sicklyPaleMaterial },
        { x: 2, y: -5, z: 0, material: poisonLimeMaterial },
        { x: 4, y: -4, z: 0, material: acidYellowMaterial },
        { x: -3, y: -4, z: 0, material: contaminateMaterial },
        { x: 3, y: -4, z: 0, material: contaminateMaterial }
    ];

    // Create spreading contamination particles
    const contaminationVoxels = [
        // Outer contamination ring
        { x: -6, y: 2, z: 0, material: contaminateMaterial },
        { x: -5, y: 3, z: 0, material: sicklyPaleMaterial },
        { x: -6, y: -2, z: 0, material: contaminateMaterial },
        { x: -5, y: -3, z: 0, material: sicklyPaleMaterial },
        { x: 6, y: 2, z: 0, material: contaminateMaterial },
        { x: 5, y: 3, z: 0, material: sicklyPaleMaterial },
        { x: 6, y: -2, z: 0, material: contaminateMaterial },
        { x: 5, y: -3, z: 0, material: sicklyPaleMaterial },
        
        // Radioactive particles
        { x: -3, y: 6, z: 0, material: radioactiveMaterial },
        { x: 0, y: 6, z: 0, material: radioactiveMaterial },
        { x: 3, y: 6, z: 0, material: radioactiveMaterial },
        { x: -3, y: -6, z: 0, material: radioactiveMaterial },
        { x: 0, y: -6, z: 0, material: radioactiveMaterial },
        { x: 3, y: -6, z: 0, material: radioactiveMaterial },
        
        // Chemical vapor trails
        { x: -7, y: 1, z: 0, material: chemicalOrangeMaterial },
        { x: -7, y: 0, z: 0, material: chemicalOrangeMaterial },
        { x: -7, y: -1, z: 0, material: chemicalOrangeMaterial },
        { x: 7, y: 1, z: 0, material: chemicalOrangeMaterial },
        { x: 7, y: 0, z: 0, material: chemicalOrangeMaterial },
        { x: 7, y: -1, z: 0, material: chemicalOrangeMaterial }
    ];

    // Create warning indicators
    const warningVoxels = [
        // Corner warning markers
        { x: -6, y: 6, z: 0, material: hazardRedMaterial },
        { x: 6, y: 6, z: 0, material: hazardRedMaterial },
        { x: -6, y: -6, z: 0, material: hazardRedMaterial },
        { x: 6, y: -6, z: 0, material: hazardRedMaterial },
        
        // Warning stripes
        { x: -5, y: 6, z: 0, material: chemicalOrangeMaterial },
        { x: 5, y: 6, z: 0, material: chemicalOrangeMaterial },
        { x: -5, y: -6, z: 0, material: chemicalOrangeMaterial },
        { x: 5, y: -6, z: 0, material: chemicalOrangeMaterial },
        
        // Additional warning indicators
        { x: -6, y: 5, z: 0, material: chemicalOrangeMaterial },
        { x: 6, y: 5, z: 0, material: chemicalOrangeMaterial },
        { x: -6, y: -5, z: 0, material: chemicalOrangeMaterial },
        { x: 6, y: -5, z: 0, material: chemicalOrangeMaterial }
    ];

    // Create all voxels
    [...trefoilVoxels, ...toxicCloudVoxels, ...contaminationVoxels, ...warningVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.3, // Compact spacing
            voxel.y * VOXEL_SIZE * 0.3,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (trefoilVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'trefoil';
        } else if (toxicCloudVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'cloud';
        } else if (contaminationVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'contamination';
        } else {
            mesh.userData.voxelType = 'warning';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        toxicPulse: 0,
        contaminationSpread: 0,
        warningFlash: 0,
        biohazardOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update biohazard card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateBiohazardCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.toxicPulse += deltaTime * 4.5; // Rapid toxic activity
    cardGroup.userData.contaminationSpread += deltaTime * 2.8;
    cardGroup.userData.warningFlash += deltaTime * 6.0; // Urgent warning flashing

    const time = cardGroup.userData.animationTime;
    const toxicPulse = cardGroup.userData.toxicPulse;
    const contaminationSpread = cardGroup.userData.contaminationSpread;
    const warningFlash = cardGroup.userData.warningFlash;
    const biohazardOffset = cardGroup.userData.biohazardOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'trefoil':
                    // Central biohazard symbol pulses with toxic energy
                    const trefoilIntensity = 0.8 + Math.sin(toxicPulse * 3.0 + biohazardOffset) * 0.7;
                    const trefoilGlow = Math.sin(toxicPulse * 5.0) * 0.003;
                    
                    child.material.emissiveIntensity = baseEmissive * trefoilIntensity;
                    child.material.opacity = baseOpacity * (0.9 + trefoilIntensity * 0.1);
                    child.position.x += trefoilGlow;
                    child.position.y += trefoilGlow * 0.7;
                    break;

                case 'cloud':
                    // Toxic cloud spreads and bubbles
                    const cloudIntensity = 0.6 + Math.sin(contaminationSpread * 2.5 + child.position.x * 0.3 + child.position.y * 0.2) * 0.8;
                    const cloudMotion = Math.sin(contaminationSpread * 3.0 + biohazardOffset) * 0.012;
                    
                    child.material.emissiveIntensity = baseEmissive * cloudIntensity;
                    child.material.opacity = baseOpacity * cloudIntensity;
                    child.position.x += cloudMotion;
                    child.position.y += cloudMotion * 0.6;
                    break;

                case 'contamination':
                    // Contamination spreads outward
                    const contaminationIntensity = 0.5 + Math.sin(contaminationSpread * 4.0 + child.position.x * 0.5 + biohazardOffset) * 0.9;
                    const spreadMotion = Math.cos(contaminationSpread * 2.0 + child.position.y * 0.4) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * contaminationIntensity;
                    child.material.opacity = baseOpacity * contaminationIntensity;
                    child.position.x += spreadMotion;
                    child.position.y += spreadMotion * 0.5;
                    break;

                case 'warning':
                    // Warning indicators flash urgently
                    const warningIntensity = 0.4 + Math.sin(warningFlash * 2.0 + biohazardOffset) * 0.8;
                    const flashMotion = Math.sin(warningFlash * 8.0) * 0.002;
                    
                    child.material.emissiveIntensity = baseEmissive * warningIntensity;
                    child.material.opacity = baseOpacity * warningIntensity;
                    child.position.x += flashMotion;
                    child.position.y += flashMotion;
                    break;
            }
        }
    });

    // Overall toxic contamination effect
    const biohazardPulse = Math.sin(time * 3.5 + biohazardOffset) * 0.01;
    cardGroup.position.x += biohazardPulse;
    cardGroup.position.y += biohazardPulse * 0.8;
    
    // Slight rotation to simulate spreading contamination
    cardGroup.rotation.z += deltaTime * 0.3;
}

// Export the biohazard card data for the loot system
export const BIOHAZARD_CARD_DATA = {
    name: 'Biohazard',
    description: 'Releases a spreading cloud of toxic chemicals that creates a hazardous area, dealing continuous poison damage to all enemies within the contaminated zone.',
    category: 'card',
    rarity: 'rare',
    effect: 'toxic_cloud',
    effectValue: 25,
    createFunction: createBiohazardCard,
    updateFunction: updateBiohazardCardAnimation,
    voxelModel: 'biohazard_card',
    glow: {
        color: 0x32CD32,
        intensity: 1.2
    }
};

export default createBiohazardCard;