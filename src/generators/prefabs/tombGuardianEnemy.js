import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import { VOXEL_SIZE, getOrCreateGeometry, mulberry32 } from './shared.js';

// Tomb Guardian Colors
const TOMB_GUARDIAN_COLORS = {
    STONE_DARK: 0x4a4a4a,      // Dark stone
    STONE_MEDIUM: 0x6a6a6a,    // Medium stone
    STONE_LIGHT: 0x8a8a8a,     // Light stone
    STONE_ACCENT: 0x5a5a5a,    // Stone accents
    MOSS: 0x3a5a3a,            // Mossy areas
    METAL_DARK: 0x3a3a3a,      // Dark metal (sword/shield)
    METAL_LIGHT: 0x7a7a7a,     // Light metal highlights
    RUST: 0x8a4a2a,            // Rust on metal
    EYE_GLOW: 0x00ffff,        // Glowing cyan eyes
    RUNE_GLOW: 0x00aaff,       // Glowing runes
};

/**
 * Creates a voxel-based tomb guardian enemy model.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The tomb guardian enemy model group.
 */
export function createTombGuardianEnemyModel(scale = 1.0) {
    console.log("Creating Tomb Guardian model with scale:", scale);
    const finalGroup = new THREE.Group();
    finalGroup.name = "TombGuardian";

    // Create a seeded random function for consistent appearance
    const random = mulberry32(54321);

    // Create animation-ready groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const headGroup = new THREE.Group();
    headGroup.name = "head";

    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";

    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";

    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";

    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";

    // Add groups to final group
    finalGroup.add(bodyGroup);
    bodyGroup.add(headGroup);
    bodyGroup.add(leftArmGroup);
    bodyGroup.add(rightArmGroup);
    bodyGroup.add(leftLegGroup);
    bodyGroup.add(rightLegGroup);

    // Position body parts (fixed positioning for proper alignment)
    headGroup.position.set(0, 12 * VOXEL_SIZE, 0); // Head above body (body goes to y=11)
    leftArmGroup.position.set(-3.5 * VOXEL_SIZE, 10 * VOXEL_SIZE, 0); // Shoulders at upper torso
    rightArmGroup.position.set(3.5 * VOXEL_SIZE, 10 * VOXEL_SIZE, 0); // Shoulders at upper torso
    leftLegGroup.position.set(-2 * VOXEL_SIZE, 6 * VOXEL_SIZE, 0); // Hips at bottom of torso
    rightLegGroup.position.set(2 * VOXEL_SIZE, 6 * VOXEL_SIZE, 0); // Hips at bottom of torso

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        head: {},
        leftArm: {},
        rightArm: {},
        leftLeg: {},
        rightLeg: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('guardian_voxel', () => new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE));
    const tempMatrix = new THREE.Matrix4();

    // Helper function to add a voxel to the appropriate group
    function addVoxel(groupName, x, y, z, color) {
        tempMatrix.makeTranslation(
            x * VOXEL_SIZE,
            y * VOXEL_SIZE,
            z * VOXEL_SIZE
        );

        // Store voxel data for destruction effects
        originalVoxels.push({
            group: groupName,
            position: new THREE.Vector3(x, y, z).multiplyScalar(VOXEL_SIZE),
            color: color
        });

        // Create or get the geometries array for this color
        if (!geometriesByMaterial[groupName][color]) {
            geometriesByMaterial[groupName][color] = [];
        }

        // Create a new instance of the geometry
        const instancedGeo = voxelGeo.clone();
        instancedGeo.applyMatrix4(tempMatrix);

        // Add to the appropriate array
        geometriesByMaterial[groupName][color].push(instancedGeo);
    }

    // Helper function to get stone color with variation
    function getStoneColor(baseColor, variation = 0.15) {
        if (random() < variation) {
            return TOMB_GUARDIAN_COLORS.MOSS; // Occasional moss
        }
        const r = random();
        if (r < 0.6) return baseColor;
        if (r < 0.8) return TOMB_GUARDIAN_COLORS.STONE_MEDIUM;
        return TOMB_GUARDIAN_COLORS.STONE_LIGHT;
    }

    // --- Build Body (Torso) ---
    // Massive stone torso with layered armor plates (similar to zombie/golem style)
    // Add lower body/waist section from y=0 to y=5
    for (let y = 0; y <= 5; y++) {
        const width = y < 2 ? 6 : 5; // Wider base for stability
        const depth = y < 2 ? 4 : 3;
        
        for (let x = -Math.floor(width/2); x <= Math.floor(width/2); x++) {
            for (let z = -Math.floor(depth/2); z <= Math.floor(depth/2); z++) {
                // Skip corners for organic shape
                if ((Math.abs(x) === Math.floor(width/2) && Math.abs(z) === Math.floor(depth/2))) {
                    if (random() < 0.5) continue;
                }
                
                // Stone base with belt details
                let color;
                if (y === 4 || y === 5) {
                    // Belt/waist armor
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK);
                }
                
                addVoxel('body', x, y, z, color);
            }
        }
    }
    
    // Upper body from y=6 to y=11
    for (let y = 6; y <= 11; y++) {
        const yOffset = y - 6; // For shape calculations
        const width = yOffset < 2 ? 5 : (yOffset < 4 ? 6 : 5); // Broader for more imposing look
        const depth = yOffset < 2 ? 3 : (yOffset < 4 ? 4 : 3);
        
        for (let x = -Math.floor(width/2); x <= Math.floor(width/2); x++) {
            for (let z = -Math.floor(depth/2); z <= Math.floor(depth/2); z++) {
                // Skip corners for more organic shape (matching golem style)
                if ((Math.abs(x) === Math.floor(width/2) && Math.abs(z) === Math.floor(depth/2))) {
                    if (random() < 0.7) continue;
                }

                // Chest armor with more detail
                let color;
                if (y >= 8 && y <= 10 && Math.abs(z) === Math.floor(depth/2) - 1) {
                    // Inner armor layer
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else if (y >= 7 && y <= 11 && Math.abs(z) === Math.floor(depth/2)) {
                    // Outer armor plates
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_ACCENT, 0.3);
                } else if (y === 9 && x === 0 && z === 0) {
                    // Central chest rune
                    color = TOMB_GUARDIAN_COLORS.RUNE_GLOW;
                } else {
                    // Base stone with variation
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK);
                }
                
                addVoxel('body', x, y, z, color);
            }
        }
    }

    // Add stone armor details with battle damage
    for (let y = 7; y <= 10; y++) {
        // Front plate with rune
        if (y === 8 || y === 9) {
            addVoxel('body', 0, y, -3, TOMB_GUARDIAN_COLORS.RUNE_GLOW);
        } else {
            addVoxel('body', 0, y, -3, TOMB_GUARDIAN_COLORS.STONE_ACCENT);
        }
        // Side plates with occasional damage
        if (!(y === 7 && random() < 0.3)) { // Random missing pieces
            addVoxel('body', -1, y, -3, TOMB_GUARDIAN_COLORS.STONE_ACCENT);
        }
        if (!(y === 10 && random() < 0.3)) { // Random missing pieces
            addVoxel('body', 1, y, -3, TOMB_GUARDIAN_COLORS.STONE_ACCENT);
        }
    }
    
    // Add shoulder pauldrons (at actual shoulder height)
    for (let x = -4; x <= 4; x += 8) { // Left and right shoulders
        for (let y = 10; y <= 11; y++) {
            for (let z = -2; z <= 2; z++) {
                if (Math.abs(z) === 2 && random() < 0.4) continue; // Worn edges
                const color = y === 11 ? TOMB_GUARDIAN_COLORS.STONE_ACCENT : TOMB_GUARDIAN_COLORS.STONE_MEDIUM;
                addVoxel('body', x, y, z, color);
            }
        }
    }
    
    // Add ancient inscriptions on sides
    for (let y = 8; y <= 9; y++) {
        if (random() < 0.7) {
            addVoxel('body', -3, y, 0, TOMB_GUARDIAN_COLORS.RUNE_GLOW);
        }
        if (random() < 0.7) {
            addVoxel('body', 3, y, 0, TOMB_GUARDIAN_COLORS.RUNE_GLOW);
        }
    }

    // --- Build Head ---
    // Stone head with detailed helmet (matching golem/zombie detail level)
    for (let y = -1; y < 3; y++) {
        let size;
        if (y === -1) size = 2; // Neck
        else if (y === 0) size = 3; // Lower head
        else if (y === 1) size = 3; // Mid head
        else size = 2; // Top head
        
        for (let x = -Math.floor(size/2); x <= Math.floor(size/2); x++) {
            for (let z = -Math.floor(size/2); z <= Math.floor(size/2); z++) {
                // Skip corners for rounded shape (like magma golem)
                if (Math.abs(x) === Math.floor(size/2) && Math.abs(z) === Math.floor(size/2)) {
                    if (y >= 1 || random() < 0.5) continue;
                }
                
                // Face details
                let color;
                if (y === 0 && Math.abs(x) <= 1 && z === Math.floor(size/2)) {
                    // Face plate
                    color = TOMB_GUARDIAN_COLORS.STONE_LIGHT;
                } else if (y >= 0 && y <= 2 && Math.abs(x) === Math.floor(size/2)) {
                    // Side helmet plates
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_MEDIUM);
                }
                
                addVoxel('head', x, y, z, color);
            }
        }
    }

    // Helmet crest
    for (let x = -1; x <= 1; x++) {
        addVoxel('head', x, 3, 0, TOMB_GUARDIAN_COLORS.STONE_ACCENT);
        if (x === 0) {
            addVoxel('head', x, 4, 0, TOMB_GUARDIAN_COLORS.STONE_ACCENT);
        }
    }

    // Add deep eye sockets (recessed into the head)
    addVoxel('head', -1, 0, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    addVoxel('head', 1, 0, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    addVoxel('head', -1, -1, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    addVoxel('head', 1, -1, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    addVoxel('head', -1, 1, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    addVoxel('head', 1, 1, 1, TOMB_GUARDIAN_COLORS.STONE_DARK);
    
    // Glowing eyes (positioned deep inside the sockets, not protruding)
    // Place them at z=0 or even z=-1 to be inside the head rather than on the surface
    addVoxel('head', -1, 0, 0, TOMB_GUARDIAN_COLORS.EYE_GLOW);
    addVoxel('head', 1, 0, 0, TOMB_GUARDIAN_COLORS.EYE_GLOW);

    // --- Build Left Arm (Shield Arm) ---
    // Upper arm (bulkier like golem)
    for (let y = 0; y <= 3; y++) {
        for (let x = -1; x <= 1; x++) {
            for (let z = -1; z <= 1; z++) {
                // Skip corners for rounded shape
                if (Math.abs(x) === 1 && Math.abs(z) === 1) continue;
                
                // Add armor plating detail
                let color;
                if (y === 1 && x === -1) {
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_MEDIUM);
                }
                addVoxel('leftArm', x, -y, z, color);
            }
        }
    }

    // Lower arm (thicker)
    for (let y = -6; y <= -3; y++) {
        for (let x = -1; x <= 1; x++) {
            for (let z = -1; z <= 1; z++) {
                if (Math.abs(x) === 1 && Math.abs(z) === 1 && random() < 0.5) continue;
                addVoxel('leftArm', x, y, z, getStoneColor(TOMB_GUARDIAN_COLORS.STONE_MEDIUM));
            }
        }
    }
    
    // Hand holding shield
    for (let x = -1; x <= 1; x++) {
        for (let z = -1; z <= 1; z++) {
            addVoxel('leftArm', x, -7, z, TOMB_GUARDIAN_COLORS.STONE_DARK);
        }
    }

    // Shield (larger and more detailed)
    for (let y = -8; y <= 0; y++) {
        const shieldWidth = y >= -6 && y <= -2 ? 3 : 2; // Wider in middle
        const shieldDepth = 4;
        
        for (let x = 2; x <= 2 + shieldWidth; x++) {
            for (let z = -2; z <= 1; z++) {
                // Shield shape
                if ((y === -8 || y === 0) && (x === 2 + shieldWidth || Math.abs(z) === 2)) {
                    // Skip corners for rounded shield
                    if (random() < 0.7) continue;
                }
                
                // Shield details
                let color;
                if (x === 2 || x === 2 + shieldWidth || y === -8 || y === 0 || Math.abs(z) === 2) {
                    // Shield rim
                    color = TOMB_GUARDIAN_COLORS.METAL_DARK;
                } else if (y >= -5 && y <= -3 && x === 3 && z === -1) {
                    // Central emblem
                    color = TOMB_GUARDIAN_COLORS.RUNE_GLOW;
                } else if ((y === -6 || y === -2) && z === -1) {
                    // Decorative bands
                    color = TOMB_GUARDIAN_COLORS.RUST;
                } else {
                    // Shield face
                    color = random() < 0.8 ? TOMB_GUARDIAN_COLORS.METAL_LIGHT : TOMB_GUARDIAN_COLORS.METAL_DARK;
                }
                
                addVoxel('leftArm', x, y, z, color);
            }
        }
    }

    // --- Build Right Arm (Sword Arm) ---
    // Upper arm (matching left arm style)
    for (let y = 0; y <= 3; y++) {
        for (let x = -1; x <= 1; x++) {
            for (let z = -1; z <= 1; z++) {
                // Skip corners for rounded shape
                if (Math.abs(x) === 1 && Math.abs(z) === 1) continue;
                
                // Add armor plating detail
                let color;
                if (y === 1 && x === 1) {
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_MEDIUM);
                }
                addVoxel('rightArm', x, -y, z, color);
            }
        }
    }

    // Lower arm (thicker)
    for (let y = -6; y <= -3; y++) {
        for (let x = -1; x <= 1; x++) {
            for (let z = -1; z <= 1; z++) {
                if (Math.abs(x) === 1 && Math.abs(z) === 1 && random() < 0.5) continue;
                addVoxel('rightArm', x, y, z, getStoneColor(TOMB_GUARDIAN_COLORS.STONE_MEDIUM));
            }
        }
    }

    // Hand gripping sword
    for (let x = -1; x <= 1; x++) {
        for (let z = -1; z <= 1; z++) {
            addVoxel('rightArm', x, -7, z, TOMB_GUARDIAN_COLORS.STONE_DARK);
        }
    }

    // Sword hilt and guard
    for (let y = -7; y <= -5; y++) {
        addVoxel('rightArm', 0, y, 0, TOMB_GUARDIAN_COLORS.METAL_DARK);
    }
    
    // Elaborate cross guard
    for (let z = -3; z <= 3; z++) {
        if (Math.abs(z) >= 1) {
            addVoxel('rightArm', 0, -7, z, TOMB_GUARDIAN_COLORS.METAL_DARK);
            if (Math.abs(z) === 3) {
                // Guard tips
                addVoxel('rightArm', 0, -6, z, TOMB_GUARDIAN_COLORS.RUST);
            }
        }
    }

    // Sword blade (wider and more imposing)
    for (let y = -14; y <= -8; y++) {
        // Main blade center
        addVoxel('rightArm', 0, y, 0, TOMB_GUARDIAN_COLORS.METAL_LIGHT);
        
        // Blade width
        if (y > -13) {
            addVoxel('rightArm', -0.5, y, 0, TOMB_GUARDIAN_COLORS.METAL_LIGHT);
            addVoxel('rightArm', 0.5, y, 0, TOMB_GUARDIAN_COLORS.METAL_LIGHT);
            
            // Blade edges
            addVoxel('rightArm', 0, y, -0.5, TOMB_GUARDIAN_COLORS.METAL_DARK);
            addVoxel('rightArm', 0, y, 0.5, TOMB_GUARDIAN_COLORS.METAL_DARK);
        }
        
        // Fuller (blood groove)
        if (y >= -12 && y <= -9 && random() < 0.7) {
            addVoxel('rightArm', 0, y, 0, TOMB_GUARDIAN_COLORS.METAL_DARK);
        }
    }
    
    // Sword point
    addVoxel('rightArm', 0, -15, 0, TOMB_GUARDIAN_COLORS.METAL_LIGHT);

    // --- Build Left Leg ---
    // Thick armored leg (similar to golem style)
    for (let y = 0; y <= 6; y++) {
        const width = y > 4 ? 3 : 2; // Wider at bottom for stability
        for (let x = -Math.floor(width/2); x <= Math.floor(width/2); x++) {
            for (let z = -Math.floor(width/2); z <= Math.floor(width/2); z++) {
                // Skip corners for worn look
                if (Math.abs(x) === Math.floor(width/2) && Math.abs(z) === Math.floor(width/2)) {
                    if (random() < 0.4) continue;
                }
                
                // Leg armor details
                let color;
                if (y === 2 && Math.abs(x) === Math.floor(width/2)) {
                    // Knee plate
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else if (y >= 4 && Math.abs(z) === Math.floor(width/2)) {
                    // Greave plates
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK);
                }
                
                addVoxel('leftLeg', x, -y, z, color);
            }
        }
    }
    
    // Foot
    for (let y = -8; y <= -6; y++) {
        for (let x = -2; x <= 2; x++) {
            for (let z = -1; z <= 2; z++) {
                // Skip corners
                if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;
                if (Math.abs(x) === 2 && z === -1) continue;
                
                addVoxel('leftLeg', x, y, z, getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK, 0.05));
            }
        }
    }

    // --- Build Right Leg ---
    // Mirror of left leg with slight variations
    for (let y = 0; y <= 5; y++) {
        const width = y > 3 ? 3 : 2; // Wider at bottom for stability
        for (let x = -Math.floor(width/2); x <= Math.floor(width/2); x++) {
            for (let z = -Math.floor(width/2); z <= Math.floor(width/2); z++) {
                // Skip corners for worn look
                if (Math.abs(x) === Math.floor(width/2) && Math.abs(z) === Math.floor(width/2)) {
                    if (random() < 0.4) continue;
                }
                
                // Leg armor details (slightly different from left)
                let color;
                if (y === 2 && Math.abs(z) === Math.floor(width/2)) {
                    // Knee plate (different position)
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else if (y >= 4 && Math.abs(x) === Math.floor(width/2)) {
                    // Greave plates (different position)
                    color = TOMB_GUARDIAN_COLORS.STONE_ACCENT;
                } else {
                    color = getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK);
                }
                
                addVoxel('rightLeg', x, -y, z, color);
            }
        }
    }
    
    // Foot
    for (let y = -8; y <= -6; y++) {
        for (let x = -2; x <= 2; x++) {
            for (let z = -1; z <= 2; z++) {
                // Skip corners
                if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;
                if (Math.abs(x) === 2 && z === -1) continue;
                
                addVoxel('rightLeg', x, y, z, getStoneColor(TOMB_GUARDIAN_COLORS.STONE_DARK, 0.05));
            }
        }
    }

    // Helper function to merge geometries for a group
    function mergeGroupGeometries(groupName, targetGroup) {
        const colorMaterials = {};

        // Process each color in the group
        Object.entries(geometriesByMaterial[groupName]).forEach(([colorHex, geometries]) => {
            const color = parseInt(colorHex);

            // Skip if no geometries for this color
            if (geometries.length === 0) return;

            // Create material with appropriate properties
            const material = new THREE.MeshStandardMaterial({
                color: color,
                roughness: 0.9, // Stone is rough
                metalness: 0.1  // Mostly non-metallic
            });

            // Add emissive properties for glowing parts
            if (color === TOMB_GUARDIAN_COLORS.EYE_GLOW || color === TOMB_GUARDIAN_COLORS.RUNE_GLOW) {
                material.emissive = new THREE.Color(color);
                material.emissiveIntensity = 0.8;
            } else if (color === TOMB_GUARDIAN_COLORS.METAL_LIGHT || color === TOMB_GUARDIAN_COLORS.METAL_DARK) {
                material.roughness = 0.4;
                material.metalness = 0.8;
            }

            colorMaterials[color] = material;

            // Merge geometries of the same color
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            // Add to the target group
            targetGroup.add(mesh);
        });
    }

    // Merge geometries for each body part
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('head', headGroup);
    mergeGroupGeometries('leftArm', leftArmGroup);
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);

    // Apply scale
    finalGroup.scale.multiplyScalar(scale);

    // Add userData for game systems
    finalGroup.userData = {
        type: 'tombGuardian',
        originalVoxels: originalVoxels,
        animationData: {
            walkSpeed: 0.3,        // Very slow
            attackSpeed: 0.8,
            idleSwayAmount: 0.02,
            walkAmplitude: Math.PI / 16  // Smaller steps for heavy stone
        },
        attackHitbox: {
            radius: 3 * VOXEL_SIZE * scale,
            damage: 25,
            knockback: 15
        },
        shieldBashHitbox: {
            radius: 4 * VOXEL_SIZE * scale,
            damage: 10,
            knockback: 20,
            stunDuration: 1.5
        },
        deathEffect: {
            type: 'crumble',
            damageRadius: 5 * VOXEL_SIZE * scale,
            damage: 15
        }
    };

    // Log body part structure for debugging
    console.log(`Tomb Guardian model created:`, {
        body: !!bodyGroup,
        head: !!headGroup,
        leftArm: !!leftArmGroup,
        rightArm: !!rightArmGroup,
        leftLeg: !!leftLegGroup,
        rightLeg: !!rightLegGroup
    });

    return finalGroup;
}