/**
 * Generates voxel models for items that can be picked up in the game.
 */

import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';
import { ITEM_RARITY } from '../../entities/ItemTypes.js';

// --- Color Definitions ---
const COLORS = {
    // Base colors
    RED: 0xff0000,
    GREEN: 0x00ff00,
    BLUE: 0x0000ff,
    YELLOW: 0xffff00,
    PURPLE: 0xff00ff,
    CYAN: 0x00ffff,
    WHITE: 0xffffff,
    BLACK: 0x000000,
    GRAY: 0x888888,
    
    // Metal colors
    GOLD: 0xffd700,
    SILVER: 0xc0c0c0,
    BRONZE: 0xcd7f32,
    
    // Potion colors
    SOUL_POTION: 0x9966ff,
    MANA_POTION: 0x3333ff,
    
    // Soul colors
    LIGHT_SOUL: 0xeeeeff,
    DARK_SOUL: 0x330066,
    BALANCED_SOUL: 0x9966ff
};

// --- Rarity Color Definitions ---
const RARITY_COLORS = {
    [ITEM_RARITY.COMMON]: 0xaaaaaa,
    [ITEM_RARITY.RARE]: 0x5555ff,
    [ITEM_RARITY.EPIC]: 0xaa55ff,
    [ITEM_RARITY.LEGENDARY]: 0xffaa00
};

// --- Rarity Glow Definitions ---
const RARITY_GLOW = {
    [ITEM_RARITY.COMMON]: { color: 0xffffff, intensity: 0.5 },
    [ITEM_RARITY.RARE]: { color: 0x5555ff, intensity: 1.0 },
    [ITEM_RARITY.EPIC]: { color: 0xaa55ff, intensity: 1.5 },
    [ITEM_RARITY.LEGENDARY]: { color: 0xffaa00, intensity: 2.0 }
};

// --- Voxel Model Definitions ---
// Each model is defined as an array of voxel positions and colors
const VOXEL_MODELS = {
    // --- Health Items ---
    soul_potion: {
        voxels: [
            // Crystal bottle base (hexagonal)
            { x: 1, y: 0, z: 0, color: COLORS.SILVER },
            { x: 2, y: 0, z: 0, color: COLORS.SILVER },
            { x: 0, y: 0, z: 1, color: COLORS.SILVER },
            { x: 3, y: 0, z: 1, color: COLORS.SILVER },
            { x: 0, y: 0, z: 2, color: COLORS.SILVER },
            { x: 3, y: 0, z: 2, color: COLORS.SILVER },
            { x: 1, y: 0, z: 3, color: COLORS.SILVER },
            { x: 2, y: 0, z: 3, color: COLORS.SILVER },
            
            // Crystal bottle body layer 1
            { x: 1, y: 1, z: 0, color: COLORS.SILVER },
            { x: 2, y: 1, z: 0, color: COLORS.SILVER },
            { x: 0, y: 1, z: 1, color: COLORS.SILVER },
            { x: 3, y: 1, z: 1, color: COLORS.SILVER },
            { x: 0, y: 1, z: 2, color: COLORS.SILVER },
            { x: 3, y: 1, z: 2, color: COLORS.SILVER },
            { x: 1, y: 1, z: 3, color: COLORS.SILVER },
            { x: 2, y: 1, z: 3, color: COLORS.SILVER },
            
            // Soul liquid layer 1 (glowing purple)
            { x: 1, y: 1, z: 1, color: COLORS.SOUL_POTION },
            { x: 2, y: 1, z: 1, color: COLORS.SOUL_POTION },
            { x: 1, y: 1, z: 2, color: COLORS.SOUL_POTION },
            { x: 2, y: 1, z: 2, color: COLORS.SOUL_POTION },
            
            // Crystal bottle body layer 2
            { x: 1, y: 2, z: 0, color: COLORS.SILVER },
            { x: 2, y: 2, z: 0, color: COLORS.SILVER },
            { x: 0, y: 2, z: 1, color: COLORS.SILVER },
            { x: 3, y: 2, z: 1, color: COLORS.SILVER },
            { x: 0, y: 2, z: 2, color: COLORS.SILVER },
            { x: 3, y: 2, z: 2, color: COLORS.SILVER },
            { x: 1, y: 2, z: 3, color: COLORS.SILVER },
            { x: 2, y: 2, z: 3, color: COLORS.SILVER },
            
            // Soul liquid layer 2 (brighter center)
            { x: 1, y: 2, z: 1, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 2, z: 1, color: COLORS.BALANCED_SOUL },
            { x: 1, y: 2, z: 2, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 2, z: 2, color: COLORS.BALANCED_SOUL },
            
            // Crystal bottle body layer 3
            { x: 1, y: 3, z: 0, color: COLORS.SILVER },
            { x: 2, y: 3, z: 0, color: COLORS.SILVER },
            { x: 0, y: 3, z: 1, color: COLORS.SILVER },
            { x: 3, y: 3, z: 1, color: COLORS.SILVER },
            { x: 0, y: 3, z: 2, color: COLORS.SILVER },
            { x: 3, y: 3, z: 2, color: COLORS.SILVER },
            { x: 1, y: 3, z: 3, color: COLORS.SILVER },
            { x: 2, y: 3, z: 3, color: COLORS.SILVER },
            
            // Soul liquid layer 3 (bright swirling effect)
            { x: 1, y: 3, z: 1, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 3, z: 1, color: COLORS.SOUL_POTION },
            { x: 1, y: 3, z: 2, color: COLORS.SOUL_POTION },
            { x: 2, y: 3, z: 2, color: COLORS.LIGHT_SOUL },
            
            // Bottle neck (narrower)
            { x: 1, y: 4, z: 1, color: COLORS.SILVER },
            { x: 2, y: 4, z: 1, color: COLORS.SILVER },
            { x: 1, y: 4, z: 2, color: COLORS.SILVER },
            { x: 2, y: 4, z: 2, color: COLORS.SILVER },
            
            // Cork/stopper (dark)
            { x: 1, y: 5, z: 1, color: COLORS.BLACK },
            { x: 2, y: 5, z: 1, color: COLORS.BLACK },
            { x: 1, y: 5, z: 2, color: COLORS.BLACK },
            { x: 2, y: 5, z: 2, color: COLORS.BLACK },
            
            // Mystical aura particles around bottle (small floating bits)
            { x: -1, y: 2, z: 1, color: COLORS.SOUL_POTION },
            { x: 4, y: 2, z: 2, color: COLORS.BALANCED_SOUL },
            { x: 1, y: 2, z: -1, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 3, z: 4, color: COLORS.SOUL_POTION },
            { x: 0, y: 4, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 3, y: 1, z: 4, color: COLORS.LIGHT_SOUL }
        ],
        scale: 0.08
    },
    
    soul_heart: {
        voxels: [
            // Heart shape
            { x: 1, y: 0, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 3, y: 0, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 0, y: 1, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 1, y: 1, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 1, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 3, y: 1, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 4, y: 1, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 0, y: 2, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 1, y: 2, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 2, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 3, y: 2, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 4, y: 2, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 1, y: 3, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 3, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 3, y: 3, z: 0, color: COLORS.BALANCED_SOUL },
            { x: 2, y: 4, z: 0, color: COLORS.BALANCED_SOUL }
        ],
        scale: 0.1
    },
    
    heart_container: {
        voxels: [
            // Heart shape
            { x: 1, y: 0, z: 0, color: COLORS.RED },
            { x: 3, y: 0, z: 0, color: COLORS.RED },
            { x: 0, y: 1, z: 0, color: COLORS.RED },
            { x: 1, y: 1, z: 0, color: COLORS.RED },
            { x: 2, y: 1, z: 0, color: COLORS.RED },
            { x: 3, y: 1, z: 0, color: COLORS.RED },
            { x: 4, y: 1, z: 0, color: COLORS.RED },
            { x: 0, y: 2, z: 0, color: COLORS.RED },
            { x: 1, y: 2, z: 0, color: COLORS.RED },
            { x: 2, y: 2, z: 0, color: COLORS.RED },
            { x: 3, y: 2, z: 0, color: COLORS.RED },
            { x: 4, y: 2, z: 0, color: COLORS.RED },
            { x: 1, y: 3, z: 0, color: COLORS.RED },
            { x: 2, y: 3, z: 0, color: COLORS.RED },
            { x: 3, y: 3, z: 0, color: COLORS.RED },
            { x: 2, y: 4, z: 0, color: COLORS.RED }
        ],
        scale: 0.1
    },
    
    // --- Utility Items ---
    key: {
        voxels: [
            // Key head
            { x: 0, y: 0, z: 0, color: COLORS.GOLD },
            { x: 1, y: 0, z: 0, color: COLORS.GOLD },
            { x: 2, y: 0, z: 0, color: COLORS.GOLD },
            { x: 0, y: 1, z: 0, color: COLORS.GOLD },
            { x: 2, y: 1, z: 0, color: COLORS.GOLD },
            { x: 0, y: 2, z: 0, color: COLORS.GOLD },
            { x: 1, y: 2, z: 0, color: COLORS.GOLD },
            { x: 2, y: 2, z: 0, color: COLORS.GOLD },
            // Key shaft
            { x: 1, y: 3, z: 0, color: COLORS.GOLD },
            { x: 1, y: 4, z: 0, color: COLORS.GOLD },
            { x: 1, y: 5, z: 0, color: COLORS.GOLD },
            // Key teeth
            { x: 2, y: 4, z: 0, color: COLORS.GOLD },
            { x: 0, y: 5, z: 0, color: COLORS.GOLD }
        ],
        scale: 0.1
    },
    
    soul_bomb: {
        voxels: [
            // Bomb body
            { x: 1, y: 0, z: 1, color: COLORS.BLACK },
            { x: 2, y: 0, z: 1, color: COLORS.BLACK },
            { x: 1, y: 0, z: 2, color: COLORS.BLACK },
            { x: 2, y: 0, z: 2, color: COLORS.BLACK },
            { x: 0, y: 1, z: 0, color: COLORS.BLACK },
            { x: 1, y: 1, z: 0, color: COLORS.BLACK },
            { x: 2, y: 1, z: 0, color: COLORS.BLACK },
            { x: 3, y: 1, z: 0, color: COLORS.BLACK },
            { x: 0, y: 1, z: 1, color: COLORS.BLACK },
            { x: 3, y: 1, z: 1, color: COLORS.BLACK },
            { x: 0, y: 1, z: 2, color: COLORS.BLACK },
            { x: 3, y: 1, z: 2, color: COLORS.BLACK },
            { x: 0, y: 1, z: 3, color: COLORS.BLACK },
            { x: 1, y: 1, z: 3, color: COLORS.BLACK },
            { x: 2, y: 1, z: 3, color: COLORS.BLACK },
            { x: 3, y: 1, z: 3, color: COLORS.BLACK },
            // Bomb fuse
            { x: 1, y: 2, z: 1, color: COLORS.GRAY },
            { x: 1, y: 3, z: 1, color: COLORS.GRAY },
            { x: 2, y: 3, z: 1, color: COLORS.RED }
        ],
        scale: 0.1
    },
    
    treasure_map: {
        voxels: [
            // Map base
            { x: 0, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 1, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 2, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 3, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 0, y: 0, z: 1, color: COLORS.BRONZE },
            { x: 1, y: 0, z: 1, color: COLORS.BRONZE },
            { x: 2, y: 0, z: 1, color: COLORS.BRONZE },
            { x: 3, y: 0, z: 1, color: COLORS.BRONZE },
            { x: 0, y: 0, z: 2, color: COLORS.BRONZE },
            { x: 1, y: 0, z: 2, color: COLORS.BRONZE },
            { x: 2, y: 0, z: 2, color: COLORS.BRONZE },
            { x: 3, y: 0, z: 2, color: COLORS.BRONZE },
            { x: 0, y: 0, z: 3, color: COLORS.BRONZE },
            { x: 1, y: 0, z: 3, color: COLORS.BRONZE },
            { x: 2, y: 0, z: 3, color: COLORS.BRONZE },
            { x: 3, y: 0, z: 3, color: COLORS.BRONZE },
            // Map details
            { x: 1, y: 1, z: 1, color: COLORS.RED },
            { x: 2, y: 1, z: 2, color: COLORS.RED }
        ],
        scale: 0.1
    },
    
    // --- Weapons/Attack Modifiers ---
    soul_blade: {
        voxels: [
            // Blade
            { x: 1, y: 2, z: 0, color: COLORS.SILVER },
            { x: 1, y: 3, z: 0, color: COLORS.SILVER },
            { x: 1, y: 4, z: 0, color: COLORS.SILVER },
            { x: 1, y: 5, z: 0, color: COLORS.SILVER },
            { x: 1, y: 6, z: 0, color: COLORS.SILVER },
            // Hilt
            { x: 0, y: 1, z: 0, color: COLORS.GOLD },
            { x: 1, y: 1, z: 0, color: COLORS.GOLD },
            { x: 2, y: 1, z: 0, color: COLORS.GOLD },
            { x: 1, y: 0, z: 0, color: COLORS.DARK_SOUL }
        ],
        scale: 0.1
    },
    
    spectral_bow: {
        voxels: [
            // Bow frame
            { x: 1, y: 0, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 1, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 2, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 3, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 1, y: 4, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 3, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 2, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 1, z: 0, color: COLORS.LIGHT_SOUL },
            // Bow string
            { x: 1, y: 1, z: 0, color: COLORS.WHITE },
            { x: 1, y: 2, z: 0, color: COLORS.WHITE },
            { x: 1, y: 3, z: 0, color: COLORS.WHITE }
        ],
        scale: 0.1
    },
    
    frost_wand: {
        voxels: [
            // Wand shaft
            { x: 1, y: 0, z: 0, color: COLORS.SILVER },
            { x: 1, y: 1, z: 0, color: COLORS.SILVER },
            { x: 1, y: 2, z: 0, color: COLORS.SILVER },
            { x: 1, y: 3, z: 0, color: COLORS.SILVER },
            // Wand tip
            { x: 0, y: 4, z: 0, color: COLORS.CYAN },
            { x: 1, y: 4, z: 0, color: COLORS.CYAN },
            { x: 2, y: 4, z: 0, color: COLORS.CYAN },
            { x: 1, y: 5, z: 0, color: COLORS.CYAN }
        ],
        scale: 0.1
    },
    
    // --- Relics ---
    zeituhr: {
        voxels: [
            // Clock face
            { x: 1, y: 1, z: 0, color: COLORS.GOLD },
            { x: 2, y: 1, z: 0, color: COLORS.GOLD },
            { x: 3, y: 1, z: 0, color: COLORS.GOLD },
            { x: 1, y: 2, z: 0, color: COLORS.GOLD },
            { x: 2, y: 2, z: 0, color: COLORS.WHITE },
            { x: 3, y: 2, z: 0, color: COLORS.GOLD },
            { x: 1, y: 3, z: 0, color: COLORS.GOLD },
            { x: 2, y: 3, z: 0, color: COLORS.GOLD },
            { x: 3, y: 3, z: 0, color: COLORS.GOLD },
            // Clock hands
            { x: 2, y: 2, z: 1, color: COLORS.BLACK }
        ],
        scale: 0.1
    },
    
    golden_mirror: {
        voxels: [
            // Mirror frame
            { x: 0, y: 0, z: 0, color: COLORS.GOLD },
            { x: 1, y: 0, z: 0, color: COLORS.GOLD },
            { x: 2, y: 0, z: 0, color: COLORS.GOLD },
            { x: 3, y: 0, z: 0, color: COLORS.GOLD },
            { x: 0, y: 1, z: 0, color: COLORS.GOLD },
            { x: 3, y: 1, z: 0, color: COLORS.GOLD },
            { x: 0, y: 2, z: 0, color: COLORS.GOLD },
            { x: 3, y: 2, z: 0, color: COLORS.GOLD },
            { x: 0, y: 3, z: 0, color: COLORS.GOLD },
            { x: 1, y: 3, z: 0, color: COLORS.GOLD },
            { x: 2, y: 3, z: 0, color: COLORS.GOLD },
            { x: 3, y: 3, z: 0, color: COLORS.GOLD },
            // Mirror surface
            { x: 1, y: 1, z: 0, color: COLORS.CYAN },
            { x: 2, y: 1, z: 0, color: COLORS.CYAN },
            { x: 1, y: 2, z: 0, color: COLORS.CYAN },
            { x: 2, y: 2, z: 0, color: COLORS.CYAN }
        ],
        scale: 0.1
    },
    
    family_photo: {
        voxels: [
            // Photo frame
            { x: 0, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 1, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 2, y: 0, z: 0, color: COLORS.BRONZE },
            { x: 0, y: 1, z: 0, color: COLORS.BRONZE },
            { x: 2, y: 1, z: 0, color: COLORS.BRONZE },
            { x: 0, y: 2, z: 0, color: COLORS.BRONZE },
            { x: 1, y: 2, z: 0, color: COLORS.BRONZE },
            { x: 2, y: 2, z: 0, color: COLORS.BRONZE },
            // Photo
            { x: 1, y: 1, z: 0, color: COLORS.WHITE }
        ],
        scale: 0.1
    },
    
    // --- Soul Items ---
    light_essence: {
        voxels: [
            // Essence core
            { x: 1, y: 1, z: 1, color: COLORS.LIGHT_SOUL },
            // Essence particles
            { x: 0, y: 0, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 0, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 0, z: 2, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 0, z: 2, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 2, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 2, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 2, z: 2, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 2, z: 2, color: COLORS.LIGHT_SOUL }
        ],
        scale: 0.1
    },
    
    dark_essence: {
        voxels: [
            // Essence core
            { x: 1, y: 1, z: 1, color: COLORS.DARK_SOUL },
            // Essence particles
            { x: 0, y: 0, z: 0, color: COLORS.DARK_SOUL },
            { x: 2, y: 0, z: 0, color: COLORS.DARK_SOUL },
            { x: 0, y: 0, z: 2, color: COLORS.DARK_SOUL },
            { x: 2, y: 0, z: 2, color: COLORS.DARK_SOUL },
            { x: 0, y: 2, z: 0, color: COLORS.DARK_SOUL },
            { x: 2, y: 2, z: 0, color: COLORS.DARK_SOUL },
            { x: 0, y: 2, z: 2, color: COLORS.DARK_SOUL },
            { x: 2, y: 2, z: 2, color: COLORS.DARK_SOUL }
        ],
        scale: 0.1
    },
    
    balanced_essence: {
        voxels: [
            // Essence core
            { x: 1, y: 1, z: 1, color: COLORS.BALANCED_SOUL },
            // Essence particles
            { x: 0, y: 0, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 0, z: 0, color: COLORS.DARK_SOUL },
            { x: 0, y: 0, z: 2, color: COLORS.DARK_SOUL },
            { x: 2, y: 0, z: 2, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 2, z: 0, color: COLORS.DARK_SOUL },
            { x: 2, y: 2, z: 0, color: COLORS.LIGHT_SOUL },
            { x: 0, y: 2, z: 2, color: COLORS.LIGHT_SOUL },
            { x: 2, y: 2, z: 2, color: COLORS.DARK_SOUL }
        ],
        scale: 0.1
    }
};

/**
 * Create a voxel model for an item
 * @param {string} modelName - The name of the model to create
 * @param {string} rarity - The rarity of the item
 * @returns {THREE.Group} The item mesh
 */
export function createVoxelItemModel(modelName, rarity) {
    // Get the model definition
    const modelDef = VOXEL_MODELS[modelName];
    if (!modelDef) {
        console.error(`Voxel model "${modelName}" not found!`);
        return null;
    }
    
    // Create a group to hold all voxels
    const group = new THREE.Group();
    
    // Create a material for each color
    const materials = {};
    
    // Create a geometry for each voxel
    const voxelSize = VOXEL_SIZE * (modelDef.scale || 0.1);
    const halfVoxel = voxelSize / 2;
    
    // Calculate center offset to center the model
    const minX = Math.min(...modelDef.voxels.map(v => v.x));
    const maxX = Math.max(...modelDef.voxels.map(v => v.x));
    const minY = Math.min(...modelDef.voxels.map(v => v.y));
    const maxY = Math.max(...modelDef.voxels.map(v => v.y));
    const minZ = Math.min(...modelDef.voxels.map(v => v.z));
    const maxZ = Math.max(...modelDef.voxels.map(v => v.z));
    
    const centerX = (minX + maxX) / 2;
    const centerY = (minY + maxY) / 2;
    const centerZ = (minZ + maxZ) / 2;
    
    // Create a mesh for each voxel
    for (const voxel of modelDef.voxels) {
        // Get or create material for this color
        if (!materials[voxel.color]) {
            materials[voxel.color] = new THREE.MeshStandardMaterial({
                color: voxel.color,
                roughness: 0.7,
                metalness: 0.3
            });
        }
        
        // Create box geometry
        const geometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
        
        // Create mesh
        const mesh = new THREE.Mesh(geometry, materials[voxel.color]);
        
        // Position relative to center
        mesh.position.set(
            (voxel.x - centerX) * voxelSize,
            (voxel.y - centerY) * voxelSize,
            (voxel.z - centerZ) * voxelSize
        );
        
        // Add to group
        group.add(mesh);
    }
    
    // Add rarity glow effect
    if (rarity && RARITY_GLOW[rarity]) {
        const { color, intensity } = RARITY_GLOW[rarity];
        
        // Make materials emissive
        Object.values(materials).forEach(material => {
            material.emissive = new THREE.Color(color);
            material.emissiveIntensity = intensity * 0.3;
        });
    }
    
    return group;
}

/**
 * Create a simple fallback model for items without a defined model
 * @param {string} rarity - The rarity of the item
 * @returns {THREE.Mesh} A simple mesh representing the item
 */
export function createFallbackItemModel(rarity) {
    const geometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
    const material = new THREE.MeshStandardMaterial({
        color: RARITY_COLORS[rarity] || 0xffffff,
        roughness: 0.7,
        metalness: 0.3
    });
    
    // Add rarity glow effect
    if (rarity && RARITY_GLOW[rarity]) {
        const { color, intensity } = RARITY_GLOW[rarity];
        material.emissive = new THREE.Color(color);
        material.emissiveIntensity = intensity * 0.3;
    }
    
    return new THREE.Mesh(geometry, material);
}
