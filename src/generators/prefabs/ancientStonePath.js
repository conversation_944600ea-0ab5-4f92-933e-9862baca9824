import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Ancient Stone Path
 *
 * Creates a weathered stone path with worn flagstones leading to the doors.
 * Uses the same faded yellowish stone palette as the room but with path-specific variations.
 */

// Ancient path stone materials - weathered flagstones with worn edges
const PATH_STONE_MATERIALS = [
    _getMaterialByHex_Cached('A8A893'), // Light worn stone (primary path stones)
    _getMaterialByHex_Cached('959587'), // Medium worn stone (secondary)
    _getMaterialByHex_Cached('C0C0AB'), // Very light worn stone (center worn areas)
    _getMaterialByHex_Cached('7A7A6E'), // Darker stone (edges and joints)
    _getMaterialByHex_Cached('8B8B7F'), // Medium dark stone (weathered areas)
    _getMaterialByHex_Cached('6A6A5E'), // Dark stone (deep joints between stones)
];

/**
 * Get random path stone material
 */
function getRandomPathStoneMat() {
    const rand = Math.random();
    if (rand < 0.35) return PATH_STONE_MATERIALS[0]; // Primary light worn stone
    if (rand < 0.60) return PATH_STONE_MATERIALS[1]; // Medium worn stone
    if (rand < 0.75) return PATH_STONE_MATERIALS[2]; // Very light worn areas
    if (rand < 0.85) return PATH_STONE_MATERIALS[4]; // Medium dark weathered
    if (rand < 0.95) return PATH_STONE_MATERIALS[3]; // Darker edges
    return PATH_STONE_MATERIALS[5]; // Deep joints
}

/**
 * Creates an ancient stone path with flagstone pattern
 * @param {object} options Options object with width, depth, etc.
 * @returns {THREE.Group} The merged path group.
 */
export function createAncientStonePath(options = {}) {
    const width = options.width || 6;
    const depth = options.depth || 18;
    const roomData = options.roomData || {};
    console.log(`[AncientStonePath] Creating path: ${width}x${depth}`);

    const geometriesByMaterial = {};
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use environment scale for path coverage
    const PATH_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5; // Slightly larger for path stones

    // Grid dimensions
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / PATH_SCALE);
    const numZ_env = Math.ceil(numZ / PATH_SCALE);

    const pathY = VOXEL_SIZE * 0.05; // Slightly raised above base floor
    
    // Path stone sizing
    const pathVoxelSize = VOXEL_SIZE * PATH_SCALE;
    const pathVoxelHeight = VOXEL_SIZE * 0.4; // Thinner flagstones

    // Centering offsets
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    
    const pathStoneGeo = getOrCreateGeometry(
        `path_stone_${pathVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(pathVoxelSize, pathVoxelHeight, pathVoxelSize)
    );

    // Use seeded PRNG for consistent patterns
    const roomSeed = roomData ? roomData.id * 41 + 23 : Date.now();
    const random = mulberry32(roomSeed);

    let voxelsAdded = 0;

    // --- Generate Flagstone Path Pattern ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            const baseX = ex * pathVoxelSize - offsetX;
            const baseZ = ez * pathVoxelSize - offsetZ;

            // Create flagstone pattern with gaps between stones
            const isJoint = (ex % 3 === 2 && random() < 0.7) || (ez % 2 === 1 && random() < 0.5);
            
            if (!isJoint) {
                const stoneMaterial = getRandomPathStoneMat();

                // Add wear patterns - more wear in center of path
                const distanceFromCenter = Math.abs(baseX) / (width / 2);
                let wearFactor = 1 - distanceFromCenter; // More wear in center
                
                // Choose more worn materials for center path
                let finalMaterial = stoneMaterial;
                if (wearFactor > 0.7 && random() < 0.6) {
                    finalMaterial = PATH_STONE_MATERIALS[2]; // Very light worn stone
                }

                // Add slight height variation for worn flagstones
                const wearDepth = -wearFactor * random() * 0.02; // Worn stones slightly lower
                const heightVariation = (random() - 0.5) * 0.01; // Small random variation
                const finalY = pathY + wearDepth + heightVariation;

                // Add slight rotation for aged flagstones
                const rotationY = (random() - 0.5) * 0.03;

                tempMatrix.makeTranslation(baseX, finalY, baseZ);
                tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                addGeometry(pathStoneGeo, finalMaterial, tempMatrix);
                voxelsAdded++;
            }
        }
    }

    // --- Add Path Borders (darker stones along edges) ---
    const borderMaterial = PATH_STONE_MATERIALS[3]; // Darker stone for borders
    
    for (let ez = 0; ez < numZ_env; ez++) {
        // Left border
        if (random() < 0.8) {
            const baseX = -offsetX;
            const baseZ = ez * pathVoxelSize - offsetZ;
            
            tempMatrix.makeTranslation(baseX - pathVoxelSize * 0.8, pathY, baseZ);
            addGeometry(pathStoneGeo, borderMaterial, tempMatrix);
            voxelsAdded++;
        }
        
        // Right border
        if (random() < 0.8) {
            const baseX = offsetX;
            const baseZ = ez * pathVoxelSize - offsetZ;
            
            tempMatrix.makeTranslation(baseX + pathVoxelSize * 0.8, pathY, baseZ);
            addGeometry(pathStoneGeo, borderMaterial, tempMatrix);
            voxelsAdded++;
        }
    }

    console.log(`[AncientStonePath] Generated ${voxelsAdded} path stones`);

    // --- Merge Geometries by Material ---
    const finalGroup = new THREE.Group();

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for ancient stone path: ${colorHex}`);
            continue;
        }

        // Clone material to apply settings without modifying shared instance
        const finalMaterial = originalMaterial.clone();
        
        // Apply polygon offset to prevent z-fighting with floor
        finalMaterial.polygonOffset = true;
        finalMaterial.polygonOffsetFactor = -2.0;
        finalMaterial.polygonOffsetUnits = -2.0;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.castShadow = false; // Paths don't cast shadows
            mesh.receiveShadow = true; // But they receive shadows
            mesh.name = 'ancientStonePath';
            mesh.userData.isPath = true;
            mesh.userData.pathType = 'ancient_stone_path';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for ancient stone path, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.pathType = 'ancient_stone_path';
    finalGroup.userData.isPath = true;
    finalGroup.name = 'ancientStonePath';

    // Set shadow properties for entire group
    finalGroup.castShadow = false;
    finalGroup.receiveShadow = true;

    console.log(`[AncientStonePath] ✅ Generated ancient stone path: ${voxelsAdded} stones, ${Object.keys(geometriesByMaterial).length} materials`);

    return finalGroup;
}