import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a hellish torch with dark metal and evil flames
 * These torches flicker in slow, unnatural rhythms
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Hellish torch object group
 */
export function createHellishTorchObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 666);

    // Use standard voxel size for torches
    const torchVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('hellish_torch_voxel', () =>
        new THREE.BoxGeometry(torchVoxelSize, torchVoxelSize, torchVoxelSize)
    );

    // Hellish torch materials
    const darkMetalMaterial = _getMaterialByHex_Cached('2A2A2A', {
        roughness: 0.8,
        metalness: 0.7,
        emissive: new THREE.Color(0x0A0A0A),
        emissiveIntensity: 0.02
    }); // Dark metal handle

    const hellishFlameMaterial = _getMaterialByHex_Cached('8B0000', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x8B0000),
        emissiveIntensity: 0.6,
        roughness: 0.1,
        metalness: 0.1
    }); // Dark red hellish flame

    const brightFlameMaterial = _getMaterialByHex_Cached('FF4500', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0xFF4500),
        emissiveIntensity: 0.8,
        roughness: 0.0,
        metalness: 0.0
    }); // Bright orange flame core

    const shadowFlameMaterial = _getMaterialByHex_Cached('4A0000', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x4A0000),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.0
    }); // Dark shadow flames

    // Torch handle (vertical post)
    const handlePositions = [
        { x: 0, y: 0, z: 0 },
        { x: 0, y: 1, z: 0 },
        { x: 0, y: 2, z: 0 },
        { x: 0, y: 3, z: 0 },
        { x: 0, y: 4, z: 0 },
        { x: 0, y: 5, z: 0 }
    ];

    // Add torch handle
    handlePositions.forEach(pos => {
        const handle = new THREE.Mesh(baseGeometry.clone(), darkMetalMaterial);
        handle.position.set(
            pos.x * torchVoxelSize,
            pos.y * torchVoxelSize,
            pos.z * torchVoxelSize
        );
        handle.castShadow = true;
        handle.receiveShadow = true;
        group.add(handle);
    });

    // Torch bowl/brazier (at top of handle)
    const brazierPositions = [
        { x: -1, y: 5, z: 0 }, { x: 0, y: 5, z: 0 }, { x: 1, y: 5, z: 0 },
        { x: 0, y: 5, z: -1 }, { x: 0, y: 5, z: 1 },
        { x: -1, y: 6, z: 0 }, { x: 1, y: 6, z: 0 },
        { x: 0, y: 6, z: -1 }, { x: 0, y: 6, z: 1 }
    ];

    brazierPositions.forEach(pos => {
        const brazier = new THREE.Mesh(baseGeometry.clone(), darkMetalMaterial);
        brazier.position.set(
            pos.x * torchVoxelSize,
            pos.y * torchVoxelSize,
            pos.z * torchVoxelSize
        );
        brazier.castShadow = true;
        brazier.receiveShadow = true;
        group.add(brazier);
    });

    // Hellish flame layers (multiple heights for complex flame)
    const flameLayer1 = [
        // Base flame layer (wider)
        { x: -1, y: 6, z: -1 }, { x: 0, y: 6, z: -1 }, { x: 1, y: 6, z: -1 },
        { x: -1, y: 6, z: 0 },  { x: 0, y: 6, z: 0 },  { x: 1, y: 6, z: 0 },
        { x: -1, y: 6, z: 1 },  { x: 0, y: 6, z: 1 },  { x: 1, y: 6, z: 1 }
    ];

    const flameLayer2 = [
        // Mid flame layer (narrower)
        { x: -1, y: 7, z: 0 }, { x: 0, y: 7, z: 0 }, { x: 1, y: 7, z: 0 },
        { x: 0, y: 7, z: -1 }, { x: 0, y: 7, z: 1 },
        { x: 0, y: 8, z: 0 }
    ];

    const flameLayer3 = [
        // Top flame layer (bright core)
        { x: 0, y: 9, z: 0 },
        { x: 0, y: 10, z: 0 }
    ];

    // Add flame layers with different materials
    flameLayer1.forEach(pos => {
        const flame = new THREE.Mesh(baseGeometry.clone(), hellishFlameMaterial);
        flame.position.set(
            pos.x * torchVoxelSize,
            pos.y * torchVoxelSize,
            pos.z * torchVoxelSize
        );
        flame.userData.flameLayer = 1;
        flame.userData.baseIntensity = 0.6;
        flame.userData.isFlickering = true;
        flame.castShadow = false;
        flame.receiveShadow = false;
        group.add(flame);
    });

    flameLayer2.forEach(pos => {
        const flame = new THREE.Mesh(baseGeometry.clone(), brightFlameMaterial);
        flame.position.set(
            pos.x * torchVoxelSize,
            pos.y * torchVoxelSize,
            pos.z * torchVoxelSize
        );
        flame.userData.flameLayer = 2;
        flame.userData.baseIntensity = 0.8;
        flame.userData.isFlickering = true;
        flame.castShadow = false;
        flame.receiveShadow = false;
        group.add(flame);
    });

    flameLayer3.forEach(pos => {
        const flame = new THREE.Mesh(baseGeometry.clone(), brightFlameMaterial);
        flame.position.set(
            pos.x * torchVoxelSize,
            pos.y * torchVoxelSize,
            pos.z * torchVoxelSize
        );
        flame.userData.flameLayer = 3;
        flame.userData.baseIntensity = 1.0;
        flame.userData.isFlickering = true;
        flame.scale.set(0.8, 1.2, 0.8); // Taller and narrower for top flame
        flame.castShadow = false;
        flame.receiveShadow = false;
        group.add(flame);
    });

    // Add shadow flames around base for ominous effect
    const shadowFlames = [
        { x: -2, y: 6, z: 0 }, { x: 2, y: 6, z: 0 },
        { x: 0, y: 6, z: -2 }, { x: 0, y: 6, z: 2 },
        { x: -1, y: 7, z: -1 }, { x: 1, y: 7, z: -1 },
        { x: -1, y: 7, z: 1 }, { x: 1, y: 7, z: 1 }
    ];

    shadowFlames.forEach(pos => {
        if (rng() > 0.4) { // Only 60% chance for each shadow flame
            const shadowFlame = new THREE.Mesh(baseGeometry.clone(), shadowFlameMaterial);
            shadowFlame.position.set(
                pos.x * torchVoxelSize,
                pos.y * torchVoxelSize,
                pos.z * torchVoxelSize
            );
            shadowFlame.userData.flameLayer = 0; // Shadow layer
            shadowFlame.userData.baseIntensity = 0.4;
            shadowFlame.userData.isFlickering = true;
            shadowFlame.scale.set(0.6, 0.8, 0.6); // Smaller shadow flames
            shadowFlame.castShadow = false;
            shadowFlame.receiveShadow = false;
            group.add(shadowFlame);
        }
    });

    // Create point light for the hellish torch
    const torchLight = new THREE.PointLight(0xFF4500, 2.0, 12, 2);
    torchLight.position.set(0, 8 * torchVoxelSize, 0);
    torchLight.castShadow = true;
    torchLight.shadow.mapSize.width = 512;
    torchLight.shadow.mapSize.height = 512;
    torchLight.shadow.camera.near = 0.1;
    torchLight.shadow.camera.far = 15;
    torchLight.userData.originalIntensity = 2.0;
    torchLight.userData.isFlickering = true;
    group.add(torchLight);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'hellish_torch',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: torchVoxelSize,
        hasLight: true,
        hasFlameAnimation: true,
        torchLight: torchLight
    };

    group.name = 'hellish_torch';
    console.log('[HellishTorch] ✅ Created flickering hellish torch');
    return group;
}