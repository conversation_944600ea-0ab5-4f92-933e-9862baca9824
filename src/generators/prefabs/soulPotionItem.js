import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Soul Potion Item Prefab
 * Creates a beautiful mystical potion bottle with glowing soul energy
 */

// Soul potion specific colors
const SOUL_COLORS = {
    CRYSTAL: 0xc0c0c0,      // Silver crystal bottle
    SOUL_LIQUID: 0x9966ff,   // Purple soul liquid
    LIGHT_SOUL: 0xeeeeff,    // Bright soul energy
    BALANCED_SOUL: 0x9966ff, // Balanced soul energy
    CORK: 0x2d1810,          // Dark cork
    AURA: 0xaa77ff           // Mystical aura
};

/**
 * Create a soul potion item
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The soul potion 3D model
 */
export function createSoulPotionItem(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const potionGroup = new THREE.Group();
    potionGroup.name = 'SoulPotionItem';

    // Materials
    const crystalMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.CRYSTAL,
        transparent: true,
        opacity: 0.8
    });

    const soulLiquidMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.SOUL_LIQUID,
        emissive: SOUL_COLORS.SOUL_LIQUID,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const lightSoulMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.LIGHT_SOUL,
        emissive: SOUL_COLORS.LIGHT_SOUL,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const balancedSoulMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.BALANCED_SOUL,
        emissive: SOUL_COLORS.BALANCED_SOUL,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const corkMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.CORK
    });

    const auraMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.AURA,
        emissive: SOUL_COLORS.AURA,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.4
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Crystal bottle structure
    const bottleVoxels = [
        // Base layer (hexagonal pattern)
        { x: 1, y: 0, z: 0, material: crystalMaterial },
        { x: 2, y: 0, z: 0, material: crystalMaterial },
        { x: 0, y: 0, z: 1, material: crystalMaterial },
        { x: 3, y: 0, z: 1, material: crystalMaterial },
        { x: 0, y: 0, z: 2, material: crystalMaterial },
        { x: 3, y: 0, z: 2, material: crystalMaterial },
        { x: 1, y: 0, z: 3, material: crystalMaterial },
        { x: 2, y: 0, z: 3, material: crystalMaterial },

        // Body layers 1-3 (crystal walls)
        ...generateBottleWalls(1, crystalMaterial),
        ...generateBottleWalls(2, crystalMaterial),
        ...generateBottleWalls(3, crystalMaterial),

        // Neck
        { x: 1, y: 4, z: 1, material: crystalMaterial },
        { x: 2, y: 4, z: 1, material: crystalMaterial },
        { x: 1, y: 4, z: 2, material: crystalMaterial },
        { x: 2, y: 4, z: 2, material: crystalMaterial },

        // Cork
        { x: 1, y: 5, z: 1, material: corkMaterial },
        { x: 2, y: 5, z: 1, material: corkMaterial },
        { x: 1, y: 5, z: 2, material: corkMaterial },
        { x: 2, y: 5, z: 2, material: corkMaterial }
    ];

    // Soul liquid layers
    const liquidVoxels = [
        // Layer 1 - base liquid
        { x: 1, y: 1, z: 1, material: soulLiquidMaterial },
        { x: 2, y: 1, z: 1, material: soulLiquidMaterial },
        { x: 1, y: 1, z: 2, material: soulLiquidMaterial },
        { x: 2, y: 1, z: 2, material: soulLiquidMaterial },

        // Layer 2 - brighter center
        { x: 1, y: 2, z: 1, material: balancedSoulMaterial },
        { x: 2, y: 2, z: 1, material: balancedSoulMaterial },
        { x: 1, y: 2, z: 2, material: balancedSoulMaterial },
        { x: 2, y: 2, z: 2, material: balancedSoulMaterial },

        // Layer 3 - swirling effect
        { x: 1, y: 3, z: 1, material: lightSoulMaterial },
        { x: 2, y: 3, z: 1, material: soulLiquidMaterial },
        { x: 1, y: 3, z: 2, material: soulLiquidMaterial },
        { x: 2, y: 3, z: 2, material: lightSoulMaterial }
    ];

    // Mystical aura particles
    const auraVoxels = [
        { x: -1, y: 2, z: 1, material: auraMaterial },
        { x: 4, y: 2, z: 2, material: auraMaterial },
        { x: 1, y: 2, z: -1, material: auraMaterial },
        { x: 2, y: 3, z: 4, material: auraMaterial },
        { x: 0, y: 4, z: 0, material: auraMaterial },
        { x: 3, y: 1, z: 4, material: auraMaterial }
    ];

    // Create all voxels
    [...bottleVoxels, ...liquidVoxels, ...auraVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            (voxel.x - 1.5) * VOXEL_SIZE,
            voxel.y * VOXEL_SIZE,
            (voxel.z - 1.5) * VOXEL_SIZE
        );
        potionGroup.add(mesh);
    });

    // Add mystical glow light
    const glowLight = new THREE.PointLight(SOUL_COLORS.SOUL_LIQUID, 0.8, 2.0);
    glowLight.position.set(0, VOXEL_SIZE * 2, 0);
    potionGroup.add(glowLight);

    // Store animation properties
    potionGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        glowIntensity: 0.8,
        originalY: position.y
    };

    // Position and scale the potion
    potionGroup.position.set(position.x, position.y, position.z);
    potionGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    potionGroup.scale.setScalar(scale);

    return potionGroup;
}

/**
 * Generate bottle wall voxels for a given layer
 * @param {number} layer - Y layer (1, 2, or 3)
 * @param {THREE.Material} material - Material to use
 * @returns {Array} Array of voxel definitions
 */
function generateBottleWalls(layer, material) {
    return [
        { x: 1, y: layer, z: 0, material },
        { x: 2, y: layer, z: 0, material },
        { x: 0, y: layer, z: 1, material },
        { x: 3, y: layer, z: 1, material },
        { x: 0, y: layer, z: 2, material },
        { x: 3, y: layer, z: 2, material },
        { x: 1, y: layer, z: 3, material },
        { x: 2, y: layer, z: 3, material }
    ];
}

/**
 * Update soul potion animation
 * @param {THREE.Group} potionGroup - The potion group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSoulPotionAnimation(potionGroup, deltaTime) {
    if (!potionGroup.userData.isAnimated) return;

    potionGroup.userData.animationTime += deltaTime;
    const time = potionGroup.userData.animationTime;

    // Gentle floating motion
    const floatHeight = Math.sin(time * 2) * 0.1;
    potionGroup.position.y = potionGroup.userData.originalY + floatHeight;

    // Rotate slowly
    potionGroup.rotation.y += deltaTime * 0.5;

    // Pulsing glow effect
    const glowPulse = 0.5 + Math.sin(time * 3) * 0.3;
    
    // Update emissive intensity for liquid materials
    potionGroup.traverse((child) => {
        if (child.isMesh && child.material) {
            if (child.material.emissive && child.material.emissive.r > 0.5) {
                child.material.emissiveIntensity = glowPulse;
            }
        }
    });

    // Update point light intensity
    const light = potionGroup.children.find(child => child.isLight);
    if (light) {
        light.intensity = potionGroup.userData.glowIntensity * glowPulse;
    }
}

// Export the soul potion data for the loot system
export const SOUL_POTION_DATA = {
    name: 'Soul Potion',
    description: 'A mystical elixir that channels the power of souls to restore 6 health points. The liquid glows with ethereal energy.',
    category: 'consumable',
    rarity: 'common',
    effect: 'heal',
    effectValue: 6,
    createFunction: createSoulPotionItem,
    updateFunction: updateSoulPotionAnimation,
    voxelModel: 'soul_potion',
    glow: {
        color: 0x9966ff,
        intensity: 1.5
    }
};

export default createSoulPotionItem;