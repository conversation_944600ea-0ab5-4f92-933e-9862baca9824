import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Soul Mirror Card Prefab
 * Creates a reflective mirror that reveals and reflects damage
 */

// Soul mirror specific colors
const MIRROR_COLORS = {
    MIRROR_SILVER: 0xC0C0C0,     // Reflective silver
    SOUL_BLUE: 0x4169E1,         // Soul energy blue
    REFLECTION_WHITE: 0xFFFFFF,   // Pure reflection white
    SPIRIT_CYAN: 0x00FFFF,       // Spirit cyan
    MYSTIC_PURPLE: 0x9932CC,     // Mystic purple
    ASTRAL_GOLD: 0xFFD700,       // Astral gold
    ETHEREAL_PINK: 0xFF69B4      // Ethereal pink
};

/**
 * Create a soul mirror card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The soul mirror card 3D model
 */
export function createSoulMirrorCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SoulMirrorCard';

    // Materials
    const mirrorSilverMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.MIRROR_SILVER,
        emissive: MIRROR_COLORS.MIRROR_SILVER,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const soulBlueMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.SOUL_BLUE,
        emissive: MIRROR_COLORS.SOUL_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const reflectionWhiteMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.REFLECTION_WHITE,
        emissive: MIRROR_COLORS.REFLECTION_WHITE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.8
    });

    const spiritCyanMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.SPIRIT_CYAN,
        emissive: MIRROR_COLORS.SPIRIT_CYAN,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.6
    });

    const mysticPurpleMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.MYSTIC_PURPLE,
        emissive: MIRROR_COLORS.MYSTIC_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const astralGoldMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.ASTRAL_GOLD,
        emissive: MIRROR_COLORS.ASTRAL_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const etherealPinkMaterial = new THREE.MeshLambertMaterial({
        color: MIRROR_COLORS.ETHEREAL_PINK,
        emissive: MIRROR_COLORS.ETHEREAL_PINK,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create mirror surface
    const mirrorSurfaceVoxels = [
        // Central mirror frame
        { x: 0, y: 4, z: 0, material: mirrorSilverMaterial },
        { x: 2, y: 4, z: 0, material: mirrorSilverMaterial },
        { x: 4, y: 2, z: 0, material: mirrorSilverMaterial },
        { x: 4, y: 0, z: 0, material: mirrorSilverMaterial },
        { x: 4, y: -2, z: 0, material: mirrorSilverMaterial },
        { x: 2, y: -4, z: 0, material: mirrorSilverMaterial },
        { x: 0, y: -4, z: 0, material: mirrorSilverMaterial },
        { x: -2, y: -4, z: 0, material: mirrorSilverMaterial },
        { x: -4, y: -2, z: 0, material: mirrorSilverMaterial },
        { x: -4, y: 0, z: 0, material: mirrorSilverMaterial },
        { x: -4, y: 2, z: 0, material: mirrorSilverMaterial },
        { x: -2, y: 4, z: 0, material: mirrorSilverMaterial },
        
        // Mirror surface reflection
        { x: 0, y: 0, z: 0, material: reflectionWhiteMaterial },
        { x: 0, y: 2, z: 0, material: reflectionWhiteMaterial },
        { x: 0, y: -2, z: 0, material: reflectionWhiteMaterial },
        { x: 2, y: 0, z: 0, material: reflectionWhiteMaterial },
        { x: -2, y: 0, z: 0, material: reflectionWhiteMaterial },
        { x: 1, y: 1, z: 0, material: spiritCyanMaterial },
        { x: -1, y: 1, z: 0, material: spiritCyanMaterial },
        { x: 1, y: -1, z: 0, material: spiritCyanMaterial },
        { x: -1, y: -1, z: 0, material: spiritCyanMaterial },
        
        // Inner mirror details
        { x: 0, y: 3, z: 0, material: soulBlueMaterial },
        { x: 3, y: 0, z: 0, material: soulBlueMaterial },
        { x: 0, y: -3, z: 0, material: soulBlueMaterial },
        { x: -3, y: 0, z: 0, material: soulBlueMaterial }
    ];

    // Create soul reflections
    const soulReflectionVoxels = [
        // Ghostly reflections around mirror
        { x: -5, y: 3, z: 0, material: etherealPinkMaterial },
        { x: -5, y: 1, z: 0, material: spiritCyanMaterial },
        { x: -5, y: -1, z: 0, material: etherealPinkMaterial },
        { x: -5, y: -3, z: 0, material: spiritCyanMaterial },
        { x: 5, y: 3, z: 0, material: etherealPinkMaterial },
        { x: 5, y: 1, z: 0, material: spiritCyanMaterial },
        { x: 5, y: -1, z: 0, material: etherealPinkMaterial },
        { x: 5, y: -3, z: 0, material: spiritCyanMaterial },
        { x: 3, y: 5, z: 0, material: etherealPinkMaterial },
        { x: 1, y: 5, z: 0, material: spiritCyanMaterial },
        { x: -1, y: 5, z: 0, material: etherealPinkMaterial },
        { x: -3, y: 5, z: 0, material: spiritCyanMaterial },
        { x: 3, y: -5, z: 0, material: etherealPinkMaterial },
        { x: 1, y: -5, z: 0, material: spiritCyanMaterial },
        { x: -1, y: -5, z: 0, material: etherealPinkMaterial },
        { x: -3, y: -5, z: 0, material: spiritCyanMaterial },
        
        // Reflection waves
        { x: -6, y: 2, z: 0, material: mysticPurpleMaterial },
        { x: -6, y: 0, z: 0, material: soulBlueMaterial },
        { x: -6, y: -2, z: 0, material: mysticPurpleMaterial },
        { x: 6, y: 2, z: 0, material: mysticPurpleMaterial },
        { x: 6, y: 0, z: 0, material: soulBlueMaterial },
        { x: 6, y: -2, z: 0, material: mysticPurpleMaterial },
        { x: 2, y: 6, z: 0, material: mysticPurpleMaterial },
        { x: 0, y: 6, z: 0, material: soulBlueMaterial },
        { x: -2, y: 6, z: 0, material: mysticPurpleMaterial },
        { x: 2, y: -6, z: 0, material: mysticPurpleMaterial },
        { x: 0, y: -6, z: 0, material: soulBlueMaterial },
        { x: -2, y: -6, z: 0, material: mysticPurpleMaterial }
    ];

    // Create astral energy
    const astralEnergyVoxels = [
        // Corner energy sources
        { x: -7, y: 7, z: 0, material: astralGoldMaterial },
        { x: 7, y: 7, z: 0, material: astralGoldMaterial },
        { x: 7, y: -7, z: 0, material: astralGoldMaterial },
        { x: -7, y: -7, z: 0, material: astralGoldMaterial },
        
        // Diagonal energy streams
        { x: -6, y: 6, z: 0, material: reflectionWhiteMaterial },
        { x: 6, y: 6, z: 0, material: reflectionWhiteMaterial },
        { x: 6, y: -6, z: 0, material: reflectionWhiteMaterial },
        { x: -6, y: -6, z: 0, material: reflectionWhiteMaterial },
        { x: -5, y: 5, z: 0, material: astralGoldMaterial },
        { x: 5, y: 5, z: 0, material: astralGoldMaterial },
        { x: 5, y: -5, z: 0, material: astralGoldMaterial },
        { x: -5, y: -5, z: 0, material: astralGoldMaterial },
        
        // Energy radiating outward
        { x: -7, y: 4, z: 0, material: spiritCyanMaterial },
        { x: -7, y: 0, z: 0, material: astralGoldMaterial },
        { x: -7, y: -4, z: 0, material: spiritCyanMaterial },
        { x: 7, y: 4, z: 0, material: spiritCyanMaterial },
        { x: 7, y: 0, z: 0, material: astralGoldMaterial },
        { x: 7, y: -4, z: 0, material: spiritCyanMaterial },
        { x: 4, y: 7, z: 0, material: spiritCyanMaterial },
        { x: 0, y: 7, z: 0, material: astralGoldMaterial },
        { x: -4, y: 7, z: 0, material: spiritCyanMaterial },
        { x: 4, y: -7, z: 0, material: spiritCyanMaterial },
        { x: 0, y: -7, z: 0, material: astralGoldMaterial },
        { x: -4, y: -7, z: 0, material: spiritCyanMaterial }
    ];

    // Create all voxels
    [...mirrorSurfaceVoxels, ...soulReflectionVoxels, ...astralEnergyVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22,
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (mirrorSurfaceVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'mirror';
        } else if (soulReflectionVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'reflection';
        } else {
            mesh.userData.voxelType = 'astral';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        mirrorShine: 0,
        soulReflection: 0,
        astralFlow: 0,
        mirrorOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update soul mirror card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSoulMirrorCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.mirrorShine += deltaTime * 3.5;
    cardGroup.userData.soulReflection += deltaTime * 4.0;
    cardGroup.userData.astralFlow += deltaTime * 2.5;

    const time = cardGroup.userData.animationTime;
    const mirrorShine = cardGroup.userData.mirrorShine;
    const soulReflection = cardGroup.userData.soulReflection;
    const astralFlow = cardGroup.userData.astralFlow;
    const mirrorOffset = cardGroup.userData.mirrorOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'mirror':
                    // Mirror surface shines and reflects
                    const mirrorIntensity = 0.8 + Math.sin(mirrorShine * 2.5 + child.position.x * 0.3 + child.position.y * 0.2 + mirrorOffset) * 0.7;
                    const mirrorReflection = Math.cos(mirrorShine * 4.0 + mirrorOffset) * 0.007;
                    
                    child.material.emissiveIntensity = baseEmissive * mirrorIntensity;
                    child.material.opacity = baseOpacity * (0.8 + mirrorIntensity * 0.2);
                    child.position.x += mirrorReflection * Math.sin(child.position.y * 0.5);
                    child.position.y += mirrorReflection * Math.cos(child.position.x * 0.5);
                    break;

                case 'reflection':
                    // Soul reflections shimmer
                    const reflectionIntensity = 0.5 + Math.sin(soulReflection * 3.0 + child.position.x * 0.4 + child.position.y * 0.3 + mirrorOffset) * 0.8;
                    const reflectionShimmer = Math.sin(soulReflection * 5.0 + mirrorOffset) * 0.009;
                    
                    child.material.emissiveIntensity = baseEmissive * reflectionIntensity;
                    child.material.opacity = baseOpacity * reflectionIntensity;
                    child.position.x += reflectionShimmer * (child.position.x > 0 ? 1 : -1);
                    child.position.y += reflectionShimmer * (child.position.y > 0 ? 1 : -1);
                    break;

                case 'astral':
                    // Astral energy flows
                    const astralIntensity = 0.6 + Math.sin(astralFlow * 2.0 + child.position.x * 0.2 + child.position.y * 0.2 + mirrorOffset) * 0.6;
                    const astralMotion = Math.cos(astralFlow * 3.0 + mirrorOffset) * 0.005;
                    
                    child.material.emissiveIntensity = baseEmissive * astralIntensity;
                    child.material.opacity = baseOpacity * astralIntensity;
                    child.position.x += astralMotion * Math.cos(child.position.y * 0.3);
                    child.position.y += astralMotion * Math.sin(child.position.x * 0.3);
                    break;
            }
        }
    });

    // Overall soul mirror effect
    const soulMirror = Math.sin(time * 2.8 + mirrorOffset) * 0.006;
    cardGroup.position.x += soulMirror;
    cardGroup.position.y += soulMirror * 0.7;
    
    // Gentle rotation for mystical effect
    cardGroup.rotation.z += deltaTime * 0.1;
}

// Export the soul mirror card data for the loot system
export const SOUL_MIRROR_CARD_DATA = {
    name: 'Soul Mirror',
    description: 'Creates a mystical mirror that reflects damage back to attackers and reveals hidden enemies through spiritual sight.',
    category: 'card',
    rarity: 'rare',
    effect: 'soul_mirror',
    effectValue: 25,
    createFunction: createSoulMirrorCard,
    updateFunction: updateSoulMirrorCardAnimation,
    voxelModel: 'soul_mirror_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.3
    }
};

export default createSoulMirrorCard;