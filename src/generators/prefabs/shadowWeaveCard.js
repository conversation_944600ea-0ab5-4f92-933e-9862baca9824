import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Shadow Weave Card Prefab
 * Creates multiple shadow copies that attack simultaneously
 */

// Shadow weave specific colors
const SHADOW_COLORS = {
    VOID_BLACK: 0x000000,        // Deep void black
    SHADOW_GRAY: 0x2F2F2F,       // Primary shadow gray
    DARK_PURPLE: 0x301934,       // Dark shadow purple
    UMBRAL_BLUE: 0x191970,       // Deep umbral blue
    MYSTIC_SILVER: 0x708090,     // Mystic silver
    PHANTOM_WHITE: 0x696969,     // Phantom white
    SHADOW_RED: 0x800000,        // Shadow-touched red
    ECLIPSE_BLACK: 0x1C1C1C      // Eclipse darkness
};

/**
 * Create a shadow weave card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The shadow weave card 3D model
 */
export function createShadowWeaveCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ShadowWeaveCard';

    // Materials
    const voidBlackMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.VOID_BLACK,
        emissive: SHADOW_COLORS.VOID_BLACK,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_GRAY,
        emissive: SHADOW_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const darkPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.DARK_PURPLE,
        emissive: SHADOW_COLORS.DARK_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    const umbralBlueMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.UMBRAL_BLUE,
        emissive: SHADOW_COLORS.UMBRAL_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const mysticSilverMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.MYSTIC_SILVER,
        emissive: SHADOW_COLORS.MYSTIC_SILVER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const phantomWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.PHANTOM_WHITE,
        emissive: SHADOW_COLORS.PHANTOM_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.5
    });

    const shadowRedMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_RED,
        emissive: SHADOW_COLORS.SHADOW_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const eclipseBlackMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.ECLIPSE_BLACK,
        emissive: SHADOW_COLORS.ECLIPSE_BLACK,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create central shadow figure (primary shadow copy)
    const centralShadowVoxels = [
        // Central shadow body
        { x: 0, y: 0, z: 0, material: shadowGrayMaterial },
        { x: 0, y: 1, z: 0, material: shadowGrayMaterial },
        { x: 0, y: 2, z: 0, material: shadowGrayMaterial },
        { x: 0, y: 3, z: 0, material: voidBlackMaterial },
        { x: 0, y: 4, z: 0, material: voidBlackMaterial },
        
        // Shadow arms
        { x: -1, y: 2, z: 0, material: shadowGrayMaterial },
        { x: 1, y: 2, z: 0, material: shadowGrayMaterial },
        { x: -2, y: 2, z: 0, material: darkPurpleMaterial },
        { x: 2, y: 2, z: 0, material: darkPurpleMaterial },
        
        // Shadow head
        { x: 0, y: 5, z: 0, material: voidBlackMaterial },
        { x: -1, y: 4, z: 0, material: shadowRedMaterial },
        { x: 1, y: 4, z: 0, material: shadowRedMaterial },
        
        // Shadow essence
        { x: 0, y: 6, z: 0, material: umbralBlueMaterial }
    ];

    // Create multiple shadow copies (surrounding the central one)
    const shadowCopyVoxels = [
        // Left shadow copy
        { x: -4, y: 0, z: 0, material: eclipseBlackMaterial },
        { x: -4, y: 1, z: 0, material: eclipseBlackMaterial },
        { x: -4, y: 2, z: 0, material: eclipseBlackMaterial },
        { x: -5, y: 2, z: 0, material: umbralBlueMaterial },
        { x: -3, y: 2, z: 0, material: umbralBlueMaterial },
        { x: -4, y: 3, z: 0, material: darkPurpleMaterial },
        
        // Right shadow copy
        { x: 4, y: 0, z: 0, material: eclipseBlackMaterial },
        { x: 4, y: 1, z: 0, material: eclipseBlackMaterial },
        { x: 4, y: 2, z: 0, material: eclipseBlackMaterial },
        { x: 5, y: 2, z: 0, material: umbralBlueMaterial },
        { x: 3, y: 2, z: 0, material: umbralBlueMaterial },
        { x: 4, y: 3, z: 0, material: darkPurpleMaterial },
        
        // Upper shadow copy
        { x: 0, y: 8, z: 0, material: eclipseBlackMaterial },
        { x: -1, y: 8, z: 0, material: eclipseBlackMaterial },
        { x: 1, y: 8, z: 0, material: eclipseBlackMaterial },
        { x: 0, y: 9, z: 0, material: umbralBlueMaterial },
        { x: 0, y: 7, z: 0, material: darkPurpleMaterial },
        
        // Lower shadow copy
        { x: 0, y: -3, z: 0, material: eclipseBlackMaterial },
        { x: -1, y: -3, z: 0, material: eclipseBlackMaterial },
        { x: 1, y: -3, z: 0, material: eclipseBlackMaterial },
        { x: 0, y: -4, z: 0, material: umbralBlueMaterial },
        { x: 0, y: -2, z: 0, material: darkPurpleMaterial }
    ];

    // Create shadow energy weaving patterns
    const shadowWeaveVoxels = [
        // Connecting energy lines between shadows
        { x: -2, y: 1, z: 0, material: mysticSilverMaterial },
        { x: -1, y: 1, z: 0, material: phantomWhiteMaterial },
        { x: 1, y: 1, z: 0, material: phantomWhiteMaterial },
        { x: 2, y: 1, z: 0, material: mysticSilverMaterial },
        
        { x: -3, y: 3, z: 0, material: mysticSilverMaterial },
        { x: -2, y: 4, z: 0, material: phantomWhiteMaterial },
        { x: 2, y: 4, z: 0, material: phantomWhiteMaterial },
        { x: 3, y: 3, z: 0, material: mysticSilverMaterial },
        
        // Vertical weave lines
        { x: 0, y: -1, z: 0, material: mysticSilverMaterial },
        { x: 0, y: 7, z: 0, material: mysticSilverMaterial },
        
        // Diagonal weave patterns
        { x: -3, y: -1, z: 0, material: darkPurpleMaterial },
        { x: -2, y: 0, z: 0, material: umbralBlueMaterial },
        { x: 2, y: 0, z: 0, material: umbralBlueMaterial },
        { x: 3, y: -1, z: 0, material: darkPurpleMaterial },
        
        { x: -3, y: 5, z: 0, material: darkPurpleMaterial },
        { x: -2, y: 6, z: 0, material: umbralBlueMaterial },
        { x: 2, y: 6, z: 0, material: umbralBlueMaterial },
        { x: 3, y: 5, z: 0, material: darkPurpleMaterial }
    ];

    // Create shadow magic runes/symbols
    const shadowRuneVoxels = [
        // Dark magic symbols around the weave
        { x: -6, y: 3, z: 0, material: shadowRedMaterial },
        { x: -5, y: 4, z: 0, material: shadowRedMaterial },
        { x: -6, y: 1, z: 0, material: shadowRedMaterial },
        
        { x: 6, y: 3, z: 0, material: shadowRedMaterial },
        { x: 5, y: 4, z: 0, material: shadowRedMaterial },
        { x: 6, y: 1, z: 0, material: shadowRedMaterial },
        
        { x: -1, y: 10, z: 0, material: shadowRedMaterial },
        { x: 0, y: 10, z: 0, material: voidBlackMaterial },
        { x: 1, y: 10, z: 0, material: shadowRedMaterial },
        
        { x: -1, y: -5, z: 0, material: shadowRedMaterial },
        { x: 0, y: -5, z: 0, material: voidBlackMaterial },
        { x: 1, y: -5, z: 0, material: shadowRedMaterial },
        
        // Corner shadow anchors
        { x: -5, y: 5, z: 0, material: voidBlackMaterial },
        { x: 5, y: 5, z: 0, material: voidBlackMaterial },
        { x: -5, y: -2, z: 0, material: voidBlackMaterial },
        { x: 5, y: -2, z: 0, material: voidBlackMaterial }
    ];

    // Create all voxels
    [...centralShadowVoxels, ...shadowCopyVoxels, ...shadowWeaveVoxels, ...shadowRuneVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.25, // More compact spacing
            voxel.y * VOXEL_SIZE * 0.25,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (centralShadowVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'central';
        } else if (shadowCopyVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'copy';
        } else if (shadowWeaveVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'weave';
        } else {
            mesh.userData.voxelType = 'rune';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        shadowPulse: 0,
        weaveFlow: 0,
        copyShift: 0,
        runeFlicker: 0,
        shadowOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update shadow weave card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateShadowWeaveCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.shadowPulse += deltaTime * 5.5; // Rapid shadow energy
    cardGroup.userData.weaveFlow += deltaTime * 3.8;
    cardGroup.userData.copyShift += deltaTime * 4.2;
    cardGroup.userData.runeFlicker += deltaTime * 7.0; // Fast rune flickering

    const time = cardGroup.userData.animationTime;
    const shadowPulse = cardGroup.userData.shadowPulse;
    const weaveFlow = cardGroup.userData.weaveFlow;
    const copyShift = cardGroup.userData.copyShift;
    const runeFlicker = cardGroup.userData.runeFlicker;
    const shadowOffset = cardGroup.userData.shadowOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'central':
                    // Central shadow pulses with dark energy
                    const centralIntensity = 0.7 + Math.sin(shadowPulse * 2.5 + shadowOffset) * 0.8;
                    const centralGlow = Math.sin(shadowPulse * 4.0) * 0.004;
                    
                    child.material.emissiveIntensity = baseEmissive * centralIntensity;
                    child.material.opacity = baseOpacity * (0.8 + centralIntensity * 0.2);
                    child.position.x += centralGlow;
                    child.position.y += centralGlow * 0.5;
                    break;

                case 'copy':
                    // Shadow copies shift and phase
                    const copyIntensity = 0.5 + Math.sin(copyShift * 3.0 + child.position.x * 0.4 + shadowOffset) * 0.9;
                    const copyMotion = Math.cos(copyShift * 2.5 + child.position.y * 0.3) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * copyIntensity;
                    child.material.opacity = baseOpacity * copyIntensity;
                    child.position.x += copyMotion;
                    child.position.y += copyMotion * 0.7;
                    break;

                case 'weave':
                    // Weave energy flows between shadows
                    const weaveIntensity = 0.6 + Math.sin(weaveFlow * 4.5 + child.position.x * 0.6 + child.position.y * 0.4 + shadowOffset) * 0.8;
                    const weaveMotion = Math.sin(weaveFlow * 5.0 + shadowOffset) * 0.006;
                    
                    child.material.emissiveIntensity = baseEmissive * weaveIntensity;
                    child.material.opacity = baseOpacity * weaveIntensity;
                    child.position.x += weaveMotion;
                    child.position.y += weaveMotion * 0.3;
                    break;

                case 'rune':
                    // Shadow runes flicker with dark magic
                    const runeIntensity = 0.4 + Math.sin(runeFlicker * 2.0 + shadowOffset) * 0.9;
                    const runeFlash = Math.sin(runeFlicker * 8.0) * 0.003;
                    
                    child.material.emissiveIntensity = baseEmissive * runeIntensity;
                    child.material.opacity = baseOpacity * runeIntensity;
                    child.position.x += runeFlash;
                    child.position.y += runeFlash * 0.6;
                    break;
            }
        }
    });

    // Overall shadow weave effect
    const shadowWeave = Math.sin(time * 4.0 + shadowOffset) * 0.008;
    cardGroup.position.x += shadowWeave;
    cardGroup.position.y += shadowWeave * 0.5;
    
    // Slight rotation to simulate weaving motion
    cardGroup.rotation.z += deltaTime * 0.4;
}

// Export the shadow weave card data for the loot system
export const SHADOW_WEAVE_CARD_DATA = {
    name: 'Shadow Weave',
    description: 'Weaves multiple shadow copies of yourself that appear around enemies and attack simultaneously. Each shadow deals massive damage before vanishing.',
    category: 'card',
    rarity: 'legendary',
    effect: 'shadow_weave',
    effectValue: 150,
    createFunction: createShadowWeaveCard,
    updateFunction: updateShadowWeaveCardAnimation,
    voxelModel: 'shadow_weave_card',
    glow: {
        color: 0x2F2F2F,
        intensity: 1.8
    }
};

export default createShadowWeaveCard;