import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import { VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE, getOrCreateGeometry, _getMaterialByHex_Cached } from './shared.js';
import { createStoneArchwayDoor } from './stoneArchwayDoor.js';

/**
 * Creates a water-themed archway door for pond event rooms
 * Uses stone archway with water stream accents
 * @param {number} width World width of the opening.
 * @param {number} height World height of the opening.
 * @param {number} depth World depth of the arch structure.
 * @returns {THREE.Group} Group containing the archway with water accents.
 */
export function createWaterArchwayDoor(width, height, depth) {
    // Create base stone archway with water-themed materials
    const waterStoneMaterials = [
        _getMaterialByHex_Cached('8b7355'), // Brown stone
        _getMaterialByHex_Cached('696969'), // Dim gray
        _getMaterialByHex_Cached('708090')  // Slate gray
    ];
    
    const doorGroup = createStoneArchwayDoor(width, height, depth, waterStoneMaterials);
    
    // Add water stream accents to the archway
    addWaterStreamAccents(doorGroup, width, height, depth);
    
    doorGroup.name = 'waterArchwayDoor';
    doorGroup.userData.doorType = 'water_archway';
    
    return doorGroup;
}

/**
 * Add animated water stream accents to the door archway
 * @param {THREE.Group} doorGroup - Door group to add accents to
 * @param {number} width - Door width
 * @param {number} height - Door height  
 * @param {number} depth - Door depth
 */
function addWaterStreamAccents(doorGroup, width, height, depth) {
    const waterGroup = new THREE.Group();
    waterGroup.name = 'waterAccents';
    
    // Water colors (same as water walls)
    const WATER_COLORS = [0x4080FF, 0x6090FF, 0x80A0FF, 0x5080E0];
    
    // FIXED: Create water droplet streams instead of solid streams
    const streamPositions = [
        { x: -width * 0.4, side: 'left' },
        { x: width * 0.4, side: 'right' }
    ];

    streamPositions.forEach((stream, index) => {
        // Create droplets for this stream
        const dropletCount = 2 + Math.floor(Math.random() * 2); // 2-3 droplets per side

        for (let i = 0; i < dropletCount; i++) {
            // Create small water droplet
            const dropletSize = 0.1 + Math.random() * 0.05; // 0.1-0.15 units
            const dropletGeometry = new THREE.SphereGeometry(dropletSize, 6, 4);
            const waterMaterial = new THREE.MeshLambertMaterial({
                color: WATER_COLORS[index % WATER_COLORS.length],
                transparent: true,
                opacity: 0.7 + Math.random() * 0.3,
                emissive: new THREE.Color(WATER_COLORS[index % WATER_COLORS.length]).multiplyScalar(0.3),
                emissiveIntensity: 0.4,
            });

            const dropletMesh = new THREE.Mesh(dropletGeometry, waterMaterial);

            // Position droplet
            const dropletX = stream.x + (Math.random() - 0.5) * 0.2; // Slight variation
            const dropletStartY = height * 0.9 + Math.random() * 0.2; // Start near top
            const dropletZ = depth * 0.52; // Slightly in front

            dropletMesh.position.set(dropletX, dropletStartY, dropletZ);
            dropletMesh.name = `waterDroplet_${stream.side}_${i}`;

            // Store animation data
            dropletMesh.userData.isWaterAccent = true;
            dropletMesh.userData.streamSide = stream.side;
            dropletMesh.userData.dropletIndex = i;
            dropletMesh.userData.originalOpacity = waterMaterial.opacity;
            dropletMesh.userData.flowSpeed = 2.0 + index * 0.5 + i * 0.3;
            dropletMesh.userData.phase = index * Math.PI + i * Math.PI * 0.5;
            dropletMesh.userData.startY = dropletStartY;
            dropletMesh.userData.fallDistance = height * 0.9;

            waterGroup.add(dropletMesh);
        }
    });
    
    // Add animation function to water group
    waterGroup.userData.updateWaterAnimation = function(time) {
        this.children.forEach(dropletMesh => {
            if (dropletMesh.userData.isWaterAccent && dropletMesh.material) {
                const speed = dropletMesh.userData.flowSpeed;
                const phase = dropletMesh.userData.phase;
                const startY = dropletMesh.userData.startY;
                const fallDistance = dropletMesh.userData.fallDistance;

                // Calculate droplet fall position (continuous loop)
                const fallProgress = ((time * speed + phase) % (Math.PI * 2)) / (Math.PI * 2);
                const currentY = startY - (fallProgress * fallDistance);

                // Reset droplet to top when it falls below the archway
                if (currentY < 0) {
                    dropletMesh.position.y = startY;
                } else {
                    dropletMesh.position.y = currentY;
                }

                // Animate opacity for droplet visibility (fade in/out during fall)
                const opacityPhase = Math.sin(fallProgress * Math.PI);
                const opacity = dropletMesh.userData.originalOpacity * opacityPhase;
                dropletMesh.material.opacity = Math.max(0.1, Math.min(1.0, opacity));

                // Animate emissive intensity for shimmer
                const shimmer = Math.sin(time * speed * 2 + phase);
                dropletMesh.material.emissiveIntensity = 0.3 + shimmer * 0.2;
            }
        });
    };
    
    doorGroup.add(waterGroup);
    
    // Mark door group for animation updates
    doorGroup.userData.hasWaterAccents = true;
    doorGroup.userData.animateWater = true;
}

/**
 * Creates a sandstone-themed archway door for treasure event rooms
 * @param {number} width World width of the opening.
 * @param {number} height World height of the opening.
 * @param {number} depth World depth of the arch structure.
 * @returns {THREE.Group} Group containing the sandstone archway.
 */
export function createSandstoneArchwayDoor(width, height, depth) {
    // Create base stone archway with sandstone materials
    const sandstoneMaterials = [
        _getMaterialByHex_Cached('f4a460'), // Sandy brown
        _getMaterialByHex_Cached('deb887'), // Burlywood
        _getMaterialByHex_Cached('d2b48c')  // Tan
    ];
    
    const doorGroup = createStoneArchwayDoor(width, height, depth, sandstoneMaterials);
    
    // Add golden accents for treasure room theme
    addGoldenAccents(doorGroup, width, height, depth);
    
    doorGroup.name = 'sandstoneArchwayDoor';
    doorGroup.userData.doorType = 'sandstone_archway';
    
    return doorGroup;
}

/**
 * Add golden accent details to sandstone archway
 * @param {THREE.Group} doorGroup - Door group to add accents to
 * @param {number} width - Door width
 * @param {number} height - Door height
 * @param {number} depth - Door depth
 */
function addGoldenAccents(doorGroup, width, height, depth) {
    const accentGroup = new THREE.Group();
    accentGroup.name = 'goldenAccents';
    
    // Golden accent material
    const goldenMaterial = new THREE.MeshLambertMaterial({
        color: 0xFFD700, // Bright gold
        emissive: 0x332200, // Dark golden glow
        emissiveIntensity: 0.4
    });
    
    // Create small golden accent voxels at key points
    const accentGeometry = getOrCreateGeometry('golden_accent_voxel', () =>
        new THREE.BoxGeometry(VOXEL_SIZE * 0.5, VOXEL_SIZE * 0.5, VOXEL_SIZE * 0.3)
    );
    
    // Add accents at the top corners of the archway
    const accentPositions = [
        { x: -width * 0.35, y: height * 0.85, z: depth * 0.1 },
        { x: width * 0.35, y: height * 0.85, z: depth * 0.1 },
        { x: 0, y: height * 0.95, z: depth * 0.1 } // Top center
    ];
    
    accentPositions.forEach((pos, index) => {
        const accentMesh = new THREE.Mesh(accentGeometry, goldenMaterial);
        accentMesh.position.set(pos.x, pos.y, pos.z);
        accentMesh.name = `goldenAccent_${index}`;
        accentMesh.castShadow = true;
        accentMesh.receiveShadow = true;
        
        accentGroup.add(accentMesh);
    });
    
    doorGroup.add(accentGroup);
}
