import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Spirit Wolf Pack Card Prefab
 * Summons a pack of spirit wolves that hunt enemies with supernatural speed
 */

// Spirit Wolf Pack specific colors
const SPIRIT_WOLF_COLORS = {
    WOLF_GRAY: 0x708090,           // Main wolf fur
    SPIRIT_BLUE: 0x87CEEB,         // Spirit energy
    ETHEREAL_WHITE: 0xF0F8FF,      // Ethereal glow
    FOREST_GREEN: 0x228B22,        // Nature connection
    MOON_SILVER: 0xC0C0C0,         // Moonlight essence
    WILD_BROWN: 0x8B4513,          // Wild earth tones
    PACK_CYAN: 0x00CED1,           // Pack bond energy
    HOWL_PURPLE: 0x9370DB          // Supernatural howl
};

/**
 * Create a spirit wolf pack card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The spirit wolf pack card 3D model
 */
export function createSpiritWolfPackCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SpiritWolfPackCard';

    // Spirit Wolf Pack materials
    const wolfGrayMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.WOLF_GRAY,
        emissive: SPIRIT_WOLF_COLORS.WOLF_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.8
    });

    const spiritBlueMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.SPIRIT_BLUE,
        emissive: SPIRIT_WOLF_COLORS.SPIRIT_BLUE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const etherealWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.ETHEREAL_WHITE,
        emissive: SPIRIT_WOLF_COLORS.ETHEREAL_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.5
    });

    const forestGreenMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.FOREST_GREEN,
        emissive: SPIRIT_WOLF_COLORS.FOREST_GREEN,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    const moonSilverMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.MOON_SILVER,
        emissive: SPIRIT_WOLF_COLORS.MOON_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const wildBrownMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.WILD_BROWN,
        emissive: SPIRIT_WOLF_COLORS.WILD_BROWN,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    const packCyanMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.PACK_CYAN,
        emissive: SPIRIT_WOLF_COLORS.PACK_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const howlPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SPIRIT_WOLF_COLORS.HOWL_PURPLE,
        emissive: SPIRIT_WOLF_COLORS.HOWL_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.4
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Spirit Wolves (pack of 3 wolves in formation)
    const spiritWolvesVoxels = [
        // Alpha Wolf (center, largest)
        { x: 0.0, y: -0.12, z: 0.0, material: wolfGrayMaterial }, // Body
        { x: 0.0, y: -0.04, z: 0.0, material: moonSilverMaterial }, // Back
        { x: 0.08, y: -0.08, z: 0.0, material: wolfGrayMaterial }, // Head
        { x: 0.12, y: -0.04, z: 0.0, material: wildBrownMaterial }, // Snout
        { x: 0.06, y: 0.0, z: 0.02, material: spiritBlueMaterial }, // Left ear
        { x: 0.06, y: 0.0, z: -0.02, material: spiritBlueMaterial }, // Right ear
        { x: -0.08, y: -0.12, z: 0.0, material: wolfGrayMaterial }, // Tail
        { x: -0.12, y: -0.08, z: 0.0, material: moonSilverMaterial }, // Tail tip
        
        // Left Wolf
        { x: -0.12, y: -0.16, z: 0.08, material: wolfGrayMaterial }, // Body
        { x: -0.12, y: -0.08, z: 0.08, material: moonSilverMaterial }, // Back
        { x: -0.04, y: -0.12, z: 0.08, material: wolfGrayMaterial }, // Head
        { x: 0.0, y: -0.08, z: 0.08, material: wildBrownMaterial }, // Snout
        { x: -0.20, y: -0.16, z: 0.08, material: wolfGrayMaterial }, // Tail
        
        // Right Wolf
        { x: -0.12, y: -0.16, z: -0.08, material: wolfGrayMaterial }, // Body
        { x: -0.12, y: -0.08, z: -0.08, material: moonSilverMaterial }, // Back
        { x: -0.04, y: -0.12, z: -0.08, material: wolfGrayMaterial }, // Head
        { x: 0.0, y: -0.08, z: -0.08, material: wildBrownMaterial }, // Snout
        { x: -0.20, y: -0.16, z: -0.08, material: wolfGrayMaterial }, // Tail
        
        // Pack formation (smaller wolves in background)
        { x: -0.20, y: -0.12, z: 0.0, material: spiritBlueMaterial }, // Back wolf 1
        { x: -0.24, y: -0.08, z: 0.04, material: spiritBlueMaterial }, // Back wolf 2
        { x: -0.24, y: -0.08, z: -0.04, material: spiritBlueMaterial } // Back wolf 3
    ];

    // Spirit Energy (supernatural wolf energy)
    const spiritEnergyVoxels = [
        // Energy auras around wolves
        { x: 0.0, y: 0.04, z: 0.0, material: etherealWhiteMaterial }, // Alpha aura
        { x: -0.12, y: 0.0, z: 0.08, material: spiritBlueMaterial }, // Left wolf aura
        { x: -0.12, y: 0.0, z: -0.08, material: spiritBlueMaterial }, // Right wolf aura
        
        // Connecting pack energy
        { x: -0.06, y: -0.06, z: 0.04, material: packCyanMaterial }, // Energy link 1
        { x: -0.06, y: -0.06, z: -0.04, material: packCyanMaterial }, // Energy link 2
        { x: -0.18, y: -0.10, z: 0.0, material: packCyanMaterial }, // Energy link 3
        
        // Floating spirit wisps
        { x: 0.16, y: 0.08, z: 0.0, material: etherealWhiteMaterial },
        { x: -0.28, y: 0.04, z: 0.08, material: etherealWhiteMaterial },
        { x: -0.28, y: 0.04, z: -0.08, material: etherealWhiteMaterial },
        { x: 0.12, y: 0.12, z: 0.06, material: spiritBlueMaterial },
        { x: 0.12, y: 0.12, z: -0.06, material: spiritBlueMaterial },
        
        // Energy swirls
        { x: -0.16, y: 0.08, z: 0.12, material: packCyanMaterial },
        { x: -0.16, y: 0.08, z: -0.12, material: packCyanMaterial },
        { x: -0.32, y: 0.08, z: 0.0, material: packCyanMaterial }
    ];

    // Nature Connection (forest/wild energy)
    const natureConnectionVoxels = [
        // Forest floor elements
        { x: 0.04, y: -0.20, z: 0.04, material: forestGreenMaterial },
        { x: -0.08, y: -0.20, z: 0.12, material: forestGreenMaterial },
        { x: -0.08, y: -0.20, z: -0.12, material: forestGreenMaterial },
        { x: -0.24, y: -0.20, z: 0.0, material: forestGreenMaterial },
        
        // Wild vegetation
        { x: 0.08, y: -0.16, z: 0.08, material: wildBrownMaterial },
        { x: -0.04, y: -0.16, z: 0.16, material: wildBrownMaterial },
        { x: -0.04, y: -0.16, z: -0.16, material: wildBrownMaterial },
        { x: -0.28, y: -0.16, z: 0.08, material: wildBrownMaterial },
        { x: -0.28, y: -0.16, z: -0.08, material: wildBrownMaterial },
        
        // Nature spirits
        { x: 0.12, y: -0.12, z: 0.12, material: forestGreenMaterial },
        { x: 0.12, y: -0.12, z: -0.12, material: forestGreenMaterial },
        { x: -0.32, y: -0.12, z: 0.04, material: forestGreenMaterial },
        { x: -0.32, y: -0.12, z: -0.04, material: forestGreenMaterial }
    ];

    // Howl Effects (supernatural communication)
    const howlEffectsVoxels = [
        // Sound waves from alpha wolf
        { x: 0.20, y: 0.0, z: 0.0, material: howlPurpleMaterial },
        { x: 0.24, y: 0.04, z: 0.0, material: howlPurpleMaterial },
        { x: 0.28, y: 0.08, z: 0.0, material: howlPurpleMaterial },
        
        // Sound waves expanding
        { x: 0.16, y: 0.04, z: 0.08, material: etherealWhiteMaterial },
        { x: 0.16, y: 0.04, z: -0.08, material: etherealWhiteMaterial },
        { x: 0.20, y: 0.08, z: 0.04, material: etherealWhiteMaterial },
        { x: 0.20, y: 0.08, z: -0.04, material: etherealWhiteMaterial },
        
        // Pack response howls
        { x: -0.16, y: 0.04, z: 0.16, material: howlPurpleMaterial },
        { x: -0.16, y: 0.04, z: -0.16, material: howlPurpleMaterial },
        { x: -0.36, y: 0.06, z: 0.0, material: howlPurpleMaterial },
        
        // Mystical resonance
        { x: 0.0, y: 0.16, z: 0.0, material: moonSilverMaterial },
        { x: -0.12, y: 0.12, z: 0.08, material: moonSilverMaterial },
        { x: -0.12, y: 0.12, z: -0.08, material: moonSilverMaterial }
    ];

    // Create spirit wolves group
    const spiritWolvesGroup = new THREE.Group();
    spiritWolvesGroup.name = 'spiritWolves';

    // Add spirit wolves voxels
    spiritWolvesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.wolfPhase = index * 0.1; // Stagger animation
        spiritWolvesGroup.add(mesh);
    });

    // Create spirit energy group
    const spiritEnergyGroup = new THREE.Group();
    spiritEnergyGroup.name = 'spiritEnergy';

    // Add spirit energy voxels
    spiritEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.15; // Stagger animation
        spiritEnergyGroup.add(mesh);
    });

    // Create nature connection group
    const natureConnectionGroup = new THREE.Group();
    natureConnectionGroup.name = 'natureConnection';

    // Add nature connection voxels
    natureConnectionVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.naturePhase = index * 0.12; // Stagger animation
        natureConnectionGroup.add(mesh);
    });

    // Create howl effects group
    const howlEffectsGroup = new THREE.Group();
    howlEffectsGroup.name = 'howlEffects';

    // Add howl effects voxels
    howlEffectsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.howlPhase = index * 0.08; // Stagger animation
        howlEffectsGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(spiritWolvesGroup);
    cardGroup.add(spiritEnergyGroup);
    cardGroup.add(natureConnectionGroup);
    cardGroup.add(howlEffectsGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        packHunt: 0,
        spiritFlow: 0,
        wildCall: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update spirit wolf pack card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSpiritWolfPackCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.packHunt += deltaTime * 3.0; // Pack hunting speed
    cardGroup.userData.spiritFlow += deltaTime * 4.0; // Spirit energy flow
    cardGroup.userData.wildCall += deltaTime * 2.0; // Wild howl speed

    const time = cardGroup.userData.animationTime;
    const packHunt = cardGroup.userData.packHunt;
    const spiritFlow = cardGroup.userData.spiritFlow;
    const wildCall = cardGroup.userData.wildCall;

    // Animate spirit wolves (pack movement)
    const spiritWolvesGroup = cardGroup.getObjectByName('spiritWolves');
    if (spiritWolvesGroup) {
        // Pack breathing/stalking motion
        const packStalk = Math.sin(packHunt * 2.0) * 0.001;
        spiritWolvesGroup.position.x = packStalk;
        
        // Individual wolf animation
        spiritWolvesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.wolfPhase !== undefined) {
                const wolfTime = packHunt + mesh.userData.wolfPhase;
                
                // Wolf breathing/alertness
                const breathe = Math.sin(wolfTime * 2.5) * 0.0005;
                const alertness = Math.cos(wolfTime * 3.0) * 0.0003;
                
                mesh.position.x = mesh.userData.originalPosition.x + breathe;
                mesh.position.y = mesh.userData.originalPosition.y + alertness;
                
                // Wolf spirit glow
                const spiritGlow = 1.0 + Math.sin(wolfTime * 2.0) * 0.3;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * spiritGlow;
                }
                
                // Wolf ethereal opacity
                const wolfOpacity = 0.9 + Math.sin(wolfTime * 2.5) * 0.1;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * wolfOpacity;
                }
            }
        });
    }

    // Animate spirit energy (pack bond energy)
    const spiritEnergyGroup = cardGroup.getObjectByName('spiritEnergy');
    if (spiritEnergyGroup) {
        spiritEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = spiritFlow + mesh.userData.energyPhase;
                
                // Energy flowing motion
                const energyFlow = Math.sin(energyTime * 4.0) * 0.002;
                const energyPulse = Math.cos(energyTime * 3.0) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + energyFlow;
                mesh.position.y = mesh.userData.originalPosition.y + energyPulse;
                
                // Energy intensity fluctuation
                const energyIntensity = 1.0 + Math.sin(energyTime * 5.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyIntensity;
                }
                
                // Energy opacity variation
                const energyOpacity = 0.7 + Math.sin(energyTime * 4.5) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * energyOpacity;
                }
                
                // Energy scale pulsing
                const energyScale = 0.9 + Math.sin(energyTime * 4.0) * 0.2;
                mesh.scale.setScalar(energyScale);
            }
        });
    }

    // Animate nature connection (wild forest energy)
    const natureConnectionGroup = cardGroup.getObjectByName('natureConnection');
    if (natureConnectionGroup) {
        natureConnectionGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.naturePhase !== undefined) {
                const natureTime = packHunt * 0.8 + mesh.userData.naturePhase;
                
                // Nature growth motion
                const growth = Math.sin(natureTime * 2.0) * 0.001;
                const sway = Math.cos(natureTime * 1.5) * 0.0008;
                
                mesh.position.x = mesh.userData.originalPosition.x + sway;
                mesh.position.y = mesh.userData.originalPosition.y + growth;
                
                // Nature intensity fluctuation
                const natureIntensity = 1.0 + Math.sin(natureTime * 3.0) * 0.3;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * natureIntensity;
                }
                
                // Nature scale variation
                const natureScale = 0.95 + Math.sin(natureTime * 2.5) * 0.1;
                mesh.scale.setScalar(natureScale);
            }
        });
    }

    // Animate howl effects (supernatural communication)
    const howlEffectsGroup = cardGroup.getObjectByName('howlEffects');
    if (howlEffectsGroup) {
        howlEffectsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.howlPhase !== undefined) {
                const howlTime = wildCall + mesh.userData.howlPhase;
                
                // Howl wave propagation
                const waveExpand = Math.sin(howlTime * 6.0) * 0.003;
                const wavePulse = Math.cos(howlTime * 4.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + waveExpand;
                mesh.position.z = mesh.userData.originalPosition.z + wavePulse;
                
                // Howl intensity fluctuation
                const howlIntensity = 1.0 + Math.sin(howlTime * 7.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * howlIntensity;
                }
                
                // Howl opacity variation (sound waves)
                const howlOpacity = 0.5 + Math.sin(howlTime * 6.0) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * howlOpacity;
                }
                
                // Howl scale pulsing
                const howlScale = 0.8 + Math.sin(howlTime * 5.0) * 0.3;
                mesh.scale.setScalar(howlScale);
            }
        });
    }

    // Overall pack presence (epic spirit energy)
    const packPulse = 1 + Math.sin(time * 2.0) * 0.08;
    const packMovement = Math.cos(time * 6.0) * 0.0008;
    cardGroup.scale.setScalar(0.8 * packPulse);
    cardGroup.position.x += packMovement;
    cardGroup.position.z += packMovement * 0.8;
}

// Export the spirit wolf pack card data for the loot system
export const SPIRIT_WOLF_PACK_CARD_DATA = {
    name: "Spirit Wolf Pack",
    description: 'Summons a pack of 3 spirit wolves that hunt enemies with supernatural speed and coordination, each dealing damage with ethereal fangs.',
    category: 'card',
    rarity: 'epic',
    effect: 'spirit_wolf_pack',
    effectValue: 3, // Number of spirit wolves summoned
    createFunction: createSpiritWolfPackCard,
    updateFunction: updateSpiritWolfPackCardAnimation,
    voxelModel: 'spirit_wolf_pack_card',
    glow: {
        color: 0x87CEEB,
        intensity: 1.5
    }
};

export default createSpiritWolfPackCard;