import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a temporal device object - ancient time-measurement/control apparatus
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Temporal device group
 */
export function createTemporalDeviceObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 444);

    // Use consistent voxel size
    const deviceVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('temporal_device_voxel', () =>
        new THREE.BoxGeometry(deviceVoxelSize, deviceVoxelSize, deviceVoxelSize)
    );

    // Ancient metal material
    const metalMaterial = _getMaterialByHex_Cached('4a4a3a', {
        emissive: new THREE.Color(0x1a1a0a),
        emissiveIntensity: 0.05,
        roughness: 0.7,
        metalness: 0.6
    });

    // Corroded metal material
    const corrodedMaterial = _getMaterialByHex_Cached('5a4a2a', {
        emissive: new THREE.Color(0x2a1a0a),
        emissiveIntensity: 0.02,
        roughness: 0.9,
        metalness: 0.3
    });

    // Base platform (stone and metal)
    const basePositions = [
        { x: -1, z: -1, mat: 'stone' }, { x: 0, z: -1, mat: 'metal' }, { x: 1, z: -1, mat: 'stone' },
        { x: -1, z: 0, mat: 'metal' },  { x: 0, z: 0, mat: 'metal' },  { x: 1, z: 0, mat: 'metal' },
        { x: -1, z: 1, mat: 'stone' },  { x: 0, z: 1, mat: 'metal' },  { x: 1, z: 1, mat: 'stone' }
    ];

    const stoneMaterial = _getMaterialByHex_Cached('3a3a3a', {
        roughness: 0.9,
        metalness: 0.1
    });

    basePositions.forEach(pos => {
        const material = pos.mat === 'stone' ? stoneMaterial : metalMaterial;
        const baseVoxel = new THREE.Mesh(baseGeometry.clone(), material);
        baseVoxel.position.set(
            pos.x * deviceVoxelSize,
            deviceVoxelSize * 0.5,
            pos.z * deviceVoxelSize
        );
        baseVoxel.userData.isFloorObject = true;
        baseVoxel.userData.hasCollision = true;
        baseVoxel.castShadow = true;
        baseVoxel.receiveShadow = true;
        group.add(baseVoxel);
    });

    // Central device structure (cylindrical shape)
    const centralPositions = [
        // Base cylinder
        { x: 0, y: 1, z: 0 },
        { x: -0.5, y: 1, z: 0 }, { x: 0.5, y: 1, z: 0 }, { x: 0, y: 1, z: -0.5 }, { x: 0, y: 1, z: 0.5 },
        // Mid cylinder  
        { x: 0, y: 1.5, z: 0 },
        { x: -0.5, y: 1.5, z: 0 }, { x: 0.5, y: 1.5, z: 0 }, { x: 0, y: 1.5, z: -0.5 }, { x: 0, y: 1.5, z: 0.5 },
        // Top section
        { x: 0, y: 2, z: 0 },
        { x: 0, y: 2.5, z: 0 }
    ];

    centralPositions.forEach((pos, index) => {
        const material = (index % 3 === 0) ? metalMaterial : corrodedMaterial;
        const deviceVoxel = new THREE.Mesh(baseGeometry.clone(), material);
        deviceVoxel.position.set(
            pos.x * deviceVoxelSize,
            pos.y * deviceVoxelSize,
            pos.z * deviceVoxelSize
        );
        deviceVoxel.userData.isDevicePart = true;
        deviceVoxel.castShadow = true;
        deviceVoxel.receiveShadow = true;
        group.add(deviceVoxel);
    });

    // Control panels/dials around the device
    const panelGeometry = getOrCreateGeometry('control_panel', () =>
        new THREE.BoxGeometry(deviceVoxelSize * 0.8, deviceVoxelSize * 0.3, deviceVoxelSize * 0.2)
    );

    const panelMaterial = _getMaterialByHex_Cached('6a6a4a', {
        emissive: new THREE.Color(0x2a2a1a),
        emissiveIntensity: 0.1,
        roughness: 0.6,
        metalness: 0.7
    });

    const panelPositions = [
        { x: -1.2, y: 1.5, z: 0, rot: Math.PI/2 },
        { x: 1.2, y: 1.5, z: 0, rot: -Math.PI/2 },
        { x: 0, y: 1.5, z: -1.2, rot: 0 },
        { x: 0, y: 1.5, z: 1.2, rot: Math.PI }
    ];

    panelPositions.forEach(pos => {
        const panel = new THREE.Mesh(panelGeometry, panelMaterial);
        panel.position.set(
            pos.x * deviceVoxelSize,
            pos.y * deviceVoxelSize,
            pos.z * deviceVoxelSize
        );
        panel.rotation.y = pos.rot;
        panel.userData.isControlPanel = true;
        panel.castShadow = true;
        panel.receiveShadow = true;
        group.add(panel);
    });

    // Temporal energy indicators (glowing elements)
    const indicatorMaterial = _getMaterialByHex_Cached('FFD700', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0xFFD700),
        emissiveIntensity: 0.4
    });

    const indicatorGeometry = getOrCreateGeometry('temporal_indicator', () =>
        new THREE.BoxGeometry(deviceVoxelSize * 0.2, deviceVoxelSize * 0.2, deviceVoxelSize * 0.2)
    );

    const indicatorPositions = [
        { x: 0, y: 2.7, z: 0 }, // Top center
        { x: -0.8, y: 1.5, z: 0 }, { x: 0.8, y: 1.5, z: 0 }, // Sides
        { x: 0, y: 1.5, z: -0.8 }, { x: 0, y: 1.5, z: 0.8 } // Front/back
    ];

    indicatorPositions.forEach((pos, index) => {
        const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        indicator.position.set(
            pos.x * deviceVoxelSize,
            pos.y * deviceVoxelSize,
            pos.z * deviceVoxelSize
        );
        indicator.userData.isTemporalIndicator = true;
        indicator.userData.animationPhase = (index / indicatorPositions.length) * Math.PI * 2;
        indicator.castShadow = false;
        indicator.receiveShadow = false;
        group.add(indicator);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'temporal_device',
        isInteractable: false,
        isEventObject: true,
        objectId: options.userData?.objectId || 'temporal_device',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: deviceVoxelSize,
        hasAnimation: true,
        animationType: 'device_pulse'
    };

    group.name = 'temporal_device';
    return group;
}

/**
 * Create a temporal artifact object - mysterious research object
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Temporal artifact group
 */
export function createTemporalArtifactObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 222);

    // Use smaller voxel size for artifacts
    const artifactVoxelSize = VOXEL_SIZE * 1.0;
    const baseGeometry = getOrCreateGeometry('temporal_artifact_voxel', () =>
        new THREE.BoxGeometry(artifactVoxelSize, artifactVoxelSize, artifactVoxelSize)
    );

    // Artifact types (random selection)
    const artifactTypes = [
        'temporal_compass', 'chrono_lens', 'time_fragment', 'temporal_scroll'
    ];
    const artifactType = artifactTypes[Math.floor(rng() * artifactTypes.length)];

    // Base materials
    const metalMaterial = _getMaterialByHex_Cached('8B7355', {
        emissive: new THREE.Color(0x3B2F1F),
        emissiveIntensity: 0.1,
        roughness: 0.7,
        metalness: 0.4
    });

    const gemMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.3
    });

    if (artifactType === 'temporal_compass') {
        // Compass-like device
        const compassPositions = [
            // Base
            { x: 0, y: 0.5, z: 0, mat: 'metal' },
            // Ring
            { x: -1, y: 0.5, z: 0, mat: 'metal' }, { x: 1, y: 0.5, z: 0, mat: 'metal' },
            { x: 0, y: 0.5, z: -1, mat: 'metal' }, { x: 0, y: 0.5, z: 1, mat: 'metal' },
            // Center gem
            { x: 0, y: 1, z: 0, mat: 'gem' }
        ];

        compassPositions.forEach(pos => {
            const material = pos.mat === 'gem' ? gemMaterial : metalMaterial;
            const voxel = new THREE.Mesh(baseGeometry.clone(), material);
            voxel.position.set(
                pos.x * artifactVoxelSize,
                pos.y * artifactVoxelSize,
                pos.z * artifactVoxelSize
            );
            voxel.userData.isArtifactPart = true;
            voxel.castShadow = true;
            voxel.receiveShadow = true;
            group.add(voxel);
        });
    } else if (artifactType === 'chrono_lens') {
        // Lens-like device
        const lensPositions = [
            // Handle
            { x: 0, y: 0.5, z: 1, mat: 'metal' }, { x: 0, y: 1, z: 1, mat: 'metal' },
            // Lens frame
            { x: -1, y: 1, z: 0, mat: 'metal' }, { x: 1, y: 1, z: 0, mat: 'metal' },
            { x: 0, y: 1, z: -1, mat: 'metal' }, { x: 0, y: 2, z: 0, mat: 'metal' },
            // Lens center
            { x: 0, y: 1, z: 0, mat: 'gem' }
        ];

        lensPositions.forEach(pos => {
            const material = pos.mat === 'gem' ? gemMaterial : metalMaterial;
            const voxel = new THREE.Mesh(baseGeometry.clone(), material);
            voxel.position.set(
                pos.x * artifactVoxelSize,
                pos.y * artifactVoxelSize,
                pos.z * artifactVoxelSize
            );
            voxel.userData.isArtifactPart = true;
            voxel.castShadow = true;
            voxel.receiveShadow = true;
            group.add(voxel);
        });
    } else {
        // Generic artifact (small crystal formation)
        const genericPositions = [
            { x: 0, y: 0.5, z: 0, mat: 'metal' },
            { x: 0, y: 1, z: 0, mat: 'gem' },
            { x: 0, y: 1.5, z: 0, mat: 'gem' }
        ];

        genericPositions.forEach(pos => {
            const material = pos.mat === 'gem' ? gemMaterial : metalMaterial;
            const voxel = new THREE.Mesh(baseGeometry.clone(), material);
            voxel.position.set(
                pos.x * artifactVoxelSize,
                pos.y * artifactVoxelSize,
                pos.z * artifactVoxelSize
            );
            voxel.userData.isArtifactPart = true;
            voxel.castShadow = true;
            voxel.receiveShadow = true;
            group.add(voxel);
        });
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'temporal_artifact',
        isInteractable: false,
        isEventObject: true,
        objectId: options.userData?.objectId || 'temporal_artifact',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: artifactVoxelSize,
        artifactType: artifactType
    };

    group.name = 'temporal_artifact';
    return group;
}