import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

/**
 * Creates a voxel-based chess queen object
 * Queens are the tallest pieces with an elaborate crown featuring multiple points
 * White queens use light stone colors, black queens use dark obsidian colors
 */

// Material cache for performance
const materialCache = new Map();

function _getMaterialByHex_Cached(hexColor, materialProperties = {}) {
    if (materialCache.has(hexColor)) {
        return materialCache.get(hexColor);
    }
    
    const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(`#${hexColor}`),
        roughness: materialProperties.roughness || 0.7,
        metalness: materialProperties.metalness || 0.1,
        ...materialProperties
    });
    
    materialCache.set(hexColor, material);
    return material;
}

/**
 * Create a chess queen with specified color
 * @param {Object} options - Configuration options
 * @param {string} options.color - 'white' or 'black'
 * @param {number} options.scale - Scale multiplier (default: 2.4)
 * @param {number} options.seed - Random seed for variation
 * @returns {THREE.Group} - The queen 3D object
 */
export function createChessQueenObject(options = {}) {
    const { 
        color = 'white', 
        scale = 2.4,
        seed = 0
    } = options;
    
    const queenGroup = new THREE.Group();
    queenGroup.userData = { 
        type: 'chess_queen', 
        chessColor: color,
        chessPiece: 'queen'
    };
    
    // Color schemes
    const colorSchemes = {
        white: {
            base: ['FFFFFF', 'F0F0F0', 'E8E8E8'],
            accent: ['CCCCCC', 'D0D0D0'],
            detail: ['B0B0B0', 'A0A0A0'],
            crown: ['FFD700', 'FFC000'], // Gold accents for royalty
            jewel: ['00AAFF'] // Blue jewels
        },
        black: {
            base: ['1A0A0A', '000000', '2A1A1A'],
            accent: ['8B0000', '660000'],
            detail: ['AA0000', 'CC0000'],
            crown: ['FF0000', 'CC0000'], // Red accents for hellish theme
            jewel: ['FF6600'] // Orange jewels
        }
    };
    
    const scheme = colorSchemes[color];
    // Higher detail for chess pieces - smaller voxels
    const CHESS_DETAIL_FACTOR = 0.5; // 2x more detailed voxels
    const voxelScale = VOXEL_SIZE * scale * CHESS_DETAIL_FACTOR;
    
    // Simple seeded random for consistent variation
    const rng = (() => {
        let s = seed;
        return () => {
            s = Math.sin(s) * 10000;
            return s - Math.floor(s);
        };
    })();
    
    // Queen shape: Tall regal figure with elaborate crown
    const queenVoxels = [
        // Base layer (y=0) - 3x3 foundation
        { x: -1, y: 0, z: -1, c: scheme.base[0] },
        { x: 0, y: 0, z: -1, c: scheme.base[0] },
        { x: 1, y: 0, z: -1, c: scheme.base[0] },
        { x: -1, y: 0, z: 0, c: scheme.base[0] },
        { x: 0, y: 0, z: 0, c: scheme.base[0] },
        { x: 1, y: 0, z: 0, c: scheme.base[0] },
        { x: -1, y: 0, z: 1, c: scheme.base[0] },
        { x: 0, y: 0, z: 1, c: scheme.base[0] },
        { x: 1, y: 0, z: 1, c: scheme.base[0] },
        
        // Layer 1 (y=1) - Base platform with decoration
        { x: -1, y: 1, z: -1, c: scheme.base[1] },
        { x: 0, y: 1, z: -1, c: scheme.accent[0] },
        { x: 1, y: 1, z: -1, c: scheme.base[1] },
        { x: -1, y: 1, z: 0, c: scheme.accent[0] },
        { x: 0, y: 1, z: 0, c: scheme.crown[0] },
        { x: 1, y: 1, z: 0, c: scheme.accent[0] },
        { x: -1, y: 1, z: 1, c: scheme.base[1] },
        { x: 0, y: 1, z: 1, c: scheme.accent[0] },
        { x: 1, y: 1, z: 1, c: scheme.base[1] },
        
        // Layer 2 (y=2) - Body begins
        { x: -1, y: 2, z: -1, c: scheme.accent[0] },
        { x: 0, y: 2, z: -1, c: scheme.accent[1] },
        { x: 1, y: 2, z: -1, c: scheme.accent[0] },
        { x: -1, y: 2, z: 0, c: scheme.accent[1] },
        { x: 0, y: 2, z: 0, c: scheme.detail[0] },
        { x: 1, y: 2, z: 0, c: scheme.accent[1] },
        { x: -1, y: 2, z: 1, c: scheme.accent[0] },
        { x: 0, y: 2, z: 1, c: scheme.accent[1] },
        { x: 1, y: 2, z: 1, c: scheme.accent[0] },
        
        // Layer 3 (y=3) - Body continues
        { x: -1, y: 3, z: -1, c: scheme.accent[1] },
        { x: 0, y: 3, z: -1, c: scheme.detail[0] },
        { x: 1, y: 3, z: -1, c: scheme.accent[1] },
        { x: -1, y: 3, z: 0, c: scheme.detail[0] },
        { x: 0, y: 3, z: 0, c: scheme.detail[1] },
        { x: 1, y: 3, z: 0, c: scheme.detail[0] },
        { x: -1, y: 3, z: 1, c: scheme.accent[1] },
        { x: 0, y: 3, z: 1, c: scheme.detail[0] },
        { x: 1, y: 3, z: 1, c: scheme.accent[1] },
        
        // Layer 4 (y=4) - Upper body/neck
        { x: 0, y: 4, z: -1, c: scheme.detail[1] },
        { x: -1, y: 4, z: 0, c: scheme.detail[1] },
        { x: 0, y: 4, z: 0, c: scheme.crown[0] },
        { x: 1, y: 4, z: 0, c: scheme.detail[1] },
        { x: 0, y: 4, z: 1, c: scheme.detail[1] },
        
        // Layer 5 (y=5) - Crown base
        { x: -1, y: 5, z: -1, c: scheme.crown[0] },
        { x: 0, y: 5, z: -1, c: scheme.crown[1] },
        { x: 1, y: 5, z: -1, c: scheme.crown[0] },
        { x: -1, y: 5, z: 0, c: scheme.crown[1] },
        { x: 0, y: 5, z: 0, c: scheme.jewel[0] },
        { x: 1, y: 5, z: 0, c: scheme.crown[1] },
        { x: -1, y: 5, z: 1, c: scheme.crown[0] },
        { x: 0, y: 5, z: 1, c: scheme.crown[1] },
        { x: 1, y: 5, z: 1, c: scheme.crown[0] },
        
        // Layer 6 (y=6) - Crown points begin
        { x: -1, y: 6, z: -1, c: scheme.crown[1] },
        { x: 0, y: 6, z: -1, c: scheme.crown[0] },
        { x: 1, y: 6, z: -1, c: scheme.crown[1] },
        { x: -1, y: 6, z: 0, c: scheme.crown[0] },
        { x: 0, y: 6, z: 0, c: scheme.jewel[0] },
        { x: 1, y: 6, z: 0, c: scheme.crown[0] },
        { x: -1, y: 6, z: 1, c: scheme.crown[1] },
        { x: 0, y: 6, z: 1, c: scheme.crown[0] },
        { x: 1, y: 6, z: 1, c: scheme.crown[1] },
        
        // Layer 7 (y=7) - Crown points
        { x: -1, y: 7, z: -1, c: scheme.crown[1] },
        { x: 1, y: 7, z: -1, c: scheme.crown[1] },
        { x: 0, y: 7, z: 0, c: scheme.crown[0] },
        { x: -1, y: 7, z: 1, c: scheme.crown[1] },
        { x: 1, y: 7, z: 1, c: scheme.crown[1] },
        
        // Layer 8 (y=8) - Tall center crown point
        { x: 0, y: 8, z: 0, c: scheme.crown[0] },
        
        // Layer 9 (y=9) - Crown pinnacle
        { x: 0, y: 9, z: 0, c: scheme.jewel[0] },
        
        // Additional jewels on crown
        { x: -1, y: 6, z: -1, c: scheme.jewel[0] }, // Corner jewels
        { x: 1, y: 6, z: -1, c: scheme.jewel[0] },
        { x: -1, y: 6, z: 1, c: scheme.jewel[0] },
        { x: 1, y: 6, z: 1, c: scheme.jewel[0] },
    ];
    
    // Group voxels by material for optimization
    const voxelsByMaterial = new Map();
    
    for (const voxel of queenVoxels) {
        if (!voxelsByMaterial.has(voxel.c)) {
            voxelsByMaterial.set(voxel.c, []);
        }
        voxelsByMaterial.get(voxel.c).push(voxel);
    }
    
    // Create merged geometry for each material
    for (const [hexColor, voxels] of voxelsByMaterial) {
        const geometries = [];
        
        for (const voxel of voxels) {
            const geometry = new THREE.BoxGeometry(voxelScale * 0.95, voxelScale * 0.95, voxelScale * 0.95);
            geometry.translate(
                voxel.x * voxelScale,
                voxel.y * voxelScale,
                voxel.z * voxelScale
            );
            geometries.push(geometry);
        }
        
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            
            // Special material properties for different elements
            let materialProps = {
                roughness: color === 'black' ? 0.2 : 0.6,
                metalness: color === 'black' ? 0.3 : 0.1
            };
            
            // Crown elements - more metallic and shiny
            if (scheme.crown.includes(hexColor)) {
                materialProps = {
                    roughness: 0.1,
                    metalness: 0.8,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: color === 'black' ? 0.1 : 0.05
                };
            }
            
            // Jewel elements - highly emissive
            if (scheme.jewel.includes(hexColor)) {
                materialProps = {
                    roughness: 0.0,
                    metalness: 0.9,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: color === 'black' ? 0.4 : 0.2
                };
            }
            
            const material = _getMaterialByHex_Cached(hexColor, materialProps);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.layers.set(0); // Ensure piece is on layer 0 for raycasting
            queenGroup.add(mesh);
        }
    }
    
    // Add powerful glow for black pieces (hellish royalty theme)
    if (color === 'black') {
        const glowGeometry = new THREE.SphereGeometry(voxelScale * 3.5, 8, 8);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x660000,
            transparent: true,
            opacity: 0.15,
            side: THREE.BackSide
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.layers.set(0); // Ensure glow is on layer 0 for raycasting
        glowMesh.position.y = voxelScale * 4.5;
        queenGroup.add(glowMesh);
    }
    
    // Position the queen so its base is at y=0
    queenGroup.position.y = 0;
    
    // Ensure the entire group is on layer 0 for raycasting
    queenGroup.layers.set(0);
    
    console.log(`[chessQueenObject] Created ${color} chess queen with ${queenVoxels.length} voxels`);
    
    return queenGroup;
}

/**
 * Helper function to create white queen
 */
export function createWhiteChessQueen(options = {}) {
    return createChessQueenObject({ ...options, color: 'white' });
}

/**
 * Helper function to create black queen
 */
export function createBlackChessQueen(options = {}) {
    return createChessQueenObject({ ...options, color: 'black' });
}