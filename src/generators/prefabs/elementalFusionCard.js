import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Elemental Fusion Card Prefab
 * Creates a tri-elemental fusion of fire, ice, and lightning converging into a powerful fusion core
 */

// Elemental fusion specific colors
const FUSION_COLORS = {
    FIRE_RED: 0xFF4500,        // Orange-red fire
    ICE_BLUE: 0x00BFFF,        // Deep sky blue ice
    LIGHTNING_YELLOW: 0xFFD700, // Gold lightning
    FUSION_PURPLE: 0x8A2BE2,   // Blue-violet fusion core
    ENERGY_WHITE: 0xF0F8FF,    // Alice blue energy
    EMBER_ORANGE: 0xFF6347     // Tomato ember glow
};

/**
 * Create an elemental fusion card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The elemental fusion card 3D model
 */
export function createElementalFusionCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ElementalFusionCard';

    // Materials
    const fireRedMaterial = new THREE.MeshLambertMaterial({
        color: FUSION_COLORS.FIRE_RED,
        emissive: FUSION_COLORS.FIRE_RED,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const iceBlueMaterial = new THREE.MeshLambertMaterial({
        color: FUSION_COLORS.ICE_BLUE,
        emissive: FUSION_COLORS.ICE_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const lightningYellowMaterial = new THREE.MeshLambertMaterial({
        color: FUSION_COLORS.LIGHTNING_YELLOW,
        emissive: FUSION_COLORS.LIGHTNING_YELLOW,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.9
    });

    const fusionPurpleMaterial = new THREE.MeshLambertMaterial({
        color: FUSION_COLORS.FUSION_PURPLE,
        emissive: FUSION_COLORS.FUSION_PURPLE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const energyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: FUSION_COLORS.ENERGY_WHITE,
        emissive: FUSION_COLORS.ENERGY_WHITE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create fire element voxels (top-left quadrant)
    const fireVoxels = [
        // Fire core
        { x: -2, y: 2, z: 0, material: fireRedMaterial },
        { x: -3, y: 3, z: 0, material: fireRedMaterial },
        { x: -1, y: 3, z: 0, material: fireRedMaterial },
        { x: -2, y: 4, z: 0, material: fireRedMaterial },
        
        // Fire flames
        { x: -4, y: 2, z: 0, material: fireRedMaterial },
        { x: -3, y: 1, z: 0, material: fireRedMaterial },
        { x: -1, y: 1, z: 0, material: fireRedMaterial },
        { x: -2, y: 5, z: 0, material: fireRedMaterial },
        
        // Fire embers
        { x: -4, y: 4, z: 0, material: energyWhiteMaterial },
        { x: -1, y: 2, z: 0, material: energyWhiteMaterial }
    ];

    // Create ice element voxels (top-right quadrant)
    const iceVoxels = [
        // Ice core
        { x: 2, y: 2, z: 0, material: iceBlueMaterial },
        { x: 3, y: 3, z: 0, material: iceBlueMaterial },
        { x: 1, y: 3, z: 0, material: iceBlueMaterial },
        { x: 2, y: 4, z: 0, material: iceBlueMaterial },
        
        // Ice crystals
        { x: 4, y: 2, z: 0, material: iceBlueMaterial },
        { x: 3, y: 1, z: 0, material: iceBlueMaterial },
        { x: 1, y: 1, z: 0, material: iceBlueMaterial },
        { x: 2, y: 5, z: 0, material: iceBlueMaterial },
        
        // Ice shards
        { x: 4, y: 4, z: 0, material: energyWhiteMaterial },
        { x: 1, y: 2, z: 0, material: energyWhiteMaterial }
    ];

    // Create lightning element voxels (bottom center)
    const lightningVoxels = [
        // Lightning bolt
        { x: 0, y: -2, z: 0, material: lightningYellowMaterial },
        { x: 1, y: -3, z: 0, material: lightningYellowMaterial },
        { x: -1, y: -3, z: 0, material: lightningYellowMaterial },
        { x: 0, y: -4, z: 0, material: lightningYellowMaterial },
        
        // Lightning branches
        { x: 2, y: -4, z: 0, material: lightningYellowMaterial },
        { x: -2, y: -4, z: 0, material: lightningYellowMaterial },
        { x: 1, y: -5, z: 0, material: lightningYellowMaterial },
        { x: -1, y: -5, z: 0, material: lightningYellowMaterial },
        
        // Lightning sparks
        { x: 0, y: -6, z: 0, material: energyWhiteMaterial },
        { x: 0, y: -1, z: 0, material: energyWhiteMaterial }
    ];

    // Create fusion core voxels (center)
    const fusionVoxels = [
        // Fusion core
        { x: 0, y: 0, z: 0, material: fusionPurpleMaterial },
        { x: 1, y: 1, z: 0, material: fusionPurpleMaterial },
        { x: -1, y: 1, z: 0, material: fusionPurpleMaterial },
        { x: 1, y: -1, z: 0, material: fusionPurpleMaterial },
        { x: -1, y: -1, z: 0, material: fusionPurpleMaterial },
        
        // Fusion energy
        { x: 0, y: 2, z: 0, material: fusionPurpleMaterial },
        { x: 0, y: -2, z: 0, material: fusionPurpleMaterial },
        { x: 2, y: 0, z: 0, material: fusionPurpleMaterial },
        { x: -2, y: 0, z: 0, material: fusionPurpleMaterial }
    ];

    // Create all voxels
    [...fireVoxels, ...iceVoxels, ...lightningVoxels, ...fusionVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.9, // Slightly compact
            voxel.y * VOXEL_SIZE * 0.9, // Slightly compact
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === fusionPurpleMaterial ? 'fusion' :
                                 voxel.material === energyWhiteMaterial ? 'energy' :
                                 voxel.material === fireRedMaterial ? 'fire' :
                                 voxel.material === iceBlueMaterial ? 'ice' : 'lightning';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        fusionPhase: 0,
        elementalPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update elemental fusion card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateElementalFusionCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Fusion pulsing effect
    cardGroup.userData.fusionPhase += deltaTime * 2.5;
    const fusionValue = (Math.sin(cardGroup.userData.fusionPhase) + 1) * 0.5; // 0 to 1

    // Elemental energy pulsing
    cardGroup.userData.elementalPulse += deltaTime * 3.0;
    const pulseValue = (Math.sin(cardGroup.userData.elementalPulse) + 1) * 0.5; // 0 to 1

    // Apply elemental animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different animation patterns for different element types
            switch (child.userData.voxelType) {
                case 'fire':
                    child.material.opacity = baseOpacity * (0.8 + fusionValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + fusionValue * 0.6;
                    }
                    // Fire flickering motion
                    child.position.y += Math.sin(time * 8 + child.position.x * 5) * 0.005;
                    break;
                case 'ice':
                    child.material.opacity = baseOpacity * (0.7 + pulseValue * 0.3);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + pulseValue * 0.4;
                    }
                    // Ice crystalline shimmer
                    child.position.x += Math.cos(time * 4 + child.position.y * 3) * 0.003;
                    break;
                case 'lightning':
                    child.material.opacity = baseOpacity * (0.9 + fusionValue * 0.1);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.9 + Math.sin(time * 12) * 0.6;
                    }
                    // Lightning crackling
                    child.position.x += (Math.random() - 0.5) * 0.004;
                    child.position.y += (Math.random() - 0.5) * 0.004;
                    break;
                case 'fusion':
                    child.material.opacity = baseOpacity * (0.8 + fusionValue * 0.2);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 1.0 + fusionValue * 0.8;
                    }
                    // Fusion core pulsing
                    const pulseScale = 1.0 + fusionValue * 0.15;
                    child.scale.setScalar(pulseScale);
                    break;
                case 'energy':
                    child.material.opacity = baseOpacity * (0.6 + pulseValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.7 + pulseValue * 0.5;
                    }
                    // Energy floating motion
                    child.position.y += Math.sin(time * 6 + child.position.x * 4) * 0.006;
                    break;
            }
        }
    });

    // Overall fusion effect rotation
    cardGroup.rotation.z = Math.sin(time * 1.5) * 0.1;
}

// Export the elemental fusion card data for the loot system
export const ELEMENTAL_FUSION_CARD_DATA = {
    name: 'Elemental Fusion',
    description: 'Combines fire, ice, and lightning into a devastating tri-elemental blast that adapts to enemy weaknesses and deals bonus damage.',
    category: 'card',
    rarity: 'legendary',
    effect: 'elemental_fusion',
    effectValue: 12,
    createFunction: createElementalFusionCard,
    updateFunction: updateElementalFusionCardAnimation,
    voxelModel: 'elemental_fusion_card',
    glow: {
        color: 0x8A2BE2,
        intensity: 1.2
    }
};

export default createElementalFusionCard;