import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

/**
 * Creates a voxel-based chess knight object
 * Knights are horse-shaped pieces with distinctive silhouette
 * White knights use light stone colors, black knights use dark obsidian colors
 */

// Material cache for performance
const materialCache = new Map();

function _getMaterialByHex_Cached(hexColor, materialProperties = {}) {
    if (materialCache.has(hexColor)) {
        return materialCache.get(hexColor);
    }
    
    const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(`#${hexColor}`),
        roughness: materialProperties.roughness || 0.7,
        metalness: materialProperties.metalness || 0.1,
        ...materialProperties
    });
    
    materialCache.set(hexColor, material);
    return material;
}

/**
 * Create a chess knight with specified color
 * @param {Object} options - Configuration options
 * @param {string} options.color - 'white' or 'black'
 * @param {number} options.scale - Scale multiplier (default: 2.4)
 * @param {number} options.seed - Random seed for variation
 * @returns {THREE.Group} - The knight 3D object
 */
export function createChessKnightObject(options = {}) {
    const { 
        color = 'white', 
        scale = 2.4,
        seed = 0
    } = options;
    
    const knightGroup = new THREE.Group();
    knightGroup.userData = { 
        type: 'chess_knight', 
        chessColor: color,
        chessPiece: 'knight'
    };
    
    // Color schemes
    const colorSchemes = {
        white: {
            base: ['FFFFFF', 'F0F0F0', 'E8E8E8'],
            accent: ['CCCCCC', 'D0D0D0'],
            detail: ['B0B0B0', 'A0A0A0']
        },
        black: {
            base: ['1A0A0A', '000000', '2A1A1A'],
            accent: ['8B0000', '660000'],
            detail: ['AA0000', 'CC0000']
        }
    };
    
    const scheme = colorSchemes[color];
    // Higher detail for chess pieces - smaller voxels
    const CHESS_DETAIL_FACTOR = 0.5; // 2x more detailed voxels
    const voxelScale = VOXEL_SIZE * scale * CHESS_DETAIL_FACTOR;
    
    // Simple seeded random for consistent variation
    const rng = (() => {
        let s = seed;
        return () => {
            s = Math.sin(s) * 10000;
            return s - Math.floor(s);
        };
    })();
    
    // Knight shape: Horse head silhouette facing forward
    const knightVoxels = [
        // Base layer (y=0) - 3x3 foundation
        { x: -1, y: 0, z: -1, c: scheme.base[0] },
        { x: 0, y: 0, z: -1, c: scheme.base[0] },
        { x: 1, y: 0, z: -1, c: scheme.base[0] },
        { x: -1, y: 0, z: 0, c: scheme.base[0] },
        { x: 0, y: 0, z: 0, c: scheme.base[0] },
        { x: 1, y: 0, z: 0, c: scheme.base[0] },
        { x: -1, y: 0, z: 1, c: scheme.base[0] },
        { x: 0, y: 0, z: 1, c: scheme.base[0] },
        { x: 1, y: 0, z: 1, c: scheme.base[0] },
        
        // Layer 1 (y=1) - Horse body base
        { x: -1, y: 1, z: -1, c: scheme.base[1] },
        { x: 0, y: 1, z: -1, c: scheme.base[1] },
        { x: 1, y: 1, z: -1, c: scheme.base[1] },
        { x: -1, y: 1, z: 0, c: scheme.base[1] },
        { x: 0, y: 1, z: 0, c: scheme.accent[0] },
        { x: 1, y: 1, z: 0, c: scheme.base[1] },
        { x: -1, y: 1, z: 1, c: scheme.base[1] },
        { x: 0, y: 1, z: 1, c: scheme.base[1] },
        { x: 1, y: 1, z: 1, c: scheme.base[1] },
        
        // Layer 2 (y=2) - Horse neck begins
        { x: -1, y: 2, z: -1, c: scheme.accent[0] },
        { x: 0, y: 2, z: -1, c: scheme.accent[0] },
        { x: 1, y: 2, z: -1, c: scheme.accent[0] },
        { x: -1, y: 2, z: 0, c: scheme.accent[0] },
        { x: 0, y: 2, z: 0, c: scheme.base[1] },
        { x: 1, y: 2, z: 0, c: scheme.accent[0] },
        { x: 0, y: 2, z: 1, c: scheme.accent[1] },
        { x: 1, y: 2, z: 1, c: scheme.accent[1] },
        
        // Layer 3 (y=3) - Horse neck continues
        { x: -1, y: 3, z: -1, c: scheme.accent[1] },
        { x: 0, y: 3, z: -1, c: scheme.accent[1] },
        { x: 1, y: 3, z: -1, c: scheme.accent[1] },
        { x: 0, y: 3, z: 0, c: scheme.detail[0] },
        { x: 1, y: 3, z: 0, c: scheme.detail[0] },
        { x: 0, y: 3, z: 1, c: scheme.detail[0] },
        { x: 1, y: 3, z: 1, c: scheme.detail[0] },
        
        // Layer 4 (y=4) - Horse head begins
        { x: 0, y: 4, z: -1, c: scheme.detail[0] },
        { x: 1, y: 4, z: -1, c: scheme.detail[0] },
        { x: 0, y: 4, z: 0, c: scheme.detail[1] },
        { x: 1, y: 4, z: 0, c: scheme.detail[1] },
        { x: 0, y: 4, z: 1, c: scheme.detail[0] },
        { x: 1, y: 4, z: 1, c: scheme.detail[0] },
        
        // Layer 5 (y=5) - Horse head continues
        { x: 0, y: 5, z: -1, c: scheme.detail[1] },
        { x: 1, y: 5, z: -1, c: scheme.detail[1] },
        { x: 0, y: 5, z: 0, c: scheme.detail[1] },
        { x: 1, y: 5, z: 0, c: scheme.detail[1] },
        { x: 0, y: 5, z: 1, c: scheme.detail[1] },
        
        // Layer 6 (y=6) - Horse ears and mane
        { x: 0, y: 6, z: -1, c: scheme.detail[1] },
        { x: 1, y: 6, z: -1, c: scheme.detail[1] },
        { x: 0, y: 6, z: 0, c: scheme.detail[1] },
        
        // Layer 7 (y=7) - Top of ears
        { x: 0, y: 7, z: -1, c: scheme.detail[1] },
        { x: 1, y: 7, z: -1, c: scheme.detail[1] },
        
        // Eyes (special detail voxels)
        { x: 0, y: 5, z: -2, c: color === 'black' ? 'FF0000' : '000000' }, // Left eye
        { x: 1, y: 5, z: -2, c: color === 'black' ? 'FF0000' : '000000' }, // Right eye
    ];
    
    // Group voxels by material for optimization
    const voxelsByMaterial = new Map();
    
    for (const voxel of knightVoxels) {
        if (!voxelsByMaterial.has(voxel.c)) {
            voxelsByMaterial.set(voxel.c, []);
        }
        voxelsByMaterial.get(voxel.c).push(voxel);
    }
    
    // Create merged geometry for each material
    for (const [hexColor, voxels] of voxelsByMaterial) {
        const geometries = [];
        
        for (const voxel of voxels) {
            const geometry = new THREE.BoxGeometry(voxelScale * 0.8, voxelScale * 0.8, voxelScale * 0.8);
            geometry.translate(
                voxel.x * voxelScale,
                voxel.y * voxelScale,
                voxel.z * voxelScale
            );
            geometries.push(geometry);
        }
        
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            
            // Special material properties for eyes
            let materialProps = {
                roughness: color === 'black' ? 0.2 : 0.6,
                metalness: color === 'black' ? 0.3 : 0.1
            };
            
            if (hexColor === 'FF0000' || hexColor === '000000') {
                // Eye material - emissive
                materialProps = {
                    ...materialProps,
                    emissive: new THREE.Color(`#${hexColor}`),
                    emissiveIntensity: hexColor === 'FF0000' ? 0.3 : 0.1
                };
            }
            
            const material = _getMaterialByHex_Cached(hexColor, materialProps);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.layers.set(0); // Ensure piece is on layer 0 for raycasting
            knightGroup.add(mesh);
        }
    }
    
    // Add subtle glow for black pieces (hellish theme)
    if (color === 'black') {
        const glowGeometry = new THREE.SphereGeometry(voxelScale * 3, 8, 8);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x440000,
            transparent: true,
            opacity: 0.1,
            side: THREE.BackSide
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.layers.set(0); // Ensure glow is on layer 0 for raycasting
        glowMesh.position.y = voxelScale * 3.5;
        knightGroup.add(glowMesh);
    }
    
    // Position the knight so its base is at y=0
    knightGroup.position.y = 0;
    
    // Ensure the entire group is on layer 0 for raycasting
    knightGroup.layers.set(0);
    
    console.log(`[chessKnightObject] Created ${color} chess knight with ${knightVoxels.length} voxels`);
    
    return knightGroup;
}

/**
 * Helper function to create white knight
 */
export function createWhiteChessKnight(options = {}) {
    return createChessKnightObject({ ...options, color: 'white' });
}

/**
 * Helper function to create black knight
 */
export function createBlackChessKnight(options = {}) {
    return createChessKnightObject({ ...options, color: 'black' });
}