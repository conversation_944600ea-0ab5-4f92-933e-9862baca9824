import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Meteor Storm Card Prefab
 * Unleashes multiple fiery meteors at target locations
 */

// Meteor storm specific colors
const METEOR_COLORS = {
    FIRE_RED: 0xFF4500,             // Orange red fire core
    FLAME_ORANGE: 0xFF8C00,         // Dark orange flames
    LAVA_YELLOW: 0xFFD700,          // Gold lava glow
    SMOKE_GRAY: 0x696969,           // Dim gray smoke trails
    EMBER_WHITE: 0xFFF8DC,          // Cornsilk ember sparks
    MOLTEN_CRIMSON: 0xDC143C        // Crimson molten rock
};

/**
 * Create a meteor storm card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The meteor storm card 3D model
 */
export function createMeteorStormCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'MeteorStormCard';

    // Meteor storm materials
    const fireRedMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.FIRE_RED,
        emissive: METEOR_COLORS.FIRE_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.95
    });

    const flameOrangeMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.FLAME_ORANGE,
        emissive: METEOR_COLORS.FLAME_ORANGE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const lavaYellowMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.LAVA_YELLOW,
        emissive: METEOR_COLORS.LAVA_YELLOW,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.85
    });

    const smokeGrayMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.SMOKE_GRAY,
        emissive: METEOR_COLORS.SMOKE_GRAY,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.6
    });

    const emberWhiteMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.EMBER_WHITE,
        emissive: METEOR_COLORS.EMBER_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const moltenCrimsonMaterial = new THREE.MeshLambertMaterial({
        color: METEOR_COLORS.MOLTEN_CRIMSON,
        emissive: METEOR_COLORS.MOLTEN_CRIMSON,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central meteor (large falling meteor)
    const centralMeteorVoxels = [
        // Core meteor body
        { x: 0.0, y: 0.0, z: 0, material: fireRedMaterial },
        { x: -0.04, y: 0.0, z: 0, material: fireRedMaterial },
        { x: 0.04, y: 0.0, z: 0, material: fireRedMaterial },
        { x: 0.0, y: 0.04, z: 0, material: fireRedMaterial },
        { x: 0.0, y: -0.04, z: 0, material: fireRedMaterial },
        
        // Meteor surface detail
        { x: -0.04, y: 0.04, z: 0, material: moltenCrimsonMaterial },
        { x: 0.04, y: 0.04, z: 0, material: moltenCrimsonMaterial },
        { x: 0.04, y: -0.04, z: 0, material: moltenCrimsonMaterial },
        { x: -0.04, y: -0.04, z: 0, material: moltenCrimsonMaterial },
        
        // Molten core (bright center)
        { x: 0.0, y: 0.0, z: 0.02, material: lavaYellowMaterial },
        { x: -0.02, y: 0.0, z: 0.02, material: lavaYellowMaterial },
        { x: 0.02, y: 0.0, z: 0.02, material: lavaYellowMaterial },
        { x: 0.0, y: 0.02, z: 0.02, material: lavaYellowMaterial },
        { x: 0.0, y: -0.02, z: 0.02, material: lavaYellowMaterial }
    ];

    // Flame trails (fire trailing behind meteors)
    const flameTrailsVoxels = [
        // Upper flame trail
        { x: 0.0, y: 0.12, z: -0.04, material: flameOrangeMaterial },
        { x: 0.0, y: 0.16, z: -0.08, material: flameOrangeMaterial },
        { x: 0.0, y: 0.20, z: -0.12, material: smokeGrayMaterial },
        { x: -0.04, y: 0.14, z: -0.06, material: flameOrangeMaterial },
        { x: 0.04, y: 0.14, z: -0.06, material: flameOrangeMaterial },
        
        // Side flame trails
        { x: -0.08, y: 0.08, z: -0.04, material: flameOrangeMaterial },
        { x: 0.08, y: 0.08, z: -0.04, material: flameOrangeMaterial },
        { x: -0.12, y: 0.12, z: -0.08, material: smokeGrayMaterial },
        { x: 0.12, y: 0.12, z: -0.08, material: smokeGrayMaterial },
        
        // Lower flame wisps
        { x: 0.0, y: -0.08, z: -0.04, material: flameOrangeMaterial },
        { x: -0.06, y: -0.06, z: -0.04, material: flameOrangeMaterial },
        { x: 0.06, y: -0.06, z: -0.04, material: flameOrangeMaterial },
        { x: 0.0, y: -0.12, z: -0.08, material: smokeGrayMaterial }
    ];

    // Surrounding meteors (smaller meteors around the main one)
    const surroundingMeteorsVoxels = [
        // Left meteor cluster
        { x: -0.20, y: 0.16, z: 0.04, material: fireRedMaterial },
        { x: -0.24, y: 0.20, z: 0.08, material: moltenCrimsonMaterial },
        { x: -0.16, y: 0.12, z: 0.02, material: lavaYellowMaterial },
        { x: -0.22, y: 0.18, z: 0.06, material: flameOrangeMaterial },
        
        // Right meteor cluster
        { x: 0.20, y: 0.16, z: -0.04, material: fireRedMaterial },
        { x: 0.24, y: 0.20, z: -0.08, material: moltenCrimsonMaterial },
        { x: 0.16, y: 0.12, z: -0.02, material: lavaYellowMaterial },
        { x: 0.22, y: 0.18, z: -0.06, material: flameOrangeMaterial },
        
        // Bottom meteor cluster
        { x: -0.12, y: -0.20, z: 0.06, material: fireRedMaterial },
        { x: 0.12, y: -0.20, z: -0.06, material: fireRedMaterial },
        { x: 0.0, y: -0.24, z: 0.04, material: moltenCrimsonMaterial },
        { x: -0.08, y: -0.16, z: 0.04, material: lavaYellowMaterial },
        { x: 0.08, y: -0.16, z: -0.04, material: lavaYellowMaterial },
        
        // Distant meteors
        { x: -0.28, y: 0.08, z: 0.10, material: smokeGrayMaterial },
        { x: 0.28, y: 0.08, z: -0.10, material: smokeGrayMaterial },
        { x: 0.0, y: 0.32, z: 0.12, material: smokeGrayMaterial }
    ];

    // Ember sparks (bright sparks flying around)
    const emberSparksVoxels = [
        // Inner spark ring
        { x: -0.10, y: 0.10, z: 0.08, material: emberWhiteMaterial },
        { x: 0.10, y: 0.10, z: 0.08, material: emberWhiteMaterial },
        { x: 0.10, y: -0.10, z: 0.08, material: emberWhiteMaterial },
        { x: -0.10, y: -0.10, z: 0.08, material: emberWhiteMaterial },
        
        // Outer spark ring
        { x: -0.16, y: 0.0, z: 0.10, material: lavaYellowMaterial },
        { x: 0.16, y: 0.0, z: 0.10, material: lavaYellowMaterial },
        { x: 0.0, y: 0.16, z: 0.10, material: lavaYellowMaterial },
        { x: 0.0, y: -0.16, z: 0.10, material: lavaYellowMaterial },
        
        // Diagonal sparks
        { x: -0.14, y: 0.14, z: 0.12, material: emberWhiteMaterial },
        { x: 0.14, y: 0.14, z: 0.12, material: emberWhiteMaterial },
        { x: 0.14, y: -0.14, z: 0.12, material: emberWhiteMaterial },
        { x: -0.14, y: -0.14, z: 0.12, material: emberWhiteMaterial },
        
        // Random floating sparks
        { x: -0.26, y: 0.18, z: 0.14, material: emberWhiteMaterial },
        { x: 0.26, y: 0.18, z: 0.14, material: emberWhiteMaterial },
        { x: 0.18, y: -0.26, z: 0.14, material: lavaYellowMaterial },
        { x: -0.18, y: -0.26, z: 0.14, material: lavaYellowMaterial },
        
        // Distant sparks
        { x: -0.30, y: 0.25, z: 0.16, material: flameOrangeMaterial },
        { x: 0.30, y: 0.25, z: 0.16, material: flameOrangeMaterial },
        { x: 0.25, y: -0.30, z: 0.16, material: flameOrangeMaterial },
        { x: -0.25, y: -0.30, z: 0.16, material: flameOrangeMaterial }
    ];

    // Create central meteor group
    const centralMeteorGroup = new THREE.Group();
    centralMeteorGroup.name = 'centralMeteor';

    // Add central meteor voxels
    centralMeteorVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        centralMeteorGroup.add(mesh);
    });

    // Create flame trails group
    const flameTrailsGroup = new THREE.Group();
    flameTrailsGroup.name = 'flameTrails';

    // Add flame trails voxels
    flameTrailsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.trailPhase = index * 0.2; // Stagger animation
        flameTrailsGroup.add(mesh);
    });

    // Create surrounding meteors group
    const surroundingMeteorsGroup = new THREE.Group();
    surroundingMeteorsGroup.name = 'surroundingMeteors';

    // Add surrounding meteors voxels
    surroundingMeteorsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.meteorPhase = index * 0.15; // Stagger animation
        surroundingMeteorsGroup.add(mesh);
    });

    // Create ember sparks group
    const emberSparksGroup = new THREE.Group();
    emberSparksGroup.name = 'emberSparks';

    // Add ember sparks voxels
    emberSparksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sparkPhase = index * 0.1; // Stagger animation
        emberSparksGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(centralMeteorGroup);
    cardGroup.add(flameTrailsGroup);
    cardGroup.add(surroundingMeteorsGroup);
    cardGroup.add(emberSparksGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        meteorFall: 0,
        flameFlicker: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update meteor storm card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateMeteorStormCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.meteorFall += deltaTime * 2.5; // Meteor falling speed
    cardGroup.userData.flameFlicker += deltaTime * 5.0; // Flame flickering speed

    const time = cardGroup.userData.animationTime;
    const meteorFall = cardGroup.userData.meteorFall;
    const flameFlicker = cardGroup.userData.flameFlicker;

    // Animate central meteor (pulsing molten core)
    const centralMeteorGroup = cardGroup.getObjectByName('centralMeteor');
    if (centralMeteorGroup) {
        // Meteor core pulsing (intense heat)
        const corePulse = 0.9 + Math.sin(flameFlicker * 3.0) * 0.1;
        centralMeteorGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * corePulse;
            }
        });
    }

    // Animate flame trails (flickering fire)
    const flameTrailsGroup = cardGroup.getObjectByName('flameTrails');
    if (flameTrailsGroup) {
        // Flame trail flickering and flowing motion
        flameTrailsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.trailPhase !== undefined) {
                const trailTime = flameFlicker + mesh.userData.trailPhase;
                
                // Flame flowing motion (upward and sideways)
                const flowX = Math.sin(trailTime * 2.5) * 0.003;
                const flowY = Math.sin(trailTime * 1.8) * 0.005;
                const flowZ = Math.cos(trailTime * 3.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + flowX;
                mesh.position.y = mesh.userData.originalPosition.y + flowY;
                mesh.position.z = mesh.userData.originalPosition.z + flowZ;
                
                // Flame intensity flickering
                const flameIntensity = 0.8 + Math.sin(trailTime * 4.5) * 0.2;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * flameIntensity;
                }
                
                // Flame opacity flickering (fire effect)
                const flameOpacity = 0.7 + Math.sin(trailTime * 6.0) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * flameOpacity;
                }
            }
        });
    }

    // Animate surrounding meteors (falling motion)
    const surroundingMeteorsGroup = cardGroup.getObjectByName('surroundingMeteors');
    if (surroundingMeteorsGroup) {
        // Slow falling motion for distant meteors
        const fallOffset = Math.sin(meteorFall * 0.5) * 0.01;
        surroundingMeteorsGroup.position.y = fallOffset;
        
        // Individual meteor animations
        surroundingMeteorsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.meteorPhase !== undefined) {
                const meteorTime = meteorFall + mesh.userData.meteorPhase;
                
                // Meteor rotation (tumbling through space)
                mesh.rotation.x = meteorTime * 1.2;
                mesh.rotation.z = meteorTime * 0.8;
                
                // Meteor heat pulsing
                const heatPulse = 0.8 + Math.sin(meteorTime * 2.8) * 0.2;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * heatPulse;
                }
            }
        });
    }

    // Animate ember sparks (floating and twinkling)
    const emberSparksGroup = cardGroup.getObjectByName('emberSparks');
    if (emberSparksGroup) {
        emberSparksGroup.rotation.y = time * 1.5; // Slow rotation
        
        // Ember spark floating and twinkling
        emberSparksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparkPhase !== undefined) {
                const sparkTime = flameFlicker + mesh.userData.sparkPhase;
                
                // Floating motion (random drift)
                const floatX = Math.sin(sparkTime * 2.2) * 0.004;
                const floatY = Math.cos(sparkTime * 1.7) * 0.004;
                const floatZ = Math.sin(sparkTime * 2.8) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + floatX;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                mesh.position.z = mesh.userData.originalPosition.z + floatZ;
                
                // Spark twinkling (bright flashes)
                const twinkle = Math.sin(sparkTime * 8.0) > 0.6 ? 1.0 : 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * twinkle;
                }
                
                // Spark scale pulsing
                const sparkScale = 1 + Math.sin(sparkTime * 5.5) * 0.3;
                mesh.scale.setScalar(sparkScale);
            }
        });
    }

    // Overall meteor storm pulsing (intense energy)
    const stormPulse = 1 + Math.sin(time * 2.8) * 0.08;
    cardGroup.scale.setScalar(0.8 * stormPulse);
}

// Export the meteor storm card data for the loot system
export const METEOR_STORM_CARD_DATA = {
    name: 'Meteor Storm',
    description: 'Calls down a devastating rain of fiery meteors at multiple target locations, each dealing massive impact and fire damage to enemies within the blast radius.',
    category: 'card',
    rarity: 'epic',
    effect: 'meteor_storm',
    effectValue: 5, // Number of meteors summoned
    createFunction: createMeteorStormCard,
    updateFunction: updateMeteorStormCardAnimation,
    voxelModel: 'meteor_storm_card',
    glow: {
        color: 0xFF4500,
        intensity: 1.5
    }
};

export default createMeteorStormCard;