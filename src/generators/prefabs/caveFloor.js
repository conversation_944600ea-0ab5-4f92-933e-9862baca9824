import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    caveFloorBaseMaterial, caveFloorVariant1Material, caveFloorCrackMaterial, dustMaterial,
    mulberry32, _getMaterialByHex_Cached, getOrCreateGeometry
} from './shared.js';

/**
 * Creates a cave-like floor surface using randomized brown/earth tone voxels.
 * Uses base VOXEL_SIZE for detail and adds overlay patches.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Mesh|THREE.Group|null} The merged floor mesh/group or null if empty.
 */
export function createCaveFloor(width, depth, roomData) {
    const geometriesByMaterial = {};
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use double environment scale for floors to reduce voxel count
    const FLOOR_SCALE = ENVIRONMENT_PIXEL_SCALE * 2;

    // Grid dimensions
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / FLOOR_SCALE);
    const numZ_env = Math.ceil(numZ / FLOOR_SCALE);

    // Centering offsets
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    const floorY = 0; // Base floor Y position
    const overlayOffsetY = VOXEL_SIZE * 5.0; // EXTREME offset - 5x voxel height to completely eliminate z-fighting

    // CONDITIONAL: Disable overlays for event rooms to prevent z-fighting with mystical floor
    let overlayPatchSpawnProbability = 0.06; // Default probability
    if (roomData && roomData.type === 'EVENT') {
        overlayPatchSpawnProbability = 0.0; // DISABLE cave floor overlays in event rooms
        console.log(`[CaveFloor] Disabled cave floor overlays for EVENT room ${roomData.id} to prevent z-fighting`);
    }
    const crackOverlayFraction = 0.4;
    const minPatchSize = 2; // Min env cells in a patch (reduced from 4)
    const maxPatchSize = 6; // Max env cells in a patch (reduced from 12)

    // Use cached geometry for floor voxels
    const floorVoxelSize = VOXEL_SIZE * FLOOR_SCALE;
    const floorVoxelGeo = getOrCreateGeometry(
        `floor_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, VOXEL_SIZE, floorVoxelSize)
    );

    const overlayGrid = Array(numZ_env).fill(null).map(() => Array(numX_env).fill(false)); // Track overlay placement

    // --- Use Seeded PRNG ---
    const roomSeed = roomData ? roomData.id * 47 + 29 : Date.now(); // Use room ID for seed
    const random = mulberry32(roomSeed);
    // ---------------------

    // --- Pass 1: Generate Base Floor with Unified Curvature ---
    // Calculate room-wide curvature pattern that all tiles will follow
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            let baseMaterial;
            const roll = random();
            if (roll < 0.15) baseMaterial = caveFloorCrackMaterial;
            else if (roll < 0.5) baseMaterial = caveFloorVariant1Material;
            else baseMaterial = caveFloorBaseMaterial;

            const baseX = ex * floorVoxelSize - offsetX;
            const baseZ = ez * floorVoxelSize - offsetZ;

            // CRITICAL FIX: Use global room coordinates for curvature calculation
            // This ensures continuity between separate floor segments in complex room shapes
            const globalX = baseX + (roomData.segmentPosition ? roomData.segmentPosition.x : 0);
            const globalZ = baseZ + (roomData.segmentPosition ? roomData.segmentPosition.z : 0);

            // Create unified terrain variation using room-wide coordinates
            // Use the same algorithm as the fog stencil for consistency
            const worldX = globalX;
            // FIXED: Check if this is a U-shaped room and adjust Z coordinate accordingly
            let worldZ = -globalZ; // Default: Invert Z coordinate for proper north/south alignment

            // For U-shaped rooms, apply additional 180-degree rotation to match curvature expectation
            if (roomData && roomData.shapeKey === 'U_SHAPE_DOWN') {
                worldZ = globalZ; // Don't invert for U-shaped rooms - use original Z
                // Also apply 180-degree rotation to the X coordinate
                // worldX = -globalX; // Uncomment if X also needs rotation
            }

            // Large-scale room curvature (similar to fog stencil approach)
            const largeWaves = Math.sin(worldX * 0.08) * Math.cos(worldZ * 0.06) * 0.2;
            const mediumWaves = (Math.sin(worldX * 0.15) + Math.cos(worldZ * 0.18)) * 0.12;
            const smallDetails = (Math.sin(worldX * 0.4) * Math.cos(worldZ * 0.35)) * 0.06;
            const ridges = Math.sin(worldX * 0.1 + worldZ * 0.08) * 0.15;

            // FIXED: Use position-based variation with global coordinates to ensure consistency between adjacent tiles
            // This ensures neighboring tiles have similar variations for smooth transitions across segments
            const tileVariationX = Math.sin(globalX * 0.7) * 0.01; // Position-based X variation using global coords
            const tileVariationZ = Math.cos(globalZ * 0.8) * 0.01; // Position-based Z variation using global coords
            const tileVariation = tileVariationX + tileVariationZ; // Combined position-based variation

            // Combine all height variations for unified floor surface
            const heightVariation = largeWaves + mediumWaves + smallDetails + ridges + tileVariation;
            const finalY = floorY + heightVariation;

            // FIXED: Use position-based rotation with global coordinates for consistent tile alignment across segments
            const rotationX = Math.sin(globalX * 0.5 + globalZ * 0.3) * 0.015; // Position-based X rotation using global coords
            const rotationZ = Math.cos(globalX * 0.4 + globalZ * 0.6) * 0.015; // Position-based Z rotation using global coords

            // Create transformation matrix with position and minimal rotation
            tempMatrix.makeRotationFromEuler(new THREE.Euler(rotationX, 0, rotationZ));
            tempMatrix.setPosition(baseX, finalY, baseZ);

            addGeometry(floorVoxelGeo, baseMaterial, tempMatrix);
        }
    }
    // --- End Pass 1 ---

    // --- Pass 2: Generate Overlay Patches ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Check if already overlaid OR random chance fails
            if (overlayGrid[ez][ex] || random() > overlayPatchSpawnProbability) {
                continue;
            }

            // Start a new patch
            const patchSize = minPatchSize + Math.floor(random() * (maxPatchSize - minPatchSize + 1));
            const overlayMaterial = (random() < crackOverlayFraction)
                                      ? caveFloorCrackMaterial
                                      : dustMaterial;
            const currentPatchCells = [];
            const queue = [[ex, ez]]; // Start flood fill / random walk queue
            overlayGrid[ez][ex] = true; // Mark starting cell as overlaid
            currentPatchCells.push([ex, ez]);

            let attempts = 0; // Prevent infinite loops if area is small
            const maxAttempts = patchSize * 5;

            while (currentPatchCells.length < patchSize && queue.length > 0 && attempts < maxAttempts) {
                attempts++;
                const [cx, cz] = queue.shift(); // Get current cell

                // Explore neighbours randomly
                const neighbours = [
                    [cx + 1, cz], [cx - 1, cz], [cx, cz + 1], [cx, cz - 1]
                ].sort(() => random() - 0.5); // Shuffle neighbours

                for (const [nx, nz] of neighbours) {
                    // Check bounds and if already part of an overlay
                    if (nx >= 0 && nx < numX_env && nz >= 0 && nz < numZ_env && !overlayGrid[nz][nx]) {
                        overlayGrid[nz][nx] = true; // Mark as overlaid
                        currentPatchCells.push([nx, nz]);
                        queue.push([nx, nz]); // Add to queue for further exploration
                        if (currentPatchCells.length >= patchSize) break; // Reached desired size
                    }
                }
            }

            // Add geometry for all cells in this patch
            for (const [patchEX, patchEZ] of currentPatchCells) {
                const posX = patchEX * floorVoxelSize - offsetX;
                const posZ = patchEZ * floorVoxelSize - offsetZ;
                tempMatrix.makeTranslation(posX, floorY + overlayOffsetY, posZ); // Apply Y offset
                addGeometry(floorVoxelGeo, overlayMaterial, tempMatrix);
            }
        }
    }
    // --- End Pass 2 ---

    // --- Merge Geometries with Polygon Offset ---
    const finalMeshes = [];
    const baseHex = caveFloorBaseMaterial.color.getHexString();
    const variant1Hex = caveFloorVariant1Material.color.getHexString();
    const crackHex = caveFloorCrackMaterial.color.getHexString(); // May exist in both layers
    const dustHex = dustMaterial.color.getHexString();

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) continue;

        // Clone the material to apply offset without modifying the shared instance
        const finalMaterial = originalMaterial.clone();

        // Apply polygon offset based on material type to help prevent z-fighting
        if (colorHex === baseHex || colorHex === variant1Hex) {
            // Push base materials away slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = 1.0;
            finalMaterial.polygonOffsetUnits = 1.0;
        } else if (colorHex === dustHex) {
            // Pull dust overlay towards camera slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = -1.0;
            finalMaterial.polygonOffsetUnits = -1.0;
        }
        // Crack material (crackHex) is left without offset as it can be in either layer.

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
             finalMeshes.push(new THREE.Mesh(mergedGeometry, finalMaterial)); // Use the potentially modified finalMaterial
        } else {
            console.warn(`Failed to merge geometry for cave floor, material: ${colorHex}`);
        }
    }

    if (finalMeshes.length === 0) return null;

    // Return single mesh if only one material type ended up being used
    if (finalMeshes.length === 1) {
        finalMeshes[0].receiveShadow = true; // Ensure floor parts receive shadow
        finalMeshes[0].name = "caveFloorMesh"; // Give mesh a floor-related name
        return finalMeshes[0];
    }

    // Otherwise, return a group containing all meshes
    const finalGroup = new THREE.Group();
    finalGroup.name = "caveFloor";
    finalMeshes.forEach((mesh, index) => {
        mesh.receiveShadow = true; // Ensure floor parts receive shadow
        mesh.name = `caveFloorMesh_${index}`; // Give meshes floor-related names
        finalGroup.add(mesh);
    });
    return finalGroup;
}