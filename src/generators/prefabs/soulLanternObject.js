import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a floating soul lantern with captured souls providing eerie illumination
 * These lanterns float and sway, casting shifting shadows around the room
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Soul lantern object group
 */
export function createSoulLanternObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 333);

    // Use medium voxel size for atmospheric lanterns
    const lanternVoxelSize = VOXEL_SIZE * 2.5;
    const baseGeometry = getOrCreateGeometry('soul_lantern_voxel', () =>
        new THREE.BoxGeometry(lanternVoxelSize, lanternVoxelSize, lanternVoxelSize)
    );

    // Soul lantern materials - ghostly and ethereal
    const lanternFrameMaterial = _getMaterialByHex_Cached('2A2A2A', {
        roughness: 0.8,
        metalness: 0.7,
        emissive: new THREE.Color(0x0A0A0A),
        emissiveIntensity: 0.05
    }); // Dark iron frame

    const soulGlassMaterial = _getMaterialByHex_Cached('404080', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0x404080),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.1
    }); // Ghostly blue-purple glass

    const soulFlameMaterial = _getMaterialByHex_Cached('9932CC', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x9932CC),
        emissiveIntensity: 0.7,
        roughness: 0.1,
        metalness: 0.0
    }); // Dark orchid soul flame

    const chainMaterial = _getMaterialByHex_Cached('1A1A1A', {
        roughness: 0.9,
        metalness: 0.8,
        emissive: new THREE.Color(0x050505),
        emissiveIntensity: 0.02
    }); // Dark chains

    const soulWispMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.5,
        emissive: new THREE.Color(0x8A2BE2),
        emissiveIntensity: 0.6,
        roughness: 0.0,
        metalness: 0.0
    }); // Blue violet soul wisps

    // Lantern frame structure
    const frameStructure = [
        // Bottom frame
        { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 },
        { x: -1, y: 0, z: 0 },                        { x: 1, y: 0, z: 0 },
        { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 }, { x: 1, y: 0, z: 1 },
        
        // Vertical frame posts
        { x: -1, y: 1, z: -1 }, { x: 1, y: 1, z: -1 },
        { x: -1, y: 1, z: 1 },  { x: 1, y: 1, z: 1 },
        { x: -1, y: 2, z: -1 }, { x: 1, y: 2, z: -1 },
        { x: -1, y: 2, z: 1 },  { x: 1, y: 2, z: 1 },
        
        // Top frame
        { x: -1, y: 3, z: -1 }, { x: 0, y: 3, z: -1 }, { x: 1, y: 3, z: -1 },
        { x: -1, y: 3, z: 0 },  { x: 0, y: 3, z: 0 },  { x: 1, y: 3, z: 0 },
        { x: -1, y: 3, z: 1 },  { x: 0, y: 3, z: 1 },  { x: 1, y: 3, z: 1 }
    ];

    // Add lantern frame
    frameStructure.forEach(pos => {
        const frame = new THREE.Mesh(baseGeometry.clone(), lanternFrameMaterial);
        frame.position.set(
            pos.x * lanternVoxelSize,
            pos.y * lanternVoxelSize,
            pos.z * lanternVoxelSize
        );
        frame.scale.set(0.8, 1.0, 0.8); // Slightly narrower frame
        frame.castShadow = true;
        frame.receiveShadow = true;
        group.add(frame);
    });

    // Glass panels (sides of lantern)
    const glassPositions = [
        // Side panels (larger glass sheets)
        { x: 0, y: 1, z: -1 }, { x: 0, y: 2, z: -1 }, // Front panels
        { x: 0, y: 1, z: 1 },  { x: 0, y: 2, z: 1 },  // Back panels
        { x: -1, y: 1, z: 0 }, { x: -1, y: 2, z: 0 }, // Left panels
        { x: 1, y: 1, z: 0 },  { x: 1, y: 2, z: 0 }   // Right panels
    ];

    glassPositions.forEach(pos => {
        const glass = new THREE.Mesh(
            getOrCreateGeometry('lantern_glass', () =>
                new THREE.BoxGeometry(lanternVoxelSize * 0.9, lanternVoxelSize * 0.9, lanternVoxelSize * 0.1)
            ),
            soulGlassMaterial
        );
        glass.position.set(
            pos.x * lanternVoxelSize,
            pos.y * lanternVoxelSize,
            pos.z * lanternVoxelSize
        );
        
        // Orient glass panels correctly
        if (pos.x !== 0) {
            glass.rotation.y = Math.PI / 2; // Rotate for side panels
        }
        
        glass.userData.isGlass = true;
        glass.castShadow = false;
        glass.receiveShadow = true;
        group.add(glass);
    });

    // Central soul flame (core of the lantern)
    const flamePositions = [
        { x: 0, y: 1, z: 0 },   // Base flame
        { x: 0, y: 1.5, z: 0 }, // Mid flame
        { x: 0, y: 2, z: 0 },   // Upper flame
        { x: 0, y: 2.5, z: 0 }  // Top flame
    ];

    flamePositions.forEach((pos, index) => {
        const flame = new THREE.Mesh(
            getOrCreateGeometry('soul_flame', () =>
                new THREE.BoxGeometry(
                    lanternVoxelSize * (0.6 - index * 0.1),
                    lanternVoxelSize * 0.8,
                    lanternVoxelSize * (0.6 - index * 0.1)
                )
            ),
            soulFlameMaterial
        );
        flame.position.set(
            pos.x * lanternVoxelSize,
            pos.y * lanternVoxelSize,
            pos.z * lanternVoxelSize
        );
        flame.userData.isSoulFlame = true;
        flame.userData.flameLayer = index;
        flame.userData.baseIntensity = 0.7 - index * 0.1;
        flame.castShadow = false;
        flame.receiveShadow = false;
        group.add(flame);
    });

    // Hanging chains (attachment points)
    const chainPositions = [
        // Four chains from top corners
        { x: -1, y: 4, z: -1 }, { x: 1, y: 4, z: -1 },
        { x: -1, y: 4, z: 1 },  { x: 1, y: 4, z: 1 },
        { x: -1, y: 5, z: -1 }, { x: 1, y: 5, z: -1 },
        { x: -1, y: 5, z: 1 },  { x: 1, y: 5, z: 1 },
        // Central chain links
        { x: 0, y: 6, z: 0 }, { x: 0, y: 7, z: 0 }
    ];

    chainPositions.forEach(pos => {
        const chain = new THREE.Mesh(
            getOrCreateGeometry('chain_link', () =>
                new THREE.BoxGeometry(lanternVoxelSize * 0.3, lanternVoxelSize * 0.8, lanternVoxelSize * 0.3)
            ),
            chainMaterial
        );
        chain.position.set(
            pos.x * lanternVoxelSize,
            pos.y * lanternVoxelSize,
            pos.z * lanternVoxelSize
        );
        chain.userData.isChain = true;
        chain.castShadow = true;
        chain.receiveShadow = true;
        group.add(chain);
    });

    // Floating soul wisps around the lantern
    const wispPositions = [
        { x: -2, y: 1.5, z: 0 },   { x: 2, y: 1.5, z: 0 },
        { x: 0, y: 1.5, z: -2 },   { x: 0, y: 1.5, z: 2 },
        { x: -1.5, y: 2.5, z: -1.5 }, { x: 1.5, y: 2.5, z: -1.5 },
        { x: -1.5, y: 2.5, z: 1.5 },  { x: 1.5, y: 2.5, z: 1.5 }
    ];

    wispPositions.forEach((pos, index) => {
        if (rng() > 0.4) { // 60% chance for each wisp
            const wisp = new THREE.Mesh(
                getOrCreateGeometry('soul_wisp', () =>
                    new THREE.BoxGeometry(lanternVoxelSize * 0.4, lanternVoxelSize * 0.4, lanternVoxelSize * 0.4)
                ),
                soulWispMaterial
            );
            wisp.position.set(
                pos.x * lanternVoxelSize,
                pos.y * lanternVoxelSize,
                pos.z * lanternVoxelSize
            );
            wisp.userData.isSoulWisp = true;
            wisp.userData.wispIndex = index;
            wisp.userData.baseX = pos.x * lanternVoxelSize;
            wisp.userData.baseY = pos.y * lanternVoxelSize;
            wisp.userData.baseZ = pos.z * lanternVoxelSize;
            wisp.userData.orbitSpeed = 0.5 + rng() * 0.5;
            wisp.scale.set(0.5, 0.5, 0.5);
            wisp.castShadow = false;
            wisp.receiveShadow = false;
            group.add(wisp);
        }
    });

    // Create point light for the soul lantern
    const soulLight = new THREE.PointLight(0x9932CC, 1.5, 15, 2);
    soulLight.position.set(0, 1.5 * lanternVoxelSize, 0);
    soulLight.castShadow = true;
    soulLight.shadow.mapSize.width = 512;
    soulLight.shadow.mapSize.height = 512;
    soulLight.shadow.camera.near = 0.1;
    soulLight.shadow.camera.far = 20;
    soulLight.userData.originalIntensity = 1.5;
    soulLight.userData.isFlickering = true;
    group.add(soulLight);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'soul_lantern',
        isInteractable: false,
        isDecorative: true,
        isFloating: true,
        hasCollision: false,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: lanternVoxelSize,
        hasLight: true,
        hasSoulAnimation: true,
        hasFloatingAnimation: true,
        soulLight: soulLight,
        // Animation properties
        floatHeight: 0.3,
        floatSpeed: 0.8,
        swayAmplitude: 0.2,
        swaySpeed: 1.2
    };

    group.name = 'soul_lantern';
    console.log('[SoulLantern] ✅ Created floating soul lantern with ethereal lighting');
    return group;
}