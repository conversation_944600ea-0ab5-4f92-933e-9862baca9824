import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a crystal cave formation with big amethysts (voxel-style, decorative)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Crystal cave object group
 */
export function createCrystalCaveObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888);

    // Use consistent voxel size
    const crystalVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('crystal_cave_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Create voxel-style stone base (larger platform for cave formation)
    const baseMaterial = _getMaterialByHex_Cached('444444', {
        emissive: new THREE.Color(0x111111),
        emissiveIntensity: 0.1
    });

    // Create larger stone base platform (5x5)
    for (let x = -2; x <= 2; x++) {
        for (let z = -2; z <= 2; z++) {
            // Skip corners for more natural cave shape
            if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;
            
            const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
            baseVoxel.position.set(
                x * crystalVoxelSize,
                0,
                z * crystalVoxelSize
            );
            baseVoxel.userData.isFloorObject = true;
            baseVoxel.userData.hasCollision = true;
            baseVoxel.castShadow = true;
            baseVoxel.receiveShadow = true;
            group.add(baseVoxel);
        }
    }

    // Create different amethyst crystal materials
    const amethystMaterial = _getMaterialByHex_Cached('9966FF', {
        transparent: true,
        opacity: 0.85,
        emissive: new THREE.Color(0x4433AA),
        emissiveIntensity: 0.6
    });

    const darkAmethystMaterial = _getMaterialByHex_Cached('6633CC', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x331188),
        emissiveIntensity: 0.4
    });

    const lightAmethystMaterial = _getMaterialByHex_Cached('BB88FF', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x5544CC),
        emissiveIntensity: 0.7
    });

    // Create large central amethyst cluster
    const centralCrystalPositions = [
        { x: 0, y: 1, z: 0, material: amethystMaterial },
        { x: 0, y: 2, z: 0, material: amethystMaterial },
        { x: 0, y: 3, z: 0, material: lightAmethystMaterial },
        { x: 0, y: 4, z: 0, material: lightAmethystMaterial },
        { x: -1, y: 2, z: 0, material: darkAmethystMaterial },
        { x: 1, y: 2, z: 0, material: darkAmethystMaterial },
        { x: 0, y: 2, z: -1, material: darkAmethystMaterial },
        { x: 0, y: 2, z: 1, material: darkAmethystMaterial },
        { x: -1, y: 3, z: 0, material: amethystMaterial },
        { x: 1, y: 3, z: 0, material: amethystMaterial }
    ];

    centralCrystalPositions.forEach(pos => {
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), pos.material);
        crystalVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        crystalVoxel.userData.isFloorObject = true;
        crystalVoxel.userData.hasCollision = true;
        crystalVoxel.castShadow = false; // Glowing objects don't cast shadows
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Create smaller amethyst formations around the cave
    const smallCrystalFormations = [
        // Formation 1 - Front left
        { x: -2, y: 1, z: -1, material: darkAmethystMaterial },
        { x: -2, y: 2, z: -1, material: amethystMaterial },
        { x: -1, y: 1, z: -2, material: darkAmethystMaterial },
        
        // Formation 2 - Front right
        { x: 2, y: 1, z: -1, material: darkAmethystMaterial },
        { x: 2, y: 2, z: -1, material: lightAmethystMaterial },
        { x: 1, y: 1, z: -2, material: darkAmethystMaterial },
        
        // Formation 3 - Back left
        { x: -2, y: 1, z: 1, material: amethystMaterial },
        { x: -1, y: 1, z: 2, material: darkAmethystMaterial },
        { x: -1, y: 2, z: 2, material: lightAmethystMaterial },
        
        // Formation 4 - Back right
        { x: 2, y: 1, z: 1, material: amethystMaterial },
        { x: 1, y: 1, z: 2, material: darkAmethystMaterial },
        { x: 1, y: 2, z: 2, material: amethystMaterial }
    ];

    smallCrystalFormations.forEach(pos => {
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), pos.material);
        crystalVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        crystalVoxel.userData.isFloorObject = true;
        crystalVoxel.userData.hasCollision = true;
        crystalVoxel.castShadow = false;
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Add crystal shards scattered around
    const shardPositions = [
        { x: -1.5, y: 0.5, z: -1.5 },
        { x: 1.5, y: 0.5, z: -1.5 },
        { x: -1.5, y: 0.5, z: 1.5 },
        { x: 1.5, y: 0.5, z: 1.5 },
        { x: 0, y: 0.5, z: -2.5 },
        { x: 0, y: 0.5, z: 2.5 },
        { x: -2.5, y: 0.5, z: 0 },
        { x: 2.5, y: 0.5, z: 0 }
    ];

    shardPositions.forEach(pos => {
        const shardVoxel = new THREE.Mesh(baseGeometry.clone(), lightAmethystMaterial);
        shardVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        shardVoxel.scale.set(0.4, 0.4, 0.4); // Small crystal shards
        shardVoxel.rotation.y = rng() * Math.PI * 2; // Random rotation
        shardVoxel.userData.isFloorObject = true;
        shardVoxel.userData.hasCollision = false; // Small shards don't block movement
        shardVoxel.castShadow = false;
        shardVoxel.receiveShadow = true;
        group.add(shardVoxel);
    });

    // Set up group properties (decorative, not interactable)
    group.userData = {
        ...(options.userData || {}),
        objectType: 'crystal_cave',
        isInteractable: false,
        isEventObject: false,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : false,
        isInteriorObject: true,
        voxelScale: crystalVoxelSize
    };

    group.name = 'crystal_cave';

    console.log('[CrystalCaveObject] ✅ Created voxel-style crystal cave formation');
    return group;
}
