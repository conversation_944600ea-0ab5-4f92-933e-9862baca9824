import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a large time crystal object - stable temporal energy storage
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Large time crystal group
 */
export function createTimeCrystalLargeObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 555);

    // Use consistent voxel size
    const crystalVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('time_crystal_large_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Stone base platform
    const baseMaterial = _getMaterialByHex_Cached('3a3a3a', {
        emissive: new THREE.Color(0x0a0a0a),
        emissiveIntensity: 0.05,
        roughness: 0.9,
        metalness: 0.1
    });

    // Create octagonal base platform
    const basePositions = [
        { x: 0, z: 0 }, // center
        { x: -1, z: 0 }, { x: 1, z: 0 }, { x: 0, z: -1 }, { x: 0, z: 1 }, // cross
        { x: -1, z: -1 }, { x: 1, z: -1 }, { x: -1, z: 1 }, { x: 1, z: 1 } // corners
    ];

    basePositions.forEach(pos => {
        const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
        baseVoxel.position.set(
            pos.x * crystalVoxelSize,
            crystalVoxelSize * 0.5,
            pos.z * crystalVoxelSize
        );
        baseVoxel.userData.isFloorObject = true;
        baseVoxel.userData.hasCollision = true;
        baseVoxel.castShadow = true;
        baseVoxel.receiveShadow = true;
        group.add(baseVoxel);
    });

    // Large crystal formation (stable temporal energy)
    const crystalMaterial = _getMaterialByHex_Cached('4169E1', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x1E3A8A),
        emissiveIntensity: 0.3
    });

    const innerCrystalMaterial = _getMaterialByHex_Cached('6495ED', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x4169E1),
        emissiveIntensity: 0.5
    });

    // Crystal structure (tower formation)
    const crystalPositions = [
        // Base layer
        { x: 0, y: 1, z: 0 },
        { x: -1, y: 1, z: 0 }, { x: 1, y: 1, z: 0 }, { x: 0, y: 1, z: -1 }, { x: 0, y: 1, z: 1 },
        // Second layer
        { x: 0, y: 1.5, z: 0 },
        { x: -0.5, y: 1.5, z: 0 }, { x: 0.5, y: 1.5, z: 0 }, { x: 0, y: 1.5, z: -0.5 }, { x: 0, y: 1.5, z: 0.5 },
        // Third layer
        { x: 0, y: 2, z: 0 },
        { x: -0.5, y: 2, z: -0.5 }, { x: 0.5, y: 2, z: -0.5 }, { x: -0.5, y: 2, z: 0.5 }, { x: 0.5, y: 2, z: 0.5 },
        // Fourth layer
        { x: 0, y: 2.5, z: 0 },
        { x: 0, y: 2.5, z: -0.5 }, { x: 0, y: 2.5, z: 0.5 },
        // Top
        { x: 0, y: 3, z: 0 }
    ];

    crystalPositions.forEach((pos, index) => {
        const material = (pos.y <= 1.5) ? crystalMaterial : innerCrystalMaterial;
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), material);
        crystalVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        crystalVoxel.userData.isCrystalPart = true;
        crystalVoxel.userData.animationPhase = (index / crystalPositions.length) * Math.PI * 2;
        crystalVoxel.castShadow = false;
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Energy field around crystal
    const fieldMaterial = _getMaterialByHex_Cached('1E3A8A', {
        transparent: true,
        opacity: 0.2,
        emissive: new THREE.Color(0x1E3A8A),
        emissiveIntensity: 0.1
    });

    const fieldGeometry = getOrCreateGeometry('time_crystal_field', () =>
        new THREE.BoxGeometry(crystalVoxelSize * 3, crystalVoxelSize * 4, crystalVoxelSize * 3)
    );

    const energyField = new THREE.Mesh(fieldGeometry, fieldMaterial);
    energyField.position.set(0, crystalVoxelSize * 2, 0);
    energyField.userData.isEnergyField = true;
    energyField.castShadow = false;
    energyField.receiveShadow = false;
    group.add(energyField);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'time_crystal_large',
        isInteractable: false,
        isEventObject: true,
        objectId: options.userData?.objectId || 'time_crystal_large',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: crystalVoxelSize,
        hasAnimation: true,
        animationType: 'crystal_pulse'
    };

    group.name = 'time_crystal_large';
    return group;
}

/**
 * Create a small time crystal object - unstable temporal energy fragment
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Small time crystal group
 */
export function createTimeCrystalSmallObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 333);

    // Use smaller voxel size
    const crystalVoxelSize = VOXEL_SIZE * 1.2;
    const baseGeometry = getOrCreateGeometry('time_crystal_small_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Small stone base
    const baseMaterial = _getMaterialByHex_Cached('2a2a2a', {
        emissive: new THREE.Color(0x0a0a0a),
        emissiveIntensity: 0.02,
        roughness: 0.95,
        metalness: 0.05
    });

    const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
    baseVoxel.position.set(0, crystalVoxelSize * 0.5, 0);
    baseVoxel.userData.isFloorObject = true;
    baseVoxel.userData.hasCollision = true;
    baseVoxel.castShadow = true;
    baseVoxel.receiveShadow = true;
    group.add(baseVoxel);

    // Small unstable crystal (different colors for variety)
    const crystalColors = [
        { main: '8A2BE2', emissive: '4B0082', intensity: 0.4 }, // Purple
        { main: '20B2AA', emissive: '008B8B', intensity: 0.3 }, // Cyan
        { main: 'FF6347', emissive: 'DC143C', intensity: 0.35 }, // Orange-red
        { main: '32CD32', emissive: '228B22', intensity: 0.3 }  // Green
    ];

    const colorIndex = Math.floor(rng() * crystalColors.length);
    const selectedColor = crystalColors[colorIndex];

    const crystalMaterial = _getMaterialByHex_Cached(selectedColor.main, {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(`0x${selectedColor.emissive}`),
        emissiveIntensity: selectedColor.intensity
    });

    // Simple crystal formation
    const crystalPositions = [
        { x: 0, y: 1, z: 0 }, // base
        { x: 0, y: 1.5, z: 0 }, // middle
        { x: 0, y: 2, z: 0 }, // top
        // Small side crystals
        { x: 0.5, y: 1, z: 0 }, { x: -0.5, y: 1, z: 0 },
        { x: 0, y: 1, z: 0.5 }, { x: 0, y: 1, z: -0.5 }
    ];

    crystalPositions.forEach((pos, index) => {
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), crystalMaterial);
        crystalVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        crystalVoxel.userData.isCrystalPart = true;
        crystalVoxel.userData.animationPhase = (index / crystalPositions.length) * Math.PI * 2;
        crystalVoxel.userData.isUnstable = true;
        crystalVoxel.castShadow = false;
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'time_crystal_small',
        isInteractable: false,
        isEventObject: true,
        objectId: options.userData?.objectId || 'time_crystal_small',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: crystalVoxelSize,
        hasAnimation: true,
        animationType: 'crystal_flicker',
        crystalColor: selectedColor
    };

    group.name = 'time_crystal_small';
    return group;
}