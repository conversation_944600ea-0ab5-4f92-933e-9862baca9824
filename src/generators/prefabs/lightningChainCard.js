import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Lightning Chain Card Prefab
 * Creates lightning that jumps between enemies
 */

// Lightning chain specific colors
const LIGHTNING_COLORS = {
    ELECTRIC_BLUE: 0x0080FF,        // Bright electric blue core
    LIGHTNING_WHITE: 0xFFFFFF,      // Pure white lightning bolts
    STORM_PURPLE: 0x8A2BE2,         // Blue violet storm energy
    THUNDER_YELLOW: 0xFFFF00,       // Bright yellow electric sparks
    PLASMA_CYAN: 0x00FFFF,          // Cyan plasma energy
    VOLT_SILVER: 0xC0C0C0           // Silver electrical conductors
};

/**
 * Create a lightning chain card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The lightning chain card 3D model
 */
export function createLightningChainCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'LightningChainCard';

    // Lightning chain materials
    const electricBlueMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.ELECTRIC_BLUE,
        emissive: LIGHTNING_COLORS.ELECTRIC_BLUE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const lightningWhiteMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.LIGHTNING_WHITE,
        emissive: LIGHTNING_COLORS.LIGHTNING_WHITE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.8
    });

    const stormPurpleMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.STORM_PURPLE,
        emissive: LIGHTNING_COLORS.STORM_PURPLE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const thunderYellowMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.THUNDER_YELLOW,
        emissive: LIGHTNING_COLORS.THUNDER_YELLOW,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.85
    });

    const plasmaCyanMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.PLASMA_CYAN,
        emissive: LIGHTNING_COLORS.PLASMA_CYAN,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.75
    });

    const voltSilverMaterial = new THREE.MeshLambertMaterial({
        color: LIGHTNING_COLORS.VOLT_SILVER,
        emissive: LIGHTNING_COLORS.VOLT_SILVER,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central lightning bolt (main striking bolt)
    const centralBoltVoxels = [
        // Lightning core (zigzag pattern)
        { x: 0.0, y: 0.24, z: 0, material: lightningWhiteMaterial },
        { x: -0.04, y: 0.20, z: 0, material: lightningWhiteMaterial },
        { x: 0.04, y: 0.16, z: 0, material: lightningWhiteMaterial },
        { x: -0.02, y: 0.12, z: 0, material: lightningWhiteMaterial },
        { x: 0.06, y: 0.08, z: 0, material: lightningWhiteMaterial },
        { x: -0.04, y: 0.04, z: 0, material: lightningWhiteMaterial },
        { x: 0.02, y: 0.0, z: 0, material: lightningWhiteMaterial },
        { x: -0.06, y: -0.04, z: 0, material: lightningWhiteMaterial },
        { x: 0.04, y: -0.08, z: 0, material: lightningWhiteMaterial },
        { x: -0.02, y: -0.12, z: 0, material: lightningWhiteMaterial },
        { x: 0.0, y: -0.16, z: 0, material: lightningWhiteMaterial },
        { x: 0.04, y: -0.20, z: 0, material: lightningWhiteMaterial },
        { x: 0.0, y: -0.24, z: 0, material: lightningWhiteMaterial },
        
        // Electric blue energy around core
        { x: -0.08, y: 0.22, z: 0.02, material: electricBlueMaterial },
        { x: 0.08, y: 0.18, z: 0.02, material: electricBlueMaterial },
        { x: -0.06, y: 0.14, z: -0.02, material: electricBlueMaterial },
        { x: 0.10, y: 0.10, z: 0.02, material: electricBlueMaterial },
        { x: -0.08, y: 0.06, z: -0.02, material: electricBlueMaterial },
        { x: 0.06, y: 0.02, z: 0.02, material: electricBlueMaterial },
        { x: -0.10, y: -0.02, z: -0.02, material: electricBlueMaterial },
        { x: 0.08, y: -0.06, z: 0.02, material: electricBlueMaterial },
        { x: -0.06, y: -0.10, z: -0.02, material: electricBlueMaterial },
        { x: 0.04, y: -0.14, z: 0.02, material: electricBlueMaterial },
        { x: -0.04, y: -0.18, z: -0.02, material: electricBlueMaterial },
        { x: 0.08, y: -0.22, z: 0.02, material: electricBlueMaterial }
    ];

    // Chain lightning bolts (branching lightning)
    const chainBoltsVoxels = [
        // Left chain branch
        { x: -0.16, y: 0.16, z: 0.04, material: thunderYellowMaterial },
        { x: -0.20, y: 0.12, z: 0.06, material: thunderYellowMaterial },
        { x: -0.24, y: 0.08, z: 0.04, material: plasmaCyanMaterial },
        { x: -0.28, y: 0.04, z: 0.06, material: plasmaCyanMaterial },
        { x: -0.24, y: 0.0, z: 0.04, material: stormPurpleMaterial },
        { x: -0.20, y: -0.04, z: 0.06, material: stormPurpleMaterial },
        { x: -0.16, y: -0.08, z: 0.04, material: thunderYellowMaterial },
        
        // Right chain branch
        { x: 0.16, y: 0.20, z: -0.04, material: thunderYellowMaterial },
        { x: 0.20, y: 0.16, z: -0.06, material: thunderYellowMaterial },
        { x: 0.24, y: 0.12, z: -0.04, material: plasmaCyanMaterial },
        { x: 0.28, y: 0.08, z: -0.06, material: plasmaCyanMaterial },
        { x: 0.24, y: 0.04, z: -0.04, material: stormPurpleMaterial },
        { x: 0.20, y: 0.0, z: -0.06, material: stormPurpleMaterial },
        { x: 0.16, y: -0.04, z: -0.04, material: thunderYellowMaterial },
        { x: 0.20, y: -0.08, z: -0.06, material: thunderYellowMaterial },
        
        // Upper chain branches
        { x: -0.12, y: 0.28, z: 0.08, material: plasmaCyanMaterial },
        { x: 0.12, y: 0.28, z: -0.08, material: plasmaCyanMaterial },
        { x: -0.08, y: 0.32, z: 0.10, material: stormPurpleMaterial },
        { x: 0.08, y: 0.32, z: -0.10, material: stormPurpleMaterial },
        
        // Lower chain branches
        { x: -0.12, y: -0.28, z: 0.08, material: plasmaCyanMaterial },
        { x: 0.12, y: -0.28, z: -0.08, material: plasmaCyanMaterial },
        { x: -0.08, y: -0.32, z: 0.10, material: stormPurpleMaterial },
        { x: 0.08, y: -0.32, z: -0.10, material: stormPurpleMaterial }
    ];

    // Electric nodes (connection points for chain lightning)
    const electricNodesVoxels = [
        // Primary nodes (larger)
        { x: -0.20, y: 0.20, z: 0.12, material: voltSilverMaterial },
        { x: 0.20, y: 0.20, z: -0.12, material: voltSilverMaterial },
        { x: -0.20, y: -0.20, z: 0.12, material: voltSilverMaterial },
        { x: 0.20, y: -0.20, z: -0.12, material: voltSilverMaterial },
        
        // Secondary nodes (medium)
        { x: -0.28, y: 0.0, z: 0.08, material: electricBlueMaterial },
        { x: 0.28, y: 0.0, z: -0.08, material: electricBlueMaterial },
        { x: 0.0, y: 0.32, z: 0.10, material: electricBlueMaterial },
        { x: 0.0, y: -0.32, z: -0.10, material: electricBlueMaterial },
        
        // Tertiary nodes (small connection points)
        { x: -0.32, y: 0.16, z: 0.14, material: thunderYellowMaterial },
        { x: 0.32, y: 0.16, z: -0.14, material: thunderYellowMaterial },
        { x: 0.32, y: -0.16, z: 0.14, material: thunderYellowMaterial },
        { x: -0.32, y: -0.16, z: -0.14, material: thunderYellowMaterial },
        { x: -0.16, y: 0.32, z: 0.12, material: thunderYellowMaterial },
        { x: 0.16, y: 0.32, z: -0.12, material: thunderYellowMaterial },
        { x: 0.16, y: -0.32, z: 0.12, material: thunderYellowMaterial },
        { x: -0.16, y: -0.32, z: -0.12, material: thunderYellowMaterial }
    ];

    // Electric sparks (random sparks around the lightning)
    const electricSparksVoxels = [
        // Inner spark layer
        { x: -0.14, y: 0.14, z: 0.16, material: lightningWhiteMaterial },
        { x: 0.14, y: 0.14, z: 0.16, material: lightningWhiteMaterial },
        { x: 0.14, y: -0.14, z: 0.16, material: lightningWhiteMaterial },
        { x: -0.14, y: -0.14, z: 0.16, material: lightningWhiteMaterial },
        
        // Outer spark layer
        { x: -0.24, y: 0.24, z: 0.18, material: thunderYellowMaterial },
        { x: 0.24, y: 0.24, z: 0.18, material: thunderYellowMaterial },
        { x: 0.24, y: -0.24, z: 0.18, material: thunderYellowMaterial },
        { x: -0.24, y: -0.24, z: 0.18, material: thunderYellowMaterial },
        
        // Random floating sparks
        { x: -0.30, y: 0.12, z: 0.20, material: plasmaCyanMaterial },
        { x: 0.30, y: 0.08, z: 0.20, material: plasmaCyanMaterial },
        { x: 0.12, y: 0.30, z: 0.18, material: electricBlueMaterial },
        { x: -0.08, y: 0.30, z: 0.18, material: electricBlueMaterial },
        { x: 0.26, y: -0.18, z: 0.22, material: stormPurpleMaterial },
        { x: -0.26, y: -0.10, z: 0.22, material: stormPurpleMaterial },
        { x: 0.18, y: -0.30, z: 0.16, material: voltSilverMaterial },
        { x: -0.10, y: -0.28, z: 0.16, material: voltSilverMaterial },
        
        // Distant electrical activity
        { x: -0.34, y: 0.20, z: 0.24, material: lightningWhiteMaterial },
        { x: 0.34, y: 0.22, z: 0.24, material: lightningWhiteMaterial },
        { x: 0.20, y: 0.34, z: 0.22, material: thunderYellowMaterial },
        { x: -0.22, y: 0.34, z: 0.22, material: thunderYellowMaterial },
        { x: 0.34, y: -0.20, z: 0.24, material: plasmaCyanMaterial },
        { x: -0.34, y: -0.22, z: 0.24, material: plasmaCyanMaterial },
        { x: 0.22, y: -0.34, z: 0.22, material: electricBlueMaterial },
        { x: -0.20, y: -0.34, z: 0.22, material: electricBlueMaterial }
    ];

    // Create central lightning bolt group
    const centralBoltGroup = new THREE.Group();
    centralBoltGroup.name = 'centralBolt';

    // Add central bolt voxels
    centralBoltVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        centralBoltGroup.add(mesh);
    });

    // Create chain bolts group
    const chainBoltsGroup = new THREE.Group();
    chainBoltsGroup.name = 'chainBolts';

    // Add chain bolts voxels
    chainBoltsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.chainPhase = index * 0.1; // Stagger animation
        chainBoltsGroup.add(mesh);
    });

    // Create electric nodes group
    const electricNodesGroup = new THREE.Group();
    electricNodesGroup.name = 'electricNodes';

    // Add electric nodes voxels
    electricNodesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.nodePhase = index * 0.15; // Stagger animation
        electricNodesGroup.add(mesh);
    });

    // Create electric sparks group
    const electricSparksGroup = new THREE.Group();
    electricSparksGroup.name = 'electricSparks';

    // Add electric sparks voxels
    electricSparksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sparkPhase = index * 0.05; // Stagger animation
        electricSparksGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(centralBoltGroup);
    cardGroup.add(chainBoltsGroup);
    cardGroup.add(electricNodesGroup);
    cardGroup.add(electricSparksGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        lightningFlash: 0,
        electricPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update lightning chain card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateLightningChainCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.lightningFlash += deltaTime * 6.0; // Lightning flash speed
    cardGroup.userData.electricPulse += deltaTime * 4.0; // Electric pulse speed

    const time = cardGroup.userData.animationTime;
    const lightningFlash = cardGroup.userData.lightningFlash;
    const electricPulse = cardGroup.userData.electricPulse;

    // Animate central lightning bolt (intense flashing)
    const centralBoltGroup = cardGroup.getObjectByName('centralBolt');
    if (centralBoltGroup) {
        // Lightning core flashing (very intense)
        const lightningIntensity = Math.sin(lightningFlash * 8.0) > 0.3 ? 1.0 : 0.2;
        centralBoltGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * lightningIntensity;
            }
        });
    }

    // Animate chain bolts (crackling electricity)
    const chainBoltsGroup = cardGroup.getObjectByName('chainBolts');
    if (chainBoltsGroup) {
        // Chain lightning crackling
        chainBoltsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.chainPhase !== undefined) {
                const chainTime = lightningFlash + mesh.userData.chainPhase;
                
                // Electrical crackling motion
                const crackleX = Math.sin(chainTime * 12.0) * 0.002;
                const crackleY = Math.cos(chainTime * 10.0) * 0.002;
                const crackleZ = Math.sin(chainTime * 14.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + crackleX;
                mesh.position.y = mesh.userData.originalPosition.y + crackleY;
                mesh.position.z = mesh.userData.originalPosition.z + crackleZ;
                
                // Chain lightning intensity flashing
                const chainFlash = Math.sin(chainTime * 15.0) > 0.5 ? 1.0 : 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * chainFlash;
                }
                
                // Chain opacity flickering
                const chainOpacity = 0.7 + Math.sin(chainTime * 20.0) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * chainOpacity;
                }
            }
        });
    }

    // Animate electric nodes (pulsing connection points)
    const electricNodesGroup = cardGroup.getObjectByName('electricNodes');
    if (electricNodesGroup) {
        // Electric node pulsing
        electricNodesGroup.children.forEach(mesh => {
            if (mesh.userData.originalEmissive !== undefined && mesh.userData.nodePhase !== undefined) {
                const nodeTime = electricPulse + mesh.userData.nodePhase;
                
                // Node electrical pulsing
                const nodePulse = 0.6 + Math.sin(nodeTime * 5.0) * 0.4;
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * nodePulse;
                
                // Node scale pulsing (electrical charge buildup)
                const nodeScale = 1 + Math.sin(nodeTime * 3.5) * 0.2;
                mesh.scale.setScalar(nodeScale);
                
                // Occasional bright flash
                const brightFlash = Math.sin(nodeTime * 7.0) > 0.8 ? 2.0 : 1.0;
                mesh.material.emissiveIntensity *= brightFlash;
            }
        });
    }

    // Animate electric sparks (random electrical activity)
    const electricSparksGroup = cardGroup.getObjectByName('electricSparks');
    if (electricSparksGroup) {
        electricSparksGroup.rotation.y = time * 0.8; // Slow rotation
        
        // Electric spark random activity
        electricSparksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparkPhase !== undefined) {
                const sparkTime = lightningFlash + mesh.userData.sparkPhase;
                
                // Random spark motion (electrical jumping)
                const jumpX = Math.sin(sparkTime * 18.0) * 0.003;
                const jumpY = Math.cos(sparkTime * 16.0) * 0.003;
                const jumpZ = Math.sin(sparkTime * 20.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + jumpX;
                mesh.position.y = mesh.userData.originalPosition.y + jumpY;
                mesh.position.z = mesh.userData.originalPosition.z + jumpZ;
                
                // Random electrical flashing
                const randomFlash = Math.random() < 0.1 ? 2.0 : (Math.sin(sparkTime * 25.0) > 0.7 ? 1.0 : 0.1);
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * randomFlash;
                }
                
                // Spark scale variation
                const sparkScale = 0.8 + Math.sin(sparkTime * 30.0) * 0.4;
                mesh.scale.setScalar(sparkScale);
            }
        });
    }

    // Overall lightning storm pulsing (electrical energy)
    const stormPulse = 1 + Math.sin(time * 3.5) * 0.1;
    cardGroup.scale.setScalar(0.8 * stormPulse);
}

// Export the lightning chain card data for the loot system
export const LIGHTNING_CHAIN_CARD_DATA = {
    name: 'Lightning Chain',
    description: 'Unleashes a devastating chain lightning that arcs between enemies, dealing electrical damage and jumping up to 6 targets with each jump dealing reduced damage.',
    category: 'card',
    rarity: 'epic',
    effect: 'lightning_chain',
    effectValue: 6, // Maximum number of chain jumps
    createFunction: createLightningChainCard,
    updateFunction: updateLightningChainCardAnimation,
    voxelModel: 'lightning_chain_card',
    glow: {
        color: 0x0080FF,
        intensity: 1.4
    }
};

export default createLightningChainCard;