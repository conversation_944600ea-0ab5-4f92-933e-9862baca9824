import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Voxel Data for Ancient Stone Pillar ---

// Use stone brick wall colors for consistency with dungeon walls
const STONE_BRICK_COLORS = ['555c66', '606873', '707880']; // Match stonebrick wall materials
const DARK_STONE = '4a4a4a'; // Dark gray for shadows and details
const LIGHT_STONE = '707880'; // Lighter stone brick color for highlights

// Define the ancient stone pillar shape using voxel coordinates
// Structure: Base -> Column -> Capital -> Details
// WALL HEIGHT MATCHED: Adjusted to match dungeon wall height (Y: 0-15, 3.84 world units vs 4.0 wall height = 96% match)
const ancientStonePillarShape = [
    // === PILLAR BASE (Y: 0-2) ===
    // Bottom layer (Y=0) - 5x5 square base
    ...generateSquareLayer(0, 2, STONE_BRICK_COLORS[0]),
    // Second layer (Y=1) - 4x4 square
    ...generateSquareLayer(1, 1.5, STONE_BRICK_COLORS[1]),
    // Top base layer (Y=2) - 3x3 square
    ...generateSquareLayer(2, 1, STONE_BRICK_COLORS[0]),

    // === MAIN COLUMN (Y: 3-12) ===
    // Column shaft - consistent 2x2 cross section
    ...generateColumnLayer(3, STONE_BRICK_COLORS[2]),
    ...generateColumnLayer(4, STONE_BRICK_COLORS[1]),
    ...generateColumnLayer(5, STONE_BRICK_COLORS[2]),
    ...generateColumnLayer(6, STONE_BRICK_COLORS[1]),
    ...generateColumnLayer(7, STONE_BRICK_COLORS[2]),
    ...generateColumnLayer(8, STONE_BRICK_COLORS[1]),
    ...generateColumnLayer(9, STONE_BRICK_COLORS[2]),
    ...generateColumnLayer(10, STONE_BRICK_COLORS[1]),
    ...generateColumnLayer(11, STONE_BRICK_COLORS[2]),
    ...generateColumnLayer(12, STONE_BRICK_COLORS[1]),

    // === CAPITAL (TOP) (Y: 13-15) ===
    // Capital base (Y=13) - 3x3 square
    ...generateSquareLayer(13, 1, STONE_BRICK_COLORS[0]),
    // Capital middle (Y=14) - 4x4 square
    ...generateSquareLayer(14, 1.5, STONE_BRICK_COLORS[1]),
    // Capital crown (Y=15) - 3x3 square
    ...generateSquareLayer(15, 1, STONE_BRICK_COLORS[0]),

    // === ANCIENT DETAILS ===
    // Weathering and cracks on column (adjusted for new height)
    { x: 1, y: 5, z: 1, c: DARK_STONE },   // Crack detail
    { x: -1, y: 7, z: 1, c: DARK_STONE },  // Weathering
    { x: 1, y: 9, z: -1, c: DARK_STONE },  // Age marks
    { x: -1, y: 11, z: -1, c: DARK_STONE }, // Shadow detail

    // Base weathering
    { x: 2, y: 1, z: 0, c: DARK_STONE },   // Base edge weathering
    { x: -2, y: 1, z: 0, c: DARK_STONE },  // Base edge weathering
    { x: 0, y: 1, z: 2, c: DARK_STONE },   // Base edge weathering
    { x: 0, y: 1, z: -2, c: DARK_STONE },  // Base edge weathering

    // Capital details (adjusted for new height)
    { x: 0, y: 14, z: 1, c: DARK_STONE },  // Capital shadow
    { x: 1, y: 14, z: 0, c: DARK_STONE },  // Capital shadow
];

// Helper function to generate a square layer
function generateSquareLayer(y, radius, color) {
    const layer = [];
    const intRadius = Math.floor(radius);
    for (let x = -intRadius; x <= intRadius; x++) {
        for (let z = -intRadius; z <= intRadius; z++) {
            // For fractional radius, add some edge voxels selectively
            if (radius % 1 !== 0 && Math.abs(x) === intRadius && Math.abs(z) === intRadius) {
                // Skip corner voxels for fractional radius to create more rounded edges
                continue;
            }
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate column layers (2x2 cross section)
function generateColumnLayer(y, color) {
    return [
        { x: -1, y, z: -1, c: color },
        { x: 0, y, z: -1, c: color },
        { x: 1, y, z: -1, c: color },
        { x: -1, y, z: 0, c: color },
        { x: 0, y, z: 0, c: color },
        { x: 1, y, z: 0, c: color },
        { x: -1, y, z: 1, c: color },
        { x: 0, y, z: 1, c: color },
        { x: 1, y, z: 1, c: color }
    ];
}

// --- Main Prefab Function ---
export function createAncientStonePillarObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 991); // Different multiplier for variation

    // Use same voxel size as angel statue for consistency
    const pillarVoxelSize = VOXEL_SIZE * 5.12; // Same size as angel statue
    const baseGeometry = getOrCreateGeometry('ancient_stone_pillar_voxel', () =>
        new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the pillar shape
    ancientStonePillarShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to stone brick color for weathered effect
        let finalColor = c;
        if (rng() < 0.25) { // 25% chance for weathering
            const colorIndex = Math.floor(rng() * STONE_BRICK_COLORS.length);
            finalColor = STONE_BRICK_COLORS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * pillarVoxelSize,
            y * pillarVoxelSize,
            z * pillarVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'ancient_stone_pillar',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : false, // CHANGED: Default to false for architectural pillars
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: pillarVoxelSize, // FIXED: Add voxel scale for proper destruction
        originalVoxels: ancientStonePillarShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 23 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('[AncientStonePillar] Created with options:', options);
    console.log('[AncientStonePillar] Final userData:', group.userData);
    console.log('[AncientStonePillar] isDestructible from options:', options.isDestructible);
    console.log('[AncientStonePillar] Final isDestructible value:', group.userData.isDestructible);

    return group;
}
