import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Shadow Army Card Prefab
 * Summons an army of shadow warriors that fight alongside the player
 */

// Shadow Army specific colors
const SHADOW_ARMY_COLORS = {
    SHADOW_BLACK: 0x1C1C1C,         // Main shadow color
    VOID_PURPLE: 0x4B0082,          // Deep void energy
    SPECTRAL_BLUE: 0x6A5ACD,        // Spectral warrior energy
    DARKNESS_GRAY: 0x2F2F2F,        // Dark essence
    SOUL_SILVER: 0x708090,          // Soul energy
    PHANTOM_WHITE: 0xF8F8FF,        // Phantom glow
    CRIMSON_RED: 0x8B0000,          // Battle energy
    ETHEREAL_CYAN: 0x48D1CC         // Ethereal magic
};

/**
 * Create a shadow army card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The shadow army card 3D model
 */
export function createShadowArmyCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ShadowArmyCard';

    // Shadow Army materials
    const shadowBlackMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.SHADOW_BLACK,
        emissive: SHADOW_ARMY_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.8
    });

    const voidPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.VOID_PURPLE,
        emissive: SHADOW_ARMY_COLORS.VOID_PURPLE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const spectralBlueMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.SPECTRAL_BLUE,
        emissive: SHADOW_ARMY_COLORS.SPECTRAL_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const darknessGrayMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.DARKNESS_GRAY,
        emissive: SHADOW_ARMY_COLORS.DARKNESS_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    const soulSilverMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.SOUL_SILVER,
        emissive: SHADOW_ARMY_COLORS.SOUL_SILVER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7
    });

    const phantomWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.PHANTOM_WHITE,
        emissive: SHADOW_ARMY_COLORS.PHANTOM_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.4
    });

    const crimsonRedMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.CRIMSON_RED,
        emissive: SHADOW_ARMY_COLORS.CRIMSON_RED,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const etherealCyanMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_ARMY_COLORS.ETHEREAL_CYAN,
        emissive: SHADOW_ARMY_COLORS.ETHEREAL_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Shadow Warriors (multiple warrior figures)
    const shadowWarriorsVoxels = [
        // Central Shadow Warrior (largest)
        { x: 0.0, y: -0.16, z: 0.0, material: shadowBlackMaterial }, // Base
        { x: 0.0, y: -0.08, z: 0.0, material: darknessGrayMaterial }, // Torso
        { x: 0.0, y: 0.0, z: 0.0, material: shadowBlackMaterial }, // Chest
        { x: 0.0, y: 0.08, z: 0.0, material: voidPurpleMaterial }, // Head
        { x: -0.04, y: 0.0, z: 0.0, material: darknessGrayMaterial }, // Left arm
        { x: 0.04, y: 0.0, z: 0.0, material: darknessGrayMaterial }, // Right arm
        { x: -0.02, y: -0.12, z: 0.0, material: shadowBlackMaterial }, // Left leg
        { x: 0.02, y: -0.12, z: 0.0, material: shadowBlackMaterial }, // Right leg
        
        // Left Shadow Warrior
        { x: -0.12, y: -0.16, z: -0.04, material: shadowBlackMaterial }, // Base
        { x: -0.12, y: -0.08, z: -0.04, material: darknessGrayMaterial }, // Torso
        { x: -0.12, y: 0.0, z: -0.04, material: shadowBlackMaterial }, // Chest
        { x: -0.12, y: 0.08, z: -0.04, material: spectralBlueMaterial }, // Head
        { x: -0.16, y: 0.0, z: -0.04, material: darknessGrayMaterial }, // Left arm
        { x: -0.08, y: 0.0, z: -0.04, material: darknessGrayMaterial }, // Right arm
        
        // Right Shadow Warrior
        { x: 0.12, y: -0.16, z: -0.04, material: shadowBlackMaterial }, // Base
        { x: 0.12, y: -0.08, z: -0.04, material: darknessGrayMaterial }, // Torso
        { x: 0.12, y: 0.0, z: -0.04, material: shadowBlackMaterial }, // Chest
        { x: 0.12, y: 0.08, z: -0.04, material: spectralBlueMaterial }, // Head
        { x: 0.08, y: 0.0, z: -0.04, material: darknessGrayMaterial }, // Left arm
        { x: 0.16, y: 0.0, z: -0.04, material: darknessGrayMaterial }, // Right arm
        
        // Back Left Shadow Warrior
        { x: -0.08, y: -0.12, z: -0.08, material: shadowBlackMaterial }, // Base
        { x: -0.08, y: -0.04, z: -0.08, material: darknessGrayMaterial }, // Torso
        { x: -0.08, y: 0.04, z: -0.08, material: voidPurpleMaterial }, // Head
        
        // Back Right Shadow Warrior
        { x: 0.08, y: -0.12, z: -0.08, material: shadowBlackMaterial }, // Base
        { x: 0.08, y: -0.04, z: -0.08, material: darknessGrayMaterial }, // Torso
        { x: 0.08, y: 0.04, z: -0.08, material: voidPurpleMaterial }, // Head
        
        // Far Back Warriors (smaller)
        { x: -0.04, y: -0.08, z: -0.12, material: shadowBlackMaterial },
        { x: 0.04, y: -0.08, z: -0.12, material: shadowBlackMaterial },
        { x: 0.0, y: -0.04, z: -0.12, material: darknessGrayMaterial }
    ];

    // Shadow Weapons (ethereal weapons wielded by warriors)
    const shadowWeaponsVoxels = [
        // Central warrior weapon (sword)
        { x: 0.06, y: 0.12, z: 0.0, material: crimsonRedMaterial }, // Sword blade top
        { x: 0.06, y: 0.08, z: 0.0, material: soulSilverMaterial }, // Sword blade mid
        { x: 0.06, y: 0.04, z: 0.0, material: crimsonRedMaterial }, // Sword blade bottom
        { x: 0.06, y: 0.0, z: 0.0, material: darknessGrayMaterial }, // Sword hilt
        
        // Left warrior weapon (spear)
        { x: -0.08, y: 0.12, z: -0.04, material: soulSilverMaterial }, // Spear tip
        { x: -0.08, y: 0.08, z: -0.04, material: darknessGrayMaterial }, // Spear shaft
        { x: -0.08, y: 0.04, z: -0.04, material: darknessGrayMaterial }, // Spear shaft
        
        // Right warrior weapon (axe)
        { x: 0.18, y: 0.08, z: -0.04, material: crimsonRedMaterial }, // Axe blade
        { x: 0.16, y: 0.06, z: -0.04, material: soulSilverMaterial }, // Axe blade
        { x: 0.14, y: 0.04, z: -0.04, material: darknessGrayMaterial }, // Axe handle
        
        // Back warriors weapons (smaller)
        { x: -0.06, y: 0.08, z: -0.08, material: spectralBlueMaterial },
        { x: 0.10, y: 0.08, z: -0.08, material: spectralBlueMaterial }
    ];

    // Dark Aura (mystical energy surrounding the army)
    const darkAuraVoxels = [
        // Inner aura ring
        { x: -0.20, y: 0.0, z: 0.0, material: voidPurpleMaterial },
        { x: 0.20, y: 0.0, z: 0.0, material: voidPurpleMaterial },
        { x: 0.0, y: 0.0, z: -0.20, material: voidPurpleMaterial },
        { x: 0.0, y: 0.0, z: 0.20, material: voidPurpleMaterial },
        
        // Diagonal aura points
        { x: -0.14, y: 0.0, z: -0.14, material: spectralBlueMaterial },
        { x: 0.14, y: 0.0, z: -0.14, material: spectralBlueMaterial },
        { x: -0.14, y: 0.0, z: 0.14, material: spectralBlueMaterial },
        { x: 0.14, y: 0.0, z: 0.14, material: spectralBlueMaterial },
        
        // Outer aura ring
        { x: -0.24, y: 0.04, z: 0.0, material: etherealCyanMaterial },
        { x: 0.24, y: 0.04, z: 0.0, material: etherealCyanMaterial },
        { x: 0.0, y: 0.04, z: -0.24, material: etherealCyanMaterial },
        { x: 0.0, y: 0.04, z: 0.24, material: etherealCyanMaterial },
        
        // Floating aura particles
        { x: -0.18, y: 0.12, z: -0.06, material: phantomWhiteMaterial },
        { x: 0.18, y: 0.12, z: -0.06, material: phantomWhiteMaterial },
        { x: -0.06, y: 0.16, z: -0.18, material: phantomWhiteMaterial },
        { x: 0.06, y: 0.16, z: -0.18, material: phantomWhiteMaterial },
        { x: -0.22, y: 0.08, z: 0.10, material: phantomWhiteMaterial },
        { x: 0.22, y: 0.08, z: 0.10, material: phantomWhiteMaterial }
    ];

    // Soul Energy (connecting energy between warriors)
    const soulEnergyVoxels = [
        // Energy connecting warriors
        { x: -0.06, y: 0.02, z: -0.02, material: etherealCyanMaterial }, // Central to left
        { x: 0.06, y: 0.02, z: -0.02, material: etherealCyanMaterial }, // Central to right
        { x: -0.04, y: 0.06, z: -0.06, material: spectralBlueMaterial }, // To back left
        { x: 0.04, y: 0.06, z: -0.06, material: spectralBlueMaterial }, // To back right
        
        // Vertical energy flows
        { x: 0.0, y: 0.12, z: 0.0, material: voidPurpleMaterial }, // Central energy
        { x: -0.12, y: 0.10, z: -0.04, material: spectralBlueMaterial }, // Left energy
        { x: 0.12, y: 0.10, z: -0.04, material: spectralBlueMaterial }, // Right energy
        
        // Swirling energy around army
        { x: -0.16, y: 0.06, z: -0.12, material: etherealCyanMaterial },
        { x: 0.16, y: 0.06, z: -0.12, material: etherealCyanMaterial },
        { x: -0.12, y: 0.14, z: 0.08, material: voidPurpleMaterial },
        { x: 0.12, y: 0.14, z: 0.08, material: voidPurpleMaterial },
        
        // Energy wisps
        { x: -0.20, y: 0.18, z: 0.0, material: phantomWhiteMaterial },
        { x: 0.20, y: 0.18, z: 0.0, material: phantomWhiteMaterial },
        { x: 0.0, y: 0.20, z: -0.16, material: phantomWhiteMaterial },
        { x: 0.0, y: 0.22, z: 0.12, material: phantomWhiteMaterial }
    ];

    // Create shadow warriors group
    const shadowWarriorsGroup = new THREE.Group();
    shadowWarriorsGroup.name = 'shadowWarriors';

    // Add shadow warriors voxels
    shadowWarriorsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.warriorPhase = index * 0.1; // Stagger animation
        shadowWarriorsGroup.add(mesh);
    });

    // Create shadow weapons group
    const shadowWeaponsGroup = new THREE.Group();
    shadowWeaponsGroup.name = 'shadowWeapons';

    // Add shadow weapons voxels
    shadowWeaponsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.weaponPhase = index * 0.15; // Stagger animation
        shadowWeaponsGroup.add(mesh);
    });

    // Create dark aura group
    const darkAuraGroup = new THREE.Group();
    darkAuraGroup.name = 'darkAura';

    // Add dark aura voxels
    darkAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.12; // Stagger animation
        darkAuraGroup.add(mesh);
    });

    // Create soul energy group
    const soulEnergyGroup = new THREE.Group();
    soulEnergyGroup.name = 'soulEnergy';

    // Add soul energy voxels
    soulEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.08; // Stagger animation
        soulEnergyGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(shadowWarriorsGroup);
    cardGroup.add(shadowWeaponsGroup);
    cardGroup.add(darkAuraGroup);
    cardGroup.add(soulEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        shadowMarch: 0,
        weaponGlow: 0,
        auraSwirl: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update shadow army card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateShadowArmyCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.shadowMarch += deltaTime * 2.0; // Shadow marching speed
    cardGroup.userData.weaponGlow += deltaTime * 4.0; // Weapon glow speed
    cardGroup.userData.auraSwirl += deltaTime * 1.5; // Aura swirling speed

    const time = cardGroup.userData.animationTime;
    const shadowMarch = cardGroup.userData.shadowMarch;
    const weaponGlow = cardGroup.userData.weaponGlow;
    const auraSwirl = cardGroup.userData.auraSwirl;

    // Animate shadow warriors (marching formation)
    const shadowWarriorsGroup = cardGroup.getObjectByName('shadowWarriors');
    if (shadowWarriorsGroup) {
        // Warriors marching motion
        const marchStep = Math.sin(shadowMarch * 3.0) * 0.001;
        shadowWarriorsGroup.position.y = marchStep;
        
        // Individual warrior animation
        shadowWarriorsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.warriorPhase !== undefined) {
                const warriorTime = shadowMarch + mesh.userData.warriorPhase;
                
                // Warrior breathing/presence effect
                const breathe = Math.sin(warriorTime * 2.0) * 0.0005;
                const flicker = Math.cos(warriorTime * 3.5) * 0.0003;
                
                mesh.position.x = mesh.userData.originalPosition.x + breathe;
                mesh.position.y = mesh.userData.originalPosition.y + flicker;
                
                // Shadow intensity fluctuation
                const shadowIntensity = 1.0 + Math.sin(warriorTime * 1.8) * 0.3;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * shadowIntensity;
                }
                
                // Shadow opacity variation (ghostly effect)
                const shadowOpacity = 0.9 + Math.sin(warriorTime * 2.2) * 0.1;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * shadowOpacity;
                }
            }
        });
    }

    // Animate shadow weapons (glowing and ready for battle)
    const shadowWeaponsGroup = cardGroup.getObjectByName('shadowWeapons');
    if (shadowWeaponsGroup) {
        shadowWeaponsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.weaponPhase !== undefined) {
                const weaponTime = weaponGlow + mesh.userData.weaponPhase;
                
                // Weapon floating motion
                const weaponFloat = Math.sin(weaponTime * 2.5) * 0.001;
                const weaponSway = Math.cos(weaponTime * 1.8) * 0.0008;
                
                mesh.position.x = mesh.userData.originalPosition.x + weaponSway;
                mesh.position.y = mesh.userData.originalPosition.y + weaponFloat;
                
                // Weapon glow intensity (battle ready)
                const weaponIntensity = 1.0 + Math.sin(weaponTime * 4.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * weaponIntensity;
                }
                
                // Weapon scale pulsing
                const weaponScale = 0.95 + Math.sin(weaponTime * 3.0) * 0.1;
                mesh.scale.setScalar(weaponScale);
            }
        });
    }

    // Animate dark aura (mystical army presence)
    const darkAuraGroup = cardGroup.getObjectByName('darkAura');
    if (darkAuraGroup) {
        darkAuraGroup.rotation.y = auraSwirl * 0.5; // Slow aura rotation
        
        darkAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = auraSwirl + mesh.userData.auraPhase;
                
                // Aura floating motion
                const auraFloat = Math.sin(auraTime * 3.0) * 0.002;
                const auraOrbit = Math.cos(auraTime * 2.0) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + auraOrbit;
                mesh.position.y = mesh.userData.originalPosition.y + auraFloat;
                
                // Aura intensity fluctuation
                const auraIntensity = 1.0 + Math.sin(auraTime * 4.5) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraIntensity;
                }
                
                // Aura opacity variation (mystical effect)
                const auraOpacity = 0.7 + Math.sin(auraTime * 3.5) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * auraOpacity;
                }
                
                // Aura scale pulsing
                const auraScale = 0.9 + Math.sin(auraTime * 3.8) * 0.2;
                mesh.scale.setScalar(auraScale);
            }
        });
    }

    // Animate soul energy (connecting energy between warriors)
    const soulEnergyGroup = cardGroup.getObjectByName('soulEnergy');
    if (soulEnergyGroup) {
        soulEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = shadowMarch * 2.0 + mesh.userData.energyPhase;
                
                // Energy flow motion
                const energyFlow = Math.sin(energyTime * 5.0) * 0.002;
                const energyPulse = Math.cos(energyTime * 6.0) * 0.0015;
                
                mesh.position.x = mesh.userData.originalPosition.x + energyFlow;
                mesh.position.z = mesh.userData.originalPosition.z + energyPulse;
                
                // Energy intensity fluctuation
                const energyIntensity = 1.0 + Math.sin(energyTime * 7.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyIntensity;
                }
                
                // Energy opacity variation
                const energyOpacity = 0.8 + Math.sin(energyTime * 5.5) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * energyOpacity;
                }
                
                // Energy scale pulsing
                const energyScale = 0.8 + Math.sin(energyTime * 6.5) * 0.3;
                mesh.scale.setScalar(energyScale);
            }
        });
    }

    // Overall shadow army presence (legendary power)
    const armyPulse = 1 + Math.sin(time * 1.5) * 0.1;
    const armyPresence = Math.cos(time * 4.0) * 0.0005;
    cardGroup.scale.setScalar(0.8 * armyPulse);
    cardGroup.position.x += armyPresence;
    cardGroup.position.z += armyPresence * 0.5;
}

// Export the shadow army card data for the loot system
export const SHADOW_ARMY_CARD_DATA = {
    name: "Shadow Army",
    description: 'Summons an army of shadow warriors that fight alongside you for 30 seconds, each dealing damage to nearby enemies with ethereal weapons.',
    category: 'card',
    rarity: 'legendary',
    effect: 'shadow_army',
    effectValue: 5, // Number of shadow warriors summoned
    createFunction: createShadowArmyCard,
    updateFunction: updateShadowArmyCardAnimation,
    voxelModel: 'shadow_army_card',
    glow: {
        color: 0x4B0082,
        intensity: 1.8
    }
};

export default createShadowArmyCard;