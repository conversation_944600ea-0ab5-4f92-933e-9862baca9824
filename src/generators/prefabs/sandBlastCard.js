import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Sand Blast Card Prefab
 * Creates a powerful sand stream with swirling dust particles
 */

// Sand blast specific colors
const SAND_COLORS = {
    SAND_TAN: 0xDEB887,         // Primary sand color
    DUST_BROWN: 0xD2B48C,       // Dusty brown particles
    GRIT_GRAY: 0xA0A0A0,        // Rocky grit
    EARTH_BROWN: 0x8B4513,      // Deep earth brown
    DESERT_YELLOW: 0xF4A460,    // Desert yellow
    STONE_GRAY: 0x696969,       // Stone gray
    WIND_WHITE: 0xF5F5DC        // Wind/motion lines
};

/**
 * Create a sand blast card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The sand blast card 3D model
 */
export function createSandBlastCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SandBlastCard';

    // Materials
    const sandTanMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.SAND_TAN,
        emissive: SAND_COLORS.SAND_TAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const dustBrownMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.DUST_BROWN,
        emissive: SAND_COLORS.DUST_BROWN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const gritGrayMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.GRIT_GRAY,
        emissive: SAND_COLORS.GRIT_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7
    });

    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.EARTH_BROWN,
        emissive: SAND_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.85
    });

    const desertYellowMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.DESERT_YELLOW,
        emissive: SAND_COLORS.DESERT_YELLOW,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const windWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SAND_COLORS.WIND_WHITE,
        emissive: SAND_COLORS.WIND_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create sand stream (horizontal blast effect)
    const sandStreamVoxels = [
        // Main stream core (left to right)
        { x: -4, y: 0, z: 0, material: sandTanMaterial },
        { x: -3, y: 0, z: 0, material: desertYellowMaterial },
        { x: -2, y: 0, z: 0, material: desertYellowMaterial },
        { x: -1, y: 0, z: 0, material: windWhiteMaterial },
        { x: 0, y: 0, z: 0, material: windWhiteMaterial },
        { x: 1, y: 0, z: 0, material: windWhiteMaterial },
        { x: 2, y: 0, z: 0, material: desertYellowMaterial },
        { x: 3, y: 0, z: 0, material: desertYellowMaterial },
        { x: 4, y: 0, z: 0, material: sandTanMaterial },

        // Stream thickness (upper)
        { x: -2, y: 1, z: 0, material: dustBrownMaterial },
        { x: -1, y: 1, z: 0, material: sandTanMaterial },
        { x: 0, y: 1, z: 0, material: desertYellowMaterial },
        { x: 1, y: 1, z: 0, material: sandTanMaterial },
        { x: 2, y: 1, z: 0, material: dustBrownMaterial },

        // Stream thickness (lower)
        { x: -2, y: -1, z: 0, material: dustBrownMaterial },
        { x: -1, y: -1, z: 0, material: sandTanMaterial },
        { x: 0, y: -1, z: 0, material: desertYellowMaterial },
        { x: 1, y: -1, z: 0, material: sandTanMaterial },
        { x: 2, y: -1, z: 0, material: dustBrownMaterial }
    ];

    // Create swirling dust cloud
    const dustCloudVoxels = [
        // Upper dust cloud
        { x: -3, y: 2, z: 0, material: gritGrayMaterial },
        { x: -1, y: 3, z: 0, material: gritGrayMaterial },
        { x: 1, y: 3, z: 0, material: gritGrayMaterial },
        { x: 3, y: 2, z: 0, material: gritGrayMaterial },
        { x: 0, y: 2, z: 0, material: dustBrownMaterial },

        // Lower dust cloud
        { x: -3, y: -2, z: 0, material: gritGrayMaterial },
        { x: -1, y: -3, z: 0, material: gritGrayMaterial },
        { x: 1, y: -3, z: 0, material: gritGrayMaterial },
        { x: 3, y: -2, z: 0, material: gritGrayMaterial },
        { x: 0, y: -2, z: 0, material: dustBrownMaterial },

        // Side dust puffs
        { x: -5, y: 1, z: 0, material: dustBrownMaterial },
        { x: -5, y: -1, z: 0, material: dustBrownMaterial },
        { x: 5, y: 1, z: 0, material: dustBrownMaterial },
        { x: 5, y: -1, z: 0, material: dustBrownMaterial }
    ];

    // Create scattered sand particles
    const sandParticleVoxels = [
        // Flying debris around the blast
        { x: -4, y: 3, z: 0, material: earthBrownMaterial },
        { x: -2, y: 4, z: 0, material: sandTanMaterial },
        { x: 0, y: 4, z: 0, material: gritGrayMaterial },
        { x: 2, y: 4, z: 0, material: sandTanMaterial },
        { x: 4, y: 3, z: 0, material: earthBrownMaterial },

        { x: -4, y: -3, z: 0, material: earthBrownMaterial },
        { x: -2, y: -4, z: 0, material: sandTanMaterial },
        { x: 0, y: -4, z: 0, material: gritGrayMaterial },
        { x: 2, y: -4, z: 0, material: sandTanMaterial },
        { x: 4, y: -3, z: 0, material: earthBrownMaterial },

        // Additional scattered particles
        { x: -5, y: 2, z: 0, material: dustBrownMaterial },
        { x: -5, y: -2, z: 0, material: dustBrownMaterial },
        { x: 5, y: 2, z: 0, material: dustBrownMaterial },
        { x: 5, y: -2, z: 0, material: dustBrownMaterial }
    ];

    // Create wind/motion lines
    const windLineVoxels = [
        // Motion indicators showing direction
        { x: -6, y: 0, z: 0, material: windWhiteMaterial },
        { x: -5, y: 0, z: 0, material: windWhiteMaterial },
        { x: 5, y: 0, z: 0, material: windWhiteMaterial },
        { x: 6, y: 0, z: 0, material: windWhiteMaterial },

        // Angled motion lines
        { x: -6, y: 1, z: 0, material: windWhiteMaterial },
        { x: -6, y: -1, z: 0, material: windWhiteMaterial },
        { x: 6, y: 1, z: 0, material: windWhiteMaterial },
        { x: 6, y: -1, z: 0, material: windWhiteMaterial }
    ];

    // Create all voxels
    [...sandStreamVoxels, ...dustCloudVoxels, ...sandParticleVoxels, ...windLineVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.4, // Compact horizontally
            voxel.y * VOXEL_SIZE * 0.4, // Compact vertically
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (sandStreamVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'stream';
        } else if (dustCloudVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'dust';
        } else if (sandParticleVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'particle';
        } else {
            mesh.userData.voxelType = 'wind';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        streamFlow: 0,
        dustSwirl: 0,
        particleFloat: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update sand blast card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSandBlastCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.streamFlow += deltaTime * 4.0;
    cardGroup.userData.dustSwirl += deltaTime * 2.5;
    cardGroup.userData.particleFloat += deltaTime * 3.0;

    const time = cardGroup.userData.animationTime;
    const streamFlow = cardGroup.userData.streamFlow;
    const dustSwirl = cardGroup.userData.dustSwirl;
    const particleFloat = cardGroup.userData.particleFloat;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'stream':
                    // Sand stream flows with intensity waves
                    const streamIntensity = 0.7 + Math.sin(streamFlow * 4.0 + child.position.x * 2) * 0.5;
                    const streamMotion = Math.sin(streamFlow * 6.0 + child.position.x) * 0.005;
                    
                    child.material.emissiveIntensity = baseEmissive * streamIntensity;
                    child.material.opacity = baseOpacity * (0.8 + streamIntensity * 0.2);
                    child.position.x += streamMotion;
                    break;

                case 'dust':
                    // Dust swirls and expands
                    const dustIntensity = 0.5 + Math.sin(dustSwirl * 3.0 + child.position.x + child.position.y) * 0.6;
                    const dustMotion = Math.sin(dustSwirl * 2.0 + child.position.y * 2) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * dustIntensity;
                    child.material.opacity = baseOpacity * dustIntensity;
                    child.position.x += dustMotion;
                    child.position.y += dustMotion * 0.5;
                    break;

                case 'particle':
                    // Particles float and flicker
                    const particleIntensity = 0.6 + Math.sin(particleFloat * 5.0 + child.position.x * 3) * 0.7;
                    const particleMotion = Math.cos(particleFloat * 3.5 + child.position.y) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * particleIntensity;
                    child.material.opacity = baseOpacity * particleIntensity;
                    child.position.x += particleMotion;
                    child.position.y += particleMotion * 0.8;
                    break;

                case 'wind':
                    // Wind lines show rapid motion
                    const windIntensity = 0.4 + Math.sin(streamFlow * 8.0 + child.position.x * 4) * 0.8;
                    const windMotion = Math.sin(streamFlow * 10.0) * 0.012;
                    
                    child.material.emissiveIntensity = baseEmissive * windIntensity;
                    child.material.opacity = baseOpacity * windIntensity;
                    child.position.x += windMotion;
                    break;
            }
        }
    });

    // Overall card motion (simulating wind force)
    const sandBlast = Math.sin(time * 3.0) * 0.01;
    cardGroup.position.x += sandBlast;
}

// Export the sand blast card data for the loot system
export const SAND_BLAST_CARD_DATA = {
    name: 'Sand Blast',
    description: 'Unleashes a powerful stream of razor-sharp sand particles that tears through enemies and creates a spreading cloud of abrasive dust.',
    category: 'card',
    rarity: 'common',
    effect: 'sand_blast',
    effectValue: 35,
    createFunction: createSandBlastCard,
    updateFunction: updateSandBlastCardAnimation,
    voxelModel: 'sand_blast_card',
    glow: {
        color: 0xDEB887,
        intensity: 1.0
    }
};

export default createSandBlastCard;