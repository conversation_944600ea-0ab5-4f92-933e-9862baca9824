import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Mystical Stone Floor
 *
 * Creates mystical stone floor surface using randomized sacred purple-blue tone voxels.
 * Uses the same structure as cave floor but with mystical materials that match the mystical walls.
 */

// Mystical stone floor materials (matching wall materials but optimized for floors)
const MYSTICAL_FLOOR_MATERIALS = [
    _getMaterialByHex_Cached('7A6C98'), // Darker mystical purple-gray (primary floor)
    _getMaterialByHex_Cached('8B7DAD'), // Lighter mystical purple (secondary floor)
    _getMaterialByHex_Cached('5B4B85'), // Darker mystical purple (accent floor)
    _getMaterialByHex_Cached('3A3A5A'), // Dark slate purple (mortar/cracks)
    _getMaterialByHex_Cached('6B58DE'), // Medium slate blue (mystical accent)
    _getMaterialByHex_Cached('8360CB'), // Medium orchid (sacred highlights)
    _getMaterialByHex_Cached('382D7B')  // Dark slate blue (deep shadows)
];

/**
 * Get random mystical floor material
 */
function getRandomMysticalFloorMat() {
    const rand = Math.random();
    if (rand < 0.40) return MYSTICAL_FLOOR_MATERIALS[0]; // Primary darker floor
    if (rand < 0.70) return MYSTICAL_FLOOR_MATERIALS[1]; // Secondary lighter floor
    if (rand < 0.85) return MYSTICAL_FLOOR_MATERIALS[2]; // Darker accent
    if (rand < 0.92) return MYSTICAL_FLOOR_MATERIALS[4]; // Mystical blue accent
    if (rand < 0.97) return MYSTICAL_FLOOR_MATERIALS[5]; // Sacred highlights
    return MYSTICAL_FLOOR_MATERIALS[6]; // Deep shadows
}

/**
 * Creates a mystical stone floor surface using randomized sacred tone voxels.
 * Uses the same structure as cave floor but with mystical materials.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Mesh|THREE.Group|null} The merged floor mesh/group or null if empty.
 */
export function createMysticalStoneFloor(width, depth, roomData) {
    console.log(`[MysticalStoneFloor] Creating floor: ${width}x${depth}`);

    const geometriesByMaterial = {};
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use double environment scale for floors to reduce voxel count (same as cave floor)
    const FLOOR_SCALE = ENVIRONMENT_PIXEL_SCALE * 2;

    // Grid dimensions (same approach as cave floor for consistent coverage)
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / FLOOR_SCALE);
    const numZ_env = Math.ceil(numZ / FLOOR_SCALE);

    const floorY = 0; // Base floor Y position
    const overlayOffsetY = VOXEL_SIZE * 5.0; // EXTREME offset - 5x voxel height to completely eliminate z-fighting

    // ULTRA-SMOOTH overlay parameters for refined, less chunky appearance
    const overlayPatchSpawnProbability = 0.20; // High density for smooth coverage
    const crackOverlayFraction = 0.15; // Very few cracks, mostly sacred patterns
    const minPatchSize = 1; // Single voxel patches for finest detail
    const maxPatchSize = 2; // Very small patches for smooth, refined look

    // Use same voxel sizing approach as cave floor for consistent coverage
    const floorVoxelSize = VOXEL_SIZE * FLOOR_SCALE;
    const floorVoxelHeight = VOXEL_SIZE * 0.8; // Slightly thinner base voxels

    // Centering offsets (same approach as cave floor)
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    const floorVoxelGeo = getOrCreateGeometry(
        `mystical_floor_smooth_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, floorVoxelHeight, floorVoxelSize)
    );

    // Create paper-thin geometry for ultra-smooth, refined overlay patches
    const overlayVoxelHeight = VOXEL_SIZE * 0.08; // Paper-thin overlay voxels for maximum smoothness
    const overlayVoxelGeo = getOrCreateGeometry(
        `mystical_floor_overlay_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, overlayVoxelHeight, floorVoxelSize)
    );

    // Use seeded PRNG for consistent patterns
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);

    let voxelsAdded = 0;

    // --- Generate Base Floor Pattern ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Use proper spacing for full coverage (same as cave floor)
            const baseX = ex * floorVoxelSize - offsetX;
            const baseZ = ez * floorVoxelSize - offsetZ;

            // POND EXCLUSION: Skip floor voxels in pond area (center of room)
            const distanceFromCenter = Math.sqrt(baseX * baseX + baseZ * baseZ);
            if (distanceFromCenter <= 3.2) { // EXPANDED: Updated to match pond hole radius
                continue; // Skip this voxel to create pond depression
            }

            const baseMaterial = getRandomMysticalFloorMat();

            // Add slight height variation for mystical texture
            const heightVariation = (random() - 0.5) * 0.02;
            const finalY = floorY + heightVariation;

            tempMatrix.makeTranslation(baseX, finalY, baseZ);
            addGeometry(floorVoxelGeo, baseMaterial, tempMatrix);
            voxelsAdded++;
        }
    }

    // --- Generate Mystical Overlay Patches ---
    const overlayPatches = [];
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Check if this patch would be in pond area
            const patchX = ex * floorVoxelSize - offsetX;
            const patchZ = ez * floorVoxelSize - offsetZ;
            const distanceFromCenter = Math.sqrt(patchX * patchX + patchZ * patchZ);

            // Skip overlay patches in pond area
            if (distanceFromCenter <= 3.2) { // EXPANDED: Updated to match pond hole radius
                continue;
            }

            if (random() < overlayPatchSpawnProbability) {
                const patchSize = Math.floor(random() * (maxPatchSize - minPatchSize + 1)) + minPatchSize;
                const patch = {
                    startX: ex,
                    startZ: ez,
                    size: patchSize,
                    isCrack: random() < crackOverlayFraction
                };
                overlayPatches.push(patch);
            }
        }
    }

    // --- Apply Overlay Patches ---
    overlayPatches.forEach(patch => {
        const overlayMaterial = patch.isCrack 
            ? MYSTICAL_FLOOR_MATERIALS[3] // Dark mortar for cracks
            : MYSTICAL_FLOOR_MATERIALS[5]; // Sacred highlights for mystical patterns

        for (let pz = 0; pz < patch.size && patch.startZ + pz < numZ_env; pz++) {
            for (let px = 0; px < patch.size && patch.startX + px < numX_env; px++) {
                const ex = patch.startX + px;
                const ez = patch.startZ + pz;

                // Use proper spacing for full coverage (same as cave floor)
                const baseX = ex * floorVoxelSize - offsetX;
                const baseZ = ez * floorVoxelSize - offsetZ;

                // POND EXCLUSION: Skip overlay voxels in pond area
                const distanceFromCenter = Math.sqrt(baseX * baseX + baseZ * baseZ);
                if (distanceFromCenter <= 3.2) { // EXPANDED: Updated to match pond hole radius
                    continue; // Skip this overlay voxel
                }

                // LAYERED overlay positioning to prevent z-fighting between overlay types
                let overlayY = floorY + overlayOffsetY;

                // CRITICAL FIX: Layer crack overlays ABOVE sacred highlights to prevent z-fighting
                if (overlayMaterial === MYSTICAL_FLOOR_MATERIALS[3]) { // Dark mortar for cracks
                    overlayY += VOXEL_SIZE * 1.0; // Put cracks one voxel height above sacred highlights
                }

                // Add maximum randomization for ultra-organic, smooth appearance
                const offsetVariationX = (random() - 0.5) * 0.06; // Maximum randomization for organic look
                const offsetVariationZ = (random() - 0.5) * 0.06; // Maximum randomization for organic look

                tempMatrix.makeTranslation(
                    baseX + offsetVariationX,
                    overlayY,
                    baseZ + offsetVariationZ
                );
                addGeometry(overlayVoxelGeo, overlayMaterial, tempMatrix); // Use thinner overlay geometry
                voxelsAdded++;
            }
        }
    });

    console.log(`[MysticalStoneFloor] Generated ${voxelsAdded} floor voxels with ${overlayPatches.length} overlay patches`);

    // --- Merge Geometries by Material with Polygon Offset ---
    const finalGroup = new THREE.Group();

    // Get material hex strings for overlay identification
    const crackHex = MYSTICAL_FLOOR_MATERIALS[3].color.getHexString(); // Dark mortar for cracks
    const sacredHex = MYSTICAL_FLOOR_MATERIALS[5].color.getHexString(); // Sacred highlights

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for mystical stone floor: ${colorHex}`);
            continue;
        }

        // Clone material to apply polygon offset without modifying shared instance
        const finalMaterial = originalMaterial.clone();

        // Apply polygon offset based on material type to prevent z-fighting
        if (colorHex === crackHex || colorHex === sacredHex) {
            // Pull overlay materials towards camera
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = -2.0; // More aggressive offset for overlays
            finalMaterial.polygonOffsetUnits = -2.0;
        } else {
            // Push base materials away slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = 1.0;
            finalMaterial.polygonOffsetUnits = 1.0;
        }

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.castShadow = false; // Floors don't cast shadows
            mesh.receiveShadow = true; // But they receive shadows
            mesh.name = 'mysticalStoneFloor';
            mesh.userData.isFloor = true;
            mesh.userData.floorType = 'mystical_stone_floor';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for mystical stone floor, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.floorType = 'mystical_stone_floor';
    finalGroup.userData.isFloor = true;
    finalGroup.name = 'mysticalStoneFloor';

    // Set shadow properties for entire group
    finalGroup.castShadow = false;
    finalGroup.receiveShadow = true;

    console.log(`[MysticalStoneFloor] ✅ Generated mystical floor with ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return finalGroup;
}


