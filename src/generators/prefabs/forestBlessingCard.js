import * as THREE from 'three';
import { getItemData } from '../../entities/ItemTypes.js';

/**
 * Forest Blessing Card Item Data
 * Creates a 3D voxel-style card for treasure chests
 */

export const FOREST_BLESSING_CARD_DATA = {
    id: 'forest_blessing',
    name: 'Forest Blessing',
    description: 'Channels the ancient power of the forest, causing vines to emerge from the ground and entangle enemies while healing the caster.',
    type: 'card',
    category: 'card',
    rarity: 'rare',
    
    // Card-specific properties
    cardType: 'spell',
    cardBorderColor: 0x00ff00, // Green border
    
    // 3D model creation function
    createFunction: (options = {}) => {
        return createForestBlessingCard(options);
    }
};

/**
 * Create a 3D Forest Blessing card model
 * @param {Object} options - Creation options
 * @returns {THREE.Group} The card 3D model
 */
function createForestBlessingCard(options = {}) {
    const { seed = Date.now() } = options;
    
    // Get item data for consistency
    const itemData = getItemData('forest_blessing');
    if (!itemData) {
        console.warn('[ForestBlessingCard] Item data not found, using defaults');
    }
    
    // Create card group
    const cardGroup = new THREE.Group();
    cardGroup.name = 'forest_blessing_card';
    cardGroup.userData.itemType = 'forest_blessing';
    cardGroup.userData.isCard = true;
    cardGroup.userData.seed = seed;
    
    // Card dimensions (voxel-compatible)
    const cardWidth = 0.8;
    const cardHeight = 1.2;
    const cardThickness = 0.08;
    
    // Create card base (main body)
    const cardGeometry = new THREE.BoxGeometry(cardWidth, cardHeight, cardThickness);
    const cardMaterial = new THREE.MeshLambertMaterial({
        color: 0x1a1a1a, // Dark card background
        transparent: false
    });
    
    const cardBase = new THREE.Mesh(cardGeometry, cardMaterial);
    cardBase.name = 'cardBase';
    cardGroup.add(cardBase);
    
    // Create green border
    createCardBorder(cardGroup, cardWidth, cardHeight, cardThickness);
    
    // Create vine artwork
    createVineArtwork(cardGroup, cardWidth, cardHeight);

    // Create card title area (simplified)
    createCardTitle(cardGroup, cardWidth, cardHeight);
    
    // Add subtle glow effect
    createCardGlow(cardGroup, cardWidth, cardHeight);
    
    // Scale card appropriately for chest display
    cardGroup.scale.setScalar(0.6);

    // Ensure proper orientation (match Call of Ascension orientation)
    cardGroup.rotation.set(0, 0, 0);

    return cardGroup;
}

/**
 * Create voxel-style border around the card
 */
function createCardBorder(cardGroup, cardWidth, cardHeight, cardThickness) {
    const borderThickness = 0.04;
    const borderColor = 0x00ff00; // Green border
    
    const borderMaterial = new THREE.MeshLambertMaterial({
        color: borderColor,
        transparent: false
    });
    
    // Top border
    const topBorder = new THREE.Mesh(
        new THREE.BoxGeometry(cardWidth + borderThickness * 2, borderThickness, cardThickness + 0.01),
        borderMaterial
    );
    topBorder.position.set(0, cardHeight / 2 + borderThickness / 2, 0);
    cardGroup.add(topBorder);
    
    // Bottom border
    const bottomBorder = new THREE.Mesh(
        new THREE.BoxGeometry(cardWidth + borderThickness * 2, borderThickness, cardThickness + 0.01),
        borderMaterial
    );
    bottomBorder.position.set(0, -cardHeight / 2 - borderThickness / 2, 0);
    cardGroup.add(bottomBorder);
    
    // Left border
    const leftBorder = new THREE.Mesh(
        new THREE.BoxGeometry(borderThickness, cardHeight, cardThickness + 0.01),
        borderMaterial
    );
    leftBorder.position.set(-cardWidth / 2 - borderThickness / 2, 0, 0);
    cardGroup.add(leftBorder);
    
    // Right border
    const rightBorder = new THREE.Mesh(
        new THREE.BoxGeometry(borderThickness, cardHeight, cardThickness + 0.01),
        borderMaterial
    );
    rightBorder.position.set(cardWidth / 2 + borderThickness / 2, 0, 0);
    cardGroup.add(rightBorder);
}

/**
 * Create animated vine artwork for the card
 */
function createVineArtwork(cardGroup, cardWidth, cardHeight) {
    const voxelSize = 0.06;
    const vineColor = 0x228B22; // Forest green
    const leafColor = 0x32CD32; // Lime green

    const vineMaterial = new THREE.MeshLambertMaterial({
        color: vineColor,
        transparent: false
    });

    const leafMaterial = new THREE.MeshLambertMaterial({
        color: leafColor,
        transparent: false
    });

    // Create animated vine groups for animation
    const mainStemGroup = new THREE.Group();
    const leftBranchGroup = new THREE.Group();
    const rightBranchGroup = new THREE.Group();
    const leavesGroup = new THREE.Group();

    mainStemGroup.name = 'mainStem';
    leftBranchGroup.name = 'leftBranch';
    rightBranchGroup.name = 'rightBranch';
    leavesGroup.name = 'leaves';

    // Main vine stem (center) - grows upward, make top more prominent
    const mainStemPattern = [
        [0, -0.3], [0, -0.2], [0, -0.1], [0, 0], [0, 0.1], [0, 0.2], [0, 0.3], [0, 0.35]
    ];

    // Left branch - sways left, extend to top
    const leftBranchPattern = [
        [-0.05, 0.05], [-0.1, 0.1], [-0.15, 0.15], [-0.2, 0.2], [-0.25, 0.25], [-0.3, 0.3],
        [-0.05, -0.15], [-0.1, -0.1]
    ];

    // Right branch - sways right, extend to top
    const rightBranchPattern = [
        [0.05, 0.05], [0.1, 0.1], [0.15, 0.15], [0.2, 0.2], [0.25, 0.25], [0.3, 0.3],
        [0.05, -0.15], [0.1, -0.1]
    ];

    // Leaf pattern coordinates - flutter, more prominent at top
    const leafPattern = [
        // Top leaves (most prominent)
        [-0.35, 0.35], [-0.28, 0.32], [0.35, 0.35], [0.28, 0.32], [0, 0.38],
        // Branch leaves
        [-0.25, 0.25], [-0.18, 0.18], [0.25, 0.25], [0.18, 0.18],
        [-0.12, -0.05], [0.12, -0.05], [0, 0.25],
        // Small decorative leaves
        [-0.08, 0.12], [0.08, 0.12], [-0.03, -0.08], [0.03, -0.08]
    ];

    // Create main stem voxels
    mainStemPattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
            vineMaterial
        );
        voxel.position.set(x, y, 0.05);
        voxel.name = 'stemVoxel';
        mainStemGroup.add(voxel);
    });

    // Create left branch voxels
    leftBranchPattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
            vineMaterial
        );
        voxel.position.set(x, y, 0.05);
        voxel.name = 'leftBranchVoxel';
        leftBranchGroup.add(voxel);
    });

    // Create right branch voxels
    rightBranchPattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
            vineMaterial
        );
        voxel.position.set(x, y, 0.05);
        voxel.name = 'rightBranchVoxel';
        rightBranchGroup.add(voxel);
    });

    // Create leaf voxels
    leafPattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(voxelSize * 0.8, voxelSize * 0.8, voxelSize * 0.8),
            leafMaterial
        );
        voxel.position.set(x, y, 0.06);
        voxel.name = 'leafVoxel';
        leavesGroup.add(voxel);
    });

    // Add groups to card model
    cardGroup.add(mainStemGroup);
    cardGroup.add(leftBranchGroup);
    cardGroup.add(rightBranchGroup);
    cardGroup.add(leavesGroup);

    // Store references for animation (will be used by Card class)
    cardGroup.userData.mainStemGroup = mainStemGroup;
    cardGroup.userData.leftBranchGroup = leftBranchGroup;
    cardGroup.userData.rightBranchGroup = rightBranchGroup;
    cardGroup.userData.leavesGroup = leavesGroup;
}



/**
 * Create card title area
 */
function createCardTitle(cardGroup, cardWidth, cardHeight) {
    // Create a simple title bar at the bottom
    const titleBarGeometry = new THREE.BoxGeometry(cardWidth * 0.9, 0.15, 0.02);
    const titleBarMaterial = new THREE.MeshLambertMaterial({
        color: 0x333333,
        transparent: false
    });
    
    const titleBar = new THREE.Mesh(titleBarGeometry, titleBarMaterial);
    titleBar.position.set(0, -cardHeight / 2 + 0.1, 0.05);
    titleBar.name = 'titleBar';
    cardGroup.add(titleBar);
    
    // Add some decorative voxels for the title
    const titleVoxelMaterial = new THREE.MeshLambertMaterial({
        color: 0x00ff00 // Green to match theme
    });
    
    // Simple pattern for "Forest Blessing" representation
    const titlePattern = [
        [-0.2, -0.45], [-0.1, -0.45], [0, -0.45], [0.1, -0.45], [0.2, -0.45]
    ];
    
    titlePattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(0.03, 0.03, 0.03),
            titleVoxelMaterial
        );
        voxel.position.set(x, y, 0.06);
        cardGroup.add(voxel);
    });
}

/**
 * Create subtle glow effect
 */
function createCardGlow(cardGroup, cardWidth, cardHeight) {
    const glowGeometry = new THREE.PlaneGeometry(cardWidth * 1.1, cardHeight * 1.1);
    const glowMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ff00, // Green glow
        transparent: true,
        opacity: 0.1,
        side: THREE.DoubleSide
    });
    
    const glow = new THREE.Mesh(glowGeometry, glowMaterial);
    glow.position.z = -0.01; // Behind the card
    glow.name = 'cardGlow';
    cardGroup.add(glow);
}

// Export the card data
export default FOREST_BLESSING_CARD_DATA;
