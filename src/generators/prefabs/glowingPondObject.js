import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a glowing pond object (voxel-style)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Pond object group
 */
export function createGlowingPondObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 999);

    // Use consistent voxel size
    const pondVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('glowing_pond_voxel', () =>
        new THREE.BoxGeometry(pondVoxelSize, pondVoxelSize * 0.4, pondVoxelSize)
    );

    // Create mystical sacred water material with deeper blue-purple tones
    const waterMaterial = _getMaterialByHex_Cached('4A5FCC', {
        transparent: true,
        opacity: 0.7, // More opaque for richer color
        emissive: new THREE.Color(0x1A2F66), // Deeper mystical blue emissive
        emissiveIntensity: 0.3 // Higher intensity for more sacred glow
    });

    // Create 5x5 grid of water voxels for larger pond
    const waterVoxels = [];
    // EXPANDED: Use 7x7 grid to better fill the 3.0 radius hole
    for (let x = -3; x <= 3; x++) {
        for (let z = -3; z <= 3; z++) {
            // Skip far corners for more natural circular shape
            if (Math.abs(x) === 3 && Math.abs(z) === 3) continue;
            
            // Additional check to make it more circular - skip voxels too far from center
            const distanceFromCenter = Math.sqrt(x * x + z * z);
            if (distanceFromCenter > 3.2) continue; // Match floor hole radius (3.0) + small buffer

            const waterVoxel = new THREE.Mesh(baseGeometry.clone(), waterMaterial);

            waterVoxel.position.set(
                x * pondVoxelSize,
                0.05, // EMBEDDED: Slightly recessed into the floor
                z * pondVoxelSize
            );

            // Store original position for animation
            waterVoxel.userData.originalPosition = waterVoxel.position.clone();
            waterVoxel.userData.isWaterVoxel = true;
            waterVoxel.userData.isFloorObject = true;
            waterVoxel.castShadow = false;
            waterVoxel.receiveShadow = true;
            group.add(waterVoxel);
            waterVoxels.push(waterVoxel);
        }
    }

    // No sacred light - just water

    // Create invisible collision boxes to prevent player from walking on pond
    // But allow fish to move freely (fish movement system will ignore these)
    const collisionBoxes = [];

    // EXTENDED COVERAGE: Cover larger area including north side
    for (let x = -3; x <= 3; x++) {
        for (let z = -3; z <= 3; z++) {
            // Skip far corners for more natural shape
            if (Math.abs(x) === 3 && Math.abs(z) === 3) continue;

            // Only create collision for water area and immediate surroundings
            const distanceFromCenter = Math.sqrt(x * x + z * z);
            if (distanceFromCenter > 3.2) continue; // EXPANDED: Match water coverage area (3.2 radius)

            // Create invisible collision box that extends only to player height to avoid fog interference
            const collisionGeometry = new THREE.BoxGeometry(pondVoxelSize, pondVoxelSize * 2.0, pondVoxelSize);
            const collisionMaterial = new THREE.MeshBasicMaterial({
                transparent: true,
                opacity: 0,
                visible: false // Completely invisible
            });
            const collisionBox = new THREE.Mesh(collisionGeometry, collisionMaterial);

            collisionBox.position.set(
                x * pondVoxelSize,
                pondVoxelSize * 1.0, // FIXED: Reduced from 3.0 to 1.0 to center at player height
                z * pondVoxelSize
            );

            // Mark as pond collision for player movement system
            collisionBox.userData = {
                isPondCollision: true,
                isFloorObject: false, // Not a floor - blocks player movement
                hasCollision: true,
                blockPlayer: true, // Specifically blocks player
                allowFish: true // Allow fish to move through
            };

            collisionBox.name = `pond_collision_${x}_${z}`;
            group.add(collisionBox);
            collisionBoxes.push(collisionBox);
        }
    }

    // Create blue light source similar to soul orbs
    const pondLight = new THREE.PointLight(0x4A90E2, 2.0, 8.0); // Blue light, intensity 2.0, range 8.0
    pondLight.position.set(0, 0.2, 0); // Slightly above water surface
    pondLight.name = 'pondLight';

    // Add subtle light animation
    pondLight.userData = {
        originalIntensity: 2.0,
        animationTime: 0,
        isPondLight: true
    };

    group.add(pondLight);
    console.log('[GlowingPondObject] ✨ Added blue pond light source');

    // Store references for animation
    group.userData.waterVoxels = waterVoxels;
    group.userData.collisionBoxes = collisionBoxes;
    group.userData.pondLight = pondLight; // Store light reference
    group.userData.animationTime = 0; // Track animation time
    group.userData.isAnimated = true; // Enable animation

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'glowing_pond',
        isPond: true,
        isInteractable: false,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : false,
        isInteriorObject: true,
        voxelScale: pondVoxelSize,
        hasCollision: true, // Enable collision for the pond
        blockPlayer: true, // Block player movement
        allowFish: true // Allow fish to move through
    };

    group.name = 'glowing_pond';

    console.log('[GlowingPondObject] ✅ Created voxel-style glowing pond with collision detection');
    console.log(`[GlowingPondObject] Added ${collisionBoxes.length} collision boxes for player blocking`);
    return group;
}

/**
 * Animate the pond water with gentle waves and mystical effects
 * Call this function in your main animation loop
 * @param {THREE.Group} pondGroup - The pond group object
 * @param {number} deltaTime - Time since last frame in seconds
 */
export function animatePondWater(pondGroup, deltaTime) {
    if (!pondGroup || !pondGroup.userData.isAnimated || !pondGroup.userData.waterVoxels) {
        return;
    }

    // Update animation time
    pondGroup.userData.animationTime += deltaTime;
    const time = pondGroup.userData.animationTime;

    const waterVoxels = pondGroup.userData.waterVoxels;
    const pondLight = pondGroup.userData.pondLight;

    waterVoxels.forEach((waterVoxel, index) => {
        if (!waterVoxel || !waterVoxel.userData.originalPosition) {
            // Store original position if not already stored
            if (waterVoxel && !waterVoxel.userData.originalPosition) {
                waterVoxel.userData.originalPosition = waterVoxel.position.clone();
            }
            return;
        }

        const originalPos = waterVoxel.userData.originalPosition;

        // Create realistic water wave motion
        const waveSpeed = 1.2; // Slightly slower for more natural movement
        const waveAmplitude = 0.08; // More noticeable waves (4x larger)
        const waveFrequency = 1.5; // Lower frequency for longer waves

        // Distance from center for radial wave effects
        const distanceFromCenter = Math.sqrt(originalPos.x * originalPos.x + originalPos.z * originalPos.z);
        const radialWavePhase = distanceFromCenter * 0.5;

        // Multiple wave layers for realistic water motion
        // Primary wave (main ripple pattern)
        const primaryWave = Math.sin(time * waveSpeed + radialWavePhase) * waveAmplitude;

        // Secondary wave (cross pattern)
        const secondaryWave = Math.cos(time * waveSpeed * 0.7 + originalPos.x * waveFrequency) * waveAmplitude * 0.6;

        // Tertiary wave (diagonal pattern)
        const tertiaryWave = Math.sin(time * waveSpeed * 1.3 + (originalPos.x + originalPos.z) * waveFrequency * 0.8) * waveAmplitude * 0.4;

        // Combine waves for complex water motion
        const waveY = primaryWave + secondaryWave + tertiaryWave;

        // Subtle horizontal movement for water flow effect
        const flowX = Math.sin(time * 0.5 + originalPos.z * 0.3) * 0.01;
        const flowZ = Math.cos(time * 0.6 + originalPos.x * 0.4) * 0.01;

        // Apply realistic wave motion
        waterVoxel.position.set(
            originalPos.x + flowX,
            originalPos.y + waveY,
            originalPos.z + flowZ
        );

        // Animate material properties for realistic water effect
        if (waterVoxel.material) {
            // Water surface reflection simulation
            const reflectionSpeed = 1.0;
            const baseOpacity = 0.7;
            const opacityVariation = 0.15;

            // Opacity changes based on wave height for reflection effect
            const waveBasedOpacity = baseOpacity + (waveY / waveAmplitude) * opacityVariation;
            waterVoxel.material.opacity = Math.max(0.5, Math.min(0.9, waveBasedOpacity));

            // Enhanced emissive pulsing that follows wave motion
            const pulseSpeed = 1.0;
            const basePulse = 0.2;
            const pulseAmplitude = 0.1;
            const waveSyncedPulse = basePulse + (waveY / waveAmplitude) * pulseAmplitude;
            const timePulse = Math.sin(time * pulseSpeed + index * 0.2) * 0.05;

            waterVoxel.material.emissiveIntensity = Math.max(0.1, waveSyncedPulse + timePulse);

            // Dynamic color shifting for water depth illusion
            const colorShift = Math.sin(time * 0.7 + index * 0.15) * 0.08;
            const depthShift = (waveY / waveAmplitude) * 0.05; // Darker when lower, brighter when higher

            const baseColor = 0x4A90E2; // Base blue color
            const r = ((baseColor >> 16) & 255) / 255;
            const g = ((baseColor >> 8) & 255) / 255;
            const b = (baseColor & 255) / 255;

            // More dynamic color changes for realistic water
            waterVoxel.material.color.setRGB(
                Math.max(0.2, Math.min(0.8, r + depthShift - colorShift * 0.1)),
                Math.max(0.4, Math.min(1.0, g + depthShift + colorShift * 0.05)),
                Math.max(0.6, Math.min(1.0, b + depthShift + colorShift * 0.1))
            );
        }
    });

    // Animate pond light similar to soul orbs
    if (pondLight && pondLight.userData) {
        const lightPulseSpeed = 1.2; // Slightly slower than water waves
        const baseLightIntensity = pondLight.userData.originalIntensity;
        const lightPulseAmplitude = 0.4; // Light intensity variation

        // Gentle pulsing light intensity
        const lightPulse = baseLightIntensity + Math.sin(time * lightPulseSpeed) * lightPulseAmplitude;
        pondLight.intensity = Math.max(0.5, lightPulse); // Never go below 0.5 intensity

        // Very subtle light color shifting (like soul orbs)
        const colorShift = Math.sin(time * 0.6) * 0.05;
        const baseColor = 0x4A90E2; // Base blue color
        const r = ((baseColor >> 16) & 255) / 255;
        const g = ((baseColor >> 8) & 255) / 255;
        const b = (baseColor & 255) / 255;

        // Shift towards cyan/white slightly
        pondLight.color.setRGB(
            Math.min(1, r + colorShift * 0.1),
            Math.min(1, g + colorShift * 0.05),
            Math.min(1, b + colorShift * 0.1)
        );
    }
}


