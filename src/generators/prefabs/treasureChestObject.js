import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';
import { mergeGeometriesEnhanced } from '../../utils/MeshProcessingIntegration.js';

// --- Voxel Data for Treasure Chest ---

// Define chest colors to match dungeon aesthetic
const CHEST_WOOD_COLORS = ['8B4513', '654321', '5D4037', '6D4C41']; // Brown wood tones
const CHEST_METAL_COLORS = ['4A4A4A', '5A5A5A', '696969', '808080']; // Metal hardware
const CHEST_GOLD_COLOR = 'FFD700'; // Gold accents

// Define the treasure chest shape using voxel coordinates
// Structure: Base -> Body -> Lid -> Hardware
const treasureChestShape = [
    // === CHEST BASE (Y: 0-1) ===
    // Bottom layer (Y=0) - 7x5 base
    ...generateRectLayer(0, 3, 2, CHEST_WOOD_COLORS[0]),
    // Base frame (Y=1) - slightly smaller
    ...generateRectLayer(1, 3, 2, CHEST_WOOD_COLORS[1]),

    // === CHEST BODY (Y: 2-4) ===
    // Main body layers
    ...generateHollowRectLayer(2, 3, 2, CHEST_WOOD_COLORS[2]),
    ...generateHollowRectLayer(3, 3, 2, CHEST_WOOD_COLORS[1]),
    ...generateHollowRectLayer(4, 3, 2, CHEST_WOOD_COLORS[2]),

    // === CHEST LID (Y: 5-7) - This will be animated ===
    // Lid layers (these will be in a separate group for animation)
    ...generateRectLayer(5, 3, 2, CHEST_WOOD_COLORS[0]),
    ...generateRectLayer(6, 2, 1, CHEST_WOOD_COLORS[1]),
    ...generateRectLayer(7, 1, 0, CHEST_WOOD_COLORS[2]),

    // === HARDWARE DETAILS ===
    // Front lock (Y=3, center front)
    { x: 0, y: 3, z: -3, c: CHEST_METAL_COLORS[0] },
    { x: 0, y: 4, z: -3, c: CHEST_GOLD_COLOR }, // Gold keyhole

    // Corner reinforcements
    { x: -3, y: 2, z: -2, c: CHEST_METAL_COLORS[1] },
    { x: 3, y: 2, z: -2, c: CHEST_METAL_COLORS[1] },
    { x: -3, y: 2, z: 2, c: CHEST_METAL_COLORS[1] },
    { x: 3, y: 2, z: 2, c: CHEST_METAL_COLORS[1] },

    // Lid hinges (front edge - symmetrical placement)
    { x: -3, y: 5, z: -2, c: CHEST_METAL_COLORS[0] }, // Left edge hinge
    { x: -1, y: 5, z: -2, c: CHEST_METAL_COLORS[0] }, // Left-center hinge
    { x: 1, y: 5, z: -2, c: CHEST_METAL_COLORS[0] },  // Right-center hinge
    { x: 3, y: 5, z: -2, c: CHEST_METAL_COLORS[0] },  // Right edge hinge
];

// Helper function to generate rectangular layer
function generateRectLayer(y, halfWidth, halfDepth, color) {
    const layer = [];
    for (let x = -halfWidth; x <= halfWidth; x++) {
        for (let z = -halfDepth; z <= halfDepth; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate hollow rectangular layer
function generateHollowRectLayer(y, halfWidth, halfDepth, color) {
    const layer = [];
    for (let x = -halfWidth; x <= halfWidth; x++) {
        for (let z = -halfDepth; z <= halfDepth; z++) {
            // Only add voxels on the perimeter
            if (x === -halfWidth || x === halfWidth || z === -halfDepth || z === halfDepth) {
                layer.push({ x, y, z, c: color });
            }
        }
    }
    return layer;
}

// Separate the lid voxels for animation
const chestBodyVoxels = treasureChestShape.filter(voxel => voxel.y < 5);
const chestLidVoxels = treasureChestShape.filter(voxel => voxel.y >= 5);

// --- Main Prefab Function ---
export async function createTreasureChestObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 1337); // Different multiplier for variation

    // Use consistent voxel size
    const chestVoxelSize = VOXEL_SIZE * 5.12; // Same size as other objects
    const baseGeometry = getOrCreateGeometry('treasure_chest_voxel', () =>
        new THREE.BoxGeometry(chestVoxelSize, chestVoxelSize, chestVoxelSize)
    );

    // Create chest body (static part)
    const bodyGroup = await createVoxelGroup(chestBodyVoxels, baseGeometry, chestVoxelSize, rng);
    bodyGroup.name = 'chestBody';

    // Rotate chest body 180 degrees so front becomes back (lid opens away from player)
    bodyGroup.rotation.y = Math.PI;

    group.add(bodyGroup);

    // Create chest lid (animated part)
    const lidGroup = await createVoxelGroup(chestLidVoxels, baseGeometry, chestVoxelSize, rng);
    lidGroup.name = 'chestLid';

    // Keep the grey hinge voxels visible as part of the lid design

    // Set pivot point at the exact hinge location (where lid meets body)
    // Since body is rotated 180°, the back edge (Z=2) is now at the front for the player
    const hingeY = 5 * chestVoxelSize;
    const hingeZ = -2 * chestVoxelSize; // Keep front edge for lid (body is rotated, not lid)

    // Move lid group to position pivot at hinge point
    lidGroup.position.set(0, hingeY, hingeZ);

    // Adjust all lid meshes to be positioned relative to the hinge point
    lidGroup.children.forEach(mesh => {
        // Shift mesh positions so the hinge point becomes the origin
        mesh.position.y -= hingeY;
        mesh.position.z -= hingeZ; // This will shift lid backward from front hinge
    });

    group.add(lidGroup);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'treasure_chest',
        isDestructible: false, // Chests are indestructible
        isInteractable: true, // New property for interaction
        interactionType: 'chest',
        health: 1, // Not used since indestructible, but kept for consistency
        isInteriorObject: true,
        voxelScale: chestVoxelSize,
        
        // Chest-specific properties
        isOpened: false,
        containsItem: true,
        chestId: options.chestId || `chest_${Date.now()}`,
        
        // Animation references
        lidGroup: lidGroup,
        bodyGroup: bodyGroup,
        
        // Loot data (will be set by loot system)
        lootItem: null,
        
        originalVoxels: treasureChestShape.map(v => {
            const voxelRng = mulberry32(seed * 23 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color
        })
    };

    console.log('Treasure chest created with options:', options);
    console.log('Treasure chest userData:', group.userData);

    return group;
}

// Helper function to create voxel group from voxel data
async function createVoxelGroup(voxelData, baseGeometry, voxelSize, rng) {
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel
    voxelData.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to color for weathered effect
        let finalColor = c;
        if (rng() < 0.15) { // 15% chance for weathering
            const colorVariations = c.startsWith('8B') ? CHEST_WOOD_COLORS : 
                                  c.startsWith('4A') ? CHEST_METAL_COLORS : [c];
            const colorIndex = Math.floor(rng() * colorVariations.length);
            finalColor = colorVariations[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * voxelSize,
            y * voxelSize,
            z * voxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material using enhanced worker-based merging
    const group = new THREE.Group();

    // Process all geometry merging operations
    const meshPromises = Object.entries(geometriesByMaterial).map(async ([colorHex, geometries]) => {
        if (geometries.length === 0) return null;

        try {
            const mergedGeometry = await mergeGeometriesEnhanced(geometries);
            const material = _getMaterialByHex_Cached(colorHex);
            const mesh = new THREE.Mesh(mergedGeometry, material);

            mesh.castShadow = true;
            mesh.receiveShadow = true;
            return { mesh, colorHex };
        } catch (error) {
            console.warn(`[TreasureChest] Worker merge failed for ${colorHex}, using direct merge:`, error);
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const material = _getMaterialByHex_Cached(colorHex);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            return { mesh, colorHex };
        }
    });

    // Wait for all mesh processing to complete
    const meshResults = await Promise.all(meshPromises);
    meshResults.filter(Boolean).forEach(result => {
        group.add(result.mesh);
    });

    return group;
}
