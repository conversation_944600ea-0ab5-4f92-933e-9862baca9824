import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Nairabos shadow creature colors - absolutely terrifying
const NAIRABOS_COLORS = {
    SHADOW_DARK: '0A0A0A',      // Very dark shadow
    SHADOW_MEDIUM: '1A1A1A',    // Medium shadow
    SHADOW_LIGHT: '2A2A2A',     // Lighter shadow
    VOID_BLACK: '000000',       // Pure black void
    EVIL_RED: '8B0000',         // Dark red for eyes/accents
    BLOOD_RED: 'FF0000',        // Bright red for glowing eyes
    SHADOW_PURPLE: '301934',    // Dark purple shadow
    NIGHTMARE_GRAY: '333333',   // Nightmare gray
    BONE_WHITE: 'F5F5DC',       // Bone/skull color
    DECAY_BROWN: '4A2C2A',      // Decaying flesh
    SCAR_PINK: '8B4B6B',        // Scar tissue
    DEMON_ORANGE: 'FF4500',     // Hellfire orange
    CORRUPTION_GREEN: '2F4F2F', // Corrupted flesh
    DEATH_BLUE: '191970',       // Death/cold blue
    HELL_YELLOW: 'FFD700',      // Sulfur/hell yellow
    DRIED_BLOOD: '722F37'       // Old dried blood
};

/**
 * Creates a horrifying shadow creature "Nairabos" - twice the size of a zombie
 * with long legs, long arms, and terrifying shadow appearance.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The Nairabos enemy model group.
 */
export function createNairabosEnemyModel(scale = 1.0) {
    console.log(`🔥 NAIRABOS MODEL CREATION - Scale parameter received: ${scale}`);

    // CRITICAL: Validate input parameters to prevent NaN propagation
    const safeScale = (isFinite(scale) && !isNaN(scale) && scale > 0) ? scale : 1.0;
    const safeVoxelSize = (isFinite(VOXEL_SIZE) && !isNaN(VOXEL_SIZE) && VOXEL_SIZE > 0) ? VOXEL_SIZE : 0.05;
    
    if (safeScale !== scale) {
        console.warn(`[NairabosEnemy] Invalid scale ${scale} corrected to ${safeScale}`);
    }

    const finalGroup = new THREE.Group();
    finalGroup.name = "Nairabos";

    // Use larger voxel size for this epic creature with validation
    const nairabosVoxelSize = safeVoxelSize * 1.2; // 20% larger than standard

    // Create animation-ready groups for body parts
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const headGroup = new THREE.Group();
    headGroup.name = "head";

    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";

    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";

    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";

    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";

    // CRITICAL FIX: Create visual offset group to elevate the model appearance
    // This keeps collision box at ground level but moves visual model higher
    const visualGroup = new THREE.Group();
    visualGroup.name = "visualModel";

    // Add all visual components to the visual group
    visualGroup.add(bodyGroup);
    visualGroup.add(headGroup);
    visualGroup.add(leftArmGroup);
    visualGroup.add(rightArmGroup);
    visualGroup.add(leftLegGroup);
    visualGroup.add(rightLegGroup);

    // Move the entire visual model up moderately for proper ground clearance
    const visualElevation = 1.0; // Move visual model 1 unit higher for proper appearance
    visualGroup.position.y = visualElevation;
    console.log(`🔥 NAIRABOS VISUAL - Elevating visual model by ${visualElevation} units for proper ground clearance`);

    // Add the elevated visual group to the final group
    finalGroup.add(visualGroup);

    // Position body parts - make it tall and menacing
    headGroup.position.set(0, 8 * nairabosVoxelSize, 0); // High head
    leftArmGroup.position.set(-4 * nairabosVoxelSize, 5 * nairabosVoxelSize, 0); // Long arms
    rightArmGroup.position.set(4 * nairabosVoxelSize, 5 * nairabosVoxelSize, 0);
    leftLegGroup.position.set(-2 * nairabosVoxelSize, -2 * nairabosVoxelSize, 0); // Long legs
    rightLegGroup.position.set(2 * nairabosVoxelSize, -2 * nairabosVoxelSize, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        head: {},
        leftArm: {},
        rightArm: {},
        leftLeg: {},
        rightLeg: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('nairabos_voxel', () => 
        new THREE.BoxGeometry(nairabosVoxelSize, nairabosVoxelSize, nairabosVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();

    // Create seeded random for consistent appearance
    const random = mulberry32(66666); // Evil seed number

    // Helper to add voxels to specific groups
    const addVoxel = (groupName, x, y, z, colorHex) => {
        // Validate all input parameters to prevent NaN propagation
        const safeX = isFinite(x) && !isNaN(x) ? x : 0;
        const safeY = isFinite(y) && !isNaN(y) ? y : 0;
        const safeZ = isFinite(z) && !isNaN(z) ? z : 0;
        
        if (safeX !== x || safeY !== y || safeZ !== z) {
            console.warn(`[NairabosEnemy] Invalid voxel coordinates: (${x}, ${y}, ${z}) corrected to (${safeX}, ${safeY}, ${safeZ})`);
        }

        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        // Calculate translation with validation
        const translateX = safeX * nairabosVoxelSize;
        const translateY = safeY * nairabosVoxelSize;
        const translateZ = safeZ * nairabosVoxelSize;
        
        // Additional validation for translation values
        if (!isFinite(translateX) || !isFinite(translateY) || !isFinite(translateZ)) {
            console.error(`[NairabosEnemy] Invalid translation values: (${translateX}, ${translateY}, ${translateZ}), skipping voxel`);
            return;
        }

        tempMatrix.makeTranslation(translateX, translateY, translateZ);

        const clonedGeo = voxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        
        // Validate geometry after matrix application
        if (clonedGeo.attributes.position) {
            const positions = clonedGeo.attributes.position.array;
            let hasNaN = false;
            for (let i = 0; i < positions.length; i++) {
                if (!isFinite(positions[i]) || isNaN(positions[i])) {
                    console.error(`[NairabosEnemy] NaN detected in voxel geometry at position ${i}, value: ${positions[i]}`);
                    positions[i] = 0; // Reset to safe value
                    hasNaN = true;
                }
            }
            if (hasNaN) {
                clonedGeo.attributes.position.needsUpdate = true;
                clonedGeo.computeBoundingSphere();
            }
        }
        
        geometriesByMaterial[groupName][colorHex].push(clonedGeo);

        // Store original voxel data for destruction effects
        originalVoxels.push({
            x: x,
            y: y,
            z: z,
            c: colorHex
        });
    };

    // Helper to merge geometries for a group with validation
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                // Validate all geometries before merging
                const validGeometries = geometriesByMaterial[groupName][colorHex].filter(geo => {
                    if (!geo || !geo.attributes.position) return false;
                    
                    const positions = geo.attributes.position.array;
                    for (let i = 0; i < positions.length; i++) {
                        if (!isFinite(positions[i]) || isNaN(positions[i])) {
                            console.warn(`[NairabosEnemy] Filtering out geometry with NaN at position ${i}`);
                            return false;
                        }
                    }
                    return true;
                });
                
                if (validGeometries.length === 0) {
                    console.warn(`[NairabosEnemy] No valid geometries found for ${groupName} color ${colorHex}`);
                    continue;
                }
                
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    validGeometries,
                    false
                );
                
                // Final validation of merged geometry
                if (mergedGeometry && mergedGeometry.attributes.position) {
                    const positions = mergedGeometry.attributes.position.array;
                    let hasNaN = false;
                    for (let i = 0; i < positions.length; i++) {
                        if (!isFinite(positions[i]) || isNaN(positions[i])) {
                            console.error(`[NairabosEnemy] NaN in merged geometry at position ${i}, fixing`);
                            positions[i] = 0;
                            hasNaN = true;
                        }
                    }
                    if (hasNaN) {
                        mergedGeometry.attributes.position.needsUpdate = true;
                        mergedGeometry.computeBoundingSphere();
                        mergedGeometry.computeBoundingBox();
                    }
                }

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(colorHex);
                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    targetGroup.add(mesh);
                }
            }
        }
    };

    // Helper to add smaller face voxels for detailed features with validation
    const addFaceVoxel = (x, y, z, colorHex, size = 0.3) => {
        // Validate all input parameters
        const safeX = isFinite(x) && !isNaN(x) ? x : 0;
        const safeY = isFinite(y) && !isNaN(y) ? y : 0;
        const safeZ = isFinite(z) && !isNaN(z) ? z : 0;
        const safeSize = isFinite(size) && !isNaN(size) && size > 0 ? size : 0.3;
        
        if (safeX !== x || safeY !== y || safeZ !== z || safeSize !== size) {
            console.warn(`[NairabosEnemy] Invalid face voxel params: (${x}, ${y}, ${z}, ${size}) corrected to (${safeX}, ${safeY}, ${safeZ}, ${safeSize})`);
        }
        
        const faceVoxelSize = nairabosVoxelSize * safeSize;
        
        // Validate face voxel size
        if (!isFinite(faceVoxelSize) || isNaN(faceVoxelSize) || faceVoxelSize <= 0) {
            console.error(`[NairabosEnemy] Invalid face voxel size: ${faceVoxelSize}, skipping`);
            return;
        }
        
        const faceVoxelGeo = new THREE.BoxGeometry(faceVoxelSize, faceVoxelSize, faceVoxelSize);

        // Calculate translation with validation
        const translateX = safeX * nairabosVoxelSize * 0.5;
        const translateY = safeY * nairabosVoxelSize * 0.5;
        const translateZ = safeZ * nairabosVoxelSize * 0.5;
        
        if (!isFinite(translateX) || !isFinite(translateY) || !isFinite(translateZ)) {
            console.error(`[NairabosEnemy] Invalid face voxel translation: (${translateX}, ${translateY}, ${translateZ}), skipping`);
            return;
        }

        tempMatrix.makeTranslation(translateX, translateY, translateZ);

        const clonedGeo = faceVoxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        
        // Validate geometry after matrix application
        if (clonedGeo.attributes.position) {
            const positions = clonedGeo.attributes.position.array;
            let hasNaN = false;
            for (let i = 0; i < positions.length; i++) {
                if (!isFinite(positions[i]) || isNaN(positions[i])) {
                    console.error(`[NairabosEnemy] NaN in face voxel geometry at position ${i}, fixing`);
                    positions[i] = 0;
                    hasNaN = true;
                }
            }
            if (hasNaN) {
                clonedGeo.attributes.position.needsUpdate = true;
                clonedGeo.computeBoundingSphere();
            }
        }

        if (!geometriesByMaterial['head'][colorHex]) {
            geometriesByMaterial['head'][colorHex] = [];
        }
        geometriesByMaterial['head'][colorHex].push(clonedGeo);

        faceVoxelGeo.dispose();
    };

    // Create basic skull shape first
    for (let x = -2; x <= 2; x++) {
        for (let y = -1; y <= 3; y++) { // Tall head
            for (let z = -2; z <= 2; z++) {
                // Create skull-like shape with NaN protection
                const xComponent = (x/2)**2;
                const yComponent = (y/3)**2;
                const zComponent = (z/2)**2;
                
                // Validate components before sqrt
                if (!isFinite(xComponent) || !isFinite(yComponent) || !isFinite(zComponent)) {
                    console.warn(`[NairabosEnemy] Invalid distance components: x=${xComponent}, y=${yComponent}, z=${zComponent}`);
                    continue;
                }
                
                const distance = Math.sqrt(xComponent + yComponent + zComponent);
                
                // Validate distance result
                if (!isFinite(distance) || isNaN(distance)) {
                    console.warn(`[NairabosEnemy] Invalid distance calculation: ${distance} for (${x}, ${y}, ${z})`);
                    continue;
                }
                
                if (distance <= 1.0) {
                    let color;
                    if (random() < 0.3) {
                        color = NAIRABOS_COLORS.VOID_BLACK;
                    } else if (random() < 0.6) {
                        color = NAIRABOS_COLORS.SHADOW_DARK;
                    } else {
                        color = NAIRABOS_COLORS.SHADOW_MEDIUM;
                    }
                    addVoxel('head', x, y, z, color);
                }
            }
        }
    }

    // Create absolutely terrifying demonic face with extreme detail

    // TWISTED DEMON HORNS - Protruding from forehead
    // Left horn
    for (let i = 0; i < 6; i++) {
        addFaceVoxel(-3 - i*0.3, 6 + i*0.8, 1 - i*0.2, NAIRABOS_COLORS.BONE_WHITE, 0.4 - i*0.05);
        addFaceVoxel(-3 - i*0.3, 6 + i*0.8, 0 - i*0.2, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }
    // Right horn
    for (let i = 0; i < 6; i++) {
        addFaceVoxel(3 + i*0.3, 6 + i*0.8, 1 - i*0.2, NAIRABOS_COLORS.BONE_WHITE, 0.4 - i*0.05);
        addFaceVoxel(3 + i*0.3, 6 + i*0.8, 0 - i*0.2, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }

    // MULTIPLE DEMONIC EYES - Absolutely horrifying
    // Main eyes (larger, primary)
    for (let x = -3; x <= -1; x++) { // Left main eye socket
        for (let y = 2; y <= 4; y++) {
            for (let z = 3; z <= 5; z++) {
                addFaceVoxel(x, y, z, NAIRABOS_COLORS.VOID_BLACK, 0.5);
            }
        }
    }
    for (let x = 1; x <= 3; x++) { // Right main eye socket
        for (let y = 2; y <= 4; y++) {
            for (let z = 3; z <= 5; z++) {
                addFaceVoxel(x, y, z, NAIRABOS_COLORS.VOID_BLACK, 0.5);
            }
        }
    }

    // Main glowing eyes with multiple layers
    addFaceVoxel(-2, 3, 4, NAIRABOS_COLORS.BLOOD_RED, 0.8);
    addFaceVoxel(-2, 3, 5, NAIRABOS_COLORS.DEMON_ORANGE, 0.6);
    addFaceVoxel(-2, 3, 6, NAIRABOS_COLORS.HELL_YELLOW, 0.4);
    addFaceVoxel(-2, 2, 4, NAIRABOS_COLORS.EVIL_RED, 0.3);
    addFaceVoxel(-2, 4, 4, NAIRABOS_COLORS.EVIL_RED, 0.3);

    addFaceVoxel(2, 3, 4, NAIRABOS_COLORS.BLOOD_RED, 0.8);
    addFaceVoxel(2, 3, 5, NAIRABOS_COLORS.DEMON_ORANGE, 0.6);
    addFaceVoxel(2, 3, 6, NAIRABOS_COLORS.HELL_YELLOW, 0.4);
    addFaceVoxel(2, 2, 4, NAIRABOS_COLORS.EVIL_RED, 0.3);
    addFaceVoxel(2, 4, 4, NAIRABOS_COLORS.EVIL_RED, 0.3);

    // THIRD EYE - Forehead demon eye
    addFaceVoxel(0, 5, 3, NAIRABOS_COLORS.VOID_BLACK, 0.6);
    addFaceVoxel(0, 5, 4, NAIRABOS_COLORS.BLOOD_RED, 0.5);
    addFaceVoxel(0, 5, 5, NAIRABOS_COLORS.DEMON_ORANGE, 0.3);

    // SMALLER DEMON EYES - Multiple additional eyes
    addFaceVoxel(-4, 1, 3, NAIRABOS_COLORS.EVIL_RED, 0.3); // Left temple eye
    addFaceVoxel(4, 1, 3, NAIRABOS_COLORS.EVIL_RED, 0.3);  // Right temple eye
    addFaceVoxel(-1, 0, 4, NAIRABOS_COLORS.DEATH_BLUE, 0.2); // Left cheek eye
    addFaceVoxel(1, 0, 4, NAIRABOS_COLORS.DEATH_BLUE, 0.2);  // Right cheek eye

    // TWISTED NASAL CAVITY - Horrifying void with bone fragments
    for (let y = 1; y <= 2; y++) {
        for (let z = 3; z <= 4; z++) {
            addFaceVoxel(0, y, z, NAIRABOS_COLORS.VOID_BLACK, 0.6);
        }
    }
    // Broken nose bone fragments
    addFaceVoxel(-0.5, 1.5, 3, NAIRABOS_COLORS.BONE_WHITE, 0.2);
    addFaceVoxel(0.5, 1.5, 3, NAIRABOS_COLORS.BONE_WHITE, 0.2);
    addFaceVoxel(0, 2, 2.5, NAIRABOS_COLORS.DRIED_BLOOD, 0.15);

    // NIGHTMARISH MOUTH - Gaping maw of terror
    // Massive mouth opening
    for (let x = -3; x <= 3; x++) {
        for (let y = -2; y <= 0; y++) {
            addFaceVoxel(x, y, 4, NAIRABOS_COLORS.VOID_BLACK, 0.6);
            addFaceVoxel(x, y, 3, NAIRABOS_COLORS.DRIED_BLOOD, 0.3);
        }
    }

    // Multiple rows of jagged demon teeth
    // Front row - massive fangs
    addFaceVoxel(-3, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.5); // Left main fang
    addFaceVoxel(-2, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.4);
    addFaceVoxel(-1, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.3);
    addFaceVoxel(0, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.2);
    addFaceVoxel(1, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.3);
    addFaceVoxel(2, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.4);
    addFaceVoxel(3, 0, 5, NAIRABOS_COLORS.BONE_WHITE, 0.5); // Right main fang

    // Longer demon fangs extending down
    addFaceVoxel(-3, -1, 5, NAIRABOS_COLORS.BONE_WHITE, 0.6);
    addFaceVoxel(-3, -2, 5, NAIRABOS_COLORS.BONE_WHITE, 0.5);
    addFaceVoxel(3, -1, 5, NAIRABOS_COLORS.BONE_WHITE, 0.6);
    addFaceVoxel(3, -2, 5, NAIRABOS_COLORS.BONE_WHITE, 0.5);

    // Blood on teeth
    addFaceVoxel(-3, -1, 5.5, NAIRABOS_COLORS.BLOOD_RED, 0.2);
    addFaceVoxel(3, -1, 5.5, NAIRABOS_COLORS.BLOOD_RED, 0.2);

    // Back row teeth (smaller, more numerous)
    for (let x = -2; x <= 2; x++) {
        addFaceVoxel(x, -1, 3, NAIRABOS_COLORS.NIGHTMARE_GRAY, 0.25);
        if (x % 2 === 0) {
            addFaceVoxel(x, -1, 2.5, NAIRABOS_COLORS.DRIED_BLOOD, 0.15);
        }
    }

    // HORRIFYING SCARS AND WOUNDS
    // Diagonal scar across left side of face
    for (let i = 0; i < 8; i++) {
        addFaceVoxel(-4 + i*0.5, 4 - i*0.7, 2 + i*0.2, NAIRABOS_COLORS.SCAR_PINK, 0.2);
        addFaceVoxel(-4 + i*0.5, 4 - i*0.7, 1.5 + i*0.2, NAIRABOS_COLORS.DRIED_BLOOD, 0.15);
    }

    // Vertical scar on right cheek
    for (let y = 0; y <= 3; y++) {
        addFaceVoxel(3.5, y, 2, NAIRABOS_COLORS.SCAR_PINK, 0.2);
        addFaceVoxel(3.5, y, 1.8, NAIRABOS_COLORS.CORRUPTION_GREEN, 0.15);
    }

    // Forehead wound
    for (let x = -1; x <= 1; x++) {
        addFaceVoxel(x, 4.5, 2.5, NAIRABOS_COLORS.DECAY_BROWN, 0.3);
        addFaceVoxel(x, 4.5, 2, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }

    // RAZOR-SHARP CHEEKBONES - Cutting through reality
    for (let x = -5; x <= -3; x++) { // Left cheekbone
        addFaceVoxel(x, 1, 2, NAIRABOS_COLORS.BONE_WHITE, 0.4);
        addFaceVoxel(x, 2, 2, NAIRABOS_COLORS.SHADOW_LIGHT, 0.3);
        addFaceVoxel(x, 1, 1, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }
    for (let x = 3; x <= 5; x++) { // Right cheekbone
        addFaceVoxel(x, 1, 2, NAIRABOS_COLORS.BONE_WHITE, 0.4);
        addFaceVoxel(x, 2, 2, NAIRABOS_COLORS.SHADOW_LIGHT, 0.3);
        addFaceVoxel(x, 1, 1, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }

    // MASSIVE DEMONIC FOREHEAD RIDGES - Protruding bone
    for (let x = -4; x <= 4; x++) {
        addFaceVoxel(x, 5, 2, NAIRABOS_COLORS.BONE_WHITE, 0.5);
        addFaceVoxel(x, 6, 1, NAIRABOS_COLORS.SHADOW_DARK, 0.4);
        if (Math.abs(x) <= 2) {
            addFaceVoxel(x, 6, 2, NAIRABOS_COLORS.NIGHTMARE_GRAY, 0.3);
            addFaceVoxel(x, 7, 1, NAIRABOS_COLORS.CORRUPTION_GREEN, 0.2);
        }
    }

    // DEEP TEMPLE VOIDS - Portals to nightmare
    addFaceVoxel(-5, 3, 1, NAIRABOS_COLORS.VOID_BLACK, 0.6);
    addFaceVoxel(-5, 3, 0, NAIRABOS_COLORS.SHADOW_PURPLE, 0.4);
    addFaceVoxel(-5, 4, 1, NAIRABOS_COLORS.DEATH_BLUE, 0.3);
    addFaceVoxel(5, 3, 1, NAIRABOS_COLORS.VOID_BLACK, 0.6);
    addFaceVoxel(5, 3, 0, NAIRABOS_COLORS.SHADOW_PURPLE, 0.4);
    addFaceVoxel(5, 4, 1, NAIRABOS_COLORS.DEATH_BLUE, 0.3);

    // MASSIVE DEMONIC JAW - Bone and decay
    for (let x = -4; x <= 4; x++) {
        addFaceVoxel(x, -2, 2, NAIRABOS_COLORS.BONE_WHITE, 0.4);
        addFaceVoxel(x, -3, 1, NAIRABOS_COLORS.DECAY_BROWN, 0.3);
        addFaceVoxel(x, -2, 1, NAIRABOS_COLORS.DRIED_BLOOD, 0.2);
    }

    // JAW SPIKES - Bone protrusions
    addFaceVoxel(-4, -3, 2, NAIRABOS_COLORS.BONE_WHITE, 0.5);
    addFaceVoxel(-4, -4, 2, NAIRABOS_COLORS.BONE_WHITE, 0.4);
    addFaceVoxel(4, -3, 2, NAIRABOS_COLORS.BONE_WHITE, 0.5);
    addFaceVoxel(4, -4, 2, NAIRABOS_COLORS.BONE_WHITE, 0.4);

    // NIGHTMARE VEINS - Pulsing corruption with NaN protection
    for (let i = 0; i < 12; i++) {
        const angle = (i / 12) * Math.PI * 2;
        
        // Validate angle before trigonometric operations
        if (!isFinite(angle) || isNaN(angle)) {
            console.warn(`[NairabosEnemy] Invalid angle: ${angle} for iteration ${i}`);
            continue;
        }
        
        const cosValue = Math.cos(angle);
        const sinValue = Math.sin(angle);
        
        // Validate trigonometric results
        if (!isFinite(cosValue) || isNaN(cosValue) || !isFinite(sinValue) || isNaN(sinValue)) {
            console.warn(`[NairabosEnemy] Invalid trig values: cos=${cosValue}, sin=${sinValue} for angle=${angle}`);
            continue;
        }
        
        const x = cosValue * 3;
        const y = sinValue * 2 + 2;
        
        // Final validation of coordinates
        if (!isFinite(x) || isNaN(x) || !isFinite(y) || isNaN(y)) {
            console.warn(`[NairabosEnemy] Invalid vein coordinates: x=${x}, y=${y}`);
            continue;
        }
        
        addFaceVoxel(x, y, 1, NAIRABOS_COLORS.CORRUPTION_GREEN, 0.15);
        addFaceVoxel(x, y, 0.5, NAIRABOS_COLORS.DEATH_BLUE, 0.1);
    }

    // FLESH TEARS - Ripped skin revealing void
    addFaceVoxel(-2.5, 1.5, 3, NAIRABOS_COLORS.SCAR_PINK, 0.3);
    addFaceVoxel(-2.5, 1.5, 2.5, NAIRABOS_COLORS.VOID_BLACK, 0.4);
    addFaceVoxel(2.5, 1.5, 3, NAIRABOS_COLORS.SCAR_PINK, 0.3);
    addFaceVoxel(2.5, 1.5, 2.5, NAIRABOS_COLORS.VOID_BLACK, 0.4);

    // DRIPPING BLOOD EFFECTS
    for (let x = -3; x <= 3; x += 2) {
        for (let drop = 0; drop < 4; drop++) {
            addFaceVoxel(x, -1 - drop*0.5, 4 + drop*0.1, NAIRABOS_COLORS.BLOOD_RED, 0.2 - drop*0.03);
        }
    }

    // Create horrifying shadow body - tall, twisted, and scarred
    for (let x = -2; x <= 2; x++) {
        for (let y = 0; y <= 6; y++) { // Tall torso
            for (let z = -1; z <= 1; z++) {
                // Skip some voxels for a more ethereal shadow look
                if (random() < 0.8) { // 80% chance to place voxel
                    let color;
                    if (random() < 0.3) {
                        color = NAIRABOS_COLORS.SHADOW_DARK;
                    } else if (random() < 0.5) {
                        color = NAIRABOS_COLORS.SHADOW_MEDIUM;
                    } else if (random() < 0.7) {
                        color = NAIRABOS_COLORS.SHADOW_PURPLE;
                    } else if (random() < 0.85) {
                        color = NAIRABOS_COLORS.DECAY_BROWN;
                    } else {
                        color = NAIRABOS_COLORS.CORRUPTION_GREEN;
                    }
                    addVoxel('body', x, y, z, color);
                }
            }
        }
    }

    // Add horrifying body details
    // Spine spikes protruding from back
    for (let y = 1; y <= 5; y++) {
        addVoxel('body', 0, y, -2, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel('body', 0, y, -3, NAIRABOS_COLORS.BONE_WHITE);
        if (y % 2 === 0) {
            addVoxel('body', 0, y, -4, NAIRABOS_COLORS.DRIED_BLOOD);
        }
    }

    // Rib cage showing through flesh
    for (let x = -2; x <= 2; x += 2) {
        for (let y = 2; y <= 4; y++) {
            addVoxel('body', x, y, 0, NAIRABOS_COLORS.BONE_WHITE);
            addVoxel('body', x, y, 1, NAIRABOS_COLORS.SCAR_PINK);
        }
    }

    // Gaping chest wound
    addVoxel('body', 0, 3, 1, NAIRABOS_COLORS.VOID_BLACK);
    addVoxel('body', 0, 3, 2, NAIRABOS_COLORS.BLOOD_RED);
    addVoxel('body', -1, 3, 1, NAIRABOS_COLORS.DRIED_BLOOD);
    addVoxel('body', 1, 3, 1, NAIRABOS_COLORS.DRIED_BLOOD);

    // Create absolutely terrifying arms with nightmare details
    for (let armSide of ['leftArm', 'rightArm']) {
        // Upper arm with bone spikes
        for (let x = -1; x <= 1; x++) {
            for (let y = -2; y <= 2; y++) {
                for (let z = -1; z <= 1; z++) {
                    if (random() < 0.9) {
                        let color;
                        if (random() < 0.6) {
                            color = NAIRABOS_COLORS.SHADOW_DARK;
                        } else if (random() < 0.8) {
                            color = NAIRABOS_COLORS.DECAY_BROWN;
                        } else {
                            color = NAIRABOS_COLORS.CORRUPTION_GREEN;
                        }
                        addVoxel(armSide, x, y, z, color);
                    }
                }
            }
        }

        // Bone spikes on shoulders
        addVoxel(armSide, 0, 2, -2, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(armSide, 0, 2, -3, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(armSide, 1, 1, -2, NAIRABOS_COLORS.DRIED_BLOOD);
        addVoxel(armSide, -1, 1, -2, NAIRABOS_COLORS.DRIED_BLOOD);

        // Long forearm extending down with exposed bone
        for (let y = -8; y <= -3; y++) { // Very long arms
            for (let x = -1; x <= 1; x++) {
                if (random() < 0.85) {
                    let color;
                    if (Math.abs(y) > 6 && x === 0) {
                        color = NAIRABOS_COLORS.BONE_WHITE; // Exposed bone
                    } else if (random() < 0.7) {
                        color = NAIRABOS_COLORS.SHADOW_MEDIUM;
                    } else {
                        color = NAIRABOS_COLORS.SCAR_PINK;
                    }
                    addVoxel(armSide, x, y, 0, color);
                }
            }
        }

        // Massive razor claws - multiple extending claws
        const clawPositions = [
            [-3, -10, 0], [-2, -11, 1], [-1, -10, -1],
            [0, -12, 0], [1, -10, 1], [2, -11, -1], [3, -10, 0]
        ];

        for (let [x, y, z] of clawPositions) {
            addVoxel(armSide, x, y, z, NAIRABOS_COLORS.BONE_WHITE);
            addVoxel(armSide, x, y-1, z, NAIRABOS_COLORS.BONE_WHITE);
            addVoxel(armSide, x, y-1, z+1, NAIRABOS_COLORS.DRIED_BLOOD); // Fixed: use integer coords
        }

        // Hand palm with void center
        addVoxel(armSide, 0, -9, 0, NAIRABOS_COLORS.VOID_BLACK);
        addVoxel(armSide, 0, -9, 1, NAIRABOS_COLORS.SHADOW_PURPLE);
    }

    // Create nightmarish legs with horrifying details
    for (let legSide of ['leftLeg', 'rightLeg']) {
        // Upper leg with muscle definition and decay
        for (let x = -1; x <= 1; x++) {
            for (let y = -2; y <= 2; y++) {
                for (let z = -1; z <= 1; z++) {
                    if (random() < 0.9) {
                        let color;
                        if (random() < 0.5) {
                            color = NAIRABOS_COLORS.SHADOW_DARK;
                        } else if (random() < 0.7) {
                            color = NAIRABOS_COLORS.DECAY_BROWN;
                        } else if (random() < 0.85) {
                            color = NAIRABOS_COLORS.CORRUPTION_GREEN;
                        } else {
                            color = NAIRABOS_COLORS.SCAR_PINK;
                        }
                        addVoxel(legSide, x, y, z, color);
                    }
                }
            }
        }

        // Knee spikes
        addVoxel(legSide, 0, -2, -2, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(legSide, 0, -3, -2, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(legSide, 1, -2, -1, NAIRABOS_COLORS.DRIED_BLOOD);
        addVoxel(legSide, -1, -2, -1, NAIRABOS_COLORS.DRIED_BLOOD);

        // Long lower leg with exposed bone and sinew
        for (let y = -12; y <= -3; y++) { // Even longer legs
            for (let x = -1; x <= 1; x++) {
                if (random() < 0.85) {
                    let color;
                    if (Math.abs(y) > 8 && x === 0) {
                        color = NAIRABOS_COLORS.BONE_WHITE; // Exposed shin bone
                    } else if (random() < 0.6) {
                        color = NAIRABOS_COLORS.SHADOW_MEDIUM;
                    } else if (random() < 0.8) {
                        color = NAIRABOS_COLORS.SCAR_PINK;
                    } else {
                        color = NAIRABOS_COLORS.CORRUPTION_GREEN;
                    }
                    addVoxel(legSide, x, y, 0, color);
                }
            }
        }

        // Sinew and tendons visible
        for (let y = -10; y <= -5; y += 2) {
            addVoxel(legSide, 1, y, 0, NAIRABOS_COLORS.SCAR_PINK);
            addVoxel(legSide, -1, y, 0, NAIRABOS_COLORS.SCAR_PINK);
        }

        // Horrifying clawed feet with multiple talons
        const footClaws = [
            [-2, -13, -3], [-1, -13, -4], [0, -13, -4],
            [1, -13, -4], [2, -13, -3], [0, -13, 2]
        ];

        for (let [x, y, z] of footClaws) {
            addVoxel(legSide, x, y, z, NAIRABOS_COLORS.BONE_WHITE);
            addVoxel(legSide, x, y-1, z, NAIRABOS_COLORS.BONE_WHITE);
            addVoxel(legSide, x, y-1, z+1, NAIRABOS_COLORS.DRIED_BLOOD); // Fixed: use integer coords
        }

        // Foot pad with void center
        for (let x = -1; x <= 1; x++) {
            for (let z = -2; z <= 1; z++) {
                if (x === 0 && z === 0) {
                    addVoxel(legSide, x, -12, z, NAIRABOS_COLORS.VOID_BLACK);
                } else {
                    addVoxel(legSide, x, -12, z, NAIRABOS_COLORS.SHADOW_DARK);
                }
            }
        }

        // Ankle spikes
        addVoxel(legSide, 0, -11, -2, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(legSide, 1, -11, -1, NAIRABOS_COLORS.BONE_WHITE);
        addVoxel(legSide, -1, -11, -1, NAIRABOS_COLORS.BONE_WHITE);
    }

    // Merge geometries for each group
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('head', headGroup);
    mergeGroupGeometries('leftArm', leftArmGroup);
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);

    // Dispose the template geometry
    voxelGeo.dispose();

    // CRITICAL FIX: Don't apply scale here - DungeonHandler handles all scaling
    // Scaling is applied by the spawn system to avoid double-scaling issues
    console.log(`🔥 NAIRABOS MODEL - Scale NOT applied in model (${scale} will be applied by DungeonHandler)`);

    // Add animation data to userData - similar to fish but more menacing
    finalGroup.userData.animationData = {
        // Shadow floating/hovering animation
        shadowFloatSpeed: 2.0,
        shadowFloatAmplitude: 0.3,
        // Limb movement for terrifying effect
        limbSwaySpeed: 1.5,
        limbSwayAmplitude: Math.PI / 12,
        // Attack animation
        attackAnimationDuration: 0.8,
        // Jumping/teleporting like fish but more dramatic
        jumpHeight: 5.0, // Higher than fish
        jumpDuration: 1.2,
        jumpChance: 0.9, // Very high chance to jump
        // Shadow effects
        shadowPulseSpeed: 3.0,
        shadowPulseAmplitude: 0.2
    };

    // Add type information
    finalGroup.userData.type = 'nairabos';

    // Add attack hitbox data - larger and more dangerous than fish
    finalGroup.userData.attackHitbox = {
        radius: 4.0 * scale, // Much larger attack radius
        damage: 1, // Same damage as other enemies
        knockback: 6.0 // Strong knockback for fear effect
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'nairabos';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = nairabosVoxelSize;

    // Add special shadow creature properties
    finalGroup.userData.isHorrifying = true;
    finalGroup.userData.shadowCreature = true;
    finalGroup.userData.canPhaseThrough = false; // Keep collision for gameplay

    // CRITICAL FIX: Don't apply scale here - DungeonHandler will handle scaling
    // The scale parameter is received but scaling is handled by the spawn system
    console.log(`🔥 NAIRABOS MODEL - Scale parameter received: ${scale} (will be applied by DungeonHandler)`);

    // CRITICAL FIX: Use visual elevation approach for towering appearance
    // Legs extend down to Y=-15 voxels (leg position -2 + foot claws at -13 = -15)
    // Keep collision box at ground level but visual model is elevated within the structure
    const groundOffset = 15 * nairabosVoxelSize; // Just bring collision box feet to ground level
    
    // Validate ground offset before applying
    if (isFinite(groundOffset) && !isNaN(groundOffset)) {
        finalGroup.position.y = groundOffset;
        console.log(`🔥 NAIRABOS MODEL - Ground offset applied: ${groundOffset} (collision box at ground level, visual model elevated by ${visualElevation} units)`);
    } else {
        console.error(`[NairabosEnemy] Invalid ground offset: ${groundOffset}, using default`);
        finalGroup.position.y = 0.75; // Safe fallback
    }

    // FINAL GEOMETRY VALIDATION - Last line of defense
    console.log(`[NairabosEnemy] Performing final geometry validation...`);
    let totalNaNFixed = 0;
    finalGroup.traverse(child => {
        if (child.isMesh && child.geometry && child.geometry.attributes.position) {
            const positions = child.geometry.attributes.position.array;
            let nanCount = 0;
            for (let i = 0; i < positions.length; i++) {
                if (!isFinite(positions[i]) || isNaN(positions[i])) {
                    positions[i] = 0;
                    nanCount++;
                    totalNaNFixed++;
                }
            }
            if (nanCount > 0) {
                child.geometry.attributes.position.needsUpdate = true;
                child.geometry.computeBoundingSphere();
                child.geometry.computeBoundingBox();
                console.warn(`[NairabosEnemy] Fixed ${nanCount} NaN values in mesh: ${child.name || 'unnamed'}`);
            }
        }
        
        // Validate transform values
        if (child.position) {
            ['x', 'y', 'z'].forEach(axis => {
                if (!isFinite(child.position[axis]) || isNaN(child.position[axis])) {
                    console.warn(`[NairabosEnemy] Fixing NaN position.${axis} in ${child.name || 'unnamed'}`);
                    child.position[axis] = 0;
                }
            });
        }
        if (child.scale) {
            ['x', 'y', 'z'].forEach(axis => {
                if (!isFinite(child.scale[axis]) || isNaN(child.scale[axis]) || child.scale[axis] <= 0) {
                    console.warn(`[NairabosEnemy] Fixing invalid scale.${axis} in ${child.name || 'unnamed'}`);
                    child.scale[axis] = 1.0;
                }
            });
        }
        if (child.rotation) {
            ['x', 'y', 'z'].forEach(axis => {
                if (!isFinite(child.rotation[axis]) || isNaN(child.rotation[axis])) {
                    console.warn(`[NairabosEnemy] Fixing NaN rotation.${axis} in ${child.name || 'unnamed'}`);
                    child.rotation[axis] = 0;
                }
            });
        }
    });
    
    // CRITICAL FIX: Force proper group hierarchy and naming for animation handler
    console.log(`[NairabosEnemy] Validating body part hierarchy for animations...`);
    const requiredParts = ['body', 'head', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];
    const missingParts = [];
    
    requiredParts.forEach(partName => {
        const part = visualGroup ? visualGroup.getObjectByName(partName) : finalGroup.getObjectByName(partName);
        if (!part) {
            missingParts.push(partName);
            console.error(`[NairabosEnemy] CRITICAL: Missing body part '${partName}' for animation`);
        } else {
            console.log(`[NairabosEnemy] ✓ Found body part '${partName}' at position:`, part.position);
        }
    });
    
    if (missingParts.length > 0) {
        console.error(`[NairabosEnemy] ANIMATION WILL FAIL: Missing parts: ${missingParts.join(', ')}`);
        // Create placeholder groups to prevent animation crashes
        missingParts.forEach(partName => {
            const placeholder = new THREE.Group();
            placeholder.name = partName;
            if (visualGroup) {
                visualGroup.add(placeholder);
            } else {
                finalGroup.add(placeholder);
            }
            console.warn(`[NairabosEnemy] Created placeholder for missing '${partName}'`);
        });
    }
    
    if (totalNaNFixed > 0) {
        console.warn(`[NairabosEnemy] FINAL VALIDATION: Fixed ${totalNaNFixed} total NaN values in geometry`);
    } else {
        console.log(`[NairabosEnemy] FINAL VALIDATION: All geometry is clean, no NaN values found`);
    }

    console.log(`Created Nairabos Shadow Creature Enemy Model - HORRIFYING! Scale: ${safeScale}`);
    return finalGroup;
}
