import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Voxel Data for Mystic Staff ---

// Define staff colors
const STAFF_COLORS = ['8B4513', '654321', '5D4037']; // Brown wood
const CRYSTAL_COLORS = ['9932CC', 'BA55D3', 'DA70D6']; // Purple crystal
const METAL_COLORS = ['C0C0C0', 'D3D3D3', 'E5E5E5']; // Silver metal bands

// Define the mystic staff shape using voxel coordinates
// Structure: Base -> Shaft -> Crystal Head
const mysticStaffShape = [
    // === BASE (Y: 0-2) ===
    // Base cap (Y=0)
    { x: 0, y: 0, z: 0, c: METAL_COLORS[0] },
    { x: -1, y: 0, z: 0, c: METAL_COLORS[1] },
    { x: 1, y: 0, z: 0, c: METAL_COLORS[1] },
    { x: 0, y: 0, z: -1, c: METAL_COLORS[1] },
    { x: 0, y: 0, z: 1, c: METAL_COLORS[1] },
    
    // Base shaft (Y=1-2)
    { x: 0, y: 1, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 2, z: 0, c: STAFF_COLORS[1] },
    
    // === SHAFT (Y: 3-12) ===
    { x: 0, y: 3, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 4, z: 0, c: STAFF_COLORS[1] },
    { x: 0, y: 5, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 6, z: 0, c: STAFF_COLORS[1] },
    { x: 0, y: 7, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 8, z: 0, c: STAFF_COLORS[1] },
    { x: 0, y: 9, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 10, z: 0, c: STAFF_COLORS[1] },
    { x: 0, y: 11, z: 0, c: STAFF_COLORS[0] },
    { x: 0, y: 12, z: 0, c: STAFF_COLORS[1] },
    
    // === METAL BAND (Y: 13) ===
    { x: 0, y: 13, z: 0, c: METAL_COLORS[0] },
    { x: -1, y: 13, z: 0, c: METAL_COLORS[1] },
    { x: 1, y: 13, z: 0, c: METAL_COLORS[1] },
    { x: 0, y: 13, z: -1, c: METAL_COLORS[1] },
    { x: 0, y: 13, z: 1, c: METAL_COLORS[1] },
    
    // === CRYSTAL HEAD (Y: 14-17) ===
    // Crystal base (Y=14)
    { x: 0, y: 14, z: 0, c: CRYSTAL_COLORS[0] },
    { x: -1, y: 14, z: 0, c: CRYSTAL_COLORS[1] },
    { x: 1, y: 14, z: 0, c: CRYSTAL_COLORS[1] },
    { x: 0, y: 14, z: -1, c: CRYSTAL_COLORS[1] },
    { x: 0, y: 14, z: 1, c: CRYSTAL_COLORS[1] },
    
    // Crystal middle (Y=15)
    { x: 0, y: 15, z: 0, c: CRYSTAL_COLORS[0] },
    { x: -1, y: 15, z: 0, c: CRYSTAL_COLORS[2] },
    { x: 1, y: 15, z: 0, c: CRYSTAL_COLORS[2] },
    { x: 0, y: 15, z: -1, c: CRYSTAL_COLORS[2] },
    { x: 0, y: 15, z: 1, c: CRYSTAL_COLORS[2] },
    { x: -1, y: 15, z: -1, c: CRYSTAL_COLORS[1] },
    { x: 1, y: 15, z: -1, c: CRYSTAL_COLORS[1] },
    { x: -1, y: 15, z: 1, c: CRYSTAL_COLORS[1] },
    { x: 1, y: 15, z: 1, c: CRYSTAL_COLORS[1] },
    
    // Crystal upper (Y=16)
    { x: 0, y: 16, z: 0, c: CRYSTAL_COLORS[0] },
    { x: -1, y: 16, z: 0, c: CRYSTAL_COLORS[1] },
    { x: 1, y: 16, z: 0, c: CRYSTAL_COLORS[1] },
    { x: 0, y: 16, z: -1, c: CRYSTAL_COLORS[1] },
    { x: 0, y: 16, z: 1, c: CRYSTAL_COLORS[1] },
    
    // Crystal tip (Y=17)
    { x: 0, y: 17, z: 0, c: CRYSTAL_COLORS[0] }
];

// --- Main Item Function ---
export function createMysticStaffItem(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 3141); // Different multiplier for variation

    // Use smaller voxel size for items
    const itemVoxelSize = VOXEL_SIZE * 2.0; // Same as other items
    const baseGeometry = getOrCreateGeometry('mystic_staff_voxel', () =>
        new THREE.BoxGeometry(itemVoxelSize, itemVoxelSize, itemVoxelSize)
    );

    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the staff shape
    mysticStaffShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to crystal colors for magical effect
        let finalColor = c;
        if (CRYSTAL_COLORS.includes(c) && rng() < 0.3) { // 30% chance for crystal variation
            const colorIndex = Math.floor(rng() * CRYSTAL_COLORS.length);
            finalColor = CRYSTAL_COLORS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * itemVoxelSize,
            y * itemVoxelSize,
            z * itemVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each color
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        
        // Add glow effect to crystal parts
        if (CRYSTAL_COLORS.includes(colorHex)) {
            material.emissive = new THREE.Color(colorHex).multiplyScalar(0.2);
            material.emissiveIntensity = 0.3;
        }
        
        const mesh = new THREE.Mesh(mergedGeometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);

        // Dispose individual geometries
        geometries.forEach(geo => geo.dispose());
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        itemType: 'mystic_staff',
        itemName: 'Mystic Staff',
        itemDescription: 'A powerful staff topped with a glowing crystal. When slammed into the ground, it unleashes magical energy in all directions.',
        itemCategory: 'weapon',
        itemRarity: 'epic',
        voxelScale: itemVoxelSize,
        
        // Animation properties
        isFloating: false,
        floatSpeed: 1.0,
        rotationSpeed: 0.5,
        
        originalVoxels: mysticStaffShape.map(v => {
            const voxelRng = mulberry32(seed * 19 + v.x * 3 + v.y * 5 + v.z * 7);
            return {...v, c: v.c}; // Keep original color
        })
    };

    console.log('Mystic staff created with options:', options);
    console.log('Mystic staff userData:', group.userData);

    return group;
}

// Item metadata for the loot system
export const MYSTIC_STAFF_DATA = {
    id: 'mystic_staff',
    name: 'Mystic Staff',
    description: 'A powerful staff topped with a glowing crystal. When slammed into the ground, it unleashes magical energy in all directions.',
    type: 'weapon',
    category: 'weapon',
    rarity: 'epic',
    createFunction: createMysticStaffItem,
    dropChance: 15, // 15% chance in normal chests
    value: 1500,
    stackable: false,

    // Weapon-specific properties
    weaponType: 'melee',
    damage: 3, // High damage for area effect
    range: 5, // Large area of effect
    attackSpeed: 0.8, // Slower than sword due to power
    
    // Attack pattern for ground slam
    attackPattern: {
        type: 'circular',
        range: 5.0,
        description: 'Ground slam with circular area damage'
    }
};
