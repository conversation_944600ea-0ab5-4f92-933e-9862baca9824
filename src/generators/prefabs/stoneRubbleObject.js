import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
// Import needed constants and materials from shared.js
import {
    VOXEL_SIZE,
    mulberry32,
    getOrCreateGeometry,      // <-- Import geometry helper
    _getMaterialByHex_Cached // <-- Import material helper
} from './shared.js';
// import VoxelBuilder from '../utils/VoxelBuilder.js'; // <-- REMOVE VoxelBuilder import

// Define rubble material colors (hex strings) to avoid circular dependency
const RUBBLE_COLOR_1 = 'C0C0B0';    // Light Beige-Grey (mainStoneMaterial color)
const RUBBLE_COLOR_2 = 'BBBBBB';    // Medium-Light Grey (midLightStoneMaterial color)
const RUBBLE_COLOR_3 = 'CCCCCC';    // Very Light Grey (lightStoneMaterial color)

/**
 * Creates a 3D pile of stone rubble using merged geometry.
 */
export function createStoneRubbleObject(options = {}) {
    const { seed = 54321 } = options;
    const random = mulberry32(seed);
    // const builder = new VoxelBuilder(VOXEL_SIZE); // <-- REMOVE

    const rubbleGroup = new THREE.Group();
    const geometriesByMaterial = {}; // Store geometries by hex color string
    const tempMatrix = new THREE.Matrix4();
    const tempEuler = new THREE.Euler();
    const tempQuat = new THREE.Quaternion();
    // Use geometry cache for base voxel
    const voxelGeo = getOrCreateGeometry('voxel_rubble', () => new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE));
    const originalVoxels = []; // <-- Add array to store voxel data

    const numPieces = Math.floor(random() * 7) + 6; // Increased from 4-8 to 6-12 pieces
    const pileRadius = VOXEL_SIZE * 2.0; // Increased from 1.5
    const pileHeight = VOXEL_SIZE * 2.0; // Increased from 1.5

    const colors = [RUBBLE_COLOR_1, RUBBLE_COLOR_2, RUBBLE_COLOR_3];

    for (let i = 0; i < numPieces; i++) {
        // Random size for each piece (1x1, 1x2, 2x1, 2x2 voxels)
        // Note: For simplicity, we'll stick to 1x1x1 voxels for merging, 
        // but could extend this later to use different geometries if needed.
        const w_vox = 1; //(random() < 0.5 ? 1 : 2); 
        const h_vox = 1;
        const d_vox = 1; //(random() < 0.5 ? 1 : 2);

        // Random position within the pile radius, slightly elevated
        const angle = random() * Math.PI * 2;
        const radius = random() * pileRadius;
        const x_pos = Math.cos(angle) * radius;
        const z_pos = Math.sin(angle) * radius;
        const y_pos = (random() * pileHeight * 0.5) + (h_vox * VOXEL_SIZE / 2); // Place base slightly above 0

        // Random rotation
        const rotX = random() * Math.PI * 0.5 - Math.PI * 0.25; // Tilt slightly
        const rotY = random() * Math.PI * 2;
        const rotZ = random() * Math.PI * 0.5 - Math.PI * 0.25; // Tilt slightly
        tempEuler.set(rotX, rotY, rotZ, 'XYZ');
        tempQuat.setFromEuler(tempEuler);

        // Random color
        const hex = colors[Math.floor(random() * colors.length)];
        if (!geometriesByMaterial[hex]) {
            geometriesByMaterial[hex] = [];
        }

        // Create and transform geometry
        const geo = voxelGeo.clone(); // Use the cached 1x1x1 voxel geometry
        // Apply rotation first, then translation
        tempMatrix.compose( 
            new THREE.Vector3(x_pos, y_pos, z_pos), // Position
            tempQuat, // Rotation
            new THREE.Vector3(w_vox, h_vox, d_vox) // Scale (using 1 for now)
        );
        geo.applyMatrix4(tempMatrix);
        geometriesByMaterial[hex].push(geo);
        
        // --- Store voxel data for destruction ---
        // Get position and color BEFORE merging
        const position = new THREE.Vector3();
        tempMatrix.decompose(position, tempQuat, new THREE.Vector3()); // Decompose matrix to get world pos
        originalVoxels.push({ 
            x: position.x / VOXEL_SIZE, // Store relative voxel coords
            y: position.y / VOXEL_SIZE,
            z: position.z / VOXEL_SIZE,
            c: hex 
        });
        // ----------------------------------------
    }

    // --- Merge Geometries --- 
    for (const hex in geometriesByMaterial) {
        const mergedGeo = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[hex], false);
        if (mergedGeo) {
             const material = _getMaterialByHex_Cached(hex); // Use cached material getter
            const mesh = new THREE.Mesh(mergedGeo, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            rubbleGroup.add(mesh);
        } else {
            console.warn(`[StoneRubble] Failed to merge geometry for material hex: ${hex}`);
        }
    }

    // --- Set UserData ---
    rubbleGroup.userData = {
        ...(rubbleGroup.userData || {}),
        objectType: 'stone_rubble',
        isDestructible: true,
        destructionEffect: 'collapse',
        health: 1,
        originalVoxels: originalVoxels, // <-- Add voxel data
        voxelScale: VOXEL_SIZE         // <-- Add voxel scale
    };

    // Rubble rests on the floor (y=0 is the base)
    // Group origin is at y=0, and pieces were positioned relative to that.

    return { group: rubbleGroup };
} 