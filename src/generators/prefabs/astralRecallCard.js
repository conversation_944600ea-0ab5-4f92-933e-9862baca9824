import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Astral Recall Card Prefab
 * Creates an astral portal with swirling energy for emergency teleportation magic
 */

// Astral recall specific colors
const ASTRAL_COLORS = {
    ASTRAL_PURPLE: 0x9370DB,    // Main astral color
    SPIRIT_BLUE: 0x4169E1,      // Spirit energy
    ETHEREAL_WHITE: 0xFFFFFF,   // Pure ethereal energy
    VOID_INDIGO: 0x4B0082,      // Deep void color
    MYSTIC_LAVENDER: 0xE6E6FA,  // Light mystic aura
    COSMIC_VIOLET: 0x8A2BE2     // Cosmic energy
};

/**
 * Create an astral recall card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The astral recall card 3D model
 */
export function createAstralRecallCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'AstralRecallCard';

    // Materials
    const astralPurpleMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.ASTRAL_PURPLE,
        emissive: ASTRAL_COLORS.ASTRAL_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const spiritBlueMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.SPIRIT_BLUE,
        emissive: ASTRAL_COLORS.SPIRIT_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const etherealWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.ETHEREAL_WHITE,
        emissive: ASTRAL_COLORS.ETHEREAL_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const voidIndigoMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.VOID_INDIGO,
        emissive: ASTRAL_COLORS.VOID_INDIGO,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.85
    });

    const mysticLavenderMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.MYSTIC_LAVENDER,
        emissive: ASTRAL_COLORS.MYSTIC_LAVENDER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    const cosmicVioletMaterial = new THREE.MeshLambertMaterial({
        color: ASTRAL_COLORS.COSMIC_VIOLET,
        emissive: ASTRAL_COLORS.COSMIC_VIOLET,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create astral portal voxels (central swirling vortex)
    const portalVoxels = [
        // Portal center (brightest)
        { x: 0, y: 0, z: 0, material: etherealWhiteMaterial },
        
        // Inner ring
        { x: 1, y: 0, z: 0, material: astralPurpleMaterial },
        { x: -1, y: 0, z: 0, material: astralPurpleMaterial },
        { x: 0, y: 1, z: 0, material: astralPurpleMaterial },
        { x: 0, y: -1, z: 0, material: astralPurpleMaterial },

        // Diagonal inner ring
        { x: 1, y: 1, z: 0, material: spiritBlueMaterial },
        { x: -1, y: 1, z: 0, material: spiritBlueMaterial },
        { x: 1, y: -1, z: 0, material: spiritBlueMaterial },
        { x: -1, y: -1, z: 0, material: spiritBlueMaterial },

        // Outer ring
        { x: 2, y: 0, z: 0, material: voidIndigoMaterial },
        { x: -2, y: 0, z: 0, material: voidIndigoMaterial },
        { x: 0, y: 2, z: 0, material: voidIndigoMaterial },
        { x: 0, y: -2, z: 0, material: voidIndigoMaterial },

        // Spiral arms
        { x: 2, y: 1, z: 0, material: cosmicVioletMaterial },
        { x: 1, y: 2, z: 0, material: cosmicVioletMaterial },
        { x: -1, y: 2, z: 0, material: cosmicVioletMaterial },
        { x: -2, y: 1, z: 0, material: cosmicVioletMaterial },
        { x: -2, y: -1, z: 0, material: cosmicVioletMaterial },
        { x: -1, y: -2, z: 0, material: cosmicVioletMaterial },
        { x: 1, y: -2, z: 0, material: cosmicVioletMaterial },
        { x: 2, y: -1, z: 0, material: cosmicVioletMaterial }
    ];

    // Orbiting energy particles
    const energyVoxels = [
        // Top orbit
        { x: 0, y: 3, z: 0, material: etherealWhiteMaterial },
        { x: 1, y: 3, z: 0, material: mysticLavenderMaterial },
        { x: -1, y: 3, z: 0, material: mysticLavenderMaterial },

        // Right orbit
        { x: 3, y: 0, z: 0, material: etherealWhiteMaterial },
        { x: 3, y: 1, z: 0, material: mysticLavenderMaterial },
        { x: 3, y: -1, z: 0, material: mysticLavenderMaterial },

        // Bottom orbit
        { x: 0, y: -3, z: 0, material: etherealWhiteMaterial },
        { x: 1, y: -3, z: 0, material: mysticLavenderMaterial },
        { x: -1, y: -3, z: 0, material: mysticLavenderMaterial },

        // Left orbit
        { x: -3, y: 0, z: 0, material: etherealWhiteMaterial },
        { x: -3, y: 1, z: 0, material: mysticLavenderMaterial },
        { x: -3, y: -1, z: 0, material: mysticLavenderMaterial },

        // Diagonal orbiting particles
        { x: 2, y: 2, z: 0, material: spiritBlueMaterial },
        { x: -2, y: 2, z: 0, material: spiritBlueMaterial },
        { x: 2, y: -2, z: 0, material: spiritBlueMaterial },
        { x: -2, y: -2, z: 0, material: spiritBlueMaterial }
    ];

    // Astral runes around the portal
    const runeVoxels = [
        // Top runes
        { x: 0, y: 4, z: 0, material: astralPurpleMaterial },
        { x: 1, y: 4, z: 0, material: voidIndigoMaterial },
        { x: -1, y: 4, z: 0, material: voidIndigoMaterial },

        // Right runes
        { x: 4, y: 0, z: 0, material: astralPurpleMaterial },
        { x: 4, y: 1, z: 0, material: voidIndigoMaterial },
        { x: 4, y: -1, z: 0, material: voidIndigoMaterial },

        // Bottom runes
        { x: 0, y: -4, z: 0, material: astralPurpleMaterial },
        { x: 1, y: -4, z: 0, material: voidIndigoMaterial },
        { x: -1, y: -4, z: 0, material: voidIndigoMaterial },

        // Left runes
        { x: -4, y: 0, z: 0, material: astralPurpleMaterial },
        { x: -4, y: 1, z: 0, material: voidIndigoMaterial },
        { x: -4, y: -1, z: 0, material: voidIndigoMaterial }
    ];

    // Create all voxels
    [...portalVoxels, ...energyVoxels, ...runeVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.7, // Compact the layout
            voxel.y * VOXEL_SIZE * 0.7,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (portalVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'portal';
        } else if (energyVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'energy';
        } else {
            mesh.userData.voxelType = 'rune';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        portalSpin: 0,
        energyOrbit: 0,
        runeFlicker: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update astral recall card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateAstralRecallCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.portalSpin += deltaTime * 2.0;
    cardGroup.userData.energyOrbit += deltaTime * 1.5;
    cardGroup.userData.runeFlicker += deltaTime * 3.0;

    const time = cardGroup.userData.animationTime;
    const portalSpin = cardGroup.userData.portalSpin;
    const energyOrbit = cardGroup.userData.energyOrbit;
    const runeFlicker = cardGroup.userData.runeFlicker;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'portal':
                    // Portal voxels swirl and pulse
                    const portalPulse = 0.8 + Math.sin(portalSpin * 3.0 + child.position.x + child.position.y) * 0.4;
                    child.material.emissiveIntensity = baseEmissive * portalPulse;
                    child.material.opacity = baseOpacity * (0.7 + portalPulse * 0.3);
                    
                    // Slight swirling motion for portal center
                    if (child.position.x === 0 && child.position.y === 0) {
                        const swirl = Math.sin(portalSpin * 4.0) * 0.01;
                        child.position.z = swirl;
                    }
                    break;

                case 'energy':
                    // Energy particles orbit and glow
                    const energyPulse = 0.6 + Math.sin(energyOrbit * 2.5 + child.position.x * 2 + child.position.y * 2) * 0.6;
                    child.material.emissiveIntensity = baseEmissive * energyPulse;
                    child.material.opacity = baseOpacity * energyPulse;
                    
                    // Orbital motion
                    const orbitOffset = Math.sin(energyOrbit + child.position.x + child.position.y) * 0.02;
                    child.position.z = orbitOffset;
                    break;

                case 'rune':
                    // Runes flicker mysteriously
                    const runeIntensity = 0.4 + Math.sin(runeFlicker + child.position.x * 3 + child.position.y * 3) * 0.6;
                    const runeVisibility = Math.abs(Math.sin(runeFlicker * 0.8 + child.position.x + child.position.y));
                    
                    child.material.emissiveIntensity = baseEmissive * runeIntensity;
                    child.material.opacity = baseOpacity * (0.3 + runeVisibility * 0.7);
                    break;
            }
        }
    });

    // Overall card gentle rotation
    cardGroup.rotation.z = Math.sin(time * 0.5) * 0.1;
}

// Export the astral recall card data for the loot system
export const ASTRAL_RECALL_CARD_DATA = {
    name: 'Astral Recall',
    description: 'Instantly teleports you back to the entrance of the current dungeon floor through astral projection. Emergency escape when overwhelmed by danger.',
    category: 'card',
    rarity: 'rare',
    effect: 'astral_recall',
    effectValue: 'teleport',
    createFunction: createAstralRecallCard,
    updateFunction: updateAstralRecallCardAnimation,
    voxelModel: 'astral_recall_card',
    glow: {
        color: 0x9370DB,
        intensity: 1.4
    }
};

export default createAstralRecallCard;