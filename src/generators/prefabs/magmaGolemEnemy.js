import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import { VOXEL_SIZE, getOrCreateGeometry, mulberry32 } from './shared.js';

// Magma Golem Colors
const MAGMA_GOLEM_COLORS = {
    BODY_DARK: 0x3a1a00, // Dark rock/obsidian
    BODY_MEDIUM: 0x4d2600, // Medium rock
    BODY_LIGHT: 0x663300, // Light rock
    MAGMA_BRIGHT: 0xff6600, // Bright magma
    MAGMA_MEDIUM: 0xff3300, // Medium magma
    MAGMA_DARK: 0xcc0000, // Dark magma/embers
    EYE: 0xff9900, // Glowing eyes
};

/**
 * Creates a voxel-based magma golem enemy model.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The magma golem enemy model group.
 */
export function createMagmaGolemEnemyModel(scale = 1.0) {
    console.log("CREATING MAGMA GOLEM MODEL WITH SCALE:", scale);
    const finalGroup = new THREE.Group();
    finalGroup.name = "MagmaGolem";

    // Create a seeded random function
    const random = mulberry32(12345); // Fixed seed for consistent appearance

    // Create animation-ready groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const headGroup = new THREE.Group();
    headGroup.name = "head";

    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";

    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";

    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";

    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";

    // Add groups to final group with proper hierarchy
    finalGroup.add(bodyGroup);
    bodyGroup.add(headGroup);
    bodyGroup.add(leftArmGroup);
    bodyGroup.add(rightArmGroup);
    bodyGroup.add(leftLegGroup);
    bodyGroup.add(rightLegGroup);

    // Position body parts
    headGroup.position.set(0, 12 * VOXEL_SIZE, 0); // Above body (body goes to y=11)
    leftArmGroup.position.set(-3 * VOXEL_SIZE, 9 * VOXEL_SIZE, 0); // Mid-torso height for shoulders
    rightArmGroup.position.set(3 * VOXEL_SIZE, 9 * VOXEL_SIZE, 0); // Mid-torso height for shoulders
    // Position legs at hip level (bottom of torso)
    leftLegGroup.position.set(-1.5 * VOXEL_SIZE, 6 * VOXEL_SIZE, 0);
    rightLegGroup.position.set(1.5 * VOXEL_SIZE, 6 * VOXEL_SIZE, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        head: {},
        leftArm: {},
        rightArm: {},
        leftLeg: {},
        rightLeg: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('golem_voxel', () => new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE));
    const tempMatrix = new THREE.Matrix4();

    // Helper function to add a voxel to the appropriate group
    function addVoxel(groupName, x, y, z, color) {
        // Convert coordinates to world space
        tempMatrix.makeTranslation(
            x * VOXEL_SIZE,
            y * VOXEL_SIZE,
            z * VOXEL_SIZE
        );

        // Store voxel data for destruction effects
        originalVoxels.push({
            group: groupName,
            position: new THREE.Vector3(x, y, z).multiplyScalar(VOXEL_SIZE),
            color: color
        });

        // Create or get the geometries array for this color
        if (!geometriesByMaterial[groupName][color]) {
            geometriesByMaterial[groupName][color] = [];
        }

        // Create a new instance of the geometry
        const instancedGeo = voxelGeo.clone();
        instancedGeo.applyMatrix4(tempMatrix);

        // Add to the appropriate array
        geometriesByMaterial[groupName][color].push(instancedGeo);
    }

    // Helper function to merge geometries for a group
    function mergeGroupGeometries(groupName, targetGroup) {
        const colorMaterials = {};

        // Process each color in the group
        Object.entries(geometriesByMaterial[groupName]).forEach(([colorHex, geometries]) => {
            const color = parseInt(colorHex);

            // Skip if no geometries for this color
            if (geometries.length === 0) return;

            // Create material with emissive properties for magma colors
            const material = new THREE.MeshStandardMaterial({
                color: color,
                roughness: 0.7,
                metalness: 0.3
            });

            // Add emissive properties for magma colors
            if (
                color === MAGMA_GOLEM_COLORS.MAGMA_BRIGHT ||
                color === MAGMA_GOLEM_COLORS.MAGMA_MEDIUM ||
                color === MAGMA_GOLEM_COLORS.MAGMA_DARK ||
                color === MAGMA_GOLEM_COLORS.EYE
            ) {
                material.emissive = new THREE.Color(color);
                material.emissiveIntensity = 0.8;
            }

            colorMaterials[color] = material;

            // Merge geometries of the same color
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;

            // Add to the target group
            targetGroup.add(mesh);
        });
    }

    // Create body (torso)
    for (let x = -2; x <= 2; x++) {
        for (let y = 6; y <= 11; y++) {
            for (let z = -1; z <= 1; z++) {
                // Skip corners for a more rounded shape
                if ((Math.abs(x) === 2 && Math.abs(z) === 1)) continue;

                // Determine color - mix rock and magma
                let color;
                if (random() < 0.7) {
                    // Rock colors
                    if (random() < 0.6) {
                        color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                    } else if (random() < 0.8) {
                        color = MAGMA_GOLEM_COLORS.BODY_DARK;
                    } else {
                        color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                    }
                } else {
                    // Magma colors
                    if (random() < 0.5) {
                        color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                    } else if (random() < 0.8) {
                        color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                    } else {
                        color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                    }
                }

                addVoxel('body', x, y, z, color);
            }
        }
    }

    // Create head
    for (let x = -2; x <= 2; x++) {
        for (let y = -2; y <= 2; y++) {
            for (let z = -2; z <= 2; z++) {
                // Skip corners for a more rounded shape
                if (Math.abs(x) === 2 && Math.abs(y) === 2) continue;
                if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;
                if (Math.abs(y) === 2 && Math.abs(z) === 2) continue;

                // Determine color - mix rock and magma
                let color;
                if (random() < 0.7) {
                    // Rock colors
                    if (random() < 0.6) {
                        color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                    } else if (random() < 0.8) {
                        color = MAGMA_GOLEM_COLORS.BODY_DARK;
                    } else {
                        color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                    }
                } else {
                    // Magma colors
                    if (random() < 0.5) {
                        color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                    } else if (random() < 0.8) {
                        color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                    } else {
                        color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                    }
                }

                addVoxel('head', x, y, z, color);
            }
        }
    }

    // Add eyes
    addVoxel('head', -1, 0, 2, MAGMA_GOLEM_COLORS.EYE);
    addVoxel('head', 1, 0, 2, MAGMA_GOLEM_COLORS.EYE);

    // Create arms (bulky)
    for (let side = 0; side < 2; side++) {
        const groupName = side === 0 ? 'leftArm' : 'rightArm';
        const xDirection = side === 0 ? -1 : 1;

        // Upper arm - build downward from shoulder
        for (let y = 0; y <= 3; y++) {
            for (let x = -1; x <= 1; x++) {
                for (let z = -1; z <= 1; z++) {
                    // Skip corners for a more rounded shape
                    if (Math.abs(x) === 1 && Math.abs(z) === 1) continue;

                    // Determine color - mix rock and magma
                    let color;
                    if (random() < 0.7) {
                        // Rock colors
                        if (random() < 0.6) {
                            color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.BODY_DARK;
                        } else {
                            color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                        }
                    } else {
                        // Magma colors
                        if (random() < 0.5) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                        } else {
                            color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                        }
                    }

                    addVoxel(groupName, x, -y, z, color);
                }
            }
        }

        // Hand/fist (larger) - positioned below upper arm
        for (let y = -6; y <= -3; y++) {
            for (let x = -2; x <= 2; x++) {
                for (let z = -2; z <= 2; z++) {
                    // Skip corners for a more rounded shape
                    if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;

                    // More magma in the fists
                    let color;
                    if (random() < 0.5) {
                        // Rock colors
                        if (random() < 0.6) {
                            color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.BODY_DARK;
                        } else {
                            color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                        }
                    } else {
                        // Magma colors
                        if (random() < 0.5) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                        } else {
                            color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                        }
                    }

                    addVoxel(groupName, x, y, z, color);
                }
            }
        }
    }

    // Create legs
    for (let side = 0; side < 2; side++) {
        const groupName = side === 0 ? 'leftLeg' : 'rightLeg';

        // Upper leg - build downward from hip
        for (let y = 0; y <= 4; y++) {
            for (let x = -1; x <= 1; x++) {
                for (let z = -1; z <= 1; z++) {
                    // Skip corners for a more rounded shape
                    if (Math.abs(x) === 1 && Math.abs(z) === 1) continue;

                    // Determine color - mix rock and magma
                    let color;
                    if (random() < 0.8) {
                        // Rock colors
                        if (random() < 0.6) {
                            color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.BODY_DARK;
                        } else {
                            color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                        }
                    } else {
                        // Magma colors
                        if (random() < 0.5) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                        } else {
                            color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                        }
                    }

                    // Build downward from pivot
                    addVoxel(groupName, x, -y, z, color);
                }
            }
        }

        // Foot - positioned below upper leg
        for (let y = -8; y <= -5; y++) {
            for (let x = -2; x <= 2; x++) {
                for (let z = -1; z <= 2; z++) {
                    // Skip corners for a more rounded shape
                    if (Math.abs(x) === 2 && Math.abs(z) === 2) continue;
                    if (Math.abs(x) === 2 && z === -1) continue;

                    // More rock in the feet for stability
                    let color;
                    if (random() < 0.9) {
                        // Rock colors
                        if (random() < 0.6) {
                            color = MAGMA_GOLEM_COLORS.BODY_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.BODY_DARK;
                        } else {
                            color = MAGMA_GOLEM_COLORS.BODY_LIGHT;
                        }
                    } else {
                        // Magma colors
                        if (random() < 0.5) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_MEDIUM;
                        } else if (random() < 0.8) {
                            color = MAGMA_GOLEM_COLORS.MAGMA_BRIGHT;
                        } else {
                            color = MAGMA_GOLEM_COLORS.MAGMA_DARK;
                        }
                    }

                    addVoxel(groupName, x, y, z, color);
                }
            }
        }
    }

    // BufferGeometryUtils already imported at the top

    // Merge geometries for each group
    console.log('🔥 MAGMA DEBUG - Before merging, bodyGroup children:', bodyGroup.children.length);
    mergeGroupGeometries('body', bodyGroup);
    console.log('🔥 MAGMA DEBUG - After body merge, bodyGroup children:', bodyGroup.children.length);
    mergeGroupGeometries('head', headGroup);
    console.log('🔥 MAGMA DEBUG - After head merge, headGroup children:', headGroup.children.length);
    mergeGroupGeometries('leftArm', leftArmGroup);
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);
    console.log('🔥 MAGMA DEBUG - Final group children:', finalGroup.children.length);

    // Dispose the template geometry
    voxelGeo.dispose();

    // Apply overall scale
    finalGroup.scale.set(scale, scale, scale);

    // No rotation needed - model is now positioned correctly above ground

    // Add animation data to userData
    finalGroup.userData.animationData = {
        walkSpeed: 1.2,              // Slower walk speed for heavy golem
        walkAmplitude: Math.PI / 8,  // Smaller amplitude for heavy movement
        armSwingAmplitude: Math.PI / 6, // Arm swing amount
        bodySwayAmplitude: 0.05,     // Body sway amount
        bodySwaySpeed: 0.8,          // Body sway speed
        attackAnimationDuration: 2.4, // Increased from 2.0 to match zombie duration for consistency
        headBobAmplitude: 0.03,      // Head bobbing amount
        headBobSpeed: 1.2            // Head bobbing speed
    };

    // Add type information to ensure proper animation handling
    finalGroup.userData.type = 'magma_golem';

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 6.0 * scale, // INCREASED: Much larger attack radius to compensate for scaling issues
        damage: 1, // 1 damage as per user preference
        knockback: 10.0 // Strong knockback
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'magma_golem';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = VOXEL_SIZE;

    console.log("Created Magma Golem Enemy Model");
    return finalGroup;
}
