import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Mystical Stone Brick Wall with Sacred Caps
 *
 * Creates mystical stone brick walls with sacred/mystical appearance and proper wall caps.
 * Based on the ominous treasure room design with mystical materials.
 * Uses the same extrusion system as normal stone walls but with mystical-themed materials.
 */

// Mystical stone materials (sacred purple-blue tones)
const MYSTICAL_STONE_MATERIALS = [
    _getMaterialByHex_Cached('8A7CA8'), // Mystical purple-gray (primary)
    _getMaterialByHex_Cached('9B7EBD'), // Lighter mystical purple (secondary)
    _getMaterialByHex_Cached('6B5B95'), // Darker mystical purple (accent)
    _getMaterialByHex_Cached('4A4A6A'), // Dark slate purple (mortar)
    _getMaterialByHex_Cached('7B68EE'), // Medium slate blue (mystical accent)
    _getMaterialByHex_Cached('9370DB'), // Medium orchid (sacred highlights)
    _getMaterialByHex_Cached('483D8B')  // Dark slate blue (deep shadows)
];

/**
 * Get random mystical stone material
 */
function getRandomMysticalStoneMat() {
    const rand = Math.random();
    if (rand < 0.35) return MYSTICAL_STONE_MATERIALS[0]; // Primary mystical purple-gray
    if (rand < 0.65) return MYSTICAL_STONE_MATERIALS[1]; // Secondary lighter purple
    if (rand < 0.80) return MYSTICAL_STONE_MATERIALS[2]; // Darker accent
    if (rand < 0.90) return MYSTICAL_STONE_MATERIALS[4]; // Mystical blue accent
    if (rand < 0.95) return MYSTICAL_STONE_MATERIALS[5]; // Sacred highlights
    return MYSTICAL_STONE_MATERIALS[6]; // Deep shadows
}

/**
 * Create mystical stone brick wall using the same extrusion system as normal stone walls
 * INCLUDES: Proper wall caps that close the tops like ominous treasure room
 */
export function createMysticalStoneBrickWallSegment(width, height, depth, roomData) {
    console.log(`[MysticalStoneWall] Creating wall segment: ${width}x${height}x${depth}`);

    // --- INPUT VALIDATION ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createMysticalStoneBrickWallSegment received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() };
    }

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    // --- Use same scaling system as normal stone walls ---
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;

    // Calculate dimensions using scaled voxels (same as normal stone walls)
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);

    // Safeguard dimensions (same as normal stone walls)
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);

    // Centering offsets
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetY = (numY - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;

    // Use seeded PRNG for consistent patterns
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);

    // Get cached wall voxel geometry
    const largeWallGeo = getOrCreateGeometry(
        `mystical_wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };

    // --- Generate Wall Voxels with Extrusion (same pattern as stone walls) ---
    let voxelsAdded = 0;
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            // 95% chance for stone block (same as normal walls)
            if (random() > 0.05) {
                const blockMaterial = getRandomMysticalStoneMat();

                // Keep extruded look but reduce gaps between voxels (same as stone wall)
                const baseX = ex * wallVoxelSize - offsetX;
                const baseY = ey * wallVoxelSize - offsetY;

                // Increase variations for more extruded appearance like other event rooms
                const depthVariation = (random() - 0.5) * 0.08; // Increased depth variation for more extrusion
                const heightVariation = (random() - 0.5) * 0.05; // Increased height variation
                const rotationY = (random() - 0.5) * 0.05; // Increased Y rotation for more texture
                const rotationZ = (random() - 0.5) * 0.04; // Increased Z rotation for more texture

                const finalX = baseX;
                const finalY = baseY + heightVariation;
                const finalZ = depthVariation;

                // Main wall block
                tempMatrix.makeTranslation(finalX, finalY, finalZ);
                tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // Add north and south face caps - FIXED: Proper positioning to eliminate gaps
                const northFaceGeo = getOrCreateGeometry(
                    `mystical_wall_ns_cap_thick_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                    () => new THREE.BoxGeometry(wallVoxelSize * 1.1, wallVoxelSize, depth * 0.5)
                );
                // FIXED: Position caps to completely fill the front/back faces
                const frontPosZ = -depth/2 - depth * 0.25;
                tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // For south face (back side of wall) - FIXED: Proper positioning to eliminate gaps
                const backPosZ = depth/2 + depth * 0.25;
                tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // COMPREHENSIVE: Add top caps using same system as catacomb walls
                if (ey === safeNumYEnv - 1) {
                    // Create extended top cap with proper overhang (same as catacomb walls)
                    const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                    const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                    const topCapGeo = getOrCreateGeometry(
                        `mystical_wall_top_cap_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                    );

                    // Position at the top of the wall voxel, slightly higher for prominence
                    const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                    // Add extended top cap with overhang
                    tempMatrix.makeTranslation(finalX, topPosY, 0);
                    addGeometry(topCapGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;

                    // Add additional gap-filling caps between voxels with matching overhang
                    if (ex < safeNumXEnv - 1) { // Not the last column
                        const gapCapGeo = getOrCreateGeometry(
                            `mystical_wall_gap_cap_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                        );
                        const gapCapX = finalX + wallVoxelSize * 0.6;
                        tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                        addGeometry(gapCapGeo, blockMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                }
            }
        }
    }

    // --- Fill Gaps with Mortar (same as stone wall) ---
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            // 5% chance for mortar/gap filler (same as normal walls)
            if (random() <= 0.05) {
                const mortarMaterial = MYSTICAL_STONE_MATERIALS[3]; // Dark slate purple for mortar

                const baseX = ex * wallVoxelSize - offsetX;
                const baseY = ey * wallVoxelSize - offsetY;

                // Reduced variations for gap fillers to match main voxels
                const depthVariation = (random() - 0.5) * 0.025; // Slightly less variation for mortar
                const heightVariation = (random() - 0.5) * 0.015; // Reduced height variation
                const rotationY = (random() - 0.5) * 0.015; // Reduced Y rotation
                const rotationZ = (random() - 0.5) * 0.01; // Reduced Z rotation

                const finalX = baseX;
                const finalY = baseY + heightVariation;
                const finalZ = depthVariation;

                // Main mortar block
                tempMatrix.makeTranslation(finalX, finalY, finalZ);
                tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                addGeometry(largeWallGeo, mortarMaterial, tempMatrix);
                voxelsAdded++;

                // Add north and south face caps for mortar - FIXED: Proper positioning to eliminate gaps
                const northFaceGeo = getOrCreateGeometry(
                    `mystical_wall_ns_cap_thick_mortar_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                    () => new THREE.BoxGeometry(wallVoxelSize * 1.1, wallVoxelSize, depth * 0.5)
                );
                // FIXED: Position caps to completely fill the front/back faces
                const frontPosZ = -depth/2 - depth * 0.25;
                tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                addGeometry(northFaceGeo, mortarMaterial, tempMatrix);
                voxelsAdded++;

                // For south face (back side of wall) - FIXED: Proper positioning to eliminate gaps
                const backPosZ = depth/2 + depth * 0.25;
                tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                addGeometry(northFaceGeo, mortarMaterial, tempMatrix);
                voxelsAdded++;

                // COMPREHENSIVE: Add top caps for mortar using same system as catacomb walls
                if (ey === safeNumYEnv - 1) {
                    // Create extended top cap with proper overhang (same as catacomb walls)
                    const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                    const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                    const topCapGeo = getOrCreateGeometry(
                        `mystical_wall_top_cap_mortar_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                    );

                    // Position at the top of the wall voxel, slightly higher for prominence
                    const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                    // Add extended top cap with overhang
                    tempMatrix.makeTranslation(finalX, topPosY, 0);
                    addGeometry(topCapGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;

                    // Add additional gap-filling caps between gap filler voxels with matching overhang
                    if (ex < safeNumXEnv - 1) { // Not the last column
                        const gapCapGeo = getOrCreateGeometry(
                            `mystical_wall_gap_cap_mortar_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                        );
                        const gapCapX = finalX + wallVoxelSize * 0.6;
                        tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                        addGeometry(gapCapGeo, mortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                }
            }
        }
    }

    console.log(`[MysticalStoneWall] Added ${voxelsAdded} voxels total with extrusion system and caps.`);

    // --- Merge Geometries by Material with Polygon Offset ---
    const mortarHex = MYSTICAL_STONE_MATERIALS[3].color.getHexString(); // Dark slate purple for mortar/caps

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for mystical stone wall: ${colorHex}`);
            continue;
        }

        // Clone material to apply polygon offset without modifying shared instance
        const finalMaterial = originalMaterial.clone();

        // Apply polygon offset to prevent z-fighting between wall elements
        if (colorHex === mortarHex) {
            // Pull mortar/cap materials towards camera slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = -1.0;
            finalMaterial.polygonOffsetUnits = -1.0;
        } else {
            // Push base wall materials away slightly
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = 1.0;
            finalMaterial.polygonOffsetUnits = 1.0;
        }

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            mesh.name = 'mysticalStoneBricks';
            mesh.userData.isWall = true;
            mesh.userData.wallType = 'mystical_stone_brick';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for mystical stone wall, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.wallType = 'mystical_stone_brick';
    finalGroup.userData.isWall = true;
    finalGroup.name = 'mysticalStoneBrickWall';

    // Set shadow properties for entire group
    finalGroup.castShadow = true;
    finalGroup.receiveShadow = true;

    console.log(`[MysticalStoneWall] ✅ Generated mystical wall with ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return {
        group: finalGroup,
        torchPositions: [] // No torches on mystical walls
    };
}


