import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a devil's throne - where the shadow silhouette with yellow eyes sits
 * An imposing throne with bone and flame motifs, partially obscured by shadow
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Devil's throne object group
 */
export function createDevilThroneObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 6660);

    // Use large voxel size for imposing throne
    const throneVoxelSize = VOXEL_SIZE * 3.5;
    const baseGeometry = getOrCreateGeometry('devil_throne_voxel', () =>
        new THREE.BoxGeometry(throneVoxelSize, throneVoxelSize, throneVoxelSize)
    );

    // Throne materials - dark with hellish accents
    const obsidianMaterial = _getMaterialByHex_Cached('1A0A0A', {
        roughness: 0.2,
        metalness: 0.8,
        emissive: new THREE.Color(0x0A0505),
        emissiveIntensity: 0.05
    }); // Dark throne base

    const boneMaterial = _getMaterialByHex_Cached('F5F5DC', {
        roughness: 0.8,
        metalness: 0.1,
        emissive: new THREE.Color(0x2A2A1A),
        emissiveIntensity: 0.02
    }); // Aged bone accents

    const flameMaterial = _getMaterialByHex_Cached('FF4500', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0xFF4500),
        emissiveIntensity: 0.6,
        roughness: 0.3,
        metalness: 0.2
    }); // Hellish flame accents

    const shadowMaterial = _getMaterialByHex_Cached('0A0A0A', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x050505),
        emissiveIntensity: 0.02
    }); // Deep shadow material

    const eyeMaterial = _getMaterialByHex_Cached('FFD700', {
        transparent: true,
        opacity: 1.0,
        emissive: new THREE.Color(0xFFD700),
        emissiveIntensity: 0.8,
        roughness: 0.1,
        metalness: 0.3
    }); // Glowing yellow eyes

    // Throne base and seat structure
    const throneStructure = [
        // Base platform (5x3)
        { x: -2, y: 0, z: -1 }, { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 }, { x: 2, y: 0, z: -1 },
        { x: -2, y: 0, z: 0 },  { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },  { x: 2, y: 0, z: 0 },
        { x: -2, y: 0, z: 1 },  { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 },  { x: 2, y: 0, z: 1 },
        
        // Seat (3x2)
        { x: -1, y: 1, z: 0 }, { x: 0, y: 1, z: 0 }, { x: 1, y: 1, z: 0 },
        { x: -1, y: 1, z: 1 }, { x: 0, y: 1, z: 1 }, { x: 1, y: 1, z: 1 },
        
        // Backrest foundation
        { x: -1, y: 2, z: 1 }, { x: 0, y: 2, z: 1 }, { x: 1, y: 2, z: 1 },
        { x: -1, y: 3, z: 1 }, { x: 0, y: 3, z: 1 }, { x: 1, y: 3, z: 1 },
        { x: -1, y: 4, z: 1 }, { x: 0, y: 4, z: 1 }, { x: 1, y: 4, z: 1 },
        { x: -1, y: 5, z: 1 }, { x: 0, y: 5, z: 1 }, { x: 1, y: 5, z: 1 },
        
        // Armrests
        { x: -2, y: 2, z: 0 }, { x: -2, y: 3, z: 0 },
        { x: 2, y: 2, z: 0 },  { x: 2, y: 3, z: 0 },
        
        // High backrest (throne spires)
        { x: -1, y: 6, z: 1 }, { x: 1, y: 6, z: 1 },
        { x: -1, y: 7, z: 1 }, { x: 1, y: 7, z: 1 },
        { x: 0, y: 8, z: 1 }  // Central spire peak
    ];

    // Add main throne structure
    throneStructure.forEach(pos => {
        const voxel = new THREE.Mesh(baseGeometry.clone(), obsidianMaterial);
        voxel.position.set(
            pos.x * throneVoxelSize,
            pos.y * throneVoxelSize,
            pos.z * throneVoxelSize
        );
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    });

    // Add bone decorations (skulls and bone motifs)
    const boneDecorations = [
        { x: -2, y: 1, z: -1 }, // Left skull
        { x: 2, y: 1, z: -1 },  // Right skull
        { x: 0, y: 6, z: 1 },   // Central skull on backrest
        { x: -1, y: 3, z: 0 },  // Left bone detail
        { x: 1, y: 3, z: 0 },   // Right bone detail
        { x: 0, y: 4, z: 0 }    // Central bone crosspiece
    ];

    boneDecorations.forEach(pos => {
        const bone = new THREE.Mesh(baseGeometry.clone(), boneMaterial);
        bone.position.set(
            pos.x * throneVoxelSize,
            pos.y * throneVoxelSize,
            pos.z * throneVoxelSize
        );
        bone.scale.set(0.8, 0.8, 0.8);
        bone.castShadow = true;
        bone.receiveShadow = true;
        group.add(bone);
    });

    // Add flame effects behind throne
    const flamePositions = [
        { x: -1, y: 7, z: 2 }, { x: 0, y: 7, z: 2 }, { x: 1, y: 7, z: 2 },
        { x: -1, y: 8, z: 2 }, { x: 0, y: 8, z: 2 }, { x: 1, y: 8, z: 2 },
        { x: 0, y: 9, z: 2 }
    ];

    flamePositions.forEach(pos => {
        const flame = new THREE.Mesh(baseGeometry.clone(), flameMaterial);
        flame.position.set(
            pos.x * throneVoxelSize,
            pos.y * throneVoxelSize,
            pos.z * throneVoxelSize
        );
        flame.scale.set(0.7, 1.2, 0.7); // Tall and narrow flames
        flame.castShadow = false; // Flames don't cast shadows
        flame.receiveShadow = false;
        group.add(flame);
    });

    // Create shadow silhouette figure
    const shadowFigure = [
        // Basic humanoid shadow shape
        { x: 0, y: 2, z: 0 },   // Lower torso
        { x: 0, y: 3, z: 0 },   // Upper torso
        { x: 0, y: 4, z: 0 },   // Chest
        { x: 0, y: 5, z: 0 },   // Head position
        { x: -1, y: 3, z: 0 },  // Left arm
        { x: 1, y: 3, z: 0 }    // Right arm
    ];

    shadowFigure.forEach(pos => {
        const shadow = new THREE.Mesh(baseGeometry.clone(), shadowMaterial);
        shadow.position.set(
            pos.x * throneVoxelSize,
            pos.y * throneVoxelSize,
            pos.z * throneVoxelSize
        );
        shadow.castShadow = false;
        shadow.receiveShadow = false;
        group.add(shadow);
    });

    // Add glowing yellow eyes
    const eyeGeometry = getOrCreateGeometry('devil_eye', () =>
        new THREE.BoxGeometry(throneVoxelSize * 0.3, throneVoxelSize * 0.3, throneVoxelSize * 0.3)
    );

    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    leftEye.position.set(
        -0.3 * throneVoxelSize,
        5 * throneVoxelSize,
        0.1 * throneVoxelSize
    );
    leftEye.userData.isDevilEye = true;
    leftEye.castShadow = false;
    leftEye.receiveShadow = false;
    group.add(leftEye);

    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
    rightEye.position.set(
        0.3 * throneVoxelSize,
        5 * throneVoxelSize,
        0.1 * throneVoxelSize
    );
    rightEye.userData.isDevilEye = true;
    rightEye.castShadow = false;
    rightEye.receiveShadow = false;
    group.add(rightEye);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'devil_throne',
        isInteractable: true,
        interactionType: 'devil_encounter',
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: throneVoxelSize,
        hasEyeGlow: true, // Enable eye glow animation
        hasFlameAnimation: true // Enable flame animation
    };

    group.name = 'devil_throne';
    console.log('[DevilThrone] ✅ Created menacing throne with shadow figure');
    return group;
}