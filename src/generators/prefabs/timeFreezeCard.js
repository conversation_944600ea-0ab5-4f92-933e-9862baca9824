import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Time Freeze Card Prefab
 * Freezes all enemies in time, stopping their movement and actions
 */

// Time Freeze specific colors
const TIME_FREEZE_COLORS = {
    FROZEN_BLUE: 0x87CEEB,          // Frozen ice blue
    TIME_GOLD: 0xFFD700,            // Golden time energy
    CRYSTAL_WHITE: 0xF0F8FF,        // Crystal ice formations
    TEMPORAL_PURPLE: 0x9932CC,      // Temporal magic
    STASIS_SILVER: 0xC0C0C0,        // Stasis field
    CHRONO_CYAN: 0x00CED1,          // Chronological energy
    VOID_BLACK: 0x191970,           // Time void
    SHIMMER_BLUE: 0x4169E1          // Shimmering effects
};

/**
 * Create a time freeze card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The time freeze card 3D model
 */
export function createTimeFreezeCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'TimeFreezeCard';

    // Time Freeze materials
    const frozenBlueMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.FROZEN_BLUE,
        emissive: TIME_FREEZE_COLORS.FROZEN_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    const timeGoldMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.TIME_GOLD,
        emissive: TIME_FREEZE_COLORS.TIME_GOLD,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.9
    });

    const crystalWhiteMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.CRYSTAL_WHITE,
        emissive: TIME_FREEZE_COLORS.CRYSTAL_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const temporalPurpleMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.TEMPORAL_PURPLE,
        emissive: TIME_FREEZE_COLORS.TEMPORAL_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const stasisSilverMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.STASIS_SILVER,
        emissive: TIME_FREEZE_COLORS.STASIS_SILVER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.6
    });

    const chronoCyanMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.CHRONO_CYAN,
        emissive: TIME_FREEZE_COLORS.CHRONO_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const voidBlackMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.VOID_BLACK,
        emissive: TIME_FREEZE_COLORS.VOID_BLACK,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const shimmerBlueMaterial = new THREE.MeshLambertMaterial({
        color: TIME_FREEZE_COLORS.SHIMMER_BLUE,
        emissive: TIME_FREEZE_COLORS.SHIMMER_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Temporal Hourglass (central time symbol)
    const temporalHourglassVoxels = [
        // Top chamber
        { x: -0.12, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top left
        { x: -0.08, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top left-center
        { x: -0.04, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top center-left
        { x: 0.0, y: 0.16, z: 0.0, material: crystalWhiteMaterial }, // Top center
        { x: 0.04, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top center-right
        { x: 0.08, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top right-center
        { x: 0.12, y: 0.16, z: 0.0, material: timeGoldMaterial }, // Top right
        
        // Upper funnel
        { x: -0.08, y: 0.12, z: 0.0, material: timeGoldMaterial }, // Upper left
        { x: -0.04, y: 0.12, z: 0.0, material: frozenBlueMaterial }, // Upper left-center
        { x: 0.0, y: 0.12, z: 0.0, material: crystalWhiteMaterial }, // Upper center
        { x: 0.04, y: 0.12, z: 0.0, material: frozenBlueMaterial }, // Upper right-center
        { x: 0.08, y: 0.12, z: 0.0, material: timeGoldMaterial }, // Upper right
        
        // Neck (time flows through)
        { x: -0.04, y: 0.08, z: 0.0, material: timeGoldMaterial }, // Neck left
        { x: 0.0, y: 0.08, z: 0.0, material: crystalWhiteMaterial }, // Neck center
        { x: 0.04, y: 0.08, z: 0.0, material: timeGoldMaterial }, // Neck right
        { x: 0.0, y: 0.04, z: 0.0, material: crystalWhiteMaterial }, // Flow center
        { x: 0.0, y: 0.0, z: 0.0, material: temporalPurpleMaterial }, // Time nexus
        { x: 0.0, y: -0.04, z: 0.0, material: crystalWhiteMaterial }, // Flow center
        
        // Lower funnel
        { x: -0.04, y: -0.08, z: 0.0, material: timeGoldMaterial }, // Lower neck left
        { x: 0.0, y: -0.08, z: 0.0, material: crystalWhiteMaterial }, // Lower neck center
        { x: 0.04, y: -0.08, z: 0.0, material: timeGoldMaterial }, // Lower neck right
        { x: -0.08, y: -0.12, z: 0.0, material: timeGoldMaterial }, // Lower left
        { x: -0.04, y: -0.12, z: 0.0, material: frozenBlueMaterial }, // Lower left-center
        { x: 0.0, y: -0.12, z: 0.0, material: crystalWhiteMaterial }, // Lower center
        { x: 0.04, y: -0.12, z: 0.0, material: frozenBlueMaterial }, // Lower right-center
        { x: 0.08, y: -0.12, z: 0.0, material: timeGoldMaterial }, // Lower right
        
        // Bottom chamber
        { x: -0.12, y: -0.16, z: 0.0, material: timeGoldMaterial }, // Bottom left
        { x: -0.08, y: -0.16, z: 0.0, material: timeGoldMaterial }, // Bottom left-center
        { x: -0.04, y: -0.16, z: 0.0, material: timeGoldMaterial }, // Bottom center-left
        { x: 0.0, y: -0.16, z: 0.0, material: crystalWhiteMaterial }, // Bottom center
        { x: 0.04, y: -0.16, z: 0.0, material: timeGoldMaterial }, // Bottom center-right
        { x: 0.08, y: -0.16, z: 0.0, material: timeGoldMaterial }, // Bottom right-center
        { x: 0.12, y: -0.16, z: 0.0, material: timeGoldMaterial } // Bottom right
    ];

    // Stasis Field (surrounding energy field)
    const stasisFieldVoxels = [
        // Outer ring
        { x: -0.20, y: 0.0, z: 0.0, material: stasisSilverMaterial }, // Left
        { x: 0.20, y: 0.0, z: 0.0, material: stasisSilverMaterial }, // Right
        { x: 0.0, y: 0.20, z: 0.0, material: stasisSilverMaterial }, // Top
        { x: 0.0, y: -0.20, z: 0.0, material: stasisSilverMaterial }, // Bottom
        { x: -0.14, y: 0.14, z: 0.0, material: stasisSilverMaterial }, // Top-left
        { x: 0.14, y: 0.14, z: 0.0, material: stasisSilverMaterial }, // Top-right
        { x: -0.14, y: -0.14, z: 0.0, material: stasisSilverMaterial }, // Bottom-left
        { x: 0.14, y: -0.14, z: 0.0, material: stasisSilverMaterial }, // Bottom-right
        
        // Middle ring
        { x: -0.16, y: 0.0, z: 0.0, material: chronoCyanMaterial }, // Left
        { x: 0.16, y: 0.0, z: 0.0, material: chronoCyanMaterial }, // Right
        { x: 0.0, y: 0.16, z: 0.0, material: chronoCyanMaterial }, // Top
        { x: 0.0, y: -0.16, z: 0.0, material: chronoCyanMaterial }, // Bottom
        
        // Energy nodes
        { x: -0.24, y: 0.0, z: 0.0, material: temporalPurpleMaterial }, // Far left
        { x: 0.24, y: 0.0, z: 0.0, material: temporalPurpleMaterial }, // Far right
        { x: 0.0, y: 0.24, z: 0.0, material: temporalPurpleMaterial }, // Far top
        { x: 0.0, y: -0.24, z: 0.0, material: temporalPurpleMaterial }, // Far bottom
        
        // Corner energy nodes
        { x: -0.17, y: 0.17, z: 0.0, material: temporalPurpleMaterial }, // Top-left corner
        { x: 0.17, y: 0.17, z: 0.0, material: temporalPurpleMaterial }, // Top-right corner
        { x: -0.17, y: -0.17, z: 0.0, material: temporalPurpleMaterial }, // Bottom-left corner
        { x: 0.17, y: -0.17, z: 0.0, material: temporalPurpleMaterial } // Bottom-right corner
    ];

    // Frozen Crystals (ice formations)
    const frozenCrystalsVoxels = [
        // Large crystals at corners
        { x: -0.28, y: 0.08, z: 0.04, material: crystalWhiteMaterial }, // Left crystal 1
        { x: -0.28, y: 0.04, z: 0.08, material: frozenBlueMaterial }, // Left crystal 2
        { x: -0.28, y: 0.0, z: 0.04, material: crystalWhiteMaterial }, // Left crystal 3
        { x: 0.28, y: 0.08, z: 0.04, material: crystalWhiteMaterial }, // Right crystal 1
        { x: 0.28, y: 0.04, z: 0.08, material: frozenBlueMaterial }, // Right crystal 2
        { x: 0.28, y: 0.0, z: 0.04, material: crystalWhiteMaterial }, // Right crystal 3
        
        { x: 0.08, y: 0.28, z: 0.04, material: crystalWhiteMaterial }, // Top crystal 1
        { x: 0.04, y: 0.28, z: 0.08, material: frozenBlueMaterial }, // Top crystal 2
        { x: 0.0, y: 0.28, z: 0.04, material: crystalWhiteMaterial }, // Top crystal 3
        { x: 0.08, y: -0.28, z: 0.04, material: crystalWhiteMaterial }, // Bottom crystal 1
        { x: 0.04, y: -0.28, z: 0.08, material: frozenBlueMaterial }, // Bottom crystal 2
        { x: 0.0, y: -0.28, z: 0.04, material: crystalWhiteMaterial }, // Bottom crystal 3
        
        // Medium crystals
        { x: -0.20, y: 0.20, z: 0.06, material: shimmerBlueMaterial }, // Top-left
        { x: 0.20, y: 0.20, z: 0.06, material: shimmerBlueMaterial }, // Top-right
        { x: -0.20, y: -0.20, z: 0.06, material: shimmerBlueMaterial }, // Bottom-left
        { x: 0.20, y: -0.20, z: 0.06, material: shimmerBlueMaterial }, // Bottom-right
        
        // Small crystals (scattered)
        { x: -0.32, y: 0.12, z: 0.02, material: crystalWhiteMaterial },
        { x: -0.32, y: -0.12, z: 0.02, material: crystalWhiteMaterial },
        { x: 0.32, y: 0.12, z: 0.02, material: crystalWhiteMaterial },
        { x: 0.32, y: -0.12, z: 0.02, material: crystalWhiteMaterial },
        { x: 0.12, y: 0.32, z: 0.02, material: crystalWhiteMaterial },
        { x: -0.12, y: 0.32, z: 0.02, material: crystalWhiteMaterial },
        { x: 0.12, y: -0.32, z: 0.02, material: crystalWhiteMaterial },
        { x: -0.12, y: -0.32, z: 0.02, material: crystalWhiteMaterial }
    ];

    // Time Distortion Effects (void spaces)
    const timeDistortionVoxels = [
        // Void rifts
        { x: -0.26, y: 0.0, z: -0.04, material: voidBlackMaterial }, // Left void
        { x: 0.26, y: 0.0, z: -0.04, material: voidBlackMaterial }, // Right void
        { x: 0.0, y: 0.26, z: -0.04, material: voidBlackMaterial }, // Top void
        { x: 0.0, y: -0.26, z: -0.04, material: voidBlackMaterial }, // Bottom void
        
        // Temporal tears
        { x: -0.18, y: 0.18, z: -0.06, material: voidBlackMaterial }, // Top-left tear
        { x: 0.18, y: 0.18, z: -0.06, material: voidBlackMaterial }, // Top-right tear
        { x: -0.18, y: -0.18, z: -0.06, material: voidBlackMaterial }, // Bottom-left tear
        { x: 0.18, y: -0.18, z: -0.06, material: voidBlackMaterial }, // Bottom-right tear
        
        // Distortion fields
        { x: -0.30, y: 0.0, z: -0.02, material: temporalPurpleMaterial }, // Left distortion
        { x: 0.30, y: 0.0, z: -0.02, material: temporalPurpleMaterial }, // Right distortion
        { x: 0.0, y: 0.30, z: -0.02, material: temporalPurpleMaterial }, // Top distortion
        { x: 0.0, y: -0.30, z: -0.02, material: temporalPurpleMaterial }, // Bottom distortion
        
        // Shimmer effects
        { x: -0.22, y: 0.22, z: -0.08, material: shimmerBlueMaterial },
        { x: 0.22, y: 0.22, z: -0.08, material: shimmerBlueMaterial },
        { x: -0.22, y: -0.22, z: -0.08, material: shimmerBlueMaterial },
        { x: 0.22, y: -0.22, z: -0.08, material: shimmerBlueMaterial }
    ];

    // Create temporal hourglass group
    const temporalHourglassGroup = new THREE.Group();
    temporalHourglassGroup.name = 'temporalHourglassGroup';

    // Add temporal hourglass voxels
    temporalHourglassVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.timePhase = index * 0.05; // Stagger animation
        temporalHourglassGroup.add(mesh);
    });

    // Create stasis field group
    const stasisFieldGroup = new THREE.Group();
    stasisFieldGroup.name = 'stasisFieldGroup';

    // Add stasis field voxels
    stasisFieldVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.stasisPhase = index * 0.1; // Stagger animation
        stasisFieldGroup.add(mesh);
    });

    // Create frozen crystals group
    const frozenCrystalsGroup = new THREE.Group();
    frozenCrystalsGroup.name = 'frozenCrystalsGroup';

    // Add frozen crystals voxels
    frozenCrystalsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.crystalPhase = index * 0.07; // Stagger animation
        frozenCrystalsGroup.add(mesh);
    });

    // Create time distortion group
    const timeDistortionGroup = new THREE.Group();
    timeDistortionGroup.name = 'timeDistortionGroup';

    // Add time distortion voxels
    timeDistortionVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.distortionPhase = index * 0.12; // Stagger animation
        timeDistortionGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(temporalHourglassGroup);
    cardGroup.add(stasisFieldGroup);
    cardGroup.add(frozenCrystalsGroup);
    cardGroup.add(timeDistortionGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        timeFlow: 0,
        stasisPulse: 0,
        crystalShimmer: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update time freeze card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateTimeFreezeCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.timeFlow += deltaTime * 1.5; // Time flow speed
    cardGroup.userData.stasisPulse += deltaTime * 3.0; // Stasis pulse speed
    cardGroup.userData.crystalShimmer += deltaTime * 4.0; // Crystal shimmer speed

    const time = cardGroup.userData.animationTime;
    const timeFlow = cardGroup.userData.timeFlow;
    const stasisPulse = cardGroup.userData.stasisPulse;
    const crystalShimmer = cardGroup.userData.crystalShimmer;

    // Animate temporal hourglass (time flowing)
    const temporalHourglassGroup = cardGroup.getObjectByName('temporalHourglassGroup');
    if (temporalHourglassGroup) {
        // Hourglass rotation (time turning)
        const hourglassRotation = Math.sin(timeFlow * 0.5) * 0.02;
        temporalHourglassGroup.rotation.z = hourglassRotation;
        
        // Individual time particle animation
        temporalHourglassGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.timePhase !== undefined) {
                const timeParticleTime = timeFlow + mesh.userData.timePhase;
                
                // Time flow motion
                const flowDown = Math.sin(timeParticleTime * 2.0) * 0.001;
                const timeWobble = Math.cos(timeParticleTime * 3.0) * 0.0005;
                
                mesh.position.x = mesh.userData.originalPosition.x + timeWobble;
                mesh.position.y = mesh.userData.originalPosition.y + flowDown;
                
                // Time energy glow
                const timeGlow = 1.0 + Math.sin(timeParticleTime * 4.0) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * timeGlow;
                }
                
                // Time particle opacity
                const timeOpacity = 0.9 + Math.sin(timeParticleTime * 5.0) * 0.1;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * timeOpacity;
                }
            }
        });
    }

    // Animate stasis field (energy pulsing)
    const stasisFieldGroup = cardGroup.getObjectByName('stasisFieldGroup');
    if (stasisFieldGroup) {
        stasisFieldGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.stasisPhase !== undefined) {
                const stasisTime = stasisPulse + mesh.userData.stasisPhase;
                
                // Stasis field pulsing
                const stasisPulseMotion = Math.sin(stasisTime * 3.0) * 0.002;
                const stasisRipple = Math.cos(stasisTime * 2.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + stasisRipple;
                mesh.position.y = mesh.userData.originalPosition.y + stasisRipple;
                mesh.position.z = mesh.userData.originalPosition.z + stasisPulseMotion;
                
                // Stasis energy intensity
                const stasisIntensity = 1.0 + Math.sin(stasisTime * 4.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * stasisIntensity;
                }
                
                // Stasis scale pulsing
                const stasisScale = 0.95 + Math.sin(stasisTime * 3.5) * 0.1;
                mesh.scale.setScalar(stasisScale);
            }
        });
    }

    // Animate frozen crystals (ice shimmering)
    const frozenCrystalsGroup = cardGroup.getObjectByName('frozenCrystalsGroup');
    if (frozenCrystalsGroup) {
        frozenCrystalsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.crystalPhase !== undefined) {
                const crystalTime = crystalShimmer + mesh.userData.crystalPhase;
                
                // Crystal shimmering
                const crystalShimmer = Math.sin(crystalTime * 6.0) * 0.003;
                const crystalTwinkle = Math.cos(crystalTime * 8.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + crystalShimmer;
                mesh.position.y = mesh.userData.originalPosition.y + crystalTwinkle;
                
                // Crystal glow intensity
                const crystalGlow = 1.0 + Math.sin(crystalTime * 7.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crystalGlow;
                }
                
                // Crystal opacity variation
                const crystalOpacity = 0.8 + Math.sin(crystalTime * 5.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * crystalOpacity;
                }
                
                // Crystal scale shimmering
                const crystalScale = 0.9 + Math.sin(crystalTime * 6.0) * 0.2;
                mesh.scale.setScalar(crystalScale);
            }
        });
    }

    // Animate time distortion (void effects)
    const timeDistortionGroup = cardGroup.getObjectByName('timeDistortionGroup');
    if (timeDistortionGroup) {
        timeDistortionGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.distortionPhase !== undefined) {
                const distortionTime = timeFlow * 0.8 + mesh.userData.distortionPhase;
                
                // Time distortion warping
                const distortionWarp = Math.sin(distortionTime * 5.0) * 0.004;
                const voidPull = Math.cos(distortionTime * 3.5) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + distortionWarp;
                mesh.position.y = mesh.userData.originalPosition.y + voidPull;
                
                // Distortion intensity fluctuation
                const distortionIntensity = 1.0 + Math.sin(distortionTime * 6.0) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * distortionIntensity;
                }
                
                // Distortion scale variation
                const distortionScale = 0.8 + Math.sin(distortionTime * 4.5) * 0.3;
                mesh.scale.setScalar(distortionScale);
            }
        });
    }

    // Overall time freeze presence (legendary time magic)
    const timePulse = 1 + Math.sin(time * 1.2) * 0.05;
    const timeShift = Math.cos(time * 2.0) * 0.0005;
    cardGroup.scale.setScalar(0.8 * timePulse);
    cardGroup.position.x += timeShift;
    cardGroup.position.z += timeShift * 0.6;
}

// Export the time freeze card data for the loot system
export const TIME_FREEZE_CARD_DATA = {
    name: "Time Freeze",
    description: 'Freezes all enemies in time for 8 seconds, stopping their movement and actions while allowing the player to move freely.',
    category: 'card',
    rarity: 'legendary',
    effect: 'time_freeze',
    effectValue: 8, // Duration in seconds
    createFunction: createTimeFreezeCard,
    updateFunction: updateTimeFreezeCardAnimation,
    voxelModel: 'time_freeze_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.8
    }
};

export default createTimeFreezeCard;