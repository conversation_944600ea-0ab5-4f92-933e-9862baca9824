import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Arcane Mastery Card Prefab
 * Creates enhanced magical abilities with arcane energy
 */

// Arcane mastery specific colors
const ARCANE_COLORS = {
    ARCANE_ORANGE: 0xFF6B35,     // Primary arcane orange
    MYSTIC_GOLD: 0xFFD700,       // Mystic golden energy
    SPELL_AMBER: 0xFFBF00,       // Spell-casting amber
    MAGIC_CRIMSON: 0xDC143C,     // Magic power crimson
    RITUAL_PURPLE: 0x8A2BE2,     // Ritual purple
    ESSENCE_WHITE: 0xFFFFFF,     // Pure magical essence
    RUNE_SILVER: 0xC0C0C0,       // Ancient rune silver
    POWER_RED: 0xFF4500          // Raw magical power red
};

/**
 * Create an arcane mastery card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The arcane mastery card 3D model
 */
export function createArcaneMasteryCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ArcaneMasteryCard';

    // Materials
    const arcaneOrangeMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.ARCANE_ORANGE,
        emissive: ARCANE_COLORS.ARCANE_ORANGE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const mysticGoldMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.MYSTIC_GOLD,
        emissive: ARCANE_COLORS.MYSTIC_GOLD,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.8
    });

    const spellAmberMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.SPELL_AMBER,
        emissive: ARCANE_COLORS.SPELL_AMBER,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const magicCrimsonMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.MAGIC_CRIMSON,
        emissive: ARCANE_COLORS.MAGIC_CRIMSON,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const ritualPurpleMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.RITUAL_PURPLE,
        emissive: ARCANE_COLORS.RITUAL_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const essenceWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.ESSENCE_WHITE,
        emissive: ARCANE_COLORS.ESSENCE_WHITE,
        emissiveIntensity: 1.3,
        transparent: true,
        opacity: 0.5
    });

    const runeSilverMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.RUNE_SILVER,
        emissive: ARCANE_COLORS.RUNE_SILVER,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const powerRedMaterial = new THREE.MeshLambertMaterial({
        color: ARCANE_COLORS.POWER_RED,
        emissive: ARCANE_COLORS.POWER_RED,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create central arcane focus (magical core)
    const arcaneFocusVoxels = [
        // Central magical core
        { x: 0, y: 0, z: 0, material: essenceWhiteMaterial },
        { x: -1, y: 0, z: 0, material: mysticGoldMaterial },
        { x: 1, y: 0, z: 0, material: mysticGoldMaterial },
        { x: 0, y: -1, z: 0, material: mysticGoldMaterial },
        { x: 0, y: 1, z: 0, material: mysticGoldMaterial },
        
        // Inner energy ring
        { x: -1, y: 1, z: 0, material: arcaneOrangeMaterial },
        { x: 1, y: 1, z: 0, material: arcaneOrangeMaterial },
        { x: -1, y: -1, z: 0, material: arcaneOrangeMaterial },
        { x: 1, y: -1, z: 0, material: arcaneOrangeMaterial },
        
        // Power amplification core
        { x: 0, y: 2, z: 0, material: powerRedMaterial },
        { x: 0, y: -2, z: 0, material: powerRedMaterial },
        { x: 2, y: 0, z: 0, material: powerRedMaterial },
        { x: -2, y: 0, z: 0, material: powerRedMaterial }
    ];

    // Create arcane rune circle (mystical symbols)
    const arcaneRuneVoxels = [
        // Outer rune circle
        { x: -3, y: 3, z: 0, material: runeSilverMaterial },
        { x: 0, y: 4, z: 0, material: runeSilverMaterial },
        { x: 3, y: 3, z: 0, material: runeSilverMaterial },
        { x: 4, y: 0, z: 0, material: runeSilverMaterial },
        { x: 3, y: -3, z: 0, material: runeSilverMaterial },
        { x: 0, y: -4, z: 0, material: runeSilverMaterial },
        { x: -3, y: -3, z: 0, material: runeSilverMaterial },
        { x: -4, y: 0, z: 0, material: runeSilverMaterial },
        
        // Intermediate rune symbols
        { x: -2, y: 3, z: 0, material: ritualPurpleMaterial },
        { x: 2, y: 3, z: 0, material: ritualPurpleMaterial },
        { x: 3, y: 2, z: 0, material: ritualPurpleMaterial },
        { x: 3, y: -2, z: 0, material: ritualPurpleMaterial },
        { x: 2, y: -3, z: 0, material: ritualPurpleMaterial },
        { x: -2, y: -3, z: 0, material: ritualPurpleMaterial },
        { x: -3, y: -2, z: 0, material: ritualPurpleMaterial },
        { x: -3, y: 2, z: 0, material: ritualPurpleMaterial },
        
        // Advanced rune patterns
        { x: -1, y: 3, z: 0, material: spellAmberMaterial },
        { x: 1, y: 3, z: 0, material: spellAmberMaterial },
        { x: 3, y: 1, z: 0, material: spellAmberMaterial },
        { x: 3, y: -1, z: 0, material: spellAmberMaterial },
        { x: 1, y: -3, z: 0, material: spellAmberMaterial },
        { x: -1, y: -3, z: 0, material: spellAmberMaterial },
        { x: -3, y: -1, z: 0, material: spellAmberMaterial },
        { x: -3, y: 1, z: 0, material: spellAmberMaterial }
    ];

    // Create magical energy streams
    const energyStreamVoxels = [
        // Primary energy streams (from corners)
        { x: -5, y: 5, z: 0, material: mysticGoldMaterial },
        { x: -4, y: 4, z: 0, material: arcaneOrangeMaterial },
        { x: 5, y: 5, z: 0, material: mysticGoldMaterial },
        { x: 4, y: 4, z: 0, material: arcaneOrangeMaterial },
        { x: 5, y: -5, z: 0, material: mysticGoldMaterial },
        { x: 4, y: -4, z: 0, material: arcaneOrangeMaterial },
        { x: -5, y: -5, z: 0, material: mysticGoldMaterial },
        { x: -4, y: -4, z: 0, material: arcaneOrangeMaterial },
        
        // Secondary energy flows
        { x: -5, y: 0, z: 0, material: powerRedMaterial },
        { x: -4, y: 1, z: 0, material: spellAmberMaterial },
        { x: -4, y: -1, z: 0, material: spellAmberMaterial },
        { x: 5, y: 0, z: 0, material: powerRedMaterial },
        { x: 4, y: 1, z: 0, material: spellAmberMaterial },
        { x: 4, y: -1, z: 0, material: spellAmberMaterial },
        { x: 0, y: 5, z: 0, material: powerRedMaterial },
        { x: 1, y: 4, z: 0, material: spellAmberMaterial },
        { x: -1, y: 4, z: 0, material: spellAmberMaterial },
        { x: 0, y: -5, z: 0, material: powerRedMaterial },
        { x: 1, y: -4, z: 0, material: spellAmberMaterial },
        { x: -1, y: -4, z: 0, material: spellAmberMaterial }
    ];

    // Create mastery enhancement aura
    const masteryAuraVoxels = [
        // Outer mastery aura
        { x: -6, y: 3, z: 0, material: essenceWhiteMaterial },
        { x: -6, y: 0, z: 0, material: essenceWhiteMaterial },
        { x: -6, y: -3, z: 0, material: essenceWhiteMaterial },
        { x: 6, y: 3, z: 0, material: essenceWhiteMaterial },
        { x: 6, y: 0, z: 0, material: essenceWhiteMaterial },
        { x: 6, y: -3, z: 0, material: essenceWhiteMaterial },
        { x: 3, y: 6, z: 0, material: essenceWhiteMaterial },
        { x: 0, y: 6, z: 0, material: essenceWhiteMaterial },
        { x: -3, y: 6, z: 0, material: essenceWhiteMaterial },
        { x: 3, y: -6, z: 0, material: essenceWhiteMaterial },
        { x: 0, y: -6, z: 0, material: essenceWhiteMaterial },
        { x: -3, y: -6, z: 0, material: essenceWhiteMaterial },
        
        // Power enhancement markers
        { x: -6, y: 6, z: 0, material: magicCrimsonMaterial },
        { x: 6, y: 6, z: 0, material: magicCrimsonMaterial },
        { x: 6, y: -6, z: 0, material: magicCrimsonMaterial },
        { x: -6, y: -6, z: 0, material: magicCrimsonMaterial },
        
        // Enhancement connectivity
        { x: -5, y: 6, z: 0, material: ritualPurpleMaterial },
        { x: 5, y: 6, z: 0, material: ritualPurpleMaterial },
        { x: 6, y: 5, z: 0, material: ritualPurpleMaterial },
        { x: 6, y: -5, z: 0, material: ritualPurpleMaterial },
        { x: 5, y: -6, z: 0, material: ritualPurpleMaterial },
        { x: -5, y: -6, z: 0, material: ritualPurpleMaterial },
        { x: -6, y: -5, z: 0, material: ritualPurpleMaterial },
        { x: -6, y: 5, z: 0, material: ritualPurpleMaterial }
    ];

    // Create all voxels
    [...arcaneFocusVoxels, ...arcaneRuneVoxels, ...energyStreamVoxels, ...masteryAuraVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22, // Compact spacing
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (arcaneFocusVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'focus';
        } else if (arcaneRuneVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'rune';
        } else if (energyStreamVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'stream';
        } else {
            mesh.userData.voxelType = 'aura';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        focusPulse: 0,
        runeRotation: 0,
        energyFlow: 0,
        auraEnhancement: 0,
        arcaneOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update arcane mastery card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateArcaneMasteryCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.focusPulse += deltaTime * 6.0; // Powerful focus pulsing
    cardGroup.userData.runeRotation += deltaTime * 1.5; // Steady rune rotation
    cardGroup.userData.energyFlow += deltaTime * 8.0; // Fast energy flow
    cardGroup.userData.auraEnhancement += deltaTime * 3.0; // Enhancement aura

    const time = cardGroup.userData.animationTime;
    const focusPulse = cardGroup.userData.focusPulse;
    const runeRotation = cardGroup.userData.runeRotation;
    const energyFlow = cardGroup.userData.energyFlow;
    const auraEnhancement = cardGroup.userData.auraEnhancement;
    const arcaneOffset = cardGroup.userData.arcaneOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'focus':
                    // Magical focus pulses with intense power
                    const focusIntensity = 0.8 + Math.sin(focusPulse * 3.0 + arcaneOffset) * 0.9;
                    const focusEnergy = Math.sin(focusPulse * 5.0) * 0.006;
                    
                    child.material.emissiveIntensity = baseEmissive * focusIntensity;
                    child.material.opacity = baseOpacity * (0.8 + focusIntensity * 0.2);
                    child.position.x += focusEnergy;
                    child.position.y += focusEnergy * 0.7;
                    break;

                case 'rune':
                    // Arcane runes rotate and glow
                    const runeIntensity = 0.7 + Math.sin(runeRotation * 2.0 + child.position.x * 0.3 + child.position.y * 0.3 + arcaneOffset) * 0.8;
                    const runeAngle = runeRotation + arcaneOffset;
                    const runeRadius = Math.sqrt(child.position.x * child.position.x + child.position.y * child.position.y);
                    
                    child.material.emissiveIntensity = baseEmissive * runeIntensity;
                    child.material.opacity = baseOpacity * runeIntensity;
                    
                    // Rotate around center
                    if (runeRadius > 0) {
                        const newAngle = Math.atan2(child.position.y, child.position.x) + deltaTime * 1.5;
                        child.position.x = Math.cos(newAngle) * runeRadius;
                        child.position.y = Math.sin(newAngle) * runeRadius;
                    }
                    break;

                case 'stream':
                    // Energy streams flow toward center
                    const streamIntensity = 0.6 + Math.sin(energyFlow * 4.0 + child.position.x * 0.5 + child.position.y * 0.4 + arcaneOffset) * 1.0;
                    const streamMotion = Math.cos(energyFlow * 6.0 + arcaneOffset) * 0.012;
                    
                    child.material.emissiveIntensity = baseEmissive * streamIntensity;
                    child.material.opacity = baseOpacity * streamIntensity;
                    
                    // Flow toward center
                    const centerDirection = new THREE.Vector2(-child.position.x, -child.position.y).normalize();
                    child.position.x += centerDirection.x * streamMotion * 0.3;
                    child.position.y += centerDirection.y * streamMotion * 0.3;
                    break;

                case 'aura':
                    // Mastery aura enhances and expands
                    const auraIntensity = 0.5 + Math.sin(auraEnhancement * 2.5 + arcaneOffset) * 0.9;
                    const auraExpansion = Math.sin(auraEnhancement * 3.0) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * auraIntensity;
                    child.material.opacity = baseOpacity * auraIntensity;
                    child.position.x += auraExpansion * (child.position.x > 0 ? 1 : -1);
                    child.position.y += auraExpansion * (child.position.y > 0 ? 1 : -1);
                    break;
            }
        }
    });

    // Overall arcane mastery effect
    const arcanePower = Math.sin(time * 4.5 + arcaneOffset) * 0.008;
    cardGroup.position.x += arcanePower;
    cardGroup.position.y += arcanePower * 0.6;
    
    // Continuous rotation for magical energy
    cardGroup.rotation.z += deltaTime * 0.8;
}

// Export the arcane mastery card data for the loot system
export const ARCANE_MASTERY_CARD_DATA = {
    name: 'Arcane Mastery',
    description: 'Grants temporary mastery over arcane forces. All card effects are enhanced, casting costs reduced, and magical energy regenerates faster.',
    category: 'card',
    rarity: 'legendary',
    effect: 'arcane_mastery',
    effectValue: 30,
    createFunction: createArcaneMasteryCard,
    updateFunction: updateArcaneMasteryCardAnimation,
    voxelModel: 'arcane_mastery_card',
    glow: {
        color: 0xFF6B35,
        intensity: 2.0
    }
};

export default createArcaneMasteryCard;