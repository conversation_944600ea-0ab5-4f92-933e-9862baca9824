import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Celestial Storm Card Prefab
 * Creates divine lightning with healing energy
 */

// Celestial storm specific colors
const CELESTIAL_COLORS = {
    CELESTIAL_GOLD: 0xFFD700,    // Divine gold
    LIGHTNING_WHITE: 0xFFFFFF,   // Pure lightning white
    HEAVENLY_BLUE: 0x87CEEB,     // Heavenly sky blue
    DIVINE_SILVER: 0xC0C0C0,     // Divine silver
    HOLY_YELLOW: 0xFFFF00,       // Holy light yellow
    CLOUD_GRAY: 0xB0C4DE,        // Cloud gray
    RADIANCE_CYAN: 0x00FFFF      // Divine radiance cyan
};

/**
 * Create a celestial storm card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The celestial storm card 3D model
 */
export function createCelestialStormCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'CelestialStormCard';

    // Materials
    const celestialGoldMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.CELESTIAL_GOLD,
        emissive: CELESTIAL_COLORS.CELESTIAL_GOLD,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.9
    });

    const lightningWhiteMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.LIGHTNING_WHITE,
        emissive: CELESTIAL_COLORS.LIGHTNING_WHITE,
        emissiveIntensity: 1.5,
        transparent: true,
        opacity: 0.8
    });

    const heavenlyBlueMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.HEAVENLY_BLUE,
        emissive: CELESTIAL_COLORS.HEAVENLY_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const divineSilverMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.DIVINE_SILVER,
        emissive: CELESTIAL_COLORS.DIVINE_SILVER,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const holyYellowMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.HOLY_YELLOW,
        emissive: CELESTIAL_COLORS.HOLY_YELLOW,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const cloudGrayMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.CLOUD_GRAY,
        emissive: CELESTIAL_COLORS.CLOUD_GRAY,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const radianceCyanMaterial = new THREE.MeshLambertMaterial({
        color: CELESTIAL_COLORS.RADIANCE_CYAN,
        emissive: CELESTIAL_COLORS.RADIANCE_CYAN,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create celestial clouds
    const celestialCloudVoxels = [
        // Upper clouds
        { x: -4, y: 5, z: 0, material: heavenlyBlueMaterial },
        { x: -2, y: 6, z: 0, material: cloudGrayMaterial },
        { x: 0, y: 6, z: 0, material: heavenlyBlueMaterial },
        { x: 2, y: 6, z: 0, material: cloudGrayMaterial },
        { x: 4, y: 5, z: 0, material: heavenlyBlueMaterial },
        { x: -3, y: 5, z: 0, material: cloudGrayMaterial },
        { x: -1, y: 5, z: 0, material: heavenlyBlueMaterial },
        { x: 1, y: 5, z: 0, material: cloudGrayMaterial },
        { x: 3, y: 5, z: 0, material: heavenlyBlueMaterial },
        
        // Lower clouds
        { x: -4, y: -5, z: 0, material: heavenlyBlueMaterial },
        { x: -2, y: -6, z: 0, material: cloudGrayMaterial },
        { x: 0, y: -6, z: 0, material: heavenlyBlueMaterial },
        { x: 2, y: -6, z: 0, material: cloudGrayMaterial },
        { x: 4, y: -5, z: 0, material: heavenlyBlueMaterial },
        { x: -3, y: -5, z: 0, material: cloudGrayMaterial },
        { x: -1, y: -5, z: 0, material: heavenlyBlueMaterial },
        { x: 1, y: -5, z: 0, material: cloudGrayMaterial },
        { x: 3, y: -5, z: 0, material: heavenlyBlueMaterial }
    ];

    // Create lightning bolts
    const lightningBoltVoxels = [
        // Central lightning bolt
        { x: 0, y: 4, z: 0, material: lightningWhiteMaterial },
        { x: 0, y: 3, z: 0, material: celestialGoldMaterial },
        { x: 0, y: 2, z: 0, material: lightningWhiteMaterial },
        { x: 0, y: 1, z: 0, material: celestialGoldMaterial },
        { x: 0, y: 0, z: 0, material: lightningWhiteMaterial },
        { x: 0, y: -1, z: 0, material: celestialGoldMaterial },
        { x: 0, y: -2, z: 0, material: lightningWhiteMaterial },
        { x: 0, y: -3, z: 0, material: celestialGoldMaterial },
        { x: 0, y: -4, z: 0, material: lightningWhiteMaterial },
        
        // Side lightning bolts
        { x: -3, y: 3, z: 0, material: holyYellowMaterial },
        { x: -3, y: 2, z: 0, material: lightningWhiteMaterial },
        { x: -3, y: 1, z: 0, material: holyYellowMaterial },
        { x: -3, y: 0, z: 0, material: lightningWhiteMaterial },
        { x: -3, y: -1, z: 0, material: holyYellowMaterial },
        { x: -3, y: -2, z: 0, material: lightningWhiteMaterial },
        
        { x: 3, y: 3, z: 0, material: holyYellowMaterial },
        { x: 3, y: 2, z: 0, material: lightningWhiteMaterial },
        { x: 3, y: 1, z: 0, material: holyYellowMaterial },
        { x: 3, y: 0, z: 0, material: lightningWhiteMaterial },
        { x: 3, y: -1, z: 0, material: holyYellowMaterial },
        { x: 3, y: -2, z: 0, material: lightningWhiteMaterial },
        
        // Branch lightning
        { x: -1, y: 3, z: 0, material: divineSilverMaterial },
        { x: 1, y: 3, z: 0, material: divineSilverMaterial },
        { x: -1, y: -3, z: 0, material: divineSilverMaterial },
        { x: 1, y: -3, z: 0, material: divineSilverMaterial }
    ];

    // Create divine energy
    const divineEnergyVoxels = [
        // Energy radiating from center
        { x: -5, y: 2, z: 0, material: celestialGoldMaterial },
        { x: -5, y: 0, z: 0, material: radianceCyanMaterial },
        { x: -5, y: -2, z: 0, material: celestialGoldMaterial },
        { x: 5, y: 2, z: 0, material: celestialGoldMaterial },
        { x: 5, y: 0, z: 0, material: radianceCyanMaterial },
        { x: 5, y: -2, z: 0, material: celestialGoldMaterial },
        { x: 2, y: 5, z: 0, material: celestialGoldMaterial },
        { x: 0, y: 5, z: 0, material: radianceCyanMaterial },
        { x: -2, y: 5, z: 0, material: celestialGoldMaterial },
        { x: 2, y: -5, z: 0, material: celestialGoldMaterial },
        { x: 0, y: -5, z: 0, material: radianceCyanMaterial },
        { x: -2, y: -5, z: 0, material: celestialGoldMaterial },
        
        // Diagonal energy
        { x: -4, y: 4, z: 0, material: divineSilverMaterial },
        { x: 4, y: 4, z: 0, material: divineSilverMaterial },
        { x: 4, y: -4, z: 0, material: divineSilverMaterial },
        { x: -4, y: -4, z: 0, material: divineSilverMaterial },
        { x: -2, y: 4, z: 0, material: radianceCyanMaterial },
        { x: 2, y: 4, z: 0, material: radianceCyanMaterial },
        { x: 4, y: 2, z: 0, material: radianceCyanMaterial },
        { x: 4, y: -2, z: 0, material: radianceCyanMaterial },
        { x: 2, y: -4, z: 0, material: radianceCyanMaterial },
        { x: -2, y: -4, z: 0, material: radianceCyanMaterial },
        { x: -4, y: -2, z: 0, material: radianceCyanMaterial },
        { x: -4, y: 2, z: 0, material: radianceCyanMaterial }
    ];

    // Create all voxels
    [...celestialCloudVoxels, ...lightningBoltVoxels, ...divineEnergyVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22,
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (celestialCloudVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'cloud';
        } else if (lightningBoltVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'lightning';
        } else {
            mesh.userData.voxelType = 'energy';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        cloudDrift: 0,
        lightningStrike: 0,
        divineRadiance: 0,
        celestialOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update celestial storm card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateCelestialStormCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.cloudDrift += deltaTime * 2.0;
    cardGroup.userData.lightningStrike += deltaTime * 7.0;
    cardGroup.userData.divineRadiance += deltaTime * 4.5;

    const time = cardGroup.userData.animationTime;
    const cloudDrift = cardGroup.userData.cloudDrift;
    const lightningStrike = cardGroup.userData.lightningStrike;
    const divineRadiance = cardGroup.userData.divineRadiance;
    const celestialOffset = cardGroup.userData.celestialOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'cloud':
                    // Clouds drift gently
                    const cloudIntensity = 0.6 + Math.sin(cloudDrift * 2.0 + child.position.x * 0.2 + celestialOffset) * 0.5;
                    const cloudMotion = Math.sin(cloudDrift * 1.5 + celestialOffset) * 0.006;
                    
                    child.material.emissiveIntensity = baseEmissive * cloudIntensity;
                    child.material.opacity = baseOpacity * cloudIntensity;
                    child.position.x += cloudMotion;
                    child.position.y += cloudMotion * 0.3;
                    break;

                case 'lightning':
                    // Lightning strikes rapidly
                    const lightningIntensity = 0.7 + Math.sin(lightningStrike * 5.0 + child.position.y * 0.8 + celestialOffset) * 1.0;
                    const lightningFlash = Math.sin(lightningStrike * 8.0) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * lightningIntensity;
                    child.material.opacity = baseOpacity * (0.6 + lightningIntensity * 0.4);
                    child.position.x += lightningFlash;
                    child.position.y += lightningFlash * 0.5;
                    break;

                case 'energy':
                    // Divine energy radiates outward
                    const energyIntensity = 0.8 + Math.sin(divineRadiance * 3.0 + child.position.x * 0.4 + child.position.y * 0.3 + celestialOffset) * 0.7;
                    const energyPulse = Math.cos(divineRadiance * 4.0 + celestialOffset) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * energyIntensity;
                    child.material.opacity = baseOpacity * energyIntensity;
                    child.position.x += energyPulse * (child.position.x > 0 ? 1 : -1);
                    child.position.y += energyPulse * (child.position.y > 0 ? 1 : -1);
                    break;
            }
        }
    });

    // Overall celestial effect
    const celestialPulse = Math.sin(time * 3.5 + celestialOffset) * 0.007;
    cardGroup.position.x += celestialPulse;
    cardGroup.position.y += celestialPulse * 0.8;
}

// Export the celestial storm card data for the loot system
export const CELESTIAL_STORM_CARD_DATA = {
    name: 'Celestial Storm',
    description: 'Calls down divine lightning from the heavens. Multiple bolts of celestial energy strike all enemies while healing the caster.',
    category: 'card',
    rarity: 'epic',
    effect: 'celestial_storm',
    effectValue: 65,
    createFunction: createCelestialStormCard,
    updateFunction: updateCelestialStormCardAnimation,
    voxelModel: 'celestial_storm_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.8
    }
};

export default createCelestialStormCard;