import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Spectral Guardian Card Prefab
 * Creates a ghostly companion guardian with ethereal presence
 */

// Spectral guardian specific colors
const SPECTRAL_COLORS = {
    SPIRIT_BLUE: 0x4169E1,      // Primary spectral blue
    ETHEREAL_WHITE: 0xFFFFFF,   // Pure ethereal energy
    GHOST_CYAN: 0x00FFFF,       // Ghostly cyan glow
    SOUL_PURPLE: 0x8A2BE2,      // Soul energy purple
    MYSTIC_SILVER: 0xC0C0C0,    // Mystic silver
    ASTRAL_INDIGO: 0x4B0082,    // Deep astral indigo
    PHANTOM_GRAY: 0x696969,     // Phantom gray
    GUARDIAN_GOLD: 0xFFD700     // Guardian divine gold
};

/**
 * Create a spectral guardian card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The spectral guardian card 3D model
 */
export function createSpectralGuardianCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SpectralGuardianCard';

    // Materials
    const spiritBlueMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.SPIRIT_BLUE,
        emissive: SPECTRAL_COLORS.SPIRIT_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const etherealWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.ETHEREAL_WHITE,
        emissive: SPECTRAL_COLORS.ETHEREAL_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.6
    });

    const ghostCyanMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.GHOST_CYAN,
        emissive: SPECTRAL_COLORS.GHOST_CYAN,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.5
    });

    const soulPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.SOUL_PURPLE,
        emissive: SPECTRAL_COLORS.SOUL_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const mysticSilverMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.MYSTIC_SILVER,
        emissive: SPECTRAL_COLORS.MYSTIC_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const astralIndigoMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.ASTRAL_INDIGO,
        emissive: SPECTRAL_COLORS.ASTRAL_INDIGO,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const guardianGoldMaterial = new THREE.MeshLambertMaterial({
        color: SPECTRAL_COLORS.GUARDIAN_GOLD,
        emissive: SPECTRAL_COLORS.GUARDIAN_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Guardian figure (humanoid form)
    const guardianBodyVoxels = [
        // Head (detailed)
        { x: 0, y: 3, z: 0, material: etherealWhiteMaterial },
        { x: -1, y: 3, z: 0, material: spiritBlueMaterial },
        { x: 1, y: 3, z: 0, material: spiritBlueMaterial },
        { x: 0, y: 4, z: 0, material: ghostCyanMaterial },

        // Torso
        { x: 0, y: 2, z: 0, material: spiritBlueMaterial },
        { x: 0, y: 1, z: 0, material: spiritBlueMaterial },
        { x: 0, y: 0, z: 0, material: astralIndigoMaterial },
        { x: -1, y: 2, z: 0, material: soulPurpleMaterial },
        { x: 1, y: 2, z: 0, material: soulPurpleMaterial },

        // Arms
        { x: -2, y: 2, z: 0, material: mysticSilverMaterial },
        { x: 2, y: 2, z: 0, material: mysticSilverMaterial },
        { x: -2, y: 1, z: 0, material: ghostCyanMaterial },
        { x: 2, y: 1, z: 0, material: ghostCyanMaterial },

        // Legs
        { x: -1, y: -1, z: 0, material: astralIndigoMaterial },
        { x: 1, y: -1, z: 0, material: astralIndigoMaterial },
        { x: -1, y: -2, z: 0, material: soulPurpleMaterial },
        { x: 1, y: -2, z: 0, material: soulPurpleMaterial }
    ];

    // Guardian cape/cloak
    const guardianCloakVoxels = [
        // Upper cape
        { x: -2, y: 3, z: 0, material: astralIndigoMaterial },
        { x: 2, y: 3, z: 0, material: astralIndigoMaterial },
        { x: -3, y: 2, z: 0, material: soulPurpleMaterial },
        { x: 3, y: 2, z: 0, material: soulPurpleMaterial },

        // Flowing cape
        { x: -2, y: 0, z: 0, material: mysticSilverMaterial },
        { x: 2, y: 0, z: 0, material: mysticSilverMaterial },
        { x: -3, y: -1, z: 0, material: ghostCyanMaterial },
        { x: 3, y: -1, z: 0, material: ghostCyanMaterial },
        { x: -2, y: -2, z: 0, material: spiritBlueMaterial },
        { x: 2, y: -2, z: 0, material: spiritBlueMaterial }
    ];

    // Guardian weapon/staff
    const guardianWeaponVoxels = [
        // Staff shaft
        { x: -3, y: 3, z: 0, material: guardianGoldMaterial },
        { x: -3, y: 4, z: 0, material: guardianGoldMaterial },
        { x: -3, y: 5, z: 0, material: guardianGoldMaterial },

        // Staff head/crystal
        { x: -3, y: 6, z: 0, material: etherealWhiteMaterial },
        { x: -4, y: 6, z: 0, material: ghostCyanMaterial },
        { x: -2, y: 6, z: 0, material: ghostCyanMaterial },
        { x: -3, y: 7, z: 0, material: spiritBlueMaterial }
    ];

    // Spectral aura around guardian
    const spectralAuraVoxels = [
        // Inner aura
        { x: 0, y: 5, z: 0, material: etherealWhiteMaterial },
        { x: -4, y: 1, z: 0, material: ghostCyanMaterial },
        { x: 4, y: 1, z: 0, material: ghostCyanMaterial },
        { x: 0, y: -3, z: 0, material: spiritBlueMaterial },

        // Outer aura particles
        { x: -4, y: 4, z: 0, material: mysticSilverMaterial },
        { x: 4, y: 4, z: 0, material: mysticSilverMaterial },
        { x: -5, y: 0, z: 0, material: soulPurpleMaterial },
        { x: 5, y: 0, z: 0, material: soulPurpleMaterial },
        { x: -3, y: -3, z: 0, material: astralIndigoMaterial },
        { x: 3, y: -3, z: 0, material: astralIndigoMaterial },

        // Floating spirit orbs
        { x: -5, y: 3, z: 0, material: etherealWhiteMaterial },
        { x: 5, y: 3, z: 0, material: etherealWhiteMaterial },
        { x: -4, y: -2, z: 0, material: ghostCyanMaterial },
        { x: 4, y: -2, z: 0, material: ghostCyanMaterial }
    ];

    // Guardian shield
    const guardianShieldVoxels = [
        // Shield center
        { x: 3, y: 3, z: 0, material: guardianGoldMaterial },
        { x: 4, y: 3, z: 0, material: mysticSilverMaterial },
        { x: 3, y: 4, z: 0, material: mysticSilverMaterial },
        { x: 4, y: 4, z: 0, material: etherealWhiteMaterial },

        // Shield edges
        { x: 5, y: 3, z: 0, material: spiritBlueMaterial },
        { x: 3, y: 5, z: 0, material: spiritBlueMaterial },
        { x: 4, y: 5, z: 0, material: ghostCyanMaterial },
        { x: 5, y: 4, z: 0, material: ghostCyanMaterial }
    ];

    // Create all voxels
    [...guardianBodyVoxels, ...guardianCloakVoxels, ...guardianWeaponVoxels, 
     ...spectralAuraVoxels, ...guardianShieldVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.4, // Compact the layout
            (voxel.y - 1) * VOXEL_SIZE * 0.4, // Center vertically
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (guardianBodyVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'body';
        } else if (guardianCloakVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'cloak';
        } else if (guardianWeaponVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'weapon';
        } else if (spectralAuraVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'aura';
        } else {
            mesh.userData.voxelType = 'shield';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        spectralPhase: 0,
        guardianFloat: 0,
        auraSwirl: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update spectral guardian card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSpectralGuardianCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.spectralPhase += deltaTime * 2.5;
    cardGroup.userData.guardianFloat += deltaTime * 1.8;
    cardGroup.userData.auraSwirl += deltaTime * 3.2;

    const time = cardGroup.userData.animationTime;
    const spectralPhase = cardGroup.userData.spectralPhase;
    const guardianFloat = cardGroup.userData.guardianFloat;
    const auraSwirl = cardGroup.userData.auraSwirl;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'body':
                    // Guardian body phases in and out of visibility
                    const bodyPhase = 0.5 + Math.sin(spectralPhase * 2.0 + child.position.y) * 0.4;
                    const bodyFloat = Math.sin(guardianFloat + child.position.y * 0.5) * 0.01;
                    
                    child.material.opacity = baseOpacity * bodyPhase;
                    child.material.emissiveIntensity = baseEmissive * bodyPhase;
                    child.position.z = bodyFloat;
                    break;

                case 'cloak':
                    // Cloak flows ethereally
                    const cloakFlow = 0.4 + Math.sin(spectralPhase * 1.5 + child.position.x) * 0.5;
                    const cloakSway = Math.sin(guardianFloat * 0.8 + child.position.x) * 0.015;
                    
                    child.material.opacity = baseOpacity * cloakFlow;
                    child.material.emissiveIntensity = baseEmissive * cloakFlow;
                    child.position.x += cloakSway;
                    break;

                case 'weapon':
                    // Weapon glows with guardian power
                    const weaponGlow = 0.7 + Math.sin(spectralPhase * 3.0 + child.position.y * 2) * 0.5;
                    child.material.emissiveIntensity = baseEmissive * weaponGlow;
                    break;

                case 'aura':
                    // Aura swirls and pulses around guardian
                    const auraIntensity = 0.3 + Math.sin(auraSwirl + child.position.x + child.position.y) * 0.7;
                    const auraMotion = Math.sin(auraSwirl * 0.5 + child.position.x * 2) * 0.02;
                    
                    child.material.opacity = baseOpacity * auraIntensity;
                    child.material.emissiveIntensity = baseEmissive * auraIntensity;
                    child.position.x += auraMotion;
                    child.position.y += auraMotion * 0.5;
                    break;

                case 'shield':
                    // Shield gleams protectively
                    const shieldGleam = 0.8 + Math.sin(spectralPhase * 2.5 + child.position.x + child.position.y) * 0.4;
                    child.material.emissiveIntensity = baseEmissive * shieldGleam;
                    break;
            }
        }
    });

    // Overall guardian floating motion
    const overallFloat = Math.sin(guardianFloat * 0.6) * 0.008;
    cardGroup.position.y += overallFloat;
    
    // Subtle spectral rotation
    cardGroup.rotation.z = Math.sin(spectralPhase * 0.3) * 0.05;
}

// Export the spectral guardian card data for the loot system
export const SPECTRAL_GUARDIAN_CARD_DATA = {
    name: 'Spectral Guardian',
    description: 'Summons a ghostly companion that follows and protects you. The guardian blocks attacks, retaliates against enemies, and provides ethereal guidance through dangerous areas.',
    category: 'card',
    rarity: 'epic',
    effect: 'spectral_guardian',
    effectValue: 60,
    createFunction: createSpectralGuardianCard,
    updateFunction: updateSpectralGuardianCardAnimation,
    voxelModel: 'spectral_guardian_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.5
    }
};

export default createSpectralGuardianCard;