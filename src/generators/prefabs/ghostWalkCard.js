import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Ghost Walk Card Prefab
 * Creates ethereal transformation that grants phase ability and stealth
 */

// Ghost walk specific colors
const GHOST_COLORS = {
    SPECTRAL_BLUE: 0x4169E1,         // Main ghostly form
    ETHEREAL_CYAN: 0x00BFFF,         // Ghost aura and energy
    PHANTOM_WHITE: 0xF0F8FF,         // Ghost highlights and mist
    SPIRIT_PURPLE: 0x9932CC,         // Spiritual energy core
    SHADOW_GRAY: 0x696969,           // Shadow elements
    MISTY_SILVER: 0xC0C0C0,          // Mist and fog effects
    PALE_BLUE: 0xB0E0E6,             // Translucent ghost body
    VOID_BLACK: 0x191970             // Deep shadow voids
};

/**
 * Create a ghost walk card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The ghost walk card 3D model
 */
export function createGhostWalkCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'GhostWalkCard';

    // Ghost walk materials
    const spectralBlueMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.SPECTRAL_BLUE,
        emissive: GHOST_COLORS.SPECTRAL_BLUE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.7
    });

    const etherealCyanMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.ETHEREAL_CYAN,
        emissive: GHOST_COLORS.ETHEREAL_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const phantomWhiteMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.PHANTOM_WHITE,
        emissive: GHOST_COLORS.PHANTOM_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.5
    });

    const spiritPurpleMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.SPIRIT_PURPLE,
        emissive: GHOST_COLORS.SPIRIT_PURPLE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.SHADOW_GRAY,
        emissive: GHOST_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    const mistySilverMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.MISTY_SILVER,
        emissive: GHOST_COLORS.MISTY_SILVER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.4
    });

    const paleBlueMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.PALE_BLUE,
        emissive: GHOST_COLORS.PALE_BLUE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    const voidBlackMaterial = new THREE.MeshLambertMaterial({
        color: GHOST_COLORS.VOID_BLACK,
        emissive: GHOST_COLORS.VOID_BLACK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Ghost figure (ethereal humanoid form)
    const ghostFigureVoxels = [
        // Ghost head
        { x: 0.0, y: 0.20, z: 0.0, material: spectralBlueMaterial },
        { x: -0.04, y: 0.20, z: 0.0, material: paleBlueMaterial },
        { x: 0.04, y: 0.20, z: 0.0, material: paleBlueMaterial },
        { x: 0.0, y: 0.24, z: 0.0, material: phantomWhiteMaterial },
        
        // Ghost eyes (spiritual glow)
        { x: -0.02, y: 0.22, z: 0.04, material: spiritPurpleMaterial },
        { x: 0.02, y: 0.22, z: 0.04, material: spiritPurpleMaterial },
        
        // Ghost torso
        { x: 0.0, y: 0.16, z: 0.0, material: spectralBlueMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: paleBlueMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: spectralBlueMaterial },
        { x: 0.0, y: 0.04, z: 0.0, material: paleBlueMaterial },
        
        // Ghost arms (reaching out)
        { x: -0.08, y: 0.12, z: 0.0, material: shadowGrayMaterial },
        { x: 0.08, y: 0.12, z: 0.0, material: shadowGrayMaterial },
        { x: -0.12, y: 0.08, z: 0.04, material: paleBlueMaterial },
        { x: 0.12, y: 0.08, z: 0.04, material: paleBlueMaterial },
        
        // Ghost lower body (fading into mist)
        { x: 0.0, y: 0.0, z: 0.0, material: mistySilverMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: mistySilverMaterial },
        { x: 0.0, y: -0.08, z: 0.0, material: mistySilverMaterial },
        
        // Ghost tail (wispy essence)
        { x: 0.0, y: -0.12, z: 0.0, material: etherealCyanMaterial },
        { x: 0.04, y: -0.16, z: 0.04, material: mistySilverMaterial },
        { x: -0.04, y: -0.16, z: -0.04, material: mistySilverMaterial },
        { x: 0.0, y: -0.20, z: 0.0, material: phantomWhiteMaterial }
    ];

    // Ethereal mist (swirling around ghost)
    const etherealMistVoxels = [
        // Inner mist ring
        { x: -0.12, y: 0.08, z: -0.08, material: mistySilverMaterial },
        { x: 0.12, y: 0.08, z: -0.08, material: mistySilverMaterial },
        { x: -0.08, y: 0.12, z: -0.12, material: phantomWhiteMaterial },
        { x: 0.08, y: 0.12, z: -0.12, material: phantomWhiteMaterial },
        { x: -0.12, y: 0.04, z: 0.08, material: mistySilverMaterial },
        { x: 0.12, y: 0.04, z: 0.08, material: mistySilverMaterial },
        { x: 0.08, y: 0.08, z: 0.12, material: phantomWhiteMaterial },
        { x: -0.08, y: 0.08, z: 0.12, material: phantomWhiteMaterial },
        
        // Middle mist ring
        { x: -0.16, y: 0.12, z: -0.12, material: etherealCyanMaterial },
        { x: 0.16, y: 0.12, z: -0.12, material: etherealCyanMaterial },
        { x: -0.12, y: 0.16, z: -0.16, material: paleBlueMaterial },
        { x: 0.12, y: 0.16, z: -0.16, material: paleBlueMaterial },
        { x: -0.16, y: 0.08, z: 0.12, material: etherealCyanMaterial },
        { x: 0.16, y: 0.08, z: 0.12, material: etherealCyanMaterial },
        { x: 0.12, y: 0.12, z: 0.16, material: paleBlueMaterial },
        { x: -0.12, y: 0.12, z: 0.16, material: paleBlueMaterial },
        
        // Outer mist ring
        { x: -0.20, y: 0.16, z: -0.16, material: mistySilverMaterial },
        { x: 0.20, y: 0.16, z: -0.16, material: mistySilverMaterial },
        { x: -0.16, y: 0.20, z: -0.20, material: phantomWhiteMaterial },
        { x: 0.16, y: 0.20, z: -0.20, material: phantomWhiteMaterial },
        { x: -0.20, y: 0.12, z: 0.16, material: mistySilverMaterial },
        { x: 0.20, y: 0.12, z: 0.16, material: mistySilverMaterial },
        { x: 0.16, y: 0.16, z: 0.20, material: phantomWhiteMaterial },
        { x: -0.16, y: 0.16, z: 0.20, material: phantomWhiteMaterial }
    ];

    // Phase portals (dimensional rifts)
    const phasePortalsVoxels = [
        // Portal A (left side)
        { x: -0.24, y: 0.20, z: 0.0, material: voidBlackMaterial },
        { x: -0.28, y: 0.20, z: 0.0, material: spiritPurpleMaterial },
        { x: -0.24, y: 0.24, z: 0.0, material: spiritPurpleMaterial },
        { x: -0.24, y: 0.16, z: 0.0, material: spiritPurpleMaterial },
        { x: -0.24, y: 0.20, z: 0.04, material: spiritPurpleMaterial },
        { x: -0.24, y: 0.20, z: -0.04, material: spiritPurpleMaterial },
        
        // Portal A energy ring
        { x: -0.32, y: 0.24, z: 0.0, material: etherealCyanMaterial },
        { x: -0.32, y: 0.16, z: 0.0, material: etherealCyanMaterial },
        { x: -0.28, y: 0.28, z: 0.0, material: phantomWhiteMaterial },
        { x: -0.28, y: 0.12, z: 0.0, material: phantomWhiteMaterial },
        
        // Portal B (right side)
        { x: 0.24, y: 0.20, z: 0.0, material: voidBlackMaterial },
        { x: 0.28, y: 0.20, z: 0.0, material: spiritPurpleMaterial },
        { x: 0.24, y: 0.24, z: 0.0, material: spiritPurpleMaterial },
        { x: 0.24, y: 0.16, z: 0.0, material: spiritPurpleMaterial },
        { x: 0.24, y: 0.20, z: 0.04, material: spiritPurpleMaterial },
        { x: 0.24, y: 0.20, z: -0.04, material: spiritPurpleMaterial },
        
        // Portal B energy ring
        { x: 0.32, y: 0.24, z: 0.0, material: etherealCyanMaterial },
        { x: 0.32, y: 0.16, z: 0.0, material: etherealCyanMaterial },
        { x: 0.28, y: 0.28, z: 0.0, material: phantomWhiteMaterial },
        { x: 0.28, y: 0.12, z: 0.0, material: phantomWhiteMaterial }
    ];

    // Stealth aura (concealment energy)
    const stealthAuraVoxels = [
        // Inner stealth layer
        { x: -0.14, y: 0.14, z: -0.08, material: shadowGrayMaterial },
        { x: 0.14, y: 0.14, z: -0.08, material: shadowGrayMaterial },
        { x: -0.14, y: -0.06, z: -0.08, material: shadowGrayMaterial },
        { x: 0.14, y: -0.06, z: -0.08, material: shadowGrayMaterial },
        { x: -0.08, y: 0.14, z: -0.14, material: voidBlackMaterial },
        { x: 0.08, y: 0.14, z: -0.14, material: voidBlackMaterial },
        { x: -0.08, y: -0.06, z: -0.14, material: voidBlackMaterial },
        { x: 0.08, y: -0.06, z: -0.14, material: voidBlackMaterial },
        
        // Outer stealth layer
        { x: -0.18, y: 0.18, z: -0.12, material: mistySilverMaterial },
        { x: 0.18, y: 0.18, z: -0.12, material: mistySilverMaterial },
        { x: -0.18, y: -0.10, z: -0.12, material: mistySilverMaterial },
        { x: 0.18, y: -0.10, z: -0.12, material: mistySilverMaterial },
        { x: -0.12, y: 0.18, z: -0.18, material: paleBlueMaterial },
        { x: 0.12, y: 0.18, z: -0.18, material: paleBlueMaterial },
        { x: -0.12, y: -0.10, z: -0.18, material: paleBlueMaterial },
        { x: 0.12, y: -0.10, z: -0.18, material: paleBlueMaterial },
        
        // Concealment sparkles
        { x: -0.22, y: 0.22, z: -0.16, material: phantomWhiteMaterial },
        { x: 0.22, y: 0.22, z: -0.16, material: phantomWhiteMaterial },
        { x: -0.22, y: -0.14, z: -0.16, material: phantomWhiteMaterial },
        { x: 0.22, y: -0.14, z: -0.16, material: phantomWhiteMaterial },
        { x: -0.26, y: 0.04, z: -0.20, material: etherealCyanMaterial },
        { x: 0.26, y: 0.04, z: -0.20, material: etherealCyanMaterial }
    ];

    // Create ghost figure group
    const ghostFigureGroup = new THREE.Group();
    ghostFigureGroup.name = 'ghostFigure';

    // Add ghost figure voxels
    ghostFigureVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        ghostFigureGroup.add(mesh);
    });

    // Create ethereal mist group
    const etherealMistGroup = new THREE.Group();
    etherealMistGroup.name = 'etherealMist';

    // Add ethereal mist voxels
    etherealMistVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.mistPhase = index * 0.12; // Stagger animation
        etherealMistGroup.add(mesh);
    });

    // Create phase portals group
    const phasePortalsGroup = new THREE.Group();
    phasePortalsGroup.name = 'phasePortals';

    // Add phase portals voxels
    phasePortalsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.portalPhase = index * 0.08; // Stagger animation
        phasePortalsGroup.add(mesh);
    });

    // Create stealth aura group
    const stealthAuraGroup = new THREE.Group();
    stealthAuraGroup.name = 'stealthAura';

    // Add stealth aura voxels
    stealthAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.stealthPhase = index * 0.06; // Stagger animation
        stealthAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(ghostFigureGroup);
    cardGroup.add(etherealMistGroup);
    cardGroup.add(phasePortalsGroup);
    cardGroup.add(stealthAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        ghostPhase: 0,
        mistSwirl: 0,
        portalPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update ghost walk card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateGhostWalkCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.ghostPhase += deltaTime * 1.5; // Ghost phase speed
    cardGroup.userData.mistSwirl += deltaTime * 2.5; // Mist swirl speed
    cardGroup.userData.portalPulse += deltaTime * 4.0; // Portal pulse speed

    const time = cardGroup.userData.animationTime;
    const ghostPhase = cardGroup.userData.ghostPhase;
    const mistSwirl = cardGroup.userData.mistSwirl;
    const portalPulse = cardGroup.userData.portalPulse;

    // Animate ghost figure (ethereal floating)
    const ghostFigureGroup = cardGroup.getObjectByName('ghostFigure');
    if (ghostFigureGroup) {
        // Ghost floating motion
        const ghostFloat = Math.sin(ghostPhase * 1.2) * 0.002;
        ghostFigureGroup.position.y = ghostFloat;
        
        // Ghost transparency pulsing (phasing in and out)
        const ghostAlpha = 0.7 + Math.sin(ghostPhase * 2.0) * 0.3;
        ghostFigureGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                mesh.material.opacity = mesh.userData.originalOpacity * ghostAlpha;
            }
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                const ghostGlow = 1.0 + Math.sin(ghostPhase * 1.8) * 0.4;
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * ghostGlow;
            }
        });
    }

    // Animate ethereal mist (swirling around ghost)
    const etherealMistGroup = cardGroup.getObjectByName('etherealMist');
    if (etherealMistGroup) {
        etherealMistGroup.rotation.y = time * 0.8; // Slow rotation
        
        etherealMistGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.mistPhase !== undefined) {
                const mistTime = mistSwirl + mesh.userData.mistPhase;
                
                // Mist swirling motion
                const mistSwirl = Math.sin(mistTime * 2.2) * 0.004;
                const mistFloat = Math.cos(mistTime * 3.0) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + mistSwirl;
                mesh.position.y = mesh.userData.originalPosition.y + mistFloat;
                
                // Mist opacity pulsing
                const mistAlpha = 0.4 + Math.sin(mistTime * 3.5) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * mistAlpha;
                }
                
                // Mist twinkling
                const mistTwinkle = 1.0 + Math.sin(mistTime * 4.5) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * mistTwinkle;
                }
            }
        });
    }

    // Animate phase portals (dimensional rifts)
    const phasePortalsGroup = cardGroup.getObjectByName('phasePortals');
    if (phasePortalsGroup) {
        phasePortalsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.portalPhase !== undefined) {
                const portalTime = portalPulse + mesh.userData.portalPhase;
                
                // Portal energy pulsing
                const portalPulse = Math.sin(portalTime * 5.0) * 0.002;
                mesh.position.z = mesh.userData.originalPosition.z + portalPulse;
                
                // Portal intensity pulsing
                const portalIntensity = 1.0 + Math.sin(portalTime * 6.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * portalIntensity;
                }
                
                // Portal scale variation
                const portalScale = 0.9 + Math.sin(portalTime * 4.0) * 0.2;
                mesh.scale.setScalar(portalScale);
            }
        });
    }

    // Animate stealth aura (concealment energy)
    const stealthAuraGroup = cardGroup.getObjectByName('stealthAura');
    if (stealthAuraGroup) {
        stealthAuraGroup.rotation.y = -time * 1.0; // Counter-rotating stealth energy
        
        stealthAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.stealthPhase !== undefined) {
                const stealthTime = ghostPhase + mesh.userData.stealthPhase;
                
                // Stealth swirling motion
                const stealthSwirl = Math.sin(stealthTime * 2.8) * 0.005;
                const stealthPulse = Math.cos(stealthTime * 3.5) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + stealthSwirl;
                mesh.position.y = mesh.userData.originalPosition.y + stealthPulse;
                
                // Stealth invisibility effect (fading in and out)
                const stealthAlpha = 0.6 + Math.sin(stealthTime * 4.2) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * stealthAlpha;
                }
                
                // Stealth shimmer
                const stealthShimmer = 0.8 + Math.sin(stealthTime * 6.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * stealthShimmer;
                }
                
                // Stealth scale flickering
                const stealthScale = 0.9 + Math.sin(stealthTime * 7.0) * 0.2;
                mesh.scale.setScalar(stealthScale);
            }
        });
    }

    // Overall ghost walk pulsing (ethereal power)
    const etherealPulse = 1 + Math.sin(time * 2.0) * 0.06;
    cardGroup.scale.setScalar(0.8 * etherealPulse);
}

// Export the ghost walk card data for the loot system
export const GHOST_WALK_CARD_DATA = {
    name: 'Ghost Walk',
    description: 'Transform into an ethereal spirit, gaining the ability to phase through walls, become invisible to enemies, and move silently through the spectral realm.',
    category: 'card',
    rarity: 'epic',
    effect: 'ghost_walk',
    effectValue: 45, // Duration in seconds
    createFunction: createGhostWalkCard,
    updateFunction: updateGhostWalkCardAnimation,
    voxelModel: 'ghost_walk_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.4
    }
};

export default createGhostWalkCard;