import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Sanctuary Ward Card Prefab
 * Creates protective barriers and healing sanctuary
 */

// Sanctuary ward specific colors
const SANCTUARY_COLORS = {
    HOLY_WHITE: 0xFFFFFF,          // Pure white sanctuary light
    DIVINE_GOLD: 0xFFD700,         // Golden divine energy
    PROTECTION_BLUE: 0x4169E1,     // Royal blue protective aura
    HEALING_GREEN: 0x32CD32,       // Lime green healing energy
    SACRED_SILVER: 0xC0C0C0,       // Silver sacred elements
    WARD_PURPLE: 0x9966FF          // Purple ward energy
};

/**
 * Create a sanctuary ward card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The sanctuary ward card 3D model
 */
export function createSanctuaryWardCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SanctuaryWardCard';

    // Sanctuary ward materials
    const holyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.HOLY_WHITE,
        emissive: SANCTUARY_COLORS.HOLY_WHITE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.9
    });

    const divineGoldMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.DIVINE_GOLD,
        emissive: SANCTUARY_COLORS.DIVINE_GOLD,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const protectionBlueMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.PROTECTION_BLUE,
        emissive: SANCTUARY_COLORS.PROTECTION_BLUE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.75
    });

    const healingGreenMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.HEALING_GREEN,
        emissive: SANCTUARY_COLORS.HEALING_GREEN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const sacredSilverMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.SACRED_SILVER,
        emissive: SANCTUARY_COLORS.SACRED_SILVER,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const wardPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SANCTUARY_COLORS.WARD_PURPLE,
        emissive: SANCTUARY_COLORS.WARD_PURPLE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.75
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central sanctuary structure (protective temple/shrine)
    const sanctuaryStructureVoxels = [
        // Base platform
        { x: -0.12, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: -0.08, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: -0.04, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: 0.0, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: 0.04, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: 0.08, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: -0.12, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: -0.08, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: -0.08, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: -0.04, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: -0.04, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: 0.0, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: 0.0, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: 0.04, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: 0.04, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: 0.08, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: 0.08, material: sacredSilverMaterial },
        { x: -0.12, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: -0.08, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: -0.04, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: 0.0, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: 0.04, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: 0.08, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        { x: 0.12, y: -0.24, z: 0.12, material: sacredSilverMaterial },
        
        // Pillars (four corners)
        { x: -0.08, y: -0.20, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.16, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.12, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.08, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.20, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.16, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.12, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.08, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.20, z: 0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.16, z: 0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.12, z: 0.08, material: holyWhiteMaterial },
        { x: -0.08, y: -0.08, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.20, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.16, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.12, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: -0.08, z: 0.08, material: holyWhiteMaterial },
        
        // Central sanctuary core
        { x: 0.0, y: -0.16, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: -0.08, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.0, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.04, z: 0.0, material: divineGoldMaterial }
    ];

    // Protective barrier dome (ward energy)
    const protectiveBarrierVoxels = [
        // Inner barrier layer
        { x: -0.16, y: 0.0, z: 0.0, material: protectionBlueMaterial },
        { x: 0.16, y: 0.0, z: 0.0, material: protectionBlueMaterial },
        { x: 0.0, y: 0.0, z: -0.16, material: protectionBlueMaterial },
        { x: 0.0, y: 0.0, z: 0.16, material: protectionBlueMaterial },
        { x: -0.12, y: 0.08, z: -0.12, material: protectionBlueMaterial },
        { x: 0.12, y: 0.08, z: -0.12, material: protectionBlueMaterial },
        { x: -0.12, y: 0.08, z: 0.12, material: protectionBlueMaterial },
        { x: 0.12, y: 0.08, z: 0.12, material: protectionBlueMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: protectionBlueMaterial },
        
        // Middle barrier layer
        { x: -0.20, y: 0.04, z: 0.0, material: wardPurpleMaterial },
        { x: 0.20, y: 0.04, z: 0.0, material: wardPurpleMaterial },
        { x: 0.0, y: 0.04, z: -0.20, material: wardPurpleMaterial },
        { x: 0.0, y: 0.04, z: 0.20, material: wardPurpleMaterial },
        { x: -0.16, y: 0.12, z: -0.16, material: wardPurpleMaterial },
        { x: 0.16, y: 0.12, z: -0.16, material: wardPurpleMaterial },
        { x: -0.16, y: 0.12, z: 0.16, material: wardPurpleMaterial },
        { x: 0.16, y: 0.12, z: 0.16, material: wardPurpleMaterial },
        { x: 0.0, y: 0.16, z: 0.0, material: wardPurpleMaterial },
        
        // Outer barrier layer
        { x: -0.24, y: 0.08, z: 0.0, material: protectionBlueMaterial },
        { x: 0.24, y: 0.08, z: 0.0, material: protectionBlueMaterial },
        { x: 0.0, y: 0.08, z: -0.24, material: protectionBlueMaterial },
        { x: 0.0, y: 0.08, z: 0.24, material: protectionBlueMaterial },
        { x: -0.20, y: 0.16, z: -0.20, material: protectionBlueMaterial },
        { x: 0.20, y: 0.16, z: -0.20, material: protectionBlueMaterial },
        { x: -0.20, y: 0.16, z: 0.20, material: protectionBlueMaterial },
        { x: 0.20, y: 0.16, z: 0.20, material: protectionBlueMaterial },
        { x: 0.0, y: 0.20, z: 0.0, material: protectionBlueMaterial }
    ];

    // Healing energy orbs (life restoration)
    const healingEnergyVoxels = [
        // Primary healing orbs (floating around sanctuary)
        { x: -0.28, y: 0.12, z: 0.0, material: healingGreenMaterial },
        { x: 0.28, y: 0.12, z: 0.0, material: healingGreenMaterial },
        { x: 0.0, y: 0.12, z: -0.28, material: healingGreenMaterial },
        { x: 0.0, y: 0.12, z: 0.28, material: healingGreenMaterial },
        
        // Secondary healing orbs (diagonal positions)
        { x: -0.20, y: 0.20, z: -0.20, material: healingGreenMaterial },
        { x: 0.20, y: 0.20, z: -0.20, material: healingGreenMaterial },
        { x: -0.20, y: 0.20, z: 0.20, material: healingGreenMaterial },
        { x: 0.20, y: 0.20, z: 0.20, material: healingGreenMaterial },
        
        // Tertiary healing orbs (upper layer)
        { x: -0.16, y: 0.28, z: 0.0, material: healingGreenMaterial },
        { x: 0.16, y: 0.28, z: 0.0, material: healingGreenMaterial },
        { x: 0.0, y: 0.28, z: -0.16, material: healingGreenMaterial },
        { x: 0.0, y: 0.28, z: 0.16, material: healingGreenMaterial },
        
        // Central healing crown
        { x: 0.0, y: 0.32, z: 0.0, material: healingGreenMaterial },
        { x: -0.04, y: 0.32, z: -0.04, material: healingGreenMaterial },
        { x: 0.04, y: 0.32, z: -0.04, material: healingGreenMaterial },
        { x: -0.04, y: 0.32, z: 0.04, material: healingGreenMaterial },
        { x: 0.04, y: 0.32, z: 0.04, material: healingGreenMaterial }
    ];

    // Divine light rays (sacred illumination)
    const divineLightVoxels = [
        // Vertical light rays from pillars
        { x: -0.08, y: 0.08, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: 0.12, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: 0.16, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.08, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.12, z: -0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.16, z: -0.08, material: holyWhiteMaterial },
        { x: -0.08, y: 0.08, z: 0.08, material: holyWhiteMaterial },
        { x: -0.08, y: 0.12, z: 0.08, material: holyWhiteMaterial },
        { x: -0.08, y: 0.16, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.08, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.12, z: 0.08, material: holyWhiteMaterial },
        { x: 0.08, y: 0.16, z: 0.08, material: holyWhiteMaterial },
        
        // Central divine beacon
        { x: 0.0, y: 0.08, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.16, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.20, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.24, z: 0.0, material: divineGoldMaterial },
        
        // Radiating light beams
        { x: -0.32, y: 0.16, z: 0.0, material: holyWhiteMaterial },
        { x: 0.32, y: 0.16, z: 0.0, material: holyWhiteMaterial },
        { x: 0.0, y: 0.16, z: -0.32, material: holyWhiteMaterial },
        { x: 0.0, y: 0.16, z: 0.32, material: holyWhiteMaterial },
        { x: -0.24, y: 0.24, z: -0.24, material: holyWhiteMaterial },
        { x: 0.24, y: 0.24, z: -0.24, material: holyWhiteMaterial },
        { x: -0.24, y: 0.24, z: 0.24, material: holyWhiteMaterial },
        { x: 0.24, y: 0.24, z: 0.24, material: holyWhiteMaterial },
        
        // Distant divine sparkles
        { x: -0.36, y: 0.20, z: 0.0, material: divineGoldMaterial },
        { x: 0.36, y: 0.20, z: 0.0, material: divineGoldMaterial },
        { x: 0.0, y: 0.20, z: -0.36, material: divineGoldMaterial },
        { x: 0.0, y: 0.20, z: 0.36, material: divineGoldMaterial },
        { x: -0.28, y: 0.32, z: -0.28, material: divineGoldMaterial },
        { x: 0.28, y: 0.32, z: -0.28, material: divineGoldMaterial },
        { x: -0.28, y: 0.32, z: 0.28, material: divineGoldMaterial },
        { x: 0.28, y: 0.32, z: 0.28, material: divineGoldMaterial }
    ];

    // Create sanctuary structure group
    const sanctuaryGroup = new THREE.Group();
    sanctuaryGroup.name = 'sanctuaryStructure';

    // Add sanctuary structure voxels
    sanctuaryStructureVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        sanctuaryGroup.add(mesh);
    });

    // Create protective barrier group
    const barrierGroup = new THREE.Group();
    barrierGroup.name = 'protectiveBarrier';

    // Add protective barrier voxels
    protectiveBarrierVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.barrierPhase = index * 0.1; // Stagger animation
        barrierGroup.add(mesh);
    });

    // Create healing energy group
    const healingGroup = new THREE.Group();
    healingGroup.name = 'healingEnergy';

    // Add healing energy voxels
    healingEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.healingPhase = index * 0.15; // Stagger animation
        healingGroup.add(mesh);
    });

    // Create divine light group
    const lightGroup = new THREE.Group();
    lightGroup.name = 'divineLight';

    // Add divine light voxels
    divineLightVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.lightPhase = index * 0.05; // Stagger animation
        lightGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(sanctuaryGroup);
    cardGroup.add(barrierGroup);
    cardGroup.add(healingGroup);
    cardGroup.add(lightGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        sanctuaryPulse: 0,
        divineGlow: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update sanctuary ward card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSanctuaryWardCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.sanctuaryPulse += deltaTime * 2.0; // Sanctuary pulse speed
    cardGroup.userData.divineGlow += deltaTime * 3.0; // Divine glow speed

    const time = cardGroup.userData.animationTime;
    const sanctuaryPulse = cardGroup.userData.sanctuaryPulse;
    const divineGlow = cardGroup.userData.divineGlow;

    // Animate sanctuary structure (divine pulsing)
    const sanctuaryGroup = cardGroup.getObjectByName('sanctuaryStructure');
    if (sanctuaryGroup) {
        // Sanctuary core divine pulsing (holy radiance)
        const sanctuaryIntensity = 1.0 + Math.sin(sanctuaryPulse * 1.5) * 0.3;
        sanctuaryGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * sanctuaryIntensity;
            }
        });
    }

    // Animate protective barrier (shield pulsing)
    const barrierGroup = cardGroup.getObjectByName('protectiveBarrier');
    if (barrierGroup) {
        // Barrier shield pulsing
        barrierGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.barrierPhase !== undefined) {
                const barrierTime = sanctuaryPulse + mesh.userData.barrierPhase;
                
                // Protective shield expansion and contraction
                const shieldPulse = Math.sin(barrierTime * 2.0) * 0.001;
                const magnitude = Math.sqrt(
                    mesh.userData.originalPosition.x * mesh.userData.originalPosition.x + 
                    mesh.userData.originalPosition.z * mesh.userData.originalPosition.z
                );
                
                if (magnitude > 0) {
                    const normalizedX = mesh.userData.originalPosition.x / magnitude;
                    const normalizedZ = mesh.userData.originalPosition.z / magnitude;
                    
                    mesh.position.x = mesh.userData.originalPosition.x + normalizedX * shieldPulse;
                    mesh.position.z = mesh.userData.originalPosition.z + normalizedZ * shieldPulse;
                }
                
                // Barrier protection intensity pulsing
                const protectionPulse = 0.8 + Math.sin(barrierTime * 3.0) * 0.2;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * protectionPulse;
                }
                
                // Barrier opacity flickering (protective field)
                const barrierOpacity = 0.7 + Math.sin(barrierTime * 4.0) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * barrierOpacity;
                }
            }
        });
    }

    // Animate healing energy (life restoration orbiting)
    const healingGroup = cardGroup.getObjectByName('healingEnergy');
    if (healingGroup) {
        healingGroup.rotation.y = time * 0.8; // Slow rotation
        
        // Healing energy orb floating
        healingGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.healingPhase !== undefined) {
                const healingTime = sanctuaryPulse + mesh.userData.healingPhase;
                
                // Healing orb floating motion
                const floatY = Math.sin(healingTime * 2.5) * 0.002;
                const healPulse = Math.cos(healingTime * 3.0) * 0.001;
                
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                
                // Healing energy intensity pulsing
                const healingIntensity = 0.7 + Math.sin(healingTime * 4.0) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * healingIntensity;
                }
                
                // Healing scale variation (energy fluctuation)
                const healingScale = 0.9 + Math.sin(healingTime * 3.5) * 0.2;
                mesh.scale.setScalar(healingScale);
            }
        });
    }

    // Animate divine light (sacred illumination)
    const lightGroup = cardGroup.getObjectByName('divineLight');
    if (lightGroup) {
        // Divine light sacred radiance
        lightGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.lightPhase !== undefined) {
                const lightTime = divineGlow + mesh.userData.lightPhase;
                
                // Divine light beam fluctuation
                const beamFlicker = Math.sin(lightTime * 6.0) * 0.0005;
                
                mesh.position.y = mesh.userData.originalPosition.y + beamFlicker;
                
                // Divine radiance intensity
                const divineIntensity = 1.0 + Math.sin(lightTime * 5.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * divineIntensity;
                }
                
                // Occasional bright divine flash
                const divineFlash = Math.sin(lightTime * 8.0) > 0.8 ? 1.5 : 1.0;
                mesh.material.emissiveIntensity *= divineFlash;
            }
        });
    }

    // Overall sanctuary divine pulsing (holy energy)
    const sanctuaryPulseScale = 1 + Math.sin(time * 2.0) * 0.08;
    cardGroup.scale.setScalar(0.8 * sanctuaryPulseScale);
}

// Export the sanctuary ward card data for the loot system
export const SANCTUARY_WARD_CARD_DATA = {
    name: 'Sanctuary Ward',
    description: 'Creates a protective sanctuary that absorbs incoming damage and slowly heals the caster while providing temporary invulnerability.',
    category: 'card',
    rarity: 'epic',
    effect: 'sanctuary_ward',
    effectValue: 10, // Amount of damage absorbed
    createFunction: createSanctuaryWardCard,
    updateFunction: updateSanctuaryWardCardAnimation,
    voxelModel: 'sanctuary_ward_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.4
    }
};

export default createSanctuaryWardCard;