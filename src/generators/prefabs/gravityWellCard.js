import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Gravity Well Card Prefab
 * Creates a gravitational field that pulls enemies toward a central point
 */

// Gravity Well specific colors
const GRAVITY_WELL_COLORS = {
    VOID_PURPLE: 0x4B0082,          // Deep void core
    GRAVITY_BLUE: 0x000080,         // Gravitational force
    SPACE_BLACK: 0x191970,          // Space distortion
    STELLAR_WHITE: 0xF8F8FF,        // Stellar particles
    COSMIC_CYAN: 0x00BFFF,          // Cosmic energy
    DARK_MATTER: 0x2F2F4F,          // Dark matter
    SINGULARITY_RED: 0x8B0000,      // Singularity core
    ORBIT_SILVER: 0xC0C0C0          // Orbital rings
};

/**
 * Create a gravity well card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The gravity well card 3D model
 */
export function createGravityWellCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'GravityWellCard';

    // Gravity Well materials
    const voidPurpleMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.VOID_PURPLE,
        emissive: GRAVITY_WELL_COLORS.VOID_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const gravityBlueMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.GRAVITY_BLUE,
        emissive: GRAVITY_WELL_COLORS.GRAVITY_BLUE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const spaceBlackMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.SPACE_BLACK,
        emissive: GRAVITY_WELL_COLORS.SPACE_BLACK,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const stellarWhiteMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.STELLAR_WHITE,
        emissive: GRAVITY_WELL_COLORS.STELLAR_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const cosmicCyanMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.COSMIC_CYAN,
        emissive: GRAVITY_WELL_COLORS.COSMIC_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const darkMatterMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.DARK_MATTER,
        emissive: GRAVITY_WELL_COLORS.DARK_MATTER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const singularityRedMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.SINGULARITY_RED,
        emissive: GRAVITY_WELL_COLORS.SINGULARITY_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.9
    });

    const orbitSilverMaterial = new THREE.MeshLambertMaterial({
        color: GRAVITY_WELL_COLORS.ORBIT_SILVER,
        emissive: GRAVITY_WELL_COLORS.ORBIT_SILVER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Singularity Core (central gravity source)
    const singularityCoreVoxels = [
        // Core nucleus
        { x: 0.0, y: 0.0, z: 0.0, material: singularityRedMaterial }, // Center
        { x: -0.04, y: 0.0, z: 0.0, material: voidPurpleMaterial }, // Left
        { x: 0.04, y: 0.0, z: 0.0, material: voidPurpleMaterial }, // Right
        { x: 0.0, y: 0.04, z: 0.0, material: voidPurpleMaterial }, // Top
        { x: 0.0, y: -0.04, z: 0.0, material: voidPurpleMaterial }, // Bottom
        { x: 0.0, y: 0.0, z: 0.04, material: voidPurpleMaterial }, // Front
        { x: 0.0, y: 0.0, z: -0.04, material: voidPurpleMaterial }, // Back
        
        // Inner core
        { x: -0.02, y: -0.02, z: 0.0, material: singularityRedMaterial }, // Bottom-left
        { x: 0.02, y: -0.02, z: 0.0, material: singularityRedMaterial }, // Bottom-right
        { x: -0.02, y: 0.02, z: 0.0, material: singularityRedMaterial }, // Top-left
        { x: 0.02, y: 0.02, z: 0.0, material: singularityRedMaterial }, // Top-right
        { x: 0.0, y: -0.02, z: -0.02, material: singularityRedMaterial }, // Bottom-back
        { x: 0.0, y: 0.02, z: -0.02, material: singularityRedMaterial }, // Top-back
        { x: 0.0, y: -0.02, z: 0.02, material: singularityRedMaterial }, // Bottom-front
        { x: 0.0, y: 0.02, z: 0.02, material: singularityRedMaterial }, // Top-front
        
        // Energy shell
        { x: -0.06, y: 0.0, z: 0.0, material: voidPurpleMaterial }, // Extended left
        { x: 0.06, y: 0.0, z: 0.0, material: voidPurpleMaterial }, // Extended right
        { x: 0.0, y: 0.06, z: 0.0, material: voidPurpleMaterial }, // Extended top
        { x: 0.0, y: -0.06, z: 0.0, material: voidPurpleMaterial } // Extended bottom
    ];

    // Gravitational Rings (orbital patterns)
    const gravitationalRingsVoxels = [
        // Inner ring
        { x: -0.08, y: 0.0, z: 0.0, material: orbitSilverMaterial }, // Left
        { x: 0.08, y: 0.0, z: 0.0, material: orbitSilverMaterial }, // Right
        { x: 0.0, y: 0.08, z: 0.0, material: orbitSilverMaterial }, // Top
        { x: 0.0, y: -0.08, z: 0.0, material: orbitSilverMaterial }, // Bottom
        { x: -0.06, y: 0.06, z: 0.0, material: orbitSilverMaterial }, // Top-left
        { x: 0.06, y: 0.06, z: 0.0, material: orbitSilverMaterial }, // Top-right
        { x: -0.06, y: -0.06, z: 0.0, material: orbitSilverMaterial }, // Bottom-left
        { x: 0.06, y: -0.06, z: 0.0, material: orbitSilverMaterial }, // Bottom-right
        
        // Middle ring
        { x: -0.12, y: 0.0, z: 0.0, material: gravityBlueMaterial }, // Left
        { x: 0.12, y: 0.0, z: 0.0, material: gravityBlueMaterial }, // Right
        { x: 0.0, y: 0.12, z: 0.0, material: gravityBlueMaterial }, // Top
        { x: 0.0, y: -0.12, z: 0.0, material: gravityBlueMaterial }, // Bottom
        { x: -0.085, y: 0.085, z: 0.0, material: gravityBlueMaterial }, // Top-left
        { x: 0.085, y: 0.085, z: 0.0, material: gravityBlueMaterial }, // Top-right
        { x: -0.085, y: -0.085, z: 0.0, material: gravityBlueMaterial }, // Bottom-left
        { x: 0.085, y: -0.085, z: 0.0, material: gravityBlueMaterial }, // Bottom-right
        
        // Outer ring
        { x: -0.16, y: 0.0, z: 0.0, material: cosmicCyanMaterial }, // Left
        { x: 0.16, y: 0.0, z: 0.0, material: cosmicCyanMaterial }, // Right
        { x: 0.0, y: 0.16, z: 0.0, material: cosmicCyanMaterial }, // Top
        { x: 0.0, y: -0.16, z: 0.0, material: cosmicCyanMaterial }, // Bottom
        { x: -0.113, y: 0.113, z: 0.0, material: cosmicCyanMaterial }, // Top-left
        { x: 0.113, y: 0.113, z: 0.0, material: cosmicCyanMaterial }, // Top-right
        { x: -0.113, y: -0.113, z: 0.0, material: cosmicCyanMaterial }, // Bottom-left
        { x: 0.113, y: -0.113, z: 0.0, material: cosmicCyanMaterial }, // Bottom-right
        
        // Extended outer ring
        { x: -0.20, y: 0.0, z: 0.0, material: darkMatterMaterial }, // Far left
        { x: 0.20, y: 0.0, z: 0.0, material: darkMatterMaterial }, // Far right
        { x: 0.0, y: 0.20, z: 0.0, material: darkMatterMaterial }, // Far top
        { x: 0.0, y: -0.20, z: 0.0, material: darkMatterMaterial }, // Far bottom
        { x: -0.141, y: 0.141, z: 0.0, material: darkMatterMaterial }, // Far top-left
        { x: 0.141, y: 0.141, z: 0.0, material: darkMatterMaterial }, // Far top-right
        { x: -0.141, y: -0.141, z: 0.0, material: darkMatterMaterial }, // Far bottom-left
        { x: 0.141, y: -0.141, z: 0.0, material: darkMatterMaterial } // Far bottom-right
    ];

    // Stellar Debris (particles being pulled in)
    const stellarDebrisVoxels = [
        // Inner debris ring
        { x: -0.10, y: 0.02, z: 0.04, material: stellarWhiteMaterial },
        { x: 0.10, y: -0.02, z: 0.04, material: stellarWhiteMaterial },
        { x: 0.02, y: 0.10, z: 0.04, material: stellarWhiteMaterial },
        { x: -0.02, y: -0.10, z: 0.04, material: stellarWhiteMaterial },
        
        // Middle debris ring
        { x: -0.14, y: 0.04, z: 0.02, material: cosmicCyanMaterial },
        { x: 0.14, y: -0.04, z: 0.02, material: cosmicCyanMaterial },
        { x: 0.04, y: 0.14, z: 0.02, material: cosmicCyanMaterial },
        { x: -0.04, y: -0.14, z: 0.02, material: cosmicCyanMaterial },
        { x: -0.10, y: 0.10, z: 0.02, material: cosmicCyanMaterial },
        { x: 0.10, y: 0.10, z: 0.02, material: cosmicCyanMaterial },
        { x: -0.10, y: -0.10, z: 0.02, material: cosmicCyanMaterial },
        { x: 0.10, y: -0.10, z: 0.02, material: cosmicCyanMaterial },
        
        // Outer debris ring
        { x: -0.18, y: 0.06, z: 0.0, material: spaceBlackMaterial },
        { x: 0.18, y: -0.06, z: 0.0, material: spaceBlackMaterial },
        { x: 0.06, y: 0.18, z: 0.0, material: spaceBlackMaterial },
        { x: -0.06, y: -0.18, z: 0.0, material: spaceBlackMaterial },
        { x: -0.13, y: 0.13, z: 0.0, material: spaceBlackMaterial },
        { x: 0.13, y: 0.13, z: 0.0, material: spaceBlackMaterial },
        { x: -0.13, y: -0.13, z: 0.0, material: spaceBlackMaterial },
        { x: 0.13, y: -0.13, z: 0.0, material: spaceBlackMaterial },
        
        // Far debris
        { x: -0.22, y: 0.08, z: -0.02, material: darkMatterMaterial },
        { x: 0.22, y: -0.08, z: -0.02, material: darkMatterMaterial },
        { x: 0.08, y: 0.22, z: -0.02, material: darkMatterMaterial },
        { x: -0.08, y: -0.22, z: -0.02, material: darkMatterMaterial },
        { x: -0.16, y: 0.16, z: -0.02, material: darkMatterMaterial },
        { x: 0.16, y: 0.16, z: -0.02, material: darkMatterMaterial },
        { x: -0.16, y: -0.16, z: -0.02, material: darkMatterMaterial },
        { x: 0.16, y: -0.16, z: -0.02, material: darkMatterMaterial }
    ];

    // Space Distortion (warped space effects)
    const spaceDistortionVoxels = [
        // Distortion waves
        { x: -0.24, y: 0.0, z: -0.04, material: voidPurpleMaterial }, // Left wave
        { x: 0.24, y: 0.0, z: -0.04, material: voidPurpleMaterial }, // Right wave
        { x: 0.0, y: 0.24, z: -0.04, material: voidPurpleMaterial }, // Top wave
        { x: 0.0, y: -0.24, z: -0.04, material: voidPurpleMaterial }, // Bottom wave
        
        // Diagonal distortions
        { x: -0.17, y: 0.17, z: -0.06, material: spaceBlackMaterial }, // Top-left
        { x: 0.17, y: 0.17, z: -0.06, material: spaceBlackMaterial }, // Top-right
        { x: -0.17, y: -0.17, z: -0.06, material: spaceBlackMaterial }, // Bottom-left
        { x: 0.17, y: -0.17, z: -0.06, material: spaceBlackMaterial }, // Bottom-right
        
        // Warped space nodes
        { x: -0.28, y: 0.0, z: -0.02, material: darkMatterMaterial }, // Far left
        { x: 0.28, y: 0.0, z: -0.02, material: darkMatterMaterial }, // Far right
        { x: 0.0, y: 0.28, z: -0.02, material: darkMatterMaterial }, // Far top
        { x: 0.0, y: -0.28, z: -0.02, material: darkMatterMaterial }, // Far bottom
        
        // Edge distortions
        { x: -0.20, y: 0.20, z: -0.08, material: voidPurpleMaterial },
        { x: 0.20, y: 0.20, z: -0.08, material: voidPurpleMaterial },
        { x: -0.20, y: -0.20, z: -0.08, material: voidPurpleMaterial },
        { x: 0.20, y: -0.20, z: -0.08, material: voidPurpleMaterial }
    ];

    // Create singularity core group
    const singularityCoreGroup = new THREE.Group();
    singularityCoreGroup.name = 'singularityCoreGroup';

    // Add singularity core voxels
    singularityCoreVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.corePhase = index * 0.1; // Stagger animation
        singularityCoreGroup.add(mesh);
    });

    // Create gravitational rings group
    const gravitationalRingsGroup = new THREE.Group();
    gravitationalRingsGroup.name = 'gravitationalRingsGroup';

    // Add gravitational rings voxels
    gravitationalRingsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.orbitPhase = index * 0.05; // Stagger animation
        gravitationalRingsGroup.add(mesh);
    });

    // Create stellar debris group
    const stellarDebrisGroup = new THREE.Group();
    stellarDebrisGroup.name = 'stellarDebrisGroup';

    // Add stellar debris voxels
    stellarDebrisVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.debrisPhase = index * 0.08; // Stagger animation
        stellarDebrisGroup.add(mesh);
    });

    // Create space distortion group
    const spaceDistortionGroup = new THREE.Group();
    spaceDistortionGroup.name = 'spaceDistortionGroup';

    // Add space distortion voxels
    spaceDistortionVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.distortionPhase = index * 0.12; // Stagger animation
        spaceDistortionGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(singularityCoreGroup);
    cardGroup.add(gravitationalRingsGroup);
    cardGroup.add(stellarDebrisGroup);
    cardGroup.add(spaceDistortionGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        gravitationalPull: 0,
        orbitalMotion: 0,
        spacialWarp: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update gravity well card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateGravityWellCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.gravitationalPull += deltaTime * 2.0; // Gravitational pull speed
    cardGroup.userData.orbitalMotion += deltaTime * 3.0; // Orbital motion speed
    cardGroup.userData.spacialWarp += deltaTime * 1.5; // Spatial warp speed

    const time = cardGroup.userData.animationTime;
    const gravitationalPull = cardGroup.userData.gravitationalPull;
    const orbitalMotion = cardGroup.userData.orbitalMotion;
    const spacialWarp = cardGroup.userData.spacialWarp;

    // Animate singularity core (gravity source)
    const singularityCoreGroup = cardGroup.getObjectByName('singularityCoreGroup');
    if (singularityCoreGroup) {
        // Core pulsing (gravitational waves)
        const corePulse = Math.sin(gravitationalPull * 4.0) * 0.001;
        singularityCoreGroup.position.y = corePulse;
        
        // Individual core element animation
        singularityCoreGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.corePhase !== undefined) {
                const coreTime = gravitationalPull + mesh.userData.corePhase;
                
                // Core energy fluctuation
                const coreFluctuation = Math.sin(coreTime * 6.0) * 0.0005;
                const coreVibration = Math.cos(coreTime * 8.0) * 0.0003;
                
                mesh.position.x = mesh.userData.originalPosition.x + coreFluctuation;
                mesh.position.z = mesh.userData.originalPosition.z + coreVibration;
                
                // Core intensity pulsing
                const coreIntensity = 1.0 + Math.sin(coreTime * 5.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * coreIntensity;
                }
                
                // Core scale pulsing
                const coreScale = 0.9 + Math.sin(coreTime * 4.0) * 0.2;
                mesh.scale.setScalar(coreScale);
            }
        });
    }

    // Animate gravitational rings (orbital motion)
    const gravitationalRingsGroup = cardGroup.getObjectByName('gravitationalRingsGroup');
    if (gravitationalRingsGroup) {
        // Ring rotation (orbital mechanics)
        const ringRotation = Math.sin(orbitalMotion * 0.8) * 0.02;
        gravitationalRingsGroup.rotation.z = ringRotation;
        
        gravitationalRingsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.orbitPhase !== undefined) {
                const orbitTime = orbitalMotion + mesh.userData.orbitPhase;
                
                // Orbital motion
                const orbitFloat = Math.sin(orbitTime * 3.0) * 0.002;
                const orbitSway = Math.cos(orbitTime * 2.5) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + orbitSway;
                mesh.position.y = mesh.userData.originalPosition.y + orbitFloat;
                
                // Orbital glow
                const orbitGlow = 1.0 + Math.sin(orbitTime * 4.0) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * orbitGlow;
                }
                
                // Ring opacity variation
                const ringOpacity = 0.7 + Math.sin(orbitTime * 3.5) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * ringOpacity;
                }
            }
        });
    }

    // Animate stellar debris (particles being pulled in)
    const stellarDebrisGroup = cardGroup.getObjectByName('stellarDebrisGroup');
    if (stellarDebrisGroup) {
        stellarDebrisGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.debrisPhase !== undefined) {
                const debrisTime = gravitationalPull + mesh.userData.debrisPhase;
                
                // Debris spiral motion (being pulled inward)
                const spiralMotion = Math.sin(debrisTime * 4.0) * 0.003;
                const inwardPull = Math.cos(debrisTime * 3.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + spiralMotion;
                mesh.position.y = mesh.userData.originalPosition.y + inwardPull;
                
                // Debris energy intensity
                const debrisIntensity = 1.0 + Math.sin(debrisTime * 6.0) * 0.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * debrisIntensity;
                }
                
                // Debris scale variation (compression)
                const debrisScale = 0.8 + Math.sin(debrisTime * 5.0) * 0.3;
                mesh.scale.setScalar(debrisScale);
                
                // Debris opacity (energy discharge)
                const debrisOpacity = 0.6 + Math.sin(debrisTime * 7.0) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * debrisOpacity;
                }
            }
        });
    }

    // Animate space distortion (warped spacetime)
    const spaceDistortionGroup = cardGroup.getObjectByName('spaceDistortionGroup');
    if (spaceDistortionGroup) {
        spaceDistortionGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.distortionPhase !== undefined) {
                const distortionTime = spacialWarp + mesh.userData.distortionPhase;
                
                // Spacetime warping
                const spaceWarp = Math.sin(distortionTime * 2.5) * 0.004;
                const timeDistortion = Math.cos(distortionTime * 1.8) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + spaceWarp;
                mesh.position.y = mesh.userData.originalPosition.y + timeDistortion;
                
                // Distortion intensity fluctuation
                const distortionIntensity = 1.0 + Math.sin(distortionTime * 3.5) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * distortionIntensity;
                }
                
                // Distortion scale warping
                const distortionScale = 0.9 + Math.sin(distortionTime * 3.0) * 0.2;
                mesh.scale.setScalar(distortionScale);
            }
        });
    }

    // Overall gravity well presence (epic gravitational magic)
    const gravityPulse = 1 + Math.sin(time * 2.5) * 0.06;
    const gravityShift = Math.cos(time * 5.0) * 0.0008;
    cardGroup.scale.setScalar(0.8 * gravityPulse);
    cardGroup.position.x += gravityShift;
    cardGroup.position.z += gravityShift * 0.8;
}

// Export the gravity well card data for the loot system
export const GRAVITY_WELL_CARD_DATA = {
    name: "Gravity Well",
    description: 'Creates a gravitational field at target location that pulls enemies toward the center for 6 seconds, dealing damage over time.',
    category: 'card',
    rarity: 'epic',
    effect: 'gravity_well',
    effectValue: 6, // Duration in seconds
    createFunction: createGravityWellCard,
    updateFunction: updateGravityWellCardAnimation,
    voxelModel: 'gravity_well_card',
    glow: {
        color: 0x4B0082,
        intensity: 1.6
    }
};

export default createGravityWellCard;