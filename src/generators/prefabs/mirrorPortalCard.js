import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Mirror Portal Card Prefab
 * Creates linked portals for instant travel between two locations
 */

// Mirror portal specific colors
const PORTAL_COLORS = {
    PORTAL_BLUE: 0x00BFFF,          // Deep sky blue portal energy
    PORTAL_PURPLE: 0x8A2BE2,        // Blue violet portal rim
    MIRROR_SILVER: 0xC0C0C0,        // Silver mirror frame
    ENERGY_WHITE: 0xFFFFFF,         // White portal energy
    PORTAL_CYAN: 0x00FFFF,          // Cyan portal glow
    REFLECTION_GOLD: 0xFFD700       // Golden reflection effects
};

/**
 * Create a mirror portal card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The mirror portal card 3D model
 */
export function createMirrorPortalCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'MirrorPortalCard';

    // Mirror portal materials
    const portalBlueMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.PORTAL_BLUE,
        emissive: PORTAL_COLORS.PORTAL_BLUE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const portalPurpleMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.PORTAL_PURPLE,
        emissive: PORTAL_COLORS.PORTAL_PURPLE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const mirrorSilverMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.MIRROR_SILVER,
        emissive: PORTAL_COLORS.MIRROR_SILVER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.95
    });

    const energyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.ENERGY_WHITE,
        emissive: PORTAL_COLORS.ENERGY_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const portalCyanMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.PORTAL_CYAN,
        emissive: PORTAL_COLORS.PORTAL_CYAN,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const reflectionGoldMaterial = new THREE.MeshLambertMaterial({
        color: PORTAL_COLORS.REFLECTION_GOLD,
        emissive: PORTAL_COLORS.REFLECTION_GOLD,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Mirror frame (circular portal frame)
    const mirrorFrameVoxels = [
        // Outer frame ring
        { x: -0.20, y: 0.16, z: 0, material: mirrorSilverMaterial },
        { x: -0.16, y: 0.20, z: 0, material: mirrorSilverMaterial },
        { x: 0.0, y: 0.24, z: 0, material: mirrorSilverMaterial },
        { x: 0.16, y: 0.20, z: 0, material: mirrorSilverMaterial },
        { x: 0.20, y: 0.16, z: 0, material: mirrorSilverMaterial },
        { x: 0.20, y: 0.0, z: 0, material: mirrorSilverMaterial },
        { x: 0.20, y: -0.16, z: 0, material: mirrorSilverMaterial },
        { x: 0.16, y: -0.20, z: 0, material: mirrorSilverMaterial },
        { x: 0.0, y: -0.24, z: 0, material: mirrorSilverMaterial },
        { x: -0.16, y: -0.20, z: 0, material: mirrorSilverMaterial },
        { x: -0.20, y: -0.16, z: 0, material: mirrorSilverMaterial },
        { x: -0.20, y: 0.0, z: 0, material: mirrorSilverMaterial },
        
        // Inner frame detail
        { x: -0.12, y: 0.12, z: 0, material: reflectionGoldMaterial },
        { x: 0.12, y: 0.12, z: 0, material: reflectionGoldMaterial },
        { x: 0.12, y: -0.12, z: 0, material: reflectionGoldMaterial },
        { x: -0.12, y: -0.12, z: 0, material: reflectionGoldMaterial }
    ];

    // Portal energy (swirling portal center)
    const portalEnergyVoxels = [
        // Central portal vortex
        { x: 0.0, y: 0.0, z: 0.02, material: portalBlueMaterial },
        { x: -0.04, y: 0.04, z: 0.02, material: portalPurpleMaterial },
        { x: 0.04, y: 0.04, z: 0.02, material: portalPurpleMaterial },
        { x: 0.04, y: -0.04, z: 0.02, material: portalPurpleMaterial },
        { x: -0.04, y: -0.04, z: 0.02, material: portalPurpleMaterial },
        
        // Inner energy ring
        { x: -0.08, y: 0.0, z: 0.02, material: portalCyanMaterial },
        { x: 0.08, y: 0.0, z: 0.02, material: portalCyanMaterial },
        { x: 0.0, y: 0.08, z: 0.02, material: portalCyanMaterial },
        { x: 0.0, y: -0.08, z: 0.02, material: portalCyanMaterial },
        
        // Outer energy ring
        { x: -0.12, y: 0.0, z: 0.01, material: energyWhiteMaterial },
        { x: 0.12, y: 0.0, z: 0.01, material: energyWhiteMaterial },
        { x: 0.0, y: 0.12, z: 0.01, material: energyWhiteMaterial },
        { x: 0.0, y: -0.12, z: 0.01, material: energyWhiteMaterial }
    ];

    // Portal sparkles (floating energy particles)
    const portalSparklesVoxels = [
        // Floating sparkles around portal
        { x: -0.28, y: 0.08, z: 0.04, material: energyWhiteMaterial },
        { x: 0.28, y: 0.08, z: -0.04, material: energyWhiteMaterial },
        { x: -0.08, y: 0.28, z: 0.08, material: portalCyanMaterial },
        { x: 0.08, y: 0.28, z: -0.08, material: portalCyanMaterial },
        { x: 0.28, y: -0.08, z: 0.04, material: energyWhiteMaterial },
        { x: -0.28, y: -0.08, z: -0.04, material: energyWhiteMaterial },
        { x: 0.08, y: -0.28, z: 0.08, material: portalCyanMaterial },
        { x: -0.08, y: -0.28, z: -0.08, material: portalCyanMaterial },
        
        // Secondary sparkles
        { x: -0.24, y: 0.20, z: 0.12, material: reflectionGoldMaterial },
        { x: 0.24, y: 0.20, z: -0.12, material: reflectionGoldMaterial },
        { x: 0.24, y: -0.20, z: 0.12, material: reflectionGoldMaterial },
        { x: -0.24, y: -0.20, z: -0.12, material: reflectionGoldMaterial },
        
        // Distant sparkles
        { x: -0.32, y: 0.0, z: 0.06, material: portalBlueMaterial },
        { x: 0.32, y: 0.0, z: -0.06, material: portalBlueMaterial },
        { x: 0.0, y: 0.32, z: 0.06, material: portalBlueMaterial },
        { x: 0.0, y: -0.32, z: -0.06, material: portalBlueMaterial }
    ];

    // Create mirror frame group
    const mirrorFrameGroup = new THREE.Group();
    mirrorFrameGroup.name = 'mirrorFrame';

    // Add mirror frame voxels
    mirrorFrameVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mirrorFrameGroup.add(mesh);
    });

    // Create portal energy group
    const portalEnergyGroup = new THREE.Group();
    portalEnergyGroup.name = 'portalEnergy';

    // Add portal energy voxels
    portalEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.3; // Stagger animation
        portalEnergyGroup.add(mesh);
    });

    // Create portal sparkles group
    const portalSparklesGroup = new THREE.Group();
    portalSparklesGroup.name = 'portalSparkles';

    // Add portal sparkles voxels
    portalSparklesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sparklePhase = index * 0.2; // Stagger animation
        portalSparklesGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(mirrorFrameGroup);
    cardGroup.add(portalEnergyGroup);
    cardGroup.add(portalSparklesGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        portalRotation: 0,
        energyPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update mirror portal card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateMirrorPortalCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.portalRotation += deltaTime * 2.0; // Portal rotation speed
    cardGroup.userData.energyPulse += deltaTime * 3.5; // Energy pulse speed

    const time = cardGroup.userData.animationTime;
    const portalRotation = cardGroup.userData.portalRotation;
    const energyPulse = cardGroup.userData.energyPulse;

    // Animate mirror frame (slow rotation for stability)
    const mirrorFrameGroup = cardGroup.getObjectByName('mirrorFrame');
    if (mirrorFrameGroup) {
        mirrorFrameGroup.rotation.z = portalRotation * 0.2;
        
        // Frame energy pulsing (silver glow)
        const framePulse = 0.4 + Math.sin(energyPulse * 1.8) * 0.3;
        mirrorFrameGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * framePulse;
            }
        });
    }

    // Animate portal energy (swirling vortex)
    const portalEnergyGroup = cardGroup.getObjectByName('portalEnergy');
    if (portalEnergyGroup) {
        portalEnergyGroup.rotation.z = -portalRotation * 1.5; // Counter-rotate faster
        
        // Portal energy swirling and pulsing
        portalEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = energyPulse + mesh.userData.energyPhase;
                
                // Swirling motion around center
                const swirl = Math.sin(energyTime * 2.5) * 0.005;
                mesh.position.x = mesh.userData.originalPosition.x + swirl;
                mesh.position.y = mesh.userData.originalPosition.y + swirl;
                
                // Portal energy pulsing
                const portalPulse = 0.6 + Math.sin(energyTime * 4.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * portalPulse;
                }
                
                // Portal depth oscillation
                const depthOscillation = Math.sin(energyTime * 3.2) * 0.008;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * (0.8 + depthOscillation);
                }
            }
        });
    }

    // Animate portal sparkles (floating and twinkling)
    const portalSparklesGroup = cardGroup.getObjectByName('portalSparkles');
    if (portalSparklesGroup) {
        portalSparklesGroup.rotation.z = portalRotation * 0.8; // Medium rotation
        
        // Portal sparkles floating and twinkling
        portalSparklesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparklePhase !== undefined) {
                const sparkleTime = energyPulse + mesh.userData.sparklePhase;
                
                // Floating motion
                const floatX = Math.sin(sparkleTime * 1.8) * 0.01;
                const floatY = Math.cos(sparkleTime * 1.5) * 0.01;
                const floatZ = Math.sin(sparkleTime * 2.2) * 0.01;
                
                mesh.position.x = mesh.userData.originalPosition.x + floatX;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                mesh.position.z = mesh.userData.originalPosition.z + floatZ;
                
                // Sparkle twinkling
                const twinkle = 0.3 + Math.sin(sparkleTime * 5.0) * 0.7;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * twinkle;
                }
                
                // Sparkle fading effect
                const sparkle = 0.6 + Math.sin(sparkleTime * 3.8) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * sparkle;
                }
            }
        });
    }

    // Gentle overall portal pulsing
    const portalPulseScale = 1 + Math.sin(time * 1.6) * 0.08;
    cardGroup.scale.setScalar(0.8 * portalPulseScale);
}

// Export the mirror portal card data for the loot system
export const MIRROR_PORTAL_CARD_DATA = {
    name: 'Mirror Portal',
    description: 'Creates two linked portals for instant travel. First use places Portal A, second use places Portal B, then travel instantly between them.',
    category: 'card',
    rarity: 'epic',
    effect: 'mirror_portal',
    effectValue: 2, // Number of portals created
    createFunction: createMirrorPortalCard,
    updateFunction: updateMirrorPortalCardAnimation,
    voxelModel: 'mirror_portal_card',
    glow: {
        color: 0x00BFFF,
        intensity: 1.4
    }
};

export default createMirrorPortalCard;