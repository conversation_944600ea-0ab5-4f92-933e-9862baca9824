import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Volcano Card Prefab
 * Creates an erupting volcano with lava flows and volcanic debris
 */

// Volcano specific colors
const VOLCANO_COLORS = {
    LAVA_RED: 0xFF4500,         // Bright lava red
    MOLTEN_ORANGE: 0xFF6347,    // Molten orange core
    VOLCANIC_YELLOW: 0xFFD700,  // Bright volcanic yellow
    MAGMA_CRIMSON: 0xDC143C,    // Deep magma crimson
    ASH_GRAY: 0x696969,         // Volcanic ash gray
    EMBER_WHITE: 0xFFF8DC,      // Hot ember white
    ROCK_BROWN: 0x8B4513,       // Volcanic rock brown
    OBSIDIAN_BLACK: 0x2F2F2F    // Obsidian black
};

/**
 * Create a volcano card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The volcano card 3D model
 */
export function createVolcanoCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'VolcanoCard';

    // Materials
    const lavaRedMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.LAVA_RED,
        emissive: VOLCANO_COLORS.LAVA_RED,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.95
    });

    const moltenOrangeMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.MOLTEN_ORANGE,
        emissive: VOLCANO_COLORS.MOLTEN_ORANGE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.9
    });

    const volcanicYellowMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.VOLCANIC_YELLOW,
        emissive: VOLCANO_COLORS.VOLCANIC_YELLOW,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.85
    });

    const magmaCrimsonMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.MAGMA_CRIMSON,
        emissive: VOLCANO_COLORS.MAGMA_CRIMSON,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const ashGrayMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.ASH_GRAY,
        emissive: VOLCANO_COLORS.ASH_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7
    });

    const emberWhiteMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.EMBER_WHITE,
        emissive: VOLCANO_COLORS.EMBER_WHITE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.8
    });

    const rockBrownMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.ROCK_BROWN,
        emissive: VOLCANO_COLORS.ROCK_BROWN,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.95
    });

    const obsidianBlackMaterial = new THREE.MeshLambertMaterial({
        color: VOLCANO_COLORS.OBSIDIAN_BLACK,
        emissive: VOLCANO_COLORS.OBSIDIAN_BLACK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create volcano mountain structure
    const volcanoVoxels = [
        // Base layer (wide foundation)
        { x: -3, y: -4, z: 0, material: rockBrownMaterial },
        { x: -2, y: -4, z: 0, material: rockBrownMaterial },
        { x: -1, y: -4, z: 0, material: rockBrownMaterial },
        { x: 0, y: -4, z: 0, material: obsidianBlackMaterial },
        { x: 1, y: -4, z: 0, material: rockBrownMaterial },
        { x: 2, y: -4, z: 0, material: rockBrownMaterial },
        { x: 3, y: -4, z: 0, material: rockBrownMaterial },

        // Second layer
        { x: -2, y: -3, z: 0, material: rockBrownMaterial },
        { x: -1, y: -3, z: 0, material: obsidianBlackMaterial },
        { x: 0, y: -3, z: 0, material: magmaCrimsonMaterial },
        { x: 1, y: -3, z: 0, material: obsidianBlackMaterial },
        { x: 2, y: -3, z: 0, material: rockBrownMaterial },

        // Third layer
        { x: -1, y: -2, z: 0, material: obsidianBlackMaterial },
        { x: 0, y: -2, z: 0, material: lavaRedMaterial },
        { x: 1, y: -2, z: 0, material: obsidianBlackMaterial },

        // Peak layer
        { x: 0, y: -1, z: 0, material: moltenOrangeMaterial },

        // Crater center (eruption source)
        { x: 0, y: 0, z: 0, material: volcanicYellowMaterial }
    ];

    // Erupting lava and debris
    const eruptionVoxels = [
        // Central eruption column
        { x: 0, y: 1, z: 0, material: lavaRedMaterial },
        { x: 0, y: 2, z: 0, material: moltenOrangeMaterial },
        { x: 0, y: 3, z: 0, material: volcanicYellowMaterial },
        { x: 0, y: 4, z: 0, material: emberWhiteMaterial },

        // Spreading lava chunks
        { x: -1, y: 1, z: 0, material: magmaCrimsonMaterial },
        { x: 1, y: 1, z: 0, material: magmaCrimsonMaterial },
        { x: -1, y: 2, z: 0, material: lavaRedMaterial },
        { x: 1, y: 2, z: 0, material: lavaRedMaterial },

        // Scattered debris
        { x: -2, y: 2, z: 0, material: obsidianBlackMaterial },
        { x: 2, y: 2, z: 0, material: obsidianBlackMaterial },
        { x: -2, y: 3, z: 0, material: ashGrayMaterial },
        { x: 2, y: 3, z: 0, material: ashGrayMaterial },

        // High flying debris
        { x: -1, y: 4, z: 0, material: rockBrownMaterial },
        { x: 1, y: 4, z: 0, material: rockBrownMaterial },
        { x: 0, y: 5, z: 0, material: ashGrayMaterial }
    ];

    // Lava flow streams
    const lavaFlowVoxels = [
        // Left flow
        { x: -2, y: -2, z: 0, material: lavaRedMaterial },
        { x: -3, y: -3, z: 0, material: moltenOrangeMaterial },
        { x: -4, y: -4, z: 0, material: magmaCrimsonMaterial },

        // Right flow
        { x: 2, y: -2, z: 0, material: lavaRedMaterial },
        { x: 3, y: -3, z: 0, material: moltenOrangeMaterial },
        { x: 4, y: -4, z: 0, material: magmaCrimsonMaterial },

        // Front flow (toward viewer)
        { x: 0, y: -5, z: 0, material: lavaRedMaterial },
        { x: 1, y: -5, z: 0, material: moltenOrangeMaterial },
        { x: -1, y: -5, z: 0, material: moltenOrangeMaterial }
    ];

    // Ash and smoke particles
    const ashCloudVoxels = [
        // Lower ash cloud
        { x: -3, y: 1, z: 0, material: ashGrayMaterial },
        { x: 3, y: 1, z: 0, material: ashGrayMaterial },
        { x: -2, y: 0, z: 0, material: ashGrayMaterial },
        { x: 2, y: 0, z: 0, material: ashGrayMaterial },

        // Mid-level ash
        { x: -3, y: 4, z: 0, material: ashGrayMaterial },
        { x: 3, y: 4, z: 0, material: ashGrayMaterial },
        { x: -4, y: 3, z: 0, material: ashGrayMaterial },
        { x: 4, y: 3, z: 0, material: ashGrayMaterial },

        // High ash cloud
        { x: -2, y: 5, z: 0, material: ashGrayMaterial },
        { x: 2, y: 5, z: 0, material: ashGrayMaterial },
        { x: 0, y: 6, z: 0, material: ashGrayMaterial },

        // Scattered embers
        { x: -4, y: 2, z: 0, material: emberWhiteMaterial },
        { x: 4, y: 2, z: 0, material: emberWhiteMaterial },
        { x: -3, y: 5, z: 0, material: emberWhiteMaterial },
        { x: 3, y: 5, z: 0, material: emberWhiteMaterial }
    ];

    // Create all voxels
    [...volcanoVoxels, ...eruptionVoxels, ...lavaFlowVoxels, ...ashCloudVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.6, // Compact horizontally
            (voxel.y + 2) * VOXEL_SIZE * 0.6, // Center vertically
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (volcanoVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'volcano';
        } else if (eruptionVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'eruption';
        } else if (lavaFlowVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'lavaFlow';
        } else {
            mesh.userData.voxelType = 'ashCloud';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        eruptionPulse: 0,
        lavaFlow: 0,
        ashDrift: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update volcano card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateVolcanoCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.eruptionPulse += deltaTime * 4.0;
    cardGroup.userData.lavaFlow += deltaTime * 2.5;
    cardGroup.userData.ashDrift += deltaTime * 1.8;

    const time = cardGroup.userData.animationTime;
    const eruptionPulse = cardGroup.userData.eruptionPulse;
    const lavaFlow = cardGroup.userData.lavaFlow;
    const ashDrift = cardGroup.userData.ashDrift;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'volcano':
                    // Volcano base glows with heat
                    const volcanoGlow = 0.8 + Math.sin(time * 2.0 + child.position.x + child.position.y) * 0.4;
                    child.material.emissiveIntensity = baseEmissive * volcanoGlow;
                    break;

                case 'eruption':
                    // Eruption voxels pulse violently
                    const eruptionIntensity = 0.6 + Math.sin(eruptionPulse * 6.0 + child.position.y * 2) * 0.8;
                    const eruptionMotion = Math.sin(eruptionPulse * 3.0 + child.position.y) * 0.02;
                    
                    child.material.emissiveIntensity = baseEmissive * eruptionIntensity;
                    child.material.opacity = baseOpacity * (0.7 + eruptionIntensity * 0.3);
                    child.position.x += eruptionMotion;
                    child.position.z += eruptionMotion * 0.5;
                    break;

                case 'lavaFlow':
                    // Lava flows pulse and shimmer
                    const flowPulse = 0.7 + Math.sin(lavaFlow * 3.5 + child.position.x * 1.5) * 0.5;
                    const flowShimmer = Math.cos(lavaFlow * 4.0 + child.position.y) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * flowPulse;
                    child.position.y += flowShimmer;
                    break;

                case 'ashCloud':
                    // Ash drifts and fades
                    const ashFade = 0.4 + Math.sin(ashDrift * 2.0 + child.position.x + child.position.y) * 0.6;
                    const ashDriftMotion = Math.sin(ashDrift + child.position.x * 2) * 0.015;
                    
                    child.material.opacity = baseOpacity * ashFade;
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = baseEmissive * ashFade;
                    }
                    child.position.x += ashDriftMotion;
                    child.position.y += Math.abs(ashDriftMotion) * 0.5; // Ash rises
                    break;
            }
        }
    });

    // Overall volcanic rumbling
    const rumble = Math.sin(time * 8.0) * 0.005;
    cardGroup.position.y += rumble;
    cardGroup.position.z += rumble * 0.3;
}

// Export the volcano card data for the loot system
export const VOLCANO_CARD_DATA = {
    name: 'Volcano',
    description: 'Erupts a massive volcano from the ground, dealing devastating fire damage to all enemies in a large area with molten lava and volcanic debris.',
    category: 'card',
    rarity: 'epic',
    effect: 'volcano',
    effectValue: 120,
    createFunction: createVolcanoCard,
    updateFunction: updateVolcanoCardAnimation,
    voxelModel: 'volcano_card',
    glow: {
        color: 0xFF4500,
        intensity: 1.8
    }
};

export default createVolcanoCard;