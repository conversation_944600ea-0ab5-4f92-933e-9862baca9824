import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a temporal rift - the centerpiece of the Chronal Anomaly room
 * A swirling vortex of temporal energy with crystalline framework
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Temporal rift object group
 */
export function createTemporalRiftObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 2147);

    // Use medium-large voxel size for impressive central feature (follows pond scaling)
    const riftVoxelSize = VOXEL_SIZE * 3.5;
    const baseGeometry = getOrCreateGeometry('temporal_rift_voxel', () =>
        new THREE.BoxGeometry(riftVoxelSize, riftVoxelSize, riftVoxelSize)
    );

    // Temporal rift materials - mystical purple-blue energy
    const riftCoreMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x8A2BE2),
        emissiveIntensity: 0.8,
        roughness: 0.0,
        metalness: 0.0
    }); // Blue-violet core energy

    const riftRingMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x9370DB),
        emissiveIntensity: 0.6,
        roughness: 0.1,
        metalness: 0.1
    }); // Medium orchid energy rings

    const temporalCrystalMaterial = _getMaterialByHex_Cached('DDA0DD', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0xDDA0DD),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.3
    }); // Plum crystal framework

    const darkStoneMaterial = _getMaterialByHex_Cached('2F2F4F', {
        roughness: 0.8,
        metalness: 0.2,
        emissive: new THREE.Color(0x191970),
        emissiveIntensity: 0.1
    }); // Midnight blue stone base

    const energySparkleMaterial = _getMaterialByHex_Cached('E6E6FA', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0xE6E6FA),
        emissiveIntensity: 0.9,
        roughness: 0.0,
        metalness: 0.0
    }); // Lavender energy sparkles

    // Stone base platform (ancient altar)
    const basePositions = [
        // Central platform (5x5)
        { x: -2, y: 0, z: -2 }, { x: -1, y: 0, z: -2 }, { x: 0, y: 0, z: -2 }, { x: 1, y: 0, z: -2 }, { x: 2, y: 0, z: -2 },
        { x: -2, y: 0, z: -1 }, { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 }, { x: 2, y: 0, z: -1 },
        { x: -2, y: 0, z: 0 },  { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },  { x: 2, y: 0, z: 0 },
        { x: -2, y: 0, z: 1 },  { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 },  { x: 2, y: 0, z: 1 },
        { x: -2, y: 0, z: 2 },  { x: -1, y: 0, z: 2 },  { x: 0, y: 0, z: 2 },  { x: 1, y: 0, z: 2 },  { x: 2, y: 0, z: 2 },
        
        // Raised center (3x3)
        { x: -1, y: 1, z: -1 }, { x: 0, y: 1, z: -1 }, { x: 1, y: 1, z: -1 },
        { x: -1, y: 1, z: 0 },  { x: 0, y: 1, z: 0 },  { x: 1, y: 1, z: 0 },
        { x: -1, y: 1, z: 1 },  { x: 0, y: 1, z: 1 },  { x: 1, y: 1, z: 1 }
    ];

    // Add stone base
    basePositions.forEach(pos => {
        const stone = new THREE.Mesh(baseGeometry.clone(), darkStoneMaterial);
        stone.position.set(
            pos.x * riftVoxelSize,
            pos.y * riftVoxelSize,
            pos.z * riftVoxelSize
        );
        stone.castShadow = true;
        stone.receiveShadow = true;
        group.add(stone);
    });

    // Crystal framework around the rift (4 corner crystals)
    const crystalPositions = [
        { x: -2, y: 2, z: -2 }, { x: 2, y: 2, z: -2 },
        { x: -2, y: 2, z: 2 },  { x: 2, y: 2, z: 2 },
        // Mid-height crystals
        { x: -2, y: 3, z: 0 },  { x: 2, y: 3, z: 0 },
        { x: 0, y: 3, z: -2 },  { x: 0, y: 3, z: 2 },
        // Top crystals
        { x: -1, y: 4, z: -1 }, { x: 1, y: 4, z: -1 },
        { x: -1, y: 4, z: 1 },  { x: 1, y: 4, z: 1 }
    ];

    crystalPositions.forEach((pos, index) => {
        const crystal = new THREE.Mesh(
            getOrCreateGeometry('temporal_crystal', () => {
                // Create octahedral crystal shape
                const geo = new THREE.OctahedronGeometry(riftVoxelSize * 0.4, 0);
                return geo;
            }),
            temporalCrystalMaterial
        );
        crystal.position.set(
            pos.x * riftVoxelSize,
            pos.y * riftVoxelSize,
            pos.z * riftVoxelSize
        );
        crystal.rotation.x = Math.PI / 4 + (index * Math.PI / 6);
        crystal.rotation.y = index * Math.PI / 3;
        crystal.userData.isTemporalCrystal = true;
        crystal.userData.crystalIndex = index;
        crystal.userData.baseIntensity = 0.4;
        crystal.castShadow = false;
        crystal.receiveShadow = false;
        group.add(crystal);
    });

    // Central rift core (swirling energy)
    const riftCorePositions = [
        // Central energy core
        { x: 0, y: 2, z: 0 },
        // Inner energy ring
        { x: -1, y: 2, z: 0 }, { x: 1, y: 2, z: 0 },
        { x: 0, y: 2, z: -1 }, { x: 0, y: 2, z: 1 },
        // Vertical energy column
        { x: 0, y: 3, z: 0 }, { x: 0, y: 4, z: 0 }, { x: 0, y: 5, z: 0 }
    ];

    riftCorePositions.forEach((pos, index) => {
        const energy = new THREE.Mesh(baseGeometry.clone(), riftCoreMaterial);
        energy.position.set(
            pos.x * riftVoxelSize,
            pos.y * riftVoxelSize,
            pos.z * riftVoxelSize
        );
        
        // Scale core elements differently
        if (index === 0) {
            energy.scale.set(1.2, 1.2, 1.2); // Central core larger
        } else if (index < 5) {
            energy.scale.set(0.8, 0.8, 0.8); // Ring elements smaller
        } else {
            energy.scale.set(0.6, 1.0, 0.6); // Vertical column narrower
        }
        
        energy.userData.isRiftCore = true;
        energy.userData.coreIndex = index;
        energy.userData.baseIntensity = 0.8;
        energy.castShadow = false;
        energy.receiveShadow = false;
        group.add(energy);
    });

    // Energy rings (outer swirls)
    const ringPositions = [
        // Lower ring
        { x: -2, y: 2, z: 0 }, { x: -1, y: 2, z: -1 }, { x: 0, y: 2, z: -2 }, { x: 1, y: 2, z: -1 }, { x: 2, y: 2, z: 0 },
        { x: 1, y: 2, z: 1 }, { x: 0, y: 2, z: 2 }, { x: -1, y: 2, z: 1 },
        // Upper ring
        { x: -1.5, y: 3, z: 0 }, { x: 0, y: 3, z: -1.5 }, { x: 1.5, y: 3, z: 0 }, { x: 0, y: 3, z: 1.5 },
        // Top ring
        { x: -1, y: 4, z: 0 }, { x: 0, y: 4, z: -1 }, { x: 1, y: 4, z: 0 }, { x: 0, y: 4, z: 1 }
    ];

    ringPositions.forEach((pos, index) => {
        if (rng() > 0.3) { // 70% chance for each ring element
            const ring = new THREE.Mesh(baseGeometry.clone(), riftRingMaterial);
            ring.position.set(
                pos.x * riftVoxelSize,
                pos.y * riftVoxelSize,
                pos.z * riftVoxelSize
            );
            ring.scale.set(0.6, 0.6, 0.6);
            ring.userData.isRiftRing = true;
            ring.userData.ringIndex = index;
            ring.userData.baseIntensity = 0.6;
            ring.castShadow = false;
            ring.receiveShadow = false;
            group.add(ring);
        }
    });

    // Energy sparkles (floating around the rift)
    const sparklePositions = [
        { x: -3, y: 3, z: 1 }, { x: 3, y: 3, z: -1 }, { x: 1, y: 4, z: 3 }, { x: -1, y: 4, z: -3 },
        { x: -2, y: 5, z: 2 }, { x: 2, y: 5, z: -2 }, { x: 0, y: 6, z: 0 },
        { x: -1, y: 3.5, z: 2.5 }, { x: 1.5, y: 3.5, z: -2 }, { x: -2.5, y: 4.5, z: -1 }
    ];

    sparklePositions.forEach((pos, index) => {
        if (rng() > 0.4) { // 60% chance for each sparkle
            const sparkle = new THREE.Mesh(
                getOrCreateGeometry('energy_sparkle', () =>
                    new THREE.BoxGeometry(riftVoxelSize * 0.3, riftVoxelSize * 0.3, riftVoxelSize * 0.3)
                ),
                energySparkleMaterial
            );
            sparkle.position.set(
                pos.x * riftVoxelSize,
                pos.y * riftVoxelSize,
                pos.z * riftVoxelSize
            );
            sparkle.userData.isEnergySparkle = true;
            sparkle.userData.sparkleIndex = index;
            sparkle.userData.baseX = pos.x * riftVoxelSize;
            sparkle.userData.baseY = pos.y * riftVoxelSize;
            sparkle.userData.baseZ = pos.z * riftVoxelSize;
            sparkle.userData.orbitSpeed = 0.3 + rng() * 0.4;
            sparkle.scale.set(0.4, 0.4, 0.4);
            sparkle.castShadow = false;
            sparkle.receiveShadow = false;
            group.add(sparkle);
        }
    });

    // Create point light for the temporal rift
    const riftLight = new THREE.PointLight(0x8A2BE2, 3.0, 20, 1.5);
    riftLight.position.set(0, 3 * riftVoxelSize, 0);
    riftLight.castShadow = true;
    riftLight.shadow.mapSize.width = 1024;
    riftLight.shadow.mapSize.height = 1024;
    riftLight.shadow.camera.near = 0.1;
    riftLight.shadow.camera.far = 25;
    riftLight.userData.originalIntensity = 3.0;
    riftLight.userData.isPulsing = true;
    group.add(riftLight);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'temporal_rift',
        isInteractable: true,
        interactionType: 'temporal_rift',
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: riftVoxelSize,
        hasTemporalAnimation: true,
        hasEnergyPulse: true,
        riftLight: riftLight
    };

    group.name = 'temporal_rift';
    console.log('[TemporalRift] ✅ Created swirling temporal rift with energy framework');
    return group;
}