import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Mystical Stone Colors (matching pond room walls) ---
// Adapted from mystical stone brick wall colors
const MYSTICAL_STONE_COLORS = [
    '9B7EBD', // Mystical purple-blue (primary wall color)
    '8A7CA8', // Darker mystical purple (secondary)
    '7A6C98', // Even darker mystical
    '6A5C88', // Deep mystical purple
    '5A4C78', // Very deep mystical
    '4A3C68', // Almost dark mystical
    '3A3A5A', // Mystical purple-gray
    '2A2A4A'  // Very dark mystical
];

const MYSTICAL_ACCENT = '9B7EBD'; // Primary mystical color for highlights

// Define mystical stalagmite formation (wall-connected, matching wall materials)
const mysticalStalagmiteFormation = [
    // === FLOOR BASE (5x5 like crystal cave base) ===
    // Layer 1 - Floor base (Y=0)
    { x: -2, y: 0, z: -2, c: MYSTICAL_STONE_COLORS[0] },
    { x: -1, y: 0, z: -2, c: MYSTICAL_STONE_COLORS[1] },
    { x: 0, y: 0, z: -2, c: MYSTICAL_STONE_COLORS[0] },
    { x: 1, y: 0, z: -2, c: MYSTICAL_STONE_COLORS[1] },
    { x: 2, y: 0, z: -2, c: MYSTICAL_STONE_COLORS[0] },
    
    { x: -2, y: 0, z: -1, c: MYSTICAL_STONE_COLORS[1] },
    { x: -1, y: 0, z: -1, c: MYSTICAL_STONE_COLORS[0] },
    { x: 0, y: 0, z: -1, c: MYSTICAL_STONE_COLORS[1] },
    { x: 1, y: 0, z: -1, c: MYSTICAL_STONE_COLORS[0] },
    { x: 2, y: 0, z: -1, c: MYSTICAL_STONE_COLORS[1] },
    
    { x: -2, y: 0, z: 0, c: MYSTICAL_STONE_COLORS[0] },
    { x: -1, y: 0, z: 0, c: MYSTICAL_STONE_COLORS[1] },
    { x: 0, y: 0, z: 0, c: MYSTICAL_STONE_COLORS[0] },
    { x: 1, y: 0, z: 0, c: MYSTICAL_STONE_COLORS[1] },
    { x: 2, y: 0, z: 0, c: MYSTICAL_STONE_COLORS[0] },
    
    { x: -2, y: 0, z: 1, c: MYSTICAL_STONE_COLORS[1] },
    { x: -1, y: 0, z: 1, c: MYSTICAL_STONE_COLORS[0] },
    { x: 0, y: 0, z: 1, c: MYSTICAL_STONE_COLORS[1] },
    { x: 1, y: 0, z: 1, c: MYSTICAL_STONE_COLORS[0] },
    { x: 2, y: 0, z: 1, c: MYSTICAL_STONE_COLORS[1] },
    
    { x: -2, y: 0, z: 2, c: MYSTICAL_STONE_COLORS[0] },
    { x: -1, y: 0, z: 2, c: MYSTICAL_STONE_COLORS[1] },
    { x: 0, y: 0, z: 2, c: MYSTICAL_STONE_COLORS[0] },
    { x: 1, y: 0, z: 2, c: MYSTICAL_STONE_COLORS[1] },
    { x: 2, y: 0, z: 2, c: MYSTICAL_STONE_COLORS[0] },

    // === LOWER STALAGMITE BODY (Y=1) ===
    { x: -1, y: 1, z: -1, c: MYSTICAL_STONE_COLORS[2] },
    { x: 0, y: 1, z: -1, c: MYSTICAL_STONE_COLORS[1] },
    { x: 1, y: 1, z: -1, c: MYSTICAL_STONE_COLORS[2] },
    { x: -1, y: 1, z: 0, c: MYSTICAL_STONE_COLORS[1] },
    { x: 0, y: 1, z: 0, c: MYSTICAL_STONE_COLORS[0] },
    { x: 1, y: 1, z: 0, c: MYSTICAL_STONE_COLORS[1] },
    { x: -1, y: 1, z: 1, c: MYSTICAL_STONE_COLORS[2] },
    { x: 0, y: 1, z: 1, c: MYSTICAL_STONE_COLORS[1] },
    { x: 1, y: 1, z: 1, c: MYSTICAL_STONE_COLORS[2] },

    // === MID-LOWER BODY (Y=2) ===
    { x: -1, y: 2, z: 0, c: MYSTICAL_STONE_COLORS[3] },
    { x: 0, y: 2, z: -1, c: MYSTICAL_STONE_COLORS[2] },
    { x: 0, y: 2, z: 0, c: MYSTICAL_STONE_COLORS[1] },
    { x: 0, y: 2, z: 1, c: MYSTICAL_STONE_COLORS[2] },
    { x: 1, y: 2, z: 0, c: MYSTICAL_STONE_COLORS[3] },

    // === CENTRAL BODY (Y=3) ===
    { x: 0, y: 3, z: -1, c: MYSTICAL_STONE_COLORS[4] },
    { x: 0, y: 3, z: 0, c: MYSTICAL_STONE_COLORS[2] },
    { x: 0, y: 3, z: 1, c: MYSTICAL_STONE_COLORS[4] },

    // === MID-UPPER BODY (Y=4) ===
    { x: 0, y: 4, z: 0, c: MYSTICAL_STONE_COLORS[3] },
    { x: 0, y: 4, z: -1, c: MYSTICAL_STONE_COLORS[5] },
    { x: 0, y: 4, z: 1, c: MYSTICAL_STONE_COLORS[5] },

    // === UPPER BODY (Y=5) ===
    { x: 0, y: 5, z: 0, c: MYSTICAL_STONE_COLORS[4] },

    // === NEAR TIP (Y=6) ===
    { x: 0, y: 6, z: 0, c: MYSTICAL_STONE_COLORS[6] },

    // === TIP POINT (Y=7) ===
    { x: 0, y: 7, z: 0, c: MYSTICAL_STONE_COLORS[7] },

    // === MYSTICAL ACCENT HIGHLIGHTS (matching wall material) ===
    { x: -1, y: 1, z: -1, c: MYSTICAL_ACCENT }, // Mystical accent on lower body
    { x: 1, y: 2, z: 1, c: MYSTICAL_ACCENT },   // Mystical accent on mid body
    { x: 0, y: 3, z: 0, c: MYSTICAL_ACCENT },   // Mystical accent on central core
];

/**
 * Create a mystical stalagmite that matches pond room wall materials
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Mystical stalagmite object group
 */
export function createMysticalStalagmiteObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888);

    // Use HALF FLOOR VOXEL SIZE for smaller stalagmites (VOXEL_SIZE * 3 = half floor scale)
    const stalagmiteVoxelSize = VOXEL_SIZE * 3;
    const baseGeometry = getOrCreateGeometry('mystical_stalagmite_voxel', () =>
        new THREE.BoxGeometry(stalagmiteVoxelSize, stalagmiteVoxelSize, stalagmiteVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the stalagmite shape
    mysticalStalagmiteFormation.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to mystical stone color for weathered effect
        let finalColor = c;
        if (rng() < 0.25) { // 25% chance for weathering
            const colorIndex = Math.floor(rng() * MYSTICAL_STONE_COLORS.length);
            finalColor = MYSTICAL_STONE_COLORS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * stalagmiteVoxelSize,
            y * stalagmiteVoxelSize,
            z * stalagmiteVoxelSize
        );

        const voxelGeo = baseGeometry.clone();
        voxelGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeo);
    });

    // Merge geometries by material and create meshes
    for (const colorHex in geometriesByMaterial) {
        if (geometriesByMaterial[colorHex].length > 0) {
            const mergedGeo = BufferGeometryUtils.mergeGeometries(
                geometriesByMaterial[colorHex], 
                false
            );

            // Create material with mystical properties
            const material = _getMaterialByHex_Cached(colorHex, {
                roughness: 0.8,
                metalness: 0.1,
                emissive: new THREE.Color(`0x${colorHex}`).multiplyScalar(0.1), // Slight mystical glow
                emissiveIntensity: 0.2
            });

            const mesh = new THREE.Mesh(mergedGeo, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            group.add(mesh);
        }
    }

    // Add metadata
    group.userData = {
        type: 'mystical_stalagmite',
        isDecorative: true,
        hasCollision: true,
        wallConnected: true,
        mysticalMaterial: true
    };

    console.log("Created Mystical Stalagmite (Wall-Connected)");
    return group;
}
