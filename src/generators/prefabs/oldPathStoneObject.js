import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a single weathered path stone - for building old stone paths
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Single path stone object
 */
export function createOldPathStoneObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    
    // Dark weathered stone materials - darker colors to match guardian theme
    const stoneColors = ['5A4A5A', '4A3A4A', '6B5B73', '555555', '666666'];
    const stoneMaterial = _getMaterialByHex_Cached(
        stoneColors[Math.floor(seed * stoneColors.length)], 
        {
            roughness: 0.8,
            metalness: 0.05
        }
    );
    
    // Large weathered stone dimensions (doubled width/depth, reduced height)
    const baseWidth = VOXEL_SIZE * (16 + seed * 8); // 16-24 voxels wide (doubled)
    const baseHeight = VOXEL_SIZE * (2 + seed * 1); // 2-3 voxels high (lower)
    const baseDepth = VOXEL_SIZE * (12 + seed * 6); // 12-18 voxels deep (doubled)
    
    // Create the main stone block
    const stoneGeometry = new THREE.BoxGeometry(baseWidth, baseHeight, baseDepth);
    const stone = new THREE.Mesh(stoneGeometry, stoneMaterial);
    
    // Position stone slightly above ground
    stone.position.set(0, baseHeight / 2, 0);
    
    // Add weathered rotation for natural look
    stone.rotation.x = (seed - 0.5) * 0.1;
    stone.rotation.y = (seed - 0.5) * 0.4; 
    stone.rotation.z = (seed - 0.5) * 0.1;
    
    // Enable shadows
    stone.castShadow = true;
    stone.receiveShadow = true;
    
    group.add(stone);
    
    // Set group properties
    group.name = "oldPathStoneObject";
    group.userData.isInteriorObject = true;
    group.userData.objectType = 'old_path_stone';
    
    return group;
}