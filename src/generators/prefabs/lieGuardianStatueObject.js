import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Lie Guardian Statue - Always lies
 * A darker stone guardian with sinister features and glowing red eyes
 */
export function createLieGuardianStatueObject(options = {}) {
    const group = new THREE.Group();
    
    // Base materials
    const darkStoneMaterial = new THREE.MeshStandardMaterial({
        color: 0x696969, // Dark gray stone
        roughness: 0.8,
        metalness: 0.1
    });
    
    const lieGlowMaterial = new THREE.MeshStandardMaterial({
        color: 0xDC143C, // Crimson lie glow
        emissive: 0x8B0000,
        emissiveIntensity: 0.4,
        roughness: 0.3,
        metalness: 0.3
    });
    
    // Create statue base/pedestal
    const baseGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 3, 
        VOXEL_SIZE * 1, 
        VOXEL_SIZE * 3
    );
    const base = new THREE.Mesh(baseGeometry, darkStoneMaterial);
    base.position.set(0, VOXEL_SIZE * 0.5, 0);
    base.castShadow = true;
    base.receiveShadow = true;
    group.add(base);
    
    // Create guardian torso (slightly hunched)
    const torsoGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 2, 
        VOXEL_SIZE * 3, 
        VOXEL_SIZE * 1
    );
    const torso = new THREE.Mesh(torsoGeometry, darkStoneMaterial);
    torso.position.set(0, VOXEL_SIZE * 2.4, VOXEL_SIZE * 0.1); // Slightly forward hunched
    torso.rotation.x = 0.1; // Slight forward lean
    torso.castShadow = true;
    group.add(torso);
    
    // Create guardian head (tilted menacingly)
    const headGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 1.5, 
        VOXEL_SIZE * 1.5, 
        VOXEL_SIZE * 1
    );
    const head = new THREE.Mesh(headGeometry, darkStoneMaterial);
    head.position.set(0, VOXEL_SIZE * 4.6, VOXEL_SIZE * 0.2);
    head.rotation.z = 0.1; // Slight tilt
    head.castShadow = true;
    group.add(head);
    
    // Create glowing lie eyes (more angular/menacing)
    const eyeGeometry = new THREE.ConeGeometry(VOXEL_SIZE * 0.15, VOXEL_SIZE * 0.3, 6);
    
    const leftEye = new THREE.Mesh(eyeGeometry, lieGlowMaterial);
    leftEye.position.set(-VOXEL_SIZE * 0.3, VOXEL_SIZE * 4.8, VOXEL_SIZE * 0.6);
    leftEye.rotation.x = Math.PI / 2;
    group.add(leftEye);
    
    const rightEye = new THREE.Mesh(eyeGeometry, lieGlowMaterial);
    rightEye.position.set(VOXEL_SIZE * 0.3, VOXEL_SIZE * 4.8, VOXEL_SIZE * 0.6);
    rightEye.rotation.x = Math.PI / 2;
    group.add(rightEye);
    
    // Create guardian arms (more threatening pose)
    const armGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 0.8, 
        VOXEL_SIZE * 2.5, 
        VOXEL_SIZE * 0.8
    );
    
    const leftArm = new THREE.Mesh(armGeometry, darkStoneMaterial);
    leftArm.position.set(-VOXEL_SIZE * 1.3, VOXEL_SIZE * 2.3, VOXEL_SIZE * 0.2);
    leftArm.rotation.z = 0.2; // Slightly raised
    leftArm.castShadow = true;
    group.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, darkStoneMaterial);
    rightArm.position.set(VOXEL_SIZE * 1.3, VOXEL_SIZE * 2.3, VOXEL_SIZE * 0.2);
    rightArm.rotation.z = -0.2; // Slightly raised
    rightArm.castShadow = true;
    group.add(rightArm);
    
    // Create deception symbol on chest (inverted triangle)
    const symbolGeometry = new THREE.ConeGeometry(
        VOXEL_SIZE * 0.3, 
        VOXEL_SIZE * 0.4, 
        3
    );
    const lieSymbol = new THREE.Mesh(symbolGeometry, lieGlowMaterial);
    lieSymbol.position.set(0, VOXEL_SIZE * 2.8, VOXEL_SIZE * 0.6);
    lieSymbol.rotation.x = Math.PI / 2;
    lieSymbol.rotation.z = Math.PI; // Inverted
    group.add(lieSymbol);
    
    // Add deception aura light (darker, more ominous)
    const lieLight = new THREE.PointLight(0xDC143C, 1.2, VOXEL_SIZE * 8);
    lieLight.position.set(0, VOXEL_SIZE * 5, 0);
    lieLight.castShadow = true;
    group.add(lieLight);
    
    // Add some menacing spikes/horns
    const spikeGeometry = new THREE.ConeGeometry(VOXEL_SIZE * 0.1, VOXEL_SIZE * 0.5, 4);
    
    const leftHorn = new THREE.Mesh(spikeGeometry, darkStoneMaterial);
    leftHorn.position.set(-VOXEL_SIZE * 0.4, VOXEL_SIZE * 5.5, VOXEL_SIZE * 0.2);
    leftHorn.rotation.z = -0.3;
    group.add(leftHorn);
    
    const rightHorn = new THREE.Mesh(spikeGeometry, darkStoneMaterial);
    rightHorn.position.set(VOXEL_SIZE * 0.4, VOXEL_SIZE * 5.5, VOXEL_SIZE * 0.2);
    rightHorn.rotation.z = 0.3;
    group.add(rightHorn);
    
    // Set up group properties
    group.name = 'lie_guardian_statue';
    group.userData = {
        objectType: 'lie_guardian_statue',
        isInteractable: true,
        guardianType: 'lie',
        alwaysLies: true,
        canSpeak: true,
        ...options.userData
    };
    
    // Store references for animation
    group.userData.eyes = [leftEye, rightEye];
    group.userData.lieSymbol = lieSymbol;
    group.userData.lieLight = lieLight;
    
    return group;
}