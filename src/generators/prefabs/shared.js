import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import { VoxelInstanceManager } from '../../systems/VoxelInstanceManager.js';

// --- Simple Seeded PRNG (Mulberry32) ---
export function mulberry32(seed) {
    return function() {
      seed |= 0; seed = seed + 0x6D2B79F5 | 0;
      let t = Math.imul(seed ^ seed >>> 15, 1 | seed);
      t = t + Math.imul(t ^ t >>> 7, 61 | t) ^ t;
      return ((t ^ t >>> 14) >>> 0) / 4294967296;
    }
}
// --------------------------------------

// --- Constants ---
export const VOXEL_SIZE = 0.05; // Base voxel size
export const ENVIRONMENT_PIXEL_SCALE = 3; // Environment elements built from NxN base voxels
export const PLAYER_PIXEL_SCALE_XY = 3; // Player pixels are 3x3 base voxels in XY
export const PLAYER_PIXEL_SCALE_Z = 3;  // Player pixels are 3 base voxels deep
export const PLAYER_VOXEL_WIDTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
export const PLAYER_VOXEL_HEIGHT = VOXEL_SIZE * PLAYER_PIXEL_SCALE_XY;
export const PLAYER_VOXEL_DEPTH = VOXEL_SIZE * PLAYER_PIXEL_SCALE_Z;
export const SKELETON_VOXEL_SIZE = VOXEL_SIZE; // Skeleton uses standard size
export const HEAD_VOXEL_SCALE = 2.0; // Skeleton head sub-voxel scale
export const HEAD_VOXEL_SIZE = SKELETON_VOXEL_SIZE / HEAD_VOXEL_SCALE;
export const ARROW_VOXEL_SIZE = VOXEL_SIZE * 0.8; // Arrow voxel size
// --------------------------------------

// --- Materials ---

// Vine Materials
export const vineMaterialPrimary = new THREE.MeshLambertMaterial({ color: 0x3d5e3d }); // Darker Moss Green
export const vineMaterialSecondary = new THREE.MeshLambertMaterial({ color: 0x537a53 }); // Lighter Moss Green
export const darkVineMaterialPrimary = new THREE.MeshLambertMaterial({ color: 0x4a4a2a }); // Dark Brownish Green
export const darkVineMaterialSecondary = new THREE.MeshLambertMaterial({ color: 0x5c5c33 }); // Brownish Green

// Stonebrick Wall Materials
export const stonebrickMaterialPrimary = new THREE.MeshStandardMaterial({
    color: 0x555c66,
    roughness: 0.8,
    metalness: 0.1
}); // Muted Grey Stone (Edge/Noise)
export const stonebrickMaterialSecondary = new THREE.MeshStandardMaterial({
    color: 0x606873,
    roughness: 0.8,
    metalness: 0.1
}); // Slight Variation (Edge/Noise)
export const stonebrickCenterMaterial = new THREE.MeshStandardMaterial({
    color: 0x707880,
    roughness: 0.7,
    metalness: 0.15
}); // Cleaner Grey Center
export const stonebrickMortarMaterial = new THREE.MeshStandardMaterial({
    color: 0x555c66,
    roughness: 0.9,
    metalness: 0.05
}); // "Mortar" for gap filling

// Moss Materials
export const mossMaterialPrimary = new THREE.MeshLambertMaterial({ color: 0x4a5d23 }); // Dark Olive Green
export const mossMaterialSecondary = new THREE.MeshLambertMaterial({ color: 0x556B2F }); // Darker Olive Green

// Torch Materials
export const torchHandleMaterial = new THREE.MeshLambertMaterial({ color: 0x8B4513 }); // SaddleBrown
// Use Lambert for non-emissive parts if needed (removed redundant ones)
// Emissive Flame Materials
export const emissiveFlameMaterialPrimary = new THREE.MeshStandardMaterial({ color: 0xFFD700, emissive: 0xFFD700, emissiveIntensity: 1.5 });
export const emissiveFlameMaterialSecondary = new THREE.MeshStandardMaterial({ color: 0xFFA500, emissive: 0xFFA500, emissiveIntensity: 1.5 });
export const emissiveFlameMaterialTertiary = new THREE.MeshStandardMaterial({ color: 0xFF4500, emissive: 0xFF4500, emissiveIntensity: 1.5 });
export const emissiveFlameMaterials = [emissiveFlameMaterialPrimary, emissiveFlameMaterialSecondary, emissiveFlameMaterialTertiary];

// Floor Overlay Materials
export const floorTileMaterial1 = new THREE.MeshLambertMaterial({ color: 0x4a4a4a }); // Dark grey tile
export const floorTileMaterial2 = new THREE.MeshLambertMaterial({ color: 0x525252 }); // Slightly lighter grey tile
export const floorCrackedMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 }); // Cracked/darker variant

// Cave Floor Materials
export const caveFloorBaseMaterial = new THREE.MeshLambertMaterial({
    color: 0x5d4a3a,
    name: 'CaveFloorBaseMaterial'
}); // Muted brown base
export const caveFloorVariant1Material = new THREE.MeshLambertMaterial({
    color: 0x6a5546,
    name: 'CaveFloorVariant1Material'
}); // Slightly lighter/redder variant
export const caveFloorCrackMaterial = new THREE.MeshLambertMaterial({
    color: 0x4a3a2a,
    name: 'CaveFloorCrackMaterial'
}); // Darker crack/detail variant

// Floor Overlay Detail Materials
export const dustMaterial = new THREE.MeshLambertMaterial({
    color: 0x9c8b7a, // Light brownish-grey
    transparent: true,
    opacity: 0.6,
    name: 'CaveFloorDustMaterial'
});

// Player Model Materials
export const elementalCoreMaterial = new THREE.MeshStandardMaterial({
    color: 0xa0ffff, // Bright Cyan/White
    emissive: 0xa0ffff,
    emissiveIntensity: 1.8
});
export const elementalFlameLightMaterial = new THREE.MeshLambertMaterial({ color: 0x40a0ff }); // Light Blue
export const elementalFlameMidMaterial = new THREE.MeshLambertMaterial({ color: 0x2060d0 });   // Medium Blue
export const elementalFlameDarkMaterial = new THREE.MeshLambertMaterial({ color: 0x1a4a8a }); // Adjusted Dark Blue

// Skeleton Enemy Materials
export const boneColors = [0xF8E8C8, 0xE0C8A0, 0xC8A078, 0x987858];
export const eyeColors = [0xC86850, 0xA05040, 0x783830];
export const bowColors = [0xA0A0B8, 0x787898, 0x505078, 0x383850]; // Skeleton Bow Blue-Grey Palette
export const boneMaterials = boneColors.map(color => new THREE.MeshLambertMaterial({ color }));
export const eyeMaterial = new THREE.MeshLambertMaterial({ color: eyeColors[0] }); // Darkest Red
export const bowMaterials = bowColors.map(color => new THREE.MeshLambertMaterial({ color }));
export const skeletonBowMaterial = new THREE.MeshLambertMaterial({ color: 0x4F4F9F }); // Specific Bow Blue

// Arrow Projectile Materials
export const shaftMaterial = new THREE.MeshLambertMaterial({ color: 0x966F33 }); // Brownish shaft
export const headMaterial = new THREE.MeshLambertMaterial({ color: 0xAAAAAA }); // Grey head
export const fletchingMaterial = new THREE.MeshLambertMaterial({ color: 0xFFFFFF }); // White fletching

// NEW: Lighter Stone Material
export const lightStoneMaterial = new THREE.MeshStandardMaterial({
    color: 0xCCCCCC,
    roughness: 0.85,
    metalness: 0.05
}); // Very Light Grey Stone

// NEW: Slightly Darker Light Stone Material
export const midLightStoneMaterial = new THREE.MeshStandardMaterial({
    color: 0xBBBBBB,
    roughness: 0.85,
    metalness: 0.05
}); // Medium-Light Grey Stone

// NEW: Warmer Stone Materials for Vases
export const mainStoneMaterial = new THREE.MeshStandardMaterial({
    color: 0xC0C0B0, // Light Beige-Grey
    roughness: 0.9,
    metalness: 0.05
});
export const accentStoneMaterial = new THREE.MeshStandardMaterial({
    color: 0xA8A8A0, // Darker Beige-Grey
    roughness: 0.9,
    metalness: 0.05
});

// NEW: Dark Vase Base Material
export const darkVaseBaseMaterial = new THREE.MeshStandardMaterial({
    color: 0x403830, // Dark Brownish Grey
    roughness: 0.8,
    metalness: 0.1
});

// NEW: Gold Band Materials
export const goldBandMaterial = new THREE.MeshStandardMaterial({
    color: 0xD4AF37, // Metallic Gold
    roughness: 0.4,
    metalness: 0.6
});
export const darkGoldBandMaterial = new THREE.MeshStandardMaterial({
    color: 0xB8860B, // Darker Gold
    roughness: 0.5,
    metalness: 0.5
});

// NEW: Ritual Circle Materials
export const ritualCircleBaseMaterial = new THREE.MeshStandardMaterial({
    color: 0x208080, // Darker Teal/Cyan
    roughness: 0.7,
    metalness: 0.1
});
// Note: For the glow, we will reuse the hex color 'a0ffff' from elementalCoreMaterial

// NEW: Aether Torch Handle Material
export const aetherTorchHandleMaterial = new THREE.MeshStandardMaterial({
    color: 0xFFFFFF, // White
    roughness: 0.8,
    metalness: 0.1
});

// NEW: Vase Pattern Colors
export const vasePatternTerracotta = new THREE.MeshStandardMaterial({
    color: 0xB97A57, // Terracotta Red/Orange
    roughness: 0.8,
    metalness: 0.1
});
export const vasePatternGreen = new THREE.MeshStandardMaterial({
    color: 0x50A050, // Muted Green
    roughness: 0.8,
    metalness: 0.1
});
export const vasePatternCyan = new THREE.MeshStandardMaterial({
    color: 0x30A0A0, // Darker Cyan
    roughness: 0.7,
    metalness: 0.1
});
export const vasePatternBlack = new THREE.MeshStandardMaterial({
    color: 0x202020, // Dark Grey / Black
    roughness: 0.8,
    metalness: 0.1
});

// NEW: Brighter Stone Vase Materials
export const brightStoneBaseMaterial = new THREE.MeshStandardMaterial({
    color: 0x7D6A5A, // Brighter muted brown base
    roughness: 0.75,
    metalness: 0.05
});
export const brightStoneAccentMaterial = new THREE.MeshStandardMaterial({
    color: 0x8A7566, // Brighter lighter/redder variant
    roughness: 0.75,
    metalness: 0.05
});

// NEW: Stone Grain Materials for Vases
export const stoneGrain1 = new THREE.MeshStandardMaterial({ color: 0x958272, roughness: 0.75, metalness: 0.05 }); // Base (Slightly darker than superBright)
export const stoneGrain2 = new THREE.MeshStandardMaterial({ color: 0x9D8A7A, roughness: 0.7, metalness: 0.05 }); // = superBrightStoneBase
export const stoneGrain3 = new THREE.MeshStandardMaterial({ color: 0xA59080, roughness: 0.7, metalness: 0.05 }); // Intermediate
export const stoneGrain4 = new THREE.MeshStandardMaterial({ color: 0xAA9586, roughness: 0.7, metalness: 0.05 }); // = superBrightStoneAccent

// NEW: Catacomb Overlord Materials
export const darkArmorMaterial1 = new THREE.MeshStandardMaterial({ color: 0x222222, roughness: 0.8, metalness: 0.1 }); // Dark armor
export const darkArmorMaterial2 = new THREE.MeshStandardMaterial({ color: 0x333333, roughness: 0.8, metalness: 0.1 }); // Dark armor
export const darkArmorMaterial3 = new THREE.MeshStandardMaterial({ color: 0x444444, roughness: 0.8, metalness: 0.1 }); // Dark armor
export const goldAccentMaterial1 = new THREE.MeshStandardMaterial({ color: 0xAA8822, roughness: 0.4, metalness: 0.6 }); // Gold accent
export const goldAccentMaterial2 = new THREE.MeshStandardMaterial({ color: 0x997711, roughness: 0.4, metalness: 0.6 }); // Gold accent
export const redEyeMaterial = new THREE.MeshStandardMaterial({ color: 0xFF0000, emissive: 0xFF0000, emissiveIntensity: 1.5 }); // Glowing red eyes

// --------------------------------------

// --- Geometries (Create once, reuse where appropriate) ---
export const wallGeo = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);
export const vineGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.3, VOXEL_SIZE, VOXEL_SIZE * 0.3);
export const leafGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.3);
export const floorTileGeo = new THREE.BoxGeometry(
    VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE,
    VOXEL_SIZE * 1.0,
    VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE
);
export const torchHandleGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.8, VOXEL_SIZE * 0.3);
export const torchFlameParticleGeo = new THREE.BoxGeometry(VOXEL_SIZE * 0.5, VOXEL_SIZE * 0.5, VOXEL_SIZE * 0.5);
// --- End Shared Geometries ---

// --- Material Cache ---
export const materialCacheByHex = {};

function _cacheMaterials() {
    const allMaterials = [
        vineMaterialPrimary, vineMaterialSecondary, darkVineMaterialPrimary, darkVineMaterialSecondary,
        stonebrickMaterialPrimary, stonebrickMaterialSecondary, stonebrickCenterMaterial, stonebrickMortarMaterial,
        mossMaterialPrimary, mossMaterialSecondary,
        torchHandleMaterial,
        emissiveFlameMaterialPrimary, emissiveFlameMaterialSecondary, emissiveFlameMaterialTertiary,
        floorTileMaterial1, floorTileMaterial2, floorCrackedMaterial,
        caveFloorBaseMaterial, caveFloorVariant1Material, caveFloorCrackMaterial,
        dustMaterial,
        elementalCoreMaterial, elementalFlameLightMaterial, elementalFlameMidMaterial, elementalFlameDarkMaterial,
        ...boneMaterials, eyeMaterial, ...bowMaterials, skeletonBowMaterial,
        shaftMaterial, headMaterial, fletchingMaterial,
        lightStoneMaterial,
        midLightStoneMaterial,
        mainStoneMaterial,
        accentStoneMaterial,
        darkVaseBaseMaterial,
        goldBandMaterial,
        darkGoldBandMaterial,
        ritualCircleBaseMaterial,
        aetherTorchHandleMaterial,
        vasePatternTerracotta, vasePatternGreen, vasePatternCyan, vasePatternBlack,
        brightStoneBaseMaterial, brightStoneAccentMaterial,
        stoneGrain1, stoneGrain2, stoneGrain3, stoneGrain4,
        darkArmorMaterial1, darkArmorMaterial2, darkArmorMaterial3,
        goldAccentMaterial1, goldAccentMaterial2, redEyeMaterial
    ];
    allMaterials.forEach(mat => {
        if (mat && mat.color) { // Check if material and color exist
            // Ensure hex string is lowercase for consistent caching
            materialCacheByHex[mat.color.getHexString().toLowerCase()] = mat;
        }
    });
     console.log(`Material cache built. ${Object.keys(materialCacheByHex).length} materials cached.`);
}

// Call cache function immediately
_cacheMaterials();

export function _getMaterialByHex_Cached(hexString) {
    // Check if hexString is a string
    if (typeof hexString !== 'string') {
        console.warn(`Invalid hexString type: ${typeof hexString}. Converting to string.`);
        if (typeof hexString === 'number') {
            // Convert number to hex string
            hexString = hexString.toString(16).padStart(6, '0');
        } else {
            hexString = String(hexString);
        }
    }

    // Ensure lookup hex string is also lowercase
    const lowerHexString = hexString.toLowerCase();
    const material = materialCacheByHex[lowerHexString];
    if (!material) {
        console.warn(`Material not found in cache for hex: ${hexString} (lookup: ${lowerHexString})`);
        // Fallback: Create a new material and add it to the cache
        const newMaterial = new THREE.MeshStandardMaterial({
            color: parseInt(lowerHexString, 16),
            roughness: 0.8,
            metalness: 0.1
        });
        materialCacheByHex[lowerHexString] = newMaterial;
        console.log(`Created new material for hex: ${lowerHexString}`);
        return newMaterial;
    }
    return material;
}

/**
 * Cache for reusable geometries to improve performance
 */
const geometryCache = new Map();

/**
 * Gets or creates a cached geometry
 * @param {string} key The cache key
 * @param {Function} createFn Function to create the geometry if not cached
 * @returns {THREE.BufferGeometry} The cached or newly created geometry
 */
export function getOrCreateGeometry(key, createFn) {
    if (!geometryCache.has(key)) {
        geometryCache.set(key, createFn());
    }
    return geometryCache.get(key).clone(); // Always return a clone to avoid modifying the cached instance
}

/**
 * Clears the geometry cache
 */
export function clearGeometryCache() {
    geometryCache.forEach(geometry => {
        if (geometry.dispose) geometry.dispose();
    });
    geometryCache.clear();
}

// --- Instanced Voxel Creation ---
/**
 * Create an instanced mesh group from voxel data
 * @param {Array} voxelData - Array of {position, size, material} objects
 * @returns {THREE.Group} Group containing instanced meshes
 */
export function createInstancedVoxelGroup(voxelData) {
    const manager = new VoxelInstanceManager();
    
    voxelData.forEach(voxel => {
        manager.addVoxel(voxel.position, voxel.size, voxel.material);
    });
    
    return manager.build();
}

/**
 * Convert a group with many meshes to use instanced rendering
 * @param {THREE.Group} group - Group containing voxel meshes
 * @returns {THREE.Group} Optimized group with instanced meshes
 */
export function optimizeVoxelGroup(group) {
    const manager = new VoxelInstanceManager();
    const voxelData = [];
    
    // Extract voxel data from existing meshes
    group.traverse(child => {
        if (child.isMesh && child.geometry && child.geometry.type === 'BoxGeometry') {
            const worldPos = new THREE.Vector3();
            child.getWorldPosition(worldPos);
            
            const scale = child.scale.x; // Assuming uniform scale
            
            voxelData.push({
                position: worldPos,
                size: scale * VOXEL_SIZE,
                material: child.material
            });
        }
    });
    
    // Clear original group
    while (group.children.length > 0) {
        const child = group.children[0];
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
            if (Array.isArray(child.material)) {
                child.material.forEach(m => m.dispose());
            } else {
                child.material.dispose();
            }
        }
        group.remove(child);
    }
    
    // Add instanced meshes
    voxelData.forEach(voxel => {
        manager.addVoxel(voxel.position, voxel.size, voxel.material);
    });
    
    const instancedGroup = manager.build();
    group.add(instancedGroup);
    
    return group;
}

/**
 * Helper to collect voxels for instancing instead of creating meshes
 * @param {VoxelInstanceManager} manager - Instance manager to add voxels to
 * @param {THREE.Vector3} position - Position for the voxel
 * @param {number} size - Size of the voxel
 * @param {THREE.Material} material - Material to use
 */
export function addVoxelToInstance(manager, position, size, material) {
    manager.addVoxel(position, size, material);
}

// --- Export all prefab functions for easy access ---
export { createStoneVaseObject } from './stoneVaseObject.js';
export { createStonePillarObject } from './stonePillarObject.js';
export { createStoneRubbleObject } from './stoneRubbleObject.js';
export { createOminousAngelStatueObject } from './ominousAngelStatueObject.js';
export { createLargeAngelStatueObject } from './largeAngelStatueObject.js';
export { createAncientStonePillarObject } from './ancientStonePillarObject.js';
export { createTreasureChestObject } from './treasureChestObject.js';

// --------------------------------------