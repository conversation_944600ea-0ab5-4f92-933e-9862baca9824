import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create an obsidian chess table - the centerpiece of the Devil's Chess Room
 * A massive, imposing table with checkered pattern and hellish details
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Obsidian chess table object group
 */
export function createObsidianChessTableObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 6666);

    // Use large voxel size for imposing table
    const tableVoxelSize = VOXEL_SIZE * 4.0;
    const baseGeometry = getOrCreateGeometry('obsidian_table_voxel', () =>
        new THREE.BoxGeometry(tableVoxelSize, tableVoxelSize, tableVoxelSize)
    );

    // Obsidian chess table materials
    const obsidianMaterial = _getMaterialByHex_Cached('1A0A0A', {
        roughness: 0.1,
        metalness: 0.8,
        emissive: new THREE.Color(0x0A0505),
        emissiveIntensity: 0.1
    }); // Dark obsidian with slight glow

    const redVeinMaterial = _getMaterialByHex_Cached('8B0000', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x8B0000),
        emissiveIntensity: 0.3,
        roughness: 0.2,
        metalness: 0.6
    }); // Glowing red veins

    const chessBoardBlackMaterial = _getMaterialByHex_Cached('000000', {
        roughness: 0.1,
        metalness: 0.9
    }); // Pure black squares

    const chessBoardRedMaterial = _getMaterialByHex_Cached('8B0000', {
        roughness: 0.1,
        metalness: 0.7,
        emissive: new THREE.Color(0x450000),
        emissiveIntensity: 0.2
    }); // Red squares with glow

    // Table base/legs (large central pedestal)
    const basePositions = [
        // Central pedestal (3x3 base)
        { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 },
        { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },
        { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 },
        
        // Pedestal column (2x2)
        { x: -0.5, y: 1, z: -0.5 }, { x: 0.5, y: 1, z: -0.5 },
        { x: -0.5, y: 1, z: 0.5 },  { x: 0.5, y: 1, z: 0.5 },
        
        { x: -0.5, y: 2, z: -0.5 }, { x: 0.5, y: 2, z: -0.5 },
        { x: -0.5, y: 2, z: 0.5 },  { x: 0.5, y: 2, z: 0.5 },
        
        // Table support (expanding)
        { x: -1, y: 3, z: -1 }, { x: 0, y: 3, z: -1 }, { x: 1, y: 3, z: -1 },
        { x: -1, y: 3, z: 0 },  { x: 0, y: 3, z: 0 },  { x: 1, y: 3, z: 0 },
        { x: -1, y: 3, z: 1 },  { x: 0, y: 3, z: 1 },  { x: 1, y: 3, z: 1 }
    ];

    // Add table base
    basePositions.forEach(pos => {
        const voxel = new THREE.Mesh(baseGeometry.clone(), obsidianMaterial);
        voxel.position.set(
            pos.x * tableVoxelSize,
            pos.y * tableVoxelSize,
            pos.z * tableVoxelSize
        );
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    });

    // Chess board surface (8x8 checkered pattern)
    const boardY = 4; // Above the base
    for (let x = -4; x < 4; x++) {
        for (let z = -4; z < 4; z++) {
            // Checkered pattern: alternating red and black
            const isRedSquare = (x + z) % 2 === 0;
            const squareMaterial = isRedSquare ? chessBoardRedMaterial : chessBoardBlackMaterial;
            
            const boardVoxel = new THREE.Mesh(baseGeometry.clone(), squareMaterial);
            boardVoxel.position.set(
                x * tableVoxelSize,
                boardY * tableVoxelSize,
                z * tableVoxelSize
            );
            boardVoxel.castShadow = true;
            boardVoxel.receiveShadow = true;
            group.add(boardVoxel);
        }
    }

    // Add decorative red veins to base
    const veinPositions = [
        { x: 0, y: 1, z: -1 }, { x: 0, y: 1, z: 1 },
        { x: -1, y: 1, z: 0 }, { x: 1, y: 1, z: 0 },
        { x: 0, y: 2, z: 0 }, // Central vein
        { x: -2, y: 3, z: 0 }, { x: 2, y: 3, z: 0 },
        { x: 0, y: 3, z: -2 }, { x: 0, y: 3, z: 2 }
    ];

    veinPositions.forEach(pos => {
        const vein = new THREE.Mesh(baseGeometry.clone(), redVeinMaterial);
        vein.position.set(
            pos.x * tableVoxelSize,
            pos.y * tableVoxelSize,
            pos.z * tableVoxelSize
        );
        vein.scale.set(0.8, 0.8, 0.8); // Slightly smaller for detail
        vein.castShadow = false; // Glowing elements don't cast shadows
        vein.receiveShadow = true;
        group.add(vein);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'obsidian_chess_table',
        isInteractable: true,
        interactionType: 'chess_table',
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false, // This is a permanent fixture
        isInteriorObject: true,
        voxelScale: tableVoxelSize
    };

    group.name = 'obsidian_chess_table';
    console.log('[ObsidianChessTable] ✅ Created imposing chess table');
    return group;
}