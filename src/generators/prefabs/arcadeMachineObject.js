import * as THREE from 'three';
import { VOXEL_SIZE, materialCacheByHex } from './shared.js';

/**
 * Create an illuminated arcade machine in retro voxel style
 * Features glowing screen, controls, and cabinet with animated details
 */
export function createArcadeMachineObject() {
    const machine = new THREE.Group();
    machine.name = 'arcade_machine';
    
    // Ensure machine is positioned properly at ground level
    machine.position.y = 0;
    
    // === BASE CABINET ===
    const cabinetGroup = new THREE.Group();
    
    // Main cabinet body (larger base)
    const cabinetGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 8,  // Width 
        VOXEL_SIZE * 12, // Height (tall cabinet)
        VOXEL_SIZE * 6   // Depth
    );
    const cabinetMaterial = materialCacheByHex[0x2C1810] || new THREE.MeshLambertMaterial({ color: 0x2C1810 }); // Dark brown cabinet
    materialCacheByHex[0x2C1810] = cabinetMaterial;
    
    const cabinet = new THREE.Mesh(cabinetGeometry, cabinetMaterial);
    cabinet.position.set(0, VOXEL_SIZE * 6, 0); // Raise to sit on ground
    cabinetGroup.add(cabinet);
    
    // Cabinet corners (darker accent)
    const cornerMaterial = materialCacheByHex[0x1A0F08] || new THREE.MeshLambertMaterial({ color: 0x1A0F08 }); // Darker brown
    materialCacheByHex[0x1A0F08] = cornerMaterial;
    
    const cornerGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 0.8, VOXEL_SIZE * 12, VOXEL_SIZE * 0.8);
    
    // Four corner pillars
    const corners = [
        { x: -3.6, z: -2.6 }, { x: 3.6, z: -2.6 },
        { x: -3.6, z: 2.6 }, { x: 3.6, z: 2.6 }
    ];
    
    corners.forEach(pos => {
        const corner = new THREE.Mesh(cornerGeometry, cornerMaterial);
        corner.position.set(pos.x * VOXEL_SIZE, VOXEL_SIZE * 6, pos.z * VOXEL_SIZE);
        cabinetGroup.add(corner);
    });
    
    machine.add(cabinetGroup);
    
    // === GLOWING SCREEN ===
    const screenGroup = new THREE.Group();
    
    // Screen frame (metallic)
    const frameGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 6.5,
        VOXEL_SIZE * 5,
        VOXEL_SIZE * 0.5
    );
    const frameMaterial = materialCacheByHex[0x4A4A4A] || new THREE.MeshLambertMaterial({ color: 0x4A4A4A }); // Dark metal
    materialCacheByHex[0x4A4A4A] = frameMaterial;
    
    const screenFrame = new THREE.Mesh(frameGeometry, frameMaterial);
    screenFrame.position.set(0, VOXEL_SIZE * 9, VOXEL_SIZE * 3.2);
    screenGroup.add(screenFrame);
    
    // Glowing screen surface 
    const screenGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 5.5,
        VOXEL_SIZE * 4,
        VOXEL_SIZE * 0.2
    );
    const screenMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x00FFFF,  // Bright cyan glow
        emissive: 0x004444,  // Subtle cyan emissive
        emissiveIntensity: 0.3
    });
    
    const screen = new THREE.Mesh(screenGeometry, screenMaterial);
    screen.position.set(0, VOXEL_SIZE * 9, VOXEL_SIZE * 3.3);
    screen.userData.isGlowing = true;
    screen.userData.glowColor = 0x00FFFF;
    screenGroup.add(screen);
    
    // Screen glow light
    const screenLight = new THREE.PointLight(0x00FFFF, 2.0, 15, 1.8);
    screenLight.position.set(0, VOXEL_SIZE * 9, VOXEL_SIZE * 4);
    screenLight.userData.isScreenLight = true;
    screenGroup.add(screenLight);
    
    machine.add(screenGroup);
    
    // === CONTROL PANEL ===
    const controlsGroup = new THREE.Group();
    
    // Control panel base
    const panelGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 7,
        VOXEL_SIZE * 1.5,
        VOXEL_SIZE * 4
    );
    const panelMaterial = materialCacheByHex[0x3A3A3A] || new THREE.MeshLambertMaterial({ color: 0x3A3A3A }); // Medium gray
    materialCacheByHex[0x3A3A3A] = panelMaterial;
    
    const controlPanel = new THREE.Mesh(panelGeometry, panelMaterial);
    controlPanel.position.set(0, VOXEL_SIZE * 5.5, VOXEL_SIZE * 2.5);
    controlsGroup.add(controlPanel);
    
    // Control buttons (colorful)
    const buttonColors = [0xFF0000, 0x00FF00, 0x0000FF, 0xFFFF00, 0xFF00FF, 0x00FFFF];
    const buttonPositions = [
        { x: -2.5, z: 0.5 }, { x: -1, z: 0.5 }, { x: 0.5, z: 0.5 },
        { x: 2, z: 0.5 }, { x: -1.5, z: -0.5 }, { x: 1, z: -0.5 }
    ];
    
    buttonPositions.forEach((pos, i) => {
        const buttonGeometry = new THREE.CylinderGeometry(VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.4, 8);
        const buttonMaterial = new THREE.MeshLambertMaterial({ 
            color: buttonColors[i % buttonColors.length],
            emissive: buttonColors[i % buttonColors.length],
            emissiveIntensity: 0.2
        });
        
        const button = new THREE.Mesh(buttonGeometry, buttonMaterial);
        button.position.set(pos.x * VOXEL_SIZE, VOXEL_SIZE * 6.4, (VOXEL_SIZE * 2.5) + (pos.z * VOXEL_SIZE));
        button.userData.isButton = true;
        controlsGroup.add(button);
    });
    
    // Joystick
    const stickGeometry = new THREE.CylinderGeometry(VOXEL_SIZE * 0.2, VOXEL_SIZE * 0.3, VOXEL_SIZE * 1.5, 8);
    const stickMaterial = materialCacheByHex[0x000000] || new THREE.MeshLambertMaterial({ color: 0x000000 }); // Black
    materialCacheByHex[0x000000] = stickMaterial;
    
    const joystick = new THREE.Mesh(stickGeometry, stickMaterial);
    joystick.position.set(VOXEL_SIZE * 3, VOXEL_SIZE * 6.8, VOXEL_SIZE * 2.5);
    joystick.userData.isJoystick = true;
    controlsGroup.add(joystick);
    
    machine.add(controlsGroup);
    
    // === DECORATIVE ELEMENTS ===
    const decorGroup = new THREE.Group();
    
    // Side panels with vents
    const ventMaterial = materialCacheByHex[0x1A1A1A] || new THREE.MeshLambertMaterial({ color: 0x1A1A1A }); // Very dark gray
    materialCacheByHex[0x1A1A1A] = ventMaterial;
    
    // Left side vents
    for (let i = 0; i < 3; i++) {
        const ventGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.8, VOXEL_SIZE * 4);
        const vent = new THREE.Mesh(ventGeometry, ventMaterial);
        vent.position.set(-VOXEL_SIZE * 3.8, VOXEL_SIZE * (7 + i * 1.5), 0);
        decorGroup.add(vent);
    }
    
    // Right side vents
    for (let i = 0; i < 3; i++) {
        const ventGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.8, VOXEL_SIZE * 4);
        const vent = new THREE.Mesh(ventGeometry, ventMaterial);
        vent.position.set(VOXEL_SIZE * 3.8, VOXEL_SIZE * (7 + i * 1.5), 0);
        decorGroup.add(vent);
    }
    
    // Coin slot
    const coinSlotGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 1.5, VOXEL_SIZE * 0.3, VOXEL_SIZE * 0.2);
    const coinSlot = new THREE.Mesh(coinSlotGeometry, ventMaterial);
    coinSlot.position.set(VOXEL_SIZE * 2, VOXEL_SIZE * 4, VOXEL_SIZE * 3.1);
    decorGroup.add(coinSlot);
    
    // Power LED indicator
    const ledGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 0.2, 8, 6);
    const ledMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x00FF00,
        emissive: 0x004400,
        emissiveIntensity: 0.8
    });
    
    const powerLED = new THREE.Mesh(ledGeometry, ledMaterial);
    powerLED.position.set(-VOXEL_SIZE * 3, VOXEL_SIZE * 4, VOXEL_SIZE * 3.1);
    powerLED.userData.isPowerLED = true;
    decorGroup.add(powerLED);
    
    machine.add(decorGroup);
    
    // === INTERACTION PROPERTIES ===
    machine.userData = {
        objectId: 'arcade_machine',
        isInteractable: true,
        interactionType: 'arcade_machine',
        isDecorative: false,
        isArcadeMachine: true,
        hasGlowAnimation: true,
        interaction: {
            type: 'rhythm_arcade',
            id: 'arcade_machine'
        }
    };
    
    // Add animation data for screen flicker and button pulse
    machine.userData.animationData = {
        screenFlicker: { enabled: true, speed: 0.002, amplitude: 0.1 },
        buttonPulse: { enabled: true, speed: 0.003, amplitude: 0.3 },
        ledBlink: { enabled: true, speed: 0.001, amplitude: 0.5 }
    };
    
    return machine;
}