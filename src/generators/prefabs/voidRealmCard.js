import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Void Realm Card Prefab
 * Creates a safe dimension portal for strategic observation
 */

// Void realm specific colors
const VOID_COLORS = {
    VOID_PURPLE: 0x4B0082,       // Primary void purple
    DEEP_INDIGO: 0x301934,       // Deep dimensional indigo
    COSMIC_BLACK: 0x0C0C0C,      // Cosmic void black
    ASTRAL_BLUE: 0x1E1E3F,       // Astral dimension blue
    ETHER_WHITE: 0xE6E6FA,       // Ethereal white
    DIMENSION_SILVER: 0x708090,   // Dimensional silver
    PORTAL_CYAN: 0x00CED1,       // Portal energy cyan
    REALITY_GOLD: 0xFFD700       // Reality anchor gold
};

/**
 * Create a void realm card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The void realm card 3D model
 */
export function createVoidRealmCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'VoidRealmCard';

    // Materials
    const voidPurpleMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.VOID_PURPLE,
        emissive: VOID_COLORS.VOID_PURPLE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const deepIndigoMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.DEEP_INDIGO,
        emissive: VOID_COLORS.DEEP_INDIGO,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    const cosmicBlackMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.COSMIC_BLACK,
        emissive: VOID_COLORS.COSMIC_BLACK,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const astralBlueMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.ASTRAL_BLUE,
        emissive: VOID_COLORS.ASTRAL_BLUE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.6
    });

    const etherWhiteMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.ETHER_WHITE,
        emissive: VOID_COLORS.ETHER_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.5
    });

    const dimensionSilverMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.DIMENSION_SILVER,
        emissive: VOID_COLORS.DIMENSION_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const portalCyanMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.PORTAL_CYAN,
        emissive: VOID_COLORS.PORTAL_CYAN,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.6
    });

    const realityGoldMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.REALITY_GOLD,
        emissive: VOID_COLORS.REALITY_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create central void portal (dimensional gateway)
    const voidPortalVoxels = [
        // Portal ring (outer)
        { x: -3, y: 3, z: 0, material: voidPurpleMaterial },
        { x: -2, y: 4, z: 0, material: voidPurpleMaterial },
        { x: 0, y: 4, z: 0, material: voidPurpleMaterial },
        { x: 2, y: 4, z: 0, material: voidPurpleMaterial },
        { x: 3, y: 3, z: 0, material: voidPurpleMaterial },
        { x: 3, y: 1, z: 0, material: voidPurpleMaterial },
        { x: 3, y: -1, z: 0, material: voidPurpleMaterial },
        { x: 3, y: -3, z: 0, material: voidPurpleMaterial },
        { x: 2, y: -4, z: 0, material: voidPurpleMaterial },
        { x: 0, y: -4, z: 0, material: voidPurpleMaterial },
        { x: -2, y: -4, z: 0, material: voidPurpleMaterial },
        { x: -3, y: -3, z: 0, material: voidPurpleMaterial },
        { x: -3, y: -1, z: 0, material: voidPurpleMaterial },
        { x: -3, y: 1, z: 0, material: voidPurpleMaterial },
        
        // Portal ring (inner)
        { x: -2, y: 2, z: 0, material: deepIndigoMaterial },
        { x: -1, y: 3, z: 0, material: deepIndigoMaterial },
        { x: 1, y: 3, z: 0, material: deepIndigoMaterial },
        { x: 2, y: 2, z: 0, material: deepIndigoMaterial },
        { x: 2, y: 0, z: 0, material: deepIndigoMaterial },
        { x: 2, y: -2, z: 0, material: deepIndigoMaterial },
        { x: 1, y: -3, z: 0, material: deepIndigoMaterial },
        { x: -1, y: -3, z: 0, material: deepIndigoMaterial },
        { x: -2, y: -2, z: 0, material: deepIndigoMaterial },
        { x: -2, y: 0, z: 0, material: deepIndigoMaterial },
        
        // Portal core (void center)
        { x: 0, y: 0, z: 0, material: cosmicBlackMaterial },
        { x: -1, y: 1, z: 0, material: cosmicBlackMaterial },
        { x: 0, y: 1, z: 0, material: cosmicBlackMaterial },
        { x: 1, y: 1, z: 0, material: cosmicBlackMaterial },
        { x: -1, y: 0, z: 0, material: cosmicBlackMaterial },
        { x: 1, y: 0, z: 0, material: cosmicBlackMaterial },
        { x: -1, y: -1, z: 0, material: cosmicBlackMaterial },
        { x: 0, y: -1, z: 0, material: cosmicBlackMaterial },
        { x: 1, y: -1, z: 0, material: cosmicBlackMaterial }
    ];

    // Create dimensional energy streams
    const dimensionStreamVoxels = [
        // Energy flowing into portal
        { x: -5, y: 0, z: 0, material: portalCyanMaterial },
        { x: -4, y: 0, z: 0, material: astralBlueMaterial },
        { x: 5, y: 0, z: 0, material: portalCyanMaterial },
        { x: 4, y: 0, z: 0, material: astralBlueMaterial },
        { x: 0, y: 5, z: 0, material: portalCyanMaterial },
        { x: 0, y: 6, z: 0, material: astralBlueMaterial },
        { x: 0, y: -5, z: 0, material: portalCyanMaterial },
        { x: 0, y: -6, z: 0, material: astralBlueMaterial },
        
        // Diagonal energy streams
        { x: -4, y: 4, z: 0, material: etherWhiteMaterial },
        { x: -5, y: 5, z: 0, material: dimensionSilverMaterial },
        { x: 4, y: 4, z: 0, material: etherWhiteMaterial },
        { x: 5, y: 5, z: 0, material: dimensionSilverMaterial },
        { x: -4, y: -4, z: 0, material: etherWhiteMaterial },
        { x: -5, y: -5, z: 0, material: dimensionSilverMaterial },
        { x: 4, y: -4, z: 0, material: etherWhiteMaterial },
        { x: 5, y: -5, z: 0, material: dimensionSilverMaterial }
    ];

    // Create reality anchors (stability points)
    const realityAnchorVoxels = [
        // Corner stability anchors
        { x: -6, y: 6, z: 0, material: realityGoldMaterial },
        { x: 6, y: 6, z: 0, material: realityGoldMaterial },
        { x: -6, y: -6, z: 0, material: realityGoldMaterial },
        { x: 6, y: -6, z: 0, material: realityGoldMaterial },
        
        // Mid-point anchors
        { x: -6, y: 0, z: 0, material: realityGoldMaterial },
        { x: 6, y: 0, z: 0, material: realityGoldMaterial },
        { x: 0, y: 6, z: 0, material: realityGoldMaterial },
        { x: 0, y: -6, z: 0, material: realityGoldMaterial },
        
        // Supporting anchor points
        { x: -5, y: 3, z: 0, material: dimensionSilverMaterial },
        { x: -3, y: 5, z: 0, material: dimensionSilverMaterial },
        { x: 5, y: 3, z: 0, material: dimensionSilverMaterial },
        { x: 3, y: 5, z: 0, material: dimensionSilverMaterial },
        { x: -5, y: -3, z: 0, material: dimensionSilverMaterial },
        { x: -3, y: -5, z: 0, material: dimensionSilverMaterial },
        { x: 5, y: -3, z: 0, material: dimensionSilverMaterial },
        { x: 3, y: -5, z: 0, material: dimensionSilverMaterial }
    ];

    // Create void essence particles
    const voidEssenceVoxels = [
        // Floating void particles around portal
        { x: -7, y: 2, z: 0, material: voidPurpleMaterial },
        { x: -6, y: 4, z: 0, material: deepIndigoMaterial },
        { x: -4, y: 6, z: 0, material: astralBlueMaterial },
        { x: -2, y: 7, z: 0, material: etherWhiteMaterial },
        { x: 2, y: 7, z: 0, material: etherWhiteMaterial },
        { x: 4, y: 6, z: 0, material: astralBlueMaterial },
        { x: 6, y: 4, z: 0, material: deepIndigoMaterial },
        { x: 7, y: 2, z: 0, material: voidPurpleMaterial },
        
        { x: 7, y: -2, z: 0, material: voidPurpleMaterial },
        { x: 6, y: -4, z: 0, material: deepIndigoMaterial },
        { x: 4, y: -6, z: 0, material: astralBlueMaterial },
        { x: 2, y: -7, z: 0, material: etherWhiteMaterial },
        { x: -2, y: -7, z: 0, material: etherWhiteMaterial },
        { x: -4, y: -6, z: 0, material: astralBlueMaterial },
        { x: -6, y: -4, z: 0, material: deepIndigoMaterial },
        { x: -7, y: -2, z: 0, material: voidPurpleMaterial },
        
        // Additional essence particles
        { x: -8, y: 0, z: 0, material: cosmicBlackMaterial },
        { x: 8, y: 0, z: 0, material: cosmicBlackMaterial },
        { x: 0, y: 8, z: 0, material: cosmicBlackMaterial },
        { x: 0, y: -8, z: 0, material: cosmicBlackMaterial }
    ];

    // Create all voxels
    [...voidPortalVoxels, ...dimensionStreamVoxels, ...realityAnchorVoxels, ...voidEssenceVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22, // Compact spacing
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (voidPortalVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'portal';
        } else if (dimensionStreamVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'stream';
        } else if (realityAnchorVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'anchor';
        } else {
            mesh.userData.voxelType = 'essence';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        portalPulse: 0,
        dimensionFlow: 0,
        anchorStability: 0,
        essenceFloat: 0,
        voidOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update void realm card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateVoidRealmCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.portalPulse += deltaTime * 3.5; // Void portal pulsing
    cardGroup.userData.dimensionFlow += deltaTime * 5.0; // Energy flow speed
    cardGroup.userData.anchorStability += deltaTime * 2.0; // Stable anchoring
    cardGroup.userData.essenceFloat += deltaTime * 4.5; // Essence movement

    const time = cardGroup.userData.animationTime;
    const portalPulse = cardGroup.userData.portalPulse;
    const dimensionFlow = cardGroup.userData.dimensionFlow;
    const anchorStability = cardGroup.userData.anchorStability;
    const essenceFloat = cardGroup.userData.essenceFloat;
    const voidOffset = cardGroup.userData.voidOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'portal':
                    // Portal pulses with dimensional energy
                    const portalIntensity = 0.7 + Math.sin(portalPulse * 2.0 + voidOffset) * 0.8;
                    const portalDepth = Math.sin(portalPulse * 3.0) * 0.005;
                    
                    child.material.emissiveIntensity = baseEmissive * portalIntensity;
                    child.material.opacity = baseOpacity * (0.6 + portalIntensity * 0.4);
                    child.position.z += portalDepth; // Depth variation for portal effect
                    break;

                case 'stream':
                    // Energy streams flow toward portal
                    const streamIntensity = 0.6 + Math.sin(dimensionFlow * 3.0 + child.position.x * 0.5 + child.position.y * 0.3 + voidOffset) * 0.9;
                    const streamMotion = Math.cos(dimensionFlow * 4.0 + voidOffset) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * streamIntensity;
                    child.material.opacity = baseOpacity * streamIntensity;
                    // Move toward center
                    const centerDirection = new THREE.Vector2(-child.position.x, -child.position.y).normalize();
                    child.position.x += centerDirection.x * streamMotion * 0.5;
                    child.position.y += centerDirection.y * streamMotion * 0.5;
                    break;

                case 'anchor':
                    // Reality anchors provide stable grounding
                    const anchorIntensity = 0.8 + Math.sin(anchorStability * 1.5 + voidOffset) * 0.3;
                    const anchorStable = Math.sin(anchorStability * 2.5) * 0.002;
                    
                    child.material.emissiveIntensity = baseEmissive * anchorIntensity;
                    child.material.opacity = baseOpacity * (0.9 + anchorIntensity * 0.1);
                    child.position.x += anchorStable;
                    child.position.y += anchorStable * 0.5;
                    break;

                case 'essence':
                    // Void essence floats and swirls
                    const essenceIntensity = 0.5 + Math.sin(essenceFloat * 2.5 + child.position.x * 0.4 + voidOffset) * 0.8;
                    const essenceOrbit = essenceFloat * 1.5;
                    const orbitRadius = 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * essenceIntensity;
                    child.material.opacity = baseOpacity * essenceIntensity;
                    child.position.x += Math.cos(essenceOrbit + voidOffset) * orbitRadius;
                    child.position.y += Math.sin(essenceOrbit + voidOffset) * orbitRadius;
                    break;
            }
        }
    });

    // Overall void realm effect
    const voidPulse = Math.sin(time * 2.5 + voidOffset) * 0.006;
    cardGroup.position.x += voidPulse;
    cardGroup.position.y += voidPulse * 0.7;
    
    // Slow rotation to simulate dimensional shifting
    cardGroup.rotation.z += deltaTime * 0.2;
}

// Export the void realm card data for the loot system
export const VOID_REALM_CARD_DATA = {
    name: 'Void Realm',
    description: 'Banishes you to a safe void dimension outside reality. While in the void, time moves slowly and you can observe the battlefield from above. Return at will.',
    category: 'card',
    rarity: 'legendary',
    effect: 'void_realm',
    effectValue: 60,
    createFunction: createVoidRealmCard,
    updateFunction: updateVoidRealmCardAnimation,
    voxelModel: 'void_realm_card',
    glow: {
        color: 0x4B0082,
        intensity: 1.9
    }
};

export default createVoidRealmCard;