import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a confession stone - a dark altar where souls confess their sins
 * These stones pulse with dark energy and whisper ancient secrets
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Confession stone object group
 */
export function createConfessionStoneObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);

    // Use medium voxel size for prominent altar stones
    const stoneVoxelSize = VOXEL_SIZE * 3.0;
    const baseGeometry = getOrCreateGeometry('confession_stone_voxel', () =>
        new THREE.BoxGeometry(stoneVoxelSize, stoneVoxelSize, stoneVoxelSize)
    );

    // Dark confession stone materials
    const blackStoneMaterial = _getMaterialByHex_Cached('1A1A1A', {
        roughness: 0.9,
        metalness: 0.1,
        emissive: new THREE.Color(0x0A0A0A),
        emissiveIntensity: 0.05
    }); // Deep black stone

    const darkRedMaterial = _getMaterialByHex_Cached('4A0000', {
        roughness: 0.8,
        metalness: 0.2,
        emissive: new THREE.Color(0x2A0000),
        emissiveIntensity: 0.15
    }); // Dark red accents

    const soulCrystalMaterial = _getMaterialByHex_Cached('800080', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.3
    }); // Purple soul crystal

    const whisperMaterial = _getMaterialByHex_Cached('2F2F4F', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0x191970),
        emissiveIntensity: 0.3
    }); // Midnight blue whispers

    // Main altar stone structure
    const altarStructure = [
        // Base platform (5x3x2)
        { x: -2, y: 0, z: -1 }, { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 }, { x: 2, y: 0, z: -1 },
        { x: -2, y: 0, z: 0 },  { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },  { x: 2, y: 0, z: 0 },
        { x: -2, y: 0, z: 1 },  { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 },  { x: 2, y: 0, z: 1 },
        
        // Second layer (3x2x1)
        { x: -1, y: 1, z: -0.5 }, { x: 0, y: 1, z: -0.5 }, { x: 1, y: 1, z: -0.5 },
        { x: -1, y: 1, z: 0.5 },  { x: 0, y: 1, z: 0.5 },  { x: 1, y: 1, z: 0.5 },
        
        // Top platform (smaller confession area)
        { x: -0.5, y: 2, z: 0 }, { x: 0.5, y: 2, z: 0 }
    ];

    // Add main altar structure
    altarStructure.forEach(pos => {
        const stone = new THREE.Mesh(baseGeometry.clone(), blackStoneMaterial);
        stone.position.set(
            pos.x * stoneVoxelSize,
            pos.y * stoneVoxelSize,
            pos.z * stoneVoxelSize
        );
        stone.castShadow = true;
        stone.receiveShadow = true;
        group.add(stone);
    });

    // Add dark red blood stains and markings
    const bloodStains = [
        { x: 0, y: 2.1, z: 0 },   // Center stain on confession platform
        { x: -1, y: 1.1, z: 0 },  // Left stain
        { x: 1, y: 1.1, z: 0 },   // Right stain
        { x: 0, y: 0.1, z: -1 },  // Front base stain
        { x: 0, y: 0.1, z: 1 }    // Back base stain
    ];

    bloodStains.forEach(pos => {
        const stain = new THREE.Mesh(
            getOrCreateGeometry('blood_stain', () =>
                new THREE.BoxGeometry(stoneVoxelSize * 0.8, stoneVoxelSize * 0.1, stoneVoxelSize * 0.8)
            ),
            darkRedMaterial
        );
        stain.position.set(
            pos.x * stoneVoxelSize,
            pos.y * stoneVoxelSize,
            pos.z * stoneVoxelSize
        );
        stain.castShadow = false;
        stain.receiveShadow = true;
        group.add(stain);
    });

    // Add soul crystals around the altar
    const soulCrystalPositions = [
        { x: -2, y: 1, z: -1 }, { x: 2, y: 1, z: -1 },
        { x: -2, y: 1, z: 1 },  { x: 2, y: 1, z: 1 },
        { x: 0, y: 3, z: 0 }    // Floating crystal above
    ];

    soulCrystalPositions.forEach(pos => {
        const crystal = new THREE.Mesh(
            getOrCreateGeometry('soul_crystal', () => {
                // Create octahedral crystal shape
                const geo = new THREE.OctahedronGeometry(stoneVoxelSize * 0.4, 0);
                return geo;
            }),
            soulCrystalMaterial
        );
        crystal.position.set(
            pos.x * stoneVoxelSize,
            pos.y * stoneVoxelSize,
            pos.z * stoneVoxelSize
        );
        crystal.userData.isSoulCrystal = true;
        crystal.userData.baseIntensity = 0.4;
        crystal.userData.isFloating = (pos.y > 2);
        crystal.castShadow = false;
        crystal.receiveShadow = false;
        group.add(crystal);
    });

    // Add whisper particles (ethereal effects)
    const whisperPositions = [
        { x: -1.5, y: 2.5, z: 0 }, { x: 1.5, y: 2.5, z: 0 },
        { x: 0, y: 2.8, z: -0.8 }, { x: 0, y: 2.8, z: 0.8 },
        { x: -0.5, y: 3.2, z: 0.5 }, { x: 0.5, y: 3.2, z: -0.5 }
    ];

    whisperPositions.forEach(pos => {
        if (rng() > 0.3) { // 70% chance for each whisper particle
            const whisper = new THREE.Mesh(
                getOrCreateGeometry('whisper_particle', () =>
                    new THREE.BoxGeometry(stoneVoxelSize * 0.3, stoneVoxelSize * 0.3, stoneVoxelSize * 0.3)
                ),
                whisperMaterial
            );
            whisper.position.set(
                pos.x * stoneVoxelSize,
                pos.y * stoneVoxelSize,
                pos.z * stoneVoxelSize
            );
            whisper.userData.isWhisper = true;
            whisper.userData.baseY = pos.y * stoneVoxelSize;
            whisper.userData.floatSpeed = 0.5 + rng() * 0.5;
            whisper.scale.set(0.6, 0.6, 0.6);
            whisper.castShadow = false;
            whisper.receiveShadow = false;
            group.add(whisper);
        }
    });

    // Add corner skull decorations
    const skullPositions = [
        { x: -2.5, y: 0.5, z: -1.5 }, { x: 2.5, y: 0.5, z: -1.5 },
        { x: -2.5, y: 0.5, z: 1.5 },  { x: 2.5, y: 0.5, z: 1.5 }
    ];

    const skullMaterial = _getMaterialByHex_Cached('F5F5DC', {
        roughness: 0.8,
        metalness: 0.1,
        emissive: new THREE.Color(0x1A1A0A),
        emissiveIntensity: 0.05
    }); // Aged bone

    skullPositions.forEach(pos => {
        const skull = new THREE.Mesh(baseGeometry.clone(), skullMaterial);
        skull.position.set(
            pos.x * stoneVoxelSize,
            pos.y * stoneVoxelSize,
            pos.z * stoneVoxelSize
        );
        skull.scale.set(0.7, 0.7, 0.7);
        skull.castShadow = true;
        skull.receiveShadow = true;
        group.add(skull);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'confession_stone',
        isInteractable: true,
        interactionType: 'confession_altar',
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: stoneVoxelSize,
        hasSoulAnimation: true,
        hasWhisperAnimation: true
    };

    group.name = 'confession_stone';
    console.log('[ConfessionStone] ✅ Created dark confession altar with soul crystals');
    return group;
}