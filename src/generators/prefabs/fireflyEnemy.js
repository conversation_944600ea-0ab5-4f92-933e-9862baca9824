import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Firefly colors
const FIREFLY_COLORS = {
    BODY_GLOW: '0x4080ff', // Bright blue glowing body
    BODY_CORE: '0x2060d0', // Medium blue core
    BODY_DARK: '0x1a4a8a', // Dark blue head/segments
    WING: '0xffffff',      // White translucent wings
    ACCENT: '0x00ffff'     // Cyan accent glow
};

/**
 * Creates a voxel-based firefly enemy model with animation-ready parts.
 * Small, blue glowing creature that flees from the player.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The firefly enemy model group.
 */
export function createFireflyEnemyModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "FireflyEnemy";

    // Use a very small voxel size for the firefly (smaller than bat)
    const fireflyVoxelSize = VOXEL_SIZE * 0.4;

    // Create animation-ready groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const leftWingGroup = new THREE.Group();
    leftWingGroup.name = "leftWing";

    const rightWingGroup = new THREE.Group();
    rightWingGroup.name = "rightWing";

    // Add groups to final group with proper hierarchy
    finalGroup.add(bodyGroup);
    bodyGroup.add(leftWingGroup);
    bodyGroup.add(rightWingGroup);

    // Position wings relative to body
    leftWingGroup.position.set(-0.3 * fireflyVoxelSize, 0, 0);
    rightWingGroup.position.set(0.3 * fireflyVoxelSize, 0, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        leftWing: {},
        rightWing: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('firefly_voxel', () => new THREE.BoxGeometry(fireflyVoxelSize, fireflyVoxelSize, fireflyVoxelSize));
    const tempMatrix = new THREE.Matrix4();

    // Helper to add voxels to specific groups
    const addVoxel = (groupName, x, y, z, colorHex) => {
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        tempMatrix.makeTranslation(
            x * fireflyVoxelSize,
            y * fireflyVoxelSize,
            z * fireflyVoxelSize
        );

        const clonedGeo = voxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[groupName][colorHex].push(clonedGeo);

        // Store original voxel data for destruction effects
        originalVoxels.push({
            x: x,
            y: y,
            z: z,
            c: colorHex // Store color hex
        });
    };

    // Helper to merge geometries for a group
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    geometriesByMaterial[groupName][colorHex],
                    false
                );

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(colorHex);
                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    targetGroup.add(mesh);
                }
            }
        }
    };

    // --- FIREFLY BODY (Small, glowing blue) ---
    // Main body - small and compact
    addVoxel('body', 0, 0, 0, FIREFLY_COLORS.BODY_GLOW); // Center core
    addVoxel('body', 0, 0, 1, FIREFLY_COLORS.BODY_CORE); // Back segment
    addVoxel('body', 0, 0, -1, FIREFLY_COLORS.BODY_CORE); // Front segment

    // Head
    addVoxel('body', 0, 0, -2, FIREFLY_COLORS.BODY_DARK); // Head

    // Abdomen segments
    addVoxel('body', 0, 0, 2, FIREFLY_COLORS.BODY_CORE); // Rear segment

    // Add small accent glow spots
    addVoxel('body', 0, 0.5, 0, FIREFLY_COLORS.ACCENT); // Top glow

    // --- FIREFLY WINGS (Translucent, small) ---
    // Left wing
    addVoxel('leftWing', 0, 0, 0, FIREFLY_COLORS.WING); // Wing base
    addVoxel('leftWing', 0, 1, 0, FIREFLY_COLORS.WING); // Wing tip
    addVoxel('leftWing', 0, 0, -1, FIREFLY_COLORS.WING); // Wing front

    // Right wing (mirror of left)
    addVoxel('rightWing', 0, 0, 0, FIREFLY_COLORS.WING); // Wing base
    addVoxel('rightWing', 0, 1, 0, FIREFLY_COLORS.WING); // Wing tip
    addVoxel('rightWing', 0, 0, -1, FIREFLY_COLORS.WING); // Wing front

    // Merge geometries for each group
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('leftWing', leftWingGroup);
    mergeGroupGeometries('rightWing', rightWingGroup);

    // Dispose the template geometry
    voxelGeo.dispose();

    // Apply overall scale
    finalGroup.scale.set(scale, scale, scale);

    // Add animation data to userData
    finalGroup.userData.animationData = {
        wingFlapSpeed: 8.0,         // Very fast wing flapping for firefly
        wingFlapAmplitude: Math.PI / 4, // Moderate wing flap amplitude
        bodyBobAmplitude: 0.08,     // Small body bobbing
        bodyBobSpeed: 3.0,          // Fast bobbing for nervous movement
        attackAnimationDuration: 0.3 // Quick attack animation
    };

    // Add type information to ensure proper animation handling
    finalGroup.userData.type = 'firefly';

    // Add attack hitbox data (very small, minimal damage)
    finalGroup.userData.attackHitbox = {
        radius: 1.0 * scale, // Very small attack radius
        damage: 1, // Minimal damage
        knockback: 1.0 // Light knockback
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'firefly';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = fireflyVoxelSize;

    // Add cyan light source with 3x normal radius
    const fireflyLight = new THREE.PointLight(0x00ffff, 0.8, 15 * scale, 2); // Cyan color, 3x normal radius (5 -> 15)
    fireflyLight.name = 'fireflyLight';
    fireflyLight.position.set(0, 0, 0); // Centered on the firefly
    fireflyLight.castShadow = false; // Fireflies don't cast shadows for performance

    // Add the light to the body group so it moves with the firefly
    bodyGroup.add(fireflyLight);

    // Store light reference for animation
    finalGroup.userData.light = fireflyLight;

    console.log("Created Firefly Enemy Model with cyan light");
    return finalGroup;
}
