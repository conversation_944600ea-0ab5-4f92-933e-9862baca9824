// Wall prefabs
export { createStonebrickWallSegment } from './stonebrickWall.js';
export { createSandstoneBrickWallSegment } from './sandstoneBrickWall.js';
export { createMysticalStoneBrickWallSegment } from './mysticalStoneBrickWall.js';
export { createAncientStoneBrickWallWithVines } from './ancientStoneBrickWallWithVines.js';
export { createCircularChamberWallSegment } from './circularChamberWall.js';

// Floor prefabs
export { createFloorOverlay } from './floorOverlay.js';
export { createCaveFloor } from './caveFloor.js';
export { createStoneFloor } from './stoneFloor.js';
export { createSandstoneFloor } from './sandstoneFloor.js';
export { createMysticalStoneFloor } from './mysticalStoneFloor.js';
export { createAncientStoneFloorWithVines } from './ancientStoneFloorWithVines.js';

// Door prefabs
export { createStoneArchwayDoor } from './stoneArchwayDoor.js';
export { createClosedDungeonDoor, createWoodenDungeonDoor, createReinforcedDungeonDoor, createIronDungeonDoor } from './closedDungeonDoor.js';

// Character prefabs
export { createElementalPlayerModel } from './elementalPlayer.js';
export { createSkeletonEnemyModel } from './skeletonEnemy.js';
export { createBatEnemyModel } from './batEnemy.js';
export { createZombieEnemyModel } from './zombieEnemy.js';
export { createMagmaGolemEnemyModel } from './magmaGolemEnemy.js';
export { createCatacombOverlordModel } from './catacombOverlordBoss.js';
export { createFishEnemyModel } from './fishEnemy.js';
export { createNairabosEnemyModel } from './nairabosEnemy.js';
export { createFireflyEnemyModel } from './fireflyEnemy.js';
export { shadowMinionObject } from './shadowMinionObject.js';
export { createTombGuardianEnemyModel } from './tombGuardianEnemy.js';
export { createVoidLordEnemyModel, createVoidLordEnergyStrain, createVoidLordEnergyOrb } from './voidLordEnemy.js';

// Projectile prefabs
export { createArrowProjectileModel } from './arrowProjectile.js';

// Interior object prefabs
export { createVineObject } from './vineObject.js';
export { createTorchObject } from './torchObject.js';
export { createStoneVaseObject } from './stoneVaseObject.js';
export { createStonePillarObject } from './stonePillarObject.js';
export { createStoneRubbleObject } from './stoneRubbleObject.js';
export { createRitualCircleObject } from './ritualCircleObject.js';
export { createAetherTorchObject } from './aetherTorchObject.js';
export { createSoulOrbObject } from './soulOrbObject.js';
export { createRuneMonolith } from './runeMonolith.js';

// Event room specific prefabs
export { createCrystalCaveObject } from './crystalCaveObject.js';
export { createImprovedFishingRodObject, animateFishingRodReelIn } from './improvedFishingRodObject.js';
export { createArcadeMachineObject } from './arcadeMachineObject.js';

// Guardians of Lies room prefabs
export { createMedievalGateObject, createTreasureGateObject, createDeathGateObject } from './medievalGateObject.js';
export { createArmoredKnightObject, animateKnightSpeaking, createTruthKnightObject, createLieKnightObject } from './armoredKnightObject.js';

// Eye of Judgment room prefabs
export { createEyeOfJudgmentObject, animateEyeOfJudgment } from './eyeOfJudgmentObject.js';
export { createRitualCircleFloorObject, animateRitualCircle } from './ritualCircleFloorObject.js';
export { createJudgmentPillarObject, animateJudgmentPillar } from './judgmentPillarObject.js';
export { createSoulLanternObject } from './soulLanternObject.js';
export { createConfessionStoneObject } from './confessionStoneObject.js';
export { createMoralCrystalObject, animateMoralCrystal } from './moralCrystalObject.js';

// Devil's Chess Room prefabs
export { createObsidianChessTableObject } from './obsidianChessTableObject.js';
export { createDevilThroneObject } from './devilThroneObject.js';

// Chess piece prefabs
export { createChessPawnObject, createWhiteChessPawn, createBlackChessPawn } from './chessPawnObject.js';
export { createChessRookObject, createWhiteChessRook, createBlackChessRook } from './chessRookObject.js';
export { createChessKnightObject, createWhiteChessKnight, createBlackChessKnight } from './chessKnightObject.js';
export { createChessBishopObject, createWhiteChessBishop, createBlackChessBishop } from './chessBishopObject.js';
export { createChessQueenObject, createWhiteChessQueen, createBlackChessQueen } from './chessQueenObject.js';
export { createChessKingObject, createWhiteChessKing, createBlackChessKing } from './chessKingObject.js';
export { createHellishTorchObject } from './hellishTorchObject.js';
export { createObsidianPillarObject } from './obsidianPillarObject.js';

// Chronal Anomaly Room prefabs
export { createTemporalRiftObject } from './temporalRiftObject.js';
export { createTemporalCrystalObject } from './temporalCrystalObject.js';

// Cave atmosphere prefabs
export { createStalactiteObject } from './stalactiteObject.js';
export { createStalagmiteObject } from './stalagmiteObject.js';
export { createMossPatchObject } from './mossPatchObject.js';
export { createCaveDebrisObject } from './caveDebrisObject.js';
export { createRockWallFormationObject } from './rockWallFormationObject.js';

export { createMysticalStalagmiteObject } from './mysticalStalagmiteObject.js';

// Legacy event room prefabs (for backward compatibility)
export { createGlowingPondObject } from './glowingPondObject.js';
export { createFishingRodObject } from './fishingRodObject.js';
export { createMysteriousCrystalObject } from './mysteriousCrystalObject.js';
export { createEgyptianVaseObject } from './egyptianVaseObject.js';
export { createGoldenPillarObject } from './goldenPillarObject.js';
export { createTreasureChestObject } from './treasureChestObject.js';
export { createAncientStonePillarObject } from './ancientStonePillarObject.js';
export { createAncientSandstoneTemplePillar } from './ancientSandstoneTemplePillar.js';
export { createAncientStonePath } from './ancientStonePath.js';
export { createStonePathObject, createShortStonePath, createLongStonePath, createWideStonePath } from './stonePathObject.js';
export { createLargeAngelStatueObject } from './largeAngelStatueObject.js';
export { createOminousAngelStatueObject } from './ominousAngelStatueObject.js';