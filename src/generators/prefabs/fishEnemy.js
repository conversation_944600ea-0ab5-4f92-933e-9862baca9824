import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Fish colors
const FISH_COLORS = {
    BODY: '4169E1', // Royal blue
    BELLY: '87CEEB', // Sky blue (lighter)
    FIN: '1E90FF', // Dodger blue
    EYE: 'FFFFFF', // White
    PUPIL: '000000', // Black
    SCALE: '6495ED' // Cornflower blue (for scale details)
};

/**
 * Creates a voxel-based fish enemy model with animation-ready parts.
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The fish enemy model group.
 */
export function createFishEnemyModel(scale = 1.0) {
    const finalGroup = new THREE.Group();
    finalGroup.name = "FishEnemy";

    // Use standard voxel size for the fish
    const fishVoxelSize = VOXEL_SIZE * 0.8;

    // Create animation-ready groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const tailGroup = new THREE.Group();
    tailGroup.name = "tail";

    const leftFinGroup = new THREE.Group();
    leftFinGroup.name = "leftFin";

    const rightFinGroup = new THREE.Group();
    rightFinGroup.name = "rightFin";

    const topFinGroup = new THREE.Group();
    topFinGroup.name = "topFin";

    // Add groups to final group with proper hierarchy
    finalGroup.add(bodyGroup);
    bodyGroup.add(tailGroup);
    bodyGroup.add(leftFinGroup);
    bodyGroup.add(rightFinGroup);
    bodyGroup.add(topFinGroup);

    // Position fins and tail relative to body
    tailGroup.position.set(-2.5 * fishVoxelSize, 0, 0);
    leftFinGroup.position.set(0.5 * fishVoxelSize, 0, -1.2 * fishVoxelSize);
    rightFinGroup.position.set(0.5 * fishVoxelSize, 0, 1.2 * fishVoxelSize);
    topFinGroup.position.set(0, 1.5 * fishVoxelSize, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        tail: {},
        leftFin: {},
        rightFin: {},
        topFin: {}
    };

    // Store original voxel data for destruction effects
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('fish_voxel', () => new THREE.BoxGeometry(fishVoxelSize, fishVoxelSize, fishVoxelSize));
    const tempMatrix = new THREE.Matrix4();

    // Helper to add voxels to specific groups
    const addVoxel = (groupName, x, y, z, colorHex) => {
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        tempMatrix.makeTranslation(
            x * fishVoxelSize,
            y * fishVoxelSize,
            z * fishVoxelSize
        );

        const clonedGeo = voxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[groupName][colorHex].push(clonedGeo);

        // Store original voxel data for destruction effects
        originalVoxels.push({
            x: x,
            y: y,
            z: z,
            c: colorHex // Store color hex
        });
    };

    // Helper to merge geometries for a group
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    geometriesByMaterial[groupName][colorHex],
                    false
                );

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(colorHex);
                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    targetGroup.add(mesh);
                }
            }
        }
    };

    // Create fish body (elongated oval shape)
    for (let x = -2; x <= 2; x++) {
        for (let y = -1; y <= 1; y++) {
            for (let z = -1; z <= 1; z++) {
                // Create fish-like body shape
                const distance = Math.sqrt((x/2.5)**2 + y**2 + z**2);
                if (distance <= 1.0) {
                    // Use belly color for bottom, body color for top and sides
                    const color = (y <= 0) ? FISH_COLORS.BELLY : FISH_COLORS.BODY;
                    addVoxel('body', x, y, z, color);
                }
            }
        }
    }

    // Add scale details
    for (let x = -1; x <= 1; x++) {
        for (let z = -1; z <= 1; z += 2) {
            addVoxel('body', x, 0, z, FISH_COLORS.SCALE);
        }
    }

    // Add eyes
    addVoxel('body', 1.5, 0.5, -0.8, FISH_COLORS.EYE);
    addVoxel('body', 1.5, 0.5, 0.8, FISH_COLORS.EYE);
    addVoxel('body', 1.7, 0.5, -0.8, FISH_COLORS.PUPIL);
    addVoxel('body', 1.7, 0.5, 0.8, FISH_COLORS.PUPIL);

    // Create tail fin
    for (let x = -2; x <= 0; x++) {
        for (let y = -2; y <= 2; y++) {
            // Create forked tail shape
            if (Math.abs(y) >= 1 || x >= -1) {
                addVoxel('tail', x, y, 0, FISH_COLORS.FIN);
            }
        }
    }

    // Create side fins
    for (let x = -1; x <= 1; x++) {
        for (let y = -1; y <= 0; y++) {
            addVoxel('leftFin', x, y, 0, FISH_COLORS.FIN);
            addVoxel('rightFin', x, y, 0, FISH_COLORS.FIN);
        }
    }

    // Create top fin
    for (let x = -1; x <= 1; x++) {
        for (let y = 0; y <= 1; y++) {
            addVoxel('topFin', x, y, 0, FISH_COLORS.FIN);
        }
    }

    // Merge geometries for each group
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('tail', tailGroup);
    mergeGroupGeometries('leftFin', leftFinGroup);
    mergeGroupGeometries('rightFin', rightFinGroup);
    mergeGroupGeometries('topFin', topFinGroup);

    // Dispose the template geometry
    voxelGeo.dispose();

    // Apply overall scale
    finalGroup.scale.set(scale, scale, scale);

    // Add animation data to userData
    finalGroup.userData.animationData = {
        tailSwimSpeed: 4.0,
        tailSwimAmplitude: Math.PI / 6,
        finFlapSpeed: 3.0,
        finFlapAmplitude: Math.PI / 8,
        bodyBobAmplitude: 0.1,
        bodyBobSpeed: 2.0,
        jumpHeight: 4.0,
        jumpDuration: 1.0,
        attackAnimationDuration: 0.5
    };

    // Add type information
    finalGroup.userData.type = 'fish';

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 2.0 * scale,
        damage: 1,
        knockback: 3.0
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'fish';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = fishVoxelSize;

    console.log("Created Fish Enemy Model");
    return finalGroup;
}
