import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    stonebrickMaterialPrimary, stonebrickMaterialSecondary, stonebrickCenterMaterial, stonebrickMortarMaterial,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Stone Brick Wall with Water Stains
 *
 * Creates stone brick walls with water-stained appearance.
 * Uses the same extrusion system as normal stone walls but with water-themed materials.
 */

// Water-stained stone materials (fewer blue voxels, more stone-like)
const WATER_STONE_MATERIALS = [
    _getMaterialByHex_Cached('8b7355'), // Brown stone (primary)
    _getMaterialByHex_Cached('696969'), // Dim gray (secondary)
    _getMaterialByHex_Cached('708090'), // Slate gray (accent)
    _getMaterialByHex_Cached('2f4f4f'), // Dark slate gray (mortar)
    _getMaterialByHex_Cached('556b2f'), // Dark olive green (moss)
    _getMaterialByHex_Cached('4682b4'), // Steel blue (water stains - rare)
    _getMaterialByHex_Cached('5f9ea0')  // Cadet blue (water stains - rare)
];

/**
 * Create water-stained stone brick wall using the same extrusion system as normal stone walls
 * FIXED: Copy the exact extrusion logic from stonebrickWall.js for consistent thickness and appearance
 */
export function createStonebrickWaterWallSegment(width, height, depth, roomData, isDarkRoom = false, position, rotation) {
    // --- INPUT VALIDATION ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createStonebrickWaterWallSegment received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() };
    }

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    // Use the same scaling system as normal stone walls
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;

    // --- Calculate Dimensions (same as stone wall) ---
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);

    // --- Safeguard Dimensions (same as stone wall) ---
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);
    const safeNumZ = Math.max(1, numZ);

    // Calculate offsets for centering (same as stone wall)
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;
    const offsetX = (safeNumXEnv - 1) * wallVoxelSize / 2;
    const offsetY = (safeNumYEnv - 1) * wallVoxelSize / 2;
    const offsetZ = (safeNumZ - 1) * VOXEL_SIZE / 2;

    // Get cached wall voxel geometry (same as stone wall)
    const largeWallGeo = getOrCreateGeometry(
        `wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };

    // Seeded random for consistent patterns
    const seed = (roomData?.id || 0) * 1000 + (position?.x || 0) * 100 + (position?.z || 0) * 10;
    const random = mulberry32(seed);

    let voxelsAdded = 0;

    // Function to get random water-stained stone material
    const getRandomWaterStoneMat = () => {
        const rand = random();
        // Material distribution: 40% brown stone, 30% gray, 15% slate, 10% dark slate, 3% moss, 1% blue stains, 1% cadet blue
        if (rand < 0.40) return WATER_STONE_MATERIALS[0]; // Brown stone
        if (rand < 0.70) return WATER_STONE_MATERIALS[1]; // Dim gray
        if (rand < 0.85) return WATER_STONE_MATERIALS[2]; // Slate gray
        if (rand < 0.95) return WATER_STONE_MATERIALS[3]; // Dark slate gray
        if (rand < 0.98) return WATER_STONE_MATERIALS[4]; // Dark olive green (moss)
        if (rand < 0.99) return WATER_STONE_MATERIALS[5]; // Steel blue (rare water stains)
        return WATER_STONE_MATERIALS[6]; // Cadet blue (rare water stains)
    };

    // --- Generate Wall Voxels with Extrusion (copied from stonebrickWall.js) ---
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            // 95% chance for stone block (same as normal walls)
            if (random() > 0.05) {
                const blockMaterial = getRandomWaterStoneMat();

                // CRITICAL FIX: Keep extruded look but reduce gaps between voxels (same as stone wall)
                const baseX = ex * wallVoxelSize - offsetX;
                const baseY = ey * wallVoxelSize - offsetY;

                // Reduce variations to minimize gaps while keeping extruded appearance
                const depthVariation = (random() - 0.5) * 0.03; // Reduced depth variation
                const heightVariation = (random() - 0.5) * 0.02; // Reduced height variation

                // Keep subtle rotation for extruded look
                const rotationY = (random() - 0.5) * 0.02; // Reduced Y rotation
                const rotationZ = (random() - 0.5) * 0.015; // Reduced Z rotation

                const finalX = baseX + depthVariation;
                const finalY = baseY + heightVariation;

                // For depth, use a single layer with slight variation for extruded look (same as stone wall)
                for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                    const posZ = z * VOXEL_SIZE - offsetZ + depthVariation * 0.3;

                    // Create transformation matrix with rotation for extruded appearance
                    tempMatrix.makeRotationFromEuler(new THREE.Euler(0, rotationY, rotationZ));
                    tempMatrix.setPosition(finalX, finalY, posZ);

                    addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // Add side caps for east and west faces of wall voxels (same as stone wall)
                // For east face (right side of wall)
                if (ex === safeNumXEnv - 1) {
                    const sideFaceGeo = getOrCreateGeometry(
                        `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                        () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                    );
                    const rightPosX = finalX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                    tempMatrix.makeTranslation(rightPosX, finalY, 0);
                    addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // For west face (left side of wall)
                if (ex === 0) {
                    const sideFaceGeo = getOrCreateGeometry(
                        `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                        () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                    );
                    const leftPosX = finalX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                    tempMatrix.makeTranslation(leftPosX, finalY, 0);
                    addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // FIXED: Add north/south face caps with proper thickness for side visibility
                // For north face (front side of wall)
                if (true) { // Always add north and south caps since they're always visible from top-down view
                    const northFaceGeo = getOrCreateGeometry(
                        `water_wall_ns_cap_thick_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, depth * 0.4)
                    );
                    // FIXED: Use proper Z positioning with thick caps
                    const frontPosZ = -depth/2 - depth * 0.2;
                    tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                    addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;

                    // For south face (back side of wall) - FIXED: Use proper wall depth for thickness
                    const backPosZ = depth/2 + depth * 0.2;
                    tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                    addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // Add top face for the top row of voxels with extended coverage and overhang (same as stone wall)
                if (ey === safeNumYEnv - 1) {
                    // Create a larger top cap that covers gaps and overhangs the sides
                    const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                    const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                    const topCapGeo = getOrCreateGeometry(
                        `wall_top_cap_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                    );

                    // Position at the top of the wall voxel, slightly higher for prominence
                    const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                    // FIXED: Add extended top cap with overhang (same as normal stone walls)
                    tempMatrix.makeTranslation(finalX, topPosY, 0);
                    addGeometry(topCapGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;

                    // CRITICAL: Add additional gap-filling caps between voxels with matching overhang
                    if (ex < safeNumXEnv - 1) { // Not the last column
                        const gapCapGeo = getOrCreateGeometry(
                            `wall_gap_cap_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                        );
                        const gapCapX = finalX + wallVoxelSize * 0.6;
                        tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                        addGeometry(gapCapGeo, blockMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                }
            }
        }
    }

    // --- Fill Gaps with Mortar (same as stone wall) ---
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            // 5% chance for mortar/gap filler (same as normal walls)
            if (random() <= 0.05) {
                const mortarMaterial = WATER_STONE_MATERIALS[3]; // Dark slate gray for mortar

                const baseX = ex * wallVoxelSize - offsetX;
                const baseY = ey * wallVoxelSize - offsetY;

                // CRITICAL FIX: Reduced variations for gap fillers to match main voxels
                const depthVariation = (random() - 0.5) * 0.025; // Slightly less variation for mortar
                const heightVariation = (random() - 0.5) * 0.015; // Reduced height variation
                const rotationY = (random() - 0.5) * 0.015; // Reduced Y rotation
                const rotationZ = (random() - 0.5) * 0.01; // Reduced Z rotation

                const finalX = baseX + depthVariation;
                const finalY = baseY + heightVariation;

                // Use two voxels for depth (front and back faces) with reduced variation
                for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                    const posZ = z * VOXEL_SIZE - offsetZ + depthVariation * 0.2;

                    // Create transformation matrix with reduced rotation
                    tempMatrix.makeRotationFromEuler(new THREE.Euler(0, rotationY, rotationZ));
                    tempMatrix.setPosition(finalX, finalY, posZ);

                    addGeometry(largeWallGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // Add side caps for east and west faces of gap filler voxels (same as stone wall)
                // For east face (right side of wall)
                if (ex === safeNumXEnv - 1) {
                    const sideFaceGeo = getOrCreateGeometry(
                        `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                        () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                    );
                    const rightPosX = finalX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                    tempMatrix.makeTranslation(rightPosX, finalY, 0);
                    addGeometry(sideFaceGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // For west face (left side of wall)
                if (ex === 0) {
                    const sideFaceGeo = getOrCreateGeometry(
                        `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                        () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                    );
                    const leftPosX = finalX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                    tempMatrix.makeTranslation(leftPosX, finalY, 0);
                    addGeometry(sideFaceGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // FIXED: Add north/south face caps for gap fillers with proper thickness
                // For north face (front side of wall)
                if (true) { // Always add north and south caps since they're always visible from top-down view
                    const northFaceGeo = getOrCreateGeometry(
                        `water_wall_ns_cap_thick_mortar_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, depth * 0.4)
                    );
                    // FIXED: Use proper Z positioning with thick caps
                    const frontPosZ = -depth/2 - depth * 0.2;
                    tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                    addGeometry(northFaceGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;

                    // For south face (back side of wall) - FIXED: Use proper wall depth for thickness
                    const backPosZ = depth/2 + depth * 0.2;
                    tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                    addGeometry(northFaceGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;
                }

                // Add top face for the top row of gap filler voxels with extended coverage and overhang
                if (ey === safeNumYEnv - 1) {
                    // Create a larger top cap that covers gaps and overhangs the sides
                    const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                    const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                    const topCapGeo = getOrCreateGeometry(
                        `wall_top_cap_mortar_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                    );

                    // Position at the top of the wall voxel, slightly higher for prominence
                    const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                    // FIXED: Add extended top cap with overhang (same as normal stone walls)
                    tempMatrix.makeTranslation(finalX, topPosY, 0);
                    addGeometry(topCapGeo, mortarMaterial, tempMatrix);
                    voxelsAdded++;

                    // CRITICAL: Add additional gap-filling caps between gap filler voxels with matching overhang
                    if (ex < safeNumXEnv - 1) { // Not the last column
                        const gapCapGeo = getOrCreateGeometry(
                            `wall_gap_cap_mortar_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                        );
                        const gapCapX = finalX + wallVoxelSize * 0.6;
                        tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                        addGeometry(gapCapGeo, mortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }
                }
            }
        }
    }

    // --- Merge Geometries by Material (same as stone wall) ---
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex);
        if (!material) {
            console.warn(`Material not found for water stone wall: ${colorHex}`);
            continue;
        }
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            mesh.name = 'waterStoneBricks';
            mesh.userData.isWall = true;
            mesh.userData.wallType = 'stonebrick_with_water';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for water stone wall, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.wallType = 'stonebrick_with_water';
    finalGroup.userData.isWall = true;
    finalGroup.name = 'stonebrickWaterWall';

    // Set shadow properties for entire group
    finalGroup.castShadow = true;
    finalGroup.receiveShadow = true;

    console.log(`[WaterStoneWall] Generated wall with ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return {
        group: finalGroup,
        torchPositions: [] // No torches on water walls
    };
}




