import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Flame Barrier Card Prefab
 * Creates a ring of fire around the player that damages enemies and provides light
 */

// Flame barrier specific colors
const FLAME_COLORS = {
    FIRE_RED: 0xFF4500,           // Main fire color
    FLAME_ORANGE: 0xFF8C00,       // Bright flame orange
    EMBER_YELLOW: 0xFFD700,       // Golden ember glow
    CRIMSON_RED: 0xDC143C,        // Deep fire red
    INFERNO_ORANGE: 0xFF6347,     // Intense fire
    MOLTEN_GOLD: 0xFFB347,        // Molten metal glow
    HEAT_SHIMMER: 0xFFA500,       // Heat distortion
    COAL_BLACK: 0x2F4F4F          // Fire base/coal
};

/**
 * Create a flame barrier card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The flame barrier card 3D model
 */
export function createFlameBarrierCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'FlameBarrierCard';

    // Flame barrier materials
    const fireRedMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.FIRE_RED,
        emissive: FLAME_COLORS.FIRE_RED,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const flameOrangeMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.FLAME_ORANGE,
        emissive: FLAME_COLORS.FLAME_ORANGE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.85
    });

    const emberYellowMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.EMBER_YELLOW,
        emissive: FLAME_COLORS.EMBER_YELLOW,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const crimsonRedMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.CRIMSON_RED,
        emissive: FLAME_COLORS.CRIMSON_RED,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.95
    });

    const infernoOrangeMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.INFERNO_ORANGE,
        emissive: FLAME_COLORS.INFERNO_ORANGE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const moltenGoldMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.MOLTEN_GOLD,
        emissive: FLAME_COLORS.MOLTEN_GOLD,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.7
    });

    const heatShimmerMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.HEAT_SHIMMER,
        emissive: FLAME_COLORS.HEAT_SHIMMER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.6
    });

    const coalBlackMaterial = new THREE.MeshLambertMaterial({
        color: FLAME_COLORS.COAL_BLACK,
        emissive: FLAME_COLORS.COAL_BLACK,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 1.0
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Fire ring (circular flame barrier)
    const fireRingVoxels = [
        // Inner fire ring
        { x: -0.12, y: 0.0, z: 0.0, material: fireRedMaterial },
        { x: 0.12, y: 0.0, z: 0.0, material: fireRedMaterial },
        { x: 0.0, y: 0.0, z: -0.12, material: fireRedMaterial },
        { x: 0.0, y: 0.0, z: 0.12, material: fireRedMaterial },
        { x: -0.08, y: 0.0, z: -0.08, material: crimsonRedMaterial },
        { x: 0.08, y: 0.0, z: -0.08, material: crimsonRedMaterial },
        { x: -0.08, y: 0.0, z: 0.08, material: crimsonRedMaterial },
        { x: 0.08, y: 0.0, z: 0.08, material: crimsonRedMaterial },
        
        // Middle fire ring
        { x: -0.16, y: 0.04, z: 0.0, material: flameOrangeMaterial },
        { x: 0.16, y: 0.04, z: 0.0, material: flameOrangeMaterial },
        { x: 0.0, y: 0.04, z: -0.16, material: flameOrangeMaterial },
        { x: 0.0, y: 0.04, z: 0.16, material: flameOrangeMaterial },
        { x: -0.12, y: 0.04, z: -0.12, material: infernoOrangeMaterial },
        { x: 0.12, y: 0.04, z: -0.12, material: infernoOrangeMaterial },
        { x: -0.12, y: 0.04, z: 0.12, material: infernoOrangeMaterial },
        { x: 0.12, y: 0.04, z: 0.12, material: infernoOrangeMaterial },
        
        // Outer fire ring
        { x: -0.20, y: 0.08, z: 0.0, material: emberYellowMaterial },
        { x: 0.20, y: 0.08, z: 0.0, material: emberYellowMaterial },
        { x: 0.0, y: 0.08, z: -0.20, material: emberYellowMaterial },
        { x: 0.0, y: 0.08, z: 0.20, material: emberYellowMaterial },
        { x: -0.16, y: 0.08, z: -0.16, material: moltenGoldMaterial },
        { x: 0.16, y: 0.08, z: -0.16, material: moltenGoldMaterial },
        { x: -0.16, y: 0.08, z: 0.16, material: moltenGoldMaterial },
        { x: 0.16, y: 0.08, z: 0.16, material: moltenGoldMaterial }
    ];

    // Flame tongues (dancing fire)
    const flameTongueVoxels = [
        // Rising flames
        { x: -0.12, y: 0.12, z: 0.0, material: emberYellowMaterial },
        { x: 0.12, y: 0.12, z: 0.0, material: emberYellowMaterial },
        { x: 0.0, y: 0.12, z: -0.12, material: emberYellowMaterial },
        { x: 0.0, y: 0.12, z: 0.12, material: emberYellowMaterial },
        { x: -0.08, y: 0.16, z: -0.08, material: moltenGoldMaterial },
        { x: 0.08, y: 0.16, z: -0.08, material: moltenGoldMaterial },
        { x: -0.08, y: 0.16, z: 0.08, material: moltenGoldMaterial },
        { x: 0.08, y: 0.16, z: 0.08, material: moltenGoldMaterial },
        
        // Higher flame tips
        { x: -0.16, y: 0.20, z: 0.0, material: moltenGoldMaterial },
        { x: 0.16, y: 0.20, z: 0.0, material: moltenGoldMaterial },
        { x: 0.0, y: 0.20, z: -0.16, material: moltenGoldMaterial },
        { x: 0.0, y: 0.20, z: 0.16, material: moltenGoldMaterial },
        { x: -0.04, y: 0.24, z: -0.04, material: emberYellowMaterial },
        { x: 0.04, y: 0.24, z: -0.04, material: emberYellowMaterial },
        { x: -0.04, y: 0.24, z: 0.04, material: emberYellowMaterial },
        { x: 0.04, y: 0.24, z: 0.04, material: emberYellowMaterial },
        
        // Flame peaks
        { x: -0.08, y: 0.28, z: 0.0, material: emberYellowMaterial },
        { x: 0.08, y: 0.28, z: 0.0, material: emberYellowMaterial },
        { x: 0.0, y: 0.28, z: -0.08, material: emberYellowMaterial },
        { x: 0.0, y: 0.28, z: 0.08, material: emberYellowMaterial },
        { x: 0.0, y: 0.32, z: 0.0, material: moltenGoldMaterial }
    ];

    // Heat shimmer (distortion effects)
    const heatShimmerVoxels = [
        // Inner heat waves
        { x: -0.10, y: 0.06, z: -0.06, material: heatShimmerMaterial },
        { x: 0.10, y: 0.06, z: 0.06, material: heatShimmerMaterial },
        { x: -0.06, y: 0.10, z: -0.10, material: heatShimmerMaterial },
        { x: 0.06, y: 0.10, z: 0.10, material: heatShimmerMaterial },
        { x: -0.14, y: 0.02, z: 0.0, material: heatShimmerMaterial },
        { x: 0.14, y: 0.02, z: 0.0, material: heatShimmerMaterial },
        
        // Middle heat waves
        { x: -0.18, y: 0.12, z: -0.08, material: heatShimmerMaterial },
        { x: 0.18, y: 0.12, z: 0.08, material: heatShimmerMaterial },
        { x: -0.08, y: 0.18, z: -0.18, material: heatShimmerMaterial },
        { x: 0.08, y: 0.18, z: 0.18, material: heatShimmerMaterial },
        { x: -0.22, y: 0.06, z: 0.04, material: heatShimmerMaterial },
        { x: 0.22, y: 0.06, z: -0.04, material: heatShimmerMaterial },
        
        // Outer heat waves
        { x: -0.26, y: 0.16, z: -0.12, material: heatShimmerMaterial },
        { x: 0.26, y: 0.16, z: 0.12, material: heatShimmerMaterial },
        { x: -0.12, y: 0.22, z: -0.26, material: heatShimmerMaterial },
        { x: 0.12, y: 0.22, z: 0.26, material: heatShimmerMaterial },
        { x: -0.30, y: 0.10, z: 0.08, material: heatShimmerMaterial },
        { x: 0.30, y: 0.10, z: -0.08, material: heatShimmerMaterial }
    ];

    // Ember sparks (floating fire particles)
    const emberSparkVoxels = [
        // Close sparks
        { x: -0.24, y: 0.20, z: 0.0, material: emberYellowMaterial },
        { x: 0.24, y: 0.20, z: 0.0, material: emberYellowMaterial },
        { x: 0.0, y: 0.24, z: -0.20, material: moltenGoldMaterial },
        { x: 0.0, y: 0.24, z: 0.20, material: moltenGoldMaterial },
        { x: -0.20, y: 0.28, z: -0.16, material: emberYellowMaterial },
        { x: 0.20, y: 0.28, z: 0.16, material: emberYellowMaterial },
        
        // Distant sparks
        { x: -0.32, y: 0.24, z: 0.08, material: moltenGoldMaterial },
        { x: 0.32, y: 0.24, z: -0.08, material: moltenGoldMaterial },
        { x: -0.08, y: 0.32, z: -0.24, material: emberYellowMaterial },
        { x: 0.08, y: 0.32, z: 0.24, material: emberYellowMaterial },
        { x: -0.28, y: 0.36, z: -0.12, material: moltenGoldMaterial },
        { x: 0.28, y: 0.36, z: 0.12, material: moltenGoldMaterial },
        
        // High sparks
        { x: -0.16, y: 0.40, z: 0.0, material: emberYellowMaterial },
        { x: 0.16, y: 0.40, z: 0.0, material: emberYellowMaterial },
        { x: 0.0, y: 0.44, z: -0.12, material: moltenGoldMaterial },
        { x: 0.0, y: 0.44, z: 0.12, material: moltenGoldMaterial },
        { x: -0.12, y: 0.48, z: -0.08, material: emberYellowMaterial },
        { x: 0.12, y: 0.48, z: 0.08, material: emberYellowMaterial }
    ];

    // Create fire ring group
    const fireRingGroup = new THREE.Group();
    fireRingGroup.name = 'fireRing';

    // Add fire ring voxels
    fireRingVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        fireRingGroup.add(mesh);
    });

    // Create flame tongues group
    const flameTongueGroup = new THREE.Group();
    flameTongueGroup.name = 'flameTongues';

    // Add flame tongue voxels
    flameTongueVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.flamePhase = index * 0.12; // Stagger animation
        flameTongueGroup.add(mesh);
    });

    // Create heat shimmer group
    const heatShimmerGroup = new THREE.Group();
    heatShimmerGroup.name = 'heatShimmer';

    // Add heat shimmer voxels
    heatShimmerVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.shimmerPhase = index * 0.08; // Stagger animation
        heatShimmerGroup.add(mesh);
    });

    // Create ember sparks group
    const emberSparkGroup = new THREE.Group();
    emberSparkGroup.name = 'emberSparks';

    // Add ember spark voxels
    emberSparkVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sparkPhase = index * 0.06; // Stagger animation
        emberSparkGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(fireRingGroup);
    cardGroup.add(flameTongueGroup);
    cardGroup.add(heatShimmerGroup);
    cardGroup.add(emberSparkGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        fireIntensity: 0,
        flameDance: 0,
        heatWaves: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update flame barrier card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateFlameBarrierCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.fireIntensity += deltaTime * 2.5; // Fire intensity speed
    cardGroup.userData.flameDance += deltaTime * 4.0; // Flame dance speed
    cardGroup.userData.heatWaves += deltaTime * 3.5; // Heat wave speed

    const time = cardGroup.userData.animationTime;
    const fireIntensity = cardGroup.userData.fireIntensity;
    const flameDance = cardGroup.userData.flameDance;
    const heatWaves = cardGroup.userData.heatWaves;

    // Animate fire ring (steady burning)
    const fireRingGroup = cardGroup.getObjectByName('fireRing');
    if (fireRingGroup) {
        // Fire intensity pulsing
        const fireGlow = 1.0 + Math.sin(fireIntensity * 1.8) * 0.4;
        fireRingGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * fireGlow;
            }
        });
    }

    // Animate flame tongues (dancing flames)
    const flameTongueGroup = cardGroup.getObjectByName('flameTongues');
    if (flameTongueGroup) {
        flameTongueGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.flamePhase !== undefined) {
                const flameTime = flameDance + mesh.userData.flamePhase;
                
                // Flame dancing motion
                const flameDancing = Math.sin(flameTime * 3.0) * 0.002;
                const flameFlicker = Math.cos(flameTime * 5.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + flameDancing;
                mesh.position.y = mesh.userData.originalPosition.y + flameFlicker;
                
                // Flame intensity fluctuation
                const flameIntensity = 1.0 + Math.sin(flameTime * 6.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * flameIntensity;
                }
                
                // Flame scale variation
                const flameScale = 0.9 + Math.sin(flameTime * 4.5) * 0.2;
                mesh.scale.setScalar(flameScale);
            }
        });
    }

    // Animate heat shimmer (heat distortion)
    const heatShimmerGroup = cardGroup.getObjectByName('heatShimmer');
    if (heatShimmerGroup) {
        heatShimmerGroup.rotation.y = time * 1.2; // Slow heat rotation
        
        heatShimmerGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.shimmerPhase !== undefined) {
                const shimmerTime = heatWaves + mesh.userData.shimmerPhase;
                
                // Heat shimmer motion
                const shimmer = Math.sin(shimmerTime * 4.0) * 0.0015;
                const distortion = Math.cos(shimmerTime * 3.5) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + shimmer;
                mesh.position.z = mesh.userData.originalPosition.z + distortion;
                
                // Heat intensity fluctuation
                const heatIntensity = 1.0 + Math.sin(shimmerTime * 5.5) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * heatIntensity;
                }
                
                // Heat opacity variation
                const heatOpacity = 0.6 + Math.sin(shimmerTime * 4.0) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * heatOpacity;
                }
            }
        });
    }

    // Animate ember sparks (floating sparks)
    const emberSparkGroup = cardGroup.getObjectByName('emberSparks');
    if (emberSparkGroup) {
        emberSparkGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparkPhase !== undefined) {
                const sparkTime = flameDance + mesh.userData.sparkPhase;
                
                // Spark floating motion
                const sparkFloat = Math.sin(sparkTime * 2.5) * 0.003;
                const sparkDrift = Math.cos(sparkTime * 3.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + sparkFloat;
                mesh.position.y = mesh.userData.originalPosition.y + sparkDrift;
                
                // Spark twinkling
                const sparkTwinkle = 1.0 + Math.sin(sparkTime * 8.0) * 0.6;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * sparkTwinkle;
                }
                
                // Spark scale variation
                const sparkScale = 0.8 + Math.sin(sparkTime * 6.0) * 0.3;
                mesh.scale.setScalar(sparkScale);
            }
        });
    }

    // Overall flame barrier pulsing (burning heat)
    const flamePulse = 1 + Math.sin(time * 2.5) * 0.06;
    cardGroup.scale.setScalar(0.8 * flamePulse);
}

// Export the flame barrier card data for the loot system
export const FLAME_BARRIER_CARD_DATA = {
    name: "Flame Barrier",
    description: 'Summons a ring of fire around the player for 35 seconds that damages approaching enemies and provides light in dark areas.',
    category: 'card',
    rarity: 'epic',
    effect: 'flame_barrier',
    effectValue: 35, // Duration in seconds
    createFunction: createFlameBarrierCard,
    updateFunction: updateFlameBarrierCardAnimation,
    voxelModel: 'flame_barrier_card',
    glow: {
        color: 0xFF4500,
        intensity: 1.5
    }
};

export default createFlameBarrierCard;