import * as THREE from 'three';
import { VoxelInstanceManager } from '../../systems/VoxelInstanceManager.js';
import { VOXEL_SIZE, _getMaterialByHex_Cached } from './shared.js';

/**
 * Create an instanced floor - this is where you'll see the biggest performance gains
 * A typical room floor might have 400+ voxels, which becomes just a few draw calls
 */
export function createFloorInstanced(width, height, options = {}) {
    const { 
        baseColor = '4a4a4a',
        variant1Color = '525252', 
        crackedColor = '333333',
        seed = 12345 
    } = options;
    
    const floorGroup = new THREE.Group();
    const manager = new VoxelInstanceManager();
    
    // Get materials
    const baseMaterial = _getMaterialByHex_Cached(baseColor);
    const variant1Material = _getMaterialByHex_Cached(variant1Color);
    const crackedMaterial = _getMaterialByHex_Cached(crackedColor);
    
    // Simple random for variation
    let rng = seed;
    const random = () => {
        rng = (rng * 9301 + 49297) % 233280;
        return rng / 233280;
    };
    
    // Create floor tiles
    for (let x = 0; x < width; x++) {
        for (let z = 0; z < height; z++) {
            const worldX = (x - width / 2) * VOXEL_SIZE;
            const worldZ = (z - height / 2) * VOXEL_SIZE;
            const worldY = -VOXEL_SIZE / 2; // Floor level
            
            // Choose material based on random variation
            let material;
            const rand = random();
            if (rand < 0.1) {
                material = crackedMaterial; // 10% cracked
            } else if (rand < 0.3) {
                material = variant1Material; // 20% variant
            } else {
                material = baseMaterial; // 70% base
            }
            
            // Add voxel to manager
            const position = new THREE.Vector3(worldX, worldY, worldZ);
            manager.addVoxel(position, VOXEL_SIZE, material);
        }
    }
    
    // Build instanced meshes
    const instancedFloor = manager.build();
    floorGroup.add(instancedFloor);
    
    // Set userData
    floorGroup.userData = {
        type: 'floor',
        isInstanced: true,
        voxelCount: width * height
    };
    
    return floorGroup;
}

/**
 * Create instanced walls for a room
 * Walls typically have hundreds of voxels - perfect for instancing
 */
export function createWallsInstanced(roomWidth, roomHeight, wallHeight, options = {}) {
    const { 
        primaryColor = '555c66',
        secondaryColor = '606873',
        seed = 54321 
    } = options;
    
    const wallsGroup = new THREE.Group();
    const manager = new VoxelInstanceManager();
    
    // Get materials
    const primaryMaterial = _getMaterialByHex_Cached(primaryColor);
    const secondaryMaterial = _getMaterialByHex_Cached(secondaryColor);
    
    // Simple random
    let rng = seed;
    const random = () => {
        rng = (rng * 9301 + 49297) % 233280;
        return rng / 233280;
    };
    
    // Create walls (north, south, east, west)
    const walls = [
        { x: 0, z: -roomHeight/2, width: roomWidth, depth: 1 }, // North
        { x: 0, z: roomHeight/2, width: roomWidth, depth: 1 },  // South
        { x: -roomWidth/2, z: 0, width: 1, depth: roomHeight }, // West
        { x: roomWidth/2, z: 0, width: 1, depth: roomHeight }   // East
    ];
    
    walls.forEach(wall => {
        for (let x = 0; x < wall.width; x++) {
            for (let z = 0; z < wall.depth; z++) {
                for (let y = 0; y < wallHeight; y++) {
                    const worldX = (wall.x + (x - wall.width/2)) * VOXEL_SIZE;
                    const worldZ = (wall.z + (z - wall.depth/2)) * VOXEL_SIZE;
                    const worldY = y * VOXEL_SIZE;
                    
                    // Choose material
                    const material = random() < 0.3 ? secondaryMaterial : primaryMaterial;
                    
                    // Add voxel
                    const position = new THREE.Vector3(worldX, worldY, worldZ);
                    manager.addVoxel(position, VOXEL_SIZE, material);
                }
            }
        }
    });
    
    // Build instanced meshes
    const instancedWalls = manager.build();
    wallsGroup.add(instancedWalls);
    
    // Set userData
    wallsGroup.userData = {
        type: 'walls',
        isInstanced: true,
        voxelCount: (roomWidth * 2 + roomHeight * 2 - 4) * wallHeight
    };
    
    return wallsGroup;
}