import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create the Eye of Judgment - a giant floating eye that judges players' sins
 * This is the central focus of the Eye of Judgment event room
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Eye of Judgment object group
 */
export function createEyeOfJudgmentObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 666);

    // Use larger voxel size for the Eye to make it prominent
    const eyeVoxelSize = VOXEL_SIZE * 3.0;
    const baseGeometry = getOrCreateGeometry('eye_voxel', () =>
        new THREE.BoxGeometry(eyeVoxelSize, eyeVoxelSize, eyeVoxelSize)
    );

    // Eye materials with cold, judging appearance
    const scleraMaterial = _getMaterialByHex_Cached('E8E8E8', {
        roughness: 0.2,
        metalness: 0.1
    }); // White sclera

    const irisMaterial = _getMaterialByHex_Cached('1E90FF', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x1E90FF),
        emissiveIntensity: 0.3,
        roughness: 0.1,
        metalness: 0.2
    }); // Cold blue iris

    const pupilMaterial = _getMaterialByHex_Cached('000022', {
        emissive: new THREE.Color(0x000044),
        emissiveIntensity: 0.1,
        roughness: 0.0,
        metalness: 0.0
    }); // Dark pupil with hint of blue

    const highlightMaterial = _getMaterialByHex_Cached('FFFFFF', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0xFFFFFF),
        emissiveIntensity: 0.5
    }); // Bright highlights

    const shadowMaterial = _getMaterialByHex_Cached('CCCCCC', {
        roughness: 0.3,
        metalness: 0.05
    }); // Eye shadows

    // Create the main eye sphere structure using voxels
    // Outer eye shape (roughly spherical, 7x5 pattern)
    const eyeVoxelPositions = [
        // Bottom row (y = -2)
        { x: -1, y: -2, z: 0 }, { x: 0, y: -2, z: 0 }, { x: 1, y: -2, z: 0 },
        
        // Lower-middle row (y = -1)  
        { x: -2, y: -1, z: 0 }, { x: -1, y: -1, z: 0 }, { x: 0, y: -1, z: 0 }, 
        { x: 1, y: -1, z: 0 }, { x: 2, y: -1, z: 0 },
        
        // Center row (y = 0) - largest
        { x: -3, y: 0, z: 0 }, { x: -2, y: 0, z: 0 }, { x: -1, y: 0, z: 0 }, 
        { x: 0, y: 0, z: 0 }, { x: 1, y: 0, z: 0 }, { x: 2, y: 0, z: 0 }, 
        { x: 3, y: 0, z: 0 },
        
        // Upper-middle row (y = 1)
        { x: -2, y: 1, z: 0 }, { x: -1, y: 1, z: 0 }, { x: 0, y: 1, z: 0 }, 
        { x: 1, y: 1, z: 0 }, { x: 2, y: 1, z: 0 },
        
        // Top row (y = 2)
        { x: -1, y: 2, z: 0 }, { x: 0, y: 2, z: 0 }, { x: 1, y: 2, z: 0 }
    ];

    // Add sclera (white) voxels - using merged geometry for performance
    const scleraGeometries = [];
    eyeVoxelPositions.forEach(pos => {
        const geometry = baseGeometry.clone();
        geometry.translate(
            pos.x * eyeVoxelSize,
            pos.y * eyeVoxelSize,
            pos.z * eyeVoxelSize
        );
        scleraGeometries.push(geometry);
    });
    
    // Merge all sclera geometries into one mesh
    if (scleraGeometries.length > 0) {
        const mergedScleraGeometry = BufferGeometryUtils.mergeGeometries(scleraGeometries);
        const scleraMesh = new THREE.Mesh(mergedScleraGeometry, scleraMaterial);
        scleraMesh.userData.isEyePart = true;
        scleraMesh.userData.partType = 'sclera';
        scleraMesh.castShadow = true;
        scleraMesh.receiveShadow = true;
        group.add(scleraMesh);
    }

    // Create iris (blue center)
    const irisPositions = [
        // Inner iris pattern
        { x: -1, y: -1, z: 0.1 }, { x: 0, y: -1, z: 0.1 }, { x: 1, y: -1, z: 0.1 },
        { x: -1, y: 0, z: 0.1 }, { x: 0, y: 0, z: 0.1 }, { x: 1, y: 0, z: 0.1 },
        { x: -1, y: 1, z: 0.1 }, { x: 0, y: 1, z: 0.1 }, { x: 1, y: 1, z: 0.1 }
    ];

    // Merge iris voxels for performance
    const irisGeometries = [];
    irisPositions.forEach(pos => {
        const geometry = baseGeometry.clone();
        geometry.translate(
            pos.x * eyeVoxelSize,
            pos.y * eyeVoxelSize,
            pos.z * eyeVoxelSize
        );
        irisGeometries.push(geometry);
    });
    
    if (irisGeometries.length > 0) {
        const mergedIrisGeometry = BufferGeometryUtils.mergeGeometries(irisGeometries);
        const irisMesh = new THREE.Mesh(mergedIrisGeometry, irisMaterial);
        irisMesh.userData.isEyePart = true;
        irisMesh.userData.partType = 'iris';
        irisMesh.castShadow = false;
        irisMesh.receiveShadow = false;
        group.add(irisMesh);
    }

    // Create pupil (dark center)
    const pupilPositions = [
        { x: 0, y: 0, z: 0.2 }, // Central pupil
        { x: 0, y: -1, z: 0.15 }, { x: 0, y: 1, z: 0.15 } // Extended pupil
    ];

    pupilPositions.forEach(pos => {
        const voxel = new THREE.Mesh(baseGeometry.clone(), pupilMaterial);
        voxel.position.set(
            pos.x * eyeVoxelSize,
            pos.y * eyeVoxelSize,
            pos.z * eyeVoxelSize
        );
        voxel.userData.isEyePart = true;
        voxel.userData.partType = 'pupil';
        voxel.castShadow = false;
        voxel.receiveShadow = false;
        group.add(voxel);
    });

    // Add highlights for realism
    const highlightPositions = [
        { x: -1, y: 1, z: 0.3 }, // Upper left highlight
        { x: 1, y: -1, z: 0.25 } // Lower right secondary highlight
    ];

    highlightPositions.forEach(pos => {
        const voxel = new THREE.Mesh(
            getOrCreateGeometry('eye_highlight', () =>
                new THREE.BoxGeometry(eyeVoxelSize * 0.6, eyeVoxelSize * 0.6, eyeVoxelSize * 0.6)
            ),
            highlightMaterial
        );
        voxel.position.set(
            pos.x * eyeVoxelSize,
            pos.y * eyeVoxelSize,
            pos.z * eyeVoxelSize
        );
        voxel.userData.isEyePart = true;
        voxel.userData.partType = 'highlight';
        voxel.castShadow = false;
        voxel.receiveShadow = false;
        group.add(voxel);
    });

    // Add shadow details around the eye
    const shadowPositions = [
        { x: -3, y: -1, z: -0.2 }, { x: 3, y: -1, z: -0.2 }, // Side shadows
        { x: 0, y: -2, z: -0.15 }, { x: 0, y: 2, z: -0.15 } // Top/bottom shadows
    ];

    shadowPositions.forEach(pos => {
        const voxel = new THREE.Mesh(
            getOrCreateGeometry('eye_shadow', () =>
                new THREE.BoxGeometry(eyeVoxelSize * 0.8, eyeVoxelSize * 0.8, eyeVoxelSize * 0.4)
            ),
            shadowMaterial
        );
        voxel.position.set(
            pos.x * eyeVoxelSize,
            pos.y * eyeVoxelSize,
            pos.z * eyeVoxelSize
        );
        voxel.userData.isEyePart = true;
        voxel.userData.partType = 'shadow';
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    });

    // Create energy tendrils around the eye (mystical atmosphere)
    const tendrilMaterial = _getMaterialByHex_Cached('4169E1', {
        transparent: true,
        opacity: 0.4,
        emissive: new THREE.Color(0x4169E1),
        emissiveIntensity: 0.2
    }); // Royal blue tendrils

    const tendrilGeometry = getOrCreateGeometry('eye_tendril', () =>
        new THREE.BoxGeometry(eyeVoxelSize * 0.3, eyeVoxelSize * 0.3, eyeVoxelSize * 0.3)
    );

    // Spiral pattern around the eye - reduced for performance
    const tendrilCount = 6;
    for (let i = 0; i < tendrilCount; i++) {
        const angle = (i / tendrilCount) * Math.PI * 2;
        const radius = 6;
        const height = Math.sin(angle * 2) * 2; // Wavy height pattern
        
        const tendril = new THREE.Mesh(tendrilGeometry, tendrilMaterial);
        tendril.position.set(
            Math.cos(angle) * radius * eyeVoxelSize,
            height * eyeVoxelSize,
            Math.sin(angle) * radius * eyeVoxelSize
        );
        tendril.userData.isEyePart = true;
        tendril.userData.partType = 'tendril';
        tendril.userData.animationPhase = angle;
        tendril.userData.baseRadius = radius;
        tendril.userData.baseHeight = height;
        tendril.castShadow = false;
        tendril.receiveShadow = false;
        group.add(tendril);
    }

    // Add ethereal particles around the eye
    const particleMaterial = _getMaterialByHex_Cached('87CEEB', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0x87CEEB),
        emissiveIntensity: 0.4
    }); // Sky blue particles

    const particleGeometry = getOrCreateGeometry('eye_particle', () =>
        new THREE.BoxGeometry(eyeVoxelSize * 0.2, eyeVoxelSize * 0.2, eyeVoxelSize * 0.2)
    );

    // Random particles floating around - reduced for performance
    for (let i = 0; i < 4; i++) {
        const particle = new THREE.Mesh(particleGeometry, particleMaterial);
        const angle = rng() * Math.PI * 2;
        const radius = 4 + rng() * 4;
        const height = (rng() - 0.5) * 8;
        
        particle.position.set(
            Math.cos(angle) * radius * eyeVoxelSize,
            height * eyeVoxelSize,
            Math.sin(angle) * radius * eyeVoxelSize
        );
        particle.userData.isEyePart = true;
        particle.userData.partType = 'particle';
        particle.userData.animationPhase = rng() * Math.PI * 2;
        particle.userData.floatSpeed = 0.5 + rng() * 1.0;
        particle.castShadow = false;
        particle.receiveShadow = false;
        group.add(particle);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'eye_of_judgment',
        isInteractable: true,
        interactionType: 'eye_judgment',
        isEventObject: true,
        objectId: options.userData?.objectId || 'central_eye_of_judgment',
        isFloating: true,
        hasAnimation: true,
        animationType: 'floating_eye',
        voxelScale: eyeVoxelSize,
        // Judgment properties
        isJudgingEye: true,
        canJudgeSins: true,
        judgmentState: 'watching', // watching, judging, reacting
        // Animation properties
        floatAmplitude: 0.3,
        floatSpeed: 0.002,
        rotationSpeed: 0.001,
        pulseSpeed: 0.003,
        // Voice properties
        hasVoice: true,
        voiceType: 'judgment',
        // Interaction
        interaction: {
            type: 'eye_judgment',
            id: 'central_eye_of_judgment'
        }
    };

    // Position eye above ground (floating)
    group.position.y = 4.0; // Float 4 units above ground

    group.name = 'eye_of_judgment';

    console.log('[EyeOfJudgmentObject] ✅ Created floating Eye of Judgment with mystical effects');
    return group;
}

/**
 * Animate the Eye of Judgment with floating, pulsing, and rotation effects
 * @param {THREE.Group} eyeGroup - The eye group to animate
 * @param {number} deltaTime - Time since last frame
 * @param {number} time - Total elapsed time
 */
export function animateEyeOfJudgment(eyeGroup, deltaTime, time) {
    if (!eyeGroup || !eyeGroup.userData.isJudgingEye) return;

    const { floatAmplitude, floatSpeed, rotationSpeed, pulseSpeed } = eyeGroup.userData;

    // Gentle floating animation
    const floatOffset = Math.sin(time * floatSpeed) * floatAmplitude;
    eyeGroup.position.y = 4.0 + floatOffset;

    // Slow rotation around Y axis
    eyeGroup.rotation.y += rotationSpeed * deltaTime;

    // Animate tendrils and particles
    eyeGroup.traverse(child => {
        if (child.userData.partType === 'tendril') {
            // Spiral motion for tendrils
            const phase = child.userData.animationPhase + time * 0.001;
            const radius = child.userData.baseRadius + Math.sin(phase * 3) * 0.5;
            const height = child.userData.baseHeight + Math.cos(phase * 2) * 0.3;
            
            child.position.x = Math.cos(phase) * radius * child.parent.userData.voxelScale;
            child.position.y = height * child.parent.userData.voxelScale;
            child.position.z = Math.sin(phase) * radius * child.parent.userData.voxelScale;
            
            // Gentle rotation
            child.rotation.y += 0.002 * deltaTime;
        } else if (child.userData.partType === 'particle') {
            // Floating motion for particles
            const phase = child.userData.animationPhase + time * child.userData.floatSpeed * 0.001;
            child.position.y += Math.sin(phase) * 0.01;
            
            // Gentle opacity pulsing
            if (child.material.transparent) {
                child.material.opacity = 0.6 + Math.sin(phase * 2) * 0.2;
            }
        } else if (child.userData.partType === 'iris' || child.userData.partType === 'pupil') {
            // Subtle pulsing for iris and pupil
            const pulse = 1.0 + Math.sin(time * pulseSpeed) * 0.05;
            child.scale.setScalar(pulse);
            
            // Enhanced emissive intensity during judgment
            if (child.material.emissive && eyeGroup.userData.judgmentState === 'judging') {
                const intensity = child.material.emissiveIntensity;
                child.material.emissiveIntensity = intensity + Math.sin(time * 0.01) * 0.1;
            }
        }
    });
}