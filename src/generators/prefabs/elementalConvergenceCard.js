import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Elemental Convergence Card Prefab
 * Creates swirling elemental energies (fire, ice, lightning) converging into a powerful storm
 */

// Elemental convergence specific colors
const ELEMENTAL_COLORS = {
    FIRE_RED: 0xFF4500,              // Orange-red fire
    FIRE_ORANGE: 0xFF6600,           // Bright orange
    FIRE_YELLOW: 0xFFD700,           // Golden fire core
    ICE_BLUE: 0x00BFFF,              // Deep sky blue
    ICE_CYAN: 0x00FFFF,              // Cyan ice
    ICE_WHITE: 0xF0F8FF,             // Alice blue (ice white)
    LIGHTNING_PURPLE: 0x9932CC,      // Dark orchid
    LIGHTNING_VIOLET: 0x8A2BE2,      // Blue violet
    LIGHTNING_WHITE: 0xFFFFFF,       // Pure white lightning
    STORM_CORE: 0x4B0082,            // Indigo storm center
    ENERGY_GOLD: 0xFFD700            // Golden convergence energy
};

/**
 * Create an elemental convergence card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The elemental convergence card 3D model
 */
export function createElementalConvergenceCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ElementalConvergenceCard';

    // Fire materials
    const fireRedMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.FIRE_RED,
        emissive: ELEMENTAL_COLORS.FIRE_RED,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const fireOrangeMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.FIRE_ORANGE,
        emissive: ELEMENTAL_COLORS.FIRE_ORANGE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const fireYellowMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.FIRE_YELLOW,
        emissive: ELEMENTAL_COLORS.FIRE_YELLOW,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.85
    });

    // Ice materials
    const iceBlueMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.ICE_BLUE,
        emissive: ELEMENTAL_COLORS.ICE_BLUE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const iceCyanMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.ICE_CYAN,
        emissive: ELEMENTAL_COLORS.ICE_CYAN,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    const iceWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.ICE_WHITE,
        emissive: ELEMENTAL_COLORS.ICE_WHITE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    // Lightning materials
    const lightningPurpleMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.LIGHTNING_PURPLE,
        emissive: ELEMENTAL_COLORS.LIGHTNING_PURPLE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.85
    });

    const lightningVioletMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.LIGHTNING_VIOLET,
        emissive: ELEMENTAL_COLORS.LIGHTNING_VIOLET,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const lightningWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.LIGHTNING_WHITE,
        emissive: ELEMENTAL_COLORS.LIGHTNING_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    // Storm core materials
    const stormCoreMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.STORM_CORE,
        emissive: ELEMENTAL_COLORS.STORM_CORE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const energyGoldMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.ENERGY_GOLD,
        emissive: ELEMENTAL_COLORS.ENERGY_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Fire element spiral (bottom-left to center)
    const fireElementVoxels = [
        // Fire base (bottom-left)
        { x: -0.25, y: -0.25, z: 0, material: fireRedMaterial },
        { x: -0.3, y: -0.2, z: 0, material: fireOrangeMaterial },
        { x: -0.2, y: -0.3, z: 0, material: fireYellowMaterial },
        
        // Fire spiral toward center
        { x: -0.2, y: -0.15, z: 0, material: fireRedMaterial },
        { x: -0.15, y: -0.1, z: 0, material: fireOrangeMaterial },
        { x: -0.1, y: -0.05, z: 0, material: fireYellowMaterial },
        { x: -0.05, y: 0, z: 0, material: fireRedMaterial },
        
        // Fire reaching toward center
        { x: -0.35, y: -0.15, z: 0, material: fireOrangeMaterial },
        { x: -0.25, y: -0.05, z: 0, material: fireYellowMaterial },
        { x: -0.15, y: 0.05, z: 0, material: fireRedMaterial },
        
        // Fire particles
        { x: -0.3, y: -0.1, z: 0, material: fireYellowMaterial },
        { x: -0.1, y: -0.2, z: 0, material: fireOrangeMaterial }
    ];

    // Ice element spiral (top-right to center)
    const iceElementVoxels = [
        // Ice base (top-right)
        { x: 0.25, y: 0.25, z: 0, material: iceBlueMaterial },
        { x: 0.3, y: 0.2, z: 0, material: iceCyanMaterial },
        { x: 0.2, y: 0.3, z: 0, material: iceWhiteMaterial },
        
        // Ice spiral toward center
        { x: 0.2, y: 0.15, z: 0, material: iceBlueMaterial },
        { x: 0.15, y: 0.1, z: 0, material: iceCyanMaterial },
        { x: 0.1, y: 0.05, z: 0, material: iceWhiteMaterial },
        { x: 0.05, y: 0, z: 0, material: iceBlueMaterial },
        
        // Ice reaching toward center
        { x: 0.35, y: 0.15, z: 0, material: iceCyanMaterial },
        { x: 0.25, y: 0.05, z: 0, material: iceWhiteMaterial },
        { x: 0.15, y: -0.05, z: 0, material: iceBlueMaterial },
        
        // Ice crystals
        { x: 0.3, y: 0.1, z: 0, material: iceWhiteMaterial },
        { x: 0.1, y: 0.2, z: 0, material: iceCyanMaterial }
    ];

    // Lightning element spiral (top-left to center)
    const lightningElementVoxels = [
        // Lightning base (top-left)
        { x: -0.25, y: 0.25, z: 0, material: lightningPurpleMaterial },
        { x: -0.3, y: 0.2, z: 0, material: lightningVioletMaterial },
        { x: -0.2, y: 0.3, z: 0, material: lightningWhiteMaterial },
        
        // Lightning bolts toward center
        { x: -0.2, y: 0.15, z: 0, material: lightningPurpleMaterial },
        { x: -0.15, y: 0.1, z: 0, material: lightningWhiteMaterial },
        { x: -0.1, y: 0.05, z: 0, material: lightningVioletMaterial },
        { x: -0.05, y: 0.02, z: 0, material: lightningWhiteMaterial },
        
        // Lightning branches
        { x: -0.35, y: 0.15, z: 0, material: lightningVioletMaterial },
        { x: -0.25, y: 0.05, z: 0, material: lightningWhiteMaterial },
        { x: -0.15, y: -0.02, z: 0, material: lightningPurpleMaterial },
        
        // Lightning sparks
        { x: -0.3, y: 0.1, z: 0, material: lightningWhiteMaterial },
        { x: -0.1, y: 0.2, z: 0, material: lightningVioletMaterial }
    ];

    // Storm convergence center
    const stormCenterVoxels = [
        // Central storm core
        { x: 0, y: 0, z: 0, material: stormCoreMaterial },
        { x: 0, y: 0.05, z: 0, material: energyGoldMaterial },
        { x: 0, y: -0.05, z: 0, material: energyGoldMaterial },
        { x: 0.05, y: 0, z: 0, material: stormCoreMaterial },
        { x: -0.05, y: 0, z: 0, material: stormCoreMaterial },
        
        // Energy convergence ring
        { x: 0.1, y: 0.1, z: 0, material: energyGoldMaterial },
        { x: -0.1, y: 0.1, z: 0, material: energyGoldMaterial },
        { x: 0.1, y: -0.1, z: 0, material: energyGoldMaterial },
        { x: -0.1, y: -0.1, z: 0, material: energyGoldMaterial },
        
        // Swirling energy
        { x: 0.08, y: 0, z: 0, material: energyGoldMaterial },
        { x: 0, y: 0.08, z: 0, material: stormCoreMaterial },
        { x: -0.08, y: 0, z: 0, material: energyGoldMaterial },
        { x: 0, y: -0.08, z: 0, material: stormCoreMaterial }
    ];

    // Energy wisps connecting elements
    const energyWispVoxels = [
        // Connecting wisps between elements
        { x: 0.15, y: 0.15, z: 0, material: energyGoldMaterial },
        { x: -0.15, y: 0.15, z: 0, material: energyGoldMaterial },
        { x: -0.15, y: -0.15, z: 0, material: energyGoldMaterial },
        { x: 0.15, y: -0.15, z: 0, material: energyGoldMaterial },
        
        // Floating energy particles
        { x: 0.3, y: 0, z: 0, material: stormCoreMaterial },
        { x: -0.3, y: 0, z: 0, material: stormCoreMaterial },
        { x: 0, y: 0.3, z: 0, material: energyGoldMaterial },
        { x: 0, y: -0.3, z: 0, material: energyGoldMaterial },
        
        // Additional energy streams
        { x: 0.2, y: 0.1, z: 0, material: energyGoldMaterial },
        { x: -0.2, y: 0.1, z: 0, material: energyGoldMaterial },
        { x: 0.1, y: -0.2, z: 0, material: stormCoreMaterial },
        { x: -0.1, y: -0.2, z: 0, material: stormCoreMaterial }
    ];

    // Create all voxels and organize into groups
    const fireElementGroup = new THREE.Group();
    const iceElementGroup = new THREE.Group();
    const lightningElementGroup = new THREE.Group();
    const stormCenterGroup = new THREE.Group();
    const energyWispGroup = new THREE.Group();

    fireElementGroup.name = 'fireElement';
    iceElementGroup.name = 'iceElement';
    lightningElementGroup.name = 'lightningElement';
    stormCenterGroup.name = 'stormCenter';
    energyWispGroup.name = 'energyWisps';

    // Add fire element voxels
    fireElementVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        fireElementGroup.add(mesh);
    });

    // Add ice element voxels
    iceElementVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            -0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        iceElementGroup.add(mesh);
    });

    // Add lightning element voxels
    lightningElementVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        lightningElementGroup.add(mesh);
    });

    // Add storm center voxels
    stormCenterVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.02
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        stormCenterGroup.add(mesh);
    });

    // Add energy wisp voxels
    energyWispVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.03
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        mesh.userData.wispPhase = index * 0.3; // Stagger animation
        energyWispGroup.add(mesh);
    });

    // Add all groups to card
    cardGroup.add(fireElementGroup);
    cardGroup.add(iceElementGroup);
    cardGroup.add(lightningElementGroup);
    cardGroup.add(stormCenterGroup);
    cardGroup.add(energyWispGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        elementalRotation: 0,
        stormIntensity: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update elemental convergence card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateElementalConvergenceCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.elementalRotation += deltaTime * 1.5; // Element rotation speed
    cardGroup.userData.stormIntensity += deltaTime * 2.5; // Storm intensity speed

    const time = cardGroup.userData.animationTime;
    const elementalRotation = cardGroup.userData.elementalRotation;
    const stormIntensity = cardGroup.userData.stormIntensity;

    // Animate fire element (clockwise spiral)
    const fireElementGroup = cardGroup.getObjectByName('fireElement');
    if (fireElementGroup) {
        fireElementGroup.rotation.z = elementalRotation;
        
        // Fire flickering effect
        const firePulse = 0.7 + Math.sin(stormIntensity * 3) * 0.3;
        fireElementGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * firePulse;
            }
        });
    }

    // Animate ice element (counter-clockwise)
    const iceElementGroup = cardGroup.getObjectByName('iceElement');
    if (iceElementGroup) {
        iceElementGroup.rotation.z = -elementalRotation * 1.2; // Faster counter-rotation
        
        // Ice crystalline pulsing
        const icePulse = 0.8 + Math.sin(stormIntensity * 2) * 0.2;
        iceElementGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * icePulse;
            }
        });
    }

    // Animate lightning element (erratic rotation)
    const lightningElementGroup = cardGroup.getObjectByName('lightningElement');
    if (lightningElementGroup) {
        const lightningRotation = elementalRotation * 0.8 + Math.sin(stormIntensity * 4) * 0.1;
        lightningElementGroup.rotation.z = lightningRotation;
        
        // Lightning crackling effect
        const lightningFlicker = Math.sin(stormIntensity * 6) > 0.3 ? 1.0 : 0.4;
        lightningElementGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * lightningFlicker;
            }
        });
    }

    // Animate storm center (pulsing convergence)
    const stormCenterGroup = cardGroup.getObjectByName('stormCenter');
    if (stormCenterGroup) {
        const stormPulse = 0.6 + Math.sin(stormIntensity * 1.8) * 0.4;
        stormCenterGroup.scale.setScalar(stormPulse);
        
        // Storm energy pulsing
        stormCenterGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * stormPulse;
            }
        });
    }

    // Animate energy wisps (connecting elements)
    const energyWispGroup = cardGroup.getObjectByName('energyWisps');
    if (energyWispGroup) {
        energyWispGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.wispPhase !== undefined) {
                const wispTime = stormIntensity + mesh.userData.wispPhase;
                
                // Flowing motion toward center
                const flowRadius = 0.02;
                const flowX = Math.cos(wispTime * 2) * flowRadius;
                const flowY = Math.sin(wispTime * 2) * flowRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + flowX;
                mesh.position.y = mesh.userData.originalPosition.y + flowY;
                
                // Pulsing energy connection
                const wispPulse = 0.5 + Math.sin(wispTime * 3) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * wispPulse;
                }
                
                // Fade in/out for mystical effect
                const fadeCycle = 0.6 + Math.sin(wispTime * 1.5) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fadeCycle;
                }
            }
        });
    }

    // Gentle overall convergence pulsing
    const convergencePulse = 1 + Math.sin(time * 1.2) * 0.06;
    cardGroup.scale.setScalar(0.8 * convergencePulse);
}

// Export the elemental convergence card data for the loot system
export const ELEMENTAL_CONVERGENCE_CARD_DATA = {
    name: 'Elemental Convergence',
    description: 'Unleashes a devastating elemental storm that combines fire, ice, and lightning in a massive area of effect attack, dealing massive damage to all enemies in a large radius.',
    category: 'card',
    rarity: 'legendary',
    effect: 'elemental_storm',
    effectValue: 50, // Base damage per element
    createFunction: createElementalConvergenceCard,
    updateFunction: updateElementalConvergenceCardAnimation,
    voxelModel: 'elemental_convergence_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.5
    }
};

export default createElementalConvergenceCard;