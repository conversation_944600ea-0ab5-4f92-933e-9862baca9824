import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Wild Magic Card Prefab
 * Triggers unpredictable magical effects when played
 */

// Wild Magic specific colors
const WILD_MAGIC_COLORS = {
    CHAOS_RED: 0xFF4500,            // Chaotic energy
    WILD_PURPLE: 0x9932CC,          // Wild magic
    UNPREDICTABLE_BLUE: 0x1E90FF,   // Unpredictable forces
    RANDOM_GREEN: 0x32CD32,         // Random energy
    ARCANE_GOLD: 0xFFD700,          // Arcane power
    MYSTIC_PINK: 0xFF69B4,          // Mystic energy
    PRIMAL_ORANGE: 0xFF8C00,        // Primal forces
    SHIFTING_CYAN: 0x00FFFF         // Shifting magic
};

/**
 * Create a wild magic card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The wild magic card 3D model
 */
export function createWildMagicCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'WildMagicCard';

    // Wild Magic materials
    const chaosRedMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.CHAOS_RED,
        emissive: WILD_MAGIC_COLORS.CHAOS_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const wildPurpleMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.WILD_PURPLE,
        emissive: WILD_MAGIC_COLORS.WILD_PURPLE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const unpredictableBlueMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.UNPREDICTABLE_BLUE,
        emissive: WILD_MAGIC_COLORS.UNPREDICTABLE_BLUE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const randomGreenMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.RANDOM_GREEN,
        emissive: WILD_MAGIC_COLORS.RANDOM_GREEN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const arcaneGoldMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.ARCANE_GOLD,
        emissive: WILD_MAGIC_COLORS.ARCANE_GOLD,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const mysticPinkMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.MYSTIC_PINK,
        emissive: WILD_MAGIC_COLORS.MYSTIC_PINK,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const primalOrangeMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.PRIMAL_ORANGE,
        emissive: WILD_MAGIC_COLORS.PRIMAL_ORANGE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const shiftingCyanMaterial = new THREE.MeshLambertMaterial({
        color: WILD_MAGIC_COLORS.SHIFTING_CYAN,
        emissive: WILD_MAGIC_COLORS.SHIFTING_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Chaos Core (central unpredictable element)
    const chaosCoreVoxels = [
        // Central chaos sphere
        { x: 0.0, y: 0.0, z: 0.0, material: arcaneGoldMaterial }, // Core center
        { x: -0.02, y: 0.0, z: 0.0, material: chaosRedMaterial }, // Core left
        { x: 0.02, y: 0.0, z: 0.0, material: wildPurpleMaterial }, // Core right
        { x: 0.0, y: -0.02, z: 0.0, material: unpredictableBlueMaterial }, // Core bottom
        { x: 0.0, y: 0.02, z: 0.0, material: randomGreenMaterial }, // Core top
        { x: 0.0, y: 0.0, z: -0.02, material: mysticPinkMaterial }, // Core back
        { x: 0.0, y: 0.0, z: 0.02, material: primalOrangeMaterial }, // Core front
        
        // Inner chaos ring
        { x: -0.04, y: 0.0, z: 0.0, material: shiftingCyanMaterial }, // Ring left
        { x: 0.04, y: 0.0, z: 0.0, material: chaosRedMaterial }, // Ring right
        { x: 0.0, y: -0.04, z: 0.0, material: wildPurpleMaterial }, // Ring bottom
        { x: 0.0, y: 0.04, z: 0.0, material: unpredictableBlueMaterial }, // Ring top
        { x: -0.03, y: -0.03, z: 0.0, material: randomGreenMaterial }, // Ring bottom-left
        { x: 0.03, y: -0.03, z: 0.0, material: mysticPinkMaterial }, // Ring bottom-right
        { x: -0.03, y: 0.03, z: 0.0, material: primalOrangeMaterial }, // Ring top-left
        { x: 0.03, y: 0.03, z: 0.0, material: arcaneGoldMaterial }, // Ring top-right
        
        // Outer chaos ring
        { x: -0.06, y: 0.0, z: 0.0, material: wildPurpleMaterial }, // Outer left
        { x: 0.06, y: 0.0, z: 0.0, material: unpredictableBlueMaterial }, // Outer right
        { x: 0.0, y: -0.06, z: 0.0, material: randomGreenMaterial }, // Outer bottom
        { x: 0.0, y: 0.06, z: 0.0, material: chaosRedMaterial }, // Outer top
        { x: -0.04, y: -0.04, z: 0.0, material: mysticPinkMaterial }, // Outer bottom-left
        { x: 0.04, y: -0.04, z: 0.0, material: primalOrangeMaterial }, // Outer bottom-right
        { x: -0.04, y: 0.04, z: 0.0, material: shiftingCyanMaterial }, // Outer top-left
        { x: 0.04, y: 0.04, z: 0.0, material: arcaneGoldMaterial } // Outer top-right
    ];

    // Wild Energy Tendrils (chaotic magic streams)
    const wildEnergyVoxels = [
        // First tendril (chaotic red)
        { x: -0.08, y: 0.02, z: 0.02, material: chaosRedMaterial },
        { x: -0.10, y: 0.04, z: 0.04, material: chaosRedMaterial },
        { x: -0.12, y: 0.06, z: 0.02, material: chaosRedMaterial },
        { x: -0.14, y: 0.08, z: 0.0, material: chaosRedMaterial },
        { x: -0.16, y: 0.10, z: -0.02, material: chaosRedMaterial },
        
        // Second tendril (wild purple)
        { x: 0.08, y: -0.02, z: 0.02, material: wildPurpleMaterial },
        { x: 0.10, y: -0.04, z: 0.04, material: wildPurpleMaterial },
        { x: 0.12, y: -0.06, z: 0.02, material: wildPurpleMaterial },
        { x: 0.14, y: -0.08, z: 0.0, material: wildPurpleMaterial },
        { x: 0.16, y: -0.10, z: -0.02, material: wildPurpleMaterial },
        
        // Third tendril (unpredictable blue)
        { x: 0.02, y: 0.08, z: 0.02, material: unpredictableBlueMaterial },
        { x: 0.04, y: 0.10, z: 0.04, material: unpredictableBlueMaterial },
        { x: 0.06, y: 0.12, z: 0.02, material: unpredictableBlueMaterial },
        { x: 0.08, y: 0.14, z: 0.0, material: unpredictableBlueMaterial },
        { x: 0.10, y: 0.16, z: -0.02, material: unpredictableBlueMaterial },
        
        // Fourth tendril (random green)
        { x: -0.02, y: -0.08, z: 0.02, material: randomGreenMaterial },
        { x: -0.04, y: -0.10, z: 0.04, material: randomGreenMaterial },
        { x: -0.06, y: -0.12, z: 0.02, material: randomGreenMaterial },
        { x: -0.08, y: -0.14, z: 0.0, material: randomGreenMaterial },
        { x: -0.10, y: -0.16, z: -0.02, material: randomGreenMaterial },
        
        // Fifth tendril (arcane gold)
        { x: 0.0, y: 0.02, z: 0.08, material: arcaneGoldMaterial },
        { x: 0.02, y: 0.04, z: 0.10, material: arcaneGoldMaterial },
        { x: 0.0, y: 0.06, z: 0.12, material: arcaneGoldMaterial },
        { x: -0.02, y: 0.08, z: 0.14, material: arcaneGoldMaterial },
        { x: 0.0, y: 0.10, z: 0.16, material: arcaneGoldMaterial },
        
        // Sixth tendril (mystic pink)
        { x: 0.0, y: -0.02, z: -0.08, material: mysticPinkMaterial },
        { x: -0.02, y: -0.04, z: -0.10, material: mysticPinkMaterial },
        { x: 0.0, y: -0.06, z: -0.12, material: mysticPinkMaterial },
        { x: 0.02, y: -0.08, z: -0.14, material: mysticPinkMaterial },
        { x: 0.0, y: -0.10, z: -0.16, material: mysticPinkMaterial }
    ];

    // Magic Sparks (random magic bursts)
    const magicSparksVoxels = [
        // Inner spark ring
        { x: -0.09, y: 0.05, z: 0.03, material: primalOrangeMaterial },
        { x: 0.09, y: -0.05, z: 0.03, material: shiftingCyanMaterial },
        { x: 0.05, y: 0.09, z: 0.03, material: chaosRedMaterial },
        { x: -0.05, y: -0.09, z: 0.03, material: wildPurpleMaterial },
        { x: -0.07, y: 0.07, z: 0.05, material: unpredictableBlueMaterial },
        { x: 0.07, y: 0.07, z: 0.05, material: randomGreenMaterial },
        { x: -0.07, y: -0.07, z: 0.05, material: arcaneGoldMaterial },
        { x: 0.07, y: -0.07, z: 0.05, material: mysticPinkMaterial },
        
        // Middle spark ring
        { x: -0.13, y: 0.07, z: 0.01, material: shiftingCyanMaterial },
        { x: 0.13, y: -0.07, z: 0.01, material: primalOrangeMaterial },
        { x: 0.07, y: 0.13, z: 0.01, material: wildPurpleMaterial },
        { x: -0.07, y: -0.13, z: 0.01, material: chaosRedMaterial },
        { x: -0.09, y: 0.09, z: 0.03, material: randomGreenMaterial },
        { x: 0.09, y: 0.09, z: 0.03, material: unpredictableBlueMaterial },
        { x: -0.09, y: -0.09, z: 0.03, material: mysticPinkMaterial },
        { x: 0.09, y: -0.09, z: 0.03, material: arcaneGoldMaterial },
        
        // Outer spark ring
        { x: -0.17, y: 0.09, z: -0.01, material: arcaneGoldMaterial },
        { x: 0.17, y: -0.09, z: -0.01, material: mysticPinkMaterial },
        { x: 0.09, y: 0.17, z: -0.01, material: chaosRedMaterial },
        { x: -0.09, y: -0.17, z: -0.01, material: wildPurpleMaterial },
        { x: -0.12, y: 0.12, z: 0.01, material: primalOrangeMaterial },
        { x: 0.12, y: 0.12, z: 0.01, material: shiftingCyanMaterial },
        { x: -0.12, y: -0.12, z: 0.01, material: unpredictableBlueMaterial },
        { x: 0.12, y: -0.12, z: 0.01, material: randomGreenMaterial }
    ];

    // Reality Distortion Field (wild magic aura)
    const realityDistortionVoxels = [
        // Inner distortion layer
        { x: -0.11, y: 0.03, z: 0.07, material: mysticPinkMaterial },
        { x: 0.11, y: -0.03, z: 0.07, material: primalOrangeMaterial },
        { x: 0.03, y: 0.11, z: 0.07, material: shiftingCyanMaterial },
        { x: -0.03, y: -0.11, z: 0.07, material: arcaneGoldMaterial },
        { x: -0.08, y: 0.08, z: 0.09, material: chaosRedMaterial },
        { x: 0.08, y: 0.08, z: 0.09, material: wildPurpleMaterial },
        { x: -0.08, y: -0.08, z: 0.09, material: unpredictableBlueMaterial },
        { x: 0.08, y: -0.08, z: 0.09, material: randomGreenMaterial },
        
        // Middle distortion layer
        { x: -0.15, y: 0.05, z: 0.05, material: randomGreenMaterial },
        { x: 0.15, y: -0.05, z: 0.05, material: unpredictableBlueMaterial },
        { x: 0.05, y: 0.15, z: 0.05, material: wildPurpleMaterial },
        { x: -0.05, y: -0.15, z: 0.05, material: chaosRedMaterial },
        { x: -0.11, y: 0.11, z: 0.07, material: arcaneGoldMaterial },
        { x: 0.11, y: 0.11, z: 0.07, material: mysticPinkMaterial },
        { x: -0.11, y: -0.11, z: 0.07, material: primalOrangeMaterial },
        { x: 0.11, y: -0.11, z: 0.07, material: shiftingCyanMaterial },
        
        // Outer distortion layer
        { x: -0.19, y: 0.07, z: 0.03, material: shiftingCyanMaterial },
        { x: 0.19, y: -0.07, z: 0.03, material: primalOrangeMaterial },
        { x: 0.07, y: 0.19, z: 0.03, material: mysticPinkMaterial },
        { x: -0.07, y: -0.19, z: 0.03, material: arcaneGoldMaterial },
        { x: -0.13, y: 0.13, z: 0.05, material: chaosRedMaterial },
        { x: 0.13, y: 0.13, z: 0.05, material: wildPurpleMaterial },
        { x: -0.13, y: -0.13, z: 0.05, material: unpredictableBlueMaterial },
        { x: 0.13, y: -0.13, z: 0.05, material: randomGreenMaterial },
        
        // Far distortion layer
        { x: -0.23, y: 0.09, z: 0.01, material: unpredictableBlueMaterial },
        { x: 0.23, y: -0.09, z: 0.01, material: randomGreenMaterial },
        { x: 0.09, y: 0.23, z: 0.01, material: arcaneGoldMaterial },
        { x: -0.09, y: -0.23, z: 0.01, material: mysticPinkMaterial },
        { x: -0.16, y: 0.16, z: 0.03, material: primalOrangeMaterial },
        { x: 0.16, y: 0.16, z: 0.03, material: shiftingCyanMaterial },
        { x: -0.16, y: -0.16, z: 0.03, material: chaosRedMaterial },
        { x: 0.16, y: -0.16, z: 0.03, material: wildPurpleMaterial }
    ];

    // Create chaos core group
    const chaosCoreGroup = new THREE.Group();
    chaosCoreGroup.name = 'chaosCoreGroup';

    // Add chaos core voxels
    chaosCoreVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.chaosPhase = index * 0.08; // Stagger animation
        chaosCoreGroup.add(mesh);
    });

    // Create wild energy group
    const wildEnergyGroup = new THREE.Group();
    wildEnergyGroup.name = 'wildEnergyGroup';

    // Add wild energy voxels
    wildEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.tendrilPhase = index * 0.06; // Stagger animation
        wildEnergyGroup.add(mesh);
    });

    // Create magic sparks group
    const magicSparksGroup = new THREE.Group();
    magicSparksGroup.name = 'magicSparksGroup';

    // Add magic sparks voxels
    magicSparksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sparkPhase = index * 0.05; // Stagger animation
        magicSparksGroup.add(mesh);
    });

    // Create reality distortion group
    const realityDistortionGroup = new THREE.Group();
    realityDistortionGroup.name = 'realityDistortionGroup';

    // Add reality distortion voxels
    realityDistortionVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.distortionPhase = index * 0.07; // Stagger animation
        realityDistortionGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(chaosCoreGroup);
    cardGroup.add(wildEnergyGroup);
    cardGroup.add(magicSparksGroup);
    cardGroup.add(realityDistortionGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        chaosEnergy: 0,
        wildSurge: 0,
        magicFlux: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update wild magic card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateWildMagicCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.chaosEnergy += deltaTime * 4.0; // Chaos energy speed
    cardGroup.userData.wildSurge += deltaTime * 3.5; // Wild surge speed
    cardGroup.userData.magicFlux += deltaTime * 5.0; // Magic flux speed

    const time = cardGroup.userData.animationTime;
    const chaosEnergy = cardGroup.userData.chaosEnergy;
    const wildSurge = cardGroup.userData.wildSurge;
    const magicFlux = cardGroup.userData.magicFlux;

    // Animate chaos core (central unpredictable element)
    const chaosCoreGroup = cardGroup.getObjectByName('chaosCoreGroup');
    if (chaosCoreGroup) {
        // Core chaotic rotation
        const chaosRotation = Math.sin(chaosEnergy * 2.5) * 0.1;
        chaosCoreGroup.rotation.x = chaosRotation;
        chaosCoreGroup.rotation.y = chaosEnergy * 0.4;
        chaosCoreGroup.rotation.z = Math.cos(chaosEnergy * 3.0) * 0.08;
        
        // Individual chaos element animation
        chaosCoreGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.chaosPhase !== undefined) {
                const chaosTime = chaosEnergy + mesh.userData.chaosPhase;
                
                // Chaotic motion
                const chaosShift = Math.sin(chaosTime * 8.0) * 0.002;
                const chaosPulse = Math.cos(chaosTime * 7.0) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + chaosShift;
                mesh.position.y = mesh.userData.originalPosition.y + chaosPulse;
                
                // Chaos intensity
                const chaosIntensity = 1.0 + Math.sin(chaosTime * 9.0) * 1.2;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * chaosIntensity;
                }
                
                // Chaos scale variation
                const chaosScale = 0.8 + Math.sin(chaosTime * 6.0) * 0.4;
                mesh.scale.setScalar(chaosScale);
            }
        });
    }

    // Animate wild energy tendrils (chaotic magic streams)
    const wildEnergyGroup = cardGroup.getObjectByName('wildEnergyGroup');
    if (wildEnergyGroup) {
        // Tendril wild motion
        const tendrilWild = Math.sin(wildSurge * 1.8) * 0.06;
        wildEnergyGroup.rotation.y = tendrilWild;
        wildEnergyGroup.rotation.z = Math.cos(wildSurge * 2.2) * 0.04;
        
        wildEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.tendrilPhase !== undefined) {
                const tendrilTime = wildSurge + mesh.userData.tendrilPhase;
                
                // Wild tendril motion
                const tendrilFlow = Math.sin(tendrilTime * 6.0) * 0.008;
                const tendrilLash = Math.cos(tendrilTime * 5.0) * 0.007;
                
                mesh.position.x = mesh.userData.originalPosition.x + tendrilFlow;
                mesh.position.y = mesh.userData.originalPosition.y + tendrilLash;
                
                // Tendril energy glow
                const tendrilGlow = 1.0 + Math.sin(tendrilTime * 8.0) * 1.0;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * tendrilGlow;
                }
                
                // Tendril opacity variation
                const tendrilOpacity = 0.7 + Math.sin(tendrilTime * 7.0) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * tendrilOpacity;
                }
            }
        });
    }

    // Animate magic sparks (random magic bursts)
    const magicSparksGroup = cardGroup.getObjectByName('magicSparksGroup');
    if (magicSparksGroup) {
        magicSparksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sparkPhase !== undefined) {
                const sparkTime = magicFlux + mesh.userData.sparkPhase;
                
                // Magic spark bursts
                const sparkBurst = Math.sin(sparkTime * 12.0) * 0.006;
                const sparkPop = Math.cos(sparkTime * 10.0) * 0.005;
                
                mesh.position.x = mesh.userData.originalPosition.x + sparkBurst;
                mesh.position.y = mesh.userData.originalPosition.y + sparkPop;
                
                // Spark intensity
                const sparkIntensity = 1.0 + Math.sin(sparkTime * 15.0) * 1.5;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * sparkIntensity;
                }
                
                // Spark scale variation (magic bursts)
                const sparkScale = 0.5 + Math.sin(sparkTime * 11.0) * 0.8;
                mesh.scale.setScalar(sparkScale);
                
                // Spark opacity (random manifestation)
                const sparkOpacity = 0.5 + Math.sin(sparkTime * 13.0) * 0.5;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * sparkOpacity;
                }
            }
        });
    }

    // Animate reality distortion field (wild magic aura)
    const realityDistortionGroup = cardGroup.getObjectByName('realityDistortionGroup');
    if (realityDistortionGroup) {
        realityDistortionGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.distortionPhase !== undefined) {
                const distortionTime = chaosEnergy + mesh.userData.distortionPhase;
                
                // Reality distortion ripples
                const distortionRipple = Math.sin(distortionTime * 4.0) * 0.010;
                const realityWarp = Math.cos(distortionTime * 3.5) * 0.008;
                
                mesh.position.x = mesh.userData.originalPosition.x + distortionRipple;
                mesh.position.y = mesh.userData.originalPosition.y + realityWarp;
                
                // Distortion field intensity
                const distortionIntensity = 1.0 + Math.sin(distortionTime * 6.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * distortionIntensity;
                }
                
                // Distortion scale variation (reality warping)
                const distortionScale = 0.9 + Math.sin(distortionTime * 5.0) * 0.3;
                mesh.scale.setScalar(distortionScale);
                
                // Distortion opacity (reality flux)
                const distortionOpacity = 0.8 + Math.sin(distortionTime * 7.0) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * distortionOpacity;
                }
            }
        });
    }

    // Overall wild magic presence (epic chaotic magic)
    const wildPulse = 1 + Math.sin(time * 3.2) * 0.15;
    const chaosShift = Math.cos(time * 4.0) * 0.002;
    cardGroup.scale.setScalar(0.8 * wildPulse);
    cardGroup.position.x += chaosShift;
    cardGroup.position.z += chaosShift * 1.2;
}

// Export the wild magic card data for the loot system
export const WILD_MAGIC_CARD_DATA = {
    name: "Wild Magic",
    description: 'Triggers a random magical effect from a pool of unpredictable spells with varying power levels.',
    category: 'card',
    rarity: 'epic',
    effect: 'wild_magic',
    effectValue: 1, // Number of effects to trigger
    createFunction: createWildMagicCard,
    updateFunction: updateWildMagicCardAnimation,
    voxelModel: 'wild_magic_card',
    glow: {
        color: 0xFF4500,
        intensity: 1.8
    }
};

export default createWildMagicCard;