import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Earthquake Card Prefab
 * Causes ground to shake violently, damaging all enemies and destroying destructible walls
 */

// Earthquake specific colors
const EARTHQUAKE_COLORS = {
    EARTH_BROWN: 0x8B4513,        // Main earth/dirt color
    STONE_GRAY: 0x696969,         // Rock and stone
    DUST_TAN: 0xD2B48C,          // Dust clouds
    CRACK_BLACK: 0x2F2F2F,       // Cracks and fissures
    RUBBLE_BROWN: 0xA0522D,      // Falling rubble
    BEDROCK_DARK: 0x36454F,      // Deep earth
    QUAKE_ORANGE: 0xFF6347,      // Seismic energy
    TREMOR_RED: 0xDC143C         // Intense tremor energy
};

/**
 * Create an earthquake card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The earthquake card 3D model
 */
export function createEarthquakeCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'EarthquakeCard';

    // Earthquake materials
    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.EARTH_BROWN,
        emissive: EARTHQUAKE_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 1.0
    });

    const stoneGrayMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.STONE_GRAY,
        emissive: EARTHQUAKE_COLORS.STONE_GRAY,
        emissiveIntensity: 0.15,
        transparent: true,
        opacity: 0.95
    });

    const dustTanMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.DUST_TAN,
        emissive: EARTHQUAKE_COLORS.DUST_TAN,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const crackBlackMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.CRACK_BLACK,
        emissive: EARTHQUAKE_COLORS.CRACK_BLACK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const rubbleBrownMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.RUBBLE_BROWN,
        emissive: EARTHQUAKE_COLORS.RUBBLE_BROWN,
        emissiveIntensity: 0.25,
        transparent: true,
        opacity: 0.9
    });

    const bedrockDarkMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.BEDROCK_DARK,
        emissive: EARTHQUAKE_COLORS.BEDROCK_DARK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 1.0
    });

    const quakeOrangeMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.QUAKE_ORANGE,
        emissive: EARTHQUAKE_COLORS.QUAKE_ORANGE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const tremorRedMaterial = new THREE.MeshLambertMaterial({
        color: EARTHQUAKE_COLORS.TREMOR_RED,
        emissive: EARTHQUAKE_COLORS.TREMOR_RED,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Ground layer (stable earth)
    const groundLayerVoxels = [
        // Base ground layer
        { x: -0.20, y: -0.16, z: 0.0, material: earthBrownMaterial },
        { x: -0.12, y: -0.16, z: 0.0, material: stoneGrayMaterial },
        { x: -0.04, y: -0.16, z: 0.0, material: earthBrownMaterial },
        { x: 0.04, y: -0.16, z: 0.0, material: bedrockDarkMaterial },
        { x: 0.12, y: -0.16, z: 0.0, material: stoneGrayMaterial },
        { x: 0.20, y: -0.16, z: 0.0, material: earthBrownMaterial },
        
        // Second layer
        { x: -0.16, y: -0.12, z: 0.0, material: stoneGrayMaterial },
        { x: -0.08, y: -0.12, z: 0.0, material: earthBrownMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: bedrockDarkMaterial },
        { x: 0.08, y: -0.12, z: 0.0, material: earthBrownMaterial },
        { x: 0.16, y: -0.12, z: 0.0, material: stoneGrayMaterial },
        
        // Third layer
        { x: -0.12, y: -0.08, z: 0.0, material: earthBrownMaterial },
        { x: -0.04, y: -0.08, z: 0.0, material: rubbleBrownMaterial },
        { x: 0.04, y: -0.08, z: 0.0, material: rubbleBrownMaterial },
        { x: 0.12, y: -0.08, z: 0.0, material: earthBrownMaterial },
        
        // Surface layer
        { x: -0.08, y: -0.04, z: 0.0, material: stoneGrayMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: earthBrownMaterial },
        { x: 0.08, y: -0.04, z: 0.0, material: stoneGrayMaterial }
    ];

    // Earthquake cracks (fissures in the ground)
    const earthquakeCracksVoxels = [
        // Main crack running through center
        { x: -0.16, y: 0.0, z: 0.0, material: crackBlackMaterial },
        { x: -0.08, y: 0.0, z: 0.0, material: crackBlackMaterial },
        { x: 0.0, y: 0.0, z: 0.0, material: crackBlackMaterial },
        { x: 0.08, y: 0.0, z: 0.0, material: crackBlackMaterial },
        { x: 0.16, y: 0.0, z: 0.0, material: crackBlackMaterial },
        
        // Branching cracks
        { x: -0.12, y: 0.04, z: -0.04, material: crackBlackMaterial },
        { x: -0.04, y: 0.04, z: 0.04, material: crackBlackMaterial },
        { x: 0.04, y: 0.04, z: -0.04, material: crackBlackMaterial },
        { x: 0.12, y: 0.04, z: 0.04, material: crackBlackMaterial },
        
        // Secondary cracks
        { x: -0.20, y: 0.08, z: 0.0, material: crackBlackMaterial },
        { x: -0.06, y: 0.08, z: 0.08, material: crackBlackMaterial },
        { x: 0.06, y: 0.08, z: -0.08, material: crackBlackMaterial },
        { x: 0.20, y: 0.08, z: 0.0, material: crackBlackMaterial }
    ];

    // Falling rubble (debris from destruction)
    const fallingRubbleVoxels = [
        // Large rubble pieces
        { x: -0.18, y: 0.12, z: 0.06, material: rubbleBrownMaterial },
        { x: -0.10, y: 0.16, z: -0.08, material: stoneGrayMaterial },
        { x: -0.02, y: 0.20, z: 0.04, material: rubbleBrownMaterial },
        { x: 0.06, y: 0.18, z: -0.06, material: stoneGrayMaterial },
        { x: 0.14, y: 0.14, z: 0.08, material: rubbleBrownMaterial },
        { x: 0.22, y: 0.10, z: -0.04, material: stoneGrayMaterial },
        
        // Medium rubble pieces
        { x: -0.24, y: 0.24, z: 0.0, material: earthBrownMaterial },
        { x: -0.16, y: 0.28, z: -0.12, material: rubbleBrownMaterial },
        { x: -0.08, y: 0.32, z: 0.10, material: stoneGrayMaterial },
        { x: 0.0, y: 0.36, z: -0.06, material: rubbleBrownMaterial },
        { x: 0.08, y: 0.34, z: 0.12, material: stoneGrayMaterial },
        { x: 0.16, y: 0.30, z: -0.10, material: earthBrownMaterial },
        { x: 0.24, y: 0.26, z: 0.08, material: rubbleBrownMaterial },
        
        // Small rubble pieces
        { x: -0.28, y: 0.38, z: 0.04, material: dustTanMaterial },
        { x: -0.20, y: 0.42, z: -0.16, material: dustTanMaterial },
        { x: -0.12, y: 0.46, z: 0.14, material: dustTanMaterial },
        { x: -0.04, y: 0.50, z: -0.10, material: dustTanMaterial },
        { x: 0.04, y: 0.48, z: 0.16, material: dustTanMaterial },
        { x: 0.12, y: 0.44, z: -0.14, material: dustTanMaterial },
        { x: 0.20, y: 0.40, z: 0.12, material: dustTanMaterial },
        { x: 0.28, y: 0.36, z: -0.08, material: dustTanMaterial }
    ];

    // Seismic energy (earthquake power visualization)
    const seismicEnergyVoxels = [
        // Energy waves emanating from center
        { x: -0.14, y: 0.06, z: -0.10, material: quakeOrangeMaterial },
        { x: 0.14, y: 0.06, z: 0.10, material: quakeOrangeMaterial },
        { x: -0.10, y: 0.10, z: -0.14, material: tremorRedMaterial },
        { x: 0.10, y: 0.10, z: 0.14, material: tremorRedMaterial },
        { x: -0.18, y: 0.14, z: 0.0, material: quakeOrangeMaterial },
        { x: 0.18, y: 0.14, z: 0.0, material: quakeOrangeMaterial },
        
        // Secondary energy waves
        { x: -0.22, y: 0.18, z: -0.14, material: tremorRedMaterial },
        { x: 0.22, y: 0.18, z: 0.14, material: tremorRedMaterial },
        { x: -0.14, y: 0.22, z: -0.22, material: quakeOrangeMaterial },
        { x: 0.14, y: 0.22, z: 0.22, material: quakeOrangeMaterial },
        { x: -0.26, y: 0.26, z: 0.04, material: tremorRedMaterial },
        { x: 0.26, y: 0.26, z: -0.04, material: tremorRedMaterial },
        
        // Outer energy waves
        { x: -0.30, y: 0.30, z: -0.18, material: quakeOrangeMaterial },
        { x: 0.30, y: 0.30, z: 0.18, material: quakeOrangeMaterial },
        { x: -0.18, y: 0.34, z: -0.30, material: tremorRedMaterial },
        { x: 0.18, y: 0.34, z: 0.30, material: tremorRedMaterial },
        { x: -0.34, y: 0.38, z: 0.08, material: quakeOrangeMaterial },
        { x: 0.34, y: 0.38, z: -0.08, material: quakeOrangeMaterial }
    ];

    // Create ground layer group
    const groundLayerGroup = new THREE.Group();
    groundLayerGroup.name = 'groundLayer';

    // Add ground layer voxels
    groundLayerVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        groundLayerGroup.add(mesh);
    });

    // Create earthquake cracks group
    const earthquakeCracksGroup = new THREE.Group();
    earthquakeCracksGroup.name = 'earthquakeCracks';

    // Add earthquake crack voxels
    earthquakeCracksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.crackPhase = index * 0.2; // Stagger animation
        earthquakeCracksGroup.add(mesh);
    });

    // Create falling rubble group
    const fallingRubbleGroup = new THREE.Group();
    fallingRubbleGroup.name = 'fallingRubble';

    // Add falling rubble voxels
    fallingRubbleVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.rubblePhase = index * 0.15; // Stagger animation
        fallingRubbleGroup.add(mesh);
    });

    // Create seismic energy group
    const seismicEnergyGroup = new THREE.Group();
    seismicEnergyGroup.name = 'seismicEnergy';

    // Add seismic energy voxels
    seismicEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.1; // Stagger animation
        seismicEnergyGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(groundLayerGroup);
    cardGroup.add(earthquakeCracksGroup);
    cardGroup.add(fallingRubbleGroup);
    cardGroup.add(seismicEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        earthquakeShake: 0,
        crackSpread: 0,
        rubbleFall: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update earthquake card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateEarthquakeCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.earthquakeShake += deltaTime * 6.0; // Earthquake shake speed
    cardGroup.userData.crackSpread += deltaTime * 3.0; // Crack spreading speed
    cardGroup.userData.rubbleFall += deltaTime * 4.0; // Rubble falling speed

    const time = cardGroup.userData.animationTime;
    const earthquakeShake = cardGroup.userData.earthquakeShake;
    const crackSpread = cardGroup.userData.crackSpread;
    const rubbleFall = cardGroup.userData.rubbleFall;

    // Animate ground layer (stable base with slight tremor)
    const groundLayerGroup = cardGroup.getObjectByName('groundLayer');
    if (groundLayerGroup) {
        // Ground tremor (subtle shaking)
        const tremor = Math.sin(earthquakeShake * 2.0) * 0.0005;
        groundLayerGroup.position.x = tremor;
        groundLayerGroup.position.y = tremor * 0.5;
        
        // Ground intensity variation
        const groundIntensity = 1.0 + Math.sin(earthquakeShake * 1.5) * 0.2;
        groundLayerGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * groundIntensity;
            }
        });
    }

    // Animate earthquake cracks (spreading and widening)
    const earthquakeCracksGroup = cardGroup.getObjectByName('earthquakeCracks');
    if (earthquakeCracksGroup) {
        earthquakeCracksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.crackPhase !== undefined) {
                const crackTime = crackSpread + mesh.userData.crackPhase;
                
                // Crack opening motion (spreading apart)
                const crackOpen = Math.sin(crackTime * 2.5) * 0.001;
                const crackDeepen = Math.cos(crackTime * 3.0) * 0.0008;
                
                mesh.position.x = mesh.userData.originalPosition.x + crackOpen;
                mesh.position.y = mesh.userData.originalPosition.y - Math.abs(crackDeepen);
                
                // Crack darkness fluctuation
                const crackDarkness = 1.0 + Math.sin(crackTime * 4.0) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crackDarkness;
                }
                
                // Crack scale variation (growing wider)
                const crackScale = 1.0 + Math.sin(crackTime * 3.5) * 0.15;
                mesh.scale.setScalar(crackScale);
            }
        });
    }

    // Animate falling rubble (tumbling debris)
    const fallingRubbleGroup = cardGroup.getObjectByName('fallingRubble');
    if (fallingRubbleGroup) {
        fallingRubbleGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.rubblePhase !== undefined) {
                const rubbleTime = rubbleFall + mesh.userData.rubblePhase;
                
                // Rubble falling motion
                const rubbleDrop = Math.sin(rubbleTime * 2.0) * 0.002;
                const rubbleTumble = Math.cos(rubbleTime * 4.0) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + rubbleTumble;
                mesh.position.y = mesh.userData.originalPosition.y + rubbleDrop;
                
                // Rubble rotation (tumbling)
                mesh.rotation.x = rubbleTime * 2.0;
                mesh.rotation.z = rubbleTime * 1.5;
                
                // Rubble dust effect
                const dustEffect = 1.0 + Math.sin(rubbleTime * 5.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * dustEffect;
                }
                
                // Rubble scale variation
                const rubbleScale = 0.9 + Math.sin(rubbleTime * 3.8) * 0.2;
                mesh.scale.setScalar(rubbleScale);
            }
        });
    }

    // Animate seismic energy (earthquake power waves)
    const seismicEnergyGroup = cardGroup.getObjectByName('seismicEnergy');
    if (seismicEnergyGroup) {
        seismicEnergyGroup.rotation.y = time * 1.5; // Energy wave rotation
        
        seismicEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = earthquakeShake + mesh.userData.energyPhase;
                
                // Energy wave propagation
                const wavePropagate = Math.sin(energyTime * 4.0) * 0.003;
                const wavePulse = Math.cos(energyTime * 5.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + wavePropagate;
                mesh.position.z = mesh.userData.originalPosition.z + wavePulse;
                
                // Energy intensity fluctuation
                const energyIntensity = 1.0 + Math.sin(energyTime * 6.0) * 0.6;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyIntensity;
                }
                
                // Energy scale pulsing
                const energyScale = 0.8 + Math.sin(energyTime * 4.5) * 0.4;
                mesh.scale.setScalar(energyScale);
            }
        });
    }

    // Overall earthquake shaking (violent tremor)
    const quakeShake = 1 + Math.sin(time * 8.0) * 0.03;
    const quakeTremor = Math.cos(time * 10.0) * 0.002;
    cardGroup.scale.setScalar(0.8 * quakeShake);
    cardGroup.position.x += quakeTremor;
    cardGroup.position.y += quakeTremor * 0.5;
}

// Export the earthquake card data for the loot system
export const EARTHQUAKE_CARD_DATA = {
    name: "Earthquake",
    description: 'Causes ground to shake violently, dealing massive damage to all enemies and destroying destructible walls with seismic force.',
    category: 'card',
    rarity: 'epic',
    effect: 'earthquake',
    effectValue: 100, // Base damage to all enemies
    createFunction: createEarthquakeCard,
    updateFunction: updateEarthquakeCardAnimation,
    voxelModel: 'earthquake_card',
    glow: {
        color: 0x8B4513,
        intensity: 1.4
    }
};

export default createEarthquakeCard;