/**
 * Shadow Minion Object
 * Dark silhouette creatures that crawl out of the ground
 * Spawned by Nairabos during boss battles
 */
import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    _getMaterialByHex_Cached,
    getOrCreateGeometry
} from './shared.js';

// Shadow minion color palette (dark, muted versions of Nairabos)
const SHADOW_DARK = '0A0A0A';    // Very dark shadow
const VOID_BLACK = '000000';     // Pure black void
const SHADOW_PURPLE = '2D1B69';  // Dark purple
const WISP_GRAY = '1C1C1C';     // Wispy gray
const BONE_DIM = '8B8682';       // Dim bone color

// Record voxel function for building
function recordVoxel(voxels, x, y, z, color) {
    voxels.push({ x, y, z, c: color });
}

export function shadowMinionObject() {
    console.log('[shadowMinionObject] Creating shadow minion...');
    
    // Create main group
    const shadowMinion = new THREE.Group();
    shadowMinion.name = 'shadowMinion';
    
    // Much larger scale for more imposing minions
    const minionVoxelSize = VOXEL_SIZE * 2.5; // Significantly larger than before
    
    // Store voxels for destruction system
    const originalVoxels = [];
    
    // === MAIN BODY (massive spider-like torso) ===
    const body = new THREE.Group();
    body.name = 'body';
    
    // Central thorax (6x4x8 - much larger and more detailed)
    for (let x = -3; x <= 2; x++) {
        for (let y = 0; y <= 3; y++) {
            for (let z = -4; z <= 3; z++) {
                // Create more detailed body structure
                const isEdge = (x === -3 || x === 2 || z === -4 || z === 3);
                const isSpine = (x === -1 || x === 0) && y >= 2;
                const color = isEdge ? VOID_BLACK : (isSpine ? SHADOW_PURPLE : SHADOW_DARK);
                recordVoxel(originalVoxels, x, y + 4, z, color);
                
                // Add spikes on the back
                if (y === 3 && Math.abs(x) <= 1 && z % 2 === 0) {
                    recordVoxel(originalVoxels, x, y + 5, z, WISP_GRAY);
                }
            }
        }
    }
    
    // Segmented abdomen (trailing behind - more detailed)
    for (let segment = 0; segment < 3; segment++) {
        const segmentZ = 4 + segment * 2;
        const segmentSize = 2 - segment; // Taper the abdomen
        
        for (let x = -segmentSize; x <= segmentSize - 1; x++) {
            for (let y = 0; y <= 1; y++) {
                for (let z = segmentZ; z <= segmentZ + 1; z++) {
                    const color = segment === 2 ? WISP_GRAY : SHADOW_PURPLE;
                    recordVoxel(originalVoxels, x, y + 4 - segment, z, color);
                }
            }
        }
    }
    
    shadowMinion.add(body);
    
    // === HEAD (menacing skull with mandibles) ===
    const head = new THREE.Group();
    head.name = 'head';
    
    // Skull base (5x3x4 - larger and more detailed)
    for (let x = -2; x <= 2; x++) {
        for (let y = 0; y <= 2; y++) {
            for (let z = -2; z <= 1; z++) {
                // Create skull shape with depth
                const isCenter = Math.abs(x) <= 1 && z >= -1;
                const color = isCenter ? SHADOW_DARK : VOID_BLACK;
                recordVoxel(originalVoxels, x, y + 7, z - 5, color);
            }
        }
    }
    
    // Mandibles (spider-like jaw)
    for (let side = -1; side <= 1; side += 2) {
        recordVoxel(originalVoxels, side * 2, 7, -6, BONE_DIM);
        recordVoxel(originalVoxels, side * 2, 6, -7, BONE_DIM);
        recordVoxel(originalVoxels, side * 1, 6, -7, WISP_GRAY);
    }
    
    // Multiple glowing eyes (6 eyes like a spider)
    const eyePositions = [
        [-2, 8, -5], [2, 8, -5],    // Outer eyes
        [-1, 8, -6], [1, 8, -6],    // Middle eyes
        [-1, 9, -5], [1, 9, -5]     // Top eyes
    ];
    
    eyePositions.forEach(pos => {
        recordVoxel(originalVoxels, pos[0], pos[1], pos[2], SHADOW_PURPLE);
    })
    
    shadowMinion.add(head);
    
    // === LEGS (8 spider-like legs) ===
    const legPositions = [
        // Front legs
        { side: -1, front: 1, name: 'frontLeftLeg' },
        { side: 1, front: 1, name: 'frontRightLeg' },
        // Middle-front legs  
        { side: -1, front: 0, name: 'midFrontLeftLeg' },
        { side: 1, front: 0, name: 'midFrontRightLeg' },
        // Middle-back legs
        { side: -1, front: -1, name: 'midBackLeftLeg' },
        { side: 1, front: -1, name: 'midBackRightLeg' },
        // Back legs
        { side: -1, front: -2, name: 'backLeftLeg' },
        { side: 1, front: -2, name: 'backRightLeg' }
    ];
    
    legPositions.forEach((legPos, index) => {
        const leg = new THREE.Group();
        leg.name = legPos.name;
        
        // More detailed leg segments (5 segments for more articulation)
        // Upper leg joint (thick attachment to body)
        for (let t = 0; t <= 1; t++) {
            recordVoxel(originalVoxels, legPos.side * 3 + t * legPos.side, 5, legPos.front, SHADOW_DARK);
            recordVoxel(originalVoxels, legPos.side * 3 + t * legPos.side, 4, legPos.front + t, SHADOW_DARK);
        }
        
        // Upper leg segment (bends outward)
        for (let seg = 0; seg < 3; seg++) {
            const xOffset = legPos.side * (4 + seg);
            const yOffset = 3 - seg;
            const zOffset = legPos.front + seg * 0.5;
            
            recordVoxel(originalVoxels, xOffset, yOffset, zOffset, SHADOW_DARK);
            recordVoxel(originalVoxels, xOffset, yOffset - 1, zOffset, WISP_GRAY);
            
            // Add thickness
            recordVoxel(originalVoxels, xOffset + legPos.side * 0.5, yOffset, zOffset, VOID_BLACK);
        }
        
        // Lower leg segments (reaching to ground)
        for (let seg = 0; seg < 4; seg++) {
            const xOffset = legPos.side * (6 + seg * 0.5);
            const yOffset = -seg;
            const zOffset = legPos.front + legPos.side + seg * 0.3;
            
            recordVoxel(originalVoxels, xOffset, yOffset, zOffset, VOID_BLACK);
            
            // Add spikes on alternate legs
            if (index % 2 === 0 && seg === 1) {
                recordVoxel(originalVoxels, xOffset, yOffset - 1, zOffset, BONE_DIM);
            }
        }
        
        // Claw foot (3-pronged)
        const clawX = legPos.side * 8;
        const clawZ = legPos.front + legPos.side + 1;
        recordVoxel(originalVoxels, clawX, -4, clawZ, BONE_DIM);
        recordVoxel(originalVoxels, clawX - legPos.side, -4, clawZ + 1, BONE_DIM);
        recordVoxel(originalVoxels, clawX + legPos.side, -4, clawZ + 1, BONE_DIM);
        recordVoxel(originalVoxels, clawX, -4, clawZ - 1, BONE_DIM);
        
        shadowMinion.add(leg);
    });
    
    // === TAIL/WISP (massive ethereal trailing element) ===
    const tail = new THREE.Group();
    tail.name = 'tail';
    
    // Create segmented stinger tail
    for (let segment = 0; segment < 5; segment++) {
        const segmentZ = 10 + segment * 2;
        const segmentSize = 3 - Math.floor(segment * 0.6);
        const segmentY = 3 - segment;
        
        // Main tail segment
        for (let x = -segmentSize; x <= segmentSize; x++) {
            for (let y = 0; y <= 1; y++) {
                for (let z = 0; z <= 1; z++) {
                    const color = segment < 3 ? SHADOW_PURPLE : WISP_GRAY;
                    recordVoxel(originalVoxels, x, segmentY + y, segmentZ + z, color);
                }
            }
        }
        
        // Add spikes on segments
        if (segment % 2 === 0) {
            recordVoxel(originalVoxels, 0, segmentY + 2, segmentZ, BONE_DIM);
            recordVoxel(originalVoxels, -segmentSize - 1, segmentY, segmentZ, VOID_BLACK);
            recordVoxel(originalVoxels, segmentSize + 1, segmentY, segmentZ, VOID_BLACK);
        }
    }
    
    // Stinger tip
    recordVoxel(originalVoxels, 0, -1, 20, BONE_DIM);
    recordVoxel(originalVoxels, 0, -2, 21, BONE_DIM);
    recordVoxel(originalVoxels, 0, -2, 22, SHADOW_PURPLE); // Poison tip
    
    shadowMinion.add(tail);
    
    // Build the actual mesh from voxels
    const materialGroups = {};
    
    originalVoxels.forEach(voxel => {
        const material = _getMaterialByHex_Cached(voxel.c);
        const key = voxel.c;
        
        if (!materialGroups[key]) {
            materialGroups[key] = {
                material: material,
                positions: []
            };
        }
        
        materialGroups[key].positions.push(
            voxel.x * minionVoxelSize,
            voxel.y * minionVoxelSize, 
            voxel.z * minionVoxelSize
        );
    });
    
    // Create merged geometry for each material using the efficient method
    Object.entries(materialGroups).forEach(([color, group]) => {
        if (group.positions.length === 0) return;
        
        const voxelCount = group.positions.length / 3;
        const geometries = [];
        
        // Create individual geometries at each position
        for (let i = 0; i < voxelCount; i++) {
            const voxelGeo = getOrCreateGeometry(`voxel_${minionVoxelSize}`, () => 
                new THREE.BoxGeometry(minionVoxelSize, minionVoxelSize, minionVoxelSize)
            );
            const clonedGeo = voxelGeo.clone();
            clonedGeo.translate(
                group.positions[i * 3],
                group.positions[i * 3 + 1], 
                group.positions[i * 3 + 2]
            );
            geometries.push(clonedGeo);
        }
        
        // Merge all geometries for this material
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const mesh = new THREE.Mesh(mergedGeometry, group.material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            shadowMinion.add(mesh);
        }
    });
    
    // Position the minion slightly above ground
    shadowMinion.position.y = 0.5;
    
    // Store voxel data for destruction system
    shadowMinion.userData = {
        objectType: 'shadow_minion',
        isDestructible: true,
        destructionEffect: 'wisp_fade', 
        health: 1,
        originalVoxels: originalVoxels,
        voxelScale: minionVoxelSize,
        // Animation properties
        animationHandler: null, // Will be set by animation system
        isBossMinion: true,
        emergingFromGround: true,
        emergenceStartTime: performance.now()
    };
    
    console.log(`[shadowMinionObject] Created shadow minion with ${originalVoxels.length} voxels`);
    return shadowMinion;
}