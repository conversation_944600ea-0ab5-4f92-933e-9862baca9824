import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Phoenix Rebirth Card Prefab
 * Creates phoenix that grants resurrection upon death
 */

// Phoenix rebirth specific colors
const PHOENIX_COLORS = {
    FLAME_RED: 0xFF4500,           // Bright flame red core
    FIRE_ORANGE: 0xFF8C00,         // Dark orange fire
    PHOENIX_GOLD: 0xFFD700,        // Golden phoenix body
    EMBER_YELLOW: 0xFFFF00,        // Bright yellow embers
    CRIMSON_RED: 0xDC143C,         // Deep crimson flames
    PHOENIX_WHITE: 0xFFFFE0         // Light yellow-white hot flames
};

/**
 * Create a phoenix rebirth card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The phoenix rebirth card 3D model
 */
export function createPhoenixRebirthCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'PhoenixRebirthCard';

    // Phoenix rebirth materials
    const flameRedMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.FLAME_RED,
        emissive: PHOENIX_COLORS.FLAME_RED,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.9
    });

    const fireOrangeMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.FIRE_ORANGE,
        emissive: PHOENIX_COLORS.FIRE_ORANGE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const phoenixGoldMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.PHOENIX_GOLD,
        emissive: PHOENIX_COLORS.PHOENIX_GOLD,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.85
    });

    const emberYellowMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.EMBER_YELLOW,
        emissive: PHOENIX_COLORS.EMBER_YELLOW,
        emissiveIntensity: 1.3,
        transparent: true,
        opacity: 0.75
    });

    const crimsonRedMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.CRIMSON_RED,
        emissive: PHOENIX_COLORS.CRIMSON_RED,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const phoenixWhiteMaterial = new THREE.MeshLambertMaterial({
        color: PHOENIX_COLORS.PHOENIX_WHITE,
        emissive: PHOENIX_COLORS.PHOENIX_WHITE,
        emissiveIntensity: 1.4,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Phoenix body (majestic bird form)
    const phoenixBodyVoxels = [
        // Phoenix head
        { x: 0.0, y: 0.20, z: 0.0, material: phoenixGoldMaterial },
        { x: -0.04, y: 0.24, z: 0.0, material: phoenixGoldMaterial }, // Crest
        { x: 0.04, y: 0.24, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.0, y: 0.28, z: 0.0, material: emberYellowMaterial }, // Top crest
        
        // Phoenix beak
        { x: 0.0, y: 0.20, z: 0.08, material: phoenixGoldMaterial },
        { x: 0.0, y: 0.16, z: 0.12, material: phoenixGoldMaterial },
        
        // Phoenix neck
        { x: 0.0, y: 0.12, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: phoenixGoldMaterial },
        
        // Phoenix body core
        { x: 0.0, y: 0.04, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.0, y: 0.0, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: phoenixGoldMaterial },
        { x: -0.04, y: 0.04, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.04, y: 0.04, z: 0.0, material: phoenixGoldMaterial },
        { x: -0.04, y: 0.0, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.04, y: 0.0, z: 0.0, material: phoenixGoldMaterial },
        
        // Phoenix tail
        { x: 0.0, y: -0.08, z: 0.0, material: phoenixGoldMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: fireOrangeMaterial },
        { x: 0.0, y: -0.16, z: 0.0, material: flameRedMaterial },
        { x: 0.0, y: -0.20, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: -0.24, z: 0.0, material: flameRedMaterial }
    ];

    // Phoenix wings (spread in flight)
    const phoenixWingsVoxels = [
        // Left wing
        { x: -0.08, y: 0.08, z: 0.0, material: phoenixGoldMaterial }, // Wing joint
        { x: -0.12, y: 0.12, z: 0.04, material: fireOrangeMaterial }, // Inner wing
        { x: -0.16, y: 0.16, z: 0.08, material: flameRedMaterial }, // Middle wing
        { x: -0.20, y: 0.20, z: 0.12, material: crimsonRedMaterial }, // Outer wing
        { x: -0.24, y: 0.24, z: 0.16, material: emberYellowMaterial }, // Wing tip
        { x: -0.28, y: 0.28, z: 0.20, material: phoenixWhiteMaterial }, // Wing flames
        
        { x: -0.12, y: 0.08, z: -0.04, material: fireOrangeMaterial }, // Wing underside
        { x: -0.16, y: 0.12, z: -0.08, material: flameRedMaterial },
        { x: -0.20, y: 0.16, z: -0.12, material: crimsonRedMaterial },
        { x: -0.24, y: 0.20, z: -0.16, material: emberYellowMaterial },
        { x: -0.28, y: 0.24, z: -0.20, material: phoenixWhiteMaterial },
        
        // Right wing
        { x: 0.08, y: 0.08, z: 0.0, material: phoenixGoldMaterial }, // Wing joint
        { x: 0.12, y: 0.12, z: 0.04, material: fireOrangeMaterial }, // Inner wing
        { x: 0.16, y: 0.16, z: 0.08, material: flameRedMaterial }, // Middle wing
        { x: 0.20, y: 0.20, z: 0.12, material: crimsonRedMaterial }, // Outer wing
        { x: 0.24, y: 0.24, z: 0.16, material: emberYellowMaterial }, // Wing tip
        { x: 0.28, y: 0.28, z: 0.20, material: phoenixWhiteMaterial }, // Wing flames
        
        { x: 0.12, y: 0.08, z: -0.04, material: fireOrangeMaterial }, // Wing underside
        { x: 0.16, y: 0.12, z: -0.08, material: flameRedMaterial },
        { x: 0.20, y: 0.16, z: -0.12, material: crimsonRedMaterial },
        { x: 0.24, y: 0.20, z: -0.16, material: emberYellowMaterial },
        { x: 0.28, y: 0.24, z: -0.20, material: phoenixWhiteMaterial }
    ];

    // Phoenix flames (rebirth fire aura)
    const phoenixFlamesVoxels = [
        // Core flames around phoenix
        { x: -0.08, y: 0.0, z: 0.08, material: flameRedMaterial },
        { x: 0.08, y: 0.0, z: 0.08, material: flameRedMaterial },
        { x: -0.08, y: 0.0, z: -0.08, material: flameRedMaterial },
        { x: 0.08, y: 0.0, z: -0.08, material: flameRedMaterial },
        
        // Rising flames
        { x: -0.12, y: 0.04, z: 0.12, material: fireOrangeMaterial },
        { x: 0.12, y: 0.04, z: 0.12, material: fireOrangeMaterial },
        { x: -0.12, y: 0.04, z: -0.12, material: fireOrangeMaterial },
        { x: 0.12, y: 0.04, z: -0.12, material: fireOrangeMaterial },
        
        // Outer flame ring
        { x: -0.16, y: 0.08, z: 0.16, material: crimsonRedMaterial },
        { x: 0.16, y: 0.08, z: 0.16, material: crimsonRedMaterial },
        { x: -0.16, y: 0.08, z: -0.16, material: crimsonRedMaterial },
        { x: 0.16, y: 0.08, z: -0.16, material: crimsonRedMaterial },
        
        // Upper flames
        { x: -0.20, y: 0.12, z: 0.20, material: emberYellowMaterial },
        { x: 0.20, y: 0.12, z: 0.20, material: emberYellowMaterial },
        { x: -0.20, y: 0.12, z: -0.20, material: emberYellowMaterial },
        { x: 0.20, y: 0.12, z: -0.20, material: emberYellowMaterial },
        
        // Hot white flames
        { x: -0.24, y: 0.16, z: 0.24, material: phoenixWhiteMaterial },
        { x: 0.24, y: 0.16, z: 0.24, material: phoenixWhiteMaterial },
        { x: -0.24, y: 0.16, z: -0.24, material: phoenixWhiteMaterial },
        { x: 0.24, y: 0.16, z: -0.24, material: phoenixWhiteMaterial }
    ];

    // Phoenix embers (floating fire particles)
    const phoenixEmbersVoxels = [
        // Inner ember layer
        { x: -0.14, y: 0.32, z: 0.08, material: emberYellowMaterial },
        { x: 0.14, y: 0.32, z: 0.08, material: emberYellowMaterial },
        { x: 0.14, y: -0.28, z: 0.08, material: emberYellowMaterial },
        { x: -0.14, y: -0.28, z: 0.08, material: emberYellowMaterial },
        
        // Outer ember layer
        { x: -0.22, y: 0.36, z: 0.16, material: fireOrangeMaterial },
        { x: 0.22, y: 0.36, z: 0.16, material: fireOrangeMaterial },
        { x: 0.22, y: -0.32, z: 0.16, material: fireOrangeMaterial },
        { x: -0.22, y: -0.32, z: 0.16, material: fireOrangeMaterial },
        
        // Floating fire particles
        { x: -0.30, y: 0.20, z: 0.24, material: flameRedMaterial },
        { x: 0.30, y: 0.16, z: 0.24, material: flameRedMaterial },
        { x: 0.20, y: 0.30, z: 0.20, material: crimsonRedMaterial },
        { x: -0.16, y: 0.30, z: 0.20, material: crimsonRedMaterial },
        { x: 0.26, y: -0.24, z: 0.28, material: flameRedMaterial },
        { x: -0.26, y: -0.20, z: 0.28, material: flameRedMaterial },
        { x: 0.18, y: -0.30, z: 0.24, material: crimsonRedMaterial },
        { x: -0.20, y: -0.30, z: 0.24, material: crimsonRedMaterial },
        
        // Distant fire sparkles
        { x: -0.34, y: 0.24, z: 0.32, material: phoenixWhiteMaterial },
        { x: 0.34, y: 0.26, z: 0.32, material: phoenixWhiteMaterial },
        { x: 0.24, y: 0.34, z: 0.28, material: emberYellowMaterial },
        { x: -0.26, y: 0.34, z: 0.28, material: emberYellowMaterial },
        { x: 0.34, y: -0.24, z: 0.32, material: phoenixWhiteMaterial },
        { x: -0.34, y: -0.26, z: 0.32, material: phoenixWhiteMaterial },
        { x: 0.26, y: -0.34, z: 0.28, material: emberYellowMaterial },
        { x: -0.24, y: -0.34, z: 0.28, material: emberYellowMaterial }
    ];

    // Create phoenix body group
    const phoenixBodyGroup = new THREE.Group();
    phoenixBodyGroup.name = 'phoenixBody';

    // Add phoenix body voxels
    phoenixBodyVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        phoenixBodyGroup.add(mesh);
    });

    // Create phoenix wings group
    const phoenixWingsGroup = new THREE.Group();
    phoenixWingsGroup.name = 'phoenixWings';

    // Add phoenix wings voxels
    phoenixWingsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.wingPhase = index * 0.1; // Stagger animation
        phoenixWingsGroup.add(mesh);
    });

    // Create phoenix flames group
    const phoenixFlamesGroup = new THREE.Group();
    phoenixFlamesGroup.name = 'phoenixFlames';

    // Add phoenix flames voxels
    phoenixFlamesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.flamePhase = index * 0.15; // Stagger animation
        phoenixFlamesGroup.add(mesh);
    });

    // Create phoenix embers group
    const phoenixEmbersGroup = new THREE.Group();
    phoenixEmbersGroup.name = 'phoenixEmbers';

    // Add phoenix embers voxels
    phoenixEmbersVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.emberPhase = index * 0.05; // Stagger animation
        phoenixEmbersGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(phoenixBodyGroup);
    cardGroup.add(phoenixWingsGroup);
    cardGroup.add(phoenixFlamesGroup);
    cardGroup.add(phoenixEmbersGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        phoenixPulse: 0,
        fireFlicker: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update phoenix rebirth card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updatePhoenixRebirthCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.phoenixPulse += deltaTime * 2.5; // Phoenix pulse speed
    cardGroup.userData.fireFlicker += deltaTime * 4.0; // Fire flicker speed

    const time = cardGroup.userData.animationTime;
    const phoenixPulse = cardGroup.userData.phoenixPulse;
    const fireFlicker = cardGroup.userData.fireFlicker;

    // Animate phoenix body (majestic pulsing)
    const phoenixBodyGroup = cardGroup.getObjectByName('phoenixBody');
    if (phoenixBodyGroup) {
        // Phoenix body golden pulsing (life force)
        const phoenixIntensity = 1.0 + Math.sin(phoenixPulse * 1.5) * 0.3;
        phoenixBodyGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * phoenixIntensity;
            }
        });
    }

    // Animate phoenix wings (wing beating)
    const phoenixWingsGroup = cardGroup.getObjectByName('phoenixWings');
    if (phoenixWingsGroup) {
        // Wing beating motion
        phoenixWingsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.wingPhase !== undefined) {
                const wingTime = phoenixPulse + mesh.userData.wingPhase;
                
                // Wing beating motion (up and down)
                const wingBeat = Math.sin(wingTime * 3.0) * 0.002;
                const wingFlap = Math.cos(wingTime * 2.5) * 0.001;
                
                mesh.position.y = mesh.userData.originalPosition.y + wingBeat;
                mesh.position.z = mesh.userData.originalPosition.z + wingFlap;
                
                // Wing fire intensity pulsing
                const wingFlame = 1.0 + Math.sin(wingTime * 4.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * wingFlame;
                }
                
                // Wing opacity flickering (fire wings)
                const wingOpacity = 0.8 + Math.sin(wingTime * 5.0) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * wingOpacity;
                }
            }
        });
    }

    // Animate phoenix flames (rebirth fire dancing)
    const phoenixFlamesGroup = cardGroup.getObjectByName('phoenixFlames');
    if (phoenixFlamesGroup) {
        phoenixFlamesGroup.rotation.y = time * 0.7; // Slow rotation
        
        // Phoenix flame dancing
        phoenixFlamesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.flamePhase !== undefined) {
                const flameTime = fireFlicker + mesh.userData.flamePhase;
                
                // Fire dancing motion
                const danceX = Math.sin(flameTime * 2.5) * 0.002;
                const danceY = Math.cos(flameTime * 3.0) * 0.003;
                const danceZ = Math.sin(flameTime * 2.8) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + danceX;
                mesh.position.y = mesh.userData.originalPosition.y + danceY;
                mesh.position.z = mesh.userData.originalPosition.z + danceZ;
                
                // Flame intensity flickering
                const flameIntensity = 1.0 + Math.sin(flameTime * 6.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * flameIntensity;
                }
                
                // Flame scale variation (fire breathing)
                const flameScale = 0.9 + Math.sin(flameTime * 4.5) * 0.3;
                mesh.scale.setScalar(flameScale);
            }
        });
    }

    // Animate phoenix embers (floating fire particles)
    const phoenixEmbersGroup = cardGroup.getObjectByName('phoenixEmbers');
    if (phoenixEmbersGroup) {
        phoenixEmbersGroup.rotation.y = time * 0.9; // Slightly faster rotation
        
        // Phoenix ember floating
        phoenixEmbersGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.emberPhase !== undefined) {
                const emberTime = fireFlicker + mesh.userData.emberPhase;
                
                // Ember floating motion (sparks rising)
                const floatX = Math.sin(emberTime * 3.5) * 0.003;
                const floatY = Math.cos(emberTime * 2.0) * 0.004 + 0.001; // Slight upward drift
                const floatZ = Math.sin(emberTime * 4.0) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + floatX;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                mesh.position.z = mesh.userData.originalPosition.z + floatZ;
                
                // Ember sparkling intensity
                const emberSpark = 0.8 + Math.sin(emberTime * 8.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * emberSpark;
                }
                
                // Random bright flashes (ember bursts)
                const emberBurst = Math.sin(emberTime * 12.0) > 0.8 ? 1.8 : 1.0;
                mesh.material.emissiveIntensity *= emberBurst;
                
                // Ember scale twinkling
                const emberTwinkle = 0.8 + Math.sin(emberTime * 10.0) * 0.4;
                mesh.scale.setScalar(emberTwinkle);
            }
        });
    }

    // Overall phoenix rebirth pulsing (life force energy)
    const rebirthPulse = 1 + Math.sin(time * 2.2) * 0.06;
    cardGroup.scale.setScalar(0.8 * rebirthPulse);
}

// Export the phoenix rebirth card data for the loot system
export const PHOENIX_REBIRTH_CARD_DATA = {
    name: 'Phoenix Rebirth',
    description: 'Grants the power of the phoenix - upon death, resurrect with full health surrounded by flames that damage nearby enemies.',
    category: 'card',
    rarity: 'epic',
    effect: 'phoenix_rebirth',
    effectValue: 1, // Number of resurrections granted
    createFunction: createPhoenixRebirthCard,
    updateFunction: updatePhoenixRebirthCardAnimation,
    voxelModel: 'phoenix_rebirth_card',
    glow: {
        color: 0xFF4500,
        intensity: 1.5
    }
};

export default createPhoenixRebirthCard;