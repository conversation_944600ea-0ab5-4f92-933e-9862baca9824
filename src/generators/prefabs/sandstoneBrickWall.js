import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Sandstone Brick Wall
 * 
 * Creates sandstone brick walls with Egyptian/desert theme.
 * Based on the standard stonebrick wall structure but with sandstone colors.
 * Uses the same size and collision detection as normal stone brick walls.
 */

// Sandstone color palette (Egyptian/desert theme)
const SANDSTONE_COLORS = [
    0xF4A460, // Sandy brown (base)
    0xDEB887, // Burlywood (lighter)
    0xD2B48C, // Tan (medium)
    0xCD853F, // Peru (darker)
    0xA0522D, // Sienna (darkest)
    0xF5DEB3, // Wheat (lightest)
    0xDAA520, // Goldenrod (accent)
    0xB8860B  // Dark goldenrod (mortar)
];

/**
 * Generate sandstone brick wall pattern
 * CRITICAL FIX: Use the same scaling system as normal stone walls
 */
function generateSandstoneBrickPattern(width, height, depth, seed = 42) {
    const rng = mulberry32(seed);
    const voxels = [];

    // CRITICAL FIX: Use the same scaling system as normal stone brick walls
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;

    // Calculate dimensions using scaled voxels (same as normal stone walls)
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);

    // Safeguard dimensions (same as normal stone walls)
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);

    // Generate base sandstone brick pattern using scaled environment blocks (same as normal stone walls)
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            // Add some randomness to brick placement (same density as stone walls)
            if (rng() > 0.05) { // 95% chance for brick
                // Choose sandstone color based on position and randomness
                let colorIndex;
                const isEdge = (ex === 0 || ex === safeNumXEnv - 1 || ey === 0 || ey === safeNumYEnv - 1);
                const isMortar = (ex % 3 === 2 || ey % 2 === 1); // Mortar lines (adjusted for environment scale)

                if (isMortar) {
                    colorIndex = 7; // Dark goldenrod for mortar
                } else if (isEdge) {
                    colorIndex = Math.floor(rng() * 3) + 3; // Darker colors for edges
                } else {
                    colorIndex = Math.floor(rng() * 6); // Lighter colors for center
                }

                voxels.push({
                    ex: ex,
                    ey: ey,
                    c: SANDSTONE_COLORS[colorIndex],
                    type: 'sandstone',
                    wallVoxelSize: wallVoxelSize // Store the scale for later use
                });
            }
        }
    }

    return voxels;
}

/**
 * Create sandstone brick wall segment
 * Uses the same structure and collision detection as normal stone brick walls
 */
export function createSandstoneBrickWallSegment(width, height, depth, roomData, isDarkRoom = false, position, rotation) {
    // --- INPUT VALIDATION (same as stone walls) ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createSandstoneBrickWallSegment received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() };
    }
    
    const group = new THREE.Group();
    const seed = (roomData?.id || 0) * 1000 + (position?.x || 0) * 100 + (position?.z || 0) * 10;
    
    // Generate wall pattern
    const wallVoxels = generateSandstoneBrickPattern(width, height, depth, seed);

    // CRITICAL FIX: Create sandstone brick group using proper scaled geometry (like normal stone walls)
    if (wallVoxels.length > 0) {
        const geometriesByMaterial = {};
        const tempMatrix = new THREE.Matrix4();

        // Calculate offsets (same as normal stone walls)
        const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;
        const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;
        const numX = Math.ceil(width / VOXEL_SIZE);
        const numY = Math.ceil(height / VOXEL_SIZE);
        const numX_env = Math.ceil(numX / WALL_SCALE);
        const numY_env = Math.ceil(numY / WALL_SCALE);
        const safeNumXEnv = Math.max(1, numX_env);
        const safeNumYEnv = Math.max(1, numY_env);
        const offsetX = (safeNumXEnv - 1) * wallVoxelSize / 2;
        const offsetY = (safeNumYEnv - 1) * wallVoxelSize / 2;

        // FIXED: Use the same extrusion system as normal stone walls for smooth tops
        const largeWallGeo = getOrCreateGeometry(
            `sandstone_wall_${wallVoxelSize.toFixed(4)}`,
            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, depth)
        );

        // Helper function to add geometry to merge list
        const addGeometry = (geometry, colorHex, matrix) => {
            if (!geometriesByMaterial[colorHex]) {
                geometriesByMaterial[colorHex] = [];
            }
            const transformedGeometry = geometry.clone();
            transformedGeometry.applyMatrix4(matrix);
            geometriesByMaterial[colorHex].push(transformedGeometry);
        };

        let voxelsAdded = 0;

        wallVoxels.forEach(voxel => {
            const colorHex = voxel.c.toString(16).padStart(6, '0');

            // Calculate position using environment coordinates (same as normal stone walls)
            const baseX = voxel.ex * wallVoxelSize - offsetX;
            const baseY = voxel.ey * wallVoxelSize - offsetY;

            // Main wall block
            tempMatrix.makeTranslation(baseX, baseY, 0);
            addGeometry(largeWallGeo, colorHex, tempMatrix);
            voxelsAdded++;

            // REMOVED: North/south face caps - normal stone walls don't use these

            // FIXED: Add top face for the top row of voxels with extended coverage and overhang
            if (voxel.ey === safeNumYEnv - 1) {
                // Create a larger top cap that covers gaps and overhangs the sides
                const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                const topCapGeo = getOrCreateGeometry(
                    `wall_top_cap_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                    () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                );

                // Position at the top of the wall voxel, slightly higher for prominence
                const topPosY = baseY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                // Add extended top cap with overhang
                tempMatrix.makeTranslation(baseX, topPosY, 0);
                addGeometry(topCapGeo, colorHex, tempMatrix);
                voxelsAdded++;

                // Add additional gap-filling caps between voxels - FIXED: Complete coverage
                if (voxel.ex < safeNumXEnv - 1) { // Not the last column
                    const gapCapGeo = getOrCreateGeometry(
                        `wall_gap_cap_overhang_complete_${(wallVoxelSize * 0.8).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize * 0.8, VOXEL_SIZE * 0.25, overhangDepth)
                    );
                    // FIXED: Position to completely fill the gap between main voxel caps
                    const gapCapX = baseX + wallVoxelSize * 0.5;
                    tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                    addGeometry(gapCapGeo, colorHex, tempMatrix);
                    voxelsAdded++;
                }

                // NEW: Add edge caps for the east end of walls to eliminate edge gaps
                if (voxel.ex === safeNumXEnv - 1) { // Last column (east edge)
                    const edgeCapGeo = getOrCreateGeometry(
                        `wall_edge_cap_east_${(wallVoxelSize * 0.6).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize * 0.6, VOXEL_SIZE * 0.25, overhangDepth)
                    );
                    // Position at the east edge to fill the gap
                    const edgeCapX = baseX + wallVoxelSize * 0.6;
                    tempMatrix.makeTranslation(edgeCapX, topPosY, 0);
                    addGeometry(edgeCapGeo, colorHex, tempMatrix);
                    voxelsAdded++;
                }

                // NEW: Add edge caps for the west end of walls to eliminate edge gaps
                if (voxel.ex === 0) { // First column (west edge)
                    const edgeCapGeo = getOrCreateGeometry(
                        `wall_edge_cap_west_${(wallVoxelSize * 0.6).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize * 0.6, VOXEL_SIZE * 0.25, overhangDepth)
                    );
                    // Position at the west edge to fill the gap
                    const edgeCapX = baseX - wallVoxelSize * 0.6;
                    tempMatrix.makeTranslation(edgeCapX, topPosY, 0);
                    addGeometry(edgeCapGeo, colorHex, tempMatrix);
                    voxelsAdded++;
                }
            }
        });

        console.log(`[SandstoneWall] Added ${voxelsAdded} voxels total with extrusion system.`);

        // Merge geometries by material (same as stone walls)
        for (const colorHex in geometriesByMaterial) {
            const material = _getMaterialByHex_Cached(colorHex);
            if (!material) continue;

            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
            if (mergedGeometry) {
                const mesh = new THREE.Mesh(mergedGeometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                mesh.name = 'sandstoneBricks';
                // CRITICAL: Mark mesh with proper userData for collision detection (same as stone walls)
                mesh.userData.isWall = true;
                mesh.userData.wallType = 'sandstone_brick';
                group.add(mesh);
            }
        }
    }
    
    // CRITICAL: Set up group properties following normal wall pattern
    group.userData.wallType = 'sandstone_brick';
    group.userData.isWall = true; // Mark entire group as wall for collision detection
    group.name = 'sandstoneBrickWall'; // Proper naming for debugging
    
    // CRITICAL: Set shadow properties for entire group (same as normal walls)
    group.castShadow = true;
    group.receiveShadow = true;
    
    return {
        group: group,
        torchPositions: [] // No torches on sandstone walls
    };
}

// Register the new wall type
if (typeof window !== 'undefined' && window.prefabRegistry) {
    window.prefabRegistry.registerWall('sandstone_brick', createSandstoneBrickWallSegment);
}
