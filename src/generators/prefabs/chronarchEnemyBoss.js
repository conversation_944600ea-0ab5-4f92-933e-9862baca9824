import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Chronarch Demonic Cyclops Boss colors - clean color palette
const CHRONARCH_COLORS = {
    // Demonic Skin
    FLESH_DEMONIC: '8B0000',      // Dark red demonic skin
    FLESH_SHADOW: '4B0000',       // Deep shadow demonic skin
    FLESH_HIGHLIGHT: 'CD5C5C',    // Lighter demonic skin tones
    
    // Cyclops Eye
    CYCLOPS_EYE: 'FF0000',        // Bright red cyclops eye
    EYE_FIRE: 'FF4500',           // Fiery orange eye glow
    EYE_CORE: 'FFFF00',           // Yellow eye core
    
    // Demonic Features
    HORN_BONE: 'F5F5DC',          // Bone-colored horns
    HORN_TIP: '696969',           // Dark horn tips
    FANG_WHITE: 'FFFFFF',         // White fangs
    CLAW_BONE: 'F5F5DC',          // Bone-colored claws
    
    // Robes
    ROBE_PURPLE: '6A5ACD',        // Royal purple robes
    ROBE_DARK: '2E1065',          // Much darker purple shadows
    ROBE_LIGHT: '9370DB',         // Light purple highlights
    
    // Accessories
    GOLD_TRIM: 'FFD700',          // Golden trim and details
    STAFF_WOOD: '8B4513',         // Wooden staff base
    STAFF_CRYSTAL: 'E6E6FA',      // Crystal staff top
    TEMPORAL_GLOW: '8A2BE2',      // Temporal energy purple
    
    // Beard (keeping the epic beard!)
    BEARD_GRAY: '708090',         // Gray wizard beard
    BEARD_WHITE: 'F5F5F5',        // White beard highlights
    
    // Misc
    SHADOW_BLACK: '191970',       // Midnight blue shadows
    BELT_LEATHER: '654321'        // Leather belt
};

/**
 * Creates Chronarch the Demonic Cyclops Boss - rebuilt from scratch
 * Features a terrifying single red cyclops eye, demonic horns, fangs, claws, and epic flowing robes
 * @param {number} [scale=1.0] Overall scale factor
 * @returns {THREE.Group} The demonic cyclops Chronarch boss model
 */
export function createChronarchEnemyBossModel(scale = 1.0) {
    console.log(`🔮 CHRONARCH REBUILD - Creating demonic cyclops boss model with scale: ${scale}`);

    // Validate input parameters
    const safeScale = (isFinite(scale) && !isNaN(scale) && scale > 0) ? scale : 1.0;
    const safeVoxelSize = (isFinite(VOXEL_SIZE) && !isNaN(VOXEL_SIZE) && VOXEL_SIZE > 0) ? VOXEL_SIZE : 0.05;
    
    const finalGroup = new THREE.Group();
    finalGroup.name = "Chronarch";

    const chronarchVoxelSize = safeVoxelSize * 1.0; // Standard voxel size but with more detail

    // Create visual offset group (like Nairabos for proper ground clearance)
    const visualGroup = new THREE.Group();
    visualGroup.name = "visualModel";
    visualGroup.position.y = 1.0; // Elevate visual model

    // Create body part groups
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";
    
    const headGroup = new THREE.Group();
    headGroup.name = "head";
    
    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";
    
    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";
    
    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";
    
    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";
    
    const staffGroup = new THREE.Group();
    staffGroup.name = "staff";

    // Position body parts
    headGroup.position.set(0, 8 * chronarchVoxelSize, 0);
    leftArmGroup.position.set(-4 * chronarchVoxelSize, 5 * chronarchVoxelSize, 0);
    rightArmGroup.position.set(4 * chronarchVoxelSize, 5 * chronarchVoxelSize, 0);
    leftLegGroup.position.set(-2 * chronarchVoxelSize, -2 * chronarchVoxelSize, 0);
    rightLegGroup.position.set(2 * chronarchVoxelSize, -2 * chronarchVoxelSize, 0);
    staffGroup.position.set(4 * chronarchVoxelSize, -3 * chronarchVoxelSize, 0);

    // Helper function to add voxels
    const addVoxel = (group, x, y, z, colorHex, materialProps = {}) => {
        const geometry = getOrCreateGeometry('chronarch_voxel', () => 
            new THREE.BoxGeometry(chronarchVoxelSize, chronarchVoxelSize, chronarchVoxelSize)
        );
        const material = _getMaterialByHex_Cached(colorHex, materialProps);
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.set(x * chronarchVoxelSize, y * chronarchVoxelSize, z * chronarchVoxelSize);
        group.add(mesh);
    };

    // BUILD HEAD - Demonic cyclops head with single red eye
    console.log('🔮 Building demonic cyclops head...');
    
    // Main demonic face (using POSITIVE Z coordinates to face forward like Nairabos)
    const faceVoxels = [
        // Main face structure (more detailed)
        { x: -2.5, y: 0, z: 1 }, { x: -2, y: 0, z: 1 }, { x: -1, y: 0, z: 1 }, { x: 0, y: 0, z: 1 }, { x: 1, y: 0, z: 1 }, { x: 2, y: 0, z: 1 }, { x: 2.5, y: 0, z: 1 },
        { x: -2.5, y: 1, z: 1 }, { x: -2, y: 1, z: 1 }, { x: -1, y: 1, z: 1 }, { x: 0, y: 1, z: 1 }, { x: 1, y: 1, z: 1 }, { x: 2, y: 1, z: 1 }, { x: 2.5, y: 1, z: 1 },
        { x: -2, y: 2, z: 1 }, { x: -1, y: 2, z: 1 }, { x: 0, y: 2, z: 1 }, { x: 1, y: 2, z: 1 }, { x: 2, y: 2, z: 1 }, // Forehead
        { x: -1.5, y: 3, z: 1 }, { x: -1, y: 3, z: 1 }, { x: 0, y: 3, z: 1 }, { x: 1, y: 3, z: 1 }, { x: 1.5, y: 3, z: 1 }, // Upper forehead
        
        // Face depth layers (more detailed)
        { x: -2, y: 0, z: 1.5 }, { x: -1, y: 0, z: 1.5 }, { x: 0, y: 0, z: 1.5 }, { x: 1, y: 0, z: 1.5 }, { x: 2, y: 0, z: 1.5 },
        { x: -2, y: 1, z: 1.5 }, { x: -1, y: 1, z: 1.5 }, { x: 0, y: 1, z: 1.5 }, { x: 1, y: 1, z: 1.5 }, { x: 2, y: 1, z: 1.5 },
        { x: -1, y: 2, z: 1.5 }, { x: 0, y: 2, z: 1.5 }, { x: 1, y: 2, z: 1.5 },
        
        // Cheekbones (more pronounced)
        { x: -3, y: 1.5, z: 1.2 }, { x: 3, y: 1.5, z: 1.2 },
        { x: -2.8, y: 1.2, z: 1.3 }, { x: 2.8, y: 1.2, z: 1.3 },
        
        // Jaw definition
        { x: -2, y: -0.5, z: 1.2 }, { x: -1, y: -0.5, z: 1.2 }, { x: 0, y: -0.5, z: 1.2 }, { x: 1, y: -0.5, z: 1.2 }, { x: 2, y: -0.5, z: 1.2 }
    ];
    
    faceVoxels.forEach(pos => {
        const isDepth = pos.z >= 1.5;
        const color = isDepth ? CHRONARCH_COLORS.FLESH_SHADOW : CHRONARCH_COLORS.FLESH_DEMONIC;
        addVoxel(headGroup, pos.x, pos.y, pos.z, color);
    });

    // SINGLE MASSIVE CYCLOPS EYE (forward-facing with positive Z) - Much more detailed
    // Eye core (bright yellow center)
    addVoxel(headGroup, 0, 1.8, 1.7, CHRONARCH_COLORS.EYE_CORE, {
        emissive: new THREE.Color(0xFFFF00),
        emissiveIntensity: 1.2
    });
    
    // Inner pupil ring (red-orange)
    const innerEyeVoxels = [
        { x: -0.3, y: 1.8, z: 1.6 }, { x: 0.3, y: 1.8, z: 1.6 },
        { x: 0, y: 2.1, z: 1.6 }, { x: 0, y: 1.5, z: 1.6 },
        { x: -0.2, y: 2, z: 1.6 }, { x: 0.2, y: 2, z: 1.6 },
        { x: -0.2, y: 1.6, z: 1.6 }, { x: 0.2, y: 1.6, z: 1.6 }
    ];
    
    innerEyeVoxels.forEach(pos => {
        addVoxel(headGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.EYE_FIRE, {
            emissive: new THREE.Color(0xFF4500),
            emissiveIntensity: 1.0
        });
    });
    
    // Middle eye ring (bright red)
    const middleEyeVoxels = [
        { x: -0.7, y: 1.8, z: 1.4 }, { x: 0.7, y: 1.8, z: 1.4 },
        { x: 0, y: 2.5, z: 1.4 }, { x: 0, y: 1.1, z: 1.4 },
        { x: -0.5, y: 2.3, z: 1.4 }, { x: 0.5, y: 2.3, z: 1.4 },
        { x: -0.5, y: 1.3, z: 1.4 }, { x: 0.5, y: 1.3, z: 1.4 },
        { x: -0.3, y: 2.4, z: 1.4 }, { x: 0.3, y: 2.4, z: 1.4 },
        { x: -0.3, y: 1.2, z: 1.4 }, { x: 0.3, y: 1.2, z: 1.4 }
    ];
    
    middleEyeVoxels.forEach(pos => {
        addVoxel(headGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.CYCLOPS_EYE, {
            emissive: new THREE.Color(0xFF0000),
            emissiveIntensity: 0.9
        });
    });
    
    // Outer eye ring (darker red)
    const outerEyeVoxels = [
        { x: -1.2, y: 1.8, z: 1.2 }, { x: 1.2, y: 1.8, z: 1.2 },
        { x: 0, y: 3, z: 1.2 }, { x: 0, y: 0.6, z: 1.2 },
        { x: -0.8, y: 2.8, z: 1.2 }, { x: 0.8, y: 2.8, z: 1.2 },
        { x: -0.8, y: 0.8, z: 1.2 }, { x: 0.8, y: 0.8, z: 1.2 },
        { x: -1, y: 2.6, z: 1.2 }, { x: 1, y: 2.6, z: 1.2 },
        { x: -1, y: 1, z: 1.2 }, { x: 1, y: 1, z: 1.2 }
    ];
    
    outerEyeVoxels.forEach(pos => {
        addVoxel(headGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.CYCLOPS_EYE, {
            emissive: new THREE.Color(0x8B0000),
            emissiveIntensity: 0.6
        });
    });
    
    // Eye socket definition
    const eyeSocketVoxels = [
        { x: -1.5, y: 1.8, z: 1.1 }, { x: 1.5, y: 1.8, z: 1.1 },
        { x: 0, y: 3.3, z: 1.1 }, { x: 0, y: 0.3, z: 1.1 },
        { x: -1.3, y: 3.1, z: 1.1 }, { x: 1.3, y: 3.1, z: 1.1 },
        { x: -1.3, y: 0.5, z: 1.1 }, { x: 1.3, y: 0.5, z: 1.1 }
    ];
    
    eyeSocketVoxels.forEach(pos => {
        addVoxel(headGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.FLESH_SHADOW);
    });

    // Demonic horns (much more detailed)
    const hornVoxels = [
        // Left horn base and curve
        { x: -2.5, y: 3.5, z: 0 }, { x: -2.7, y: 3.7, z: 0.2 }, { x: -2.9, y: 3.9, z: 0.4 },
        { x: -3.1, y: 4.1, z: 0.6 }, { x: -3.3, y: 4.3, z: 0.8 }, { x: -3.5, y: 4.5, z: 1.0 },
        { x: -3.7, y: 4.7, z: 1.2 }, { x: -3.9, y: 4.9, z: 1.4 }, { x: -4.1, y: 5.1, z: 1.6 },
        
        // Left horn thickness
        { x: -2.3, y: 3.3, z: 0 }, { x: -2.5, y: 3.5, z: 0.2 }, { x: -2.7, y: 3.7, z: 0.4 },
        { x: -2.9, y: 3.9, z: 0.6 }, { x: -3.1, y: 4.1, z: 0.8 },
        
        // Right horn base and curve  
        { x: 2.5, y: 3.5, z: 0 }, { x: 2.7, y: 3.7, z: 0.2 }, { x: 2.9, y: 3.9, z: 0.4 },
        { x: 3.1, y: 4.1, z: 0.6 }, { x: 3.3, y: 4.3, z: 0.8 }, { x: 3.5, y: 4.5, z: 1.0 },
        { x: 3.7, y: 4.7, z: 1.2 }, { x: 3.9, y: 4.9, z: 1.4 }, { x: 4.1, y: 5.1, z: 1.6 },
        
        // Right horn thickness
        { x: 2.3, y: 3.3, z: 0 }, { x: 2.5, y: 3.5, z: 0.2 }, { x: 2.7, y: 3.7, z: 0.4 },
        { x: 2.9, y: 3.9, z: 0.6 }, { x: 3.1, y: 4.1, z: 0.8 },
        
        // Horn ridges and texture
        { x: -2.6, y: 3.6, z: 0.1 }, { x: -2.8, y: 3.8, z: 0.3 }, { x: -3.0, y: 4.0, z: 0.5 },
        { x: 2.6, y: 3.6, z: 0.1 }, { x: 2.8, y: 3.8, z: 0.3 }, { x: 3.0, y: 4.0, z: 0.5 }
    ];
    
    hornVoxels.forEach(pos => {
        const color = pos.y <= 3.8 ? CHRONARCH_COLORS.HORN_BONE : CHRONARCH_COLORS.HORN_TIP;
        addVoxel(headGroup, pos.x, pos.y, pos.z, color);
    });

    // Demonic fangs (more detailed and menacing)
    const fangVoxels = [
        // Left fang (larger and more detailed)
        { x: -0.6, y: 0.2, z: 1.7 }, { x: -0.6, y: 0, z: 1.7 }, { x: -0.6, y: -0.2, z: 1.7 },
        { x: -0.6, y: -0.4, z: 1.8 }, { x: -0.6, y: -0.6, z: 1.9 }, // Extending outward
        { x: -0.5, y: 0.1, z: 1.7 }, { x: -0.7, y: 0.1, z: 1.7 }, // Thickness
        { x: -0.5, y: -0.3, z: 1.8 }, { x: -0.7, y: -0.3, z: 1.8 },
        
        // Right fang (larger and more detailed)
        { x: 0.6, y: 0.2, z: 1.7 }, { x: 0.6, y: 0, z: 1.7 }, { x: 0.6, y: -0.2, z: 1.7 },
        { x: 0.6, y: -0.4, z: 1.8 }, { x: 0.6, y: -0.6, z: 1.9 }, // Extending outward
        { x: 0.5, y: 0.1, z: 1.7 }, { x: 0.7, y: 0.1, z: 1.7 }, // Thickness
        { x: 0.5, y: -0.3, z: 1.8 }, { x: 0.7, y: -0.3, z: 1.8 },
        
        // Secondary smaller fangs
        { x: -0.3, y: 0.1, z: 1.6 }, { x: -0.3, y: -0.1, z: 1.6 }, // Left secondary
        { x: 0.3, y: 0.1, z: 1.6 }, { x: 0.3, y: -0.1, z: 1.6 },   // Right secondary
        
        // Fang tips (sharp points)
        { x: -0.6, y: -0.8, z: 2.0 }, { x: 0.6, y: -0.8, z: 2.0 }
    ];
    
    fangVoxels.forEach(pos => {
        addVoxel(headGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.FANG_WHITE, {
            emissive: new THREE.Color(0x333333),
            emissiveIntensity: 0.1
        });
    });

    // Epic flowing beard (keeping this!)
    const beardVoxels = [
        { x: -1, y: -0.2, z: 1.2 }, { x: 0, y: -0.2, z: 1.2 }, { x: 1, y: -0.2, z: 1.2 },
        { x: -1.5, y: -0.5, z: 1.1 }, { x: 0, y: -0.5, z: 1.1 }, { x: 1.5, y: -0.5, z: 1.1 },
        { x: -1, y: -1, z: 1 }, { x: 0, y: -1, z: 1 }, { x: 1, y: -1, z: 1 },
        { x: -0.5, y: -1.5, z: 0.8 }, { x: 0, y: -1.5, z: 0.8 }, { x: 0.5, y: -1.5, z: 0.8 },
        { x: 0, y: -2, z: 0.6 }, { x: 0, y: -2.5, z: 0.4 }, { x: 0, y: -3, z: 0.2 }
    ];
    
    beardVoxels.forEach(pos => {
        const isOuter = Math.abs(pos.x) > 1.2 || pos.y < -2;
        const color = isOuter ? CHRONARCH_COLORS.BEARD_WHITE : CHRONARCH_COLORS.BEARD_GRAY;
        addVoxel(headGroup, pos.x, pos.y, pos.z, color);
    });

    // BUILD BODY - Flowing robes
    console.log('🔮 Building robed body...');
    
    const bodyVoxels = [
        // Main torso
        { x: -3, y: 4, z: 0 }, { x: -2, y: 4, z: 0 }, { x: -1, y: 4, z: 0 }, { x: 0, y: 4, z: 0 }, 
        { x: 1, y: 4, z: 0 }, { x: 2, y: 4, z: 0 }, { x: 3, y: 4, z: 0 },
        { x: -3, y: 3, z: 0 }, { x: -2, y: 3, z: 0 }, { x: -1, y: 3, z: 0 }, { x: 0, y: 3, z: 0 }, 
        { x: 1, y: 3, z: 0 }, { x: 2, y: 3, z: 0 }, { x: 3, y: 3, z: 0 },
        { x: -2, y: 2, z: 0 }, { x: -1, y: 2, z: 0 }, { x: 0, y: 2, z: 0 }, { x: 1, y: 2, z: 0 }, { x: 2, y: 2, z: 0 },
        { x: -2, y: 1, z: 0 }, { x: -1, y: 1, z: 0 }, { x: 0, y: 1, z: 0 }, { x: 1, y: 1, z: 0 }, { x: 2, y: 1, z: 0 },
        { x: -1, y: 0, z: 0 }, { x: 0, y: 0, z: 0 }, { x: 1, y: 0, z: 0 },
        
        // Robe depth
        { x: -2, y: 3, z: 1 }, { x: -1, y: 3, z: 1 }, { x: 0, y: 3, z: 1 }, { x: 1, y: 3, z: 1 }, { x: 2, y: 3, z: 1 },
        { x: -1, y: 2, z: 1 }, { x: 0, y: 2, z: 1 }, { x: 1, y: 2, z: 1 },
        { x: 0, y: 1, z: 1 }
    ];
    
    bodyVoxels.forEach(pos => {
        const isInner = pos.z >= 1;
        const color = isInner ? CHRONARCH_COLORS.ROBE_DARK : CHRONARCH_COLORS.ROBE_PURPLE;
        addVoxel(bodyGroup, pos.x, pos.y, pos.z, color);
    });

    // BUILD ARMS - Demonic arms with claws
    console.log('🔮 Building demonic arms...');
    
    // Left arm
    const leftArmVoxels = [
        { x: 0, y: 0, z: 0 }, { x: 0, y: -1, z: 0 }, { x: 0, y: -2, z: 0 }, { x: 0, y: -3, z: 0 },
        { x: -1, y: -4, z: 0 }, { x: -1, y: -5, z: 0 }, { x: -1, y: -6, z: 0 }
    ];
    
    leftArmVoxels.forEach(pos => {
        addVoxel(leftArmGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.ROBE_PURPLE);
    });

    // Left hand with claws
    const leftHandVoxels = [
        { x: -1.5, y: -7, z: 0 }, { x: -1, y: -7, z: 0 }, // Palm
        { x: -2, y: -7.5, z: 0 }, { x: -1.5, y: -8, z: 0 }, { x: -1, y: -8, z: 0 }, { x: -0.5, y: -7.5, z: 0 } // Fingers
    ];
    
    leftHandVoxels.forEach(pos => {
        addVoxel(leftArmGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.FLESH_DEMONIC);
    });

    // Left claws
    const leftClawVoxels = [
        { x: -2.2, y: -7.7, z: 0 }, { x: -1.7, y: -8.2, z: 0 }, { x: -1.2, y: -8.2, z: 0 }, { x: -0.7, y: -7.7, z: 0 }
    ];
    
    leftClawVoxels.forEach(pos => {
        addVoxel(leftArmGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.CLAW_BONE);
    });

    // Right arm (staff-holding)
    const rightArmVoxels = [
        { x: 0, y: 0, z: 0 }, { x: 0, y: -1, z: 0 }, { x: 0, y: -2, z: 0 }, { x: 0, y: -3, z: 0 },
        { x: 1, y: -4, z: 0 }, { x: 1, y: -5, z: 0 }, { x: 1, y: -6, z: 0 }
    ];
    
    rightArmVoxels.forEach(pos => {
        addVoxel(rightArmGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.ROBE_PURPLE);
    });

    // Right hand gripping staff
    const rightHandVoxels = [
        { x: 1.5, y: -7, z: 0 }, { x: 1, y: -7, z: 0 }, // Palm
        { x: 2, y: -7.5, z: 0 }, { x: 1.5, y: -8, z: 0 }, { x: 1, y: -8, z: 0 }, { x: 0.5, y: -7.5, z: 0 } // Fingers
    ];
    
    rightHandVoxels.forEach(pos => {
        addVoxel(rightArmGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.FLESH_DEMONIC);
    });

    // BUILD LEGS - Robed legs
    console.log('🔮 Building legs...');
    
    // Left leg
    const leftLegVoxels = [
        { x: 0, y: 0, z: 0 }, { x: 0, y: -1, z: 0 }, { x: 0, y: -2, z: 0 }, { x: 0, y: -3, z: 0 },
        { x: 0, y: -4, z: 0 }, { x: 0, y: -5, z: 0 }, { x: 0, y: -6, z: 0 }
    ];
    
    leftLegVoxels.forEach(pos => {
        const isBoot = pos.y <= -5;
        const color = isBoot ? CHRONARCH_COLORS.BELT_LEATHER : CHRONARCH_COLORS.ROBE_DARK;
        addVoxel(leftLegGroup, pos.x, pos.y, pos.z, color);
    });

    // Right leg
    const rightLegVoxels = [
        { x: 0, y: 0, z: 0 }, { x: 0, y: -1, z: 0 }, { x: 0, y: -2, z: 0 }, { x: 0, y: -3, z: 0 },
        { x: 0, y: -4, z: 0 }, { x: 0, y: -5, z: 0 }, { x: 0, y: -6, z: 0 }
    ];
    
    rightLegVoxels.forEach(pos => {
        const isBoot = pos.y <= -5;
        const color = isBoot ? CHRONARCH_COLORS.BELT_LEATHER : CHRONARCH_COLORS.ROBE_DARK;
        addVoxel(rightLegGroup, pos.x, pos.y, pos.z, color);
    });

    // BUILD STAFF - Magnificent crystalline staff
    console.log('🔮 Building temporal staff...');
    
    // Staff shaft
    const staffVoxels = [
        { x: 0, y: 0, z: 0 }, { x: 0, y: 1, z: 0 }, { x: 0, y: 2, z: 0 }, { x: 0, y: 3, z: 0 },
        { x: 0, y: 4, z: 0 }, { x: 0, y: 5, z: 0 }, { x: 0, y: 6, z: 0 }, { x: 0, y: 7, z: 0 },
        { x: 0, y: 8, z: 0 }, { x: 0, y: 9, z: 0 }, { x: 0, y: 10, z: 0 }
    ];
    
    staffVoxels.forEach(pos => {
        addVoxel(staffGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.STAFF_WOOD);
    });

    // Crystal staff top
    const crystalTopVoxels = [
        { x: 0, y: 11, z: 0 }, { x: 0, y: 12, z: 0 }, { x: 0, y: 13, z: 0 },
        { x: -1, y: 11, z: 0 }, { x: 1, y: 11, z: 0 }, { x: 0, y: 11, z: 1 }, { x: 0, y: 11, z: -1 },
        { x: 0, y: 14, z: 0 }
    ];
    
    crystalTopVoxels.forEach(pos => {
        addVoxel(staffGroup, pos.x, pos.y, pos.z, CHRONARCH_COLORS.STAFF_CRYSTAL, {
            emissive: new THREE.Color(0x8A2BE2),
            emissiveIntensity: 0.8,
            transparent: true,
            opacity: 0.85
        });
    });

    // Add all groups to visual group
    visualGroup.add(bodyGroup);
    visualGroup.add(headGroup);
    visualGroup.add(leftArmGroup);
    visualGroup.add(rightArmGroup);
    visualGroup.add(leftLegGroup);
    visualGroup.add(rightLegGroup);
    visualGroup.add(staffGroup);

    // Add visual group to final group
    finalGroup.add(visualGroup);

    // Apply scale
    finalGroup.scale.set(safeScale, safeScale, safeScale);

    // Store animation data
    finalGroup.userData = {
        bodyParts: {
            body: bodyGroup,
            head: headGroup,
            leftArm: leftArmGroup,
            rightArm: rightArmGroup,
            leftLeg: leftLegGroup,
            rightLeg: rightLegGroup,
            staff: staffGroup
        },
        health: 750,
        detectionRange: 12.0,
        animationComplexity: 'epic_boss'
    };

    console.log(`🔮 CHRONARCH REBUILD COMPLETE - Clean demonic cyclops boss created`);
    console.log(`🔮 Model facing: Forward (positive Z coordinates like Nairabos)`);
    console.log(`🔮 Features: Single red cyclops eye, demonic horns, fangs, claws, epic beard, flowing robes`);
    
    return finalGroup;
}