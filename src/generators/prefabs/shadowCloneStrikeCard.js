import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Shadow Clone Strike Card Prefab
 * Creates shadow clones that strike enemies
 */

// Shadow clone specific colors
const SHADOW_COLORS = {
    SHADOW_BLACK: 0x1A1A1A,        // Deep shadow black core
    DARK_PURPLE: 0x4B0082,         // Dark purple shadow energy
    SHADOW_GRAY: 0x2F2F2F,         // Gray shadow forms
    VOID_BLUE: 0x191970,           // Midnight blue void energy
    PHANTOM_SILVER: 0x696969,      // Silver phantom outlines
    DARKNESS_RED: 0x8B0000         // Dark red shadow strikes
};

/**
 * Create a shadow clone strike card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The shadow clone strike card 3D model
 */
export function createShadowCloneStrikeCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ShadowCloneStrikeCard';

    // Shadow clone materials
    const shadowBlackMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_BLACK,
        emissive: SHADOW_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const darkPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.DARK_PURPLE,
        emissive: SHADOW_COLORS.DARK_PURPLE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_GRAY,
        emissive: SHADOW_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const voidBlueMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.VOID_BLUE,
        emissive: SHADOW_COLORS.VOID_BLUE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.75
    });

    const phantomSilverMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.PHANTOM_SILVER,
        emissive: SHADOW_COLORS.PHANTOM_SILVER,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const darknessRedMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.DARKNESS_RED,
        emissive: SHADOW_COLORS.DARKNESS_RED,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.85
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central shadow figure (main striking clone)
    const centralShadowVoxels = [
        // Shadow figure core (humanoid silhouette)
        { x: 0.0, y: 0.16, z: 0, material: shadowBlackMaterial }, // Head
        { x: 0.0, y: 0.08, z: 0, material: shadowBlackMaterial }, // Upper torso
        { x: 0.0, y: 0.0, z: 0, material: shadowBlackMaterial },  // Lower torso
        { x: 0.0, y: -0.08, z: 0, material: shadowBlackMaterial }, // Hips
        { x: 0.0, y: -0.16, z: 0, material: shadowBlackMaterial }, // Upper legs
        { x: 0.0, y: -0.24, z: 0, material: shadowBlackMaterial }, // Lower legs
        
        // Arms (striking pose)
        { x: -0.08, y: 0.08, z: 0, material: shadowBlackMaterial }, // Left shoulder
        { x: -0.12, y: 0.04, z: 0.04, material: shadowBlackMaterial }, // Left arm
        { x: -0.16, y: 0.0, z: 0.08, material: darknessRedMaterial }, // Left strike
        { x: 0.08, y: 0.08, z: 0, material: shadowBlackMaterial }, // Right shoulder
        { x: 0.12, y: 0.04, z: -0.04, material: shadowBlackMaterial }, // Right arm
        { x: 0.16, y: 0.0, z: -0.08, material: darknessRedMaterial }, // Right strike
        
        // Shadow energy aura
        { x: -0.04, y: 0.20, z: 0.04, material: darkPurpleMaterial },
        { x: 0.04, y: 0.20, z: -0.04, material: darkPurpleMaterial },
        { x: -0.06, y: 0.12, z: 0.06, material: voidBlueMaterial },
        { x: 0.06, y: 0.12, z: -0.06, material: voidBlueMaterial },
        { x: -0.04, y: 0.04, z: 0.08, material: darkPurpleMaterial },
        { x: 0.04, y: 0.04, z: -0.08, material: darkPurpleMaterial },
        { x: -0.08, y: -0.04, z: 0.06, material: voidBlueMaterial },
        { x: 0.08, y: -0.04, z: -0.06, material: voidBlueMaterial },
        { x: -0.04, y: -0.12, z: 0.04, material: darkPurpleMaterial },
        { x: 0.04, y: -0.12, z: -0.04, material: darkPurpleMaterial },
        { x: -0.06, y: -0.20, z: 0.06, material: voidBlueMaterial },
        { x: 0.06, y: -0.20, z: -0.06, material: voidBlueMaterial }
    ];

    // Shadow clone duplicates (additional striking figures)
    const shadowClonesVoxels = [
        // Clone 1 (left side)
        { x: -0.24, y: 0.12, z: 0.12, material: shadowGrayMaterial }, // Head
        { x: -0.24, y: 0.04, z: 0.12, material: shadowGrayMaterial }, // Torso
        { x: -0.24, y: -0.04, z: 0.12, material: shadowGrayMaterial }, // Body
        { x: -0.24, y: -0.12, z: 0.12, material: shadowGrayMaterial }, // Legs
        { x: -0.28, y: 0.08, z: 0.16, material: phantomSilverMaterial }, // Arm
        { x: -0.32, y: 0.04, z: 0.20, material: darknessRedMaterial }, // Strike
        
        // Clone 2 (right side)
        { x: 0.24, y: 0.12, z: -0.12, material: shadowGrayMaterial }, // Head
        { x: 0.24, y: 0.04, z: -0.12, material: shadowGrayMaterial }, // Torso
        { x: 0.24, y: -0.04, z: -0.12, material: shadowGrayMaterial }, // Body
        { x: 0.24, y: -0.12, z: -0.12, material: shadowGrayMaterial }, // Legs
        { x: 0.28, y: 0.08, z: -0.16, material: phantomSilverMaterial }, // Arm
        { x: 0.32, y: 0.04, z: -0.20, material: darknessRedMaterial }, // Strike
        
        // Clone 3 (behind)
        { x: 0.0, y: 0.08, z: -0.24, material: shadowGrayMaterial }, // Head
        { x: 0.0, y: 0.0, z: -0.24, material: shadowGrayMaterial }, // Torso
        { x: 0.0, y: -0.08, z: -0.24, material: shadowGrayMaterial }, // Body
        { x: 0.0, y: -0.16, z: -0.24, material: shadowGrayMaterial }, // Legs
        { x: 0.04, y: 0.04, z: -0.28, material: phantomSilverMaterial }, // Arm
        { x: 0.08, y: 0.0, z: -0.32, material: darknessRedMaterial }, // Strike
        
        // Clone 4 (front)
        { x: 0.0, y: 0.08, z: 0.24, material: shadowGrayMaterial }, // Head
        { x: 0.0, y: 0.0, z: 0.24, material: shadowGrayMaterial }, // Torso
        { x: 0.0, y: -0.08, z: 0.24, material: shadowGrayMaterial }, // Body
        { x: 0.0, y: -0.16, z: 0.24, material: shadowGrayMaterial }, // Legs
        { x: -0.04, y: 0.04, z: 0.28, material: phantomSilverMaterial }, // Arm
        { x: -0.08, y: 0.0, z: 0.32, material: darknessRedMaterial } // Strike
    ];

    // Shadow weapons (dark blades and strikes)
    const shadowWeaponsVoxels = [
        // Central figure weapons
        { x: -0.20, y: 0.0, z: 0.12, material: darknessRedMaterial },
        { x: -0.24, y: -0.04, z: 0.16, material: darknessRedMaterial },
        { x: 0.20, y: 0.0, z: -0.12, material: darknessRedMaterial },
        { x: 0.24, y: -0.04, z: -0.16, material: darknessRedMaterial },
        
        // Clone weapons (extending strikes)
        { x: -0.36, y: 0.0, z: 0.24, material: voidBlueMaterial },
        { x: -0.40, y: -0.04, z: 0.28, material: voidBlueMaterial },
        { x: 0.36, y: 0.0, z: -0.24, material: voidBlueMaterial },
        { x: 0.40, y: -0.04, z: -0.28, material: voidBlueMaterial },
        { x: 0.12, y: 0.0, z: -0.36, material: voidBlueMaterial },
        { x: 0.16, y: -0.04, z: -0.40, material: voidBlueMaterial },
        { x: -0.12, y: 0.0, z: 0.36, material: voidBlueMaterial },
        { x: -0.16, y: -0.04, z: 0.40, material: voidBlueMaterial },
        
        // Weapon energy trails
        { x: -0.28, y: 0.08, z: 0.20, material: phantomSilverMaterial },
        { x: 0.28, y: 0.08, z: -0.20, material: phantomSilverMaterial },
        { x: 0.08, y: 0.08, z: -0.28, material: phantomSilverMaterial },
        { x: -0.08, y: 0.08, z: 0.28, material: phantomSilverMaterial }
    ];

    // Shadow essence (dark energy particles)
    const shadowEssenceVoxels = [
        // Inner shadow layer
        { x: -0.12, y: 0.24, z: 0.08, material: darkPurpleMaterial },
        { x: 0.12, y: 0.24, z: 0.08, material: darkPurpleMaterial },
        { x: 0.12, y: -0.28, z: 0.08, material: darkPurpleMaterial },
        { x: -0.12, y: -0.28, z: 0.08, material: darkPurpleMaterial },
        
        // Outer shadow layer
        { x: -0.20, y: 0.28, z: 0.16, material: voidBlueMaterial },
        { x: 0.20, y: 0.28, z: 0.16, material: voidBlueMaterial },
        { x: 0.20, y: -0.32, z: 0.16, material: voidBlueMaterial },
        { x: -0.20, y: -0.32, z: 0.16, material: voidBlueMaterial },
        
        // Floating shadow particles
        { x: -0.28, y: 0.20, z: 0.24, material: shadowGrayMaterial },
        { x: 0.28, y: 0.16, z: 0.24, material: shadowGrayMaterial },
        { x: 0.20, y: 0.28, z: 0.20, material: phantomSilverMaterial },
        { x: -0.16, y: 0.28, z: 0.20, material: phantomSilverMaterial },
        { x: 0.24, y: -0.24, z: 0.28, material: shadowGrayMaterial },
        { x: -0.24, y: -0.20, z: 0.28, material: shadowGrayMaterial },
        { x: 0.16, y: -0.28, z: 0.24, material: phantomSilverMaterial },
        { x: -0.20, y: -0.28, z: 0.24, material: phantomSilverMaterial },
        
        // Distant shadow energy
        { x: -0.32, y: 0.24, z: 0.32, material: darkPurpleMaterial },
        { x: 0.32, y: 0.26, z: 0.32, material: darkPurpleMaterial },
        { x: 0.24, y: 0.32, z: 0.28, material: voidBlueMaterial },
        { x: -0.26, y: 0.32, z: 0.28, material: voidBlueMaterial },
        { x: 0.32, y: -0.24, z: 0.32, material: shadowGrayMaterial },
        { x: -0.32, y: -0.26, z: 0.32, material: shadowGrayMaterial },
        { x: 0.26, y: -0.32, z: 0.28, material: phantomSilverMaterial },
        { x: -0.24, y: -0.32, z: 0.28, material: phantomSilverMaterial }
    ];

    // Create central shadow group
    const centralShadowGroup = new THREE.Group();
    centralShadowGroup.name = 'centralShadow';

    // Add central shadow voxels
    centralShadowVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        centralShadowGroup.add(mesh);
    });

    // Create shadow clones group
    const shadowClonesGroup = new THREE.Group();
    shadowClonesGroup.name = 'shadowClones';

    // Add shadow clones voxels
    shadowClonesVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.clonePhase = index * 0.1; // Stagger animation
        shadowClonesGroup.add(mesh);
    });

    // Create shadow weapons group
    const shadowWeaponsGroup = new THREE.Group();
    shadowWeaponsGroup.name = 'shadowWeapons';

    // Add shadow weapons voxels
    shadowWeaponsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.weaponPhase = index * 0.15; // Stagger animation
        shadowWeaponsGroup.add(mesh);
    });

    // Create shadow essence group
    const shadowEssenceGroup = new THREE.Group();
    shadowEssenceGroup.name = 'shadowEssence';

    // Add shadow essence voxels
    shadowEssenceVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.essencePhase = index * 0.05; // Stagger animation
        shadowEssenceGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(centralShadowGroup);
    cardGroup.add(shadowClonesGroup);
    cardGroup.add(shadowWeaponsGroup);
    cardGroup.add(shadowEssenceGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        shadowPulse: 0,
        strikePulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update shadow clone strike card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateShadowCloneStrikeCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.shadowPulse += deltaTime * 3.0; // Shadow pulse speed
    cardGroup.userData.strikePulse += deltaTime * 5.0; // Strike pulse speed

    const time = cardGroup.userData.animationTime;
    const shadowPulse = cardGroup.userData.shadowPulse;
    const strikePulse = cardGroup.userData.strikePulse;

    // Animate central shadow (dark pulsing)
    const centralShadowGroup = cardGroup.getObjectByName('centralShadow');
    if (centralShadowGroup) {
        // Shadow core pulsing (ominous darkness)
        const shadowIntensity = 0.8 + Math.sin(shadowPulse * 2.0) * 0.3;
        centralShadowGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * shadowIntensity;
            }
        });
    }

    // Animate shadow clones (phase shifting)
    const shadowClonesGroup = cardGroup.getObjectByName('shadowClones');
    if (shadowClonesGroup) {
        // Shadow clone phase shifting
        shadowClonesGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.clonePhase !== undefined) {
                const cloneTime = shadowPulse + mesh.userData.clonePhase;
                
                // Phase shifting motion (shadow displacement)
                const phaseX = Math.sin(cloneTime * 1.5) * 0.001;
                const phaseY = Math.cos(cloneTime * 1.8) * 0.001;
                const phaseZ = Math.sin(cloneTime * 1.2) * 0.001;
                
                mesh.position.x = mesh.userData.originalPosition.x + phaseX;
                mesh.position.y = mesh.userData.originalPosition.y + phaseY;
                mesh.position.z = mesh.userData.originalPosition.z + phaseZ;
                
                // Clone shadow intensity pulsing
                const clonePulse = 0.7 + Math.sin(cloneTime * 3.0) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * clonePulse;
                }
                
                // Clone opacity flickering (shadow materializing)
                const cloneOpacity = 0.6 + Math.sin(cloneTime * 4.0) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * cloneOpacity;
                }
            }
        });
    }

    // Animate shadow weapons (striking motion)
    const shadowWeaponsGroup = cardGroup.getObjectByName('shadowWeapons');
    if (shadowWeaponsGroup) {
        // Shadow weapon striking motion
        shadowWeaponsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.weaponPhase !== undefined) {
                const weaponTime = strikePulse + mesh.userData.weaponPhase;
                
                // Striking motion (extending and retracting)
                const strikeExtension = Math.sin(weaponTime * 2.5) * 0.003;
                const weaponDirection = mesh.userData.originalPosition;
                const magnitude = Math.sqrt(
                    weaponDirection.x * weaponDirection.x + 
                    weaponDirection.z * weaponDirection.z
                );
                
                if (magnitude > 0) {
                    const normalizedX = weaponDirection.x / magnitude;
                    const normalizedZ = weaponDirection.z / magnitude;
                    
                    mesh.position.x = mesh.userData.originalPosition.x + normalizedX * strikeExtension;
                    mesh.position.z = mesh.userData.originalPosition.z + normalizedZ * strikeExtension;
                }
                
                // Weapon strike intensity flashing
                const strikeFlash = 1.0 + Math.sin(weaponTime * 6.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * strikeFlash;
                }
            }
        });
    }

    // Animate shadow essence (dark energy swirling)
    const shadowEssenceGroup = cardGroup.getObjectByName('shadowEssence');
    if (shadowEssenceGroup) {
        shadowEssenceGroup.rotation.y = time * 0.6; // Slow rotation
        
        // Shadow essence dark energy swirling
        shadowEssenceGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.essencePhase !== undefined) {
                const essenceTime = shadowPulse + mesh.userData.essencePhase;
                
                // Dark energy swirling motion
                const swirlX = Math.sin(essenceTime * 2.0) * 0.002;
                const swirlY = Math.cos(essenceTime * 1.5) * 0.002;
                const swirlZ = Math.sin(essenceTime * 2.5) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + swirlX;
                mesh.position.y = mesh.userData.originalPosition.y + swirlY;
                mesh.position.z = mesh.userData.originalPosition.z + swirlZ;
                
                // Essence shadow energy pulsing
                const essencePulse = 0.5 + Math.sin(essenceTime * 4.0) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * essencePulse;
                }
                
                // Essence scale variation (energy fluctuation)
                const essenceScale = 0.9 + Math.sin(essenceTime * 3.5) * 0.2;
                mesh.scale.setScalar(essenceScale);
            }
        });
    }

    // Overall shadow figure pulsing (dark energy)
    const shadowPulseScale = 1 + Math.sin(time * 2.5) * 0.05;
    cardGroup.scale.setScalar(0.8 * shadowPulseScale);
}

// Export the shadow clone strike card data for the loot system
export const SHADOW_CLONE_STRIKE_CARD_DATA = {
    name: 'Shadow Clone Strike',
    description: 'Summons shadow clones that appear around enemies and strike simultaneously, dealing dark damage from multiple directions with perfect coordination.',
    category: 'card',
    rarity: 'epic',
    effect: 'shadow_clone_strike',
    effectValue: 4, // Number of shadow clones summoned
    createFunction: createShadowCloneStrikeCard,
    updateFunction: updateShadowCloneStrikeCardAnimation,
    voxelModel: 'shadow_clone_strike_card',
    glow: {
        color: 0x4B0082,
        intensity: 1.3
    }
};

export default createShadowCloneStrikeCard;