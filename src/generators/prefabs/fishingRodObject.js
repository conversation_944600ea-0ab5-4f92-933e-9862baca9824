import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a fishing rod object (voxel-style, interactable like chest)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Fishing rod object group
 */
export function createFishingRodObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);

    // Use consistent voxel size
    const rodVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('fishing_rod_voxel', () =>
        new THREE.BoxGeometry(rodVoxelSize, rodVoxelSize, rodVoxelSize)
    );

    // Create voxel-style rod base (wooden platform)
    const baseMaterial = _getMaterialByHex_Cached('8B4513', {
        emissive: new THREE.Color(0x1a0f08),
        emissiveIntensity: 0.1
    });

    // Wooden platform base (2x2)
    for (let x = -0.5; x <= 0.5; x += 1) {
        for (let z = -0.5; z <= 0.5; z += 1) {
            const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
            baseVoxel.position.set(
                x * rodVoxelSize,
                rodVoxelSize * 0.5,
                z * rodVoxelSize
            );
            baseVoxel.userData.isFloorObject = true;
            baseVoxel.userData.hasCollision = true;
            baseVoxel.castShadow = true;
            baseVoxel.receiveShadow = true;
            group.add(baseVoxel);
        }
    }

    // Fishing rod handle (vertical brown stick)
    const handleMaterial = _getMaterialByHex_Cached('654321', {
        emissive: new THREE.Color(0x0f0a05),
        emissiveIntensity: 0.1
    });

    const handleGeometry = getOrCreateGeometry('fishing_rod_handle', () =>
        new THREE.BoxGeometry(rodVoxelSize * 0.3, rodVoxelSize * 3, rodVoxelSize * 0.3)
    );

    const handle = new THREE.Mesh(handleGeometry, handleMaterial);
    handle.position.set(0, rodVoxelSize * 2, 0);
    handle.userData.isFloorObject = true;
    handle.userData.hasCollision = true;
    handle.castShadow = true;
    handle.receiveShadow = true;
    group.add(handle);

    // Fishing line (thin gray line extending toward pond)
    const lineMaterial = _getMaterialByHex_Cached('888888', {
        emissive: new THREE.Color(0x111111),
        emissiveIntensity: 0.1
    });

    const lineGeometry = getOrCreateGeometry('fishing_line', () =>
        new THREE.BoxGeometry(rodVoxelSize * 0.1, rodVoxelSize * 0.1, rodVoxelSize * 2)
    );

    const line = new THREE.Mesh(lineGeometry, lineMaterial);
    line.position.set(0, rodVoxelSize * 3.5, rodVoxelSize * 1);
    line.userData.isFloorObject = true;
    line.castShadow = true;
    line.receiveShadow = true;
    group.add(line);

    // Set up group properties with chest-like interaction
    group.userData = {
        ...(options.userData || {}),
        objectType: 'fishing_rod',
        isInteractable: true,
        isEventObject: true,
        rodId: options.userData?.rodId || 'mysterious_fishing_rod',
        interactionRange: 3.0, // Match ChestInteractionSystem
        isChest: true, // Helps with interaction detection
        chestType: 'event_object',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false, // Fishing rod should not be destructible by projectiles
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: rodVoxelSize
    };

    // Add collision detection to all fishing rod components
    group.traverse(child => {
        if (child.isMesh) {
            child.userData.isFloorObject = true;
            child.userData.hasCollision = true;
            child.userData.objectType = 'fishing_rod';
        }
    });

    group.name = 'fishing_rod';

    console.log('[FishingRodObject] ✅ Created voxel-style fishing rod with chest-like interaction');
    return group;
}
