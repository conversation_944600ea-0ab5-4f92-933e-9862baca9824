import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Circular Chamber Wall for the Eye of Judgment room
 *
 * Creates curved stone walls that form a circular chamber.
 * Uses similar materials to mystical stone but arranged in curved segments.
 */

// Judgment chamber materials (darker, more ominous than mystical stone)
const JUDGMENT_WALL_MATERIALS = [
    _getMaterialByHex_Cached('3C3C3C'), // Dark gray (primary)
    _getMaterialByHex_Cached('4A4A4A'), // Medium gray (secondary)
    _getMaterialByHex_Cached('2F2F2F'), // Darker gray (shadows)
    _getMaterialByHex_Cached('555555'), // Light gray (highlights)
    _getMaterialByHex_Cached('1A1A2A'), // Dark blue-gray (mystical shadows)
    _getMaterialByHex_Cached('4B0082'), // Indigo (mystical accents)
    _getMaterialByHex_Cached('2F1B69')  // Dark indigo (deep mystical)
];

/**
 * Get random judgment wall material
 */
function getRandomJudgmentWallMat() {
    const rand = Math.random();
    if (rand < 0.40) return JUDGMENT_WALL_MATERIALS[0]; // Primary dark gray
    if (rand < 0.70) return JUDGMENT_WALL_MATERIALS[1]; // Medium gray
    if (rand < 0.85) return JUDGMENT_WALL_MATERIALS[2]; // Darker shadows
    if (rand < 0.95) return JUDGMENT_WALL_MATERIALS[3]; // Light highlights
    if (rand < 0.98) return JUDGMENT_WALL_MATERIALS[4]; // Mystical blue-gray
    return JUDGMENT_WALL_MATERIALS[5]; // Indigo accents
}

/**
 * Create circular chamber wall segments
 * Creates curved wall segments that form a circular room
 */
export function createCircularChamberWallSegment(radius, height, wallThickness, segments, roomData) {
    console.log(`[CircularChamberWall] Creating circular wall: radius=${radius}, height=${height}, segments=${segments}`);

    // Input validation
    if (!radius || !height || !segments || radius <= 0 || height <= 0 || segments <= 0) {
        console.error(`createCircularChamberWallSegment received invalid parameters: r=${radius}, h=${height}, s=${segments}`);
        return { group: new THREE.Group() };
    }

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    // Wall scaling system (same as mystical stone walls)
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;

    // Calculate dimensions
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numY_env = Math.ceil(numY / WALL_SCALE);
    const safeNumYEnv = Math.max(1, numY_env);

    // Use seeded PRNG for consistent patterns
    const roomSeed = roomData ? roomData.id * 37 + 23 : Date.now();
    const random = mulberry32(roomSeed);

    // Get cached wall voxel geometry
    const wallGeo = getOrCreateGeometry(
        `circular_wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, wallThickness)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };

    // Generate circular wall voxels
    let voxelsAdded = 0;
    const angleStep = (Math.PI * 2) / segments;

    for (let segment = 0; segment < segments; segment++) {
        const angle = segment * angleStep;
        const wallX = Math.cos(angle) * radius;
        const wallZ = Math.sin(angle) * radius;

        // Calculate wall normal for thickness direction
        const normalX = Math.cos(angle);
        const normalZ = Math.sin(angle);

        for (let ey = 0; ey < safeNumYEnv; ey++) {
            // 90% chance for wall block (slightly higher than normal walls for solidity)
            if (random() > 0.1) {
                const blockMaterial = getRandomJudgmentWallMat();

                const baseY = ey * wallVoxelSize - (height / 2);

                // Add variations for textured appearance
                const depthVariation = (random() - 0.5) * 0.1;
                const heightVariation = (random() - 0.5) * 0.06;
                const radialVariation = (random() - 0.5) * 0.05;

                // Calculate final position with variations
                const finalRadius = radius + radialVariation;
                const finalX = Math.cos(angle) * finalRadius + normalX * depthVariation;
                const finalZ = Math.sin(angle) * finalRadius + normalZ * depthVariation;
                const finalY = baseY + heightVariation;

                // Main wall block
                tempMatrix.makeTranslation(finalX, finalY, finalZ);
                
                // Rotate block to face outward from center
                const rotationMatrix = new THREE.Matrix4().makeRotationY(angle + Math.PI);
                tempMatrix.multiply(rotationMatrix);
                
                // Add slight random rotation for texture
                const randomRotY = (random() - 0.5) * 0.08;
                const randomRotZ = (random() - 0.5) * 0.06;
                tempMatrix.multiply(new THREE.Matrix4().makeRotationY(randomRotY));
                tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(randomRotZ));
                
                addGeometry(wallGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // Add inner face (inside of circular wall)
                const innerThickness = wallThickness * 0.4;
                const innerWallGeo = getOrCreateGeometry(
                    `circular_inner_wall_${wallVoxelSize.toFixed(4)}_${innerThickness.toFixed(4)}`,
                    () => new THREE.BoxGeometry(wallVoxelSize * 1.1, wallVoxelSize, innerThickness)
                );
                
                const innerX = finalX - normalX * (wallThickness * 0.7);
                const innerZ = finalZ - normalZ * (wallThickness * 0.7);
                
                tempMatrix.makeTranslation(innerX, finalY, innerZ);
                tempMatrix.multiply(rotationMatrix);
                addGeometry(innerWallGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // Add outer face (outside of circular wall)
                const outerX = finalX + normalX * (wallThickness * 0.7);
                const outerZ = finalZ + normalZ * (wallThickness * 0.7);
                
                tempMatrix.makeTranslation(outerX, finalY, outerZ);
                tempMatrix.multiply(rotationMatrix);
                addGeometry(innerWallGeo, blockMaterial, tempMatrix);
                voxelsAdded++;

                // Add wall caps at the top
                if (ey === safeNumYEnv - 1) {
                    const capGeo = getOrCreateGeometry(
                        `circular_wall_cap_${wallVoxelSize.toFixed(4)}_${wallThickness.toFixed(4)}`,
                        () => new THREE.BoxGeometry(wallVoxelSize * 1.3, VOXEL_SIZE * 0.3, wallThickness * 1.2)
                    );

                    const capY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.15;
                    tempMatrix.makeTranslation(finalX, capY, finalZ);
                    tempMatrix.multiply(rotationMatrix);
                    addGeometry(capGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }
            }
        }
    }

    // Add decorative elements - buttresses at key points
    const buttressCount = 8; // 8 buttresses around the circle
    const buttressAngleStep = (Math.PI * 2) / buttressCount;
    
    for (let i = 0; i < buttressCount; i++) {
        const buttressAngle = i * buttressAngleStep;
        const buttressX = Math.cos(buttressAngle) * (radius + wallThickness * 0.5);
        const buttressZ = Math.sin(buttressAngle) * (radius + wallThickness * 0.5);
        
        // Create buttress structure (3 voxels high, tapered)
        for (let by = 0; by < 3; by++) {
            const buttressMaterial = JUDGMENT_WALL_MATERIALS[2]; // Darker material for buttresses
            const buttressScale = 1.0 - (by * 0.2); // Taper as it goes up
            
            const buttressGeo = getOrCreateGeometry(
                `buttress_${by}_${buttressScale.toFixed(2)}`,
                () => new THREE.BoxGeometry(
                    wallVoxelSize * buttressScale,
                    wallVoxelSize,
                    wallThickness * 1.5 * buttressScale
                )
            );
            
            const buttressY = (by - 1) * wallVoxelSize;
            tempMatrix.makeTranslation(buttressX, buttressY, buttressZ);
            tempMatrix.multiply(new THREE.Matrix4().makeRotationY(buttressAngle + Math.PI));
            addGeometry(buttressGeo, buttressMaterial, tempMatrix);
            voxelsAdded++;
        }
    }

    // Add mystical accent stones at cardinal points
    const cardinalAngles = [0, Math.PI/2, Math.PI, 3*Math.PI/2];
    cardinalAngles.forEach(angle => {
        const accentX = Math.cos(angle) * (radius - wallThickness * 0.3);
        const accentZ = Math.sin(angle) * (radius - wallThickness * 0.3);
        
        // Create vertical accent line (5 voxels high)
        for (let ay = 0; ay < 5; ay++) {
            const accentMaterial = JUDGMENT_WALL_MATERIALS[5]; // Indigo mystical accent
            const accentY = (ay - 2) * wallVoxelSize;
            
            const accentGeo = getOrCreateGeometry(
                'mystical_accent_voxel',
                () => new THREE.BoxGeometry(wallVoxelSize * 0.6, wallVoxelSize * 0.8, wallVoxelSize * 0.6)
            );
            
            tempMatrix.makeTranslation(accentX, accentY, accentZ);
            addGeometry(accentGeo, accentMaterial, tempMatrix);
            voxelsAdded++;
        }
    });

    console.log(`[CircularChamberWall] Added ${voxelsAdded} voxels total for circular chamber.`);

    // Merge geometries by material with polygon offset
    const mortarHex = JUDGMENT_WALL_MATERIALS[2].color.getHexString(); // Darker material for depth

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for circular chamber wall: ${colorHex}`);
            continue;
        }

        // Clone material to apply polygon offset
        const finalMaterial = originalMaterial.clone();

        // Apply polygon offset to prevent z-fighting
        if (colorHex === mortarHex) {
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = -1.0;
            finalMaterial.polygonOffsetUnits = -1.0;
        } else {
            finalMaterial.polygonOffset = true;
            finalMaterial.polygonOffsetFactor = 1.0;
            finalMaterial.polygonOffsetUnits = 1.0;
        }

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            mesh.name = 'circularChamberWall';
            mesh.userData.isWall = true;
            mesh.userData.wallType = 'circular_chamber';
            mesh.userData.isCircularWall = true;
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for circular chamber wall, material: ${colorHex}`);
        }
    }

    // Set group properties
    finalGroup.userData.wallType = 'circular_chamber';
    finalGroup.userData.isWall = true;
    finalGroup.userData.isCircularWall = true;
    finalGroup.userData.radius = radius;
    finalGroup.userData.segments = segments;
    finalGroup.name = 'circularChamberWall';

    // Set shadow properties
    finalGroup.castShadow = true;
    finalGroup.receiveShadow = true;

    console.log(`[CircularChamberWall] ✅ Generated circular chamber wall with ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return {
        group: finalGroup,
        torchPositions: [] // No torches on judgment chamber walls
    };
}