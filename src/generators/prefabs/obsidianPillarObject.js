import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// Obsidian Pillar colors - dark volcanic glass
const OBSIDIAN_COLORS = {
    BLACK_OBSIDIAN: '0D0D0D',      // Very dark obsidian base
    DARK_OBSIDIAN: '1A1A1A',       // Dark obsidian
    OBSIDIAN_HIGHLIGHT: '262626',   // Lighter obsidian highlights
    RED_VEINS: '4A0000',            // Dark red veins through obsidian
    LAVA_GLOW: '8B0000',            // Emissive lava glow in cracks
    VOLCANIC_ASH: '2F2F2F',         // Volcanic ash details
    GLASS_SHEEN: '404040'           // Glassy obsidian sheen
};

// Pillar dimensions (following ancient pillar pattern)
const BASE_WIDTH_VOXELS = 3;       // Base width
const BASE_DEPTH_VOXELS = 3;       // Base depth  
const BASE_HEIGHT_VOXELS = 1;      // Base height
const SHAFT_WIDTH_VOXELS = 2;      // Shaft width
const SHAFT_DEPTH_VOXELS = 2;      // Shaft depth
const TOP_HEIGHT_VOXELS = 1;       // Top cap height

/**
 * Creates a tall obsidian pillar with volcanic glass appearance
 * @param {object} options - Options object
 * @param {number} options.seed - Random seed for variations
 * @param {number} options.wallHeight - Target height for the pillar
 * @returns {THREE.Group} The obsidian pillar object
 */
export function createObsidianPillarObject(options = {}) {
    const { seed = 12345, wallHeight = 5 } = options;
    const random = mulberry32(seed);
    const rng = () => random();
    
    const pillarGroup = new THREE.Group();
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    // Use large voxel size like other architectural elements
    const pillarVoxelSize = VOXEL_SIZE * 5.12; // Same size as ancient stone pillar
    const voxelGeo = getOrCreateGeometry('voxel_obsidian_pillar', () => 
        new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize)
    );

    // Helper to add a voxel
    const addVoxel = (x_vox, y_vox, z_vox, colorHex) => {
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const geo = voxelGeo.clone();
        tempMatrix.makeTranslation(
            x_vox * pillarVoxelSize,
            y_vox * pillarVoxelSize,
            z_vox * pillarVoxelSize
        );
        geo.applyMatrix4(tempMatrix);
        geometriesByMaterial[colorHex].push(geo);
    };

    // Calculate total height in voxels
    const totalHeightVoxels = Math.floor(wallHeight / pillarVoxelSize);
    const shaftHeightVoxels = Math.max(1, totalHeightVoxels - BASE_HEIGHT_VOXELS - TOP_HEIGHT_VOXELS);

    // Store voxel data for destruction
    const originalVoxels = [];
    const recordVoxel = (x_vox, y_vox, z_vox, colorHex) => {
        originalVoxels.push({ x: x_vox, y: y_vox, z: z_vox, c: colorHex });
        addVoxel(x_vox, y_vox, z_vox, colorHex);
    };

    console.log('🌋 Building obsidian pillar...');

    // Coordinate helpers
    const halfBaseW = (BASE_WIDTH_VOXELS - 1) / 2;
    const halfBaseD = (BASE_DEPTH_VOXELS - 1) / 2;
    const halfShaftW = (SHAFT_WIDTH_VOXELS - 1) / 2;
    const halfShaftD = (SHAFT_DEPTH_VOXELS - 1) / 2;

    // Build pillar base (wider foundation)
    for (let y = 0; y < BASE_HEIGHT_VOXELS; y++) {
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                // Create obsidian base with variations
                let colorHex = OBSIDIAN_COLORS.BLACK_OBSIDIAN; // Default to darkest obsidian

                // Add natural variation to obsidian
                if (rng() < 0.3) { // 30% chance for dark obsidian
                    colorHex = OBSIDIAN_COLORS.DARK_OBSIDIAN;
                } else if (rng() < 0.15) { // 15% chance for highlights
                    colorHex = OBSIDIAN_COLORS.OBSIDIAN_HIGHLIGHT;
                } else if (rng() < 0.1) { // 10% chance for red veins
                    colorHex = OBSIDIAN_COLORS.RED_VEINS;
                } else if (rng() < 0.05) { // 5% chance for glassy sheen
                    colorHex = OBSIDIAN_COLORS.GLASS_SHEEN;
                }

                recordVoxel(x, y, z, colorHex);
            }
        }
    }

    // Build pillar shaft (main column)
    for (let y = BASE_HEIGHT_VOXELS; y < BASE_HEIGHT_VOXELS + shaftHeightVoxels; y++) {
        for (let x = -halfShaftW; x <= halfShaftW; x++) {
            for (let z = -halfShaftD; z <= halfShaftD; z++) {
                // Create shaft with more dramatic obsidian patterns
                let colorHex = OBSIDIAN_COLORS.BLACK_OBSIDIAN;

                // Edge details for shaft
                const isEdge = (x === -halfShaftW || x === halfShaftW || 
                               z === -halfShaftD || z === halfShaftD);
                
                if (isEdge && rng() < 0.4) {
                    colorHex = OBSIDIAN_COLORS.DARK_OBSIDIAN; // Darker edges
                } else if (rng() < 0.25) {
                    colorHex = OBSIDIAN_COLORS.OBSIDIAN_HIGHLIGHT;
                } else if (rng() < 0.15) {
                    colorHex = OBSIDIAN_COLORS.RED_VEINS; // Red volcanic veins
                } else if (rng() < 0.08) {
                    colorHex = OBSIDIAN_COLORS.LAVA_GLOW; // Rare lava glow
                } else if (rng() < 0.12) {
                    colorHex = OBSIDIAN_COLORS.GLASS_SHEEN; // Glassy surfaces
                }

                recordVoxel(x, y, z, colorHex);
            }
        }
    }

    // Build pillar top/capital (decorative cap)
    const topStartY = BASE_HEIGHT_VOXELS + shaftHeightVoxels;
    for (let y = topStartY; y < topStartY + TOP_HEIGHT_VOXELS; y++) {
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                // Ornate top with more glass sheen and red veins
                let colorHex = OBSIDIAN_COLORS.OBSIDIAN_HIGHLIGHT; // Default to highlights for top

                if (rng() < 0.4) {
                    colorHex = OBSIDIAN_COLORS.GLASS_SHEEN; // More glassy top
                } else if (rng() < 0.3) {
                    colorHex = OBSIDIAN_COLORS.RED_VEINS; // Red veining
                } else if (rng() < 0.15) {
                    colorHex = OBSIDIAN_COLORS.LAVA_GLOW; // Rare lava glow accents
                } else if (rng() < 0.2) {
                    colorHex = OBSIDIAN_COLORS.DARK_OBSIDIAN;
                }

                recordVoxel(x, y, z, colorHex);
            }
        }
    }

    // Add volcanic detail accents randomly throughout shaft
    for (let i = 0; i < Math.floor(shaftHeightVoxels / 2); i++) {
        const accentY = BASE_HEIGHT_VOXELS + Math.floor(rng() * shaftHeightVoxels);
        const accentX = Math.floor(rng() * SHAFT_WIDTH_VOXELS) - halfShaftW;
        const accentZ = Math.floor(rng() * SHAFT_DEPTH_VOXELS) - halfShaftD;
        
        // Add lava glow accents
        recordVoxel(accentX, accentY, accentZ, OBSIDIAN_COLORS.LAVA_GLOW);
    }

    // Create merged geometries by material
    for (const [colorHex, geometries] of Object.entries(geometriesByMaterial)) {
        if (geometries.length === 0) continue;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        
        // Create material with special properties for obsidian
        let materialProps = {};
        if (colorHex === OBSIDIAN_COLORS.LAVA_GLOW) {
            materialProps = {
                emissive: new THREE.Color(0x8B0000),
                emissiveIntensity: 0.6
            };
        } else if (colorHex === OBSIDIAN_COLORS.GLASS_SHEEN) {
            materialProps = {
                metalness: 0.1,
                roughness: 0.1, // Very smooth glassy surface
                transparent: true,
                opacity: 0.9
            };
        } else if (colorHex === OBSIDIAN_COLORS.RED_VEINS) {
            materialProps = {
                emissive: new THREE.Color(0x220000),
                emissiveIntensity: 0.3
            };
        } else {
            materialProps = {
                metalness: 0.2,
                roughness: 0.7 // Matte obsidian finish
            };
        }

        const material = _getMaterialByHex_Cached(colorHex, materialProps);
        const mesh = new THREE.Mesh(mergedGeometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        pillarGroup.add(mesh);

        // Dispose individual geometries
        geometries.forEach(geo => geo.dispose());
    }

    // Store metadata for game systems
    pillarGroup.userData = {
        objectType: 'obsidian_pillar',
        isInteractable: false,
        originalVoxels: originalVoxels,
        voxelScale: pillarVoxelSize,
        isDestructible: true,
        destructionEffect: 'shatter', // Obsidian shatters dramatically
        health: 2, // Slightly tougher than regular pillars
        isArchitectural: true,
        materialType: 'obsidian'
    };

    pillarGroup.name = 'obsidian_pillar';

    console.log(`✅ Obsidian pillar created with ${originalVoxels.length} voxels (${totalHeightVoxels} height)`);
    return pillarGroup;
}

// Export for use in event room
export default { createObsidianPillarObject };