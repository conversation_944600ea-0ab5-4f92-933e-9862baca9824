import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Blood Pact Card Prefab
 * Creates a dark ritual sacrifice for immense power
 */

// Blood pact specific colors
const BLOOD_COLORS = {
    BLOOD_RED: 0x8B0000,         // Primary blood red
    CRIMSON_MAGIC: 0xDC143C,     // Bright crimson magic
    DARK_CRIMSON: 0x800000,      // Dark crimson sacrifice
    SHADOW_BLACK: 0x2F2F2F,      // Shadow darkness
    POWER_GOLD: 0xFFD700,        // Magical power gold
    RITUAL_PURPLE: 0x4B0082,     // Ritual circle purple
    SACRIFICE_ORANGE: 0xFF4500   // Sacrifice flame orange
};

/**
 * Create a blood pact card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The blood pact card 3D model
 */
export function createBloodPactCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'BloodPactCard';

    // Materials
    const bloodRedMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.BLOOD_RED,
        emissive: BLOOD_COLORS.BLOOD_RED,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const crimsonMagicMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.CRIMSON_MAGIC,
        emissive: BLOOD_COLORS.CRIMSON_MAGIC,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.8
    });

    const darkCrimsonMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.DARK_CRIMSON,
        emissive: BLOOD_COLORS.DARK_CRIMSON,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const shadowBlackMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.SHADOW_BLACK,
        emissive: BLOOD_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const powerGoldMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.POWER_GOLD,
        emissive: BLOOD_COLORS.POWER_GOLD,
        emissiveIntensity: 1.1,
        transparent: true,
        opacity: 0.8
    });

    const ritualPurpleMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.RITUAL_PURPLE,
        emissive: BLOOD_COLORS.RITUAL_PURPLE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.6
    });

    const sacrificeOrangeMaterial = new THREE.MeshLambertMaterial({
        color: BLOOD_COLORS.SACRIFICE_ORANGE,
        emissive: BLOOD_COLORS.SACRIFICE_ORANGE,
        emissiveIntensity: 1.3,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create ritual circle (outer ring)
    const ritualCircleVoxels = [
        // Outer circle
        { x: 0, y: 6, z: 0, material: bloodRedMaterial },
        { x: 2, y: 6, z: 0, material: bloodRedMaterial },
        { x: 4, y: 5, z: 0, material: bloodRedMaterial },
        { x: 5, y: 4, z: 0, material: bloodRedMaterial },
        { x: 6, y: 2, z: 0, material: bloodRedMaterial },
        { x: 6, y: 0, z: 0, material: bloodRedMaterial },
        { x: 6, y: -2, z: 0, material: bloodRedMaterial },
        { x: 5, y: -4, z: 0, material: bloodRedMaterial },
        { x: 4, y: -5, z: 0, material: bloodRedMaterial },
        { x: 2, y: -6, z: 0, material: bloodRedMaterial },
        { x: 0, y: -6, z: 0, material: bloodRedMaterial },
        { x: -2, y: -6, z: 0, material: bloodRedMaterial },
        { x: -4, y: -5, z: 0, material: bloodRedMaterial },
        { x: -5, y: -4, z: 0, material: bloodRedMaterial },
        { x: -6, y: -2, z: 0, material: bloodRedMaterial },
        { x: -6, y: 0, z: 0, material: bloodRedMaterial },
        { x: -6, y: 2, z: 0, material: bloodRedMaterial },
        { x: -5, y: 4, z: 0, material: bloodRedMaterial },
        { x: -4, y: 5, z: 0, material: bloodRedMaterial },
        { x: -2, y: 6, z: 0, material: bloodRedMaterial },
        
        // Inner ritual symbols
        { x: 0, y: 4, z: 0, material: ritualPurpleMaterial },
        { x: 3, y: 3, z: 0, material: ritualPurpleMaterial },
        { x: 4, y: 0, z: 0, material: ritualPurpleMaterial },
        { x: 3, y: -3, z: 0, material: ritualPurpleMaterial },
        { x: 0, y: -4, z: 0, material: ritualPurpleMaterial },
        { x: -3, y: -3, z: 0, material: ritualPurpleMaterial },
        { x: -4, y: 0, z: 0, material: ritualPurpleMaterial },
        { x: -3, y: 3, z: 0, material: ritualPurpleMaterial }
    ];

    // Create sacrifice altar (center)
    const sacrificeAltarVoxels = [
        // Central altar
        { x: 0, y: 0, z: 0, material: crimsonMagicMaterial },
        { x: -1, y: 0, z: 0, material: bloodRedMaterial },
        { x: 1, y: 0, z: 0, material: bloodRedMaterial },
        { x: 0, y: -1, z: 0, material: bloodRedMaterial },
        { x: 0, y: 1, z: 0, material: bloodRedMaterial },
        
        // Power emanations from altar
        { x: 0, y: 2, z: 0, material: powerGoldMaterial },
        { x: 0, y: -2, z: 0, material: powerGoldMaterial },
        { x: 2, y: 0, z: 0, material: powerGoldMaterial },
        { x: -2, y: 0, z: 0, material: powerGoldMaterial },
        
        // Sacrifice flames
        { x: -1, y: 1, z: 0, material: sacrificeOrangeMaterial },
        { x: 1, y: 1, z: 0, material: sacrificeOrangeMaterial },
        { x: -1, y: -1, z: 0, material: sacrificeOrangeMaterial },
        { x: 1, y: -1, z: 0, material: sacrificeOrangeMaterial }
    ];

    // Create blood magic streams
    const bloodMagicVoxels = [
        // Magic flowing toward center
        { x: -5, y: 0, z: 0, material: darkCrimsonMaterial },
        { x: -4, y: 1, z: 0, material: crimsonMagicMaterial },
        { x: -4, y: -1, z: 0, material: crimsonMagicMaterial },
        { x: 5, y: 0, z: 0, material: darkCrimsonMaterial },
        { x: 4, y: 1, z: 0, material: crimsonMagicMaterial },
        { x: 4, y: -1, z: 0, material: crimsonMagicMaterial },
        { x: 0, y: 5, z: 0, material: darkCrimsonMaterial },
        { x: 1, y: 4, z: 0, material: crimsonMagicMaterial },
        { x: -1, y: 4, z: 0, material: crimsonMagicMaterial },
        { x: 0, y: -5, z: 0, material: darkCrimsonMaterial },
        { x: 1, y: -4, z: 0, material: crimsonMagicMaterial },
        { x: -1, y: -4, z: 0, material: crimsonMagicMaterial },
        
        // Diagonal streams
        { x: -3, y: -3, z: 0, material: shadowBlackMaterial },
        { x: 3, y: 3, z: 0, material: shadowBlackMaterial },
        { x: -3, y: 3, z: 0, material: shadowBlackMaterial },
        { x: 3, y: -3, z: 0, material: shadowBlackMaterial }
    ];

    // Create dark power emanations
    const darkPowerVoxels = [
        // Corner power sources
        { x: -7, y: 7, z: 0, material: powerGoldMaterial },
        { x: 7, y: 7, z: 0, material: powerGoldMaterial },
        { x: 7, y: -7, z: 0, material: powerGoldMaterial },
        { x: -7, y: -7, z: 0, material: powerGoldMaterial },
        
        // Edge emanations
        { x: -7, y: 3, z: 0, material: shadowBlackMaterial },
        { x: -7, y: 0, z: 0, material: shadowBlackMaterial },
        { x: -7, y: -3, z: 0, material: shadowBlackMaterial },
        { x: 7, y: 3, z: 0, material: shadowBlackMaterial },
        { x: 7, y: 0, z: 0, material: shadowBlackMaterial },
        { x: 7, y: -3, z: 0, material: shadowBlackMaterial },
        { x: 3, y: 7, z: 0, material: shadowBlackMaterial },
        { x: 0, y: 7, z: 0, material: shadowBlackMaterial },
        { x: -3, y: 7, z: 0, material: shadowBlackMaterial },
        { x: 3, y: -7, z: 0, material: shadowBlackMaterial },
        { x: 0, y: -7, z: 0, material: shadowBlackMaterial },
        { x: -3, y: -7, z: 0, material: shadowBlackMaterial },
        
        // Additional dark energy
        { x: -6, y: 6, z: 0, material: ritualPurpleMaterial },
        { x: 6, y: 6, z: 0, material: ritualPurpleMaterial },
        { x: 6, y: -6, z: 0, material: ritualPurpleMaterial },
        { x: -6, y: -6, z: 0, material: ritualPurpleMaterial }
    ];

    // Create all voxels
    [...ritualCircleVoxels, ...sacrificeAltarVoxels, ...bloodMagicVoxels, ...darkPowerVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.2, // Compact spacing
            voxel.y * VOXEL_SIZE * 0.2,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (ritualCircleVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'ritual';
        } else if (sacrificeAltarVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'altar';
        } else if (bloodMagicVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'magic';
        } else {
            mesh.userData.voxelType = 'power';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        ritualPulse: 0,
        sacrificeFlame: 0,
        magicFlow: 0,
        powerSurge: 0,
        bloodOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update blood pact card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateBloodPactCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.ritualPulse += deltaTime * 4.0; // Dark ritual pulsing
    cardGroup.userData.sacrificeFlame += deltaTime * 8.0; // Rapid flame flicker
    cardGroup.userData.magicFlow += deltaTime * 5.5; // Blood magic flow
    cardGroup.userData.powerSurge += deltaTime * 3.0; // Power buildup

    const time = cardGroup.userData.animationTime;
    const ritualPulse = cardGroup.userData.ritualPulse;
    const sacrificeFlame = cardGroup.userData.sacrificeFlame;
    const magicFlow = cardGroup.userData.magicFlow;
    const powerSurge = cardGroup.userData.powerSurge;
    const bloodOffset = cardGroup.userData.bloodOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'ritual':
                    // Ritual circle pulses with dark energy
                    const ritualIntensity = 0.7 + Math.sin(ritualPulse * 2.0 + bloodOffset) * 0.8;
                    const ritualFlow = Math.sin(ritualPulse * 3.0 + child.position.x * 0.5) * 0.006;
                    
                    child.material.emissiveIntensity = baseEmissive * ritualIntensity;
                    child.material.opacity = baseOpacity * (0.8 + ritualIntensity * 0.2);
                    child.position.x += ritualFlow;
                    child.position.y += ritualFlow * 0.7;
                    break;

                case 'altar':
                    // Sacrifice altar flames and power
                    const altarIntensity = 0.8 + Math.sin(sacrificeFlame * 4.0 + bloodOffset) * 0.9;
                    const flameFlicker = Math.sin(sacrificeFlame * 6.0) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * altarIntensity;
                    child.material.opacity = baseOpacity * (0.9 + altarIntensity * 0.1);
                    child.position.y += flameFlicker;
                    child.position.x += flameFlicker * 0.5;
                    break;

                case 'magic':
                    // Blood magic flows toward altar
                    const magicIntensity = 0.6 + Math.sin(magicFlow * 3.5 + child.position.x * 0.4 + child.position.y * 0.3 + bloodOffset) * 0.9;
                    const magicMotion = Math.cos(magicFlow * 4.0 + bloodOffset) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * magicIntensity;
                    child.material.opacity = baseOpacity * magicIntensity;
                    
                    // Flow toward center
                    const centerDirection = new THREE.Vector2(-child.position.x, -child.position.y).normalize();
                    child.position.x += centerDirection.x * magicMotion * 0.3;
                    child.position.y += centerDirection.y * magicMotion * 0.3;
                    break;

                case 'power':
                    // Dark power surges
                    const powerIntensity = 0.5 + Math.sin(powerSurge * 2.5 + bloodOffset) * 0.8;
                    const powerPulse = Math.sin(powerSurge * 4.0) * 0.005;
                    
                    child.material.emissiveIntensity = baseEmissive * powerIntensity;
                    child.material.opacity = baseOpacity * powerIntensity;
                    child.position.x += powerPulse * (child.position.x > 0 ? 1 : -1);
                    child.position.y += powerPulse * (child.position.y > 0 ? 1 : -1);
                    break;
            }
        }
    });

    // Overall blood pact effect
    const bloodPact = Math.sin(time * 5.0 + bloodOffset) * 0.01;
    cardGroup.position.x += bloodPact;
    cardGroup.position.y += bloodPact * 0.8;
    
    // Ominous rotation
    cardGroup.rotation.z += deltaTime * 0.3;
}

// Export the blood pact card data for the loot system
export const BLOOD_PACT_CARD_DATA = {
    name: 'Blood Pact',
    description: 'Sacrifice health to gain immense magical power. Converts all remaining health into devastating blood magic that obliterates enemies.',
    category: 'card',
    rarity: 'legendary',
    effect: 'blood_pact',
    effectValue: 8,
    createFunction: createBloodPactCard,
    updateFunction: updateBloodPactCardAnimation,
    voxelModel: 'blood_pact_card',
    glow: {
        color: 0x8B0000,
        intensity: 2.2
    }
};

export default createBloodPactCard;