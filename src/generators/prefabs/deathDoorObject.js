import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Death Door - Ominous dark door leading to death/trap
 * Features dark materials, skull motifs, and red warning glow
 */
export function createDeathDoorObject(options = {}) {
    const group = new THREE.Group();
    
    // Dark ominous materials
    const darkStoneMaterial = new THREE.MeshStandardMaterial({
        color: 0x2F2F2F, // Very dark gray
        roughness: 0.9,
        metalness: 0.0
    });
    
    const deathGlowMaterial = new THREE.MeshStandardMaterial({
        color: 0x8B0000, // Dark red death glow
        emissive: 0x4B0000,
        emissiveIntensity: 0.5,
        roughness: 0.2,
        metalness: 0.1
    });
    
    // Create door frame
    const frameWidth = VOXEL_SIZE * 0.5;
    const doorWidth = VOXEL_SIZE * 3;
    const doorHeight = VOXEL_SIZE * 5;
    
    // Left frame pillar
    const leftFrameGeometry = new THREE.BoxGeometry(frameWidth, doorHeight, frameWidth);
    const leftFrame = new THREE.Mesh(leftFrameGeometry, darkStoneMaterial);
    leftFrame.position.set(-doorWidth / 2 - frameWidth / 2, doorHeight / 2, 0);
    leftFrame.castShadow = true;
    leftFrame.receiveShadow = true;
    group.add(leftFrame);
    
    // Right frame pillar
    const rightFrame = new THREE.Mesh(leftFrameGeometry, darkStoneMaterial);
    rightFrame.position.set(doorWidth / 2 + frameWidth / 2, doorHeight / 2, 0);
    rightFrame.castShadow = true;
    rightFrame.receiveShadow = true;
    group.add(rightFrame);
    
    // Top frame lintel
    const lintelGeometry = new THREE.BoxGeometry(doorWidth + frameWidth * 2, frameWidth, frameWidth);
    const lintel = new THREE.Mesh(lintelGeometry, darkStoneMaterial);
    lintel.position.set(0, doorHeight + frameWidth / 2, 0);
    lintel.castShadow = true;
    lintel.receiveShadow = true;
    group.add(lintel);
    
    // Door surface (dark void)
    const doorGeometry = new THREE.BoxGeometry(doorWidth, doorHeight, VOXEL_SIZE * 0.1);
    const doorSurface = new THREE.Mesh(doorGeometry, new THREE.MeshStandardMaterial({
        color: 0x000000, // Pure black void
        transparent: true,
        opacity: 0.8,
        emissive: 0x1A0000,
        emissiveIntensity: 0.3
    }));
    doorSurface.position.set(0, doorHeight / 2, -VOXEL_SIZE * 0.05);
    group.add(doorSurface);
    
    // Death warning symbols - skulls carved into frame
    const skullGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 0.3, 8, 6);
    
    // Left skull
    const leftSkull = new THREE.Mesh(skullGeometry, deathGlowMaterial);
    leftSkull.position.set(-doorWidth / 2 - frameWidth / 2, doorHeight * 0.8, frameWidth / 2);
    leftSkull.scale.set(1, 1, 0.5); // Flatten to make relief
    group.add(leftSkull);
    
    // Right skull
    const rightSkull = new THREE.Mesh(skullGeometry, deathGlowMaterial);
    rightSkull.position.set(doorWidth / 2 + frameWidth / 2, doorHeight * 0.8, frameWidth / 2);
    rightSkull.scale.set(1, 1, 0.5); // Flatten to make relief
    group.add(rightSkull);
    
    // Center death symbol on lintel (inverted cross)
    const symbolGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 0.1, VOXEL_SIZE * 0.6, VOXEL_SIZE * 0.1);
    const verticalBar = new THREE.Mesh(symbolGeometry, deathGlowMaterial);
    verticalBar.position.set(0, doorHeight + frameWidth / 2, frameWidth / 2);
    group.add(verticalBar);
    
    const horizontalBarGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 0.4, VOXEL_SIZE * 0.1, VOXEL_SIZE * 0.1);
    const horizontalBar = new THREE.Mesh(horizontalBarGeometry, deathGlowMaterial);
    horizontalBar.position.set(0, doorHeight + frameWidth / 2 + VOXEL_SIZE * 0.15, frameWidth / 2);
    group.add(horizontalBar);
    
    // Ominous red warning light
    const warningLight = new THREE.PointLight(0x8B0000, 2.0, VOXEL_SIZE * 12);
    warningLight.position.set(0, doorHeight + frameWidth, frameWidth);
    warningLight.castShadow = true;
    group.add(warningLight);
    
    // Add some spikes around the frame for danger
    const spikeGeometry = new THREE.ConeGeometry(VOXEL_SIZE * 0.1, VOXEL_SIZE * 0.4, 4);
    
    for (let i = 0; i < 6; i++) {
        const spike = new THREE.Mesh(spikeGeometry, darkStoneMaterial);
        const angle = (i / 6) * Math.PI * 2;
        const radius = doorWidth / 2 + frameWidth * 1.5;
        spike.position.set(
            Math.cos(angle) * radius,
            doorHeight * 0.6 + Math.sin(angle * 2) * VOXEL_SIZE * 0.5,
            frameWidth / 2
        );
        spike.rotation.z = angle + Math.PI / 2;
        group.add(spike);
    }
    
    // Set up group properties
    group.name = 'death_door';
    group.userData = {
        objectType: 'death_door',
        isInteractable: true,
        doorType: 'death',
        leadsTo: 'death',
        isDangerous: true,
        warningLevel: 'high',
        ...options.userData
    };
    
    // Store references for animation
    group.userData.warningLight = warningLight;
    group.userData.skulls = [leftSkull, rightSkull];
    group.userData.doorSurface = doorSurface;
    
    return group;
}