import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * CHRONARCH EPIC ENVIRONMENTAL ATTACK OBJECTS
 * Redesigned to follow the game's proper visual design language
 * Uses architectural logic, material hierarchies, and proper proportions
 */

// TEMPORAL ATTACK COLORS - Following game's established crystal palette
const TEMPORAL_ATTACK_COLORS = {
    // Foundation materials (matching existing stone objects)
    STONE_BASE: '4a4a4a',           // Dark stone foundation
    STONE_ACCENT: '5a5a5a',         // Stone accent/detail
    STONE_PLATFORM: '3a3a3a',       // Platform stone (darker)
    
    // Crystal materials (matching time crystal objects)
    CRYSTAL_MAIN: '4169E1',         // Main temporal blue crystal
    CRYSTAL_CORE: '6495ED',         // Lighter blue core
    CRYSTAL_DEEP: '1E3A8A',         // Deep blue crystal base
    
    // Energy materials
    ENERGY_GLOW: 'E6E6FA',          // Bright energy glow
    TEMPORAL_PURPLE: '8A2BE2',      // Temporal rift purple
    ENERGY_CORE: 'B0E0E6',          // Pale blue energy
    
    // Detail materials
    METAL_DARK: '696969',           // Dark metal accents
    RUNE_GLOW: '9370DB'             // Magical rune glow
};

/**
 * Create properly designed crystalline eruption - emerges from stone foundation
 * Follows architectural logic: Foundation → Crystal Base → Crystal Spires → Energy Field
 */
export function createCrystallineEruption(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);
    
    // Use larger voxel size for epic scale while maintaining design quality
    const voxelSize = VOXEL_SIZE * 1.8; // Increased scale for epic environmental attacks
    const baseGeometry = getOrCreateGeometry('eruption_voxel', () =>
        new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize)
    );
    
    // Create materials following game's hierarchy
    const stoneMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.STONE_BASE, {
        roughness: 0.9,
        metalness: 0.1
    });
    
    const crystalMainMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.CRYSTAL_MAIN, {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.CRYSTAL_DEEP}`),
        emissiveIntensity: 0.3,
        roughness: 0.2,
        metalness: 0.1
    });
    
    const crystalCoreMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.CRYSTAL_CORE, {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.ENERGY_GLOW}`),
        emissiveIntensity: 0.6,
        roughness: 0.1,
        metalness: 0.0
    });
    
    const energyMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.ENERGY_GLOW, {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.ENERGY_GLOW}`),
        emissiveIntensity: 1.0,
        roughness: 0.0,
        metalness: 0.0
    });
    
    // Helper to add voxels with proper batching
    const voxelsByMaterial = {
        stone: [],
        crystal: [],
        core: [],
        energy: []
    };
    
    const addVoxel = (x, y, z, materialType) => {
        const matrix = new THREE.Matrix4();
        matrix.makeTranslation(x * voxelSize, y * voxelSize, z * voxelSize);
        const geometry = baseGeometry.clone();
        geometry.applyMatrix4(matrix);
        voxelsByMaterial[materialType].push(geometry);
    };
    
    // 1. FOUNDATION PLATFORM (emerges from ground)
    // Create 7x7 stone platform with corner cuts for natural look
    for (let x = -3; x <= 3; x++) {
        for (let z = -3; z <= 3; z++) {
            // Cut corners for natural shape
            const distance = Math.sqrt(x * x + z * z);
            if (distance <= 3.2) {
                // Underground foundation layers
                addVoxel(x, -2, z, 'stone');
                addVoxel(x, -1, z, 'stone');
                // Ground level platform
                if (distance <= 2.8) {
                    addVoxel(x, 0, z, 'stone');
                }
            }
        }
    }
    
    // 2. CRYSTAL SPIRES (3 spires in triangular formation) - Increased for epic scale
    const spires = [
        { x: 0, z: 0, height: 12, radius: 2.2 },    // Central main spire (taller)
        { x: -3, z: 2, height: 9, radius: 1.5 },   // Left supporting spire (larger)
        { x: 3, z: 2, height: 9, radius: 1.5 }     // Right supporting spire (larger)
    ];
    
    spires.forEach(spire => {
        for (let y = 1; y <= spire.height; y++) {
            // Calculate tapering (wider at base, narrow at top)
            const heightRatio = y / spire.height;
            const currentRadius = spire.radius * (1 - heightRatio * 0.7); // Taper to 30% at top
            
            // Build each level of the spire
            for (let x = -Math.ceil(currentRadius); x <= Math.ceil(currentRadius); x++) {
                for (let z = -Math.ceil(currentRadius); z <= Math.ceil(currentRadius); z++) {
                    const distance = Math.sqrt(x * x + z * z);
                    
                    if (distance <= currentRadius) {
                        const worldX = spire.x + x;
                        const worldZ = spire.z + z;
                        
                        // Material based on position (architectural hierarchy)
                        if (distance <= currentRadius * 0.3) {
                            addVoxel(worldX, y, worldZ, 'energy'); // Bright core
                        } else if (distance <= currentRadius * 0.6) {
                            addVoxel(worldX, y, worldZ, 'core'); // Inner crystal
                        } else {
                            addVoxel(worldX, y, worldZ, 'crystal'); // Outer crystal
                        }
                    }
                }
            }
        }
    });
    
    // 3. ENERGY FIELD PARTICLES (scattered around spires)
    for (let i = 0; i < 12; i++) {
        const angle = (i / 12) * Math.PI * 2;
        const radius = 3 + rng() * 1.5;
        const height = 2 + rng() * 4;
        
        const x = Math.cos(angle) * radius;
        const z = Math.sin(angle) * radius;
        
        // Small energy particles floating around
        addVoxel(Math.round(x), Math.round(height), Math.round(z), 'energy');
    }
    
    // 4. CONNECTING ENERGY STREAMS (between spires)
    // Add small crystal connections between spires at mid-height
    for (let i = 0; i < spires.length; i++) {
        const spire1 = spires[i];
        const spire2 = spires[(i + 1) % spires.length];
        
        const steps = 3;
        for (let step = 1; step < steps; step++) {
            const t = step / steps;
            const x = Math.round(spire1.x + (spire2.x - spire1.x) * t);
            const z = Math.round(spire1.z + (spire2.z - spire1.z) * t);
            const y = Math.round(3 + Math.sin(t * Math.PI) * 1); // Arc between spires
            
            addVoxel(x, y, z, 'core');
        }
    }
    
    // Create final meshes by material (efficient rendering)
    const materials = {
        stone: stoneMaterial,
        crystal: crystalMainMaterial,
        core: crystalCoreMaterial,
        energy: energyMaterial
    };
    
    Object.entries(voxelsByMaterial).forEach(([type, geometries]) => {
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const mesh = new THREE.Mesh(mergedGeometry, materials[type]);
            
            // Proper shadow settings
            mesh.castShadow = (type === 'stone');
            mesh.receiveShadow = true;
            
            group.add(mesh);
        }
    });
    
    // Set up group properties
    group.userData = {
        objectType: 'crystalline_eruption',
        attackType: 'environmental',
        isTemporalAttack: true,
        isDestructible: true,
        health: 120,
        damageRadius: 6.0, // Reasonable radius
        duration: 12000,
        spawnEffect: 'crystal_emergence',
        destructionEffect: 'crystal_shatter'
    };
    
    group.name = 'crystalline_eruption';
    console.log(`⚡ CRYSTALLINE ERUPTION - Created with proper design architecture`);
    return group;
}

/**
 * Create properly designed shard projectile - follows arrow design pattern
 * Structure: Base → Shaft → Crystal Head → Energy Trail
 */
export function createShardProjectile(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    
    // Use larger projectile size for epic temporal attacks
    const voxelSize = VOXEL_SIZE * 1.4; // Larger than standard for epic projectiles
    const baseGeometry = getOrCreateGeometry('shard_voxel', () =>
        new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize)
    );
    
    // Materials following arrow design pattern
    const shaftMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.STONE_ACCENT, {
        roughness: 0.7,
        metalness: 0.2
    });
    
    const headMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.CRYSTAL_MAIN, {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.CRYSTAL_CORE}`),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.1
    });
    
    const energyMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.ENERGY_GLOW, {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.ENERGY_GLOW}`),
        emissiveIntensity: 0.8,
        roughness: 0.0,
        metalness: 0.0
    });
    
    const voxelsByMaterial = {
        shaft: [],
        head: [],
        energy: []
    };
    
    const addVoxel = (x, y, z, materialType) => {
        const matrix = new THREE.Matrix4();
        matrix.makeTranslation(x * voxelSize, y * voxelSize, z * voxelSize);
        const geometry = baseGeometry.clone();
        geometry.applyMatrix4(matrix);
        voxelsByMaterial[materialType].push(geometry);
    };
    
    // Build projectile along X-axis (like arrow) - Increased for epic scale
    const shardLength = 12; // Larger projectile length for epic attacks
    
    // 1. CRYSTAL HEAD (pointed front section)
    for (let x = 0; x < 3; x++) {
        const width = Math.max(1, 2 - x); // Tapering point
        
        for (let y = -width; y <= width; y++) {
            for (let z = -width; z <= width; z++) {
                const distance = Math.sqrt(y * y + z * z);
                if (distance <= width) {
                    if (distance <= width * 0.5) {
                        addVoxel(x, y, z, 'energy'); // Bright core
                    } else {
                        addVoxel(x, y, z, 'head'); // Crystal head
                    }
                }
            }
        }
    }
    
    // 2. SHAFT (main body)
    for (let x = 3; x < shardLength - 1; x++) {
        // Cross-section shaft
        addVoxel(x, 0, 0, 'shaft'); // Center
        if (x % 2 === 0) {
            // Wider sections for visual interest
            addVoxel(x, 1, 0, 'shaft');
            addVoxel(x, -1, 0, 'shaft');
            addVoxel(x, 0, 1, 'shaft');
            addVoxel(x, 0, -1, 'shaft');
        }
    }
    
    // 3. ENERGY TRAIL (back section)
    for (let x = shardLength - 1; x < shardLength + 2; x++) {
        const trailWidth = Math.max(1, x - shardLength + 2);
        
        for (let y = -trailWidth; y <= trailWidth; y++) {
            for (let z = -trailWidth; z <= trailWidth; z++) {
                const distance = Math.sqrt(y * y + z * z);
                if (distance <= trailWidth) {
                    addVoxel(x, y, z, 'energy'); // Energy trail
                }
            }
        }
    }
    
    // Create final meshes
    const materials = {
        shaft: shaftMaterial,
        head: headMaterial,
        energy: energyMaterial
    };
    
    Object.entries(voxelsByMaterial).forEach(([type, geometries]) => {
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const mesh = new THREE.Mesh(mergedGeometry, materials[type]);
            
            mesh.castShadow = (type === 'shaft');
            mesh.receiveShadow = true;
            
            group.add(mesh);
        }
    });
    
    group.userData = {
        objectType: 'shard_projectile',
        attackType: 'projectile',
        isTemporalAttack: true,
        damage: 35,
        speed: 8.0,
        range: 15.0,
        size: 1.2,
        piercing: true,
        trailEffect: 'temporal_energy',
        impactEffect: 'crystal_explosion'
    };
    
    group.name = 'shard_projectile';
    console.log(`💎 SHARD PROJECTILE - Created with proper projectile design`);
    return group;
}

/**
 * Create time dilation field sphere - clean energy effect
 */
export function createTimeDilationField(options = {}) {
    const group = new THREE.Group();
    const radius = options.radius || 4.0; // Reasonable radius for room scale
    
    // Create main field sphere
    const sphereGeometry = getOrCreateGeometry('dilation_sphere', () =>
        new THREE.IcosahedronGeometry(radius, 2)
    );
    
    const fieldMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.TEMPORAL_PURPLE, {
        transparent: true,
        opacity: 0.25,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.TEMPORAL_PURPLE}`),
        emissiveIntensity: 0.4,
        roughness: 0.0,
        metalness: 0.0,
        side: THREE.DoubleSide
    });
    
    const sphere = new THREE.Mesh(sphereGeometry, fieldMaterial);
    sphere.userData.isTimeDilationField = true;
    sphere.castShadow = false;
    sphere.receiveShadow = false;
    group.add(sphere);
    
    // Add floating energy particles
    const particleCount = 8;
    const particleGeometry = getOrCreateGeometry('dilation_particle', () =>
        new THREE.SphereGeometry(0.1, 6, 4)
    );
    
    const particleMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.ENERGY_GLOW, {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.ENERGY_GLOW}`),
        emissiveIntensity: 1.0
    });
    
    for (let i = 0; i < particleCount; i++) {
        const particle = new THREE.Mesh(particleGeometry, particleMaterial);
        
        // Distribute particles in sphere
        const theta = Math.random() * Math.PI * 2;
        const phi = Math.random() * Math.PI;
        const r = Math.random() * radius * 0.8;
        
        particle.position.set(
            r * Math.sin(phi) * Math.cos(theta),
            r * Math.cos(phi),
            r * Math.sin(phi) * Math.sin(theta)
        );
        
        particle.userData.orbitPhase = Math.random() * Math.PI * 2;
        particle.userData.orbitSpeed = 0.3 + Math.random() * 0.4;
        group.add(particle);
    }
    
    group.userData = {
        objectType: 'time_dilation_field',
        attackType: 'area_effect',
        isTemporalAttack: true,
        radius: radius,
        effect: 'time_slowdown',
        slowdownFactor: 0.4,
        duration: 8000,
        hasAnimation: true
    };
    
    group.name = 'time_dilation_field';
    console.log(`⏰ TIME DILATION FIELD - Created with proper energy field design`);
    return group;
}


/**
 * Create temporal echo of boss - ghostly translucent version
 * Safely clones only the visual mesh without circular references
 */
export function createTemporalEcho(originalBoss, options = {}) {
    const group = new THREE.Group();
    
    if (!originalBoss) {
        console.warn('⚠️ TEMPORAL ECHO - No original boss provided');
        return group;
    }
    
    // Create ghostly echo by manually reconstructing visual elements only
    const echoGroup = new THREE.Group();
    echoGroup.name = 'temporal_echo';
    echoGroup.position.copy(originalBoss.position);
    echoGroup.rotation.copy(originalBoss.rotation);
    echoGroup.scale.copy(originalBoss.scale);
    
    // Recursively clone only mesh children, avoiding userData circular references
    const cloneMeshOnly = (source, target) => {
        source.children.forEach(child => {
            if (child.isMesh && child.geometry && child.material) {
                // Clone mesh safely without userData
                const clonedMesh = new THREE.Mesh(child.geometry, child.material.clone());
                clonedMesh.position.copy(child.position);
                clonedMesh.rotation.copy(child.rotation);
                clonedMesh.scale.copy(child.scale);
                clonedMesh.name = child.name + '_echo';
                
                // Apply ghostly transformation
                const ghostMaterial = clonedMesh.material;
                ghostMaterial.transparent = true;
                ghostMaterial.opacity = 0.4;
                ghostMaterial.color.setHex(parseInt(TEMPORAL_ATTACK_COLORS.ENERGY_CORE, 16));
                ghostMaterial.emissive = new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.TEMPORAL_PURPLE}`);
                ghostMaterial.emissiveIntensity = 0.3;
                clonedMesh.castShadow = false;
                clonedMesh.receiveShadow = false;
                
                target.add(clonedMesh);
            } else if (child.isGroup || child.isObject3D) {
                // Recursively clone groups but skip userData
                const clonedGroup = new THREE.Group();
                clonedGroup.position.copy(child.position);
                clonedGroup.rotation.copy(child.rotation);
                clonedGroup.scale.copy(child.scale);
                clonedGroup.name = child.name + '_echo';
                
                cloneMeshOnly(child, clonedGroup);
                if (clonedGroup.children.length > 0) {
                    target.add(clonedGroup);
                }
            }
        });
    };
    
    cloneMeshOnly(originalBoss, echoGroup);
    group.add(echoGroup);
    
    group.userData = {
        objectType: 'temporal_echo',
        attackType: 'boss_echo',
        isTemporalAttack: true,
        isGhost: true,
        echoDelay: options.echoDelay || 2000,
        lifetime: options.lifetime || 8000,
        attacksToEcho: options.attacksToEcho || ['crystalline_eruption', 'shard_volley']
    };
    
    group.name = 'temporal_echo';
    console.log(`👻 TEMPORAL ECHO - Created ghostly boss clone safely`);
    return group;
}

/**
 * Create reality fracture crack - subtle spatial distortion effect
 */
export function createRealityFracture(options = {}) {
    const group = new THREE.Group();
    const length = options.length || 6.0; // Reasonable crack length
    const segments = Math.floor(length / 0.4); // Smaller segments for detail
    
    // Create fracture using small energy particles
    const crackMaterial = _getMaterialByHex_Cached(TEMPORAL_ATTACK_COLORS.TEMPORAL_PURPLE, {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(`0x${TEMPORAL_ATTACK_COLORS.TEMPORAL_PURPLE}`),
        emissiveIntensity: 0.8
    });
    
    const segmentGeometry = getOrCreateGeometry('fracture_segment', () =>
        new THREE.BoxGeometry(0.05, 0.15, 0.05)
    );
    
    for (let i = 0; i < segments; i++) {
        const segment = new THREE.Mesh(segmentGeometry, crackMaterial);
        const progress = i / segments;
        
        // Create jagged crack pattern
        segment.position.set(
            progress * length + (Math.random() - 0.5) * 0.3,
            Math.random() * 1.5,
            (Math.random() - 0.5) * 0.6
        );
        
        segment.rotation.set(
            (Math.random() - 0.5) * Math.PI * 0.2,
            (Math.random() - 0.5) * Math.PI * 0.2,
            (Math.random() - 0.5) * Math.PI * 0.2
        );
        
        segment.userData.animationPhase = progress * Math.PI * 2;
        segment.castShadow = false;
        segment.receiveShadow = false;
        group.add(segment);
    }
    
    group.userData = {
        objectType: 'reality_fracture',
        attackType: 'area_effect',
        isTemporalAttack: true,
        length: length,
        damageZone: true,
        damagePerSecond: 15,
        duration: 10000
    };
    
    group.name = 'reality_fracture';
    console.log(`⚡ REALITY FRACTURE - Created subtle spatial crack effect`);
    return group;
}