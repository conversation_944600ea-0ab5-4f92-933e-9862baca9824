import * as THREE from 'three';
import { getItemData } from '../../entities/ItemTypes.js';

/**
 * Call of Ascension Card Item Data
 * Creates a 3D voxel-style card for treasure chests
 */

export const CALL_OF_ASCENSION_CARD_DATA = {
    id: 'call_of_ascension',
    name: 'Call of Ascension',
    description: 'Summons ethereal wings that lift the caster above earthly bounds, granting temporary flight and revealing hidden paths through the spiritual realm.',
    type: 'card',
    category: 'card',
    rarity: 'epic',
    
    // Card-specific properties
    cardType: 'summon',
    cardBorderColor: 0xffd700, // Golden border
    
    // 3D model creation function
    createFunction: (options = {}) => {
        return createCallOfAscensionCard(options);
    }
};

/**
 * Create a 3D Call of Ascension card model
 * @param {Object} options - Creation options
 * @returns {THREE.Group} The card 3D model
 */
function createCallOfAscensionCard(options = {}) {
    const { seed = Date.now() } = options;
    
    // Get item data for consistency
    const itemData = getItemData('call_of_ascension');
    if (!itemData) {
        console.warn('[CallOfAscensionCard] Item data not found, using defaults');
    }
    
    // Create card group
    const cardGroup = new THREE.Group();
    cardGroup.name = 'call_of_ascension_card';
    cardGroup.userData.itemType = 'call_of_ascension';
    cardGroup.userData.isCard = true;
    cardGroup.userData.seed = seed;
    
    // Card dimensions (voxel-compatible)
    const cardWidth = 0.8;
    const cardHeight = 1.2;
    const cardThickness = 0.08;
    
    // Create card base (main body)
    const cardGeometry = new THREE.BoxGeometry(cardWidth, cardHeight, cardThickness);
    const cardMaterial = new THREE.MeshLambertMaterial({
        color: 0x1a1a1a, // Dark card background
        transparent: false
    });
    
    const cardBase = new THREE.Mesh(cardGeometry, cardMaterial);
    cardBase.name = 'cardBase';
    cardGroup.add(cardBase);
    
    // Create golden border
    createCardBorder(cardGroup, cardWidth, cardHeight, cardThickness);
    
    // Create wing artwork
    createWingArtwork(cardGroup, cardWidth, cardHeight);

    // Create card title area (simplified)
    createCardTitle(cardGroup, cardWidth, cardHeight);
    
    // Add subtle glow effect
    createCardGlow(cardGroup, cardWidth, cardHeight);
    
    // Scale card appropriately for chest display
    cardGroup.scale.setScalar(0.6);
    
    return cardGroup;
}

/**
 * Create voxel-style border around the card
 */
function createCardBorder(cardGroup, cardWidth, cardHeight, cardThickness) {
    const borderThickness = 0.04;
    const borderColor = 0xffd700; // Golden border
    
    const borderMaterial = new THREE.MeshLambertMaterial({
        color: borderColor,
        transparent: false
    });
    
    // Top border
    const topBorder = new THREE.Mesh(
        new THREE.BoxGeometry(cardWidth + borderThickness * 2, borderThickness, cardThickness + 0.01),
        borderMaterial
    );
    topBorder.position.set(0, cardHeight / 2 + borderThickness / 2, 0);
    cardGroup.add(topBorder);
    
    // Bottom border
    const bottomBorder = new THREE.Mesh(
        new THREE.BoxGeometry(cardWidth + borderThickness * 2, borderThickness, cardThickness + 0.01),
        borderMaterial
    );
    bottomBorder.position.set(0, -cardHeight / 2 - borderThickness / 2, 0);
    cardGroup.add(bottomBorder);
    
    // Left border
    const leftBorder = new THREE.Mesh(
        new THREE.BoxGeometry(borderThickness, cardHeight, cardThickness + 0.01),
        borderMaterial
    );
    leftBorder.position.set(-cardWidth / 2 - borderThickness / 2, 0, 0);
    cardGroup.add(leftBorder);
    
    // Right border
    const rightBorder = new THREE.Mesh(
        new THREE.BoxGeometry(borderThickness, cardHeight, cardThickness + 0.01),
        borderMaterial
    );
    rightBorder.position.set(cardWidth / 2 + borderThickness / 2, 0, 0);
    cardGroup.add(rightBorder);
}

/**
 * Create wing artwork for the card
 */
function createWingArtwork(cardGroup, cardWidth, cardHeight) {
    const voxelSize = 0.06;
    const artworkMaterial = new THREE.MeshLambertMaterial({
        color: 0xffd700, // Golden color
        transparent: false
    });
    
    // Wing pattern coordinates (simplified voxel wing design)
    const wingPattern = [
        // Left wing
        [-0.25, 0.15], [-0.2, 0.2], [-0.15, 0.25], [-0.1, 0.2],
        [-0.3, 0.05], [-0.25, 0.1], [-0.2, 0.15], [-0.15, 0.1],
        [-0.35, -0.05], [-0.3, 0], [-0.25, 0.05], [-0.2, 0],
        // Right wing (mirrored)
        [0.25, 0.15], [0.2, 0.2], [0.15, 0.25], [0.1, 0.2],
        [0.3, 0.05], [0.25, 0.1], [0.2, 0.15], [0.15, 0.1],
        [0.35, -0.05], [0.3, 0], [0.25, 0.05], [0.2, 0],
        // Center body
        [0, 0.08], [0, 0.04], [0, 0], [0, -0.04]
    ];
    
    wingPattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
            artworkMaterial
        );
        voxel.position.set(x, y, 0.05); // Slightly in front of card
        voxel.name = 'wingVoxel';
        cardGroup.add(voxel);
    });
}



/**
 * Create card title area
 */
function createCardTitle(cardGroup, cardWidth, cardHeight) {
    // Create a simple title bar at the bottom
    const titleBarGeometry = new THREE.BoxGeometry(cardWidth * 0.9, 0.15, 0.02);
    const titleBarMaterial = new THREE.MeshLambertMaterial({
        color: 0x333333,
        transparent: false
    });
    
    const titleBar = new THREE.Mesh(titleBarGeometry, titleBarMaterial);
    titleBar.position.set(0, -cardHeight / 2 + 0.1, 0.05);
    titleBar.name = 'titleBar';
    cardGroup.add(titleBar);
    
    // Add some decorative voxels for the title
    const titleVoxelMaterial = new THREE.MeshLambertMaterial({
        color: 0xffd700
    });
    
    // Simple pattern for "Call of Ascension" representation
    const titlePattern = [
        [-0.2, -0.45], [-0.1, -0.45], [0, -0.45], [0.1, -0.45], [0.2, -0.45]
    ];
    
    titlePattern.forEach(([x, y]) => {
        const voxel = new THREE.Mesh(
            new THREE.BoxGeometry(0.03, 0.03, 0.03),
            titleVoxelMaterial
        );
        voxel.position.set(x, y, 0.06);
        cardGroup.add(voxel);
    });
}

/**
 * Create subtle glow effect
 */
function createCardGlow(cardGroup, cardWidth, cardHeight) {
    const glowGeometry = new THREE.PlaneGeometry(cardWidth * 1.1, cardHeight * 1.1);
    const glowMaterial = new THREE.MeshBasicMaterial({
        color: 0xffd700,
        transparent: true,
        opacity: 0.1,
        side: THREE.DoubleSide
    });
    
    const glow = new THREE.Mesh(glowGeometry, glowMaterial);
    glow.position.z = -0.01; // Behind the card
    glow.name = 'cardGlow';
    cardGroup.add(glow);
}

// Export the card data
export default CALL_OF_ASCENSION_CARD_DATA;
