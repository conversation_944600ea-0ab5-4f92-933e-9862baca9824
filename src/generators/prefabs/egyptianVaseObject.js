import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Voxel Data for Egyptian Vase ---

// Egyptian vase color palette (matching ominous treasure room golden theme)
const EGYPTIAN_COLORS = {
    gold_base: 'D4AF37',      // Golden base (matches room wallTint)
    gold_accent: 'DAA520',    // Darker gold accent (matches room floorTint)
    gold_bright: 'FFD700',    // Bright gold rim (matches room spotlight)
    gold_dark: 'B8860B',      // Dark gold pattern
    hieroglyph: 'CD853F',     // Peru gold for hieroglyphs (more golden than brown)
    gold_metallic: 'FFA500'   // Orange-gold for metallic shine
};

// Helper function to generate a square layer (like ancient pillar)
function generateSquareLayer(y, radius, color) {
    const layer = [];
    const intRadius = Math.floor(radius);
    for (let x = -intRadius; x <= intRadius; x++) {
        for (let z = -intRadius; z <= intRadius; z++) {
            if (radius % 1 !== 0 && Math.abs(x) === intRadius && Math.abs(z) === intRadius) {
                continue; // Skip corners for fractional radius
            }
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate a solid layer (like stone vase)
function generateSolidLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate a rim layer (like stone vase)
function generateRimLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            if (radius === 0 || Math.abs(x) === radius || Math.abs(z) === radius) {
                layer.push({ x, y, z, c: color });
            }
        }
    }
    return layer;
}

// Detailed Egyptian vase shape using voxel coordinates (like stone vase)
const egyptianVaseShape = [
    // === BASE PLATFORM (Y: 0-1) ===
    ...generateSquareLayer(0, 2, EGYPTIAN_COLORS.gold_base),
    ...generateSquareLayer(1, 1.5, EGYPTIAN_COLORS.gold_accent),

    // === LOWER BODY (Y: 2-4) - Wide section ===
    ...generateSolidLayer(2, 2, EGYPTIAN_COLORS.gold_base),
    ...generateSolidLayer(3, 2, EGYPTIAN_COLORS.gold_accent),
    ...generateSolidLayer(4, 2, EGYPTIAN_COLORS.gold_base),

    // === HIEROGLYPH BAND (Y: 5) - Decorative middle ===
    ...generateSolidLayer(5, 2, EGYPTIAN_COLORS.hieroglyph),
    // Add hieroglyph details with metallic gold
    { x: -2, y: 5, z: 0, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 2, y: 5, z: 0, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 0, y: 5, z: -2, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 0, y: 5, z: 2, c: EGYPTIAN_COLORS.gold_metallic },

    // === UPPER BODY (Y: 6-8) - Tapering ===
    ...generateSolidLayer(6, 2, EGYPTIAN_COLORS.gold_accent),
    ...generateSolidLayer(7, 1, EGYPTIAN_COLORS.gold_base),
    ...generateSolidLayer(8, 1, EGYPTIAN_COLORS.gold_accent),

    // === NECK (Y: 9-10) - Narrow section ===
    ...generateSolidLayer(9, 1, EGYPTIAN_COLORS.gold_base),
    ...generateSolidLayer(10, 1, EGYPTIAN_COLORS.gold_accent),

    // === RIM (Y: 11-12) - Flared opening ===
    ...generateRimLayer(11, 2, EGYPTIAN_COLORS.gold_bright),
    ...generateRimLayer(12, 2, EGYPTIAN_COLORS.gold_bright),

    // === DECORATIVE DETAILS ===
    // Base decorations with metallic gold
    { x: -1, y: 1, z: -1, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 1, y: 1, z: -1, c: EGYPTIAN_COLORS.gold_metallic },
    { x: -1, y: 1, z: 1, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 1, y: 1, z: 1, c: EGYPTIAN_COLORS.gold_metallic },

    // Body accent lines with bright gold
    { x: -2, y: 3, z: -1, c: EGYPTIAN_COLORS.gold_bright },
    { x: 2, y: 3, z: -1, c: EGYPTIAN_COLORS.gold_bright },
    { x: -1, y: 3, z: -2, c: EGYPTIAN_COLORS.gold_bright },
    { x: 1, y: 3, z: 2, c: EGYPTIAN_COLORS.gold_bright },

    // Neck details with metallic gold
    { x: -1, y: 9, z: 0, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 1, y: 9, z: 0, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 0, y: 10, z: -1, c: EGYPTIAN_COLORS.gold_metallic },
    { x: 0, y: 10, z: 1, c: EGYPTIAN_COLORS.gold_metallic },
];

// --- Main Prefab Function ---
export function createEgyptianVaseObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777); // Different multiplier for variation

    // Use same voxel size as stone vase for consistency
    const vaseVoxelSize = VOXEL_SIZE * 2.5;
    const baseGeometry = getOrCreateGeometry('egyptian_vase_voxel', () =>
        new THREE.BoxGeometry(vaseVoxelSize, vaseVoxelSize, vaseVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the vase shape
    egyptianVaseShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to gold color for variation
        let finalColor = c;
        if (rng() < 0.15) { // 15% chance for slight variation
            const goldColors = [EGYPTIAN_COLORS.gold_base, EGYPTIAN_COLORS.gold_accent];
            if (c === EGYPTIAN_COLORS.gold_base || c === EGYPTIAN_COLORS.gold_accent) {
                finalColor = goldColors[Math.floor(rng() * goldColors.length)];
            }
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * vaseVoxelSize,
            y * vaseVoxelSize,
            z * vaseVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'egyptian_vase',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: vaseVoxelSize,
        originalVoxels: egyptianVaseShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 19 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('Egyptian vase created with options:', options);
    console.log('Egyptian vase userData:', group.userData);

    return group;
}
