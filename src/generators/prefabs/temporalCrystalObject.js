import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a temporal crystal formation - mystical crystalline structures for the Chronal Anomaly room
 * These crystals pulse with temporal energy and emit soft purple-blue light
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Temporal crystal object group
 */
export function createTemporalCrystalObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 3141);

    // Use medium voxel size for visible crystals (follows scaling analysis)
    const crystalVoxelSize = VOXEL_SIZE * 2.5;
    const baseGeometry = getOrCreateGeometry('temporal_crystal_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Temporal crystal materials - mystical purple spectrum
    const primaryCrystalMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x8A2BE2),
        emissiveIntensity: 0.5,
        roughness: 0.1,
        metalness: 0.2
    }); // Blue-violet primary crystal

    const secondaryCrystalMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x9370DB),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.3
    }); // Medium orchid secondary crystal

    const accentCrystalMaterial = _getMaterialByHex_Cached('DDA0DD', {
        transparent: true,
        opacity: 0.6,
        emissive: new THREE.Color(0xDDA0DD),
        emissiveIntensity: 0.3,
        roughness: 0.3,
        metalness: 0.4
    }); // Plum accent crystal

    const baseStoneMaterial = _getMaterialByHex_Cached('483D8B', {
        roughness: 0.8,
        metalness: 0.1,
        emissive: new THREE.Color(0x2F2F4F),
        emissiveIntensity: 0.05
    }); // Dark slate blue base stone

    // Crystal base/foundation
    const basePositions = [
        // Foundation platform (3x3)
        { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 },
        { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },
        { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 }
    ];

    // Add foundation stones
    basePositions.forEach(pos => {
        const stone = new THREE.Mesh(baseGeometry.clone(), baseStoneMaterial);
        stone.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        stone.scale.set(1.0, 0.6, 1.0); // Flatter foundation
        stone.castShadow = true;
        stone.receiveShadow = true;
        group.add(stone);
    });

    // Main crystal formation - central spire
    const mainCrystalPositions = [
        // Central growing spire
        { x: 0, y: 1, z: 0 }, { x: 0, y: 2, z: 0 }, { x: 0, y: 3, z: 0 }, { x: 0, y: 4, z: 0 },
        // Supporting crystals at base
        { x: -1, y: 1, z: 0 }, { x: 1, y: 1, z: 0 }, { x: 0, y: 1, z: -1 }, { x: 0, y: 1, z: 1 },
        // Mid-level supports
        { x: -1, y: 2, z: 0 }, { x: 1, y: 2, z: 0 }
    ];

    mainCrystalPositions.forEach((pos, index) => {
        const isCentral = pos.x === 0 && pos.z === 0;
        const material = isCentral ? primaryCrystalMaterial : secondaryCrystalMaterial;
        
        const crystal = new THREE.Mesh(baseGeometry.clone(), material);
        crystal.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        
        // Scale crystals based on height and position
        if (isCentral) {
            const heightScale = 1.0 + (pos.y * 0.1); // Taller crystals get slightly thicker
            crystal.scale.set(0.8, heightScale, 0.8);
        } else {
            crystal.scale.set(0.6, 0.8, 0.6); // Smaller support crystals
        }
        
        // Add subtle rotation for organic feel
        crystal.rotation.y = (index * Math.PI / 8) + (rng() * Math.PI / 4);
        
        crystal.userData.isTemporalCrystal = true;
        crystal.userData.crystalType = isCentral ? 'main' : 'support';
        crystal.userData.crystalIndex = index;
        crystal.userData.baseIntensity = isCentral ? 0.5 : 0.4;
        crystal.castShadow = false;
        crystal.receiveShadow = true;
        group.add(crystal);
    });

    // Accent crystal clusters (smaller formations around the main spire)
    const accentPositions = [
        { x: -1, y: 1, z: -1 }, { x: 1, y: 1, z: -1 }, { x: -1, y: 1, z: 1 }, { x: 1, y: 1, z: 1 },
        { x: -1, y: 2, z: -1 }, { x: 1, y: 2, z: 1 }, // Diagonal elevations
        { x: -0.5, y: 3, z: -0.5 }, { x: 0.5, y: 3, z: 0.5 } // High accent points
    ];

    accentPositions.forEach((pos, index) => {
        if (rng() > 0.3) { // 70% chance for each accent crystal
            const crystal = new THREE.Mesh(
                getOrCreateGeometry('accent_crystal', () => {
                    // Create irregular crystal shape using octahedron
                    const geo = new THREE.OctahedronGeometry(crystalVoxelSize * 0.3, 0);
                    return geo;
                }),
                accentCrystalMaterial
            );
            crystal.position.set(
                pos.x * crystalVoxelSize,
                pos.y * crystalVoxelSize,
                pos.z * crystalVoxelSize
            );
            
            // Random scale and rotation for natural variety
            const scale = 0.5 + rng() * 0.4;
            crystal.scale.set(scale, scale * 1.5, scale); // Taller than wide
            crystal.rotation.x = rng() * Math.PI;
            crystal.rotation.y = rng() * Math.PI * 2;
            crystal.rotation.z = rng() * Math.PI;
            
            crystal.userData.isAccentCrystal = true;
            crystal.userData.accentIndex = index;
            crystal.userData.baseIntensity = 0.3;
            crystal.castShadow = false;
            crystal.receiveShadow = true;
            group.add(crystal);
        }
    });

    // Energy wisps floating around the crystal formation
    const wispPositions = [
        { x: -2, y: 2, z: 0 }, { x: 2, y: 2, z: 0 }, { x: 0, y: 2, z: -2 }, { x: 0, y: 2, z: 2 },
        { x: -1.5, y: 3.5, z: 1.5 }, { x: 1.5, y: 3.5, z: -1.5 }, { x: 0, y: 5, z: 0 }
    ];

    const wispMaterial = _getMaterialByHex_Cached('E6E6FA', {
        transparent: true,
        opacity: 0.5,
        emissive: new THREE.Color(0xE6E6FA),
        emissiveIntensity: 0.7,
        roughness: 0.0,
        metalness: 0.0
    }); // Lavender energy wisps

    wispPositions.forEach((pos, index) => {
        if (rng() > 0.5) { // 50% chance for each wisp
            const wisp = new THREE.Mesh(
                getOrCreateGeometry('temporal_wisp', () =>
                    new THREE.SphereGeometry(crystalVoxelSize * 0.2, 8, 6)
                ),
                wispMaterial
            );
            wisp.position.set(
                pos.x * crystalVoxelSize,
                pos.y * crystalVoxelSize,
                pos.z * crystalVoxelSize
            );
            wisp.userData.isTemporalWisp = true;
            wisp.userData.wispIndex = index;
            wisp.userData.baseX = pos.x * crystalVoxelSize;
            wisp.userData.baseY = pos.y * crystalVoxelSize;
            wisp.userData.baseZ = pos.z * crystalVoxelSize;
            wisp.userData.orbitSpeed = 0.2 + rng() * 0.3;
            wisp.scale.set(0.3, 0.3, 0.3);
            wisp.castShadow = false;
            wisp.receiveShadow = false;
            group.add(wisp);
        }
    });

    // Create soft point light for the crystal formation
    const crystalLight = new THREE.PointLight(0x9370DB, 1.2, 12, 2);
    crystalLight.position.set(0, 2.5 * crystalVoxelSize, 0);
    crystalLight.castShadow = false; // Soft lighting
    crystalLight.userData.originalIntensity = 1.2;
    crystalLight.userData.isPulsing = true;
    group.add(crystalLight);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'temporal_crystal',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: crystalVoxelSize,
        hasTemporalAnimation: true,
        hasCrystalPulse: true,
        crystalLight: crystalLight
    };

    group.name = 'temporal_crystal';
    console.log('[TemporalCrystal]  Created mystical temporal crystal formation');
    return group;
}