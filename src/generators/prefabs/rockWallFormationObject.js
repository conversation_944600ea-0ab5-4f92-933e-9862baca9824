import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Pond Room Mystical Rock Colors ---
// Adapted from pond room mystical theme
const MYSTICAL_ROCK_COLORS = [
    '9B7EBD', // Mystical purple-blue (wall tint)
    '8A7CA8', // Darker mystical purple (floor tint)
    '7A6C98', // Even darker mystical
    '6A5C88', // Deep mystical purple
    '5A4C78', // Very deep mystical
    '4A3C68', // Almost dark mystical
    '3A3A5A', // Mystical purple-gray (ambient)
    '2A2A4A'  // Very dark mystical
];

const CYAN_ACCENT = '00FFFF'; // Pure cyan (pond glow)

// Define stacked rock wall formation (like natural cave wall)
const rockWallFormation = [
    // === BOTTOM LAYER (Y=0) - Foundation rocks ===
    { x: 0, y: 0, z: 0, c: MYSTICAL_ROCK_COLORS[0] },
    { x: 1, y: 0, z: 0, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 2, y: 0, z: 0, c: MYSTICAL_ROCK_COLORS[0] },
    { x: 3, y: 0, z: 0, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 4, y: 0, z: 0, c: MYSTICAL_ROCK_COLORS[0] },

    // === SECOND LAYER (Y=1) - Offset stacking ===
    { x: 0, y: 1, z: 0, c: MYSTICAL_ROCK_COLORS[2] },
    { x: 1, y: 1, z: 0, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 2, y: 1, z: 0, c: MYSTICAL_ROCK_COLORS[2] },
    { x: 3, y: 1, z: 0, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 4, y: 1, z: 0, c: MYSTICAL_ROCK_COLORS[2] },

    // === THIRD LAYER (Y=2) - More irregular ===
    { x: 0, y: 2, z: 0, c: MYSTICAL_ROCK_COLORS[3] },
    { x: 1, y: 2, z: 0, c: MYSTICAL_ROCK_COLORS[2] },
    { x: 2, y: 2, z: 0, c: MYSTICAL_ROCK_COLORS[3] },
    { x: 3, y: 2, z: 0, c: MYSTICAL_ROCK_COLORS[2] },

    // === FOURTH LAYER (Y=3) - Smaller rocks ===
    { x: 0, y: 3, z: 0, c: MYSTICAL_ROCK_COLORS[4] },
    { x: 1, y: 3, z: 0, c: MYSTICAL_ROCK_COLORS[3] },
    { x: 2, y: 3, z: 0, c: MYSTICAL_ROCK_COLORS[4] },

    // === FIFTH LAYER (Y=4) - Top rocks ===
    { x: 0, y: 4, z: 0, c: MYSTICAL_ROCK_COLORS[5] },
    { x: 1, y: 4, z: 0, c: MYSTICAL_ROCK_COLORS[4] },

    // === PROTRUDING ROCKS (Z=-1, forward from wall) ===
    { x: 0, y: 0, z: -1, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 2, y: 0, z: -1, c: MYSTICAL_ROCK_COLORS[2] },
    { x: 4, y: 0, z: -1, c: MYSTICAL_ROCK_COLORS[1] },
    { x: 1, y: 1, z: -1, c: MYSTICAL_ROCK_COLORS[3] },
    { x: 3, y: 1, z: -1, c: MYSTICAL_ROCK_COLORS[2] },
    { x: 0, y: 2, z: -1, c: MYSTICAL_ROCK_COLORS[4] },
    { x: 2, y: 2, z: -1, c: MYSTICAL_ROCK_COLORS[3] },

    // === RECESSED ROCKS (Z=1, back into wall) ===
    { x: 1, y: 0, z: 1, c: MYSTICAL_ROCK_COLORS[6] },
    { x: 3, y: 0, z: 1, c: MYSTICAL_ROCK_COLORS[5] },
    { x: 0, y: 1, z: 1, c: MYSTICAL_ROCK_COLORS[6] },
    { x: 2, y: 1, z: 1, c: MYSTICAL_ROCK_COLORS[5] },
    { x: 1, y: 2, z: 1, c: MYSTICAL_ROCK_COLORS[7] },

    // === CYAN MYSTICAL ACCENTS (like pond glow) ===
    { x: 2, y: 1, z: 0, c: CYAN_ACCENT }, // Central accent
    { x: 0, y: 3, z: 0, c: CYAN_ACCENT }, // Upper accent
    { x: 4, y: 2, z: 0, c: CYAN_ACCENT }, // Side accent
];

/**
 * Create a mystical rock wall formation for room edges
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Rock wall formation group
 */
export function createRockWallFormationObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 1234);

    // Use FLOOR VOXEL SIZE for proper scale (VOXEL_SIZE * 6 = floor scale)
    const rockVoxelSize = VOXEL_SIZE * 6;
    const baseGeometry = getOrCreateGeometry('rock_wall_floor_voxel', () =>
        new THREE.BoxGeometry(rockVoxelSize, rockVoxelSize, rockVoxelSize)
    );

    // Create mystical rock materials with pond room colors
    const mysticalRockMaterials = {};
    MYSTICAL_ROCK_COLORS.forEach(color => {
        mysticalRockMaterials[color] = _getMaterialByHex_Cached(color, {
            emissive: new THREE.Color(`0x${color}`).multiplyScalar(0.08),
            emissiveIntensity: 0.15,
            roughness: 0.8,
            metalness: 0.05
        });
    });

    // Special cyan accent material (glowing like pond)
    const cyanMaterial = _getMaterialByHex_Cached(CYAN_ACCENT, {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x00FFFF),
        emissiveIntensity: 0.6,
        roughness: 0.4,
        metalness: 0.1
    });

    // Build the rock wall formation
    rockWallFormation.forEach(rock => {
        // Add natural variation to rock positioning
        const finalX = rock.x * rockVoxelSize + (rng() - 0.5) * rockVoxelSize * 0.2;
        const finalY = rock.y * rockVoxelSize + (rng() - 0.5) * rockVoxelSize * 0.1;
        const finalZ = rock.z * rockVoxelSize + (rng() - 0.5) * rockVoxelSize * 0.3;

        // Choose material based on color
        let material;
        if (rock.c === CYAN_ACCENT) {
            material = cyanMaterial;
        } else {
            material = mysticalRockMaterials[rock.c];
        }

        const rockVoxel = new THREE.Mesh(baseGeometry.clone(), material);
        rockVoxel.position.set(finalX, finalY, finalZ);
        
        // Add natural rock variation
        const scaleVariation = 0.8 + rng() * 0.4; // 0.8x to 1.2x scale
        rockVoxel.scale.set(
            scaleVariation,
            0.7 + rng() * 0.6, // Height variation 0.7x to 1.3x
            scaleVariation
        );
        
        // Add natural rotation
        rockVoxel.rotation.set(
            (rng() - 0.5) * 0.3,
            (rng() - 0.5) * Math.PI * 0.4,
            (rng() - 0.5) * 0.3
        );
        
        // Cyan accents don't cast shadows (they glow)
        rockVoxel.castShadow = rock.c !== CYAN_ACCENT;
        rockVoxel.receiveShadow = true;
        
        group.add(rockVoxel);
    });

    // Add mystical crystal veins (small glowing elements between rocks)
    const veinCount = 2 + Math.floor(rng() * 3); // 2-4 crystal veins
    for (let i = 0; i < veinCount; i++) {
        const veinGeometry = getOrCreateGeometry('rock_crystal_vein', () =>
            new THREE.CylinderGeometry(rockVoxelSize * 0.05, rockVoxelSize * 0.05, rockVoxelSize * 0.8, 6)
        );
        
        const veinMaterial = _getMaterialByHex_Cached(CYAN_ACCENT, {
            transparent: true,
            opacity: 0.7,
            emissive: new THREE.Color(0x00FFFF),
            emissiveIntensity: 0.9
        });
        
        const vein = new THREE.Mesh(veinGeometry, veinMaterial);
        vein.position.set(
            rng() * rockVoxelSize * 4,
            rockVoxelSize * (1 + rng() * 2), // Between rocks
            (rng() - 0.5) * rockVoxelSize * 2
        );
        
        vein.rotation.z = (rng() - 0.5) * Math.PI * 0.3; // Slight angle
        
        vein.castShadow = false;
        vein.receiveShadow = false;
        
        group.add(vein);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'rock_wall_formation',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: false,
        hasCollision: true, // Wall formations have collision
        isInteriorObject: true,
        voxelScale: rockVoxelSize
    };

    group.name = 'rock_wall_formation';

    console.log('[RockWallFormationObject] ✅ Created mystical rock wall formation');
    return group;
}
