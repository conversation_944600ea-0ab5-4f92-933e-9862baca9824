import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Sandstone Floor
 * 
 * Creates sandstone floor surfaces with Egyptian/desert theme.
 * Based on the cave floor structure but with sandstone colors and patterns.
 * Uses the same size and collision detection as normal cave floors.
 */

// Sandstone floor color palette (Egyptian/desert theme)
const SANDSTONE_FLOOR_COLORS = [
    0xF4A460, // Sandy brown (base)
    0xDEB887, // Burlywood (variant 1)
    0xD2B48C, // Tan (variant 2)
    0xF5DEB3, // Wheat (light variant)
    0xDAA520, // Goldenrod (accent)
    0xCD853F, // Peru (darker accent)
    0xB8860B, // Dark goldenrod (cracks)
    0xA0522D  // Sienna (deep cracks)
];

/**
 * Creates a sandstone floor surface using randomized desert tone voxels.
 * Uses the same structure as cave floor but with sandstone materials.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Mesh|THREE.Group|null} The merged floor mesh/group or null if empty.
 */
export function createSandstoneFloor(width, depth, roomData) {
    const geometriesByMaterial = {};
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use same scale as cave floors for consistency
    const FLOOR_SCALE = ENVIRONMENT_PIXEL_SCALE * 2;

    // Grid dimensions (same as cave floor)
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / FLOOR_SCALE);
    const numZ_env = Math.ceil(numZ / FLOOR_SCALE);

    // Centering offsets (same as cave floor)
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    const floorY = 0; // Base floor Y position
    const overlayOffsetY = VOXEL_SIZE * 5.0; // EXTREME offset - 5x voxel height to completely eliminate z-fighting

    // Overlay patch parameters (same as cave floor)
    const overlayPatchSpawnProbability = 0.06;
    const crackOverlayFraction = 0.4;
    const minPatchSize = 2;
    const maxPatchSize = 6;

    // Use cached geometry for floor voxels (same size as cave floor)
    const floorVoxelSize = VOXEL_SIZE * FLOOR_SCALE;
    const floorVoxelGeo = getOrCreateGeometry(
        `sandstone_floor_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, VOXEL_SIZE, floorVoxelSize)
    );

    const overlayGrid = Array(numZ_env).fill(null).map(() => Array(numX_env).fill(false));

    // --- Use Seeded PRNG (same as cave floor) ---
    const roomSeed = roomData ? roomData.id * 47 + 29 : Date.now();
    const random = mulberry32(roomSeed);

    // --- Pass 1: Generate Base Sandstone Floor ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Choose sandstone floor material based on position and randomness
            let materialColor;
            const roll = random();
            const isEdge = (ex === 0 || ex === numX_env - 1 || ez === 0 || ez === numZ_env - 1);
            
            if (roll < 0.1) {
                materialColor = SANDSTONE_FLOOR_COLORS[6]; // Dark goldenrod (cracks)
            } else if (roll < 0.2) {
                materialColor = SANDSTONE_FLOOR_COLORS[7]; // Sienna (deep cracks)
            } else if (isEdge) {
                materialColor = SANDSTONE_FLOOR_COLORS[2]; // Tan (edges)
            } else if (roll < 0.4) {
                materialColor = SANDSTONE_FLOOR_COLORS[1]; // Burlywood (variant)
            } else if (roll < 0.7) {
                materialColor = SANDSTONE_FLOOR_COLORS[3]; // Wheat (light)
            } else {
                materialColor = SANDSTONE_FLOOR_COLORS[0]; // Sandy brown (base)
            }

            const baseX = ex * floorVoxelSize - offsetX;
            const baseZ = ez * floorVoxelSize - offsetZ;

            // Create unified terrain variation (same algorithm as cave floor)
            const globalX = baseX + (roomData.segmentPosition ? roomData.segmentPosition.x : 0);
            const globalZ = baseZ + (roomData.segmentPosition ? roomData.segmentPosition.z : 0);

            const worldX = globalX;
            let worldZ = -globalZ;

            // For U-shaped rooms, apply same adjustment as cave floor
            if (roomData && roomData.shapeKey === 'U_SHAPE_DOWN') {
                worldZ = globalZ;
            }

            // Large-scale room curvature (same as cave floor)
            const largeWaves = Math.sin(worldX * 0.08) * Math.cos(worldZ * 0.06) * 0.2;
            const mediumWaves = (Math.sin(worldX * 0.15) + Math.cos(worldZ * 0.18)) * 0.12;
            const smallDetails = (Math.sin(worldX * 0.4) * Math.cos(worldZ * 0.35)) * 0.06;
            const ridges = Math.sin(worldX * 0.1 + worldZ * 0.08) * 0.15;

            const tileVariationX = Math.sin(globalX * 0.7) * 0.01;
            const tileVariationZ = Math.cos(globalZ * 0.8) * 0.01;
            const tileVariation = tileVariationX + tileVariationZ;

            // Combine all height variations
            const heightVariation = largeWaves + mediumWaves + smallDetails + ridges + tileVariation;
            const finalY = floorY + heightVariation;

            // Position-based rotation (same as cave floor)
            const rotationX = Math.sin(globalX * 0.5 + globalZ * 0.3) * 0.015;
            const rotationZ = Math.cos(globalX * 0.4 + globalZ * 0.6) * 0.015;

            // Create transformation matrix
            tempMatrix.makeRotationFromEuler(new THREE.Euler(rotationX, 0, rotationZ));
            tempMatrix.setPosition(baseX, finalY, baseZ);

            // Create material and add geometry
            const material = _getMaterialByHex_Cached(materialColor.toString(16).padStart(6, '0'));
            if (material) {
                addGeometry(floorVoxelGeo, material, tempMatrix);
            }
        }
    }

    // --- Pass 2: Generate Overlay Patches (same as cave floor) ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            if (overlayGrid[ez][ex] || random() > overlayPatchSpawnProbability) {
                continue;
            }

            const patchSize = minPatchSize + Math.floor(random() * (maxPatchSize - minPatchSize + 1));
            const overlayColor = (random() < crackOverlayFraction)
                                ? SANDSTONE_FLOOR_COLORS[6] // Dark goldenrod (cracks)
                                : SANDSTONE_FLOOR_COLORS[4]; // Goldenrod (accent)
            
            const currentPatchCells = [];
            const queue = [[ex, ez]];
            overlayGrid[ez][ex] = true;
            currentPatchCells.push([ex, ez]);

            let attempts = 0;
            const maxAttempts = patchSize * 5;

            while (currentPatchCells.length < patchSize && queue.length > 0 && attempts < maxAttempts) {
                attempts++;
                const [cx, cz] = queue.shift();

                const neighbours = [
                    [cx + 1, cz], [cx - 1, cz], [cx, cz + 1], [cx, cz - 1]
                ].sort(() => random() - 0.5);

                for (const [nx, nz] of neighbours) {
                    if (nx >= 0 && nx < numX_env && nz >= 0 && nz < numZ_env && !overlayGrid[nz][nx]) {
                        overlayGrid[nz][nx] = true;
                        currentPatchCells.push([nx, nz]);
                        queue.push([nx, nz]);
                        if (currentPatchCells.length >= patchSize) break;
                    }
                }
            }

            // Add geometry for all cells in this patch
            for (const [patchEX, patchEZ] of currentPatchCells) {
                const posX = patchEX * floorVoxelSize - offsetX;
                const posZ = patchEZ * floorVoxelSize - offsetZ;
                tempMatrix.makeTranslation(posX, floorY + overlayOffsetY, posZ);
                
                const overlayMaterial = _getMaterialByHex_Cached(overlayColor.toString(16).padStart(6, '0'));
                if (overlayMaterial) {
                    addGeometry(floorVoxelGeo, overlayMaterial, tempMatrix);
                }
            }
        }
    }

    // --- Merge Geometries (same as cave floor) ---
    const finalMeshes = [];

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) continue;

        const finalMaterial = originalMaterial.clone();
        
        // Apply polygon offset to prevent z-fighting (same as cave floor)
        finalMaterial.polygonOffset = true;
        finalMaterial.polygonOffsetFactor = 1.0;
        finalMaterial.polygonOffsetUnits = 1.0;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.receiveShadow = true;
            // CRITICAL: Mark mesh with proper userData for collision detection (same as cave floor)
            mesh.userData.isFloor = true;
            mesh.userData.floorType = 'sandstone_floor';
            finalMeshes.push(mesh);
        }
    }

    if (finalMeshes.length === 0) return null;

    // Return single mesh or group (same as cave floor)
    if (finalMeshes.length === 1) {
        return finalMeshes[0];
    }

    const finalGroup = new THREE.Group();
    finalGroup.name = "sandstoneFloor";
    // CRITICAL: Mark group with proper userData for collision detection
    finalGroup.userData.isFloor = true;
    finalGroup.userData.floorType = 'sandstone_floor';
    finalMeshes.forEach(mesh => {
        finalGroup.add(mesh);
    });
    return finalGroup;
}

// Register the new floor type
if (typeof window !== 'undefined' && window.prefabRegistry) {
    window.prefabRegistry.registerFloor('sandstone_floor', createSandstoneFloor);
}
