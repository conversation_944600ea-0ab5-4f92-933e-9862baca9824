import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Void Rupture Card Prefab
 * Creates a dark void tear with swirling void fragments
 */

// Void rupture specific colors
const VOID_COLORS = {
    VOID_PURPLE: 0x2E0066,           // Deep purple void
    VOID_BLACK: 0x1a1a1a,           // Dark black void
    VOID_EDGE: 0x4B0082,            // Indigo void edge
    REALITY_TEAR: 0x0D0D0D          // Almost black reality tear
};

/**
 * Create a void rupture card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The void rupture card 3D model
 */
export function createVoidRuptureCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'VoidRuptureCard';

    // Void materials
    const voidPurpleMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.VOID_PURPLE,
        emissive: VOID_COLORS.VOID_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const voidBlackMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.VOID_BLACK,
        emissive: VOID_COLORS.VOID_BLACK,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const voidEdgeMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.VOID_EDGE,
        emissive: VOID_COLORS.VOID_EDGE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const realityTearMaterial = new THREE.MeshLambertMaterial({
        color: VOID_COLORS.REALITY_TEAR,
        emissive: VOID_COLORS.REALITY_TEAR,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.95
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Central void core (dark sphere representing the tear)
    const voidCoreVoxels = [
        // Central core
        { x: 0, y: 0, z: 0, material: realityTearMaterial },
        { x: 0, y: 0.05, z: 0, material: voidBlackMaterial },
        { x: 0, y: -0.05, z: 0, material: voidBlackMaterial },
        { x: 0.05, y: 0, z: 0, material: voidBlackMaterial },
        { x: -0.05, y: 0, z: 0, material: voidBlackMaterial },
        
        // Core ring
        { x: 0.1, y: 0, z: 0, material: voidPurpleMaterial },
        { x: -0.1, y: 0, z: 0, material: voidPurpleMaterial },
        { x: 0, y: 0.1, z: 0, material: voidPurpleMaterial },
        { x: 0, y: -0.1, z: 0, material: voidPurpleMaterial }
    ];

    // Void fragments (swirling dark pieces)
    const voidFragmentVoxels = [
        // Inner ring fragments
        { x: 0.15, y: 0.05, z: 0, material: voidEdgeMaterial },
        { x: -0.15, y: -0.05, z: 0, material: voidEdgeMaterial },
        { x: 0.05, y: 0.15, z: 0, material: voidBlackMaterial },
        { x: -0.05, y: -0.15, z: 0, material: voidBlackMaterial },
        
        // Outer ring fragments
        { x: 0.25, y: 0, z: 0, material: voidPurpleMaterial },
        { x: -0.25, y: 0, z: 0, material: voidPurpleMaterial },
        { x: 0, y: 0.25, z: 0, material: voidEdgeMaterial },
        { x: 0, y: -0.25, z: 0, material: voidEdgeMaterial },
        
        // Scattered void particles
        { x: 0.2, y: 0.15, z: 0, material: voidBlackMaterial },
        { x: -0.2, y: -0.15, z: 0, material: voidBlackMaterial },
        { x: 0.15, y: -0.2, z: 0, material: voidPurpleMaterial },
        { x: -0.15, y: 0.2, z: 0, material: voidPurpleMaterial },
        
        // Edge distortion
        { x: 0.3, y: 0.05, z: 0, material: voidEdgeMaterial },
        { x: -0.3, y: -0.05, z: 0, material: voidEdgeMaterial },
        { x: 0.05, y: 0.3, z: 0, material: realityTearMaterial },
        { x: -0.05, y: -0.3, z: 0, material: realityTearMaterial }
    ];

    // Create void core group
    const voidCoreGroup = new THREE.Group();
    voidCoreGroup.name = 'voidCore';

    // Add void core voxels
    voidCoreVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        voidCoreGroup.add(mesh);
    });

    // Create void fragment group
    const voidFragmentGroup = new THREE.Group();
    voidFragmentGroup.name = 'voidFragments';

    // Add void fragment voxels
    voidFragmentVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        mesh.userData.fragmentPhase = index * 0.2; // Stagger animation
        voidFragmentGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(voidCoreGroup);
    cardGroup.add(voidFragmentGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        voidRotation: 0,
        voidPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update void rupture card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateVoidRuptureCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.voidRotation += deltaTime * 2.0; // Void rotation speed
    cardGroup.userData.voidPulse += deltaTime * 3.0; // Void pulse speed

    const time = cardGroup.userData.animationTime;
    const voidRotation = cardGroup.userData.voidRotation;
    const voidPulse = cardGroup.userData.voidPulse;

    // Animate void core (counter-clockwise rotation)
    const voidCoreGroup = cardGroup.getObjectByName('voidCore');
    if (voidCoreGroup) {
        voidCoreGroup.rotation.z = -voidRotation;
        
        // Void core pulsing (dark energy)
        const corePulse = 0.3 + Math.sin(voidPulse * 2) * 0.3;
        voidCoreGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * corePulse;
            }
        });
    }

    // Animate void fragments (inward spiral motion)
    const voidFragmentGroup = cardGroup.getObjectByName('voidFragments');
    if (voidFragmentGroup) {
        voidFragmentGroup.rotation.z = voidRotation * 1.5; // Faster rotation
        
        // Fragment spiraling inward
        voidFragmentGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.fragmentPhase !== undefined) {
                const fragmentTime = voidPulse + mesh.userData.fragmentPhase;
                
                // Spiral inward motion
                const spiralRadius = 0.02;
                const spiralX = Math.cos(fragmentTime * 2) * spiralRadius;
                const spiralY = Math.sin(fragmentTime * 2) * spiralRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + spiralX;
                mesh.position.y = mesh.userData.originalPosition.y + spiralY;
                
                // Void energy fluctuation
                const voidFluctuation = 0.4 + Math.sin(fragmentTime * 4) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * voidFluctuation;
                }
                
                // Fade in/out for void distortion effect
                const fadeCycle = 0.5 + Math.sin(fragmentTime * 1.8) * 0.5;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fadeCycle;
                }
            }
        });
    }

    // Gentle overall void pulsing
    const voidPulseScale = 1 + Math.sin(time * 1.5) * 0.08;
    cardGroup.scale.setScalar(0.8 * voidPulseScale);
}

// Export the void rupture card data for the loot system
export const VOID_RUPTURE_CARD_DATA = {
    name: 'Void Rupture',
    description: 'Tears open a rift in reality itself, creating a swirling void that pulls enemies toward its center while dealing continuous damage. The dark energy consumes all light in its vicinity.',
    category: 'card',
    rarity: 'epic',
    effect: 'void_tear',
    effectValue: 35, // Damage per second
    createFunction: createVoidRuptureCard,
    updateFunction: updateVoidRuptureCardAnimation,
    voxelModel: 'void_rupture_card',
    glow: {
        color: 0x2E0066,
        intensity: 0.8
    }
};

export default createVoidRuptureCard;