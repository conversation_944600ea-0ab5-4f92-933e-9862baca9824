import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Stone Brick Color Palette (matching ancient stone pillars) ---
// Adapted from ancient stone pillar colors
const STONE_BRICK_COLORS = [
    '555c66', // Muted grey stone (primary pillar color)
    '606873', // Slight variation grey (secondary pillar color)
    '707880', // Cleaner grey center (lightest pillar color)
    '4a4a4a', // Dark gray for shadows and details
    '5a5a5a', // Medium dark gray
    '6a6a6a', // Medium gray
    '7a7a7a', // Light gray
    '8a8a8a'  // Very light gray
];

const STONE_ACCENT = '707880'; // Cleaner grey center (for highlights)
const LIGHT_MYSTICAL = 'BB9EDD'; // Light mystical for highlights

// Define large stalagmite formation (rising from floor) - MUCH BIGGER like crystal cave
const stalagmiteFormation = [
    // === FLOOR BASE (5x5 like crystal cave base) ===
    // Layer 1 - Floor base (Y=0)
    { x: -2, y: 0, z: -2, c: STONE_BRICK_COLORS[0] },
    { x: -1, y: 0, z: -2, c: STONE_BRICK_COLORS[1] },
    { x: 0, y: 0, z: -2, c: STONE_BRICK_COLORS[0] },
    { x: 1, y: 0, z: -2, c: STONE_BRICK_COLORS[1] },
    { x: 2, y: 0, z: -2, c: STONE_BRICK_COLORS[0] },

    { x: -2, y: 0, z: -1, c: STONE_BRICK_COLORS[1] },
    { x: -1, y: 0, z: -1, c: STONE_BRICK_COLORS[0] },
    { x: 0, y: 0, z: -1, c: STONE_BRICK_COLORS[1] },
    { x: 1, y: 0, z: -1, c: STONE_BRICK_COLORS[0] },
    { x: 2, y: 0, z: -1, c: STONE_BRICK_COLORS[1] },

    { x: -2, y: 0, z: 0, c: STONE_BRICK_COLORS[0] },
    { x: -1, y: 0, z: 0, c: STONE_BRICK_COLORS[1] },
    { x: 0, y: 0, z: 0, c: STONE_BRICK_COLORS[0] },
    { x: 1, y: 0, z: 0, c: STONE_BRICK_COLORS[1] },
    { x: 2, y: 0, z: 0, c: STONE_BRICK_COLORS[0] },

    { x: -2, y: 0, z: 1, c: STONE_BRICK_COLORS[1] },
    { x: -1, y: 0, z: 1, c: STONE_BRICK_COLORS[0] },
    { x: 0, y: 0, z: 1, c: STONE_BRICK_COLORS[1] },
    { x: 1, y: 0, z: 1, c: STONE_BRICK_COLORS[0] },
    { x: 2, y: 0, z: 1, c: STONE_BRICK_COLORS[1] },

    { x: -2, y: 0, z: 2, c: STONE_BRICK_COLORS[0] },
    { x: -1, y: 0, z: 2, c: STONE_BRICK_COLORS[1] },
    { x: 0, y: 0, z: 2, c: STONE_BRICK_COLORS[0] },
    { x: 1, y: 0, z: 2, c: STONE_BRICK_COLORS[1] },
    { x: 2, y: 0, z: 2, c: STONE_BRICK_COLORS[0] },

    // === LOWER STALAGMITE BODY (Y=1) ===
    { x: -1, y: 1, z: -1, c: STONE_BRICK_COLORS[2] },
    { x: 0, y: 1, z: -1, c: STONE_BRICK_COLORS[1] },
    { x: 1, y: 1, z: -1, c: STONE_BRICK_COLORS[2] },
    { x: -1, y: 1, z: 0, c: STONE_BRICK_COLORS[1] },
    { x: 0, y: 1, z: 0, c: STONE_BRICK_COLORS[0] },
    { x: 1, y: 1, z: 0, c: STONE_BRICK_COLORS[1] },
    { x: -1, y: 1, z: 1, c: STONE_BRICK_COLORS[2] },
    { x: 0, y: 1, z: 1, c: STONE_BRICK_COLORS[1] },
    { x: 1, y: 1, z: 1, c: STONE_BRICK_COLORS[2] },

    // === MID-LOWER BODY (Y=2) ===
    { x: -1, y: 2, z: 0, c: STONE_BRICK_COLORS[3] },
    { x: 0, y: 2, z: -1, c: STONE_BRICK_COLORS[2] },
    { x: 0, y: 2, z: 0, c: STONE_BRICK_COLORS[1] },
    { x: 0, y: 2, z: 1, c: STONE_BRICK_COLORS[2] },
    { x: 1, y: 2, z: 0, c: STONE_BRICK_COLORS[3] },

    // === CENTRAL BODY (Y=3) ===
    { x: 0, y: 3, z: -1, c: STONE_BRICK_COLORS[4] },
    { x: 0, y: 3, z: 0, c: STONE_BRICK_COLORS[2] },
    { x: 0, y: 3, z: 1, c: STONE_BRICK_COLORS[4] },

    // === MID-UPPER BODY (Y=4) ===
    { x: 0, y: 4, z: 0, c: STONE_BRICK_COLORS[3] },
    { x: 0, y: 4, z: -1, c: STONE_BRICK_COLORS[5] },
    { x: 0, y: 4, z: 1, c: STONE_BRICK_COLORS[5] },

    // === UPPER BODY (Y=5) ===
    { x: 0, y: 5, z: 0, c: STONE_BRICK_COLORS[4] },

    // === NEAR TIP (Y=6) ===
    { x: 0, y: 6, z: 0, c: STONE_BRICK_COLORS[6] },

    // === TIP POINT (Y=7) ===
    { x: 0, y: 7, z: 0, c: STONE_BRICK_COLORS[7] },

    // === STONE ACCENT HIGHLIGHTS (like crystal cave shards but stone) ===
    { x: -1, y: 1, z: -1, c: STONE_ACCENT }, // Stone accent on lower body
    { x: 1, y: 2, z: 1, c: STONE_ACCENT },   // Stone accent on mid body
    { x: 0, y: 3, z: 0, c: STONE_ACCENT },   // Stone accent on central core
];

/**
 * Create a large mystical stalagmite rising from the floor (like crystal cave scale)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Stalagmite object group
 */
export function createStalactiteObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);

    // Use HALF FLOOR VOXEL SIZE for smaller stalagmites (VOXEL_SIZE * 3 = half floor scale)
    const stalagmiteVoxelSize = VOXEL_SIZE * 3;
    const baseGeometry = getOrCreateGeometry('stalagmite_floor_voxel', () =>
        new THREE.BoxGeometry(stalagmiteVoxelSize, stalagmiteVoxelSize, stalagmiteVoxelSize)
    );

    // Create mystical materials with pond room colors
    const mysticalMaterials = {};
    MYSTICAL_COLORS.forEach(color => {
        mysticalMaterials[color] = _getMaterialByHex_Cached(color, {
            emissive: new THREE.Color(`0x${color}`).multiplyScalar(0.1),
            emissiveIntensity: 0.2,
            roughness: 0.7,
            metalness: 0.1
        });
    });

    // Special cyan accent material (glowing like pond)
    const cyanMaterial = _getMaterialByHex_Cached(CYAN_ACCENT, {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x00FFFF),
        emissiveIntensity: 0.8,
        roughness: 0.3,
        metalness: 0.2
    });

    // Build the large stalagmite formation
    stalagmiteFormation.forEach(voxel => {
        const finalX = voxel.x * stalagmiteVoxelSize;
        const finalY = voxel.y * stalagmiteVoxelSize;
        const finalZ = voxel.z * stalagmiteVoxelSize;

        // Choose material based on color
        let material;
        if (voxel.c === CYAN_ACCENT) {
            material = cyanMaterial;
        } else {
            material = mysticalMaterials[voxel.c];
        }

        const stalagmiteVoxel = new THREE.Mesh(baseGeometry.clone(), material);
        stalagmiteVoxel.position.set(finalX, finalY, finalZ);

        // Cyan accents don't cast shadows (they glow)
        stalagmiteVoxel.castShadow = voxel.c !== CYAN_ACCENT;
        stalagmiteVoxel.receiveShadow = true;

        // Add slight random rotation for natural look
        stalagmiteVoxel.rotation.y = (rng() - 0.5) * 0.2;

        group.add(stalagmiteVoxel);
    });

    // Add mystical crystal growths (small protruding elements)
    const crystalPositions = [
        { x: -0.5, y: 7.5, z: -0.5 },
        { x: 0.5, y: 7.5, z: 0.5 },
        { x: 0, y: 7.8, z: 0 }
    ];

    crystalPositions.forEach(pos => {
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), mysticalMaterials[MYSTICAL_COLORS[6]]);
        crystalVoxel.position.set(
            pos.x * stalagmiteVoxelSize,
            pos.y * stalagmiteVoxelSize,
            pos.z * stalagmiteVoxelSize
        );
        crystalVoxel.scale.set(0.3, 0.6, 0.3); // Small crystal growth shape
        crystalVoxel.castShadow = true;
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'stalagmite',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: true, // Rising from floor, has collision
        isInteriorObject: true,
        voxelScale: stalagmiteVoxelSize
    };

    group.name = 'stalagmite';

    console.log('[StalactiteObject] ✅ Created large mystical stalagmite formation');
    return group;
}
