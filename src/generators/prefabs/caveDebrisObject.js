import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached,
    // Use stone and earth materials
    stoneGrain1, // 958272
    stoneGrain2, // 9D8A7A
    stoneGrain3, // A59080
    stoneGrain4, // AA9586
    caveFloorCrackMaterial // 4a3a2a (For darker details)
} from './shared.js';

// --- Pond Room Mystical Debris Colors ---
// Adapted from pond room mystical theme with darker tones
const MYSTICAL_DEBRIS_COLORS = [
    '6A5C88', // Deep mystical purple (from pond room)
    '5A4C78', // Very deep mystical
    '4A3C68', // Almost dark mystical
    '3A3A5A', // Mystical purple-gray (ambient)
    '2A2A4A', // Very dark mystical
    '1A1A3A', // Almost black mystical
    '4A6C88', // Mystical blue-gray
    '3A5C78'  // Dark mystical blue-gray
];

// Define LARGE mystical cave debris formation (like crystal cave complexity)
const mysticalDebrisFormation = [
    // === MAIN DEBRIS PILE (3x3 base) ===
    { x: -1, y: 0, z: -1, c: MYSTICAL_DEBRIS_COLORS[0] },
    { x: 0, y: 0, z: -1, c: MYSTICAL_DEBRIS_COLORS[1] },
    { x: 1, y: 0, z: -1, c: MYSTICAL_DEBRIS_COLORS[0] },
    { x: -1, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[1] },
    { x: 0, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[0] }, // Center
    { x: 1, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[1] },
    { x: -1, y: 0, z: 1, c: MYSTICAL_DEBRIS_COLORS[0] },
    { x: 0, y: 0, z: 1, c: MYSTICAL_DEBRIS_COLORS[1] },
    { x: 1, y: 0, z: 1, c: MYSTICAL_DEBRIS_COLORS[0] },

    // === UPPER LAYER (smaller pile on top) ===
    { x: -1, y: 1, z: 0, c: MYSTICAL_DEBRIS_COLORS[2] },
    { x: 0, y: 1, z: -1, c: MYSTICAL_DEBRIS_COLORS[3] },
    { x: 0, y: 1, z: 0, c: MYSTICAL_DEBRIS_COLORS[2] },
    { x: 0, y: 1, z: 1, c: MYSTICAL_DEBRIS_COLORS[3] },
    { x: 1, y: 1, z: 0, c: MYSTICAL_DEBRIS_COLORS[2] },

    // === TOP LAYER (peak) ===
    { x: 0, y: 2, z: 0, c: MYSTICAL_DEBRIS_COLORS[4] },

    // === SCATTERED OUTER DEBRIS ===
    { x: -2, y: 0, z: -1, c: MYSTICAL_DEBRIS_COLORS[3] },
    { x: -2, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[4] },
    { x: -2, y: 0, z: 1, c: MYSTICAL_DEBRIS_COLORS[3] },

    { x: 2, y: 0, z: -1, c: MYSTICAL_DEBRIS_COLORS[3] },
    { x: 2, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[4] },
    { x: 2, y: 0, z: 1, c: MYSTICAL_DEBRIS_COLORS[3] },

    { x: -1, y: 0, z: -2, c: MYSTICAL_DEBRIS_COLORS[5] },
    { x: 0, y: 0, z: -2, c: MYSTICAL_DEBRIS_COLORS[4] },
    { x: 1, y: 0, z: -2, c: MYSTICAL_DEBRIS_COLORS[5] },

    { x: -1, y: 0, z: 2, c: MYSTICAL_DEBRIS_COLORS[5] },
    { x: 0, y: 0, z: 2, c: MYSTICAL_DEBRIS_COLORS[4] },
    { x: 1, y: 0, z: 2, c: MYSTICAL_DEBRIS_COLORS[5] },

    // === CORNER DEBRIS CLUSTERS ===
    { x: -2, y: 0, z: -2, c: MYSTICAL_DEBRIS_COLORS[6] },
    { x: 2, y: 0, z: -2, c: MYSTICAL_DEBRIS_COLORS[6] },
    { x: -2, y: 0, z: 2, c: MYSTICAL_DEBRIS_COLORS[6] },
    { x: 2, y: 0, z: 2, c: MYSTICAL_DEBRIS_COLORS[6] },

    // === VERY SCATTERED OUTER BITS ===
    { x: -3, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[7] },
    { x: 3, y: 0, z: 0, c: MYSTICAL_DEBRIS_COLORS[7] },
    { x: 0, y: 0, z: -3, c: MYSTICAL_DEBRIS_COLORS[7] },
    { x: 0, y: 0, z: 3, c: MYSTICAL_DEBRIS_COLORS[7] },

    // === SMALL ELEVATED DEBRIS ===
    { x: -1, y: 1, z: -1, c: MYSTICAL_DEBRIS_COLORS[5] },
    { x: 1, y: 1, z: 1, c: MYSTICAL_DEBRIS_COLORS[5] },
    { x: -1, y: 1, z: 1, c: MYSTICAL_DEBRIS_COLORS[6] },
    { x: 1, y: 1, z: -1, c: MYSTICAL_DEBRIS_COLORS[6] }
];

/**
 * Create large mystical cave debris formation (like crystal cave scale)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Cave debris object group
 */
export function createCaveDebrisObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 1111);

    // Use FLOOR VOXEL SIZE for proper scale (VOXEL_SIZE * 6 = floor scale)
    const debrisVoxelSize = VOXEL_SIZE * 6;

    // Create mystical debris materials with pond room colors
    const mysticalDebrisMaterials = {};
    MYSTICAL_DEBRIS_COLORS.forEach(color => {
        mysticalDebrisMaterials[color] = _getMaterialByHex_Cached(color, {
            emissive: new THREE.Color(`0x${color}`).multiplyScalar(0.05),
            emissiveIntensity: 0.1,
            roughness: 0.9,
            metalness: 0.1
        });
    });

    // Build the large mystical debris formation
    mysticalDebrisFormation.forEach(voxel => {
        // Skip some outer voxels randomly for natural variation
        if (Math.abs(voxel.x) > 2 || Math.abs(voxel.z) > 2) {
            if (rng() < 0.2) return; // 20% chance to skip outer debris
        }

        // Vary voxel size for natural debris look
        const sizeVariation = 0.7 + rng() * 0.6; // 0.7x to 1.3x size
        const voxelSize = debrisVoxelSize * sizeVariation;

        const geometry = getOrCreateGeometry(`mystical_debris_voxel_${sizeVariation.toFixed(2)}`, () =>
            new THREE.BoxGeometry(
                voxelSize,
                voxelSize * (0.4 + rng() * 0.8), // Varied height 0.4x to 1.2x
                voxelSize
            )
        );

        // Add position variation for organic scatter
        const finalX = voxel.x * debrisVoxelSize + (rng() - 0.5) * debrisVoxelSize * 0.3;
        const finalY = voxel.y * debrisVoxelSize;
        const finalZ = voxel.z * debrisVoxelSize + (rng() - 0.5) * debrisVoxelSize * 0.3;

        const debrisVoxel = new THREE.Mesh(geometry, mysticalDebrisMaterials[voxel.c]);
        debrisVoxel.position.set(finalX, finalY, finalZ);

        // Add natural rotation and slight scale variation
        debrisVoxel.rotation.set(
            (rng() - 0.5) * 0.3,
            (rng() - 0.5) * Math.PI,
            (rng() - 0.5) * 0.3
        );

        debrisVoxel.castShadow = true;
        debrisVoxel.receiveShadow = true;

        group.add(debrisVoxel);
    });

    // Add mystical dust particles (small floating elements)
    const dustCount = 2 + Math.floor(rng() * 3); // 2-4 dust particles
    for (let i = 0; i < dustCount; i++) {
        const dustGeometry = getOrCreateGeometry('mystical_dust', () =>
            new THREE.SphereGeometry(debrisVoxelSize * 0.05, 6, 4)
        );

        const dustMaterial = _getMaterialByHex_Cached(MYSTICAL_DEBRIS_COLORS[6], {
            transparent: true,
            opacity: 0.4,
            emissive: new THREE.Color(0x4A6C88),
            emissiveIntensity: 0.6
        });

        const dust = new THREE.Mesh(dustGeometry, dustMaterial);
        dust.position.set(
            (rng() - 0.5) * debrisVoxelSize * 3,
            debrisVoxelSize * (0.2 + rng() * 1.0), // Float above debris
            (rng() - 0.5) * debrisVoxelSize * 3
        );

        dust.castShadow = false;
        dust.receiveShadow = false;

        group.add(dust);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'cave_debris',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: false, // Can walk through debris
        isInteriorObject: true,
        voxelScale: debrisVoxelSize
    };

    group.name = 'cave_debris';
    
    // Mark as small ground decoration that shouldn't block movement
    group.userData.isGroundDecoration = true;
    group.userData.isSmallObject = true;

    console.log('[CaveDebrisObject] ✅ Created large mystical cave debris formation');
    return group;
}
