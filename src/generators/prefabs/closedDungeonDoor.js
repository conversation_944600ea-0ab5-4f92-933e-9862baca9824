import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Closed Dungeon Door - Voxel-style medieval door matching screenshot aesthetic
 * Features arched stone frame with wooden/metal door panels and metal reinforcements
 * Uses geometry merging for seamless voxel construction like other game models
 */
export function createClosedDungeonDoor(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);
    
    const doorStyle = options.doorStyle || 'wooden'; // 'wooden', 'reinforced', 'iron'
    const archHeight = options.archHeight || 32; // Height in voxels - increased for more detail
    const doorWidth = options.doorWidth || 24; // Width in voxels - increased for better proportions
    
    // Use optimal voxel size for 12x scale - balanced between detail and performance
    const doorVoxelSize = VOXEL_SIZE * 0.5; // Medium-small voxels for good detail at large scale
    const baseGeometry = getOrCreateGeometry('door_voxel', () =>
        new THREE.BoxGeometry(doorVoxelSize, doorVoxelSize, doorVoxelSize)
    );
    
    // Geometry merging setup like other game models
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};
    
    // Enhanced material colors to match faded yellowish ancient stone (matches screenshot)
    let stoneColor = '8B8B7A';      // Faded yellowish stone frame (matches floor primary)
    let stoneDark = '6B6B5A';       // Dark faded stone (matches floor shadows)
    let stoneLight = 'A0A089';      // Light faded yellow stone (matches floor secondary)
    let doorColor, doorDark, metalColor, metalLight, metalDark, decorColor, accentColor;
    
    switch (doorStyle) {
        case 'wooden':
            doorColor = '8B4513';   // Brown wood
            doorDark = '654321';    // Dark brown wood
            metalColor = '3A3A35';  // Muted dark metal (warmer, less bright)
            metalLight = '4A4A42';  // Muted light metal (warmer tone)
            metalDark = '2A2A25';   // Muted very dark metal (warmer)
            decorColor = '654321';  // Dark brown
            accentColor = 'CD853F';  // Light wood accent
            break;
        case 'reinforced':
            doorColor = '2F4F4F';   // Dark slate
            doorDark = '1F2F2F';    // Very dark slate
            metalColor = '1C1C18';  // Muted very dark metal (warmer)
            metalLight = '2C2C28';  // Muted dark gray metal (warmer)
            metalDark = '0C0C0A';   // Muted black metal (warmer)
            decorColor = '8B4B3D';  // Muted dark red-brown (warmer, matches stone tone)
            accentColor = 'B8654F';  // Warm muted red (less bright, more earthen)
            break;
        case 'iron':
            doorColor = '2C2C2C';   // Dark gray
            doorDark = '1A1A1A';    // Very dark gray
            metalColor = '1A1A16';  // Muted black metal (warmer)
            metalLight = '3A3A35';  // Muted dark gray metal (warmer)
            metalDark = '0A0A08';   // Muted very black metal (warmer)
            decorColor = 'B8A082';  // Muted golden bronze (warmer, less bright than gold)
            accentColor = 'D4AF7A';  // Warm brass tone (softer than orange-gold)
            break;
    }
    
    // Helper function to add voxel to geometry collection
    const addVoxel = (x, y, z, colorHex) => {
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        
        tempMatrix.makeTranslation(
            x * doorVoxelSize,
            y * doorVoxelSize,
            z * doorVoxelSize
        );
        
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[colorHex].push(voxelGeometry);
    };
    
    // === ENHANCED STONE FRAME CONSTRUCTION ===
    const lintelY = archHeight - 6; // More space for detailed arch
    const pillarWidth = 3; // Wider pillars for more impressive look
    
    // === FOUNDATION WITH STEPS ===
    // Foundation step 1 (widest)
    for (let x = -doorWidth/2 - pillarWidth - 1; x <= doorWidth/2 + pillarWidth + 1; x++) {
        addVoxel(x, 0, 0, stoneDark);
        addVoxel(x, 0, 1, stoneDark);
    }
    
    // Foundation step 2 (medium)
    for (let x = -doorWidth/2 - pillarWidth; x <= doorWidth/2 + pillarWidth; x++) {
        addVoxel(x, 1, 0, stoneColor);
    }
    
    // Foundation step 3 (narrow)
    for (let x = -doorWidth/2 - pillarWidth + 1; x <= doorWidth/2 + pillarWidth - 1; x++) {
        addVoxel(x, 2, 0, stoneLight);
    }
    
    // === DETAILED PILLARS WITH CAPITALS ===
    for (let y = 3; y < lintelY - 2; y++) {
        // Left pillar core (3 voxels wide)
        for (let x = -doorWidth/2 - pillarWidth; x < -doorWidth/2; x++) {
            let color = stoneColor;
            // Add depth variation
            if (x === -doorWidth/2 - pillarWidth) color = stoneDark; // Outer edge
            if (x === -doorWidth/2 - 1) color = stoneLight; // Inner edge
            addVoxel(x, y, 0, color);
            
            // Add pillar depth
            if (y % 4 === 0) addVoxel(x, y, 1, stoneDark); // Shadow lines
        }
        
        // Right pillar core (3 voxels wide)
        for (let x = doorWidth/2 + 1; x <= doorWidth/2 + pillarWidth; x++) {
            let color = stoneColor;
            // Add depth variation
            if (x === doorWidth/2 + pillarWidth) color = stoneDark; // Outer edge
            if (x === doorWidth/2 + 1) color = stoneLight; // Inner edge
            addVoxel(x, y, 0, color);
            
            // Add pillar depth
            if (y % 4 === 0) addVoxel(x, y, 1, stoneDark); // Shadow lines
        }
    }
    
    // === PILLAR CAPITALS ===
    const capitalY = lintelY - 2;
    // Left capital
    for (let x = -doorWidth/2 - pillarWidth - 1; x < -doorWidth/2; x++) {
        addVoxel(x, capitalY, 0, stoneLight);
        addVoxel(x, capitalY + 1, 0, stoneColor);
        if (Math.abs(x + doorWidth/2 + pillarWidth/2) <= 1) {
            addVoxel(x, capitalY, 1, decorColor); // Capital decoration
        }
    }
    
    // Right capital
    for (let x = doorWidth/2 + 1; x <= doorWidth/2 + pillarWidth + 1; x++) {
        addVoxel(x, capitalY, 0, stoneLight);
        addVoxel(x, capitalY + 1, 0, stoneColor);
        if (Math.abs(x - doorWidth/2 - pillarWidth/2) <= 1) {
            addVoxel(x, capitalY, 1, decorColor); // Capital decoration
        }
    }
    
    // === DETAILED ARCH CONSTRUCTION ===
    const archRadius = (doorWidth/2) + 2;
    const archCenterY = lintelY;
    const archSteps = Math.ceil(archRadius * 6); // Double the steps for smoother arch
    
    // Track placed voxels to avoid gaps
    const placedVoxels = new Set();
    
    for (let i = 0; i <= archSteps; i++) {
        const angle = (i / archSteps) * Math.PI;
        const x = Math.cos(angle) * archRadius;
        const y = Math.sin(angle) * archRadius * 0.8; // Taller arch
        
        const voxelX = Math.round(x);
        const voxelY = Math.round(archCenterY + y);
        const voxelKey = `${voxelX},${voxelY}`;
        
        // Multi-layer arch construction
        if (Math.abs(voxelX) <= doorWidth/2 + pillarWidth && voxelY < archHeight) {
            // Outer arch layer
            if (!placedVoxels.has(voxelKey)) {
                addVoxel(voxelX, voxelY, 0, stoneColor);
                addVoxel(voxelX, voxelY, 1, stoneDark);
                placedVoxels.add(voxelKey);
            }
            
            // Inner arch layer
            if (Math.abs(voxelX) <= doorWidth/2 + pillarWidth - 1) {
                const innerKey = `${voxelX},${voxelY - 1}`;
                if (!placedVoxels.has(innerKey)) {
                    addVoxel(voxelX, voxelY - 1, 0, stoneLight);
                    placedVoxels.add(innerKey);
                }
            }
            
            // Arch decorative elements
            if (i % 3 === 0 && Math.abs(voxelX) <= doorWidth/2 + 1) {
                addVoxel(voxelX, voxelY, 2, decorColor);
            }
        }
    }
    
    // Fill any remaining gaps in the arch by checking adjacent positions
    for (let x = -archRadius; x <= archRadius; x++) {
        for (let y = 0; y <= archRadius; y++) {
            const realY = archCenterY + y;
            const distanceFromCenter = Math.sqrt(x * x + y * y);
            
            // Check if this position should be part of the arch
            if (distanceFromCenter >= archRadius - 1 && distanceFromCenter <= archRadius + 1 && 
                Math.abs(x) <= doorWidth/2 + pillarWidth && realY < archHeight) {
                
                const voxelKey = `${x},${realY}`;
                if (!placedVoxels.has(voxelKey)) {
                    // Check if we have neighbors (indicating this should be filled)
                    const hasNeighbor = placedVoxels.has(`${x-1},${realY}`) || 
                                      placedVoxels.has(`${x+1},${realY}`) || 
                                      placedVoxels.has(`${x},${realY-1}`) || 
                                      placedVoxels.has(`${x},${realY+1}`);
                    
                    if (hasNeighbor) {
                        addVoxel(x, realY, 0, stoneColor);
                        addVoxel(x, realY, 1, stoneDark);
                        placedVoxels.add(voxelKey);
                    }
                }
            }
        }
    }
    
    // === KEYSTONE (top center of arch) - REMOVED ===
    // Keystone decorative elements removed for cleaner arch appearance
    
    // === ENHANCED DOOR PANELS CONSTRUCTION ===
    const panelInset = 1; // Inset from frame edges
    
    // === BASE DOOR PANELS ===
    for (let x = -doorWidth/2 + panelInset; x <= doorWidth/2 - panelInset; x++) {
        for (let y = 4; y <= lintelY + 2; y++) { // Extended to blend with arch
            let panelColor = doorColor;
            let includeVoxel = true;
            
            // Check if we're in the arch area - create arch-shaped door top
            if (y >= lintelY - 1) {
                const archRadius = (doorWidth/2) + 2;
                const archCenterY = lintelY;
                const distanceFromArchCenter = Math.sqrt(x * x + Math.pow(y - archCenterY, 2));
                
                // Only include voxels that are inside the arch shape
                if (distanceFromArchCenter > archRadius * 0.75) {
                    includeVoxel = false; // Outside arch boundary
                }
                
                // Blend with arch construction
                if (includeVoxel && y >= lintelY) {
                    // Use arch-appropriate colors for blending
                    panelColor = doorDark; // Darker for depth
                }
            }
            
            // Add depth variation to panels
            if ((x + y) % 2 === 0) panelColor = doorDark;
            
            // Style-specific panel designs
            if (doorStyle === 'wooden') {
                // Wooden planks pattern
                if (y % 6 === 0) panelColor = doorDark; // Horizontal plank lines
                
                // Small arched window at top
                if (y >= lintelY - 4 && Math.abs(x) <= 3) {
                    const windowRadius = 2.5;
                    const windowCenterY = lintelY - 2;
                    const distFromCenter = Math.sqrt((x * x) + Math.pow(y - windowCenterY, 2));
                    if (distFromCenter <= windowRadius) {
                        includeVoxel = false; // Window opening
                    }
                }
            } else if (doorStyle === 'reinforced') {
                // Diamond pattern with reinforcement gaps
                const centerX = 0;
                const centerY = (4 + lintelY - 2) / 2;
                const diamondPattern = Math.abs(x - centerX) + Math.abs(y - centerY);
                
                if (diamondPattern % 4 === 0 && Math.abs(x) > 2 && y > 8 && y < lintelY - 4) {
                    includeVoxel = false; // Diamond gaps
                } else if (diamondPattern % 8 === 0) {
                    panelColor = accentColor; // Accent diamonds
                }
            } else if (doorStyle === 'iron') {
                // Cross-hatch metal pattern
                if ((x + y) % 3 === 0) panelColor = doorDark;
                if ((x - y) % 6 === 0) panelColor = metalLight;
            }
            
            if (includeVoxel) {
                addVoxel(x, y, -1, panelColor);
                // Add some depth layers
                if ((x + y) % 4 === 0) addVoxel(x, y, -2, doorDark);
            }
        }
    }
    
    // === DOOR FRAME BORDERS ===
    // Thick inner frame around door panels
    for (let x = -doorWidth/2; x <= doorWidth/2; x++) {
        // Top and bottom borders
        addVoxel(x, 3, 0, metalColor);
        addVoxel(x, lintelY - 1, 0, metalColor);
        
        // Side borders - extended to match door panel height
        if (x === -doorWidth/2 || x === doorWidth/2) {
            for (let y = 4; y <= lintelY + 2; y++) {
                // Check if we're in the arch area for proper blending
                if (y >= lintelY - 1) {
                    const archRadius = (doorWidth/2) + 2;
                    const archCenterY = lintelY;
                    const distanceFromArchCenter = Math.sqrt(x * x + Math.pow(y - archCenterY, 2));
                    
                    // Only add frame if within arch boundary
                    if (distanceFromArchCenter <= archRadius * 0.75) {
                        addVoxel(x, y, 0, metalColor);
                        addVoxel(x, y, -1, metalDark);
                    }
                } else {
                    addVoxel(x, y, 0, metalColor);
                    addVoxel(x, y, -1, metalDark);
                }
            }
        }
    }
    
    // === DOOR DIVIDER (CENTER SEAM) ===
    for (let y = 4; y <= lintelY + 2; y++) {
        // Check if we're in the arch area for proper blending
        let includeDivider = true;
        if (y >= lintelY - 1) {
            const archRadius = (doorWidth/2) + 2;
            const archCenterY = lintelY;
            const distanceFromArchCenter = Math.sqrt(0 * 0 + Math.pow(y - archCenterY, 2));
            
            // Only add divider if within arch boundary
            if (distanceFromArchCenter > archRadius * 0.75) {
                includeDivider = false;
            }
        }
        
        if (includeDivider) {
            addVoxel(0, y, 0, metalColor);
            addVoxel(0, y, 1, metalLight);
            // Add decorative elements at regular intervals
            if (y % 4 === 0) {
                addVoxel(-1, y, 1, decorColor);
                addVoxel(1, y, 1, decorColor);
            }
        }
    }
    
    // === ENHANCED METAL REINFORCEMENTS ===
    switch (doorStyle) {
        case 'wooden':
            // Decorative metal corner brackets
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 3; j++) {
                    addVoxel(-doorWidth/2 + 1 + i, 5 + j, 1, metalColor);
                    addVoxel(doorWidth/2 - 3 + i, 5 + j, 1, metalColor);
                    addVoxel(-doorWidth/2 + 1 + i, lintelY - 5 + j, 1, metalColor);
                    addVoxel(doorWidth/2 - 3 + i, lintelY - 5 + j, 1, metalColor);
                }
            }
            
            // Ornate center lock mechanism
            const lockY = Math.floor(lintelY/2);
            for (let i = -2; i <= 2; i++) {
                for (let j = -1; j <= 1; j++) {
                    addVoxel(i, lockY + j, 2, metalColor);
                    if (Math.abs(i) <= 1 && Math.abs(j) <= 0) {
                        addVoxel(i, lockY + j, 3, decorColor);
                    }
                }
            }
            
            // Wood grain simulation with metal strips
            for (let y = 6; y < lintelY - 3; y += 4) {
                for (let x = -doorWidth/2 + 2; x <= doorWidth/2 - 2; x += 2) {
                    addVoxel(x, y, 0, metalDark);
                }
            }
            break;
            
        case 'reinforced':
            // Heavy duty cross reinforcement pattern
            const midY = Math.floor(lintelY/2);
            
            // Diagonal cross pattern
            for (let i = -6; i <= 6; i++) {
                if (Math.abs(i) <= doorWidth/2 - 2) {
                    addVoxel(i, midY + i/2, 1, metalColor);
                    addVoxel(i, midY - i/2, 1, metalColor);
                    
                    // Add depth to diagonals
                    addVoxel(i, midY + i/2, 2, metalDark);
                    addVoxel(i, midY - i/2, 2, metalDark);
                }
            }
            
            // Heavy corner reinforcements with red accents
            for (let corner = 0; corner < 4; corner++) {
                const cornerX = corner < 2 ? -doorWidth/2 + 2 : doorWidth/2 - 2;
                const cornerY = corner % 2 === 0 ? 6 : lintelY - 6;
                
                for (let i = -2; i <= 2; i++) {
                    for (let j = -2; j <= 2; j++) {
                        if (Math.abs(i) + Math.abs(j) <= 2) {
                            addVoxel(cornerX + i, cornerY + j, 1, decorColor);
                            if (Math.abs(i) + Math.abs(j) <= 1) {
                                addVoxel(cornerX + i, cornerY + j, 2, accentColor);
                            }
                        }
                    }
                }
            }
            
            // Central armored core
            for (let i = -3; i <= 3; i++) {
                for (let j = -2; j <= 2; j++) {
                    if (Math.abs(i) <= 2 || Math.abs(j) <= 1) {
                        addVoxel(i, midY + j, 2, metalColor);
                        if (Math.abs(i) <= 1 && Math.abs(j) <= 0) {
                            addVoxel(i, midY + j, 3, decorColor);
                        }
                    }
                }
            }
            break;
            
        case 'iron':
            // Ornate iron scrollwork pattern
            const ironMidY = Math.floor(lintelY/2);
            
            // Create decorative spiral patterns
            for (let spiral = 0; spiral < 4; spiral++) {
                const spiralX = spiral < 2 ? -doorWidth/4 : doorWidth/4;
                const spiralY = spiral % 2 === 0 ? ironMidY - 4 : ironMidY + 4;
                
                for (let angle = 0; angle < 360; angle += 30) {
                    const rad = angle * Math.PI / 180;
                    const radius = 2 + Math.sin(angle * Math.PI / 90);
                    const x = Math.round(spiralX + Math.cos(rad) * radius);
                    const y = Math.round(spiralY + Math.sin(rad) * radius);
                    
                    if (Math.abs(x) <= doorWidth/2 - 1 && y >= 5 && y <= lintelY - 3) {
                        addVoxel(x, y, 1, metalColor);
                        if (angle % 60 === 0) {
                            addVoxel(x, y, 2, decorColor);
                        }
                    }
                }
            }
            
            // Central golden emblem
            for (let i = -4; i <= 4; i++) {
                for (let j = -3; j <= 3; j++) {
                    const dist = Math.sqrt(i*i + j*j);
                    if (dist <= 3.5 && dist >= 1.5) {
                        addVoxel(i, ironMidY + j, 2, decorColor);
                        if (dist <= 2.5) {
                            addVoxel(i, ironMidY + j, 3, accentColor);
                        }
                    }
                }
            }
            
            // Iron rivets around the perimeter
            for (let x = -doorWidth/2 + 1; x <= doorWidth/2 - 1; x += 3) {
                addVoxel(x, 5, 1, metalLight);
                addVoxel(x, lintelY - 3, 1, metalLight);
            }
            for (let y = 7; y <= lintelY - 5; y += 3) {
                addVoxel(-doorWidth/2 + 1, y, 1, metalLight);
                addVoxel(doorWidth/2 - 1, y, 1, metalLight);
            }
            break;
    }
    
    // === ENHANCED DOOR HINGES ===
    // Large, ornate hinges that match the door's grandeur
    const hingeY = [lintelY - 4, Math.floor(lintelY * 0.7), Math.floor(lintelY * 0.3), 6];
    
    hingeY.forEach(y => {
        // Main hinge body (3x2 voxels)
        for (let i = 0; i < 3; i++) {
            for (let j = 0; j < 2; j++) {
                addVoxel(-doorWidth/2 - 2 + i, y + j, 1, metalColor);
                addVoxel(-doorWidth/2 - 2 + i, y + j, 2, metalDark);
            }
        }
        
        // Hinge pin (decorative center)
        addVoxel(-doorWidth/2 - 1, y, 3, decorColor);
        addVoxel(-doorWidth/2 - 1, y + 1, 3, decorColor);
        
        // Hinge mounting bolts
        addVoxel(-doorWidth/2 - 2, y, 0, metalDark);
        addVoxel(-doorWidth/2, y + 1, 0, metalDark);
    });
    
    // === DOOR HANDLES AND LOCKS ===
    const handleY = Math.floor(lintelY/2);
    
    // Right side handle
    for (let i = -1; i <= 1; i++) {
        for (let j = -2; j <= 2; j++) {
            if (Math.abs(j) <= 1 || Math.abs(i) === 0) {
                addVoxel(doorWidth/2 - 1, handleY + j, 2 + Math.abs(i), metalColor);
            }
        }
    }
    
    // Handle decoration
    addVoxel(doorWidth/2 - 1, handleY, 4, decorColor);
    
    // === ADDITIONAL ARCHITECTURAL DETAILS ===
    // Side moldings along the frame
    for (let y = 4; y <= lintelY - 2; y += 2) {
        // Left side molding
        addVoxel(-doorWidth/2 - 3, y, 1, stoneLight);
        addVoxel(-doorWidth/2 - 3, y, 0, stoneDark);
        
        // Right side molding
        addVoxel(doorWidth/2 + 3, y, 1, stoneLight);
        addVoxel(doorWidth/2 + 3, y, 0, stoneDark);
    }
    
    // === GEOMETRY MERGING (like other game models) ===
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex, {
            roughness: colorHex === stoneColor ? 0.8 : 0.6,
            metalness: colorHex === metalColor || colorHex === decorColor ? 0.8 : 0.2
        });
        const mesh = new THREE.Mesh(mergedGeometry, material);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });
    
    // Set up group properties following game conventions
    group.userData = {
        ...(options.userData || {}),
        objectType: 'closed_dungeon_door',
        isInteractable: options.isInteractable || false,
        isDoor: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        doorStyle: doorStyle,
        voxelScale: doorVoxelSize,
        canOpen: options.canOpen || false
    };
    
    group.name = `closed_dungeon_door_${doorStyle}`;
    
    console.log(`[ClosedDungeonDoor] ✅ Created ${doorStyle} dungeon door with merged geometry`);
    return group;
}

/**
 * Create wooden door variant
 */
export function createWoodenDungeonDoor(options = {}) {
    return createClosedDungeonDoor({
        ...options,
        doorStyle: 'wooden'
    });
}

/**
 * Create reinforced door variant
 */
export function createReinforcedDungeonDoor(options = {}) {
    return createClosedDungeonDoor({
        ...options,
        doorStyle: 'reinforced'
    });
}

/**
 * Create iron door variant
 */
export function createIronDungeonDoor(options = {}) {
    return createClosedDungeonDoor({
        ...options,
        doorStyle: 'iron'
    });
}