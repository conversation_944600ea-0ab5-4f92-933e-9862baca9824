import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached
} from './shared.js';


export function createRuneMonolith() {
    const monolithGroup = new THREE.Group();
    
    // Monolith dimensions - massive for boss room
    const baseWidth = 24;    // 8x original (3 * 8)
    const baseHeight = 64;   // 8x original (8 * 8)
    const baseDepth = 16;    // 8x original (2 * 8)
    
    // Dark stone material for the monolith
    const stoneMaterial = _getMaterialByHex_Cached(0x2a2a2a);
    
    // Glowing rune material with emissive properties
    const runeMaterial = new THREE.MeshPhongMaterial({ 
        color: 0x4a90e2, 
        emissive: 0x4a90e2,
        emissiveIntensity: 0.5
    });
    
    // Main monolith body
    const monolithGeometry = getOrCreateGeometry(
        `monolith_${baseWidth}x${baseHeight}x${baseDepth}`,
        () => new THREE.BoxGeometry(
            baseWidth * VOXEL_SIZE,
            baseHeight * VOXEL_SIZE,
            baseDepth * VOXEL_SIZE
        )
    );
    
    const monolith = new THREE.Mesh(monolithGeometry, stoneMaterial);
    monolith.position.y = (baseHeight * VOXEL_SIZE) / 2;
    monolith.castShadow = true;
    monolith.receiveShadow = true;
    monolithGroup.add(monolith);
    
    // Add glowing runes on the front face - massive scale
    const runeSize = 4.0;    // 8x original (0.5 * 8)
    const runeDepth = 0.8;   // 8x original (0.1 * 8)
    const runeGeometry = getOrCreateGeometry(
        `rune_${runeSize}x${runeSize}x${runeDepth}`,
        () => new THREE.BoxGeometry(
            runeSize * VOXEL_SIZE,
            runeSize * VOXEL_SIZE,
            runeDepth * VOXEL_SIZE
        )
    );
    
    // Create a pattern of runes - spread across massive monolith
    const runePositions = [
        { x: 0, y: 48 },      // Top center
        { x: -6.4, y: 40 },   // Upper left
        { x: 6.4, y: 40 },    // Upper right
        { x: 0, y: 32 },      // Middle center
        { x: -6.4, y: 24 },   // Lower left
        { x: 6.4, y: 24 },    // Lower right
        { x: 0, y: 16 },      // Bottom center
        // Add more runes for the massive scale
        { x: -6.4, y: 48 },   // Top left
        { x: 6.4, y: 48 },    // Top right
        { x: -6.4, y: 32 },   // Middle left
        { x: 6.4, y: 32 },    // Middle right
        { x: -6.4, y: 16 },   // Bottom left
        { x: 6.4, y: 16 },    // Bottom right
        { x: 0, y: 8 }        // Very bottom
    ];
    
    runePositions.forEach(pos => {
        const rune = new THREE.Mesh(runeGeometry, runeMaterial);
        rune.position.set(
            pos.x * VOXEL_SIZE,
            pos.y * VOXEL_SIZE,
            (baseDepth / 2 + runeDepth / 2) * VOXEL_SIZE
        );
        monolithGroup.add(rune);
        
        // Add point light for glow effect - massive scale
        const light = new THREE.PointLight(0x4a90e2, 1.0, 16);
        light.position.copy(rune.position);
        light.position.z += 1.6;
        monolithGroup.add(light);
    });
    
    // Add a subtle ambient glow at the base - massive scale
    const baseLight = new THREE.PointLight(0x4a90e2, 2.0, 24);
    baseLight.position.set(0, 4.0, 0);
    monolithGroup.add(baseLight);
    
    // Add userData for identification
    monolithGroup.userData = {
        type: 'rune_monolith',
        isInteractable: false,
        isDestructible: false
    };
    
    return monolithGroup;
}