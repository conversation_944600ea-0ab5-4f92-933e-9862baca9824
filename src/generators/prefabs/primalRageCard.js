import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Primal Rage Card Prefab
 * Creates glowing red eyes, claw marks, and a wild primal aura representing unleashed fury
 */

// Primal rage specific colors
const RAGE_COLORS = {
    RAGE_RED: 0xFF0000,          // Pure red for rage
    BLOOD_CRIMSON: 0x8B0000,     // Dark red for blood
    FERAL_ORANGE: 0xFF4500,      // Orange-red for feral energy
    PRIMAL_BROWN: 0x8B4513,      // Saddle brown for primal earth
    FURY_YELLOW: 0xFFD700,       // Gold for fury glow
    WILD_GREEN: 0x228B22         // Forest green for wild nature
};

/**
 * Create a primal rage card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The primal rage card 3D model
 */
export function createPrimalRageCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'PrimalRageCard';

    // Materials
    const rageRedMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.RAGE_RED,
        emissive: RAGE_COLORS.RAGE_RED,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const bloodCrimsonMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.BLOOD_CRIMSON,
        emissive: RAGE_COLORS.BLOOD_CRIMSON,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const feralOrangeMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.FERAL_ORANGE,
        emissive: RAGE_COLORS.FERAL_ORANGE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.7
    });

    const primalBrownMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.PRIMAL_BROWN,
        emissive: RAGE_COLORS.PRIMAL_BROWN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const furyYellowMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.FURY_YELLOW,
        emissive: RAGE_COLORS.FURY_YELLOW,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const wildGreenMaterial = new THREE.MeshLambertMaterial({
        color: RAGE_COLORS.WILD_GREEN,
        emissive: RAGE_COLORS.WILD_GREEN,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create rage eyes voxels (glowing red eyes)
    const rageEyesVoxels = [
        // Left eye
        { x: -1.5, y: 2, z: 0, material: rageRedMaterial },
        { x: -1, y: 2.5, z: 0, material: bloodCrimsonMaterial },
        { x: -2, y: 2, z: 0, material: bloodCrimsonMaterial },
        { x: -1.5, y: 1.5, z: 0, material: feralOrangeMaterial },
        
        // Right eye
        { x: 1.5, y: 2, z: 0, material: rageRedMaterial },
        { x: 1, y: 2.5, z: 0, material: bloodCrimsonMaterial },
        { x: 2, y: 2, z: 0, material: bloodCrimsonMaterial },
        { x: 1.5, y: 1.5, z: 0, material: feralOrangeMaterial },
        
        // Eye glow
        { x: -1.5, y: 2.5, z: 0, material: furyYellowMaterial },
        { x: 1.5, y: 2.5, z: 0, material: furyYellowMaterial }
    ];

    // Create claw marks voxels (diagonal slashes)
    const clawMarksVoxels = [
        // First claw mark (diagonal slash)
        { x: -3, y: 0, z: 0, material: bloodCrimsonMaterial },
        { x: -2, y: 1, z: 0, material: bloodCrimsonMaterial },
        { x: -1, y: 2, z: 0, material: bloodCrimsonMaterial },
        { x: 0, y: 3, z: 0, material: bloodCrimsonMaterial },
        { x: 1, y: 4, z: 0, material: bloodCrimsonMaterial },
        
        // Second claw mark
        { x: -2, y: -1, z: 0, material: bloodCrimsonMaterial },
        { x: -1, y: 0, z: 0, material: bloodCrimsonMaterial },
        { x: 0, y: 1, z: 0, material: bloodCrimsonMaterial },
        { x: 1, y: 2, z: 0, material: bloodCrimsonMaterial },
        
        // Third claw mark
        { x: -1, y: -2, z: 0, material: bloodCrimsonMaterial },
        { x: 0, y: -1, z: 0, material: bloodCrimsonMaterial },
        { x: 1, y: 0, z: 0, material: bloodCrimsonMaterial },
        { x: 2, y: 1, z: 0, material: bloodCrimsonMaterial },
        
        // Blood drops
        { x: 0, y: -3, z: 0, material: rageRedMaterial },
        { x: 2, y: -2, z: 0, material: rageRedMaterial }
    ];

    // Create primal aura voxels (surrounding wild energy)
    const primalAuraVoxels = [
        // Outer aura ring
        { x: -4, y: 3, z: 0, material: feralOrangeMaterial },
        { x: 4, y: 3, z: 0, material: feralOrangeMaterial },
        { x: -4, y: -3, z: 0, material: feralOrangeMaterial },
        { x: 4, y: -3, z: 0, material: feralOrangeMaterial },
        
        // Cardinal directions
        { x: -5, y: 0, z: 0, material: primalBrownMaterial },
        { x: 5, y: 0, z: 0, material: primalBrownMaterial },
        { x: 0, y: 5, z: 0, material: primalBrownMaterial },
        { x: 0, y: -5, z: 0, material: primalBrownMaterial },
        
        // Intermediate aura points
        { x: -3.5, y: 1.5, z: 0, material: feralOrangeMaterial },
        { x: 3.5, y: 1.5, z: 0, material: feralOrangeMaterial },
        { x: -3.5, y: -1.5, z: 0, material: feralOrangeMaterial },
        { x: 3.5, y: -1.5, z: 0, material: feralOrangeMaterial },
        
        // Wild energy sparks
        { x: -2.5, y: 4, z: 0, material: wildGreenMaterial },
        { x: 2.5, y: 4, z: 0, material: wildGreenMaterial },
        { x: -2.5, y: -4, z: 0, material: wildGreenMaterial },
        { x: 2.5, y: -4, z: 0, material: wildGreenMaterial },
        
        // Fury bursts
        { x: -4.5, y: 1, z: 0, material: furyYellowMaterial },
        { x: 4.5, y: 1, z: 0, material: furyYellowMaterial },
        { x: -4.5, y: -1, z: 0, material: furyYellowMaterial },
        { x: 4.5, y: -1, z: 0, material: furyYellowMaterial }
    ];

    // Create all voxels
    [...rageEyesVoxels, ...clawMarksVoxels, ...primalAuraVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8, // Slightly compact
            voxel.y * VOXEL_SIZE * 0.8, // Slightly compact
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === rageRedMaterial ? 'rage_eyes' :
                                 voxel.material === bloodCrimsonMaterial ? 'claw_marks' :
                                 voxel.material === feralOrangeMaterial ? 'feral_aura' :
                                 voxel.material === furyYellowMaterial ? 'fury_burst' :
                                 voxel.material === wildGreenMaterial ? 'wild_energy' : 'primal_aura';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        ragePhase: 0,
        furyPhase: 0,
        wildPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update primal rage card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updatePrimalRageCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Rage intensity phase
    cardGroup.userData.ragePhase += deltaTime * 4.0;
    const rageValue = (Math.sin(cardGroup.userData.ragePhase) + 1) * 0.5; // 0 to 1

    // Fury bursts phase
    cardGroup.userData.furyPhase += deltaTime * 6.0;
    const furyValue = (Math.sin(cardGroup.userData.furyPhase) + 1) * 0.5; // 0 to 1

    // Wild energy phase
    cardGroup.userData.wildPhase += deltaTime * 3.5;
    const wildValue = (Math.sin(cardGroup.userData.wildPhase) + 1) * 0.5; // 0 to 1

    // Apply primal rage animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different animation patterns for different rage elements
            switch (child.userData.voxelType) {
                case 'rage_eyes':
                    child.material.opacity = baseOpacity * (1.0 + rageValue * 0.0); // Always visible
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 1.0 + rageValue * 0.8;
                    }
                    // Eye glow pulsing
                    const eyePulseScale = 1.0 + rageValue * 0.3;
                    child.scale.setScalar(eyePulseScale);
                    break;
                case 'claw_marks':
                    child.material.opacity = baseOpacity * (0.8 + furyValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + furyValue * 0.6;
                    }
                    // Claw mark flickering
                    child.position.x += (Math.random() - 0.5) * 0.003;
                    child.position.y += (Math.random() - 0.5) * 0.003;
                    break;
                case 'feral_aura':
                    child.material.opacity = baseOpacity * (0.7 + wildValue * 0.5);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.9 + wildValue * 0.7;
                    }
                    // Feral aura movement
                    child.position.x += Math.sin(time * 4 + child.position.y * 2) * 0.006;
                    child.position.y += Math.cos(time * 3.5 + child.position.x * 2) * 0.005;
                    break;
                case 'fury_burst':
                    child.material.opacity = baseOpacity * (0.6 + furyValue * 0.8);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + furyValue * 1.0;
                    }
                    // Fury burst expansion
                    const furyPulseScale = 1.0 + furyValue * 0.4;
                    child.scale.setScalar(furyPulseScale);
                    break;
                case 'wild_energy':
                    child.material.opacity = baseOpacity * (0.5 + wildValue * 0.7);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.7 + wildValue * 0.8;
                    }
                    // Wild energy sparking
                    child.position.x += Math.sin(time * 8 + child.position.y * 4) * 0.008;
                    child.position.y += Math.cos(time * 7 + child.position.x * 3) * 0.007;
                    break;
                case 'primal_aura':
                    child.material.opacity = baseOpacity * (0.8 + rageValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.6 + rageValue * 0.6;
                    }
                    // Primal aura undulation
                    child.position.x += Math.sin(time * 2.5 + child.position.y * 1.5) * 0.005;
                    child.position.y += Math.cos(time * 2 + child.position.x * 1.5) * 0.004;
                    break;
            }
        }
    });

    // Overall primal rage effect (violent shaking)
    const rageShake = Math.sin(time * 5) * 0.008;
    cardGroup.position.x += rageShake * (Math.random() - 0.5);
    cardGroup.position.y += rageShake * (Math.random() - 0.5);
    
    // Rotation from fury
    cardGroup.rotation.z += Math.sin(time * 3) * 0.05;
}

// Export the primal rage card data for the loot system
export const PRIMAL_RAGE_CARD_DATA = {
    name: 'Primal Rage',
    description: 'Unleashes an uncontrollable berserker fury that dramatically increases attack power but makes you vulnerable to damage.',
    category: 'card',
    rarity: 'epic',
    effect: 'primal_rage',
    effectValue: 10,
    createFunction: createPrimalRageCard,
    updateFunction: updatePrimalRageCardAnimation,
    voxelModel: 'primal_rage_card',
    glow: {
        color: 0xFF0000,
        intensity: 1.3
    }
};

export default createPrimalRageCard;