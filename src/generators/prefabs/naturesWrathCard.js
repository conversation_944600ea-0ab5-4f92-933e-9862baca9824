import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Nature's Wrath Card Prefab
 * Creates the fury of nature with vines, thorns, and natural magic
 */

// Nature's wrath specific colors
const NATURE_COLORS = {
    FOREST_GREEN: 0x228B22,      // Forest green
    LEAF_GREEN: 0x32CD32,        // Bright leaf green
    VINE_GREEN: 0x6B8E23,        // Olive vine green
    EARTH_BROWN: 0x8B4513,       // Earth brown
    THORN_RED: 0xB22222,         // Thorn red
    BARK_BROWN: 0x654321,        // Tree bark brown
    MOSS_GREEN: 0x90EE90,        // Light moss green
    ROOT_BROWN: 0xA0522D         // Root brown
};

/**
 * Create a nature's wrath card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The nature's wrath card 3D model
 */
export function createNaturesWrathCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'NaturesWrathCard';

    // Materials
    const forestGreenMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.FOREST_GREEN,
        emissive: NATURE_COLORS.FOREST_GREEN,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const leafGreenMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.LEAF_GREEN,
        emissive: NATURE_COLORS.LEAF_GREEN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    const vineGreenMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.VINE_GREEN,
        emissive: NATURE_COLORS.VINE_GREEN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.EARTH_BROWN,
        emissive: NATURE_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.8
    });

    const thornRedMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.THORN_RED,
        emissive: NATURE_COLORS.THORN_RED,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const barkBrownMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.BARK_BROWN,
        emissive: NATURE_COLORS.BARK_BROWN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const mossGreenMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.MOSS_GREEN,
        emissive: NATURE_COLORS.MOSS_GREEN,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const rootBrownMaterial = new THREE.MeshLambertMaterial({
        color: NATURE_COLORS.ROOT_BROWN,
        emissive: NATURE_COLORS.ROOT_BROWN,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create erupting vines
    const vineEruptionVoxels = [
        // Central vine eruption
        { x: 0, y: -4, z: 0, material: earthBrownMaterial },
        { x: 0, y: -3, z: 0, material: rootBrownMaterial },
        { x: 0, y: -2, z: 0, material: vineGreenMaterial },
        { x: 0, y: -1, z: 0, material: forestGreenMaterial },
        { x: 0, y: 0, z: 0, material: leafGreenMaterial },
        { x: 0, y: 1, z: 0, material: forestGreenMaterial },
        { x: 0, y: 2, z: 0, material: leafGreenMaterial },
        { x: 0, y: 3, z: 0, material: vineGreenMaterial },
        { x: 0, y: 4, z: 0, material: mossGreenMaterial },
        
        // Side vines (left)
        { x: -2, y: -3, z: 0, material: rootBrownMaterial },
        { x: -2, y: -2, z: 0, material: vineGreenMaterial },
        { x: -2, y: -1, z: 0, material: forestGreenMaterial },
        { x: -2, y: 0, z: 0, material: leafGreenMaterial },
        { x: -2, y: 1, z: 0, material: forestGreenMaterial },
        { x: -2, y: 2, z: 0, material: vineGreenMaterial },
        
        // Side vines (right)
        { x: 2, y: -3, z: 0, material: rootBrownMaterial },
        { x: 2, y: -2, z: 0, material: vineGreenMaterial },
        { x: 2, y: -1, z: 0, material: forestGreenMaterial },
        { x: 2, y: 0, z: 0, material: leafGreenMaterial },
        { x: 2, y: 1, z: 0, material: forestGreenMaterial },
        { x: 2, y: 2, z: 0, material: vineGreenMaterial },
        
        // Thorned branches
        { x: -1, y: 3, z: 0, material: thornRedMaterial },
        { x: 1, y: 3, z: 0, material: thornRedMaterial },
        { x: -3, y: 1, z: 0, material: thornRedMaterial },
        { x: 3, y: 1, z: 0, material: thornRedMaterial },
        
        // Additional vine tendrils
        { x: -1, y: -1, z: 0, material: vineGreenMaterial },
        { x: 1, y: -1, z: 0, material: vineGreenMaterial },
        { x: -1, y: 1, z: 0, material: leafGreenMaterial },
        { x: 1, y: 1, z: 0, material: leafGreenMaterial }
    ];

    // Create thorn rain
    const thornRainVoxels = [
        // Falling thorns (upper layer)
        { x: -5, y: 5, z: 0, material: thornRedMaterial },
        { x: -3, y: 6, z: 0, material: thornRedMaterial },
        { x: -1, y: 5, z: 0, material: thornRedMaterial },
        { x: 1, y: 6, z: 0, material: thornRedMaterial },
        { x: 3, y: 5, z: 0, material: thornRedMaterial },
        { x: 5, y: 6, z: 0, material: thornRedMaterial },
        
        // Falling thorns (mid layer)
        { x: -4, y: 4, z: 0, material: thornRedMaterial },
        { x: -2, y: 5, z: 0, material: thornRedMaterial },
        { x: 0, y: 4, z: 0, material: thornRedMaterial },
        { x: 2, y: 5, z: 0, material: thornRedMaterial },
        { x: 4, y: 4, z: 0, material: thornRedMaterial },
        
        // Falling thorns (lower layer)
        { x: -5, y: 3, z: 0, material: thornRedMaterial },
        { x: -3, y: 4, z: 0, material: thornRedMaterial },
        { x: -1, y: 3, z: 0, material: thornRedMaterial },
        { x: 1, y: 4, z: 0, material: thornRedMaterial },
        { x: 3, y: 3, z: 0, material: thornRedMaterial },
        { x: 5, y: 4, z: 0, material: thornRedMaterial },
        
        // Scattered thorns
        { x: -6, y: 2, z: 0, material: thornRedMaterial },
        { x: -4, y: 1, z: 0, material: thornRedMaterial },
        { x: -2, y: 2, z: 0, material: thornRedMaterial },
        { x: 0, y: 1, z: 0, material: thornRedMaterial },
        { x: 2, y: 2, z: 0, material: thornRedMaterial },
        { x: 4, y: 1, z: 0, material: thornRedMaterial },
        { x: 6, y: 2, z: 0, material: thornRedMaterial }
    ];

    // Create nature's wrath emanation
    const natureWrathVoxels = [
        // Ground eruption
        { x: -4, y: -4, z: 0, material: earthBrownMaterial },
        { x: -3, y: -5, z: 0, material: rootBrownMaterial },
        { x: -1, y: -4, z: 0, material: earthBrownMaterial },
        { x: 1, y: -5, z: 0, material: rootBrownMaterial },
        { x: 3, y: -4, z: 0, material: earthBrownMaterial },
        { x: 4, y: -5, z: 0, material: rootBrownMaterial },
        { x: -5, y: -3, z: 0, material: barkBrownMaterial },
        { x: -2, y: -4, z: 0, material: earthBrownMaterial },
        { x: 0, y: -3, z: 0, material: rootBrownMaterial },
        { x: 2, y: -4, z: 0, material: earthBrownMaterial },
        { x: 5, y: -3, z: 0, material: barkBrownMaterial },
        
        // Nature energy spreading
        { x: -6, y: 0, z: 0, material: forestGreenMaterial },
        { x: -5, y: 1, z: 0, material: leafGreenMaterial },
        { x: -5, y: -1, z: 0, material: vineGreenMaterial },
        { x: 5, y: 1, z: 0, material: leafGreenMaterial },
        { x: 5, y: -1, z: 0, material: vineGreenMaterial },
        { x: 6, y: 0, z: 0, material: forestGreenMaterial },
        { x: -4, y: 3, z: 0, material: mossGreenMaterial },
        { x: -3, y: 2, z: 0, material: forestGreenMaterial },
        { x: 3, y: 2, z: 0, material: forestGreenMaterial },
        { x: 4, y: 3, z: 0, material: mossGreenMaterial },
        
        // Corner fury
        { x: -6, y: 6, z: 0, material: forestGreenMaterial },
        { x: 6, y: 6, z: 0, material: forestGreenMaterial },
        { x: 6, y: -6, z: 0, material: forestGreenMaterial },
        { x: -6, y: -6, z: 0, material: forestGreenMaterial },
        
        // Additional nature essence
        { x: -5, y: 5, z: 0, material: mossGreenMaterial },
        { x: 5, y: 5, z: 0, material: mossGreenMaterial },
        { x: 5, y: -5, z: 0, material: mossGreenMaterial },
        { x: -5, y: -5, z: 0, material: mossGreenMaterial }
    ];

    // Create all voxels
    [...vineEruptionVoxels, ...thornRainVoxels, ...natureWrathVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22,
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (vineEruptionVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'vine';
        } else if (thornRainVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'thorn';
        } else {
            mesh.userData.voxelType = 'wrath';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        vineGrowth: 0,
        thornFall: 0,
        wrathPulse: 0,
        natureOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update nature's wrath card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateNaturesWrathCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.vineGrowth += deltaTime * 3.0;
    cardGroup.userData.thornFall += deltaTime * 5.0;
    cardGroup.userData.wrathPulse += deltaTime * 4.5;

    const time = cardGroup.userData.animationTime;
    const vineGrowth = cardGroup.userData.vineGrowth;
    const thornFall = cardGroup.userData.thornFall;
    const wrathPulse = cardGroup.userData.wrathPulse;
    const natureOffset = cardGroup.userData.natureOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'vine':
                    // Vines grow and sway
                    const vineIntensity = 0.7 + Math.sin(vineGrowth * 2.5 + child.position.y * 0.4 + natureOffset) * 0.8;
                    const vineSway = Math.sin(vineGrowth * 3.0 + child.position.y * 0.5) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * vineIntensity;
                    child.material.opacity = baseOpacity * (0.8 + vineIntensity * 0.2);
                    child.position.x += vineSway;
                    child.position.y += vineSway * 0.3;
                    break;

                case 'thorn':
                    // Thorns fall from above
                    const thornIntensity = 0.6 + Math.sin(thornFall * 4.0 + child.position.x * 0.5 + natureOffset) * 0.9;
                    const thornMotion = Math.sin(thornFall * 6.0 + child.position.x * 0.3) * 0.01;
                    
                    child.material.emissiveIntensity = baseEmissive * thornIntensity;
                    child.material.opacity = baseOpacity * thornIntensity;
                    child.position.y += thornMotion * -0.5; // Falling motion
                    child.position.x += thornMotion * 0.2;
                    break;

                case 'wrath':
                    // Nature's wrath pulses outward
                    const wrathIntensity = 0.8 + Math.sin(wrathPulse * 3.0 + child.position.x * 0.3 + child.position.y * 0.2 + natureOffset) * 0.7;
                    const wrathExpansion = Math.cos(wrathPulse * 2.5 + natureOffset) * 0.007;
                    
                    child.material.emissiveIntensity = baseEmissive * wrathIntensity;
                    child.material.opacity = baseOpacity * wrathIntensity;
                    child.position.x += wrathExpansion * (child.position.x > 0 ? 1 : -1);
                    child.position.y += wrathExpansion * (child.position.y > 0 ? 1 : -1);
                    break;
            }
        }
    });

    // Overall nature's wrath effect
    const natureFury = Math.sin(time * 3.5 + natureOffset) * 0.008;
    cardGroup.position.x += natureFury;
    cardGroup.position.y += natureFury * 0.6;
    
    // Nature growth rotation
    cardGroup.rotation.z += deltaTime * 0.2;
}

// Export the nature's wrath card data for the loot system
export const NATURES_WRATH_CARD_DATA = {
    name: "Nature's Wrath",
    description: 'Unleashes the fury of nature itself. Vines emerge from the ground, thorns rain from above, and poisonous spores fill the air.',
    category: 'card',
    rarity: 'epic',
    effect: 'natures_wrath',
    effectValue: 45,
    createFunction: createNaturesWrathCard,
    updateFunction: updateNaturesWrathCardAnimation,
    voxelModel: 'natures_wrath_card',
    glow: {
        color: 0x228B22,
        intensity: 1.6
    }
};

export default createNaturesWrathCard;