import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Necromantic Grasp Card Prefab
 * Creates skeletal hands and dark energy for summoning undead minions
 */

// Necromantic specific colors
const NECROMANTIC_COLORS = {
    BONE_WHITE: 0xF5F5DC,           // Bone white
    BONE_AGED: 0xE6E0B7,           // Aged bone
    DARK_ENERGY: 0x4B0082,         // Dark purple energy
    SHADOW_BLACK: 0x1C1C1C,        // Deep shadow
    SOUL_GREEN: 0x32CD32,          // Eerie soul green
    DEATH_PURPLE: 0x8B008B,        // Death magic purple
    NECRO_BLUE: 0x191970           // Midnight blue
};

/**
 * Create a necromantic grasp card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The necromantic grasp card 3D model
 */
export function createNecromanticGraspCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'NecromanticGraspCard';

    // Materials
    const boneWhiteMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.BONE_WHITE,
        emissive: NECROMANTIC_COLORS.BONE_WHITE,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const boneAgedMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.BONE_AGED,
        emissive: NECROMANTIC_COLORS.BONE_AGED,
        emissiveIntensity: 0.05,
        transparent: true,
        opacity: 0.95
    });

    const darkEnergyMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.DARK_ENERGY,
        emissive: NECROMANTIC_COLORS.DARK_ENERGY,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const shadowMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.SHADOW_BLACK,
        emissive: NECROMANTIC_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.6
    });

    const soulGreenMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.SOUL_GREEN,
        emissive: NECROMANTIC_COLORS.SOUL_GREEN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const deathPurpleMaterial = new THREE.MeshLambertMaterial({
        color: NECROMANTIC_COLORS.DEATH_PURPLE,
        emissive: NECROMANTIC_COLORS.DEATH_PURPLE,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create main skeletal hand (reaching upward)
    const mainHandVoxels = [
        // Palm
        { x: 0, y: -0.1, z: 0, material: boneAgedMaterial },
        { x: -0.05, y: -0.1, z: 0, material: boneAgedMaterial },
        { x: 0.05, y: -0.1, z: 0, material: boneAgedMaterial },
        { x: 0, y: -0.15, z: 0, material: boneWhiteMaterial },
        
        // Thumb
        { x: -0.1, y: -0.05, z: 0, material: boneWhiteMaterial },
        { x: -0.1, y: 0, z: 0, material: boneAgedMaterial },
        
        // Index finger
        { x: -0.05, y: 0, z: 0, material: boneWhiteMaterial },
        { x: -0.05, y: 0.1, z: 0, material: boneWhiteMaterial },
        { x: -0.05, y: 0.15, z: 0, material: boneAgedMaterial },
        
        // Middle finger (longest)
        { x: 0, y: 0, z: 0, material: boneWhiteMaterial },
        { x: 0, y: 0.1, z: 0, material: boneWhiteMaterial },
        { x: 0, y: 0.2, z: 0, material: boneAgedMaterial },
        { x: 0, y: 0.25, z: 0, material: boneWhiteMaterial },
        
        // Ring finger
        { x: 0.05, y: 0, z: 0, material: boneWhiteMaterial },
        { x: 0.05, y: 0.1, z: 0, material: boneWhiteMaterial },
        { x: 0.05, y: 0.15, z: 0, material: boneAgedMaterial },
        
        // Pinky
        { x: 0.1, y: -0.05, z: 0, material: boneWhiteMaterial },
        { x: 0.1, y: 0.05, z: 0, material: boneAgedMaterial },
        
        // Wrist
        { x: 0, y: -0.2, z: 0, material: boneAgedMaterial },
        { x: -0.05, y: -0.2, z: 0, material: shadowMaterial },
        { x: 0.05, y: -0.2, z: 0, material: shadowMaterial }
    ];

    // Create secondary skeletal hand (emerging from shadow)
    const secondHandVoxels = [
        // Palm (offset and rotated)
        { x: -0.2, y: -0.25, z: 0, material: boneAgedMaterial },
        { x: -0.25, y: -0.25, z: 0, material: shadowMaterial },
        { x: -0.15, y: -0.25, z: 0, material: boneWhiteMaterial },
        
        // Fingers (grasping)
        { x: -0.2, y: -0.15, z: 0, material: boneWhiteMaterial },
        { x: -0.25, y: -0.1, z: 0, material: boneAgedMaterial },
        { x: -0.15, y: -0.1, z: 0, material: boneWhiteMaterial },
        { x: -0.2, y: -0.05, z: 0, material: boneAgedMaterial },
        
        // Third hand (background)
        { x: 0.2, y: -0.3, z: 0, material: shadowMaterial },
        { x: 0.25, y: -0.25, z: 0, material: shadowMaterial },
        { x: 0.2, y: -0.2, z: 0, material: boneAgedMaterial },
        { x: 0.15, y: -0.15, z: 0, material: shadowMaterial }
    ];

    // Create dark energy swirls around hands
    const darkEnergyVoxels = [
        // Central vortex
        { x: 0, y: 0.3, z: 0, material: darkEnergyMaterial },
        { x: -0.1, y: 0.3, z: 0, material: deathPurpleMaterial },
        { x: 0.1, y: 0.3, z: 0, material: deathPurpleMaterial },
        
        // Spiral around main hand
        { x: -0.15, y: 0.2, z: 0, material: darkEnergyMaterial },
        { x: 0.15, y: 0.1, z: 0, material: darkEnergyMaterial },
        { x: -0.1, y: 0, z: 0, material: deathPurpleMaterial },
        { x: 0.15, y: -0.05, z: 0, material: deathPurpleMaterial },
        { x: -0.2, y: -0.1, z: 0, material: darkEnergyMaterial },
        
        // Energy tendrils
        { x: -0.3, y: 0.15, z: 0, material: soulGreenMaterial },
        { x: 0.3, y: 0.05, z: 0, material: soulGreenMaterial },
        { x: -0.25, y: -0.2, z: 0, material: soulGreenMaterial },
        { x: 0.25, y: -0.15, z: 0, material: soulGreenMaterial },
        
        // Floating soul wisps
        { x: -0.35, y: 0.25, z: 0, material: soulGreenMaterial },
        { x: 0.35, y: 0.2, z: 0, material: soulGreenMaterial },
        { x: 0, y: 0.35, z: 0, material: darkEnergyMaterial },
        { x: -0.1, y: -0.35, z: 0, material: deathPurpleMaterial },
        { x: 0.1, y: -0.35, z: 0, material: deathPurpleMaterial }
    ];

    // Create all voxels and organize into groups
    const mainHandGroup = new THREE.Group();
    const secondHandGroup = new THREE.Group();
    const darkEnergyGroup = new THREE.Group();

    mainHandGroup.name = 'mainHand';
    secondHandGroup.name = 'secondHand';
    darkEnergyGroup.name = 'darkEnergy';

    // Add main hand voxels
    mainHandVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mainHandGroup.add(mesh);
    });

    // Add second hand voxels
    secondHandVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            -0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        secondHandGroup.add(mesh);
    });

    // Add dark energy voxels
    darkEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        mesh.userData.energyPhase = index * 0.4; // Stagger animation
        darkEnergyGroup.add(mesh);
    });

    // Add all groups to card
    cardGroup.add(mainHandGroup);
    cardGroup.add(secondHandGroup);
    cardGroup.add(darkEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        handGrasp: 0,
        energySwirl: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update necromantic grasp card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateNecromanticGraspCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.handGrasp += deltaTime * 2; // Grasping motion speed
    cardGroup.userData.energySwirl += deltaTime * 3; // Energy swirl speed

    const time = cardGroup.userData.animationTime;
    const handGrasp = cardGroup.userData.handGrasp;
    const energySwirl = cardGroup.userData.energySwirl;

    // Animate main hand grasping motion
    const mainHandGroup = cardGroup.getObjectByName('mainHand');
    if (mainHandGroup) {
        // Subtle grasping/closing motion
        const graspAngle = Math.sin(handGrasp) * 0.1;
        mainHandGroup.rotation.z = graspAngle;
        
        // Bone glow pulsing
        const bonePulse = 0.9 + Math.sin(energySwirl * 1.2) * 0.1;
        mainHandGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * bonePulse;
            }
        });
    }

    // Animate second hand (offset motion)
    const secondHandGroup = cardGroup.getObjectByName('secondHand');
    if (secondHandGroup) {
        const secondGraspAngle = Math.sin(handGrasp + Math.PI * 0.5) * 0.15; // Offset phase
        secondHandGroup.rotation.z = secondGraspAngle;
        
        // Shadowy bone effect
        const shadowPulse = 0.7 + Math.sin(energySwirl * 1.8) * 0.3;
        secondHandGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                mesh.material.opacity = mesh.userData.originalOpacity * shadowPulse;
            }
        });
    }

    // Animate dark energy swirls
    const darkEnergyGroup = cardGroup.getObjectByName('darkEnergy');
    if (darkEnergyGroup) {
        darkEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = energySwirl + mesh.userData.energyPhase;
                
                // Swirling motion around hands
                const swirlRadius = 0.03;
                const swirlX = Math.cos(energyTime * 2) * swirlRadius;
                const swirlY = Math.sin(energyTime * 2) * swirlRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + swirlX;
                mesh.position.y = mesh.userData.originalPosition.y + swirlY;
                
                // Pulsing dark energy glow
                const energyPulse = 0.5 + Math.sin(energyTime * 3) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyPulse;
                }
                
                // Fade in/out effect for wisps
                const fadeWave = 0.6 + Math.sin(energyTime * 1.5) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fadeWave;
                }
            }
        });
    }

    // Gentle overall undulating motion for necromantic effect
    const necroPulse = 1 + Math.sin(time * 0.7) * 0.03;
    cardGroup.scale.setScalar(0.8 * necroPulse);
}

// Export the necromantic grasp card data for the loot system
export const NECROMANTIC_GRASP_CARD_DATA = {
    name: 'Necromantic Grasp',
    description: 'Summons skeletal hands from the underworld to grasp and damage enemies while healing the caster with stolen life force.',
    category: 'card',
    rarity: 'epic',
    effect: 'necromantic_summon',
    effectValue: 3, // Number of skeletal hands
    createFunction: createNecromanticGraspCard,
    updateFunction: updateNecromanticGraspCardAnimation,
    voxelModel: 'necromantic_grasp_card',
    glow: {
        color: 0x4B0082,
        intensity: 1.3
    }
};

export default createNecromanticGraspCard;