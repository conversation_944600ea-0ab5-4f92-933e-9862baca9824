import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Chrono Shift Card Prefab
 * Creates time manipulation effects with temporal distortions
 */

// Chrono shift specific colors
const CHRONO_COLORS = {
    TIME_VIOLET: 0x9400D3,       // Time manipulation violet
    CHRONO_BLUE: 0x4169E1,       // Chrono energy blue
    TEMPORAL_SILVER: 0xC0C0C0,   // Temporal silver
    CLOCK_GOLD: 0xFFD700,        // Clock mechanism gold
    DIMENSION_PURPLE: 0x8A2BE2,  // Dimensional purple
    RIFT_INDIGO: 0x4B0082,       // Time rift indigo
    CRYSTAL_WHITE: 0xF0F8FF      // Temporal crystal white
};

/**
 * Create a chrono shift card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The chrono shift card 3D model
 */
export function createChronoShiftCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ChronoShiftCard';

    // Materials
    const timeVioletMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.TIME_VIOLET,
        emissive: CHRONO_COLORS.TIME_VIOLET,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const chronoBlueMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.CHRONO_BLUE,
        emissive: CHRONO_COLORS.CHRONO_BLUE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.7
    });

    const temporalSilverMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.TEMPORAL_SILVER,
        emissive: CHRONO_COLORS.TEMPORAL_SILVER,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const clockGoldMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.CLOCK_GOLD,
        emissive: CHRONO_COLORS.CLOCK_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const dimensionPurpleMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.DIMENSION_PURPLE,
        emissive: CHRONO_COLORS.DIMENSION_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const riftIndigoMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.RIFT_INDIGO,
        emissive: CHRONO_COLORS.RIFT_INDIGO,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.5
    });

    const crystalWhiteMaterial = new THREE.MeshLambertMaterial({
        color: CHRONO_COLORS.CRYSTAL_WHITE,
        emissive: CHRONO_COLORS.CRYSTAL_WHITE,
        emissiveIntensity: 1.2,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create clock face
    const timeClockVoxels = [
        // Clock face outline
        { x: 0, y: 4, z: 0, material: clockGoldMaterial },
        { x: 2, y: 3, z: 0, material: clockGoldMaterial },
        { x: 3, y: 2, z: 0, material: clockGoldMaterial },
        { x: 4, y: 0, z: 0, material: clockGoldMaterial },
        { x: 3, y: -2, z: 0, material: clockGoldMaterial },
        { x: 2, y: -3, z: 0, material: clockGoldMaterial },
        { x: 0, y: -4, z: 0, material: clockGoldMaterial },
        { x: -2, y: -3, z: 0, material: clockGoldMaterial },
        { x: -3, y: -2, z: 0, material: clockGoldMaterial },
        { x: -4, y: 0, z: 0, material: clockGoldMaterial },
        { x: -3, y: 2, z: 0, material: clockGoldMaterial },
        { x: -2, y: 3, z: 0, material: clockGoldMaterial },
        
        // Clock center and hands
        { x: 0, y: 0, z: 0, material: timeVioletMaterial },
        { x: 0, y: 1, z: 0, material: temporalSilverMaterial },
        { x: 0, y: 2, z: 0, material: temporalSilverMaterial },
        { x: 1, y: 0, z: 0, material: temporalSilverMaterial },
        { x: 2, y: 0, z: 0, material: temporalSilverMaterial },
        
        // Hour markers
        { x: 0, y: 3, z: 0, material: dimensionPurpleMaterial },
        { x: 3, y: 0, z: 0, material: dimensionPurpleMaterial },
        { x: 0, y: -3, z: 0, material: dimensionPurpleMaterial },
        { x: -3, y: 0, z: 0, material: dimensionPurpleMaterial }
    ];

    // Create time distortion waves
    const timeWaveVoxels = [
        // Concentric time waves
        { x: -5, y: 0, z: 0, material: chronoBlueMaterial },
        { x: -4, y: 2, z: 0, material: chronoBlueMaterial },
        { x: -4, y: -2, z: 0, material: chronoBlueMaterial },
        { x: 4, y: 2, z: 0, material: chronoBlueMaterial },
        { x: 4, y: -2, z: 0, material: chronoBlueMaterial },
        { x: 5, y: 0, z: 0, material: chronoBlueMaterial },
        { x: 0, y: 5, z: 0, material: chronoBlueMaterial },
        { x: 2, y: 4, z: 0, material: chronoBlueMaterial },
        { x: -2, y: 4, z: 0, material: chronoBlueMaterial },
        { x: 2, y: -4, z: 0, material: chronoBlueMaterial },
        { x: -2, y: -4, z: 0, material: chronoBlueMaterial },
        { x: 0, y: -5, z: 0, material: chronoBlueMaterial },
        
        // Diagonal waves
        { x: -3, y: 3, z: 0, material: crystalWhiteMaterial },
        { x: 3, y: 3, z: 0, material: crystalWhiteMaterial },
        { x: 3, y: -3, z: 0, material: crystalWhiteMaterial },
        { x: -3, y: -3, z: 0, material: crystalWhiteMaterial },
        { x: -1, y: 4, z: 0, material: dimensionPurpleMaterial },
        { x: 1, y: 4, z: 0, material: dimensionPurpleMaterial },
        { x: 4, y: 1, z: 0, material: dimensionPurpleMaterial },
        { x: 4, y: -1, z: 0, material: dimensionPurpleMaterial },
        { x: 1, y: -4, z: 0, material: dimensionPurpleMaterial },
        { x: -1, y: -4, z: 0, material: dimensionPurpleMaterial },
        { x: -4, y: -1, z: 0, material: dimensionPurpleMaterial },
        { x: -4, y: 1, z: 0, material: dimensionPurpleMaterial }
    ];

    // Create temporal rifts
    const temporalRiftVoxels = [
        // Corner rifts
        { x: -6, y: 6, z: 0, material: riftIndigoMaterial },
        { x: 6, y: 6, z: 0, material: riftIndigoMaterial },
        { x: 6, y: -6, z: 0, material: riftIndigoMaterial },
        { x: -6, y: -6, z: 0, material: riftIndigoMaterial },
        
        // Edge rifts
        { x: -6, y: 3, z: 0, material: timeVioletMaterial },
        { x: -6, y: 0, z: 0, material: riftIndigoMaterial },
        { x: -6, y: -3, z: 0, material: timeVioletMaterial },
        { x: 6, y: 3, z: 0, material: timeVioletMaterial },
        { x: 6, y: 0, z: 0, material: riftIndigoMaterial },
        { x: 6, y: -3, z: 0, material: timeVioletMaterial },
        { x: 3, y: 6, z: 0, material: timeVioletMaterial },
        { x: 0, y: 6, z: 0, material: riftIndigoMaterial },
        { x: -3, y: 6, z: 0, material: timeVioletMaterial },
        { x: 3, y: -6, z: 0, material: timeVioletMaterial },
        { x: 0, y: -6, z: 0, material: riftIndigoMaterial },
        { x: -3, y: -6, z: 0, material: timeVioletMaterial },
        
        // Additional time tears
        { x: -5, y: 5, z: 0, material: crystalWhiteMaterial },
        { x: 5, y: 5, z: 0, material: crystalWhiteMaterial },
        { x: 5, y: -5, z: 0, material: crystalWhiteMaterial },
        { x: -5, y: -5, z: 0, material: crystalWhiteMaterial }
    ];

    // Create all voxels
    [...timeClockVoxels, ...timeWaveVoxels, ...temporalRiftVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.22,
            voxel.y * VOXEL_SIZE * 0.22,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (timeClockVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'clock';
        } else if (timeWaveVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'wave';
        } else {
            mesh.userData.voxelType = 'rift';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        clockRotation: 0,
        timeFlow: 0,
        riftPulse: 0,
        chronoOffset: Math.random() * Math.PI * 2
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update chrono shift card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateChronoShiftCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.clockRotation += deltaTime * 1.5;
    cardGroup.userData.timeFlow += deltaTime * 3.5;
    cardGroup.userData.riftPulse += deltaTime * 4.0;

    const time = cardGroup.userData.animationTime;
    const clockRotation = cardGroup.userData.clockRotation;
    const timeFlow = cardGroup.userData.timeFlow;
    const riftPulse = cardGroup.userData.riftPulse;
    const chronoOffset = cardGroup.userData.chronoOffset;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'clock':
                    // Clock rotates and pulses with time energy
                    const clockIntensity = 0.8 + Math.sin(clockRotation * 2.0 + chronoOffset) * 0.7;
                    const clockRadius = Math.sqrt(child.position.x * child.position.x + child.position.y * child.position.y);
                    
                    child.material.emissiveIntensity = baseEmissive * clockIntensity;
                    child.material.opacity = baseOpacity * (0.7 + clockIntensity * 0.3);
                    
                    // Rotate around center
                    if (clockRadius > 0) {
                        const newAngle = Math.atan2(child.position.y, child.position.x) + deltaTime * 1.5;
                        child.position.x = Math.cos(newAngle) * clockRadius;
                        child.position.y = Math.sin(newAngle) * clockRadius;
                    }
                    break;

                case 'wave':
                    // Time waves flow outward
                    const waveIntensity = 0.6 + Math.sin(timeFlow * 3.0 + child.position.x * 0.3 + child.position.y * 0.3 + chronoOffset) * 0.9;
                    const waveMotion = Math.cos(timeFlow * 4.0 + chronoOffset) * 0.008;
                    
                    child.material.emissiveIntensity = baseEmissive * waveIntensity;
                    child.material.opacity = baseOpacity * waveIntensity;
                    child.position.x += waveMotion * (child.position.x > 0 ? 1 : -1);
                    child.position.y += waveMotion * (child.position.y > 0 ? 1 : -1);
                    break;

                case 'rift':
                    // Temporal rifts pulse and distort
                    const riftIntensity = 0.5 + Math.sin(riftPulse * 2.5 + chronoOffset) * 0.8;
                    const riftDistortion = Math.sin(riftPulse * 5.0) * 0.006;
                    
                    child.material.emissiveIntensity = baseEmissive * riftIntensity;
                    child.material.opacity = baseOpacity * riftIntensity;
                    child.position.x += riftDistortion;
                    child.position.y += riftDistortion * 0.7;
                    break;
            }
        }
    });

    // Overall temporal effect
    const chronoShift = Math.sin(time * 2.5 + chronoOffset) * 0.006;
    cardGroup.position.x += chronoShift;
    cardGroup.position.y += chronoShift * 0.8;
    
    // Time distortion rotation
    cardGroup.rotation.z += deltaTime * 0.4;
}

// Export the chrono shift card data for the loot system
export const CHRONO_SHIFT_CARD_DATA = {
    name: 'Chrono Shift',
    description: 'Manipulates the flow of time in your favor. Slows down all enemies while accelerating your movement and attack speed.',
    category: 'card',
    rarity: 'rare',
    effect: 'chrono_shift',
    effectValue: 15,
    createFunction: createChronoShiftCard,
    updateFunction: updateChronoShiftCardAnimation,
    voxelModel: 'chrono_shift_card',
    glow: {
        color: 0x9400D3,
        intensity: 1.4
    }
};

export default createChronoShiftCard;