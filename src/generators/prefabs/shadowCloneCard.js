import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Shadow Clone Card Prefab
 * Creates a shadowy figure splitting into multiple forms with connecting shadow trails
 */

// Shadow clone specific colors
const SHADOW_COLORS = {
    SHADOW_BLACK: 0x1a1a1a,     // Very dark shadow
    SHADOW_GRAY: 0x4a4a4a,      // Medium shadow
    SHADOW_MIST: 0x696969,      // Gray mist
    DARK_PURPLE: 0x301934,      // Dark purple shadow
    VOID_BLACK: 0x0f0f0f,       // Deep void black
    ETHEREAL_GRAY: 0x8a8a8a     // Ethereal gray
};

/**
 * Create a shadow clone card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The shadow clone card 3D model
 */
export function createShadowCloneCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ShadowCloneCard';

    // Materials
    const shadowBlackMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_BLACK,
        emissive: SHADOW_COLORS.SHADOW_BLACK,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const shadowGrayMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_GRAY,
        emissive: SHADOW_COLORS.SHADOW_GRAY,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const shadowMistMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.SHADOW_MIST,
        emissive: SHADOW_COLORS.SHADOW_MIST,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.5
    });

    const darkPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.DARK_PURPLE,
        emissive: SHADOW_COLORS.DARK_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.6
    });

    const etherealGrayMaterial = new THREE.MeshLambertMaterial({
        color: SHADOW_COLORS.ETHEREAL_GRAY,
        emissive: SHADOW_COLORS.ETHEREAL_GRAY,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.4
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create main shadow figure voxels (left side)
    const mainShadowVoxels = [
        // Shadow body core
        { x: -2, y: 3, z: 0, material: shadowBlackMaterial },
        { x: -2, y: 2, z: 0, material: shadowBlackMaterial },
        { x: -2, y: 1, z: 0, material: shadowBlackMaterial },
        { x: -2, y: 0, z: 0, material: shadowBlackMaterial },
        { x: -2, y: -1, z: 0, material: shadowBlackMaterial },
        
        // Shadow outline
        { x: -3, y: 2, z: 0, material: shadowGrayMaterial },
        { x: -1, y: 2, z: 0, material: shadowGrayMaterial },
        { x: -3, y: 1, z: 0, material: shadowGrayMaterial },
        { x: -1, y: 1, z: 0, material: shadowGrayMaterial },
        { x: -2, y: -2, z: 0, material: shadowGrayMaterial },
        
        // Shadow extensions
        { x: -4, y: 2, z: 0, material: shadowMistMaterial },
        { x: -4, y: 1, z: 0, material: shadowMistMaterial },
        { x: -1, y: 3, z: 0, material: shadowMistMaterial },
        { x: -3, y: 3, z: 0, material: shadowMistMaterial }
    ];

    // Create clone shadow figure voxels (right side)
    const cloneShadowVoxels = [
        // Clone body (more ethereal)
        { x: 2, y: 3, z: 0, material: shadowGrayMaterial },
        { x: 2, y: 2, z: 0, material: shadowGrayMaterial },
        { x: 2, y: 1, z: 0, material: shadowGrayMaterial },
        { x: 2, y: 0, z: 0, material: shadowGrayMaterial },
        { x: 2, y: -1, z: 0, material: shadowGrayMaterial },
        
        // Clone outline (fainter)
        { x: 3, y: 2, z: 0, material: shadowMistMaterial },
        { x: 1, y: 2, z: 0, material: shadowMistMaterial },
        { x: 3, y: 1, z: 0, material: shadowMistMaterial },
        { x: 1, y: 1, z: 0, material: shadowMistMaterial },
        { x: 2, y: -2, z: 0, material: shadowMistMaterial },
        
        // Clone ethereal parts
        { x: 4, y: 2, z: 0, material: etherealGrayMaterial },
        { x: 4, y: 1, z: 0, material: etherealGrayMaterial },
        { x: 1, y: 3, z: 0, material: etherealGrayMaterial },
        { x: 3, y: 3, z: 0, material: etherealGrayMaterial }
    ];

    // Create shadow trail voxels (connecting the two)
    const shadowTrailVoxels = [
        // Central connecting trail
        { x: 0, y: 2, z: 0, material: shadowMistMaterial },
        { x: 0, y: 1, z: 0, material: shadowMistMaterial },
        { x: 0, y: 0, z: 0, material: shadowMistMaterial },
        { x: 0, y: -1, z: 0, material: shadowMistMaterial },
        
        // Trail wisps
        { x: -1, y: 1.5, z: 0, material: etherealGrayMaterial },
        { x: 1, y: 1.5, z: 0, material: etherealGrayMaterial },
        { x: -1, y: 0.5, z: 0, material: etherealGrayMaterial },
        { x: 1, y: 0.5, z: 0, material: etherealGrayMaterial },
        { x: -1, y: -0.5, z: 0, material: etherealGrayMaterial },
        { x: 1, y: -0.5, z: 0, material: etherealGrayMaterial },
        
        // Shadow energy nodes
        { x: 0, y: 3, z: 0, material: darkPurpleMaterial },
        { x: 0, y: -2, z: 0, material: darkPurpleMaterial }
    ];

    // Create all voxels
    [...mainShadowVoxels, ...cloneShadowVoxels, ...shadowTrailVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8, // Slightly compact
            voxel.y * VOXEL_SIZE * 0.8, // Slightly compact
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === shadowBlackMaterial ? 'main_shadow' :
                                 voxel.material === shadowGrayMaterial ? 'clone_shadow' :
                                 voxel.material === shadowMistMaterial ? 'shadow_mist' :
                                 voxel.material === darkPurpleMaterial ? 'shadow_energy' : 'ethereal';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        shadowPhase: 0,
        cloningPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update shadow clone card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateShadowCloneCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Shadow movement phase
    cardGroup.userData.shadowPhase += deltaTime * 2.0;
    const shadowValue = (Math.sin(cardGroup.userData.shadowPhase) + 1) * 0.5; // 0 to 1

    // Cloning effect phase
    cardGroup.userData.cloningPhase += deltaTime * 1.8;
    const cloningValue = (Math.sin(cardGroup.userData.cloningPhase) + 1) * 0.5; // 0 to 1

    // Apply shadow animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different animation patterns for different shadow types
            switch (child.userData.voxelType) {
                case 'main_shadow':
                    child.material.opacity = baseOpacity * (0.9 + shadowValue * 0.1);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.6 + shadowValue * 0.4;
                    }
                    // Main shadow movement (subtle shifting)
                    child.position.x += Math.sin(time * 2 + child.position.y * 2) * 0.003;
                    break;
                case 'clone_shadow':
                    child.material.opacity = baseOpacity * (0.7 + cloningValue * 0.3);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.5 + cloningValue * 0.5;
                    }
                    // Clone shadow movement (phase difference)
                    child.position.x += Math.cos(time * 2.2 + child.position.y * 2.5) * 0.004;
                    break;
                case 'shadow_mist':
                    child.material.opacity = baseOpacity * (0.5 + shadowValue * 0.5);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.4 + shadowValue * 0.4;
                    }
                    // Mist swaying motion
                    child.position.y += Math.sin(time * 3 + child.position.x * 4) * 0.005;
                    break;
                case 'shadow_energy':
                    child.material.opacity = baseOpacity * (0.6 + cloningValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.7 + cloningValue * 0.6;
                    }
                    // Energy node pulsing
                    const pulseScale = 1.0 + cloningValue * 0.2;
                    child.scale.setScalar(pulseScale);
                    break;
                case 'ethereal':
                    child.material.opacity = baseOpacity * (0.4 + shadowValue * 0.6);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.3 + shadowValue * 0.5;
                    }
                    // Ethereal floating motion
                    child.position.x += Math.sin(time * 4 + child.position.y * 3) * 0.006;
                    child.position.y += Math.cos(time * 3.5 + child.position.x * 3) * 0.004;
                    break;
            }
        }
    });

    // Overall shadow clone effect
    const cloneOffset = Math.sin(time * 1.5) * 0.02;
    cardGroup.children.forEach((child, index) => {
        if (child.userData.voxelType === 'clone_shadow' || child.userData.voxelType === 'ethereal') {
            child.position.x += cloneOffset;
        }
    });
}

// Export the shadow clone card data for the loot system
export const SHADOW_CLONE_CARD_DATA = {
    name: 'Shadow Clone',
    description: 'Creates multiple shadow duplicates that confuse enemies and strike from unexpected angles with shadowy precision.',
    category: 'card',
    rarity: 'epic',
    effect: 'shadow_clone',
    effectValue: 8,
    createFunction: createShadowCloneCard,
    updateFunction: updateShadowCloneCardAnimation,
    voxelModel: 'shadow_clone_card',
    glow: {
        color: 0x4a4a4a,
        intensity: 1.0
    }
};

export default createShadowCloneCard;