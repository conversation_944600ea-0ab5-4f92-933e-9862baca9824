import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

export function createStonePathObject(length = 10, width = 3) {
    const group = new THREE.Group();
    
    // Create weathered stone material with variations - brighter colors for visibility
    const stoneColors = [
        0x9B8B93, // Light gray-purple
        0xAB9DAB, // Medium light gray
        0x8A7A8A, // Medium gray
        0xBA9BBA, // Light purple-gray
        0x7A6A7A  // Darker gray
    ];
    
    const stoneMaterials = stoneColors.map(color => 
        new THREE.MeshLambertMaterial({ color })
    );
    
    // Create stone path blocks
    for (let z = 0; z < length; z++) {
        for (let x = 0; x < width; x++) {
            // Create multiple stone blocks per path tile for realistic look
            const blocksPerTile = 2 + Math.floor(Math.random() * 3); // 2-4 blocks per tile
            
            for (let block = 0; block < blocksPerTile; block++) {
                // Random block dimensions for weathered look - made much larger for visibility
                const blockWidth = (0.8 + Math.random() * 0.4) * VOXEL_SIZE * 24; // 20-28 voxels wide  
                const blockHeight = (0.5 + Math.random() * 0.3) * VOXEL_SIZE * 12; // 6-9 voxels high
                const blockDepth = (0.6 + Math.random() * 0.8) * VOXEL_SIZE * 18; // 12-24 voxels deep
                
                const geometry = new THREE.BoxGeometry(blockWidth, blockHeight, blockDepth);
                const material = stoneMaterials[Math.floor(Math.random() * stoneMaterials.length)];
                const stoneCube = new THREE.Mesh(geometry, material);
                
                // Position with slight randomization - made spacing much larger
                const baseX = (x - width/2 + 0.5) * VOXEL_SIZE * 36; // 3x larger spacing
                const baseZ = (z - length/2 + 0.5) * VOXEL_SIZE * 36; // 3x larger spacing
                
                stoneCube.position.set(
                    baseX + (Math.random() - 0.5) * VOXEL_SIZE * 18,
                    blockHeight / 2,
                    baseZ + (Math.random() - 0.5) * VOXEL_SIZE * 18
                );
                
                // Add realistic weathered rotation
                stoneCube.rotation.x = (Math.random() - 0.5) * 0.1; // Slight tilt
                stoneCube.rotation.y = (Math.random() - 0.5) * 0.3; // More Y rotation for natural look
                stoneCube.rotation.z = (Math.random() - 0.5) * 0.1; // Slight roll
                
                group.add(stoneCube);
            }
            
            // Add occasional small pebbles/debris
            if (Math.random() < 0.3) {
                const pebbleGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 1.5, 4, 4);
                const pebbleMaterial = stoneMaterials[Math.floor(Math.random() * stoneMaterials.length)];
                const pebble = new THREE.Mesh(pebbleGeometry, pebbleMaterial);
                
                const baseX = (x - width/2 + 0.5) * VOXEL_SIZE * 36;
                const baseZ = (z - length/2 + 0.5) * VOXEL_SIZE * 36;
                
                pebble.position.set(
                    baseX + (Math.random() - 0.5) * VOXEL_SIZE * 30,
                    VOXEL_SIZE * 1.5,
                    baseZ + (Math.random() - 0.5) * VOXEL_SIZE * 30
                );
                
                group.add(pebble);
            }
        }
    }
    
    // Add some moss or weathering effects
    const mossGeometry = new THREE.PlaneGeometry(VOXEL_SIZE * 6, VOXEL_SIZE * 6);
    const mossMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x2D4A2D, 
        transparent: true, 
        opacity: 0.7 
    });
    
    // Randomly place moss patches
    for (let i = 0; i < length * width * 0.3; i++) {
        const moss = new THREE.Mesh(mossGeometry, mossMaterial);
        moss.rotation.x = -Math.PI / 2;
        moss.position.set(
            (Math.random() - 0.5) * width * VOXEL_SIZE * 36,
            VOXEL_SIZE * 0.3,
            (Math.random() - 0.5) * length * VOXEL_SIZE * 36
        );
        group.add(moss);
    }
    
    return group;
}

// Preset configurations for different path types
export function createShortStonePath() {
    return createStonePathObject(5, 2);
}

export function createLongStonePath() {
    return createStonePathObject(15, 3);
}

export function createWideStonePath() {
    return createStonePathObject(10, 5);
}