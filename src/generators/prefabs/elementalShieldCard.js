import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Elemental Shield Card Prefab
 * Creates rotating elemental barriers around the caster for protection
 */

// Elemental shield specific colors
const ELEMENTAL_COLORS = {
    FIRE_RED: 0xFF4500,         // Fire element
    WATER_BLUE: 0x4169E1,       // Water element
    EARTH_BROWN: 0x8B4513,      // Earth element
    AIR_CYAN: 0x00FFFF,         // Air element
    ENERGY_WHITE: 0xFFFFFF,     // Pure elemental energy
    SHIELD_SILVER: 0xC0C0C0,    // Shield metallic
    BARRIER_GOLD: 0xFFD700,     // Barrier glow
    MYSTIC_PURPLE: 0x8A2BE2     // Mystic binding energy
};

/**
 * Create an elemental shield card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The elemental shield card 3D model
 */
export function createElementalShieldCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'ElementalShieldCard';

    // Materials
    const fireRedMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.FIRE_RED,
        emissive: ELEMENTAL_COLORS.FIRE_RED,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const waterBlueMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.WATER_BLUE,
        emissive: ELEMENTAL_COLORS.WATER_BLUE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.85
    });

    const earthBrownMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.EARTH_BROWN,
        emissive: ELEMENTAL_COLORS.EARTH_BROWN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const airCyanMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.AIR_CYAN,
        emissive: ELEMENTAL_COLORS.AIR_CYAN,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const energyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.ENERGY_WHITE,
        emissive: ELEMENTAL_COLORS.ENERGY_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.8
    });

    const shieldSilverMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.SHIELD_SILVER,
        emissive: ELEMENTAL_COLORS.SHIELD_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.85
    });

    const barrierGoldMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.BARRIER_GOLD,
        emissive: ELEMENTAL_COLORS.BARRIER_GOLD,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const mysticPurpleMaterial = new THREE.MeshLambertMaterial({
        color: ELEMENTAL_COLORS.MYSTIC_PURPLE,
        emissive: ELEMENTAL_COLORS.MYSTIC_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Central shield core
    const shieldCoreVoxels = [
        // Center
        { x: 0, y: 0, z: 0, material: energyWhiteMaterial },
        // Inner cross
        { x: 1, y: 0, z: 0, material: shieldSilverMaterial },
        { x: -1, y: 0, z: 0, material: shieldSilverMaterial },
        { x: 0, y: 1, z: 0, material: shieldSilverMaterial },
        { x: 0, y: -1, z: 0, material: shieldSilverMaterial }
    ];

    // Fire elemental barrier (top right)
    const fireBarrierVoxels = [
        { x: 2, y: 2, z: 0, material: fireRedMaterial },
        { x: 3, y: 2, z: 0, material: fireRedMaterial },
        { x: 2, y: 3, z: 0, material: fireRedMaterial },
        { x: 3, y: 3, z: 0, material: fireRedMaterial },
        { x: 4, y: 2, z: 0, material: fireRedMaterial },
        { x: 2, y: 4, z: 0, material: fireRedMaterial }
    ];

    // Water elemental barrier (top left)
    const waterBarrierVoxels = [
        { x: -2, y: 2, z: 0, material: waterBlueMaterial },
        { x: -3, y: 2, z: 0, material: waterBlueMaterial },
        { x: -2, y: 3, z: 0, material: waterBlueMaterial },
        { x: -3, y: 3, z: 0, material: waterBlueMaterial },
        { x: -4, y: 2, z: 0, material: waterBlueMaterial },
        { x: -2, y: 4, z: 0, material: waterBlueMaterial }
    ];

    // Earth elemental barrier (bottom left)
    const earthBarrierVoxels = [
        { x: -2, y: -2, z: 0, material: earthBrownMaterial },
        { x: -3, y: -2, z: 0, material: earthBrownMaterial },
        { x: -2, y: -3, z: 0, material: earthBrownMaterial },
        { x: -3, y: -3, z: 0, material: earthBrownMaterial },
        { x: -4, y: -2, z: 0, material: earthBrownMaterial },
        { x: -2, y: -4, z: 0, material: earthBrownMaterial }
    ];

    // Air elemental barrier (bottom right)
    const airBarrierVoxels = [
        { x: 2, y: -2, z: 0, material: airCyanMaterial },
        { x: 3, y: -2, z: 0, material: airCyanMaterial },
        { x: 2, y: -3, z: 0, material: airCyanMaterial },
        { x: 3, y: -3, z: 0, material: airCyanMaterial },
        { x: 4, y: -2, z: 0, material: airCyanMaterial },
        { x: 2, y: -4, z: 0, material: airCyanMaterial }
    ];

    // Connecting energy streams
    const energyStreamVoxels = [
        // To fire barrier
        { x: 1, y: 1, z: 0, material: barrierGoldMaterial },
        { x: 2, y: 1, z: 0, material: mysticPurpleMaterial },
        { x: 1, y: 2, z: 0, material: mysticPurpleMaterial },

        // To water barrier
        { x: -1, y: 1, z: 0, material: barrierGoldMaterial },
        { x: -2, y: 1, z: 0, material: mysticPurpleMaterial },
        { x: -1, y: 2, z: 0, material: mysticPurpleMaterial },

        // To earth barrier
        { x: -1, y: -1, z: 0, material: barrierGoldMaterial },
        { x: -2, y: -1, z: 0, material: mysticPurpleMaterial },
        { x: -1, y: -2, z: 0, material: mysticPurpleMaterial },

        // To air barrier
        { x: 1, y: -1, z: 0, material: barrierGoldMaterial },
        { x: 2, y: -1, z: 0, material: mysticPurpleMaterial },
        { x: 1, y: -2, z: 0, material: mysticPurpleMaterial }
    ];

    // Outer protective ring
    const protectiveRingVoxels = [
        // Top ring
        { x: 0, y: 5, z: 0, material: shieldSilverMaterial },
        { x: 1, y: 5, z: 0, material: energyWhiteMaterial },
        { x: -1, y: 5, z: 0, material: energyWhiteMaterial },

        // Right ring
        { x: 5, y: 0, z: 0, material: shieldSilverMaterial },
        { x: 5, y: 1, z: 0, material: energyWhiteMaterial },
        { x: 5, y: -1, z: 0, material: energyWhiteMaterial },

        // Bottom ring
        { x: 0, y: -5, z: 0, material: shieldSilverMaterial },
        { x: 1, y: -5, z: 0, material: energyWhiteMaterial },
        { x: -1, y: -5, z: 0, material: energyWhiteMaterial },

        // Left ring
        { x: -5, y: 0, z: 0, material: shieldSilverMaterial },
        { x: -5, y: 1, z: 0, material: energyWhiteMaterial },
        { x: -5, y: -1, z: 0, material: energyWhiteMaterial }
    ];

    // Create all voxels
    [...shieldCoreVoxels, ...fireBarrierVoxels, ...waterBarrierVoxels, 
     ...earthBarrierVoxels, ...airBarrierVoxels, ...energyStreamVoxels, 
     ...protectiveRingVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.5, // Compact the layout
            voxel.y * VOXEL_SIZE * 0.5,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        
        // Categorize voxels for animation
        if (shieldCoreVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'core';
        } else if (fireBarrierVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'fire';
        } else if (waterBarrierVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'water';
        } else if (earthBarrierVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'earth';
        } else if (airBarrierVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'air';
        } else if (energyStreamVoxels.includes(voxel)) {
            mesh.userData.voxelType = 'stream';
        } else {
            mesh.userData.voxelType = 'ring';
        }
        
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        barrierRotation: 0,
        energyPulse: 0,
        elementalCycle: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update elemental shield card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateElementalShieldCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.barrierRotation += deltaTime * 1.5;
    cardGroup.userData.energyPulse += deltaTime * 3.0;
    cardGroup.userData.elementalCycle += deltaTime * 2.0;

    const time = cardGroup.userData.animationTime;
    const barrierRotation = cardGroup.userData.barrierRotation;
    const energyPulse = cardGroup.userData.energyPulse;
    const elementalCycle = cardGroup.userData.elementalCycle;

    // Apply animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.voxelType) {
            const baseOpacity = child.userData.originalOpacity;
            const baseEmissive = child.userData.originalEmissive;

            switch (child.userData.voxelType) {
                case 'core':
                    // Core pulses with energy
                    const corePulse = 0.8 + Math.sin(energyPulse * 4.0) * 0.4;
                    child.material.emissiveIntensity = baseEmissive * corePulse;
                    break;

                case 'fire':
                case 'water':
                case 'earth':
                case 'air':
                    // Elemental barriers flicker with their own rhythm
                    const elementOffset = child.userData.voxelType === 'fire' ? 0 :
                                        child.userData.voxelType === 'water' ? Math.PI/2 :
                                        child.userData.voxelType === 'earth' ? Math.PI :
                                        3*Math.PI/2;
                    const elementalFlicker = 0.7 + Math.sin(elementalCycle * 3.0 + elementOffset) * 0.5;
                    
                    child.material.emissiveIntensity = baseEmissive * elementalFlicker;
                    child.material.opacity = baseOpacity * elementalFlicker;
                    break;

                case 'stream':
                    // Energy streams flow and pulse
                    const streamFlow = 0.6 + Math.sin(energyPulse * 5.0 + child.position.x + child.position.y) * 0.6;
                    child.material.emissiveIntensity = baseEmissive * streamFlow;
                    break;

                case 'ring':
                    // Protective ring rotates and glows
                    const ringGlow = 0.8 + Math.sin(barrierRotation * 2.0 + child.position.x * 2 + child.position.y * 2) * 0.4;
                    child.material.emissiveIntensity = baseEmissive * ringGlow;
                    
                    // Slight orbital motion
                    const orbitOffset = Math.sin(barrierRotation + child.position.x + child.position.y) * 0.01;
                    child.position.z = orbitOffset;
                    break;
            }
        }
    });

    // Overall shield rotation
    cardGroup.rotation.z = barrierRotation * 0.3;
}

// Export the elemental shield card data for the loot system
export const ELEMENTAL_SHIELD_CARD_DATA = {
    name: 'Elemental Shield',
    description: 'Conjures rotating barriers of fire, water, earth, and air that orbit around you, blocking incoming attacks and reflecting elemental damage back at enemies.',
    category: 'card',
    rarity: 'rare',
    effect: 'elemental_shield',
    effectValue: 20,
    createFunction: createElementalShieldCard,
    updateFunction: updateElementalShieldCardAnimation,
    voxelModel: 'elemental_shield_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.2
    }
};

export default createElementalShieldCard;