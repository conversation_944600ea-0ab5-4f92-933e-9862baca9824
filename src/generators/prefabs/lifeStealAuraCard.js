import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Life Steal Aura Card Prefab
 * Creates an aura around the player that steals health from nearby enemies
 */

// Life Steal Aura specific colors
const LIFE_STEAL_COLORS = {
    CRIMSON_RED: 0xDC143C,          // Blood red core
    BLOOD_MAROON: 0x800000,         // Dark blood energy
    VAMPIRIC_PURPLE: 0x8B008B,      // Vampiric magic
    LIFE_GREEN: 0x32CD32,           // Life energy
    SOUL_GOLD: 0xFFD700,            // Soul essence
    DARK_RED: 0x8B0000,             // Dark crimson
    HEALTH_PINK: 0xFF1493,          // Health restoration
    ENERGY_ORANGE: 0xFF4500         // Energy transfer
};

/**
 * Create a life steal aura card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The life steal aura card 3D model
 */
export function createLifeStealAuraCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'LifeStealAuraCard';

    // Life Steal Aura materials
    const crimsonRedMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.CRIMSON_RED,
        emissive: LIFE_STEAL_COLORS.CRIMSON_RED,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const bloodMaroonMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.BLOOD_MAROON,
        emissive: LIFE_STEAL_COLORS.BLOOD_MAROON,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const vampiricPurpleMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.VAMPIRIC_PURPLE,
        emissive: LIFE_STEAL_COLORS.VAMPIRIC_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const lifeGreenMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.LIFE_GREEN,
        emissive: LIFE_STEAL_COLORS.LIFE_GREEN,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.7
    });

    const soulGoldMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.SOUL_GOLD,
        emissive: LIFE_STEAL_COLORS.SOUL_GOLD,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const darkRedMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.DARK_RED,
        emissive: LIFE_STEAL_COLORS.DARK_RED,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const healthPinkMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.HEALTH_PINK,
        emissive: LIFE_STEAL_COLORS.HEALTH_PINK,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const energyOrangeMaterial = new THREE.MeshLambertMaterial({
        color: LIFE_STEAL_COLORS.ENERGY_ORANGE,
        emissive: LIFE_STEAL_COLORS.ENERGY_ORANGE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Vampiric Heart Core (central life force)
    const vampiricHeartVoxels = [
        // Heart center
        { x: 0.0, y: 0.0, z: 0.0, material: crimsonRedMaterial }, // Core
        { x: -0.04, y: 0.04, z: 0.0, material: bloodMaroonMaterial }, // Left top
        { x: 0.04, y: 0.04, z: 0.0, material: bloodMaroonMaterial }, // Right top
        { x: -0.04, y: -0.04, z: 0.0, material: darkRedMaterial }, // Left bottom
        { x: 0.04, y: -0.04, z: 0.0, material: darkRedMaterial }, // Right bottom
        { x: 0.0, y: 0.04, z: 0.0, material: crimsonRedMaterial }, // Top
        { x: 0.0, y: -0.04, z: 0.0, material: crimsonRedMaterial }, // Bottom
        
        // Heart chambers
        { x: -0.02, y: 0.02, z: 0.02, material: bloodMaroonMaterial }, // Left chamber front
        { x: 0.02, y: 0.02, z: 0.02, material: bloodMaroonMaterial }, // Right chamber front
        { x: -0.02, y: 0.02, z: -0.02, material: bloodMaroonMaterial }, // Left chamber back
        { x: 0.02, y: 0.02, z: -0.02, material: bloodMaroonMaterial }, // Right chamber back
        { x: 0.0, y: -0.02, z: 0.02, material: darkRedMaterial }, // Bottom front
        { x: 0.0, y: -0.02, z: -0.02, material: darkRedMaterial }, // Bottom back
        
        // Pulsing arteries
        { x: -0.06, y: 0.02, z: 0.0, material: crimsonRedMaterial }, // Left artery
        { x: 0.06, y: 0.02, z: 0.0, material: crimsonRedMaterial }, // Right artery
        { x: 0.0, y: 0.06, z: 0.0, material: crimsonRedMaterial }, // Top artery
        { x: 0.0, y: -0.06, z: 0.0, material: darkRedMaterial } // Bottom artery
    ];

    // Blood Crystals (life force collectors)
    const bloodCrystalsVoxels = [
        // Inner crystal ring
        { x: -0.08, y: 0.0, z: 0.0, material: vampiricPurpleMaterial }, // Left
        { x: 0.08, y: 0.0, z: 0.0, material: vampiricPurpleMaterial }, // Right
        { x: 0.0, y: 0.08, z: 0.0, material: vampiricPurpleMaterial }, // Top
        { x: 0.0, y: -0.08, z: 0.0, material: vampiricPurpleMaterial }, // Bottom
        { x: -0.06, y: 0.06, z: 0.0, material: bloodMaroonMaterial }, // Top-left
        { x: 0.06, y: 0.06, z: 0.0, material: bloodMaroonMaterial }, // Top-right
        { x: -0.06, y: -0.06, z: 0.0, material: bloodMaroonMaterial }, // Bottom-left
        { x: 0.06, y: -0.06, z: 0.0, material: bloodMaroonMaterial }, // Bottom-right
        
        // Crystal points
        { x: -0.10, y: 0.02, z: 0.02, material: darkRedMaterial }, // Left point
        { x: 0.10, y: 0.02, z: 0.02, material: darkRedMaterial }, // Right point
        { x: 0.02, y: 0.10, z: 0.02, material: darkRedMaterial }, // Top point
        { x: 0.02, y: -0.10, z: 0.02, material: darkRedMaterial }, // Bottom point
        { x: -0.08, y: 0.08, z: 0.02, material: vampiricPurpleMaterial }, // Top-left point
        { x: 0.08, y: 0.08, z: 0.02, material: vampiricPurpleMaterial }, // Top-right point
        { x: -0.08, y: -0.08, z: 0.02, material: vampiricPurpleMaterial }, // Bottom-left point
        { x: 0.08, y: -0.08, z: 0.02, material: vampiricPurpleMaterial }, // Bottom-right point
        
        // Outer crystal ring
        { x: -0.12, y: 0.0, z: 0.0, material: bloodMaroonMaterial }, // Far left
        { x: 0.12, y: 0.0, z: 0.0, material: bloodMaroonMaterial }, // Far right
        { x: 0.0, y: 0.12, z: 0.0, material: bloodMaroonMaterial }, // Far top
        { x: 0.0, y: -0.12, z: 0.0, material: bloodMaroonMaterial }, // Far bottom
        { x: -0.085, y: 0.085, z: 0.0, material: darkRedMaterial }, // Far top-left
        { x: 0.085, y: 0.085, z: 0.0, material: darkRedMaterial }, // Far top-right
        { x: -0.085, y: -0.085, z: 0.0, material: darkRedMaterial }, // Far bottom-left
        { x: 0.085, y: -0.085, z: 0.0, material: darkRedMaterial } // Far bottom-right
    ];

    // Life Force Streams (energy transfer)
    const lifeForceStreamsVoxels = [
        // Inner streams
        { x: -0.10, y: 0.04, z: 0.04, material: lifeGreenMaterial },
        { x: 0.10, y: -0.04, z: 0.04, material: lifeGreenMaterial },
        { x: 0.04, y: 0.10, z: 0.04, material: lifeGreenMaterial },
        { x: -0.04, y: -0.10, z: 0.04, material: lifeGreenMaterial },
        { x: -0.08, y: 0.08, z: 0.04, material: healthPinkMaterial },
        { x: 0.08, y: 0.08, z: 0.04, material: healthPinkMaterial },
        { x: -0.08, y: -0.08, z: 0.04, material: healthPinkMaterial },
        { x: 0.08, y: -0.08, z: 0.04, material: healthPinkMaterial },
        
        // Middle streams
        { x: -0.14, y: 0.06, z: 0.02, material: soulGoldMaterial },
        { x: 0.14, y: -0.06, z: 0.02, material: soulGoldMaterial },
        { x: 0.06, y: 0.14, z: 0.02, material: soulGoldMaterial },
        { x: -0.06, y: -0.14, z: 0.02, material: soulGoldMaterial },
        { x: -0.10, y: 0.10, z: 0.02, material: energyOrangeMaterial },
        { x: 0.10, y: 0.10, z: 0.02, material: energyOrangeMaterial },
        { x: -0.10, y: -0.10, z: 0.02, material: energyOrangeMaterial },
        { x: 0.10, y: -0.10, z: 0.02, material: energyOrangeMaterial },
        
        // Outer streams
        { x: -0.18, y: 0.08, z: 0.0, material: vampiricPurpleMaterial },
        { x: 0.18, y: -0.08, z: 0.0, material: vampiricPurpleMaterial },
        { x: 0.08, y: 0.18, z: 0.0, material: vampiricPurpleMaterial },
        { x: -0.08, y: -0.18, z: 0.0, material: vampiricPurpleMaterial },
        { x: -0.13, y: 0.13, z: 0.0, material: healthPinkMaterial },
        { x: 0.13, y: 0.13, z: 0.0, material: healthPinkMaterial },
        { x: -0.13, y: -0.13, z: 0.0, material: healthPinkMaterial },
        { x: 0.13, y: -0.13, z: 0.0, material: healthPinkMaterial },
        
        // Far streams
        { x: -0.22, y: 0.10, z: -0.02, material: crimsonRedMaterial },
        { x: 0.22, y: -0.10, z: -0.02, material: crimsonRedMaterial },
        { x: 0.10, y: 0.22, z: -0.02, material: crimsonRedMaterial },
        { x: -0.10, y: -0.22, z: -0.02, material: crimsonRedMaterial },
        { x: -0.16, y: 0.16, z: -0.02, material: bloodMaroonMaterial },
        { x: 0.16, y: 0.16, z: -0.02, material: bloodMaroonMaterial },
        { x: -0.16, y: -0.16, z: -0.02, material: bloodMaroonMaterial },
        { x: 0.16, y: -0.16, z: -0.02, material: bloodMaroonMaterial }
    ];

    // Soul Essence Particles (vampiric energy)
    const soulEssenceVoxels = [
        // Inner essence ring
        { x: -0.12, y: 0.02, z: 0.06, material: soulGoldMaterial },
        { x: 0.12, y: -0.02, z: 0.06, material: soulGoldMaterial },
        { x: 0.02, y: 0.12, z: 0.06, material: soulGoldMaterial },
        { x: -0.02, y: -0.12, z: 0.06, material: soulGoldMaterial },
        { x: -0.085, y: 0.085, z: 0.06, material: lifeGreenMaterial },
        { x: 0.085, y: 0.085, z: 0.06, material: lifeGreenMaterial },
        { x: -0.085, y: -0.085, z: 0.06, material: lifeGreenMaterial },
        { x: 0.085, y: -0.085, z: 0.06, material: lifeGreenMaterial },
        
        // Middle essence ring
        { x: -0.16, y: 0.04, z: 0.04, material: energyOrangeMaterial },
        { x: 0.16, y: -0.04, z: 0.04, material: energyOrangeMaterial },
        { x: 0.04, y: 0.16, z: 0.04, material: energyOrangeMaterial },
        { x: -0.04, y: -0.16, z: 0.04, material: energyOrangeMaterial },
        { x: -0.113, y: 0.113, z: 0.04, material: healthPinkMaterial },
        { x: 0.113, y: 0.113, z: 0.04, material: healthPinkMaterial },
        { x: -0.113, y: -0.113, z: 0.04, material: healthPinkMaterial },
        { x: 0.113, y: -0.113, z: 0.04, material: healthPinkMaterial },
        
        // Outer essence ring
        { x: -0.20, y: 0.06, z: 0.02, material: vampiricPurpleMaterial },
        { x: 0.20, y: -0.06, z: 0.02, material: vampiricPurpleMaterial },
        { x: 0.06, y: 0.20, z: 0.02, material: vampiricPurpleMaterial },
        { x: -0.06, y: -0.20, z: 0.02, material: vampiricPurpleMaterial },
        { x: -0.141, y: 0.141, z: 0.02, material: crimsonRedMaterial },
        { x: 0.141, y: 0.141, z: 0.02, material: crimsonRedMaterial },
        { x: -0.141, y: -0.141, z: 0.02, material: crimsonRedMaterial },
        { x: 0.141, y: -0.141, z: 0.02, material: crimsonRedMaterial },
        
        // Far essence particles
        { x: -0.24, y: 0.08, z: 0.0, material: bloodMaroonMaterial },
        { x: 0.24, y: -0.08, z: 0.0, material: bloodMaroonMaterial },
        { x: 0.08, y: 0.24, z: 0.0, material: bloodMaroonMaterial },
        { x: -0.08, y: -0.24, z: 0.0, material: bloodMaroonMaterial },
        { x: -0.17, y: 0.17, z: 0.0, material: darkRedMaterial },
        { x: 0.17, y: 0.17, z: 0.0, material: darkRedMaterial },
        { x: -0.17, y: -0.17, z: 0.0, material: darkRedMaterial },
        { x: 0.17, y: -0.17, z: 0.0, material: darkRedMaterial }
    ];

    // Create vampiric heart group
    const vampiricHeartGroup = new THREE.Group();
    vampiricHeartGroup.name = 'vampiricHeartGroup';

    // Add vampiric heart voxels
    vampiricHeartVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.heartPhase = index * 0.08; // Stagger animation
        vampiricHeartGroup.add(mesh);
    });

    // Create blood crystals group
    const bloodCrystalsGroup = new THREE.Group();
    bloodCrystalsGroup.name = 'bloodCrystalsGroup';

    // Add blood crystals voxels
    bloodCrystalsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.crystalPhase = index * 0.06; // Stagger animation
        bloodCrystalsGroup.add(mesh);
    });

    // Create life force streams group
    const lifeForceStreamsGroup = new THREE.Group();
    lifeForceStreamsGroup.name = 'lifeForceStreamsGroup';

    // Add life force streams voxels
    lifeForceStreamsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.streamPhase = index * 0.04; // Stagger animation
        lifeForceStreamsGroup.add(mesh);
    });

    // Create soul essence group
    const soulEssenceGroup = new THREE.Group();
    soulEssenceGroup.name = 'soulEssenceGroup';

    // Add soul essence voxels
    soulEssenceVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.essencePhase = index * 0.1; // Stagger animation
        soulEssenceGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(vampiricHeartGroup);
    cardGroup.add(bloodCrystalsGroup);
    cardGroup.add(lifeForceStreamsGroup);
    cardGroup.add(soulEssenceGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        heartbeat: 0,
        lifeForceFlow: 0,
        soulDrain: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update life steal aura card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateLifeStealAuraCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.heartbeat += deltaTime * 4.0; // Heartbeat speed
    cardGroup.userData.lifeForceFlow += deltaTime * 2.5; // Life force flow speed
    cardGroup.userData.soulDrain += deltaTime * 3.0; // Soul drain speed

    const time = cardGroup.userData.animationTime;
    const heartbeat = cardGroup.userData.heartbeat;
    const lifeForceFlow = cardGroup.userData.lifeForceFlow;
    const soulDrain = cardGroup.userData.soulDrain;

    // Animate vampiric heart (life force center)
    const vampiricHeartGroup = cardGroup.getObjectByName('vampiricHeartGroup');
    if (vampiricHeartGroup) {
        // Heart pulsing (vampiric heartbeat)
        const heartPulse = Math.sin(heartbeat * 5.0) * 0.002;
        vampiricHeartGroup.position.y = heartPulse;
        
        // Individual heart element animation
        vampiricHeartGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.heartPhase !== undefined) {
                const heartTime = heartbeat + mesh.userData.heartPhase;
                
                // Heart chamber pulsing
                const chamberPulse = Math.sin(heartTime * 8.0) * 0.0008;
                const chamberThrob = Math.cos(heartTime * 6.0) * 0.0006;
                
                mesh.position.x = mesh.userData.originalPosition.x + chamberPulse;
                mesh.position.z = mesh.userData.originalPosition.z + chamberThrob;
                
                // Heart intensity pulsing
                const heartIntensity = 1.0 + Math.sin(heartTime * 7.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * heartIntensity;
                }
                
                // Heart scale pulsing
                const heartScale = 0.9 + Math.sin(heartTime * 5.0) * 0.3;
                mesh.scale.setScalar(heartScale);
            }
        });
    }

    // Animate blood crystals (life force collectors)
    const bloodCrystalsGroup = cardGroup.getObjectByName('bloodCrystalsGroup');
    if (bloodCrystalsGroup) {
        // Crystal rotation (blood energy collection)
        const crystalRotation = Math.sin(soulDrain * 1.2) * 0.03;
        bloodCrystalsGroup.rotation.z = crystalRotation;
        
        bloodCrystalsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.crystalPhase !== undefined) {
                const crystalTime = soulDrain + mesh.userData.crystalPhase;
                
                // Crystal energy pulsing
                const crystalPulse = Math.sin(crystalTime * 4.0) * 0.003;
                const crystalVibration = Math.cos(crystalTime * 6.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + crystalPulse;
                mesh.position.y = mesh.userData.originalPosition.y + crystalVibration;
                
                // Crystal glow
                const crystalGlow = 1.0 + Math.sin(crystalTime * 5.0) * 0.6;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crystalGlow;
                }
                
                // Crystal opacity variation
                const crystalOpacity = 0.8 + Math.sin(crystalTime * 4.5) * 0.2;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * crystalOpacity;
                }
            }
        });
    }

    // Animate life force streams (energy transfer)
    const lifeForceStreamsGroup = cardGroup.getObjectByName('lifeForceStreamsGroup');
    if (lifeForceStreamsGroup) {
        lifeForceStreamsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.streamPhase !== undefined) {
                const streamTime = lifeForceFlow + mesh.userData.streamPhase;
                
                // Life force flowing motion
                const streamFlow = Math.sin(streamTime * 5.0) * 0.004;
                const streamWave = Math.cos(streamTime * 3.5) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + streamFlow;
                mesh.position.y = mesh.userData.originalPosition.y + streamWave;
                
                // Stream energy intensity
                const streamIntensity = 1.0 + Math.sin(streamTime * 6.0) * 0.7;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * streamIntensity;
                }
                
                // Stream scale variation (energy transfer)
                const streamScale = 0.8 + Math.sin(streamTime * 4.0) * 0.4;
                mesh.scale.setScalar(streamScale);
                
                // Stream opacity (energy flow)
                const streamOpacity = 0.7 + Math.sin(streamTime * 5.5) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * streamOpacity;
                }
            }
        });
    }

    // Animate soul essence (vampiric energy)
    const soulEssenceGroup = cardGroup.getObjectByName('soulEssenceGroup');
    if (soulEssenceGroup) {
        soulEssenceGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.essencePhase !== undefined) {
                const essenceTime = soulDrain + mesh.userData.essencePhase;
                
                // Soul essence spiral motion (vampiric drain)
                const essenceSpiral = Math.sin(essenceTime * 3.0) * 0.005;
                const essenceFloat = Math.cos(essenceTime * 2.0) * 0.004;
                
                mesh.position.x = mesh.userData.originalPosition.x + essenceSpiral;
                mesh.position.y = mesh.userData.originalPosition.y + essenceFloat;
                
                // Essence energy intensity
                const essenceIntensity = 1.0 + Math.sin(essenceTime * 7.0) * 0.8;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * essenceIntensity;
                }
                
                // Essence scale variation (soul absorption)
                const essenceScale = 0.7 + Math.sin(essenceTime * 5.0) * 0.5;
                mesh.scale.setScalar(essenceScale);
                
                // Essence opacity (vampiric energy)
                const essenceOpacity = 0.6 + Math.sin(essenceTime * 6.5) * 0.4;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * essenceOpacity;
                }
            }
        });
    }

    // Overall life steal aura presence (epic vampiric magic)
    const auraPulse = 1 + Math.sin(time * 3.0) * 0.08;
    const auraShift = Math.cos(time * 4.0) * 0.001;
    cardGroup.scale.setScalar(0.8 * auraPulse);
    cardGroup.position.x += auraShift;
    cardGroup.position.z += auraShift * 0.7;
}

// Export the life steal aura card data for the loot system
export const LIFE_STEAL_AURA_CARD_DATA = {
    name: "Life Steal Aura",
    description: 'Creates a vampiric aura around the player for 20 seconds that steals health from nearby enemies and heals the player.',
    category: 'card',
    rarity: 'epic',
    effect: 'life_steal_aura',
    effectValue: 20, // Duration in seconds
    createFunction: createLifeStealAuraCard,
    updateFunction: updateLifeStealAuraCardAnimation,
    voxelModel: 'life_steal_aura_card',
    glow: {
        color: 0xDC143C,
        intensity: 1.8
    }
};

export default createLifeStealAuraCard;