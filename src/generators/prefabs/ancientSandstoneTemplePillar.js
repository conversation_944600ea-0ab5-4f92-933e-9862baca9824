import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Ancient Sandstone Temple Pillar
 * 
 * Creates weathered sandstone temple pillars with classical proportions.
 * Uses smaller voxels than the ancient stone pillar for more detailed temple architecture.
 * Matches the faded yellowish stone color palette of the room.
 */

// Ancient sandstone materials - faded yellowish temple stone (matching room palette)
const SANDSTONE_TEMPLE_MATERIALS = [
    _getMaterialByHex_Cached('A0A089'), // Light faded yellow stone (primary)
    _getMaterialByHex_Cached('8B8B7A'), // Faded yellowish stone (secondary)
    _getMaterialByHex_Cached('B5B59E'), // Lightest faded yellow (highlights)
    _getMaterialByHex_Cached('6B6B5A'), // Darker faded stone (shadows)
    _getMaterialByHex_Cached('C0C0A8'), // Very light faded yellow (worn edges)
    _getMaterialByHex_Cached('5A5A4A'), // Very dark weathered stone (deep shadows)
];

// Define the temple pillar shape using voxel coordinates
// Structure: Base -> Column -> Capital -> Top
const templePillarShape = [
    // === TEMPLE BASE (Y: 0-3) ===
    // Foundation layer (Y=0) - 6x6 square base for temple grandeur
    ...generateSquareLayer(0, 3, SANDSTONE_TEMPLE_MATERIALS[3]), // Dark base
    // Second layer (Y=1) - 5x5 square
    ...generateSquareLayer(1, 2.5, SANDSTONE_TEMPLE_MATERIALS[1]),
    // Third layer (Y=2) - 4x4 square
    ...generateSquareLayer(2, 2, SANDSTONE_TEMPLE_MATERIALS[0]),
    // Top base layer (Y=3) - 3x3 square
    ...generateSquareLayer(3, 1.5, SANDSTONE_TEMPLE_MATERIALS[2]),

    // === TEMPLE COLUMN (Y: 4-18) ===
    // Main column shaft - consistent 2x2 cross section with classical fluting
    ...generateColumnLayer(4, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(5, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(6, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(7, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(8, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(9, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(10, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(11, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(12, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(13, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(14, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(15, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(16, SANDSTONE_TEMPLE_MATERIALS[0]),
    ...generateColumnLayer(17, SANDSTONE_TEMPLE_MATERIALS[1]),
    ...generateColumnLayer(18, SANDSTONE_TEMPLE_MATERIALS[0]),

    // === TEMPLE CAPITAL (Y: 19-22) ===
    // Capital base (Y=19) - 3x3 square
    ...generateSquareLayer(19, 1.5, SANDSTONE_TEMPLE_MATERIALS[2]),
    // Capital middle (Y=20) - 4x4 square (flared)
    ...generateSquareLayer(20, 2, SANDSTONE_TEMPLE_MATERIALS[0]),
    // Capital top (Y=21) - 5x5 square (flared wider)
    ...generateSquareLayer(21, 2.5, SANDSTONE_TEMPLE_MATERIALS[2]),
    // Capital crown (Y=22) - 4x4 square
    ...generateSquareLayer(22, 2, SANDSTONE_TEMPLE_MATERIALS[1]),

    // === TEMPLE DETAILS ===
    // Weathering and ancient details on column
    { x: 1, y: 6, z: 1, c: SANDSTONE_TEMPLE_MATERIALS[3] },   // Weathering detail
    { x: -1, y: 9, z: 1, c: SANDSTONE_TEMPLE_MATERIALS[3] },  // Age marks
    { x: 1, y: 12, z: -1, c: SANDSTONE_TEMPLE_MATERIALS[3] }, // Temple weathering
    { x: -1, y: 15, z: -1, c: SANDSTONE_TEMPLE_MATERIALS[3] }, // Ancient marks

    // Base edge details
    { x: 3, y: 1, z: 0, c: SANDSTONE_TEMPLE_MATERIALS[3] },   // Base edge weathering
    { x: -3, y: 1, z: 0, c: SANDSTONE_TEMPLE_MATERIALS[3] },  // Base edge weathering
    { x: 0, y: 1, z: 3, c: SANDSTONE_TEMPLE_MATERIALS[3] },   // Base edge weathering
    { x: 0, y: 1, z: -3, c: SANDSTONE_TEMPLE_MATERIALS[3] },  // Base edge weathering

    // Capital decorative details
    { x: 0, y: 20, z: 2, c: SANDSTONE_TEMPLE_MATERIALS[4] },  // Capital highlight
    { x: 2, y: 20, z: 0, c: SANDSTONE_TEMPLE_MATERIALS[4] },  // Capital highlight
    { x: 0, y: 21, z: 1, c: SANDSTONE_TEMPLE_MATERIALS[4] },  // Capital crown detail
    { x: 1, y: 21, z: 0, c: SANDSTONE_TEMPLE_MATERIALS[4] },  // Capital crown detail
];

// Helper function to generate a square layer with rounded edges for temple style
function generateSquareLayer(y, radius, color) {
    const layer = [];
    const intRadius = Math.floor(radius);
    const hasHalfStep = radius % 1 !== 0;
    
    for (let x = -intRadius; x <= intRadius; x++) {
        for (let z = -intRadius; z <= intRadius; z++) {
            // For fractional radius, create more refined temple edges
            if (hasHalfStep && Math.abs(x) === intRadius && Math.abs(z) === intRadius) {
                // Skip corner voxels for temple refinement
                continue;
            }
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate temple column layers (3x3 cross section for more substantial temple feel)
function generateColumnLayer(y, color) {
    return [
        { x: -1, y, z: -1, c: color },
        { x: 0, y, z: -1, c: color },
        { x: 1, y, z: -1, c: color },
        { x: -1, y, z: 0, c: color },
        { x: 0, y, z: 0, c: color },
        { x: 1, y, z: 0, c: color },
        { x: -1, y, z: 1, c: color },
        { x: 0, y, z: 1, c: color },
        { x: 1, y, z: 1, c: color }
    ];
}

// --- Main Prefab Function ---
export function createAncientSandstoneTemplePillar(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 887); // Different multiplier for temple variation

    // Smaller voxel size than ancient stone pillar for more detailed temple architecture
    const templePillarVoxelSize = VOXEL_SIZE * 3.5; // Smaller than ancient pillar (5.12)
    const baseGeometry = getOrCreateGeometry('temple_pillar_voxel', () =>
        new THREE.BoxGeometry(templePillarVoxelSize, templePillarVoxelSize, templePillarVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the temple pillar shape
    templePillarShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to sandstone color for weathered temple effect
        let finalColor = c;
        if (rng() < 0.2) { // 20% chance for temple weathering
            const colorIndex = Math.floor(rng() * SANDSTONE_TEMPLE_MATERIALS.length);
            finalColor = SANDSTONE_TEMPLE_MATERIALS[colorIndex];
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * templePillarVoxelSize,
            y * templePillarVoxelSize,
            z * templePillarVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'ancient_sandstone_temple_pillar',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : false,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: templePillarVoxelSize,
        originalVoxels: templePillarShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 29 + v.x * 7 + v.y * 11 + v.z * 13);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('[AncientSandstoneTemplePillar] Created temple pillar with options:', options);

    return group;
}