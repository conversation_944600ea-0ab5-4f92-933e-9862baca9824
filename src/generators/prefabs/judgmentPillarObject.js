import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a judgment pillar - tall stone pillars marking cardinal points in the Eye of Judgment chamber
 * These pillars represent the four aspects of moral judgment: Truth, Justice, Mercy, and Wisdom
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Judgment pillar object group
 */
export function createJudgmentPillarObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 999);

    // Use standard voxel size for pillars
    const pillarVoxelSize = VOXEL_SIZE * 2.5;
    const baseGeometry = getOrCreateGeometry('judgment_pillar_voxel', () =>
        new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize)
    );

    // Judgment pillar materials (ancient stone with mystical accents)
    const baseStoneMaterial = _getMaterialByHex_Cached('4A4A4A', {
        roughness: 0.9,
        metalness: 0.1
    }); // Medium gray base stone

    const darkStoneMaterial = _getMaterialByHex_Cached('3C3C3C', {
        roughness: 0.9,
        metalness: 0.05
    }); // Dark gray accent stone

    const mysticalAccentMaterial = _getMaterialByHex_Cached('4B0082', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.2,
        roughness: 0.6,
        metalness: 0.3
    }); // Indigo mystical accents

    const capMaterial = _getMaterialByHex_Cached('6A5ACD', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x483D8B),
        emissiveIntensity: 0.15,
        roughness: 0.5,
        metalness: 0.4
    }); // Slate blue cap with subtle glow

    // Create pillar base (3x3 foundation)
    const basePositions = [
        { x: -1, y: 0, z: -1 }, { x: 0, y: 0, z: -1 }, { x: 1, y: 0, z: -1 },
        { x: -1, y: 0, z: 0 },  { x: 0, y: 0, z: 0 },  { x: 1, y: 0, z: 0 },
        { x: -1, y: 0, z: 1 },  { x: 0, y: 0, z: 1 },  { x: 1, y: 0, z: 1 }
    ];

    basePositions.forEach(pos => {
        const voxel = new THREE.Mesh(baseGeometry.clone(), baseStoneMaterial);
        voxel.position.set(
            pos.x * pillarVoxelSize,
            pos.y * pillarVoxelSize,
            pos.z * pillarVoxelSize
        );
        voxel.userData.isPillarPart = true;
        voxel.userData.partType = 'base';
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    });

    // Create pillar shaft (central column, 8 voxels high)
    for (let y = 1; y <= 8; y++) {
        const material = (y % 2 === 0) ? baseStoneMaterial : darkStoneMaterial;
        
        const voxel = new THREE.Mesh(baseGeometry.clone(), material);
        voxel.position.set(
            0,
            y * pillarVoxelSize,
            0
        );
        voxel.userData.isPillarPart = true;
        voxel.userData.partType = 'shaft';
        voxel.userData.shaftLevel = y;
        voxel.castShadow = true;
        voxel.receiveShadow = true;
        group.add(voxel);
    }

    // Add mystical accent bands at specific heights
    const accentLevels = [3, 6];
    accentLevels.forEach(level => {
        // Create accent ring around the shaft
        const ringPositions = [
            { x: -1, z: 0 }, { x: 1, z: 0 }, { x: 0, z: -1 }, { x: 0, z: 1 }
        ];
        
        ringPositions.forEach(pos => {
            const accent = new THREE.Mesh(
                getOrCreateGeometry('pillar_accent', () =>
                    new THREE.BoxGeometry(pillarVoxelSize * 0.7, pillarVoxelSize * 0.4, pillarVoxelSize * 0.7)
                ),
                mysticalAccentMaterial
            );
            accent.position.set(
                pos.x * pillarVoxelSize,
                level * pillarVoxelSize,
                pos.z * pillarVoxelSize
            );
            accent.userData.isPillarPart = true;
            accent.userData.partType = 'accent';
            accent.userData.accentLevel = level;
            accent.castShadow = false; // Emissive parts don't cast shadows
            accent.receiveShadow = true;
            group.add(accent);
        });
    });

    // Create pillar capital (ornate top, 2x2)
    const capitalHeight = 9;
    const capitalPositions = [
        { x: -0.5, z: -0.5 }, { x: 0.5, z: -0.5 },
        { x: -0.5, z: 0.5 },  { x: 0.5, z: 0.5 }
    ];

    capitalPositions.forEach(pos => {
        const capital = new THREE.Mesh(
            getOrCreateGeometry('pillar_capital', () =>
                new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize * 0.8, pillarVoxelSize)
            ),
            capMaterial
        );
        capital.position.set(
            pos.x * pillarVoxelSize,
            capitalHeight * pillarVoxelSize,
            pos.z * pillarVoxelSize
        );
        capital.userData.isPillarPart = true;
        capital.userData.partType = 'capital';
        capital.castShadow = false;
        capital.receiveShadow = true;
        group.add(capital);
    });

    // Add mystical runes on pillar faces
    const runeMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.7,
        emissive: new THREE.Color(0x8A2BE2),
        emissiveIntensity: 0.3
    }); // Blue violet runes

    // Place runes on four faces of the pillar at mid-height
    const runeHeight = 4.5;
    const runeFaces = [
        { x: 0, z: -1.2, rot: 0 },        // North face
        { x: 1.2, z: 0, rot: Math.PI/2 }, // East face  
        { x: 0, z: 1.2, rot: Math.PI },   // South face
        { x: -1.2, z: 0, rot: -Math.PI/2 } // West face
    ];

    runeFaces.forEach((face, index) => {
        // Create 3x3 rune pattern on each face
        for (let rx = -1; rx <= 1; rx++) {
            for (let ry = -1; ry <= 1; ry++) {
                // Skip some voxels to create rune patterns
                if (Math.abs(rx) + Math.abs(ry) > 1.5 && rng() > 0.4) continue;
                
                const rune = new THREE.Mesh(
                    getOrCreateGeometry('pillar_rune', () =>
                        new THREE.BoxGeometry(pillarVoxelSize * 0.3, pillarVoxelSize * 0.3, pillarVoxelSize * 0.1)
                    ),
                    runeMaterial
                );
                
                // Position rune relative to face
                const localX = rx * pillarVoxelSize * 0.4;
                const localY = (runeHeight + ry * 0.4) * pillarVoxelSize;
                
                // Rotate and position based on face
                rune.position.set(
                    face.x * pillarVoxelSize + Math.cos(face.rot) * localX,
                    localY,
                    face.z * pillarVoxelSize + Math.sin(face.rot) * localX
                );
                rune.rotation.y = face.rot;
                
                rune.userData.isPillarPart = true;
                rune.userData.partType = 'rune';
                rune.userData.runeIndex = index;
                rune.userData.animationPhase = (index * Math.PI / 2) + (rx + ry) * 0.1;
                rune.castShadow = false;
                rune.receiveShadow = false;
                group.add(rune);
            }
        }
    });

    // Add floating energy orbs around the capital
    const orbMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x9370DB),
        emissiveIntensity: 0.4
    }); // Medium orchid orbs

    const orbCount = 4;
    for (let i = 0; i < orbCount; i++) {
        const angle = (i / orbCount) * Math.PI * 2;
        const radius = 2.5;
        const orbHeight = capitalHeight + 0.5;
        
        const orb = new THREE.Mesh(
            getOrCreateGeometry('judgment_orb', () =>
                new THREE.BoxGeometry(pillarVoxelSize * 0.4, pillarVoxelSize * 0.4, pillarVoxelSize * 0.4)
            ),
            orbMaterial
        );
        orb.position.set(
            Math.cos(angle) * radius * pillarVoxelSize,
            orbHeight * pillarVoxelSize,
            Math.sin(angle) * radius * pillarVoxelSize
        );
        orb.userData.isPillarPart = true;
        orb.userData.partType = 'orb';
        orb.userData.animationPhase = angle;
        orb.userData.baseRadius = radius;
        orb.userData.baseHeight = orbHeight;
        orb.castShadow = false;
        orb.receiveShadow = false;
        group.add(orb);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'judgment_pillar',
        isDecorative: true,
        isEventObject: true,
        objectId: options.userData?.objectId || 'judgment_pillar',
        hasAnimation: true,
        animationType: 'mystical_pillar',
        voxelScale: pillarVoxelSize,
        // Pillar properties
        isJudgmentPillar: true,
        cardinalDirection: options.userData?.cardinalDirection || 'north',
        pillarAspect: options.userData?.pillarAspect || 'wisdom', // truth, justice, mercy, wisdom
        // Animation properties
        runeGlowSpeed: 0.003,
        orbRotationSpeed: 0.001,
        mysticalPulseSpeed: 0.002
    };

    group.name = 'judgment_pillar';

    console.log('[JudgmentPillar] ✅ Created judgment pillar with mystical runes and floating orbs');
    return group;
}

/**
 * Animate the judgment pillar with rune glowing and floating orbs
 * @param {THREE.Group} pillarGroup - The pillar group to animate
 * @param {number} deltaTime - Time since last frame
 * @param {number} time - Total elapsed time
 */
export function animateJudgmentPillar(pillarGroup, deltaTime, time) {
    if (!pillarGroup || !pillarGroup.userData.isJudgmentPillar) return;

    const { runeGlowSpeed, orbRotationSpeed, mysticalPulseSpeed } = pillarGroup.userData;

    // Animate different parts based on their type
    pillarGroup.traverse(child => {
        if (!child.userData.partType) return;

        const { partType } = child.userData;

        switch (partType) {
            case 'rune':
                // Runes pulse with different phases
                const runePhase = child.userData.animationPhase + time * runeGlowSpeed;
                const runePulse = (Math.sin(runePhase) + 1) * 0.5; // 0 to 1
                
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.3 + runePulse * 0.2;
                }
                if (child.material.opacity) {
                    child.material.opacity = 0.7 + runePulse * 0.3;
                }
                break;

            case 'orb':
                // Orbs rotate around the pillar capital
                const orbPhase = child.userData.animationPhase + time * orbRotationSpeed;
                const radius = child.userData.baseRadius + Math.sin(orbPhase * 3) * 0.3;
                const height = child.userData.baseHeight + Math.cos(orbPhase * 2) * 0.2;
                
                child.position.x = Math.cos(orbPhase) * radius * pillarGroup.userData.voxelScale;
                child.position.y = height * pillarGroup.userData.voxelScale;
                child.position.z = Math.sin(orbPhase) * radius * pillarGroup.userData.voxelScale;
                
                // Orb pulsing
                const orbPulse = (Math.sin(orbPhase * 4) + 1) * 0.5;
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.4 + orbPulse * 0.2;
                }
                break;

            case 'accent':
                // Accent bands pulse in sequence
                const accentPulse = Math.sin(time * mysticalPulseSpeed + 
                    child.userData.accentLevel * Math.PI / 2) * 0.5 + 0.5;
                
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.2 + accentPulse * 0.1;
                }
                break;

            case 'capital':
                // Capital has gentle overall glow
                const capitalPulse = Math.sin(time * mysticalPulseSpeed * 0.7) * 0.5 + 0.5;
                
                if (child.material.emissive) {
                    child.material.emissiveIntensity = 0.15 + capitalPulse * 0.08;
                }
                break;
        }
    });
}