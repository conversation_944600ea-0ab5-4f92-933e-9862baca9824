import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a mysterious crystal object (voxel-style, interactable like chest)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Crystal object group
 */
export function createMysteriousCrystalObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888);

    // Use consistent voxel size
    const crystalVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('mysterious_crystal_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Create voxel-style crystal base (stone platform)
    const baseMaterial = _getMaterialByHex_Cached('444444', {
        emissive: new THREE.Color(0x111111),
        emissiveIntensity: 0.1
    });

    // Stone platform base (2x2)
    for (let x = -0.5; x <= 0.5; x += 1) {
        for (let z = -0.5; z <= 0.5; z += 1) {
            const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
            baseVoxel.position.set(
                x * crystalVoxelSize,
                crystalVoxelSize * 0.5,
                z * crystalVoxelSize
            );
            baseVoxel.userData.isFloorObject = true;
            baseVoxel.userData.hasCollision = true;
            baseVoxel.castShadow = true;
            baseVoxel.receiveShadow = true;
            group.add(baseVoxel);
        }
    }

    // Crystal core (glowing purple/blue crystal)
    const crystalMaterial = _getMaterialByHex_Cached('8A2BE2', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.4
    });

    // Main crystal body (cross pattern for interesting shape)
    const crystalPositions = [
        { x: 0, y: 1.5, z: 0 }, // Center
        { x: 0, y: 2.5, z: 0 }, // Top center
        { x: -1, y: 1.5, z: 0 }, // Left
        { x: 1, y: 1.5, z: 0 }, // Right
        { x: 0, y: 1.5, z: -1 }, // Front
        { x: 0, y: 1.5, z: 1 }, // Back
    ];

    crystalPositions.forEach(pos => {
        const crystalVoxel = new THREE.Mesh(baseGeometry.clone(), crystalMaterial);
        crystalVoxel.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        crystalVoxel.userData.isFloorObject = true;
        crystalVoxel.userData.hasCollision = true;
        crystalVoxel.castShadow = false; // Glowing objects don't cast shadows
        crystalVoxel.receiveShadow = true;
        group.add(crystalVoxel);
    });

    // Crystal tips (brighter glow)
    const tipMaterial = _getMaterialByHex_Cached('9370DB', {
        transparent: true,
        opacity: 0.9,
        emissive: new THREE.Color(0x6A5ACD),
        emissiveIntensity: 0.6
    });

    const tipGeometry = getOrCreateGeometry('crystal_tip', () =>
        new THREE.BoxGeometry(crystalVoxelSize * 0.6, crystalVoxelSize * 0.6, crystalVoxelSize * 0.6)
    );

    const tip = new THREE.Mesh(tipGeometry, tipMaterial);
    tip.position.set(0, crystalVoxelSize * 3, 0);
    tip.userData.isFloorObject = true;
    tip.castShadow = false;
    tip.receiveShadow = true;
    group.add(tip);

    // Set up group properties with chest-like interaction
    group.userData = {
        ...(options.userData || {}),
        objectType: 'mysterious_crystal',
        isInteractable: true,
        isEventObject: true,
        objectId: options.userData?.objectId || 'mysterious_pond_crystal',
        interactionRange: 3.0,
        isChest: true, // Helps with interaction detection
        chestType: 'event_object',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: crystalVoxelSize
    };

    // Add collision detection to all crystal components
    group.traverse(child => {
        if (child.isMesh) {
            child.userData.isFloorObject = true;
            child.userData.hasCollision = true;
            child.userData.objectType = 'mysterious_crystal';
        }
    });

    group.name = 'mysterious_crystal';

    console.log('[MysteriousCrystalObject] ✅ Created voxel-style mysterious crystal with chest-like interaction');
    return group;
}
