import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    floorTileMaterial1, floorTileMaterial2, floorCrackedMaterial,
    floorTileGeo,
    mulberry32, _getMaterialByHex_Cached
} from './shared.js';

/**
 * Creates a randomized floor overlay mesh.
 * Uses ENVIRONMENT_PIXEL_SCALE for chunky look.
 * Includes rounded corners and edge variation.
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Mesh|THREE.Group|null} The merged floor overlay mesh or group, or null if empty.
 */
export function createFloorOverlay(width, depth, roomData) {
    const geometriesByMaterial = {}; 
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    const numX_env = Math.ceil(width / (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE));
    const numZ_env = Math.ceil(depth / (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE));
    // Correct offsetX and offsetZ calculation for positioning tiles
    const totalWidth_env = numX_env * (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE);
    const totalDepth_env = numZ_env * (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE);
    const offsetX = (totalWidth_env - (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE)) / 2;
    const offsetZ = (totalDepth_env - (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE)) / 2;
    
    const overlayY = VOXEL_SIZE * 5.0; // EXTREME offset - 5x voxel height to completely eliminate z-fighting

    // --- Use Seeded PRNG ---
    const roomSeed = roomData ? roomData.id * 61 + 41 : Date.now(); // Use room ID for seed
    const random = mulberry32(roomSeed);
    // ---------------------

    const centerEX = (numX_env - 1) / 2;
    const centerEZ = (numZ_env - 1) / 2;
    const maxDistSq = Math.max(1, centerEX * centerEX + centerEZ * centerEZ); // Ensure maxDistSq is at least 1
    const cornerCutoffFactor = 0.8; 
    const crackProbability = 0.05; // Re-enabled with extreme Y-offset

    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            // Calculate distance from center (normalized)
            const normDx = (ex - centerEX) / Math.max(1, centerEX);
            const normDz = (ez - centerEZ) / Math.max(1, centerEZ);
            const distSq = normDx * normDx + normDz * normDz;

            // --- Rounded Corners Logic ---
            // Skip tiles that are far from the center AND near a corner simultaneously
            if (distSq > cornerCutoffFactor * cornerCutoffFactor && // Check squared distance
                (Math.abs(normDx) > 0.7 && Math.abs(normDz) > 0.7)) { 
                 continue; 
            }
            // ---------------------------

            // --- Determine Material (Checkerboard + Crack Chance) ---
            let tileMaterial;
            // 1. Determine base checkerboard color
            if ((ex + ez) % 2 === 0) {
                tileMaterial = floorTileMaterial1;
            } else {
                tileMaterial = floorTileMaterial2;
            }
            // 2. Apply low random chance for cracked tile override
            if (random() < crackProbability) {
                tileMaterial = floorCrackedMaterial;
            }
            // ---------------------------------------------------------

            // Calculate position for this env tile
            const posX = ex * (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE) - offsetX;
            const posZ = ez * (VOXEL_SIZE * ENVIRONMENT_PIXEL_SCALE) - offsetZ;
            
            tempMatrix.makeTranslation(posX, overlayY, posZ);
            addGeometry(floorTileGeo, tileMaterial, tempMatrix);
        }
    }

    // --- Merge Geometries --- 
    const finalMeshes = [];
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex);
        if (!material) continue;
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
             const mesh = new THREE.Mesh(mergedGeometry, material);
             mesh.receiveShadow = true; // Floor should receive shadows
             // mesh.castShadow = false; // Floor overlay likely doesn't need to cast shadows
             finalMeshes.push(mesh);
        } else {
            console.warn(`Failed to merge geometry for floor overlay, material: ${colorHex}`);
        }
    }

    if (finalMeshes.length === 0) return null;
    if (finalMeshes.length === 1) return finalMeshes[0]; // Return single mesh if only one material

    // If multiple materials, return a group
    const finalGroup = new THREE.Group();
    finalGroup.name = "floorOverlay"; // Name the group
    finalMeshes.forEach(mesh => {
        finalGroup.add(mesh);
    });
    return finalGroup; 
} 