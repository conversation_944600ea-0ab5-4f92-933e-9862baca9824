import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Temporal Rift Card Prefab
 * Creates swirling clockwork gears and time distortion effects for time manipulation magic
 */

// Temporal rift specific colors
const TEMPORAL_COLORS = {
    BRONZE_DARK: 0x8B4513,      // Dark bronze
    BRONZE_LIGHT: 0xCD853F,     // Light bronze
    COPPER: 0xB87333,           // Copper
    GOLD: 0xFFD700,             // Golden highlights
    TIME_ENERGY: 0xFFA500,      // Orange time energy
    CLOCKWORK: 0x696969         // Gray metal
};

/**
 * Create a temporal rift card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The temporal rift card 3D model
 */
export function createTemporalRiftCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'TemporalRiftCard';

    // Materials
    const bronzeDarkMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.BRONZE_DARK,
        emissive: TEMPORAL_COLORS.BRONZE_DARK,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.9
    });

    const bronzeLightMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.BRONZE_LIGHT,
        emissive: TEMPORAL_COLORS.BRONZE_LIGHT,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const copperMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.COPPER,
        emissive: TEMPORAL_COLORS.COPPER,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.85
    });

    const goldMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.GOLD,
        emissive: TEMPORAL_COLORS.GOLD,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.9
    });

    const timeEnergyMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.TIME_ENERGY,
        emissive: TEMPORAL_COLORS.TIME_ENERGY,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const clockworkMaterial = new THREE.MeshLambertMaterial({
        color: TEMPORAL_COLORS.CLOCKWORK,
        emissive: TEMPORAL_COLORS.CLOCKWORK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create main clockwork gear (center)
    const mainGearVoxels = [
        // Outer ring
        { x: 0, y: 0.2, z: 0, material: bronzeDarkMaterial },
        { x: 0.15, y: 0.15, z: 0, material: bronzeDarkMaterial },
        { x: 0.2, y: 0, z: 0, material: bronzeDarkMaterial },
        { x: 0.15, y: -0.15, z: 0, material: bronzeDarkMaterial },
        { x: 0, y: -0.2, z: 0, material: bronzeDarkMaterial },
        { x: -0.15, y: -0.15, z: 0, material: bronzeDarkMaterial },
        { x: -0.2, y: 0, z: 0, material: bronzeDarkMaterial },
        { x: -0.15, y: 0.15, z: 0, material: bronzeDarkMaterial },
        
        // Inner ring
        { x: 0, y: 0.1, z: 0, material: copperMaterial },
        { x: 0.1, y: 0, z: 0, material: copperMaterial },
        { x: 0, y: -0.1, z: 0, material: copperMaterial },
        { x: -0.1, y: 0, z: 0, material: copperMaterial },
        
        // Center hub
        { x: 0, y: 0, z: 0, material: goldMaterial },
        
        // Gear teeth
        { x: 0, y: 0.25, z: 0, material: bronzeLightMaterial },
        { x: 0.18, y: 0.18, z: 0, material: bronzeLightMaterial },
        { x: 0.25, y: 0, z: 0, material: bronzeLightMaterial },
        { x: 0.18, y: -0.18, z: 0, material: bronzeLightMaterial },
        { x: 0, y: -0.25, z: 0, material: bronzeLightMaterial },
        { x: -0.18, y: -0.18, z: 0, material: bronzeLightMaterial },
        { x: -0.25, y: 0, z: 0, material: bronzeLightMaterial },
        { x: -0.18, y: 0.18, z: 0, material: bronzeLightMaterial }
    ];

    // Create smaller gear (top right)
    const smallGear1Voxels = [
        { x: 0.15, y: 0.25, z: 0, material: clockworkMaterial },
        { x: 0.2, y: 0.2, z: 0, material: clockworkMaterial },
        { x: 0.25, y: 0.15, z: 0, material: clockworkMaterial },
        { x: 0.2, y: 0.1, z: 0, material: clockworkMaterial },
        { x: 0.15, y: 0.15, z: 0, material: copperMaterial },
        // Teeth
        { x: 0.15, y: 0.3, z: 0, material: bronzeLightMaterial },
        { x: 0.3, y: 0.15, z: 0, material: bronzeLightMaterial },
        { x: 0.15, y: 0.05, z: 0, material: bronzeLightMaterial },
        { x: 0.05, y: 0.15, z: 0, material: bronzeLightMaterial }
    ];

    // Create smaller gear (bottom left)
    const smallGear2Voxels = [
        { x: -0.15, y: -0.25, z: 0, material: clockworkMaterial },
        { x: -0.2, y: -0.2, z: 0, material: clockworkMaterial },
        { x: -0.25, y: -0.15, z: 0, material: clockworkMaterial },
        { x: -0.2, y: -0.1, z: 0, material: clockworkMaterial },
        { x: -0.15, y: -0.15, z: 0, material: copperMaterial },
        // Teeth
        { x: -0.15, y: -0.3, z: 0, material: bronzeLightMaterial },
        { x: -0.3, y: -0.15, z: 0, material: bronzeLightMaterial },
        { x: -0.15, y: -0.05, z: 0, material: bronzeLightMaterial },
        { x: -0.05, y: -0.15, z: 0, material: bronzeLightMaterial }
    ];

    // Create time energy swirls
    const timeEnergyVoxels = [
        // Spiral pattern around main gear
        { x: 0.3, y: 0.1, z: 0, material: timeEnergyMaterial },
        { x: 0.25, y: 0.25, z: 0, material: timeEnergyMaterial },
        { x: 0.1, y: 0.3, z: 0, material: timeEnergyMaterial },
        { x: -0.1, y: 0.25, z: 0, material: timeEnergyMaterial },
        { x: -0.25, y: 0.1, z: 0, material: timeEnergyMaterial },
        { x: -0.3, y: -0.1, z: 0, material: timeEnergyMaterial },
        { x: -0.25, y: -0.25, z: 0, material: timeEnergyMaterial },
        { x: -0.1, y: -0.3, z: 0, material: timeEnergyMaterial },
        { x: 0.1, y: -0.25, z: 0, material: timeEnergyMaterial },
        { x: 0.25, y: -0.1, z: 0, material: timeEnergyMaterial },
        
        // Floating energy particles
        { x: 0.35, y: 0.05, z: 0, material: goldMaterial },
        { x: -0.35, y: -0.05, z: 0, material: goldMaterial },
        { x: 0.05, y: 0.35, z: 0, material: goldMaterial },
        { x: -0.05, y: -0.35, z: 0, material: goldMaterial }
    ];

    // Create all voxels and organize into groups
    const mainGearGroup = new THREE.Group();
    const smallGearsGroup = new THREE.Group();
    const timeEnergyGroup = new THREE.Group();

    mainGearGroup.name = 'mainGear';
    smallGearsGroup.name = 'smallGears';
    timeEnergyGroup.name = 'timeEnergy';

    // Add main gear voxels
    mainGearVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mainGearGroup.add(mesh);
    });

    // Add small gear voxels
    [...smallGear1Voxels, ...smallGear2Voxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            -0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        smallGearsGroup.add(mesh);
    });

    // Add time energy voxels
    timeEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8 
        };
        mesh.userData.energyPhase = index * 0.3; // Stagger animation
        timeEnergyGroup.add(mesh);
    });

    // Add all groups to card
    cardGroup.add(mainGearGroup);
    cardGroup.add(smallGearsGroup);
    cardGroup.add(timeEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        gearRotation: 0,
        energyPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update temporal rift card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateTemporalRiftCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.gearRotation += deltaTime * 1.5; // Gear rotation speed
    cardGroup.userData.energyPhase += deltaTime * 2; // Energy flow speed

    const time = cardGroup.userData.animationTime;
    const gearRotation = cardGroup.userData.gearRotation;
    const energyPhase = cardGroup.userData.energyPhase;

    // Animate main gear rotation
    const mainGearGroup = cardGroup.getObjectByName('mainGear');
    if (mainGearGroup) {
        mainGearGroup.rotation.z = gearRotation;
        
        // Pulsing bronze glow
        const gearPulse = 0.8 + Math.sin(energyPhase * 1.5) * 0.2;
        mainGearGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * gearPulse;
            }
        });
    }

    // Animate small gears (counter-rotation)
    const smallGearsGroup = cardGroup.getObjectByName('smallGears');
    if (smallGearsGroup) {
        smallGearsGroup.rotation.z = -gearRotation * 1.5; // Faster counter-rotation
        
        // Subtle copper glow
        const smallGearPulse = 0.9 + Math.sin(energyPhase * 2) * 0.1;
        smallGearsGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * smallGearPulse;
            }
        });
    }

    // Animate time energy swirls
    const timeEnergyGroup = cardGroup.getObjectByName('timeEnergy');
    if (timeEnergyGroup) {
        timeEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = energyPhase + mesh.userData.energyPhase;
                
                // Spiral motion
                const spiralRadius = 0.02;
                const spiralX = Math.cos(energyTime * 3) * spiralRadius;
                const spiralY = Math.sin(energyTime * 3) * spiralRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + spiralX;
                mesh.position.y = mesh.userData.originalPosition.y + spiralY;
                
                // Pulsing energy glow
                const energyPulse = 0.6 + Math.sin(energyTime * 4) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyPulse;
                }
                
                // Fade in/out effect
                const fadeCycle = 0.7 + Math.sin(energyTime * 2) * 0.3;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fadeCycle;
                }
            }
        });
    }

    // Gentle overall pulsing for temporal effect
    const temporalPulse = 1 + Math.sin(time * 0.8) * 0.05;
    cardGroup.scale.setScalar(0.8 * temporalPulse);
}

// Export the temporal rift card data for the loot system
export const TEMPORAL_RIFT_CARD_DATA = {
    name: 'Temporal Rift',
    description: 'Creates a time bubble that slows all enemies to 20% speed for 8 seconds while you move at normal speed, allowing tactical repositioning and escape.',
    category: 'card',
    rarity: 'epic',
    effect: 'time_manipulation',
    effectValue: 8, // Duration in seconds
    createFunction: createTemporalRiftCard,
    updateFunction: updateTemporalRiftCardAnimation,
    voxelModel: 'temporal_rift_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.2
    }
};

export default createTemporalRiftCard;