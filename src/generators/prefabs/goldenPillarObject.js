import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Voxel Data for Golden Pillar ---

// Golden pillar color palette (matching ominous treasure room golden theme)
const GOLDEN_COLORS = {
    base: 'DAA520',       // Dark goldenrod base (matches room floorTint)
    shaft: 'D4AF37',      // Golden shaft (matches room wallTint)
    capital: 'FFD700',    // Bright gold capital (matches room spotlight)
    accent: 'B8860B',     // Dark gold accents
    detail: 'CD853F',     // Peru gold for fine details
    metallic: 'FFA500',   // Orange-gold for metallic shine
    bright: 'FFFF00'      // Bright yellow-gold for highlights
};

// Helper function to generate a square layer (like ancient pillar)
function generateSquareLayer(y, radius, color) {
    const layer = [];
    const intRadius = Math.floor(radius);
    for (let x = -intRadius; x <= intRadius; x++) {
        for (let z = -intRadius; z <= intRadius; z++) {
            if (radius % 1 !== 0 && Math.abs(x) === intRadius && Math.abs(z) === intRadius) {
                continue; // Skip corners for fractional radius
            }
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper function to generate column layers (like ancient pillar)
function generateColumnLayer(y, color) {
    return [
        { x: -1, y, z: -1, c: color },
        { x: 0, y, z: -1, c: color },
        { x: 1, y, z: -1, c: color },
        { x: -1, y, z: 0, c: color },
        { x: 0, y, z: 0, c: color },
        { x: 1, y, z: 0, c: color },
        { x: -1, y, z: 1, c: color },
        { x: 0, y, z: 1, c: color },
        { x: 1, y, z: 1, c: color }
    ];
}

// Detailed golden pillar shape using voxel coordinates (like ancient stone pillar)
const goldenPillarShape = [
    // === PILLAR BASE (Y: 0-2) ===
    // Bottom layer (Y=0) - 5x5 square base
    ...generateSquareLayer(0, 2, GOLDEN_COLORS.base),
    // Second layer (Y=1) - 4x4 square  
    ...generateSquareLayer(1, 1.5, GOLDEN_COLORS.accent),
    // Top base layer (Y=2) - 3x3 square
    ...generateSquareLayer(2, 1, GOLDEN_COLORS.base),

    // === MAIN COLUMN (Y: 3-12) ===
    // Column shaft with decorative bands
    ...generateColumnLayer(3, GOLDEN_COLORS.shaft),
    ...generateColumnLayer(4, GOLDEN_COLORS.accent),
    ...generateColumnLayer(5, GOLDEN_COLORS.shaft),
    ...generateColumnLayer(6, GOLDEN_COLORS.accent),
    ...generateColumnLayer(7, GOLDEN_COLORS.shaft),
    ...generateColumnLayer(8, GOLDEN_COLORS.accent),
    ...generateColumnLayer(9, GOLDEN_COLORS.shaft),
    ...generateColumnLayer(10, GOLDEN_COLORS.accent),
    ...generateColumnLayer(11, GOLDEN_COLORS.shaft),
    ...generateColumnLayer(12, GOLDEN_COLORS.accent),

    // === CAPITAL (TOP) (Y: 13-15) ===
    // Capital base (Y=13) - 3x3 square
    ...generateSquareLayer(13, 1, GOLDEN_COLORS.capital),
    // Capital middle (Y=14) - 4x4 square
    ...generateSquareLayer(14, 1.5, GOLDEN_COLORS.capital),
    // Capital crown (Y=15) - 3x3 square
    ...generateSquareLayer(15, 1, GOLDEN_COLORS.capital),

    // === DECORATIVE DETAILS ===
    // Base edge decorations with metallic gold
    { x: 2, y: 1, z: 0, c: GOLDEN_COLORS.metallic },
    { x: -2, y: 1, z: 0, c: GOLDEN_COLORS.metallic },
    { x: 0, y: 1, z: 2, c: GOLDEN_COLORS.metallic },
    { x: 0, y: 1, z: -2, c: GOLDEN_COLORS.metallic },

    // Column decorative elements with bright gold
    { x: 1, y: 5, z: 1, c: GOLDEN_COLORS.bright },
    { x: -1, y: 7, z: 1, c: GOLDEN_COLORS.bright },
    { x: 1, y: 9, z: -1, c: GOLDEN_COLORS.bright },
    { x: -1, y: 11, z: -1, c: GOLDEN_COLORS.bright },

    // Capital ornaments with capital gold
    { x: 0, y: 14, z: 1, c: GOLDEN_COLORS.capital },
    { x: 1, y: 14, z: 0, c: GOLDEN_COLORS.capital },
    { x: 0, y: 14, z: -1, c: GOLDEN_COLORS.capital },
    { x: -1, y: 14, z: 0, c: GOLDEN_COLORS.capital },

    // Additional golden details for richness
    { x: 1, y: 3, z: 0, c: GOLDEN_COLORS.metallic },
    { x: -1, y: 3, z: 0, c: GOLDEN_COLORS.metallic },
    { x: 0, y: 6, z: 1, c: GOLDEN_COLORS.bright },
    { x: 0, y: 6, z: -1, c: GOLDEN_COLORS.bright },
    { x: 1, y: 12, z: 0, c: GOLDEN_COLORS.capital },
    { x: -1, y: 12, z: 0, c: GOLDEN_COLORS.capital },
];

// --- Main Prefab Function ---
export function createGoldenPillarObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888); // Different multiplier for variation

    // Use same voxel size as ancient stone pillar for consistency
    const pillarVoxelSize = VOXEL_SIZE * 5.12;
    const baseGeometry = getOrCreateGeometry('golden_pillar_voxel', () =>
        new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};

    // Process each voxel in the pillar shape
    goldenPillarShape.forEach(voxel => {
        const { x, y, z, c } = voxel;

        // Add some randomness to gold color for variation
        let finalColor = c;
        if (rng() < 0.2) { // 20% chance for slight variation
            const goldColors = [GOLDEN_COLORS.base, GOLDEN_COLORS.shaft, GOLDEN_COLORS.accent];
            if (goldColors.includes(c)) {
                finalColor = goldColors[Math.floor(rng() * goldColors.length)];
            }
        }

        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }

        // Position the voxel
        tempMatrix.makeTranslation(
            x * pillarVoxelSize,
            y * pillarVoxelSize,
            z * pillarVoxelSize
        );

        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });

    // Create merged meshes for each material
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        const material = _getMaterialByHex_Cached(colorHex);
        const mesh = new THREE.Mesh(mergedGeometry, material);

        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'golden_pillar',
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : true,
        destructionEffect: options.destructionEffect || 'collapse',
        health: options.health || 1,
        isInteriorObject: true,
        voxelScale: pillarVoxelSize,
        originalVoxels: goldenPillarShape.map(v => {
            // Stable RNG per voxel for destruction consistency
            const voxelRng = mulberry32(seed * 23 + v.x * 5 + v.y * 7 + v.z * 11);
            return {...v, c: v.c}; // Keep original color for destruction
        })
    };

    console.log('Golden pillar created with options:', options);
    console.log('Golden pillar userData:', group.userData);

    return group;
}
