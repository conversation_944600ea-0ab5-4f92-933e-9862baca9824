import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create an improved fishing rod object (voxel-style, larger and more detailed)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Fishing rod object group
 */
export function createImprovedFishingRodObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 777);

    // Use much larger voxel size for impressive fishing rod
    const rodVoxelSize = VOXEL_SIZE * 3.5;
    const baseGeometry = getOrCreateGeometry('improved_fishing_rod_voxel', () =>
        new THREE.BoxGeometry(rodVoxelSize, rodVoxelSize, rodVoxelSize)
    );

    // Create voxel-style rod base (wooden platform)
    const baseMaterial = _getMaterialByHex_Cached('8B4513', {
        emissive: new THREE.Color(0x1a0f08),
        emissiveIntensity: 0.1
    });

    // Create larger wooden base platform (4x4 for stability)
    for (let x = -1.5; x <= 1.5; x += 1) {
        for (let z = -1.5; z <= 1.5; z += 1) {
            const baseVoxel = new THREE.Mesh(baseGeometry.clone(), baseMaterial);
            baseVoxel.position.set(
                x * rodVoxelSize,
                rodVoxelSize * 0.5,
                z * rodVoxelSize
            );
            baseVoxel.userData.isFloorObject = true;
            baseVoxel.userData.hasCollision = true;
            baseVoxel.castShadow = true;
            baseVoxel.receiveShadow = true;
            group.add(baseVoxel);
        }
    }

    // Fishing rod handle (brown wood) - much longer and more detailed
    const handleMaterial = _getMaterialByHex_Cached('654321', {
        emissive: new THREE.Color(0x0f0a05),
        emissiveIntensity: 0.1
    });

    // Create much longer segmented rod handle for impressive size
    for (let i = 0; i < 8; i++) {
        const handleSegment = new THREE.Mesh(baseGeometry.clone(), handleMaterial);
        handleSegment.position.set(0, (i + 1) * rodVoxelSize, 0);
        // Taper the rod slightly toward the tip
        const taper = 1.0 - (i * 0.05);
        handleSegment.scale.set(0.5 * taper, 1.0, 0.5 * taper);
        handleSegment.userData.isFloorObject = true;
        handleSegment.userData.hasCollision = true;
        handleSegment.castShadow = true;
        handleSegment.receiveShadow = true;
        group.add(handleSegment);
    }

    // Rod tip (darker wood) - positioned at end of longer rod
    const tipMaterial = _getMaterialByHex_Cached('4A2C17', {
        emissive: new THREE.Color(0x0a0603),
        emissiveIntensity: 0.1
    });

    const tip = new THREE.Mesh(baseGeometry.clone(), tipMaterial);
    tip.position.set(0, 9 * rodVoxelSize, 0);
    tip.scale.set(0.2, 0.5, 0.2);
    tip.userData.isFloorObject = true;
    tip.userData.hasCollision = true;
    tip.castShadow = true;
    tip.receiveShadow = true;
    group.add(tip);

    // Fishing reel (metallic) - larger and more detailed
    const reelMaterial = _getMaterialByHex_Cached('C0C0C0', {
        emissive: new THREE.Color(0x202020),
        emissiveIntensity: 0.2
    });

    const reel = new THREE.Mesh(baseGeometry.clone(), reelMaterial);
    reel.position.set(0.7 * rodVoxelSize, 3.5 * rodVoxelSize, 0);
    reel.scale.set(0.8, 0.6, 0.8);
    reel.userData.isFloorObject = true;
    reel.userData.hasCollision = true;
    reel.castShadow = true;
    reel.receiveShadow = true;
    group.add(reel);

    // Add reel handle
    const reelHandle = new THREE.Mesh(baseGeometry.clone(), reelMaterial);
    reelHandle.position.set(1.2 * rodVoxelSize, 3.5 * rodVoxelSize, 0);
    reelHandle.scale.set(0.3, 0.3, 0.3);
    reelHandle.userData.isFloorObject = true;
    reelHandle.userData.hasCollision = true;
    reelHandle.castShadow = true;
    reelHandle.receiveShadow = true;
    group.add(reelHandle);

    // Fishing line (continuous line extending toward pond) - much longer from taller rod
    const lineMaterial = new THREE.LineBasicMaterial({
        color: 0xCCCCCC,
        linewidth: 2,
        transparent: true,
        opacity: 1.0,
        alphaTest: 0.01, // Helps with transparency rendering
        depthWrite: false // Prevents depth buffer issues with transparency
    });

    // Create continuous fishing line using BufferGeometry
    const linePoints = [];
    const numLinePoints = 50; // More points for smoother curve

    // Create curved line path from rod tip to pond center
    const rodTipX = 0; // Rod tip X relative to rod base
    const rodTipY = 9 * rodVoxelSize; // Rod tip height
    const rodTipZ = 0; // Rod tip Z relative to rod base

    const targetX = 3.0; // Distance from rod (-3) to pond center (0) = +3
    const targetY = 0.1; // Pond water surface height
    const targetZ = 1.0; // Distance from rod (-1) to pond center (0) = +1

    for (let i = 0; i < numLinePoints; i++) {
        const t = i / (numLinePoints - 1); // 0 to 1

        // Create smooth curve with slight sag in the middle
        const sagAmount = 0.5; // How much the line sags
        const sagFactor = Math.sin(t * Math.PI) * sagAmount;

        const x = rodTipX + t * targetX;
        const y = rodTipY - t * (rodTipY - targetY) - sagFactor; // Add sag
        const z = rodTipZ + t * targetZ;

        linePoints.push(new THREE.Vector3(x, y, z));
    }

    const lineGeometry = new THREE.BufferGeometry().setFromPoints(linePoints);
    const fishingLine = new THREE.Line(lineGeometry, lineMaterial);
    fishingLine.userData.isFloorObject = true;
    fishingLine.userData.isFishingLine = true;
    fishingLine.userData.originalPoints = linePoints.map(p => p.clone()); // Store for animation
    fishingLine.castShadow = false;
    fishingLine.receiveShadow = false;
    group.add(fishingLine);

    // Fishing hook at end of line (floating on water surface, closer to rod)
    const hookMaterial = _getMaterialByHex_Cached('A0A0A0', {
        emissive: new THREE.Color(0x101010),
        emissiveIntensity: 0.1
    });

    const hook = new THREE.Mesh(baseGeometry.clone(), hookMaterial);
    hook.position.set(3.0, -0.05, 1.0); // At slightly recessed pond water level relative to rod position
    hook.scale.set(0.15, 0.3, 0.15);
    hook.userData.isFloorObject = true;
    hook.userData.isHook = true;
    hook.userData.hasCollision = false;
    hook.userData.originalPosition = hook.position.clone(); // Store for animation
    hook.castShadow = true;
    hook.receiveShadow = true;
    group.add(hook);

    // Store fishing line and hook for animation - CRITICAL for reel-in animation
    group.userData.fishingLine = fishingLine;
    group.userData.hook = hook;

    console.log(`[ImprovedFishingRodObject] Stored fishing line with ${numLinePoints} points for animation`);
    console.log(`[ImprovedFishingRodObject] Hook stored:`, !!hook);

    // Set up group properties with chest-like interaction
    group.userData = {
        ...(options.userData || {}),
        objectType: 'improved_fishing_rod',
        isInteractable: true,
        isEventObject: true,
        rodId: options.userData?.rodId || 'mysterious_fishing_rod',
        interactionRange: 3.0,
        isChest: true, // Helps with interaction detection
        chestType: 'event_object',
        isFloorObject: true,
        hasCollision: true,
        isDestructible: options.isDestructible !== undefined ? options.isDestructible : false, // Default to indestructible
        destructionEffect: options.destructionEffect || 'none',
        health: options.health || 999, // High health as backup
        isInteriorObject: true,
        voxelScale: rodVoxelSize,
        // Animation properties
        isReeled: false,
        canAnimate: true
    };

    group.name = 'improved_fishing_rod';

    console.log('[ImprovedFishingRodObject] ✅ Created detailed voxel-style fishing rod');
    return group;
}

/**
 * Animate fishing rod reel-in effect
 * @param {THREE.Group} rodGroup - The fishing rod group
 * @param {number} duration - Animation duration in milliseconds
 * @returns {Promise} Promise that resolves when animation completes
 */
export function animateFishingRodReelIn(rodGroup, duration = 2000) {
    return new Promise((resolve) => {
        if (!rodGroup.userData.canAnimate || rodGroup.userData.isReeled) {
            resolve();
            return;
        }

        const fishingLine = rodGroup.userData.fishingLine;
        const hook = rodGroup.userData.hook;

        console.log(`[FishingRodReelIn] Fishing line found:`, !!fishingLine);
        console.log(`[FishingRodReelIn] Hook found:`, !!hook);

        if (!fishingLine || !hook) {
            console.error(`[FishingRodReelIn] ❌ Missing animation components - fishingLine: ${!!fishingLine}, hook: ${!!hook}`);
            resolve();
            return;
        }

        const startTime = Date.now();
        const originalLinePoints = fishingLine.userData.originalPoints || [];
        const originalHookPosition = hook.userData.originalPosition || hook.position.clone();

        console.log(`[FishingRodReelIn] Original line points:`, originalLinePoints.length);

        // Rod tip position (where line should end up) - relative to rod base
        const rodTipPosition = new THREE.Vector3(0, 9 * rodGroup.userData.voxelScale, 0);

        // IMPORTANT: The hook should start from pond center (0,0,0) in world coordinates
        // But since the rod is positioned at (-3, 0, -1), the hook's local position relative to rod is (3, 0.1, 1)
        // For animation, we want to pull from pond center to rod tip
        const pondCenterRelativeToRod = new THREE.Vector3(3.0, 0.1, 1.0); // Pond center relative to rod position

        console.log('[FishingRodReelIn] Starting reel-in animation');
        console.log('[FishingRodReelIn] Hook original position (relative to rod):', originalHookPosition);
        console.log('[FishingRodReelIn] Pond center relative to rod:', pondCenterRelativeToRod);
        console.log('[FishingRodReelIn] Rod tip target position:', rodTipPosition);

        // Reset hook to pond center position for animation start
        hook.position.copy(pondCenterRelativeToRod);

        // Find any fish enemies to animate with the hook
        let fishEnemy = null;

        // Search in multiple scopes to find the fish enemy
        console.log('[FishingRodReelIn] Searching for fish enemy...');
        console.log('[FishingRodReelIn] Rod group parent:', !!rodGroup.parent);
        console.log('[FishingRodReelIn] Rod group parent.parent:', !!rodGroup.parent?.parent);

        // First try: Search in the scene directly (most reliable)
        if (rodGroup.parent && rodGroup.parent.parent && rodGroup.parent.parent.parent) {
            console.log('[FishingRodReelIn] Searching in scene (parent.parent.parent)...');
            rodGroup.parent.parent.parent.traverse(child => {
                if (child.userData && (child.userData.enemyType === 'fish' || child.userData.type === 'fish')) {
                    fishEnemy = child;
                    console.log('[FishingRodReelIn] Found fish enemy in scene:', child.name, child.userData);
                }
            });
        }

        // Second try: Search in room group (parent.parent)
        if (!fishEnemy && rodGroup.parent && rodGroup.parent.parent) {
            console.log('[FishingRodReelIn] Searching in room group (parent.parent)...');
            rodGroup.parent.parent.traverse(child => {
                if (child.userData && (child.userData.enemyType === 'fish' || child.userData.type === 'fish')) {
                    fishEnemy = child;
                    console.log('[FishingRodReelIn] Found fish enemy in room group:', child.name, child.userData);
                }
            });
        }

        // Third try: Search globally via window.dungeonHandler if available
        if (!fishEnemy && window.dungeonHandler && window.dungeonHandler.activeEnemies) {
            console.log('[FishingRodReelIn] Searching in global activeEnemies...');
            for (const enemy of window.dungeonHandler.activeEnemies) {
                if (enemy.userData && (enemy.userData.enemyType === 'fish' || enemy.userData.type === 'fish')) {
                    fishEnemy = enemy;
                    console.log('[FishingRodReelIn] Found fish enemy in activeEnemies:', enemy.name, enemy.userData);
                    break;
                }
            }
        }

        if (!fishEnemy) {
            console.warn('[FishingRodReelIn] No fish enemy found for animation');
            // Debug: List all available enemies
            if (window.dungeonHandler && window.dungeonHandler.activeEnemies) {
                console.log('[FishingRodReelIn] Available enemies:');
                window.dungeonHandler.activeEnemies.forEach((enemy, index) => {
                    console.log(`  ${index}: ${enemy.name} - type: ${enemy.userData?.type}, enemyType: ${enemy.userData?.enemyType}`);
                });
            }
        } else {
            console.log('[FishingRodReelIn] ✅ Fish enemy found for animation:', fishEnemy.name);
        }

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out cubic

            // Animate fishing line reeling in from pond center to rod tip
            if (fishingLine && originalLinePoints.length > 0) {
                const newPoints = [];

                for (let i = 0; i < originalLinePoints.length; i++) {
                    const t = i / (originalLinePoints.length - 1); // 0 to 1 along the line
                    const originalPoint = originalLinePoints[i];

                    // Create smooth animation path from original position to rod tip
                    const startPos = originalPoint.clone();
                    const targetPos = rodTipPosition.clone();

                    // Add slight delay for points further from rod (more realistic)
                    const pointDelay = t * 0.4; // 40% delay spread across points
                    const adjustedProgress = Math.max(0, Math.min(1, easeProgress - pointDelay));

                    // Interpolate between start and target position
                    const newPoint = new THREE.Vector3();
                    newPoint.lerpVectors(startPos, targetPos, adjustedProgress);
                    newPoints.push(newPoint);
                }

                // Update the line geometry with new points
                fishingLine.geometry.setFromPoints(newPoints);
                fishingLine.geometry.attributes.position.needsUpdate = true;

                // Fade out fishing line as it reels in
                if (fishingLine.material) {
                    const newOpacity = Math.max(0, 1 - easeProgress);
                    fishingLine.material.opacity = newOpacity;
                    fishingLine.material.transparent = true;
                    fishingLine.material.needsUpdate = true; // Force material update

                    // Also set visibility based on opacity
                    fishingLine.visible = newOpacity > 0.01;
                }

                // Debug logging for first few frames
                if (elapsed < 200) {
                    console.log(`[FishingRodReelIn] Line progress: ${easeProgress.toFixed(2)}, opacity: ${fishingLine.material?.opacity?.toFixed(2)}, visible: ${fishingLine.visible}`);
                }
            }

            // Animate hook reeling in from pond center (0,0,0) to rod tip
            hook.position.lerpVectors(pondCenterRelativeToRod, rodTipPosition, easeProgress);

            // Animate fish enemy to follow the hook if it exists
            if (fishEnemy) {
                // Convert hook position to world coordinates for fish
                const hookWorldPos = rodGroup.localToWorld(hook.position.clone());

                // Position fish at hook location
                fishEnemy.position.copy(hookWorldPos);

                // Add some fish movement/rotation for realism
                fishEnemy.rotation.z = Math.sin(elapsed * 0.01) * 0.2; // Wiggle effect
                fishEnemy.rotation.y = Math.sin(elapsed * 0.008) * 0.1; // Additional wiggle

                // Debug logging for fish animation
                if (elapsed < 200) {
                    console.log(`[FishingRodReelIn] Fish position updated:`, fishEnemy.position.clone());
                    console.log(`[FishingRodReelIn] Hook world position:`, hookWorldPos.clone());
                }
            }

            // Debug logging for first few frames
            if (elapsed < 100) {
                console.log(`[FishingRodReelIn] Progress: ${progress.toFixed(2)}, Hook pos:`, hook.position);
                console.log(`[FishingRodReelIn] Lerping from pond center:`, pondCenterRelativeToRod, 'to rod tip:', rodTipPosition);
                if (fishEnemy) {
                    console.log(`[FishingRodReelIn] Fish position:`, fishEnemy.position);
                }
            }
            
            if (hook.material) {
                hook.material.opacity = 1 - easeProgress;
                hook.material.transparent = true;
            }

            if (progress < 1.0) {
                requestAnimationFrame(animate);
            } else {
                // Mark as reeled and hide line/hook completely
                rodGroup.userData.isReeled = true;

                // Hide fishing line completely
                if (fishingLine) {
                    fishingLine.visible = false;
                    if (fishingLine.material) {
                        fishingLine.material.opacity = 0;
                        fishingLine.material.transparent = true;
                        fishingLine.material.needsUpdate = true;
                    }

                    // AGGRESSIVE FIX: Remove line from parent to ensure it disappears
                    if (fishingLine.parent) {
                        fishingLine.parent.remove(fishingLine);
                        console.log('[FishingRodReelIn] ✅ Fishing line removed from scene');
                    }

                    // ADDITIONAL FIX: Dispose of geometry and material to free memory
                    if (fishingLine.geometry) {
                        fishingLine.geometry.dispose();
                    }
                    if (fishingLine.material) {
                        fishingLine.material.dispose();
                    }

                    console.log('[FishingRodReelIn] ✅ Fishing line completely removed and disposed');
                }

                // NUCLEAR OPTION: Search and remove ALL fishing lines in the rod group
                console.log('[FishingRodReelIn] 🔍 Searching for any remaining fishing lines...');
                const linesToRemove = [];
                rodGroup.traverse(child => {
                    if (child.userData?.isFishingLine ||
                        child.type === 'Line' ||
                        child.geometry?.type === 'BufferGeometry') {
                        linesToRemove.push(child);
                        console.log('[FishingRodReelIn] 🎯 Found line to remove:', child.type, child.userData);
                    }
                });

                linesToRemove.forEach(line => {
                    line.visible = false;
                    if (line.material) {
                        line.material.opacity = 0;
                        line.material.transparent = true;
                        line.material.needsUpdate = true;
                        line.material.dispose();
                    }
                    if (line.geometry) {
                        line.geometry.dispose();
                    }
                    if (line.parent) {
                        line.parent.remove(line);
                    }
                    console.log('[FishingRodReelIn] 💥 REMOVED line:', line.type);
                });

                console.log(`[FishingRodReelIn] 🧹 Removed ${linesToRemove.length} fishing lines total`);

                // Hide hook completely
                hook.visible = false;
                if (hook.material) {
                    hook.material.opacity = 0;
                    hook.material.transparent = true;
                    hook.material.needsUpdate = true;
                }

                // ADDITIONAL FIX: Remove hook from parent to ensure it disappears
                if (hook.parent) {
                    hook.parent.remove(hook);
                    console.log('[FishingRodReelIn] ✅ Hook removed from scene');
                }

                // Dispose of hook geometry and material
                if (hook.geometry) {
                    hook.geometry.dispose();
                }
                if (hook.material) {
                    hook.material.dispose();
                }

                console.log('[FishingRodReelIn] ✅ Hook completely removed and disposed');

                console.log('[ImprovedFishingRodObject] ✅ Reel-in animation complete - line and hook hidden');
                resolve();
            }
        };

        animate();
    });
}
