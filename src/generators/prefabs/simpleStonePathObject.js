import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a simple, visible stone path using the same patterns as working prefabs
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Stone path object group
 */
export function createSimpleStonePathObject(options = {}) {
    const group = new THREE.Group();
    
    // Use proper materials from shared.js like other working prefabs
    const stoneMaterial1 = _getMaterialByHex_Cached('8B7D8B', {
        roughness: 0.7,
        metalness: 0.1
    });
    const stoneMaterial2 = _getMaterialByHex_Cached('7A6B7A', {
        roughness: 0.8,
        metalness: 0.05
    });
    const stoneMaterial3 = _getMaterialByHex_Cached('9B8B93', {
        roughness: 0.6,
        metalness: 0.15
    });
    
    const materials = [stoneMaterial1, stoneMaterial2, stoneMaterial3];
    
    // Create larger, simple stone blocks
    const blockVoxelSize = VOXEL_SIZE * 4;
    const blockGeometry = getOrCreateGeometry('stone_path_block', () =>
        new THREE.BoxGeometry(blockVoxelSize, blockVoxelSize * 0.5, blockVoxelSize)
    );
    
    // Create a simple 5x3 grid of stone blocks
    for (let x = -2; x <= 2; x++) {
        for (let z = -1; z <= 1; z++) {
            const block = new THREE.Mesh(
                blockGeometry, 
                materials[Math.floor(Math.random() * materials.length)]
            );
            
            // Position blocks with spacing
            block.position.set(
                x * blockVoxelSize * 1.2,
                blockVoxelSize * 0.25, // Half height above ground
                z * blockVoxelSize * 1.2
            );
            
            // Add slight rotation for weathered look
            block.rotation.y = (Math.random() - 0.5) * 0.2;
            
            // Enable shadows
            block.castShadow = true;
            block.receiveShadow = true;
            
            group.add(block);
        }
    }
    
    // Set group properties like other prefabs
    group.name = "simpleStonePathObject";
    group.userData.isInteriorObject = true;
    group.userData.objectType = 'simple_stone_path';
    
    return group;
}