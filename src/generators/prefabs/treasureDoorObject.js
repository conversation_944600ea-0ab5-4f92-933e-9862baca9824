import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Treasure Door - Gleaming golden door leading to treasure
 * Features rich materials, gem motifs, and golden welcoming glow
 */
export function createTreasureDoorObject(options = {}) {
    const group = new THREE.Group();
    
    // Rich golden materials
    const goldenMaterial = new THREE.MeshStandardMaterial({
        color: 0xFFD700, // Gold
        roughness: 0.3,
        metalness: 0.8
    });
    
    const gemGlowMaterial = new THREE.MeshStandardMaterial({
        color: 0x00CED1, // Dark turquoise gem glow
        emissive: 0x008B8B,
        emissiveIntensity: 0.4,
        roughness: 0.1,
        metalness: 0.9
    });
    
    // Create door frame
    const frameWidth = VOXEL_SIZE * 0.5;
    const doorWidth = VOXEL_SIZE * 3;
    const doorHeight = VOXEL_SIZE * 5;
    
    // Left frame pillar
    const leftFrameGeometry = new THREE.BoxGeometry(frameWidth, doorHeight, frameWidth);
    const leftFrame = new THREE.Mesh(leftFrameGeometry, goldenMaterial);
    leftFrame.position.set(-doorWidth / 2 - frameWidth / 2, doorHeight / 2, 0);
    leftFrame.castShadow = true;
    leftFrame.receiveShadow = true;
    group.add(leftFrame);
    
    // Right frame pillar
    const rightFrame = new THREE.Mesh(leftFrameGeometry, goldenMaterial);
    rightFrame.position.set(doorWidth / 2 + frameWidth / 2, doorHeight / 2, 0);
    rightFrame.castShadow = true;
    rightFrame.receiveShadow = true;
    group.add(rightFrame);
    
    // Top frame lintel
    const lintelGeometry = new THREE.BoxGeometry(doorWidth + frameWidth * 2, frameWidth, frameWidth);
    const lintel = new THREE.Mesh(lintelGeometry, goldenMaterial);
    lintel.position.set(0, doorHeight + frameWidth / 2, 0);
    lintel.castShadow = true;
    lintel.receiveShadow = true;
    group.add(lintel);
    
    // Door surface (golden with gem patterns)
    const doorGeometry = new THREE.BoxGeometry(doorWidth, doorHeight, VOXEL_SIZE * 0.1);
    const doorSurface = new THREE.Mesh(doorGeometry, new THREE.MeshStandardMaterial({
        color: 0xDAA520, // Darker gold for door surface
        roughness: 0.4,
        metalness: 0.7,
        emissive: 0x4A3B00,
        emissiveIntensity: 0.2
    }));
    doorSurface.position.set(0, doorHeight / 2, -VOXEL_SIZE * 0.05);
    group.add(doorSurface);
    
    // Treasure symbols - gems embedded in frame
    const gemGeometry = new THREE.OctahedronGeometry(VOXEL_SIZE * 0.2);
    
    // Left gem
    const leftGem = new THREE.Mesh(gemGeometry, gemGlowMaterial);
    leftGem.position.set(-doorWidth / 2 - frameWidth / 2, doorHeight * 0.8, frameWidth / 2);
    group.add(leftGem);
    
    // Right gem
    const rightGem = new THREE.Mesh(gemGeometry, gemGlowMaterial);
    rightGem.position.set(doorWidth / 2 + frameWidth / 2, doorHeight * 0.8, frameWidth / 2);
    group.add(rightGem);
    
    // Center treasure symbol on lintel (diamond shape)
    const diamondGeometry = new THREE.OctahedronGeometry(VOXEL_SIZE * 0.3);
    const centerDiamond = new THREE.Mesh(diamondGeometry, gemGlowMaterial);
    centerDiamond.position.set(0, doorHeight + frameWidth / 2, frameWidth / 2);
    centerDiamond.rotation.x = Math.PI / 4;
    group.add(centerDiamond);
    
    // Golden coin decorations on door surface
    const coinGeometry = new THREE.CylinderGeometry(VOXEL_SIZE * 0.15, VOXEL_SIZE * 0.15, VOXEL_SIZE * 0.05, 8);
    
    for (let i = 0; i < 9; i++) {
        const coin = new THREE.Mesh(coinGeometry, goldenMaterial);
        const row = Math.floor(i / 3);
        const col = i % 3;
        coin.position.set(
            (col - 1) * VOXEL_SIZE * 0.8,
            doorHeight * 0.25 + row * VOXEL_SIZE * 0.6,
            VOXEL_SIZE * 0.02
        );
        coin.rotation.x = Math.PI / 2;
        group.add(coin);
    }
    
    // Welcoming golden light
    const treasureLight = new THREE.PointLight(0xFFD700, 2.5, VOXEL_SIZE * 15);
    treasureLight.position.set(0, doorHeight + frameWidth, frameWidth);
    treasureLight.castShadow = true;
    group.add(treasureLight);
    
    // Add decorative flourishes around the frame
    const flourishGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 0.08, 6, 6);
    
    for (let i = 0; i < 8; i++) {
        const flourish = new THREE.Mesh(flourishGeometry, goldenMaterial);
        const angle = (i / 8) * Math.PI * 2;
        const radius = doorWidth / 2 + frameWidth * 1.2;
        flourish.position.set(
            Math.cos(angle) * radius,
            doorHeight * 0.6 + Math.sin(angle * 3) * VOXEL_SIZE * 0.3,
            frameWidth / 2
        );
        group.add(flourish);
    }
    
    // Add mystical sparkle effects
    const sparkleGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 0.05, 4, 4);
    const sparkleMaterial = new THREE.MeshStandardMaterial({
        color: 0xFFFFFF,
        emissive: 0xFFFFFF,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });
    
    for (let i = 0; i < 6; i++) {
        const sparkle = new THREE.Mesh(sparkleGeometry, sparkleMaterial);
        sparkle.position.set(
            (Math.random() - 0.5) * doorWidth * 1.5,
            doorHeight * 0.3 + Math.random() * doorHeight * 0.4,
            frameWidth * 0.8
        );
        sparkle.userData.sparkleIndex = i;
        sparkle.userData.basePosition = sparkle.position.clone();
        group.add(sparkle);
    }
    
    // Set up group properties
    group.name = 'treasure_door';
    group.userData = {
        objectType: 'treasure_door',
        isInteractable: true,
        doorType: 'treasure',
        leadsTo: 'treasure',
        isRewarding: true,
        welcomingLevel: 'high',
        ...options.userData
    };
    
    // Store references for animation
    group.userData.treasureLight = treasureLight;
    group.userData.gems = [leftGem, rightGem, centerDiamond];
    group.userData.doorSurface = doorSurface;
    group.userData.sparkles = group.children.filter(child => child.userData.sparkleIndex !== undefined);
    
    return group;
}