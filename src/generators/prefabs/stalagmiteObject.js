import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached,
    // Use stone materials for cave theme
    stoneGrain1, // 958272
    stoneGrain2, // 9D8A7A
    stoneGrain3, // A59080
    stoneGrain4, // AA9586
    caveFloorCrackMaterial // 4a3a2a (For darker details)
} from './shared.js';

// --- Voxel Data for Stalagmite ---

// Store stone colors in an array for easier selection
const STONE_COLORS = ['958272', '9d8a7a', 'a59080', 'aa9586'];
const DARK_STONE = '4a3a2a'; // Dark color for shadows and details
const LIGHT_STONE = 'aa9586'; // Lighter stone for highlights

// Define stalagmite shape using voxel coordinates (rising from floor)
const stalagmiteShape = [
    // Base (widest part on floor)
    { x: -1, y: 0, z: -1, c: STONE_COLORS[1] },
    { x: 0, y: 0, z: -1, c: STONE_COLORS[0] },
    { x: 1, y: 0, z: -1, c: STONE_COLORS[1] },
    { x: -1, y: 0, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 0, z: 0, c: STONE_COLORS[0] },
    { x: 1, y: 0, z: 0, c: STONE_COLORS[0] },
    { x: -1, y: 0, z: 1, c: STONE_COLORS[1] },
    { x: 0, y: 0, z: 1, c: STONE_COLORS[0] },
    { x: 1, y: 0, z: 1, c: STONE_COLORS[1] },

    // Lower body (slightly narrower)
    { x: -1, y: 1, z: -1, c: STONE_COLORS[2] },
    { x: 0, y: 1, z: -1, c: STONE_COLORS[0] },
    { x: 1, y: 1, z: -1, c: STONE_COLORS[2] },
    { x: -1, y: 1, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 1, z: 0, c: STONE_COLORS[0] },
    { x: 1, y: 1, z: 0, c: STONE_COLORS[0] },
    { x: -1, y: 1, z: 1, c: STONE_COLORS[2] },
    { x: 0, y: 1, z: 1, c: STONE_COLORS[0] },
    { x: 1, y: 1, z: 1, c: STONE_COLORS[2] },

    // Mid-lower body
    { x: -1, y: 2, z: 0, c: STONE_COLORS[1] },
    { x: 0, y: 2, z: -1, c: STONE_COLORS[0] },
    { x: 0, y: 2, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 2, z: 1, c: STONE_COLORS[0] },
    { x: 1, y: 2, z: 0, c: STONE_COLORS[1] },

    // Mid body (narrower)
    { x: 0, y: 3, z: -1, c: STONE_COLORS[2] },
    { x: 0, y: 3, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 3, z: 1, c: STONE_COLORS[2] },

    // Upper mid body
    { x: 0, y: 4, z: 0, c: STONE_COLORS[0] },
    { x: 0, y: 4, z: -1, c: DARK_STONE },
    { x: 0, y: 4, z: 1, c: DARK_STONE },

    // Upper body (very narrow)
    { x: 0, y: 5, z: 0, c: STONE_COLORS[1] },

    // Near tip
    { x: 0, y: 6, z: 0, c: STONE_COLORS[2] },

    // Tip point
    { x: 0, y: 7, z: 0, c: DARK_STONE }
];

/**
 * Create a stalagmite rising from the floor
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Stalagmite object group
 */
export function createStalagmiteObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 888);

    // Use consistent voxel size
    const voxelSize = VOXEL_SIZE;
    const baseGeometry = getOrCreateGeometry('stalagmite_voxel', () =>
        new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize)
    );

    // Group geometries by material for efficient rendering
    const geometriesByMaterial = {};
    const tempMatrix = new THREE.Matrix4();

    const addGeometry = (geometry, colorHex, matrix) => {
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        geometriesByMaterial[colorHex].push(geometry.clone().applyMatrix4(matrix));
    };

    // Build the stalagmite from voxel data
    stalagmiteShape.forEach(voxel => {
        const finalX = voxel.x * voxelSize;
        const finalY = voxel.y * voxelSize;
        const finalZ = voxel.z * voxelSize;

        tempMatrix.makeTranslation(finalX, finalY, finalZ);
        addGeometry(baseGeometry, voxel.c, tempMatrix);
    });

    // Merge geometries by material
    for (const colorHex in geometriesByMaterial) {
        const mergedGeo = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeo) {
            const material = _getMaterialByHex_Cached(colorHex, {
                emissive: new THREE.Color(0x111111),
                emissiveIntensity: 0.05
            });
            const mesh = new THREE.Mesh(mergedGeo, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            group.add(mesh);
        }
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'stalagmite',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: true,
        isInteriorObject: true,
        voxelScale: voxelSize
    };

    group.name = 'stalagmite';

    console.log('[StalagmiteObject] ✅ Created voxel-style stalagmite');
    return group;
}
