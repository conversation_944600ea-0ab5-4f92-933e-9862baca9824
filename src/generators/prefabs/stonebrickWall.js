import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    stonebrickMaterialPrimary, stonebrickMaterialSecondary, stonebrickCenterMaterial, stonebrickMortarMaterial,
    mossMaterialPrimary, mossMaterialSecondary,
    mulberry32, _getMaterialByHex_Cached, getOrCreateGeometry
} from './shared.js';

/**
 * Creates a Stonebrick voxel wall segment with a pixel art inspired pattern.
 * Uses Geometry Merging for performance.
 * Does NOT include vines or torches anymore.
 * Returns an object: { group: THREE.Group }
 */
export function createStonebrickWallSegment(
    width, height, depth, roomData,
) {
    // --- INPUT VALIDATION ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createStonebrickWallSegment received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() }; // NEW Return (no torch positions)
    }
    // --- END INPUT VALIDATION ---

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {};

    // --- Use larger scale for walls to reduce voxel count ---
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;

    // --- Calculate Dimensions ---
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);
    // --- End Calculation ---

    // --- Safeguard Dimensions (Ensure >= 1) ---
    const safeNumX = Math.max(1, numX);
    const safeNumY = Math.max(1, numY);
    const safeNumZ = Math.max(1, numZ);
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);
    if (numX <= 0 || numY <= 0 || numZ <= 0 || numX_env <= 0 || numY_env <= 0) {
        console.warn(`Clamped dimension in createStonebrickWallSegment: w=${width}, h=${height}. Original base(${numX},${numY}), env(${numX_env},${numY_env}). Using safe(${safeNumX},${safeNumY}), env(${safeNumXEnv},${safeNumYEnv})`);
    }
    // --- End Safeguard ---

    const filledVoxels_env = Array(safeNumYEnv).fill(null).map(() => Array(safeNumXEnv).fill(false));

    const offsetX = (safeNumX - 1) * VOXEL_SIZE / 2;
    const offsetY = (safeNumY - 1) * VOXEL_SIZE / 2;
    const offsetZ = (safeNumZ - 1) * VOXEL_SIZE / 2;
    const mossProbability = 0.15;

    // --- Use Seeded PRNG ---
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);
    // --- End Seeded PRNG ---

    // --- Block Templates ---
    const blockTemplates = [
        { w: 4, h: 3 },
        { w: 3, h: 3 },
        { w: 5, h: 2 },
        { w: 3, h: 4 },
        { w: 4, h: 3 },
    ];
    // -----------------------

    // Get cached wall voxel geometry
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;
    const largeWallGeo = getOrCreateGeometry(
        `wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // --- Wall Generation using Environment Blocks ---
    let voxelsAdded = 0;
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            if (!filledVoxels_env[ey][ex]) {
                let blockPlaced = false;
                const shuffledTemplates = [...blockTemplates].sort(() => random() - 0.5);

                for (const template of shuffledTemplates) {
                    if (ey + template.h <= safeNumYEnv && ex + template.w <= safeNumXEnv) {
                        let canPlace = true;
                        // Basic overlap check
                        for (let checkY = 0; checkY < template.h; checkY++) {
                            for (let checkX = 0; checkX < template.w; checkX++) {
                                if (filledVoxels_env[ey + checkY][ex + checkX]) {
                                    canPlace = false; break;
                                }
                            }
                            if (!canPlace) break;
                        }

                        if (canPlace) {
                            for (let blockEY = 0; blockEY < template.h; blockEY++) {
                                for (let blockEX = 0; blockEX < template.w; blockEX++) {
                                    const currentEY = ey + blockEY;
                                    const currentEX = ex + blockEX;
                                    filledVoxels_env[currentEY][currentEX] = true;

                                    // --- Determine material based on ENV coords ---
                                    let blockMaterial;
                                    const isOuterBottomEdge = (blockEY === template.h - 1);
                                    const isOuterRightEdge = (blockEX === template.w - 1);

                                    if (isOuterBottomEdge || isOuterRightEdge) {
                                        blockMaterial = stonebrickMortarMaterial;
                                    } else {
                                        const centerX = (template.w - 1) / 2;
                                        const centerY = (template.h - 1) / 2;
                                        const normDx = (blockEX - centerX) / Math.max(1, centerX);
                                        const normDy = (blockEY - centerY) / Math.max(1, centerY);
                                        const distSq = normDx * normDx + normDy * normDy;
                                        const clampedDistSq = Math.min(1.0, distSq);
                                        const edgeNoiseProbability = clampedDistSq * 0.8;
                                        const edgeMossProbability = clampedDistSq * mossProbability; // Use mossProbability

                                        const roll = random();
                                        if (roll < edgeMossProbability) {
                                            blockMaterial = random() < 0.5 ? mossMaterialPrimary : mossMaterialSecondary;
                                        } else if (roll < edgeNoiseProbability) {
                                            const brickTypeRoll = random();
                                            blockMaterial = brickTypeRoll < 0.7 ? stonebrickMaterialPrimary : stonebrickMaterialSecondary;
                                        } else {
                                            blockMaterial = stonebrickCenterMaterial;
                                        }
                                    }
                                    // --- End Material Logic ---

                                    // CRITICAL FIX: Keep extruded look but reduce gaps between voxels
                                    const baseX = currentEX * wallVoxelSize - offsetX;
                                    const baseY = currentEY * wallVoxelSize - offsetY;

                                    // Reduce variations to minimize gaps while keeping extruded appearance
                                    const depthVariation = (random() - 0.5) * 0.03; // Reduced depth variation
                                    const heightVariation = (random() - 0.5) * 0.02; // Reduced height variation

                                    // Keep subtle rotation for extruded look
                                    const rotationY = (random() - 0.5) * 0.02; // Reduced Y rotation
                                    const rotationZ = (random() - 0.5) * 0.015; // Reduced Z rotation

                                    const finalX = baseX + depthVariation;
                                    const finalY = baseY + heightVariation;

                                    // For depth, use a single layer with slight variation for extruded look
                                    for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                                        const posZ = z * VOXEL_SIZE - offsetZ + depthVariation * 0.3;

                                        // Create transformation matrix with rotation for extruded appearance
                                        tempMatrix.makeRotationFromEuler(new THREE.Euler(0, rotationY, rotationZ));
                                        tempMatrix.setPosition(finalX, finalY, posZ);

                                        addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;

                                        // Add gap-filling connector voxels between adjacent positions
                                        if (currentEX > 0) { // Not the first column
                                            const connectorGeo = getOrCreateGeometry(
                                                `wall_connector_${(wallVoxelSize * 0.3).toFixed(4)}`,
                                                () => new THREE.BoxGeometry(wallVoxelSize * 0.3, wallVoxelSize * 0.8, VOXEL_SIZE * 0.8)
                                            );
                                            const connectorX = finalX - wallVoxelSize * 0.35;
                                            tempMatrix.makeTranslation(connectorX, finalY, posZ);
                                            addGeometry(connectorGeo, blockMaterial, tempMatrix);
                                        }

                                        if (currentEY > 0) { // Not the first row
                                            const connectorGeo = getOrCreateGeometry(
                                                `wall_connector_v_${(wallVoxelSize * 0.3).toFixed(4)}`,
                                                () => new THREE.BoxGeometry(wallVoxelSize * 0.8, wallVoxelSize * 0.3, VOXEL_SIZE * 0.8)
                                            );
                                            const connectorY = finalY - wallVoxelSize * 0.35;
                                            tempMatrix.makeTranslation(finalX, connectorY, posZ);
                                            addGeometry(connectorGeo, blockMaterial, tempMatrix);
                                        }
                                    }

                                    // Add side caps for east and west faces of wall voxels
                                    // For east face (right side of wall)
                                    if (currentEX === safeNumXEnv - 1 || (blockEX === template.w - 1 && ex + template.w === safeNumXEnv)) {
                                        const sideFaceGeo = getOrCreateGeometry(
                                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                                        );
                                        const rightPosX = finalX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(rightPosX, finalY, 0);
                                        addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // For west face (left side of wall)
                                    if (currentEX === 0 || blockEX === 0) {
                                        const sideFaceGeo = getOrCreateGeometry(
                                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                                        );
                                        const leftPosX = finalX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                                        tempMatrix.makeTranslation(leftPosX, finalY, 0);
                                        addGeometry(sideFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // For north face (front side of wall) - FIXED: Use proper wall depth
                                    if (true) { // Always add north and south caps since they're always visible from top-down view
                                        const northFaceGeo = getOrCreateGeometry(
                                            `wall_ns_cap_thick_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, depth * 0.4)
                                        );
                                        const frontPosZ = -offsetZ - depth * 0.2;
                                        tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                                        addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;

                                        // For south face (back side of wall) - FIXED: Use proper wall depth
                                        const backPosZ = offsetZ + depth * 0.2;
                                        tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                                        addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }

                                    // Add top face for the top row of voxels with extended coverage and overhang
                                    if (currentEY === safeNumYEnv - 1 || (blockEY === template.h - 1 && ey + template.h === safeNumYEnv)) {
                                        // Create a larger top cap that covers gaps and overhangs the sides
                                        const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                                        const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                                        const topCapGeo = getOrCreateGeometry(
                                            `wall_top_cap_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                                            () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                                        );

                                        // Position at the top of the wall voxel, slightly higher for prominence
                                        const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                                        // Add extended top cap with overhang
                                        tempMatrix.makeTranslation(finalX, topPosY, 0);
                                        addGeometry(topCapGeo, blockMaterial, tempMatrix);
                                        voxelsAdded++;

                                        // Add additional gap-filling caps between voxels with matching overhang
                                        if (currentEX < safeNumXEnv - 1) { // Not the last column
                                            const gapCapGeo = getOrCreateGeometry(
                                                `wall_gap_cap_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                                                () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                                            );
                                            const gapCapX = finalX + wallVoxelSize * 0.6;
                                            tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                                            addGeometry(gapCapGeo, blockMaterial, tempMatrix);
                                        }
                                    }
                                }
                            }
                            blockPlaced = true; break;
                        }
                    }
                }

                // Fill gaps if no block was placed with curvature
                if (!blockPlaced && !filledVoxels_env[ey][ex]) {
                    const baseX = ex * wallVoxelSize - offsetX;
                    const baseY = ey * wallVoxelSize - offsetY;

                    // CRITICAL FIX: Reduced variations for gap fillers to match main voxels
                    const depthVariation = (random() - 0.5) * 0.025; // Slightly less variation for mortar
                    const heightVariation = (random() - 0.5) * 0.015; // Reduced height variation
                    const rotationY = (random() - 0.5) * 0.015; // Reduced Y rotation
                    const rotationZ = (random() - 0.5) * 0.01; // Reduced Z rotation

                    const finalX = baseX + depthVariation;
                    const finalY = baseY + heightVariation;

                    // Use two voxels for depth (front and back faces) with reduced variation
                    for (let z = 0; z < safeNumZ; z += safeNumZ - 1) {
                        const posZ = z * VOXEL_SIZE - offsetZ + depthVariation * 0.2;

                        // Create transformation matrix with reduced rotation
                        tempMatrix.makeRotationFromEuler(new THREE.Euler(0, rotationY, rotationZ));
                        tempMatrix.setPosition(finalX, finalY, posZ);

                        addGeometry(largeWallGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // Add side caps for east and west faces of gap filler voxels
                    // For east face (right side of wall)
                    if (ex === safeNumXEnv - 1) {
                        const sideFaceGeo = getOrCreateGeometry(
                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                        );
                        const rightPosX = finalX + wallVoxelSize/2 + VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(rightPosX, finalY, 0);
                        addGeometry(sideFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // For west face (left side of wall)
                    if (ex === 0) {
                        const sideFaceGeo = getOrCreateGeometry(
                            `wall_side_cap_${wallVoxelSize.toFixed(4)}`,
                            () => new THREE.BoxGeometry(VOXEL_SIZE * 0.2, wallVoxelSize, depth)
                        );
                        const leftPosX = finalX - wallVoxelSize/2 - VOXEL_SIZE * 0.1;
                        tempMatrix.makeTranslation(leftPosX, finalY, 0);
                        addGeometry(sideFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // For north face (front side of wall) - FIXED: Use proper wall depth for thickness
                    if (true) { // Always add north and south caps since they're always visible from top-down view
                        const northFaceGeo = getOrCreateGeometry(
                            `wall_ns_cap_thick_mortar_${wallVoxelSize.toFixed(4)}_${depth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, depth * 0.4)
                        );
                        const frontPosZ = -offsetZ - depth * 0.2;
                        tempMatrix.makeTranslation(finalX, finalY, frontPosZ);
                        addGeometry(northFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;

                        // For south face (back side of wall) - FIXED: Use proper wall depth for thickness
                        const backPosZ = offsetZ + depth * 0.2;
                        tempMatrix.makeTranslation(finalX, finalY, backPosZ);
                        addGeometry(northFaceGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;
                    }

                    // Add top face for the top row of gap filler voxels with extended coverage and overhang
                    if (ey === safeNumYEnv - 1) {
                        // Create a larger top cap that covers gaps and overhangs the sides
                        const extendedCapSize = wallVoxelSize * 1.2; // 20% larger to cover gaps
                        const overhangDepth = depth * 1.4; // 40% deeper for side overhang
                        const topCapGeo = getOrCreateGeometry(
                            `wall_top_cap_mortar_overhang_${extendedCapSize.toFixed(4)}_${overhangDepth.toFixed(4)}`,
                            () => new THREE.BoxGeometry(extendedCapSize, VOXEL_SIZE * 0.25, overhangDepth)
                        );

                        // Position at the top of the wall voxel, slightly higher for prominence
                        const topPosY = finalY + wallVoxelSize/2 + VOXEL_SIZE * 0.125;

                        // Add extended top cap with overhang
                        tempMatrix.makeTranslation(finalX, topPosY, 0);
                        addGeometry(topCapGeo, stonebrickMortarMaterial, tempMatrix);
                        voxelsAdded++;

                        // Add additional gap-filling caps between gap filler voxels with matching overhang
                        if (ex < safeNumXEnv - 1) { // Not the last column
                            const gapCapGeo = getOrCreateGeometry(
                                `wall_gap_cap_mortar_overhang_${(wallVoxelSize * 0.4).toFixed(4)}_${overhangDepth.toFixed(4)}`,
                                () => new THREE.BoxGeometry(wallVoxelSize * 0.4, VOXEL_SIZE * 0.2, overhangDepth)
                            );
                            const gapCapX = finalX + wallVoxelSize * 0.6;
                            tempMatrix.makeTranslation(gapCapX, topPosY, 0);
                            addGeometry(gapCapGeo, stonebrickMortarMaterial, tempMatrix);
                        }
                    }
                }
            }
        }
    }
    console.log(`Added ${voxelsAdded} wall voxels total.`);

    // --- Merge Geometries by Material ---
    for (const colorHex in geometriesByMaterial) {
        const material = _getMaterialByHex_Cached(colorHex);
        if (!material) continue;
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for stonebrick wall, material: ${colorHex}`);
        }
    }
    // --- End Merge ---

    return {
        group: finalGroup
    };
}