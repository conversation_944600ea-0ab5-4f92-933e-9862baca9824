import * as THREE from 'three';
import { VoxelInstanceManager } from '../../systems/VoxelInstanceManager.js';
import {
    VOXEL_SIZE,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// Define rubble material colors (hex strings) to avoid circular dependency
const RUBBLE_COLOR_1 = 'C0C0B0';    // Light Beige-Grey (mainStoneMaterial color)
const RUBBLE_COLOR_2 = 'BBBBBB';    // Medium-Light Grey (midLightStoneMaterial color)
const RUBBLE_COLOR_3 = 'CCCCCC';    // Very Light Grey (lightStoneMaterial color)

/**
 * Creates a 3D pile of stone rubble using instanced rendering.
 * This is an optimized version that dramatically reduces draw calls.
 */
export function createStoneRubbleObjectInstanced(options = {}) {
    const { seed = 54321 } = options;
    const random = mulberry32(seed);
    
    const rubbleGroup = new THREE.Group();
    const manager = new VoxelInstanceManager();
    const originalVoxels = [];
    
    const numPieces = Math.floor(random() * 7) + 6; // 6-12 pieces
    const pileRadius = VOXEL_SIZE * 2.0;
    const pileHeight = VOXEL_SIZE * 2.0;
    
    const colors = [RUBBLE_COLOR_1, RUBBLE_COLOR_2, RUBBLE_COLOR_3];
    const tempVector = new THREE.Vector3();
    
    for (let i = 0; i < numPieces; i++) {
        // For instanced rendering, we'll create multiple voxels per "piece"
        // to simulate the irregular shapes
        const pieceVoxels = Math.floor(random() * 3) + 1; // 1-3 voxels per piece
        
        // Base position for this piece
        const angle = random() * Math.PI * 2;
        const radius = random() * pileRadius;
        const baseX = Math.cos(angle) * radius;
        const baseZ = Math.sin(angle) * radius;
        const baseY = (random() * pileHeight * 0.5) + (VOXEL_SIZE / 2);
        
        // Random rotation for the piece (we'll apply this to voxel offsets)
        const rotY = random() * Math.PI * 2;
        
        // Choose color for this piece
        const hex = colors[Math.floor(random() * colors.length)];
        const material = _getMaterialByHex_Cached(hex);
        
        // Create voxels for this piece
        for (let v = 0; v < pieceVoxels; v++) {
            // Offset from piece center
            const offsetX = (v % 2) * VOXEL_SIZE * Math.cos(rotY);
            const offsetZ = (v % 2) * VOXEL_SIZE * Math.sin(rotY);
            const offsetY = Math.floor(v / 2) * VOXEL_SIZE;
            
            // Final position
            tempVector.set(
                baseX + offsetX,
                baseY + offsetY,
                baseZ + offsetZ
            );
            
            // Add voxel to instance manager
            manager.addVoxel(tempVector, VOXEL_SIZE, material);
            
            // Store voxel data for destruction
            originalVoxels.push({
                x: tempVector.x / VOXEL_SIZE,
                y: tempVector.y / VOXEL_SIZE,
                z: tempVector.z / VOXEL_SIZE,
                c: hex
            });
        }
    }
    
    // Build instanced meshes
    const instancedMeshes = manager.build();
    rubbleGroup.add(instancedMeshes);
    
    // Set UserData
    rubbleGroup.userData = {
        ...(rubbleGroup.userData || {}),
        objectType: 'stone_rubble',
        isDestructible: true,
        destructionEffect: 'collapse',
        health: 1,
        originalVoxels: originalVoxels,
        voxelScale: VOXEL_SIZE,
        isInstanced: true // Mark as instanced for special handling
    };
    
    return { group: rubbleGroup };
}