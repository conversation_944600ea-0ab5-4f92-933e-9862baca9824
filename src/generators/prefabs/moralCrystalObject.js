import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a moral crystal - small atmospheric crystals that react to moral alignment
 * These crystals change color and intensity based on the player's moral choices
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Moral crystal object group
 */
export function createMoralCrystalObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 333);

    // Use small voxel size for delicate crystals
    const crystalVoxelSize = VOXEL_SIZE * 1.2;
    const baseGeometry = getOrCreateGeometry('moral_crystal_voxel', () =>
        new THREE.BoxGeometry(crystalVoxelSize, crystalVoxelSize, crystalVoxelSize)
    );

    // Moral crystal materials (neutral, good, evil)
    const neutralCrystalMaterial = _getMaterialByHex_Cached('6A5ACD', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x6A5ACD),
        emissiveIntensity: 0.3,
        roughness: 0.2,
        metalness: 0.1
    }); // Slate blue - neutral

    const goodCrystalMaterial = _getMaterialByHex_Cached('87CEEB', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x87CEEB),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.1
    }); // Sky blue - good alignment

    const evilCrystalMaterial = _getMaterialByHex_Cached('DC143C', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0xDC143C),
        emissiveIntensity: 0.4,
        roughness: 0.2,
        metalness: 0.1
    }); // Crimson - evil alignment

    const baseMaterial = _getMaterialByHex_Cached('2F2F2F', {
        roughness: 0.9,
        metalness: 0.1
    }); // Dark stone base

    // Determine crystal material based on seed (can be changed by player alignment)
    const alignmentRoll = rng();
    let crystalMaterial = neutralCrystalMaterial;
    if (alignmentRoll < 0.1) {
        crystalMaterial = evilCrystalMaterial;
    } else if (alignmentRoll > 0.9) {
        crystalMaterial = goodCrystalMaterial;
    }

    // Create small stone base
    const base = new THREE.Mesh(
        getOrCreateGeometry('crystal_base', () =>
            new THREE.BoxGeometry(crystalVoxelSize * 1.2, crystalVoxelSize * 0.3, crystalVoxelSize * 1.2)
        ),
        baseMaterial
    );
    base.position.set(0, crystalVoxelSize * 0.15, 0);
    base.userData.isCrystalPart = true;
    base.userData.partType = 'base';
    base.castShadow = true;
    base.receiveShadow = true;
    group.add(base);

    // Create crystal formation (pointed upward)
    const crystalPositions = [
        // Main crystal stem
        { x: 0, y: 1, z: 0 },
        { x: 0, y: 2, z: 0 },
        { x: 0, y: 3, z: 0 },
        
        // Side crystal growths
        { x: -1, y: 1, z: 0 },
        { x: 1, y: 1, z: 0 },
        { x: 0, y: 1, z: -1 },
        { x: 0, y: 1, z: 1 },
        
        // Secondary growths
        { x: -1, y: 2, z: 0 },
        { x: 1, y: 2, z: 0 },
        
        // Crystal tip
        { x: 0, y: 4, z: 0 }
    ];

    crystalPositions.forEach((pos, index) => {
        // Scale gets smaller as we go up
        const scale = Math.max(0.3, 1.0 - (pos.y * 0.15));
        const crystalGeo = getOrCreateGeometry(
            `moral_crystal_${scale.toFixed(2)}`,
            () => new THREE.BoxGeometry(
                crystalVoxelSize * scale,
                crystalVoxelSize * scale,
                crystalVoxelSize * scale
            )
        );
        
        const crystal = new THREE.Mesh(crystalGeo, crystalMaterial);
        crystal.position.set(
            pos.x * crystalVoxelSize,
            pos.y * crystalVoxelSize,
            pos.z * crystalVoxelSize
        );
        
        // Add slight random rotation for organic look
        crystal.rotation.y = rng() * Math.PI * 2;
        crystal.rotation.x = (rng() - 0.5) * 0.2;
        crystal.rotation.z = (rng() - 0.5) * 0.2;
        
        crystal.userData.isCrystalPart = true;
        crystal.userData.partType = 'crystal';
        crystal.userData.crystalIndex = index;
        crystal.userData.animationPhase = (index / crystalPositions.length) * Math.PI * 2;
        crystal.userData.baseScale = scale;
        crystal.castShadow = false; // Emissive crystals don't cast shadows
        crystal.receiveShadow = false;
        group.add(crystal);
    });

    // Add floating energy motes around the crystal
    const moteCount = 3;
    for (let i = 0; i < moteCount; i++) {
        const angle = (i / moteCount) * Math.PI * 2;
        const radius = 1.5 + rng() * 0.5;
        const height = 2 + rng() * 2;
        
        const mote = new THREE.Mesh(
            getOrCreateGeometry('crystal_mote', () =>
                new THREE.BoxGeometry(crystalVoxelSize * 0.3, crystalVoxelSize * 0.3, crystalVoxelSize * 0.3)
            ),
            crystalMaterial
        );
        mote.position.set(
            Math.cos(angle) * radius * crystalVoxelSize,
            height * crystalVoxelSize,
            Math.sin(angle) * radius * crystalVoxelSize
        );
        mote.userData.isCrystalPart = true;
        mote.userData.partType = 'mote';
        mote.userData.animationPhase = angle;
        mote.userData.baseRadius = radius;
        mote.userData.baseHeight = height;
        mote.castShadow = false;
        mote.receiveShadow = false;
        group.add(mote);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'moral_crystal',
        isDecorative: true,
        isEventObject: true,
        objectId: options.userData?.objectId || 'moral_crystal',
        hasAnimation: true,
        animationType: 'moral_resonance',
        voxelScale: crystalVoxelSize,
        // Moral crystal properties
        isMoralCrystal: true,
        currentAlignment: 'neutral', // neutral, good, evil
        alignmentSensitivity: 0.5 + rng() * 0.5, // How strongly it reacts
        // Animation properties
        pulseSpeed: 0.003,
        moteOrbitSpeed: 0.001,
        resonanceIntensity: 1.0,
        // Material references for alignment changes
        neutralMaterial: neutralCrystalMaterial,
        goodMaterial: goodCrystalMaterial,
        evilMaterial: evilCrystalMaterial
    };

    group.name = 'moral_crystal';

    console.log('[MoralCrystal] ✅ Created moral crystal with floating energy motes');
    return group;
}

/**
 * Animate the moral crystal with pulsing and orbital motes
 * @param {THREE.Group} crystalGroup - The crystal group to animate
 * @param {number} deltaTime - Time since last frame
 * @param {number} time - Total elapsed time
 * @param {number} playerMoralAlignment - Player's current moral alignment (-1 to 1)
 */
export function animateMoralCrystal(crystalGroup, deltaTime, time, playerMoralAlignment = 0) {
    if (!crystalGroup || !crystalGroup.userData.isMoralCrystal) return;

    const { pulseSpeed, moteOrbitSpeed, alignmentSensitivity, resonanceIntensity } = crystalGroup.userData;

    // Update crystal material based on player alignment
    let targetMaterial = crystalGroup.userData.neutralMaterial;
    let targetAlignment = 'neutral';
    
    if (playerMoralAlignment > 0.3) {
        targetMaterial = crystalGroup.userData.goodMaterial;
        targetAlignment = 'good';
    } else if (playerMoralAlignment < -0.3) {
        targetMaterial = crystalGroup.userData.evilMaterial;
        targetAlignment = 'evil';
    }

    // Update alignment if changed
    if (crystalGroup.userData.currentAlignment !== targetAlignment) {
        crystalGroup.userData.currentAlignment = targetAlignment;
        
        // Update crystal materials
        crystalGroup.traverse(child => {
            if (child.userData.partType === 'crystal' || child.userData.partType === 'mote') {
                child.material = targetMaterial;
            }
        });
    }

    // Calculate resonance based on moral alignment strength
    const alignmentStrength = Math.abs(playerMoralAlignment);
    const resonance = resonanceIntensity * (1 + alignmentStrength * alignmentSensitivity);

    // Animate different parts based on their type
    crystalGroup.traverse(child => {
        if (!child.userData.partType) return;

        const { partType } = child.userData;

        switch (partType) {
            case 'crystal':
                // Crystals pulse with varying intensity based on alignment
                const crystalPhase = child.userData.animationPhase + time * pulseSpeed;
                const crystalPulse = (Math.sin(crystalPhase) + 1) * 0.5; // 0 to 1
                
                // Scale pulsing
                const pulseScale = child.userData.baseScale * (1 + crystalPulse * 0.1 * resonance);
                child.scale.setScalar(pulseScale);
                
                // Emissive intensity varies with alignment strength
                if (child.material.emissive) {
                    const baseIntensity = targetAlignment === 'neutral' ? 0.3 : 0.4;
                    child.material.emissiveIntensity = baseIntensity + crystalPulse * 0.2 * resonance;
                }
                
                // Gentle rotation
                child.rotation.y += pulseSpeed * deltaTime * 0.5;
                break;

            case 'mote':
                // Motes orbit around the crystal with vertical bobbing
                const motePhase = child.userData.animationPhase + time * moteOrbitSpeed;
                const radius = child.userData.baseRadius + Math.sin(motePhase * 3) * 0.3;
                const height = child.userData.baseHeight + Math.sin(motePhase * 2 + Math.PI/3) * 0.4;
                
                child.position.x = Math.cos(motePhase) * radius * crystalGroup.userData.voxelScale;
                child.position.y = height * crystalGroup.userData.voxelScale;
                child.position.z = Math.sin(motePhase) * radius * crystalGroup.userData.voxelScale;
                
                // Mote intensity varies with resonance
                const motePulse = (Math.sin(motePhase * 4) + 1) * 0.5;
                if (child.material.emissive) {
                    const baseIntensity = targetAlignment === 'neutral' ? 0.3 : 0.4;
                    child.material.emissiveIntensity = baseIntensity + motePulse * 0.15 * resonance;
                }
                
                // Scale pulsing for motes
                const moteScale = 1.0 + motePulse * 0.2 * resonance;
                child.scale.setScalar(moteScale);
                break;
        }
    });
}

/**
 * Update moral crystal based on player's moral alignment
 * @param {THREE.Group} crystalGroup - The crystal group to update
 * @param {number} moralAlignment - Player's moral alignment (-1 evil to 1 good)
 */
export function updateMoralCrystalAlignment(crystalGroup, moralAlignment) {
    if (!crystalGroup || !crystalGroup.userData.isMoralCrystal) return;

    // This function is called when the player's moral alignment changes
    // The actual material switching is handled in the animation function
    crystalGroup.userData.lastKnownAlignment = moralAlignment;
    
    console.log(`[MoralCrystal] Alignment updated: ${moralAlignment.toFixed(2)}`);
}