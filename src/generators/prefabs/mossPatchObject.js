import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// --- Pond Room Mystical Moss Colors ---
// Adapted from pond room mystical theme with cyan accents
const MYSTICAL_MOSS_COLORS = [
    '5A7EBD', // Mystical blue-green (pond theme)
    '4A6CAD', // Darker mystical blue-green
    '3A5A9D', // Deep mystical blue
    '2A488D', // Very deep mystical blue
    '1A367D', // Almost dark mystical blue
    '0A246D', // Very dark mystical
    '00AAAA', // Cyan-green (pond accent)
    '008888'  // Dark cyan-green
];

// Define LARGE mystical moss patch (like crystal cave base size - 5x5 area)
const mysticalMossPatch = [
    // === CENTRAL DENSE CORE (3x3) ===
    { x: -1, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[0] },
    { x: 0, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[1] },
    { x: 1, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[0] },
    { x: -1, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[1] },
    { x: 0, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[0] }, // Center
    { x: 1, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[1] },
    { x: -1, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[0] },
    { x: 0, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[1] },
    { x: 1, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[0] },

    // === INNER RING (5x5 minus center) ===
    { x: -2, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[2] },
    { x: -1, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 0, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[2] },
    { x: 1, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 2, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[2] },

    { x: -2, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 2, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[3] },

    { x: -2, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[2] },
    { x: 2, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[2] },

    { x: -2, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 2, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[3] },

    { x: -2, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[2] },
    { x: -1, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 0, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[2] },
    { x: 1, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[3] },
    { x: 2, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[2] },

    // === OUTER SCATTERED PATCHES ===
    { x: -3, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[4] },
    { x: -3, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[5] },
    { x: -3, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[4] },

    { x: 3, y: 0, z: -1, c: MYSTICAL_MOSS_COLORS[4] },
    { x: 3, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[5] },
    { x: 3, y: 0, z: 1, c: MYSTICAL_MOSS_COLORS[4] },

    { x: -1, y: 0, z: -3, c: MYSTICAL_MOSS_COLORS[4] },
    { x: 0, y: 0, z: -3, c: MYSTICAL_MOSS_COLORS[5] },
    { x: 1, y: 0, z: -3, c: MYSTICAL_MOSS_COLORS[4] },

    { x: -1, y: 0, z: 3, c: MYSTICAL_MOSS_COLORS[4] },
    { x: 0, y: 0, z: 3, c: MYSTICAL_MOSS_COLORS[5] },
    { x: 1, y: 0, z: 3, c: MYSTICAL_MOSS_COLORS[4] },

    // === CYAN MYSTICAL ACCENTS (like pond glow) ===
    { x: 0, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[6] }, // Cyan center accent
    { x: -2, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[6] }, // Corner accents
    { x: 2, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[6] },
    { x: -2, y: 0, z: 2, c: MYSTICAL_MOSS_COLORS[7] },
    { x: 2, y: 0, z: -2, c: MYSTICAL_MOSS_COLORS[7] },

    // === VERY SPARSE EDGE TENDRILS ===
    { x: -4, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[7] },
    { x: 4, y: 0, z: 0, c: MYSTICAL_MOSS_COLORS[7] },
    { x: 0, y: 0, z: -4, c: MYSTICAL_MOSS_COLORS[7] },
    { x: 0, y: 0, z: 4, c: MYSTICAL_MOSS_COLORS[7] }
];

/**
 * Create a large mystical moss patch on the ground (like crystal cave scale)
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Moss patch object group
 */
export function createMossPatchObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 999);

    // Use FLOOR VOXEL SIZE for proper scale (VOXEL_SIZE * 6 = floor scale)
    const mossVoxelSize = VOXEL_SIZE * 6;
    const baseGeometry = getOrCreateGeometry('moss_patch_large_voxel', () =>
        new THREE.BoxGeometry(mossVoxelSize, mossVoxelSize * 0.3, mossVoxelSize) // Flatter but larger
    );

    // Create mystical moss materials with pond room colors
    const mysticalMossMaterials = {};
    MYSTICAL_MOSS_COLORS.forEach(color => {
        mysticalMossMaterials[color] = _getMaterialByHex_Cached(color, {
            emissive: new THREE.Color(`0x${color}`).multiplyScalar(0.15),
            emissiveIntensity: 0.3,
            roughness: 0.8,
            metalness: 0.0,
            transparent: true,
            opacity: 0.9
        });
    });

    // Build the large mystical moss patch
    mysticalMossPatch.forEach(voxel => {
        // Skip some outer voxels randomly for organic variation
        if (voxel.x > 2 || voxel.x < -2 || voxel.z > 2 || voxel.z < -2) {
            if (rng() < 0.3) return; // 30% chance to skip outer voxels
        }

        const finalX = voxel.x * mossVoxelSize + (rng() - 0.5) * mossVoxelSize * 0.2;
        const finalY = voxel.y * mossVoxelSize * 0.3; // Keep moss very low
        const finalZ = voxel.z * mossVoxelSize + (rng() - 0.5) * mossVoxelSize * 0.2;

        const mossVoxel = new THREE.Mesh(baseGeometry.clone(), mysticalMossMaterials[voxel.c]);
        mossVoxel.position.set(finalX, finalY, finalZ);

        // Add slight random rotation and scale variation
        mossVoxel.rotation.y = (rng() - 0.5) * Math.PI * 0.5;
        const scaleVariation = 0.8 + rng() * 0.4; // 0.8x to 1.2x scale
        mossVoxel.scale.set(scaleVariation, 1, scaleVariation);

        mossVoxel.castShadow = false; // Moss doesn't cast strong shadows
        mossVoxel.receiveShadow = true;

        group.add(mossVoxel);
    });

    // Add mystical floating spores (small glowing particles)
    const sporeCount = 3 + Math.floor(rng() * 3); // 3-5 spores
    for (let i = 0; i < sporeCount; i++) {
        const sporeGeometry = getOrCreateGeometry('moss_spore', () =>
            new THREE.SphereGeometry(mossVoxelSize * 0.1, 8, 6)
        );

        const sporeMaterial = _getMaterialByHex_Cached(MYSTICAL_MOSS_COLORS[6], {
            transparent: true,
            opacity: 0.6,
            emissive: new THREE.Color(0x00AAAA),
            emissiveIntensity: 0.8
        });

        const spore = new THREE.Mesh(sporeGeometry, sporeMaterial);
        spore.position.set(
            (rng() - 0.5) * mossVoxelSize * 4,
            mossVoxelSize * (0.5 + rng() * 1.5), // Float above moss
            (rng() - 0.5) * mossVoxelSize * 4
        );

        spore.castShadow = false;
        spore.receiveShadow = false;

        group.add(spore);
    }

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'moss_patch',
        isInteractable: false,
        isDecorative: true,
        isFloorObject: true,
        hasCollision: false, // Can walk through moss
        isInteriorObject: true,
        voxelScale: mossVoxelSize
    };

    group.name = 'moss_patch';
    
    // Mark as ground decoration that shouldn't block movement
    group.userData.isGroundDecoration = true;
    group.userData.isSmallObject = true;

    console.log('[MossPatchObject] ✅ Created large mystical moss patch');
    return group;
}
