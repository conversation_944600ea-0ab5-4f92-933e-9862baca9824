import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    mulberry32, _getMaterialByHex_Cached, getOrCreateGeometry
} from './shared.js';

/**
 * Ancient Stone Brick Wall with Vines
 *
 * Creates weathered ancient stone brick walls with creeping vines and moss growth.
 * Matches the ancient stone floor with vines for a cohesive abandoned medieval hall aesthetic.
 * Based on the stonebrick wall structure but with weathered stone and vine materials.
 */

// Ancient stone materials - faded yellowish weathered stone (matching screenshot and floor)
const ANCIENT_STONE_MATERIALS = [
    _getMaterialByHex_Cached('8B8B7A'), // Faded yellowish stone (primary) - matches floor
    _getMaterialByHex_Cached('A0A089'), // Light faded yellow stone (secondary) - matches floor
    _getMaterialByHex_Cached('6B6B5A'), // Darker faded stone (shadows/cracks) - matches floor
    _getMaterialByHex_Cached('B5B59E'), // Lightest faded yellow (highlights) - matches floor
    _getMaterialByHex_Cached('5A5A4A'), // Very dark weathered stone (deep mortar) - matches floor
    _getMaterialByHex_Cached('C0C0A8'), // Very light faded yellow (worn edges) - matches floor
];

// Vine materials for wall growth (matching floor vines)
const WALL_VINE_MATERIALS = [
    _getMaterialByHex_Cached('2D4A2B'), // Dark forest green (main vines) - matches floor
    _getMaterialByHex_Cached('3E5C3A'), // Medium green (vine leaves) - matches floor
    _getMaterialByHex_Cached('1F3A1E'), // Very dark green (vine shadows) - matches floor
    _getMaterialByHex_Cached('6B8E23'), // Olive green (moss patches) - matches floor
    _getMaterialByHex_Cached('8B4513'), // Saddle brown (vine stems) - matches floor
];

/**
 * Creates an ancient stone brick wall segment with vines using the same structure as stonebrick walls.
 * Returns an object: { group: THREE.Group }
 */
export function createAncientStoneBrickWallWithVines(width, height, depth, roomData) {
    // --- INPUT VALIDATION ---
    if (!width || !height || !depth || width <= 0 || height <= 0 || depth <= 0) {
        console.error(`createAncientStoneBrickWallWithVines received invalid dimensions: w=${width}, h=${height}, d=${depth}. Skipping segment.`);
        return { group: new THREE.Group() };
    }
    // --- END INPUT VALIDATION ---

    const finalGroup = new THREE.Group();
    const geometriesByMaterial = {};

    // --- Use larger scale for walls to reduce voxel count (same as stonebrick) ---
    const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 1.5;

    // --- Calculate Dimensions (same as stonebrick) ---
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numY = Math.ceil(height / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / WALL_SCALE);
    const numY_env = Math.ceil(numY / WALL_SCALE);
    // --- End Calculation ---

    // --- Safeguard Dimensions (same as stonebrick) ---
    const safeNumX = Math.max(1, numX);
    const safeNumY = Math.max(1, numY);
    const safeNumZ = Math.max(1, numZ);
    const safeNumXEnv = Math.max(1, numX_env);
    const safeNumYEnv = Math.max(1, numY_env);
    if (numX <= 0 || numY <= 0 || numZ <= 0 || numX_env <= 0 || numY_env <= 0) {
        console.warn(`Clamped dimension in createAncientStoneBrickWallWithVines: w=${width}, h=${height}. Original base(${numX},${numY}), env(${numX_env},${numY_env}). Using safe(${safeNumX},${safeNumY}), env(${safeNumXEnv},${safeNumYEnv})`);
    }
    // --- End Safeguard ---

    const filledVoxels_env = Array(safeNumYEnv).fill(null).map(() => Array(safeNumXEnv).fill(false));

    const offsetX = (safeNumX - 1) * VOXEL_SIZE / 2;
    const offsetY = (safeNumY - 1) * VOXEL_SIZE / 2;
    const offsetZ = (safeNumZ - 1) * VOXEL_SIZE / 2;
    const vineProbability = 0.20; // 20% chance for vine coverage

    // --- Use Seeded PRNG (same as stonebrick) ---
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);
    // --- End Seeded PRNG ---

    // --- Block Templates (same as stonebrick) ---
    const blockTemplates = [
        { w: 4, h: 3 },
        { w: 3, h: 3 },
        { w: 5, h: 2 },
        { w: 3, h: 4 },
        { w: 4, h: 3 },
    ];
    // -----------------------

    // Get cached wall voxel geometry
    const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;
    const largeWallGeo = getOrCreateGeometry(
        `ancient_wall_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE)
    );

    // Vine geometry for wall accents
    const vineGeo = getOrCreateGeometry(
        `ancient_wall_vine_${wallVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(wallVoxelSize * 0.3, wallVoxelSize * 0.8, VOXEL_SIZE * 0.2)
    );

    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // --- Wall Generation using Environment Blocks (same structure as stonebrick) ---
    let voxelsAdded = 0;
    for (let ey = 0; ey < safeNumYEnv; ey++) {
        for (let ex = 0; ex < safeNumXEnv; ex++) {
            if (!filledVoxels_env[ey][ex]) {
                let blockPlaced = false;
                const shuffledTemplates = [...blockTemplates].sort(() => random() - 0.5);

                for (const template of shuffledTemplates) {
                    if (ey + template.h <= safeNumYEnv && ex + template.w <= safeNumXEnv) {
                        // Check if all positions are available
                        let canPlace = true;
                        for (let by = ey; by < ey + template.h; by++) {
                            for (let bx = ex; bx < ex + template.w; bx++) {
                                if (filledVoxels_env[by][bx]) {
                                    canPlace = false;
                                    break;
                                }
                            }
                            if (!canPlace) break;
                        }

                        if (canPlace) {
                            // Place the block - choose random ancient stone material
                            const blockMaterial = ANCIENT_STONE_MATERIALS[Math.floor(random() * ANCIENT_STONE_MATERIALS.length)];

                            for (let by = ey; by < ey + template.h; by++) {
                                for (let bx = ex; bx < ex + template.w; bx++) {
                                    filledVoxels_env[by][bx] = true;

                                    const baseX = bx * wallVoxelSize - offsetX;
                                    const baseY = by * wallVoxelSize - offsetY;

                                    // Add weathered variations for aged appearance
                                    const depthVariation = (random() - 0.5) * 0.08;
                                    const heightVariation = (random() - 0.5) * 0.05;
                                    const rotationY = (random() - 0.5) * 0.05;
                                    const rotationZ = (random() - 0.5) * 0.04;

                                    const finalX = baseX;
                                    const finalY = baseY + heightVariation;
                                    const finalZ = depthVariation;

                                    // Main weathered wall block
                                    tempMatrix.makeTranslation(finalX, finalY, finalZ);
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                                    addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                                    voxelsAdded++;

                                    // Add north face cap
                                    const northFaceGeo = getOrCreateGeometry(
                                        `ancient_wall_cap_north_${wallVoxelSize.toFixed(4)}`,
                                        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE * 0.1)
                                    );
                                    tempMatrix.makeTranslation(finalX, finalY, depth / 2 + VOXEL_SIZE * 0.05);
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                                    addGeometry(northFaceGeo, blockMaterial, tempMatrix);
                                    voxelsAdded++;

                                    // Add south face cap
                                    const southFaceGeo = getOrCreateGeometry(
                                        `ancient_wall_cap_south_${wallVoxelSize.toFixed(4)}`,
                                        () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, VOXEL_SIZE * 0.1)
                                    );
                                    tempMatrix.makeTranslation(finalX, finalY, -depth / 2 - VOXEL_SIZE * 0.05);
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                                    tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                                    addGeometry(southFaceGeo, blockMaterial, tempMatrix);
                                    voxelsAdded++;

                                    // Add vine growth (more likely at bottom of wall)
                                    const heightFactor = 1 - (by / safeNumYEnv); // More vines at bottom
                                    const vineChance = vineProbability * heightFactor + 0.05;
                                    
                                    if (random() < vineChance) {
                                        const vineMaterial = WALL_VINE_MATERIALS[Math.floor(random() * WALL_VINE_MATERIALS.length)];
                                        
                                        // Random vine positioning for organic growth
                                        const vineOffsetX = (random() - 0.5) * wallVoxelSize * 0.6;
                                        const vineOffsetY = (random() - 0.5) * wallVoxelSize * 0.3;
                                        const vineDepth = depth / 2 + VOXEL_SIZE * 0.15;

                                        tempMatrix.makeTranslation(
                                            finalX + vineOffsetX,
                                            finalY + vineOffsetY,
                                            vineDepth
                                        );
                                        tempMatrix.multiply(new THREE.Matrix4().makeRotationZ((random() - 0.5) * Math.PI * 0.2));
                                        addGeometry(vineGeo, vineMaterial, tempMatrix);
                                        voxelsAdded++;
                                    }
                                }
                            }
                            blockPlaced = true;
                            break;
                        }
                    }
                }

                if (!blockPlaced) {
                    // Place a single voxel if no template fits
                    filledVoxels_env[ey][ex] = true;
                    const blockMaterial = ANCIENT_STONE_MATERIALS[Math.floor(random() * ANCIENT_STONE_MATERIALS.length)];

                    const baseX = ex * wallVoxelSize - offsetX;
                    const baseY = ey * wallVoxelSize - offsetY;

                    const depthVariation = (random() - 0.5) * 0.08;
                    const heightVariation = (random() - 0.5) * 0.05;
                    const rotationY = (random() - 0.5) * 0.05;
                    const rotationZ = (random() - 0.5) * 0.04;

                    const finalX = baseX;
                    const finalY = baseY + heightVariation;
                    const finalZ = depthVariation;

                    tempMatrix.makeTranslation(finalX, finalY, finalZ);
                    tempMatrix.multiply(new THREE.Matrix4().makeRotationY(rotationY));
                    tempMatrix.multiply(new THREE.Matrix4().makeRotationZ(rotationZ));
                    addGeometry(largeWallGeo, blockMaterial, tempMatrix);
                    voxelsAdded++;
                }
            }
        }
    }

    console.log(`[AncientStoneWallWithVines] Generated ${voxelsAdded} wall voxels with vine accents`);

    // --- Merge Geometries by Material (same as stonebrick) ---
    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for ancient stone wall: ${colorHex}`);
            continue;
        }

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, originalMaterial);
            mesh.castShadow = true; // Walls cast shadows
            mesh.receiveShadow = true; // And receive shadows
            mesh.name = 'ancientStoneWallWithVines';
            mesh.userData.isWall = true;
            mesh.userData.wallType = 'ancient_stone_with_vines';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for ancient stone wall, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.wallType = 'ancient_stone_with_vines';
    finalGroup.userData.isWall = true;
    finalGroup.name = 'ancientStoneWallWithVines';
    finalGroup.castShadow = true;
    finalGroup.receiveShadow = true;

    console.log(`[AncientStoneWallWithVines] ✅ Generated ancient stone wall with vines: ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return { group: finalGroup };
}