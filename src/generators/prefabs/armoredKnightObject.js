import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

// Traditional Iron Knight Colors - Following angel statue and nairabos scale patterns
const IRON_KNIGHT_COLORS = {
    // Iron armor palette - metallic and worn
    iron_base: '4A4A4A',        // Dark iron base
    iron_plate: '606060',       // Iron armor plates
    iron_trim: '707070',        // Iron trim and edges
    iron_highlight: '808080',   // Iron highlights
    iron_shadow: '2A2A2A',      // Deep iron shadows
    iron_dark: '1A1A1A',        // Very dark iron
    // Visor and interior
    visor_black: '000000',      // Black visor interior
    visor_shadow: '0A0A0A',     // Visor shadow
    // Mystical glow (same for both knights)
    mystical_glow: '4169E1',    // Royal blue mystical glow
    // Weathering system like angel statues
    weathering: ['3A3A3A', '5A5A5A', '6A6A6A'] // Iron weathering variations
};

// Helper functions matching angel statue pattern
function generateSquareLayer(y, radius, color) {
    const layer = [];
    for (let x = -radius; x <= radius; x++) {
        for (let z = -radius; z <= radius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

function generateRectangleLayer(y, xRadius, zRadius, color) {
    const layer = [];
    for (let x = -xRadius; x <= xRadius; x++) {
        for (let z = -zRadius; z <= zRadius; z++) {
            layer.push({ x, y, z, c: color });
        }
    }
    return layer;
}

// Helper functions are already defined above

// Generate knight pedestal details - following angel statue decorative pattern
function generateKnightPedestalDetails() {
    return [
        // Heraldic shields on pedestal sides
        { x: 2, y: 2, z: 0, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: -2, y: 2, z: 0, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 0, y: 2, z: 2, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 0, y: 2, z: -2, c: IRON_KNIGHT_COLORS.iron_shadow },
        
        // Corner reinforcements
        { x: 1, y: 1, z: 1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: -1, y: 1, z: 1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: 1, y: 1, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: -1, y: 1, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
    ];
}

// Generate traditional iron knight shape - following angel statue scale and nairabos detail
function generateTraditionalIronKnightShape() {
    
    return [
        // === IRON KNIGHT PEDESTAL (Y: 0-6) === Expanded for scale compensation
        // Large stone foundation (Y=0) - 6x6 for bigger base
        ...generateSquareLayer(0, 3, IRON_KNIGHT_COLORS.iron_shadow),
        // Second tier (Y=1) - 5x5 platform
        ...generateSquareLayer(1, 2, IRON_KNIGHT_COLORS.iron_base),
        // Third tier (Y=2) - 4x4 platform
        ...generateSquareLayer(2, 1, IRON_KNIGHT_COLORS.iron_plate),
        // Fourth tier (Y=3) - 3x3 with heraldic center
        ...generateSquareLayer(3, 1, IRON_KNIGHT_COLORS.iron_base),
        { x: 0, y: 3, z: 0, c: IRON_KNIGHT_COLORS.mystical_glow }, // Heraldic center
        // Upper platform (Y=4-6) - knight standing platform
        ...generateSquareLayer(4, 1, IRON_KNIGHT_COLORS.iron_trim),
        ...generateSquareLayer(5, 1, IRON_KNIGHT_COLORS.iron_plate),
        ...generateSquareLayer(6, 1, IRON_KNIGHT_COLORS.iron_base),
        
        // === PEDESTAL HERALDIC DETAILS ===
        ...generateKnightPedestalDetails(),
        
        // === IRON KNIGHT LEGS (Y: 7-13) === Shortened for better proportions
        // Iron sabatons (boots) - Y=7-8
        { x: -1, y: 7, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 1, y: 7, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -1, y: 7, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 1, y: 7, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -1, y: 8, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 1, y: 8, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Lower leg armor (greaves) - Y=9-10
        { x: -1, y: 9, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 1, y: 9, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -1, y: 10, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 1, y: 10, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        
        // Knee armor (poleyns) - Y=11
        { x: -1, y: 11, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 1, y: 11, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 0, y: 11, z: 0, c: IRON_KNIGHT_COLORS.iron_plate }, // Center connection
        { x: -1, y: 11, z: 1, c: IRON_KNIGHT_COLORS.iron_plate }, // Knee guards
        { x: 1, y: 11, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Upper leg armor (cuisses) - Y=12-13
        { x: -1, y: 12, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 1, y: 12, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 0, y: 12, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -1, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 1, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 0, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        
        // Leg armor shadows
        { x: -1, y: 9, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 1, y: 9, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        
        // === IRON KNIGHT TORSO (Y: 14-22) === Shortened for better proportions
        // Lower torso (faulds) - Y=14-15
        ...generateRectangleLayer(14, 1, 0, IRON_KNIGHT_COLORS.iron_base),
        ...generateRectangleLayer(15, 1, 0, IRON_KNIGHT_COLORS.iron_trim),
        
        // Mid torso (breastplate) - Y=16-19
        ...generateRectangleLayer(16, 1, 0, IRON_KNIGHT_COLORS.iron_plate),
        ...generateRectangleLayer(17, 1, 0, IRON_KNIGHT_COLORS.iron_highlight),
        ...generateRectangleLayer(18, 1, 0, IRON_KNIGHT_COLORS.iron_plate),
        ...generateRectangleLayer(19, 1, 0, IRON_KNIGHT_COLORS.iron_trim),
        
        // Upper torso (upper breastplate) - Y=20-22
        ...generateRectangleLayer(20, 1, 0, IRON_KNIGHT_COLORS.iron_base),
        ...generateRectangleLayer(21, 1, 0, IRON_KNIGHT_COLORS.iron_plate),
        ...generateRectangleLayer(22, 1, 0, IRON_KNIGHT_COLORS.iron_highlight),
        
        // Breastplate heraldic cross - traditional knight emblem (adjusted)
        { x: 0, y: 17, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow }, // Cross center
        { x: -1, y: 17, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow }, // Cross horizontal
        { x: 1, y: 17, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow },
        { x: 0, y: 16, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow }, // Cross vertical
        { x: 0, y: 18, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow },
        
        // Breastplate edge details
        { x: -1, y: 16, z: 1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 1, y: 16, z: 1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 0, y: 15, z: 1, c: IRON_KNIGHT_COLORS.iron_shadow },
        
        // === IRON KNIGHT ARMS (Y: 14-22) === Shortened traditional arm armor
        // Left arm (rerebrace and vambrace) - shortened
        { x: -2, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -2, y: 15, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -2, y: 16, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -2, y: 17, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -2, y: 18, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -2, y: 19, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: -2, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -2, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -2, y: 22, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        
        // Left pauldron (shoulder armor) - adjusted
        { x: -2, y: 20, z: 1, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: -2, y: 21, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -2, y: 22, z: 1, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -3, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_trim }, // Extended pauldron
        { x: -3, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Right arm (mirrored) - shortened
        { x: 2, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 2, y: 15, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 2, y: 16, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 2, y: 17, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 2, y: 18, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 2, y: 19, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 2, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 2, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 2, y: 22, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        
        // Right pauldron (shoulder armor) - adjusted
        { x: 2, y: 20, z: 1, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 2, y: 21, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 2, y: 22, z: 1, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 3, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_trim }, // Extended pauldron
        { x: 3, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Gauntlets (hands) - Y=13
        { x: -2, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 2, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -2, y: 13, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 2, y: 13, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // === TRADITIONAL IRON HELMET (Y: 23-28) === Shortened enclosed great helm
        // Neck guard (aventail) - Y=23
        { x: 0, y: 23, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -1, y: 23, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 1, y: 23, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 0, y: 23, z: -1, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Main helmet body (great helm) - Y=24-26
        ...generateSquareLayer(24, 1, IRON_KNIGHT_COLORS.iron_plate),
        ...generateSquareLayer(25, 1, IRON_KNIGHT_COLORS.iron_highlight),
        ...generateSquareLayer(26, 1, IRON_KNIGHT_COLORS.iron_plate),
        
        // Helmet crown - Y=27
        { x: -1, y: 27, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 0, y: 27, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 1, y: 27, z: 0, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 0, y: 27, z: -1, c: IRON_KNIGHT_COLORS.iron_plate },
        
        // Helmet crest - Y=28
        { x: 0, y: 28, z: 0, c: IRON_KNIGHT_COLORS.mystical_glow },
        
        // === TRADITIONAL VISOR - ENCLOSED DESIGN ===
        // No visible face - only visor with black interior
        
        // Visor front plate (adjusted for new height)
        { x: 0, y: 24, z: 1, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: -1, y: 24, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 1, y: 24, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: 0, y: 25, z: 1, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -1, y: 25, z: 1, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 1, y: 25, z: 1, c: IRON_KNIGHT_COLORS.iron_trim },
        { x: 0, y: 26, z: 1, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -1, y: 26, z: 1, c: IRON_KNIGHT_COLORS.iron_base },
        { x: 1, y: 26, z: 1, c: IRON_KNIGHT_COLORS.iron_base },
        
        // Visor eye slits - narrow traditional slits
        { x: -1, y: 24, z: 2, c: IRON_KNIGHT_COLORS.visor_black }, // Left eye slit
        { x: 1, y: 24, z: 2, c: IRON_KNIGHT_COLORS.visor_black }, // Right eye slit
        { x: -1, y: 25, z: 2, c: IRON_KNIGHT_COLORS.visor_black }, // Left eye slit upper
        { x: 1, y: 25, z: 2, c: IRON_KNIGHT_COLORS.visor_black }, // Right eye slit upper
        
        // Black interior behind visor (no visible face)
        { x: 0, y: 24, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        { x: -1, y: 24, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        { x: 1, y: 24, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        { x: 0, y: 25, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        { x: -1, y: 25, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        { x: 1, y: 25, z: 0, c: IRON_KNIGHT_COLORS.visor_shadow },
        
        // Helmet breathing holes (small slits)
        { x: -1, y: 23, z: 1, c: IRON_KNIGHT_COLORS.visor_black },
        { x: 1, y: 23, z: 1, c: IRON_KNIGHT_COLORS.visor_black },
        
        // === LONGSWORD (following skeleton bow pattern) === Adjusted for new scale
        // Sword beside right hand - traditional longsword
        { x: 3, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_trim }, // Hilt
        { x: 3, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base }, // Guard
        { x: 3, y: 15, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight }, // Blade start
        { x: 3, y: 16, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 17, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 18, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 19, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 22, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight },
        { x: 3, y: 23, z: 0, c: IRON_KNIGHT_COLORS.iron_highlight }, // Blade tip
        
        // Sword crossguard and pommel
        { x: 3, y: 13, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow }, // Pommel
        { x: 4, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base }, // Crossguard
        { x: 2, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        
        // === HEATER SHIELD (left side) === Adjusted for new scale
        // Traditional knight's heater shield
        { x: -3, y: 14, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -3, y: 15, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -3, y: 16, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -3, y: 17, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -3, y: 18, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -3, y: 19, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -3, y: 20, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        { x: -3, y: 21, z: 0, c: IRON_KNIGHT_COLORS.iron_plate },
        { x: -3, y: 22, z: 0, c: IRON_KNIGHT_COLORS.iron_base },
        
        // Shield boss (center)
        { x: -3, y: 18, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow },
        
        // Shield rim
        { x: -3, y: 13, z: 0, c: IRON_KNIGHT_COLORS.iron_trim }, // Bottom
        { x: -3, y: 23, z: 0, c: IRON_KNIGHT_COLORS.iron_trim }, // Top"}
        
        // === CHAINMAIL HAUBERK (back protection) ===
        // Chainmail draping behind armor
        { x: 0, y: 13, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: 0, y: 12, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 0, y: 11, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: 0, y: 10, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 0, y: 9, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        
        // Chainmail sides
        { x: -1, y: 12, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 1, y: 12, z: -1, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: -1, y: 10, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        { x: 1, y: 10, z: -1, c: IRON_KNIGHT_COLORS.iron_dark },
        
        // === TRADITIONAL KNIGHT DETAILS ===
        // Pedestal corner stones
        { x: 2, y: 0, z: 2, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: -2, y: 0, z: 2, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: 2, y: 0, z: -2, c: IRON_KNIGHT_COLORS.iron_shadow },
        { x: -2, y: 0, z: -2, c: IRON_KNIGHT_COLORS.iron_shadow },
        
        // Tabard emblems on sides (heraldic displays)
        { x: 2, y: 11, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow },
        { x: -2, y: 11, z: 1, c: IRON_KNIGHT_COLORS.mystical_glow },
        
        // Armor joint shadows
        { x: 0, y: 8, z: 1, c: IRON_KNIGHT_COLORS.iron_shadow }, // Belt
        { x: 0, y: 14, z: 1, c: IRON_KNIGHT_COLORS.iron_shadow }, // Neck
    ];
}

/**
 * Create Traditional Iron Knight - IDENTICAL design for both truth and lie guardians
 * Following angel statue scale (VOXEL_SIZE * 5.12) and nairabos detail patterns
 * Traditional knight in full plate armor with enclosed helmet - no visible face
 * Player cannot visually distinguish between them to maintain mystery
 */
export function createArmoredKnightObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 999); // Unique seed for iron knights
    
    const knightType = options.knightType || 'truth';
    
    console.log(`[IronKnight] Creating ${knightType} traditional iron knight (angel statue scale)`);
    
    // Use smaller voxels for more detail, but larger overall scale
    const knightVoxelSize = VOXEL_SIZE * 3.0; // Smaller voxels for finer detail
    const baseGeometry = getOrCreateGeometry('iron_knight_voxel', () =>
        new THREE.BoxGeometry(knightVoxelSize, knightVoxelSize, knightVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();
    const geometriesByMaterial = {};
    
    // Generate traditional iron knight shape - IDENTICAL for both guardians
    const knightShape = generateTraditionalIronKnightShape();
    
    // Process each voxel - following angel statue weathering pattern
    knightShape.forEach(voxel => {
        const { x, y, z, c } = voxel;
        
        // Add iron weathering effect like angel statues (15% chance)
        let finalColor = c;
        if (rng() < 0.15) { // 15% chance for iron weathering
            const weatheringOptions = IRON_KNIGHT_COLORS.weathering;
            finalColor = weatheringOptions[Math.floor(rng() * weatheringOptions.length)];
        }
        
        // Get or create material for this color
        if (!geometriesByMaterial[finalColor]) {
            geometriesByMaterial[finalColor] = [];
        }
        
        // Position the voxel
        tempMatrix.makeTranslation(
            x * knightVoxelSize,
            y * knightVoxelSize,
            z * knightVoxelSize
        );
        
        // Clone geometry and apply transformation
        const voxelGeometry = baseGeometry.clone();
        voxelGeometry.applyMatrix4(tempMatrix);
        geometriesByMaterial[finalColor].push(voxelGeometry);
    });
    
    // Create merged meshes with traditional iron armor materials
    Object.entries(geometriesByMaterial).forEach(([colorHex, geometries]) => {
        if (geometries.length === 0) return;
        
        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
        
        // Create traditional iron armor materials
        let materialProps = {
            roughness: 0.6,  // Iron armor finish
            metalness: 0.8   // High metallic for iron
        };
        
        // Mystical glow elements (identical for both knights)
        if (colorHex === IRON_KNIGHT_COLORS.mystical_glow) {
            materialProps.emissive = new THREE.Color(`#${colorHex}`);
            materialProps.emissiveIntensity = 0.4; // Mystical glow
            materialProps.transparent = true;
            materialProps.opacity = 0.9;
        }
        
        // Black visor interior - no glow, pure black
        if (colorHex === IRON_KNIGHT_COLORS.visor_black || colorHex === IRON_KNIGHT_COLORS.visor_shadow) {
            materialProps.roughness = 1.0;
            materialProps.metalness = 0.0;
            materialProps.emissive = new THREE.Color(0x000000);
            materialProps.emissiveIntensity = 0.0;
        }
        
        const material = _getMaterialByHex_Cached(colorHex, materialProps);
        const mesh = new THREE.Mesh(mergedGeometry, material);
        
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        group.add(mesh);
    });
    
    // Add subtle mystical lighting - traditional knight scale
    const knightColor = parseInt(IRON_KNIGHT_COLORS.mystical_glow, 16);
    const eyeLight = new THREE.PointLight(knightColor, 1.0, knightVoxelSize * 12);
    eyeLight.position.set(0, 24 * knightVoxelSize, 2 * knightVoxelSize); // Visor level (Y=24-25)
    eyeLight.castShadow = false; // Soft mystical glow
    group.add(eyeLight);
    
    // Add ambient knightly aura (identical for both)
    const auraLight = new THREE.PointLight(knightColor, 0.3, knightVoxelSize * 8);
    auraLight.position.set(0, 17 * knightVoxelSize, 0); // Chest level (Y=17)
    auraLight.castShadow = false;
    group.add(auraLight);
    
    // Set up group properties following game object patterns
    group.userData = {
        ...(options.userData || {}),
        objectType: `${knightType}_knight`,
        isInteractable: true,
        interactionType: 'knight_chat',
        knightType: knightType,
        canSpeak: true,
        alwaysLies: knightType === 'lie', // Only behavioral difference
        hasAI: true,
        isEventObject: true,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false,
        isInteriorObject: true,
        voxelScale: knightVoxelSize,
        canDisappear: true,
        eyeLight: eyeLight,
        auraLight: auraLight,
        isAnimating: false,
        originalVoxels: knightShape.map(v => ({...v})) // For destruction consistency
    };
    
    group.name = `${knightType}_knight`;
    
    console.log(`[IronKnight] ✅ Created ${knightType} traditional iron knight with ${knightShape.length} voxels`);
    return group;
}

/**
 * Knight arm management - creates separate arm groups for animation during knight creation
 */
function createKnightArmGroups(knightGroup, armVoxels) {
    const leftArmGroup = new THREE.Group();
    const rightArmGroup = new THREE.Group();
    
    leftArmGroup.name = 'leftArmGroup';
    rightArmGroup.name = 'rightArmGroup';
    
    // Add arm groups to knight
    knightGroup.add(leftArmGroup);
    knightGroup.add(rightArmGroup);
    
    // Store references
    knightGroup.userData.leftArmGroup = leftArmGroup;
    knightGroup.userData.rightArmGroup = rightArmGroup;
    
    return { leftArmGroup, rightArmGroup };
}

/**
 * Get knight arm groups (now just retrieves the pre-created groups)
 */
function getKnightArmGroups(knightGroup) {
    const leftArmGroup = knightGroup.userData.leftArmGroup;
    const rightArmGroup = knightGroup.userData.rightArmGroup;
    
    return { 
        leftArmMeshes: leftArmGroup ? [leftArmGroup] : [],
        rightArmMeshes: rightArmGroup ? [rightArmGroup] : []
    };
}

/**
 * Idle arm animation - subtle whole knight movement like breathing
 */
export function startKnightIdleAnimation(knightGroup) {
    if (knightGroup.userData.isAnimating || knightGroup.userData.armsCrossed) return;
    
    console.log('[KnightAnimation] Starting idle animation for knight');
    
    knightGroup.userData.isAnimating = true;
    knightGroup.userData.animationType = 'idle';
    
    // Store original rotation and position if not stored
    if (!knightGroup.userData.originalTransform) {
        knightGroup.userData.originalTransform = {
            position: knightGroup.position.clone(),
            rotation: knightGroup.rotation.clone()
        };
    }
    
    const startTime = Date.now();
    
    const animate = () => {
        if (!knightGroup.userData.isAnimating || knightGroup.userData.animationType !== 'idle') return;
        
        const elapsed = Date.now() - startTime;
        const progress = (elapsed % 6000) / 6000; // 6 second cycle for more noticeable movement
        
        // Subtle breathing-like movement - gentle sway and slight rotation
        const breathingOffset = Math.sin(progress * Math.PI * 2) * 0.05; // Increased amplitude
        const swayOffset = Math.sin(progress * Math.PI * 2 + Math.PI/4) * 0.03; // Side sway
        
        // Apply breathing movement
        const originalPos = knightGroup.userData.originalTransform.position;
        const originalRot = knightGroup.userData.originalTransform.rotation;
        
        // Subtle position changes
        knightGroup.position.copy(originalPos);
        knightGroup.position.y += breathingOffset; // Gentle up/down movement
        knightGroup.position.x += swayOffset; // Gentle side sway
        
        // Subtle rotation changes (like shifting weight)
        knightGroup.rotation.copy(originalRot);
        knightGroup.rotation.z += swayOffset * 0.5; // Slight lean
        knightGroup.rotation.y += breathingOffset * 0.3; // Slight turn
        
        requestAnimationFrame(animate);
    };
    
    animate();
}

/**
 * Cross arms animation when player approaches - defensive stance
 */
export function crossKnightArms(knightGroup, duration = 1000) {
    if (knightGroup.userData.armsCrossed) return;
    
    console.log('[KnightAnimation] Knight taking defensive stance - player approached');
    
    knightGroup.userData.isAnimating = true;
    knightGroup.userData.animationType = 'crossing';
    knightGroup.userData.armsCrossed = true;
    
    // Store original transform if not stored
    if (!knightGroup.userData.originalTransform) {
        knightGroup.userData.originalTransform = {
            position: knightGroup.position.clone(),
            rotation: knightGroup.rotation.clone()
        };
    }
    
    const startTime = Date.now();
    
    const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out
        
        const originalPos = knightGroup.userData.originalTransform.position;
        const originalRot = knightGroup.userData.originalTransform.rotation;
        
        // Defensive stance animation - slight lean forward and more rigid posture
        knightGroup.position.copy(originalPos);
        knightGroup.rotation.copy(originalRot);
        
        // Lean slightly forward (defensive posture)
        knightGroup.rotation.x += easeProgress * 0.1; // Lean forward
        knightGroup.position.z += easeProgress * 0.2; // Step slightly forward
        
        // Slight scale increase to show tensing up
        const tenseFactor = 1 + easeProgress * 0.05; // 5% larger when tense
        knightGroup.scale.setScalar(tenseFactor);
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            knightGroup.userData.isAnimating = false;
            knightGroup.userData.animationType = 'crossed';
            console.log('[KnightAnimation] Defensive stance complete');
        }
    };
    
    animate();
}

/**
 * Uncross arms animation when player moves away - return to relaxed stance
 */
export function uncrossKnightArms(knightGroup, duration = 1000) {
    if (!knightGroup.userData.armsCrossed) return;
    
    console.log('[KnightAnimation] Knight relaxing stance - player moved away');
    
    knightGroup.userData.isAnimating = true;
    knightGroup.userData.animationType = 'uncrossing';
    knightGroup.userData.armsCrossed = false;
    
    const startTime = Date.now();
    
    const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out
        
        const originalPos = knightGroup.userData.originalTransform.position;
        const originalRot = knightGroup.userData.originalTransform.rotation;
        
        // Return to relaxed stance
        knightGroup.position.lerpVectors(knightGroup.position, originalPos, easeProgress);
        knightGroup.rotation.x = THREE.MathUtils.lerp(knightGroup.rotation.x, originalRot.x, easeProgress);
        knightGroup.rotation.y = THREE.MathUtils.lerp(knightGroup.rotation.y, originalRot.y, easeProgress);
        knightGroup.rotation.z = THREE.MathUtils.lerp(knightGroup.rotation.z, originalRot.z, easeProgress);
        
        // Return scale to normal
        const currentScale = knightGroup.scale.x;
        const newScale = THREE.MathUtils.lerp(currentScale, 1.0, easeProgress);
        knightGroup.scale.setScalar(newScale);
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        } else {
            knightGroup.userData.isAnimating = false;
            knightGroup.userData.animationType = 'idle';
            console.log('[KnightAnimation] Relaxed stance complete - returning to idle');
            
            // Restart idle animation
            setTimeout(() => startKnightIdleAnimation(knightGroup), 500);
        }
    };
    
    animate();
}

/**
 * Check proximity and update knight arm state
 */
export function updateKnightProximity(knightGroup, playerPosition, crossDistance = 5.0, uncrossDistance = 7.0) {
    if (!knightGroup || !playerPosition) return;
    
    const knightPosition = knightGroup.position;
    const distance = knightPosition.distanceTo(playerPosition);
    
    const shouldCross = distance < crossDistance;
    const shouldUncross = distance > uncrossDistance;
    
    if (shouldCross && !knightGroup.userData.armsCrossed) {
        crossKnightArms(knightGroup);
    } else if (shouldUncross && knightGroup.userData.armsCrossed) {
        uncrossKnightArms(knightGroup);
    }
}

/**
 * Traditional iron knight speaking animation - subtle knightly acknowledgment
 */
export function animateKnightSpeaking(knightGroup, duration = 3000) {
    if (!knightGroup.userData.eyeLight || !knightGroup.userData.auraLight) return;
    
    const { eyeLight, auraLight } = knightGroup.userData;
    const wasAnimating = knightGroup.userData.isAnimating;
    const previousAnimationType = knightGroup.userData.animationType;
    
    knightGroup.userData.isAnimating = true;
    knightGroup.userData.animationType = 'speaking';
    
    const originalEyeIntensity = eyeLight.intensity;
    const originalAuraIntensity = auraLight.intensity;
    
    // Traditional knight speaking animation
    const startTime = Date.now();
    const animate = () => {
        if (knightGroup.userData.animationType !== 'speaking') return;
        
        const elapsed = Date.now() - startTime;
        const progress = elapsed / duration;
        
        if (progress >= 1) {
            // Reset to original state
            eyeLight.intensity = originalEyeIntensity;
            auraLight.intensity = originalAuraIntensity;
            knightGroup.position.y = 0;
            knightGroup.rotation.x = 0;
            
            // Return to previous animation state
            knightGroup.userData.isAnimating = wasAnimating;
            knightGroup.userData.animationType = previousAnimationType || 'idle';
            
            // Restart idle if needed
            if (!wasAnimating || previousAnimationType === 'idle') {
                setTimeout(() => startKnightIdleAnimation(knightGroup), 500);
            }
            return;
        }
        
        // Subtle visor glow and chest heraldic pulse
        const knightlyPulse = 1 + Math.sin(progress * Math.PI * 4) * 0.3;
        eyeLight.intensity = originalEyeIntensity * knightlyPulse;
        auraLight.intensity = originalAuraIntensity * (1 + knightlyPulse * 0.2);
        
        // Very subtle stance shift (heavy armor movement)
        const heavyStance = Math.sin(progress * Math.PI * 2) * 0.02;
        knightGroup.position.y = heavyStance;
        
        // Slight forward nod (knightly acknowledgment)
        const nod = Math.sin(progress * Math.PI * 1.5) * 0.025;
        knightGroup.rotation.x = nod;
        
        requestAnimationFrame(animate);
    };
    
    animate();
}

/**
 * Create truth knight variant - IDENTICAL traditional iron knight appearance
 */
export function createTruthKnightObject(options = {}) {
    return createArmoredKnightObject({
        ...options,
        knightType: 'truth'
    });
}

/**
 * Create lie knight variant - IDENTICAL traditional iron knight appearance
 */
export function createLieKnightObject(options = {}) {
    return createArmoredKnightObject({
        ...options,
        knightType: 'lie'
    });
}