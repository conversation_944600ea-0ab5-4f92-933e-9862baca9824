import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

/**
 * Creates a voxel-based chess pawn object
 * Pawns are the smallest chess pieces with a simple pyramidal shape
 * White pawns use light stone colors, black pawns use dark obsidian colors
 */

// Material cache for performance
const materialCache = new Map();

function _getMaterialByHex_Cached(hexColor, materialProperties = {}) {
    if (materialCache.has(hexColor)) {
        return materialCache.get(hexColor);
    }
    
    const material = new THREE.MeshStandardMaterial({
        color: new THREE.Color(`#${hexColor}`),
        roughness: materialProperties.roughness || 0.7,
        metalness: materialProperties.metalness || 0.1,
        ...materialProperties
    });
    
    materialCache.set(hexColor, material);
    return material;
}

/**
 * Create a chess pawn with specified color
 * @param {Object} options - Configuration options
 * @param {string} options.color - 'white' or 'black'
 * @param {number} options.scale - Scale multiplier (default: 2.4)
 * @param {number} options.seed - Random seed for variation
 * @returns {THREE.Group} - The pawn 3D object
 */
export function createChessPawnObject(options = {}) {
    const { 
        color = 'white', 
        scale = 2.4,
        seed = 0
    } = options;
    
    const pawnGroup = new THREE.Group();
    pawnGroup.userData = { 
        type: 'chess_pawn', 
        chessColor: color,
        chessPiece: 'pawn'
    };
    
    // Color schemes
    const colorSchemes = {
        white: {
            base: ['FFFFFF', 'F0F0F0', 'E8E8E8'],
            accent: ['CCCCCC', 'D0D0D0']
        },
        black: {
            base: ['1A0A0A', '000000', '2A1A1A'],
            accent: ['8B0000', '660000']
        }
    };
    
    const scheme = colorSchemes[color];
    // Higher detail for chess pieces - smaller voxels
    const CHESS_DETAIL_FACTOR = 0.5; // 2x more detailed voxels
    const voxelScale = VOXEL_SIZE * scale * CHESS_DETAIL_FACTOR;
    
    // Simple seeded random for consistent variation
    const rng = (() => {
        let s = seed;
        return () => {
            s = Math.sin(s) * 10000;
            return s - Math.floor(s);
        };
    })();
    
    // Pawn shape: Simple pyramidal structure
    const pawnVoxels = [
        // Base layer (y=0) - 3x3 foundation
        { x: -1, y: 0, z: -1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 0, y: 0, z: -1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 1, y: 0, z: -1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: -1, y: 0, z: 0, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 0, y: 0, z: 0, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 1, y: 0, z: 0, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: -1, y: 0, z: 1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 0, y: 0, z: 1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        { x: 1, y: 0, z: 1, c: scheme.base[Math.floor(rng() * scheme.base.length)] },
        
        // Middle layer (y=1) - Cross pattern
        { x: 0, y: 1, z: -1, c: scheme.accent[Math.floor(rng() * scheme.accent.length)] },
        { x: -1, y: 1, z: 0, c: scheme.accent[Math.floor(rng() * scheme.accent.length)] },
        { x: 0, y: 1, z: 0, c: scheme.base[0] },
        { x: 1, y: 1, z: 0, c: scheme.accent[Math.floor(rng() * scheme.accent.length)] },
        { x: 0, y: 1, z: 1, c: scheme.accent[Math.floor(rng() * scheme.accent.length)] },
        
        // Upper layer (y=2) - Smaller cross
        { x: 0, y: 2, z: 0, c: scheme.base[0] },
        { x: 0, y: 2, z: -1, c: scheme.accent[0] },
        { x: 0, y: 2, z: 1, c: scheme.accent[0] },
        
        // Top layer (y=3) - Single voxel cap
        { x: 0, y: 3, z: 0, c: scheme.accent[0] }
    ];
    
    // Group voxels by material for optimization
    const voxelsByMaterial = new Map();
    
    for (const voxel of pawnVoxels) {
        if (!voxelsByMaterial.has(voxel.c)) {
            voxelsByMaterial.set(voxel.c, []);
        }
        voxelsByMaterial.get(voxel.c).push(voxel);
    }
    
    // Create merged geometry for each material
    for (const [hexColor, voxels] of voxelsByMaterial) {
        const geometries = [];
        
        for (const voxel of voxels) {
            const geometry = new THREE.BoxGeometry(voxelScale, voxelScale, voxelScale);
            geometry.translate(
                voxel.x * voxelScale,
                voxel.y * voxelScale,
                voxel.z * voxelScale
            );
            geometries.push(geometry);
        }
        
        if (geometries.length > 0) {
            const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometries);
            const material = _getMaterialByHex_Cached(hexColor, {
                roughness: color === 'black' ? 0.3 : 0.7,
                metalness: color === 'black' ? 0.2 : 0.1
            });
            
            const mesh = new THREE.Mesh(mergedGeometry, material);
            mesh.layers.set(0); // Ensure piece is on layer 0 for raycasting
            pawnGroup.add(mesh);
        }
    }
    
    // Add subtle glow for black pieces (hellish theme)
    if (color === 'black') {
        const glowGeometry = new THREE.SphereGeometry(voxelScale * 2, 8, 8);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0x330000,
            transparent: true,
            opacity: 0.15,
            side: THREE.BackSide
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        glowMesh.layers.set(0); // Ensure glow is on layer 0 for raycasting
        glowMesh.position.y = voxelScale * 1.5;
        pawnGroup.add(glowMesh);
    }
    
    // Position the pawn so its base is at y=0
    pawnGroup.position.y = 0;
    
    // Ensure the entire group is on layer 0 for raycasting
    pawnGroup.layers.set(0);
    
    console.log(`[chessPawnObject] Created ${color} chess pawn with ${pawnVoxels.length} voxels`);
    
    return pawnGroup;
}

/**
 * Helper function to create white pawn
 */
export function createWhiteChessPawn(options = {}) {
    return createChessPawnObject({ ...options, color: 'white' });
}

/**
 * Helper function to create black pawn
 */
export function createBlackChessPawn(options = {}) {
    return createChessPawnObject({ ...options, color: 'black' });
}