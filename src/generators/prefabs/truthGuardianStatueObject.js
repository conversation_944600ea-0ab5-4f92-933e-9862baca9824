import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Truth Guardian Statue - Always tells the truth
 * A majestic stone guardian with celestial features and glowing blue eyes
 */
export function createTruthGuardianStatueObject(options = {}) {
    const group = new THREE.Group();
    
    // Base materials
    const stoneMaterial = new THREE.MeshStandardMaterial({
        color: 0xD3D3D3, // Light gray stone
        roughness: 0.7,
        metalness: 0.1
    });
    
    const truthGlowMaterial = new THREE.MeshStandardMaterial({
        color: 0x4169E1, // Royal blue truth glow
        emissive: 0x1E3A8A,
        emissiveIntensity: 0.3,
        roughness: 0.4,
        metalness: 0.2
    });
    
    // Create statue base/pedestal
    const baseGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 3, 
        VOXEL_SIZE * 1, 
        VOXEL_SIZE * 3
    );
    const base = new THREE.Mesh(baseGeometry, stoneMaterial);
    base.position.set(0, VOXEL_SIZE * 0.5, 0);
    base.castShadow = true;
    base.receiveShadow = true;
    group.add(base);
    
    // Create guardian torso
    const torsoGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 2, 
        VOXEL_SIZE * 3, 
        VOXEL_SIZE * 1
    );
    const torso = new THREE.Mesh(torsoGeometry, stoneMaterial);
    torso.position.set(0, VOXEL_SIZE * 2.5, 0);
    torso.castShadow = true;
    group.add(torso);
    
    // Create guardian head
    const headGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 1.5, 
        VOXEL_SIZE * 1.5, 
        VOXEL_SIZE * 1
    );
    const head = new THREE.Mesh(headGeometry, stoneMaterial);
    head.position.set(0, VOXEL_SIZE * 4.75, 0);
    head.castShadow = true;
    group.add(head);
    
    // Create glowing truth eyes
    const eyeGeometry = new THREE.SphereGeometry(VOXEL_SIZE * 0.15, 8, 8);
    
    const leftEye = new THREE.Mesh(eyeGeometry, truthGlowMaterial);
    leftEye.position.set(-VOXEL_SIZE * 0.3, VOXEL_SIZE * 4.9, VOXEL_SIZE * 0.45);
    group.add(leftEye);
    
    const rightEye = new THREE.Mesh(eyeGeometry, truthGlowMaterial);
    rightEye.position.set(VOXEL_SIZE * 0.3, VOXEL_SIZE * 4.9, VOXEL_SIZE * 0.45);
    group.add(rightEye);
    
    // Create guardian arms
    const armGeometry = new THREE.BoxGeometry(
        VOXEL_SIZE * 0.8, 
        VOXEL_SIZE * 2.5, 
        VOXEL_SIZE * 0.8
    );
    
    const leftArm = new THREE.Mesh(armGeometry, stoneMaterial);
    leftArm.position.set(-VOXEL_SIZE * 1.4, VOXEL_SIZE * 2.5, 0);
    leftArm.castShadow = true;
    group.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, stoneMaterial);
    rightArm.position.set(VOXEL_SIZE * 1.4, VOXEL_SIZE * 2.5, 0);
    rightArm.castShadow = true;
    group.add(rightArm);
    
    // Create truth symbol on chest
    const symbolGeometry = new THREE.CylinderGeometry(
        VOXEL_SIZE * 0.3, 
        VOXEL_SIZE * 0.3, 
        VOXEL_SIZE * 0.1, 
        8
    );
    const truthSymbol = new THREE.Mesh(symbolGeometry, truthGlowMaterial);
    truthSymbol.position.set(0, VOXEL_SIZE * 3, VOXEL_SIZE * 0.55);
    truthSymbol.rotation.x = Math.PI / 2;
    group.add(truthSymbol);
    
    // Add truth aura light
    const truthLight = new THREE.PointLight(0x4169E1, 1.5, VOXEL_SIZE * 8);
    truthLight.position.set(0, VOXEL_SIZE * 5, 0);
    truthLight.castShadow = true;
    group.add(truthLight);
    
    // Set up group properties
    group.name = 'truth_guardian_statue';
    group.userData = {
        objectType: 'truth_guardian_statue',
        isInteractable: true,
        guardianType: 'truth',
        alwaysLies: false,
        canSpeak: true,
        ...options.userData
    };
    
    // Store references for animation
    group.userData.eyes = [leftEye, rightEye];
    group.userData.truthSymbol = truthSymbol;
    group.userData.truthLight = truthLight;
    
    return group;
}