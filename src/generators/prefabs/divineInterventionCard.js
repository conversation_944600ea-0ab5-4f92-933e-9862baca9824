import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Divine Intervention Card Prefab
 * Creates divine rays emanating from a celestial symbol with a holy aura representing divine protection
 */

// Divine intervention specific colors
const DIVINE_COLORS = {
    DIVINE_GOLD: 0xFFD700,       // Gold for divine rays
    CELESTIAL_WHITE: 0xF8F8FF,   // Ghost white for celestial energy
    HOLY_BLUE: 0x87CEEB,         // Sky blue for holy aura
    PURE_WHITE: 0xFFFFFF,        // Pure white for divine core
    ANGELIC_SILVER: 0xC0C0C0,    // Silver for angelic presence
    BLESSED_YELLOW: 0xFFFACD     // Lemon chiffon for blessed light
};

/**
 * Create a divine intervention card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The divine intervention card 3D model
 */
export function createDivineInterventionCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'DivineInterventionCard';

    // Materials
    const divineGoldMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.DIVINE_GOLD,
        emissive: DIVINE_COLORS.DIVINE_GOLD,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.9
    });

    const celestialWhiteMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.CELESTIAL_WHITE,
        emissive: DIVINE_COLORS.CELESTIAL_WHITE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const holyBlueMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.HOLY_BLUE,
        emissive: DIVINE_COLORS.HOLY_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.7
    });

    const pureWhiteMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.PURE_WHITE,
        emissive: DIVINE_COLORS.PURE_WHITE,
        emissiveIntensity: 1.0,
        transparent: true,
        opacity: 0.6
    });

    const angelicSilverMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.ANGELIC_SILVER,
        emissive: DIVINE_COLORS.ANGELIC_SILVER,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.5
    });

    const blessedYellowMaterial = new THREE.MeshLambertMaterial({
        color: DIVINE_COLORS.BLESSED_YELLOW,
        emissive: DIVINE_COLORS.BLESSED_YELLOW,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.4
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create divine rays voxels (emanating from center)
    const divineRaysVoxels = [
        // Central cross rays
        { x: 0, y: 4, z: 0, material: divineGoldMaterial },
        { x: 0, y: -4, z: 0, material: divineGoldMaterial },
        { x: 4, y: 0, z: 0, material: divineGoldMaterial },
        { x: -4, y: 0, z: 0, material: divineGoldMaterial },
        
        // Secondary rays
        { x: 0, y: 3, z: 0, material: celestialWhiteMaterial },
        { x: 0, y: -3, z: 0, material: celestialWhiteMaterial },
        { x: 3, y: 0, z: 0, material: celestialWhiteMaterial },
        { x: -3, y: 0, z: 0, material: celestialWhiteMaterial },
        
        // Diagonal rays
        { x: 3, y: 3, z: 0, material: celestialWhiteMaterial },
        { x: -3, y: 3, z: 0, material: celestialWhiteMaterial },
        { x: 3, y: -3, z: 0, material: celestialWhiteMaterial },
        { x: -3, y: -3, z: 0, material: celestialWhiteMaterial },
        
        // Extended rays
        { x: 0, y: 5, z: 0, material: holyBlueMaterial },
        { x: 0, y: -5, z: 0, material: holyBlueMaterial },
        { x: 5, y: 0, z: 0, material: holyBlueMaterial },
        { x: -5, y: 0, z: 0, material: holyBlueMaterial }
    ];

    // Create celestial symbol voxels (central cross/star)
    const celestialSymbolVoxels = [
        // Divine core
        { x: 0, y: 0, z: 0, material: pureWhiteMaterial },
        
        // Cross arms
        { x: 0, y: 1, z: 0, material: divineGoldMaterial },
        { x: 0, y: -1, z: 0, material: divineGoldMaterial },
        { x: 1, y: 0, z: 0, material: divineGoldMaterial },
        { x: -1, y: 0, z: 0, material: divineGoldMaterial },
        
        // Cross extensions
        { x: 0, y: 2, z: 0, material: celestialWhiteMaterial },
        { x: 0, y: -2, z: 0, material: celestialWhiteMaterial },
        { x: 2, y: 0, z: 0, material: celestialWhiteMaterial },
        { x: -2, y: 0, z: 0, material: celestialWhiteMaterial },
        
        // Divine points
        { x: 1, y: 1, z: 0, material: angelicSilverMaterial },
        { x: -1, y: 1, z: 0, material: angelicSilverMaterial },
        { x: 1, y: -1, z: 0, material: angelicSilverMaterial },
        { x: -1, y: -1, z: 0, material: angelicSilverMaterial }
    ];

    // Create holy aura voxels (surrounding glow)
    const holyAuraVoxels = [
        // Inner aura ring
        { x: -2.5, y: 2.5, z: 0, material: holyBlueMaterial },
        { x: 2.5, y: 2.5, z: 0, material: holyBlueMaterial },
        { x: -2.5, y: -2.5, z: 0, material: holyBlueMaterial },
        { x: 2.5, y: -2.5, z: 0, material: holyBlueMaterial },
        
        // Outer aura points
        { x: -1.5, y: 3.5, z: 0, material: celestialWhiteMaterial },
        { x: 1.5, y: 3.5, z: 0, material: celestialWhiteMaterial },
        { x: -1.5, y: -3.5, z: 0, material: celestialWhiteMaterial },
        { x: 1.5, y: -3.5, z: 0, material: celestialWhiteMaterial },
        { x: -3.5, y: 1.5, z: 0, material: celestialWhiteMaterial },
        { x: 3.5, y: 1.5, z: 0, material: celestialWhiteMaterial },
        { x: -3.5, y: -1.5, z: 0, material: celestialWhiteMaterial },
        { x: 3.5, y: -1.5, z: 0, material: celestialWhiteMaterial },
        
        // Blessed light particles
        { x: -4, y: 2, z: 0, material: blessedYellowMaterial },
        { x: 4, y: 2, z: 0, material: blessedYellowMaterial },
        { x: -4, y: -2, z: 0, material: blessedYellowMaterial },
        { x: 4, y: -2, z: 0, material: blessedYellowMaterial },
        { x: -2, y: 4, z: 0, material: blessedYellowMaterial },
        { x: 2, y: 4, z: 0, material: blessedYellowMaterial },
        { x: -2, y: -4, z: 0, material: blessedYellowMaterial },
        { x: 2, y: -4, z: 0, material: blessedYellowMaterial },
        
        // Angelic presence
        { x: -1, y: 4.5, z: 0, material: angelicSilverMaterial },
        { x: 1, y: 4.5, z: 0, material: angelicSilverMaterial },
        { x: -1, y: -4.5, z: 0, material: angelicSilverMaterial },
        { x: 1, y: -4.5, z: 0, material: angelicSilverMaterial },
        { x: -4.5, y: 1, z: 0, material: angelicSilverMaterial },
        { x: 4.5, y: 1, z: 0, material: angelicSilverMaterial },
        { x: -4.5, y: -1, z: 0, material: angelicSilverMaterial },
        { x: 4.5, y: -1, z: 0, material: angelicSilverMaterial }
    ];

    // Create all voxels
    [...divineRaysVoxels, ...celestialSymbolVoxels, ...holyAuraVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8, // Slightly compact
            voxel.y * VOXEL_SIZE * 0.8, // Slightly compact
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === divineGoldMaterial ? 'divine_rays' :
                                 voxel.material === pureWhiteMaterial ? 'divine_core' :
                                 voxel.material === celestialWhiteMaterial ? 'celestial_energy' :
                                 voxel.material === holyBlueMaterial ? 'holy_aura' :
                                 voxel.material === angelicSilverMaterial ? 'angelic_presence' : 'blessed_light';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        divinePhase: 0,
        celestialPhase: 0,
        blessedPhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update divine intervention card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateDivineInterventionCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Divine radiance phase
    cardGroup.userData.divinePhase += deltaTime * 2.0;
    const divineValue = (Math.sin(cardGroup.userData.divinePhase) + 1) * 0.5; // 0 to 1

    // Celestial energy phase
    cardGroup.userData.celestialPhase += deltaTime * 2.5;
    const celestialValue = (Math.sin(cardGroup.userData.celestialPhase) + 1) * 0.5; // 0 to 1

    // Blessed light phase
    cardGroup.userData.blessedPhase += deltaTime * 3.0;
    const blessedValue = (Math.sin(cardGroup.userData.blessedPhase) + 1) * 0.5; // 0 to 1

    // Apply divine intervention animations to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different animation patterns for different divine elements
            switch (child.userData.voxelType) {
                case 'divine_rays':
                    child.material.opacity = baseOpacity * (1.0 + divineValue * 0.0); // Always visible
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 1.0 + divineValue * 0.8;
                    }
                    // Ray emanation effect
                    const rayDistance = Math.sqrt(child.position.x * child.position.x + child.position.y * child.position.y);
                    if (rayDistance > 0) {
                        const normalizedX = child.position.x / rayDistance;
                        const normalizedY = child.position.y / rayDistance;
                        const emanationOffset = Math.sin(time * 2 + rayDistance * 0.5) * 0.006;
                        child.position.x += normalizedX * emanationOffset;
                        child.position.y += normalizedY * emanationOffset;
                    }
                    break;
                case 'divine_core':
                    child.material.opacity = baseOpacity * (0.6 + divineValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 1.0 + divineValue * 1.0;
                    }
                    // Core pulsing
                    const corePulseScale = 1.0 + divineValue * 0.3;
                    child.scale.setScalar(corePulseScale);
                    break;
                case 'celestial_energy':
                    child.material.opacity = baseOpacity * (0.8 + celestialValue * 0.4);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.9 + celestialValue * 0.8;
                    }
                    // Celestial energy flowing
                    child.position.y += Math.sin(time * 2.5 + child.position.x * 2) * 0.004;
                    child.position.x += Math.cos(time * 2.2 + child.position.y * 2) * 0.003;
                    break;
                case 'holy_aura':
                    child.material.opacity = baseOpacity * (0.7 + celestialValue * 0.5);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + celestialValue * 0.6;
                    }
                    // Holy aura undulation
                    child.position.y += Math.sin(time * 1.8 + child.position.x * 1.5) * 0.005;
                    child.position.x += Math.cos(time * 2.0 + child.position.y * 1.5) * 0.004;
                    break;
                case 'angelic_presence':
                    child.material.opacity = baseOpacity * (0.5 + blessedValue * 0.7);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.7 + blessedValue * 0.6;
                    }
                    // Angelic floating motion
                    child.position.y += Math.sin(time * 3 + child.position.x * 3) * 0.006;
                    child.position.x += Math.cos(time * 2.8 + child.position.y * 2.5) * 0.005;
                    break;
                case 'blessed_light':
                    child.material.opacity = baseOpacity * (0.4 + blessedValue * 0.8);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.8 + blessedValue * 0.8;
                    }
                    // Blessed light twinkling
                    const twinkleScale = 1.0 + blessedValue * 0.4;
                    child.scale.setScalar(twinkleScale);
                    // Twinkling motion
                    child.position.y += Math.sin(time * 5 + child.position.x * 4) * 0.003;
                    child.position.x += Math.cos(time * 4.5 + child.position.y * 3) * 0.002;
                    break;
            }
        }
    });

    // Overall divine intervention effect (gentle ascension)
    const divineAscension = Math.sin(time * 1.3) * 0.005;
    cardGroup.position.y += divineAscension * 0.8;
    cardGroup.position.x += divineAscension * 0.3;
    
    // Gentle divine rotation
    cardGroup.rotation.z = Math.sin(time * 0.8) * 0.05;
}

// Export the divine intervention card data for the loot system
export const DIVINE_INTERVENTION_CARD_DATA = {
    name: 'Divine Intervention',
    description: 'Calls upon divine protection that automatically shields you from fatal damage and provides temporary invulnerability with holy light.',
    category: 'card',
    rarity: 'legendary',
    effect: 'divine_intervention',
    effectValue: 15,
    createFunction: createDivineInterventionCard,
    updateFunction: updateDivineInterventionCardAnimation,
    voxelModel: 'divine_intervention_card',
    glow: {
        color: 0xFFD700,
        intensity: 1.4
    }
};

export default createDivineInterventionCard;