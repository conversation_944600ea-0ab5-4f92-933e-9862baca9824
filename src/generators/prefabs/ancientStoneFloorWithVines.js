import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE, ENVIRONMENT_PIXEL_SCALE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

/**
 * Ancient Stone Floor with Vines
 *
 * Creates ancient weathered stone brick floor with creeping vines and moss.
 * Perfect for the guardians of lies room to create an old, abandoned hall atmosphere.
 */

// Ancient stone floor materials - faded yellowish weathered stone (matching screenshot)
const ANCIENT_STONE_MATERIALS = [
    _getMaterialByHex_Cached('8B8B7A'), // Faded yellowish stone (primary)
    _getMaterialByHex_Cached('A0A089'), // Light faded yellow stone (secondary)
    _getMaterialByHex_Cached('6B6B5A'), // Darker faded stone (shadows/cracks)
    _getMaterialByHex_Cached('B5B59E'), // Lightest faded yellow (highlights)
    _getMaterialByHex_Cached('5A5A4A'), // Very dark weathered stone (deep mortar)
    _getMaterialByHex_Cached('C0C0A8'), // Very light faded yellow (worn edges)
];

// Vine and moss materials - natural greens and browns
const VINE_MATERIALS = [
    _getMaterialByHex_Cached('2D4A2B'), // Dark forest green (main vines)
    _getMaterialByHex_Cached('3E5C3A'), // Medium green (vine leaves)
    _getMaterialByHex_Cached('1F3A1E'), // Very dark green (vine shadows)
    _getMaterialByHex_Cached('4B6B47'), // Lighter green (young growth)
    _getMaterialByHex_Cached('228B22'), // Forest green (healthy vines)
    _getMaterialByHex_Cached('6B8E23'), // Olive green (moss patches)
    _getMaterialByHex_Cached('8B4513'), // Saddle brown (vine stems)
    _getMaterialByHex_Cached('654321'), // Dark brown (thick vine roots)
];

/**
 * Get random ancient stone material
 */
function getRandomStoneMat() {
    const rand = Math.random();
    if (rand < 0.35) return ANCIENT_STONE_MATERIALS[0]; // Primary dark stone
    if (rand < 0.65) return ANCIENT_STONE_MATERIALS[1]; // Medium stone
    if (rand < 0.80) return ANCIENT_STONE_MATERIALS[3]; // Light highlights
    if (rand < 0.90) return ANCIENT_STONE_MATERIALS[2]; // Dark shadows
    if (rand < 0.95) return ANCIENT_STONE_MATERIALS[5]; // Worn edges
    return ANCIENT_STONE_MATERIALS[4]; // Deep mortar
}

/**
 * Get random vine/moss material
 */
function getRandomVineMat() {
    const rand = Math.random();
    if (rand < 0.25) return VINE_MATERIALS[0]; // Dark forest green
    if (rand < 0.45) return VINE_MATERIALS[1]; // Medium green
    if (rand < 0.60) return VINE_MATERIALS[4]; // Forest green
    if (rand < 0.75) return VINE_MATERIALS[5]; // Olive moss
    if (rand < 0.85) return VINE_MATERIALS[3]; // Light green
    if (rand < 0.92) return VINE_MATERIALS[6]; // Brown stems
    if (rand < 0.97) return VINE_MATERIALS[7]; // Dark brown roots
    return VINE_MATERIALS[2]; // Very dark green
}

/**
 * Creates ancient stone floor with creeping vines and moss
 * @param {number} width World units width (X-axis).
 * @param {number} depth World units depth (Z-axis).
 * @param {object} roomData The room data object containing the ID for seeding.
 * @returns {THREE.Group} The merged floor group with stones and vines.
 */
export function createAncientStoneFloorWithVines(width, depth, roomData) {
    console.log(`[AncientStoneFloorWithVines] Creating floor: ${width}x${depth}`);

    const geometriesByMaterial = {};
    const addGeometry = (geometry, material, matrix) => {
        const colorHex = material.color.getHexString();
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const transformedGeometry = geometry.clone();
        transformedGeometry.applyMatrix4(matrix);
        geometriesByMaterial[colorHex].push(transformedGeometry);
    };
    const tempMatrix = new THREE.Matrix4();

    // Use environment scale for base coverage
    const FLOOR_SCALE = ENVIRONMENT_PIXEL_SCALE * 2;

    // Grid dimensions
    const numX = Math.ceil(width / VOXEL_SIZE);
    const numZ = Math.ceil(depth / VOXEL_SIZE);
    const numX_env = Math.ceil(numX / FLOOR_SCALE);
    const numZ_env = Math.ceil(numZ / FLOOR_SCALE);

    const floorY = 0; // Base floor Y position
    const vineOffsetY = VOXEL_SIZE * 0.4; // Much higher offset for bright green vines to eliminate flickering

    // Vine coverage parameters
    const vineSpawnProbability = 0.35; // 35% chance for vine coverage
    const vineClusterSize = 2; // Size of vine clusters
    const mossSpawnProbability = 0.20; // 20% chance for moss patches

    // Voxel sizing
    const floorVoxelSize = VOXEL_SIZE * FLOOR_SCALE;
    const floorVoxelHeight = VOXEL_SIZE * 0.6; // Slightly thinner base stones
    const vineVoxelHeight = VOXEL_SIZE * 0.15; // Thin vine layer

    // Centering offsets
    const offsetX = (numX - 1) * VOXEL_SIZE / 2;
    const offsetZ = (numZ - 1) * VOXEL_SIZE / 2;
    
    const stoneVoxelGeo = getOrCreateGeometry(
        `ancient_stone_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize, floorVoxelHeight, floorVoxelSize)
    );

    const vineVoxelGeo = getOrCreateGeometry(
        `vine_overlay_${floorVoxelSize.toFixed(4)}`,
        () => new THREE.BoxGeometry(floorVoxelSize * 0.8, vineVoxelHeight, floorVoxelSize * 0.8)
    );

    // Use seeded PRNG for consistent patterns
    const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
    const random = mulberry32(roomSeed);

    let voxelsAdded = 0;

    // --- Generate Base Stone Floor ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            const baseX = ex * floorVoxelSize - offsetX;
            const baseZ = ez * floorVoxelSize - offsetZ;

            const stoneMaterial = getRandomStoneMat();

            // Add slight height variation for worn stone texture
            const heightVariation = (random() - 0.5) * 0.05;
            const finalY = floorY + heightVariation;

            tempMatrix.makeTranslation(baseX, finalY, baseZ);
            addGeometry(stoneVoxelGeo, stoneMaterial, tempMatrix);
            voxelsAdded++;
        }
    }

    // --- Generate Vine Clusters ---
    // Create natural vine paths that spread across the floor
    const vineSeeds = [];
    for (let i = 0; i < Math.floor(numX_env * numZ_env * 0.1); i++) {
        vineSeeds.push({
            x: Math.floor(random() * numX_env),
            z: Math.floor(random() * numZ_env),
            spread: 3 + Math.floor(random() * 4) // Vine spread radius
        });
    }

    // Grow vines from seeds using organic spreading
    vineSeeds.forEach(seed => {
        const vinePositions = new Set();
        const queue = [{ x: seed.x, z: seed.z, distance: 0 }];
        
        while (queue.length > 0) {
            const current = queue.shift();
            const key = `${current.x},${current.z}`;
            
            if (vinePositions.has(key) || current.distance > seed.spread) continue;
            if (current.x < 0 || current.x >= numX_env || current.z < 0 || current.z >= numZ_env) continue;
            
            vinePositions.add(key);
            
            // Spread to adjacent positions with decreasing probability
            const spreadChance = 0.7 - (current.distance / seed.spread) * 0.4;
            if (random() < spreadChance) {
                const directions = [
                    { dx: 1, dz: 0 }, { dx: -1, dz: 0 },
                    { dx: 0, dz: 1 }, { dx: 0, dz: -1 },
                    { dx: 1, dz: 1 }, { dx: -1, dz: -1 },
                    { dx: 1, dz: -1 }, { dx: -1, dz: 1 }
                ];
                
                directions.forEach(dir => {
                    if (random() < 0.4) { // 40% chance to spread in each direction
                        queue.push({
                            x: current.x + dir.dx,
                            z: current.z + dir.dz,
                            distance: current.distance + 1
                        });
                    }
                });
            }
        }
        
        // Place vine voxels
        vinePositions.forEach(posKey => {
            const [ex, ez] = posKey.split(',').map(Number);
            const baseX = ex * floorVoxelSize - offsetX;
            const baseZ = ez * floorVoxelSize - offsetZ;
            
            const vineMaterial = getRandomVineMat();
            
            // Add random offset for organic vine placement
            const offsetVariationX = (random() - 0.5) * 0.3;
            const offsetVariationZ = (random() - 0.5) * 0.3;
            
            // Add height variation to prevent z-fighting between adjacent vines
            const heightVariation = (random() - 0.5) * 0.1 * VOXEL_SIZE; // Slight height variation
            
            tempMatrix.makeTranslation(
                baseX + offsetVariationX,
                floorY + vineOffsetY + heightVariation,
                baseZ + offsetVariationZ
            );
            addGeometry(vineVoxelGeo, vineMaterial, tempMatrix);
            voxelsAdded++;
        });
    });

    // --- Generate Random Moss Patches ---
    for (let ez = 0; ez < numZ_env; ez++) {
        for (let ex = 0; ex < numX_env; ex++) {
            if (random() < mossSpawnProbability) {
                const baseX = ex * floorVoxelSize - offsetX;
                const baseZ = ez * floorVoxelSize - offsetZ;
                
                const mossMaterial = VINE_MATERIALS[5]; // Olive moss color
                
                // Small random offset for natural moss placement
                const offsetVariationX = (random() - 0.5) * 0.2;
                const offsetVariationZ = (random() - 0.5) * 0.2;
                
                // Add height variation to moss patches too
                const mossHeightVariation = (random() - 0.5) * 0.08 * VOXEL_SIZE; // Smaller variation for moss
                
                tempMatrix.makeTranslation(
                    baseX + offsetVariationX,
                    floorY + vineOffsetY * 0.5 + mossHeightVariation, // Moss sits lower than vines with variation
                    baseZ + offsetVariationZ
                );
                addGeometry(vineVoxelGeo, mossMaterial, tempMatrix);
                voxelsAdded++;
            }
        }
    }

    console.log(`[AncientStoneFloorWithVines] Generated ${voxelsAdded} floor voxels with ${vineSeeds.length} vine clusters`);

    // --- Merge Geometries by Material ---
    const finalGroup = new THREE.Group();

    for (const colorHex in geometriesByMaterial) {
        const originalMaterial = _getMaterialByHex_Cached(colorHex);
        if (!originalMaterial) {
            console.warn(`Material not found for ancient stone floor: ${colorHex}`);
            continue;
        }

        // Clone material to apply settings without modifying shared instance
        const finalMaterial = originalMaterial.clone();
        
        // Apply polygon offset to prevent z-fighting
        finalMaterial.polygonOffset = true;
        finalMaterial.polygonOffsetFactor = -1.0;
        finalMaterial.polygonOffsetUnits = -1.0;

        const mergedGeometry = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[colorHex], false);
        if (mergedGeometry) {
            const mesh = new THREE.Mesh(mergedGeometry, finalMaterial);
            mesh.castShadow = false; // Floors don't cast shadows
            mesh.receiveShadow = true; // But they receive shadows
            mesh.name = 'ancientStoneFloorWithVines';
            mesh.userData.isFloor = true;
            mesh.userData.floorType = 'ancient_stone_with_vines';
            finalGroup.add(mesh);
        } else {
            console.warn(`Failed to merge geometry for ancient stone floor, material: ${colorHex}`);
        }
    }

    // --- Set Group Properties ---
    finalGroup.userData.floorType = 'ancient_stone_with_vines';
    finalGroup.userData.isFloor = true;
    finalGroup.name = 'ancientStoneFloorWithVines';

    // Set shadow properties for entire group
    finalGroup.castShadow = false;
    finalGroup.receiveShadow = true;

    console.log(`[AncientStoneFloorWithVines] ✅ Generated ancient stone floor with vines: ${voxelsAdded} voxels, ${Object.keys(geometriesByMaterial).length} materials`);

    return finalGroup;
}