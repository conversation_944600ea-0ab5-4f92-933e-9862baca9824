import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Tornado Card Prefab
 * Creates a swirling tornado that moves across the battlefield, dealing damage to enemies it touches
 */

// Tornado specific colors
const TORNADO_COLORS = {
    WIND_GRAY: 0x708090,          // Main tornado funnel
    STORM_BLUE: 0x4682B4,         // Storm clouds
    DEBRIS_BROWN: 0x8B7355,       // Flying debris
    LIGHTNING_YELLOW: 0xFFFF00,   // Electric energy
    DUST_TAN: 0xD2B48C,          // Dust clouds
    WIND_WHITE: 0xF5F5F5,        // Wind streams
    VORTEX_DARK: 0x2F4F4F,       // Dark vortex core
    ENERGY_CYAN: 0x00FFFF         // Wind energy
};

/**
 * Create a tornado card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The tornado card 3D model
 */
export function createTornadoCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'TornadoCard';

    // Tornado materials
    const windGrayMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.WIND_GRAY,
        emissive: TORNADO_COLORS.WIND_GRAY,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.8
    });

    const stormBlueMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.STORM_BLUE,
        emissive: TORNADO_COLORS.STORM_BLUE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.7
    });

    const debrisBrownMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.DEBRIS_BROWN,
        emissive: TORNADO_COLORS.DEBRIS_BROWN,
        emissiveIntensity: 0.15,
        transparent: true,
        opacity: 0.9
    });

    const lightningYellowMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.LIGHTNING_YELLOW,
        emissive: TORNADO_COLORS.LIGHTNING_YELLOW,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.6
    });

    const dustTanMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.DUST_TAN,
        emissive: TORNADO_COLORS.DUST_TAN,
        emissiveIntensity: 0.25,
        transparent: true,
        opacity: 0.6
    });

    const windWhiteMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.WIND_WHITE,
        emissive: TORNADO_COLORS.WIND_WHITE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.5
    });

    const vortexDarkMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.VORTEX_DARK,
        emissive: TORNADO_COLORS.VORTEX_DARK,
        emissiveIntensity: 0.1,
        transparent: true,
        opacity: 0.9
    });

    const energyCyanMaterial = new THREE.MeshLambertMaterial({
        color: TORNADO_COLORS.ENERGY_CYAN,
        emissive: TORNADO_COLORS.ENERGY_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Tornado funnel (main spiral structure)
    const tornadoFunnelVoxels = [
        // Base of tornado (widest part)
        { x: -0.16, y: -0.20, z: 0.0, material: windGrayMaterial },
        { x: -0.08, y: -0.20, z: 0.0, material: vortexDarkMaterial },
        { x: 0.0, y: -0.20, z: 0.0, material: windGrayMaterial },
        { x: 0.08, y: -0.20, z: 0.0, material: vortexDarkMaterial },
        { x: 0.16, y: -0.20, z: 0.0, material: windGrayMaterial },
        
        // Second layer (slightly narrower)
        { x: -0.12, y: -0.12, z: 0.04, material: stormBlueMaterial },
        { x: -0.04, y: -0.12, z: -0.04, material: vortexDarkMaterial },
        { x: 0.04, y: -0.12, z: 0.04, material: stormBlueMaterial },
        { x: 0.12, y: -0.12, z: -0.04, material: vortexDarkMaterial },
        
        // Third layer (spiral pattern)
        { x: -0.08, y: -0.04, z: -0.08, material: windGrayMaterial },
        { x: 0.0, y: -0.04, z: 0.0, material: vortexDarkMaterial },
        { x: 0.08, y: -0.04, z: 0.08, material: windGrayMaterial },
        
        // Fourth layer (tighter spiral)
        { x: -0.06, y: 0.04, z: 0.06, material: stormBlueMaterial },
        { x: 0.06, y: 0.04, z: -0.06, material: windGrayMaterial },
        
        // Top layer (narrow funnel tip)
        { x: -0.04, y: 0.12, z: -0.04, material: vortexDarkMaterial },
        { x: 0.04, y: 0.12, z: 0.04, material: stormBlueMaterial },
        
        // Peak
        { x: 0.0, y: 0.20, z: 0.0, material: windGrayMaterial }
    ];

    // Flying debris (objects caught in tornado)
    const flyingDebrisVoxels = [
        // Large debris pieces
        { x: -0.24, y: 0.08, z: 0.12, material: debrisBrownMaterial },
        { x: 0.22, y: 0.16, z: -0.10, material: debrisBrownMaterial },
        { x: -0.18, y: 0.24, z: -0.14, material: debrisBrownMaterial },
        { x: 0.20, y: 0.32, z: 0.08, material: debrisBrownMaterial },
        
        // Medium debris pieces
        { x: -0.28, y: -0.04, z: -0.08, material: dustTanMaterial },
        { x: 0.26, y: 0.02, z: 0.12, material: dustTanMaterial },
        { x: -0.22, y: 0.12, z: 0.16, material: dustTanMaterial },
        { x: 0.24, y: 0.20, z: -0.12, material: dustTanMaterial },
        { x: -0.20, y: 0.28, z: -0.06, material: dustTanMaterial },
        { x: 0.18, y: 0.36, z: 0.14, material: dustTanMaterial },
        
        // Small debris pieces
        { x: -0.32, y: 0.06, z: 0.04, material: debrisBrownMaterial },
        { x: 0.30, y: 0.14, z: -0.16, material: debrisBrownMaterial },
        { x: -0.26, y: 0.22, z: 0.18, material: debrisBrownMaterial },
        { x: 0.28, y: 0.30, z: -0.04, material: debrisBrownMaterial },
        { x: -0.24, y: 0.38, z: -0.10, material: debrisBrownMaterial },
        { x: 0.22, y: 0.46, z: 0.12, material: debrisBrownMaterial }
    ];

    // Wind streams (visible air currents)
    const windStreamsVoxels = [
        // Outer wind streams
        { x: -0.20, y: 0.0, z: -0.20, material: windWhiteMaterial },
        { x: 0.20, y: 0.0, z: 0.20, material: windWhiteMaterial },
        { x: -0.20, y: 0.0, z: 0.20, material: windWhiteMaterial },
        { x: 0.20, y: 0.0, z: -0.20, material: windWhiteMaterial },
        
        // Mid-level wind streams
        { x: -0.14, y: 0.08, z: -0.14, material: energyCyanMaterial },
        { x: 0.14, y: 0.08, z: 0.14, material: energyCyanMaterial },
        { x: -0.14, y: 0.08, z: 0.14, material: energyCyanMaterial },
        { x: 0.14, y: 0.08, z: -0.14, material: energyCyanMaterial },
        
        // Upper wind streams
        { x: -0.10, y: 0.16, z: -0.10, material: windWhiteMaterial },
        { x: 0.10, y: 0.16, z: 0.10, material: windWhiteMaterial },
        { x: -0.10, y: 0.16, z: 0.10, material: windWhiteMaterial },
        { x: 0.10, y: 0.16, z: -0.10, material: windWhiteMaterial },
        
        // Top wind streams
        { x: -0.06, y: 0.24, z: -0.06, material: energyCyanMaterial },
        { x: 0.06, y: 0.24, z: 0.06, material: energyCyanMaterial },
        { x: -0.06, y: 0.24, z: 0.06, material: energyCyanMaterial },
        { x: 0.06, y: 0.24, z: -0.06, material: energyCyanMaterial }
    ];

    // Lightning energy (electrical tornado energy)
    const lightningEnergyVoxels = [
        // Lightning bolts in tornado
        { x: -0.12, y: 0.10, z: 0.0, material: lightningYellowMaterial },
        { x: 0.12, y: 0.18, z: 0.0, material: lightningYellowMaterial },
        { x: 0.0, y: 0.26, z: -0.12, material: lightningYellowMaterial },
        { x: 0.0, y: 0.34, z: 0.12, material: lightningYellowMaterial },
        
        // Energy crackling around tornado
        { x: -0.16, y: 0.06, z: -0.16, material: lightningYellowMaterial },
        { x: 0.16, y: 0.14, z: 0.16, material: lightningYellowMaterial },
        { x: -0.16, y: 0.22, z: 0.16, material: lightningYellowMaterial },
        { x: 0.16, y: 0.30, z: -0.16, material: lightningYellowMaterial },
        
        // Core energy
        { x: 0.0, y: 0.0, z: 0.0, material: lightningYellowMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: lightningYellowMaterial },
        { x: 0.0, y: 0.16, z: 0.0, material: lightningYellowMaterial }
    ];

    // Create tornado funnel group
    const tornadoFunnelGroup = new THREE.Group();
    tornadoFunnelGroup.name = 'tornadoFunnel';

    // Add tornado funnel voxels
    tornadoFunnelVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.funnelPhase = index * 0.1; // Stagger animation
        tornadoFunnelGroup.add(mesh);
    });

    // Create flying debris group
    const flyingDebrisGroup = new THREE.Group();
    flyingDebrisGroup.name = 'flyingDebris';

    // Add flying debris voxels
    flyingDebrisVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.debrisPhase = index * 0.2; // Stagger animation
        flyingDebrisGroup.add(mesh);
    });

    // Create wind streams group
    const windStreamsGroup = new THREE.Group();
    windStreamsGroup.name = 'windStreams';

    // Add wind streams voxels
    windStreamsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.windPhase = index * 0.15; // Stagger animation
        windStreamsGroup.add(mesh);
    });

    // Create lightning energy group
    const lightningEnergyGroup = new THREE.Group();
    lightningEnergyGroup.name = 'lightningEnergy';

    // Add lightning energy voxels
    lightningEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.lightningPhase = index * 0.12; // Stagger animation
        lightningEnergyGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(tornadoFunnelGroup);
    cardGroup.add(flyingDebrisGroup);
    cardGroup.add(windStreamsGroup);
    cardGroup.add(lightningEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        tornadoSpin: 0,
        debrisSwirl: 0,
        windFlow: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update tornado card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateTornadoCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.tornadoSpin += deltaTime * 8.0; // Fast tornado rotation
    cardGroup.userData.debrisSwirl += deltaTime * 5.0; // Debris swirling speed
    cardGroup.userData.windFlow += deltaTime * 6.0; // Wind flow speed

    const time = cardGroup.userData.animationTime;
    const tornadoSpin = cardGroup.userData.tornadoSpin;
    const debrisSwirl = cardGroup.userData.debrisSwirl;
    const windFlow = cardGroup.userData.windFlow;

    // Animate tornado funnel (spinning vortex)
    const tornadoFunnelGroup = cardGroup.getObjectByName('tornadoFunnel');
    if (tornadoFunnelGroup) {
        // Main tornado rotation
        tornadoFunnelGroup.rotation.y = tornadoSpin;
        
        // Funnel undulation (breathing effect)
        const funnelPulse = 1.0 + Math.sin(tornadoSpin * 0.5) * 0.1;
        tornadoFunnelGroup.scale.setScalar(funnelPulse);
        
        // Individual funnel voxel motion
        tornadoFunnelGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.funnelPhase !== undefined) {
                const funnelTime = tornadoSpin + mesh.userData.funnelPhase;
                
                // Spiral motion within funnel
                const spiralRadius = 0.001;
                const spiralX = Math.cos(funnelTime * 3.0) * spiralRadius;
                const spiralZ = Math.sin(funnelTime * 3.0) * spiralRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + spiralX;
                mesh.position.z = mesh.userData.originalPosition.z + spiralZ;
                
                // Funnel intensity fluctuation
                const funnelIntensity = 1.0 + Math.sin(funnelTime * 2.5) * 0.3;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * funnelIntensity;
                }
                
                // Funnel opacity variation
                const funnelOpacity = 0.9 + Math.sin(funnelTime * 3.5) * 0.1;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * funnelOpacity;
                }
            }
        });
    }

    // Animate flying debris (swirling around tornado)
    const flyingDebrisGroup = cardGroup.getObjectByName('flyingDebris');
    if (flyingDebrisGroup) {
        flyingDebrisGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.debrisPhase !== undefined) {
                const debrisTime = debrisSwirl + mesh.userData.debrisPhase;
                
                // Debris orbital motion around tornado
                const orbitRadius = mesh.userData.originalPosition.length() * 0.1;
                const orbitX = Math.cos(debrisTime * 2.0) * orbitRadius;
                const orbitZ = Math.sin(debrisTime * 2.0) * orbitRadius;
                const orbitY = Math.sin(debrisTime * 1.5) * 0.002; // Vertical bobbing
                
                mesh.position.x = mesh.userData.originalPosition.x + orbitX;
                mesh.position.y = mesh.userData.originalPosition.y + orbitY;
                mesh.position.z = mesh.userData.originalPosition.z + orbitZ;
                
                // Debris tumbling rotation
                mesh.rotation.x = debrisTime * 2.5;
                mesh.rotation.y = debrisTime * 3.0;
                mesh.rotation.z = debrisTime * 1.8;
                
                // Debris intensity fluctuation
                const debrisIntensity = 1.0 + Math.sin(debrisTime * 4.0) * 0.2;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * debrisIntensity;
                }
            }
        });
    }

    // Animate wind streams (flowing air currents)
    const windStreamsGroup = cardGroup.getObjectByName('windStreams');
    if (windStreamsGroup) {
        windStreamsGroup.rotation.y = windFlow * 0.8; // Wind stream rotation
        
        windStreamsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.windPhase !== undefined) {
                const windTime = windFlow + mesh.userData.windPhase;
                
                // Wind stream flowing motion
                const flowRadius = 0.003;
                const flowX = Math.cos(windTime * 4.0) * flowRadius;
                const flowZ = Math.sin(windTime * 4.0) * flowRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + flowX;
                mesh.position.z = mesh.userData.originalPosition.z + flowZ;
                
                // Wind intensity fluctuation
                const windIntensity = 1.0 + Math.sin(windTime * 5.0) * 0.4;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * windIntensity;
                }
                
                // Wind opacity variation
                const windOpacity = 0.6 + Math.sin(windTime * 3.0) * 0.3;
                if (mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * windOpacity;
                }
                
                // Wind scale pulsing
                const windScale = 0.9 + Math.sin(windTime * 4.5) * 0.2;
                mesh.scale.setScalar(windScale);
            }
        });
    }

    // Animate lightning energy (electrical tornado energy)
    const lightningEnergyGroup = cardGroup.getObjectByName('lightningEnergy');
    if (lightningEnergyGroup) {
        lightningEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.lightningPhase !== undefined) {
                const lightningTime = tornadoSpin * 2.0 + mesh.userData.lightningPhase;
                
                // Lightning crackling motion
                const crackleX = Math.sin(lightningTime * 8.0) * 0.002;
                const crackleY = Math.cos(lightningTime * 6.0) * 0.001;
                const crackleZ = Math.sin(lightningTime * 7.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + crackleX;
                mesh.position.y = mesh.userData.originalPosition.y + crackleY;
                mesh.position.z = mesh.userData.originalPosition.z + crackleZ;
                
                // Lightning intensity flickering
                const lightningFlicker = Math.sin(lightningTime * 10.0) > 0.2 ? 1.0 : 0.3;
                if (mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * lightningFlicker;
                }
                
                // Lightning scale variation
                const lightningScale = 0.8 + Math.sin(lightningTime * 9.0) * 0.3;
                mesh.scale.setScalar(lightningScale);
            }
        });
    }

    // Overall tornado pulsing (powerful storm)
    const tornadoPulse = 1 + Math.sin(time * 3.0) * 0.08;
    const tornadoTremor = Math.cos(time * 12.0) * 0.001;
    cardGroup.scale.setScalar(0.8 * tornadoPulse);
    cardGroup.position.x += tornadoTremor;
    cardGroup.position.z += tornadoTremor;
}

// Export the tornado card data for the loot system
export const TORNADO_CARD_DATA = {
    name: "Tornado",
    description: 'Summons a powerful tornado that moves across the battlefield, dealing wind damage to enemies it touches and hurling debris with devastating force.',
    category: 'card',
    rarity: 'epic',
    effect: 'tornado',
    effectValue: 80, // Base damage per tornado hit
    createFunction: createTornadoCard,
    updateFunction: updateTornadoCardAnimation,
    voxelModel: 'tornado_card',
    glow: {
        color: 0x4682B4,
        intensity: 1.3
    }
};

export default createTornadoCard;