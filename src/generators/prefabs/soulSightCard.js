import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Soul Sight Card Prefab
 * Reveals all hidden rooms and secrets on the current floor
 */

// Soul sight specific colors
const SIGHT_COLORS = {
    MYSTIC_BLUE: 0x4169E1,          // Royal blue mystic energy
    SPIRIT_WHITE: 0xF0F8FF,         // Alice blue spirit light
    VISION_GOLD: 0xDAA520,          // Goldenrod vision power
    ETHEREAL_CYAN: 0x00CED1,        // Dark turquoise ethereal sight
    SOUL_PURPLE: 0x9370DB,          // Medium purple soul energy
    DIVINE_SILVER: 0xC0C0C0         // Silver divine insight
};

/**
 * Create a soul sight card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The soul sight card 3D model
 */
export function createSoulSightCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SoulSightCard';

    // Soul sight materials
    const mysticBlueMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.MYSTIC_BLUE,
        emissive: SIGHT_COLORS.MYSTIC_BLUE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.9
    });

    const spiritWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.SPIRIT_WHITE,
        emissive: SIGHT_COLORS.SPIRIT_WHITE,
        emissiveIntensity: 0.9,
        transparent: true,
        opacity: 0.8
    });

    const visionGoldMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.VISION_GOLD,
        emissive: SIGHT_COLORS.VISION_GOLD,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.85
    });

    const etherealCyanMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.ETHEREAL_CYAN,
        emissive: SIGHT_COLORS.ETHEREAL_CYAN,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.75
    });

    const soulPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.SOUL_PURPLE,
        emissive: SIGHT_COLORS.SOUL_PURPLE,
        emissiveIntensity: 0.7,
        transparent: true,
        opacity: 0.8
    });

    const divineSilverMaterial = new THREE.MeshLambertMaterial({
        color: SIGHT_COLORS.DIVINE_SILVER,
        emissive: SIGHT_COLORS.DIVINE_SILVER,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central mystical eye (all-seeing eye design)
    const mysticalEyeVoxels = [
        // Eye outline (diamond shape)
        { x: 0.0, y: 0.16, z: 0, material: divineSilverMaterial },     // Top
        { x: -0.12, y: 0.0, z: 0, material: divineSilverMaterial },   // Left
        { x: 0.12, y: 0.0, z: 0, material: divineSilverMaterial },    // Right
        { x: 0.0, y: -0.16, z: 0, material: divineSilverMaterial },   // Bottom
        
        // Eye corners
        { x: -0.08, y: 0.08, z: 0, material: divineSilverMaterial },  // Top-left
        { x: 0.08, y: 0.08, z: 0, material: divineSilverMaterial },   // Top-right
        { x: -0.08, y: -0.08, z: 0, material: divineSilverMaterial }, // Bottom-left
        { x: 0.08, y: -0.08, z: 0, material: divineSilverMaterial },  // Bottom-right
        
        // Eye iris (mystic blue)
        { x: 0.0, y: 0.0, z: 0.02, material: mysticBlueMaterial },
        { x: -0.04, y: 0.0, z: 0.02, material: mysticBlueMaterial },
        { x: 0.04, y: 0.0, z: 0.02, material: mysticBlueMaterial },
        { x: 0.0, y: 0.04, z: 0.02, material: mysticBlueMaterial },
        { x: 0.0, y: -0.04, z: 0.02, material: mysticBlueMaterial },
        
        // Eye pupil (spirit white - the seeing center)
        { x: 0.0, y: 0.0, z: 0.04, material: spiritWhiteMaterial }
    ];

    // Vision rays (emanating from the eye)
    const visionRaysVoxels = [
        // Cardinal direction rays
        { x: 0.0, y: 0.24, z: 0.06, material: visionGoldMaterial },   // North ray
        { x: 0.0, y: 0.32, z: 0.08, material: visionGoldMaterial },   // Extended north
        { x: 0.20, y: 0.0, z: 0.06, material: visionGoldMaterial },   // East ray
        { x: 0.28, y: 0.0, z: 0.08, material: visionGoldMaterial },   // Extended east
        { x: 0.0, y: -0.24, z: 0.06, material: visionGoldMaterial },  // South ray
        { x: 0.0, y: -0.32, z: 0.08, material: visionGoldMaterial },  // Extended south
        { x: -0.20, y: 0.0, z: 0.06, material: visionGoldMaterial },  // West ray
        { x: -0.28, y: 0.0, z: 0.08, material: visionGoldMaterial },  // Extended west
        
        // Diagonal rays
        { x: 0.16, y: 0.16, z: 0.04, material: etherealCyanMaterial }, // NE ray
        { x: 0.24, y: 0.24, z: 0.06, material: etherealCyanMaterial }, // Extended NE
        { x: -0.16, y: 0.16, z: 0.04, material: etherealCyanMaterial }, // NW ray
        { x: -0.24, y: 0.24, z: 0.06, material: etherealCyanMaterial }, // Extended NW
        { x: 0.16, y: -0.16, z: 0.04, material: etherealCyanMaterial }, // SE ray
        { x: 0.24, y: -0.24, z: 0.06, material: etherealCyanMaterial }, // Extended SE
        { x: -0.16, y: -0.16, z: 0.04, material: etherealCyanMaterial }, // SW ray
        { x: -0.24, y: -0.24, z: 0.06, material: etherealCyanMaterial }  // Extended SW
    ];

    // Mystic sigils (mystical symbols around the eye)
    const mysticSigilsVoxels = [
        // Inner sigil ring
        { x: 0.0, y: 0.20, z: -0.04, material: soulPurpleMaterial },
        { x: 0.14, y: 0.14, z: -0.04, material: soulPurpleMaterial },
        { x: 0.20, y: 0.0, z: -0.04, material: soulPurpleMaterial },
        { x: 0.14, y: -0.14, z: -0.04, material: soulPurpleMaterial },
        { x: 0.0, y: -0.20, z: -0.04, material: soulPurpleMaterial },
        { x: -0.14, y: -0.14, z: -0.04, material: soulPurpleMaterial },
        { x: -0.20, y: 0.0, z: -0.04, material: soulPurpleMaterial },
        { x: -0.14, y: 0.14, z: -0.04, material: soulPurpleMaterial },
        
        // Outer sigil ring
        { x: 0.0, y: 0.28, z: -0.08, material: spiritWhiteMaterial },
        { x: 0.20, y: 0.20, z: -0.08, material: spiritWhiteMaterial },
        { x: 0.28, y: 0.0, z: -0.08, material: spiritWhiteMaterial },
        { x: 0.20, y: -0.20, z: -0.08, material: spiritWhiteMaterial },
        { x: 0.0, y: -0.28, z: -0.08, material: spiritWhiteMaterial },
        { x: -0.20, y: -0.20, z: -0.08, material: spiritWhiteMaterial },
        { x: -0.28, y: 0.0, z: -0.08, material: spiritWhiteMaterial },
        { x: -0.20, y: 0.20, z: -0.08, material: spiritWhiteMaterial }
    ];

    // Revelation aura (energy field showing hidden things)
    const revelationAuraVoxels = [
        // Inner aura layer
        { x: 0.0, y: 0.12, z: 0.10, material: etherealCyanMaterial },
        { x: 0.08, y: 0.08, z: 0.10, material: etherealCyanMaterial },
        { x: 0.12, y: 0.0, z: 0.10, material: etherealCyanMaterial },
        { x: 0.08, y: -0.08, z: 0.10, material: etherealCyanMaterial },
        { x: 0.0, y: -0.12, z: 0.10, material: etherealCyanMaterial },
        { x: -0.08, y: -0.08, z: 0.10, material: etherealCyanMaterial },
        { x: -0.12, y: 0.0, z: 0.10, material: etherealCyanMaterial },
        { x: -0.08, y: 0.08, z: 0.10, material: etherealCyanMaterial },
        
        // Outer aura layer (revealing hidden secrets)
        { x: 0.0, y: 0.36, z: 0.12, material: visionGoldMaterial },
        { x: 0.24, y: 0.24, z: 0.12, material: visionGoldMaterial },
        { x: 0.36, y: 0.0, z: 0.12, material: visionGoldMaterial },
        { x: 0.24, y: -0.24, z: 0.12, material: visionGoldMaterial },
        { x: 0.0, y: -0.36, z: 0.12, material: visionGoldMaterial },
        { x: -0.24, y: -0.24, z: 0.12, material: visionGoldMaterial },
        { x: -0.36, y: 0.0, z: 0.12, material: visionGoldMaterial },
        { x: -0.24, y: 0.24, z: 0.12, material: visionGoldMaterial },
        
        // Distant revelation points (hidden room indicators)
        { x: 0.32, y: 0.12, z: 0.14, material: spiritWhiteMaterial },
        { x: 0.12, y: 0.32, z: 0.14, material: spiritWhiteMaterial },
        { x: -0.12, y: 0.32, z: 0.14, material: spiritWhiteMaterial },
        { x: -0.32, y: 0.12, z: 0.14, material: spiritWhiteMaterial },
        { x: -0.32, y: -0.12, z: 0.14, material: spiritWhiteMaterial },
        { x: -0.12, y: -0.32, z: 0.14, material: spiritWhiteMaterial },
        { x: 0.12, y: -0.32, z: 0.14, material: spiritWhiteMaterial },
        { x: 0.32, y: -0.12, z: 0.14, material: spiritWhiteMaterial }
    ];

    // Create mystical eye group
    const mysticalEyeGroup = new THREE.Group();
    mysticalEyeGroup.name = 'mysticalEye';

    // Add mystical eye voxels
    mysticalEyeVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mysticalEyeGroup.add(mesh);
    });

    // Create vision rays group
    const visionRaysGroup = new THREE.Group();
    visionRaysGroup.name = 'visionRays';

    // Add vision rays voxels
    visionRaysVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.rayPhase = index * 0.3; // Stagger animation
        visionRaysGroup.add(mesh);
    });

    // Create mystic sigils group
    const mysticSigilsGroup = new THREE.Group();
    mysticSigilsGroup.name = 'mysticSigils';

    // Add mystic sigils voxels
    mysticSigilsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.sigilPhase = index * 0.2; // Stagger animation
        mysticSigilsGroup.add(mesh);
    });

    // Create revelation aura group
    const revelationAuraGroup = new THREE.Group();
    revelationAuraGroup.name = 'revelationAura';

    // Add revelation aura voxels
    revelationAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.25; // Stagger animation
        revelationAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(mysticalEyeGroup);
    cardGroup.add(visionRaysGroup);
    cardGroup.add(mysticSigilsGroup);
    cardGroup.add(revelationAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        visionPulse: 0,
        sigilRotation: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update soul sight card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSoulSightCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.visionPulse += deltaTime * 3.0; // Vision pulse speed
    cardGroup.userData.sigilRotation += deltaTime * 1.2; // Sigil rotation speed

    const time = cardGroup.userData.animationTime;
    const visionPulse = cardGroup.userData.visionPulse;
    const sigilRotation = cardGroup.userData.sigilRotation;

    // Animate mystical eye (all-seeing eye pulsing)
    const mysticalEyeGroup = cardGroup.getObjectByName('mysticalEye');
    if (mysticalEyeGroup) {
        // Eye iris and pupil energy pulsing
        const eyePulse = 0.8 + Math.sin(visionPulse * 2.5) * 0.4;
        mysticalEyeGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * eyePulse;
            }
        });
    }

    // Animate vision rays (scanning/revealing motion)
    const visionRaysGroup = cardGroup.getObjectByName('visionRays');
    if (visionRaysGroup) {
        // Vision rays extending and retracting
        visionRaysGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.rayPhase !== undefined) {
                const rayTime = visionPulse + mesh.userData.rayPhase;
                
                // Ray extension effect
                const extension = 1 + Math.sin(rayTime * 2.0) * 0.2;
                const directionX = Math.sign(mesh.userData.originalPosition.x) || 0;
                const directionY = Math.sign(mesh.userData.originalPosition.y) || 0;
                const directionZ = Math.sign(mesh.userData.originalPosition.z) || 0;
                
                mesh.position.x = mesh.userData.originalPosition.x + (directionX * extension * 0.01);
                mesh.position.y = mesh.userData.originalPosition.y + (directionY * extension * 0.01);
                mesh.position.z = mesh.userData.originalPosition.z + (directionZ * extension * 0.01);
                
                // Ray energy pulsing
                const rayPulse = 0.7 + Math.sin(rayTime * 3.5) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * rayPulse;
                }
            }
        });
    }

    // Animate mystic sigils (rotating mystical symbols)
    const mysticSigilsGroup = cardGroup.getObjectByName('mysticSigils');
    if (mysticSigilsGroup) {
        mysticSigilsGroup.rotation.z = sigilRotation * 0.7; // Slow rotation
        
        // Sigil energy pulsing
        mysticSigilsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.sigilPhase !== undefined) {
                const sigilTime = visionPulse + mesh.userData.sigilPhase;
                
                // Sigil mystical pulsing
                const sigilPulse = 0.5 + Math.sin(sigilTime * 4.0) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * sigilPulse;
                }
                
                // Sigil opacity flickering (mystical appearance)
                const sigilFlicker = 0.8 + Math.sin(sigilTime * 5.5) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * sigilFlicker;
                }
            }
        });
    }

    // Animate revelation aura (revealing hidden secrets)
    const revelationAuraGroup = cardGroup.getObjectByName('revelationAura');
    if (revelationAuraGroup) {
        revelationAuraGroup.rotation.y = -sigilRotation * 0.5; // Counter-rotate
        
        // Aura revelation waves
        revelationAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = visionPulse + mesh.userData.auraPhase;
                
                // Aura wave expansion
                const wave = 1 + Math.sin(auraTime * 2.8) * 0.15;
                mesh.scale.setScalar(wave);
                
                // Aura revelation energy
                const auraPulse = 0.6 + Math.sin(auraTime * 3.8) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraPulse;
                }
                
                // Aura transparency waves (revealing effect)
                const revealWave = 0.75 + Math.sin(auraTime * 4.2) * 0.25;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * revealWave;
                }
            }
        });
    }

    // Overall mystical sight pulsing
    const sightPulseScale = 1 + Math.sin(time * 2.0) * 0.05;
    cardGroup.scale.setScalar(0.8 * sightPulseScale);
}

// Export the soul sight card data for the loot system
export const SOUL_SIGHT_CARD_DATA = {
    name: 'Soul Sight',
    description: 'Grants mystical vision that reveals all hidden rooms, secret passages, and concealed treasures on the current floor, illuminating the unseen paths through divine insight.',
    category: 'card',
    rarity: 'epic',
    effect: 'soul_sight',
    effectValue: 1, // Reveals all hidden elements
    createFunction: createSoulSightCard,
    updateFunction: updateSoulSightCardAnimation,
    voxelModel: 'soul_sight_card',
    glow: {
        color: 0x4169E1,
        intensity: 1.4
    }
};

export default createSoulSightCard;