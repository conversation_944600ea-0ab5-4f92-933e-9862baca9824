import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js'; // <-- Import BufferGeometryUtils
// Import needed constants and materials from shared.js
import {
    VOXEL_SIZE,
    mulberry32,
    getOrCreateGeometry,      // <-- Import geometry helper
    _getMaterialByHex_Cached // <-- Import material helper
} from './shared.js';
// import VoxelBuilder from '../utils/VoxelBuilder.js'; // <-- REMOVE VoxelBuilder import
// import { WALL_HEIGHT } from '/src/utils/constants.js'; // <-- REMOVE import

// Use stone brick wall colors for consistency with dungeon walls
const STONE_BRICK_COLORS = ['555c66', '606873', '707880']; // Match stonebrick wall materials
const DARK_STONE = '4a4a4a'; // Dark gray for shadows and details
const LIGHT_STONE = '707880'; // Lighter stone brick color for highlights

// Balanced dimensions for proper proportions with large voxel scale
const BASE_WIDTH_VOXELS = 3;  // Slightly thicker base for better proportions
const BASE_DEPTH_VOXELS = 3;  // Slightly thicker base for better proportions
const BASE_HEIGHT_VOXELS = 1; // Keep at 1
const SHAFT_WIDTH_VOXELS = 2; // Thicker shaft for better appearance
const SHAFT_DEPTH_VOXELS = 2; // Thicker shaft for better appearance
const TOP_HEIGHT_VOXELS = 1;  // Keep at 1

/**
 * Creates a 3D stone pillar object using merged geometry.
 * @param {object} options - Options object.
 * @param {number} options.seed - Random seed.
 * @param {number} options.wallHeight - The target height for the pillar.
 * @returns {object} - Object containing the THREE.Group for the pillar.
 */
export function createStonePillarObject(options = {}) {
    const { seed = 12345, wallHeight = 3 } = options;
    const random = mulberry32(seed);

    // Create RNG function for color variation (like ancient pillar)
    const rng = () => random();
    
    const pillarGroup = new THREE.Group();
    const geometriesByMaterial = {}; // Store geometries by hex color string
    const tempMatrix = new THREE.Matrix4();

    // Use same large voxel size as ancient stone pillar for consistent debris size
    const pillarVoxelSize = VOXEL_SIZE * 5.12; // Same size as ancient stone pillar for debris consistency
    const voxelGeo = getOrCreateGeometry('voxel_pillar', () => new THREE.BoxGeometry(pillarVoxelSize, pillarVoxelSize, pillarVoxelSize));

    // Helper to add a voxel
    const addVoxel = (x_vox, y_vox, z_vox, colorHex) => {
        if (!geometriesByMaterial[colorHex]) {
            geometriesByMaterial[colorHex] = [];
        }
        const geo = voxelGeo.clone();
        tempMatrix.makeTranslation(
            x_vox * pillarVoxelSize,
            y_vox * pillarVoxelSize,
            z_vox * pillarVoxelSize
        );
        geo.applyMatrix4(tempMatrix);
        geometriesByMaterial[colorHex].push(geo);
    };

    // Calculate Total Height in Voxels based on passed wallHeight and actual pillar voxel size
    const totalHeightVoxels = Math.floor(wallHeight / pillarVoxelSize);
    const shaftHeightVoxels = totalHeightVoxels - BASE_HEIGHT_VOXELS - TOP_HEIGHT_VOXELS;

    // --- Ensure shaft height is at least 1 voxel ---
    if (shaftHeightVoxels < 1) {
        console.warn(`[StonePillar] Calculated shaft height too small (${shaftHeightVoxels} voxels) for wallHeight ${wallHeight}. Clamping.`);
        // Adjust base/top or clamp shaft height? For now, just log.
        // Maybe adjust TOP_HEIGHT_VOXELS dynamically?
        // shaftHeightVoxels = 1;
        // Re-calculate topStartY if clamping shaft height
    }
    // ---------------------------------------------

    // Voxel coordinates are relative to the center base (0,0,0)
    const halfBaseW = (BASE_WIDTH_VOXELS - 1) / 2;
    const halfBaseD = (BASE_DEPTH_VOXELS - 1) / 2;
    const halfShaftW = (SHAFT_WIDTH_VOXELS - 1) / 2;
    const halfShaftD = (SHAFT_DEPTH_VOXELS - 1) / 2;

    // --- Collect Voxel Data for Destruction ---
    const originalVoxels = [];
    const recordVoxel = (x_vox, y_vox, z_vox, colorHex) => {
        originalVoxels.push({ x: x_vox, y: y_vox, z: z_vox, c: colorHex });
    };

    // --- Build the pillar and record voxel data ---
    // Base - using ancient pillar color scheme
    for (let y = 0; y < BASE_HEIGHT_VOXELS; y++) {
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                // Use ancient pillar color logic
                let colorHex = LIGHT_STONE; // Default to light stone

                // Add weathering variation using stone brick colors
                if (rng() < 0.25) { // 25% chance for weathering
                    const colorIndex = Math.floor(rng() * STONE_BRICK_COLORS.length);
                    colorHex = STONE_BRICK_COLORS[colorIndex];
                }

                // Use dark stone for edge details
                const isEdge = Math.abs(x) === halfBaseW || Math.abs(z) === halfBaseD;
                if (isEdge && rng() < 0.3) {
                    colorHex = DARK_STONE;
                }

                addVoxel(x, y, z, colorHex);
                recordVoxel(x, y, z, colorHex);
            }
        }
    }
    // Shaft - using ancient pillar color scheme
    const shaftStartY = BASE_HEIGHT_VOXELS;
    for (let y = 0; y < shaftHeightVoxels; y++) {
        const currentY = shaftStartY + y;
        for (let x = -halfShaftW; x <= halfShaftW; x++) {
            for (let z = -halfShaftD; z <= halfShaftD; z++) {
                // Use ancient pillar color logic
                let colorHex = LIGHT_STONE; // Default to light stone

                // Add weathering variation using stone brick colors
                if (rng() < 0.25) { // 25% chance for weathering
                    const colorIndex = Math.floor(rng() * STONE_BRICK_COLORS.length);
                    colorHex = STONE_BRICK_COLORS[colorIndex];
                }

                // Use dark stone for edge details
                const isEdge = Math.abs(x) === halfShaftW || Math.abs(z) === halfShaftD;
                if (isEdge && rng() < 0.3) {
                    colorHex = DARK_STONE;
                }

                addVoxel(x, currentY, z, colorHex);
                recordVoxel(x, currentY, z, colorHex);
            }
        }
    }
    // Top - using ancient pillar color scheme
    const topStartY = shaftStartY + shaftHeightVoxels;
    for (let y = 0; y < TOP_HEIGHT_VOXELS; y++) {
        const currentY = topStartY + y;
        for (let x = -halfBaseW; x <= halfBaseW; x++) {
            for (let z = -halfBaseD; z <= halfBaseD; z++) {
                // Use ancient pillar color logic
                let colorHex = LIGHT_STONE; // Default to light stone

                // Add weathering variation using stone brick colors
                if (rng() < 0.25) { // 25% chance for weathering
                    const colorIndex = Math.floor(rng() * STONE_BRICK_COLORS.length);
                    colorHex = STONE_BRICK_COLORS[colorIndex];
                }

                // Use dark stone for edge details
                const isEdge = Math.abs(x) === halfBaseW || Math.abs(z) === halfBaseD;
                if (isEdge && rng() < 0.3) {
                    colorHex = DARK_STONE;
                }

                addVoxel(x, currentY, z, colorHex);
                recordVoxel(x, currentY, z, colorHex);
            }
        }
    }

    // --- Merge Geometries --- 
    for (const hex in geometriesByMaterial) {
        const mergedGeo = BufferGeometryUtils.mergeGeometries(geometriesByMaterial[hex], false);
        if (mergedGeo) {
            const material = _getMaterialByHex_Cached(hex); // Use cached material getter
            const mesh = new THREE.Mesh(mergedGeo, material);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            pillarGroup.add(mesh);
        } else {
            console.warn(`[StonePillar] Failed to merge geometry for material hex: ${hex}`);
        }
    }

    // --- Set UserData (Important for destruction) ---
    pillarGroup.userData = {
        ...(pillarGroup.userData || {}),
        objectType: 'stone_pillar',
        isDestructible: true,       // As per definition
        destructionEffect: 'collapse', // As per definition
        health: 1,                  // As per definition
        originalVoxels: originalVoxels, // <-- Add voxel data
        voxelScale: pillarVoxelSize    // <-- Add voxel scale (same as ancient pillar)
    };

    // Adjust Group Position: Built relative to base center at 0,0,0.
    // Room generator expects origin at y=0 (floor level).
    // Shift the group content up by half the total height.
    pillarGroup.position.y = (totalHeightVoxels / 2) * pillarVoxelSize;

    return { group: pillarGroup };
}