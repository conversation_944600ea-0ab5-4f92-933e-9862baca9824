import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Dragon Form Card Prefab
 * Creates dragon transformation that grants flight and fire breath
 */

// Dragon form specific colors
const DRAGON_COLORS = {
    CRIMSON_RED: 0xDC143C,           // Main dragon scales
    FLAME_ORANGE: 0xFF4500,          // Dragon fire and inner glow
    GOLDEN_YELLOW: 0xFFD700,         // Dragon horns and claws
    EMBER_RED: 0xFF6347,             // Dragon wing membranes
    DARK_RED: 0x8B0000,              // Dragon shadows and depths
    BRIGHT_YELLOW: 0xFFFF00,         // Dragon fire breath
    BURNT_ORANGE: 0xFF8C00,          // Dragon belly scales
    RUBY_RED: 0xE0115F               // Dragon eyes and accents
};

/**
 * Create a dragon form card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The dragon form card 3D model
 */
export function createDragonFormCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'DragonFormCard';

    // Dragon form materials
    const crimsonRedMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.CRIMSON_RED,
        emissive: DRAGON_COLORS.CRIMSON_RED,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.95
    });

    const flameOrangeMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.FLAME_ORANGE,
        emissive: DRAGON_COLORS.FLAME_ORANGE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const goldenYellowMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.GOLDEN_YELLOW,
        emissive: DRAGON_COLORS.GOLDEN_YELLOW,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.9
    });

    const emberRedMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.EMBER_RED,
        emissive: DRAGON_COLORS.EMBER_RED,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const darkRedMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.DARK_RED,
        emissive: DRAGON_COLORS.DARK_RED,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 1.0
    });

    const brightYellowMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.BRIGHT_YELLOW,
        emissive: DRAGON_COLORS.BRIGHT_YELLOW,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const burntOrangeMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.BURNT_ORANGE,
        emissive: DRAGON_COLORS.BURNT_ORANGE,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.9
    });

    const rubyRedMaterial = new THREE.MeshLambertMaterial({
        color: DRAGON_COLORS.RUBY_RED,
        emissive: DRAGON_COLORS.RUBY_RED,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Dragon head and body (majestic dragon form)
    const dragonBodyVoxels = [
        // Dragon head
        { x: 0.0, y: 0.20, z: 0.0, material: crimsonRedMaterial },
        { x: -0.04, y: 0.20, z: 0.0, material: crimsonRedMaterial },
        { x: 0.04, y: 0.20, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: 0.20, z: 0.04, material: crimsonRedMaterial },
        { x: 0.0, y: 0.20, z: -0.04, material: crimsonRedMaterial },
        
        // Dragon horns
        { x: -0.04, y: 0.24, z: 0.0, material: goldenYellowMaterial },
        { x: 0.04, y: 0.24, z: 0.0, material: goldenYellowMaterial },
        { x: -0.08, y: 0.28, z: 0.0, material: goldenYellowMaterial },
        { x: 0.08, y: 0.28, z: 0.0, material: goldenYellowMaterial },
        
        // Dragon snout
        { x: 0.0, y: 0.20, z: 0.08, material: darkRedMaterial },
        { x: 0.0, y: 0.16, z: 0.12, material: darkRedMaterial },
        { x: 0.0, y: 0.20, z: 0.12, material: darkRedMaterial },
        
        // Dragon eyes
        { x: -0.04, y: 0.24, z: 0.04, material: rubyRedMaterial },
        { x: 0.04, y: 0.24, z: 0.04, material: rubyRedMaterial },
        
        // Dragon neck
        { x: 0.0, y: 0.16, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: 0.12, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: 0.08, z: 0.0, material: crimsonRedMaterial },
        
        // Dragon chest
        { x: 0.0, y: 0.04, z: 0.0, material: burntOrangeMaterial },
        { x: -0.04, y: 0.04, z: 0.0, material: crimsonRedMaterial },
        { x: 0.04, y: 0.04, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: 0.04, z: 0.04, material: burntOrangeMaterial },
        
        // Dragon torso
        { x: 0.0, y: 0.0, z: 0.0, material: burntOrangeMaterial },
        { x: -0.04, y: 0.0, z: 0.0, material: crimsonRedMaterial },
        { x: 0.04, y: 0.0, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: 0.0, z: 0.04, material: burntOrangeMaterial },
        
        // Dragon lower body
        { x: 0.0, y: -0.04, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: -0.08, z: 0.0, material: crimsonRedMaterial },
        { x: 0.0, y: -0.12, z: 0.0, material: crimsonRedMaterial },
        
        // Dragon tail
        { x: 0.0, y: -0.16, z: 0.0, material: darkRedMaterial },
        { x: 0.0, y: -0.20, z: 0.04, material: darkRedMaterial },
        { x: 0.0, y: -0.24, z: 0.08, material: darkRedMaterial }
    ];

    // Dragon wings (spread for flight)
    const dragonWingsVoxels = [
        // Left wing structure
        { x: -0.08, y: 0.08, z: 0.0, material: darkRedMaterial }, // Wing joint
        { x: -0.12, y: 0.12, z: 0.04, material: crimsonRedMaterial }, // Wing bone
        { x: -0.16, y: 0.16, z: 0.08, material: crimsonRedMaterial }, // Wing bone
        { x: -0.20, y: 0.20, z: 0.12, material: crimsonRedMaterial }, // Wing bone
        { x: -0.24, y: 0.24, z: 0.16, material: crimsonRedMaterial }, // Wing tip
        
        // Left wing membrane
        { x: -0.12, y: 0.08, z: 0.08, material: emberRedMaterial },
        { x: -0.16, y: 0.12, z: 0.12, material: emberRedMaterial },
        { x: -0.20, y: 0.16, z: 0.16, material: emberRedMaterial },
        { x: -0.24, y: 0.20, z: 0.20, material: emberRedMaterial },
        { x: -0.16, y: 0.08, z: 0.12, material: emberRedMaterial },
        { x: -0.20, y: 0.12, z: 0.16, material: emberRedMaterial },
        { x: -0.24, y: 0.16, z: 0.20, material: emberRedMaterial },
        
        // Right wing structure
        { x: 0.08, y: 0.08, z: 0.0, material: darkRedMaterial }, // Wing joint
        { x: 0.12, y: 0.12, z: 0.04, material: crimsonRedMaterial }, // Wing bone
        { x: 0.16, y: 0.16, z: 0.08, material: crimsonRedMaterial }, // Wing bone
        { x: 0.20, y: 0.20, z: 0.12, material: crimsonRedMaterial }, // Wing bone
        { x: 0.24, y: 0.24, z: 0.16, material: crimsonRedMaterial }, // Wing tip
        
        // Right wing membrane
        { x: 0.12, y: 0.08, z: 0.08, material: emberRedMaterial },
        { x: 0.16, y: 0.12, z: 0.12, material: emberRedMaterial },
        { x: 0.20, y: 0.16, z: 0.16, material: emberRedMaterial },
        { x: 0.24, y: 0.20, z: 0.20, material: emberRedMaterial },
        { x: 0.16, y: 0.08, z: 0.12, material: emberRedMaterial },
        { x: 0.20, y: 0.12, z: 0.16, material: emberRedMaterial },
        { x: 0.24, y: 0.16, z: 0.20, material: emberRedMaterial }
    ];

    // Dragon claws and limbs
    const dragonClawsVoxels = [
        // Front left claw
        { x: -0.08, y: 0.0, z: 0.08, material: goldenYellowMaterial },
        { x: -0.12, y: -0.04, z: 0.12, material: goldenYellowMaterial },
        { x: -0.16, y: -0.08, z: 0.16, material: goldenYellowMaterial },
        
        // Front right claw
        { x: 0.08, y: 0.0, z: 0.08, material: goldenYellowMaterial },
        { x: 0.12, y: -0.04, z: 0.12, material: goldenYellowMaterial },
        { x: 0.16, y: -0.08, z: 0.16, material: goldenYellowMaterial },
        
        // Rear left claw
        { x: -0.08, y: -0.12, z: 0.08, material: goldenYellowMaterial },
        { x: -0.12, y: -0.16, z: 0.12, material: goldenYellowMaterial },
        
        // Rear right claw
        { x: 0.08, y: -0.12, z: 0.08, material: goldenYellowMaterial },
        { x: 0.12, y: -0.16, z: 0.12, material: goldenYellowMaterial },
        
        // Dragon legs
        { x: -0.08, y: -0.04, z: 0.0, material: darkRedMaterial },
        { x: 0.08, y: -0.04, z: 0.0, material: darkRedMaterial },
        { x: -0.08, y: -0.08, z: 0.0, material: darkRedMaterial },
        { x: 0.08, y: -0.08, z: 0.0, material: darkRedMaterial }
    ];

    // Dragon fire breath
    const dragonFireVoxels = [
        // Fire breath cone
        { x: 0.0, y: 0.16, z: 0.16, material: brightYellowMaterial },
        { x: 0.0, y: 0.16, z: 0.20, material: flameOrangeMaterial },
        { x: 0.0, y: 0.16, z: 0.24, material: emberRedMaterial },
        { x: 0.0, y: 0.16, z: 0.28, material: crimsonRedMaterial },
        
        // Fire spread
        { x: -0.04, y: 0.16, z: 0.20, material: flameOrangeMaterial },
        { x: 0.04, y: 0.16, z: 0.20, material: flameOrangeMaterial },
        { x: -0.08, y: 0.16, z: 0.24, material: emberRedMaterial },
        { x: 0.08, y: 0.16, z: 0.24, material: emberRedMaterial },
        { x: -0.04, y: 0.20, z: 0.20, material: flameOrangeMaterial },
        { x: 0.04, y: 0.20, z: 0.20, material: flameOrangeMaterial },
        { x: -0.04, y: 0.12, z: 0.20, material: flameOrangeMaterial },
        { x: 0.04, y: 0.12, z: 0.20, material: flameOrangeMaterial },
        
        // Fire particles
        { x: -0.12, y: 0.20, z: 0.28, material: brightYellowMaterial },
        { x: 0.12, y: 0.20, z: 0.28, material: brightYellowMaterial },
        { x: -0.08, y: 0.24, z: 0.32, material: flameOrangeMaterial },
        { x: 0.08, y: 0.24, z: 0.32, material: flameOrangeMaterial },
        { x: -0.12, y: 0.12, z: 0.28, material: brightYellowMaterial },
        { x: 0.12, y: 0.12, z: 0.28, material: brightYellowMaterial },
        { x: -0.08, y: 0.08, z: 0.32, material: flameOrangeMaterial },
        { x: 0.08, y: 0.08, z: 0.32, material: flameOrangeMaterial }
    ];

    // Dragon aura (transformation energy)
    const dragonAuraVoxels = [
        // Inner transformation energy
        { x: -0.14, y: 0.14, z: -0.08, material: flameOrangeMaterial },
        { x: 0.14, y: 0.14, z: -0.08, material: flameOrangeMaterial },
        { x: -0.14, y: -0.06, z: -0.08, material: flameOrangeMaterial },
        { x: 0.14, y: -0.06, z: -0.08, material: flameOrangeMaterial },
        
        // Outer transformation energy
        { x: -0.18, y: 0.18, z: -0.12, material: brightYellowMaterial },
        { x: 0.18, y: 0.18, z: -0.12, material: brightYellowMaterial },
        { x: -0.18, y: -0.10, z: -0.12, material: brightYellowMaterial },
        { x: 0.18, y: -0.10, z: -0.12, material: brightYellowMaterial },
        
        // Power emanations
        { x: -0.22, y: 0.22, z: -0.16, material: goldenYellowMaterial },
        { x: 0.22, y: 0.22, z: -0.16, material: goldenYellowMaterial },
        { x: -0.22, y: -0.14, z: -0.16, material: goldenYellowMaterial },
        { x: 0.22, y: -0.14, z: -0.16, material: goldenYellowMaterial },
        
        // Distant energy sparkles
        { x: -0.26, y: 0.26, z: -0.20, material: rubyRedMaterial },
        { x: 0.26, y: 0.26, z: -0.20, material: rubyRedMaterial },
        { x: -0.26, y: -0.18, z: -0.20, material: rubyRedMaterial },
        { x: 0.26, y: -0.18, z: -0.20, material: rubyRedMaterial },
        { x: -0.30, y: 0.04, z: -0.24, material: brightYellowMaterial },
        { x: 0.30, y: 0.04, z: -0.24, material: brightYellowMaterial }
    ];

    // Create dragon body group
    const dragonBodyGroup = new THREE.Group();
    dragonBodyGroup.name = 'dragonBody';

    // Add dragon body voxels
    dragonBodyVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        dragonBodyGroup.add(mesh);
    });

    // Create dragon wings group
    const dragonWingsGroup = new THREE.Group();
    dragonWingsGroup.name = 'dragonWings';

    // Add dragon wings voxels
    dragonWingsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.wingPhase = index * 0.1; // Stagger animation
        dragonWingsGroup.add(mesh);
    });

    // Create dragon claws group
    const dragonClawsGroup = new THREE.Group();
    dragonClawsGroup.name = 'dragonClaws';

    // Add dragon claws voxels
    dragonClawsVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.clawPhase = index * 0.15; // Stagger animation
        dragonClawsGroup.add(mesh);
    });

    // Create dragon fire group
    const dragonFireGroup = new THREE.Group();
    dragonFireGroup.name = 'dragonFire';

    // Add dragon fire voxels
    dragonFireVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.firePhase = index * 0.08; // Stagger animation
        dragonFireGroup.add(mesh);
    });

    // Create dragon aura group
    const dragonAuraGroup = new THREE.Group();
    dragonAuraGroup.name = 'dragonAura';

    // Add dragon aura voxels
    dragonAuraVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.auraPhase = index * 0.05; // Stagger animation
        dragonAuraGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(dragonBodyGroup);
    cardGroup.add(dragonWingsGroup);
    cardGroup.add(dragonClawsGroup);
    cardGroup.add(dragonFireGroup);
    cardGroup.add(dragonAuraGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        dragonPulse: 0,
        fireFlicker: 0,
        wingBeat: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update dragon form card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateDragonFormCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.dragonPulse += deltaTime * 2.0; // Dragon pulse speed
    cardGroup.userData.fireFlicker += deltaTime * 5.0; // Fire flicker speed
    cardGroup.userData.wingBeat += deltaTime * 4.0; // Wing beat speed

    const time = cardGroup.userData.animationTime;
    const dragonPulse = cardGroup.userData.dragonPulse;
    const fireFlicker = cardGroup.userData.fireFlicker;
    const wingBeat = cardGroup.userData.wingBeat;

    // Animate dragon body (powerful pulsing)
    const dragonBodyGroup = cardGroup.getObjectByName('dragonBody');
    if (dragonBodyGroup) {
        // Dragon body powerful pulsing (transformation energy)
        const dragonIntensity = 1.0 + Math.sin(dragonPulse * 1.8) * 0.4;
        dragonBodyGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * dragonIntensity;
            }
        });
    }

    // Animate dragon wings (wing beating)
    const dragonWingsGroup = cardGroup.getObjectByName('dragonWings');
    if (dragonWingsGroup) {
        dragonWingsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.wingPhase !== undefined) {
                const wingTime = wingBeat + mesh.userData.wingPhase;
                
                // Wing beating motion (flight preparation)
                const wingFlap = Math.sin(wingTime * 2.5) * 0.003;
                const wingTilt = Math.cos(wingTime * 2.0) * 0.002;
                
                mesh.position.y = mesh.userData.originalPosition.y + wingFlap;
                mesh.position.z = mesh.userData.originalPosition.z + wingTilt;
                
                // Wing membrane intensity (blood flow)
                const membraneGlow = 1.0 + Math.sin(wingTime * 3.5) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * membraneGlow;
                }
            }
        });
    }

    // Animate dragon claws (flexing)
    const dragonClawsGroup = cardGroup.getObjectByName('dragonClaws');
    if (dragonClawsGroup) {
        dragonClawsGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.clawPhase !== undefined) {
                const clawTime = dragonPulse + mesh.userData.clawPhase;
                
                // Claw flexing motion
                const clawFlex = Math.sin(clawTime * 2.8) * 0.001;
                mesh.position.z = mesh.userData.originalPosition.z + clawFlex;
                
                // Claw golden glow
                const clawGlow = 1.0 + Math.sin(clawTime * 4.0) * 0.3;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * clawGlow;
                }
            }
        });
    }

    // Animate dragon fire (breath effect)
    const dragonFireGroup = cardGroup.getObjectByName('dragonFire');
    if (dragonFireGroup) {
        dragonFireGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.firePhase !== undefined) {
                const fireTime = fireFlicker + mesh.userData.firePhase;
                
                // Fire dancing motion (breath flow)
                const fireDance = Math.sin(fireTime * 3.5) * 0.003;
                const fireWave = Math.cos(fireTime * 4.0) * 0.002;
                
                mesh.position.x = mesh.userData.originalPosition.x + fireDance;
                mesh.position.y = mesh.userData.originalPosition.y + fireWave;
                
                // Fire intensity flickering
                const fireIntensity = 1.0 + Math.sin(fireTime * 6.0) * 0.6;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * fireIntensity;
                }
                
                // Fire scale variation (breathing)
                const fireScale = 0.8 + Math.sin(fireTime * 5.0) * 0.4;
                mesh.scale.setScalar(fireScale);
                
                // Fire opacity flickering
                const fireOpacity = 0.8 + Math.sin(fireTime * 7.0) * 0.2;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fireOpacity;
                }
            }
        });
    }

    // Animate dragon aura (transformation energy)
    const dragonAuraGroup = cardGroup.getObjectByName('dragonAura');
    if (dragonAuraGroup) {
        dragonAuraGroup.rotation.y = time * 1.2; // Rotating transformation energy
        
        dragonAuraGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.auraPhase !== undefined) {
                const auraTime = dragonPulse + mesh.userData.auraPhase;
                
                // Aura swirling motion (power emanation)
                const auraSwirl = Math.sin(auraTime * 2.8) * 0.004;
                const auraPulse = Math.cos(auraTime * 3.2) * 0.003;
                
                mesh.position.x = mesh.userData.originalPosition.x + auraSwirl;
                mesh.position.y = mesh.userData.originalPosition.y + auraPulse;
                
                // Aura intensity pulsing
                const auraIntensity = 1.0 + Math.sin(auraTime * 4.5) * 0.5;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * auraIntensity;
                }
                
                // Aura twinkling
                const auraTwinkle = 0.9 + Math.sin(auraTime * 8.0) * 0.3;
                mesh.scale.setScalar(auraTwinkle);
            }
        });
    }

    // Overall dragon form pulsing (transformation power)
    const transformationPulse = 1 + Math.sin(time * 2.5) * 0.08;
    cardGroup.scale.setScalar(0.8 * transformationPulse);
}

// Export the dragon form card data for the loot system
export const DRAGON_FORM_CARD_DATA = {
    name: 'Dragon Form',
    description: 'Transform into a mighty dragon, gaining the ability to fly, breathe fire, and increased damage while maintaining combat mobility.',
    category: 'card',
    rarity: 'epic',
    effect: 'dragon_form',
    effectValue: 60, // Duration in seconds
    createFunction: createDragonFormCard,
    updateFunction: updateDragonFormCardAnimation,
    voxelModel: 'dragon_form_card',
    glow: {
        color: 0xDC143C,
        intensity: 1.6
    }
};

export default createDragonFormCard;