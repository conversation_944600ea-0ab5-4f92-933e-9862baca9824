import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Phantom Step Card Prefab
 * Creates a shadowy figure dissolving into mist for teleportation magic
 */

// Phantom step specific colors
const PHANTOM_COLORS = {
    SHADOW_DARK: 0x1a1a1a,      // Very dark shadow
    SHADOW_MEDIUM: 0x2F2F2F,    // Medium shadow
    SHADOW_LIGHT: 0x4a4a4a,     // Light shadow
    MIST: 0x696969,             // Gray mist
    SILVER_ACCENT: 0xc0c0c0,    // Silver highlights
    ETHEREAL: 0x8a8a8a          // Ethereal glow
};

/**
 * Create a phantom step card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The phantom step card 3D model
 */
export function createPhantomStepCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'PhantomStepCard';

    // Materials
    const shadowDarkMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.SHADOW_DARK,
        transparent: true,
        opacity: 0.9
    });

    const shadowMediumMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.SHADOW_MEDIUM,
        transparent: true,
        opacity: 0.8
    });

    const shadowLightMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.SHADOW_LIGHT,
        transparent: true,
        opacity: 0.7
    });

    const mistMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.MIST,
        transparent: true,
        opacity: 0.6
    });

    const silverMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.SILVER_ACCENT,
        emissive: PHANTOM_COLORS.SILVER_ACCENT,
        emissiveIntensity: 0.2,
        transparent: true,
        opacity: 0.8
    });

    const etherealMaterial = new THREE.MeshLambertMaterial({
        color: PHANTOM_COLORS.ETHEREAL,
        emissive: PHANTOM_COLORS.ETHEREAL,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.5
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Create shadowy figure voxels
    const figureVoxels = [
        // Head
        { x: 0, y: 4, z: 0, material: shadowDarkMaterial },
        { x: -1, y: 4, z: 0, material: shadowMediumMaterial },
        { x: 1, y: 4, z: 0, material: shadowMediumMaterial },

        // Body core
        { x: 0, y: 3, z: 0, material: shadowDarkMaterial },
        { x: 0, y: 2, z: 0, material: shadowDarkMaterial },
        { x: 0, y: 1, z: 0, material: shadowDarkMaterial },
        { x: 0, y: 0, z: 0, material: shadowDarkMaterial },

        // Body outline
        { x: -1, y: 3, z: 0, material: shadowMediumMaterial },
        { x: 1, y: 3, z: 0, material: shadowMediumMaterial },
        { x: -1, y: 2, z: 0, material: shadowMediumMaterial },
        { x: 1, y: 2, z: 0, material: shadowMediumMaterial },

        // Arms (dissolving)
        { x: -2, y: 3, z: 0, material: shadowLightMaterial },
        { x: 2, y: 3, z: 0, material: shadowLightMaterial },
        { x: -2, y: 2, z: 0, material: mistMaterial },
        { x: 2, y: 2, z: 0, material: mistMaterial },

        // Legs (fading)
        { x: -1, y: 1, z: 0, material: shadowMediumMaterial },
        { x: 1, y: 1, z: 0, material: shadowMediumMaterial },
        { x: -1, y: 0, z: 0, material: shadowLightMaterial },
        { x: 1, y: 0, z: 0, material: shadowLightMaterial },

        // Lower legs (dissolving)
        { x: 0, y: -1, z: 0, material: mistMaterial }
    ];

    // Mist particles around figure
    const mistVoxels = [
        // Around head
        { x: -2, y: 4, z: 0, material: mistMaterial },
        { x: 2, y: 4, z: 0, material: mistMaterial },
        { x: 0, y: 5, z: 0, material: etherealMaterial },

        // Swirling around body
        { x: -3, y: 3, z: 0, material: mistMaterial },
        { x: 3, y: 3, z: 0, material: mistMaterial },
        { x: -2, y: 1, z: 0, material: etherealMaterial },
        { x: 2, y: 1, z: 0, material: etherealMaterial },

        // Base mist
        { x: -1, y: -1, z: 0, material: mistMaterial },
        { x: 1, y: -1, z: 0, material: mistMaterial },
        { x: 0, y: -2, z: 0, material: etherealMaterial },

        // Floating particles
        { x: -3, y: 0, z: 0, material: silverMaterial },
        { x: 3, y: 1, z: 0, material: silverMaterial },
        { x: -1, y: 5, z: 0, material: silverMaterial },
        { x: 1, y: -2, z: 0, material: silverMaterial }
    ];

    // Create all voxels
    [...figureVoxels, ...mistVoxels].forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8, // Slightly compact
            (voxel.y - 1) * VOXEL_SIZE * 0.8, // Center vertically
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.voxelType = voxel.material === silverMaterial ? 'silver' : 
                                 voxel.material === etherealMaterial ? 'ethereal' :
                                 voxel.material === mistMaterial ? 'mist' : 'shadow';
        cardGroup.add(mesh);
    });

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        fadePhase: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update phantom step card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updatePhantomStepCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    const time = cardGroup.userData.animationTime;

    // Fading in/out effect
    cardGroup.userData.fadePhase += deltaTime * 1.5;
    const fadeValue = (Math.sin(cardGroup.userData.fadePhase) + 1) * 0.5; // 0 to 1

    // Apply fading to all voxels
    cardGroup.traverse((child) => {
        if (child.isMesh && child.material && child.userData.originalOpacity !== undefined) {
            const baseOpacity = child.userData.originalOpacity;
            
            // Different fade patterns for different voxel types
            switch (child.userData.voxelType) {
                case 'shadow':
                    child.material.opacity = baseOpacity * (0.7 + fadeValue * 0.3);
                    break;
                case 'mist':
                    child.material.opacity = baseOpacity * (0.3 + fadeValue * 0.7);
                    break;
                case 'ethereal':
                    child.material.opacity = baseOpacity * fadeValue;
                    break;
                case 'silver':
                    child.material.opacity = baseOpacity * (0.5 + fadeValue * 0.5);
                    if (child.material.emissiveIntensity !== undefined) {
                        child.material.emissiveIntensity = 0.2 + fadeValue * 0.3;
                    }
                    break;
            }
        }
    });

    // Gentle swaying motion for mist particles
    const swayOffset = Math.sin(time * 2) * 0.02;
    cardGroup.traverse((child) => {
        if (child.isMesh && (child.userData.voxelType === 'mist' || child.userData.voxelType === 'ethereal')) {
            child.position.x += swayOffset * (Math.random() - 0.5);
            child.position.y += Math.sin(time * 3 + child.position.x * 10) * 0.01;
        }
    });
}

// Export the phantom step card data for the loot system
export const PHANTOM_STEP_CARD_DATA = {
    name: 'Phantom Step',
    description: 'Dissolves your form into shadow and mist, allowing passage through the ethereal realm. Emerge at your destination cloaked in darkness, unseen by mortal eyes.',
    category: 'card',
    rarity: 'rare',
    effect: 'phantom_teleport',
    effectValue: 'teleport',
    createFunction: createPhantomStepCard,
    updateFunction: updatePhantomStepCardAnimation,
    voxelModel: 'phantom_step_card',
    glow: {
        color: 0x696969,
        intensity: 1.0
    }
};

export default createPhantomStepCard;