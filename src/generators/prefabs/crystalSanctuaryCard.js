import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Crystal Sanctuary Card Prefab
 * Creates a protective crystal barrier with shimmering crystal formations
 */

// Crystal sanctuary specific colors
const CRYSTAL_COLORS = {
    CRYSTAL_BLUE: 0x4DC8FF,           // Bright crystal blue
    CRYSTAL_WHITE: 0xF0F8FF,          // Ice white crystal
    CRYSTAL_CYAN: 0x00FFFF,           // Cyan crystal glow
    BARRIER_SHIELD: 0x87CEEB          // Sky blue barrier
};

/**
 * Create a crystal sanctuary card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The crystal sanctuary card 3D model
 */
export function createCrystalSanctuaryCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'CrystalSanctuaryCard';

    // Crystal materials
    const crystalBlueMaterial = new THREE.MeshLambertMaterial({
        color: CRYSTAL_COLORS.CRYSTAL_BLUE,
        emissive: CRYSTAL_COLORS.CRYSTAL_BLUE,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const crystalWhiteMaterial = new THREE.MeshLambertMaterial({
        color: CRYSTAL_COLORS.CRYSTAL_WHITE,
        emissive: CRYSTAL_COLORS.CRYSTAL_WHITE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.8
    });

    const crystalCyanMaterial = new THREE.MeshLambertMaterial({
        color: CRYSTAL_COLORS.CRYSTAL_CYAN,
        emissive: CRYSTAL_COLORS.CRYSTAL_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.7
    });

    const barrierShieldMaterial = new THREE.MeshLambertMaterial({
        color: CRYSTAL_COLORS.BARRIER_SHIELD,
        emissive: CRYSTAL_COLORS.BARRIER_SHIELD,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.6
    });

    // Voxel geometry
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, VOXEL_SIZE);

    // Central crystal formation (5 tall crystals in a protective pattern)
    const crystalFormationVoxels = [
        // Center crystal (tallest)
        { x: 0, y: 0, z: 0, material: crystalWhiteMaterial },
        { x: 0, y: 0.05, z: 0, material: crystalBlueMaterial },
        { x: 0, y: 0.1, z: 0, material: crystalWhiteMaterial },
        { x: 0, y: 0.15, z: 0, material: crystalCyanMaterial },
        
        // North crystal
        { x: 0, y: 0, z: 0.15, material: crystalBlueMaterial },
        { x: 0, y: 0.05, z: 0.15, material: crystalWhiteMaterial },
        { x: 0, y: 0.1, z: 0.15, material: crystalCyanMaterial },
        
        // South crystal
        { x: 0, y: 0, z: -0.15, material: crystalBlueMaterial },
        { x: 0, y: 0.05, z: -0.15, material: crystalWhiteMaterial },
        { x: 0, y: 0.1, z: -0.15, material: crystalCyanMaterial },
        
        // East crystal
        { x: 0.15, y: 0, z: 0, material: crystalBlueMaterial },
        { x: 0.15, y: 0.05, z: 0, material: crystalWhiteMaterial },
        { x: 0.15, y: 0.1, z: 0, material: crystalCyanMaterial },
        
        // West crystal
        { x: -0.15, y: 0, z: 0, material: crystalBlueMaterial },
        { x: -0.15, y: 0.05, z: 0, material: crystalWhiteMaterial },
        { x: -0.15, y: 0.1, z: 0, material: crystalCyanMaterial }
    ];

    // Protective barrier particles (ring of smaller crystals)
    const barrierParticleVoxels = [
        // Inner protective ring
        { x: 0.1, y: 0, z: 0.1, material: barrierShieldMaterial },
        { x: -0.1, y: 0, z: 0.1, material: barrierShieldMaterial },
        { x: 0.1, y: 0, z: -0.1, material: barrierShieldMaterial },
        { x: -0.1, y: 0, z: -0.1, material: barrierShieldMaterial },
        
        // Outer protective ring
        { x: 0.2, y: 0.02, z: 0, material: crystalCyanMaterial },
        { x: -0.2, y: 0.02, z: 0, material: crystalCyanMaterial },
        { x: 0, y: 0.02, z: 0.2, material: crystalCyanMaterial },
        { x: 0, y: 0.02, z: -0.2, material: crystalCyanMaterial },
        
        // Corner barrier crystals
        { x: 0.15, y: 0.02, z: 0.15, material: crystalBlueMaterial },
        { x: -0.15, y: 0.02, z: 0.15, material: crystalBlueMaterial },
        { x: 0.15, y: 0.02, z: -0.15, material: crystalBlueMaterial },
        { x: -0.15, y: 0.02, z: -0.15, material: crystalBlueMaterial },
        
        // Shield sparkles
        { x: 0.25, y: 0.08, z: 0.05, material: crystalWhiteMaterial },
        { x: -0.25, y: 0.08, z: -0.05, material: crystalWhiteMaterial },
        { x: 0.05, y: 0.08, z: 0.25, material: crystalWhiteMaterial },
        { x: -0.05, y: 0.08, z: -0.25, material: crystalWhiteMaterial }
    ];

    // Create crystal formation group
    const crystalFormationGroup = new THREE.Group();
    crystalFormationGroup.name = 'crystalFormation';

    // Add crystal formation voxels
    crystalFormationVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            voxel.z * VOXEL_SIZE * 0.8
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        crystalFormationGroup.add(mesh);
    });

    // Create barrier particle group
    const barrierParticleGroup = new THREE.Group();
    barrierParticleGroup.name = 'barrierParticles';

    // Add barrier particle voxels
    barrierParticleVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 0.8,
            voxel.y * VOXEL_SIZE * 0.8,
            voxel.z * VOXEL_SIZE * 0.8
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 0.8, 
            y: voxel.y * VOXEL_SIZE * 0.8,
            z: voxel.z * VOXEL_SIZE * 0.8
        };
        mesh.userData.particlePhase = index * 0.3; // Stagger animation
        barrierParticleGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(crystalFormationGroup);
    cardGroup.add(barrierParticleGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        crystalRotation: 0,
        barrierPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update crystal sanctuary card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateCrystalSanctuaryCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.crystalRotation += deltaTime * 1.5; // Crystal rotation speed
    cardGroup.userData.barrierPulse += deltaTime * 2.5; // Barrier pulse speed

    const time = cardGroup.userData.animationTime;
    const crystalRotation = cardGroup.userData.crystalRotation;
    const barrierPulse = cardGroup.userData.barrierPulse;

    // Animate crystal formation (slow clockwise rotation)
    const crystalFormationGroup = cardGroup.getObjectByName('crystalFormation');
    if (crystalFormationGroup) {
        crystalFormationGroup.rotation.y = crystalRotation;
        
        // Crystal energy pulsing (protective glow)
        const crystalPulse = 0.4 + Math.sin(barrierPulse * 1.8) * 0.3;
        crystalFormationGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * crystalPulse;
            }
        });
    }

    // Animate barrier particles (floating and shimmering)
    const barrierParticleGroup = cardGroup.getObjectByName('barrierParticles');
    if (barrierParticleGroup) {
        barrierParticleGroup.rotation.y = -crystalRotation * 0.7; // Counter-rotate
        
        // Barrier particle floating and shimmering
        barrierParticleGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.particlePhase !== undefined) {
                const particleTime = barrierPulse + mesh.userData.particlePhase;
                
                // Gentle floating motion
                const floatY = Math.sin(particleTime * 1.2) * 0.01;
                
                mesh.position.x = mesh.userData.originalPosition.x;
                mesh.position.y = mesh.userData.originalPosition.y + floatY;
                mesh.position.z = mesh.userData.originalPosition.z;
                
                // Barrier energy shimmer
                const shimmer = 0.3 + Math.sin(particleTime * 2.5) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * shimmer;
                }
                
                // Protective sparkle effect
                const sparkle = 0.6 + Math.sin(particleTime * 3.0) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * sparkle;
                }
            }
        });
    }

    // Gentle overall crystal sanctuary pulsing
    const sanctuaryPulseScale = 1 + Math.sin(time * 1.3) * 0.06;
    cardGroup.scale.setScalar(0.8 * sanctuaryPulseScale);
}

// Export the crystal sanctuary card data for the loot system
export const CRYSTAL_SANCTUARY_CARD_DATA = {
    name: 'Crystal Sanctuary',
    description: 'Summons 8 protective crystal shields for 40 seconds. Each hit breaks one crystal shield, protecting you completely until all crystals are destroyed.',
    category: 'card',
    rarity: 'rare',
    effect: 'crystal_barrier',
    effectValue: 8, // Number of crystal shields
    createFunction: createCrystalSanctuaryCard,
    updateFunction: updateCrystalSanctuaryCardAnimation,
    voxelModel: 'crystal_sanctuary_card',
    glow: {
        color: 0x4DC8FF,
        intensity: 1.2
    }
};

export default createCrystalSanctuaryCard;