import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    _getMaterialByHex_Cached,
    mulberry32
} from './shared.js';

// Void Lord colors - cosmic horror theme
const VOIDLORD_COLORS = {
    VOID_BLACK: '000000',       // Pure black for void areas
    DARK_MATTER: '1A0033',      // Very dark purple
    SHADOW_PURPLE: '2D0052',    // Deep shadow purple
    ENERGY_RED: 'FF0000',       // Bright red energy
    BLOOD_RED: '8B0000',        // Dark red
    CRIMSON: 'DC143C',          // Crimson energy trails
    MAGMA_ORANGE: 'FF4500',     // Hot orange for energy cores
    PLASMA_PINK: 'FF69B4',      // Plasma energy
    COSMIC_BLUE: '000080',      // Deep cosmic blue
    STELLAR_WHITE: 'FFFFFF',    // Pure white for energy highlights
    ABYSSAL_GRAY: '1C1C1C',     // Very dark gray
    ETHEREAL_PURPLE: '6A0DAD',  // Ethereal purple glow
    CORRUPTION_GREEN: '00FF00',  // Corrupted energy (phase 3)
    GOLD_ACCENT: 'FFD700'       // Divine gold accents
};

/**
 * Creates the Void Lord boss enemy - a massive cosmic horror with a black hole head
 * and red energy strains that can form into various attack patterns
 * @param {number} [scale=1.0] Optional overall scale factor.
 * @returns {THREE.Group} The Void Lord enemy model group.
 */
export function createVoidLordEnemyModel(scale = 1.0) {
    console.log(`🌌 VOID LORD MODEL CREATION - Scale parameter: ${scale}`);

    const finalGroup = new THREE.Group();
    finalGroup.name = "VoidLord";

    // Use even larger voxel size for this colossal boss
    const voidLordVoxelSize = VOXEL_SIZE * 1.5; // 50% larger than standard

    // Create animation-ready groups for body parts
    const bodyGroup = new THREE.Group();
    bodyGroup.name = "body";

    const headGroup = new THREE.Group();
    headGroup.name = "head";

    const leftArmGroup = new THREE.Group();
    leftArmGroup.name = "leftArm";

    const rightArmGroup = new THREE.Group();
    rightArmGroup.name = "rightArm";

    const leftLegGroup = new THREE.Group();
    leftLegGroup.name = "leftLeg";

    const rightLegGroup = new THREE.Group();
    rightLegGroup.name = "rightLeg";

    // Create visual offset group for proper ground positioning
    const visualGroup = new THREE.Group();
    visualGroup.name = "visualModel";

    // Add all visual components to the visual group
    visualGroup.add(bodyGroup);
    visualGroup.add(headGroup);
    visualGroup.add(leftArmGroup);
    visualGroup.add(rightArmGroup);
    visualGroup.add(leftLegGroup);
    visualGroup.add(rightLegGroup);

    // Elevate the visual model for imposing presence
    const visualElevation = 1.5;
    visualGroup.position.y = visualElevation;

    finalGroup.add(visualGroup);

    // Position body parts - make it massive and imposing
    headGroup.position.set(0, 10 * voidLordVoxelSize, 0); // Very high head
    leftArmGroup.position.set(-6 * voidLordVoxelSize, 7 * voidLordVoxelSize, 0);
    rightArmGroup.position.set(6 * voidLordVoxelSize, 7 * voidLordVoxelSize, 0);
    leftLegGroup.position.set(-3 * voidLordVoxelSize, -3 * voidLordVoxelSize, 0);
    rightLegGroup.position.set(3 * voidLordVoxelSize, -3 * voidLordVoxelSize, 0);

    // Store geometries by material for each group
    const geometriesByMaterial = {
        body: {},
        head: {},
        leftArm: {},
        rightArm: {},
        leftLeg: {},
        rightLeg: {}
    };

    // Store original voxel data
    const originalVoxels = [];

    // Create template geometry
    const voxelGeo = getOrCreateGeometry('voidlord_voxel', () => 
        new THREE.BoxGeometry(voidLordVoxelSize, voidLordVoxelSize, voidLordVoxelSize)
    );
    const tempMatrix = new THREE.Matrix4();

    // Create seeded random for consistent appearance
    const random = mulberry32(99999); // Ominous seed

    // Helper to add voxels
    const addVoxel = (groupName, x, y, z, colorHex) => {
        if (!geometriesByMaterial[groupName][colorHex]) {
            geometriesByMaterial[groupName][colorHex] = [];
        }

        tempMatrix.makeTranslation(
            x * voidLordVoxelSize,
            y * voidLordVoxelSize,
            z * voidLordVoxelSize
        );

        const clonedGeo = voxelGeo.clone();
        clonedGeo.applyMatrix4(tempMatrix);
        geometriesByMaterial[groupName][colorHex].push(clonedGeo);

        originalVoxels.push({ x, y, z, c: colorHex });
    };

    // Helper to merge geometries
    const mergeGroupGeometries = (groupName, targetGroup) => {
        for (const colorHex in geometriesByMaterial[groupName]) {
            if (geometriesByMaterial[groupName][colorHex].length > 0) {
                const mergedGeometry = BufferGeometryUtils.mergeGeometries(
                    geometriesByMaterial[groupName][colorHex],
                    false
                );

                if (mergedGeometry) {
                    const material = _getMaterialByHex_Cached(colorHex);
                    
                    // Add emissive properties for energy colors
                    if (colorHex === VOIDLORD_COLORS.ENERGY_RED || 
                        colorHex === VOIDLORD_COLORS.CRIMSON ||
                        colorHex === VOIDLORD_COLORS.MAGMA_ORANGE ||
                        colorHex === VOIDLORD_COLORS.PLASMA_PINK ||
                        colorHex === VOIDLORD_COLORS.STELLAR_WHITE) {
                        material.emissive = new THREE.Color(`#${colorHex}`);
                        material.emissiveIntensity = 0.5;
                    }

                    const mesh = new THREE.Mesh(mergedGeometry, material);
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    targetGroup.add(mesh);
                }
            }
        }
    };

    // CREATE THE BLACK HOLE HEAD - The most terrifying feature
    // Outer event horizon
    for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 12) {
        for (let radius = 4; radius <= 6; radius++) {
            const x = Math.round(Math.cos(angle) * radius);
            const z = Math.round(Math.sin(angle) * radius);
            for (let y = -2; y <= 2; y++) {
                if (Math.abs(y) + radius <= 6) {
                    addVoxel('head', x, y, z, VOIDLORD_COLORS.VOID_BLACK);
                }
            }
        }
    }

    // Inner swirling void with energy
    for (let i = 0; i < 50; i++) {
        const angle = (i / 50) * Math.PI * 6; // Spiral
        const radius = 3 - (i / 50) * 2.5;
        const x = Math.round(Math.cos(angle) * radius);
        const z = Math.round(Math.sin(angle) * radius);
        const y = Math.round((i / 50) * 4 - 2);
        
        if (i % 3 === 0) {
            addVoxel('head', x, y, z, VOIDLORD_COLORS.ENERGY_RED);
        } else if (i % 3 === 1) {
            addVoxel('head', x, y, z, VOIDLORD_COLORS.CRIMSON);
        } else {
            addVoxel('head', x, y, z, VOIDLORD_COLORS.DARK_MATTER);
        }
    }

    // Singularity core
    for (let x = -1; x <= 1; x++) {
        for (let y = -1; y <= 1; y++) {
            for (let z = -1; z <= 1; z++) {
                if (Math.abs(x) + Math.abs(y) + Math.abs(z) <= 2) {
                    addVoxel('head', x, y, z, VOIDLORD_COLORS.STELLAR_WHITE);
                }
            }
        }
    }

    // Energy corona around black hole
    for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 8) {
        const x = Math.round(Math.cos(angle) * 7);
        const z = Math.round(Math.sin(angle) * 7);
        for (let y = -3; y <= 3; y++) {
            if (random() < 0.7) {
                addVoxel('head', x, y, z, VOIDLORD_COLORS.PLASMA_PINK);
            }
        }
    }

    // CREATE MASSIVE COSMIC BODY
    // Core torso with energy channels
    for (let x = -4; x <= 4; x++) {
        for (let y = 0; y <= 10; y++) {
            for (let z = -3; z <= 3; z++) {
                const distance = Math.sqrt(x*x + z*z);
                if (distance <= 4) {
                    // Main body mass
                    if (random() < 0.9) {
                        if (Math.abs(x) <= 1 && y % 3 === 0) {
                            // Energy channels
                            addVoxel('body', x, y, z, VOIDLORD_COLORS.ENERGY_RED);
                        } else if (distance <= 2) {
                            addVoxel('body', x, y, z, VOIDLORD_COLORS.SHADOW_PURPLE);
                        } else {
                            addVoxel('body', x, y, z, VOIDLORD_COLORS.DARK_MATTER);
                        }
                    }
                }
            }
        }
    }

    // Chest energy core
    for (let x = -2; x <= 2; x++) {
        for (let y = 5; y <= 7; y++) {
            for (let z = 2; z <= 4; z++) {
                if (Math.abs(x) + Math.abs(y - 6) <= 2) {
                    addVoxel('body', x, y, z, VOIDLORD_COLORS.MAGMA_ORANGE);
                }
            }
        }
    }

    // Shoulder spikes/energy emitters
    for (let side of [-1, 1]) {
        for (let spike = 0; spike < 3; spike++) {
            const x = side * (5 + spike);
            const y = 9 - spike;
            for (let z = -1; z <= 1; z++) {
                addVoxel('body', x, y, z, VOIDLORD_COLORS.CRIMSON);
                addVoxel('body', x, y + 1, z, VOIDLORD_COLORS.ENERGY_RED);
            }
        }
    }

    // CREATE MASSIVE ARMS WITH ENERGY TENDRILS
    for (let armSide of ['leftArm', 'rightArm']) {
        // Upper arm - muscular with energy veins
        for (let x = -2; x <= 2; x++) {
            for (let y = -3; y <= 3; y++) {
                for (let z = -2; z <= 2; z++) {
                    if (Math.abs(x) + Math.abs(z) <= 3) {
                        if (x === 0 && y % 2 === 0) {
                            // Energy veins
                            addVoxel(armSide, x, y, z, VOIDLORD_COLORS.CRIMSON);
                        } else {
                            addVoxel(armSide, x, y, z, VOIDLORD_COLORS.SHADOW_PURPLE);
                        }
                    }
                }
            }
        }

        // Forearm with energy buildup
        for (let y = -10; y <= -4; y++) {
            for (let x = -2; x <= 2; x++) {
                for (let z = -2; z <= 2; z++) {
                    if (Math.abs(x) + Math.abs(z) <= 2) {
                        if (y <= -8) {
                            // Energy concentration at hands
                            addVoxel(armSide, x, y, z, VOIDLORD_COLORS.ENERGY_RED);
                        } else {
                            addVoxel(armSide, x, y, z, VOIDLORD_COLORS.DARK_MATTER);
                        }
                    }
                }
            }
        }

        // Energy tendrils/claws
        for (let finger = -2; finger <= 2; finger++) {
            for (let segment = 0; segment < 4; segment++) {
                const x = finger;
                const y = -11 - segment;
                const z = segment / 2;
                addVoxel(armSide, x, y, z, VOIDLORD_COLORS.PLASMA_PINK);
                
                // Energy particles around fingers
                if (random() < 0.5) {
                    addVoxel(armSide, x + 1, y, z, VOIDLORD_COLORS.CRIMSON);
                    addVoxel(armSide, x - 1, y, z, VOIDLORD_COLORS.CRIMSON);
                }
            }
        }
    }

    // CREATE MASSIVE LEGS WITH VOID ENERGY
    for (let legSide of ['leftLeg', 'rightLeg']) {
        // Upper leg - thick and powerful
        for (let x = -2; x <= 2; x++) {
            for (let y = -3; y <= 3; y++) {
                for (let z = -2; z <= 2; z++) {
                    if (Math.abs(x) + Math.abs(z) <= 3) {
                        if (y === 0) {
                            // Energy band
                            addVoxel(legSide, x, y, z, VOIDLORD_COLORS.MAGMA_ORANGE);
                        } else {
                            addVoxel(legSide, x, y, z, VOIDLORD_COLORS.SHADOW_PURPLE);
                        }
                    }
                }
            }
        }

        // Lower leg with void corruption
        for (let y = -12; y <= -4; y++) {
            for (let x = -2; x <= 2; x++) {
                for (let z = -2; z <= 2; z++) {
                    if (Math.abs(x) + Math.abs(z) <= 2) {
                        if (y <= -10) {
                            // Void corruption at feet
                            addVoxel(legSide, x, y, z, VOIDLORD_COLORS.VOID_BLACK);
                        } else if (y % 3 === 0) {
                            // Energy rings
                            addVoxel(legSide, x, y, z, VOIDLORD_COLORS.CRIMSON);
                        } else {
                            addVoxel(legSide, x, y, z, VOIDLORD_COLORS.DARK_MATTER);
                        }
                    }
                }
            }
        }

        // Void talons
        for (let toe = -2; toe <= 2; toe++) {
            for (let segment = 0; segment < 3; segment++) {
                const x = toe;
                const y = -13 - segment;
                const z = -3 - segment;
                addVoxel(legSide, x, y, z, VOIDLORD_COLORS.ABYSSAL_GRAY);
                addVoxel(legSide, x, y - 1, z, VOIDLORD_COLORS.ENERGY_RED);
            }
        }
    }

    // Merge all geometries
    mergeGroupGeometries('body', bodyGroup);
    mergeGroupGeometries('head', headGroup);
    mergeGroupGeometries('leftArm', leftArmGroup);
    mergeGroupGeometries('rightArm', rightArmGroup);
    mergeGroupGeometries('leftLeg', leftLegGroup);
    mergeGroupGeometries('rightLeg', rightLegGroup);

    // Dispose template geometry
    voxelGeo.dispose();

    // Add animation data
    finalGroup.userData.animationData = {
        // Floating/hovering animation
        floatSpeed: 1.5,
        floatAmplitude: 0.5,
        // Energy pulsing
        energyPulseSpeed: 3.0,
        energyPulseAmplitude: 0.3,
        // Attack animations
        attackAnimationDuration: 1.2,
        // Phase transitions
        phaseTransitionDuration: 2.0
    };

    // Add type information
    finalGroup.userData.type = 'voidlord';
    finalGroup.userData.isBoss = true;

    // Add attack hitbox data
    finalGroup.userData.attackHitbox = {
        radius: 6.0 * scale, // Massive attack radius
        damage: 2, // High damage
        knockback: 8.0 // Extreme knockback
    };

    // Add destruction data
    finalGroup.userData.isDestructible = true;
    finalGroup.userData.objectType = 'voidlord';
    finalGroup.userData.originalVoxels = originalVoxels;
    finalGroup.userData.voxelScale = voidLordVoxelSize;

    // Special boss properties
    finalGroup.userData.bossProperties = {
        hasBlackHoleHead: true,
        hasEnergyStrains: true,
        canFormEnergyShapes: true,
        phaseCount: 3
    };

    // Ground offset for proper positioning
    const groundOffset = 15 * voidLordVoxelSize;
    finalGroup.position.y = groundOffset;

    console.log(`🌌 Created Void Lord Boss Model - Scale: ${scale}`);
    return finalGroup;
}

// Export special effect creators for the boss
export function createVoidLordEnergyStrain(startPos, endPos, color = 0xff0000) {
    // Create a tentacle-like energy strain instead of simple tube
    const tentacleGroup = new THREE.Group();
    const segmentCount = 12; // Back to original
    
    const direction = endPos.clone().sub(startPos);
    const distance = direction.length();
    direction.normalize();
    
    const segmentLength = distance / segmentCount;
    
    for (let i = 0; i < segmentCount; i++) {
        const t = i / (segmentCount - 1);
        // Proper tapering - thick at base, thin at top
        const radius = 0.5 - t * 0.35; // Taper from 0.5 at base to 0.15 at tip
        
        // Add wave offset for organic look
        const waveOffset = Math.sin(t * Math.PI * 3) * 0.5;
        
        // Use voxel boxes
        const voxelSize = radius * 2;
        const geometry = new THREE.BoxGeometry(voxelSize, segmentLength, voxelSize);
        const material = new THREE.MeshBasicMaterial({
            color: color,
            emissive: color,
            emissiveIntensity: 1.0 + t * 0.5,
            transparent: true,
            opacity: 0.9 - t * 0.2
        });
        
        const segment = new THREE.Mesh(geometry, material);
        
        // Position along path with wave
        const pos = startPos.clone().add(direction.clone().multiplyScalar(i * segmentLength));
        pos.x += Math.sin(i * 0.5) * waveOffset;
        pos.z += Math.cos(i * 0.5) * waveOffset;
        
        segment.position.copy(pos);
        segment.lookAt(endPos);
        
        tentacleGroup.add(segment);
    }
    
    tentacleGroup.name = 'VoidLordEnergyStrain';
    return tentacleGroup;
}

export function createVoidLordEnergyOrb(radius = 1.0, color = 0xff0000) {
    const geometry = new THREE.SphereGeometry(radius, 16, 12);
    const material = new THREE.MeshBasicMaterial({
        color: color,
        emissive: color,
        emissiveIntensity: 1.5,
        transparent: true,
        opacity: 0.6
    });

    const mesh = new THREE.Mesh(geometry, material);
    mesh.name = 'VoidLordEnergyOrb';
    
    // Add pulsing animation data
    mesh.userData.pulseSpeed = 2.0;
    mesh.userData.baseScale = radius;
    
    return mesh;
}