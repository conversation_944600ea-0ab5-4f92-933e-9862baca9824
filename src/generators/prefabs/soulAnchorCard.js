import * as THREE from 'three';
import { VOXEL_SIZE } from './shared.js';

/**
 * Soul Anchor Card Prefab
 * Creates a mystical teleportation anchor with soul energy chains
 */

// Soul anchor specific colors
const SOUL_COLORS = {
    SOUL_PURPLE: 0x9966FF,           // Deep soul purple
    SOUL_CYAN: 0x00FFFF,             // Ethereal cyan glow
    ANCHOR_GOLD: 0xFFD700,           // Golden anchor metal
    CHAIN_SILVER: 0xC0C0C0,          // Silver chain links
    ENERGY_WHITE: 0xFFFFFF           // Pure white soul energy
};

/**
 * Create a soul anchor card
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} The soul anchor card 3D model
 */
export function createSoulAnchorCard(options = {}) {
    const {
        position = { x: 0, y: 0, z: 0 },
        rotation = { x: 0, y: 0, z: 0 },
        scale = 1.0,
        animate = true
    } = options;

    const cardGroup = new THREE.Group();
    cardGroup.name = 'SoulAnchorCard';

    // Soul anchor materials
    const anchorGoldMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.ANCHOR_GOLD,
        emissive: SOUL_COLORS.ANCHOR_GOLD,
        emissiveIntensity: 0.4,
        transparent: true,
        opacity: 0.9
    });

    const chainSilverMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.CHAIN_SILVER,
        emissive: SOUL_COLORS.CHAIN_SILVER,
        emissiveIntensity: 0.3,
        transparent: true,
        opacity: 0.8
    });

    const soulPurpleMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.SOUL_PURPLE,
        emissive: SOUL_COLORS.SOUL_PURPLE,
        emissiveIntensity: 0.6,
        transparent: true,
        opacity: 0.7
    });

    const soulCyanMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.SOUL_CYAN,
        emissive: SOUL_COLORS.SOUL_CYAN,
        emissiveIntensity: 0.5,
        transparent: true,
        opacity: 0.6
    });

    const energyWhiteMaterial = new THREE.MeshLambertMaterial({
        color: SOUL_COLORS.ENERGY_WHITE,
        emissive: SOUL_COLORS.ENERGY_WHITE,
        emissiveIntensity: 0.8,
        transparent: true,
        opacity: 0.8
    });

    // Voxel geometry (larger for visibility)
    const voxelGeometry = new THREE.BoxGeometry(VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0, VOXEL_SIZE * 2.0);

    // Central soul anchor (main anchor structure)
    const anchorCoreVoxels = [
        // Anchor base (horizontal bar)
        { x: -0.16, y: -0.24, z: 0, material: anchorGoldMaterial },
        { x: -0.08, y: -0.24, z: 0, material: anchorGoldMaterial },
        { x: 0.0, y: -0.24, z: 0, material: anchorGoldMaterial },
        { x: 0.08, y: -0.24, z: 0, material: anchorGoldMaterial },
        { x: 0.16, y: -0.24, z: 0, material: anchorGoldMaterial },
        
        // Anchor shaft (vertical bar)
        { x: 0.0, y: -0.16, z: 0, material: anchorGoldMaterial },
        { x: 0.0, y: -0.08, z: 0, material: anchorGoldMaterial },
        { x: 0.0, y: 0.0, z: 0, material: anchorGoldMaterial },
        { x: 0.0, y: 0.08, z: 0, material: anchorGoldMaterial },
        { x: 0.0, y: 0.16, z: 0, material: anchorGoldMaterial },
        
        // Anchor arms (curved top)
        { x: -0.08, y: 0.24, z: 0, material: anchorGoldMaterial },
        { x: 0.08, y: 0.24, z: 0, material: anchorGoldMaterial },
        { x: -0.16, y: 0.16, z: 0, material: anchorGoldMaterial },
        { x: 0.16, y: 0.16, z: 0, material: anchorGoldMaterial },
        
        // Anchor ring (top)
        { x: 0.0, y: 0.24, z: 0, material: anchorGoldMaterial }
    ];

    // Soul chain links (connecting the anchor to spiritual realm)
    const chainLinksVoxels = [
        // Chain ascending upward
        { x: 0.0, y: 0.32, z: 0, material: chainSilverMaterial },
        { x: 0.0, y: 0.40, z: 0, material: chainSilverMaterial },
        
        // Left chain branch
        { x: -0.08, y: 0.32, z: 0, material: chainSilverMaterial },
        { x: -0.16, y: 0.40, z: 0, material: chainSilverMaterial },
        { x: -0.24, y: 0.32, z: 0, material: chainSilverMaterial },
        
        // Right chain branch
        { x: 0.08, y: 0.32, z: 0, material: chainSilverMaterial },
        { x: 0.16, y: 0.40, z: 0, material: chainSilverMaterial },
        { x: 0.24, y: 0.32, z: 0, material: chainSilverMaterial },
        
        // Lower anchor chains
        { x: -0.08, y: -0.32, z: 0, material: chainSilverMaterial },
        { x: 0.08, y: -0.32, z: 0, material: chainSilverMaterial }
    ];

    // Soul energy orbs (floating mystical energy)
    const soulEnergyVoxels = [
        // Purple soul energy (larger orbs)
        { x: -0.20, y: 0.20, z: 0.08, material: soulPurpleMaterial },
        { x: 0.20, y: 0.20, z: -0.08, material: soulPurpleMaterial },
        { x: -0.12, y: -0.20, z: 0.12, material: soulPurpleMaterial },
        { x: 0.12, y: -0.20, z: -0.12, material: soulPurpleMaterial },
        
        // Cyan soul energy (medium orbs)
        { x: 0.0, y: 0.48, z: 0.04, material: soulCyanMaterial },
        { x: -0.24, y: 0.08, z: -0.04, material: soulCyanMaterial },
        { x: 0.24, y: 0.08, z: 0.04, material: soulCyanMaterial },
        { x: 0.0, y: -0.40, z: -0.04, material: soulCyanMaterial },
        
        // White energy sparkles (small orbs)
        { x: -0.16, y: 0.36, z: 0.12, material: energyWhiteMaterial },
        { x: 0.16, y: 0.36, z: -0.12, material: energyWhiteMaterial },
        { x: -0.32, y: 0.16, z: 0.08, material: energyWhiteMaterial },
        { x: 0.32, y: 0.16, z: -0.08, material: energyWhiteMaterial },
        { x: -0.20, y: -0.32, z: -0.12, material: energyWhiteMaterial },
        { x: 0.20, y: -0.32, z: 0.12, material: energyWhiteMaterial }
    ];

    // Create anchor core group
    const anchorCoreGroup = new THREE.Group();
    anchorCoreGroup.name = 'anchorCore';

    // Add anchor core voxels
    anchorCoreVoxels.forEach(voxel => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        anchorCoreGroup.add(mesh);
    });

    // Create chain links group
    const chainLinksGroup = new THREE.Group();
    chainLinksGroup.name = 'chainLinks';

    // Add chain link voxels
    chainLinksVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            0.01
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0 
        };
        mesh.userData.linkPhase = index * 0.4; // Stagger animation
        chainLinksGroup.add(mesh);
    });

    // Create soul energy group
    const soulEnergyGroup = new THREE.Group();
    soulEnergyGroup.name = 'soulEnergy';

    // Add soul energy voxels
    soulEnergyVoxels.forEach((voxel, index) => {
        const mesh = new THREE.Mesh(voxelGeometry, voxel.material);
        mesh.position.set(
            voxel.x * VOXEL_SIZE * 2.0,
            voxel.y * VOXEL_SIZE * 2.0,
            voxel.z * VOXEL_SIZE * 2.0
        );
        mesh.userData.originalOpacity = voxel.material.opacity;
        mesh.userData.originalEmissive = voxel.material.emissiveIntensity;
        mesh.userData.originalPosition = { 
            x: voxel.x * VOXEL_SIZE * 2.0, 
            y: voxel.y * VOXEL_SIZE * 2.0,
            z: voxel.z * VOXEL_SIZE * 2.0
        };
        mesh.userData.energyPhase = index * 0.2; // Stagger animation
        soulEnergyGroup.add(mesh);
    });

    // Add groups to card
    cardGroup.add(anchorCoreGroup);
    cardGroup.add(chainLinksGroup);
    cardGroup.add(soulEnergyGroup);

    // Store animation properties
    cardGroup.userData = {
        isAnimated: animate,
        animationTime: 0,
        anchorRotation: 0,
        soulPulse: 0
    };

    // Position and scale the card
    cardGroup.position.set(position.x, position.y, position.z);
    cardGroup.rotation.set(rotation.x, rotation.y, rotation.z);
    cardGroup.scale.setScalar(scale);

    return cardGroup;
}

/**
 * Update soul anchor card animation
 * @param {THREE.Group} cardGroup - The card group to animate
 * @param {number} deltaTime - Time since last frame
 */
export function updateSoulAnchorCardAnimation(cardGroup, deltaTime) {
    if (!cardGroup.userData.isAnimated) return;

    cardGroup.userData.animationTime += deltaTime;
    cardGroup.userData.anchorRotation += deltaTime * 1.0; // Anchor rotation speed
    cardGroup.userData.soulPulse += deltaTime * 2.8; // Soul pulse speed

    const time = cardGroup.userData.animationTime;
    const anchorRotation = cardGroup.userData.anchorRotation;
    const soulPulse = cardGroup.userData.soulPulse;

    // Animate anchor core (slow rotation for stability)
    const anchorCoreGroup = cardGroup.getObjectByName('anchorCore');
    if (anchorCoreGroup) {
        anchorCoreGroup.rotation.y = anchorRotation * 0.3;
        
        // Anchor energy pulsing (golden glow)
        const anchorPulse = 0.4 + Math.sin(soulPulse * 1.5) * 0.3;
        anchorCoreGroup.children.forEach(mesh => {
            if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                mesh.material.emissiveIntensity = mesh.userData.originalEmissive * anchorPulse;
            }
        });
    }

    // Animate chain links (swaying motion)
    const chainLinksGroup = cardGroup.getObjectByName('chainLinks');
    if (chainLinksGroup) {
        chainLinksGroup.rotation.y = -anchorRotation * 0.2; // Counter-rotate slower
        
        // Chain swaying animation
        chainLinksGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.linkPhase !== undefined) {
                const linkTime = soulPulse + mesh.userData.linkPhase;
                
                // Gentle swaying motion for chains
                const swayX = Math.sin(linkTime * 1.1) * 0.015;
                const swayY = Math.cos(linkTime * 0.8) * 0.01;
                
                mesh.position.x = mesh.userData.originalPosition.x + swayX;
                mesh.position.y = mesh.userData.originalPosition.y + swayY;
                
                // Chain shimmer
                const shimmer = 0.3 + Math.sin(linkTime * 2.2) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * shimmer;
                }
            }
        });
    }

    // Animate soul energy (orbiting and pulsing)
    const soulEnergyGroup = cardGroup.getObjectByName('soulEnergy');
    if (soulEnergyGroup) {
        soulEnergyGroup.rotation.y = anchorRotation * 1.5; // Faster rotation
        
        // Soul energy orbiting and pulsing
        soulEnergyGroup.children.forEach(mesh => {
            if (mesh.userData.originalPosition && mesh.userData.energyPhase !== undefined) {
                const energyTime = soulPulse + mesh.userData.energyPhase;
                
                // Orbiting motion around anchor
                const orbitRadius = 0.01;
                const orbitX = Math.cos(energyTime * 1.8) * orbitRadius;
                const orbitY = Math.sin(energyTime * 1.3) * orbitRadius;
                const orbitZ = Math.sin(energyTime * 2.1) * orbitRadius;
                
                mesh.position.x = mesh.userData.originalPosition.x + orbitX;
                mesh.position.y = mesh.userData.originalPosition.y + orbitY;
                mesh.position.z = mesh.userData.originalPosition.z + orbitZ;
                
                // Soul energy pulsing
                const energyPulse = 0.5 + Math.sin(energyTime * 3.2) * 0.4;
                if (mesh.material && mesh.userData.originalEmissive !== undefined) {
                    mesh.material.emissiveIntensity = mesh.userData.originalEmissive * energyPulse;
                }
                
                // Mystical fade effect
                const fade = 0.6 + Math.sin(energyTime * 2.7) * 0.4;
                if (mesh.material && mesh.userData.originalOpacity !== undefined) {
                    mesh.material.opacity = mesh.userData.originalOpacity * fade;
                }
            }
        });
    }

    // Gentle overall soul anchor pulsing
    const anchorPulseScale = 1 + Math.sin(time * 1.4) * 0.05;
    cardGroup.scale.setScalar(0.8 * anchorPulseScale);
}

// Export the soul anchor card data for the loot system
export const SOUL_ANCHOR_CARD_DATA = {
    name: 'Soul Anchor',
    description: 'Places a mystical anchor that creates a permanent teleportation point. Use again to instantly teleport back to the anchor location, bridging vast distances through soul magic.',
    category: 'card',
    rarity: 'epic',
    effect: 'teleport_anchor',
    effectValue: 1, // Number of anchor placements
    createFunction: createSoulAnchorCard,
    updateFunction: updateSoulAnchorCardAnimation,
    voxelModel: 'soul_anchor_card',
    glow: {
        color: 0x9966FF,
        intensity: 1.3
    }
};

export default createSoulAnchorCard;