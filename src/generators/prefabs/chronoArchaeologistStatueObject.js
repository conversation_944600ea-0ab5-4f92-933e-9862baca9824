import * as THREE from 'three';
import {
    VOXEL_SIZE,
    getOrCreateGeometry,
    mulberry32,
    _getMaterialByHex_Cached
} from './shared.js';

/**
 * Create a chrono-archaeologist statue object - the main interactive element
 * This ancient statue holds the consciousness of a trapped time researcher
 * @param {Object} options - Configuration options
 * @returns {THREE.Group} Chrono-archaeologist statue group
 */
export function createChronoArchaeologistStatueObject(options = {}) {
    const group = new THREE.Group();
    const seed = options.seed || Math.random();
    const rng = mulberry32(seed * 999);

    // Use consistent voxel size
    const statueVoxelSize = VOXEL_SIZE * 2.0;
    const baseGeometry = getOrCreateGeometry('archaeologist_statue_voxel', () =>
        new THREE.BoxGeometry(statueVoxelSize, statueVoxelSize, statueVoxelSize)
    );

    // Aged stone material (weathered by time)
    const stoneMaterial = _getMaterialByHex_Cached('4a4a4a', {
        emissive: new THREE.Color(0x0a0a0a),
        emissiveIntensity: 0.02,
        roughness: 0.95,
        metalness: 0.05
    });

    // Temporal-touched stone (slightly glowing)
    const temporalStoneMaterial = _getMaterialByHex_Cached('5a5a6a', {
        emissive: new THREE.Color(0x2a2a3a),
        emissiveIntensity: 0.1,
        roughness: 0.8,
        metalness: 0.1
    });

    // Stone pedestal base (3x3 platform)
    const pedestalPositions = [
        { x: -1, z: -1 }, { x: 0, z: -1 }, { x: 1, z: -1 },
        { x: -1, z: 0 },  { x: 0, z: 0 },  { x: 1, z: 0 },
        { x: -1, z: 1 },  { x: 0, z: 1 },  { x: 1, z: 1 }
    ];

    pedestalPositions.forEach(pos => {
        const pedestalVoxel = new THREE.Mesh(baseGeometry.clone(), stoneMaterial);
        pedestalVoxel.position.set(
            pos.x * statueVoxelSize,
            statueVoxelSize * 0.5,
            pos.z * statueVoxelSize
        );
        pedestalVoxel.userData.isFloorObject = true;
        pedestalVoxel.userData.hasCollision = true;
        pedestalVoxel.castShadow = true;
        pedestalVoxel.receiveShadow = true;
        group.add(pedestalVoxel);
    });

    // Statue body (humanoid figure with robes)
    const bodyPositions = [
        // Legs/base
        { x: -0.5, y: 1, z: 0 }, { x: 0.5, y: 1, z: 0 },
        { x: 0, y: 1, z: 0 }, // center support
        // Lower torso
        { x: -0.5, y: 1.5, z: 0 }, { x: 0, y: 1.5, z: 0 }, { x: 0.5, y: 1.5, z: 0 },
        { x: 0, y: 1.5, z: -0.5 }, // robe depth
        // Upper torso
        { x: -0.5, y: 2, z: 0 }, { x: 0, y: 2, z: 0 }, { x: 0.5, y: 2, z: 0 },
        { x: 0, y: 2, z: -0.5 }, // robe depth
        // Shoulders
        { x: -1, y: 2.5, z: 0 }, { x: 0, y: 2.5, z: 0 }, { x: 1, y: 2.5, z: 0 },
        // Head
        { x: 0, y: 3, z: 0 },
        // Arms (reaching toward temporal rift)
        { x: -1.5, y: 2, z: -0.5 }, { x: 1.5, y: 2, z: -0.5 }, // extended arms
        { x: -1, y: 1.5, z: -1 }, { x: 1, y: 1.5, z: -1 } // reaching hands
    ];

    bodyPositions.forEach(pos => {
        const bodyVoxel = new THREE.Mesh(baseGeometry.clone(), stoneMaterial);
        bodyVoxel.position.set(
            pos.x * statueVoxelSize,
            pos.y * statueVoxelSize,
            pos.z * statueVoxelSize
        );
        bodyVoxel.userData.isStatuePart = true;
        bodyVoxel.castShadow = true;
        bodyVoxel.receiveShadow = true;
        group.add(bodyVoxel);
    });

    // Temporal energy traces (glowing elements)
    const energyPositions = [
        // Eyes
        { x: -0.2, y: 3, z: -0.5 }, { x: 0.2, y: 3, z: -0.5 },
        // Heart/chest area
        { x: 0, y: 2, z: -0.5 },
        // Hands (temporal manipulation)
        { x: -1, y: 1.5, z: -1.2 }, { x: 1, y: 1.5, z: -1.2 }
    ];

    const energyGeometry = getOrCreateGeometry('temporal_energy_trace', () =>
        new THREE.BoxGeometry(statueVoxelSize * 0.3, statueVoxelSize * 0.3, statueVoxelSize * 0.3)
    );

    const energyMaterial = _getMaterialByHex_Cached('6A5ACD', {
        transparent: true,
        opacity: 0.8,
        emissive: new THREE.Color(0x6A5ACD),
        emissiveIntensity: 0.4
    });

    energyPositions.forEach((pos, index) => {
        const energyVoxel = new THREE.Mesh(energyGeometry, energyMaterial);
        energyVoxel.position.set(
            pos.x * statueVoxelSize,
            pos.y * statueVoxelSize,
            pos.z * statueVoxelSize
        );
        energyVoxel.userData.isTemporalTrace = true;
        energyVoxel.userData.animationPhase = (index / energyPositions.length) * Math.PI * 2;
        energyVoxel.castShadow = false;
        energyVoxel.receiveShadow = false;
        group.add(energyVoxel);
    });

    // Ancient scrolls/research materials at base
    const scrollMaterial = _getMaterialByHex_Cached('8B7355', {
        roughness: 0.9,
        metalness: 0.0
    });

    const scrollGeometry = getOrCreateGeometry('ancient_scroll', () =>
        new THREE.BoxGeometry(statueVoxelSize * 0.8, statueVoxelSize * 0.2, statueVoxelSize * 0.3)
    );

    // Scattered scrolls around base
    const scrollPositions = [
        { x: -1.5, y: 0.1, z: 1.2, rot: Math.PI/6 },
        { x: 1.2, y: 0.1, z: 1.5, rot: -Math.PI/4 },
        { x: -0.8, y: 0.1, z: -1.8, rot: Math.PI/3 }
    ];

    scrollPositions.forEach(pos => {
        const scroll = new THREE.Mesh(scrollGeometry, scrollMaterial);
        scroll.position.set(
            pos.x * statueVoxelSize,
            pos.y * statueVoxelSize,
            pos.z * statueVoxelSize
        );
        scroll.rotation.y = pos.rot;
        scroll.userData.isScroll = true;
        scroll.castShadow = true;
        scroll.receiveShadow = true;
        group.add(scroll);
    });

    // Temporal distortion field around statue (subtle effect)
    const distortionMaterial = _getMaterialByHex_Cached('4B0082', {
        transparent: true,
        opacity: 0.15,
        emissive: new THREE.Color(0x4B0082),
        emissiveIntensity: 0.1
    });

    const distortionGeometry = getOrCreateGeometry('temporal_distortion', () =>
        new THREE.BoxGeometry(statueVoxelSize * 0.5, statueVoxelSize * 4, statueVoxelSize * 0.5)
    );

    const distortionField = new THREE.Mesh(distortionGeometry, distortionMaterial);
    distortionField.position.set(0, statueVoxelSize * 2, 0);
    distortionField.userData.isDistortionField = true;
    distortionField.castShadow = false;
    distortionField.receiveShadow = false;
    group.add(distortionField);

    // Set up group properties
    group.userData = {
        ...(options.userData || {}),
        objectType: 'chrono_archaeologist_statue',
        isInteractable: true,
        isEventObject: true,
        objectId: options.userData?.objectId || 'archaeologist_statue',
        interactionRange: 4.0,
        isFloorObject: true,
        hasCollision: true,
        isDestructible: false, // Ancient artifact, indestructible
        isInteriorObject: true,
        voxelScale: statueVoxelSize,
        // Interaction properties
        interactionType: 'archaeologist_statue',
        isMainInteraction: true,
        // Animation properties
        hasAnimation: true,
        animationType: 'temporal_pulse'
    };

    // Add collision detection to solid parts only
    group.traverse(child => {
        if (child.isMesh && !child.userData.isTemporalTrace && !child.userData.isDistortionField) {
            child.userData.isFloorObject = true;
            child.userData.hasCollision = true;
            child.userData.objectType = 'chrono_archaeologist_statue';
        }
    });

    group.name = 'chrono_archaeologist_statue';

    console.log('[ChronoArchaeologistStatueObject] ✅ Created archaeologist statue with temporal effects');
    return group;
}