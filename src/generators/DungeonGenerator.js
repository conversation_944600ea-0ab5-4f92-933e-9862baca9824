// src/generators/DungeonGenerator.js
import * as THREE from 'three'; // Import THREE namespace
// Room class is no longer imported, define it internally
// import { Room } from './Room.js';
// Remove incorrect imports - Tile is likely defined elsewhere or no longer used directly here
// import { Tile } from './Tile.js';
// Import theme/area helpers
// import { getThemeData, getRandomEnemyList } from './AreaThemes.js'; // OLD
import { getAreaData } from '../gameData/areas.js'; // NEW

// Import web worker infrastructure
import { WorkerManager } from '../core/WorkerManager.js';

// --- Constants ---
const MIN_ROOMS = 12;      // GUARANTEED minimum rooms (increased for reliable special assignments)
const MAX_ROOMS = 25;      // Target/Maximum number of rooms to generate
const MAX_PLACEMENT_ATTEMPTS = 150; // Increased attempts to find a spot for a new room
const MAX_GENERATION_RETRIES = 10; // Reduced retries - focus on intelligent room addition instead

// Add this new constant for room shapes
const ROOM_SHAPES = [
    'SQUARE_1X1',    // Default square shape
    'L_SHAPE',       // L-shaped room
    'T_SHAPE',       // T-shaped room
    'RECTANGULAR',   // Rectangular room
    'CROSS_SHAPE',   // Cross-shaped room
    'U_SHAPE_DOWN',  // U-shaped room with opening at bottom (already implemented)
    'U_SHAPE_UP',    // U-shaped room with opening at top
    'U_SHAPE_LEFT',  // U-shaped room with opening at left
    'U_SHAPE_RIGHT', // U-shaped room with opening at right
    'CORRIDOR_LONG', // Long narrow corridor (4x1)
    'CORRIDOR_SHORT', // Short corridor (2x1)
    'SQUARE_2X2',    // Larger square (2x2)
    'RECT_2X1',      // Rectangle 2x1
    'RECT_1X2',      // Rectangle 1x2  
    'RECT_3X1',      // Rectangle 3x1
    'RECT_1X3',      // Rectangle 1x3
    'BOSS_ARENA'     // Special boss arena shape
];

// --- Room Class (Internal Helper for Generator Graph) ---
class Room {
    constructor(id, dungeonGenerator = null) {
        this.id = id; // Unique numerical ID (e.g., 0, 1, 2...)
        this._dungeonGeneratorRef = dungeonGenerator ? new WeakRef(dungeonGenerator) : null; // Weak reference to prevent memory leaks
        
        // Private properties - single source of truth
        this._type = 'Normal';
        this._shapeKey = 'SQUARE_1X1';
        this._visited = false;
        this._hasSecretRoom = false;
        
        // Direct properties (no sync needed)
        this.neighbors = { n: null, s: null, e: null, w: null };
        this.coords = { x: 0, y: 0 };
        
        // Initialize roomData object which will be stored in the final layout map
        this.roomData = {
            id: this.id,
            type: this._type,
            shapeKey: this._shapeKey,
            connections: this.neighbors, // Reference to same object
            coords: this.coords, // Reference to same object
            visited: this._visited,
            hasSecretRoom: this._hasSecretRoom,
            state: {
                enemiesCleared: false,
                initialEnemies: [],
                area: null
            },
            getCenter: function() {
                const ROOM_WORLD_SIZE = 14;
                return { x: 0, y: 0, z: 0 };
            }
        };
    }

    // Getter/setter for type with automatic synchronization
    get type() {
        return this._type;
    }

    set type(newType) {
        if (this._type !== newType) {
            const oldType = this._type;
            this._type = newType;
            this.roomData.type = newType;
            
            // Auto-sync to dungeon generator's floorLayout if available
            const dungeonGenerator = this._dungeonGeneratorRef?.deref();
            if (dungeonGenerator && dungeonGenerator.floorLayout) {
                const floorRoom = dungeonGenerator.floorLayout.get(this.id);
                if (floorRoom) {
                    floorRoom.type = newType;
                }
            }
            
            // Log the change for debugging
            if (dungeonGenerator && dungeonGenerator.debugMode) {
                console.log(`🔄 Room ${this.id} type: ${oldType} → ${newType}`);
            }
        }
    }

    // Getter/setter for shapeKey with automatic synchronization
    get shapeKey() {
        return this._shapeKey;
    }

    set shapeKey(newShape) {
        if (this._shapeKey !== newShape) {
            const oldShape = this._shapeKey;
            this._shapeKey = newShape;
            this.roomData.shapeKey = newShape;
            
            // Auto-sync to floorLayout
            const dungeonGenerator = this._dungeonGeneratorRef?.deref();
            if (dungeonGenerator && dungeonGenerator.floorLayout) {
                const floorRoom = dungeonGenerator.floorLayout.get(this.id);
                if (floorRoom) {
                    floorRoom.shapeKey = newShape;
                }
            }
            
            if (dungeonGenerator && dungeonGenerator.debugMode) {
                console.log(`🔄 Room ${this.id} shape: ${oldShape} → ${newShape}`);
            }
        }
    }

    // Getter/setter for visited with automatic synchronization
    get visited() {
        return this._visited;
    }

    set visited(newVisited) {
        if (this._visited !== newVisited) {
            this._visited = newVisited;
            this.roomData.visited = newVisited;
        }
    }

    // Getter/setter for hasSecretRoom with automatic synchronization
    get hasSecretRoom() {
        return this._hasSecretRoom;
    }

    set hasSecretRoom(hasSecret) {
        if (this._hasSecretRoom !== hasSecret) {
            this._hasSecretRoom = hasSecret;
            this.roomData.hasSecretRoom = hasSecret;
            
            const dungeonGenerator = this._dungeonGeneratorRef?.deref();
            if (dungeonGenerator && dungeonGenerator.debugMode) {
                console.log(`🔄 Room ${this.id} hasSecretRoom: ${hasSecret}`);
            }
        }
    }

    // Check how many connections this room has
    getConnectionCount() {
        return Object.values(this.neighbors).filter(n => n !== null).length;
    }

    // Enhanced sync method - ensures all data stores are consistent
    syncRoomData() {
        // Update roomData with current values (properties auto-sync, this is a safety check)
        this.roomData.id = this.id;
        this.roomData.type = this._type;
        this.roomData.shapeKey = this._shapeKey;
        this.roomData.connections = this.neighbors; // Reference same object
        this.roomData.coords = this.coords; // Reference same object  
        this.roomData.visited = this._visited;
        this.roomData.hasSecretRoom = this._hasSecretRoom;
        
        // Sync to floorLayout if available
        const dungeonGenerator = this._dungeonGeneratorRef?.deref();
        if (dungeonGenerator && dungeonGenerator.floorLayout) {
            const floorRoom = dungeonGenerator.floorLayout.get(this.id);
            if (floorRoom) {
                floorRoom.type = this._type;
                floorRoom.shapeKey = this._shapeKey;
                floorRoom.visited = this._visited;
                floorRoom.hasSecretRoom = this._hasSecretRoom;
                
                // CRITICAL: Sync event room data to floorLayout
                if (this.roomData.eventRoomData) {
                    floorRoom.eventRoomData = this.roomData.eventRoomData;
                    floorRoom.eventRoomId = this.roomData.eventRoomId;
                    floorRoom.eventRoomName = this.roomData.eventRoomName;
                    floorRoom.entranceDirection = this.roomData.entranceDirection;
                }
                
                // CRITICAL: Sync boss room data to floorLayout
                if (this.roomData.bossRoomData) {
                    floorRoom.bossRoomData = this.roomData.bossRoomData;
                    floorRoom.bossRoomKey = this.roomData.bossRoomKey;
                    floorRoom.bossRoomName = this.roomData.bossRoomName;
                    floorRoom.areaKey = this.roomData.areaKey;
                    if (dungeonGenerator && dungeonGenerator.debugMode) {
                        console.log(`🔄 Room ${this.id} synced bossRoomData with staticBrightness: ${this.roomData.bossRoomData.staticBrightness}`);
                    }
                }
            }
        }
    }

    // Validation method to ensure data consistency
    validateConsistency() {
        const errors = [];
        
        if (this.roomData.type !== this._type) {
            errors.push(`Type mismatch: roomData.type=${this.roomData.type}, room._type=${this._type}`);
        }
        
        if (this.roomData.shapeKey !== this._shapeKey) {
            errors.push(`Shape mismatch: roomData.shapeKey=${this.roomData.shapeKey}, room._shapeKey=${this._shapeKey}`);
        }
        
        const dungeonGenerator = this._dungeonGeneratorRef?.deref();
        if (dungeonGenerator && dungeonGenerator.floorLayout) {
            const floorRoom = dungeonGenerator.floorLayout.get(this.id);
            if (floorRoom && floorRoom.type !== this._type) {
                errors.push(`FloorLayout type mismatch: floorLayout.type=${floorRoom.type}, room._type=${this._type}`);
            }
        }
        
        return {
            isConsistent: errors.length === 0,
            errors: errors
        };
    }
}

// Helper function for random integer in a range
function randInt(min, max) {
    // Input validation
    if (typeof min !== 'number' || typeof max !== 'number') {
        throw new Error(`randInt: Invalid input types. Expected numbers, got ${typeof min}, ${typeof max}`);
    }
    if (min > max) {
        throw new Error(`randInt: min (${min}) cannot be greater than max (${max})`);
    }
    if (!isFinite(min) || !isFinite(max)) {
        throw new Error(`randInt: min and max must be finite numbers`);
    }
    
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Helper to get a random available direction from a room
function getRandomAvailableDirection(room, layout) {
    const directions = ['n', 's', 'e', 'w'];
    const available = directions.filter(dir => room.neighbors[dir] === null);
    if (available.length === 0) return null;
    return available[Math.floor(Math.random() * available.length)];
}

// Helper to get opposite direction (centralized)
function getOppositeDirection(dir) {
    const direction = dir?.toLowerCase();
    switch (direction) {
        case 'n': return 's';
        case 's': return 'n';
        case 'e': return 'w';
        case 'w': return 'e';
        default: return null;
    }
}

// Convert abbreviated directions to full words for event room compatibility
function getFullDirectionName(dir) {
    const direction = dir?.toLowerCase();
    switch (direction) {
        case 'n': return 'north';
        case 's': return 'south';
        case 'e': return 'east';
        case 'w': return 'west';
        default: return null;
    }
}

// --- DungeonGenerator Class (Graph-based) ---
class DungeonGenerator {
    constructor() { // Removed seed parameter
        // this.seed = seed; // Seed not currently used
        this.layout = new Map(); // Map<RoomID, Room Instance>
        this.nextRoomId = 0;
        // Keep track of room positions for placement logic
        this.roomPositions = new Set(); // Stores "x,y" strings
        // this.grid = []; // Grid representation not currently used in this version
        this.rooms = []; // List of Room instances (used for length check?)
        this.floorLayout = new Map(); // Map<RoomID, RoomData Object> - This is returned
        // this.currentTheme = null; // Store the current theme data - Renamed
        this.currentArea = null; // Store the current area data
        this.areaId = null; // Store the current area ID
        this.eventRoomManager = null; // NEW: Event room manager reference
        this.bossRoomManager = null; // NEW: Boss room manager reference
        this.currentFloorNumber = 1; // NEW: Track current floor number
        
        // Web worker setup for performance optimization
        this.workerManager = new WorkerManager();
        this.dungeonWorkerId = null;
        this.useWebWorker = this._shouldUseWebWorker();
        
        // NEW: Event room tracking across stages
        // For stage 1, always start with clean slate (no used event rooms)
        if (this.currentFloorNumber === 1) {
            this.usedEventRooms = new Set(); // Fresh start for stage 1
            this.saveUsedEventRooms(); // Clear localStorage
            console.log('🎪 Stage 1 detected - event rooms reset for fresh start');
        } else {
            this.usedEventRooms = this.getUsedEventRooms(); // Load from storage for later stages
        }

        // Optimization: Enable debug logging only in development
        try {
            this.debugMode = typeof window !== 'undefined' && 
                            (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
        } catch (error) {
            // Fallback for non-browser environments
            this.debugMode = false;
        }
    }

    // Optimized logging helper
    _log(message, ...args) {
        if (this.debugMode) {
            console.log(message, ...args);
        }
    }

    // Determine if web worker should be used
    _shouldUseWebWorker() {
        try {
            // Check if web workers are supported
            if (typeof Worker === 'undefined') {
                return false;
            }
            
            // Check if we're in a compatible environment
            if (typeof window === 'undefined') {
                return false;
            }
            
            // For now, always use web worker if available (can add more conditions later)
            return true;
        } catch (error) {
            console.warn('Web worker check failed, falling back to synchronous generation:', error);
            return false;
        }
    }

    // Initialize web worker if needed
    async _initializeWorker() {
        if (!this.useWebWorker || this.dungeonWorkerId) {
            return;
        }

        try {
            // Initialize worker pool for dungeon generation
            await this.workerManager.initializeWorkerPool(
                'dungeon',
                './src/workers/dungeonWorker.js',
                { debugMode: this.debugMode },
                2 // Pool size for dungeon workers
            );

            // Get a worker from the pool
            this.dungeonWorkerId = await this.workerManager.getOrCreateWorker(
                'dungeon',
                './src/workers/dungeonWorker.js',
                { debugMode: this.debugMode }
            );

            if (this.dungeonWorkerId) {
                this._log('Dungeon web worker initialized from pool');
            } else {
                console.warn('No dungeon workers available, falling back to synchronous');
                this.useWebWorker = false;
            }
        } catch (error) {
            console.warn('Failed to initialize dungeon web worker, falling back to synchronous:', error);
            this.useWebWorker = false;
        }
    }

    // Convert worker result back to Room instances for compatibility
    _deserializeWorkerResult(workerResult) {
        const { layout, roomCount, metadata } = workerResult;
        
        // Clear existing data
        this.layout.clear();
        this.floorLayout.clear();
        this.roomPositions.clear();
        this.nextRoomId = 0;

        // Convert serialized rooms back to Room instances
        for (const [roomIdStr, roomData] of Object.entries(layout)) {
            const roomId = parseInt(roomIdStr);
            
            // Create Room instance
            const room = new Room(roomId, this);
            room.type = roomData.type;
            room.shapeKey = roomData.shapeKey; // This should preserve the worker's shape assignment
            room.coords = roomData.coords;
            
            // DEBUG: Verify shape assignment
            console.log(`🏗️ [Deserialize] Room ${roomId}: type=${roomData.type}, shapeKey=${roomData.shapeKey}`);
            room.neighbors = roomData.connections; // Note: worker uses 'connections', Room uses 'neighbors'
            room.visited = roomData.visited;
            room.hasSecretRoom = roomData.hasSecretRoom;
            room.roomData.state = roomData.state;
            
            // CRITICAL: Handle entrance direction for boss rooms
            if (roomData.preferredEntranceDirection) {
                room.preferredEntranceDirection = roomData.preferredEntranceDirection;
                room.roomData.entranceDirection = roomData.preferredEntranceDirection;
            }
            
            // CRITICAL: Ensure roomData.connections points to the same object as room.neighbors
            // This is essential for door generation and other systems that read room.connections
            room.roomData.connections = room.neighbors;
            
            // CRITICAL: Add missing methods that room systems expect
            room.roomData.getCenter = function() {
                const ROOM_WORLD_SIZE = 14;
                return { x: 0, y: 0, z: 0 };
            };
            
            // CRITICAL: Ensure all expected room properties are initialized
            // These properties are required by various room systems (doors, objects, rendering)
            if (!room.roomData.state.enemiesCleared) {
                room.roomData.state.enemiesCleared = false;
            }
            if (!room.roomData.state.initialEnemies) {
                room.roomData.state.initialEnemies = [];
            }
            
            // Ensure visited flag is synchronized between room and roomData
            room.roomData.visited = room.visited;
            room.roomData.hasSecretRoom = room.hasSecretRoom;
            
            // CRITICAL: Ensure roomData has all properties the minimap expects
            room.roomData.id = room.id;
            room.roomData.type = room.type;
            room.roomData.coords = room.coords;

            // Add to layout and floorLayout
            this.layout.set(roomId, room);
            this.floorLayout.set(roomId, room.roomData);
            
            // Track position
            const posKey = `${room.coords.x},${room.coords.y}`;
            this.roomPositions.add(posKey);
            
            // Update nextRoomId
            if (roomId >= this.nextRoomId) {
                this.nextRoomId = roomId + 1;
            }
        }

        this._log(`Deserialized ${roomCount} rooms from web worker`);
        return this.floorLayout;
    }

    // Apply game-specific post-processing that requires access to game managers
    async _applyGameSpecificPostProcessing(areaId) {
        this._log('Applying game-specific post-processing');

        // Set current area data
        this.areaId = areaId;
        this.currentArea = getAreaData(areaId);

        // Apply area to all rooms
        this.layout.forEach(room => {
            room.roomData.state.area = areaId;
        });

        // Apply event room placement (requires EventRoomManager)
        try {
            await this._placeEventRooms();
        } catch (error) {
            console.warn('Event room placement failed:', error);
        }

        // Apply boss room configuration (requires BossRoomManager)
        try {
            if (this.bossRoomManager) {
                const bossRooms = Array.from(this.layout.values()).filter(room => room.type === 'Boss');
                for (const bossRoom of bossRooms) {
                    this.bossRoomManager.applyBossRoomConfig(bossRoom.roomData, areaId);
                    bossRoom.syncRoomData();
                }
            }
        } catch (error) {
            console.warn('Boss room configuration failed:', error);
        }

        // Final data consistency validation
        this._validateDataConsistency();
    }

    // Cleanup method for proper resource management
    cleanup() {
        if (this.workerManager && this.dungeonWorkerId) {
            this.workerManager.terminateWorker(this.dungeonWorkerId);
            this.dungeonWorkerId = null;
        }
    }

    // Method to get performance metrics from web worker
    async getWorkerPerformanceMetrics() {
        if (!this.useWebWorker || !this.dungeonWorkerId) {
            return null;
        }

        try {
            const response = await this.workerManager.sendMessage(
                this.dungeonWorkerId,
                { type: 'GET_PERFORMANCE_METRICS', data: {} }
            );
            return response.result;
        } catch (error) {
            console.warn('Failed to get worker performance metrics:', error);
            return null;
        }
    }

    _addRoom(x, y, type = 'Normal') {
        const roomId = this.nextRoomId++;
        const newRoom = new Room(roomId, this); // Pass dungeonGenerator reference for auto-sync
        newRoom.type = type;
        newRoom.coords = { x, y }; // Set grid coordinates

        // --- Assign Room Shape based on Area ---
        let assignedShape = 'SQUARE_1X1'; // Default shape
        if (type === 'Normal' && this.currentArea && this.currentArea.allowedShapes && this.currentArea.allowedShapes.length > 0) {
            // Select a random shape from the area's allowed list
            const shapes = this.currentArea.allowedShapes;
            assignedShape = shapes[Math.floor(Math.random() * shapes.length)];
            console.log(`🏗️ Room ${roomId}: Assigned shape '${assignedShape}' from area '${this.areaId}' (${shapes.length} available)`);
        } else {
            // Keep special rooms (Start, Boss) or rooms in areas without defined shapes as squares
            assignedShape = 'SQUARE_1X1';
            if (type === 'Normal') {
                console.warn(`⚠️ Room ${roomId}: Area '${this.areaId}' has no allowedShapes (currentArea:${!!this.currentArea}, allowedShapes:${this.currentArea?.allowedShapes?.length}). Defaulting to '${assignedShape}'.`);
            }
        }
        newRoom.shapeKey = assignedShape;
        // --- End Shape Assignment ---

        // Note: Manual sync no longer needed - properties auto-sync via getters/setters

        // Remove incorrect boundary check based on undefined width/depth
        /*
        // Check boundaries
        if (x + newRoom.width >= this.gridSize || y + newRoom.depth >= this.gridSize || x < 0 || y < 0) {
            console.warn(`Room placement failed: Out of bounds at ${x},${y}`);
            return null;
        }
        */

        // --- Setup roomData State ---
        let initialEnemies = [];
        // Populate enemies based on area for non-Start/Boss rooms (skip room 0)
        if (type === 'Normal' && newRoom.id !== 0 && this.currentArea && this.currentArea.enemies && this.currentArea.enemies.length > 0) {
             // Simple weighted random selection (example: choose 1-3 enemies)
             const numEnemies = randInt(1, 3);
             const enemyPool = this.currentArea.enemies;
             const totalWeight = enemyPool.reduce((sum, enemy) => sum + enemy.weight, 0);

             if (totalWeight > 0) {
                 for (let i = 0; i < numEnemies; i++) {
                     let randomWeight = Math.random() * totalWeight;
                     for (const enemy of enemyPool) {
                         randomWeight -= enemy.weight;
                         if (randomWeight <= 0) {
                             initialEnemies.push(enemy.type); // Add the chosen enemy type string
                             break;
                         }
                     }
                 }
             }
             // console.log(`   - Room ${newRoom.id} (${type}) enemies: [${initialEnemies.join(', ')}] (Area: ${this.areaId})`);
        }

        newRoom.roomData.state = {
            enemiesCleared: false,
            initialEnemies: initialEnemies,
            // theme: this.themeName // OLD: Store the theme name for the handler
            area: this.areaId // NEW: Store the area ID
        };
        // --- End State Setup ---

        this.layout.set(roomId, newRoom); // Store Room instance internally
        this.roomPositions.add(`${x},${y}`);
        this.rooms.push(newRoom);

        // Store room data (sync will happen at the end of generation)
        this.floorLayout.set(newRoom.id, newRoom.roomData); // Store final RoomData object
        return newRoom; // Return the Room instance for graph building
    }

    _canPlaceRoom(x, y) {
        // Check if the coordinate is already occupied
        return !this.roomPositions.has(`${x},${y}`);
    }

    // --- Main Generation Method (Growing Tree Algorithm) ---
    async generateLayout(areaId = 'catacombs', _retryCount = 0) { // NEW
        // Input validation
        if (typeof areaId !== 'string' || areaId.trim().length === 0) {
            throw new Error(`generateLayout: Invalid areaId. Expected non-empty string, got: ${typeof areaId}`);
        }
        if (typeof _retryCount !== 'number' || _retryCount < 0 || !Number.isInteger(_retryCount)) {
            throw new Error(`generateLayout: Invalid _retryCount. Expected non-negative integer, got: ${_retryCount}`);
        }

        console.log("🎪 generateLayout() called - this should include event room placement");

        // --- Web Worker Path ---
        if (this.useWebWorker) {
            try {
                await this._initializeWorker();
                
                if (this.dungeonWorkerId) {
                    this._log('Using web worker for dungeon generation');
                    
                    const areaData = getAreaData(areaId);
                    console.log(`🚀 Sending to worker: areaId=${areaId}, allowedShapes=${areaData?.allowedShapes?.length}`, areaData);
                    const workerParams = {
                        areaId,
                        areaData: areaData, // Pass full area data to worker
                        minRooms: MIN_ROOMS,
                        maxRooms: MAX_ROOMS,
                        debugMode: this.debugMode,
                        retryCount: _retryCount
                    };

                    const response = await this.workerManager.sendMessage(
                        this.dungeonWorkerId,
                        {
                            type: 'GENERATE_DUNGEON',
                            data: workerParams
                        }
                    );

                    // Deserialize worker result and apply post-processing
                    const workerLayout = this._deserializeWorkerResult(response.result);
                    
                    // Apply post-processing that requires game-specific logic
                    await this._applyGameSpecificPostProcessing(areaId);
                    
                    this._log(`Web worker generation completed: ${this.floorLayout.size} rooms`);
                    return this.floorLayout;
                }
            } catch (error) {
                console.warn('Web worker generation failed, falling back to synchronous:', error);
                this.useWebWorker = false;
            }
        }

        // --- Synchronous Fallback Path (Original Implementation) ---
        console.log('🔄 Using synchronous dungeon generation - currentArea allowedShapes:', this.currentArea?.allowedShapes?.length);
        this._log('Using synchronous dungeon generation');

        // --- Retry Limit Check ---
        if (_retryCount > MAX_GENERATION_RETRIES) {
            console.error(`DungeonGenerator: CRITICAL - Exceeded max retries (${MAX_GENERATION_RETRIES}) trying to generate at least ${MIN_ROOMS} rooms. Returning last attempt with ${this.layout.size} rooms.`);
            // Return the potentially too-small layout to prevent infinite loop
            return this.floorLayout;
        }
        // --- End Retry Limit Check ---

        // --- Calculate Target Room Count for this specific generation ---
        const currentTargetRooms = randInt(MIN_ROOMS, MAX_ROOMS);
        // console.log(`--- Generating Dungeon Layout (Theme: ${themeName}, Attempt: ${_retryCount + 1}, Target Rooms: ${currentTargetRooms}) ---`); // OLD
        this._log(`--- Generating Dungeon Layout (Area: ${areaId}, Attempt: ${_retryCount + 1}, Target Rooms: ${currentTargetRooms}) ---`);
        // -------------------------------------------------------------

        // this.themeName = themeName; // Store the theme name - OLD
        this.areaId = areaId; // Store the area ID - NEW
        // this.currentTheme = getThemeData(themeName); // OLD
        this.currentArea = getAreaData(areaId); // NEW
        // if (!this.currentTheme) { // OLD
        if (!this.currentArea) { // NEW
            // console.error(`Error: Theme "${themeName}" not found! Defaulting...`); // OLD
            console.error(`Error: Area "${areaId}" not found! Defaulting...`); // NEW
            // this.themeName = 'CATACUMBS'; // Fallback? - OLD
            this.areaId = 'catacombs'; // Fallback - NEW
            // this.currentTheme = getThemeData(this.themeName); // OLD
            this.currentArea = getAreaData(this.areaId); // NEW
            // if (!this.currentTheme) { // OLD
            if (!this.currentArea) { // NEW
                throw new Error("Default area 'catacombs' also not found!");
            }
        }

        this._log("DungeonGenerator: Starting graph generation (Growing Tree)...");
        // Reset state for new generation
        this.layout.clear();
        this.floorLayout.clear();
        this.roomPositions.clear();
        this.rooms = [];
        this.nextRoomId = 0;

        const directions = {
            n: { x: 0, y: -1 }, // Assuming Y decreases going North
            s: { x: 0, y: 1 },  // Assuming Y increases going South
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 },
        };

        // 1. Create the starting room
        const startRoom = this._addRoom(0, 0, 'Start');
        startRoom.visited = true;
        startRoom.roomData.visited = true; // Sync visited status
        const activeRooms = [startRoom]; // List of rooms to expand from

        let attempts = 0;
        const maxTotalAttempts = MAX_PLACEMENT_ATTEMPTS * currentTargetRooms; // Adjust attempt limit based on target?

        // 2. Grow the dungeon tree - Use currentTargetRooms in condition
        while (activeRooms.length > 0 && this.layout.size < currentTargetRooms && attempts < maxTotalAttempts) {
            attempts++;
            // Choose a room to expand from (newest for longer corridors)
            const currentRoom = activeRooms[activeRooms.length - 1];

            // Find a random available direction from the current room
            const availableDirections = Object.keys(directions).filter(dir => {
                const nx = currentRoom.coords.x + directions[dir].x;
                const ny = currentRoom.coords.y + directions[dir].y;
                return this._canPlaceRoom(nx, ny); // Check if the target cell is empty
            });

            if (availableDirections.length > 0) {
                // Pick a random direction
                const chosenDir = availableDirections[Math.floor(Math.random() * availableDirections.length)];
                const oppositeDir = getOppositeDirection(chosenDir);

                // Coordinates for the new room
                const newX = currentRoom.coords.x + directions[chosenDir].x;
                const newY = currentRoom.coords.y + directions[chosenDir].y;

                // Create the new room
                const newRoom = this._addRoom(newX, newY);

                // Connect the rooms (both Room instance and roomData neighbors)
                currentRoom.neighbors[chosenDir] = newRoom.id;
                newRoom.neighbors[oppositeDir] = currentRoom.id;
                // Ensure roomData neighbors are also updated (they should reference the same object)
                // currentRoom.roomData.neighbors[chosenDir] = newRoom.id;
                // newRoom.roomData.neighbors[oppositeDir] = currentRoom.id;

                // Add the new room to the active list
                activeRooms.push(newRoom);
            } else {
                // If no available directions, backtrack
                activeRooms.pop();
            }
        }

        // --- MANDATORY: Ensure Start Room (ID 0) ALWAYS has a NORTH exit ---
        const startRoomInstance = this.layout.get(0);
        if (startRoomInstance && MAX_ROOMS >= 2) {
            console.log(`DungeonGenerator: MANDATORY - Ensuring Start Room 0 has a north connection...`);

            // Check if room 0 already has a north connection
            if (!startRoomInstance.neighbors.n) {
                console.log(`DungeonGenerator: Room 0 missing north connection. FORCING north room creation...`);

                const nx = startRoomInstance.coords.x;
                const ny = startRoomInstance.coords.y - 1; // North is -Y

                // Check if there's already a room to the north
                let northRoom = null;
                for (const room of this.layout.values()) {
                    if (room.coords.x === nx && room.coords.y === ny) {
                        northRoom = room;
                        break;
                    }
                }

                if (northRoom) {
                    // Room exists to the north, just connect them
                    console.log(` -> [North Connection] Found existing room ${northRoom.id} to the north. Connecting...`);
                    startRoomInstance.neighbors.n = northRoom.id;
                    northRoom.neighbors.s = startRoomInstance.id;
                    console.log(` -> [North Connection Success] Connected to existing room ${northRoom.id}`);
                } else {
                    // No room to the north, create one
                    console.log(` -> [North Connection] No room to the north. Creating new room...`);
                    northRoom = this._addRoom(nx, ny, 'Normal');
                    if (northRoom) {
                        console.log(` -> [North Connection] Created new room ${northRoom.id} at (${nx}, ${ny})`);
                        startRoomInstance.neighbors.n = northRoom.id;
                        northRoom.neighbors.s = startRoomInstance.id;
                        console.log(` -> [North Connection Success] Connected to new room ${northRoom.id}`);
                    } else {
                        console.error(` -> [North Connection FAILED] Could not create north room at (${nx}, ${ny})`);
                        // This is a critical failure - regenerate the entire dungeon
                        console.error(`DungeonGenerator: CRITICAL - Cannot create mandatory north connection for Room 0. Regenerating dungeon...`);
                        if (_retryCount < 3) {
                            return await this.generateLayout(areaId, _retryCount + 1);
                        } else {
                            console.error(`DungeonGenerator: FATAL - Failed to create north connection after 3 retries!`);
                        }
                    }
                }
            } else {
                console.log(`DungeonGenerator: Room 0 already has north connection to room ${startRoomInstance.neighbors.n}. ✅`);
            }

            // Final verification
            if (startRoomInstance.neighbors.n) {
                console.log(`DungeonGenerator: ✅ MANDATORY north connection for Room 0 confirmed.`);
            } else {
                console.error(`DungeonGenerator: ❌ FAILED to establish mandatory north connection for Room 0!`);
            }
        }
        // --- End Safeguard ---

        // Log if the *random target* wasn't reached (can happen due to space/attempts)
        if (this.layout.size < currentTargetRooms && _retryCount === 0) {
            console.warn(`DungeonGenerator: Could only generate ${this.layout.size} rooms (target: ${currentTargetRooms}). Limit: ${maxTotalAttempts} attempts.`);
        }

        // --- SIMPLIFIED AND ORDERED POST-PROCESSING ---
        this._log("=== STARTING SIMPLIFIED POST-PROCESSING ===");

        // Step 1: Assign all special rooms FIRST while we have Normal rooms available
        this._assignAllSpecialRooms();

        // Step 2: Protect critical connections before any cleanup
        this._protectCriticalConnections();

        // Step 3: Validate and fix connections in a single optimized pass
        this._validateAndFixConnections();

        // Step 4: Remove unreachable rooms (if any)
        this._removeUnreachableRooms();

        // Step 5: Final verification
        this._verifyFinalLayout();

        // Step 5.5: Data consistency validation (NEW)
        this._validateDataConsistency();

        // Step 6: Event Room Placement (NEW)
        console.log("🎪 About to call _placeEventRooms()...");
        console.log(`🎪 Current room types before event placement:`, Array.from(this.layout.values()).map(r => `${r.id}:${r.type}`).join(', '));
        console.log(`🎪 Current layout size: ${this.layout.size}, floorLayout size: ${this.floorLayout.size}`);
        
        // NO FALLBACK SYSTEMS - make the primary system work perfectly
        await this._placeEventRooms();
        
        console.log("🎪 _placeEventRooms() completed successfully");
        console.log(`🎪 Current room types after event placement:`, Array.from(this.layout.values()).map(r => `${r.id}:${r.type}`).join(', '));
        console.log(`🎪 Final layout size: ${this.layout.size}, floorLayout size: ${this.floorLayout.size}`);
        
        // Debug: Check for EVENT rooms
        const eventRooms = Array.from(this.layout.values()).filter(r => r.type === 'EVENT');
        console.log(`🎪 Event rooms found: ${eventRooms.length}`);
        eventRooms.forEach(er => {
            console.log(`🎪 Event Room ${er.id}: type=${er.type}, roomData.type=${er.roomData.type}, name=${er.roomData.eventRoomName}`);
        })

        // Step 7: Generate ASCII Map (NEW)
        this._log("=== GENERATING ASCII MAP ===");
        console.log("🎪 About to generate ASCII map...");
        this.generateASCIIMap();
        console.log("🎪 ASCII map generation complete");

        this._log("=== POST-PROCESSING COMPLETE ===");
        
        // CRITICAL: Verify special room counts
        let bossCount = 0, eventCount = 0, secretHostCount = 0;
        this.layout.forEach(room => {
            if (room.type === 'Boss') bossCount++;
            if (room.type === 'EVENT') eventCount++;
            if (room.hasSecretRoom) secretHostCount++;
        });
        
        console.log("🎯 SPECIAL ROOM VERIFICATION:");
        console.log(`  Boss Rooms: ${bossCount} ${bossCount === 1 ? '✅' : '❌'}`);
        console.log(`  Event Rooms: ${eventCount} ${eventCount === 1 ? '✅' : '❌'}`);
        console.log(`  Secret Room Hosts: ${secretHostCount} ${secretHostCount === 1 ? '✅' : '❌'}`);
        
        if (bossCount !== 1 || eventCount !== 1 || secretHostCount !== 1) {
            console.error("❌ CRITICAL: Special room counts are incorrect!");
        }

        // Final sync before returning?
        this.layout.forEach(room => room.syncRoomData());

        this._log(`--- Layout Generation Attempt Complete: ${this.layout.size} rooms ---`);

        // --- GUARANTEED MINIMUM ROOM COUNT ENFORCEMENT ---
        if (this.layout.size < MIN_ROOMS) {
            console.warn(`DungeonGenerator: Generated layout has ${this.layout.size} rooms (min: ${MIN_ROOMS}). Adding rooms intelligently...`);
            
            const roomsToAdd = MIN_ROOMS - this.layout.size;
            const successfullyAdded = this._intelligentlyAddRooms(roomsToAdd);
            
            if (successfullyAdded < roomsToAdd && _retryCount < MAX_GENERATION_RETRIES) {
                console.warn(`DungeonGenerator: Could only add ${successfullyAdded}/${roomsToAdd} rooms. Retrying generation... (Attempt ${_retryCount + 2})`);
                return await this.generateLayout(areaId, _retryCount + 1);
            }
            
            console.log(`DungeonGenerator: ✅ Added ${successfullyAdded} rooms. Final count: ${this.layout.size}`);
        }
        // --- End Guaranteed Room Count Enforcement ---

        // If minimum size is met (or retries exhausted)
        this._log(`Final floorLayout Map (${this.layout.size} rooms):`, this.floorLayout);
        return this.floorLayout; // Return the map of RoomData objects
    }

    // Helper method to calculate actual path distance from start room using BFS
    _calculatePathDistances() {
        const distances = new Map();
        const queue = [{ room: this.layout.get(0), distance: 0 }];
        const visited = new Set([0]);
        
        while (queue.length > 0) {
            const { room, distance } = queue.shift();
            distances.set(room.id, distance);
            
            // Check all neighbors
            for (const direction of ['n', 's', 'e', 'w']) {
                const neighborId = room.neighbors[direction];
                if (neighborId !== null && !visited.has(neighborId)) {
                    visited.add(neighborId);
                    const neighborRoom = this.layout.get(neighborId);
                    if (neighborRoom) {
                        queue.push({ room: neighborRoom, distance: distance + 1 });
                    }
                }
            }
        }
        
        return distances;
    }

    // Assign Boss Room to the furthest room from Start (using actual path distance)
     _assignBossRoom() {
        console.log("=== Boss Room Assignment Debug ===");
        if (this.layout.size <= 1) {
            console.warn("Cannot assign Boss room, only Start room exists.");
            return;
        }

        // CRITICAL: Check if we have enough Normal rooms for all special assignments
        const normalRooms = this._getNormalRooms();
        console.log(`Available Normal rooms for boss assignment: ${normalRooms.length}`);
        console.log(`Normal room IDs:`, normalRooms.map(r => r.id));

        if (normalRooms.length < 3) {
            console.error(`❌ CRITICAL: Not enough Normal rooms (${normalRooms.length}) for special room assignments! Need at least 3 (Boss + Event + Secret).`);
            console.error(`Total rooms: ${this.layout.size}, Normal rooms: ${normalRooms.length}`);

            // Log all room types for debugging
            const allRooms = Array.from(this.layout.values());
            const roomsByType = {};
            allRooms.forEach(room => {
                if (!roomsByType[room.type]) roomsByType[room.type] = [];
                roomsByType[room.type].push(room.id);
            });
            console.error(`Room distribution:`, roomsByType);

            // If we don't have enough rooms, skip special room assignments
            if (normalRooms.length === 0) {
                console.error("❌ No Normal rooms available - cannot assign boss room!");
                return;
            }
        }

        // Calculate actual path distances from start room
        const pathDistances = this._calculatePathDistances();
        console.log("Path distances from start:", Array.from(pathDistances.entries()));

        // Find the furthest room from start using PATH distance (not coordinate distance)
        let furthestRoom = null;
        let maxPathDistance = -1;

        // Get boss room configuration to check door requirements
        const areaKey = 'catacombs'; // Default area for now
        const bossConfig = this.bossRoomManager?.getBossRoomConfig(areaKey);
        const fullRequiredEntrance = bossConfig?.primaryEntrance || 'south'; // Boss rooms should be entered from south
        // Convert to abbreviated direction for dungeon generator
        const requiredEntrance = fullRequiredEntrance[0]; // 's' for 'south'
        console.log(`Boss room configuration: requires entrance from ${fullRequiredEntrance} (${requiredEntrance})`);

        // First pass: Look for dead-end rooms with correct entrance direction at maximum path distance
        console.log("=== FIRST PASS: Looking for dead-end Normal rooms with correct entrance at max path distance ===");
        const MIN_BOSS_DISTANCE = 3; // Minimum path distance from Room 0 to boss room
        
        for (const room of normalRooms) {
            const connectionCount = room.getConnectionCount();
            const pathDistance = pathDistances.get(room.id) || 0;

            console.log(`Checking room ${room.id} - Connections: ${connectionCount}, Path Distance: ${pathDistance}, Coords: (${room.coords.x}, ${room.coords.y})`);

            // Skip rooms that are too close to the start
            if (pathDistance < MIN_BOSS_DISTANCE) {
                console.log(`  Skipping room ${room.id} - too close to start (distance: ${pathDistance} < ${MIN_BOSS_DISTANCE})`);
                continue;
            }

            if (connectionCount === 1) {
                // Check if the single connection is from the required direction
                const hasRequiredEntrance = room.neighbors[requiredEntrance] !== null;
                
                if (hasRequiredEntrance && pathDistance > maxPathDistance) {
                    maxPathDistance = pathDistance;
                    furthestRoom = room;
                    console.log(`New furthest dead-end room with ${requiredEntrance} entrance found: Room ${room.id} at path distance ${pathDistance}`);
                }
            }
        }

        // Second pass: If no suitable dead ends found, convert furthest room (by path distance) to dead-end
        if (!furthestRoom) {
            console.log("=== SECOND PASS: No suitable dead-end rooms found, will create dead-end from furthest room (by path) ===");
            maxPathDistance = -1;
            let candidateRoom = null;

            for (const room of normalRooms) {
                const pathDistance = pathDistances.get(room.id) || 0;

                // Skip rooms that are too close to the start
                if (pathDistance < MIN_BOSS_DISTANCE) {
                    console.log(`  Skipping room ${room.id} - too close to start (distance: ${pathDistance} < ${MIN_BOSS_DISTANCE})`);
                    continue;
                }

                // CRITICAL: Boss room must have or be able to get a south connection
                const hasRequiredConnection = room.neighbors[requiredEntrance] !== null;
                
                // If no south connection exists, check if there's a room to the south that could connect
                let canGetSouthConnection = hasRequiredConnection;
                if (!hasRequiredConnection && requiredEntrance === 's') {
                    const southCoords = { x: room.coords.x, y: room.coords.y + 1 };
                    const roomToSouth = Array.from(this.layout.values()).find(r => 
                        r.coords.x === southCoords.x && r.coords.y === southCoords.y
                    );
                    canGetSouthConnection = roomToSouth !== undefined;
                    console.log(`Room ${room.id} - checking south connection: hasConnection=${hasRequiredConnection}, roomToSouth=${roomToSouth?.id || 'none'}, canConnect=${canGetSouthConnection}`);
                }
                
                // Only consider rooms that can have a south connection
                if (canGetSouthConnection) {
                    const adjustedDistance = hasRequiredConnection ? pathDistance + 100 : pathDistance; // Strongly prefer rooms with existing connection

                    if (adjustedDistance > maxPathDistance) {
                        maxPathDistance = adjustedDistance;
                        candidateRoom = room;
                        console.log(`New furthest room candidate: Room ${room.id} at path distance ${pathDistance} (adjusted: ${adjustedDistance}, has ${requiredEntrance}: ${hasRequiredConnection})`);
                    }
                } else {
                    console.log(`Skipping room ${room.id} - cannot establish required south connection`);
                }
            }

            if (candidateRoom) {
                console.log(`🔧 Converting Room ${candidateRoom.id} to dead-end for boss room (was ${candidateRoom.getConnectionCount()} connections)`);
                
                // Mark as boss room first so _convertToDeadEnd knows it's a boss room
                candidateRoom.type = 'Boss';
                
                // CRITICAL: Ensure south connection exists before converting to dead-end
                if (requiredEntrance === 's' && candidateRoom.neighbors.s === null) {
                    console.log(`🔧 Boss room ${candidateRoom.id} missing south connection - establishing connection`);
                    const southCoords = { x: candidateRoom.coords.x, y: candidateRoom.coords.y + 1 };
                    const roomToSouth = Array.from(this.layout.values()).find(r => 
                        r.coords.x === southCoords.x && r.coords.y === southCoords.y
                    );
                    if (roomToSouth) {
                        // Establish bidirectional connection
                        candidateRoom.neighbors.s = roomToSouth.id;
                        roomToSouth.neighbors.n = candidateRoom.id;
                        console.log(`✅ Established south connection: Boss room ${candidateRoom.id} ← Room ${roomToSouth.id}`);
                    } else {
                        console.error(`❌ CRITICAL: No room found to south of boss room ${candidateRoom.id} at (${southCoords.x}, ${southCoords.y})`);
                    }
                }
                
                // Force convert to dead-end by removing all but one connection
                // Boss room prefers the required entrance direction
                this._convertToDeadEnd(candidateRoom, requiredEntrance); // Already abbreviated
                furthestRoom = candidateRoom;
                
                console.log(`✅ Room ${candidateRoom.id} converted to dead-end (now ${candidateRoom.getConnectionCount()} connections)`);
            } else {
                // FALLBACK: If no room meets minimum distance, just use the furthest room available
                console.log(`⚠️ WARNING: No room meets minimum distance requirement (${MIN_BOSS_DISTANCE}). Using furthest available room.`);
                maxPathDistance = -1;
                
                for (const room of normalRooms) {
                    const pathDistance = pathDistances.get(room.id) || 0;
                    if (pathDistance > maxPathDistance) {
                        maxPathDistance = pathDistance;
                        candidateRoom = room;
                    }
                }
                
                if (candidateRoom) {
                    console.log(`🔧 FALLBACK: Converting Room ${candidateRoom.id} to dead-end for boss room (distance: ${maxPathDistance})`);
                    candidateRoom.type = 'Boss';
                    this._convertToDeadEnd(candidateRoom, requiredEntrance);
                    furthestRoom = candidateRoom;
                }
            }
        }

        if (furthestRoom) {
            const actualPathDistance = pathDistances.get(furthestRoom.id) || 0;
            console.log(`✅ Assigning Room ${furthestRoom.id} as FINAL Boss Room (Path Distance: ${actualPathDistance}, Connections: ${furthestRoom.getConnectionCount()})`);
            console.log(`🎯 Boss room is a dead-end - players must explore all other rooms before final battle`);

            // CRITICAL: Mark as boss room
            furthestRoom.type = 'Boss'; // Auto-syncs to roomData and floorLayout
            furthestRoom.shapeKey = 'BOSS_ARENA'; // Auto-syncs to roomData and floorLayout
            furthestRoom.roomData.state.initialEnemies = []; // No boss for now
            
            // Store the area key for later boss room configuration
            const areaKey = furthestRoom.roomData.state.area || 'catacombs';
            furthestRoom.roomData.areaKey = areaKey;
            furthestRoom.roomData.bossRoomKey = `${areaKey}_boss_room`;
            
            // Apply boss room configuration using BossRoomManager
            if (this.bossRoomManager) {
                this.bossRoomManager.applyBossRoomConfig(furthestRoom.roomData, areaKey);
                console.log(`✅ Applied boss room configuration for area: ${areaKey}`);
                
                // Store the entrance direction on the room data (similar to event rooms)
                const entranceDirection = furthestRoom.neighbors.s !== null ? 'south' : 
                                         furthestRoom.neighbors.n !== null ? 'north' :
                                         furthestRoom.neighbors.e !== null ? 'east' : 'west';
                furthestRoom.roomData.entranceDirection = entranceDirection;
                console.log(`🚪 Boss room entrance direction: ${entranceDirection}`);
                
                // CRITICAL: Sync boss room data to floor layout after configuration
                furthestRoom.syncRoomData();
                console.log(`🔄 Synced boss room data to floor layout`);
                
                // Verify the sync worked
                const floorRoomData = this.floorLayout.get(furthestRoom.id);
                if (floorRoomData) {
                    console.log(`🔍 Verifying boss room sync for room ${furthestRoom.id}:`);
                    console.log(`  - Has bossRoomData: ${!!floorRoomData.bossRoomData}`);
                    if (floorRoomData.bossRoomData) {
                        console.log(`  - staticBrightness: ${floorRoomData.bossRoomData.staticBrightness}`);
                    }
                } else {
                    console.error(`❌ Failed to find room ${furthestRoom.id} in floor layout after sync!`);
                }
            } else {
                console.log(`✅ Marked room as boss room for area: ${areaKey} (no BossRoomManager available)`);
            }

            // Note: Manual floorLayout sync no longer needed - handled automatically

            console.log(`Boss room data:`, {
                id: furthestRoom.id,
                type: furthestRoom.type,
                shapeKey: furthestRoom.shapeKey,
                coords: furthestRoom.coords,
                connections: furthestRoom.neighbors,
                distanceFromStart: actualPathDistance
            });
        } else {
            console.error("❌ CRITICAL: No Normal room found for boss room assignment!");
        }
        console.log("=== End Boss Room Assignment ===");
    }

    /**
     * Get coordinate offset for a direction
     */
    _getDirectionOffset(dir) {
        switch (dir) {
            case 'n': return { x: 0, y: -1 };
            case 's': return { x: 0, y: 1 };
            case 'e': return { x: 1, y: 0 };
            case 'w': return { x: -1, y: 0 };
            default: return { x: 0, y: 0 };
        }
    }

    /**
     * Convert a room to a dead-end by removing all but one connection
     * This ensures the boss room has only one entrance/exit
     */
    _convertToDeadEnd(room, preferredDirection = null) {
        console.log(`🔧 Converting room ${room.id} to dead-end...`);
        const directions = ['n', 's', 'e', 'w'];
        const connections = [];
        
        // Find all current connections
        directions.forEach(dir => {
            if (room.neighbors[dir] !== null) {
                connections.push({
                    direction: dir,
                    connectedRoomId: room.neighbors[dir],
                    connectedRoom: this.layout.get(room.neighbors[dir])
                });
            }
        });
        
        console.log(`Room ${room.id} has ${connections.length} connections:`, connections.map(c => `${c.direction}->${c.connectedRoomId}`));
        
        if (connections.length <= 1) {
            console.log(`Room ${room.id} already has ${connections.length} connections, no conversion needed`);
            return;
        }
        
        // Check if this is a boss room
        const isBossRoom = room.type === 'Boss' || room.roomData?.type === 'Boss';
        
        let keepConnection;
        
        if (isBossRoom && preferredDirection === 's') {
            // CRITICAL: Boss room MUST have south connection (enters from south, has south door)
            const southConnection = connections.find(c => c.direction === 's');
            if (southConnection) {
                keepConnection = southConnection;
                console.log(`Boss room: keeping REQUIRED south connection for south door configuration`);
            } else {
                console.error(`❌ CRITICAL: Boss room ${room.id} has no south connection! Available connections:`, connections.map(c => c.direction));
                // This should never happen if boss assignment logic is correct
                throw new Error(`Boss room ${room.id} must have a south connection but none found`);
            }
        }
        
        if (!keepConnection) {
            // Smart selection: prefer keeping the connection closest to Room 0 (maintains path to start)
            const room0 = this.layout.get(0);
            let bestConnection = connections[0];
            let shortestDistance = Infinity;
            
            connections.forEach(conn => {
                if (conn.connectedRoom) {
                    const distance = Math.abs(conn.connectedRoom.coords.x - room0.coords.x) + 
                                   Math.abs(conn.connectedRoom.coords.y - room0.coords.y);
                    if (distance < shortestDistance) {
                        shortestDistance = distance;
                        bestConnection = conn;
                    }
                }
            });
            
            keepConnection = bestConnection;
        }
        
        const removeConnections = connections.filter(c => c !== keepConnection);
        
        console.log(`Keeping connection: ${keepConnection.direction}->${keepConnection.connectedRoomId}`);
        console.log(`Removing connections:`, removeConnections.map(c => `${c.direction}->${c.connectedRoomId}`));
        
        // Remove the unwanted connections
        removeConnections.forEach(conn => {
            const oppositeDir = this._getOppositeDirection(conn.direction);
            
            // Remove from this room
            room.neighbors[conn.direction] = null;
            
            // Remove from connected room
            if (conn.connectedRoom) {
                conn.connectedRoom.neighbors[oppositeDir] = null;
                console.log(`Removed bidirectional connection: Room ${room.id}(${conn.direction}) <-> Room ${conn.connectedRoomId}(${oppositeDir})`);
            }
        });
        
        console.log(`✅ Room ${room.id} converted to dead-end with ${room.getConnectionCount()} connections`);
        
        // CRITICAL: Verify we didn't break accessibility
        this._ensureAllRoomsAccessible();
    }

    // NEW: Assign Secret Room to one normal room per floor
    _assignSecretRoom() {
        console.log("=== Secret Room Assignment Debug ===");
        console.log(`Total rooms in layout: ${this.layout.size}`);
        console.log(`Current floor number: ${this.currentFloorNumber || 1}`);

        // Get all normal rooms (excluding start, boss, event rooms)
        const allRooms = Array.from(this.layout.values());
        console.log(`All rooms:`, allRooms.map(r => `${r.id}(${r.type})`));

        // CRITICAL: Verify boss room was assigned
        const bossRooms = allRooms.filter(r => r.type === 'Boss');
        console.log(`Boss rooms found: ${bossRooms.length}`, bossRooms.map(r => `${r.id}(${r.type})`));

        // CRITICAL: Only use normal rooms for secret room hosts (exclude EVENT and Boss rooms)
        // ALSO: Exclude rooms adjacent to boss room
        const normalRooms = allRooms.filter(room => {
            if (room.type !== 'Normal' || room.id === 0 || room.isSecret) {
                return false;
            }
            
            // Check if room is adjacent to boss room
            for (const [dir, neighborId] of Object.entries(room.neighbors || {})) {
                if (neighborId !== null) {
                    const neighbor = this.layout.get(neighborId);
                    if (neighbor && neighbor.type === 'Boss') {
                        console.log(`🚫 Room ${room.id} is adjacent to Boss room ${neighborId} - excluding from secret room candidates`);
                        return false;
                    }
                }
            }
            
            return true;
        });

        console.log(`Normal rooms available for secret room assignment: ${normalRooms.length}`);
        console.log(`Normal room IDs:`, normalRooms.map(r => r.id));

        if (normalRooms.length === 0) {
            console.warn("❌ No normal rooms available for secret room assignment");
            return;
        }

        // Select a random normal room for the secret room host
        const selectedRoom = normalRooms[Math.floor(Math.random() * normalRooms.length)];
        console.log(`🎯 Selected room ${selectedRoom.id} as secret room host`);

        // Mark this room as having a secret room (for SecretRoomManager to find later)
        selectedRoom.hasSecretRoom = true;
        selectedRoom.roomData.hasSecretRoom = true;

        console.log(`✅ Room ${selectedRoom.id} marked as secret room host`);
        console.log("=== End Secret Room Assignment ===");
    }

    // NEW: Assign Event Room to one normal room per floor
    _assignEventRoom() {
        console.log("=== Event Room Assignment Debug ===");
        console.log(`🔍 _assignEventRoom() called!`);
        console.log(`Total rooms in layout: ${this.layout.size}`);
        console.log(`Event room manager available: ${!!this.eventRoomManager}`);
        console.log(`Current floor number: ${this.currentFloorNumber || 1}`);

        // Skip if no event room manager available
        if (!this.eventRoomManager) {
            console.warn("❌ No event room manager available, skipping event room assignment");
            return;
        }

        // Get normal rooms
        const normalRooms = this._getNormalRooms();
        console.log(`Found ${normalRooms.length} normal rooms:`, normalRooms.map(r => `${r.id}(${r.type})`));

        if (normalRooms.length === 0) {
            console.warn("❌ No normal rooms available for event room assignment");
            return;
        }

        // Select a random normal room for the event room
        const selectedRoom = normalRooms[Math.floor(Math.random() * normalRooms.length)];
        console.log(`🎯 Selected room ${selectedRoom.id} for event room conversion`);

        // Get current floor number (this would need to be passed from DungeonHandler)
        const floorNumber = this.currentFloorNumber || 1;

        // Determine the entrance direction based on host connection
        // First find the best host connection
        const originalConnections = { ...selectedRoom.neighbors };
        let hostConnection = null;
        let hostDirection = null;
        let maxHostConnections = 0;

        Object.entries(originalConnections).forEach(([direction, connectedRoomId]) => {
            if (connectedRoomId !== null) {
                const connectedRoom = this.layout.get(connectedRoomId);
                if (connectedRoom) {
                    const connectionCount = connectedRoom.getConnectionCount();
                    if (connectionCount > maxHostConnections) {
                        maxHostConnections = connectionCount;
                        hostConnection = connectedRoomId;
                        hostDirection = direction;
                    }
                }
            }
        });

        // Calculate entrance direction (opposite of host direction)
        const entranceDirection = hostDirection ? getOppositeDirection(hostDirection) : null;
        console.log(`🚪 Event room will be entered from ${entranceDirection} (host is ${hostDirection})`);

        // Select an event room that supports this entrance direction
        const eventRoomKey = this.eventRoomManager.selectEventRoomForFloor(floorNumber, entranceDirection);
        console.log(`🎲 Selected event room key: ${eventRoomKey}`);

        if (eventRoomKey) {
            console.log(`✅ Assigning Room ${selectedRoom.id} as Event Room: ${eventRoomKey}`);

            // CRITICAL FIX: Mark as event room FIRST to prevent redistribution back to it
            selectedRoom.type = 'EVENT'; // Auto-syncs to roomData and floorLayout

            console.log(`🏷️ Marked Room ${selectedRoom.id} as EVENT type before isolation`);

            // CRITICAL FIX: Isolate event room by removing extra connections
            console.log(`🔧 Isolating event room ${selectedRoom.id} - removing extra connections`);

            // Original connections and host were already calculated above
            console.log(`📋 Original connections for room ${selectedRoom.id}:`, originalConnections);
            console.log(`🎯 Selected host connection: Room ${hostConnection} via ${hostDirection} direction`);

            // Clear all connections from the event room
            selectedRoom.neighbors = { n: null, s: null, e: null, w: null };

            // Restore only the host connection
            if (hostConnection && hostDirection) {
                selectedRoom.neighbors[hostDirection] = hostConnection;
                console.log(`✅ Restored host connection: ${hostDirection} -> Room ${hostConnection}`);
            }

            // Remove this room from all other rooms' connections and redistribute
            Object.entries(originalConnections).forEach(([direction, connectedRoomId]) => {
                if (connectedRoomId !== null && connectedRoomId !== hostConnection) {
                    const connectedRoom = this.layout.get(connectedRoomId);
                    if (connectedRoom) {
                        // Find the direction from connected room back to event room
                        const oppositeDir = getOppositeDirection(direction);
                        if (connectedRoom.neighbors[oppositeDir] === selectedRoom.id) {
                            console.log(`🔗 Removing connection from Room ${connectedRoomId} (${oppositeDir}) to event room ${selectedRoom.id}`);
                            connectedRoom.neighbors[oppositeDir] = null;

                            // Try to redistribute this connection
                            this._redistributeConnection(connectedRoom, oppositeDir);
                        }
                    }
                }
            });

            // Apply event room configuration
            this.eventRoomManager.applyEventRoomConfig(selectedRoom.roomData, eventRoomKey);
            
            // CRITICAL: Set the entrance direction on the room data
            selectedRoom.roomData.entranceDirection = entranceDirection;
            console.log(`🚪 Set entrance direction for event room ${selectedRoom.id}: ${entranceDirection}`);

            // CRITICAL FIX: Apply event room shape to override original room shape
            const eventRoomData = this.eventRoomManager.getEventRoomData(eventRoomKey);
            if (eventRoomData && eventRoomData.shape) {
                console.log(`🔧 Changing room ${selectedRoom.id} shape from ${selectedRoom.shapeKey} to ${eventRoomData.shape}`);
                selectedRoom.shapeKey = eventRoomData.shape;
                selectedRoom.roomData.shapeKey = eventRoomData.shape;

                // CRITICAL FIX: Clear pre-generated cache for this room so it regenerates with new shape
                if (window.dungeonHandler && window.dungeonHandler.preGeneratedRooms) {
                    if (window.dungeonHandler.preGeneratedRooms.has(selectedRoom.id)) {
                        console.log(`🗑️ Clearing pre-generated cache for room ${selectedRoom.id} due to shape change`);
                        window.dungeonHandler.preGeneratedRooms.delete(selectedRoom.id);
                    }
                }
            }

            // Room data will be synced at the end of generation

            console.log(`🎉 Event room isolation and assignment complete:`, {
                id: selectedRoom.id,
                type: selectedRoom.type,
                eventRoomKey: eventRoomKey,
                eventRoomName: selectedRoom.roomData.eventRoomName,
                coords: selectedRoom.coords,
                finalConnections: selectedRoom.neighbors
            });

            // Verify the assignment worked
            const verifyRoom = this.layout.get(selectedRoom.id);
            console.log(`🔍 Verification - Room ${selectedRoom.id} type is now: ${verifyRoom.type}`);
            console.log(`🔍 Verification - Final connections:`, verifyRoom.neighbors);
        } else {
            console.warn("❌ No event room selected for this floor");
        }

        console.log("=== End Event Room Assignment ===");
    }

    /**
     * Simplified method to assign all special rooms at once
     * Guarantees proper assignment order and availability checks
     */
    _assignAllSpecialRooms() {
        this._log("=== ASSIGNING ALL SPECIAL ROOMS ===");

        const normalRooms = this._getNormalRooms();
        this._log(`Available Normal rooms: ${normalRooms.length} (need minimum 3 for Boss + Event + Secret)`);

        if (normalRooms.length < 3) {
            console.error(`❌ CRITICAL: Only ${normalRooms.length} Normal rooms available for special assignments (need 3 minimum)`);
            
            // Try to add more rooms if we're critically short
            if (normalRooms.length < 1) {
                console.error(`❌ FATAL: No normal rooms for boss assignment! This should not happen.`);
                return;
            }
        }

        // Always assign boss room first (most critical)
        this._assignBossRoom();

        // Assign secret room if we have enough rooms
        if (normalRooms.length >= 2) {
            this._assignSecretRoom();
        } else {
            this._log(`⚠️ Skipping secret room assignment - insufficient normal rooms`);
        }

        // OLD: Assign event room if we have enough rooms
        // DISABLED: Using new event room placement system instead
        // if (normalRooms.length >= 3) {
        //     this._assignEventRoom();
        // } else {
        //     this._log(`⚠️ Skipping event room assignment - insufficient normal rooms`);
        // }

        this._log("=== SPECIAL ROOM ASSIGNMENT COMPLETE ===");
    }

    /**
     * Protect critical connections that must never be broken
     * This includes paths to special rooms and Room 0's mandatory north exit
     */
    _protectCriticalConnections() {
        this._log("=== PROTECTING CRITICAL CONNECTIONS ===");

        const protectedConnections = new Set();

        // Protect Room 0's north connection (mandatory exit)
        const room0 = this.layout.get(0);
        if (room0 && room0.neighbors.n) {
            protectedConnections.add(`0->n->${room0.neighbors.n}`);
            protectedConnections.add(`${room0.neighbors.n}->s->0`);
            this._log(`🛡️ Protected Room 0 north exit to Room ${room0.neighbors.n}`);
        }

        // Protect all connections to special rooms (Boss, Event, Secret)
        const allRooms = Array.from(this.layout.values());
        const specialRooms = allRooms.filter(room => 
            room.type === 'Boss' || room.type === 'EVENT' || room.type === 'SECRET'
        );

        specialRooms.forEach(specialRoom => {
            Object.entries(specialRoom.neighbors).forEach(([direction, connectedRoomId]) => {
                if (connectedRoomId !== null) {
                    protectedConnections.add(`${specialRoom.id}->${direction}->${connectedRoomId}`);
                    const oppositeDir = this._getOppositeDirection(direction);
                    protectedConnections.add(`${connectedRoomId}->${oppositeDir}->${specialRoom.id}`);
                    this._log(`🛡️ Protected ${specialRoom.type} Room ${specialRoom.id} connection to Room ${connectedRoomId}`);
                }
            });
        });

        // Store protected connections for later reference
        this.protectedConnections = protectedConnections;

        this._log(`=== PROTECTION COMPLETE: ${protectedConnections.size} connections protected ===`);
    }

    /**
     * Validate and fix all connections in a single optimized pass
     * Respects protected connections and maintains tree structure
     */
    _validateAndFixConnections() {
        this._log("=== VALIDATING AND FIXING CONNECTIONS ===");

        let fixedConnections = 0;
        let brokenConnections = 0;
        const allRooms = Array.from(this.layout.values());

        // Single pass validation and fixing
        for (const room of allRooms) {
            Object.entries(room.neighbors).forEach(([direction, connectedRoomId]) => {
                if (connectedRoomId !== null) {
                    const connectedRoom = this.layout.get(connectedRoomId);
                    
                    // Remove connections to non-existent rooms
                    if (!connectedRoom) {
                        room.neighbors[direction] = null;
                        brokenConnections++;
                        this._log(`🗑️ Removed connection to non-existent room ${connectedRoomId}`);
                        return;
                    }

                    // Check bidirectional consistency
                    const oppositeDir = this._getOppositeDirection(direction);
                    const backConnection = connectedRoom.neighbors[oppositeDir];

                    if (backConnection !== room.id) {
                        // Check if this connection is protected
                        const connectionKey = `${room.id}->${direction}->${connectedRoomId}`;
                        if (this.protectedConnections && this.protectedConnections.has(connectionKey)) {
                            // Force the back-connection for protected connections
                            connectedRoom.neighbors[oppositeDir] = room.id;
                            fixedConnections++;
                            this._log(`🛡️ Forced protected back-connection: Room ${connectedRoomId} → Room ${room.id}`);
                        } else if (room.id === 0) {
                            // Room 0 can have one-way connections
                            this._log(`✅ One-way connection from Room 0 to Room ${connectedRoomId} (allowed)`);
                        } else if (connectedRoom.type === 'EVENT') {
                            // Event rooms can receive one-way connections
                            this._log(`✅ One-way connection to Event Room ${connectedRoomId} (allowed)`);
                        } else if (connectedRoom.type === 'Boss') {
                            // CRITICAL: Boss rooms must remain dead-ends - never add connections to boss rooms
                            this._log(`🛡️ Protected Boss Room ${connectedRoomId} - not adding back-connection (maintains dead-end)`);
                        } else {
                            // Fix normal bidirectional connections
                            connectedRoom.neighbors[oppositeDir] = room.id;
                            fixedConnections++;
                            this._log(`🔧 Fixed bidirectional connection: Room ${room.id} ↔ Room ${connectedRoomId}`);
                        }
                    }
                }
            });
        }

        this._log(`✅ Connection validation complete: ${fixedConnections} fixed, ${brokenConnections} removed`);
    }

    /**
     * Remove unreachable rooms in a single efficient pass
     */
    _removeUnreachableRooms() {
        this._log("=== REMOVING UNREACHABLE ROOMS ===");

        const reachableRooms = new Set();
        const toVisit = [0]; // Start from Room 0

        // Breadth-first search to find all reachable rooms
        while (toVisit.length > 0) {
            const currentRoomId = toVisit.shift();
            if (reachableRooms.has(currentRoomId)) continue;

            reachableRooms.add(currentRoomId);
            const currentRoom = this.layout.get(currentRoomId);

            if (currentRoom) {
                Object.values(currentRoom.neighbors).forEach(connectedRoomId => {
                    if (connectedRoomId !== null && !reachableRooms.has(connectedRoomId)) {
                        toVisit.push(connectedRoomId);
                    }
                });
            }
        }

        // Remove unreachable rooms
        const allRoomIds = Array.from(this.layout.keys());
        const unreachableRooms = allRoomIds.filter(roomId => !reachableRooms.has(roomId));

        if (unreachableRooms.length === 0) {
            this._log(`✅ All ${allRoomIds.length} rooms are reachable`);
            return;
        }

        this._log(`🗑️ Removing ${unreachableRooms.length} unreachable rooms: [${unreachableRooms.join(', ')}]`);

        unreachableRooms.forEach(roomId => {
            this.layout.delete(roomId);
            this.floorLayout.delete(roomId);
        });

        this._log(`✅ Cleanup complete: ${this.layout.size} rooms remaining`);
    }

    /**
     * Final verification of the complete layout
     */
    _verifyFinalLayout() {
        this._log("=== FINAL LAYOUT VERIFICATION ===");

        const allRooms = Array.from(this.layout.values());
        const roomsByType = {
            Start: allRooms.filter(r => r.type === 'Start'),
            Normal: allRooms.filter(r => r.type === 'Normal'),
            Boss: allRooms.filter(r => r.type === 'Boss'),
            EVENT: allRooms.filter(r => r.type === 'EVENT'),
            SECRET: allRooms.filter(r => r.type === 'SECRET')
        };

        this._log("Final room distribution:");
        Object.entries(roomsByType).forEach(([type, rooms]) => {
            this._log(`  ${type}: ${rooms.length} rooms [${rooms.map(r => r.id).join(', ')}]`);
        });

        // Critical checks
        const issues = [];

        if (roomsByType.Start.length !== 1) {
            issues.push(`Expected 1 Start room, found ${roomsByType.Start.length}`);
        }

        if (roomsByType.Boss.length === 0) {
            issues.push("No Boss room found");
        } else if (roomsByType.Boss.length > 1) {
            issues.push(`Multiple Boss rooms found: ${roomsByType.Boss.length}`);
        } else {
            // Verify boss room has exactly 1 connection (dead-end)
            const bossRoom = roomsByType.Boss[0];
            const bossConnections = bossRoom.getConnectionCount();
            if (bossConnections !== 1) {
                issues.push(`Boss room ${bossRoom.id} has ${bossConnections} connections (must be exactly 1 for dead-end)`);
            } else {
                this._log(`✅ Boss room ${bossRoom.id} correctly has 1 connection (dead-end)`);
            }
        }

        if (this.layout.size < MIN_ROOMS) {
            issues.push(`Layout has ${this.layout.size} rooms (minimum: ${MIN_ROOMS})`);
        }

        // Verify Room 0 has north connection
        const room0 = this.layout.get(0);
        if (!room0 || !room0.neighbors.n) {
            issues.push("Room 0 missing mandatory north connection");
        }

        if (issues.length === 0) {
            this._log("✅ Layout verification PASSED - all requirements met");
        } else {
            console.error("❌ Layout verification FAILED:");
            issues.forEach(issue => console.error(`  - ${issue}`));
        }

        this._log("=== VERIFICATION COMPLETE ===");
    }

    /**
     * Emergency boss room assignment if none was created
     */
    _emergencyBossRoomAssignment() {
        console.log("=== EMERGENCY BOSS ROOM ASSIGNMENT ===");

        const normalRooms = this._getNormalRooms();
        if (normalRooms.length === 0) {
            console.error("❌ No normal rooms available for emergency boss assignment!");

            // As a last resort, try to convert any non-Start room to boss room
            const allRooms = Array.from(this.layout.values());
            const nonStartRooms = allRooms.filter(room => room.type !== 'Start');

            if (nonStartRooms.length === 0) {
                console.error("❌ CRITICAL: Only Start room exists - cannot assign boss room!");
                return;
            }

            // Calculate actual path distances from start room
            const pathDistances = this._calculatePathDistances();
            
            // Find the furthest non-Start room using path distance
            let furthestRoom = null;
            let maxPathDistance = -1;

            nonStartRooms.forEach(room => {
                const pathDistance = pathDistances.get(room.id) || 0;
                if (pathDistance > maxPathDistance) {
                    maxPathDistance = pathDistance;
                    furthestRoom = room;
                }
            });

            if (furthestRoom) {
                console.log(`🚨 LAST RESORT: Converting Room ${furthestRoom.id} (${furthestRoom.type}) to Boss Room (Path Distance: ${maxPathDistance})`);

                furthestRoom.type = 'Boss'; // Auto-syncs to roomData and floorLayout
                furthestRoom.shapeKey = 'BOSS_ARENA'; // Auto-syncs to roomData and floorLayout
                furthestRoom.roomData.state.initialEnemies = []; // No boss for now
                
                // Store the area key for later boss room configuration
                const areaKey = furthestRoom.roomData.state.area || 'catacombs';
                furthestRoom.roomData.areaKey = areaKey;
                furthestRoom.roomData.bossRoomKey = `${areaKey}_boss_room`;
                console.log(`✅ Marked room as boss room for area: ${areaKey}`);

                console.log(`✅ Last resort boss room assignment complete`);
            }

            console.log("=== END EMERGENCY ASSIGNMENT ===");
            return;
        }

        // Find the furthest normal room from start
        const startRoom = this.layout.get(0);
        let furthestRoom = null;
        let maxDistance = -1;

        normalRooms.forEach(room => {
            const distance = Math.abs(room.coords.x - startRoom.coords.x) +
                           Math.abs(room.coords.y - startRoom.coords.y);
            if (distance > maxDistance) {
                maxDistance = distance;
                furthestRoom = room;
            }
        });

        if (furthestRoom) {
            console.log(`🚨 Emergency assignment: Room ${furthestRoom.id} as Boss Room (distance: ${maxDistance})`);

            furthestRoom.type = 'Boss'; // Auto-syncs to roomData and floorLayout
            furthestRoom.shapeKey = 'BOSS_ARENA'; // Auto-syncs to roomData and floorLayout
            furthestRoom.roomData.state.initialEnemies = [];
            
            // Store the area key for later boss room configuration
            const areaKey = furthestRoom.roomData.state.area || 'catacombs';
            furthestRoom.roomData.areaKey = areaKey;
            furthestRoom.roomData.bossRoomKey = `${areaKey}_boss_room`;
            
            // Apply boss room configuration in emergency assignment too
            if (this.bossRoomManager) {
                this.bossRoomManager.applyBossRoomConfig(furthestRoom.roomData, areaKey);
                console.log(`✅ Applied boss room configuration for area: ${areaKey} (emergency assignment)`);
                
                // CRITICAL: Sync boss room data to floor layout after configuration
                furthestRoom.syncRoomData();
                console.log(`🔄 Synced boss room data to floor layout (emergency assignment)`);
            } else {
                console.log(`✅ Marked room as boss room for area: ${areaKey} (no BossRoomManager available)`);
            }

            console.log(`✅ Emergency boss room assignment complete`);
        }

        console.log("=== END EMERGENCY ASSIGNMENT ===");
    }

    // REMOVED: _protectSpecialRoomConnections() - replaced by _protectCriticalConnections()

    /**
     * Reconnect a secret room to a normal room
     */
    _reconnectSecretRoomToNormal(secretRoom) {
        console.log(`🔧 Reconnecting Secret Room ${secretRoom.id} to a Normal room`);

        const normalRooms = this._getNormalRooms();

        // Find nearby normal rooms
        const nearbyNormalRooms = normalRooms.filter(normalRoom => {
            const distance = Math.abs(normalRoom.coords.x - secretRoom.coords.x) +
                           Math.abs(normalRoom.coords.y - secretRoom.coords.y);
            return distance <= 2; // Within 2 units
        });

        if (nearbyNormalRooms.length > 0) {
            // Sort by distance
            nearbyNormalRooms.sort((a, b) => {
                const distA = Math.abs(a.coords.x - secretRoom.coords.x) + Math.abs(a.coords.y - secretRoom.coords.y);
                const distB = Math.abs(b.coords.x - secretRoom.coords.x) + Math.abs(b.coords.y - secretRoom.coords.y);
                return distA - distB;
            });

            for (const normalRoom of nearbyNormalRooms) {
                // Try to create a connection
                const deltaX = normalRoom.coords.x - secretRoom.coords.x;
                const deltaY = normalRoom.coords.y - secretRoom.coords.y;

                let connectionDir = null;
                let oppositeDir = null;

                if (Math.abs(deltaX) === 1 && deltaY === 0) {
                    connectionDir = deltaX > 0 ? 'e' : 'w';
                    oppositeDir = deltaX > 0 ? 'w' : 'e';
                } else if (Math.abs(deltaY) === 1 && deltaX === 0) {
                    connectionDir = deltaY > 0 ? 's' : 'n';
                    oppositeDir = deltaY > 0 ? 'n' : 's';
                }

                if (connectionDir &&
                    secretRoom.neighbors[connectionDir] === null &&
                    normalRoom.neighbors[oppositeDir] === null) {

                    secretRoom.neighbors[connectionDir] = normalRoom.id;
                    normalRoom.neighbors[oppositeDir] = secretRoom.id;

                    console.log(`✅ Reconnected Secret Room ${secretRoom.id} (${connectionDir}) <-> Normal Room ${normalRoom.id} (${oppositeDir})`);
                    return true;
                }
            }
        }

        console.log(`❌ Could not reconnect Secret Room ${secretRoom.id} to a Normal room`);
        return false;
    }

    // REMOVED: Replaced by optimized logic in _performPostProcessingOptimizations()

    // REMOVED: Replaced by _optimizedConnectionValidation() in _performPostProcessingOptimizations()

    // REMOVED: Replaced by optimized logic in _performPostProcessingOptimizations()

    // REMOVED: _performPostProcessingOptimizations() - replaced by simplified post-processing

    // OPTIMIZATION: Integrated Room 0 isolation
    _enforceRoom0IsolationOptimized() {
        const room0 = this.layout.get(0);
        if (!room0 || !room0.neighbors.n) return;

        const northConnection = room0.neighbors.n;
        const originalConnections = { ...room0.neighbors };

        // Clear all connections except north
        room0.neighbors = { n: northConnection, s: null, e: null, w: null };

        // Remove Room 0 from other rooms' connections
        Object.entries(originalConnections).forEach(([direction, connectedRoomId]) => {
            if (connectedRoomId !== null && connectedRoomId !== northConnection) {
                const connectedRoom = this.layout.get(connectedRoomId);
                if (connectedRoom) {
                    const oppositeDir = getOppositeDirection(direction);
                    if (connectedRoom.neighbors[oppositeDir] === room0.id) {
                        connectedRoom.neighbors[oppositeDir] = null;
                        this._redistributeConnection(connectedRoom, oppositeDir);
                    }
                }
            }
        });

        this._log(`✅ Room 0 isolated - only north connection to Room ${northConnection}`);
    }

    // OPTIMIZATION: Integrated unreachable room removal
    _removeUnreachableRoomsOptimized() {
        const originalRoomCount = this.layout.size;

        // Find reachable rooms using breadth-first search
        const reachableRooms = new Set();
        const toVisit = [0];

        while (toVisit.length > 0) {
            const currentRoomId = toVisit.shift();
            if (reachableRooms.has(currentRoomId)) continue;

            reachableRooms.add(currentRoomId);
            const currentRoom = this.layout.get(currentRoomId);

            if (currentRoom) {
                Object.values(currentRoom.neighbors).forEach(connectedRoomId => {
                    if (connectedRoomId !== null && !reachableRooms.has(connectedRoomId)) {
                        toVisit.push(connectedRoomId);
                    }
                });
            }
        }

        // Remove unreachable rooms
        const allRoomIds = Array.from(this.layout.keys());
        const unreachableRooms = allRoomIds.filter(roomId => !reachableRooms.has(roomId));

        if (unreachableRooms.length === 0) {
            this._log(`✅ All ${originalRoomCount} rooms are reachable`);
            return;
        }

        // Clean up unreachable rooms
        unreachableRooms.forEach(roomId => {
            const roomToRemove = this.layout.get(roomId);
            if (roomToRemove) {
                // Remove connections to this room
                this.layout.forEach(otherRoom => {
                    Object.entries(otherRoom.neighbors).forEach(([direction, connectedRoomId]) => {
                        if (connectedRoomId === roomId) {
                            otherRoom.neighbors[direction] = null;
                        }
                    });
                });
                this.layout.delete(roomId);
            }
        });

        const finalRoomCount = this.layout.size;
        this._log(`📊 Room count: ${originalRoomCount} → ${finalRoomCount} (removed ${unreachableRooms.length})`);
    }

    // OPTIMIZATION: Combined connection validation to reduce iterations
    _optimizedConnectionValidation() {
        this._log("=== OPTIMIZED CONNECTION VALIDATION ===");

        let fixedConnections = 0;
        let brokenConnections = 0;
        const roomsToProcess = Array.from(this.layout.values());

        // Single pass through all rooms and connections
        for (const room of roomsToProcess) {
            Object.entries(room.neighbors).forEach(([direction, connectedRoomId]) => {
                if (connectedRoomId !== null) {
                    const connectedRoom = this.layout.get(connectedRoomId);
                    if (!connectedRoom) {
                        // Remove connections to non-existent rooms
                        room.neighbors[direction] = null;
                        brokenConnections++;
                        return;
                    }

                    const oppositeDir = getOppositeDirection(direction);
                    const backConnection = connectedRoom.neighbors[oppositeDir];

                    if (backConnection !== room.id) {
                        // Apply protection rules and fix connections
                        if (room.type === 'EVENT') {
                            // Event rooms: allow one-way OUT connections
                            this._log(`✅ One-way connection from Event Room ${room.id} to Room ${connectedRoomId}`);
                        } else if (connectedRoom.type === 'EVENT') {
                            // Remove connections TO event rooms (except from host)
                            room.neighbors[direction] = null;
                            brokenConnections++;
                            this._log(`🛡️ Removed connection to Event Room ${connectedRoom.id}`);
                        } else if (room.id === 0) {
                            // Room 0: allow one-way OUT connections
                            this._log(`✅ One-way connection from Room 0 to Room ${connectedRoomId}`);
                        } else if (connectedRoom.id === 0) {
                            // CRITICAL FIX: Only allow connections TO Room 0 from its designated north neighbor
                            const room0 = this.layout.get(0);
                            const allowedNorthNeighbor = room0?.neighbors.n;

                            // Check if this is the allowed connection from Room 1 back to Room 0
                            if (room.id === allowedNorthNeighbor) {
                                // This is Room 1 (the north neighbor) connecting south back to Room 0
                                // Keep this connection - it's needed for door traversal from Room 1 to Room 0
                                this._log(`✅ Allowed connection: Room ${room.id} (${direction}) → Room 0 (return path)`);
                                // Don't create a back-connection from Room 0 - it should only have north connection
                            } else {
                                // Remove unauthorized connections TO Room 0
                                room.neighbors[direction] = null;
                                brokenConnections++;
                                this._log(`🛡️ Removed unauthorized connection from Room ${room.id} to Room 0`);
                            }
                        } else {
                            // TREE STRUCTURE: Only fix bidirectional connections if they don't create loops
                            // For tree structure, be very strict about loop prevention
                            if (!this._wouldCreateLoop(room.id, connectedRoomId, oppositeDir, false)) {
                                connectedRoom.neighbors[oppositeDir] = room.id;
                                fixedConnections++;
                                this._log(`✅ Fixed bidirectional connection: ${room.id} ↔ ${connectedRoomId}`);
                            } else {
                                this._log(`🚫 Skipped connection ${room.id} ↔ ${connectedRoomId} - would create loop`);
                            }
                        }
                    }
                }
            });
        }

        this._log(`✅ Optimized validation: ${fixedConnections} fixed, ${brokenConnections} removed`);

        // CRITICAL: Ensure all rooms remain accessible after validation
        this._ensureAllRoomsAccessible();

        // CRITICAL: Enforce Room 0 north-only connection after all processing
        this._enforceRoom0NorthOnly();

        // Debug: Log final Room 0 and Room 1 connections after validation
        const room0Final = this.layout.get(0);
        const room1Final = this.layout.get(1);
        if (room0Final) this._log(`🔍 AFTER - Room 0 connections:`, room0Final.neighbors);
        if (room1Final) this._log(`🔍 AFTER - Room 1 connections:`, room1Final.neighbors);

        this._log("=== END OPTIMIZED CONNECTION VALIDATION ===");
    }

    /**
     * Ensure all rooms remain accessible after validation
     * Fixes any rooms that became isolated during connection validation
     */
    _ensureAllRoomsAccessible() {
        this._log("=== ENSURING ALL ROOMS ACCESSIBLE ===");

        // Find reachable rooms from start room
        const reachableRooms = new Set();
        const toVisit = [0]; // Start from Room 0

        while (toVisit.length > 0) {
            const currentRoomId = toVisit.shift();
            if (reachableRooms.has(currentRoomId)) continue;

            reachableRooms.add(currentRoomId);
            const currentRoom = this.layout.get(currentRoomId);

            if (currentRoom) {
                Object.values(currentRoom.neighbors).forEach(connectedRoomId => {
                    if (connectedRoomId !== null && !reachableRooms.has(connectedRoomId)) {
                        toVisit.push(connectedRoomId);
                    }
                });
            }
        }

        // Find unreachable rooms
        const allRoomIds = Array.from(this.layout.keys());
        const unreachableRooms = allRoomIds.filter(roomId => !reachableRooms.has(roomId));

        if (unreachableRooms.length === 0) {
            this._log(`✅ All ${allRoomIds.length} rooms are accessible`);
            return;
        }

        this._log(`🚨 Found ${unreachableRooms.length} unreachable rooms: [${unreachableRooms.join(', ')}]`);

        // Fix unreachable rooms by connecting them to reachable rooms
        unreachableRooms.forEach(unreachableRoomId => {
            const unreachableRoom = this.layout.get(unreachableRoomId);
            if (!unreachableRoom) return;

            this._log(`🔧 Fixing accessibility for Room ${unreachableRoomId}`);

            // Find the closest reachable room to connect to
            let closestReachableRoom = null;
            let minDistance = Infinity;

            reachableRooms.forEach(reachableRoomId => {
                const reachableRoom = this.layout.get(reachableRoomId);
                if (!reachableRoom) return;

                // Skip Room 0 and Event rooms as connection targets
                if (reachableRoom.id === 0 || reachableRoom.type === 'EVENT') return;

                // CRITICAL: If this is a boss room, don't connect it directly to Room 0
                if (unreachableRoom.type === 'Boss' && reachableRoom.id === 0) return;

                const distance = Math.abs(unreachableRoom.coords.x - reachableRoom.coords.x) +
                               Math.abs(unreachableRoom.coords.y - reachableRoom.coords.y);

                if (distance < minDistance) {
                    minDistance = distance;
                    closestReachableRoom = reachableRoom;
                }
            });

            if (closestReachableRoom && minDistance <= 3) { // Increased range to 3
                // Create connection between unreachable and closest reachable room
                const deltaX = closestReachableRoom.coords.x - unreachableRoom.coords.x;
                const deltaY = closestReachableRoom.coords.y - unreachableRoom.coords.y;

                let connectionDir = null;
                let oppositeDir = null;

                // Allow direct adjacent connections
                if (Math.abs(deltaX) === 1 && deltaY === 0) {
                    connectionDir = deltaX > 0 ? 'e' : 'w';
                    oppositeDir = deltaX > 0 ? 'w' : 'e';
                } else if (Math.abs(deltaY) === 1 && deltaX === 0) {
                    connectionDir = deltaY > 0 ? 'n' : 's';
                    oppositeDir = deltaY > 0 ? 's' : 'n';
                }

                // If direct connection possible and doesn't create any loop, create it
                if (connectionDir &&
                    unreachableRoom.neighbors[connectionDir] === null &&
                    closestReachableRoom.neighbors[oppositeDir] === null &&
                    !this._wouldCreateLoop(unreachableRoomId, closestReachableRoom.id, oppositeDir, false)) {

                    unreachableRoom.neighbors[connectionDir] = closestReachableRoom.id;
                    closestReachableRoom.neighbors[oppositeDir] = unreachableRoom.id;

                    this._log(`✅ Connected Room ${unreachableRoomId} (${connectionDir}) <-> Room ${closestReachableRoom.id} (${oppositeDir})`);

                    // Add to reachable set for next iterations
                    reachableRooms.add(unreachableRoomId);
                } else {
                    // Force connection by finding any available direction
                    this._log(`🔧 Forcing connection for Room ${unreachableRoomId} - trying all directions`);

                    const directions = ['n', 's', 'e', 'w'];
                    let connected = false;

                    for (const dir of directions) {
                        if (unreachableRoom.neighbors[dir] === null) {
                            const oppositeDir = this._getOppositeDirection(dir);

                            // Find any reachable room that can accept this connection
                            // CRITICAL: Maintain boss room distance from Room 0
                            const sortedReachableRooms = Array.from(reachableRooms)
                                .map(id => this.layout.get(id))
                                .filter(room => room &&
                                    room.id !== 0 &&
                                    room.type !== 'EVENT' &&
                                    room.neighbors[oppositeDir] === null)
                                .sort((a, b) => {
                                    // If connecting a boss room, prefer rooms furthest from Room 0
                                    if (unreachableRoom.type === 'Boss') {
                                        const distA = Math.abs(a.coords.x) + Math.abs(a.coords.y);
                                        const distB = Math.abs(b.coords.x) + Math.abs(b.coords.y);
                                        return distB - distA; // Furthest first
                                    }
                                    // For normal rooms, prefer closer connections
                                    const distA = Math.abs(a.coords.x) + Math.abs(a.coords.y);
                                    const distB = Math.abs(b.coords.x) + Math.abs(b.coords.y);
                                    return distA - distB; // Closest first
                                });

                            for (const reachableRoom of sortedReachableRooms) {
                                // CRITICAL: Never connect boss room directly to Room 0
                                if (unreachableRoom.type === 'Boss' && reachableRoom.id === 0) {
                                    continue;
                                }

                                // CRITICAL: For boss rooms, prefer connections to rooms further from Room 0
                                if (unreachableRoom.type === 'Boss') {
                                    const distanceFromRoom0 = Math.abs(reachableRoom.coords.x) + Math.abs(reachableRoom.coords.y);
                                    if (distanceFromRoom0 < 2) { // Don't connect boss to rooms too close to Room 0
                                        continue;
                                    }
                                }

                                // Only connect if it doesn't create any loop (strict for tree structure)
                                if (!this._wouldCreateLoop(unreachableRoomId, reachableRoom.id, oppositeDir, false)) {
                                    unreachableRoom.neighbors[dir] = reachableRoom.id;
                                    reachableRoom.neighbors[oppositeDir] = unreachableRoom.id;

                                    this._log(`✅ FORCED connection: Room ${unreachableRoomId} (${unreachableRoom.type}) (${dir}) <-> Room ${reachableRoom.id} (${oppositeDir})`);
                                    reachableRooms.add(unreachableRoomId);
                                    connected = true;
                                    break;
                                } else {
                                    this._log(`🚫 Skipped forced connection ${unreachableRoomId} ↔ ${reachableRoom.id} - would create loop`);
                                }
                            }
                            if (connected) break;
                        }
                    }

                    if (!connected) {
                        this._log(`❌ CRITICAL: Could not connect Room ${unreachableRoomId} - may need manual intervention`);
                    }
                }
            }
        });

        // Final verification - ensure all rooms are now accessible
        const finalReachableRooms = new Set();
        const finalToVisit = [0];

        while (finalToVisit.length > 0) {
            const currentRoomId = finalToVisit.shift();
            if (finalReachableRooms.has(currentRoomId)) continue;

            finalReachableRooms.add(currentRoomId);
            const currentRoom = this.layout.get(currentRoomId);

            if (currentRoom) {
                Object.values(currentRoom.neighbors).forEach(connectedRoomId => {
                    if (connectedRoomId !== null && !finalReachableRooms.has(connectedRoomId)) {
                        finalToVisit.push(connectedRoomId);
                    }
                });
            }
        }

        const finalAllRoomIds = Array.from(this.layout.keys());
        const finalUnreachableRooms = finalAllRoomIds.filter(roomId => !finalReachableRooms.has(roomId));

        if (finalUnreachableRooms.length === 0) {
            this._log(`✅ SUCCESS: All ${finalAllRoomIds.length} rooms are now accessible`);
        } else {
            this._log(`❌ CRITICAL: ${finalUnreachableRooms.length} rooms still unreachable: [${finalUnreachableRooms.join(', ')}]`);
            // This is a critical failure - the dungeon is broken
        }

        this._log("=== END ACCESSIBILITY ENFORCEMENT ===");
    }

    /**
     * Enforce Room 0 to have ONLY a north connection
     * This runs after all other processing to ensure Room 0 design integrity
     */
    _enforceRoom0NorthOnly() {
        this._log("=== ENFORCING ROOM 0 NORTH-ONLY CONNECTION ===");

        const room0 = this.layout.get(0);
        if (!room0) {
            this._log("❌ Room 0 not found!");
            return;
        }

        // Store the current north connection
        const northConnection = room0.neighbors.n;
        this._log(`🔍 Room 0 current connections:`, room0.neighbors);

        // Check if Room 0 has any non-north connections
        const hasExtraConnections = room0.neighbors.s || room0.neighbors.e || room0.neighbors.w;

        if (hasExtraConnections) {
            this._log(`🚨 Room 0 has extra connections beyond north - fixing...`);

            // Store connections that need to be redistributed
            const connectionsToRedistribute = [];
            if (room0.neighbors.s) connectionsToRedistribute.push({ direction: 's', roomId: room0.neighbors.s });
            if (room0.neighbors.e) connectionsToRedistribute.push({ direction: 'e', roomId: room0.neighbors.e });
            if (room0.neighbors.w) connectionsToRedistribute.push({ direction: 'w', roomId: room0.neighbors.w });

            // Clear ALL connections from Room 0
            room0.neighbors = { n: null, s: null, e: null, w: null };

            // Restore ONLY the north connection
            if (northConnection) {
                room0.neighbors.n = northConnection;
                this._log(`✅ Restored north connection: Room 0 → Room ${northConnection}`);
            }

            // Redistribute the removed connections to maintain accessibility
            connectionsToRedistribute.forEach(conn => {
                const connectedRoom = this.layout.get(conn.roomId);
                if (connectedRoom) {
                    this._log(`🔄 Redistributing connection from Room 0 to Room ${conn.roomId}`);

                    // Remove the back-connection to Room 0
                    const oppositeDir = this._getOppositeDirection(conn.direction);
                    if (connectedRoom.neighbors[oppositeDir] === 0) {
                        connectedRoom.neighbors[oppositeDir] = null;
                        this._log(`🗑️ Removed back-connection from Room ${conn.roomId} to Room 0`);
                    }

                    // Try to connect this room to another nearby room to maintain accessibility
                    this._redistributeConnectionForRoom0Fix(connectedRoom);
                }
            });
        } else {
            this._log(`✅ Room 0 already has correct north-only connection`);
        }

        // Final verification
        const finalRoom0 = this.layout.get(0);
        if (finalRoom0.neighbors.n &&
            !finalRoom0.neighbors.s &&
            !finalRoom0.neighbors.e &&
            !finalRoom0.neighbors.w) {
            this._log(`✅ Room 0 north-only connection enforced successfully`);
        } else {
            this._log(`❌ Failed to enforce Room 0 north-only connection:`, finalRoom0.neighbors);
        }

        this._log("=== END ROOM 0 ENFORCEMENT ===");
    }

    /**
     * Helper to get opposite direction
     */
    _getOppositeDirection(direction) {
        const opposites = { n: 's', s: 'n', e: 'w', w: 'e' };
        return opposites[direction];
    }

    /**
     * Check if adding a connection would create a loop (multiple paths between rooms)
     * @param {number} roomId1 - First room ID
     * @param {number} roomId2 - Second room ID
     * @param {string} direction - Direction from room2 to room1
     * @param {boolean} forAccessibility - True if this connection is for accessibility (allow some loops)
     * @returns {boolean} - True if connection would create a loop
     */
    _wouldCreateLoop(roomId1, roomId2, direction, forAccessibility = false) {
        const room1 = this.layout.get(roomId1);
        const room2 = this.layout.get(roomId2);

        if (!room1 || !room2) return false;

        // Check if rooms already have a connection in any direction
        const existingConnections = Object.values(room1.neighbors).filter(id => id === roomId2).length +
                                   Object.values(room2.neighbors).filter(id => id === roomId1).length;

        if (existingConnections > 0) {
            this._log(`🔍 Direct loop detected: Room ${roomId1} and Room ${roomId2} already connected`);
            return true; // Already connected, would create multiple direct paths
        }

        // For accessibility connections, be more lenient - only prevent direct loops
        if (forAccessibility) {
            this._log(`🔧 Allowing accessibility connection: Room ${roomId1} ↔ Room ${roomId2}`);
            return false;
        }

        // For normal connections, check if there's already a path between these rooms
        const visited = new Set();
        const hasPath = this._hasPathBetween(roomId1, roomId2, visited);

        if (hasPath) {
            this._log(`🔍 Cycle detected: Path already exists between Room ${roomId1} and Room ${roomId2}`);
            return true;
        }

        return false;
    }

    /**
     * Check if there's already a path between two rooms (DFS)
     * @param {number} startId - Starting room ID
     * @param {number} targetId - Target room ID
     * @param {Set} visited - Set of visited room IDs
     * @returns {boolean} - True if path exists
     */
    _hasPathBetween(startId, targetId, visited) {
        if (startId === targetId) return true;
        if (visited.has(startId)) return false;

        visited.add(startId);
        const startRoom = this.layout.get(startId);

        if (!startRoom) return false;

        // Check all connections from current room
        for (const connectedId of Object.values(startRoom.neighbors)) {
            if (connectedId !== null && this._hasPathBetween(connectedId, targetId, visited)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Redistribute connection for a room that was disconnected from Room 0
     */
    _redistributeConnectionForRoom0Fix(room) {
        this._log(`🔧 Finding new connection for Room ${room.id} (disconnected from Room 0)`);

        // Find nearby rooms that could be connected (excluding Room 0 and Event rooms)
        const nearbyRooms = Array.from(this.layout.values()).filter(nearbyRoom => {
            if (nearbyRoom.id === room.id) return false;
            if (nearbyRoom.id === 0) return false; // Never connect back to Room 0
            if (nearbyRoom.type === 'EVENT') return false; // Don't connect to event rooms

            const distance = Math.abs(nearbyRoom.coords.x - room.coords.x) +
                           Math.abs(nearbyRoom.coords.y - room.coords.y);
            return distance === 1; // Only direct neighbors
        });

        // Try to connect to the first available nearby room
        for (const nearbyRoom of nearbyRooms) {
            const deltaX = nearbyRoom.coords.x - room.coords.x;
            const deltaY = nearbyRoom.coords.y - room.coords.y;

            let connectionDir = null;
            let oppositeDir = null;

            if (deltaX === 1 && deltaY === 0) { connectionDir = 'e'; oppositeDir = 'w'; }
            else if (deltaX === -1 && deltaY === 0) { connectionDir = 'w'; oppositeDir = 'e'; }
            else if (deltaY === -1 && deltaX === 0) { connectionDir = 'n'; oppositeDir = 's'; }
            else if (deltaY === 1 && deltaX === 0) { connectionDir = 's'; oppositeDir = 'n'; }

            if (connectionDir &&
                room.neighbors[connectionDir] === null &&
                nearbyRoom.neighbors[oppositeDir] === null &&
                !this._wouldCreateLoop(room.id, nearbyRoom.id, oppositeDir, false)) {

                room.neighbors[connectionDir] = nearbyRoom.id;
                nearbyRoom.neighbors[oppositeDir] = room.id;

                this._log(`✅ Redistributed: Room ${room.id} (${connectionDir}) <-> Room ${nearbyRoom.id} (${oppositeDir})`);
                return true;
            }
        }

        this._log(`⚠️ Could not redistribute connection for Room ${room.id} - may become isolated`);
        return false;
    }

    /**
     * Enforce pure tree structure by removing any loops that may have been created
     * during accessibility enforcement while maintaining all room accessibility
     */
    _enforceTreeStructure() {
        this._log("=== ENFORCING TREE STRUCTURE ===");

        let loopsRemoved = 0;
        const roomIds = Array.from(this.layout.keys());

        // Find and remove loops while maintaining accessibility
        for (const roomId of roomIds) {
            const room = this.layout.get(roomId);
            if (!room) continue;

            // Check each connection for potential loops
            Object.entries(room.neighbors).forEach(([direction, connectedRoomId]) => {
                if (connectedRoomId === null) return;

                const connectedRoom = this.layout.get(connectedRoomId);
                if (!connectedRoom) return;

                // Check if removing this connection would still leave all rooms accessible
                if (this._canRemoveConnectionSafely(roomId, connectedRoomId, direction)) {
                    // Temporarily remove the connection
                    const oppositeDir = this._getOppositeDirection(direction);
                    room.neighbors[direction] = null;
                    connectedRoom.neighbors[oppositeDir] = null;

                    // Verify all rooms are still accessible
                    if (this._areAllRoomsAccessible()) {
                        loopsRemoved++;
                        this._log(`🗑️ Removed loop: Room ${roomId} (${direction}) ↔ Room ${connectedRoomId} (${oppositeDir})`);
                    } else {
                        // Restore the connection if it breaks accessibility
                        room.neighbors[direction] = connectedRoomId;
                        connectedRoom.neighbors[oppositeDir] = roomId;
                        this._log(`🔄 Restored essential connection: Room ${roomId} ↔ Room ${connectedRoomId}`);
                    }
                }
            });
        }

        this._log(`✅ Tree structure enforced: ${loopsRemoved} loops removed`);
        this._log("=== END TREE STRUCTURE ENFORCEMENT ===");
    }

    /**
     * Check if a connection can be safely removed without breaking accessibility
     */
    _canRemoveConnectionSafely(roomId1, roomId2, direction) {
        // Never remove connections involving Room 0, special rooms, or critical paths
        if (roomId1 === 0 || roomId2 === 0) return false;

        const room1 = this.layout.get(roomId1);
        const room2 = this.layout.get(roomId2);

        if (!room1 || !room2) return false;

        // Don't remove connections to/from special rooms if they have limited connections
        if ((room1.type === 'Boss' || room1.type === 'EVENT') && room1.getConnectionCount() <= 1) return false;
        if ((room2.type === 'Boss' || room2.type === 'EVENT') && room2.getConnectionCount() <= 1) return false;

        // Only remove if both rooms have multiple connections
        return room1.getConnectionCount() > 1 && room2.getConnectionCount() > 1;
    }

    /**
     * Check if all rooms are accessible from Room 0
     */
    _areAllRoomsAccessible() {
        const reachableRooms = new Set();
        const toVisit = [0];

        while (toVisit.length > 0) {
            const currentRoomId = toVisit.shift();
            if (reachableRooms.has(currentRoomId)) continue;

            reachableRooms.add(currentRoomId);
            const currentRoom = this.layout.get(currentRoomId);

            if (currentRoom) {
                Object.values(currentRoom.neighbors).forEach(connectedRoomId => {
                    if (connectedRoomId !== null && !reachableRooms.has(connectedRoomId)) {
                        toVisit.push(connectedRoomId);
                    }
                });
            }
        }

        return reachableRooms.size === this.layout.size;
    }

    /**
     * Intelligently add rooms to meet minimum requirements
     * Prioritizes extending dead ends and filling gaps
     * @param {number} roomsToAdd - Number of rooms to add
     * @returns {number} Number of rooms successfully added
     */
    _intelligentlyAddRooms(roomsToAdd) {
        this._log(`=== INTELLIGENT ROOM ADDITION (Target: +${roomsToAdd} rooms) ===`);
        
        let roomsAdded = 0;
        const maxAttempts = roomsToAdd * 10; // Give plenty of attempts
        let attempts = 0;

        const directions = {
            n: { x: 0, y: -1 },
            s: { x: 0, y: 1 },
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 }
        };

        while (roomsAdded < roomsToAdd && attempts < maxAttempts) {
            attempts++;

            // Strategy 1: Extend dead ends first (most reliable)
            const deadEndRooms = Array.from(this.layout.values()).filter(room => 
                room.getConnectionCount() === 1 && room.id !== 0
            );

            for (const deadEndRoom of deadEndRooms) {
                if (roomsAdded >= roomsToAdd) break;

                const availableDirections = Object.keys(directions).filter(dir => 
                    deadEndRoom.neighbors[dir] === null
                );

                for (const direction of availableDirections) {
                    const newX = deadEndRoom.coords.x + directions[direction].x;
                    const newY = deadEndRoom.coords.y + directions[direction].y;

                    if (this._canPlaceRoom(newX, newY)) {
                        const newRoom = this._addRoom(newX, newY, 'Normal');
                        if (newRoom) {
                            // Connect to the dead end room
                            const oppositeDir = getOppositeDirection(direction);
                            deadEndRoom.neighbors[direction] = newRoom.id;
                            newRoom.neighbors[oppositeDir] = deadEndRoom.id;

                            roomsAdded++;
                            this._log(`✅ Extended dead end: Room ${deadEndRoom.id} → Room ${newRoom.id} (${direction})`);
                            break; // Move to next dead end
                        }
                    }
                }
            }

            // Strategy 2: Fill gaps between existing rooms
            if (roomsAdded < roomsToAdd) {
                const existingRooms = Array.from(this.layout.values()).filter(room => 
                    room.getConnectionCount() < 4 // Has potential for more connections
                );

                for (const room of existingRooms) {
                    if (roomsAdded >= roomsToAdd) break;

                    const availableDirections = Object.keys(directions).filter(dir => 
                        room.neighbors[dir] === null
                    );

                    for (const direction of availableDirections) {
                        const newX = room.coords.x + directions[direction].x;
                        const newY = room.coords.y + directions[direction].y;

                        if (this._canPlaceRoom(newX, newY)) {
                            const newRoom = this._addRoom(newX, newY, 'Normal');
                            if (newRoom) {
                                const oppositeDir = getOppositeDirection(direction);
                                room.neighbors[direction] = newRoom.id;
                                newRoom.neighbors[oppositeDir] = room.id;

                                roomsAdded++;
                                this._log(`✅ Filled gap: Room ${room.id} → Room ${newRoom.id} (${direction})`);
                                break;
                            }
                        }
                    }
                }
            }

            // Strategy 3: Create new branches from well-connected rooms
            if (roomsAdded < roomsToAdd) {
                const branchableRooms = Array.from(this.layout.values()).filter(room => 
                    room.getConnectionCount() >= 2 && room.getConnectionCount() < 4
                );

                // Sort by connection count (prefer rooms with more connections for stability)
                branchableRooms.sort((a, b) => b.getConnectionCount() - a.getConnectionCount());

                for (const room of branchableRooms.slice(0, 3)) { // Limit to prevent over-branching
                    if (roomsAdded >= roomsToAdd) break;

                    const availableDirections = Object.keys(directions).filter(dir => 
                        room.neighbors[dir] === null
                    );

                    if (availableDirections.length > 0) {
                        const direction = availableDirections[Math.floor(Math.random() * availableDirections.length)];
                        const newX = room.coords.x + directions[direction].x;
                        const newY = room.coords.y + directions[direction].y;

                        if (this._canPlaceRoom(newX, newY)) {
                            const newRoom = this._addRoom(newX, newY, 'Normal');
                            if (newRoom) {
                                const oppositeDir = getOppositeDirection(direction);
                                room.neighbors[direction] = newRoom.id;
                                newRoom.neighbors[oppositeDir] = room.id;

                                roomsAdded++;
                                this._log(`✅ Created branch: Room ${room.id} → Room ${newRoom.id} (${direction})`);
                            }
                        }
                    }
                }
            }
        }

        this._log(`=== INTELLIGENT ADDITION COMPLETE: ${roomsAdded}/${roomsToAdd} rooms added (${attempts} attempts) ===`);
        return roomsAdded;
    }

    // Add extra connections (loops) to reduce dead ends
    _addLoops(probability = 0.25, maxDoors = 3) {
        console.log(` -> Adding loops with ~${probability*100}% probability...`);
        if (this.layout.size < 3) return; // Need at least 3 rooms for potential loops

        const roomIds = Array.from(this.layout.keys());
        let loopsAdded = 0;

        // Define potential relative offsets for loop candidates (adjacent to adjacent)
        const loopOffsets = [
            { x: 0, y: -2 }, { x: 0, y: 2 }, { x: 2, y: 0 }, { x: -2, y: 0 }, // Cardinal 2 steps
            // { x: 1, y: -1 }, { x: 1, y: 1 }, { x: -1, y: 1 }, { x: -1, y: -1 } // Diagonal (ignore for now?)
        ];

        // Keep track of coords to Room object for easier lookup
        const coordToRoomMap = new Map();
        this.layout.forEach(room => {
            coordToRoomMap.set(`${room.coords.x},${room.coords.y}`, room);
        });

        for (const roomId of roomIds) {
            const roomA = this.layout.get(roomId);
            if (!roomA) continue;

            // CRITICAL FIX: Protect Room 0 from getting additional connections
            if (roomA.id === 0) {
                console.log(`🛡️ Skipping loop connections for Room 0 (starting room) - should only have north exit`);
                continue;
            }

            // CRITICAL FIX: Protect Event Rooms from getting additional connections
            if (roomA.type === 'EVENT') {
                console.log(`🛡️ Skipping loop connections for Room ${roomA.id} (event room) - should remain isolated`);
                continue;
            }

            // Shuffle offsets to check in random order
            loopOffsets.sort(() => Math.random() - 0.5);

            for (const offset of loopOffsets) {
                if (roomA.getConnectionCount() >= maxDoors) break;

                const potentialNeighborX = roomA.coords.x + offset.x;
                const potentialNeighborY = roomA.coords.y + offset.y;
                const roomB = coordToRoomMap.get(`${potentialNeighborX},${potentialNeighborY}`);

                if (roomB && roomB.id !== roomA.id && roomB.getConnectionCount() < maxDoors) {
                    // CRITICAL FIX: Protect Room 0 and Event Rooms from being targets of loop connections
                    if (roomB.id === 0) {
                        console.log(`🛡️ Skipping loop connection to Room 0 (starting room)`);
                        continue;
                    }

                    if (roomB.type === 'EVENT') {
                        console.log(`🛡️ Skipping loop connection to Room ${roomB.id} (event room)`);
                        continue;
                    }

                    let alreadyConnected = Object.values(roomA.neighbors).includes(roomB.id);

                    if (!alreadyConnected && Math.random() < probability) {
                        // Determine connection direction based on offset
                        let dirA = null, dirB = null;
                        if (offset.x === 2) { dirA = 'e'; dirB = 'w'; }
                        else if (offset.x === -2) { dirA = 'w'; dirB = 'e'; }
                        else if (offset.y === 2) { dirA = 's'; dirB = 'n'; }
                        else if (offset.y === -2) { dirA = 'n'; dirB = 's'; }

                        if (dirA && roomA.neighbors[dirA] === null && roomB.neighbors[dirB] === null) {
                             roomA.neighbors[dirA] = roomB.id;
                             roomB.neighbors[dirB] = roomA.id;
                             // Neighbors should be synced automatically as they reference the same object
                             loopsAdded++;
                             console.log(` --> Added loop connection between Room ${roomA.id} (${dirA}) and Room ${roomB.id} (${dirB})`);
                             if (roomA.getConnectionCount() >= maxDoors) break; // Stop checking offsets for roomA
                        }
                    }
                }
            }
        }
         console.log(` -> Added ${loopsAdded} loop connections.`);
    }

    // Helper to get room by coordinates (if needed, seems unused currently)
    _getRoomAt(x, y) {
        return this.rooms.find(room => room.coords.x === x && room.coords.y === y);
    }

    // Helper to get normal rooms (centralized filtering logic)
    _getNormalRooms(allRooms = null) {
        const rooms = allRooms || Array.from(this.layout.values());

        // Simple, direct filter - no complex logic
        const normalRooms = rooms.filter(room =>
            room.type === 'Normal' &&
            room.id !== 0 &&
            !room.isSecret
        );

        return normalRooms;
    }

    // REMOVED: Redundant wrapper - use getOppositeDirection() directly

    // Helper to redistribute a broken connection
    _redistributeConnection(room, direction) {
        console.log(`🔄 Attempting to redistribute connection for Room ${room.id} in direction ${direction}`);

        // Calculate target position for new connection
        const targetX = room.coords.x + (direction === 'e' ? 1 : direction === 'w' ? -1 : 0);
        const targetY = room.coords.y + (direction === 'n' ? -1 : direction === 's' ? 1 : 0);

        // Look for nearby rooms that could be connected
        const nearbyRooms = Array.from(this.layout.values()).filter(nearbyRoom => {
            if (nearbyRoom.id === room.id) return false;
            if (nearbyRoom.type === 'EVENT') return false; // CRITICAL: Don't connect to event rooms
            if (nearbyRoom.id === 0) return false; // CRITICAL: Don't connect to Room 0

            const distance = Math.abs(nearbyRoom.coords.x - targetX) + Math.abs(nearbyRoom.coords.y - targetY);
            return distance <= 2; // Within 2 grid units
        });

        // Try to connect to the closest suitable room
        if (nearbyRooms.length > 0) {
            // Sort by distance
            nearbyRooms.sort((a, b) => {
                const distA = Math.abs(a.coords.x - targetX) + Math.abs(a.coords.y - targetY);
                const distB = Math.abs(b.coords.x - targetX) + Math.abs(b.coords.y - targetY);
                return distA - distB;
            });

            for (const nearbyRoom of nearbyRooms) {
                // Check if we can create a valid connection
                const deltaX = nearbyRoom.coords.x - room.coords.x;
                const deltaY = nearbyRoom.coords.y - room.coords.y;

                // Only allow direct cardinal connections
                if ((Math.abs(deltaX) === 1 && deltaY === 0) || (Math.abs(deltaY) === 1 && deltaX === 0)) {
                    let connectionDir = null;
                    let oppositeDir = null;

                    if (deltaX === 1) { connectionDir = 'e'; oppositeDir = 'w'; }
                    else if (deltaX === -1) { connectionDir = 'w'; oppositeDir = 'e'; }
                    else if (deltaY === -1) { connectionDir = 'n'; oppositeDir = 's'; }
                    else if (deltaY === 1) { connectionDir = 's'; oppositeDir = 'n'; }

                    // Check if both rooms have free connections in the required directions
                    if (connectionDir && room.neighbors[connectionDir] === null && nearbyRoom.neighbors[oppositeDir] === null) {
                        // CRITICAL FIX: Double-check that we're not accidentally reconnecting to event rooms
                        if (nearbyRoom.type === 'EVENT') {
                            console.log(`🛡️ Redistribution blocked: Cannot connect Room ${room.id} to Event Room ${nearbyRoom.id}`);
                            continue; // Skip this room and try the next one
                        }

                        room.neighbors[connectionDir] = nearbyRoom.id;
                        nearbyRoom.neighbors[oppositeDir] = room.id;
                        console.log(`✅ Redistributed connection: Room ${room.id} (${connectionDir}) <-> Room ${nearbyRoom.id} (${oppositeDir})`);
                        return true;
                    }
                }
            }
        }

        console.log(`❌ Could not redistribute connection for Room ${room.id} in direction ${direction}`);
        return false;
    }

    // Debug function to print layout to console
    printLayout() {
        // Find grid bounds
        let minX = 0, maxX = 0, minY = 0, maxY = 0;
        for (const room of this.layout.values()) {
            minX = Math.min(minX, room.coords.x);
            maxX = Math.max(maxX, room.coords.x);
            minY = Math.min(minY, room.coords.y);
            maxY = Math.max(maxY, room.coords.y);
        }

        console.log("--- Dungeon Layout --- (Grid Coords)");
        for (let y = minY; y <= maxY; y++) {
            let row = "";
            for (let x = minX; x <= maxX; x++) {
                const room = this._getRoomAt(x, y);
                if (room) {
                    let char = 'N'; // Normal
                    if (room.type === 'Start') char = 'S';
                    else if (room.type === 'Boss') char = 'B';
                    else if (room.type === 'EVENT') char = 'E';
                    else if (room.type === 'Secret') char = 'R'; // Secret Room
                    row += `[${char}${room.id}]`;
                } else {
                    row += " .  ";
                }
            }
            console.log(row);
        }
        console.log("---------------------");
    }

    /**
     * Generate detailed ASCII map with connections and event room info
     */
    generateASCIIMap() {
        const map = this._generateDetailedASCIIMap();
        console.log("\n🗺️  DUNGEON ASCII MAP 🗺️");
        console.log("=".repeat(60));
        console.log(map);
        console.log("=".repeat(60));
        
        // Add legend
        console.log("\n📋 LEGEND:");
        console.log("S = Start Room    B = Boss Room    E = Event Room");
        console.log("H = Secret Host   N = Normal Room  . = Empty Space");
        console.log("│ ─ = Connections between rooms");
        console.log("▓ = Secret wall (hidden room behind)");
        console.log("");
        
        // Add event room details
        this._displayEventRoomDetails();
        
        // Add secret room details
        this._displaySecretRoomDetails();
        
        return map;
    }

    /**
     * Generate detailed ASCII map with connections
     */
    _generateDetailedASCIIMap() {
        // Find grid bounds
        let minX = 0, maxX = 0, minY = 0, maxY = 0;
        for (const room of this.layout.values()) {
            minX = Math.min(minX, room.coords.x);
            maxX = Math.max(maxX, room.coords.x);
            minY = Math.min(minY, room.coords.y);
            maxY = Math.max(maxY, room.coords.y);
        }

        const width = (maxX - minX + 1) * 6; // 6 chars per room ([X00] + space)
        const height = (maxY - minY + 1) * 3; // 3 lines per room
        
        // Create empty grid
        const grid = [];
        for (let i = 0; i < height; i++) {
            grid[i] = new Array(width).fill(' ');
        }

        // Helper function to get grid position (flip Y axis so up movement shows above)
        const getGridPos = (x, y) => ({
            x: (x - minX) * 6,      // Keep X normal
            y: (maxY - y) * 3       // Flip Y axis: maxY - y so north (-Y) appears above
        });

        // Place rooms and connections
        for (const room of this.layout.values()) {
            const pos = getGridPos(room.coords.x, room.coords.y);
            
            // Room type character
            let char = 'N';
            if (room.type === 'Start') char = 'S';
            else if (room.type === 'Boss') char = 'B';
            else if (room.type === 'EVENT') char = 'E';
            else if (room.type === 'Secret') char = 'R';
            
            // Special handling for rooms with secret walls
            if (room.hasSecretRoom || room.roomData?.hasSecretRoom) {
                char = 'H'; // Show as Host (H for Secret host)
            }
            
            // Draw room box
            const roomStr = `${char}${room.id.toString().padStart(2, '0')}`;
            if (pos.x + 4 < width && pos.y < height) {
                grid[pos.y][pos.x] = '[';
                grid[pos.y][pos.x + 1] = roomStr[0];
                grid[pos.y][pos.x + 2] = roomStr[1];
                grid[pos.y][pos.x + 3] = roomStr[2];
                grid[pos.y][pos.x + 4] = ']';
            }
            
            // Draw normal connections
            if (room.neighbors.n && pos.y > 0) {
                grid[pos.y - 1][pos.x + 2] = '│';
            }
            if (room.neighbors.s && pos.y + 1 < height) {
                grid[pos.y + 1][pos.x + 2] = '│';
            }
            if (room.neighbors.e && pos.x + 5 < width) {
                grid[pos.y][pos.x + 5] = '─';
            }
            if (room.neighbors.w && pos.x > 0) {
                grid[pos.y][pos.x - 1] = '─';
            }
            
            // Draw secret wall connections (indicated by special character)
            if (room.hasSecretRoom || room.roomData?.hasSecretRoom) {
                // Add secret wall indicator in an available direction
                // Priority: north, south, east, west
                if (!room.neighbors.n && pos.y > 0) {
                    grid[pos.y - 1][pos.x + 2] = '▓';
                } else if (!room.neighbors.s && pos.y + 1 < height) {
                    grid[pos.y + 1][pos.x + 2] = '▓';
                } else if (!room.neighbors.e && pos.x + 5 < width) {
                    grid[pos.y][pos.x + 5] = '▓';
                } else if (!room.neighbors.w && pos.x > 0) {
                    grid[pos.y][pos.x - 1] = '▓';
                }
            }
        }

        // Convert grid to string
        return grid.map(row => row.join('')).join('\n');
    }

    /**
     * Display event room details
     */
    _displayEventRoomDetails() {
        const eventRooms = [...this.layout.values()].filter(room => room.type === 'EVENT');
        
        if (eventRooms.length === 0) {
            console.log("🎪 No event rooms in this dungeon");
            return;
        }

        console.log("🎪 EVENT ROOMS IN THIS DUNGEON:");
        console.log("─".repeat(60));
        
        eventRooms.forEach(room => {
            const eventData = room.eventRoomData || room.roomData?.eventRoomData;
            const eventName = eventData?.name || 'Unknown Event Room';
            const eventId = eventData?.id || 'unknown';
            
            // Calculate entrance direction from connection to parent room
            let entrance = room.roomData?.entranceDirection || 'unknown';
            if (entrance === 'unknown') {
                // Find the direction from which the player enters
                // This is the direction from the event room TO the parent room
                for (const [dir, neighborId] of Object.entries(room.neighbors)) {
                    if (neighborId !== null) {
                        const neighbor = this.layout.get(neighborId);
                        if (neighbor && neighbor.type !== 'EVENT') {
                            // Player enters FROM this direction (the direction TO the parent)
                            entrance = dir;
                            break;
                        }
                    }
                }
            }
            
            // Calculate door rotation based on entrance direction
            const getDoorRotation = (entranceDir) => {
                // Convert full direction names to single letters if needed
                const dirMap = {
                    'north': 'n', 'south': 's', 'east': 'e', 'west': 'w',
                    'n': 'n', 's': 's', 'e': 'e', 'w': 'w'
                };
                const normalizedDir = dirMap[entranceDir.toLowerCase()] || entranceDir.toLowerCase();
                
                const rotations = {
                    'n': '180°', // Entering from north -> door faces south
                    's': '0°',   // Entering from south -> door faces north  
                    'e': '-90°', // Entering from east -> door faces west
                    'w': '90°'   // Entering from west -> door faces east
                };
                return rotations[normalizedDir] || 'unknown';
            };
            
            // Find connected parent room
            let parentRoom = null;
            let connectionDirection = null;
            for (const [dir, neighborId] of Object.entries(room.neighbors)) {
                if (neighborId !== null) {
                    const neighbor = this.layout.get(neighborId);
                    if (neighbor && neighbor.type !== 'EVENT') {
                        parentRoom = neighbor;
                        connectionDirection = dir;
                        break;
                    }
                }
            }
            
            console.log(`Room ${room.id}: ${eventName}`);
            console.log(`  Type: ${eventId}`);
            console.log(`  Shape: ${eventData?.shape || 'unknown'}`);
            console.log(`  Position: (${room.coords.x}, ${room.coords.y})`);
            console.log(`  🚪 ENTRANCE DETAILS:`);
            console.log(`    Entrance Direction: ${entrance.toUpperCase()} (player enters FROM ${entrance.toUpperCase()})`);
            const rotationInput = entrance.toLowerCase();
            const doorRotation = getDoorRotation(rotationInput);
            console.log(`    Door Rotation: ${doorRotation} (input: '${rotationInput}')`);
            if (parentRoom) {
                // Find which direction the parent connects to this event room
                let parentConnectDir = null;
                for (const [dir, neighborId] of Object.entries(parentRoom.neighbors)) {
                    if (neighborId === room.id) {
                        parentConnectDir = dir;
                        break;
                    }
                }
                console.log(`    Connected From: Room ${parentRoom.id} via ${parentConnectDir?.toUpperCase() || 'unknown'} door`);
                console.log(`    Connection: Room ${parentRoom.id}(${parentConnectDir?.toUpperCase()}) ↔ Room ${room.id}(${connectionDirection?.toUpperCase()})`);
            }
            
            // Show available connections from event room data
            if (eventData?.availableConnections) {
                const available = Object.entries(eventData.availableConnections)
                    .filter(([dir, enabled]) => enabled)
                    .map(([dir]) => dir.toUpperCase());
                console.log(`    Available Entrances: [${available.join(', ')}]`);
            }
            
            console.log('');
        });
        
        // Show used event rooms status
        const usedStatus = this.getUsedEventRoomsStatus();
        console.log("📊 EVENT ROOM USAGE STATUS:");
        console.log(`Used: ${usedStatus.count} rooms`);
        console.log(`Rooms used: [${usedStatus.used.join(', ')}]`);
    }

    /**
     * Display secret room details
     */
    _displaySecretRoomDetails() {
        const secretHostRooms = [...this.layout.values()].filter(room => 
            room.hasSecretRoom || room.roomData?.hasSecretRoom
        );
        
        if (secretHostRooms.length === 0) {
            console.log("🔐 No secret rooms in this dungeon");
            return;
        }

        console.log("🔐 SECRET ROOMS IN THIS DUNGEON:");
        console.log("─".repeat(40));
        
        secretHostRooms.forEach(room => {
            console.log(`Room ${room.id}: Secret Host Room`);
            console.log(`  Type: ${room.type} (contains hidden room)`);
            console.log(`  Position: (${room.coords.x}, ${room.coords.y})`);
            console.log(`  Secret Wall: Look for ▓ symbol on map`);
            console.log("");
        });
    }

    // === EVENT ROOM PLACEMENT SYSTEM ===
    
    /**
     * Get used event rooms from localStorage
     */
    getUsedEventRooms() {
        try {
            const stored = localStorage.getItem('usedEventRooms');
            return stored ? new Set(JSON.parse(stored)) : new Set();
        } catch (error) {
            console.warn('Error loading used event rooms:', error);
            return new Set();
        }
    }

    /**
     * Save used event rooms to localStorage
     */
    saveUsedEventRooms() {
        try {
            localStorage.setItem('usedEventRooms', JSON.stringify([...this.usedEventRooms]));
        } catch (error) {
            console.warn('Error saving used event rooms:', error);
        }
    }

    /**
     * Place event rooms - Select one event room first, then ensure it gets placed
     */
    async _placeEventRooms() {
        this._log("=== EVENT ROOM PLACEMENT ===");
        console.log("🎪 Event room placement starting...");
        console.log("🎪 Layout size:", this.layout.size);
        console.log("🎪 Rooms in layout:", Array.from(this.layout.keys()));
        
        try {
            // Import event room system
            const { getAvailableEventRooms, getEventRoom } = await import('../gameData/eventRooms/index.js');
            
            // Get all available event rooms (not used in previous stages)
            const allEventRooms = getAvailableEventRooms();
            const availableEventRooms = allEventRooms.filter(id => !this.usedEventRooms.has(id));
            
            console.log("🎪 Available event rooms:", availableEventRooms);
            
            if (availableEventRooms.length === 0) {
                console.error('❌ No available event rooms left for this stage!');
                return;
            }
            
            // SELECT ONE EVENT ROOM RANDOMLY FIRST
            const selectedEventRoomId = availableEventRooms[Math.floor(Math.random() * availableEventRooms.length)];
            const selectedEventRoom = getEventRoom(selectedEventRoomId);
            
            if (!selectedEventRoom) {
                console.error(`❌ Failed to get event room data for: ${selectedEventRoomId}`);
                return;
            }
            
            console.log(`🎪 SELECTED EVENT ROOM FOR THIS STAGE: ${selectedEventRoom.name} (${selectedEventRoomId})`);
            console.log(`🎪 Door configuration:`, selectedEventRoom.availableConnections);
            
            // Now find suitable locations for this specific event room
            const suitableLocations = [];
            
            console.log("🎪 Searching for suitable locations for this event room...");
            
            // Get the directions this event room can be entered from
            const eventRoomEntrances = Object.entries(selectedEventRoom.availableConnections)
                .filter(([dir, allowed]) => allowed)
                .map(([dir]) => dir);
            
            console.log(`🎪 Event room can be entered from: ${eventRoomEntrances.join(', ')}`);
            
            for (const [roomId, room] of this.layout) {
                // Skip non-normal rooms, room 0, and secret hosts
                if (room.type !== 'Normal' || roomId === 0 || room.hasSecretRoom) {
                    continue;
                }
                
                // CRITICAL: Skip rooms adjacent to boss room
                let adjacentToBoss = false;
                for (const [dir, neighborId] of Object.entries(room.neighbors)) {
                    if (neighborId !== null) {
                        const neighbor = this.layout.get(neighborId);
                        if (neighbor && neighbor.type === 'Boss') {
                            adjacentToBoss = true;
                            console.log(`🎪   Room ${roomId} is adjacent to Boss room ${neighborId} - skipping!`);
                            break;
                        }
                    }
                }
                if (adjacentToBoss) continue;
                
                // Check each direction for free space
                const freeDirections = [];
                console.log(`🎪   Room ${roomId} neighbors:`, room.neighbors);
                
                for (const dir of ['n', 's', 'e', 'w']) {
                    if (room.neighbors[dir] === null) {
                        // Check if we can place a room in this direction
                        const offset = this._getDirectionOffset(dir);
                        const newX = room.coords.x + offset.x;
                        const newY = room.coords.y + offset.y;
                        
                        console.log(`🎪   Checking ${dir}: would place at (${newX}, ${newY})`);
                        
                        // Check if position is free
                        let positionFree = true;
                        for (const [_, existingRoom] of this.layout) {
                            if (existingRoom.coords.x === newX && existingRoom.coords.y === newY) {
                                positionFree = false;
                                console.log(`🎪     Position occupied by room ${existingRoom.id}`);
                                break;
                            }
                        }
                        
                        if (positionFree) {
                            freeDirections.push(dir);
                            console.log(`🎪     Direction ${dir} is FREE!`);
                        }
                    }
                }
                
                // Check if this room can host our selected event room
                for (const freeDir of freeDirections) {
                    // Event room will be entered from the opposite direction
                    const entranceDirection = getOppositeDirection(freeDir);
                    const fullEntranceDirection = {
                        'n': 'north',
                        's': 'south',
                        'e': 'east',
                        'w': 'west'
                    }[entranceDirection] || entranceDirection;
                    
                    // Check if our selected event room can be entered from this direction
                    if (eventRoomEntrances.includes(fullEntranceDirection)) {
                        suitableLocations.push({
                            hostRoom: room,
                            hostRoomId: roomId,
                            hostDirection: freeDir,
                            entranceDirection: fullEntranceDirection
                        });
                        console.log(`🎪 Found suitable location: Room ${roomId} can host event room via ${freeDir} (entrance from ${fullEntranceDirection})`);
                    }
                }
            }
            
            if (suitableLocations.length === 0) {
                console.error(`❌ No suitable locations found for event room: ${selectedEventRoom.name}`);
                console.error(`   Event room requires entrance from: ${eventRoomEntrances.join(', ')}`);
                console.error('   This is a critical error - the dungeon generation should ensure all event rooms can be placed');
                return;
            }
            
            console.log(`🎪 Found ${suitableLocations.length} suitable locations for event room placement`);
            
            // Select a random suitable location
            const selectedLocation = suitableLocations[Math.floor(Math.random() * suitableLocations.length)];
            console.log(`🎪 Selected location: Room ${selectedLocation.hostRoomId} via ${selectedLocation.hostDirection}`);
            
            // Place the event room at the selected location
            const offset = this._getDirectionOffset(selectedLocation.hostDirection);
            const eventRoomCoords = {
                x: selectedLocation.hostRoom.coords.x + offset.x,
                y: selectedLocation.hostRoom.coords.y + offset.y
            };
            
            console.log(`🎪 Placing event room at coords (${eventRoomCoords.x}, ${eventRoomCoords.y})`);
            
            // Create new room object
            const eventRoomId = this.nextRoomId++;
            const eventRoom = new Room(eventRoomId, this);
            eventRoom.type = 'EVENT';
            eventRoom.coords = eventRoomCoords;
            eventRoom.roomData.coords = eventRoomCoords;
            eventRoom.roomData.type = 'EVENT';
            eventRoom.roomData.eventRoomName = selectedEventRoom.name;
            eventRoom.roomData.eventRoomId = selectedEventRoomId;
            eventRoom.roomData.eventRoomData = selectedEventRoom;
            eventRoom.roomData.entranceDirection = selectedLocation.entranceDirection;
            
            // Set shape from event room data
            if (selectedEventRoom.shape) {
                eventRoom.shapeKey = selectedEventRoom.shape;
                eventRoom.roomData.shapeKey = selectedEventRoom.shape;
            }
            
            // Set up connections (event room only connects back to host)
            const abbrevEntranceDir = selectedLocation.entranceDirection[0]; // Convert back to abbreviated
            eventRoom.neighbors[abbrevEntranceDir] = selectedLocation.hostRoomId;
            selectedLocation.hostRoom.neighbors[selectedLocation.hostDirection] = eventRoomId;
            
            // Add to layout
            this.layout.set(eventRoomId, eventRoom);
            this.usedEventRooms.add(selectedEventRoomId);
            
            // CRITICAL: Also add to floorLayout!
            this.floorLayout.set(eventRoomId, eventRoom.roomData);
            
            // Update room position tracking
            const positionKey = `${eventRoomCoords.x},${eventRoomCoords.y}`;
            this.roomPositions.add(positionKey);
            
            console.log(`🎪 ✅ Successfully placed event room '${selectedEventRoom.name}' (ID: ${eventRoomId})`);
            console.log(`🎪    Location: ${selectedLocation.hostDirection} of room ${selectedLocation.hostRoomId}`);
            console.log(`🎪    Entrance: from ${selectedLocation.entranceDirection}`);
            
            // Save the used event rooms to ensure they don't appear in future stages
            this.saveUsedEventRooms();
            
        } catch (error) {
            console.error('🎪 Error in event room placement:', error);
        }
    }

    /**
     * Identify branch ends (rooms with 1 or 2 connections that can support event rooms)
     */
    _identifyBranchEnds() {
        const branchEnds = [];
        
        for (const [roomId, room] of this.layout) {
            const connectionCount = room.getConnectionCount();
            
            // Debug logging
            console.log(`Room ${roomId} (${room.type}): ${connectionCount} connections`);
            
            // Skip start room and already assigned special rooms
            // UPDATED: Allow rooms with up to 3 connections for more flexibility
            if (room.type !== 'Normal' || connectionCount > 3) {
                if (room.type !== 'Normal') {
                    console.log(`  Skipped: not Normal room (${room.type})`);
                }
                if (connectionCount > 3) {
                    console.log(`  Skipped: ${connectionCount} connections (too many)`);
                }
                continue;
            }

            // Find the connected direction and available directions
            const connectedDirection = this._getConnectedDirection(room);
            console.log(`  Connected direction: ${connectedDirection}`);
            
            const availableDirections = ['n', 's', 'e', 'w'].filter(dir => {
                const hasNeighbor = room.neighbors[dir] !== null;
                const isValidPosition = this._isValidEventRoomPosition(room, dir);
                console.log(`    Direction ${dir}: hasNeighbor=${hasNeighbor}, isValidPosition=${isValidPosition}`);
                return !hasNeighbor && isValidPosition;
            });

            console.log(`  Available directions: [${availableDirections.join(', ')}]`);

            if (availableDirections.length > 0) {
                console.log(`  ✅ Room ${roomId} is a valid branch end!`);
                branchEnds.push({
                    roomId: roomId,
                    room: room,
                    position: room.coords,
                    connectedDirection: connectedDirection,
                    availableDirections: availableDirections
                });
            } else {
                console.log(`  ❌ Room ${roomId} has no available directions`);
            }
        }

        return branchEnds;
    }

    /**
     * Get the direction that a room is connected to
     */
    _getConnectedDirection(room) {
        for (const [dir, neighborId] of Object.entries(room.neighbors)) {
            if (neighborId !== null) {
                return dir;
            }
        }
        return null;
    }

    /**
     * Check if a position is valid for event room placement
     */
    _isValidEventRoomPosition(room, direction) {
        const directions = {
            n: { x: 0, y: -1 },
            s: { x: 0, y: 1 },
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 }
        };

        const newCoords = {
            x: room.coords.x + directions[direction].x,
            y: room.coords.y + directions[direction].y
        };

        // Check if position is already occupied
        const positionKey = `${newCoords.x},${newCoords.y}`;
        return !this.roomPositions.has(positionKey);
    }

    /**
     * Place an event room at a specific branch end
     */
    _placeEventRoomAtBranchEnd(branchEnd, availableEventRooms, getEventRoom) {
        this._log(`Attempting to place event room at branch end ${branchEnd.roomId}`);
        
        // Get the required entrance direction (opposite of the connecting direction)
        const requiredEntrance = getOppositeDirection(branchEnd.connectedDirection);
        this._log(`Required entrance direction: ${requiredEntrance}`);

        // Find compatible event rooms
        console.log(`🔍 Checking ${availableEventRooms.length} available event rooms for entrance: ${requiredEntrance}`);
        
        // Convert abbreviated direction to full name for event room compatibility
        const fullDirectionName = getFullDirectionName(requiredEntrance);
        console.log(`🔍 Converting direction '${requiredEntrance}' to '${fullDirectionName}' for event room compatibility`);
        
        const compatibleRooms = availableEventRooms
            .map(id => {
                const room = getEventRoom(id);
                console.log(`  Room ${id}:`, room ? `availableConnections.${fullDirectionName} = ${room.availableConnections?.[fullDirectionName]}` : 'not found');
                if (room && room.availableConnections) {
                    console.log(`    Full availableConnections:`, room.availableConnections);
                }
                return room;
            })
            .filter(room => room && room.availableConnections && room.availableConnections[fullDirectionName]);

        console.log(`🔍 Found ${compatibleRooms.length} compatible rooms:`, compatibleRooms.map(r => r.id));

        if (compatibleRooms.length === 0) {
            console.warn(`No compatible event rooms found for entrance direction: ${requiredEntrance}`);
            return false;
        }

        // Prioritize rooms with matching primaryEntrance
        const preferredRooms = compatibleRooms.filter(room => room.primaryEntrance === fullDirectionName);
        const candidates = preferredRooms.length > 0 ? preferredRooms : compatibleRooms;

        // Select random compatible room
        const selectedRoom = candidates[Math.floor(Math.random() * candidates.length)];
        this._log(`Selected event room: ${selectedRoom.id} (${selectedRoom.name})`);

        // Place the event room
        const placed = this._attachEventRoom(branchEnd, selectedRoom, fullDirectionName);
        
        if (placed) {
            this.usedEventRooms.add(selectedRoom.id);
            this._log(`✅ Event room ${selectedRoom.id} placed and marked as used`);
            return true;
        }

        return false;
    }

    /**
     * Attach an event room to a branch end
     */
    _attachEventRoom(branchEnd, eventRoomData, entranceDirection) {
        const parentRoom = branchEnd.room;
        const connectionDirection = branchEnd.availableDirections[0]; // Use first available direction
        
        
        const directions = {
            n: { x: 0, y: -1 },
            s: { x: 0, y: 1 },
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 }
        };

        const newCoords = {
            x: parentRoom.coords.x + directions[connectionDirection].x,
            y: parentRoom.coords.y + directions[connectionDirection].y
        };

        // Create event room instance
        const eventRoom = this._addRoom(newCoords.x, newCoords.y, 'Event');
        if (!eventRoom) {
            console.error('Failed to create event room instance');
            return false;
        }

        // Configure event room
        eventRoom.eventRoomData = eventRoomData;
        eventRoom.shapeKey = eventRoomData.shape || 'SQUARE_1X1';
        
        // Apply event room configuration properties (matching applyEventRoomConfig)
        eventRoom.eventRoomKey = eventRoomData.id;
        eventRoom.eventRoomName = eventRoomData.name;
        
        // CRITICAL FIX: Apply event room data to roomData object for visual generation
        eventRoom.roomData.eventRoomData = eventRoomData;
        eventRoom.roomData.eventRoomKey = eventRoomData.id;
        eventRoom.roomData.eventRoomId = eventRoomData.id;
        eventRoom.roomData.eventRoomName = eventRoomData.name;
        eventRoom.roomData.entranceDirection = entranceDirection;
        eventRoom.roomData.doorPosition = eventRoomData.doorPositions ? eventRoomData.doorPositions[entranceDirection] : null;
        
        console.log(`🔧 Applied event room data to room ${eventRoom.id}:`, {
            eventRoomId: eventRoomData.id,
            eventRoomName: eventRoomData.name,
            shape: eventRoomData.shape,
            hasEventRoomData: !!eventRoom.roomData.eventRoomData
        });

        // Connect to parent room
        const oppositeDirection = getOppositeDirection(connectionDirection);
        parentRoom.neighbors[connectionDirection] = eventRoom.id;
        eventRoom.neighbors[oppositeDirection] = parentRoom.id;

        // Add to position tracking
        const positionKey = `${newCoords.x},${newCoords.y}`;
        this.roomPositions.add(positionKey);

        this._log(`✅ Event room ${eventRoomData.id} attached to room ${parentRoom.id} via ${connectionDirection} -> ${oppositeDirection}`);
        return true;
    }

    /**
     * Reset used event rooms (for testing/debugging)
     */
    resetUsedEventRooms() {
        this.usedEventRooms.clear();
        this.saveUsedEventRooms();
        console.log('Used event rooms reset');
    }

    /**
     * Get status of used event rooms (for debugging)
     */
    getUsedEventRoomsStatus() {
        return {
            used: [...this.usedEventRooms],
            count: this.usedEventRooms.size
        };
    }

    /**
     * Validate data consistency across all room data stores
     * Ensures room properties are synchronized between layout, roomData, and floorLayout
     */
    _validateDataConsistency() {
        this._log("=== VALIDATING DATA CONSISTENCY ===");

        let inconsistencyCount = 0;
        let errorMessages = [];

        for (const [roomId, room] of this.layout) {
            // Validate room internal consistency
            const roomValidation = room.validateConsistency();
            if (!roomValidation.isConsistent) {
                inconsistencyCount++;
                errorMessages.push(`Room ${roomId}: ${roomValidation.errors.join(', ')}`);
                
                // Auto-fix the inconsistency
                room.syncRoomData();
                this._log(`🔧 Auto-fixed inconsistency in Room ${roomId}`);
            }

            // Validate floorLayout consistency
            const floorRoom = this.floorLayout.get(roomId);
            if (floorRoom) {
                if (floorRoom.type !== room.type) {
                    inconsistencyCount++;
                    errorMessages.push(`Room ${roomId}: floorLayout type mismatch`);
                    
                    // Auto-fix
                    floorRoom.type = room.type;
                    this._log(`🔧 Fixed floorLayout type for Room ${roomId}: ${room.type}`);
                }

                if (floorRoom.shapeKey !== room.shapeKey) {
                    inconsistencyCount++;
                    errorMessages.push(`Room ${roomId}: floorLayout shapeKey mismatch`);
                    
                    // Auto-fix
                    floorRoom.shapeKey = room.shapeKey;
                    this._log(`🔧 Fixed floorLayout shapeKey for Room ${roomId}: ${room.shapeKey}`);
                }
            }
        }

        if (inconsistencyCount === 0) {
            this._log(`✅ Data consistency validation passed - all ${this.layout.size} rooms consistent`);
        } else {
            this._log(`⚠️ Fixed ${inconsistencyCount} data inconsistencies automatically`);
            if (this.debugMode) {
                errorMessages.forEach(msg => this._log(`  - ${msg}`));
            }
        }

        this._log("=== DATA CONSISTENCY VALIDATION COMPLETE ===");
    }

    /**
     * Performance-optimized room categorization
     * Single-pass categorization instead of multiple filter operations
     */
    _categorizeRooms() {
        const categories = {
            start: [],
            normal: [],
            boss: [],
            event: [],
            secret: []
        };

        for (const room of this.layout.values()) {
            const type = room.type.toLowerCase();
            switch (type) {
                case 'start':
                    categories.start.push(room);
                    break;
                case 'boss':
                    categories.boss.push(room);
                    break;
                case 'event':
                    categories.event.push(room);
                    break;
                case 'normal':
                    if (room.hasSecretRoom) {
                        categories.secret.push(room);
                    } else {
                        categories.normal.push(room);
                    }
                    break;
                default:
                    categories.normal.push(room); // Default fallback
            }
        }

        return categories;
    }

    /**
     * Fallback event room creation for error recovery
     * Creates a simple treasure room when event room system fails
     */
    // REMOVED: No fallback systems per CLAUDE.md - make the primary system work perfectly

    /**
     * Add resource cleanup method
     */
    dispose() {
        // Clear all Maps and Sets
        if (this.layout) {
            this.layout.clear();
        }
        if (this.floorLayout) {
            this.floorLayout.clear();
        }
        if (this.roomPositions) {
            this.roomPositions.clear();
        }
        if (this.usedEventRooms) {
            this.usedEventRooms.clear();
        }
        
        // Clear arrays
        if (this.rooms) {
            this.rooms.length = 0;
        }

        // Clear references
        this.currentArea = null;
        this.eventRoomManager = null;
        
        this._log("🧹 DungeonGenerator disposed - resources cleaned up");
    }
    
    /**
     * Force sync all boss room data to ensure it persists
     * @private
     */
    _forceSyncBossRoomData() {
        console.log("=== FORCE SYNC BOSS ROOM DATA ===");
        
        let bossRoomCount = 0;
        let syncedCount = 0;
        
        // Iterate through all rooms
        for (const [roomId, room] of this.layout) {
            if (room.type === 'Boss') {
                bossRoomCount++;
                
                // Force sync the room data
                room.syncRoomData();
                
                // Verify the sync worked
                const floorData = this.floorLayout.get(roomId);
                if (floorData && floorData.bossRoomData) {
                    syncedCount++;
                    console.log(`✅ Boss room ${roomId} synced successfully:`);
                    console.log(`  - staticBrightness: ${floorData.bossRoomData.staticBrightness}`);
                    console.log(`  - name: ${floorData.bossRoomData.name}`);
                    console.log(`  - removeTorches: ${floorData.bossRoomData.lighting?.removeTorches}`);
                } else {
                    console.error(`❌ Boss room ${roomId} FAILED to sync bossRoomData!`);
                    
                    // Try to manually copy the data as a last resort
                    if (room.roomData && room.roomData.bossRoomData && floorData) {
                        console.log(`🔧 Attempting manual copy of bossRoomData...`);
                        floorData.bossRoomData = room.roomData.bossRoomData;
                        floorData.bossRoomKey = room.roomData.bossRoomKey;
                        floorData.bossRoomName = room.roomData.bossRoomName;
                        floorData.areaKey = room.roomData.areaKey;
                        
                        // Verify manual copy
                        if (floorData.bossRoomData) {
                            syncedCount++;
                            console.log(`✅ Manual copy successful!`);
                        }
                    }
                }
            }
        }
        
        console.log(`🎯 Boss Room Sync Summary:`);
        console.log(`  - Total boss rooms: ${bossRoomCount}`);
        console.log(`  - Successfully synced: ${syncedCount}`);
        
        if (bossRoomCount > 0 && syncedCount === 0) {
            console.error(`❌ CRITICAL: No boss rooms were successfully synced!`);
        }
        
        console.log("=== END FORCE SYNC BOSS ROOM DATA ===");
    }
}

// Only export DungeonGenerator from this file
export { DungeonGenerator };

// Add global debug functions for testing
if (typeof window !== 'undefined') {
    window.resetUsedEventRooms = () => {
        try {
            localStorage.removeItem('usedEventRooms');
            console.log('✅ Used event rooms cleared from localStorage');
            console.log('🎪 Event rooms reset - all event rooms are now available again');
            return true;
        } catch (error) {
            console.error('❌ Error clearing used event rooms:', error);
            return false;
        }
    };
    
    window.getUsedEventRoomsStatus = () => {
        try {
            const stored = localStorage.getItem('usedEventRooms');
            const used = stored ? JSON.parse(stored) : [];
            console.log('📊 Used Event Rooms Status:');
            console.log(`  Used: ${used.length} rooms`);
            console.log(`  Rooms: [${used.join(', ')}]`);
            return { used, count: used.length };
        } catch (error) {
            console.error('Error reading used event rooms:', error);
            return { used: [], count: 0 };
        }
    };
    
    window.debugEventRoomSystem = () => {
        const status = window.getUsedEventRoomsStatus();
        console.log('Event Room System Status:', status);
        return status;
    };
    
    // Removed old showDungeonMap function - now properly implemented in DungeonHandler.js
    
    window.testEventRoomSystem = async () => {
        console.log('🎪 Testing event room system with new dungeon generation...');
        const generator = new DungeonGenerator();
        const layout = await generator.generateLayout('catacombs');
        console.log('🎪 New dungeon generated! Layout:', layout);
        console.log('🎪 Calling ASCII map on new dungeon...');
        generator.generateASCIIMap();
        return layout;
    };
    
    window.testEventRoomSystemQuick = async () => {
        console.log('🎪 Quick test - generating new dungeon...');
        const generator = new DungeonGenerator();
        await generator.generateLayout('catacombs');
        return generator.generateASCIIMap();
    };
}
