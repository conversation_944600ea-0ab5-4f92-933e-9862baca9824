// src/generators/DungeonGeneratorCore.js
// Pure computation logic for dungeon generation - no Three.js dependencies
// This code runs in web workers

// --- Constants ---
const MIN_ROOMS = 12;
const MAX_ROOMS = 25;
const MAX_PLACEMENT_ATTEMPTS = 150;
const MAX_GENERATION_RETRIES = 10;

const ROOM_SHAPES = [
    'SQUARE_1X1', 'L_SHAPE', 'T_SHAPE', 'RECTANGULAR', 'CROSS_SHAPE',
    'U_SHAPE_DOWN', 'U_SHAPE_UP', 'U_SHAPE_LEFT', 'U_SHAPE_RIGHT',
    'CORRIDOR_LONG', 'CORRIDOR_SHORT', 'SQUARE_2X2',
    'RECT_2X1', 'RECT_1X2', 'RECT_3X1', 'RECT_1X3', 'BOSS_ARENA'
];

// Helper function
function getOppositeDirection(dir) {
    const opposites = { n: 's', s: 'n', e: 'w', w: 'e' };
    return opposites[dir];
}

// Simple room data structure for worker processing
class WorkerRoom {
    constructor(id, type = 'Normal', areaConfig = null) {
        this.id = id;
        this.type = type;
        this.shapeKey = this._assignRandomShape(type, areaConfig);
        this.coords = { x: 0, y: 0 };
        this.neighbors = { n: null, s: null, e: null, w: null };
        this.visited = false;
        this.hasSecretRoom = false;
        this.state = {
            enemiesCleared: false,
            initialEnemies: [],
            area: null
        };
    }

    _assignRandomShape(roomType, areaConfig) {
        // Boss rooms always use BOSS_ARENA
        if (roomType === 'Boss') {
            return 'BOSS_ARENA';
        }
        
        // Start rooms use SQUARE_1X1 for consistency
        if (roomType === 'Start') {
            return 'SQUARE_1X1';
        }
        
        // Normal rooms use area's allowedShapes if available
        if (roomType === 'Normal' && areaConfig && areaConfig.data && areaConfig.data.allowedShapes && areaConfig.data.allowedShapes.length > 0) {
            const allowedShapes = areaConfig.data.allowedShapes;
            const randomIndex = Math.floor(Math.random() * allowedShapes.length);
            const selectedShape = allowedShapes[randomIndex];
            console.log(`🏗️ [Worker] Room assigned shape: ${selectedShape} from ${allowedShapes.length} options`);
            return selectedShape;
        }
        
        // DEBUG: Log why we're not using area shapes
        if (roomType === 'Normal') {
            console.log(`🏗️ [Worker] Normal room fallback - areaConfig: ${!!areaConfig}, data: ${!!areaConfig?.data}, allowedShapes: ${areaConfig?.data?.allowedShapes?.length}`);
        }
        
        // Fallback: use any shape except BOSS_ARENA
        const availableShapes = ROOM_SHAPES.filter(shape => shape !== 'BOSS_ARENA');
        const randomIndex = Math.floor(Math.random() * availableShapes.length);
        return availableShapes[randomIndex];
    }

    getConnectionCount() {
        return Object.values(this.neighbors).filter(n => n !== null).length;
    }

    serializeForTransfer() {
        return {
            id: this.id,
            type: this.type,
            shapeKey: this.shapeKey,
            coords: { ...this.coords },
            connections: { ...this.neighbors }, // Use 'connections' for API compatibility
            visited: this.visited,
            hasSecretRoom: this.hasSecretRoom,
            state: { ...this.state },
            preferredEntranceDirection: this.preferredEntranceDirection // For boss rooms
        };
    }
}

export class DungeonGeneratorCore {
    constructor() {
        this.layout = new Map();
        this.roomPositions = new Set();
        this.nextRoomId = 0;
        this.debugMode = false;
    }

    _log(message, ...args) {
        if (this.debugMode) {
            console.log(`[DungeonCore] ${message}`, ...args);
        }
    }

    _canPlaceRoom(x, y) {
        const posKey = `${x},${y}`;
        return !this.roomPositions.has(posKey);
    }

    _addRoom(x, y, type = 'Normal') {
        const newRoom = new WorkerRoom(this.nextRoomId++, type, this.areaConfig);
        newRoom.coords.x = x;
        newRoom.coords.y = y;

        // Track position
        const posKey = `${x},${y}`;
        this.roomPositions.add(posKey);

        // Store room
        this.layout.set(newRoom.id, newRoom);

        this._log(`Added room ${newRoom.id} at (${x}, ${y}) type: ${type}, shape: ${newRoom.shapeKey}`);
        return newRoom;
    }

    _getNormalRooms() {
        return Array.from(this.layout.values()).filter(room => room.type === 'Normal');
    }

    _calculatePathDistances() {
        if (!this.layout.has(0)) return new Map();

        const distances = new Map();
        const queue = [{ room: this.layout.get(0), distance: 0 }];
        const visited = new Set([0]);
        
        while (queue.length > 0) {
            const { room, distance } = queue.shift();
            distances.set(room.id, distance);
            
            for (const direction of ['n', 's', 'e', 'w']) {
                const neighborId = room.neighbors[direction];
                if (neighborId !== null && !visited.has(neighborId)) {
                    visited.add(neighborId);
                    const neighborRoom = this.layout.get(neighborId);
                    if (neighborRoom) {
                        queue.push({ room: neighborRoom, distance: distance + 1 });
                    }
                }
            }
        }
        
        return distances;
    }

    _assignBossRoom() {
        this._log("=== Boss Room Assignment ===");
        if (this.layout.size <= 1) {
            this._log("Cannot assign Boss room, only Start room exists.");
            return;
        }

        const normalRooms = this._getNormalRooms();
        if (normalRooms.length < 1) {
            this._log("No Normal rooms available for boss assignment");
            return;
        }

        const pathDistances = this._calculatePathDistances();
        
        // Find the TRUE maximum distance from start room
        let actualMaxDistance = 0;
        for (const room of normalRooms) {
            const distance = pathDistances.get(room.id) || 0;
            if (distance > actualMaxDistance) {
                actualMaxDistance = distance;
            }
        }
        
        this._log(`Maximum distance from start: ${actualMaxDistance}`);
        
        // Find ALL rooms at maximum distance
        const furthestRooms = normalRooms.filter(room => {
            const distance = pathDistances.get(room.id) || 0;
            return distance === actualMaxDistance;
        });
        
        this._log(`Found ${furthestRooms.length} rooms at maximum distance`);
        
        let selectedBossRoom = null;
        const preferredEntrance = 's';
        
        // FOR CATACOMBS: Enforce south entrance requirement
        // Look for rooms that can be converted to have ONLY a south entrance
        const southCapableFurthestRooms = furthestRooms.filter(room => {
            // Room must have south connection OR be convertible to have south connection
            return room.neighbors[preferredEntrance] !== null || this._canCreateSouthEntrance(room);
        });
        
        this._log(`Found ${southCapableFurthestRooms.length} rooms at max distance that can have/get south entrance`);
        
        // If no rooms at max distance can have south entrance, use next-furthest distance
        let candidateRooms = southCapableFurthestRooms;
        if (candidateRooms.length === 0) {
            this._log("No rooms at max distance support south entrance, checking next distance level");
            
            // Find second-highest distance that supports south entrance
            const distances = new Map();
            normalRooms.forEach(room => {
                const distance = pathDistances.get(room.id) || 0;
                if (!distances.has(distance)) distances.set(distance, []);
                distances.get(distance).push(room);
            });
            
            const sortedDistances = Array.from(distances.keys()).sort((a, b) => b - a);
            for (const distance of sortedDistances) {
                if (distance < actualMaxDistance) {
                    const roomsAtDistance = distances.get(distance);
                    const southCapableAtDistance = roomsAtDistance.filter(room => {
                        return room.neighbors[preferredEntrance] !== null || this._canCreateSouthEntrance(room);
                    });
                    
                    if (southCapableAtDistance.length > 0) {
                        candidateRooms = southCapableAtDistance;
                        this._log(`Using rooms at distance ${distance} that support south entrance`);
                        break;
                    }
                }
            }
        }
        
        // Prioritize selection among south-capable rooms:
        // 1. Dead-ends with south entrance (already perfect)
        // 2. Rooms with south entrance connection (need conversion to dead-end)
        
        // Priority 1: Dead-ends with south entrance (already perfect)
        for (const room of candidateRooms) {
            if (room.getConnectionCount() === 1 && room.neighbors[preferredEntrance] !== null) {
                selectedBossRoom = room;
                this._log(`Selected dead-end room with south entrance: Room ${room.id}`);
                break;
            }
        }
        
        // Priority 2: Rooms with south entrance connection (need conversion to dead-end)
        if (!selectedBossRoom) {
            for (const room of candidateRooms) {
                if (room.neighbors[preferredEntrance] !== null) {
                    selectedBossRoom = room;
                    this._log(`Selected room with south connection: Room ${room.id}`);
                    break;
                }
            }
        }
        
        // Emergency fallback: Use any room at maximum distance
        if (!selectedBossRoom && furthestRooms.length > 0) {
            selectedBossRoom = furthestRooms[0];
            this._log(`Emergency fallback: Using room at max distance without south entrance: Room ${selectedBossRoom.id}`);
        }
        
        // Emergency fallback (should never happen)
        if (!selectedBossRoom && normalRooms.length > 0) {
            selectedBossRoom = normalRooms[0];
            this._log(`Emergency fallback: Using first normal room ${selectedBossRoom.id}`);
        }

        if (selectedBossRoom) {
            selectedBossRoom.type = 'Boss';
            selectedBossRoom.shapeKey = 'BOSS_ARENA'; // Ensure boss arena shape
            
            // CRITICAL: Convert to dead-end with ONLY south entrance
            this._convertToSouthOnlyDeadEnd(selectedBossRoom);
            
            // Store entrance direction for post-processing
            selectedBossRoom.preferredEntranceDirection = 'south';
            
            const finalDistance = pathDistances.get(selectedBossRoom.id) || 0;
            this._log(`Assigned Boss Room: ${selectedBossRoom.id} at distance ${finalDistance} (max: ${actualMaxDistance})`);
            this._log(`Boss room entrance direction: ${selectedBossRoom.preferredEntranceDirection}`);
            this._log(`Boss room connections after conversion: N=${selectedBossRoom.neighbors.n}, S=${selectedBossRoom.neighbors.s}, E=${selectedBossRoom.neighbors.e}, W=${selectedBossRoom.neighbors.w}`);
        }
    }

    _canCreateSouthEntrance(room) {
        // Check if there's a room to the south that we can connect to
        const southY = room.coords.y + 1;
        for (const otherRoom of this.layout.values()) {
            if (otherRoom.coords.x === room.coords.x && otherRoom.coords.y === southY) {
                return true; // Found a room to the south
            }
        }
        return false;
    }

    _convertToSouthOnlyDeadEnd(bossRoom) {
        this._log(`Converting room ${bossRoom.id} to south-only dead-end`);
        
        // First, remove ALL existing connections
        for (const direction of ['n', 's', 'e', 'w']) {
            const neighborId = bossRoom.neighbors[direction];
            if (neighborId !== null) {
                const neighbor = this.layout.get(neighborId);
                if (neighbor) {
                    const oppositeDir = getOppositeDirection(direction);
                    neighbor.neighbors[oppositeDir] = null;
                    this._log(`Removed connection from ${bossRoom.id} to ${neighborId} (${direction})`);
                }
                bossRoom.neighbors[direction] = null;
            }
        }
        
        // Now create ONLY a south connection
        const southY = bossRoom.coords.y + 1;
        let southRoom = null;
        for (const room of this.layout.values()) {
            if (room.coords.x === bossRoom.coords.x && room.coords.y === southY) {
                southRoom = room;
                break;
            }
        }
        
        if (southRoom) {
            // Create the south connection
            bossRoom.neighbors.s = southRoom.id;
            southRoom.neighbors.n = bossRoom.id;
            this._log(`Created ONLY south connection: Boss ${bossRoom.id} ← ${southRoom.id}`);
        } else {
            this._log(`ERROR: Could not find room to south of boss room ${bossRoom.id} at (${bossRoom.coords.x}, ${southY})`);
        }
        
        // Verify boss room is now a dead-end with only south connection
        const connectionCount = Object.values(bossRoom.neighbors).filter(id => id !== null).length;
        this._log(`Boss room ${bossRoom.id} now has ${connectionCount} connection(s)`);
    }

    // Helper to determine entrance direction based on connections
    _getEntranceDirection(room) {
        if (room.neighbors.s !== null) return 'south';
        if (room.neighbors.n !== null) return 'north';
        if (room.neighbors.e !== null) return 'east';
        if (room.neighbors.w !== null) return 'west';
        return 'south'; // Default fallback
    }

    _convertToDeadEnd(room, preferredEntrance = 's') {
        const connections = Object.keys(room.neighbors).filter(dir => room.neighbors[dir] !== null);
        
        if (connections.length <= 1) return; // Already a dead end

        // Keep the preferred entrance if it exists, otherwise keep the first connection
        const keepConnection = connections.includes(preferredEntrance) ? preferredEntrance : connections[0];
        
        // Remove all other connections
        for (const dir of connections) {
            if (dir !== keepConnection) {
                const neighborId = room.neighbors[dir];
                if (neighborId !== null) {
                    const neighbor = this.layout.get(neighborId);
                    if (neighbor) {
                        const oppositeDir = getOppositeDirection(dir);
                        neighbor.neighbors[oppositeDir] = null;
                    }
                    room.neighbors[dir] = null;
                }
            }
        }

        this._log(`Converted room ${room.id} to dead-end (kept ${keepConnection} entrance)`);
    }

    _convertToDeadEndSafely(room, preferredEntrance = 's') {
        const connections = Object.keys(room.neighbors).filter(dir => room.neighbors[dir] !== null);
        
        if (connections.length <= 1) return; // Already a dead end

        // FOR CATACOMBS BOSS: Enforce south entrance creation
        let keepConnection = null;
        
        // Priority 1: Keep existing south connection if available
        if (connections.includes(preferredEntrance)) {
            keepConnection = preferredEntrance;
            this._log(`Room already has ${preferredEntrance} connection, keeping it`);
        }
        // Priority 2: Create south entrance by modifying room layout if possible
        else {
            // To have a "south entrance", the boss room needs a connection TO THE SOUTH
            // This means we need to keep a connection where the boss room connects southward
            
            // Look for any connection that can be converted to south entrance
            // We need a connection in the south direction (room.neighbors.s)
            // If we don't have that, we'll have to work with what we have
            
            this._log(`Room lacks south connection. Available connections: ${connections.join(', ')}`);
            
            // For now, keep the preferred entrance if possible, otherwise first connection
            // The entrance direction will be determined by the connection we keep
            keepConnection = connections.includes(preferredEntrance) ? preferredEntrance : connections[0];
            this._log(`Selected ${keepConnection} connection (will determine actual entrance direction)`);
        }
        
        // Before removing connections, check if any neighbors would become unreachable
        const connectionsToRemove = connections.filter(dir => dir !== keepConnection);
        
        for (const dir of connectionsToRemove) {
            const neighborId = room.neighbors[dir];
            if (neighborId !== null) {
                const neighbor = this.layout.get(neighborId);
                if (neighbor) {
                    // Check if neighbor has alternative paths to the start room
                    const wouldBecomeUnreachable = this._wouldBecomeUnreachable(neighbor.id, room.id, dir);
                    
                    if (wouldBecomeUnreachable) {
                        this._log(`Warning: Removing connection ${room.id}-${neighbor.id} would create unreachable rooms. Skipping this connection.`);
                        continue; // Skip removing this connection to maintain connectivity
                    }
                    
                    // Safe to remove - no connectivity issues
                    const oppositeDir = getOppositeDirection(dir);
                    neighbor.neighbors[oppositeDir] = null;
                    room.neighbors[dir] = null;
                    this._log(`Safely removed connection ${room.id}-${neighbor.id}`);
                }
            }
        }

        this._log(`Safely converted room ${room.id} to dead-end (kept ${keepConnection} entrance)`);
    }

    _wouldBecomeUnreachable(testRoomId, fromRoomId, fromDirection) {
        // Temporarily remove the connection and test reachability
        const fromRoom = this.layout.get(fromRoomId);
        const testRoom = this.layout.get(testRoomId);
        
        if (!fromRoom || !testRoom) return false;
        
        // Temporarily sever the connection
        const oppositeDir = getOppositeDirection(fromDirection);
        const originalFromConnection = fromRoom.neighbors[fromDirection];
        const originalTestConnection = testRoom.neighbors[oppositeDir];
        
        fromRoom.neighbors[fromDirection] = null;
        testRoom.neighbors[oppositeDir] = null;
        
        // Test if testRoom is still reachable from start (room 0)
        const reachable = new Set();
        const queue = [0]; // Start from room 0
        
        while (queue.length > 0) {
            const roomId = queue.shift();
            if (reachable.has(roomId)) continue;
            
            reachable.add(roomId);
            
            const room = this.layout.get(roomId);
            if (!room) continue;
            
            for (const direction of ['n', 's', 'e', 'w']) {
                const neighborId = room.neighbors[direction];
                if (neighborId !== null && !reachable.has(neighborId)) {
                    queue.push(neighborId);
                }
            }
        }
        
        // Restore the connection
        fromRoom.neighbors[fromDirection] = originalFromConnection;
        testRoom.neighbors[oppositeDir] = originalTestConnection;
        
        // Check if the test room (and potentially others) became unreachable
        const isUnreachable = !reachable.has(testRoomId);
        
        if (isUnreachable) {
            this._log(`Connection removal would make room ${testRoomId} unreachable`);
        }
        
        return isUnreachable;
    }

    _assignSecretRoom() {
        this._log("=== Secret Room Assignment ===");
        
        const normalRooms = this._getNormalRooms();
        if (normalRooms.length < 1) {
            this._log("No Normal rooms available for secret room assignment");
            return;
        }

        // Find rooms with only 1 or 2 connections (good candidates for secret rooms)
        const suitableRooms = normalRooms.filter(room => {
            const connectionCount = room.getConnectionCount();
            return connectionCount >= 1 && connectionCount <= 2;
        });

        if (suitableRooms.length === 0) {
            this._log("No suitable rooms found for secret room assignment");
            return;
        }

        // Prefer rooms with fewer connections
        suitableRooms.sort((a, b) => a.getConnectionCount() - b.getConnectionCount());
        
        const selectedRoom = suitableRooms[0];
        selectedRoom.hasSecretRoom = true;
        
        this._log(`Assigned Secret Room to Room ${selectedRoom.id} (${selectedRoom.getConnectionCount()} connections)`);
    }


    _ensureFullConnectivity() {
        this._log("=== Ensuring Full Connectivity ===");
        
        if (!this.layout.has(0)) {
            this._log("ERROR: No start room found");
            return;
        }

        // Find all unreachable rooms
        const reachable = new Set();
        const queue = [0];
        
        while (queue.length > 0) {
            const roomId = queue.shift();
            if (reachable.has(roomId)) continue;
            
            reachable.add(roomId);
            
            const room = this.layout.get(roomId);
            if (room) {
                for (const direction of ['n', 's', 'e', 'w']) {
                    const neighborId = room.neighbors[direction];
                    if (neighborId !== null && !reachable.has(neighborId)) {
                        queue.push(neighborId);
                    }
                }
            }
        }

        // Find unreachable rooms
        const unreachableRooms = [];
        this.layout.forEach((room, id) => {
            if (!reachable.has(id)) {
                unreachableRooms.push(room);
            }
        });

        if (unreachableRooms.length > 0) {
            this._log(`Found ${unreachableRooms.length} unreachable rooms, attempting to reconnect...`);
            
            // Try to reconnect each unreachable room
            for (const unreachableRoom of unreachableRooms) {
                this._reconnectRoomToNetwork(unreachableRoom, reachable);
            }
            
            // Recheck connectivity after reconnection attempts
            const newReachableCheck = this._findReachableRooms();
            const stillUnreachable = this.layout.size - newReachableCheck.size;
            
            if (stillUnreachable > 0) {
                this._log(`WARNING: ${stillUnreachable} rooms still unreachable after reconnection attempts`);
            } else {
                this._log(`✅ Successfully reconnected all unreachable rooms`);
            }
        } else {
            this._log(`✅ All rooms already reachable`);
        }
    }

    _reconnectRoomToNetwork(unreachableRoom, reachableRoomIds) {
        const directions = ['n', 's', 'e', 'w'];
        const deltas = {
            n: { x: 0, y: -1 },
            s: { x: 0, y: 1 },
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 }
        };

        // Find the closest reachable room to connect to
        let bestConnection = null;
        let minDistance = Infinity;

        for (const direction of directions) {
            const delta = deltas[direction];
            const targetX = unreachableRoom.coords.x + delta.x;
            const targetY = unreachableRoom.coords.y + delta.y;

            // Find room at target position
            for (const room of this.layout.values()) {
                if (room.coords.x === targetX && room.coords.y === targetY && reachableRoomIds.has(room.id)) {
                    const distance = Math.abs(targetX) + Math.abs(targetY);
                    if (distance < minDistance) {
                        minDistance = distance;
                        bestConnection = {
                            direction: direction,
                            targetRoom: room,
                            oppositeDirection: getOppositeDirection(direction)
                        };
                    }
                    break;
                }
            }
        }

        if (bestConnection) {
            // Special handling: don't break boss room dead-end configuration
            if (bestConnection.targetRoom.type === 'Boss') {
                // Boss rooms should remain dead-ends, so skip this connection
                this._log(`Skipping connection to boss room ${bestConnection.targetRoom.id} to preserve dead-end configuration`);
                return false;
            }

            // Special handling: don't break event room entrance configurations
            if (bestConnection.targetRoom.type === 'EVENT') {
                // Event rooms should maintain their specific entrance configurations
                this._log(`Skipping connection to event room ${bestConnection.targetRoom.id} to preserve entrance configuration`);
                return false;
            }

            // Create the connection
            unreachableRoom.neighbors[bestConnection.direction] = bestConnection.targetRoom.id;
            bestConnection.targetRoom.neighbors[bestConnection.oppositeDirection] = unreachableRoom.id;
            
            this._log(`Reconnected room ${unreachableRoom.id} to room ${bestConnection.targetRoom.id} via ${bestConnection.direction}`);
            return true;
        }

        this._log(`Failed to reconnect room ${unreachableRoom.id} - no adjacent reachable rooms found`);
        return false;
    }

    _findReachableRooms() {
        const reachable = new Set();
        const queue = [0];
        
        while (queue.length > 0) {
            const roomId = queue.shift();
            if (reachable.has(roomId)) continue;
            
            reachable.add(roomId);
            
            const room = this.layout.get(roomId);
            if (room) {
                for (const direction of ['n', 's', 'e', 'w']) {
                    const neighborId = room.neighbors[direction];
                    if (neighborId !== null && !reachable.has(neighborId)) {
                        queue.push(neighborId);
                    }
                }
            }
        }
        
        return reachable;
    }

    _validateLinearProgression() {
        this._log("=== Validating Linear Progression ===");
        
        let issuesFound = 0;
        
        this.layout.forEach((room, id) => {
            const connectionCount = Object.values(room.neighbors).filter(neighborId => neighborId !== null).length;
            
            if (room.type === 'Start') {
                // Start room can have multiple connections (it's the entry point)
                this._log(`Start room ${id}: ${connectionCount} connections ✓`);
            } else if (room.type === 'Boss') {
                // Boss room should have exactly 1 connection (south entrance)
                if (connectionCount !== 1) {
                    this._log(`❌ Boss room ${id} has ${connectionCount} connections, should have 1`);
                    issuesFound++;
                } else {
                    this._log(`Boss room ${id}: ${connectionCount} connection ✓`);
                }
            } else {
                // All other rooms should have exactly 1 entrance (plus any exits they provide)
                // But since we're building linearly, most should have 1-2 connections
                if (connectionCount === 0) {
                    this._log(`❌ Room ${id} has 0 connections - unreachable`);
                    issuesFound++;
                } else {
                    this._log(`Room ${id} (${room.type}): ${connectionCount} connections`);
                }
            }
        });
        
        if (issuesFound === 0) {
            this._log(`✅ Linear progression validation passed`);
        } else {
            this._log(`❌ Found ${issuesFound} linear progression issues`);
        }
        
        return issuesFound === 0;
    }

    _validateFinalConnectivity() {
        this._log("=== Final Connectivity Validation ===");
        
        if (!this.layout.has(0)) {
            this._log("ERROR: No start room found");
            return { isFullyConnected: false, unreachableCount: this.layout.size, error: "No start room" };
        }

        // BFS from start room to find all reachable rooms
        const reachable = this._findReachableRooms();

        const totalRooms = this.layout.size;
        const reachableCount = reachable.size;
        const unreachableCount = totalRooms - reachableCount;
        const isFullyConnected = unreachableCount === 0;

        this._log(`Total rooms: ${totalRooms}, Reachable: ${reachableCount}, Unreachable: ${unreachableCount}`);
        
        // Specifically check boss room connectivity
        const bossRooms = Array.from(this.layout.values()).filter(room => room.type === 'Boss');
        if (bossRooms.length > 0) {
            const bossReachable = reachable.has(bossRooms[0].id);
            this._log(`Boss room ${bossRooms[0].id} reachable: ${bossReachable}`);
            
            if (!bossReachable) {
                this._log(`CRITICAL: Boss room is unreachable - dungeon generation failed`);
                return { isFullyConnected: false, unreachableCount: unreachableCount + 1, error: "Boss room unreachable" };
            }
        }

        if (isFullyConnected) {
            this._log("✅ All rooms are reachable - connectivity validation PASSED");
        } else {
            this._log(`❌ ${unreachableCount} rooms are unreachable - connectivity validation FAILED`);
            
            // Log which rooms are unreachable for debugging
            const unreachableRooms = [];
            this.layout.forEach((room, id) => {
                if (!reachable.has(id)) {
                    unreachableRooms.push(`Room ${id} (${room.type}) at (${room.coords.x}, ${room.coords.y})`);
                }
            });
            this._log(`Unreachable rooms: ${unreachableRooms.join(', ')}`);
        }

        return {
            isFullyConnected,
            reachableCount,
            unreachableCount,
            totalRooms
        };
    }

    _addCrossConnections(newRoom, directions, probability = 0.3) {
        if (Math.random() > probability) return;

        // Try to connect to adjacent existing rooms (not the one we just connected to)
        for (const direction of ['n', 's', 'e', 'w']) {
            if (newRoom.neighbors[direction] !== null) continue; // Already connected

            const delta = directions[direction];
            const targetX = newRoom.coords.x + delta.x;
            const targetY = newRoom.coords.y + delta.y;

            // Find existing room at this position
            for (const existingRoom of this.layout.values()) {
                if (existingRoom.coords.x === targetX && existingRoom.coords.y === targetY) {
                    const oppositeDir = getOppositeDirection(direction);
                    
                    // Create cross-connection if the other room also has a free slot
                    if (existingRoom.neighbors[oppositeDir] === null) {
                        newRoom.neighbors[direction] = existingRoom.id;
                        existingRoom.neighbors[oppositeDir] = newRoom.id;
                        this._log(`Added cross-connection: Room ${newRoom.id} <-> Room ${existingRoom.id}`);
                        return; // Only add one cross-connection per room
                    }
                    break;
                }
            }
        }
    }

    _validateConnections() {
        let fixes = 0;
        
        this.layout.forEach(room => {
            for (const direction of ['n', 's', 'e', 'w']) {
                const neighborId = room.neighbors[direction];
                if (neighborId !== null) {
                    const neighbor = this.layout.get(neighborId);
                    if (!neighbor) {
                        // Neighbor doesn't exist, remove connection
                        room.neighbors[direction] = null;
                        fixes++;
                    } else {
                        // Check if neighbor has reciprocal connection
                        const oppositeDir = getOppositeDirection(direction);
                        if (neighbor.neighbors[oppositeDir] !== room.id) {
                            // Fix reciprocal connection
                            neighbor.neighbors[oppositeDir] = room.id;
                            fixes++;
                        }
                    }
                }
            }
        });

        if (fixes > 0) {
            this._log(`Fixed ${fixes} connection inconsistencies`);
        }
    }

    async generateLayout(areaConfig = {}, _retryCount = 0) {
        this._log(`Starting layout generation (attempt ${_retryCount + 1})`);
        
        // Store area config for shape assignment
        this.areaConfig = areaConfig;
        
        if (_retryCount > MAX_GENERATION_RETRIES) {
            throw new Error(`Exceeded max retries (${MAX_GENERATION_RETRIES})`);
        }

        // Reset state
        this.layout.clear();
        this.roomPositions.clear();
        this.nextRoomId = 0;

        const currentTargetRooms = MIN_ROOMS + Math.floor(Math.random() * (MAX_ROOMS - MIN_ROOMS + 1));
        
        const directions = {
            n: { x: 0, y: -1 },
            s: { x: 0, y: 1 },
            e: { x: 1, y: 0 },
            w: { x: -1, y: 0 },
        };

        // Create starting room
        const startRoom = this._addRoom(0, 0, 'Start');
        startRoom.visited = true;
        const activeRooms = [startRoom];

        let attempts = 0;
        const maxTotalAttempts = MAX_PLACEMENT_ATTEMPTS * currentTargetRooms;

        // Grow the dungeon with linear progression - each room has only one entrance
        while (activeRooms.length > 0 && this.layout.size < currentTargetRooms && attempts < maxTotalAttempts) {
            attempts++;
            
            // Work from active rooms but prioritize linear growth
            let currentRoom;
            
            // 70% chance to continue main path, 30% chance to branch from earlier rooms
            if (Math.random() < 0.7 && activeRooms.length > 0) {
                // Continue main path - use the last room
                currentRoom = activeRooms[activeRooms.length - 1];
            } else {
                // Create branch - use a random earlier room
                const randomIndex = Math.floor(Math.random() * activeRooms.length);
                currentRoom = activeRooms[randomIndex];
            }

            const availableDirections = Object.keys(directions).filter(dir => {
                const nx = currentRoom.coords.x + directions[dir].x;
                const ny = currentRoom.coords.y + directions[dir].y;
                return this._canPlaceRoom(nx, ny);
            });

            if (availableDirections.length > 0) {
                const chosenDir = availableDirections[Math.floor(Math.random() * availableDirections.length)];
                const oppositeDir = getOppositeDirection(chosenDir);

                const newX = currentRoom.coords.x + directions[chosenDir].x;
                const newY = currentRoom.coords.y + directions[chosenDir].y;

                const newRoom = this._addRoom(newX, newY);

                // Connect the rooms - each room has exactly one entrance
                currentRoom.neighbors[chosenDir] = newRoom.id;
                newRoom.neighbors[oppositeDir] = currentRoom.id;

                activeRooms.push(newRoom);
                
                this._log(`Connected room ${currentRoom.id} → room ${newRoom.id} (${chosenDir}). Room ${newRoom.id} has 1 entrance.`);
            } else {
                // Remove this room from active list since it can't expand further
                const roomIndex = activeRooms.indexOf(currentRoom);
                if (roomIndex !== -1) {
                    activeRooms.splice(roomIndex, 1);
                }
            }
        }

        // Ensure start room has north connection
        const startRoomInstance = this.layout.get(0);
        if (startRoomInstance && !startRoomInstance.neighbors.n) {
            const nx = startRoomInstance.coords.x;
            const ny = startRoomInstance.coords.y - 1;

            let northRoom = null;
            for (const room of this.layout.values()) {
                if (room.coords.x === nx && room.coords.y === ny) {
                    northRoom = room;
                    break;
                }
            }

            if (northRoom) {
                startRoomInstance.neighbors.n = northRoom.id;
                northRoom.neighbors.s = startRoomInstance.id;
            } else if (this._canPlaceRoom(nx, ny)) {
                northRoom = this._addRoom(nx, ny, 'Normal');
                startRoomInstance.neighbors.n = northRoom.id;
                northRoom.neighbors.s = startRoomInstance.id;
            }
        }

        // Post-processing - linear dungeon already has perfect connectivity
        this._assignBossRoom(); // Boss room is already a dead-end at maximum distance
        this._assignSecretRoom();
        this._validateConnections(); // Just fix any minor inconsistencies
        
        // Verification - should always pass with linear generation
        const finalConnectivityCheck = this._validateFinalConnectivity();
        if (!finalConnectivityCheck.isFullyConnected && _retryCount < MAX_GENERATION_RETRIES) {
            this._log(`UNEXPECTED: Linear dungeon connectivity failed. Retrying...`);
            return await this.generateLayout(areaConfig, _retryCount + 1);
        }
        
        // Verify each room has exactly one entrance (except start room)
        this._validateLinearProgression();

        // Check if we have minimum rooms
        if (this.layout.size < MIN_ROOMS && _retryCount < MAX_GENERATION_RETRIES) {
            this._log(`Only ${this.layout.size} rooms generated, retrying...`);
            return await this.generateLayout(areaConfig, _retryCount + 1);
        }

        this._log(`Layout generation complete: ${this.layout.size} rooms`);
        
        // Serialize for transfer
        const serializedRooms = {};
        this.layout.forEach((room, id) => {
            serializedRooms[id] = room.serializeForTransfer();
        });

        return {
            rooms: serializedRooms,
            roomCount: this.layout.size,
            metadata: {
                targetRooms: currentTargetRooms,
                attempts: attempts,
                retryCount: _retryCount
            }
        };
    }
}