/**
 * Event Room Auto-Loader
 * 
 * This file automatically discovers and loads all event room files.
 * Each event room should be in its own .js file in this directory.
 * 
 * To add a new event room:
 * 1. Create a new .js file (e.g., hauntedLibrary.js)
 * 2. Export EVENT_ROOM_DATA with the room configuration
 * 3. Add the import to the ROOM_IMPORTS array below
 * 4. The room will be automatically available in the game
 */

// Import all individual event room files
import ominousTreasure from './ominousTreasure.js';
import mysteriousPond from './mysteriousPond.js';
import chronalAnomaly from './chronalAnomaly.js';
import eyeOfJudgment from './eyeOfJudgment.js';
import devilsChessRoom from './devilsChessRoom.js';
import guardiansOfLies from './guardiansOfLies.js';
import arcadeGame from './arcadeGame.js';

// Add new event room imports here:
// import hauntedLibrary from './hauntedLibrary.js';
// import crystalCave from './crystalCave.js';
// import ancientAltar from './ancientAltar.js';
// import forgottenTomb from './forgottenTomb.js';
// import cursedArmory from './cursedArmory.js';
// ... (add up to 100+ rooms)

/**
 * Array of all event room imports
 * Add new imports to this array to register them automatically
 */
const ROOM_IMPORTS = [
    ominousTreasure,
    mysteriousPond,
    chronalAnomaly,
    eyeOfJudgment,
    devilsChessRoom,
    guardiansOfLies,
    arcadeGame,
    
    // Add new rooms here:
    // hauntedLibrary,
    // crystalCave,
    // ancientAltar,
    // forgottenTomb,
    // cursedArmory,
    // ... (continue adding)
];

/**
 * Automatically merge all event rooms into a single registry
 */
export const EVENT_ROOMS = ROOM_IMPORTS.reduce((registry, roomModule) => {
    return { ...registry, ...roomModule };
}, {});

/**
 * Get list of all available event room IDs (updated for new architecture)
 */
export function getAvailableEventRooms() {
    const roomIds = [];

    for (const [key, value] of Object.entries(EVENT_ROOMS)) {
        // Skip 'handler' keys - they are class constructors, not room data
        if (key === 'handler') {
            continue;
        }

        // Check if this is a valid room data object
        if (typeof value === 'object' && value !== null && value.id) {
            roomIds.push(value.id);
        } else if (typeof value === 'string') {
            // Fallback for simple string IDs (old architecture)
            roomIds.push(key);
        }
    }

    console.log(`[EventRooms] Available event room IDs:`, roomIds);
    return roomIds;
}

/**
 * Get event room data by ID (updated for new architecture)
 */
export function getEventRoom(roomId) {
    console.log(`[EventRooms] Looking for room '${roomId}'`);
    console.log(`[EventRooms] Available keys in EVENT_ROOMS:`, Object.keys(EVENT_ROOMS));
    
    // First, try direct key lookup (the room ID should be a key)
    if (EVENT_ROOMS[roomId] && typeof EVENT_ROOMS[roomId] === 'object' && EVENT_ROOMS[roomId] !== null) {
        console.log(`[EventRooms] Found room data for '${roomId}' via direct lookup:`, EVENT_ROOMS[roomId]);
        return EVENT_ROOMS[roomId];
    }

    // Fallback: try to find by searching through all room data objects
    for (const [key, value] of Object.entries(EVENT_ROOMS)) {
        if (typeof value === 'object' && value !== null && value.id === roomId) {
            console.log(`[EventRooms] Found room data for '${roomId}' via fallback search:`, value);
            return value;
        }
    }

    console.warn(`[EventRooms] Room '${roomId}' not found`);
    console.warn(`[EventRooms] Available EVENT_ROOMS structure:`, EVENT_ROOMS);
    return null;
}

/**
 * Validate event room data structure (updated for new architecture)
 */
export function validateEventRoom(eventRoomData) {
    // New architecture: mechanics are handled by room handler classes
    const required = ['id', 'name', 'shape', 'objects'];
    for (const field of required) {
        if (!eventRoomData[field]) {
            return { valid: false, error: `Missing required field: ${field}` };
        }
    }
    return { valid: true };
}

/**
 * Get random unused event room
 */
export function getRandomUnusedEventRoom(usedRooms = new Set()) {
    const availableRooms = getAvailableEventRooms().filter(id => !usedRooms.has(id));
    if (availableRooms.length === 0) return null;
    return availableRooms[Math.floor(Math.random() * availableRooms.length)];
}

/**
 * Get event rooms by category/tag (if rooms have tags)
 */
export function getEventRoomsByTag(tag) {
    return Object.values(EVENT_ROOMS).filter(room => 
        room.tags && room.tags.includes(tag)
    );
}

/**
 * Get event room statistics
 */
export function getEventRoomStats() {
    const rooms = Object.values(EVENT_ROOMS);
    return {
        total: rooms.length,
        byShape: rooms.reduce((acc, room) => {
            acc[room.shape] = (acc[room.shape] || 0) + 1;
            return acc;
        }, {}),
        byTriggerType: rooms.reduce((acc, room) => {
            const triggerType = room.mechanics?.triggerType || 'unknown';
            acc[triggerType] = (acc[triggerType] || 0) + 1;
            return acc;
        }, {}),
        withDialogue: rooms.filter(room => room.mechanics?.onTrigger?.dialogue).length,
        withEnemies: rooms.filter(room => room.enemySpawns?.length > 0).length
    };
}

// Log statistics when loaded (helpful for development)
console.log('[EventRooms] Loaded event rooms:', getEventRoomStats());
