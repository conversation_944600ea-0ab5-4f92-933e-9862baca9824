import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';

/**
 * Ominous Treasure Chamber Event Room
 *
 * A golden chamber filled with Egyptian treasures.
 * When the player opens the central chest, skeleton enemies spawn from the corners.
 */

/**
 * Ominous Treasure Room Handler
 */
export class OminousTreasureRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[OminousTreasureRoom] Handling interaction with ${objectId}`);

        if (objectId === 'ominous_treasure_chest') {
            await this.handleTreasureChestInteraction();
        } else {
            console.warn(`[OminousTreasureRoom] Unknown object interaction: ${objectId}`);
        }
    }

    /**
     * Handle custom interactions specific to Ominous Treasure Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[OminousTreasureRoom] 🎯 Handling custom interaction:`, interaction);

        switch (interaction.type) {
            case 'ominous_chest':
                await this.handleTreasureChestInteraction();
                break;
            case 'cursed_treasure':
                await this.handleTreasureChestInteraction();
                break;
            case 'treasure_trap':
                await this.handleTreasureChestInteraction();
                break;
            default:
                console.warn(`[OminousTreasureRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle treasure chest interaction - spawn enemies when opened
     */
    async handleTreasureChestInteraction() {
        if (this.state.triggered) {
            console.log(`[OminousTreasureRoom] Treasure chest already triggered`);
            return;
        }

        this.state.triggered = true;

        console.log(`[OminousTreasureRoom] Treasure chest opened - spawning skeleton guards`);

        // Play audio
        this.playAudio("nightmare_chest");

        // Spawn skeleton enemies from corners
        const enemySpawns = [
            {
                type: "skeleton",
                position: { x: -3, y: 0, z: -3 },
                spawnDelay: 0.5
            },
            {
                type: "skeleton",
                position: { x: 3, y: 0, z: -3 },
                spawnDelay: 1.0
            },
            {
                type: "skeleton",
                position: { x: -3, y: 0, z: 3 },
                spawnDelay: 1.5
            },
            {
                type: "skeleton",
                position: { x: 3, y: 0, z: 3 },
                spawnDelay: 2.0
            }
        ];

        this.spawnEnemies(enemySpawns);
    }

    /**
     * Handle enemy defeat - no special mechanics for this room
     */
    handleEnemyDefeat(enemyId) {
        super.handleEnemyDefeat(enemyId);
        console.log(`[OminousTreasureRoom] Skeleton guard ${enemyId} defeated`);
        // No special mechanics - just let player loot the chest
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "ominous_treasure",
    name: "Ominous Treasure Chamber",
    shape: "SQUARE_1X1",
    description: "A golden chamber filled with Egyptian treasures",
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "north", // Preferred entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -7 },   // Door on south wall when entered from north
        south: { x: 0, y: 0, z: 7 },    // Door on north wall when entered from south
        east: { x: -7, y: 0, z: 0 },    // Door on west wall when entered from east
        west: { x: 7, y: 0, z: 0 }      // Door on east wall when entered from west
    },
    
    // Visual overrides - now using the newly created sandstone prefabs
    materials: {
        walls: "sandstone_brick", // Custom sandstone brick wall prefab
        floors: "sandstone_floor", // Custom sandstone floor prefab
        wallTint: 0xD4AF37, // Golden tint overlay
        floorTint: 0xDAA520 // Darker gold for floor
    },

    // Static brightness override - golden treasure chamber atmosphere
    staticBrightness: 7, // Bright (7/10) - golden treasures should gleam and be clearly visible
    
    // Lighting configuration
    lighting: {
        ambient: {
            intensity: 0.2, // Dark ambient
            color: 0x404040
        },
        spotlight: {
            position: { x: 0, y: 8, z: 0 }, // Above chest
            target: { x: 0, y: 1, z: 0 }, // Chest position
            intensity: 2.0,
            color: 0xFFD700, // Golden light
            angle: Math.PI / 6, // 30 degrees
            penumbra: 0.3,
            distance: 15,
            castShadow: true
        }
    },
    
    // Object placement using local coordinates (0,0,0 = room center)
    // FIXED: Proper positioning and scaling for visibility with corrected voxel sizes
    objects: [
        // Central chest
        {
            type: "treasure_chest",
            position: { x: 0, y: 0, z: 0 }, // Center of room
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.6, y: 0.6, z: 0.6 }, // Reduced scale to match wall proportions
            userData: {
                chestId: "ominous_treasure_chest",
                chestType: "event_chest",
                isEventChest: true,
                isInteractable: true,
                interaction: {
                    type: "ominous_chest",
                    id: "ominous_treasure_chest"
                }
            }
        },
        // Egyptian treasure vases (detailed models with proper positioning)
        {
            type: "egyptian_vase",
            position: { x: -3, y: 0, z: -3 }, // Corner positioning for room layout
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 } // Normal scale for detailed model
        },
        {
            type: "egyptian_vase",
            position: { x: 3, y: 0, z: -3 }, // Corner positioning for room layout
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 }
        },
        {
            type: "egyptian_vase",
            position: { x: -3, y: 0, z: 3 }, // Corner positioning for room layout
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 }
        },
        {
            type: "egyptian_vase",
            position: { x: 3, y: 0, z: 3 }, // Corner positioning for room layout
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 }
        },
        // Golden pillars (detailed models with proper positioning)
        {
            type: "golden_pillar",
            position: { x: -4, y: 0, z: 0 }, // Side positioning for room layout
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 } // Normal scale for detailed model
        },
        {
            type: "golden_pillar",
            position: { x: 4, y: 0, z: 0 }, // Side positioning for room layout
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 } // Normal scale for detailed model
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: OminousTreasureRoom
};
