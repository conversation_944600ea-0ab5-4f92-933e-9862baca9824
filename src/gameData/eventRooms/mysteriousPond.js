import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { animateFishingRodReelIn } from '../../generators/prefabs/improvedFishingRodObject.js';
import { animatePondWater } from '../../generators/prefabs/glowingPondObject.js';
import { createFloorMistPlanes } from '../../effects/FloorMistPlane.js';
import { createUnrealFog } from '../../effects/VolumetricFogManager.js';

/**
 * Mysterious Pond Event Room
 *
 * A glowing pond with an ancient fishing rod.
 * When the player interacts with the fishing rod, they can choose to pull it out,
 * which spawns an enemy and eventually rewards them with a chest.
 */

/**
 * Mysterious Pond Room Handler
 */
export class MysteriousPondRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        console.log(`[MysteriousPondRoom] 🏗️ Constructor called for room ${roomId}`);
        
        this.state = {
            ...this.state,
            fogSystem: null,
            fogAdded: false,
            triggered: false,
            updateCount: 0
        };

        // --- MUSIC INTEGRATION: Music is now handled by universal system in DungeonHandler ---
        // Individual room music will be started automatically when room is entered

        // NOTE: Effect initialization moved to initialize() method to prevent race conditions
    }

    /**
     * Initialize room effects when player enters the room
     * This method is called when the room becomes active
     */
    initialize() {
        console.log(`[MysteriousPondRoom] 🌊 Initializing Mysterious Pond Room effects`);

        // Spawn fireflies immediately when room is created
        this.spawnInitialFireflies();

        // Replace previous failsafe with an interval that waits until the roomGroup exists
        this.state.fogInitInterval = setInterval(() => {
            if (this.state.fogAdded) {
                clearInterval(this.state.fogInitInterval);
                this.state.fogInitInterval = null;
                return;
            }
            if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
                console.warn('[MysteriousPondRoom] Delayed initFog trigger (room group now available)');
                this.initFog();
                this.state.fogAdded = true;
                clearInterval(this.state.fogInitInterval);
                this.state.fogInitInterval = null;
            }
        }, 200); // check every 200 ms
    }

    /**
     * Clean up room resources and effects
     */
    cleanup() {
        super.cleanup(); // Call the base class method
        console.log(`[MysteriousPondRoom] Cleaning up effects for room ${this.roomId}`);

        // 1. Clear the pending timer to prevent it from firing late.
        if (this.state.fogInitInterval) {
            clearInterval(this.state.fogInitInterval);
            this.state.fogInitInterval = null;
        }

        // 2. If the fog system was created, remove it.
        if (this.state.fogSystem && this.state.fogSystem.dispose) {
            this.state.fogSystem.dispose();
            this.state.fogSystem = null;
        }

        // 3. Remove any other added objects, like mist planes.
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            const toRemove = this.dungeonHandler.currentRoomGroup.children.filter(
                child => child.userData.isFloorMist // Assuming you add this identifier
            );
            toRemove.forEach(child => {
                this.dungeonHandler.currentRoomGroup.remove(child);
                if (child.geometry) child.geometry.dispose();
                if (child.material) child.material.dispose();
            });
        }

        this.state.fogAdded = false;
    }

    /**
     * Spawn 3 fireflies immediately when entering the pond room
     */
    spawnInitialFireflies() {
        console.log(`[MysteriousPondRoom] ✨ SPAWNING 3 INITIAL FIREFLIES UPON ROOM ENTRY!`);

        // Get room center position for accurate firefly spawning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        console.log(`[MysteriousPondRoom] Room center position: (${roomCenterX}, ${roomCenterZ})`);

        // Spawn 3 fireflies in a triangle formation around the pond
        const enemySpawns = [
            {
                type: "firefly",
                position: {
                    x: roomCenterX + 2.0, // Right side of pond
                    y: 2.0, // Hovering above ground
                    z: roomCenterZ + 0.0  // Pond center Z + room offset
                },
                spawnDelay: 0.2 // Quick spawn
            },
            {
                type: "firefly",
                position: {
                    x: roomCenterX - 1.0, // Left-back of pond
                    y: 2.5, // Slightly higher
                    z: roomCenterZ + 1.5  // Back of pond
                },
                spawnDelay: 0.4 // Staggered spawn
            },
            {
                type: "firefly",
                position: {
                    x: roomCenterX - 1.0, // Left-front of pond
                    y: 1.8, // Lower hover
                    z: roomCenterZ - 1.5  // Front of pond
                },
                spawnDelay: 0.6 // Last to spawn
            }
        ];

        console.log(`[MysteriousPondRoom] Initial firefly spawn data:`, enemySpawns);

        try {
            // Spawn fireflies WITHOUT hiding doors (custom behavior for pond room)
            // We don't use this.spawnEnemies() because it automatically hides doors
            // Instead, spawn each firefly individually without door management
            enemySpawns.forEach((enemySpawn, index) => {
                setTimeout(() => {
                    this.spawnEnemy(enemySpawn);
                }, enemySpawn.spawnDelay * 1000);
            });
            
            this.state.enemiesSpawned = true;
            console.log(`[MysteriousPondRoom] ✅ Successfully initiated firefly spawning (doors remain visible)`);
        } catch (error) {
            console.error(`[MysteriousPondRoom] ❌ Error spawning initial fireflies:`, error);
        }
    }

    /**
     * Update method called every frame for animations
     */
    update(deltaTime) {
        // Debug logging for first few updates
        if (!this.state.updateCount) this.state.updateCount = 0;
        this.state.updateCount++;

        if (this.state.updateCount <= 5) {
            console.log(`[MysteriousPondRoom] Update #${this.state.updateCount} - deltaTime: ${deltaTime}`);
        }

        // Initialize fog on first update when room is ready
        if (!this.state.fogAdded && this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            console.log(`[MysteriousPondRoom] 🌫️ Room ready - initializing fog now`);
            this.initFog();
            this.state.fogAdded = true;
        }

        // Update fog animation
        if (this.state.fogSystem) {
            this.state.fogSystem.update(deltaTime);
        }

        // Animate pond water
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            this.dungeonHandler.currentRoomGroup.traverse(child => {
                if (child.name === 'glowing_pond' && child.userData?.isAnimated) {
                    animatePondWater(child, deltaTime);
                }
            });
        }
    }

    initFog() {
        try {
            if (!this.dungeonHandler || !this.dungeonHandler.currentRoomGroup) {
                console.warn(`[MysteriousPondRoom] Cannot add fog - room group not available`);
                return;
            }

            // Log room group details
            const roomGroup = this.dungeonHandler.currentRoomGroup;
            console.log(`[MysteriousPondRoom] Initializing uniform volumetric fog with no water exclusion`);

            // Create fog system with uniform distribution over entire room including water
            this.state.fogSystem = createUnrealFog(
                this.dungeonHandler.scene,
                roomGroup,
                {
                    preset: 'pondMist',
                    // Natural white-gray fog like real mist
                    color: 0xC4C4C4, // Natural white-gray mist
                    density: 0.014,  // Transparent for better visibility
                    godrayIntensity: 0.45, // Enhanced godrays for mystical effect
                    windSpeed: 0.4,  // Gentle breeze for pond atmosphere
                    turbulence: 0.6, // More organic movement
                    organicDistribution: true, // Use organic fog patches
                    pondAware: false, // Disable pond exclusion - fog covers everything
                    quality: 'high'  // High quality for best visuals
                }
            );

            if (!this.state.fogSystem) {
                console.error(`[MysteriousPondRoom] Failed to create fog system!`);
                return;
            }

            console.log(`[MysteriousPondRoom] ✅ Uniform fog system created with no water exclusion`);

        } catch (error) {
            console.error(`[MysteriousPondRoom] ❌ Error creating fog:`, error);
        }
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[MysteriousPondRoom] 🎯 Handling interaction with ${objectId}`);
        console.log(`[MysteriousPondRoom] Current state before interaction:`, this.state);

        // Check for fishing rod interaction (multiple possible IDs)
        if (objectId === 'mysterious_fishing_rod' ||
            objectId === 'improved_fishing_rod' ||
            objectId.includes('fishing_rod')) {
            await this.handleFishingRodInteraction();
        } else {
            console.warn(`[MysteriousPondRoom] Unknown object interaction: ${objectId}`);
        }
    }

    /**
     * Handle custom interactions specific to Mysterious Pond Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[MysteriousPondRoom] 🎯 Handling custom interaction:`, interaction);
        console.log(`[MysteriousPondRoom] Current state before interaction:`, this.state);

        switch (interaction.type) {
            case 'fishing_rod':
            case 'mysterious_fishing':
                await this.handleFishingRodInteraction();
                break;
            case 'pond_interaction':
                await this.handlePondInteraction();
                break;
            default:
                console.warn(`[MysteriousPondRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle pond-specific interactions
     */
    async handlePondInteraction() {
        console.log(`[MysteriousPondRoom] 🌊 Pond interaction - examining the mysterious waters...`);
        
        const dialogueData = {
            lines: [
                "The pond's water glows with an ethereal blue light...",
                "Ancient runes are carved around its perimeter.",
                "You sense powerful magic emanating from its depths."
            ],
            options: [
                { text: "Touch the water", value: "touch_water" },
                { text: "Examine the runes", value: "examine_runes" },
                { text: "Step back", value: "step_back" }
            ]
        };

        const choice = await this.showDialogue(dialogueData);
        
        if (choice === "touch_water") {
            console.log(`[MysteriousPondRoom] Player touched the water`);
            // Could trigger special effects or spawn something
        } else if (choice === "examine_runes") {
            console.log(`[MysteriousPondRoom] Player examined the runes`);
            // Could reveal lore or hints
        }
    }

    /**
     * Handle fishing rod interaction with dialogue
     */
    async handleFishingRodInteraction() {
        console.log(`[MysteriousPondRoom] 🎣 Fishing rod interaction started`);
        console.log(`[MysteriousPondRoom] State triggered before check:`, this.state.triggered);

        if (this.state.triggered) {
            console.log(`[MysteriousPondRoom] ⚠️ Fishing rod already used, skipping interaction`);
            return;
        }

        const dialogueData = {
            lines: [
                "An ancient fishing rod...",
                "Its line is already cast into the sacred waters.",
                "Should I reel it in?"
            ],
            options: [
                { text: "Yes, reel it in", value: "yes" },
                { text: "No, leave it be", value: "no" }
            ]
        };

        const choice = await this.showDialogue(dialogueData);
        console.log(`[MysteriousPondRoom] Player choice:`, choice);

        if (choice === 'yes') {
            console.log(`[MysteriousPondRoom] ✅ Setting triggered state to true for Yes choice`);
            this.state.triggered = true;
            await this.handleYesChoice();
        } else if (choice === 'no') {
            console.log(`[MysteriousPondRoom] Player chose No - keeping interaction available`);
            await this.handleNoChoice();
        } else {
            console.log(`[MysteriousPondRoom] Unknown choice or cancelled dialogue:`, choice);
        }
    }

    /**
     * Handle "Yes" choice - spawn Nairabos enemy first, then animate fishing rod reel-in
     */
    async handleYesChoice() {
        console.log(`[MysteriousPondRoom] Player chose to reel in the fishing rod from the sacred waters`);

        // STEP 1: Break fishing rod into debris and start pond draining animation immediately
        console.log(`[MysteriousPondRoom] 🎣 BREAKING FISHING ROD INTO DEBRIS - PLAYER CHOSE YES!`);
        this.breakFishingRodIntoDebris();

        console.log(`[MysteriousPondRoom] 🌊 STARTING POND DRAIN ANIMATION - PLAYER CHOSE YES!`);
        this.playPondDrainAnimation();

        // STEP 2: Spawn Nairabos enemy from the pond center after draining
        console.log(`[MysteriousPondRoom] 👹 SPAWNING NAIRABOS ENEMY FROM POND CENTER!`);
        this.spawnNairabosFromPond();

        // Wait a moment for draining to start, then show simple message
        await new Promise(resolve => setTimeout(resolve, 500));

        // Show simple dialogue while pond drains
        try {
            const dialogueData = {
                lines: [
                    "The sacred waters begin to churn violently!",
                    "The pond is draining into the depths!"
                ],
                options: [
                    { text: "Continue", value: "continue" }
                ]
            };

            console.log(`[MysteriousPondRoom] About to show dialogue with data:`, dialogueData);
            await this.showDialogue(dialogueData);
        } catch (error) {
            console.error(`[MysteriousPondRoom] Dialogue error:`, error);
            // Continue without dialogue if it fails
        }

        // STEP 1: Find the fishing rod object in the scene
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        let fishingRodObject = null;

        console.log(`[MysteriousPondRoom] Searching for fishing rod in room group:`, roomGroup);
        console.log(`[MysteriousPondRoom] Room group exists:`, !!roomGroup);
        console.log(`[MysteriousPondRoom] Room group children count:`, roomGroup?.children?.length || 0);

        // Search in room group first
        if (roomGroup) {
            roomGroup.traverse(child => {
                console.log(`[MysteriousPondRoom] Checking child:`, child.name, child.userData);
                if (child.userData?.rodId === "mysterious_fishing_rod" ||
                    child.name === 'improved_fishing_rod' ||
                    child.userData?.objectType === 'improved_fishing_rod') {
                    fishingRodObject = child;
                    console.log(`[MysteriousPondRoom] ✅ Found fishing rod object:`, child.name, child.userData);
                }
            });
        }

        // If not found in room group, search the entire scene
        if (!fishingRodObject && this.dungeonHandler.scene) {
            console.log(`[MysteriousPondRoom] Fishing rod not found in room group, searching entire scene...`);
            this.dungeonHandler.scene.traverse(child => {
                if (child.userData?.rodId === "mysterious_fishing_rod" ||
                    child.name === 'improved_fishing_rod' ||
                    child.userData?.objectType === 'improved_fishing_rod') {
                    fishingRodObject = child;
                    console.log(`[MysteriousPondRoom] ✅ Found fishing rod in scene:`, child.name, child.userData);
                }
            });
        }

        if (!fishingRodObject) {
            console.error(`[MysteriousPondRoom] ❌ Fishing rod object not found in scene or room group!`);
            console.log(`[MysteriousPondRoom] Available objects in room group:`);
            if (roomGroup) {
                roomGroup.traverse(child => {
                    if (child.userData && Object.keys(child.userData).length > 0) {
                        console.log(`  - ${child.name}:`, child.userData);
                    }
                });
            }
            return;
        }

        // STEP 2: Animate fishing rod reel-in with fish attached
        if (fishingRodObject && fishingRodObject.userData.canAnimate) {
            console.log(`[MysteriousPondRoom] Animating fishing rod reel-in with fish attached...`);
            try {
                await animateFishingRodReelIn(fishingRodObject, 2000);
                console.log(`[MysteriousPondRoom] ✅ Fishing rod reel-in animation completed`);
            } catch (error) {
                console.error(`[MysteriousPondRoom] ❌ Error during reel-in animation:`, error);
            }
        } else {
            console.warn(`[MysteriousPondRoom] ⚠️ Skipping reel-in animation - rod not found or not animatable`);
        }

        // STEP 3: Verify firefly enemies spawned and are ready for combat
        setTimeout(() => {
            const currentEnemies = this.dungeonHandler.enemyManager?.enemies || [];
            console.log(`[MysteriousPondRoom] Current enemies after animation:`, currentEnemies.length);
            currentEnemies.forEach((enemy, index) => {
                console.log(`[MysteriousPondRoom] Enemy ${index}:`, enemy.type, enemy.position);
            });
        }, 500);
    }

    /**
     * Handle "No" choice - do nothing, allow retry
     */
    async handleNoChoice() {
        console.log(`[MysteriousPondRoom] Player chose No - resetting for retry`);
        this.state.triggered = false; // Allow retry
        
        // Find and reset the fishing rod object's isUsed flag
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            const roomGroup = this.dungeonHandler.currentRoomGroup;
            roomGroup.traverse((child) => {
                if (child.userData && 
                    (child.userData.objectId === 'mysterious_fishing_rod' ||
                     child.userData.rodId === 'mysterious_fishing_rod' ||
                     child.userData.objectType === 'improved_fishing_rod')) {
                    console.log(`[MysteriousPondRoom] Found fishing rod object, resetting isUsed flag`);
                    child.userData.isUsed = false;
                }
            });
        }
    }

    /**
     * Spawn Nairabos enemy from the pond center after draining
     */
    spawnNairabosFromPond() {
        // Get room center position for accurate enemy spawning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn Nairabos directly at the pond centre
        const nairabosSpawn = {
            type: "nairabos",
            position: {
                x: roomCenterX,
                y: 1.0, // Ground level
                z: roomCenterZ
            },
            spawnDelay: 0 // spawn immediately
        };

        // Hide doors when miniboss spawns (this is when the real challenge begins)
        this.hideEventRoomDoors();
        
        // Spawn miniboss using individual spawn to avoid double door hiding
        this.spawnEnemy(nairabosSpawn);

        // After spawn, run the debris‐assembly animation almost immediately
        setTimeout(() => {
            this.assembleNairabosFromShadows();
        }, 50);
    }

    /**
     * Animate Nairabos assembling from shadowy debris parts that rise from the depths.
     */
    assembleNairabosFromShadows() {
        // Locate the spawned Nairabos instance
        let nairabos = null;
        if (this.dungeonHandler && this.dungeonHandler.activeEnemies) {
            nairabos = this.dungeonHandler.activeEnemies.find(e =>
                e.userData?.enemyType === 'nairabos' ||
                e.userData?.type === 'nairabos' ||
                (e.name && e.name.toLowerCase().includes('nairabos'))
            );
        }

        // If not yet available, retry shortly
        if (!nairabos) {
            setTimeout(() => this.assembleNairabosFromShadows(), 100);
            return;
        }

        // Store Nairabos unique id for later defeat detection
        if (!this.state.nairabosId && nairabos.userData?.id) {
            this.state.nairabosId = nairabos.userData.id;
            console.log(`[MysteriousPondRoom] ✅ Stored Nairabos ID for defeat tracking: ${this.state.nairabosId}`);
        } else {
            console.log(`[MysteriousPondRoom] ⚠️ Nairabos ID not stored - already set: ${this.state.nairabosId}, userData.id: ${nairabos.userData?.id}`);
        }

        // Hide until we place parts below ground
        nairabos.visible = false;

        // Ensure he starts at pond centre (no lateral movement during animation)
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        if (currentRoomGroup) {
            nairabos.position.set(
                currentRoomGroup.position.x,
                0, // ground level for group; parts handle emergence depth
                currentRoomGroup.position.z
            );
        }

        const visualGroup = nairabos.getObjectByName('visualModel');
        if (!visualGroup) return;

        const partNames = ['body', 'head', 'leftArm', 'rightArm', 'leftLeg', 'rightLeg'];
        const originalPositions = {};
        const startPositions = {};

        const startY = -3.0; // below ground
        const horizontalSpread = 2.0; // random horizontal offset for debris scatter

        // Record original positions and set start positions
        partNames.forEach(name => {
            const part = visualGroup.getObjectByName(name);
            if (!part) return;

            originalPositions[name] = part.position.clone();

            // Randomised start offset for more organic look
            const offsetX = (Math.random() * horizontalSpread * 2) - horizontalSpread;
            const offsetZ = (Math.random() * horizontalSpread * 2) - horizontalSpread;
            startPositions[name] = new THREE.Vector3(
                part.position.x + offsetX,
                startY,
                part.position.z + offsetZ
            );

            // Move part to start position immediately
            part.position.copy(startPositions[name]);
        });

        // Show now that parts are hidden below ground
        nairabos.visible = true;

        // Animation loop variables
        const duration = 3000; // ms
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const t = Math.min(elapsed / duration, 1);
            // Smooth cubic ease-out
            const ease = 1 - Math.pow(1 - t, 3);

            partNames.forEach(name => {
                const part = visualGroup.getObjectByName(name);
                if (!part) return;

                const from = startPositions[name];
                const to = originalPositions[name];
                part.position.set(
                    from.x + (to.x - from.x) * ease,
                    from.y + (to.y - from.y) * ease,
                    from.z + (to.z - from.z) * ease
                );
            });

            // Rotate the entire creature while assembling for dramatic effect
            nairabos.rotation.y = ease * Math.PI * 4; // two full spins

            if (t < 1) {
                requestAnimationFrame(animate);
            } else {
                // Ensure exact final positions and store base Y for future logic
                partNames.forEach(name => {
                    const part = visualGroup.getObjectByName(name);
                    if (part && originalPositions[name]) {
                        part.position.copy(originalPositions[name]);
                    }
                });

                if (nairabos.userData) {
                    nairabos.userData.basePositionY = nairabos.position.y;
                }
            }
        };

        animate();
    }

    /**
     * Handle enemy defeat - spawn chest and close pond
     * IMPORTANT: This completely overrides BaseEventRoom behavior for custom door management
     */
    handleEnemyDefeat(enemyId) {
        console.log(`[MysteriousPondRoom] Enemy ${enemyId} defeated`);
        console.log(`[MysteriousPondRoom] Current nairabosId: ${this.state.nairabosId}`);
        console.log(`[MysteriousPondRoom] Enemy ID type: ${typeof enemyId}, Nairabos ID type: ${typeof this.state.nairabosId}`);

        // React only to Nairabos' defeat - fireflies are ignored for room completion
        // If Nairabos hasn't spawned yet OR the defeated enemy is not Nairabos, ignore it
        if (!this.state.nairabosId || enemyId !== this.state.nairabosId) {
            console.log(`[MysteriousPondRoom] Defeated enemy ${enemyId} is not Nairabos (nairabosId: ${this.state.nairabosId}) – ignoring (no door changes)`);
            
            // CRITICAL: Don't call super.handleEnemyDefeat() for fireflies
            // This prevents BaseEventRoom from showing doors when last firefly dies
            return;
        }

        // Only Nairabos defeat triggers room completion
        if (this.state.enemiesDefeated) return;
        this.state.enemiesDefeated = true;

        console.log('[MysteriousPondRoom] Nairabos defeated - completing room');

        // Close pond & spawn chest
        setTimeout(() => this.playPondClosingAnimation(), 500);
        setTimeout(async () => {
            await this.spawnChestAtPondCenter();
            // Reveal doors after chest emerges (only for miniboss defeat)
            this.showEventRoomDoors();
            // Call base logic only for miniboss defeat to update event-room system
            super.handleEnemyDefeat(enemyId);
        }, 1500);
    }

    /**
     * Spawn treasure chest at pond center with rising animation and close black hole
     */
    async spawnChestAtPondCenter() {
        console.log(`[MysteriousPondRoom] 📦 Spawning treasure chest at pond center`);

        // Start closing hole concurrently (slow circular animation)
        this.closeBlackHoleAnimation();

        // Get room center position for accurate chest positioning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn chest at pond center (same position as pond object)
        const chestConfig = {
            position: {
                x: roomCenterX + 0.0, // Pond center X (local coordinate 0)
                y: 0.0, // Ground level (will rise up in animation)
                z: roomCenterZ + 0.0  // Pond center Z (local coordinate 0)
            },
            chestType: "event_chest"
        };

        console.log(`[MysteriousPondRoom] Chest spawn position:`, chestConfig.position);

        // Spawn the chest with custom animation
        await this.spawnChestWithRisingAnimation(chestConfig);
    }

    /**
     * Spawn chest with rising animation from black hole
     */
    async spawnChestWithRisingAnimation(chestConfig) {
        try {
            console.log(`[MysteriousPondRoom] Creating chest with rising animation...`);
            
            // Use the proper chest creation method from BaseEventRoom
            const { createTreasureChestObject } = await import('../../generators/prefabs/treasureChestObject.js');
            
            const chestId = `pond_chest_${Date.now()}`;
            
            const chest = await createTreasureChestObject({
                userData: {
                    chestId: chestId,
                    chestType: chestConfig.chestType || 'event_chest',
                    isEventChest: true,
                    eventRoomId: this.roomId,
                    roomId: this.roomId,
                    isInteractable: true,
                    containsItem: true
                }
            });

            if (chest) {
                // Position chest below ground initially
                chest.position.set(
                    chestConfig.position.x,
                    chestConfig.position.y - 2.0, // Start below ground
                    chestConfig.position.z
                );

                // Add to scene immediately
                this.dungeonHandler.scene.add(chest);

                // Animate chest rising from black hole
                const duration = 2500; // slower, matches chest rise
                const startTime = Date.now();
                const startY = chest.position.y;
                const targetY = chestConfig.position.y + 1.0; // Rise above ground

                const animate = () => {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1.0);

                    // Smooth easing for dramatic effect
                    const easeProgress = 1 - Math.pow(1 - progress, 3);
                    chest.position.y = startY + (targetY - startY) * easeProgress;

                    // Add rotation for dramatic effect
                    chest.rotation.y = progress * Math.PI * 2;

                    if (progress < 1.0) {
                        requestAnimationFrame(animate);
                    } else {
                        console.log(`[MysteriousPondRoom] ✅ Chest rising animation complete`);
                    }
                };

                animate();

                // Add to collision objects
                if (this.dungeonHandler.collisionObjects) {
                    this.dungeonHandler.collisionObjects.push(chest);
                }

                this.state.chestsSpawned.add(chestId);
                console.log(`[MysteriousPondRoom] ✅ Chest spawned at pond center with rising animation`);
            } else {
                console.error(`[MysteriousPondRoom] Failed to create chest object`);
            }
        } catch (error) {
            console.error(`[MysteriousPondRoom] Failed to spawn chest at pond center:`, error);
        }
    }

    /**
     * Close black hole animation - restore normal floor
     */
    closeBlackHoleAnimation() {
        console.log(`[MysteriousPondRoom] 🕳️ Closing black hole and restoring normal floor`);

        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.warn(`[MysteriousPondRoom] No room group found for black hole closing`);
            return;
        }

        // Find original floor objects (if any) to restore their material
        const floorObjects = [];
        roomGroup.traverse(child => {
            if (child.userData?.isFloor && child.isMesh) {
                floorObjects.push(child);
            }
        });

        // --- ALWAYS create an animated floor patch disc to seal the stencil hole ---
        const baseMat = floorObjects[0]?.material ? floorObjects[0].material.clone() : new THREE.MeshStandardMaterial({ color: 0x4a4a4a});
        baseMat.side = THREE.DoubleSide;
        const discRadius = 3.2; // FIXED: Increased from 2.6 to 3.2 to match floor hole radius (3.0) + 0.2 overlap
        const discGeo = new THREE.CircleGeometry(discRadius, 48);
        const disc = new THREE.Mesh(discGeo, baseMat);
        disc.rotation.x = -Math.PI/2;
        disc.position.set(0, 0.02, 0);
        disc.scale.set(0,0,0);
        
        // CRITICAL: Mark as floor for collision detection so player can walk on it
        disc.userData.isFloor = true;
        disc.userData.floorType = 'pond_hole_patch';
        disc.userData.isWalkable = true;
        disc.receiveShadow = true;
        disc.name = 'pondHolePatch';
        
        roomGroup.add(disc);

        const durationDisc = 2500;
        const startDisc = Date.now();
        const animatePatch = () => {
            const elapsed = Date.now() - startDisc;
            const t = Math.min(elapsed/durationDisc,1);
            const ease = 1 - Math.pow(1-t,3);
            disc.scale.setScalar(ease);
            if(t<1){
                requestAnimationFrame(animatePatch);
            } else {
                disc.scale.setScalar(1);
                
                // CRITICAL: Add the completed floor patch to collision objects for player walking
                if (this.dungeonHandler.collisionObjects && !this.dungeonHandler.collisionObjects.includes(disc)) {
                    this.dungeonHandler.collisionObjects.push(disc);
                }
                
                console.log('[MysteriousPondRoom] ✅ Floor patch disc fully grown - player can now walk on it');
            }
        };
        animatePatch();

        // --- ALSO restore original floor objects if they exist ---
        if (floorObjects.length > 0) {
            const duration = 2500; // Slower, matches chest rise
            const startTime = Date.now();
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1.0);

                // Smooth closing animation
                const easeProgress = Math.pow(progress, 2);

                // Restore floor opacity and position
                floorObjects.forEach(floorObj => {
                    if (floorObj.material) {
                        // Ensure floor is visible and solid
                        floorObj.material.transparent = false;
                        floorObj.material.opacity = 1.0;

                        // Restore original color if it was darkened
                        if (floorObj.userData.originalColor) {
                            floorObj.material.color.copy(floorObj.userData.originalColor);
                        }
                    }
                });

                if (progress < 1.0) {
                    requestAnimationFrame(animate);
                } else {
                    console.log(`[MysteriousPondRoom] ✅ Black hole closing animation complete`);
                }
            };

            animate();
        }
    }

    /**
     * Play pond closing animation
     */
    playPondClosingAnimation() {
        console.log(`[MysteriousPondRoom] Playing pond closing animation`);

        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) return;

        let pondObject = null;
        roomGroup.traverse(child => {
            if (child.userData?.pondId === "mysterious_pond_water" || child.name === 'glowing_pond') {
                pondObject = child;
            }
        });

        if (pondObject) {
            const duration = 2000;
            const startTime = Date.now();
            const startY = pondObject.position.y;
            const targetY = startY - 2.0;

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1.0);

                const easeProgress = Math.pow(progress, 2);
                pondObject.position.y = startY + (targetY - startY) * easeProgress;

                pondObject.traverse(child => {
                    if (child.isMesh && child.material) {
                        if (!child.material.transparent) {
                            child.material.transparent = true;
                        }
                        child.material.opacity = 1.0 - progress;
                    }
                });

                if (progress < 1.0) {
                    requestAnimationFrame(animate);
                } else {
                    roomGroup.remove(pondObject);
                    console.log(`[MysteriousPondRoom] ✅ Pond closing animation complete`);
                }
            };

            animate();
        }
    }

    /**
     * Play pond draining animation - water drains down like a real pond
     */
    playPondDrainAnimation() {
        console.log(`[MysteriousPondRoom] 🌊 STARTING POND DRAINING ANIMATION!`);

        // Try multiple ways to find the room group
        let roomGroup = this.dungeonHandler.currentRoomGroup;

        if (!roomGroup) {
            console.log(`[MysteriousPondRoom] currentRoomGroup not available, searching scene...`);
            // Search the entire scene for the pond
            if (this.dungeonHandler.scene) {
                console.log(`[MysteriousPondRoom] 🔍 Searching entire scene for pond object...`);
                let pondObject = null;
                this.dungeonHandler.scene.traverse(child => {
                    if (child.userData?.pondId === "mysterious_pond_water" ||
                        child.userData?.objectId === "mysterious_pond_water" ||
                        child.name === 'glowing_pond') {
                        pondObject = child;
                        console.log(`[MysteriousPondRoom] ✅ Found pond object in scene:`, child.name);
                    }
                });

                if (pondObject) {
                    this.animatePondDraining(pondObject);
                    return;
                }
            }

            console.error(`[MysteriousPondRoom] ❌ No scene or room group found for draining animation`);
            return;
        }

        console.log(`[MysteriousPondRoom] 🔍 Searching for pond object in room group...`);
        let pondObject = null;
        roomGroup.traverse(child => {
            console.log(`[MysteriousPondRoom] Checking child: ${child.name}, userData:`, child.userData);
            if (child.userData?.pondId === "mysterious_pond_water" ||
                child.userData?.objectId === "mysterious_pond_water" ||
                child.name === 'glowing_pond') {
                pondObject = child;
                console.log(`[MysteriousPondRoom] ✅ Found pond object:`, child.name);
            }
        });

        if (!pondObject) {
            console.error(`[MysteriousPondRoom] ❌ No pond object found for draining animation`);
            console.log(`[MysteriousPondRoom] Available objects in room:`);
            roomGroup.traverse(child => {
                if (child.userData && Object.keys(child.userData).length > 0) {
                    console.log(`  - ${child.name}:`, child.userData);
                }
            });
            return;
        }

        this.animatePondDraining(pondObject);
    }

    /**
     * Break fishing rod into debris using the existing debris system
     */
    breakFishingRodIntoDebris() {
        console.log(`[MysteriousPondRoom] 🎣 Breaking fishing rod into debris pieces`);

        // Find the fishing rod object
        let fishingRodObject = null;

        // Try multiple ways to find the fishing rod
        if (this.dungeonHandler.currentRoomGroup) {
            this.dungeonHandler.currentRoomGroup.traverse(child => {
                if (child.userData?.rodId === "mysterious_fishing_rod" ||
                    child.userData?.objectId === "mysterious_fishing_rod" ||
                    child.userData?.objectType === "improved_fishing_rod" ||
                    child.name === 'improved_fishing_rod') {
                    fishingRodObject = child;
                    console.log(`[MysteriousPondRoom] ✅ Found fishing rod object:`, child.name);
                }
            });
        }

        // Fallback: search entire scene
        if (!fishingRodObject && this.dungeonHandler.scene) {
            console.log(`[MysteriousPondRoom] 🔍 Searching entire scene for fishing rod...`);
            this.dungeonHandler.scene.traverse(child => {
                if (child.userData?.rodId === "mysterious_fishing_rod" ||
                    child.userData?.objectId === "mysterious_fishing_rod" ||
                    child.userData?.objectType === "improved_fishing_rod" ||
                    child.name === 'improved_fishing_rod') {
                    fishingRodObject = child;
                    console.log(`[MysteriousPondRoom] ✅ Found fishing rod object in scene:`, child.name);
                }
            });
        }

        if (!fishingRodObject) {
            console.error(`[MysteriousPondRoom] ❌ No fishing rod object found for debris animation`);
            return;
        }

        // Mark the fishing rod as destructible so the debris system can handle it
        if (!fishingRodObject.userData) {
            fishingRodObject.userData = {};
        }

        // Set up for debris system
        fishingRodObject.userData.isDestructible = true;
        fishingRodObject.userData.health = 0; // Already "destroyed"

        console.log(`[MysteriousPondRoom] 🎯 Triggering debris system for fishing rod`);

        // Use the existing debris system through the DungeonHandler
        if (this.dungeonHandler._handleObjectDestruction) {
            // Create impact point at rod position
            const impactPoint = fishingRodObject.position.clone();
            // Create velocity as if hit from player direction
            const playerPos = this.dungeonHandler.player?.position || new THREE.Vector3(0, 0, 0);
            const impactVelocity = impactPoint.clone().sub(playerPos).normalize().multiplyScalar(2.0);

            // Trigger the debris system
            this.dungeonHandler._handleObjectDestruction(fishingRodObject, impactPoint, impactVelocity);

            console.log(`[MysteriousPondRoom] ✅ Fishing rod broken into debris!`);
        } else {
            console.error(`[MysteriousPondRoom] ❌ Debris system not available`);
        }
    }

    /**
     * Animate the actual pond draining effect
     */
    animatePondDraining(pondObject) {

        console.log(`[MysteriousPondRoom] Found pond object for draining:`, pondObject.name);

        // Get all water voxels for individual animation
        const waterVoxels = [];
        pondObject.traverse(child => {
            if (child.userData?.isWaterVoxel === true) {
                waterVoxels.push(child);
            }
        });

        console.log(`[MysteriousPondRoom] Found ${waterVoxels.length} water voxels to drain`);

        if (waterVoxels.length === 0) {
            console.warn(`[MysteriousPondRoom] No water voxels found for draining animation`);
            console.log(`[MysteriousPondRoom] Pond object children:`, pondObject.children.length);
            pondObject.traverse(child => {
                console.log(`  - Child: ${child.name}, userData:`, child.userData);
            });
            return;
        }

        // Disable normal water animation during draining
        pondObject.userData.isAnimated = false;

        const duration = 3000; // 3 seconds for dramatic draining effect
        const startTime = Date.now();

        // Store original positions and create swirl parameters
        waterVoxels.forEach((voxel, index) => {
            if (!voxel.userData.originalDrainPosition) {
                voxel.userData.originalDrainPosition = voxel.position.clone();
            }

            // Calculate distance from center for spiral drain effect
            const distanceFromCenter = Math.sqrt(
                voxel.userData.originalDrainPosition.x * voxel.userData.originalDrainPosition.x +
                voxel.userData.originalDrainPosition.z * voxel.userData.originalDrainPosition.z
            );

            // Outer voxels start swirling first, inner ones follow (like a real whirlpool)
            voxel.userData.drainDelay = distanceFromCenter * 0.1; // 0-0.4 second delay based on distance
            voxel.userData.drainStartTime = startTime + (voxel.userData.drainDelay * 1000);

            // Store swirl parameters
            voxel.userData.originalDistance = distanceFromCenter;
            voxel.userData.swirlSpeed = 2.0 + Math.random() * 1.0; // Random swirl speed for variety
            voxel.userData.originalAngle = Math.atan2(voxel.userData.originalDrainPosition.z, voxel.userData.originalDrainPosition.x);
        });

        const animate = () => {
            const currentTime = Date.now();
            const totalElapsed = currentTime - startTime;
            const totalProgress = Math.min(totalElapsed / duration, 1.0);

            let allVoxelsDrained = true;

            waterVoxels.forEach((voxel, index) => {
                const voxelStartTime = voxel.userData.drainStartTime;

                if (currentTime < voxelStartTime) {
                    allVoxelsDrained = false;
                    return; // This voxel hasn't started draining yet
                }

                const voxelElapsed = currentTime - voxelStartTime;
                const voxelDuration = duration * 0.8; // Individual voxel drains in 80% of total time
                const voxelProgress = Math.min(voxelElapsed / voxelDuration, 1.0);

                if (voxelProgress < 1.0) {
                    allVoxelsDrained = false;
                }

                // Create realistic swirling dissolve effect
                const originalPos = voxel.userData.originalDrainPosition;
                const originalDistance = voxel.userData.originalDistance;
                const originalAngle = voxel.userData.originalAngle;
                const swirlSpeed = voxel.userData.swirlSpeed;

                // Three phases: 1) Swirl inward, 2) Break apart, 3) Dissolve
                const phase1Duration = 0.4; // 40% of time - swirling inward
                const phase2Duration = 0.3; // 30% of time - breaking apart
                const phase3Duration = 0.3; // 30% of time - dissolving

                let newX, newY, newZ, scale, opacity;

                if (voxelProgress < phase1Duration) {
                    // PHASE 1: Swirl inward toward center (no Y change)
                    const phase1Progress = voxelProgress / phase1Duration;
                    const easeInProgress = Math.pow(phase1Progress, 2); // Accelerating inward

                    // Calculate spiral motion
                    const currentAngle = originalAngle + (easeInProgress * swirlSpeed * Math.PI * 2); // Multiple rotations
                    const currentDistance = originalDistance * (1.0 - easeInProgress * 0.7); // Move 70% toward center

                    newX = Math.cos(currentAngle) * currentDistance;
                    newZ = Math.sin(currentAngle) * currentDistance;
                    newY = originalPos.y; // Keep same Y level

                    scale = 1.0; // Keep full size during swirl
                    opacity = 1.0; // Keep fully visible

                } else if (voxelProgress < phase1Duration + phase2Duration) {
                    // PHASE 2: Break apart into individual voxels with chaotic motion
                    const phase2Progress = (voxelProgress - phase1Duration) / phase2Duration;
                    const chaosProgress = Math.pow(phase2Progress, 1.5);

                    // Final swirl position from phase 1
                    const finalAngle = originalAngle + (swirlSpeed * Math.PI * 2);
                    const finalDistance = originalDistance * 0.3;
                    const centerX = Math.cos(finalAngle) * finalDistance;
                    const centerZ = Math.sin(finalAngle) * finalDistance;

                    // Add chaotic breakup motion
                    const chaosAmplitude = 0.3 * chaosProgress;
                    const chaosX = (Math.random() - 0.5) * chaosAmplitude;
                    const chaosZ = (Math.random() - 0.5) * chaosAmplitude;
                    const chaosY = Math.sin(chaosProgress * Math.PI * 3) * 0.1; // Slight bobbing

                    newX = centerX + chaosX;
                    newZ = centerZ + chaosZ;
                    newY = originalPos.y + chaosY;

                    scale = 1.0 - chaosProgress * 0.3; // Slight shrinking
                    opacity = 1.0 - chaosProgress * 0.2; // Slight fading

                } else {
                    // PHASE 3: Dissolve into nothingness
                    const phase3Progress = (voxelProgress - phase1Duration - phase2Duration) / phase3Duration;
                    const dissolveProgress = Math.pow(phase3Progress, 2); // Accelerating dissolve

                    // Keep position from end of phase 2 but add final swirl
                    const finalAngle = originalAngle + (swirlSpeed * Math.PI * 2);
                    const finalDistance = originalDistance * 0.3;
                    const centerX = Math.cos(finalAngle) * finalDistance;
                    const centerZ = Math.sin(finalAngle) * finalDistance;

                    // Final spiral into center point
                    const finalSwirlAngle = finalAngle + (dissolveProgress * Math.PI * 2);
                    const finalSwirlDistance = finalDistance * (1.0 - dissolveProgress);

                    newX = Math.cos(finalSwirlAngle) * finalSwirlDistance;
                    newZ = Math.sin(finalSwirlAngle) * finalSwirlDistance;
                    newY = originalPos.y;

                    scale = Math.max(0.1, 1.0 - dissolveProgress); // Shrink to almost nothing
                    opacity = Math.max(0, 1.0 - dissolveProgress); // Fade to transparent
                }

                // Apply the calculated position and effects
                voxel.position.set(newX, newY, newZ);
                voxel.scale.set(scale, scale, scale);

                // Apply material effects
                if (voxel.material) {
                    if (!voxel.material.transparent) {
                        voxel.material.transparent = true;
                    }
                    voxel.material.opacity = opacity;
                }
            });

            // Animate pond light dimming
            const pondLight = pondObject.userData.pondLight;
            if (pondLight && pondLight.userData) {
                const lightProgress = Math.min(totalProgress * 1.5, 1.0); // Light dims faster
                pondLight.intensity = pondLight.userData.originalIntensity * (1.0 - lightProgress);
            }

            if (!allVoxelsDrained && totalProgress < 1.0) {
                requestAnimationFrame(animate);
            } else {
                // Draining complete - hide remaining water voxels
                waterVoxels.forEach(voxel => {
                    voxel.visible = false;
                });

                console.log(`[MysteriousPondRoom] ✅ Pond draining animation complete`);

                // Mark pond as drained but don't remove it yet (fish still needs to spawn from it)
                pondObject.userData.isDrained = true;
            }
        };

        animate();
    }

    /**
     * Start room corruption animation - slowly corrupt room into shadows over 60 seconds
     */
    startRoomCorruption() {
        const corruptionDuration = 60000; // 60 seconds
        const startTime = Date.now();

        // Get all room elements that can be corrupted
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) return;

        const corruptibleElements = [];
        roomGroup.traverse(child => {
            if (child.isMesh && child.material &&
                (child.userData?.isWall || child.userData?.isFloor)) {
                corruptibleElements.push({
                    mesh: child,
                    originalMaterial: child.material.clone(),
                    originalColor: child.material.color ? child.material.color.clone() : null
                });
            }
        });

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / corruptionDuration, 1.0);

            // Corruption effect: gradually darken and add shadow tint
            const corruptionIntensity = Math.pow(progress, 0.5); // Slower start, faster end

            corruptibleElements.forEach(element => {
                if (element.mesh.material && element.originalColor) {
                    // Create shadow corruption color (pure black)
                    const shadowColor = new THREE.Color(0x000000); // Pure black
                    const currentColor = element.originalColor.clone();

                    // Interpolate between original color and shadow color
                    currentColor.lerp(shadowColor, corruptionIntensity);
                    element.mesh.material.color.copy(currentColor);

                    // Add dramatic darkness by reducing overall brightness
                    element.mesh.material.color.multiplyScalar(1.0 - corruptionIntensity * 0.95);
                }
            });

            if (progress < 1.0) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    /**
     * Add atmospheric ground fog like catacomb rooms
     */
    addGroundFog() {
        console.log(`[MysteriousPondRoom] Adding atmospheric ground fog`);

        if (!this.dungeonHandler || !this.dungeonHandler.currentRoomGroup) {
            console.warn(`[MysteriousPondRoom] Cannot add fog - room group not available`);
            return;
        }

        // Calculate room bounds for the 2x2 room (28x28 units)
        const roomSize = 28; // SQUARE_2X2 size
        const roomBounds = {
            minX: -roomSize / 2,
            maxX: roomSize / 2,
            minZ: -roomSize / 2,
            maxZ: roomSize / 2
        };

        // Create floor mist with mystical blue-green tint for pond room
        const mistPlanes = createFloorMistPlanes(
            roomBounds,
            1.2,    // mistHeight - slightly higher for mystical effect
            4,      // layerCount - more layers for depth
            0.04,   // opacity - slightly more visible than catacombs
            [],     // floorSegments - empty, will use simple bounds
            this.dungeonHandler.scene
        );

        // Tint the mist with a mystical blue-green color
        mistPlanes.traverse(child => {
            if (child.isMesh && child.material) {
                child.material.color.setHex(0x88bbcc); // Mystical blue-green
            }
        });

        // Add fog to the room
        this.dungeonHandler.currentRoomGroup.add(mistPlanes);

        console.log(`[MysteriousPondRoom] ✅ Added mystical ground fog to pond room`);
    }

    /**
     * Start pond event music when entering the room
     * NOTE: This method is kept for backwards compatibility but music is now
     * handled automatically by the universal system in DungeonHandler._handleEventRoomMusic()
     */
    async startPondMusic() {
        console.log(`[MysteriousPondRoom] 🎵 Starting pond event music`);
        
        try {
            if (!this.dungeonHandler?.audioManager) {
                console.warn(`[MysteriousPondRoom] ⚠️ AudioManager not available for pond music`);
                return;
            }

            const audioManager = this.dungeonHandler.audioManager;
            
            // Ensure music system is initialized
            if (!audioManager.musicConductor?.initialized) {
                console.log(`[MysteriousPondRoom] 🎵 Initializing music system...`);
                const initSuccess = await audioManager.initMusicSystem();
                if (!initSuccess) {
                    console.warn(`[MysteriousPondRoom] ⚠️ Failed to initialize music system`);
                    return;
                }
            }

            // Get current area - check multiple sources
            const currentArea = this.dungeonHandler.previousAreaId || 
                               this.dungeonHandler.currentArea?.id || 
                               'catacombs';
            
            console.log(`[MysteriousPondRoom] 🎵 Entering pond event music for area: ${currentArea}`);
            
            // Enter special room music for pond event
            const success = await audioManager.enterSpecialRoomMusic(currentArea, 'pond_event');
            
            if (success) {
                console.log(`[MysteriousPondRoom] ✅ Successfully started pond event music`);
            } else {
                console.warn(`[MysteriousPondRoom] ⚠️ Failed to start pond event music`);
            }
        } catch (error) {
            console.error(`[MysteriousPondRoom] ❌ Error starting pond music:`, error);
        }
    }

    /**
     * Stop pond event music and return to dungeon music when leaving the room
     * NOTE: This method is kept for backwards compatibility but music is now
     * handled automatically by the universal system in DungeonHandler._cleanupEventRoomMusic()
     */
    async stopPondMusic() {
        console.log(`[MysteriousPondRoom] 🎵 Stopping pond event music and returning to dungeon music`);
        
        try {
            if (this.dungeonHandler.audioManager) {
                const currentArea = this.dungeonHandler.previousAreaId || 'catacombs';
                console.log(`[MysteriousPondRoom] Exiting pond event special room music for area: ${currentArea}`);
                
                // Exit special room music (returns to main area music)
                const success = await this.dungeonHandler.audioManager.exitSpecialRoomMusic(currentArea, 'pond_event');
                
                if (success) {
                    console.log(`[MysteriousPondRoom] ✅ Successfully returned to dungeon music`);
                } else {
                    console.warn(`[MysteriousPondRoom] ⚠️ Failed to exit pond event music properly`);
                }
            } else {
                console.warn(`[MysteriousPondRoom] ⚠️ AudioManager not available for music transition`);
            }
        } catch (error) {
            console.error(`[MysteriousPondRoom] ❌ Error stopping pond music:`, error);
        }
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "mysterious_pond",
    name: "Mysterious Pond",
    shape: "SQUARE_2X2", // CHANGED: Larger room (28x28 units) - smaller than boss arena (56x56) but bigger than 1x1 (14x14)
    description: "A glowing pond with an ancient fishing rod",
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "south", // Preferred entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -14 },   // Door on south wall when entered from north
        south: { x: 0, y: 0, z: 14 },    // Door on north wall when entered from south
        east: { x: -14, y: 0, z: 0 },    // Door on west wall when entered from east
        west: { x: 14, y: 0, z: 0 }      // Door on east wall when entered from west
    },
    
    // Custom materials with mystical sacred stone
    materials: {
        walls: "mystical_stone_brick", // Custom mystical stone brick walls with caps
        floors: "mystical_stone_floor", // Custom mystical stone floor
        wallTint: 0x9B7EBD, // Mystical purple-blue tint
        floorTint: 0x8A7CA8 // Darker mystical tint for floor
    },

    // Static brightness override (1-10 scale, overrides health-based brightness)
    staticBrightness: 3, // Dark mystical atmosphere (3/10)

    // Enemy spawning data
    enemies: [],
    
    // Mystical lighting with cyan pond glow
    lighting: {
        ambient: {
            intensity: 0.3, // Slightly brighter ambient
            color: 0x3A3A5A // Mystical purple-gray ambient
        },
        pondCyanGlow: {
            position: { x: 0.0, y: 1.0, z: 0.0 }, // Higher above pond center for maximum visibility
            intensity: 5.0, // Very bright cyan glow - maximum visibility
            color: 0x00FFFF, // Pure cyan color
            distance: 20, // Very large distance for maximum coverage
            decay: 1.0 // Minimal decay for strongest light spread
        },
        mysticalAccent: {
            position: { x: 0, y: 6, z: 0 }, // High above for mystical atmosphere
            intensity: 0.8,
            color: 0x9B7EBD, // Mystical purple accent light
            distance: 15,
            decay: 2.5
        }
    },

    // Object placement
    objects: [
        // Central pond with holy magic pulsating center line
        {
            type: "glowing_pond",
            position: { x: 0, y: -0.5, z: 0 }, // Moved down 0.5 units below ground level
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 15.0, y: 7.5, z: 15.0 }, // Bigger pond to fill the ground hole
            userData: {
                pondId: "mysterious_pond_water",
                objectId: "mysterious_pond_water", // Add objectId for consistency
                isInteractable: false // Pond itself not interactable
            }
        },
        // Improved fishing rod (interactable) - positioned at pond corner, already cast
        {
            type: "improved_fishing_rod",
            position: { x: -6.0, y: 0.0, z: -0.5 }, // MOVED: Repositioned to corner of pond, out of water
            rotation: { x: 0, y: 0, z: 0 }, // Facing toward pond center
            scale: { x: 1.0, y: 1.0, z: 1.0 }, // Normal scale
            userData: {
                rodId: "mysterious_fishing_rod",
                objectId: "mysterious_fishing_rod", // Add objectId for interaction system
                objectType: "improved_fishing_rod", // Add objectType for consistency
                isInteractable: true,
                interactionType: "fishing_rod",
                isCastOut: true, // Rod is already cast into the water
                castIntoWater: true, // Visual state: line extends into pond
                isDestructible: true, // Allow breaking into debris when triggered
                health: 1, // Set health for debris system
                hasCollision: true, // Keep collision
                canAnimate: true, // Enable animation capability
                canBreakIntoDebris: true // Flag for debris breaking
            }
        },
        // Crystal cave formation 1 - Front left corner
        {
            type: "crystal_cave",
            position: { x: -4, y: 0, z: -4 },
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 2.0, y: 2.5, z: 2.0 }, // Bigger and taller
            userData: {
                objectId: "crystal_cave_1",
                isInteractable: false,
                isDecorative: true
            }
        },
        // Crystal cave formation 2 - Front right corner
        {
            type: "crystal_cave",
            position: { x: 4, y: 0, z: -4 },
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.8, y: 2.3, z: 1.8 }, // Bigger and taller
            userData: {
                objectId: "crystal_cave_2",
                isInteractable: false,
                isDecorative: true
            }
        },
        // Crystal cave formation 3 - Back left corner
        {
            type: "crystal_cave",
            position: { x: -4, y: 0, z: 4 },
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 1.9, y: 2.4, z: 1.9 }, // Bigger and taller
            userData: {
                objectId: "crystal_cave_3",
                isInteractable: false,
                isDecorative: true
            }
        },
        // Crystal cave formation 4 - Back right corner
        {
            type: "crystal_cave",
            position: { x: 4, y: 0, z: 4 },
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 2.1, y: 2.6, z: 2.1 }, // Biggest and tallest
            userData: {
                objectId: "crystal_cave_4",
                isInteractable: false,
                isDecorative: true
            }
        },

        // === CAVE ATMOSPHERE ENHANCEMENTS ===

        // Stalagmites rising from floor (changed from stalactites)

        // Additional stalagmites (using existing stalagmite object)
        {
            type: "stalagmite",
            position: { x: -7.0, y: 0, z: 1.0 }, // Specific coordinates
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 1.3, y: 1.8, z: 1.3 },
            userData: {
                objectId: "stalagmite_small_1",
                isDecorative: true
            }
        },

        {
            type: "stalagmite",
            position: { x: 1.0, y: 0, z: 7.0 }, // Specific coordinates
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 0.9, y: 1.2, z: 0.9 },
            userData: {
                objectId: "stalagmite_small_3",
                isDecorative: true
            }
        },

        // === WALL-CONNECTED STALAGMITES (Matching wall colors) ===

        // Wall-connected stalagmite 1 - Left side of north wall
        {
            type: "mystical_stalagmite", // Custom type matching wall materials
            position: { x: -5.5, y: 0, z: -13.2 }, // Connected to north wall
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 1.4, y: 2.2, z: 1.4 }, // Tall wall-connected formation
            userData: {
                objectId: "wall_stalagmite_north_left",
                isDecorative: true,
                wallConnected: true,
                wallMaterial: "mystical_stone_brick",
                wallTint: 0x9B7EBD // Match wall tint
            }
        },

        // Wall-connected stalagmite 2 - Right side of north wall
        {
            type: "mystical_stalagmite", // Custom type matching wall materials
            position: { x: 5.5, y: 0, z: -13.2 }, // Connected to north wall
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 1.4, y: 2.2, z: 1.4 }, // Tall wall-connected formation
            userData: {
                objectId: "wall_stalagmite_north_right",
                isDecorative: true,
                wallConnected: true,
                wallMaterial: "mystical_stone_brick",
                wallTint: 0x9B7EBD // Match wall tint
            }
        },

        // Cave debris and rubble
        {
            type: "cave_debris",
            position: { x: -6.0, y: 0, z: 5.0 }, // Specific coordinates
            rotation: { x: 0, y: Math.PI / 7, z: 0 },
            scale: { x: 1.5, y: 0.8, z: 1.5 },
            userData: {
                objectId: "cave_debris_1",
                isDecorative: true
            }
        },
        {
            type: "cave_debris",
            position: { x: 4.0, y: 0, z: -7.0 }, // Specific coordinates
            rotation: { x: 0, y: -Math.PI / 5, z: 0 },
            scale: { x: 1.2, y: 0.6, z: 1.2 },
            userData: {
                objectId: "cave_debris_2",
                isDecorative: true
            }
        },
        {
            type: "cave_debris",
            position: { x: -8.0, y: 0, z: 3.0 }, // Specific coordinates
            rotation: { x: 0, y: Math.PI / 3, z: 0 },
            scale: { x: 1.3, y: 0.7, z: 1.3 },
            userData: {
                objectId: "cave_debris_3",
                isDecorative: true
            }
        },
        {
            type: "cave_debris",
            position: { x: 7.0, y: 0, z: -5.0 }, // Specific coordinates
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 0.5, z: 1.0 },
            userData: {
                objectId: "cave_debris_4",
                isDecorative: true
            }
        },

        // === ANCIENT STONE PILLARS (Using catacombs destruction mechanics) ===

        // Ancient stone pillar - Southeast corner (matching catacombs system)
        {
            type: "ancient_stone_pillar",
            position: { x: 11.0, y: 0, z: 11.0 }, // Southeast corner
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "ancient_pillar_southeast",
                isDestructible: true,
                destructionEffect: 'collapse',
                health: 1
            }
        },

        // Ancient stone pillar - Southwest corner (matching catacombs system)
        {
            type: "ancient_stone_pillar",
            position: { x: -11.0, y: 0, z: 11.0 }, // Southwest corner
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "ancient_pillar_southwest",
                isDestructible: true,
                destructionEffect: 'collapse',
                health: 1
            }
        },

        // Ancient stone pillar - Northeast corner (matching catacombs system)
        {
            type: "ancient_stone_pillar",
            position: { x: 11.0, y: 0, z: -11.0 }, // Northeast corner
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "ancient_pillar_northeast",
                isDestructible: true,
                destructionEffect: 'collapse',
                health: 1
            }
        },

        // Ancient stone pillar - Northwest corner (matching catacombs system)
        {
            type: "ancient_stone_pillar",
            position: { x: -11.0, y: 0, z: -11.0 }, // Northwest corner
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "ancient_pillar_northwest",
                isDestructible: true,
                destructionEffect: 'collapse',
                health: 1
            }
        },

        // === ROCK WALL FORMATIONS (Around room edges) ===

        // North wall rock formations
        {
            type: "rock_wall_formation",
            position: { x: -10, y: 0, z: -12 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.0 },
            userData: {
                objectId: "rock_wall_north_1",
                isDecorative: true
            }
        },
        {
            type: "rock_wall_formation",
            position: { x: -2, y: 0, z: -12 },
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 1.0, y: 1.1, z: 1.0 },
            userData: {
                objectId: "rock_wall_north_2",
                isDecorative: true
            }
        },
        {
            type: "rock_wall_formation",
            position: { x: 6, y: 0, z: -12 },
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 1.1, y: 0.9, z: 1.0 },
            userData: {
                objectId: "rock_wall_north_3",
                isDecorative: true
            }
        },

        // East wall rock formations
        {
            type: "rock_wall_formation",
            position: { x: 12, y: 0, z: 0 },
            rotation: { x: 0, y: Math.PI / 2 + Math.PI / 8, z: 0 },
            scale: { x: 1.1, y: 1.0, z: 1.0 },
            userData: {
                objectId: "rock_wall_east_2",
                isDecorative: true
            }
        },
        {
            type: "rock_wall_formation",
            position: { x: 12, y: 0, z: 8 },
            rotation: { x: 0, y: Math.PI / 2 - Math.PI / 6, z: 0 },
            scale: { x: 0.9, y: 1.1, z: 1.0 },
            userData: {
                objectId: "rock_wall_east_3",
                isDecorative: true
            }
        },

        // South wall rock formations
        {
            type: "rock_wall_formation",
            position: { x: 0, y: 0, z: 12 },
            rotation: { x: 0, y: Math.PI + Math.PI / 8, z: 0 },
            scale: { x: 1.2, y: 0.9, z: 1.0 },
            userData: {
                objectId: "rock_wall_south_2",
                isDecorative: true
            }
        },
        {
            type: "rock_wall_formation",
            position: { x: -8, y: 0, z: 12 },
            rotation: { x: 0, y: Math.PI - Math.PI / 6, z: 0 },
            scale: { x: 1.1, y: 1.1, z: 1.0 },
            userData: {
                objectId: "rock_wall_south_3",
                isDecorative: true
            }
        },

        // West wall rock formations
        {
            type: "rock_wall_formation",
            position: { x: -12, y: 0, z: 6 },
            rotation: { x: 0, y: -Math.PI / 2, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "rock_wall_west_1",
                isDecorative: true
            }
        },

        {
            type: "rock_wall_formation",
            position: { x: -12, y: 0, z: -10 },
            rotation: { x: 0, y: -Math.PI / 2 - Math.PI / 6, z: 0 },
            scale: { x: 0.9, y: 1.0, z: 1.0 },
            userData: {
                objectId: "rock_wall_west_3",
                isDecorative: true
            }
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: MysteriousPondRoom
};
