import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { textInputDialogue } from '../../ui/TextInputDialogue.js';
import { npcDialogueService } from '../../ai/NPCDialogueService.js';
import { 
    animateKnightSpeaking, 
    startKnightIdleAnimation, 
    updateKnightProximity 
} from '../../generators/prefabs/armoredKnightObject.js';

/**
 * Guardians of Lies Event Room - "The Hall of Truth and Deception"
 * 
 * A grand medieval hall where two armored knights guard two massive gates.
 * One knight always tells the truth, one always lies.
 * One gate leads to a treasure chamber, one leads to death.
 * Players must discover through conversation which knight is truthful
 * and which gate is safe. The knights can also talk to each other,
 * providing additional clues through their AI conversations.
 * 
 * THEME: Medieval honor, logic puzzles, AI dialogue systems
 * ATMOSPHERE: Grand stone hall with torch lighting and mystical ambiance
 */

/**
 * Guardians of Lies Room Handler
 */
export class GuardiansOfLiesRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        console.log(`[GuardiansOfLiesRoom] 🏰 Hall of Truth and Deception initialized for room ${roomId}`);
        
        this.state = {
            ...this.state,
            // Puzzle solution (randomized each time)
            treasureGateSide: null, // 'left' or 'right'
            truthKnightSide: null, // 'left' or 'right'
            
            // Gate choice state
            gateChoiceMade: false,
            chosenGate: null,
            puzzleSolved: false,
            playerDied: false,
            treasureChamberRevealed: false
        };
        
        // Initialize the puzzle randomly
        this.initializePuzzle();
        
        // Initialize AI service for knight conversations
        this.initializeAI();
        
        // Set up proper object properties after room generation
        setTimeout(() => {
            this.setupObjectProperties();
        }, 100);
        
        console.log(`[GuardiansOfLiesRoom] Truth Knight on ${this.state.truthKnightSide}, Treasure Gate on ${this.state.treasureGateSide}`);
    }

    /**
     * Initialize room effects when player enters the room
     * This method is called when the room becomes active
     */
    initialize() {
        console.log(`[GuardiansOfLiesRoom] 🏰 Initializing Guardians of Lies Room effects`);

        // Spawn fireflies immediately when room is created
        this.spawnInitialFireflies();
        
        // Start knight idle animations after short delay
        setTimeout(() => {
            this.startKnightIdleAnimations();
        }, 1500);
    }

    /**
     * Spawn 4 fireflies immediately when entering the guardians room
     * Following the same pattern as the mysterious pond room
     */
    spawnInitialFireflies() {
        console.log(`[GuardiansOfLiesRoom] ✨ SPAWNING 4 INITIAL FIREFLIES UPON ROOM ENTRY!`);

        // Get room center position for accurate firefly spawning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        console.log(`[GuardiansOfLiesRoom] Room center position: (${roomCenterX}, ${roomCenterZ})`);

        // Spawn 4 fireflies in corners around the knights
        const enemySpawns = [
            {
                type: "firefly",
                position: {
                    x: roomCenterX - 8.0, // Left side, near left knight
                    y: 2.5, // Hovering above ground
                    z: roomCenterZ + 3.0  // Slightly behind knights
                },
                spawnDelay: 0.2 // Quick spawn
            },
            {
                type: "firefly",
                position: {
                    x: roomCenterX + 8.0, // Right side, near right knight
                    y: 2.8, // Slightly higher
                    z: roomCenterZ + 3.0  // Slightly behind knights
                },
                spawnDelay: 0.4 // Staggered spawn
            },
            {
                type: "firefly",
                position: {
                    x: roomCenterX - 6.0, // Left side, towards back
                    y: 2.2, // Lower hover
                    z: roomCenterZ - 4.0  // Behind the gates
                },
                spawnDelay: 0.6 // Later spawn
            },
            {
                type: "firefly",
                position: {
                    x: roomCenterX + 6.0, // Right side, towards back
                    y: 3.0, // Highest hover
                    z: roomCenterZ - 4.0  // Behind the gates
                },
                spawnDelay: 0.8 // Last to spawn
            }
        ];

        console.log(`[GuardiansOfLiesRoom] Initial firefly spawn data:`, enemySpawns);

        try {
            // Spawn fireflies WITHOUT hiding doors (custom behavior for guardians room)
            // We don't use this.spawnEnemies() because it automatically hides doors
            // Instead, spawn each firefly individually without door management
            enemySpawns.forEach((enemySpawn, index) => {
                setTimeout(() => {
                    this.spawnEnemy(enemySpawn);
                }, enemySpawn.spawnDelay * 1000);
            });
            
            this.state.enemiesSpawned = true;
            console.log(`[GuardiansOfLiesRoom] ✅ Successfully initiated firefly spawning (doors remain visible)`);
        } catch (error) {
            console.error(`[GuardiansOfLiesRoom] ❌ Error spawning initial fireflies:`, error);
        }
    }

    /**
     * Start idle animations for both knights
     */
    startKnightIdleAnimations() {
        console.log('[GuardiansOfLiesRoom] 🎭 Starting knight idle animations');
        
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.warn('[GuardiansOfLiesRoom] No room group found for knight animations');
            return;
        }
        
        let knightsFound = 0;
        roomGroup.traverse(child => {
            if (child.userData?.objectId?.includes('knight')) {
                console.log(`[GuardiansOfLiesRoom] Starting idle animation for: ${child.userData.objectId}`);
                startKnightIdleAnimation(child);
                knightsFound++;
            }
        });
        
        console.log(`[GuardiansOfLiesRoom] ✅ Started idle animations for ${knightsFound} knights`);
    }

    /**
     * Update method - handles proximity detection for knight arm crossing
     */
    update(deltaTime) {
        super.update(deltaTime);
        
        // Update knight proximities for arm crossing
        this.updateKnightProximities();
    }

    /**
     * Update knight arm states based on player proximity
     */
    updateKnightProximities() {
        if (!this.dungeonHandler?.player) return;
        
        const playerPosition = this.dungeonHandler.player.position;
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        
        if (!roomGroup) return;
        
        roomGroup.traverse(child => {
            if (child.userData?.objectId?.includes('knight')) {
                // 5 units to cross arms, 7 units to uncross (hysteresis)
                updateKnightProximity(child, playerPosition, 5.0, 7.0);
            }
        });
    }
    
    /**
     * Randomly assign which knight is truthful and which gate has treasure
     */
    initializePuzzle() {
        // Randomly assign truth knight to left or right
        this.state.truthKnightSide = Math.random() < 0.5 ? 'left' : 'right';
        
        // Randomly assign treasure gate to left or right  
        this.state.treasureGateSide = Math.random() < 0.5 ? 'left' : 'right';
        
        console.log(`[GuardiansOfLiesRoom] 🎲 Puzzle initialized:`);
        console.log(`  Truth Knight: ${this.state.truthKnightSide} side`);
        console.log(`  Treasure Gate: ${this.state.treasureGateSide} side`);
        
        // Debug scenario summary
        const leftGate = this.state.treasureGateSide === 'left' ? 'TREASURE' : 'DEATH';
        const rightGate = this.state.treasureGateSide === 'right' ? 'TREASURE' : 'DEATH';
        const leftGuardian = this.state.truthKnightSide === 'left' ? 'TRUTH' : 'LIE';
        const rightGuardian = this.state.truthKnightSide === 'right' ? 'TRUTH' : 'LIE';
        
        console.log(`🎯 [GUARDIANS DEBUG] Left Gate: ${leftGate} | Right Gate: ${rightGate} | Left Guardian: ${leftGuardian} | Right Guardian: ${rightGuardian}`);
    }
    
    /**
     * Set up proper object properties after room generation
     */
    setupObjectProperties() {
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.warn('[GuardiansOfLiesRoom] No room group found for object setup');
            return;
        }
        
        let knightCount = 0;
        let gateCount = 0;
        
        roomGroup.traverse(child => {
            if (child.userData?.objectId) {
                const objectId = child.userData.objectId;
                
                // Knights should always be interactable and stay interactable
                if (objectId.includes('knight')) {
                    console.log(`[GuardiansOfLiesRoom] 🛡️ Configuring knight: ${objectId}`, child.userData);
                    child.userData.isInteractable = true;
                    child.userData.staysInteractable = true;
                    child.userData.isEventObject = true;
                    child.userData.eventRoomId = this.roomId;
                    child.userData.isUsed = false;
                    knightCount++;
                    console.log(`[GuardiansOfLiesRoom] ✅ Knight ${objectId} configured as interactable`);
                }
                // Gates and doors should be interactable and configured based on puzzle
                else if (objectId.includes('gate') || objectId.includes('guardian_door')) {
                    console.log(`[GuardiansOfLiesRoom] 🚪 Configuring gate/door: ${objectId}`);
                    child.userData.isInteractable = true;
                    child.userData.isEventObject = true;
                    child.userData.eventRoomId = this.roomId;
                    
                    // Configure gate type based on puzzle solution
                    if (objectId === 'gate_left' || objectId === 'left_guardian_door') {
                        child.userData.gateType = this.state.treasureGateSide === 'left' ? 'treasure' : 'death';
                        child.userData.leadsTo = this.state.treasureGateSide === 'left' ? 'treasure' : 'death';
                    } else if (objectId === 'gate_right' || objectId === 'right_guardian_door') {
                        child.userData.gateType = this.state.treasureGateSide === 'right' ? 'treasure' : 'death';
                        child.userData.leadsTo = this.state.treasureGateSide === 'right' ? 'treasure' : 'death';
                    }
                    
                    gateCount++;
                    console.log(`[GuardiansOfLiesRoom] ✅ Gate/Door ${objectId} configured as ${child.userData.gateType} gate`);
                }
                // Decorative objects should NOT be interactable
                else if (child.userData.isDecorative || 
                         objectId.includes('crystal') || 
                         objectId.includes('torch') || 
                         objectId.includes('pillar') || 
                         objectId.includes('vase') || 
                         objectId.includes('orb')) {
                    child.userData.isInteractable = false;
                    child.userData.isEventObject = false;
                    child.userData.isDecorative = true;
                    delete child.userData.eventRoomId;
                }
            }
        });
        
        console.log(`[GuardiansOfLiesRoom] 📊 Object setup complete - Knights: ${knightCount}, Gates: ${gateCount}`);
        console.log(`[GuardiansOfLiesRoom] 🎯 Gate configuration: Left=${this.state.treasureGateSide === 'left' ? 'TREASURE' : 'DEATH'}, Right=${this.state.treasureGateSide === 'right' ? 'TREASURE' : 'DEATH'}`);
        
        // Additional check - let's list all objects with knight in the name
        roomGroup.traverse(child => {
            if (child.userData?.objectId?.includes('knight')) {
                console.log(`[GuardiansOfLiesRoom] 🔍 Found knight object:`, {
                    objectId: child.userData.objectId,
                    isInteractable: child.userData.isInteractable,
                    isEventObject: child.userData.isEventObject,
                    position: { x: child.position.x, y: child.position.y, z: child.position.z }
                });
            }
        });
    }

    /**
     * Initialize AI system for knight conversations
     */
    async initializeAI() {
        console.log('[GuardiansOfLiesRoom] 🤖 Initializing NPC dialogue service for knight conversations...');
        
        try {
            // Start AI initialization in background (don't wait)
            npcDialogueService.initialize().then(() => {
                console.log('[GuardiansOfLiesRoom] ✅ NPC dialogue service ready');
            }).catch(error => {
                console.warn('[GuardiansOfLiesRoom] NPC dialogue service initialization failed:', error);
            });
        } catch (error) {
            console.warn('[GuardiansOfLiesRoom] Failed to start NPC dialogue service initialization:', error);
        }
    }
    
    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[GuardiansOfLiesRoom] 🎯 INTERACTION RECEIVED - Object: ${objectId}, Room ID: ${this.roomId}`);
        console.log(`[GuardiansOfLiesRoom] Context:`, context);
        
        // Knight chat interactions (always allow knight interactions)
        if (objectId.includes('knight_left')) {
            console.log(`[GuardiansOfLiesRoom] 💬 Starting left knight chat interaction`);
            await this.handleKnightChatInteraction('left');
            return;
        } else if (objectId.includes('knight_right')) {
            console.log(`[GuardiansOfLiesRoom] 💬 Starting right knight chat interaction`);
            await this.handleKnightChatInteraction('right');
            return;
        }
        
        // Gate interactions only if no choice made yet
        if (this.state.gateChoiceMade) {
            console.log(`[GuardiansOfLiesRoom] Gate choice already made, ignoring gate interactions`);
            return;
        }
        
        if (objectId.includes('gate_left') || objectId.includes('left_guardian_door')) {
            console.log(`[GuardiansOfLiesRoom] 🚪 Starting left gate interaction`);
            await this.handleGateChoice('left');
        } else if (objectId.includes('gate_right') || objectId.includes('right_guardian_door')) {
            console.log(`[GuardiansOfLiesRoom] 🚪 Starting right gate interaction`);
            await this.handleGateChoice('right');
        }
        else {
            console.warn(`[GuardiansOfLiesRoom] ❌ Unknown object interaction: ${objectId}`);
        }
    }

    /**
     * Handle custom interactions specific to Guardians of Lies Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[GuardiansOfLiesRoom] 🎯 Handling custom interaction:`, interaction);

        switch (interaction.type) {
            case 'knight_chat':
                // Determine which knight based on the side
                if (interaction.side === 'left') {
                    await this.handleKnightChatInteraction('left');
                } else if (interaction.side === 'right') {
                    await this.handleKnightChatInteraction('right');
                } else {
                    console.warn(`[GuardiansOfLiesRoom] Unknown knight side: ${interaction.side}`);
                }
                break;
            case 'gate_choice':
                if (!this.state.gateChoiceMade) {
                    if (interaction.side === 'left') {
                        await this.handleGateChoice('left');
                    } else if (interaction.side === 'right') {
                        await this.handleGateChoice('right');
                    } else {
                        console.warn(`[GuardiansOfLiesRoom] Unknown gate side: ${interaction.side}`);
                    }
                } else {
                    console.log(`[GuardiansOfLiesRoom] Gate choice already made, ignoring gate interactions`);
                }
                break;
            case 'truth_knight':
                await this.handleKnightChatInteraction('left'); // Assuming truth knight is on left
                break;
            case 'lie_knight':
                await this.handleKnightChatInteraction('right'); // Assuming lie knight is on right
                break;
            default:
                console.warn(`[GuardiansOfLiesRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }
    
    /**
     * Reset knight interaction states (ensure they stay interactable)
     */
    resetKnightInteractionStates() {
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) return;
        
        roomGroup.traverse(child => {
            if (child.userData?.objectId?.includes('knight')) {
                child.userData.isUsed = false;
                child.userData.isInteractable = true;
                child.userData.staysInteractable = true;
            }
        });
        
        console.log('[GuardiansOfLiesRoom] Reset knight interaction states');
    }

    /**
     * Find and animate knight statue when speaking
     */
    animateKnightSpeaking(side) {
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) return;
        
        // Find the knight object on the specified side
        let knightObject = null;
        roomGroup.traverse(child => {
            if (child.userData?.objectId?.includes(`knight_${side}`)) {
                knightObject = child;
            }
        });
        
        if (knightObject) {
            console.log(`[GuardiansOfLiesRoom] 🎭 Animating ${side} knight speech`);
            animateKnightSpeaking(knightObject, 3000); // 3 second animation
        } else {
            console.warn(`[GuardiansOfLiesRoom] Could not find knight object for ${side} side`);
        }
    }
    
    /**
     * Handle knight chat interaction using new AI text input system
     */
    async handleKnightChatInteraction(side) {
        const isTruthKnight = (side === this.state.truthKnightSide);
        const knightType = isTruthKnight ? 'truth' : 'lie';
        const knightName = isTruthKnight ? 'Truth Knight' : 'Lie Knight';
        
        console.log(`[GuardiansOfLiesRoom] 💬 Opening chat with ${knightName} on ${side} side`);
        console.log(`[GuardiansOfLiesRoom] Debug info:`, {
            truthKnightSide: this.state.truthKnightSide,
            treasureGateSide: this.state.treasureGateSide,
            clickedSide: side,
            isTruthKnight: isTruthKnight,
            knightType: knightType
        });
        
        // Prepare context for AI service
        const chatContext = {
            knightType: knightType,
            knightSide: side,
            truthKnightSide: this.state.truthKnightSide,
            treasureGateSide: this.state.treasureGateSide,
            onGateChoice: (chosenSide) => {
                console.log(`[GuardiansOfLiesRoom] 🚪 Gate choice detected from chat: ${chosenSide}`);
                // Close chat first, then handle gate choice
                textInputDialogue.close();
                setTimeout(() => {
                    this.handleGateChoice(chosenSide);
                }, 500);
            }
        };
        
        // Open text input dialogue
        try {
            await textInputDialogue.showKnightChat(
                chatContext,
                (userMessage, knightResponse) => {
                    // Response callback - animate knight when it speaks
                    console.log(`[GuardiansOfLiesRoom] Chat exchange - User: "${userMessage}" -> Knight: "${knightResponse}"`);
                    
                    // Animate the knight when it responds
                    this.animateKnightSpeaking(side);
                    
                    // Check for gate choice keywords in user message
                    const gateChoicePattern = /i\s+(choose|pick|want|take)\s+(?:the\s+)?(left|right)\s*gate/i;
                    const match = userMessage.match(gateChoicePattern);
                    if (match) {
                        const chosenSide = match[2].toLowerCase();
                        console.log(`[GuardiansOfLiesRoom] 🚪 Gate choice detected: ${chosenSide}`);
                        chatContext.onGateChoice(chosenSide);
                    }
                },
                () => {
                    // Close callback
                    console.log(`[GuardiansOfLiesRoom] Chat with ${knightName} closed`);
                    // Ensure knights stay interactable after chat
                    this.resetKnightInteractionStates();
                }
            );
        } catch (error) {
            console.error(`[GuardiansOfLiesRoom] Error during knight chat:`, error);
            
            // Fallback to simple dialogue
            await this.showDialogue({
                lines: [
                    `The ${knightName.toLowerCase()} regards you silently.`,
                    "The mystical connection seems disrupted...",
                    "Perhaps try approaching again."
                ],
                options: [
                    { text: "Step back", value: "leave" }
                ]
            });
        }
    }
    
    /**
     * Handle player's gate choice
     */
    async handleGateChoice(chosenSide) {
        if (this.state.gateChoiceMade) {
            return;
        }
        
        console.log(`[GuardiansOfLiesRoom] 🚪 Player chose ${chosenSide} gate`);
        
        // First confirm the choice
        const dialogueData = {
            lines: [
                `You approach the massive ${chosenSide} gate.`,
                "The knights turn their glowing eyes toward you.",
                "The air crackles with ancient magic...",
                "Are you certain of your choice?"
            ],
            options: [
                { text: "Yes, open this gate", value: "open" },
                { text: "No, step back", value: "reconsider" }
            ]
        };
        
        const choice = await this.showDialogue(dialogueData);
        
        if (choice === 'open') {
            this.state.gateChoiceMade = true;
            this.state.chosenGate = chosenSide;
            
            // Make the guardian of the chosen side disappear
            await this.makeKnightDisappear(chosenSide);
            
            // Then resolve the gate choice
            await this.resolveGateChoice(chosenSide);
        } else {
            // Player chose to reconsider - reset knight interaction states
            this.resetKnightInteractionStates();
        }
    }
    
    /**
     * Make the knight in front of the chosen gate disappear
     */
    async makeKnightDisappear(chosenSide) {
        console.log(`[GuardiansOfLiesRoom] ✨ Making ${chosenSide} knight disappear`);
        
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.warn(`[GuardiansOfLiesRoom] No room group found for knight disappearing`);
            return;
        }
        
        // Find the knight object on the chosen side
        let knightObject = null;
        const knightId = chosenSide === 'left' ? 
            (this.state.truthKnightSide === 'left' ? 'truth_knight_left' : 'lie_knight_left') :
            (this.state.truthKnightSide === 'right' ? 'truth_knight_right' : 'lie_knight_right');
        
        roomGroup.traverse(child => {
            if (child.userData?.objectId === knightId) {
                knightObject = child;
            }
        });
        
        if (!knightObject) {
            console.warn(`[GuardiansOfLiesRoom] Could not find knight object: ${knightId}`);
            return;
        }
        
        // Show disappearing dialogue
        const knightName = this.state.truthKnightSide === chosenSide ? 'Truth Knight' : 'Lie Knight';
        await this.showDialogue({
            lines: [
                `The ${knightName} steps aside with a solemn nod.`,
                "\"Your choice is made, traveler. I shall guard no more.\"",
                "The knight's form begins to shimmer and fade...",
                "A path to the gate opens before you."
            ],
            options: [
                { text: "Watch the knight disappear", value: "continue" }
            ]
        });
        
        // Animate knight disappearing
        await this.animateKnightDisappearing(knightObject);
        
        // Remove knight from scene
        roomGroup.remove(knightObject);
        console.log(`[GuardiansOfLiesRoom] ✅ Knight removed from scene`);
    }
    
    /**
     * Animate knight fading away
     */
    async animateKnightDisappearing(knightObject) {
        return new Promise((resolve) => {
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const startOpacity = 1.0;
            const startScale = 1.0;
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Smooth fade and scale down
                const opacity = startOpacity * (1 - progress);
                const scale = startScale * (1 - progress * 0.5); // Scale down to 50%
                
                // Apply to all materials in the knight object
                knightObject.traverse(child => {
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => {
                                mat.transparent = true;
                                mat.opacity = opacity;
                            });
                        } else {
                            child.material.transparent = true;
                            child.material.opacity = opacity;
                        }
                    }
                });
                
                // Scale the entire knight
                knightObject.scale.setScalar(scale);
                
                // Add floating effect
                const floatHeight = Math.sin(progress * Math.PI * 3) * 2;
                knightObject.position.y = knightObject.userData.originalY || 0 + floatHeight;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };
            
            // Store original Y position
            knightObject.userData.originalY = knightObject.position.y;
            animate();
        });
    }
    
    /**
     * Resolve the gate choice - treasure or death
     */
    async resolveGateChoice(chosenSide) {
        const choseTreasureGate = (chosenSide === this.state.treasureGateSide);
        
        console.log(`[GuardiansOfLiesRoom] 🎯 Gate choice resolution: chose ${chosenSide}, treasure is on ${this.state.treasureGateSide}`);
        
        if (choseTreasureGate) {
            await this.handleTreasureGateChoice();
        } else {
            await this.handleDeathGateChoice();
        }
    }
    
    /**
     * Handle successful treasure gate choice
     */
    async handleTreasureGateChoice() {
        console.log(`[GuardiansOfLiesRoom] 🏆 Player chose correctly - treasure gate!`);
        
        this.state.puzzleSolved = true;
        this.state.treasureChamberRevealed = true;
        
        await this.showDialogue({
            lines: [
                "The massive gate rumbles open with golden light!",
                "Beyond lies a magnificent treasure chamber!",
                "The knights nod approvingly.",
                "\"You have chosen wisely, brave traveler.\"",
                "\"Enter and claim your reward.\""
            ],
            options: [
                { text: "Enter the treasure chamber", value: "enter" }
            ]
        });
        
        // Create the treasure chamber
        this.createTreasureChamber();
        
        // Show doors after success
        setTimeout(() => {
            this.showEventRoomDoors();
        }, 2000);
        
        this.playAudio("puzzle_solved");
    }
    
    /**
     * Create treasure chamber behind the gate
     */
    createTreasureChamber() {
        console.log(`[GuardiansOfLiesRoom] 🏛️ Creating treasure chamber`);
        
        // For now, spawn treasure chest in main room
        // In a full implementation, this would create a separate chamber
        setTimeout(() => {
            this.spawnChest({
                position: { x: 0, y: 1, z: 0 }, // Center of room
                chestType: "guardians_victory_chest"
            });
        }, 1000);
    }
    
    /**
     * Handle death gate choice
     */
    async handleDeathGateChoice() {
        console.log(`[GuardiansOfLiesRoom] 💀 Player chose incorrectly - death gate!`);
        
        this.state.playerDied = true;
        
        await this.showDialogue({
            lines: [
                "The gate creaks open revealing swirling darkness!",
                "Deadly shadows pour forth from the void!",
                "The knights speak in unison:",
                "\"You have chosen... poorly, traveler.\"",
                "\"Face the consequences of your deception.\""
            ],
            options: [
                { text: "Face the shadows", value: "accept_fate" }
            ]
        });
        
        // Spawn death trap enemies following pond room patterns
        const deathSpawns = [
            {
                type: "shadow_wraith",
                position: { x: 0, y: 1, z: 2 },
                spawnDelay: 0.5
            },
            {
                type: "void_sentinel", 
                position: { x: -2, y: 1, z: 1 },
                spawnDelay: 1.0
            },
            {
                type: "death_phantom",
                position: { x: 2, y: 1, z: 1 },
                spawnDelay: 1.5
            }
        ];
        
        this.spawnEnemies(deathSpawns);
        this.playAudio("nightmare_chest");
    }
    
    /**
     * Handle enemy defeat - only relevant if player chose death gate
     */
    handleEnemyDefeat(enemyId) {
        super.handleEnemyDefeat(enemyId);
        
        // If player chose death gate but survived the trap
        if (this.state.playerDied && this.state.enemiesDefeated) {
            console.log(`[GuardiansOfLiesRoom] 🎉 Player survived the death trap!`);
            
            setTimeout(async () => {
                await this.showDialogue({
                    lines: [
                        "Against all odds, you have survived the shadows!",
                        "The knights seem impressed by your resilience.",
                        "\"Perhaps there is honor in you yet, traveler...\"",
                        "As a reward for your tenacity, a consolation appears."
                    ],
                    options: [
                        { text: "Accept the honor", value: "accept" }
                    ]
                });
                
                // Spawn smaller consolation chest
                this.spawnChest({
                    position: { x: 0, y: 1, z: 0 },
                    chestType: "consolation_chest"
                });
            }, 2000);
        }
    }
    
    /**
     * Cleanup when leaving room
     */
    cleanup() {
        console.log('[GuardiansOfLiesRoom] 🧹 Cleaning up knight animations');
        
        // Stop all knight animations
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (roomGroup) {
            roomGroup.traverse(child => {
                if (child.userData?.objectId?.includes('knight')) {
                    child.userData.isAnimating = false;
                    child.userData.animationType = null;
                    child.userData.armsCrossed = false;
                }
            });
        }
        
        super.cleanup();
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "guardians_of_lies",
    name: "Hall of Truth and Deception", 
    shape: "SQUARE_2X2", // Large room like mysterious pond (28x28 units)
    description: "A grand medieval hall where armored knights guard the gates of fate",
    tags: ["medieval", "logic", "knights", "ai_dialogue"],
    
    // Door connection system - ONLY SOUTH DOOR (entered from north)
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: false,   // Cannot be entered from south
        east: false,    // Cannot be entered from east
        west: false     // Cannot be entered from west
    },
    primaryEntrance: "north", // Only entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -14 },   // Door on south wall when entered from north
        south: null,    // No door
        east: null,     // No door
        west: null      // No door
    },
    
    // Visual overrides - medieval stone hall
    materials: {
        walls: "ancient_stone_with_vines", // Custom ancient stone walls with creeping vines
        floors: "ancient_stone_with_vines", // Custom ancient stone floor with creeping vines
        wallTint: 0x8B7D6B, // Warm medieval stone
        floorTint: 0x696969  // Darker stone floor
    },
    
    // Static brightness - moody torch lighting
    staticBrightness: 3, // Lower lighting (3/10) for moody medieval atmosphere
    
    // Moody lighting configuration with dramatic torch lighting
    lighting: {
        ambient: {
            intensity: 0.2, // Much lower ambient for moodier atmosphere
            color: 0x6B5D4F // Darker, warmer medieval ambient
        },
        // Central fire removed for more dramatic torch lighting
        leftTorchLight: {
            position: { x: -11, y: 3, z: -13 }, // Above left torch
            intensity: 4.0, // Strong torch light
            color: 0xFF7F00, // Warm orange torch flame
            distance: 18,
            decay: 2.0
        },
        rightTorchLight: {
            position: { x: 11, y: 3, z: -13 }, // Above right torch
            intensity: 4.0, // Strong torch light
            color: 0xFF7F00, // Warm orange torch flame
            distance: 18,
            decay: 2.0
        },
        leftGateLight: {
            position: { x: -4, y: 2, z: -6 }, // Above left gate (reduced)
            intensity: 1.5, // Dimmer gate light
            color: 0xFFD700, // Bright golden gate light (original)
            distance: 12,
            decay: 2.5
        },
        rightGateLight: {
            position: { x: 4, y: 2, z: -6 }, // Above right gate (reduced)
            intensity: 1.5, // Dimmer gate light
            color: 0x8B0000, // Dark red gate light (original)
            distance: 12,
            decay: 2.5
        }
    },
    
    // Object placement - minimal room with only pillars and knights
    objects: [
        // === KNIGHTS ===
        // Left Knight (randomized truth/lie) - positioned away from pillars
        {
            type: "truth_knight", // Will be swapped based on puzzle
            position: { x: -4, y: 0, z: 2 }, // Moved closer to center, away from pillars
            rotation: { x: 0, y: 0, z: 0 }, // Facing forward toward player entrance
            scale: { x: 1.15, y: 1.15, z: 1.15 }, // 15% larger
            userData: {
                objectId: "truth_knight_left", // Dynamic ID
                isInteractable: true,
                interactionType: "knight_chat",
                knightSide: "left",
                hasAI: true,
                isEventObject: true,
                eventRoomId: "guardians_of_lies", // Required for ChestInteractionSystem routing
                staysInteractable: true, // Prevent ChestInteractionSystem from marking as used
                interaction: {
                    type: "knight_chat",
                    side: "left",
                    id: "truth_knight_left"
                }
            }
        },
        
        // Right Knight (randomized truth/lie) - positioned away from pillars
        {
            type: "lie_knight", // Will be swapped based on puzzle
            position: { x: 4, y: 0, z: 2 }, // Moved closer to center, away from pillars
            rotation: { x: 0, y: 0, z: 0 }, // Facing forward toward player entrance
            scale: { x: 1.15, y: 1.15, z: 1.15 }, // 15% larger
            userData: {
                objectId: "lie_knight_right", // Dynamic ID
                isInteractable: true,
                interactionType: "knight_chat", 
                knightSide: "right",
                hasAI: true,
                isEventObject: true,
                eventRoomId: "guardians_of_lies", // Required for ChestInteractionSystem routing
                staysInteractable: true, // Prevent ChestInteractionSystem from marking as used
                interaction: {
                    type: "knight_chat",
                    side: "right",
                    id: "lie_knight_right"
                }
            }
        },
        
        // === PILLARS REMOVED ===
        // Ancient stone pillars removed for cleaner room layout
        
        // === OLD STONE PATH FROM ENTRANCE TO DOORS ===
        // Individual weathered stones creating an ancient path
        {
            type: "old_path_stone",
            position: { x: 0.5, y: 0, z: 12 },
            userData: { objectId: "path_stone_1", isDecorative: true }
        },
        {
            type: "old_path_stone", 
            position: { x: -0.3, y: 0, z: 10 },
            userData: { objectId: "path_stone_2", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: 0.7, y: 0, z: 8 },
            userData: { objectId: "path_stone_3", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: -0.4, y: 0, z: 6 },
            userData: { objectId: "path_stone_4", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: 0.2, y: 0, z: 4 },
            userData: { objectId: "path_stone_5", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: -0.6, y: 0, z: 2 },
            userData: { objectId: "path_stone_6", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: 0.4, y: 0, z: 0 },
            userData: { objectId: "path_stone_7", isDecorative: true }
        },
        {
            type: "old_path_stone",
            position: { x: -0.2, y: 0, z: -2 },
            userData: { objectId: "path_stone_8", isDecorative: true }
        },
        
        // === TEMPLE PILLARS ===
        // Left temple pillar (doubled height)
        {
            type: "ancient_sandstone_temple_pillar",
            position: { x: -9.0, y: 0.0, z: 5.0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 2.0, z: 1.0 },
            userData: { 
                objectId: "left_temple_pillar", 
                isDecorative: true
            }
        },
        
        // Right temple pillar (doubled height)
        {
            type: "ancient_sandstone_temple_pillar",
            position: { x: 9.0, y: 0.0, z: 5.0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 2.0, z: 1.0 },
            userData: { 
                objectId: "right_temple_pillar", 
                isDecorative: true
            }
        },
        
        // Back right corner temple pillar (doubled height)
        {
            type: "ancient_sandstone_temple_pillar",
            position: { x: 11.5, y: 0.0, z: 11.5 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 2.0, z: 1.0 },
            userData: { 
                objectId: "back_right_temple_pillar", 
                isDecorative: true
            }
        },
        
        // Back left corner temple pillar (doubled height)
        {
            type: "ancient_sandstone_temple_pillar",
            position: { x: -11.5, y: 0.0, z: 11.5 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 2.0, z: 1.0 },
            userData: { 
                objectId: "back_left_temple_pillar", 
                isDecorative: true
            }
        },
        
        // === HELLISH TORCHES FOR DRAMATIC LIGHTING ===
        // Left hellish torch mounted on north wall
        {
            type: "hellish_torch",
            position: { x: -11, y: 1.5, z: -12.5 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 2.0, y: 2.0, z: 2.0 }, // Large wall-mounted hellish torches
            userData: { 
                objectId: "left_wall_hellish_torch", 
                isDecorative: true,
                isLightSource: true,
                hasFlameAnimation: true
            }
        },
        
        // Right hellish torch mounted on north wall
        {
            type: "hellish_torch",
            position: { x: 11, y: 1.5, z: -12.5 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 2.0, y: 2.0, z: 2.0 }, // Large wall-mounted hellish torches
            userData: { 
                objectId: "right_wall_hellish_torch", 
                isDecorative: true,
                isLightSource: true,
                hasFlameAnimation: true
            }
        },
        
        // === GUARDIAN DOORS ===
        // Left Guardian Door (positioned behind left knight) - dark armor theme
        {
            type: "iron_dungeon_door",
            position: { x: -4, y: 0, z: -6 }, // Behind left guardian, moved further back for massive size
            rotation: { x: 0, y: 0, z: 0 }, // Facing forward
            scale: { x: 12.0, y: 12.0, z: 12.0 }, // Increased by 20% (10 * 1.2 = 12)
            userData: { 
                objectId: "left_guardian_door", 
                isInteractable: true,
                interactionType: "gate",
                gateSide: "left",
                isEventObject: true,
                gateType: "left_gate",
                interaction: {
                    type: "gate_choice",
                    side: "left",
                    id: "gate_left"
                }
            }
        },
        
        // Right Guardian Door (positioned behind right knight) - dark armor theme
        {
            type: "iron_dungeon_door",
            position: { x: 4, y: 0, z: -6 }, // Behind right guardian, moved further back for massive size
            rotation: { x: 0, y: 0, z: 0 }, // Facing forward
            scale: { x: 12.0, y: 12.0, z: 12.0 }, // Increased by 20% (10 * 1.2 = 12)
            userData: { 
                objectId: "right_guardian_door", 
                isInteractable: true,
                interactionType: "gate",
                gateSide: "right", 
                isEventObject: true,
                gateType: "right_gate",
                interaction: {
                    type: "gate_choice",
                    side: "right",
                    id: "gate_right"
                }
            }
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: GuardiansOfLiesRoom
};