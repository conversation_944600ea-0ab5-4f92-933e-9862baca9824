import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { createUnrealFog } from '../../effects/VolumetricFogManager.js';
import { createFloorMistPlanes } from '../../effects/FloorMistPlane.js';
import { RhythmArcade3D } from '../../systems/RhythmArcade3D.js';
import { VOXEL_SIZE } from '../../generators/prefabs/shared.js';

/**
 * Arcade Game Room Event Room - "Rhythmic Challenge"
 *
 * A retro arcade room with a glowing arcade machine that initiates
 * a rhythm-based mini-game similar to Crypt of the NecroDancer or Piano Tiles.
 * Players must time their inputs to the music beats to destroy falling enemy icons.
 * Success provides rewards, failure causes damage.
 * 
 * The room has a nostalgic arcade atmosphere with neon lighting,
 * electronic sound effects, and retro-futuristic decorations.
 *
 * THEME: Nostalgia, skill, rhythm, retro gaming, electronic music
 * ATMOSPHERE: Dark room with neon glow, electronic ambiance, arcade vibes
 */

/**
 * Arcade Game Room Handler
 */
export class ArcadeGameRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        
        // Extended state for arcade game
        this.state = {
            ...this.state,
            // Game progression
            arcadeEncountered: false,
            gameActive: false,
            gameCompleted: false,
            gameResult: null,
            // Atmospheric state
            fogSystem: null,
            fogAdded: false,
            neonGlowActive: true,
            // Animation state
            screenFlickering: true,
            neonPulsing: true,
            updateCount: 0,
            // Game state
            currentDifficulty: 'normal',
            screenAnimationStartTime: 0
        };

        console.log(`[ArcadeGameRoom] 🕹️ Arcade Game Room initialized for room ${roomId}`);
        
        // NOTE: Effect initialization moved to initialize() method to prevent race conditions
    }

    /**
     * Initialize room effects when player enters the room
     * This method is called when the room becomes active
     */
    initialize() {
        console.log(`[ArcadeGameRoom] 🕹️ Initializing Arcade Game Room effects`);

        // Initialize neon atmospheric effects immediately
        this.initializeNeonEffects();
        
        // Fog initialization disabled to prevent memory issues
        console.log('[ArcadeGameRoom] Skipping fog initialization to prevent memory issues');
        this.state.fogAdded = true; // Mark as added to prevent update attempts
    }

    /**
     * Clean up room resources and effects
     */
    cleanup() {
        super.cleanup(); // Call the base class method
        console.log(`[ArcadeGameRoom] Cleaning up effects for room ${this.roomId}`);

        // 1. Clear the pending timer to prevent it from firing late.
        if (this.state.fogInitInterval) {
            clearInterval(this.state.fogInitInterval);
            this.state.fogInitInterval = null;
        }

        // 2. If the fog system was created, remove it.
        if (this.state.fogSystem && this.state.fogSystem.dispose) {
            this.state.fogSystem.dispose();
            this.state.fogSystem = null;
        }

        // 3. Remove any other added objects, like mist planes.
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            const toRemove = this.dungeonHandler.currentRoomGroup.children.filter(
                child => child.userData.isFloorMist // Assuming you add this identifier
            );
            toRemove.forEach(child => {
                this.dungeonHandler.currentRoomGroup.remove(child);
                if (child.geometry) child.geometry.dispose();
                if (child.material) child.material.dispose();
            });
        }

        this.state.fogAdded = false;
    }

    /**
     * Initialize neon fog and atmospheric effects
     */
    initNeonFog() {
        console.log('[ArcadeGameRoom] 🕹️ Neon fog disabled to prevent memory issues');
        // Fog system temporarily disabled to prevent memory issues
        // Will be re-enabled once memory optimization is complete
        return;
    }

    /**
     * Initialize neon atmospheric effects
     */
    initializeNeonEffects() {
        console.log('[ArcadeGameRoom] 🕹️ Neon arcade effects initialized');
        // This would set up screen flickering, neon pulsing, etc.
    }

    /**
     * Update method called every frame for animations
     */
    update(deltaTime) {
        // Debug logging for first few updates
        if (!this.state.updateCount) this.state.updateCount = 0;
        this.state.updateCount++;

        if (this.state.updateCount <= 5) {
            console.log(`[ArcadeGameRoom] Update #${this.state.updateCount} - deltaTime: ${deltaTime}`);
        }

        // Initialize fog on first update when room is ready
        if (!this.state.fogAdded && this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            console.log(`[ArcadeGameRoom] 🕹️ Room ready - initializing neon fog now`);
            this.initNeonFog();
            this.state.fogAdded = true;
        }

        // Fog system disabled - skip update
        // if (this.state.fogSystem) {
        //     this.state.fogSystem.update(deltaTime);
        // }

        // Animate screen flickering
        if (this.state.screenFlickering && this.dungeonHandler?.currentRoomGroup) {
            this.updateScreenFlickering(deltaTime);
        }

        // Animate neon pulsing
        if (this.state.neonPulsing && this.dungeonHandler?.currentRoomGroup) {
            this.updateNeonPulsing(deltaTime);
        }
    }

    /**
     * Update screen flickering animation
     */
    updateScreenFlickering(deltaTime) {
        const currentTime = Date.now();
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        
        roomGroup.traverse(child => {
            if (child.name === 'arcade_machine' && child.userData?.isArcadeMachine) {
                // Find screen elements
                child.traverse(screen => {
                    if (screen.userData?.isGlowing) {
                        // Create realistic screen flicker effect
                        const flickerSpeed = 0.01;
                        const flickerAmplitude = 0.2;
                        const baseIntensity = screen.userData.baseEmissiveIntensity || 0.3;
                        
                        const flicker = Math.sin(currentTime * flickerSpeed) * flickerAmplitude;
                        const newIntensity = baseIntensity + flicker;
                        
                        screen.material.emissiveIntensity = Math.max(0.1, newIntensity);
                    }
                    
                    // Update screen light
                    if (screen.userData?.isScreenLight) {
                        const lightFlicker = Math.sin(currentTime * 0.008) * 0.5;
                        screen.intensity = Math.max(1.0, 2.0 + lightFlicker);
                    }
                });
            }
        });
    }

    /**
     * Update neon pulsing animation
     */
    updateNeonPulsing(deltaTime) {
        const currentTime = Date.now();
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        
        roomGroup.traverse(child => {
            // Update neon light pulsing
            if (child.userData?.isNeonLight) {
                const pulseSpeed = 0.005;
                const pulseAmplitude = 0.3;
                const baseIntensity = child.userData.baseIntensity || 1.0;
                
                const pulse = Math.sin(currentTime * pulseSpeed) * pulseAmplitude;
                const newIntensity = baseIntensity + pulse;
                
                child.intensity = Math.max(0.3, newIntensity);
            }
            
            // Update button pulsing
            if (child.userData?.isButton) {
                const buttonPulse = Math.sin(currentTime * 0.006) * 0.4;
                const baseEmissive = child.userData.baseEmissiveIntensity || 0.2;
                child.material.emissiveIntensity = Math.max(0.1, baseEmissive + buttonPulse);
            }
        });
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[ArcadeGameRoom] 🎯 Handling interaction with ${objectId}`);
        console.log(`[ArcadeGameRoom] Current state before interaction:`, this.state);

        // Check for arcade machine interaction
        if (objectId === 'arcade_machine' ||
            objectId.includes('arcade')) {
            await this.handleArcadeEncounter();
        } else {
            console.warn(`[ArcadeGameRoom] Unknown object interaction: ${objectId} - Only arcade machine is interactive`);
        }
    }

    /**
     * Handle custom interactions specific to Arcade Game Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[ArcadeGameRoom] 🎯 Handling custom interaction:`, interaction);
        console.log(`[ArcadeGameRoom] Current state before interaction:`, this.state);

        switch (interaction.type) {
            case 'rhythm_arcade':
                await this.handleArcadeEncounter();
                break;
            case 'arcade_machine':
                await this.handleArcadeEncounter();
                break;
            default:
                console.warn(`[ArcadeGameRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle Arcade machine encounter - main interaction
     */
    async handleArcadeEncounter() {
        console.log(`[ArcadeGameRoom] 🕹️ Arcade machine encounter started`);

        if (this.state.gameCompleted) {
            console.log(`[ArcadeGameRoom] ⚠️ Game already completed - machine shows high scores`);
            await this.showHighScores();
            return;
        }

        if (this.state.arcadeEncountered) {
            console.log(`[ArcadeGameRoom] ⚠️ Arcade already encountered - game in progress`);
            return;
        }

        // Mark encounter as started
        this.state.arcadeEncountered = true;
        console.log(`[ArcadeGameRoom] 🕹️ ARCADE ENCOUNTER INITIATED`);

        // Play arcade startup sound
        this.playAudio("chest_open"); // Using available sound

        // Show initial arcade dialogue
        await this.showArcadeGreeting();
    }

    /**
     * Show Arcade machine's greeting
     */
    async showArcadeGreeting() {
        console.log(`[ArcadeGameRoom] 🕹️ Showing arcade greeting dialogue`);

        const greetingData = {
            lines: [
                "The arcade machine hums to life as you approach...",
                "Neon lights flicker across the screen.",
                "\"RHYTHMIC DESTROYER - Test your timing against the beat!\"",
                "\"Insert coin to play... or not. This is a dungeon after all.\""
            ],
            options: [
                { text: "Play Game", value: "play" },
                { text: "Check High Scores", value: "scores" },
                { text: "Walk Away", value: "walk_away" }
            ]
        };

        const choice = await this.showDialogue(greetingData);
        console.log(`[ArcadeGameRoom] Player's choice:`, choice);

        switch (choice) {
            case 'play':
                await this.showDifficultySelection();
                break;
            case 'scores':
                await this.showHighScores();
                break;
            case 'walk_away':
                await this.handleWalkAway();
                break;
            default:
                console.log(`[ArcadeGameRoom] No choice made - resetting encounter`);
                this.state.arcadeEncountered = false;
        }
    }

    /**
     * Show difficulty selection
     */
    async showDifficultySelection() {
        console.log(`[ArcadeGameRoom] 🎮 Showing difficulty selection`);

        const difficultyData = {
            lines: [
                "\"Select your challenge level:\"",
                "\"Easy: Slow rhythm, more forgiveness\"",
                "\"Normal: Standard arcade challenge\"", 
                "\"Hard: Fast beats, precise timing required\""
            ],
            options: [
                { text: "Easy Mode", value: "easy" },
                { text: "Normal Mode", value: "normal" },
                { text: "Hard Mode", value: "hard" },
                { text: "Back", value: "back" }
            ]
        };

        const difficulty = await this.showDialogue(difficultyData);
        console.log(`[ArcadeGameRoom] Difficulty choice:`, difficulty);

        if (difficulty === 'back') {
            await this.showArcadeGreeting();
            return;
        }

        if (difficulty) {
            this.state.currentDifficulty = difficulty;
            await this.startRhythmGame();
        } else {
            console.log(`[ArcadeGameRoom] No difficulty chosen - resetting encounter`);
            this.state.arcadeEncountered = false;
        }
    }

    /**
     * Show high scores
     */
    async showHighScores() {
        console.log(`[ArcadeGameRoom] 🏆 Showing high scores`);

        const scoresData = {
            lines: [
                "\"RHYTHMIC DESTROYER - HIGH SCORES\"",
                "\"1. LEGENDARY DUNGEON CRAWLER - 5000\"",
                "\"2. BEAT MASTER - 3500\"",
                "\"3. RHYTHM KNIGHT - 2800\"",
                "\"4. TIMING WARRIOR - 2200\"",
                "\"5. CASUAL PLAYER - 1500\""
            ],
            options: [
                { text: "Play Game", value: "play" },
                { text: "Back", value: "back" }
            ]
        };

        const choice = await this.showDialogue(scoresData);
        
        if (choice === 'play') {
            await this.showDifficultySelection();
        } else {
            await this.showArcadeGreeting();
        }
    }

    /**
     * Start the rhythm game
     */
    async startRhythmGame() {
        console.log(`[ArcadeGameRoom] 🎵 Starting rhythm game with difficulty: ${this.state.currentDifficulty}`);

        this.state.gameActive = true;

        // Show game start dialogue
        const gameStartData = {
            lines: [
                "\"GAME STARTING...\"",
                "The screen comes alive with falling enemy icons!",
                "\"Use 1, 2, 3, 4 keys or click the target zones!\"",
                "\"Match the rhythm to destroy enemies and score points!\""
            ],
            options: [
                { text: "Begin Game", value: "start_game" }
            ]
        };

        await this.showDialogue(gameStartData);

        // Launch the rhythm game
        await this.launchRhythmGame();
    }

    /**
     * Launch the rhythm game
     */
    async launchRhythmGame() {
        console.log(`[ArcadeGameRoom] 🎮 Launching rhythm game`);

        // Get the scene manager from dungeonHandler
        const sceneManager = this.dungeonHandler ? this.dungeonHandler.sceneManager : null;
        
        if (!sceneManager) {
            console.error('[ArcadeGameRoom] No scene manager available for rhythm game');
            throw new Error('Scene manager not available');
        }

        // Create and start the rhythm game
        const rhythmGame = new RhythmArcade3D(sceneManager, this.dungeonHandler);
        const gameResult = await rhythmGame.startGame();
        
        await this.handleGameResult(gameResult);
    }

    /**
     * Handle game result
     */
    async handleGameResult(result) {
        console.log(`[ArcadeGameRoom] 🏆 Handling game result:`, result);

        this.state.gameActive = false;
        this.state.gameCompleted = true;
        this.state.gameResult = result;

        if (result.won) {
            await this.handleVictory(result);
        } else {
            await this.handleDefeat(result);
        }
    }

    /**
     * Handle victory
     */
    async handleVictory(result) {
        console.log(`[ArcadeGameRoom] 🎉 VICTORY! Player beat the rhythm game`);

        const victoryData = {
            lines: [
                "\"CONGRATULATIONS! NEW HIGH SCORE!\"",
                `\"Final Score: ${result.score}\"`,
                `\"Max Combo: ${result.maxCombo}\"`,
                "\"You've mastered the rhythmic challenge!\"",
                "\"Claim your well-earned reward!\""
            ],
            options: [
                { text: "Claim Reward", value: "claim" }
            ]
        };

        await this.showDialogue(victoryData);

        // Spawn reward based on score performance
        await this.spawnVictoryReward(result);

        // Show doors and allow exit
        this.showEventRoomDoors();
    }

    /**
     * Handle defeat
     */
    async handleDefeat(result) {
        console.log(`[ArcadeGameRoom] 💀 DEFEAT! Player lost the rhythm game`);

        const defeatData = {
            lines: [
                "\"GAME OVER\"",
                `\"Final Score: ${result.score}\"`,
                "\"Better luck next time!\"",
                "\"The machine powers down with a disappointed beep...\""
            ],
            options: [
                { text: "Try Again", value: "retry" },
                { text: "Give Up", value: "give_up" }
            ]
        };

        const choice = await this.showDialogue(defeatData);
        
        if (choice === 'retry') {
            // Reset game state for retry
            this.state.gameCompleted = false;
            this.state.gameActive = false;
            await this.showDifficultySelection();
        } else {
            // Small consolation reward for trying
            await this.spawnConsolationReward();
            this.showEventRoomDoors();
        }
    }

    /**
     * Handle walking away from arcade
     */
    async handleWalkAway() {
        console.log(`[ArcadeGameRoom] 🚶 Player chose to walk away`);

        const walkAwayData = {
            lines: [
                "The arcade machine's screen dims slightly...",
                "\"Maybe next time, player.\"",
                "The neon lights continue to pulse invitingly."
            ],
            options: [
                { text: "Leave", value: "leave" }
            ]
        };

        await this.showDialogue(walkAwayData);

        // Reset encounter state - allow retry
        this.state.arcadeEncountered = false;
        this.state.currentDifficulty = 'normal';

        console.log(`[ArcadeGameRoom] ✅ Encounter reset - player can retry`);
    }

    /**
     * Spawn victory reward based on performance
     */
    async spawnVictoryReward(result) {
        console.log(`[ArcadeGameRoom] 🎁 Spawning victory reward for score: ${result.score}`);

        // Get room center position for chest placement
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Determine reward quality based on score
        let chestType = "rhythm_victory_chest";
        if (result.score >= 3000) {
            chestType = "legendary_rhythm_chest";
        } else if (result.score >= 2000) {
            chestType = "epic_rhythm_chest";
        }

        // Spawn reward chest next to arcade machine
        const chestConfig = {
            position: {
                x: roomCenterX + VOXEL_SIZE * 3, // Next to arcade machine
                y: 1.0,
                z: roomCenterZ + 0.0
            },
            chestType: chestType
        };

        console.log(`[ArcadeGameRoom] Victory chest spawn position:`, chestConfig.position);

        // Spawn the victory chest
        await this.spawnChest(chestConfig);

        // Apply score-based effects
        await this.applyScoreEffects(result);
    }

    /**
     * Spawn consolation reward for trying
     */
    async spawnConsolationReward() {
        console.log(`[ArcadeGameRoom] 🎁 Spawning consolation reward`);

        // Get room center position
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Small reward for effort
        const chestConfig = {
            position: {
                x: roomCenterX + VOXEL_SIZE * 3,
                y: 1.0,
                z: roomCenterZ + 0.0
            },
            chestType: "consolation_chest"
        };

        await this.spawnChest(chestConfig);
    }

    /**
     * Apply score-based effects to player
     */
    async applyScoreEffects(result) {
        console.log(`[ArcadeGameRoom] ⚡ Applying score effects for result:`, result);

        // This would integrate with the game's systems to apply actual rewards
        // For now, just log what would happen based on score
        if (result.score >= 3000) {
            console.log(`[ArcadeGameRoom] Legendary performance! Would add rare items and significant health`);
        } else if (result.score >= 2000) {
            console.log(`[ArcadeGameRoom] Excellent performance! Would add good items and moderate health`);
        } else if (result.score >= 1000) {
            console.log(`[ArcadeGameRoom] Good performance! Would add basic items and small health boost`);
        } else {
            console.log(`[ArcadeGameRoom] Participation reward! Would add small health boost`);
        }
        
        // Combo bonuses
        if (result.maxCombo >= 50) {
            console.log(`[ArcadeGameRoom] Master combo! Would add special combo-based reward`);
        }
    }

    /**
     * Handle enemy defeat - no enemies in this room, just the rhythm game
     */
    handleEnemyDefeat(enemyId) {
        console.log(`[ArcadeGameRoom] No enemies in arcade room to defeat: ${enemyId}`);
        // This room doesn't have traditional enemies, just the arcade game
        // Any "enemies" are part of the rhythm game minigame
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification  
    id: "arcade_game",
    name: "Rhythmic Challenge - The Retro Arcade",
    isMultiRoom: false,
    description: "A retro arcade room with a glowing machine that challenges rhythm and timing",
    tags: ["arcade", "rhythm", "skill", "retro", "neon", "electronic"],
    
    // Room configuration
    shape: "SQUARE_1X1", // Smaller intimate arcade room
    
    // Door configuration - arcade room supports all entrances
    availableConnections: {
        north: true,  // Can be entered from north (door on south wall)
        south: true,  // Can be entered from south (door on north wall)
        east: true,   // Can be entered from east (door on west wall)
        west: true    // Can be entered from west (door on east wall)
    },
    
    // Door positions for SQUARE_1X1 room (14x14 units, doors at ±7)
    doorPositions: {
        north: { x: 0, y: 0, z: -7 },  // Door on south wall when entered from north
        south: { x: 0, y: 0, z: 7 },   // Door on north wall when entered from south
        east: { x: -7, y: 0, z: 0 },   // Door on west wall when entered from east
        west: { x: 7, y: 0, z: 0 }     // Door on east wall when entered from west
    },
    
    // Using existing materials with neon/electronic tinting
    materials: {
        walls: "stonebrick", // Use existing stone brick walls
        floors: "stone_floor", // Use existing stone floor
        wallTint: 0x1A1A2A, // Dark blue-purple tint for electronic feel
        floorTint: 0x0A0A1A  // Very dark blue-purple for floor
    },

    // Static brightness - neon atmosphere but still visible
    staticBrightness: 4, // Darker for neon effect but still playable (4/10)
    
    // Neon electronic lighting
    lighting: {
        ambient: {
            intensity: 0.3, // Dim ambient for neon contrast
            color: 0x2A2A4A // Dark blue-purple ambient
        },
        arcadeGlow: {
            position: { x: 0, y: 4, z: 0 }, // Above arcade machine
            intensity: 6.0, // Bright neon glow
            color: 0x00DDFF, // Bright cyan arcade glow
            distance: 25,
            decay: 1.0
        },
        neonAccent1: {
            position: { x: -6, y: 3, z: -6 }, // Corner neon
            intensity: 3.0,
            color: 0xFF0088, // Pink neon
            distance: 15,
            decay: 1.5
        },
        neonAccent2: {
            position: { x: 6, y: 3, z: 6 }, // Corner neon
            intensity: 3.0,
            color: 0x8800FF, // Purple neon
            distance: 15,
            decay: 1.5
        },
        electronicGlow: {
            position: { x: 0, y: 2, z: -6 }, // Back wall glow
            intensity: 2.5,
            color: 0x00FF44, // Green electronic glow
            distance: 18,
            decay: 1.8
        }
    },

    // Object placement - arcade room with retro-futuristic aesthetic
    objects: [
        // === CENTRAL ARCADE MACHINE (MAIN INTERACTION) ===
        {
            type: "arcade_machine", // Custom arcade machine with glowing screen
            position: { x: 0, y: 0, z: 0 }, // Center of room
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 4.0, y: 4.0, z: 4.0 },
            userData: {
                objectId: "arcade_machine",
                isInteractable: true,
                interactionType: "arcade_machine",
                isDecorative: false,
                interaction: {
                    type: "rhythm_arcade",
                    id: "arcade_machine"
                }
            }
        },

        // === RITUAL CIRCLES (Electronic power sources) ===
        {
            type: "ritual_circle", // Electronic ritual circles
            position: { x: -3, y: 0, z: -3 }, // Around arcade machine
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.2 },
            userData: {
                objectId: "electronic_circle_nw",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x00FFFF // Cyan electronic glow
            }
        },
        {
            type: "ritual_circle", // Electronic ritual circles
            position: { x: 3, y: 0, z: -3 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.2 },
            userData: {
                objectId: "electronic_circle_ne",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0xFF0088 // Pink electronic glow
            }
        },
        {
            type: "ritual_circle", // Electronic ritual circles
            position: { x: -3, y: 0, z: 3 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.2 },
            userData: {
                objectId: "electronic_circle_sw",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x8800FF // Purple electronic glow
            }
        },
        {
            type: "ritual_circle", // Electronic ritual circles
            position: { x: 3, y: 0, z: 3 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.2 },
            userData: {
                objectId: "electronic_circle_se",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x00FF44 // Green electronic glow
            }
        },

        // === TORCHES (Neon-like electronic light sources) ===
        {
            type: "torch", // Use existing torch type
            position: { x: -5, y: 0, z: -5 }, // Corner positions
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "neon_torch_nw",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x00FFFF // Cyan glow
            }
        },
        {
            type: "torch", // Use existing torch type
            position: { x: 5, y: 0, z: -5 },
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "neon_torch_ne",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0xFF0088 // Pink glow
            }
        },
        {
            type: "torch", // Use existing torch type
            position: { x: -5, y: 0, z: 5 },
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "neon_torch_sw",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x8800FF // Purple glow
            }
        },
        {
            type: "torch", // Use existing torch type
            position: { x: 5, y: 0, z: 5 },
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "neon_torch_se",
                isDecorative: true,
                hasGlowAnimation: true,
                glowColor: 0x00FF44 // Green glow
            }
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: ArcadeGameRoom
};