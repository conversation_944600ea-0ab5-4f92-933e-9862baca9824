import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { createUnrealFog } from '../../effects/VolumetricFogManager.js';
import { createFloorMistPlanes } from '../../effects/FloorMistPlane.js';
import { ChessGame } from '../../systems/ChessGame.js';

/**
 * Devil's Chess Room Event Room - "Game of Desire"
 *
 * A hellish chess room where the player faces the Devil in a game of strategy.
 * Before entering the main chamber, the player must pass through two dark corridors
 * with combat encounters that test focus and stamina.
 * 
 * The central chamber features a massive obsidian chess table where the Devil,
 * appearing as a shadow silhouette with glowing yellow eyes, waits to play.
 * The player's choice of desired reward affects the chess AI difficulty.
 *
 * THEME: Temptation, strategy, risk vs reward, infernal bargains
 * ATMOSPHERE: Dark, ominous, luxurious hellish chamber with flickering flames
 */

/**
 * Devil's Chess Room Handler
 */
export class DevilsChessRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        
        // Extended state for chess game and devil interaction
        this.state = {
            ...this.state,
            // Game progression
            devilEncountered: false,
            chessGameActive: false,
            playerChoice: null,
            gameCompleted: false,
            // Atmospheric state
            fogSystem: null,
            fogAdded: false,
            hellishGlowActive: true,
            // Animation state
            torchFlickering: true,
            emberFalling: true,
            devilEyesGlowing: true,
            updateCount: 0,
            // Chess game state
            chessAIDifficulty: 'normal',
            chessBoardVisible: false,
            chessAnimationStartTime: 0
        };

        console.log(`[DevilsChessRoom] 🔥 Devil's Chess Chamber initialized for room ${roomId}`);
        
        // NOTE: Effect initialization moved to initialize() method to prevent race conditions
    }

    /**
     * Initialize room effects when player enters the room
     * This method is called when the room becomes active
     */
    initialize() {
        console.log(`[DevilsChessRoom] 🔥 Initializing Devil's Chess Room effects`);

        // Initialize hellish atmospheric effects immediately
        this.initializeHellishEffects();
        
        // Spawn warning enemies (dark corridor tests) immediately
        this.spawnCorridorWarnings();
        
        // Initialize hellish fog with delay like other rooms
        this.state.fogInitInterval = setInterval(() => {
            if (this.state.fogAdded) {
                clearInterval(this.state.fogInitInterval);
                this.state.fogInitInterval = null;
                return;
            }
            if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
                console.warn('[DevilsChessRoom] Delayed initFog trigger (room group now available)');
                this.initHellishFog();
                this.state.fogAdded = true;
                clearInterval(this.state.fogInitInterval);
                this.state.fogInitInterval = null;
            }
        }, 200);
    }

    /**
     * Clean up room resources and effects
     */
    cleanup() {
        super.cleanup(); // Call the base class method
        console.log(`[DevilsChessRoom] Cleaning up effects for room ${this.roomId}`);

        // 1. Clear the pending timer to prevent it from firing late.
        if (this.state.fogInitInterval) {
            clearInterval(this.state.fogInitInterval);
            this.state.fogInitInterval = null;
        }

        // 2. If the fog system was created, remove it.
        if (this.state.fogSystem && this.state.fogSystem.dispose) {
            this.state.fogSystem.dispose();
            this.state.fogSystem = null;
        }

        // 3. Remove any other added objects, like mist planes.
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            const toRemove = this.dungeonHandler.currentRoomGroup.children.filter(
                child => child.userData.isFloorMist // Assuming you add this identifier
            );
            toRemove.forEach(child => {
                this.dungeonHandler.currentRoomGroup.remove(child);
                if (child.geometry) child.geometry.dispose();
                if (child.material) child.material.dispose();
            });
        }

        this.state.fogAdded = false;
    }

    /**
     * Spawn warning enemies for atmospheric tension (corridor tests)
     */
    spawnCorridorWarnings() {
        console.log(`[DevilsChessRoom] ⚡ SPAWNING CORRIDOR WARNING ENCOUNTERS`);

        // Get room center position for accurate spawning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn 2 shadow enemies in the "dark corridors" before main encounter
        const warningSpawns = [
            {
                type: "firefly", // Shadow creatures
                position: {
                    x: roomCenterX - 8.0, // Far left corridor
                    y: 2.0,
                    z: roomCenterZ + 0.0
                },
                spawnDelay: 0.5
            },
            {
                type: "firefly", // Shadow creatures
                position: {
                    x: roomCenterX + 8.0, // Far right corridor
                    y: 2.0,
                    z: roomCenterZ + 0.0
                },
                spawnDelay: 1.0
            }
        ];

        this.spawnAmbientCreatures(warningSpawns);
    }

    /**
     * Spawn ambient creatures WITHOUT locking doors (like mysteriousPond)
     */
    spawnAmbientCreatures(ambientSpawns) {
        console.log(`[DevilsChessRoom] Spawning ${ambientSpawns.length} corridor warning creatures WITHOUT door locking`);

        try {
            // Spawn creatures WITHOUT hiding doors (custom behavior for ambient warnings)
            ambientSpawns.forEach((enemySpawn, index) => {
                setTimeout(() => {
                    this.spawnEnemy(enemySpawn);
                }, enemySpawn.spawnDelay * 1000);
            });
            
            this.state.enemiesSpawned = true;
            console.log(`[DevilsChessRoom] ✅ Successfully initiated warning creature spawning (doors remain visible)`);
        } catch (error) {
            console.error(`[DevilsChessRoom] ❌ Error spawning warning creatures:`, error);
        }
    }

    /**
     * Initialize hellish fog and atmospheric effects
     */
    initHellishFog() {
        console.log('[DevilsChessRoom] 🔥 Initializing hellish fog effects');
        
        if (!this.dungeonHandler?.currentRoomGroup) {
            console.warn('[DevilsChessRoom] No current room group for fog initialization');
            return;
        }

        try {
            // Create hellish fog with red-orange tint like mysterious pond but hellish
            this.state.fogSystem = createUnrealFog(
                this.dungeonHandler.scene,
                this.dungeonHandler.currentRoomGroup,
                {
                    preset: 'hellishChamber',
                    // Hellish red-orange fog with dark undertones
                    color: 0x8B0000, // Dark red hellish mist
                    density: 0.016,  // Slightly denser for ominous effect
                    godrayIntensity: 0.6, // Enhanced godrays for dramatic lighting
                    windSpeed: 0.3,  // Slow, heavy hellish atmosphere
                    turbulence: 0.8, // More chaotic movement
                    organicDistribution: true, // Use organic fog patches
                    quality: 'high'  // High quality for best visuals
                }
            );

            if (!this.state.fogSystem) {
                console.error(`[DevilsChessRoom] Failed to create hellish fog system!`);
                return;
            }

            console.log(`[DevilsChessRoom] ✅ Hellish fog system created successfully`);

            // Add floor mist for additional depth
            const mistPlanes = createFloorMistPlanes({
                count: 4,
                scale: 12,
                opacity: 0.25,
                color: 0x660000, // Dark red floor mist
                animationSpeed: 0.2
            });

            if (mistPlanes && Array.isArray(mistPlanes)) {
                mistPlanes.forEach(mist => {
                    this.dungeonHandler.currentRoomGroup.add(mist);
                });
                console.log('[DevilsChessRoom] ✅ Hellish floor mist planes added');
            }

        } catch (error) {
            console.error('[DevilsChessRoom] Error creating hellish fog effects:', error);
        }
    }

    /**
     * Initialize hellish atmospheric effects
     */
    initializeHellishEffects() {
        console.log('[DevilsChessRoom] 🔥 Hellish chamber effects initialized');
        // This would set up torch flickering, ember particle effects, etc.
    }

    /**
     * Update method called every frame for animations
     */
    update(deltaTime) {
        // Debug logging for first few updates
        if (!this.state.updateCount) this.state.updateCount = 0;
        this.state.updateCount++;

        if (this.state.updateCount <= 5) {
            console.log(`[DevilsChessRoom] Update #${this.state.updateCount} - deltaTime: ${deltaTime}`);
        }

        // Initialize fog on first update when room is ready
        if (!this.state.fogAdded && this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            console.log(`[DevilsChessRoom] 🔥 Room ready - initializing hellish fog now`);
            this.initHellishFog();
            this.state.fogAdded = true;
        }

        // Update fog animation
        if (this.state.fogSystem) {
            this.state.fogSystem.update(deltaTime);
        }

        // Animate hellish torch flickering
        if (this.state.torchFlickering && this.dungeonHandler?.currentRoomGroup) {
            this.updateTorchFlickering(deltaTime);
        }

        // Animate devil's glowing eyes
        if (this.state.devilEyesGlowing && this.dungeonHandler?.currentRoomGroup) {
            this.updateDevilEyeGlow(deltaTime);
        }
    }

    /**
     * Update torch flickering animation
     */
    updateTorchFlickering(deltaTime) {
        const currentTime = Date.now();
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        
        roomGroup.traverse(child => {
            if (child.name === 'hellish_torch' && child.userData?.isFlickering) {
                // Create realistic torch flicker effect
                const flickerSpeed = 0.005;
                const flickerAmplitude = 0.3;
                const baseIntensity = child.userData.baseIntensity || 1.0;
                
                const flicker = Math.sin(currentTime * flickerSpeed) * flickerAmplitude;
                const newIntensity = baseIntensity + flicker;
                
                // Apply flicker to any lights in the torch
                child.traverse(light => {
                    if (light.isLight) {
                        light.intensity = Math.max(0.1, newIntensity);
                    }
                });
            }
        });
    }

    /**
     * Update devil's eye glow animation
     */
    updateDevilEyeGlow(deltaTime) {
        const currentTime = Date.now();
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        
        roomGroup.traverse(child => {
            if (child.name === 'devil_throne' && child.userData?.hasEyeGlow) {
                // Create menacing eye glow pulse
                const pulseSpeed = 0.003;
                const pulseAmplitude = 0.4;
                const baseBrightness = 1.0;
                
                const pulse = Math.sin(currentTime * pulseSpeed) * pulseAmplitude;
                const eyeGlow = baseBrightness + pulse;
                
                // Apply to devil's eyes (stored as emissive materials)
                child.traverse(eyeMesh => {
                    if (eyeMesh.userData?.isDevilEye && eyeMesh.material) {
                        const emissiveIntensity = Math.max(0.5, eyeGlow);
                        eyeMesh.material.emissiveIntensity = emissiveIntensity;
                    }
                });
            }
        });
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[DevilsChessRoom] 🎯 Handling interaction with ${objectId}`);
        console.log(`[DevilsChessRoom] Current state before interaction:`, this.state);

        // Check for devil throne interaction (main encounter)
        if (objectId === 'devil_throne' ||
            objectId === 'chess_table' ||
            objectId.includes('devil')) {
            await this.handleDevilEncounter();
        } else {
            console.warn(`[DevilsChessRoom] Unknown object interaction: ${objectId} - Only devil/chess table is interactive`);
        }
    }

    /**
     * Handle custom interactions specific to Devil's Chess Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[DevilsChessRoom] 🎯 Handling custom interaction:`, interaction);
        console.log(`[DevilsChessRoom] Current state before interaction:`, this.state);

        switch (interaction.type) {
            case 'devil_chess':
                await this.handleDevilEncounter();
                break;
            case 'devil_throne':
                await this.handleDevilEncounter();
                break;
            default:
                console.warn(`[DevilsChessRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle Devil encounter - main dialogue and choice system
     */
    async handleDevilEncounter() {
        console.log(`[DevilsChessRoom] 😈 Devil encounter started`);

        if (this.state.gameCompleted) {
            console.log(`[DevilsChessRoom] ⚠️ Game already completed - Devil cannot be approached again`);
            return;
        }

        if (this.state.devilEncountered) {
            console.log(`[DevilsChessRoom] ⚠️ Devil already encountered - game in progress`);
            return;
        }

        // Mark encounter as started
        this.state.devilEncountered = true;
        console.log(`[DevilsChessRoom] 😈 DEVIL ENCOUNTER INITIATED`);

        // Play ominous sound
        this.playAudio("nightmare_chest");

        // Show initial devil dialogue
        await this.showDevilGreeting();
    }

    /**
     * Show Devil's greeting - intimidation first
     */
    async showDevilGreeting() {
        console.log(`[DevilsChessRoom] 😈 Showing Devil's greeting dialogue`);

        const greetingData = {
            lines: [
                "You look into the face of the Devil...",
                "The Devil grins, his yellow eyes gleaming with ancient malice.",
                "\"Well, well... another mortal seeks to test their fate.\""
            ],
            options: [
                { text: "Continue", value: "continue" }
            ]
        };

        await this.showDialogue(greetingData);

        // Go directly to final choice (Talk/Play/Walk Away)
        await this.showFinalChoice();
    }

    /**
     * Show the desire choice menu (affects chess difficulty) - only when player chooses to play
     */
    async showDesireChoiceMenu() {
        console.log(`[DevilsChessRoom] 💰 Showing desire choice menu`);

        const choiceData = {
            lines: [
                "\"Excellent... Now then, what do you desire?\"",
                "\"Choose your heart's desire carefully...\"",
                "\"Greater greed demands greater skill at the board.\""
            ],
            options: [
                { text: "🩸 5 Hearts", value: "hearts_5" },
                { text: "🩸 10 Hearts", value: "hearts_10" },
                { text: "🩸 20 Hearts", value: "hearts_20" },
                { text: "🩸 50 Hearts (Very Greedy)", value: "hearts_50" },
                { text: "🧪 Rare Items", value: "items_rare" },
                { text: "⚡ Power Boost", value: "power_boost" }
            ]
        };

        const choice = await this.showDialogue(choiceData);
        console.log(`[DevilsChessRoom] Player's desire choice:`, choice);

        if (choice) {
            this.state.playerChoice = choice;
            await this.processDesireChoice(choice);
        } else {
            console.log(`[DevilsChessRoom] No choice made - resetting encounter`);
            this.state.devilEncountered = false;
        }
    }

    /**
     * Process the player's desire choice and set chess difficulty
     */
    async processDesireChoice(choice) {
        console.log(`[DevilsChessRoom] 🎯 Processing desire choice: ${choice}`);

        // Set chess AI difficulty based on greed level
        switch (choice) {
            case 'hearts_5':
                this.state.chessAIDifficulty = 'easy';
                break;
            case 'hearts_10':
                this.state.chessAIDifficulty = 'normal';
                break;
            case 'hearts_20':
                this.state.chessAIDifficulty = 'hard';
                break;
            case 'hearts_50':
                this.state.chessAIDifficulty = 'nightmare';
                break;
            case 'items_rare':
                this.state.chessAIDifficulty = 'normal';
                break;
            case 'power_boost':
                this.state.chessAIDifficulty = 'hard';
                break;
            default:
                this.state.chessAIDifficulty = 'normal';
        }

        console.log(`[DevilsChessRoom] Chess AI difficulty set to: ${this.state.chessAIDifficulty}`);

        // Start the chess game immediately after choosing reward
        await this.handleChessGame();
    }

    /**
     * Show final choice - Talk/Play/Walk Away
     */
    async showFinalChoice() {
        console.log(`[DevilsChessRoom] 😈 Showing final choice dialogue`);

        const finalData = {
            lines: [
                "\"What will it be, mortal?\""
            ],
            options: [
                { text: "Talk", value: "talk" },
                { text: "Play", value: "play" },
                { text: "Walk Away", value: "walk_away" }
            ]
        };

        const finalChoice = await this.showDialogue(finalData);
        console.log(`[DevilsChessRoom] Final choice:`, finalChoice);

        switch (finalChoice) {
            case 'talk':
                await this.handleDevilTalk();
                break;
            case 'play':
                await this.showDesireChoiceMenu(); // Only ask for reward when committing to play
                break;
            case 'walk_away':
                await this.handleWalkAway();
                break;
            default:
                console.log(`[DevilsChessRoom] Unknown final choice - resetting encounter`);
                this.state.devilEncountered = false;
        }
    }

    /**
     * Handle devil conversation
     */
    async handleDevilTalk() {
        console.log(`[DevilsChessRoom] 💬 Devil conversation initiated`);

        const talkData = {
            lines: [
                "\"I have played this game for millennia...\"",
                "\"Kings and beggars, all have sat across from me.\"",
                "\"Some left with their desires fulfilled...\"",
                "\"Others... well, they learned the price of greed.\""
            ],
            options: [
                { text: "Continue", value: "continue" }
            ]
        };

        await this.showDialogue(talkData);

        // Return to final choice
        await this.showFinalChoice();
    }

    /**
     * Handle chess game initiation
     */
    async handleChessGame() {
        console.log(`[DevilsChessRoom] ♟️ CHESS GAME INITIATED`);

        this.state.chessGameActive = true;

        // Show game start dialogue
        const gameStartData = {
            lines: [
                "\"Excellent...\"",
                "The obsidian chess board materializes before you.",
                "\"Let the game begin!\""
            ],
            options: [
                { text: "Begin Chess Game", value: "start_chess" }
            ]
        };

        await this.showDialogue(gameStartData);

        // Transition to fullscreen chess mode
        await this.launchChessGame();
    }

    /**
     * Handle walking away from the devil
     */
    async handleWalkAway() {
        console.log(`[DevilsChessRoom] 🚶 Player chose to walk away`);

        const walkAwayData = {
            lines: [
                "\"Wise... perhaps.\"",
                "The Devil's laughter echoes as you turn away...",
                "\"The door remains open, should you change your mind.\""
            ],
            options: [
                { text: "Leave", value: "leave" }
            ]
        };

        await this.showDialogue(walkAwayData);

        // Reset encounter state - allow retry
        this.state.devilEncountered = false;
        this.state.playerChoice = null;

        console.log(`[DevilsChessRoom] ✅ Encounter reset - player can retry`);
    }

    /**
     * Launch the fullscreen chess game
     */
    async launchChessGame() {
        console.log(`[DevilsChessRoom] ♟️ Launching 3D chess game with difficulty: ${this.state.chessAIDifficulty}`);

        // Get the scene manager from dungeonHandler
        const sceneManager = this.dungeonHandler ? this.dungeonHandler.sceneManager : null;
        
        if (!sceneManager) {
            console.error('[DevilsChessRoom] No scene manager available for chess game');
            throw new Error('Scene manager not available');
        }

        // Create and start the actual chess game with proper scene manager and dungeon handler
        const chessGame = new ChessGame(sceneManager, this.state.chessAIDifficulty, this.dungeonHandler);
        const gameResult = await chessGame.startGame();
        
        await this.handleChessGameResult(gameResult);
    }

    /**
     * Simulate chess game (placeholder for actual chess implementation)
     */
    async simulateChessGame() {
        console.log(`[DevilsChessRoom] 🎲 Simulating chess game...`);

        // Show chess game in progress
        const gameProgressData = {
            lines: [
                "The chess pieces move with supernatural precision...",
                "The Devil's strategy is as old as time itself...",
                "The game reaches its climax..."
            ],
            options: [
                { text: "Continue", value: "continue" }
            ]
        };

        await this.showDialogue(gameProgressData);

        // Simulate game result based on difficulty
        const winChance = this.getWinChanceByDifficulty();
        const won = Math.random() < winChance;

        console.log(`[DevilsChessRoom] Chess game result: ${won ? 'WIN' : 'LOSE'} (chance: ${winChance})`);

        return {
            won: won,
            difficulty: this.state.chessAIDifficulty,
            playerChoice: this.state.playerChoice
        };
    }

    /**
     * Get win chance based on AI difficulty
     */
    getWinChanceByDifficulty() {
        switch (this.state.chessAIDifficulty) {
            case 'easy': return 0.8;      // 80% win chance
            case 'normal': return 0.6;    // 60% win chance  
            case 'hard': return 0.4;      // 40% win chance
            case 'nightmare': return 0.2; // 20% win chance
            default: return 0.6;
        }
    }

    /**
     * Handle chess game result
     */
    async handleChessGameResult(result) {
        console.log(`[DevilsChessRoom] 🏆 Handling chess game result:`, result);

        this.state.chessGameActive = false;
        this.state.gameCompleted = true;

        if (result.won) {
            await this.handleVictory(result);
        } else {
            await this.handleDefeat(result);
        }
    }

    /**
     * Handle victory over the Devil
     */
    async handleVictory(result) {
        console.log(`[DevilsChessRoom] 🎉 VICTORY! Player defeated the Devil`);

        const victoryData = {
            lines: [
                "\"Impossible...\"",
                "The Devil's eyes flicker with surprise and... respect?",
                "\"You have earned your reward, mortal.\"",
                "\"Take what you have won... you've beaten the Devil at his own game.\""
            ],
            options: [
                { text: "Claim Victory", value: "claim" }
            ]
        };

        await this.showDialogue(victoryData);

        // Spawn reward based on player's choice
        await this.spawnVictoryReward(result);

        // Show doors and allow exit
        this.showEventRoomDoors();
    }

    /**
     * Handle defeat by the Devil
     */
    async handleDefeat(result) {
        console.log(`[DevilsChessRoom] 💀 DEFEAT! Devil won the chess game`);

        const defeatData = {
            lines: [
                "\"As I expected...\"",
                "The Devil's laughter fills the chamber.",
                "\"You may leave... but empty-handed.\"",
                "\"Perhaps next time you'll be more... modest in your desires.\""
            ],
            options: [
                { text: "Accept Defeat", value: "accept" }
            ]
        };

        await this.showDialogue(defeatData);

        // No reward for defeat, but allow exit
        this.showEventRoomDoors();
    }

    /**
     * Spawn victory reward based on player's choice
     */
    async spawnVictoryReward(result) {
        console.log(`[DevilsChessRoom] 🎁 Spawning victory reward for choice: ${result.playerChoice}`);

        // Get room center position for chest placement
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn reward chest at chess table
        const chestConfig = {
            position: {
                x: roomCenterX + 0.0, // Chess table center
                y: 1.0, // Elevated on table
                z: roomCenterZ + 0.0
            },
            chestType: "devils_victory_chest"
        };

        console.log(`[DevilsChessRoom] Victory chest spawn position:`, chestConfig.position);

        // Spawn the victory chest
        await this.spawnChest(chestConfig);

        // TODO: Apply actual rewards based on playerChoice
        // This would integrate with the game's item/health systems
        await this.applyRewardEffects(result.playerChoice);
    }

    /**
     * Apply reward effects to player
     */
    async applyRewardEffects(choice) {
        console.log(`[DevilsChessRoom] ⚡ Applying reward effects for choice: ${choice}`);

        // This would integrate with the game's systems to apply actual rewards
        // For now, just log what would happen
        switch (choice) {
            case 'hearts_5':
                console.log(`[DevilsChessRoom] Would add 5 hearts to player`);
                break;
            case 'hearts_10':
                console.log(`[DevilsChessRoom] Would add 10 hearts to player`);
                break;
            case 'hearts_20':
                console.log(`[DevilsChessRoom] Would add 20 hearts to player`);
                break;
            case 'hearts_50':
                console.log(`[DevilsChessRoom] Would add 50 hearts to player`);
                break;
            case 'items_rare':
                console.log(`[DevilsChessRoom] Would add rare items to player inventory`);
                break;
            case 'power_boost':
                console.log(`[DevilsChessRoom] Would apply 20% speed boost to player`);
                break;
        }
    }

    /**
     * Handle enemy defeat - only for corridor warning creatures
     */
    handleEnemyDefeat(enemyId) {
        console.log(`[DevilsChessRoom] Enemy ${enemyId} defeated`);

        // Only react to warning creatures, not the main devil encounter
        // The devil encounter is handled through dialogue, not combat
        
        // Don't call super.handleEnemyDefeat() to prevent automatic door showing
        // Doors should only show after chess game completion
        
        console.log(`[DevilsChessRoom] Warning creature defeated - doors remain controlled by chess game`);
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification  
    id: "devils_chess_room",
    name: "Game of Desire - The Devil's Chess Room",
    isMultiRoom: true, // Multi-room event sequence
    description: "Two dark corridors lead to a hellish chess chamber where the Devil awaits challengers",
    tags: ["devil", "chess", "choice", "strategy", "infernal", "corridor"],
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: false,    // Multi-room corridor doesn't support east entrance
        west: false     // Multi-room corridor doesn't support west entrance
    },
    primaryEntrance: "north", // Preferred entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -7 },   // Door on south wall when entered from north
        south: { x: 0, y: 0, z: 7 },    // Door on north wall when entered from south
        east: null,    // Not supported
        west: null     // Not supported
    },
    
    // Multi-room sequence: Dark Corridor 1 → Dark Corridor 2 → Main Chess Chamber
    roomSequence: [
        {
            id: "dark_corridor_1", 
            name: "Dark Corridor - First Trial",
            shape: "CORRIDOR_LONG", // First narrow corridor
            type: "corridor",
            description: "A narrow, dark corridor with ominous shadows",
            enemies: ["firefly", "firefly"], // Two shadow creatures for focus test
            staticBrightness: 2, // Very dark (2/10)
            lighting: {
                ambient: { intensity: 0.2, color: 0x2A0A0A }, // Dim red ambient
                torchLight: {
                    position: { x: 0, y: 3, z: 0 },
                    intensity: 1.5, color: 0xFF4500, distance: 12, decay: 2.0
                }
            },
            materials: {
                walls: "stone_brick", floors: "stone_floor",
                wallTint: 0x1A0A0A, floorTint: 0x0A0505 // Dark red tints
            }
        },
        {
            id: "dark_corridor_2",
            name: "Dark Corridor - Second Trial", 
            shape: "CORRIDOR_LONG", // Second narrow corridor
            type: "corridor",
            description: "Another dark corridor, shadows seem to move",
            enemies: ["firefly", "firefly"], // Two more shadow creatures for stamina test
            staticBrightness: 2, // Very dark (2/10)
            lighting: {
                ambient: { intensity: 0.2, color: 0x2A0A0A }, // Dim red ambient
                torchLight: {
                    position: { x: 0, y: 3, z: 0 },
                    intensity: 1.5, color: 0xFF4500, distance: 12, decay: 2.0
                }
            },
            materials: {
                walls: "stone_brick", floors: "stone_floor", 
                wallTint: 0x1A0A0A, floorTint: 0x0A0505 // Dark red tints
            }
        },
        {
            id: "main_chess_chamber",
            name: "The Devil's Chess Chamber",
            shape: "SQUARE_2X2", // Large main chamber
            type: "main_event",
            description: "The Devil's chess chamber with obsidian table and throne",
            // Main chamber content (current room data)
        }
    ],
    
    // How the rooms connect in sequence
    connections: [
        { from: "dark_corridor_1", to: "dark_corridor_2", direction: "e" },
        { from: "dark_corridor_2", to: "main_chess_chamber", direction: "e" }
    ],
    
    // Main chamber configuration (current room settings)
    shape: "SQUARE_2X2", // Large chamber like mysterious pond
    
    // Using existing materials with hellish tinting
    materials: {
        walls: "stonebrick", // Use existing stone brick walls
        floors: "stone_floor", // Use existing stone floor
        wallTint: 0x2A1010, // Dark red-black tint
        floorTint: 0x1A0A0A  // Very dark red-black for floor
    },

    // Static brightness - hellish but visible atmosphere
    staticBrightness: 6, // Brighter hellish atmosphere for visibility (6/10)
    
    // Hellish lighting with flickering flames - brightened for visibility
    lighting: {
        ambient: {
            intensity: 0.6, // Much brighter ambient for visibility
            color: 0x6A2A2A // Lighter red ambient
        },
        hellishGlow: {
            position: { x: 0, y: 6, z: 0 }, // High above center
            intensity: 7.0, // Much brighter than pond's main light for visibility
            color: 0xFF4500, // Brighter orange-red hellish glow
            distance: 30,
            decay: 0.8 // Even less decay for maximum coverage
        },
        tableSpotlight: {
            position: { x: 0, y: 4, z: 0 }, // Above chess table
            intensity: 4.5, // Tripled intensity for better visibility
            color: 0xFF6600, // Brighter orange flame light
            distance: 25,
            decay: 1.2 // Less decay for better coverage
        },
        devilThrone: {
            position: { x: 0, y: 3, z: 8 }, // Behind devil's position
            intensity: 4.0, // Much brighter for devil area visibility
            color: 0xFFD700, // Golden light for devil's eyes
            distance: 20,
            decay: 1.5 // Less decay for better coverage
        },
        torchLight1: {
            position: { x: -10, y: 3, z: -10 }, // Corner torch lighting
            intensity: 3.0, // Brighter corner lighting
            color: 0xFF8C00, // Bright orange torch light
            distance: 22,
            decay: 1.8 // Less decay for better coverage
        },
        torchLight2: {
            position: { x: 10, y: 3, z: -10 }, // Corner torch lighting
            intensity: 3.0, // Brighter corner lighting
            color: 0xFF8C00, // Bright orange torch light
            distance: 22,
            decay: 1.8 // Less decay for better coverage
        }
    },

    // Object placement - rich atmospheric density like mysterious pond
    objects: [
        // === CENTRAL CHESS TABLE (MAIN INTERACTION) - Custom obsidian chess table ===
        {
            type: "obsidian_chess_table", // Custom devil's chess table
            position: { x: 0, y: 0, z: 0 }, // Center of room
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "chess_table",
                isInteractable: true,
                interactionType: "chess_table",
                isDecorative: false,
                interaction: {
                    type: "devil_chess",
                    id: "chess_table"
                }
            }
        },


        // === HELLISH TORCHES (Atmospheric lighting) - Custom hellish torches ===
        {
            type: "hellish_torch", // Custom hellish torch with flickering flames
            position: { x: -10, y: 0, z: -10 }, // Corner torches
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_nw",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },
        {
            type: "hellish_torch", // Custom hellish torch
            position: { x: 10, y: 0, z: -10 }, // Corner torches
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_ne",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },
        {
            type: "hellish_torch", // Custom hellish torch
            position: { x: -10, y: 0, z: 10 }, // Corner torches
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_sw",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },
        {
            type: "hellish_torch", // Custom hellish torch
            position: { x: 10, y: 0, z: 10 }, // Corner torches
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_se",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },

        // === OBSIDIAN PILLARS (Architectural support) - Custom obsidian pillars ===
        {
            type: "obsidian_pillar", // Custom obsidian pillar with volcanic details
            position: { x: -8, y: 0, z: -8 }, // Supporting pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "pillar_nw",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Custom obsidian pillar
            position: { x: 8, y: 0, z: -8 }, // Supporting pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "pillar_ne",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Custom obsidian pillar
            position: { x: -8, y: 0, z: 8 }, // Supporting pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "pillar_sw",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Custom obsidian pillar
            position: { x: 8, y: 0, z: 8 }, // Supporting pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "pillar_se",
                isDecorative: true
            }
        },

        // === CONFESSION STONES (Soul altars) - Custom confession altars ===
        {
            type: "confession_stone", // Custom confession stone with soul crystals
            position: { x: -6, y: 0, z: -6 }, // Corner soul altars
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "confession_nw",
                isInteractable: true,
                interactionType: "confession_altar",
                isDecorative: false,
                hasSoulAnimation: true
            }
        },
        {
            type: "confession_stone", // Custom confession stone
            position: { x: 6, y: 0, z: -6 }, // Corner soul altars
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "confession_ne",
                isInteractable: true,
                interactionType: "confession_altar",
                isDecorative: false,
                hasSoulAnimation: true
            }
        },

        // === FLOATING SOUL LANTERNS (Ethereal lighting) - Custom soul lanterns ===
        {
            type: "soul_lantern", // Custom floating soul lantern
            position: { x: -5, y: 3, z: 0 }, // Floating at medium height
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "lantern_west",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },
        {
            type: "soul_lantern", // Custom floating soul lantern
            position: { x: 5, y: 3, z: 0 }, // Floating at medium height
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "lantern_east",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },
        {
            type: "soul_lantern", // Custom floating soul lantern
            position: { x: 0, y: 4, z: -4 }, // Higher floating lantern
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.8, y: 0.8, z: 0.8 },
            userData: {
                objectId: "lantern_north",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },

        // === ADDITIONAL HELLISH TORCHES (Perimeter lighting) ===
        {
            type: "hellish_torch", // Additional perimeter torches
            position: { x: -12, y: 0, z: 0 }, // Side wall torches
            rotation: { x: 0, y: Math.PI / 2, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_west",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },
        {
            type: "hellish_torch", // Additional perimeter torches
            position: { x: 12, y: 0, z: 0 }, // Side wall torches
            rotation: { x: 0, y: -Math.PI / 2, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_east",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },
        {
            type: "hellish_torch", // Additional perimeter torches
            position: { x: 0, y: 0, z: -12 }, // Front wall torch
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "torch_north",
                isDecorative: true,
                hasFlameAnimation: true
            }
        },

        // === MORE CONFESSION STONES (Soul collection areas) ===
        {
            type: "confession_stone", // Additional confession altars
            position: { x: -6, y: 0, z: 6 }, // South corner altars
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 0.8, z: 0.8 },
            userData: {
                objectId: "confession_sw",
                isInteractable: true,
                interactionType: "confession_altar",
                isDecorative: false,
                hasSoulAnimation: true
            }
        },
        {
            type: "confession_stone", // Additional confession altars
            position: { x: 6, y: 0, z: 6 }, // South corner altars
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 0.8, z: 0.8 },
            userData: {
                objectId: "confession_se",
                isInteractable: true,
                interactionType: "confession_altar",
                isDecorative: false,
                hasSoulAnimation: true
            }
        },

        // === MORE SOUL LANTERNS (Floating ethereal lighting) ===
        {
            type: "soul_lantern", // Additional floating lanterns
            position: { x: -3, y: 4.5, z: 3 }, // High floating positions
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.7, z: 0.7 },
            userData: {
                objectId: "lantern_sw_high",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },
        {
            type: "soul_lantern", // Additional floating lanterns
            position: { x: 3, y: 4.5, z: 3 }, // High floating positions
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.7, z: 0.7 },
            userData: {
                objectId: "lantern_se_high",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },
        {
            type: "soul_lantern", // Central high lantern
            position: { x: 0, y: 5, z: 2 }, // Highest central position
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.6, y: 0.6, z: 0.6 },
            userData: {
                objectId: "lantern_center_high",
                isDecorative: true,
                isFloating: true,
                hasFloatingAnimation: true,
                hasSoulAnimation: true
            }
        },

        // === SMALLER OBSIDIAN PILLARS (Detail pillars) ===
        {
            type: "obsidian_pillar", // Smaller detail pillars
            position: { x: -4, y: 0, z: -10 }, // Wall detail pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.8, z: 0.7 },
            userData: {
                objectId: "detail_pillar_nw",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Smaller detail pillars
            position: { x: 4, y: 0, z: -10 }, // Wall detail pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.8, z: 0.7 },
            userData: {
                objectId: "detail_pillar_ne",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Smaller detail pillars
            position: { x: -10, y: 0, z: -4 }, // Side wall pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.8, z: 0.7 },
            userData: {
                objectId: "detail_pillar_west",
                isDecorative: true
            }
        },
        {
            type: "obsidian_pillar", // Smaller detail pillars
            position: { x: 10, y: 0, z: -4 }, // Side wall pillars
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.7, y: 0.8, z: 0.7 },
            userData: {
                objectId: "detail_pillar_east",
                isDecorative: true
            }
        },







    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: DevilsChessRoom
};