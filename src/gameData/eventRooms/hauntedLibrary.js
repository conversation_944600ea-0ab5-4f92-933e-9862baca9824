import * as THREE from 'three';

/**
 * Haunted Library Event Room
 * 
 * A dark library with floating books and ancient tomes.
 * When the player approaches the central lectern, ghostly enemies appear.
 */

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "haunted_library",
    name: "Haunted Library",
    shape: "L_SHAPE_1X2",
    description: "A dark library filled with ancient knowledge and restless spirits",
    tags: ["spooky", "puzzle", "books"],
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "south", // Preferred entrance direction
    
    // Visual overrides
    materials: {
        walls: "stone_brick",
        floors: "wooden_floor",
        wallTint: 0x2D2D2D, // Dark gray
        floorTint: 0x4A3728 // Dark brown wood
    },

    // Static brightness override - dark spooky atmosphere
    staticBrightness: 3, // Dark (3/10) - spooky library with flickering candles
    
    // Dim lighting with flickering candles
    lighting: {
        ambient: {
            intensity: 0.1,
            color: 0x1A1A2E
        },
        candleLight: {
            position: { x: 0, y: 3, z: 0 },
            intensity: 1.2,
            color: 0xFF6B35, // Warm candle glow
            distance: 8,
            decay: 2
        }
    },
    
    // Object placement
    objects: [
        // Central lectern with ancient tome
        {
            type: "ancient_lectern",
            position: { x: 0, y: 0, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            userData: {
                objectId: "cursed_tome",
                isInteractable: true,
                interactionType: "lectern"
            }
        },
        // Bookshelves around the room
        {
            type: "tall_bookshelf",
            position: { x: -4, y: 0, z: -3 },
            rotation: { x: 0, y: Math.PI / 2, z: 0 },
            scale: { x: 1, y: 1.5, z: 1 }
        },
        {
            type: "tall_bookshelf",
            position: { x: 4, y: 0, z: -3 },
            rotation: { x: 0, y: -Math.PI / 2, z: 0 },
            scale: { x: 1, y: 1.5, z: 1 }
        },
        // Floating candles
        {
            type: "floating_candle",
            position: { x: -2, y: 4, z: 2 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
        },
        {
            type: "floating_candle",
            position: { x: 2, y: 4, z: 2 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
        }
    ],
    
    // Ghost enemies spawn when lectern is approached
    enemySpawns: [
        {
            type: "ghost", // New enemy type
            position: { x: -3, y: 2, z: -2 },
            spawnDelay: 0.5
        },
        {
            type: "ghost",
            position: { x: 3, y: 2, z: -2 },
            spawnDelay: 1.0
        },
        {
            type: "spectral_librarian", // Boss-type enemy
            position: { x: 0, y: 2, z: -4 },
            spawnDelay: 2.0
        }
    ],
    
    // Event mechanics
    mechanics: {
        triggerType: "object_interaction",
        triggerObjectId: "cursed_tome",
        onTrigger: {
            audio: "ghostly_whispers",
            spawnEnemies: false, // Spawn after dialogue
            dialogue: {
                lines: [
                    "An ancient tome lies open on the lectern...",
                    "The pages seem to glow with an otherworldly light.",
                    "Do you dare to read from it?"
                ],
                options: [
                    { text: "Read the tome", value: "read" },
                    { text: "Leave it alone", value: "leave" }
                ],
                onChoice: {
                    "read": {
                        spawnEnemies: true,
                        removeObject: null, // Keep the tome
                        onEnemyDefeat: {
                            spawnChest: {
                                position: { x: 0, y: 1, z: -2 },
                                chestType: "rare_chest"
                            },
                            audio: "knowledge_gained"
                        }
                    },
                    "leave": {
                        // Player can return and try again
                    }
                }
            }
        }
    }
};

// Export the room data with its ID as the key
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA
};
