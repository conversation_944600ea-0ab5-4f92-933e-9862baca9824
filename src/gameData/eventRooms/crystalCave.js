import * as THREE from 'three';

/**
 * Crystal Cave Event Room
 * 
 * A shimmering cave filled with magical crystals.
 * Player must activate crystal nodes in the correct sequence to unlock the treasure.
 */

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "crystal_cave",
    name: "Crystal Cave",
    shape: "CROSS_SHAPE",
    description: "A mystical cave where crystals pulse with ancient magic",
    tags: ["puzzle", "magic", "crystals"],
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "south", // Preferred entrance direction
    
    // Visual overrides
    materials: {
        walls: "cave_stone",
        floors: "crystal_floor",
        wallTint: 0x4A4A8A, // Purple tint
        floorTint: 0x6A5ACD // Slate blue
    },

    // Static brightness override - bright magical atmosphere
    staticBrightness: 8, // Bright (8/10) - crystals should glow and be clearly visible
    
    // Magical crystal lighting
    lighting: {
        ambient: {
            intensity: 0.2,
            color: 0x2E2E4A
        },
        crystalGlow: {
            position: { x: 0, y: 2, z: 0 },
            intensity: 2.0,
            color: 0x9370DB, // Medium slate blue
            distance: 12,
            decay: 1.5
        }
    },
    
    // Object placement - crystal nodes in cross pattern
    objects: [
        // Central crystal cluster (final activation)
        {
            type: "master_crystal",
            position: { x: 0, y: 1, z: 0 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.5, y: 1.5, z: 1.5 },
            userData: {
                objectId: "master_crystal",
                isInteractable: true,
                interactionType: "crystal",
                activationOrder: 5 // Last to activate
            }
        },
        // North crystal node
        {
            type: "crystal_node",
            position: { x: 0, y: 0.5, z: -4 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            userData: {
                objectId: "north_crystal",
                isInteractable: true,
                interactionType: "crystal",
                activationOrder: 1
            }
        },
        // East crystal node
        {
            type: "crystal_node",
            position: { x: 4, y: 0.5, z: 0 },
            rotation: { x: 0, y: Math.PI / 2, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            userData: {
                objectId: "east_crystal",
                isInteractable: true,
                interactionType: "crystal",
                activationOrder: 2
            }
        },
        // South crystal node
        {
            type: "crystal_node",
            position: { x: 0, y: 0.5, z: 4 },
            rotation: { x: 0, y: Math.PI, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            userData: {
                objectId: "south_crystal",
                isInteractable: true,
                interactionType: "crystal",
                activationOrder: 3
            }
        },
        // West crystal node
        {
            type: "crystal_node",
            position: { x: -4, y: 0.5, z: 0 },
            rotation: { x: 0, y: -Math.PI / 2, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            userData: {
                objectId: "west_crystal",
                isInteractable: true,
                interactionType: "crystal",
                activationOrder: 4
            }
        }
    ],
    
    // Crystal guardians spawn if wrong sequence
    enemySpawns: [
        {
            type: "crystal_golem",
            position: { x: -2, y: 0, z: -2 },
            spawnDelay: 0.5
        },
        {
            type: "crystal_golem",
            position: { x: 2, y: 0, z: -2 },
            spawnDelay: 1.0
        }
    ],
    
    // Event mechanics - complex puzzle system
    mechanics: {
        triggerType: "puzzle_sequence",
        triggerObjectId: "crystal_nodes",
        onTrigger: {
            audio: "crystal_chime",
            spawnEnemies: false, // Only if wrong sequence
            puzzle: {
                type: "sequence_activation",
                correctSequence: ["north_crystal", "east_crystal", "south_crystal", "west_crystal", "master_crystal"],
                onCorrectSequence: {
                    audio: "puzzle_solved",
                    spawnChest: {
                        position: { x: 0, y: 1, z: 0 },
                        chestType: "rare_chest"
                    },
                    animation: "crystal_harmony"
                },
                onWrongSequence: {
                    audio: "crystal_discord",
                    spawnEnemies: true,
                    resetPuzzle: true
                }
            }
        }
    }
};

// Export the room data with its ID as the key
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA
};
