import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { createUnrealFog } from '../../effects/VolumetricFogManager.js'; // Re-enabled for enhanced beauty
import { createFloorMistPlanes } from '../../effects/FloorMistPlane.js';

/**
 * The Chronal Anomaly Event Room - "The Chrono-Archaeologist's Laboratory"
 *
 * A tragic laboratory where an ancient time researcher became trapped in a temporal loop.
 * Their consciousness exists as a temporal echo, bound to their stone form.
 * The player discovers this forgotten place and must choose the archaeologist's fate.
 *
 * THEME: Temporal archaeology, lost knowledge, redemption through choice
 * ATMOSPHERE: Mysterious, melancholic, beautiful but haunting
 */

/**
 * Chronal Anomaly Room Handler
 */
export class ChronalAnomalyRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        
        // Extended state for the temporal transformation
        this.state = {
            ...this.state,
            // Transformation progression
            riftActivated: false,
            magicalTransformationActive: false,
            bossSpawned: false,
            // Atmospheric state
            fogSystem: null,
            fogAdded: false,
            // Animation state
            riftPulsing: false,
            temporalTraces: [],
            updateCount: 0,
            // Magical object animation tracking
            floatingObjects: [],
            animationStartTime: 0
        };

        console.log(`[ChronalAnomalyRoom] 🕰️ Chrono-archaeologist's laboratory initialized for room ${roomId}`);
        
        // Initialize atmospheric effects immediately
        this.initializeTemporalEffects();
        
        // Spawn temporal echoes (gentle ambiance) immediately when room is created
        this.spawnTemporalAmbiance();
        
        // Initialize fog with delay like pond room
        const fogInitInterval = setInterval(() => {
            if (this.state.fogAdded) {
                clearInterval(fogInitInterval);
                return;
            }
            if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
                console.warn('[ChronalAnomalyRoom] Delayed initFog trigger (room group now available)');
                this.initTemporalFog();
                this.state.fogAdded = true;
                clearInterval(fogInitInterval);
            }
        }, 200);
    }

    /**
     * Spawn gentle temporal echoes for atmospheric ambiance
     */
    spawnTemporalAmbiance() {
        console.log(`[ChronalAnomalyRoom] ✨ SPAWNING TEMPORAL ECHOES FOR ATMOSPHERE`);

        // Get room center position for accurate spawning
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn 3 fireflies as temporal echoes floating around gently
        const ambientSpawns = [
            {
                type: "firefly", // Temporal echoes
                position: {
                    x: roomCenterX + 3.0,
                    y: 2.5,
                    z: roomCenterZ + 2.0
                },
                spawnDelay: 0.3
            },
            {
                type: "firefly", // Temporal echoes
                position: {
                    x: roomCenterX - 2.5,
                    y: 3.0,
                    z: roomCenterZ - 1.5
                },
                spawnDelay: 0.6
            },
            {
                type: "firefly", // Temporal echoes
                position: {
                    x: roomCenterX + 1.0,
                    y: 2.2,
                    z: roomCenterZ - 3.0
                },
                spawnDelay: 0.9
            }
        ];

        this.spawnAmbientCreatures(ambientSpawns);
    }

    /**
     * Spawn ambient creatures WITHOUT locking doors
     * Similar to mysteriousPond.js approach
     */
    spawnAmbientCreatures(ambientSpawns) {
        console.log(`[ChronalAnomalyRoom] Spawning ${ambientSpawns.length} ambient creatures WITHOUT door locking`);

        try {
            // Spawn fireflies WITHOUT hiding doors (custom behavior for ambient creatures)
            // We don't use this.spawnEnemies() because it automatically hides doors
            // Instead, spawn each firefly individually without door management
            ambientSpawns.forEach((enemySpawn, index) => {
                setTimeout(() => {
                    this.spawnEnemy(enemySpawn);
                }, enemySpawn.spawnDelay * 1000);
            });
            
            this.state.enemiesSpawned = true;
            console.log(`[ChronalAnomalyRoom] ✅ Successfully initiated ambient creature spawning (doors remain visible)`);
        } catch (error) {
            console.error(`[ChronalAnomalyRoom] ❌ Error spawning ambient creatures:`, error);
        }
    }

    /**
     * Initialize temporal fog effects
     */
    initTemporalFog() {
        console.log('[ChronalAnomalyRoom] 🌫️ Initializing temporal fog effects');
        
        if (!this.dungeonHandler?.currentRoomGroup) {
            console.warn('[ChronalAnomalyRoom] No current room group for fog initialization');
            return;
        }

        try {
            // Create mystical fog like mysterious pond room
            this.state.fogSystem = createUnrealFog(
                this.dungeonHandler.scene,
                this.dungeonHandler.currentRoomGroup,
                {
                    preset: 'temporalMyst',
                    // Mystical purple-blue fog with temporal energy
                    color: 0x8A2BE2, // Blue-violet temporal mist
                    density: 0.018,  // Denser for more mystical effect
                    godrayIntensity: 0.7, // Enhanced godrays for magical effect
                    windSpeed: 0.5,  // Gentle temporal breeze
                    turbulence: 0.9, // More organic temporal distortion
                    organicDistribution: true, // Use organic fog patches like pond
                    quality: 'high'  // High quality for best visuals
                }
            );
            
            if (!this.state.fogSystem) {
                console.error('[ChronalAnomalyRoom] Failed to create temporal fog system!');
                return;
            }
            
            console.log('[ChronalAnomalyRoom] ✅ Enhanced temporal fog system created');

            // Add enhanced floor mist for atmospheric depth like mysterious pond
            const roomBounds = {
                minX: -10,
                maxX: 10,
                minZ: -10,
                maxZ: 10
            };

            const mistPlanes = createFloorMistPlanes(
                roomBounds,
                1.8,    // mistHeight - higher for more mystical effect
                5,      // layerCount - more layers for depth like pond
                0.06,   // opacity - more visible for mystical atmosphere
                [],     // floorSegments - empty, will use simple bounds
                this.dungeonHandler.scene
            );

            // Tint the mist with temporal purple-blue color
            mistPlanes.traverse(child => {
                if (child.isMesh && child.material) {
                    child.material.color.setHex(0x8A7CE8); // Mystical purple-blue
                }
            });

            // Add fog to the room
            this.dungeonHandler.currentRoomGroup.add(mistPlanes);
            console.log('[ChronalAnomalyRoom] ✅ Enhanced temporal floor mist added');

        } catch (error) {
            console.error('[ChronalAnomalyRoom] Error creating fog effects:', error);
        }
    }

    /**
     * Initialize temporal field effects
     */
    initializeTemporalEffects() {
        // This would be called to set up any ongoing temporal distortion effects
        // For now, just log that effects are initialized
        console.log('[ChronalAnomalyRoom] ⚡ Temporal field effects initialized');
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[ChronalAnomalyRoom] Handling interaction with ${objectId}`);

        // Check for temporal rift interaction (central purple object)
        if (objectId === 'central_temporal_rift' ||
            objectId === 'temporal_rift' ||
            objectId.includes('rift')) {
            await this.handleTemporalRiftActivation();
        } else {
            console.warn(`[ChronalAnomalyRoom] Unknown object interaction: ${objectId} - Only temporal rift is interactive`);
        }
    }

    /**
     * Handle custom interactions specific to Chronal Anomaly Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[ChronalAnomalyRoom] 🎯 Handling custom interaction:`, interaction);

        switch (interaction.type) {
            case 'temporal_rift':
                await this.handleTemporalRiftActivation();
                break;
            case 'chronal_anomaly':
                await this.handleTemporalRiftActivation();
                break;
            default:
                console.warn(`[ChronalAnomalyRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle temporal rift activation - triggers magical transformation and boss spawn
     */
    async handleTemporalRiftActivation() {
        console.log(`[ChronalAnomalyRoom] 🌀 Temporal rift activation started`);

        if (this.state.bossSpawned) {
            console.log(`[ChronalAnomalyRoom] ⚠️ Boss already active - rift cannot be used again`);
            return;
        }

        if (this.state.riftActivated) {
            console.log(`[ChronalAnomalyRoom] ⚠️ Rift already activated - magical transformation in progress`);
            return;
        }

        // Mark rift as activated
        this.state.riftActivated = true;
        console.log(`[ChronalAnomalyRoom] 🔮 TEMPORAL RIFT ACTIVATED - Beginning magical transformation`);

        // Play mystical activation sound
        this.playAudio("nightmare_chest");

        // Start the magical object levitation and rotation sequence
        await this.triggerMagicalTransformation();

        // After magical animation, spawn the Chronarch boss
        setTimeout(() => {
            this.spawnChronarchBoss();
        }, 4000); // 4 second delay for dramatic effect
    }

    /**
     * Trigger magical transformation - all objects rise and rotate
     */
    async triggerMagicalTransformation() {
        console.log(`[ChronalAnomalyRoom] ✨ TRIGGERING MAGICAL TRANSFORMATION`);

        this.state.magicalTransformationActive = true;
        this.state.animationStartTime = Date.now();

        // Get all room objects that should float
        const roomGroup = this.dungeonHandler?.currentRoomGroup;
        if (!roomGroup) {
            console.warn('[ChronalAnomalyRoom] No room group available for magical transformation');
            return;
        }

        // Find all interactable room objects (excluding the rift itself)
        this.state.floatingObjects = [];
        roomGroup.traverse(child => {
            if (child.userData && child.userData.objectId && 
                !child.userData.objectId.includes('rift') && 
                child.userData.isDecorative) {
                
                // Store original position for animation
                child.userData.originalPosition = child.position.clone();
                child.userData.originalRotation = child.rotation.clone();
                child.userData.floatStartTime = Date.now() + Math.random() * 1000; // Stagger starts
                child.userData.isFloating = true;
                
                this.state.floatingObjects.push(child);
                console.log(`[ChronalAnomalyRoom] ✨ Added ${child.userData.objectId} to floating objects`);
            }
        });

        console.log(`[ChronalAnomalyRoom] ✨ ${this.state.floatingObjects.length} objects will levitate and rotate`);

        // Start animation loop
        this.startMagicalAnimation();
    }

    /**
     * Start the magical animation loop for floating objects
     */
    startMagicalAnimation() {
        const animationLoop = () => {
            if (!this.state.magicalTransformationActive) {
                return; // Stop animation
            }

            const currentTime = Date.now();
            const globalProgress = Math.min((currentTime - this.state.animationStartTime) / 4000, 1); // 4 second total animation

            this.state.floatingObjects.forEach(obj => {
                if (!obj.userData.isFloating) return;

                const timeSinceStart = currentTime - obj.userData.floatStartTime;
                if (timeSinceStart < 0) return; // Not started yet

                const progress = Math.min(timeSinceStart / 3000, 1); // 3 second per-object animation
                const easeProgress = this.easeInOutCubic(progress);

                // Levitation animation
                const originalY = obj.userData.originalPosition.y;
                const floatHeight = 2.0 + Math.sin(currentTime * 0.003) * 0.5; // Gentle floating
                obj.position.y = originalY + (floatHeight * easeProgress);

                // Rotation animation
                obj.rotation.y = obj.userData.originalRotation.y + (currentTime * 0.002 * easeProgress);
                obj.rotation.x = obj.userData.originalRotation.x + Math.sin(currentTime * 0.001) * 0.2 * easeProgress;

                // Magical sparkle effect (scale pulsing)
                const sparkle = 1 + Math.sin(currentTime * 0.004) * 0.1 * easeProgress;
                obj.scale.setScalar(sparkle);
            });

            // Continue animation
            requestAnimationFrame(animationLoop);
        };

        animationLoop();
        console.log(`[ChronalAnomalyRoom] ✨ Magical animation loop started`);
    }

    /**
     * Easing function for smooth animations
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Spawn Chronarch the Temporal Alchemist boss
     */
    spawnChronarchBoss() {
        console.log(`[ChronalAnomalyRoom] 🔮 SPAWNING CHRONARCH THE TEMPORAL ALCHEMIST`);

        if (this.state.bossSpawned) {
            console.warn('[ChronalAnomalyRoom] Boss already spawned!');
            return;
        }

        this.state.bossSpawned = true;

        // Get room center position for boss spawn
        const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
        let roomCenterX = 0, roomCenterZ = 0;
        if (currentRoomGroup) {
            roomCenterX = currentRoomGroup.position.x;
            roomCenterZ = currentRoomGroup.position.z;
        }

        // Spawn the Chronarch boss near the temporal rift
        const bossSpawn = {
            type: "chronarch", // This should match the boss prefab type
            position: {
                x: roomCenterX + 0.5, // Slightly offset from center
                y: 2.0, // Elevated spawn
                z: roomCenterZ + 1.0
            },
            spawnDelay: 0.2
        };

        console.log(`[ChronalAnomalyRoom] 🔮 Spawning Chronarch at position:`, bossSpawn.position);

        // Use the enemy spawning system
        this.spawnEnemies([bossSpawn]);

        // Play dramatic boss entrance sound
        setTimeout(() => {
            this.playAudio("nightmare_chest"); // Could be boss_entrance if available
        }, 500);

        console.log(`[ChronalAnomalyRoom] 🔮 CHRONARCH BOSS SPAWNED - Magical duel begins!`);
    }

    /**
     * Handle enemy defeat - reward after defeating Chronarch boss
     */
    handleEnemyDefeat(enemyId) {
        // Check if the defeated enemy is the Chronarch boss
        if (enemyId && enemyId.includes('chronarch')) {
            console.log(`[ChronalAnomalyRoom] 🏆 CHRONARCH DEFEATED - The Temporal Alchemist has fallen!`);

            // Stop magical transformation
            this.state.magicalTransformationActive = false;

            // Return floating objects to their original positions
            this.returnObjectsToOriginalPositions();

            // Spawn epic boss reward chest and unlock doors AFTER victory sequence
            setTimeout(() => {
                this.spawnChest({
                    position: { x: 0, y: 1, z: 0 }, // Center of room near rift
                    chestType: "chronarch_victory_chest"
                });
                
                // Manually show doors after the victory sequence is complete
                this.showEventRoomDoors();
                console.log(`[ChronalAnomalyRoom] 🚪 Doors unlocked after victory sequence completion`);
            }, 2000);

            console.log(`[ChronalAnomalyRoom] 🎉 Epic victory reward spawned!`);
        } else {
            // For non-boss enemies (ambient fireflies), call super to handle normally
            super.handleEnemyDefeat(enemyId);
        }
    }

    /**
     * Return floating objects to their original positions
     */
    returnObjectsToOriginalPositions() {
        console.log(`[ChronalAnomalyRoom] 🌊 Returning ${this.state.floatingObjects.length} objects to original positions`);

        this.state.floatingObjects.forEach(obj => {
            if (obj.userData && obj.userData.originalPosition) {
                // Store original rotation and scale for smooth animation
                if (!obj.userData.originalScale) {
                    obj.userData.originalScale = new THREE.Vector3(1, 1, 1); // Default scale
                }
                
                // Smooth return animation with rotation and scale
                const returnAnimation = () => {
                    const currentPos = obj.position.clone();
                    const targetPos = obj.userData.originalPosition;
                    const targetRot = obj.userData.originalRotation;
                    const targetScale = obj.userData.originalScale;
                    
                    // Lerp back to original position
                    obj.position.lerp(targetPos, 0.1);
                    
                    // Smooth rotation animation using quaternion slerp
                    const currentQuat = obj.quaternion.clone();
                    const targetQuat = new THREE.Quaternion().setFromEuler(targetRot);
                    obj.quaternion.slerp(targetQuat, 0.1);
                    
                    // Smooth scale animation using vector lerp
                    obj.scale.lerp(targetScale, 0.1);
                    
                    // Check if close enough to stop (position, rotation, and scale)
                    const positionClose = currentPos.distanceTo(targetPos) < 0.1;
                    const rotationClose = currentQuat.angleTo(obj.quaternion) < 0.05;
                    const scaleClose = obj.scale.distanceTo(targetScale) < 0.05;
                    
                    if (!positionClose || !rotationClose || !scaleClose) {
                        requestAnimationFrame(returnAnimation);
                    } else {
                        // Snap to final values when close enough
                        obj.position.copy(targetPos);
                        obj.quaternion.copy(targetQuat);
                        obj.scale.copy(targetScale);
                        obj.userData.isFloating = false;
                    }
                };
                
                returnAnimation();
            }
        });
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "chronal_anomaly",
    name: "The Chrono-Archaeologist's Laboratory",
    shape: "CROSS_SHAPE",
    description: "A forgotten laboratory where time itself has been fractured",
    tags: ["temporal", "archaeology", "choice", "story"],
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "south", // Preferred entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -7 },   // Door on south arm when entered from north
        south: { x: 0, y: 0, z: 7 },    // Door on north arm when entered from south
        east: { x: -7, y: 0, z: 0 },    // Door on west arm when entered from east
        west: { x: 7, y: 0, z: 0 }      // Door on east arm when entered from west
    },
    
    // Visual overrides - keep the mystical materials as requested
    materials: {
        walls: "mystical_stone_brick", // Keep as requested
        floors: "mystical_stone_floor", // Keep as requested
        wallTint: 0x303040, // Deep blue-grey
        floorTint: 0x202030  // Darker blue-grey
    },

    // Static brightness - dim lighting for temporal mystery
    staticBrightness: 4, // Dim mystical atmosphere (4/10) like pond room
    
    // Lighting configuration - using supported EventRoomManager format
    lighting: {
        ambient: {
            intensity: 0.4, // Brighter ambient to prevent black room
            color: 0x404060 // Lighter blue-grey for visibility
        },
        spotlight: {
            position: { x: 0, y: 8, z: 0 }, // High above center
            target: { x: 0, y: 0, z: 0 }, // Pointing down at center
            intensity: 1.5,
            color: 0x8A2BE2, // Blue-violet temporal energy
            angle: Math.PI / 3, // 60 degrees
            penumbra: 0.3,
            distance: 20,
            castShadow: true
        },
        waterGlow: {
            position: { x: 0, y: 4, z: 0 }, // Central temporal rift light
            intensity: 3.0, // Bright temporal glow
            color: 0x8A2BE2, // Blue-violet
            distance: 18,
            decay: 1.2
        }
    },
    
    // Object placement using local coordinates - PROPER THEMATIC PREFABS
    objects: [
        // === CENTRAL TEMPORAL RIFT (MAIN INTERACTION) ===
        {
            type: "temporal_rift",
            position: { x: 0, y: 0, z: 0 }, // Center of cross
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.2, y: 1.0, z: 1.2 }, // Slightly larger
            userData: {
                objectId: "central_temporal_rift",
                isInteractable: true,
                interactionType: "temporal_rift",
                isDecorative: false,
                interaction: {
                    type: "temporal_rift",
                    id: "central_temporal_rift"
                }
            }
        },

        // === CHRONO-ARCHAEOLOGIST STATUE (Decorative only) ===
        {
            type: "chrono_archaeologist_statue",
            position: { x: 0, y: 0, z: -4 }, // North arm of cross, facing rift
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "archaeologist_statue_decorative",
                isInteractable: false,
                isDecorative: true
            }
        },

        // === LARGE TIME CRYSTALS (Temporal energy pillars) ===
        {
            type: "time_crystal_large",
            position: { x: -4, y: 0, z: 0 }, // West arm of cross
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "large_crystal_west",
                isDecorative: true
            }
        },
        {
            type: "time_crystal_large",
            position: { x: 4, y: 0, z: 0 }, // East arm of cross
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "large_crystal_east",
                isDecorative: true
            }
        },

        // === TEMPORAL DEVICES (Ancient research equipment) ===
        {
            type: "temporal_device",
            position: { x: -2, y: 0, z: -3 }, // Near statue, left
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "research_device_left",
                isDecorative: true
            }
        },
        {
            type: "temporal_device",
            position: { x: 2, y: 0, z: -3 }, // Near statue, right
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "research_device_right",
                isDecorative: true
            }
        },

        // === TIME-WORN PILLARS (Ancient architecture) ===
        {
            type: "time_worn_pillar",
            position: { x: -3, y: 0, z: -3 }, // Corner supports
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "worn_pillar_nw",
                isDecorative: true
            }
        },
        {
            type: "time_worn_pillar",
            position: { x: 3, y: 0, z: -3 }, // Corner supports
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "worn_pillar_ne",
                isDecorative: true
            }
        },
        {
            type: "time_worn_pillar",
            position: { x: -3, y: 0, z: 3 }, // Corner supports
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "worn_pillar_sw",
                isDecorative: true
            }
        },
        {
            type: "time_worn_pillar",
            position: { x: 3, y: 0, z: 3 }, // Corner supports
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "worn_pillar_se",
                isDecorative: true
            }
        },

        // === SMALL TIME CRYSTALS (Scattered temporal fragments) ===
        {
            type: "time_crystal_small",
            position: { x: -1.5, y: 0, z: -1.5 },
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 0.8, y: 0.9, z: 0.8 },
            userData: { objectId: "small_crystal_1", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 1.8, y: 0, z: -1.2 },
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 0.9, y: 0.8, z: 0.9 },
            userData: { objectId: "small_crystal_2", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: -2.1, y: 0, z: 1.5 },
            rotation: { x: 0, y: Math.PI / 3, z: 0 },
            scale: { x: 0.7, y: 1.0, z: 0.7 },
            userData: { objectId: "small_crystal_3", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 1.2, y: 0, z: 2.0 },
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 0.8, z: 0.8 },
            userData: { objectId: "small_crystal_4", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 0.5, y: 0, z: 4.2 },
            rotation: { x: 0, y: Math.PI / 5, z: 0 },
            scale: { x: 0.9, y: 0.7, z: 0.9 },
            userData: { objectId: "small_crystal_5", isDecorative: true }
        },

        // === TEMPORAL ARTIFACTS (Research remnants) ===
        {
            type: "temporal_artifact",
            position: { x: -3.5, y: 0, z: -1 },
            rotation: { x: 0, y: Math.PI / 7, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: { objectId: "artifact_1", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: 3.2, y: 0, z: -0.8 },
            rotation: { x: 0, y: -Math.PI / 5, z: 0 },
            scale: { x: 1.1, y: 1.0, z: 1.1 },
            userData: { objectId: "artifact_2", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: -1.8, y: 0, z: 3.5 },
            rotation: { x: 0, y: Math.PI / 3, z: 0 },
            scale: { x: 0.9, y: 1.0, z: 0.9 },
            userData: { objectId: "artifact_3", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: 2.5, y: 0, z: 3.8 },
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: { objectId: "artifact_4", isDecorative: true }
        },

        // === ATMOSPHERIC CRYSTALS (Additional ambiance) ===
        {
            type: "time_crystal_small",
            position: { x: -4.5, y: 0, z: -2 },
            rotation: { x: 0, y: Math.PI / 9, z: 0 },
            scale: { x: 0.6, y: 0.8, z: 0.6 },
            userData: { objectId: "ambient_crystal_1", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 4.3, y: 0, z: -1.8 },
            rotation: { x: 0, y: -Math.PI / 7, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: { objectId: "ambient_crystal_2", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: -4.2, y: 0, z: 2.3 },
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 0.5, y: 0.7, z: 0.5 },
            userData: { objectId: "ambient_crystal_3", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 4.6, y: 0, z: 2.1 },
            rotation: { x: 0, y: -Math.PI / 3, z: 0 },
            scale: { x: 1.0, y: 0.9, z: 1.0 },
            userData: { objectId: "ambient_crystal_4", isDecorative: true }
        },

        // === ADDITIONAL TEMPORAL CRYSTALS (Increased density like pond) ===
        {
            type: "time_crystal_small",
            position: { x: -2.8, y: 0, z: -4.1 },
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 0.7, y: 1.1, z: 0.7 },
            userData: { objectId: "crystal_dense_1", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 3.2, y: 0, z: -4.3 },
            rotation: { x: 0, y: -Math.PI / 5, z: 0 },
            scale: { x: 1.2, y: 0.8, z: 1.2 },
            userData: { objectId: "crystal_dense_2", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: -1.5, y: 0, z: 4.8 },
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 0.9, y: 1.3, z: 0.9 },
            userData: { objectId: "crystal_dense_3", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 2.7, y: 0, z: 4.5 },
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.1, y: 1.0, z: 1.1 },
            userData: { objectId: "crystal_dense_4", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: -4.9, y: 0, z: 0.5 },
            rotation: { x: 0, y: Math.PI / 12, z: 0 },
            scale: { x: 0.6, y: 1.5, z: 0.6 },
            userData: { objectId: "crystal_dense_5", isDecorative: true }
        },
        {
            type: "time_crystal_small",
            position: { x: 4.8, y: 0, z: 0.8 },
            rotation: { x: 0, y: -Math.PI / 10, z: 0 },
            scale: { x: 1.3, y: 0.7, z: 1.3 },
            userData: { objectId: "crystal_dense_6", isDecorative: true }
        },

        // === TEMPORAL DEBRIS (Like pond's cave debris) ===
        {
            type: "temporal_debris",
            position: { x: -3.5, y: 0, z: -3.2 },
            rotation: { x: 0, y: Math.PI / 7, z: 0 },
            scale: { x: 1.8, y: 0.9, z: 1.8 },
            userData: { objectId: "temporal_debris_1", isDecorative: true }
        },
        {
            type: "temporal_debris",
            position: { x: 3.8, y: 0, z: -3.0 },
            rotation: { x: 0, y: -Math.PI / 5, z: 0 },
            scale: { x: 1.5, y: 0.7, z: 1.5 },
            userData: { objectId: "temporal_debris_2", isDecorative: true }
        },
        {
            type: "temporal_debris",
            position: { x: -4.0, y: 0, z: 3.5 },
            rotation: { x: 0, y: Math.PI / 3, z: 0 },
            scale: { x: 1.6, y: 0.8, z: 1.6 },
            userData: { objectId: "temporal_debris_3", isDecorative: true }
        },
        {
            type: "temporal_debris",
            position: { x: 3.6, y: 0, z: 3.7 },
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 1.4, y: 0.6, z: 1.4 },
            userData: { objectId: "temporal_debris_4", isDecorative: true }
        },

        // === TEMPORAL FORMATIONS (Like pond's rock formations) ===
        {
            type: "temporal_formation",
            position: { x: -4.8, y: 0, z: -4.5 },
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 2.2, y: 1.3, z: 2.2 },
            userData: { objectId: "formation_nw", isDecorative: true }
        },
        {
            type: "temporal_formation",
            position: { x: 4.5, y: 0, z: -4.8 },
            rotation: { x: 0, y: -Math.PI / 8, z: 0 },
            scale: { x: 2.0, y: 1.1, z: 2.0 },
            userData: { objectId: "formation_ne", isDecorative: true }
        },
        {
            type: "temporal_formation",
            position: { x: -4.6, y: 0, z: 4.3 },
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 2.3, y: 1.4, z: 2.3 },
            userData: { objectId: "formation_sw", isDecorative: true }
        },
        {
            type: "temporal_formation",
            position: { x: 4.7, y: 0, z: 4.6 },
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 2.1, y: 1.2, z: 2.1 },
            userData: { objectId: "formation_se", isDecorative: true }
        },

        // === MYSTICAL TEMPORAL STONES (Scattered atmosphere) ===
        {
            type: "mystical_temporal_stone",
            position: { x: -2.3, y: 0, z: -2.8 },
            rotation: { x: 0, y: Math.PI / 9, z: 0 },
            scale: { x: 1.1, y: 1.3, z: 1.1 },
            userData: { objectId: "mystical_stone_1", isDecorative: true }
        },
        {
            type: "mystical_temporal_stone",
            position: { x: 2.8, y: 0, z: -2.5 },
            rotation: { x: 0, y: -Math.PI / 7, z: 0 },
            scale: { x: 0.9, y: 1.5, z: 0.9 },
            userData: { objectId: "mystical_stone_2", isDecorative: true }
        },
        {
            type: "mystical_temporal_stone",
            position: { x: -2.7, y: 0, z: 2.9 },
            rotation: { x: 0, y: Math.PI / 5, z: 0 },
            scale: { x: 1.4, y: 1.0, z: 1.4 },
            userData: { objectId: "mystical_stone_3", isDecorative: true }
        },
        {
            type: "mystical_temporal_stone",
            position: { x: 2.5, y: 0, z: 3.1 },
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 1.2, y: 1.1, z: 1.2 },
            userData: { objectId: "mystical_stone_4", isDecorative: true }
        },

        // === TEMPORAL ENERGY NODES (Like pond's crystal caves) ===
        {
            type: "temporal_energy_node",
            position: { x: -3.8, y: 0, z: -1.5 },
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 1.6, y: 2.1, z: 1.6 },
            userData: { objectId: "energy_node_1", isDecorative: true }
        },
        {
            type: "temporal_energy_node",
            position: { x: 4.1, y: 0, z: -1.2 },
            rotation: { x: 0, y: -Math.PI / 3, z: 0 },
            scale: { x: 1.7, y: 1.9, z: 1.7 },
            userData: { objectId: "energy_node_2", isDecorative: true }
        },
        {
            type: "temporal_energy_node",
            position: { x: -4.0, y: 0, z: 1.8 },
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 1.5, y: 2.3, z: 1.5 },
            userData: { objectId: "energy_node_3", isDecorative: true }
        },
        {
            type: "temporal_energy_node",
            position: { x: 3.9, y: 0, z: 2.0 },
            rotation: { x: 0, y: -Math.PI / 9, z: 0 },
            scale: { x: 1.8, y: 2.0, z: 1.8 },
            userData: { objectId: "energy_node_4", isDecorative: true }
        },

        // === ADDITIONAL ATMOSPHERIC DENSITY ===
        {
            type: "temporal_artifact",
            position: { x: -1.8, y: 0, z: -3.9 },
            rotation: { x: 0, y: Math.PI / 11, z: 0 },
            scale: { x: 0.8, y: 1.2, z: 0.8 },
            userData: { objectId: "artifact_scatter_1", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: 2.1, y: 0, z: -4.2 },
            rotation: { x: 0, y: -Math.PI / 13, z: 0 },
            scale: { x: 1.3, y: 0.9, z: 1.3 },
            userData: { objectId: "artifact_scatter_2", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: -2.0, y: 0, z: 4.1 },
            rotation: { x: 0, y: Math.PI / 7, z: 0 },
            scale: { x: 1.1, y: 1.4, z: 1.1 },
            userData: { objectId: "artifact_scatter_3", isDecorative: true }
        },
        {
            type: "temporal_artifact",
            position: { x: 1.9, y: 0, z: 4.4 },
            rotation: { x: 0, y: -Math.PI / 14, z: 0 },
            scale: { x: 0.9, y: 1.1, z: 0.9 },
            userData: { objectId: "artifact_scatter_4", isDecorative: true }
        },

        // === TEMPORAL SHARDS (Small atmospheric details) ===
        {
            type: "temporal_shard",
            position: { x: -1.2, y: 0, z: -1.8 },
            rotation: { x: 0, y: Math.PI / 15, z: 0 },
            scale: { x: 0.6, y: 0.8, z: 0.6 },
            userData: { objectId: "shard_1", isDecorative: true }
        },
        {
            type: "temporal_shard",
            position: { x: 1.5, y: 0, z: -1.6 },
            rotation: { x: 0, y: -Math.PI / 16, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: { objectId: "shard_2", isDecorative: true }
        },
        {
            type: "temporal_shard",
            position: { x: -1.4, y: 0, z: 1.7 },
            rotation: { x: 0, y: Math.PI / 17, z: 0 },
            scale: { x: 0.8, y: 0.7, z: 0.8 },
            userData: { objectId: "shard_3", isDecorative: true }
        },
        {
            type: "temporal_shard",
            position: { x: 1.3, y: 0, z: 1.9 },
            rotation: { x: 0, y: -Math.PI / 18, z: 0 },
            scale: { x: 0.5, y: 1.0, z: 0.5 },
            userData: { objectId: "shard_4", isDecorative: true }
        },

        // === TEMPORAL RUNE CIRCLES (Floor decorations) ===
        {
            type: "temporal_rune_circle",
            position: { x: -2.5, y: 0, z: 0 },
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 1.8, y: 0.1, z: 1.8 },
            userData: { objectId: "rune_circle_1", isDecorative: true }
        },
        {
            type: "temporal_rune_circle",
            position: { x: 2.3, y: 0, z: 0 },
            rotation: { x: 0, y: -Math.PI / 8, z: 0 },
            scale: { x: 2.0, y: 0.1, z: 2.0 },
            userData: { objectId: "rune_circle_2", isDecorative: true }
        },
        {
            type: "temporal_rune_circle",
            position: { x: 0, y: 0, z: -2.8 },
            rotation: { x: 0, y: Math.PI / 12, z: 0 },
            scale: { x: 1.6, y: 0.1, z: 1.6 },
            userData: { objectId: "rune_circle_3", isDecorative: true }
        },
        {
            type: "temporal_rune_circle",
            position: { x: 0, y: 0, z: 2.7 },
            rotation: { x: 0, y: -Math.PI / 10, z: 0 },
            scale: { x: 1.9, y: 0.1, z: 1.9 },
            userData: { objectId: "rune_circle_4", isDecorative: true }
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: ChronalAnomalyRoom
};