import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';
import { animateEyeOfJudgment } from '../../generators/prefabs/eyeOfJudgmentObject.js';
import { animateRitualCircle } from '../../generators/prefabs/ritualCircleFloorObject.js';
import { createFloorMistPlanes } from '../../effects/FloorMistPlane.js';

/**
 * The Eye of Judgment Event Room - "The Chamber of Souls"
 *
 * A circular chamber where a giant, floating eye judges the player's confessed sins.
 * The Eye uses advanced AI to analyze sincerity vs deception, affecting future encounters.
 * The room's atmosphere responds to the player's moral choices and confession quality.
 */

/**
 * Eye of Judgment Room Handler
 */
export class EyeOfJudgmentRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        
        // Extended state for judgment mechanics
        this.state = {
            ...this.state,
            // Judgment progression
            eyeActivated: false,
            confessionStarted: false,
            judgmentComplete: false,
            // Player confession analysis
            playerInput: '',
            confessionAttempts: 0,
            sincerityScore: 0,
            jokeDetected: false,
            // Atmospheric state
            chamberIntensity: 0,
            ritualPowerLevel: 0,
            eyeJudgmentState: 'watching', // watching, questioning, judging, reacting
            // Animation state
            floatingObjects: [],
            animationStartTime: 0,
            // Moral alignment impact
            moralFlags: new Set(),
            alignmentShift: 0
        };

        console.log(`[EyeOfJudgmentRoom] 👁️ Chamber of Souls initialized for room ${roomId}`);
        
        // Initialize atmospheric effects immediately
        this.initializeJudgmentAtmosphere();
        
        // Initialize floor mist for mystical atmosphere
        setTimeout(() => {
            this.initializeJudgmentMist();
        }, 500);
    }

    /**
     * Initialize judgment chamber atmosphere
     */
    initializeJudgmentAtmosphere() {
        console.log('[EyeOfJudgmentRoom] ⚡ Initializing judgment chamber atmosphere');
        
        // Start with low intensity ambient sounds
        this.startAmbientDrone();
    }

    /**
     * Initialize mystical floor mist for the judgment chamber
     */
    initializeJudgmentMist() {
        console.log('[EyeOfJudgmentRoom] 🌫️ Initializing judgment chamber mist');
        
        if (!this.dungeonHandler?.currentRoomGroup) {
            console.warn('[EyeOfJudgmentRoom] No current room group for mist initialization');
            return;
        }

        try {
            // Create mystical mist for circular chamber (expanded size)
            const roomBounds = {
                minX: -16,
                maxX: 16,
                minZ: -16,
                maxZ: 16
            };

            const mistPlanes = createFloorMistPlanes(
                roomBounds,
                2.0,    // mistHeight - higher for mystical effect
                6,      // layerCount - more layers for atmospheric depth
                0.08,   // opacity - more visible for judgment atmosphere
                [],     // floorSegments - empty, will use simple bounds
                this.dungeonHandler.scene
            );

            // Tint the mist with cold judgment blue
            mistPlanes.traverse(child => {
                if (child.isMesh && child.material) {
                    child.material.color.setHex(0x4169E1); // Royal blue judgment mist
                }
            });

            // Add mist to the room
            this.dungeonHandler.currentRoomGroup.add(mistPlanes);
            console.log('[EyeOfJudgmentRoom] ✅ Judgment chamber mist added');

        } catch (error) {
            console.error('[EyeOfJudgmentRoom] Error creating mist effects:', error);
        }
    }

    /**
     * Update method called every frame for animations
     */
    update(deltaTime) {
        const currentTime = Date.now();

        // Animate the Eye of Judgment
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            this.dungeonHandler.currentRoomGroup.traverse(child => {
                if (child.name === 'eye_of_judgment' && child.userData?.isJudgingEye) {
                    // Update eye's judgment state
                    child.userData.judgmentState = this.state.eyeJudgmentState;
                    animateEyeOfJudgment(child, deltaTime, currentTime);
                }
            });
        }

        // Animate ritual circles
        if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
            this.dungeonHandler.currentRoomGroup.traverse(child => {
                if (child.name === 'ritual_circle_floor' && child.userData?.isRitualCircle) {
                    // Update circle power based on judgment state
                    child.userData.isActiveCircle = this.state.confessionStarted;
                    child.userData.powerLevel = this.state.ritualPowerLevel;
                    animateRitualCircle(child, deltaTime, currentTime);
                }
            });
        }
    }

    /**
     * Handle interactions in this room
     */
    async handleInteraction(objectId, context) {
        console.log(`[EyeOfJudgmentRoom] Handling interaction with ${objectId}`);

        // Check for Eye of Judgment interaction
        if (objectId === 'central_eye_of_judgment' ||
            objectId === 'eye_of_judgment' ||
            objectId.includes('eye')) {
            await this.handleEyeOfJudgmentInteraction();
        } else if (objectId === 'central_ritual_circle' ||
                   objectId.includes('ritual_circle')) {
            // Allow ritual circle to trigger the same Eye interaction
            console.log(`[EyeOfJudgmentRoom] Ritual circle activated - triggering Eye of Judgment`);
            await this.handleEyeOfJudgmentInteraction();
        } else {
            console.warn(`[EyeOfJudgmentRoom] Unknown object interaction: ${objectId} - Only the Eye and ritual circle can be interacted with`);
        }
    }

    /**
     * Handle custom interactions specific to Eye of Judgment Room
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.log(`[EyeOfJudgmentRoom] 🎯 Handling custom interaction:`, interaction);

        switch (interaction.type) {
            case 'eye_judgment':
                await this.handleEyeOfJudgmentInteraction();
                break;
            case 'confessional':
                await this.handleEyeOfJudgmentInteraction();
                break;
            case 'moral_examination':
                await this.handleEyeOfJudgmentInteraction();
                break;
            default:
                console.warn(`[EyeOfJudgmentRoom] Unknown custom interaction type: ${interaction.type}`);
        }
    }

    /**
     * Handle Eye of Judgment interaction - triggers confession sequence
     */
    async handleEyeOfJudgmentInteraction() {
        console.log(`[EyeOfJudgmentRoom] 👁️ Eye of Judgment interaction started`);

        if (this.state.judgmentComplete) {
            console.log(`[EyeOfJudgmentRoom] ⚠️ Judgment already complete - Eye remains silent`);
            return;
        }

        if (this.state.eyeActivated) {
            console.log(`[EyeOfJudgmentRoom] ⚠️ Eye already active - continuing existing judgment`);
            return;
        }

        // Mark eye as activated
        this.state.eyeActivated = true;
        this.state.eyeJudgmentState = 'questioning';
        console.log(`[EyeOfJudgmentRoom] 👁️ EYE OF JUDGMENT ACTIVATED - Beginning confession sequence`);

        // Play mystical activation sound
        this.playAudio("nightmare_chest");

        // Start chamber intensity build-up
        this.startChamberIntensification();

        // Begin the confession sequence
        await this.beginConfessionSequence();
    }

    /**
     * Begin the confession sequence with dialogue
     */
    async beginConfessionSequence() {
        console.log(`[EyeOfJudgmentRoom] 📜 BEGINNING CONFESSION SEQUENCE`);

        this.state.confessionStarted = true;
        this.state.ritualPowerLevel = 0.5;

        // Initial dialogue to set the mood
        const initialDialogue = {
            lines: [
                "The Eye opens wider, its gaze piercing through your very soul.",
                "A voice resonates directly into your mind, cold and ancient:",
                "\"I am the Eye of Judgment. I see all truths, all lies, all sins.\"",
                "\"Before you may pass, you must confess your greatest transgression.\"",
                "\"Speak truthfully... for I will know if you deceive me.\""
            ],
            options: [
                { text: "I understand. I will confess.", value: "confess" },
                { text: "I have no sins to confess.", value: "deny" },
                { text: "This is ridiculous.", value: "dismiss" }
            ]
        };

        const choice = await this.showDialogue(initialDialogue);
        console.log(`[EyeOfJudgmentRoom] Player's initial response:`, choice);

        switch (choice) {
            case 'confess':
                await this.handleConfessionAcceptance();
                break;
            case 'deny':
                await this.handleConfessionDenial();
                break;
            case 'dismiss':
                await this.handleConfessionDismissal();
                break;
            default:
                console.log(`[EyeOfJudgmentRoom] Unknown choice or cancelled dialogue:`, choice);
                await this.handleConfessionDismissal(); // Default to dismissal
        }
    }

    /**
     * Handle when player accepts to confess
     */
    async handleConfessionAcceptance() {
        console.log(`[EyeOfJudgmentRoom] Player chose to confess - beginning sin input`);

        this.state.eyeJudgmentState = 'judging';
        this.state.ritualPowerLevel = 0.8;

        // Show confession prompt with text input
        await this.showSinInputPrompt();
    }

    /**
     * Handle when player denies having sins
     */
    async handleConfessionDenial() {
        console.log(`[EyeOfJudgmentRoom] Player denied having sins - Eye responds with displeasure`);

        const denialDialogue = {
            lines: [
                "The Eye narrows, its light growing colder and more intense.",
                "\"You claim to be without sin? Impossible.\"",
                "\"Every soul carries the weight of their choices.\"",
                "\"I will give you one more chance to speak truthfully.\""
            ],
            options: [
                { text: "You're right. I'll confess my sin.", value: "confess_after_denial" },
                { text: "I insist - I am pure of heart.", value: "persist_denial" }
            ]
        };

        const choice = await this.showDialogue(denialDialogue);
        
        if (choice === 'confess_after_denial') {
            await this.handleConfessionAcceptance();
        } else {
            await this.handlePersistentDenial();
        }
    }

    /**
     * Handle when player dismisses the Eye
     */
    async handleConfessionDismissal() {
        console.log(`[EyeOfJudgmentRoom] Player dismissed the Eye - triggering wrath sequence`);

        this.state.eyeJudgmentState = 'reacting';
        this.state.ritualPowerLevel = 1.0;
        this.state.jokeDetected = true;

        const wrathDialogue = {
            lines: [
                "The Eye BLAZES with cold fury, its light becoming harsh and blinding.",
                "\"You MOCK the sacred judgment? You treat this as a jest?\"",
                "\"Your soul reeks of arrogance and contempt!\"",
                "\"Face the consequences of your insolence!\""
            ],
            options: [
                { text: "Wait! I was wrong! I'll confess!", value: "desperate_confess" },
                { text: "Do your worst.", value: "defiant" }
            ]
        };

        const choice = await this.showDialogue(wrathDialogue);
        
        if (choice === 'desperate_confess') {
            this.state.confessionAttempts++;
            await this.handleDesperateConfession();
        } else {
            await this.handleDefiantResponse();
        }
    }

    /**
     * Show sin input prompt with text input system
     */
    async showSinInputPrompt() {
        console.log(`[EyeOfJudgmentRoom] 📝 Showing sin input prompt`);

        // This is a simplified version - in a real implementation, you'd create a custom UI
        // For now, we'll use a dialogue-based approach that simulates text input
        const inputPrompt = {
            lines: [
                "The chamber falls silent except for your heartbeat.",
                "The Eye waits, unblinking, as a mystical force compels you to speak.",
                "",
                "What is your greatest sin?",
                "(Type your confession below - the Eye will judge your sincerity)"
            ],
            options: [
                { text: "[Enter confession...]", value: "input_sin" },
                { text: "I... I can't say it.", value: "refuse_input" }
            ]
        };

        const choice = await this.showDialogue(inputPrompt);
        
        if (choice === 'input_sin') {
            // In a real implementation, this would open a text input field
            // For this demo, we'll simulate with predefined responses
            await this.simulateTextInput();
        } else {
            await this.handleRefusalToConfess();
        }
    }

    /**
     * Simulate text input for confession (in real game, this would be actual text input)
     */
    async simulateTextInput() {
        console.log(`[EyeOfJudgmentRoom] 💬 Simulating text input for confession`);

        // In a real implementation, this would:
        // 1. Show a text input UI overlay
        // 2. Capture user input
        // 3. Send to AI service for analysis
        // 4. Return sincerity score and categorization

        // For demo purposes, we'll show examples of different types of responses
        const confessionExamples = {
            lines: [
                "Please type your confession. Examples of how the Eye judges:",
                "",
                "SINCERE: 'I stole money from my mother's purse when I was young'",
                "EVASIVE: 'I sometimes think bad thoughts'", 
                "JOKING: 'I killed a man in Reno just to watch him die'",
                "DISMISSIVE: 'lol idk whatever'"
            ],
            options: [
                { text: "Confess a serious personal failing", value: "sincere" },
                { text: "Give a vague, evasive answer", value: "evasive" },
                { text: "Make a joke or pop culture reference", value: "joke" },
                { text: "Give a dismissive response", value: "dismissive" }
            ]
        };

        const inputType = await this.showDialogue(confessionExamples);
        await this.analyzeConfessionInput(inputType);
    }

    /**
     * Analyze the player's confession input and respond accordingly
     */
    async analyzeConfessionInput(inputType) {
        console.log(`[EyeOfJudgmentRoom] 🔍 Analyzing confession input type: ${inputType}`);

        this.state.confessionAttempts++;
        let sincerityScore = 0;
        let response = null;

        switch (inputType) {
            case 'sincere':
                sincerityScore = 0.9;
                response = await this.handleSincereConfession();
                break;
            case 'evasive':
                sincerityScore = 0.3;
                response = await this.handleEvasiveConfession();
                break;
            case 'joke':
                sincerityScore = 0.1;
                this.state.jokeDetected = true;
                response = await this.handleJokingConfession();
                break;
            case 'dismissive':
                sincerityScore = 0.0;
                this.state.jokeDetected = true;
                response = await this.handleDismissiveConfession();
                break;
            default:
                sincerityScore = 0.2;
                response = await this.handleUnknownConfession();
        }

        this.state.sincerityScore = sincerityScore;
        await this.completeJudgment();
    }

    /**
     * Handle sincere confession
     */
    async handleSincereConfession() {
        console.log(`[EyeOfJudgmentRoom] ✅ Sincere confession detected`);

        this.state.eyeJudgmentState = 'reacting';
        this.state.moralFlags.add('confessed_honestly');
        this.state.alignmentShift = 1; // Positive karma

        const sincerity_response = {
            lines: [
                "The Eye's harsh light softens, becoming almost... approving.",
                "\"Truth. I sense truth in your words.\"",
                "\"Your honesty is noted, and your burden acknowledged.\"",
                "\"Confession is the first step toward redemption.\"",
                "\"You may pass, but carry this wisdom: growth comes through truth.\""
            ],
            options: [
                { text: "Thank you for your judgment.", value: "accept_judgment" }
            ]
        };

        await this.showDialogue(sincerity_response);
        return 'sincere';
    }

    /**
     * Handle evasive confession
     */
    async handleEvasiveConfession() {
        console.log(`[EyeOfJudgmentRoom] ⚠️ Evasive confession detected`);

        this.state.eyeJudgmentState = 'reacting';
        this.state.moralFlags.add('confession_incomplete');

        const evasive_response = {
            lines: [
                "The Eye's light flickers with mild disapproval.",
                "\"Your words dance around truth without embracing it.\"",
                "\"You speak, yet reveal little of substance.\"",
                "\"I sense deeper sins beneath your careful words.\"",
                "\"This confession is... incomplete. But it is something.\""
            ],
            options: [
                { text: "I've told you what I can.", value: "defend_evasion" },
                { text: "You're right. Let me try again.", value: "attempt_retry" }
            ]
        };

        const choice = await this.showDialogue(evasive_response);
        
        if (choice === 'attempt_retry' && this.state.confessionAttempts < 3) {
            await this.showSinInputPrompt();
        }
        
        return 'evasive';
    }

    /**
     * Handle joking confession
     */
    async handleJokingConfession() {
        console.log(`[EyeOfJudgmentRoom] 😠 Joke confession detected - Eye becomes angry`);

        this.state.eyeJudgmentState = 'reacting';
        this.state.ritualPowerLevel = 1.0;
        this.state.moralFlags.add('mocked_judgment');
        this.state.alignmentShift = -1; // Negative karma

        const joke_response = {
            lines: [
                "The Eye FLARES with cold rage, its light becoming harsh and accusatory.",
                "\"You DARE make jest of sacred judgment?\"",
                "\"Your frivolity reveals the corruption in your heart!\"",
                "\"You mock what you do not understand!\"",
                "\"Your insolence will be remembered.\""
            ],
            options: [
                { text: "I'm sorry! I was nervous!", value: "apologize" },
                { text: "Lighten up, it's just a game.", value: "double_down" }
            ]
        };

        const choice = await this.showDialogue(joke_response);
        
        if (choice === 'apologize') {
            await this.handleApologyAfterJoke();
        } else {
            await this.handleDoubledDownJoke();
        }
        
        return 'joke';
    }

    /**
     * Handle dismissive confession
     */
    async handleDismissiveConfession() {
        console.log(`[EyeOfJudgmentRoom] 😡 Dismissive confession detected - Eye shows contempt`);

        this.state.eyeJudgmentState = 'reacting';
        this.state.ritualPowerLevel = 1.0;
        this.state.moralFlags.add('contempt_for_judgment');
        this.state.alignmentShift = -2; // Strong negative karma

        const dismissive_response = {
            lines: [
                "The Eye's glow turns ice-cold, filled with ancient contempt.",
                "\"Such disrespect. Such profound emptiness of spirit.\"",
                "\"You treat sacred matters as beneath your notice.\"",
                "\"Your soul is marked by arrogance and callousness.\"",
                "\"This judgment will follow you.\""
            ],
            options: [
                { text: "Whatever.", value: "continue_dismissive" },
                { text: "Wait, I didn't mean...", value: "late_realization" }
            ]
        };

        await this.showDialogue(dismissive_response);
        return 'dismissive';
    }

    /**
     * Complete the judgment and set lasting consequences
     */
    async completeJudgment() {
        console.log(`[EyeOfJudgmentRoom] ⚖️ Completing judgment with sincerity score: ${this.state.sincerityScore}`);

        this.state.judgmentComplete = true;
        this.state.eyeJudgmentState = 'watching';
        this.state.ritualPowerLevel = 0.2;

        // Store judgment results in persistent game state (would integrate with save system)
        this.storeJudgmentResults();

        // Spawn reward chest based on judgment quality
        await this.spawnJudgmentReward();

        // Show doors after judgment
        this.showEventRoomDoors();

        console.log(`[EyeOfJudgmentRoom] 🎭 Judgment complete - consequences set`);
    }

    /**
     * Store judgment results for future reference
     */
    storeJudgmentResults() {
        console.log(`[EyeOfJudgmentRoom] 💾 Storing judgment results for future encounters`);

        // In a real implementation, this would save to persistent storage
        const judgmentData = {
            roomId: this.roomId,
            sincerityScore: this.state.sincerityScore,
            confessionAttempts: this.state.confessionAttempts,
            jokeDetected: this.state.jokeDetected,
            moralFlags: Array.from(this.state.moralFlags),
            alignmentShift: this.state.alignmentShift,
            timestamp: Date.now()
        };

        // Save to player data (simulated)
        if (this.dungeonHandler.player) {
            if (!this.dungeonHandler.player.judgmentHistory) {
                this.dungeonHandler.player.judgmentHistory = [];
            }
            this.dungeonHandler.player.judgmentHistory.push(judgmentData);
            
            // Update moral alignment
            if (!this.dungeonHandler.player.moralAlignment) {
                this.dungeonHandler.player.moralAlignment = 0;
            }
            this.dungeonHandler.player.moralAlignment += this.state.alignmentShift;
        }

        console.log(`[EyeOfJudgmentRoom] ✅ Judgment data saved:`, judgmentData);
    }

    /**
     * Spawn judgment reward with mystical effects
     */
    async spawnJudgmentReward() {
        console.log(`[EyeOfJudgmentRoom] 📦 Spawning judgment reward based on sincerity`);

        // Fade out the Eye and ritual circle first
        await this.fadeOutJudgmentElements();

        // Determine reward quality based on sincerity
        let chestType = 'normal_chest'; // Use standard chest types for loot pool
        
        // Better rewards for higher sincerity
        if (this.state.sincerityScore > 0.7) {
            chestType = 'rare_chest'; // High sincerity gets rare chest loot
        } else if (this.state.sincerityScore < 0.3) {
            chestType = 'normal_chest'; // Low sincerity gets normal chest loot
        } else {
            chestType = 'event_chest'; // Medium sincerity gets event chest loot
        }

        // Spawn item directly at the center without chest
        const spawnPosition = { x: 0, y: 0, z: 0 };
        await this.spawnDirectItemReward(spawnPosition, chestType);
        
        console.log(`[EyeOfJudgmentRoom] ✅ Judgment reward spawned: ${chestType}`);
    }

    /**
     * Fade out the Eye of Judgment and ritual circle
     */
    async fadeOutJudgmentElements() {
        console.log(`[EyeOfJudgmentRoom] 🌫️ Fading out judgment elements`);

        if (!this.dungeonHandler?.currentRoomGroup) return;

        const elementsToFade = [];

        // Find Eye and ritual circle objects
        this.dungeonHandler.currentRoomGroup.traverse(child => {
            if (child.name === 'eye_of_judgment' || 
                child.name === 'ritual_circle_floor' ||
                child.userData?.isJudgingEye ||
                child.userData?.isRitualCircle) {
                elementsToFade.push(child);
            }
        });

        // Fade out each element
        const fadePromises = elementsToFade.map(element => {
            return new Promise(resolve => {
                const fadeStep = () => {
                    element.traverse(child => {
                        if (child.material) {
                            if (Array.isArray(child.material)) {
                                child.material.forEach(mat => {
                                    if (mat.opacity !== undefined) {
                                        mat.transparent = true;
                                        mat.opacity = Math.max(0, mat.opacity - 0.02);
                                    }
                                });
                            } else {
                                child.material.transparent = true;
                                child.material.opacity = Math.max(0, child.material.opacity - 0.02);
                            }
                        }
                    });

                    // Check if fully faded
                    let fullyFaded = true;
                    element.traverse(child => {
                        if (child.material) {
                            const materials = Array.isArray(child.material) ? child.material : [child.material];
                            materials.forEach(mat => {
                                if (mat.opacity > 0) fullyFaded = false;
                            });
                        }
                    });

                    if (fullyFaded) {
                        element.visible = false;
                        resolve();
                    } else {
                        requestAnimationFrame(fadeStep);
                    }
                };
                fadeStep();
            });
        });

        await Promise.all(fadePromises);
        console.log(`[EyeOfJudgmentRoom] ✅ Judgment elements faded out`);
    }

    /**
     * Spawn item directly without chest using modular system
     * @param {Object} position - Spawn position {x, y, z}
     * @param {string} chestType - Chest type for loot pool
     */
    async spawnDirectItemReward(position, chestType) {
        // Use the modular direct item spawning function
        if (this.dungeonHandler.spawnDirectItem) {
            await this.dungeonHandler.spawnDirectItem(
                this.dungeonHandler.scene,
                position,
                chestType,
                Math.random() * 1000
            );
        } else {
            console.warn('[EyeOfJudgmentRoom] Direct item spawning not available - falling back to chest');
            // Fallback to normal chest spawning
            const chestConfig = {
                position: { x: position.x, y: position.y + 1, z: position.z },
                chestType: chestType
            };
            await this.spawnChest(chestConfig);
        }
    }

    /**
     * Start ambient drone sound
     */
    startAmbientDrone() {
        console.log(`[EyeOfJudgmentRoom] 🔊 Starting ambient judgment drone`);
        
        // In a real implementation, this would start a low, ominous ambient sound
        // For now, we'll use the existing audio system
        if (this.dungeonHandler.audioManager) {
            // Use nightmare_chest as placeholder for ambient effect
            setTimeout(() => {
                this.playAudio("nightmare_chest");
            }, 1000);
        }
    }

    /**
     * Start chamber intensification effects
     */
    startChamberIntensification() {
        console.log(`[EyeOfJudgmentRoom] ⚡ Starting chamber power intensification`);

        const duration = 3000; // 3 seconds
        const startTime = Date.now();

        const intensify = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            
            this.state.chamberIntensity = progress * 0.8; // Build to 80% intensity
            this.state.ritualPowerLevel = progress * 0.5; // Build ritual power
            
            if (progress < 1.0) {
                requestAnimationFrame(intensify);
            }
        };

        intensify();
    }

    /**
     * Handle enemy defeat - no enemies in this contemplative room
     */
    handleEnemyDefeat(enemyId) {
        // This room focuses on moral judgment, not combat
        console.log(`[EyeOfJudgmentRoom] No enemies to defeat in the Chamber of Souls`);
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification
    id: "eye_of_judgment",
    name: "The Chamber of Souls",
    shape: "SQUARE_1X1", // Standard square shape for compatibility
    description: "A circular chamber where the Eye of Judgment weighs souls",
    tags: ["judgment", "moral", "confession", "soul", "karma"],
    
    // Door connection system
    availableConnections: {
        north: true,    // Can be entered from north (has south door)
        south: true,    // Can be entered from south (has north door)
        east: true,     // Can be entered from east (has west door)
        west: true      // Can be entered from west (has east door)
    },
    primaryEntrance: "west", // Preferred entrance direction
    
    // Door positions for each entrance direction (relative to room center)
    doorPositions: {
        north: { x: 0, y: 0, z: -7 },   // Door on south wall when entered from north
        south: { x: 0, y: 0, z: 7 },    // Door on north wall when entered from south
        east: { x: -7, y: 0, z: 0 },    // Door on west wall when entered from east
        west: { x: 7, y: 0, z: 0 }      // Door on east wall when entered from west
    },
    
    // Custom materials for judgment chamber
    materials: {
        walls: "mystical_stone_brick", // Standard mystical walls for compatibility
        floors: "mystical_stone_floor", // Use mystical stone floor (temporary - will create ritual floor later)
        wallTint: 0x3C3C3C, // Dark gray judgment walls
        floorTint: 0x8B0000  // Dark red floor for judgment atmosphere
    },

    // Static brightness - dim for serious atmosphere
    staticBrightness: 2, // Very dim (2/10) for serious judgment atmosphere
    
    // Lighting configuration - cold blue judgment light
    lighting: {
        ambient: {
            intensity: 0.1, // Very dark ambient
            color: 0x2A2A4A // Dark blue-gray
        },
        eyeLight: {
            position: { x: 0, y: 4, z: 0 }, // Above the floating eye
            intensity: 3.0, // Bright judgment light
            color: 0x1E90FF, // Cold blue
            distance: 20,
            decay: 1.0,
            castShadow: true
        },
        ritualGlow: {
            position: { x: 0, y: 0.5, z: 0 }, // From ritual circle
            intensity: 1.5,
            color: 0x4B0082, // Indigo ritual glow
            distance: 15,
            decay: 1.5
        },
        accent1: {
            position: { x: -8, y: 3, z: 0 }, // West accent
            intensity: 0.8,
            color: 0x483D8B, // Dark slate blue
            distance: 12,
            decay: 2.0
        },
        accent2: {
            position: { x: 8, y: 3, z: 0 }, // East accent
            intensity: 0.8,
            color: 0x483D8B, // Dark slate blue
            distance: 12,
            decay: 2.0
        }
    },
    
    // Object placement using local coordinates
    objects: [
        // === CENTRAL EYE OF JUDGMENT (MAIN INTERACTION) ===
        {
            type: "eye_of_judgment",
            position: { x: 0, y: 4, z: 0 }, // Floating above center
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "central_eye_of_judgment",
                isInteractable: true,
                interactionType: "eye_judgment",
                isFloating: true,
                isEventObject: true,
                interaction: {
                    type: "eye_judgment",
                    id: "central_eye_of_judgment"
                }
            }
        },

        // === RITUAL CIRCLE FLOOR (CENTERPIECE) ===
        {
            type: "ritual_circle_floor",
            position: { x: 0, y: 0, z: 0 }, // Center of chamber
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.0, z: 1.0 },
            userData: {
                objectId: "central_ritual_circle",
                isFloorDecoration: true,
                isRitualCircle: true,
                isFloor: true, // Disable collision
                isInteractable: true, // Make it interactable
                interactionType: "ritual_activation"
            }
        },

        // === JUDGMENT PILLARS (Cardinal Points) ===
        {
            type: "judgment_pillar",
            position: { x: 0, y: 0, z: -8 }, // North - moved closer to center to avoid door collision
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1.0, y: 1.2, z: 1.0 },
            userData: {
                objectId: "pillar_north",
                isDecorative: true,
                cardinalDirection: "north"
            }
        },
        {
            type: "judgment_pillar",
            position: { x: 8, y: 0, z: 0 }, // East - moved closer to center to avoid door collision
            rotation: { x: 0, y: Math.PI / 2, z: 0 },
            scale: { x: 1.0, y: 1.2, z: 1.0 },
            userData: {
                objectId: "pillar_east",
                isDecorative: true,
                cardinalDirection: "east"
            }
        },
        {
            type: "judgment_pillar",
            position: { x: 0, y: 0, z: 8 }, // South - moved closer to center to avoid door collision
            rotation: { x: 0, y: Math.PI, z: 0 },
            scale: { x: 1.0, y: 1.2, z: 1.0 },
            userData: {
                objectId: "pillar_south",
                isDecorative: true,
                cardinalDirection: "south"
            }
        },
        {
            type: "judgment_pillar",
            position: { x: -8, y: 0, z: 0 }, // West - moved closer to center to avoid door collision
            rotation: { x: 0, y: -Math.PI / 2, z: 0 },
            scale: { x: 1.0, y: 1.2, z: 1.0 },
            userData: {
                objectId: "pillar_west",
                isDecorative: true,
                cardinalDirection: "west"
            }
        },

        // === SOUL LANTERNS (Atmospheric lighting) ===
        {
            type: "soul_lantern",
            position: { x: -6, y: 0, z: -6 }, // NW - moved closer to center to avoid door collision
            rotation: { x: 0, y: Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "soul_lantern_nw",
                isDecorative: true,
                hasLight: true
            }
        },
        {
            type: "soul_lantern",
            position: { x: 6, y: 0, z: -6 }, // NE - moved closer to center to avoid door collision
            rotation: { x: 0, y: -Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "soul_lantern_ne",
                isDecorative: true,
                hasLight: true
            }
        },
        {
            type: "soul_lantern",
            position: { x: 6, y: 0, z: 6 }, // SE - moved closer to center to avoid door collision
            rotation: { x: 0, y: -3 * Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "soul_lantern_se",
                isDecorative: true,
                hasLight: true
            }
        },
        {
            type: "soul_lantern",
            position: { x: -6, y: 0, z: 6 }, // SW - moved closer to center to avoid door collision
            rotation: { x: 0, y: 3 * Math.PI / 4, z: 0 },
            scale: { x: 0.8, y: 1.0, z: 0.8 },
            userData: {
                objectId: "soul_lantern_sw",
                isDecorative: true,
                hasLight: true
            }
        },

        // === CONFESSION STONES (Smaller ritual markers) ===
        {
            type: "confession_stone",
            position: { x: -4, y: 0, z: 0 }, // West marker - moved closer to center to avoid door collision
            rotation: { x: 0, y: Math.PI / 6, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: {
                objectId: "confession_stone_west",
                isDecorative: true
            }
        },
        {
            type: "confession_stone",
            position: { x: 4, y: 0, z: 0 }, // East marker - moved closer to center to avoid door collision
            rotation: { x: 0, y: -Math.PI / 6, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: {
                objectId: "confession_stone_east",
                isDecorative: true
            }
        },
        {
            type: "confession_stone",
            position: { x: 0, y: 0, z: -4 }, // North marker - moved closer to center to avoid door collision
            rotation: { x: 0, y: Math.PI / 8, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: {
                objectId: "confession_stone_north",
                isDecorative: true
            }
        },
        {
            type: "confession_stone",
            position: { x: 0, y: 0, z: 4 }, // South marker - moved closer to center to avoid door collision
            rotation: { x: 0, y: -Math.PI / 8, z: 0 },
            scale: { x: 0.7, y: 0.9, z: 0.7 },
            userData: {
                objectId: "confession_stone_south",
                isDecorative: true
            }
        },

        // === MORAL CRYSTALS (Atmospheric details) ===
        {
            type: "moral_crystal",
            position: { x: -3, y: 0, z: -7 },
            rotation: { x: 0, y: Math.PI / 12, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_1", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: 3, y: 0, z: -7 },
            rotation: { x: 0, y: -Math.PI / 12, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_2", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: 7, y: 0, z: -3 },
            rotation: { x: 0, y: Math.PI / 10, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_3", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: 7, y: 0, z: 3 },
            rotation: { x: 0, y: -Math.PI / 10, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_4", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: 3, y: 0, z: 7 },
            rotation: { x: 0, y: Math.PI / 9, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_5", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: -3, y: 0, z: 7 },
            rotation: { x: 0, y: -Math.PI / 9, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_6", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: -7, y: 0, z: 3 },
            rotation: { x: 0, y: Math.PI / 11, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_7", isDecorative: true }
        },
        {
            type: "moral_crystal",
            position: { x: -7, y: 0, z: -3 },
            rotation: { x: 0, y: -Math.PI / 11, z: 0 },
            scale: { x: 0.6, y: 1.1, z: 0.6 },
            userData: { objectId: "moral_crystal_8", isDecorative: true }
        }
    ]
};

// Export both the room handler class and room data
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: EyeOfJudgmentRoom
};