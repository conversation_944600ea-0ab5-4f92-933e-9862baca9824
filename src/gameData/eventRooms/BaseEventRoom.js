import * as THREE from 'three';

/**
 * Base class for all event rooms
 * Each event room should extend this class and implement its own logic
 */
export class BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        this.roomId = roomId;
        this.dungeonHandler = dungeonHandler;
        this.roomData = roomData;
        this.state = {
            triggered: false,
            enemiesSpawned: false,
            enemiesDefeated: false,
            dialogueCompleted: false,
            objectsRemoved: new Set(),
            chestsSpawned: new Set()
        };
    }

    /**
     * Handle interaction with objects in this room (legacy string-based)
     * @param {string} objectId - ID of the object being interacted with
     * @param {Object} context - Interaction context
     */
    async handleInteraction(objectId, context) {
        console.log(`[${this.constructor.name}] Handling interaction with ${objectId}`);
        // Override in subclasses
    }

    /**
     * Handle interaction with objects using the new data-driven system
     * @param {THREE.Object3D} intersectedObject - The intersected object from raycaster
     * @param {Object} context - Interaction context
     */
    async handleObjectInteraction(intersectedObject, context) {
        console.log(`[${this.constructor.name}] Handling object interaction`);
        
        // Find the interactive parent object
        const interactiveObject = this.findInteractiveParent(intersectedObject);
        
        if (!interactiveObject) {
            console.warn(`[${this.constructor.name}] No interactive object found in hierarchy`);
            return;
        }

        // Check for new interaction data structure
        if (interactiveObject.userData.interaction) {
            const interaction = interactiveObject.userData.interaction;
            console.log(`[${this.constructor.name}] Processing interaction:`, interaction);
            
            await this.processInteraction(interaction, interactiveObject, context);
        } else if (interactiveObject.userData.objectId) {
            // Fallback to legacy string-based system
            console.log(`[${this.constructor.name}] Fallback to legacy interaction system`);
            await this.handleInteraction(interactiveObject.userData.objectId, context);
        } else {
            console.warn(`[${this.constructor.name}] No interaction data found on object`);
        }
    }

    /**
     * Process a data-driven interaction
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async processInteraction(interaction, interactiveObject, context) {
        console.log(`[${this.constructor.name}] Processing interaction type: ${interaction.type}`);
        
        switch (interaction.type) {
            case 'start_dialogue':
                await this.handleDialogueInteraction(interaction, interactiveObject, context);
                break;
            case 'spawn_enemies':
                await this.handleEnemySpawnInteraction(interaction, interactiveObject, context);
                break;
            case 'remove_object':
                await this.handleRemoveObjectInteraction(interaction, interactiveObject, context);
                break;
            default:
                console.warn(`[${this.constructor.name}] Unhandled interaction type: ${interaction.type}`);
                // Fallback to subclass-specific handling
                await this.handleCustomInteraction(interaction, interactiveObject, context);
        }
    }

    /**
     * Handle custom interactions (to be overridden by subclasses)
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleCustomInteraction(interaction, interactiveObject, context) {
        console.warn(`[${this.constructor.name}] Custom interaction not implemented for type: ${interaction.type}`);
    }

    /**
     * Handle dialogue interactions
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleDialogueInteraction(interaction, interactiveObject, context) {
        if (interaction.dialogueId) {
            // TODO: Load dialogue data by ID
            console.log(`[${this.constructor.name}] Starting dialogue: ${interaction.dialogueId}`);
        }
    }

    /**
     * Handle enemy spawn interactions
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleEnemySpawnInteraction(interaction, interactiveObject, context) {
        if (interaction.enemySpawns) {
            this.spawnEnemies(interaction.enemySpawns);
        }
    }

    /**
     * Handle object removal interactions
     * @param {Object} interaction - The interaction data object
     * @param {THREE.Object3D} interactiveObject - The interactive object
     * @param {Object} context - Interaction context
     */
    async handleRemoveObjectInteraction(interaction, interactiveObject, context) {
        if (interaction.targetObjectId) {
            this.removeObject(interaction.targetObjectId);
        }
    }

    /**
     * Show dialogue using the chest interaction system
     * @param {Object} dialogueData - Dialogue configuration
     */
    async showDialogue(dialogueData) {
        const chestInteractionSystem = this.dungeonHandler.chestInteractionSystem;
        if (!chestInteractionSystem) {
            console.error(`[${this.constructor.name}] ChestInteractionSystem not available for dialogue`);
            return null;
        }

        // Disable player movement during dialogue
        chestInteractionSystem.disablePlayerMovement();

        try {
            // Validate dialogue data
            if (!dialogueData || !dialogueData.lines) {
                console.error(`[${this.constructor.name}] Invalid dialogue data - missing lines:`, dialogueData);
                return null;
            }

            if (!dialogueData.options || !Array.isArray(dialogueData.options)) {
                console.error(`[${this.constructor.name}] Invalid dialogue data - missing or invalid options:`, dialogueData);
                return null;
            }

            // Combine dialogue lines into single text
            const dialogueText = dialogueData.lines.join('\n\n');

            // Convert options to the format expected by showSimpleDialogue
            const options = dialogueData.options.map(option => ({
                text: option.text,
                value: option.value
            }));

            // Show dialogue using the chest interaction system's dialogue method
            const choice = await chestInteractionSystem.showSimpleDialogue(dialogueText, options);
            console.log(`[${this.constructor.name}] Player chose: ${choice}`);
            
            return choice;
        } finally {
            // Re-enable player movement
            chestInteractionSystem.enablePlayerMovement();
        }
    }

    /**
     * Spawn enemies for this room
     * @param {Array} enemySpawns - Enemy spawn configurations
     */
    spawnEnemies(enemySpawns) {
        if (!enemySpawns || enemySpawns.length === 0) {
            console.log(`[${this.constructor.name}] No enemies to spawn`);
            return;
        }

        console.log(`[${this.constructor.name}] Spawning ${enemySpawns.length} enemies`);

        // Hide doors when enemies spawn
        this.hideEventRoomDoors();

        enemySpawns.forEach((enemySpawn, index) => {
            setTimeout(() => {
                this.spawnEnemy(enemySpawn);
            }, enemySpawn.spawnDelay * 1000);
        });

        this.state.enemiesSpawned = true;
    }

    /**
     * Spawn a single enemy
     * @param {Object} enemySpawn - Enemy spawn data
     */
    spawnEnemy(enemySpawn) {
        if (!this.dungeonHandler._spawnEnemy) {
            console.warn(`[${this.constructor.name}] Dungeon handler spawn method not available`);
            return;
        }

        const worldPosition = new THREE.Vector3(
            enemySpawn.position.x,
            enemySpawn.position.y,
            enemySpawn.position.z
        );

        const enemy = this.dungeonHandler._spawnEnemy(enemySpawn.type, worldPosition);

        if (enemy) {
            console.log(`[${this.constructor.name}] Spawned enemy: ${enemySpawn.type} at (${worldPosition.x}, ${worldPosition.y}, ${worldPosition.z})`);
        } else {
            console.warn(`[${this.constructor.name}] Failed to spawn enemy: ${enemySpawn.type}`);
        }
    }

    /**
     * Remove an object from the room
     * @param {string} objectId - Object ID to remove
     */
    removeObject(objectId) {
        console.log(`[${this.constructor.name}] Removing object ${objectId}`);

        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.warn(`[${this.constructor.name}] No current room group found`);
            return;
        }

        let objectToRemove = null;
        roomGroup.traverse(child => {
            if (child.userData?.rodId === objectId ||
                child.userData?.pondId === objectId ||
                child.userData?.objectId === objectId ||
                child.name === objectId) {
                objectToRemove = child;
            }
        });

        if (objectToRemove) {
            this.animateObjectRemoval(objectToRemove, () => {
                roomGroup.remove(objectToRemove);
                console.log(`[${this.constructor.name}] ✅ Removed object ${objectId}`);
            });
            this.state.objectsRemoved.add(objectId);
        } else {
            console.warn(`[${this.constructor.name}] Object ${objectId} not found for removal`);
        }
    }

    /**
     * Animate object removal with fade out effect
     * @param {THREE.Object3D} object - Object to remove
     * @param {Function} onComplete - Callback when animation completes
     */
    animateObjectRemoval(object, onComplete) {
        const duration = 1000;
        const startTime = Date.now();
        const originalOpacity = {};

        // Store original opacity values
        object.traverse(child => {
            if (child.isMesh && child.material) {
                originalOpacity[child.uuid] = child.material.opacity || 1.0;
                if (!child.material.transparent) {
                    child.material.transparent = true;
                }
            }
        });

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            const opacity = 1.0 - progress;

            object.traverse(child => {
                if (child.isMesh && child.material) {
                    child.material.opacity = originalOpacity[child.uuid] * opacity;
                }
            });

            if (progress < 1.0) {
                requestAnimationFrame(animate);
            } else {
                onComplete();
            }
        };

        animate();
    }

    /**
     * Spawn a treasure chest
     * @param {Object} chestConfig - Chest configuration
     */
    async spawnChest(chestConfig) {
        console.log(`[${this.constructor.name}] Spawning chest`);

        try {
            const { createTreasureChestObject } = await import('../../generators/prefabs/treasureChestObject.js');
            
            const chestId = `event_chest_${this.roomId}_${Date.now()}`;
            
            const chest = await createTreasureChestObject({
                userData: {
                    chestId: chestId,
                    chestType: chestConfig.chestType || 'event_chest',
                    isEventChest: true,
                    eventRoomId: this.roomId,
                    roomId: this.roomId,
                    isInteractable: true,
                    containsItem: true
                }
            });

            if (chest) {
                chest.position.set(
                    chestConfig.position.x,
                    chestConfig.position.y,
                    chestConfig.position.z
                );

                this.animateChestSpawn(chest, () => {
                    this.dungeonHandler.scene.add(chest);

                    if (this.dungeonHandler.collisionObjects) {
                        this.dungeonHandler.collisionObjects.push(chest);
                    }

                    console.log(`[${this.constructor.name}] ✅ Chest spawned at (${chestConfig.position.x}, ${chestConfig.position.y}, ${chestConfig.position.z})`);
                });

                this.state.chestsSpawned.add(chestId);
            }
        } catch (error) {
            console.error(`[${this.constructor.name}] Failed to spawn chest:`, error);
        }
    }

    /**
     * Animate chest spawn with scale-up effect
     * @param {THREE.Object3D} chest - Chest object
     * @param {Function} onComplete - Callback when animation completes
     */
    animateChestSpawn(chest, onComplete) {
        const duration = 1000;
        const startTime = Date.now();

        chest.scale.set(0, 0, 0);

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);

            const scale = 1.0 - Math.pow(1.0 - progress, 3);
            chest.scale.set(scale, scale, scale);

            if (progress < 1.0) {
                requestAnimationFrame(animate);
            } else {
                onComplete();
            }
        };

        animate();
    }

    /**
     * Handle enemy defeat
     * @param {string} enemyId - Enemy ID that was defeated
     */
    handleEnemyDefeat(enemyId) {
        console.log(`[${this.constructor.name}] Enemy ${enemyId} defeated`);

        // TIMING FIX: Check if all enemies are defeated AFTER the current enemy is processed
        // The enemy defeat is called BEFORE the enemy is removed from activeEnemies array
        // So we need to check if remainingEnemies <= 1 (the one being defeated)
        if (this.dungeonHandler && this.dungeonHandler.activeEnemies) {
            const remainingEnemies = this.dungeonHandler.activeEnemies.length;
            console.log(`[${this.constructor.name}] Remaining enemies: ${remainingEnemies}`);

            // FIXED: Check for === 0 because the defeated enemy is already removed from the array
            if (remainingEnemies === 0) {
                console.log(`[${this.constructor.name}] Last enemy defeated - showing doors`);
                this.showEventRoomDoors();
                this.state.enemiesDefeated = true;
            }
        }

        // Override in subclasses for specific behavior
    }

    /**
     * Hide doors in the event room
     */
    hideEventRoomDoors() {
        console.log(`[${this.constructor.name}] Hiding event room doors`);

        if (this.dungeonHandler && this.dungeonHandler.eventRoomManager) {
            this.dungeonHandler.eventRoomManager.hideEventRoomDoors(this.roomId);
        } else {
            console.warn(`[${this.constructor.name}] Cannot hide doors - event room manager not available`);
        }
    }

    /**
     * Show doors in the event room
     */
    showEventRoomDoors() {
        console.log(`[${this.constructor.name}] Showing event room doors`);

        if (this.dungeonHandler && this.dungeonHandler.eventRoomManager) {
            this.dungeonHandler.eventRoomManager.showEventRoomDoors(this.roomId);
        } else {
            console.warn(`[${this.constructor.name}] Cannot show doors - event room manager not available`);
        }
    }

    /**
     * Play audio
     * @param {string} audioKey - Audio key to play
     */
    playAudio(audioKey) {
        if (this.dungeonHandler.audioManager && this.dungeonHandler.audioManager.playSound) {
            console.log(`[${this.constructor.name}] Playing audio: ${audioKey}`);
            this.dungeonHandler.audioManager.playSound(audioKey);
        } else {
            console.warn(`[${this.constructor.name}] Audio manager not available for sound: ${audioKey}`);
        }
    }

    /**
     * Get current state
     * @returns {Object} Current room state
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Update state
     * @param {Object} updates - State updates
     */
    updateState(updates) {
        Object.assign(this.state, updates);
        console.log(`[${this.constructor.name}] Updated state:`, updates);
    }

    /**
     * Find the interactive parent object from an intersected child
     * Traverses up the object hierarchy to find the object with interaction data
     * @param {THREE.Object3D} intersectedObject - The object that was intersected by raycaster
     * @returns {THREE.Object3D|null} The interactive parent object or null if not found
     */
    findInteractiveParent(intersectedObject) {
        let current = intersectedObject;
        let maxDepth = 10; // Prevent infinite loops
        let depth = 0;

        while (current && depth < maxDepth) {
            // Check if this object has interaction data
            if (current.userData && current.userData.isInteractable && current.userData.interaction) {
                console.log(`[${this.constructor.name}] Found interactive parent at depth ${depth}:`, current.userData.interaction);
                return current;
            }

            // Also check for legacy objectId for backwards compatibility
            if (current.userData && current.userData.isInteractable && current.userData.objectId) {
                console.log(`[${this.constructor.name}] Found legacy interactive parent at depth ${depth}:`, current.userData.objectId);
                return current;
            }

            current = current.parent;
            depth++;
        }

        console.warn(`[${this.constructor.name}] No interactive parent found for object`, intersectedObject);
        return null;
    }

    /**
     * Clean up room resources and effects
     * Base implementation - can be overridden by subclasses
     */
    cleanup() {
        // Base implementation, can be overridden by subclasses.
        console.log(`[BaseEventRoom] Cleaning up room ${this.roomId}`);
    }
}
