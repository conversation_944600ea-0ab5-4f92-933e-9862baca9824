import * as THREE from 'three';
import { BaseEventRoom } from './BaseEventRoom.js';

/**
 * EVENT ROOM TEMPLATE
 *
 * NEW ARCHITECTURE - Each event room now has its own handler class!
 *
 * HOW TO ADD NEW EVENT ROOMS:
 *
 * 1. CREATE NEW PREFABS (if needed):
 *    - Create new object files in src/generators/prefabs/ (e.g., magicAltarObject.js)
 *    - Follow existing prefab patterns (use shared.js, proper voxel data, materials)
 *    - Register in src/prefabs/prefabs.js under interior category
 *
 * 2. CREATE EVENT ROOM:
 *    - Copy this template: cp _template.js newRoomName.js
 *    - Edit the handler class below (rename TemplateRoom to YourRoomName)
 *    - Implement handleInteraction() method with your room's logic
 *    - Edit room data: change id, name, shape, materials, lighting
 *    - Define objects array using prefab types (treasure_chest, egyptian_vase, etc.)
 *
 * 3. REGISTER ROOM:
 *    - Add to index.js in src/gameData/eventRooms/
 *    - Room automatically becomes available in game
 *
 * AVAILABLE PREFAB OBJECTS:
 * - treasure_chest, egyptian_vase, golden_pillar
 * - glowing_pond, fishing_rod, mysterious_crystal
 * - stone_vase, ancient_stone_pillar, torch, vine
 * - (see src/prefabs/prefabs.js for complete list)
 */

/**
 * Template Room Handler
 *
 * CHANGE THIS: Rename this class to match your room (e.g., MagicAltarRoom)
 * This class handles ALL the logic for this specific event room.
 */
export class TemplateRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
    }

    /**
     * Handle interactions in this room
     * CHANGE THIS: Implement your room's interaction logic
     */
    async handleInteraction(objectId, context) {
        console.log(`[TemplateRoom] Handling interaction with ${objectId}`);

        if (objectId === 'template_object') {
            await this.handleTemplateObjectInteraction();
        } else {
            console.warn(`[TemplateRoom] Unknown object interaction: ${objectId}`);
        }
    }

    /**
     * Handle template object interaction
     * CHANGE THIS: Implement your specific object interaction
     */
    async handleTemplateObjectInteraction() {
        if (this.state.triggered) {
            console.log(`[TemplateRoom] Template object already used`);
            return;
        }

        this.state.triggered = true;

        // Example dialogue - CHANGE THIS
        const dialogueData = {
            lines: [
                "You found a mysterious object...",
                "What do you want to do?"
            ],
            options: [
                { text: "Examine", value: "examine" },
                { text: "Leave", value: "leave" }
            ]
        };

        const choice = await this.showDialogue(dialogueData);

        if (choice === 'examine') {
            await this.handleExamineChoice();
        } else if (choice === 'leave') {
            await this.handleLeaveChoice();
        }
    }

    /**
     * Handle "Examine" choice
     * CHANGE THIS: Implement your choice logic
     */
    async handleExamineChoice() {
        console.log(`[TemplateRoom] Player chose to examine`);

        // Example: spawn enemies - CHANGE THIS
        const enemySpawns = [
            {
                type: "skeleton",
                position: { x: 2, y: 1, z: 2 },
                spawnDelay: 1.0
            }
        ];

        this.spawnEnemies(enemySpawns);
    }

    /**
     * Handle "Leave" choice
     * CHANGE THIS: Implement your choice logic
     */
    async handleLeaveChoice() {
        console.log(`[TemplateRoom] Player chose to leave`);
        this.state.triggered = false; // Allow retry
    }

    /**
     * Handle enemy defeat
     * CHANGE THIS: Implement what happens when enemies are defeated
     */
    handleEnemyDefeat(enemyId) {
        super.handleEnemyDefeat(enemyId);

        if (!this.state.enemiesDefeated) {
            this.state.enemiesDefeated = true;

            console.log(`[TemplateRoom] All enemies defeated - spawning reward`);

            // Example: spawn treasure chest - CHANGE THIS
            setTimeout(() => {
                this.spawnChest({
                    position: { x: 0, y: 1, z: 0 },
                    chestType: "event_chest"
                });
            }, 1000);
        }
    }
}

export const EVENT_ROOM_DATA = {
    // Room identification (REQUIRED)
    id: "your_room_id", // CHANGE THIS - unique identifier (no spaces, use underscores)
    name: "Your Room Name", // CHANGE THIS - display name
    shape: "SQUARE_1X1", // CHANGE THIS - room shape (SQUARE_1X1, L_SHAPE_1X2, CROSS_SHAPE, etc.)
    description: "Your room description", // CHANGE THIS - brief description
    tags: ["category1", "category2"], // OPTIONAL - tags for filtering/organization
    
    // Visual overrides (OPTIONAL - leave null to use area defaults)
    materials: {
        walls: null, // Material name or null for default
        floors: null, // Material name or null for default
        wallTint: null, // Hex color (0xFFFFFF) or null
        floorTint: null // Hex color (0xFFFFFF) or null
    },
    
    // Lighting configuration (OPTIONAL)
    lighting: {
        ambient: {
            intensity: 0.3, // 0.0 to 1.0
            color: 0x404040 // Hex color
        },
        // Add additional lights as needed:
        // spotlight: { position: {x,y,z}, target: {x,y,z}, intensity, color, angle, etc. }
        // pointLight: { position: {x,y,z}, intensity, color, distance, decay }
    },
    
    // Object placement using local coordinates (REQUIRED - at least one object)
    // ALL OBJECTS NOW USE PREFAB SYSTEM - see available types above
    objects: [
        {
            type: "treasure_chest", // CHANGE THIS - use prefab types (treasure_chest, egyptian_vase, golden_pillar, etc.)
            position: { x: 0, y: 0, z: 0 }, // CHANGE THIS - position in room
            rotation: { x: 0, y: 0, z: 0 }, // CHANGE THIS - rotation in radians
            scale: { x: 1, y: 1, z: 1 }, // CHANGE THIS - scale multiplier
            userData: {
                // CHANGE THIS - object-specific data
                chestId: "unique_chest_id", // For treasure chests
                objectId: "unique_object_id", // For other objects
                isInteractable: true, // true if player can interact
                interactionType: "treasure_chest" // type of interaction
            }
        }
        // Add more objects as needed - all use prefab system now
    ]

    // NOTE: Enemy spawns and mechanics are now handled by the room handler class above!
    // No need to define enemySpawns or mechanics here anymore.
    // All logic is in the TemplateRoom class methods.
};

// Export both the room handler class and room data
// CHANGE THIS: Update the handler reference to match your class name
export default {
    [EVENT_ROOM_DATA.id]: EVENT_ROOM_DATA,
    handler: TemplateRoom
};
