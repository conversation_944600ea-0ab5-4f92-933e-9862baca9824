import * as THREE from 'three';
import { BaseEventRoom } from '../eventRooms/BaseEventRoom.js';
import { getPrefabFunction } from '../../prefabs/prefabs.js';
import { cutsceneManager } from '../../cutscenes/CutsceneManager.js';

/**
 * Catacombs Boss Room
 *
 * The final chamber of the catacombs, featuring three mystical rune monoliths
 * arranged in a triangle formation. This room will house the first major boss battle.
 */
export class CatacombsBossRoom extends BaseEventRoom {
    constructor(roomId, dungeonHandler, roomData) {
        super(roomId, dungeonHandler, roomData);
        console.log(`[CatacombsBossRoom] 🏛️ Constructor called for room ${roomId}`);
        
        this.state = {
            ...this.state,
            bossSpawned: false,
            entranceCutscenePlayed: false
        };
    }

    /**
     * Initialize room when player enters
     */
    initialize() {
        console.log(`[CatacombsBossRoom] 🏛️ Initializing Catacombs Boss Room`);
        console.log(`[CatacombsBossRoom] Current room group available:`, !!this.dungeonHandler.currentRoomGroup);
        console.log(`[CatacombsBossRoom] Scene available:`, !!this.dungeonHandler.scene);
        
        // Monoliths are now placed by BossRoomManager during room generation
        // This follows the event room pattern for consistency
        console.log(`[CatacombsBossRoom] Monoliths already placed by BossRoomManager during generation`);
        
        // Add atmospheric effects if needed
        this.addAtmosphericEffects();
        
        // Check if we should play the entrance cutscene
        const skipCutscene = window.debugSkipBossCutscenes || false;
        
        if (!this.state.entranceCutscenePlayed && !skipCutscene) {
            // Wait for room to be fully loaded and any transitions to complete
            this.waitForRoomReady(() => {
                // Double-check the flag in case of race conditions
                if (!this.state.entranceCutscenePlayed) {
                    console.log(`[CatacombsBossRoom] 🎬 Auto-triggering entrance cutscene`);
                    this.state.entranceCutscenePlayed = true;
                    this.playEntranceCutscene();
                }
            });
        } else {
            if (skipCutscene) {
                console.log(`[CatacombsBossRoom] 🎬 Cutscene skipped due to debug flag`);
            } else {
                console.log(`[CatacombsBossRoom] 🎬 Entrance cutscene already played, skipping`);
            }
            // Still mark it as played to prevent future triggers
            this.state.entranceCutscenePlayed = true;
        }
    }


    /**
     * Play entrance cutscene when entering the boss room
     */
    playEntranceCutscene() {
        console.log(`[CatacombsBossRoom] 🎬 Playing entrance cutscene`);
        
        // Play the cutscene using the cutscene manager
        const context = {
            room: this,
            roomId: this.roomId,
            onCutsceneComplete: () => {
                console.log(`[CatacombsBossRoom] 🎬 Entrance cutscene complete`);
                // You can add additional logic here after cutscene completes
                // For example, spawn the boss or trigger dialogue
                this.onEntranceCutsceneComplete();
            }
        };
        
        // Play the registered cutscene
        cutsceneManager.play('catacombs_boss_entrance', this.dungeonHandler, context);
    }
    
    /**
     * Called when entrance cutscene completes
     */
    onEntranceCutsceneComplete() {
        // Add any post-cutscene logic here
        // For example:
        // - Spawn the boss
        // - Start boss music
        // - Show boss health bar
        // - Trigger dialogue
        console.log(`[CatacombsBossRoom] Ready to spawn boss!`);
        
        // Spawn the Void Lord boss
        this.spawnVoidLordBoss();
    }
    
    /**
     * Spawn the Void Lord boss
     */
    spawnVoidLordBoss() {
        console.log(`[CatacombsBossRoom] 🌌 Spawning Void Lord boss!`);
        
        // Spawn at the center of the boss room
        const bossPosition = { x: 0, y: 0, z: 0 };
        
        // Spawn the Void Lord
        this.spawnEnemyAtPosition('void_lord', bossPosition, 0);
        
        // Add dramatic effect after spawn
        setTimeout(() => {
            console.log(`[CatacombsBossRoom] 🌌 Void Lord has arrived!`);
            
            // Trigger boss music if available
            if (this.dungeonHandler.audioManager && this.dungeonHandler.audioManager.musicConductor) {
                // Start boss music
                console.log(`[CatacombsBossRoom] 🎵 Starting boss music`);
            }
        }, 1000);
    }
    
    /**
     * Spawn enemies at predefined coordinates
     * This is used instead of the normal enemy spawn system
     */
    spawnPredefinedEnemies() {
        console.log(`[CatacombsBossRoom] Spawning predefined enemies`);
        
        // Define enemy spawn data with exact local coordinates
        const enemySpawns = [
            // Example spawns - you can modify these coordinates
            { type: 'skeleton', position: { x: -5, y: 0, z: -5 } },
            { type: 'skeleton', position: { x: 5, y: 0, z: -5 } },
            { type: 'zombie', position: { x: 0, y: 0, z: 10 } }
        ];
        
        // Spawn each enemy at the specified position
        enemySpawns.forEach((spawn, index) => {
            this.spawnEnemyAtPosition(spawn.type, spawn.position, index);
        });
    }
    
    /**
     * Spawn a single enemy at a specific position
     * @param {string} enemyType - Type of enemy to spawn
     * @param {Object} position - Local coordinates {x, y, z}
     * @param {number} index - Index for unique ID
     */
    spawnEnemyAtPosition(enemyType, position, index) {
        console.log(`[CatacombsBossRoom] Spawning ${enemyType} at position:`, position);
        
        // Use the dungeon handler's enemy spawn method
        if (this.dungeonHandler._spawnEnemy) {
            // Convert local coordinates to world coordinates if needed
            const worldPosition = this.dungeonHandler.localCoordinateSystem 
                ? this.dungeonHandler.localCoordinateSystem.localToWorld(
                    new THREE.Vector3(position.x, position.y, position.z)
                )
                : new THREE.Vector3(position.x, position.y, position.z);
            
            // Spawn the enemy
            const enemy = this.dungeonHandler._spawnEnemy(enemyType);
            
            if (enemy && enemy.position) {
                // Set the exact position
                enemy.position.copy(worldPosition);
                
                // Tag the enemy as a boss room enemy
                enemy.userData.isBossRoomEnemy = true;
                enemy.userData.bossRoomId = this.roomId;
                
                console.log(`[CatacombsBossRoom] ✅ Spawned ${enemyType} at world position:`, worldPosition);
            }
        } else {
            console.error(`[CatacombsBossRoom] ❌ Unable to spawn enemy - _spawnEnemy method not found`);
        }
    }

    /**
     * Wait for room to be fully ready before triggering cutscene
     * @param {Function} callback - Function to call when room is ready
     */
    waitForRoomReady(callback) {
        const checkInterval = 100; // Check every 100ms
        const maxWaitTime = 5000; // Maximum wait of 5 seconds
        let waitTime = 0;
        
        const checkReady = () => {
            waitTime += checkInterval;
            
            // Check if we've waited too long
            if (waitTime > maxWaitTime) {
                console.warn(`[CatacombsBossRoom] ⚠️ Timeout waiting for room ready, proceeding anyway`);
                callback();
                return;
            }
            
            // Check various conditions that indicate room is ready
            const sceneManager = this.dungeonHandler.sceneManager;
            const player = this.dungeonHandler.player;
            const playerController = this.dungeonHandler.playerController;
            
            // Conditions to check:
            // 1. No fade transition in progress
            // 2. Player exists and is positioned
            // 3. Player controller is ready
            // 4. Room transition cooldown has passed
            const isFading = sceneManager && sceneManager.isFading;
            const playerReady = player && player.position && playerController;
            const transitionReady = !this.dungeonHandler.transitionCooldown || 
                                   this.dungeonHandler.transitionCooldown <= 0;
            
            if (!isFading && playerReady && transitionReady) {
                console.log(`[CatacombsBossRoom] ✅ Room is ready after ${waitTime}ms`);
                // Add a small additional delay to ensure everything is settled
                setTimeout(callback, 200);
            } else {
                // Not ready yet, check again
                console.log(`[CatacombsBossRoom] ⏳ Waiting for room ready... (${waitTime}ms)`);
                setTimeout(checkReady, checkInterval);
            }
        };
        
        // Start checking
        checkReady();
    }

    /**
     * Add atmospheric effects to the room
     */
    addAtmosphericEffects() {
        // Add some mystical particle effects around the monoliths
        // This could include floating runes, energy fields, etc.
        console.log(`[CatacombsBossRoom] ✨ Adding atmospheric effects`);
        
        // For now, just add some ambient lighting
        const ambientLight = new THREE.PointLight(0x4a90e2, 0.3, 10);
        ambientLight.position.set(0, 2, 0);
        
        // Add to current room group instead of scene directly
        if (this.dungeonHandler.currentRoomGroup) {
            this.dungeonHandler.currentRoomGroup.add(ambientLight);
            console.log(`[CatacombsBossRoom] ✨ Added ambient light to room group`);
        } else {
            console.warn(`[CatacombsBossRoom] ⚠️ No room group available for ambient light`);
        }
        
        // Store reference for cleanup
        this.state.ambientLight = ambientLight;
    }

    /**
     * Handle player interaction with objects in the room
     */
    handleInteraction(interactableId) {
        console.log(`[CatacombsBossRoom] 🤝 Player interacted with: ${interactableId}`);
        
        if (interactableId.startsWith('monolith_')) {
            this.handleMonolithInteraction(interactableId);
        }
    }

    /**
     * Handle interaction with a monolith
     */
    handleMonolithInteraction(monolithId) {
        console.log(`[CatacombsBossRoom] 🗿 Monolith interaction: ${monolithId}`);
        
        // For now, just log the interaction
        // In the future, this could trigger boss spawning, puzzles, etc.
        const monolithIndex = parseInt(monolithId.split('_').pop());
        console.log(`[CatacombsBossRoom] Player touched monolith ${monolithIndex}`);
    }

    /**
     * Update function called each frame
     */
    update(deltaTime) {
        // Update any ongoing effects
        if (this.state.ambientLight) {
            // Pulsate the ambient light
            const time = Date.now() * 0.001;
            this.state.ambientLight.intensity = 0.3 + Math.sin(time) * 0.1;
        }
    }

    /**
     * Cleanup when leaving the room
     */
    cleanup() {
        console.log(`[CatacombsBossRoom] 🧹 Cleaning up boss room`);
        
        // Remove ambient light
        if (this.state.ambientLight) {
            if (this.dungeonHandler.currentRoomGroup) {
                this.dungeonHandler.currentRoomGroup.remove(this.state.ambientLight);
            }
            this.state.ambientLight = null;
        }
        
        // Let base class handle remaining cleanup
        super.cleanup();
    }
}

// Export the room configuration
export const catacombsBossRoomConfig = {
    id: 'catacombs_boss_room',
    name: 'Catacombs Chamber',
    type: 'boss',
    handler: CatacombsBossRoom,
    shape: 'BOSS_ARENA',
    music: null, // No special music for now
    entranceDirection: null, // Boss rooms can be entered from any direction
    
    // Visual configuration
    walls: ['stonebrick'],
    floors: ['cave_floor'],
    doors: ['stone_archway'],
    
    // Lighting
    lighting: {
        ambient: 0.3,
        intensity: 1.2
    },
    
    // Interior objects (handled by the room itself now)
    interiorObjects: [
        { type: 'torch', probability: 1.0, minQuantity: 8, maxQuantity: 12, placement: 'wall' },
        { type: 'stone_pillar', probability: 1.0, minQuantity: 4, maxQuantity: 4, placement: 'floor', placementDetail: ['corners'], isDestructible: false }
    ],
    
    // No enemies for now
    enemies: [],
    
    // Door positions for BOSS_ARENA shape
    doorPositions: {
        n: { x: 0, y: 0, z: -20.0 },
        s: { x: 0, y: 0, z: 20.0 },
        e: { x: 20.0, y: 0, z: 0 },
        w: { x: -20.0, y: 0, z: 0 }
    }
};