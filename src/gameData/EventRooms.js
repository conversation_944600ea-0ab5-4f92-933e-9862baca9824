/**
 * Event Room Registry - Modular Event Room System
 *
 * This file serves as the main entry point for all event rooms.
 * Individual event rooms are now defined in separate files for better organization.
 *
 * To add a new event room:
 * 1. Create a new .js file in src/gameData/eventRooms/ (e.g., hauntedLibrary.js)
 * 2. Export EVENT_ROOM_DATA with the room configuration
 * 3. Add the import to src/gameData/eventRooms/index.js
 * 4. The room will be automatically available in the game
 *
 * Benefits of this approach:
 * - Each room is self-contained and easy to edit
 * - No merge conflicts when multiple people add rooms
 * - Easy to find and modify specific rooms
 * - Scales to hundreds of rooms without performance issues
 * - Individual rooms can be easily disabled/enabled
 */

// Import the auto-loader that discovers all event room files
import {
    EVENT_ROOMS,
    getAvailableEventRooms,
    getEventRoom,
    validateEventRoom,
    getRandomUnusedEventRoom,
    getEventRoomsByTag,
    getEventRoomStats
} from './eventRooms/index.js';

// Re-export everything for backward compatibility
export {
    EVENT_ROOMS,
    getAvailableEventRooms,
    getEventRoom,
    validateEventRoom,
    getRandomUnusedEventRoom,
    getEventRoomsByTag,
    getEventRoomStats
};

// Legacy compatibility functions (now handled by index.js)
export function isEventRoomUsed(eventRoomKey) {
    // This will be implemented by the EventRoomManager
    return false;
}
