/**
 * Defines item pools for different biomes, room types, and enemy types.
 * These pools are used by the item drop system to determine what items can drop in different contexts.
 */

import { ITEM_TYPES, ITEM_RARITY, getItemsByBiome, getItemsByRoomType, getItemsByRarity } from '../entities/ItemTypes.js';
import { mulberry32 } from '../generators/prefabs/shared.js';

// --- Rarity Weight Definitions ---
// These define the probability weights for different rarities in different contexts
export const RARITY_WEIGHTS = {
    // Normal enemy drops
    normal_enemy: {
        [ITEM_RARITY.COMMON]: 90,
        [ITEM_RARITY.RARE]: 10,
        [ITEM_RARITY.EPIC]: 0,
        [ITEM_RARITY.LEGENDARY]: 0
    },
    // Elite enemy drops
    elite_enemy: {
        [ITEM_RARITY.COMMON]: 60,
        [ITEM_RARITY.RARE]: 35,
        [ITEM_RARITY.EPIC]: 5,
        [ITEM_RARITY.LEGENDARY]: 0
    },
    // Mini-boss drops
    mini_boss: {
        [ITEM_RARITY.COMMON]: 0,
        [ITEM_RARITY.RARE]: 85,
        [ITEM_RARITY.EPIC]: 15,
        [ITEM_RARITY.LEGENDARY]: 0
    },
    // Boss drops
    boss: {
        [ITEM_RARITY.COMMON]: 0,
        [ITEM_RARITY.RARE]: 30,
        [ITEM_RARITY.EPIC]: 60,
        [ITEM_RARITY.LEGENDARY]: 10
    },
    // Secret room drops
    secret_room: {
        [ITEM_RARITY.COMMON]: 10,
        [ITEM_RARITY.RARE]: 60,
        [ITEM_RARITY.EPIC]: 25,
        [ITEM_RARITY.LEGENDARY]: 5
    },
    // Soul door room drops
    soul_door: {
        [ITEM_RARITY.COMMON]: 0,
        [ITEM_RARITY.RARE]: 70,
        [ITEM_RARITY.EPIC]: 25,
        [ITEM_RARITY.LEGENDARY]: 5
    },
    // Destructible object drops
    destructible: {
        [ITEM_RARITY.COMMON]: 95,
        [ITEM_RARITY.RARE]: 5,
        [ITEM_RARITY.EPIC]: 0,
        [ITEM_RARITY.LEGENDARY]: 0
    }
};

// --- Soul Weight Influence Definitions ---
// These define how soul weight affects item drop probabilities
export const SOUL_WEIGHT_MODIFIERS = {
    light: {
        light: 2.0,    // Double chance for light items
        dark: 0.5,     // Half chance for dark items
        balanced: 1.0, // Normal chance for balanced items
        neutral: 1.0   // Normal chance for neutral items
    },
    dark: {
        light: 0.5,    // Half chance for light items
        dark: 2.0,     // Double chance for dark items
        balanced: 1.0, // Normal chance for balanced items
        neutral: 1.0   // Normal chance for neutral items
    },
    balanced: {
        light: 1.0,    // Normal chance for light items
        dark: 1.0,     // Normal chance for dark items
        balanced: 2.0, // Double chance for balanced items
        neutral: 1.0   // Normal chance for neutral items
    }
};

// --- Relic Influence Definitions ---
// These define how active relics affect item drop probabilities
export const RELIC_MODIFIERS = {
    zeituhr: {
        // Time-related items more likely when Zeituhr is equipped
        modifiedItems: [ITEM_TYPES.SOUL_BLADE, ITEM_TYPES.SPECTRAL_BOW],
        modifier: 1.5 // 50% more likely
    },
    golden_mirror: {
        // Reflective items more likely when Golden Mirror is equipped
        modifiedItems: [ITEM_TYPES.GOLDEN_MIRROR, ITEM_TYPES.BALANCED_ESSENCE],
        modifier: 1.5 // 50% more likely
    },
    family_photo: {
        // Story-based items more likely when Family Photo is equipped
        modifiedItems: [ITEM_TYPES.FAMILY_PHOTO, ITEM_TYPES.HEART_CONTAINER],
        modifier: 1.5 // 50% more likely
    }
};

// --- Biome-Specific Item Pools ---
// These define additional items that are more likely to drop in specific biomes
export const BIOME_ITEM_POOLS = {
    catacombs: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.SOUL_BLADE,
        ITEM_TYPES.KEY
    ],
    fungal_caverns: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.SOUL_BOMB,
        ITEM_TYPES.DARK_ESSENCE
    ],
    flooded_ruins: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.SPECTRAL_BOW,
        ITEM_TYPES.SOUL_HEART,
        ITEM_TYPES.LIGHT_ESSENCE
    ],
    crystal_caves: [
        ITEM_TYPES.FROST_WAND,
        ITEM_TYPES.GOLDEN_MIRROR,
        ITEM_TYPES.BALANCED_ESSENCE
    ],
    ancient_library: [
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.TREASURE_MAP,
        ITEM_TYPES.LIGHT_ESSENCE
    ],
    lava_tubes: [
        ITEM_TYPES.FROST_WAND,
        ITEM_TYPES.SOUL_BOMB,
        ITEM_TYPES.DARK_ESSENCE
    ],
    obsidian_fortress: [
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.SOUL_BLADE,
        ITEM_TYPES.DARK_ESSENCE
    ],
    astral_plane: [
        ITEM_TYPES.GOLDEN_MIRROR,
        ITEM_TYPES.BALANCED_ESSENCE,
        ITEM_TYPES.FAMILY_PHOTO
    ],
    final_boss_arena: [
        ITEM_TYPES.FAMILY_PHOTO,
        ITEM_TYPES.HEART_CONTAINER,
        ITEM_TYPES.BALANCED_ESSENCE
    ]
};

// --- Room Type Item Pools ---
// These define additional items that are more likely to drop in specific room types
export const ROOM_TYPE_ITEM_POOLS = {
    normal: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.KEY,
        ITEM_TYPES.SOUL_BOMB
    ],
    elite: [
        ITEM_TYPES.SOUL_HEART,
        ITEM_TYPES.SOUL_BLADE,
        ITEM_TYPES.SPECTRAL_BOW,
        ITEM_TYPES.FROST_WAND
    ],
    mini_boss: [
        ITEM_TYPES.SOUL_HEART,
        ITEM_TYPES.SOUL_BLADE,
        ITEM_TYPES.SPECTRAL_BOW,
        ITEM_TYPES.FROST_WAND
    ],
    boss: [
        ITEM_TYPES.HEART_CONTAINER,
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.GOLDEN_MIRROR,
        ITEM_TYPES.FAMILY_PHOTO
    ],
    secret: [
        ITEM_TYPES.TREASURE_MAP,
        ITEM_TYPES.HEART_CONTAINER,
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.GOLDEN_MIRROR
    ],
    soul_door: [
        ITEM_TYPES.LIGHT_ESSENCE,
        ITEM_TYPES.DARK_ESSENCE,
        ITEM_TYPES.BALANCED_ESSENCE,
        ITEM_TYPES.SOUL_HEART
    ],
    trap: [
        ITEM_TYPES.SOUL_HEART
    ],
    shop: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.KEY,
        ITEM_TYPES.SOUL_BOMB,
        ITEM_TYPES.TREASURE_MAP
    ]
};

// --- Enemy Type Item Pools ---
// These define additional items that are more likely to drop from specific enemy types
export const ENEMY_TYPE_ITEM_POOLS = {
    skeleton: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.KEY
    ],
    skeleton_archer: [
        ITEM_TYPES.SPECTRAL_BOW,
        ITEM_TYPES.SOUL_BOMB
    ],
    skeleton_warrior: [
        ITEM_TYPES.SOUL_BLADE
    ],
    skeleton_assassin: [
        ITEM_TYPES.DARK_ESSENCE,
        ITEM_TYPES.SOUL_BOMB
    ],
    bat: [
        ITEM_TYPES.SOUL_POTION,
        ITEM_TYPES.SOUL_BOMB
    ],
    ghost: [
        ITEM_TYPES.LIGHT_ESSENCE,
        ITEM_TYPES.SOUL_HEART
    ],
    skeleton_boss: [
        ITEM_TYPES.HEART_CONTAINER,
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.SOUL_BLADE
    ],
    catacombs_overlord: [
        ITEM_TYPES.HEART_CONTAINER,
        ITEM_TYPES.ZEITUHR,
        ITEM_TYPES.SOUL_BLADE,
        ITEM_TYPES.GOLDEN_MIRROR
    ]
};

/**
 * Get the appropriate rarity weights based on the context
 * @param {string} enemyType - The type of enemy (normal, elite, mini_boss, boss)
 * @param {string} roomType - The type of room
 * @returns {object} The rarity weights to use
 */
export function getRarityWeights(enemyType, roomType) {
    // If it's a boss room, use boss weights regardless of enemy type
    if (roomType === 'boss') {
        return RARITY_WEIGHTS.boss;
    }

    // If it's a special room type, use those weights
    if (roomType === 'secret') {
        return RARITY_WEIGHTS.secret_room;
    }

    if (roomType === 'soul_door') {
        return RARITY_WEIGHTS.soul_door;
    }

    // Otherwise use weights based on enemy type
    switch (enemyType) {
        case 'elite':
            return RARITY_WEIGHTS.elite_enemy;
        case 'mini_boss':
            return RARITY_WEIGHTS.mini_boss;
        case 'boss':
            return RARITY_WEIGHTS.boss;
        case 'destructible':
            return RARITY_WEIGHTS.destructible;
        default:
            return RARITY_WEIGHTS.normal_enemy;
    }
}

/**
 * Get a random item from a weighted pool
 * @param {Array} pool - Array of item types
 * @param {object} weights - Object mapping item types to weights
 * @param {function} random - Random number generator function
 * @returns {string} The selected item type
 */
export function getRandomItemFromWeightedPool(pool, weights, random = Math.random) {
    if (!pool || pool.length === 0) {
        console.error("Item pool is empty!");
        return null;
    }

    // Calculate total weight
    let totalWeight = 0;
    const weightedPool = pool.map(itemType => {
        const weight = weights[itemType] || 1;
        totalWeight += weight;
        return { itemType, weight };
    });

    // Select random item based on weight
    let randomWeight = random() * totalWeight;
    for (const item of weightedPool) {
        randomWeight -= item.weight;
        if (randomWeight <= 0) {
            return item.itemType;
        }
    }

    // Fallback (should never reach here if weights are positive)
    return pool[0];
}

/**
 * Get a random rarity based on weights
 * @param {object} rarityWeights - Object mapping rarities to weights
 * @param {function} random - Random number generator function
 * @returns {string} The selected rarity
 */
export function getRandomRarity(rarityWeights, random = Math.random) {
    const rarities = Object.keys(rarityWeights);
    const weights = Object.values(rarityWeights);

    // Calculate total weight
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

    // Select random rarity based on weight
    let randomWeight = random() * totalWeight;
    for (let i = 0; i < rarities.length; i++) {
        randomWeight -= weights[i];
        if (randomWeight <= 0) {
            return rarities[i];
        }
    }

    // Fallback (should never reach here if weights are positive)
    return rarities[0];
}

/**
 * Build a pool of items based on the context
 * @param {string} biome - The current biome
 * @param {string} roomType - The type of room
 * @param {string} enemyType - The type of enemy
 * @returns {Array} Array of item types that can drop in this context
 */
export function buildItemPool(biome, roomType, enemyType) {
    // Start with items allowed in this biome
    let pool = getItemsByBiome(biome);

    // Add items allowed in this room type
    pool = [...new Set([...pool, ...getItemsByRoomType(roomType)])];

    // Add biome-specific items
    if (BIOME_ITEM_POOLS[biome]) {
        pool = [...new Set([...pool, ...BIOME_ITEM_POOLS[biome]])];
    }

    // Add room type-specific items
    if (ROOM_TYPE_ITEM_POOLS[roomType]) {
        pool = [...new Set([...pool, ...ROOM_TYPE_ITEM_POOLS[roomType]])];
    }

    // Add enemy type-specific items
    if (ENEMY_TYPE_ITEM_POOLS[enemyType]) {
        pool = [...new Set([...pool, ...ENEMY_TYPE_ITEM_POOLS[enemyType]])];
    }

    return pool;
}

// Make the main definitions read-only
Object.freeze(RARITY_WEIGHTS);
Object.freeze(SOUL_WEIGHT_MODIFIERS);
Object.freeze(RELIC_MODIFIERS);
Object.freeze(BIOME_ITEM_POOLS);
Object.freeze(ROOM_TYPE_ITEM_POOLS);
Object.freeze(ENEMY_TYPE_ITEM_POOLS);
