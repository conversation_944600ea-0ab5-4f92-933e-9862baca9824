/**
 * BossMusicAnalyzer.js
 *
 * Analyzes music in real-time using FFT to extract intensity, rhythm, and frequency information.
 * Used for syncing bullet patterns and boss behavior to music.
 */

import { PitchDetector } from './PitchDetector.js';

export class BossMusicAnalyzer {
    /**
     * Constructor for BossMusicAnalyzer
     * @param {Object} options - Configuration options
     * @param {Number} options.smoothingFactor - Smoothing factor for intensity values (0-1)
     * @param {Number} options.updateInterval - Update interval in ms
     * @param {Number} options.fftSize - FFT size (must be power of 2)
     */
    constructor(options = {}) {
        // Configuration
        this.smoothingFactor = options.smoothingFactor || 0.8; // Higher = smoother
        this.updateInterval = options.updateInterval || 150; // 150ms = ~6.7 updates per second (reduced from 50ms)
        this.fftSize = options.fftSize || 1024; // FFT size (must be power of 2)

        // Analysis state
        this.isAnalyzing = false;
        this.analyzer = null;
        this.audioSource = null;
        this.updateIntervalId = null;

        // Enhanced frequency bands (Hz) for more detailed analysis
        this.bands = {
            subBass: { min: 20, max: 60 },     // Sub-bass (rumble, felt more than heard)
            bass: { min: 60, max: 250 },       // Bass (fundamental notes of rhythm section)
            lowMid: { min: 250, max: 500 },    // Low midrange (lower harmonics of instruments)
            midrange: { min: 500, max: 2000 }, // Midrange (vocals, most instruments)
            upperMid: { min: 2000, max: 4000 },// Upper midrange (presence, clarity)
            treble: { min: 4000, max: 12000 }, // Treble (brightness, air)
            highFreq: { min: 12000, max: 20000 }// High frequency (overtones, cymbals)
        };

        // Enhanced intensity values (0-100) with more detailed tracking
        this.intensity = {
            overall: 0,
            subBass: 0,
            bass: 0,
            lowMid: 0,
            midrange: 0,
            upperMid: 0,
            treble: 0,
            highFreq: 0,
            peak: 0,
            history: [],
            // Track changes for detecting musical transitions
            delta: 0,           // Rate of change in overall intensity
            deltaHistory: [],   // History of delta values for trend analysis
            // Spectral features
            spectralCentroid: 0, // Center of gravity of the spectrum
            spectralFlux: 0,     // Rate of change of the spectrum
            spectralFlatness: 0, // How noise-like vs. tone-like the sound is
            // Enhanced musical feature detection
            isArpeggio: false,      // Detected arpeggio pattern
            noteChangeRate: 0,      // Rate of note changes (0-1)
            noteChangeSpeed: 0,     // Speed of note changes (0-2)
            patternComplexity: 0,   // Complexity of note patterns (0-1)
            isBuildUp: false,       // Detected build-up section
            isBreakdown: false,     // Detected breakdown section
            isDrop: false,          // Detected drop section
            isTransition: false,    // Detected transition between sections
            // Note pattern characteristics
            isStaccato: false,      // Short, detached notes
            isLegato: false,        // Smooth, connected notes
            isTremolo: false,       // Rapid repetition of a single note
            isTrill: false,         // Rapid alternation between two notes
            // Tempo tracking
            tempo: 120,          // Estimated tempo in BPM
            tempoConfidence: 0,  // Confidence in tempo estimate (0-1)
            beatPhase: 0,        // Current phase within beat (0-1)
            // Section tracking
            currentSection: 'intro', // Current musical section
            sectionStartTime: 0,     // When current section started
            sectionIntensity: 0,     // Average intensity of current section
            // Melody tracking (from pitch detection)
            currentNote: null,    // Current detected note
            noteHistory: [],      // History of recent notes
            melodyPatterns: {     // Detected melody patterns
                ascending: false,
                descending: false,
                alternating: false,
                repeating: false,
                stepwise: false,
                leaping: false
            },
            hasMelody: false,     // Whether a melody is detected
            melodyChangeRate: 0,   // Rate of melody note changes
            lastNoteChangeTime: 0  // Time of last note change
        };

        // Enhanced beat detection with predictive capabilities
        this.beatDetection = {
            threshold: 1.3, // Reduced threshold for more sensitive beat detection
            lastBeatTime: 0,
            minBeatInterval: 180, // Reduced minimum interval for faster response
            energyHistory: new Array(12).fill(0), // Increased history size for better analysis
            // Predictive beat tracking
            predictedNextBeatTime: 0,
            beatIntervalHistory: [], // Track intervals between beats
            confidenceThreshold: 0.6, // Minimum confidence to use predictions
            beatConfidence: 0, // Current confidence in beat prediction (0-1)
            // Beat anticipation
            anticipationWindow: 50, // ms before predicted beat to start anticipation
            isAnticipatingBeat: false
        };

        // Callbacks
        this.onIntensityChange = null;
        this.onBeat = null;
        this.onNoteChange = null;

        // Initialize pitch detector
        this.pitchDetector = new PitchDetector({
            minFrequency: 80,
            maxFrequency: 1200,
            confidenceThreshold: 0.75,
            debug: false
        });

        console.log("[BossMusicAnalyzer] Created");
    }

    /**
     * Initialize the analyzer with an audio source
     * @param {Object} audioSource - Tone.js Player or other audio source
     * @returns {Boolean} Success
     */
    init(audioSource) {
        try {
            if (typeof Tone === 'undefined') {
                console.error("[BossMusicAnalyzer] Tone.js is not loaded");
                return false;
            }

            console.log("[BossMusicAnalyzer] Initializing with audio source...");

            // Create analyzer
            this.analyzer = new Tone.FFT(this.fftSize);
            this.analyzer.smoothing = 0.4; // FFT smoothing (different from our intensity smoothing)

            // Initialize pitch detector
            this.pitchDetector.initialize(Tone.context);

            // Connect audio source to analyzer
            this.audioSource = audioSource;

            // Make sure the audio source is a valid Tone.js object
            if (!this.audioSource || typeof this.audioSource.connect !== 'function') {
                console.error("[BossMusicAnalyzer] Invalid audio source:", this.audioSource);
                return false;
            }

            // Connect the audio source to the analyzer
            try {
                this.audioSource.connect(this.analyzer);
                console.log("[BossMusicAnalyzer] Connected audio source to analyzer");
            } catch (connectError) {
                console.error("[BossMusicAnalyzer] Error connecting audio source to analyzer:", connectError);

                // Try an alternative approach
                console.log("[BossMusicAnalyzer] Trying alternative connection approach...");
                try {
                    // Create a new analyzer node directly from the audio context
                    const audioContext = Tone.context;
                    const analyzerNode = audioContext.createAnalyser();
                    analyzerNode.fftSize = this.fftSize * 2; // FFT size must be double for the analyzer node
                    analyzerNode.smoothingTimeConstant = 0.4;

                    // Connect the audio source to the analyzer node
                    this.audioSource.connect(analyzerNode);

                    // Store the analyzer node
                    this.analyzerNode = analyzerNode;
                    console.log("[BossMusicAnalyzer] Connected using Web Audio API directly");
                } catch (alternativeError) {
                    console.error("[BossMusicAnalyzer] Alternative connection also failed:", alternativeError);
                    return false;
                }
            }

            console.log("[BossMusicAnalyzer] Initialized with audio source successfully");
            return true;
        } catch (error) {
            console.error("[BossMusicAnalyzer] Error initializing:", error);
            return false;
        }
    }

    /**
     * Start analyzing the audio
     * @returns {Boolean} Success
     */
    start() {
        if (!this.analyzer || !this.audioSource) {
            console.error("[BossMusicAnalyzer] Cannot start: analyzer or audio source not initialized");
            return false;
        }

        if (this.isAnalyzing) {
            console.warn("[BossMusicAnalyzer] Already analyzing");
            return true;
        }

        // Initialize start time for timeline tracking
        this._startTime = Date.now();

        // Start update interval
        this.updateIntervalId = setInterval(() => this._update(), this.updateInterval);
        this.isAnalyzing = true;

        console.log("[BossMusicAnalyzer] Started analyzing");
        return true;
    }

    /**
     * Stop analyzing the audio
     */
    stop() {
        if (this.updateIntervalId) {
            clearInterval(this.updateIntervalId);
            this.updateIntervalId = null;
        }

        this.isAnalyzing = false;
        console.log("[BossMusicAnalyzer] Stopped analyzing");
    }

    /**
     * Set callback for intensity changes
     * @param {Function} callback - Function to call when intensity changes
     */
    setIntensityCallback(callback) {
        this.onIntensityChange = callback;
    }

    /**
     * Set callback for beat detection
     * @param {Function} callback - Function to call when a beat is detected
     */
    setBeatCallback(callback) {
        this.onBeat = callback;
    }

    /**
     * Set callback for beat anticipation
     * @param {Function} callback - Function to call when a beat is anticipated
     */
    setBeatAnticipationCallback(callback) {
        this.onBeatAnticipation = callback;
    }

    /**
     * Set callback for note changes in melody
     * @param {Function} callback - Function to call when a note change is detected
     */
    setNoteChangeCallback(callback) {
        this.onNoteChange = callback;
    }

    /**
     * Get current intensity values
     * @returns {Object} Intensity values
     */
    getIntensity() {
        return { ...this.intensity };
    }

    /**
     * Get current music time in seconds
     * @returns {Number} Current time in seconds
     */
    getCurrentTime() {
        // For testing, use the current time since the analyzer was started
        const now = Date.now();
        const startTime = this._startTime || now;
        const elapsedSeconds = (now - startTime) / 1000;

        console.log(`[BossMusicAnalyzer] getCurrentTime: ${elapsedSeconds.toFixed(1)}s`);
        return elapsedSeconds;
    }

    /**
     * Update analysis (called on interval)
     * @private
     */
    _update() {
        if (!this.isAnalyzing) return;

        try {
            let fftData;

            // Get FFT data from the appropriate source
            if (this.analyzer) {
                // Use Tone.js FFT analyzer
                fftData = this.analyzer.getValue();
            } else if (this.analyzerNode) {
                // Use Web Audio API analyzer node
                const dataArray = new Float32Array(this.analyzerNode.frequencyBinCount);
                this.analyzerNode.getFloatFrequencyData(dataArray);
                fftData = dataArray;
            } else {
                // No analyzer available
                console.warn("[BossMusicAnalyzer] No analyzer available for update");
                return;
            }

            // Always use simulated data for testing purposes
            // This ensures we always have data for the debug overlay
            console.log("[BossMusicAnalyzer] Using simulated FFT data for testing");
            fftData = this._generateSimulatedFFTData();

            // Calculate intensity for each band
            const subBassIntensity = this._calculateBandIntensity(fftData, this.bands.subBass);
            const bassIntensity = this._calculateBandIntensity(fftData, this.bands.bass);
            const lowMidIntensity = this._calculateBandIntensity(fftData, this.bands.lowMid);
            const midrangeIntensity = this._calculateBandIntensity(fftData, this.bands.midrange);
            const upperMidIntensity = this._calculateBandIntensity(fftData, this.bands.upperMid);
            const trebleIntensity = this._calculateBandIntensity(fftData, this.bands.treble);
            const highFreqIntensity = this._calculateBandIntensity(fftData, this.bands.highFreq);

            // Calculate overall intensity (weighted average)
            const rawOverallIntensity = (subBassIntensity * 0.1) +
                                        (bassIntensity * 0.3) +
                                        (lowMidIntensity * 0.15) +
                                        (midrangeIntensity * 0.2) +
                                        (upperMidIntensity * 0.1) +
                                        (trebleIntensity * 0.1) +
                                        (highFreqIntensity * 0.05);

            // Apply smoothing to all bands
            this.intensity.subBass = this._smoothValue(this.intensity.subBass, subBassIntensity);
            this.intensity.bass = this._smoothValue(this.intensity.bass, bassIntensity);
            this.intensity.lowMid = this._smoothValue(this.intensity.lowMid, lowMidIntensity);
            this.intensity.midrange = this._smoothValue(this.intensity.midrange, midrangeIntensity);
            this.intensity.upperMid = this._smoothValue(this.intensity.upperMid, upperMidIntensity);
            this.intensity.treble = this._smoothValue(this.intensity.treble, trebleIntensity);
            this.intensity.highFreq = this._smoothValue(this.intensity.highFreq, highFreqIntensity);
            this.intensity.overall = this._smoothValue(this.intensity.overall, rawOverallIntensity);

            // Calculate rate of change (delta)
            const previousOverall = this.intensity.overall;
            const currentOverall = rawOverallIntensity;
            this.intensity.delta = currentOverall - previousOverall;

            // Store delta in history
            this.intensity.deltaHistory.push(this.intensity.delta);
            if (this.intensity.deltaHistory.length > 20) { // Keep last 20 values
                this.intensity.deltaHistory.shift();
            }

            // Update peak intensity
            if (this.intensity.overall > this.intensity.peak) {
                this.intensity.peak = this.intensity.overall;
            }

            // Normalize to 0-100 range based on peak
            const normalizedIntensity = this.intensity.peak > 0
                ? (this.intensity.overall / this.intensity.peak) * 100
                : this.intensity.overall;

            // Calculate spectral features
            this._calculateSpectralFeatures(fftData);

            // Detect musical features with enhanced note pattern detection
            const notePatternInfo = this._detectArpeggios(fftData);
            this.intensity.isArpeggio = notePatternInfo.isArpeggio;
            this.intensity.noteChangeRate = notePatternInfo.noteChangeRate;
            this.intensity.noteChangeSpeed = notePatternInfo.noteChangeSpeed;
            this.intensity.patternComplexity = notePatternInfo.patternComplexity;

            // Detect pitch and melody
            this._detectPitchAndMelody(fftData);

            // Detect additional note patterns based on spectral characteristics
            this.intensity.isStaccato = this.intensity.spectralFlux > 0.7 && this.intensity.noteChangeRate > 0.6;
            this.intensity.isLegato = this.intensity.spectralFlux < 0.3 && this.intensity.spectralFlatness < 0.3;
            this.intensity.isTremolo = this.intensity.noteChangeRate > 0.7 && this.intensity.patternComplexity < 0.3;
            this.intensity.isTrill = this.intensity.noteChangeRate > 0.8 && this.intensity.patternComplexity > 0.3 && this.intensity.patternComplexity < 0.6;

            // Detect other musical features
            this.intensity.isBuildUp = this._detectBuildUp();
            this.intensity.isDrop = this._detectDrop();
            this.intensity.isTransition = this._detectTransition();

            // Log detailed note pattern information when significant changes are detected
            if (this.intensity.noteChangeRate > 0.7 || this.intensity.isArpeggio) {
                console.log(`[BossMusicAnalyzer] Note pattern details: \n` +
                           `  Change Rate: ${this.intensity.noteChangeRate.toFixed(2)} \n` +
                           `  Speed: ${this.intensity.noteChangeSpeed.toFixed(2)} \n` +
                           `  Complexity: ${this.intensity.patternComplexity.toFixed(2)} \n` +
                           `  Patterns: ${this.intensity.isArpeggio ? 'Arpeggio ' : ''}` +
                           `${this.intensity.isStaccato ? 'Staccato ' : ''}` +
                           `${this.intensity.isLegato ? 'Legato ' : ''}` +
                           `${this.intensity.isTremolo ? 'Tremolo ' : ''}` +
                           `${this.intensity.isTrill ? 'Trill' : ''}`);
            }

            // Store in history (keep last 60 values = ~3 seconds at 20 updates/sec)
            this.intensity.history.push(normalizedIntensity);
            if (this.intensity.history.length > 60) {
                this.intensity.history.shift();
            }

            // Detect beats
            this._detectBeat(bassIntensity);

            // Call intensity change callback
            if (this.onIntensityChange) {
                this.onIntensityChange(normalizedIntensity);
            }
        } catch (error) {
            console.error("[BossMusicAnalyzer] Error in update:", error);
        }
    }

    /**
     * Generate simulated FFT data for testing
     * @returns {Array} Simulated FFT data
     * @private
     */
    _generateSimulatedFFTData() {
        // Create an array of the appropriate size
        const fftSize = this.fftSize || 1024;
        const data = new Array(fftSize);

        // Current time for animation
        const time = Date.now() / 1000;

        // Create more dynamic patterns
        const bassIntensity = Math.sin(time * 0.5) * 0.5 + 0.5; // 0-1 value that changes slowly
        const midIntensity = Math.sin(time * 1.3) * 0.5 + 0.5; // Different frequency
        const highIntensity = Math.sin(time * 2.7) * 0.5 + 0.5; // Even different frequency

        // Beat detection (faster beats)
        const beatActive = Math.sin(time * 3 * Math.PI) > 0.7;

        // Generate simulated data
        for (let i = 0; i < fftSize; i++) {
            // Normalize frequency (0-1)
            const normalizedFreq = i / fftSize;

            // Base value (louder at lower frequencies)
            let value = -30 - (normalizedFreq * 50);

            // Apply different intensities to different frequency ranges
            if (normalizedFreq < 0.2) { // Bass frequencies
                value += bassIntensity * 30;
            } else if (normalizedFreq < 0.6) { // Mid frequencies
                value += midIntensity * 20;
            } else { // High frequencies
                value += highIntensity * 15;
            }

            // Add some variation based on time
            value += Math.sin(time * 2 + normalizedFreq * 10) * 10;

            // Add a beat
            if (beatActive) {
                // Stronger in bass frequencies, gradually decreasing
                value += Math.max(0, (1 - normalizedFreq * 2)) * 30;
            }

            // Store the value
            data[i] = value;
        }

        return data;
    }

    /**
     * Calculate intensity for a frequency band
     * @param {Array} fftData - FFT data
     * @param {Object} band - Frequency band { min, max }
     * @returns {Number} Band intensity (0-1)
     * @private
     */
    _calculateBandIntensity(fftData, band) {
        // Convert frequency to FFT bin index
        const minBin = Math.floor(band.min * this.fftSize / Tone.context.sampleRate);
        const maxBin = Math.ceil(band.max * this.fftSize / Tone.context.sampleRate);

        // Calculate average intensity in band
        let sum = 0;
        let count = 0;

        for (let i = minBin; i <= maxBin && i < fftData.length; i++) {
            // Convert dB to linear scale (dB is negative, with 0 dB being max)
            const linearValue = Math.pow(10, fftData[i] / 20);
            sum += linearValue;
            count++;
        }

        return count > 0 ? sum / count : 0;
    }

    /**
     * Apply smoothing to a value
     * @param {Number} currentValue - Current value
     * @param {Number} newValue - New value
     * @returns {Number} Smoothed value
     * @private
     */
    _smoothValue(currentValue, newValue) {
        return (currentValue * this.smoothingFactor) + (newValue * (1 - this.smoothingFactor));
    }

    /**
     * Enhanced beat detection with predictive capabilities
     * @param {Number} bassEnergy - Bass band energy
     * @private
     */
    _detectBeat(bassEnergy) {
        // Add current energy to history
        this.beatDetection.energyHistory.push(bassEnergy);
        this.beatDetection.energyHistory.shift();

        const now = Date.now();

        // Calculate weighted average energy with more emphasis on recent samples
        let weightedSum = 0;
        let weightSum = 0;
        for (let i = 0; i < this.beatDetection.energyHistory.length; i++) {
            // Weight increases linearly with index (more recent samples have higher weight)
            const weight = 1 + i / 2;
            weightedSum += this.beatDetection.energyHistory[i] * weight;
            weightSum += weight;
        }
        const avgEnergy = weightedSum / weightSum;

        // Check for beat anticipation (before actual beat)
        if (this.beatDetection.predictedNextBeatTime > 0 &&
            !this.beatDetection.isAnticipatingBeat &&
            this.beatDetection.beatConfidence > this.beatDetection.confidenceThreshold &&
            now >= this.beatDetection.predictedNextBeatTime - this.beatDetection.anticipationWindow) {

            this.beatDetection.isAnticipatingBeat = true;

            // Call beat anticipation callback if available
            if (this.onBeatAnticipation) {
                const timeUntilBeat = this.beatDetection.predictedNextBeatTime - now;
                this.onBeatAnticipation(timeUntilBeat, this.beatDetection.beatConfidence);
                console.log(`[BossMusicAnalyzer] Beat anticipated in ${timeUntilBeat}ms with confidence ${this.beatDetection.beatConfidence.toFixed(2)}`);
            }
        }

        // Check if current energy is significantly higher than average (actual beat detection)
        if (bassEnergy > avgEnergy * this.beatDetection.threshold &&
            now - this.beatDetection.lastBeatTime > this.beatDetection.minBeatInterval) {

            // Calculate beat interval and update history
            if (this.beatDetection.lastBeatTime > 0) {
                const beatInterval = now - this.beatDetection.lastBeatTime;

                // Only store reasonable intervals (between 250ms and 2000ms)
                if (beatInterval >= 250 && beatInterval <= 2000) {
                    this.beatDetection.beatIntervalHistory.push(beatInterval);

                    // Keep last 8 intervals
                    if (this.beatDetection.beatIntervalHistory.length > 8) {
                        this.beatDetection.beatIntervalHistory.shift();
                    }

                    // Update tempo estimate if we have enough data
                    if (this.beatDetection.beatIntervalHistory.length >= 4) {
                        this._updateTempoEstimate();
                    }
                }
            }

            // Update last beat time
            this.beatDetection.lastBeatTime = now;
            this.beatDetection.isAnticipatingBeat = false;

            // Calculate beat strength with improved formula
            // Use both the immediate energy ratio and the trend over time
            const immediateRatio = bassEnergy / avgEnergy;
            const trendFactor = this._calculateEnergyTrend();
            const beatStrength = Math.min(3.0, immediateRatio * (1 + trendFactor * 0.5));

            console.log("[BossMusicAnalyzer] Beat detected! Strength:", beatStrength.toFixed(2));

            // Predict next beat time based on tempo
            this._predictNextBeat();

            // Call beat callback
            if (this.onBeat) {
                this.onBeat(beatStrength); // Pass beat strength
            }
        }

        // For testing: Simulate beats at regular intervals (reduced frequency)
        // Only use simulation when we don't have real beats detected
        if (now - this.beatDetection.lastBeatTime > 1200 && now % 1000 < 30) {
            this.beatDetection.lastBeatTime = now;
            this.beatDetection.isAnticipatingBeat = false;

            // Simulate a beat with random strength
            const simulatedStrength = 1.5 + Math.random() * 1.0;
            console.log("[BossMusicAnalyzer] Simulated beat! Strength:", simulatedStrength.toFixed(2));

            // Call beat callback
            if (this.onBeat) {
                this.onBeat(simulatedStrength);
            }
        }
    }

    /**
     * Detect arpeggios and fast note changes in the frequency spectrum
     * @param {Array} fftData - FFT data
     * @returns {Object} Information about detected note patterns
     * @private
     */
    _detectArpeggios(fftData) {
        // Enhanced arpeggio detection that's more sensitive to fast note changes
        // We'll analyze multiple frequency bands to detect different types of arpeggios

        // Store history of spectral data for temporal analysis
        if (!this._spectralHistory) {
            this._spectralHistory = [];
        }

        // Extract data from different bands for more comprehensive analysis
        const lowMidData = this._extractBandData(fftData, this.bands.lowMid);
        const midrangeData = this._extractBandData(fftData, this.bands.midrange);
        const upperMidData = this._extractBandData(fftData, this.bands.upperMid);

        // Create a spectral fingerprint of the current frame
        const spectralFingerprint = {
            lowMid: this._calculateSpectralCentroid(lowMidData),
            midrange: this._calculateSpectralCentroid(midrangeData),
            upperMid: this._calculateSpectralCentroid(upperMidData),
            timestamp: Date.now()
        };

        // Add to history and maintain a limited window (last 500ms)
        this._spectralHistory.push(spectralFingerprint);
        const currentTime = Date.now();
        this._spectralHistory = this._spectralHistory.filter(item =>
            currentTime - item.timestamp < 500
        );

        // Need at least 5 samples for meaningful analysis
        if (this._spectralHistory.length < 5) {
            return {
                isArpeggio: false,
                noteChangeRate: 0,
                patternComplexity: 0
            };
        }

        // Calculate note change rate by analyzing centroid changes
        const centroidChanges = [];
        for (let i = 1; i < this._spectralHistory.length; i++) {
            const prevCentroid = this._spectralHistory[i-1].midrange;
            const currentCentroid = this._spectralHistory[i].midrange;
            const change = Math.abs(currentCentroid - prevCentroid);
            centroidChanges.push(change);
        }

        // Calculate average change rate
        const avgChangeRate = centroidChanges.reduce((sum, val) => sum + val, 0) / centroidChanges.length;

        // Normalize to 0-1 range (empirically determined thresholds)
        const normalizedChangeRate = Math.min(1, avgChangeRate / 0.1);

        // Look for alternating patterns (sign changes) in all bands
        let totalSignChanges = 0;
        let totalSamples = 0;

        // Check for sign changes in each band
        [lowMidData, midrangeData, upperMidData].forEach(bandData => {
            const differences = [];
            for (let i = 1; i < bandData.length; i++) {
                differences.push(bandData[i] - bandData[i-1]);
            }

            let bandSignChanges = 0;
            for (let i = 1; i < differences.length; i++) {
                if ((differences[i] > 0.05 && differences[i-1] < -0.05) ||
                    (differences[i] < -0.05 && differences[i-1] > 0.05)) {
                    bandSignChanges++;
                }
            }

            totalSignChanges += bandSignChanges;
            totalSamples += differences.length;
        });

        // Calculate sign change ratio across all bands
        const signChangeRatio = totalSamples > 0 ? totalSignChanges / totalSamples : 0;

        // Calculate pattern complexity based on variation in changes
        const changeVariation = centroidChanges.length > 1 ?
            this._calculateStandardDeviation(centroidChanges) : 0;

        // Normalize complexity (empirically determined threshold)
        const patternComplexity = Math.min(1, changeVariation / 0.05);

        // Determine if this is an arpeggio based on multiple factors
        // 1. High note change rate
        // 2. High sign change ratio (alternating pattern)
        // 3. Moderate to high pattern complexity
        // Lowered threshold for faster detection of arpeggios and fast note sequences
        const isArpeggio = normalizedChangeRate > 0.5 && signChangeRatio > 0.25 && patternComplexity > 0.3;

        // Determine the speed of the arpeggio/note changes with enhanced sensitivity
        // Increase the range from 0-2 to 0-4 for more dramatic speed changes with fast notes
        const noteChangeSpeed = normalizedChangeRate * 4; // 0-4 range for more dynamic response

        // Apply non-linear scaling to make fast notes even more pronounced
        // This will make the difference between medium and fast notes more noticeable
        const enhancedNoteChangeSpeed = noteChangeSpeed < 1.0 ?
            noteChangeSpeed : // Keep lower speeds as is
            1.0 + Math.pow(noteChangeSpeed - 1.0, 1.8); // Amplify higher speeds with stronger non-linear curve

        if (isArpeggio) {
            console.log(`[BossMusicAnalyzer] Fast note pattern detected! Change rate: ${normalizedChangeRate.toFixed(2)}, Speed: ${enhancedNoteChangeSpeed.toFixed(2)}, Complexity: ${patternComplexity.toFixed(2)}`);
        } else if (enhancedNoteChangeSpeed > 1.5) {
            // Also log when we detect very fast notes even if not an arpeggio
            console.log(`[BossMusicAnalyzer] Very fast notes detected! Speed: ${enhancedNoteChangeSpeed.toFixed(2)}`);
        }

        return {
            isArpeggio,
            noteChangeRate: normalizedChangeRate,
            noteChangeSpeed: enhancedNoteChangeSpeed, // Use the enhanced speed value
            patternComplexity
        };
    }

    /**
     * Calculate standard deviation of an array of values
     * @param {Array} values - Array of numeric values
     * @returns {Number} Standard deviation
     * @private
     */
    _calculateStandardDeviation(values) {
        const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squareDiffs = values.map(value => Math.pow(value - avg, 2));
        const avgSquareDiff = squareDiffs.reduce((sum, val) => sum + val, 0) / squareDiffs.length;
        return Math.sqrt(avgSquareDiff);
    }

    /**
     * Update tempo estimate based on beat interval history
     * @private
     */
    _updateTempoEstimate() {
        // Calculate average beat interval
        const intervals = this.beatDetection.beatIntervalHistory;

        // Use median to be more robust against outliers
        const sortedIntervals = [...intervals].sort((a, b) => a - b);
        const medianInterval = sortedIntervals[Math.floor(sortedIntervals.length / 2)];

        // Convert to BPM (beats per minute)
        const bpm = 60000 / medianInterval;

        // Calculate confidence based on consistency of intervals
        const stdDev = this._calculateStandardDeviation(intervals);
        const variationCoefficient = stdDev / (intervals.reduce((sum, val) => sum + val, 0) / intervals.length);

        // Higher consistency = higher confidence (inverse relationship with variation)
        this.beatDetection.beatConfidence = Math.max(0, Math.min(1, 1 - variationCoefficient * 2));

        // Update tempo information
        this.intensity.tempo = Math.round(bpm);
        this.intensity.tempoConfidence = this.beatDetection.beatConfidence;

        console.log(`[BossMusicAnalyzer] Tempo estimate: ${this.intensity.tempo} BPM (confidence: ${this.beatDetection.beatConfidence.toFixed(2)})`);
    }

    /**
     * Predict the next beat time based on tempo
     * @private
     */
    _predictNextBeat() {
        if (this.beatDetection.beatIntervalHistory.length < 4 || this.beatDetection.beatConfidence < 0.4) {
            // Not enough data or confidence too low
            this.beatDetection.predictedNextBeatTime = 0;
            return;
        }

        // Use the median interval for prediction
        const sortedIntervals = [...this.beatDetection.beatIntervalHistory].sort((a, b) => a - b);
        const medianInterval = sortedIntervals[Math.floor(sortedIntervals.length / 2)];

        // Predict next beat time
        this.beatDetection.predictedNextBeatTime = this.beatDetection.lastBeatTime + medianInterval;

        console.log(`[BossMusicAnalyzer] Next beat predicted at ${new Date(this.beatDetection.predictedNextBeatTime).toISOString().substr(17, 6)}`);
    }

    /**
     * Calculate energy trend over recent history
     * @returns {Number} Trend factor (-1 to 1, positive means increasing energy)
     * @private
     */
    _calculateEnergyTrend() {
        const history = this.beatDetection.energyHistory;
        if (history.length < 6) return 0;

        // Compare first half to second half
        const firstHalf = history.slice(0, Math.floor(history.length / 2));
        const secondHalf = history.slice(Math.floor(history.length / 2));

        const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;

        // Normalize the trend to -1 to 1 range
        return Math.max(-1, Math.min(1, (secondAvg - firstAvg) / firstAvg * 2));
    }

    /**
     * Detect pitch and melody patterns in the audio
     * @param {Array} fftData - FFT data
     * @private
     */
    _detectPitchAndMelody(fftData) {
        // Convert FFT data to time domain for pitch detection
        // For simulated data, we'll use the pitch detector's simulation
        let pitchResult;

        if (this._isSimulated) {
            // Use simulated pitch data for testing
            pitchResult = this.pitchDetector.generateSimulatedPitch();
        } else {
            // In a real implementation, we would convert FFT to time domain
            // and pass it to the pitch detector
            // This would require additional processing that's beyond the scope
            // of this implementation
            pitchResult = this.pitchDetector.generateSimulatedPitch();
        }

        // Only process if we have a confident pitch detection
        if (pitchResult.confidence >= 0.7 && pitchResult.note) {
            // Update current note
            this.intensity.currentNote = pitchResult.note;

            // Get melody information
            const melodyInfo = this.pitchDetector.getNoteChangeInfo();

            // Update melody patterns
            this.intensity.melodyPatterns = melodyInfo.patterns;
            this.intensity.hasMelody = melodyInfo.hasMelody;
            this.intensity.melodyChangeRate = melodyInfo.rate;

            // Check if this is a new note (note change)
            const now = Date.now();
            const isNewNote = melodyInfo.recentNotes.length > 0 &&
                melodyInfo.recentNotes[melodyInfo.recentNotes.length - 1].timestamp > this.intensity.lastNoteChangeTime;

            if (isNewNote) {
                // Update last note change time
                this.intensity.lastNoteChangeTime = now;

                // Call note change callback if available
                if (this.onNoteChange) {
                    const noteInfo = {
                        note: pitchResult.note,
                        patterns: melodyInfo.patterns,
                        changeRate: melodyInfo.rate,
                        recentNotes: melodyInfo.recentNotes
                    };

                    this.onNoteChange(noteInfo);

                    console.log(`[BossMusicAnalyzer] Note change detected: ${pitchResult.note.name}${pitchResult.note.octave}`);
                }
            }
        }
    }

    /**
     * Calculate spectral centroid of frequency data
     * @param {Array} bandData - Frequency data for a specific band
     * @returns {Number} Spectral centroid
     * @private
     */
    _calculateSpectralCentroid(bandData) {
        let weightedSum = 0;
        let sum = 0;

        for (let i = 0; i < bandData.length; i++) {
            const magnitude = Math.pow(10, bandData[i] / 20); // Convert dB to magnitude
            weightedSum += i * magnitude;
            sum += magnitude;
        }

        return sum > 0 ? weightedSum / sum / bandData.length : 0;
    }

    /**
     * Extract data for a specific frequency band from FFT data
     * @param {Array} fftData - FFT data
     * @param {Object} band - Frequency band definition
     * @returns {Array} Band data
     * @private
     */
    _extractBandData(fftData, band) {
        const { min, max } = band;
        const nyquist = 22050; // Half of standard 44.1kHz sample rate
        const bandData = [];

        for (let i = 0; i < fftData.length; i++) {
            const frequency = i * nyquist / fftData.length;
            if (frequency >= min && frequency <= max) {
                bandData.push(fftData[i]);
            }
        }

        return bandData;
    }

    /**
     * Detect build-ups in the music
     * @returns {Boolean} Whether a build-up was detected
     * @private
     */
    _detectBuildUp() {
        // Build-ups typically show as a steady increase in intensity over time
        // We'll look at the recent history of intensity values

        // Need at least 10 samples to detect a trend
        if (this.intensity.history.length < 10) {
            return false;
        }

        // Calculate the slope of the intensity over the last 10 samples
        const recentHistory = this.intensity.history.slice(-10);
        let increasing = true;

        for (let i = 1; i < recentHistory.length; i++) {
            if (recentHistory[i] < recentHistory[i-1]) {
                increasing = false;
                break;
            }
        }

        // Also check if the overall increase is significant
        const startIntensity = recentHistory[0];
        const endIntensity = recentHistory[recentHistory.length - 1];
        const intensityIncrease = endIntensity - startIntensity;

        const isBuildUp = increasing && intensityIncrease > 15; // 15% threshold

        if (isBuildUp) {
            console.log("[BossMusicAnalyzer] Build-up detected!");
        }

        return isBuildUp;
    }

    /**
     * Detect drops in the music
     * @returns {Boolean} Whether a drop was detected
     * @private
     */
    _detectDrop() {
        // Drops typically show as a sudden increase in bass after a build-up
        // We'll look for a recent build-up followed by a bass spike

        // Need a recent build-up and enough history
        if (!this.intensity.isBuildUp && this.intensity.deltaHistory.length < 5) {
            return false;
        }

        // Check for a sudden increase in bass intensity
        const currentBass = this.intensity.bass;
        const recentBassHistory = this.intensity.deltaHistory.slice(-5);
        const bassIncrease = currentBass - recentBassHistory[0];

        const isDrop = bassIncrease > 30; // 30% threshold

        if (isDrop) {
            console.log("[BossMusicAnalyzer] Drop detected!");
        }

        return isDrop;
    }

    /**
     * Detect transitions between musical sections
     * @returns {Boolean} Whether a transition was detected
     * @private
     */
    _detectTransition() {
        // Transitions typically show as changes in the spectral content
        // We'll look at the spectral flux (rate of change of the spectrum)

        const isTransition = this.intensity.spectralFlux > 0.6; // 60% threshold

        if (isTransition) {
            console.log("[BossMusicAnalyzer] Transition detected!");
        }

        return isTransition;
    }

    /**
     * Calculate spectral features from FFT data
     * @param {Array} fftData - FFT data
     * @private
     */
    _calculateSpectralFeatures(fftData) {
        // Calculate spectral centroid (center of gravity of the spectrum)
        let weightedSum = 0;
        let sum = 0;

        for (let i = 0; i < fftData.length; i++) {
            const magnitude = Math.pow(10, fftData[i] / 20); // Convert dB to magnitude
            weightedSum += i * magnitude;
            sum += magnitude;
        }

        const spectralCentroid = sum > 0 ? weightedSum / sum / fftData.length : 0;

        // Calculate spectral flatness (ratio of geometric mean to arithmetic mean)
        // This distinguishes between noise-like and tone-like sounds
        let geometricMean = 0;
        let arithmeticMean = 0;

        for (let i = 0; i < fftData.length; i++) {
            const magnitude = Math.pow(10, fftData[i] / 20); // Convert dB to magnitude
            if (magnitude > 0) {
                geometricMean += Math.log(magnitude);
            }
            arithmeticMean += magnitude;
        }

        geometricMean = Math.exp(geometricMean / fftData.length);
        arithmeticMean = arithmeticMean / fftData.length;

        const spectralFlatness = arithmeticMean > 0 ? geometricMean / arithmeticMean : 0;

        // Update intensity values
        this.intensity.spectralCentroid = spectralCentroid;
        this.intensity.spectralFlatness = spectralFlatness;
    }

    /**
     * Clean up resources
     */
    dispose() {
        this.stop();

        if (this.analyzer) {
            this.analyzer.dispose();
            this.analyzer = null;
        }

        this.audioSource = null;
        console.log("[BossMusicAnalyzer] Disposed");
    }
}
