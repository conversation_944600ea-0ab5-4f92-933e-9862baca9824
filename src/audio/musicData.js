/**
 * Music Data Configuration
 *
 * This file defines all music assets and their metadata for the adaptive music system.
 * It follows a modular Loop Chain structure with Hit Interrupts, Temporal Insertions, and Emotive Cues.
 */

export const MusicData = {
    // --- Starting Area ---
    "catacombs": {
        // Main theme - single looping file
        mainTheme: "assets/music/catacombs/catacomb_theme.mp3",
        tempo: 100, // BPM
        barLength: 4, // Number of beats per bar

        // Interrupts - Played when specific events occur
        interrupts: {
            // Hit interrupt - Played when player takes damage
            "hit": {
                path: "assets/music/catacombs/interrupts/hit_interrupt.mp3",
                priority: 2 // Higher priority interrupts can override lower ones
            },
            // Other interrupts
            "danger": {
                path: "assets/music/catacombs/interrupts/danger_interrupt.mp3",
                priority: 1
            }
        },

        // Temporal Insertions - One-off loops for narrative/emotional moments
        temporalInsertions: {
            // Narrative insertions
            "memory_recall": {
                path: "assets/music/catacombs/insertions/memory_recall.mp3",
                type: "narrative",
                priority: 3
            },
            "dialogue": {
                path: "assets/music/catacombs/insertions/dialogue.mp3",
                type: "narrative",
                priority: 3
            },
            // Emotive insertions
            "fear": {
                path: "assets/music/catacombs/insertions/fear.mp3",
                type: "emotive",
                priority: 2
            }
        },

        // Legacy support for leitmotifs
        leitmotifs: {
            "mother": "assets/music/catacombs/leitmotifs/mother_layer.mp3",
            "guilt": "assets/music/catacombs/leitmotifs/guilt_layer.mp3"
        },

        // Special rooms
        specialRooms: {
            "shop": {
                mainTheme: "assets/music/catacombs/placeholder.mp3",
                // No transitions available yet
                enterTransition: null,
                exitTransition: null
            },
            "pond_event": {
                mainTheme: "assets/music/events/pond_event.wav",
                // No transitions available yet  
                enterTransition: null,
                exitTransition: null
            }
        },

        // Miniboss music (played during miniboss fights in event rooms)
        minibosses: {
            "sairabos": {
                mainTheme: "assets/music/events/sairabos_fight.wav",
                enterTransition: null,
                exitTransition: null
            },
            "nairabos": {
                mainTheme: "assets/music/events/sairabos_fight.wav", // Same music for both variants
                enterTransition: null,
                exitTransition: null
            }
        },

        // Area transitions
        transitions: {
            "lava_tubes": "assets/music/catacombs/placeholder.mp3"
        }
    },

    // --- Other Areas (to be implemented) ---
    "lava_tubes": {
        // For now, just use the catacombs theme as placeholder
        mainTheme: "assets/music/catacombs/placeholder.mp3",
        tempo: 110, // Faster tempo for more intense area
        barLength: 4,
        interrupts: {
            "hit": {
                path: "assets/music/catacombs/interrupts/hit_interrupt.mp3", // Use catacombs as placeholder
                priority: 2
            }
        },
        temporalInsertions: {},
        leitmotifs: {},
        specialRooms: {
            "pond_event": {
                mainTheme: "assets/music/events/pond_event.wav",
                // No transitions available yet  
                enterTransition: null,
                exitTransition: null
            }
        },

        // Miniboss music (played during miniboss fights in event rooms)
        minibosses: {
            "sairabos": {
                mainTheme: "assets/music/events/sairabos_fight.wav",
                enterTransition: null,
                exitTransition: null
            },
            "nairabos": {
                mainTheme: "assets/music/events/sairabos_fight.wav", // Same music for both variants
                enterTransition: null,
                exitTransition: null
            }
        },
        transitions: {
            "catacombs": "assets/music/catacombs/placeholder.mp3"
        }
    },

    // --- Stingers & One-Shots ---
    "stingers": {
        // Use catacomb theme file for stingers
        "player_hit": "assets/music/catacombs/catacomb_theme.mp3",
        "player_death": "assets/music/catacombs/catacomb_theme.mp3",
        "enemy_death": "assets/music/catacombs/catacomb_theme.mp3",
        "boss_appear": "assets/music/catacombs/catacomb_theme.mp3"
    },

    // --- Default Fallbacks ---
    "defaults": {
        crossfadeDuration: 2, // seconds
        barTransitionWaitTime: 2, // seconds (maximum time to wait for a bar to complete)
        defaultTempo: 95, // BPM
        defaultBarLength: 4 // Number of beats per bar
    }
};

/**
 * Placeholder function to check if music assets exist
 * This helps prevent runtime errors when music files aren't available yet
 */
export function validateMusicAssets() {
    // This would normally check if files exist, but for now we'll just log
    console.log("[MusicSystem] Validating music assets (placeholder)");

    // In a real implementation, this would:
    // 1. Check if required files exist
    // 2. Create a list of missing files
    // 3. Return fallback paths for missing files

    return {
        valid: false,
        missingFiles: ["All music files are currently placeholders"],
        message: "Music system is using placeholder paths. Add actual music files to enable the full system."
    };
}

/**
 * Get special room data if it exists for the given area and room type
 */
export function getSpecialRoomData(areaId, roomType) {
    if (!MusicData[areaId] || !MusicData[areaId].specialRooms || !MusicData[areaId].specialRooms[roomType]) {
        return null;
    }
    return MusicData[areaId].specialRooms[roomType];
}

/**
 * Get transition music between two areas if it exists
 */
export function getTransitionMusic(fromAreaId, toAreaId) {
    if (!MusicData[fromAreaId] || !MusicData[fromAreaId].transitions || !MusicData[fromAreaId].transitions[toAreaId]) {
        return null;
    }
    return MusicData[fromAreaId].transitions[toAreaId];
}

/**
 * Get the music type for an event room based on its configuration
 * @param {Object} eventRoomData - The event room data from the room configuration
 * @returns {string|null} - The music type or null if not found
 */
export function getEventRoomMusicType(eventRoomData) {
    if (!eventRoomData || !eventRoomData.name) {
        return null;
    }
    
    // Map event room names to music types
    const eventRoomMusicMap = {
        'Mysterious Pond': 'pond_event',
        'Ancient Altar': 'altar_event',
        'Cursed Shrine': 'shrine_event',
        'Treasure Chamber': 'treasure_event'
        // Add more event room music types as needed
    };
    
    return eventRoomMusicMap[eventRoomData.name] || null;
}

/**
 * Get miniboss music data if it exists for the given area and miniboss type
 * @param {string} areaId - The area ID
 * @param {string} minibossType - The miniboss type (e.g., "nairabos", "sairabos")
 * @returns {Object|null} - The miniboss music data or null if not found
 */
export function getMinibossData(areaId, minibossType) {
    if (!MusicData[areaId] || !MusicData[areaId].minibosses || !MusicData[areaId].minibosses[minibossType]) {
        return null;
    }
    return MusicData[areaId].minibosses[minibossType];
}

/**
 * Check if an enemy type is a miniboss
 * @param {string} enemyType - The enemy type to check
 * @returns {boolean} - True if the enemy is a miniboss
 */
export function isMiniboss(enemyType) {
    // List of known miniboss types
    const minibossTypes = ['nairabos', 'sairabos', 'skeleton_boss', 'catacombs_overlord'];
    return minibossTypes.includes(enemyType);
}
