// Wall prefabs
import { createStonebrickWallSegment } from '../generators/prefabs/stonebrickWall.js';
import { createStonebrickWaterWallSegment } from '../generators/prefabs/stonebrickWaterWall.js';
import { createSandstoneBrickWallSegment } from '../generators/prefabs/sandstoneBrickWall.js';
import { createMysticalStoneBrickWallSegment } from '../generators/prefabs/mysticalStoneBrickWall.js';
import { createAncientStoneBrickWallWithVines } from '../generators/prefabs/ancientStoneBrickWallWithVines.js';
import { createCircularChamberWallSegment } from '../generators/prefabs/circularChamberWall.js';
// ... other wall imports

// Floor prefabs
import { createCaveFloor } from '../generators/prefabs/caveFloor.js';
import { createStoneFloor } from '../generators/prefabs/stoneFloor.js';
import { createSandstoneFloor } from '../generators/prefabs/sandstoneFloor.js';
import { createMysticalStoneFloor } from '../generators/prefabs/mysticalStoneFloor.js';
import { createAncientStoneFloorWithVines } from '../generators/prefabs/ancientStoneFloorWithVines.js';
// ... other floor imports

// Door prefabs
import { createStoneArchwayDoor } from '../generators/prefabs/stoneArchwayDoor.js';
import { createClosedDungeonDoor, createWoodenDungeonDoor, createReinforcedDungeonDoor, createIronDungeonDoor } from '../generators/prefabs/closedDungeonDoor.js';
// ... other door imports

// Interior Object prefabs
import { createVineObject } from '../generators/prefabs/vineObject.js';
import { createTorchObject } from '../generators/prefabs/torchObject.js';
import { createStoneVaseObject } from '../generators/prefabs/stoneVaseObject.js';
import { createStonePillarObject } from '../generators/prefabs/stonePillarObject.js'; // <-- Import Pillar
import { createStoneRubbleObject } from '../generators/prefabs/stoneRubbleObject.js'; // <-- Import Rubble
import { createRitualCircleObject } from '../generators/prefabs/ritualCircleObject.js'; // <-- Import Ritual Circle
import { createAetherTorchObject } from '../generators/prefabs/aetherTorchObject.js'; // <-- Import Aether Torch
import { createSoulOrbObject } from '../generators/prefabs/soulOrbObject.js'; // <-- Import Soul Orb
import { createOminousAngelStatueObject } from '../generators/prefabs/ominousAngelStatueObject.js'; // <-- Import Angel Statue
import { createLargeAngelStatueObject } from '../generators/prefabs/largeAngelStatueObject.js'; // <-- Import Large Angel Statue
import { createAncientStonePillarObject } from '../generators/prefabs/ancientStonePillarObject.js'; // <-- Import Ancient Stone Pillar
import { createAncientSandstoneTemplePillar } from '../generators/prefabs/ancientSandstoneTemplePillar.js'; // <-- Import Ancient Sandstone Temple Pillar
import { createAncientStonePath } from '../generators/prefabs/ancientStonePath.js'; // <-- Import Ancient Stone Path
import { createStonePathObject } from '../generators/prefabs/stonePathObject.js'; // <-- Import Stone Path
import { createSimpleStonePathObject } from '../generators/prefabs/simpleStonePathObject.js'; // <-- Import Simple Stone Path
import { createOldPathStoneObject } from '../generators/prefabs/oldPathStoneObject.js'; // <-- Import Old Path Stone
import { createTreasureChestObject } from '../generators/prefabs/treasureChestObject.js'; // <-- Import Treasure Chest
import { createEgyptianVaseObject } from '../generators/prefabs/egyptianVaseObject.js'; // <-- Import Egyptian Vase
import { createGoldenPillarObject } from '../generators/prefabs/goldenPillarObject.js'; // <-- Import Golden Pillar
import { createGlowingPondObject } from '../generators/prefabs/glowingPondObject.js'; // <-- Import Glowing Pond
import { createFishingRodObject } from '../generators/prefabs/fishingRodObject.js'; // <-- Import Fishing Rod
import { createMysteriousCrystalObject } from '../generators/prefabs/mysteriousCrystalObject.js'; // <-- Import Mysterious Crystal
import { createCrystalCaveObject } from '../generators/prefabs/crystalCaveObject.js'; // <-- Import Crystal Cave
import { createImprovedFishingRodObject } from '../generators/prefabs/improvedFishingRodObject.js'; // <-- Import Improved Fishing Rod
import { createArcadeMachineObject } from '../generators/prefabs/arcadeMachineObject.js'; // <-- Import Arcade Machine

// Temporal/Chronal prefabs for Chronal Anomaly event room
import { createTemporalRiftObject } from '../generators/prefabs/temporalRiftObject.js'; // <-- Import Temporal Rift
import { createChronoArchaeologistStatueObject } from '../generators/prefabs/chronoArchaeologistStatueObject.js'; // <-- Import Chrono Archaeologist Statue
import { createTimeCrystalLargeObject, createTimeCrystalSmallObject } from '../generators/prefabs/timeCrystalObject.js'; // <-- Import Time Crystals
import { createTemporalDeviceObject, createTemporalArtifactObject } from '../generators/prefabs/temporalDeviceObject.js'; // <-- Import Temporal Devices
import { createTimeWornPillarObject } from '../generators/prefabs/timeWornPillarObject.js'; // <-- Import Time Worn Pillar

// Guardians of Lies room prefabs
import { createMedievalGateObject, createTreasureGateObject, createDeathGateObject } from '../generators/prefabs/medievalGateObject.js'; // <-- Import Medieval Gates
import { createArmoredKnightObject, createTruthKnightObject, createLieKnightObject } from '../generators/prefabs/armoredKnightObject.js'; // <-- Import Armored Knights

// Cave atmosphere prefabs
import { createStalactiteObject } from '../generators/prefabs/stalactiteObject.js'; // <-- Import Stalactite
import { createStalagmiteObject } from '../generators/prefabs/stalagmiteObject.js'; // <-- Import Stalagmite
import { createMossPatchObject } from '../generators/prefabs/mossPatchObject.js'; // <-- Import Moss Patch
import { createCaveDebrisObject } from '../generators/prefabs/caveDebrisObject.js'; // <-- Import Cave Debris
import { createRockWallFormationObject } from '../generators/prefabs/rockWallFormationObject.js'; // <-- Import Rock Wall Formation
// Blue firefly removed - now implemented as enemy
import { createMysticalStalagmiteObject } from '../generators/prefabs/mysticalStalagmiteObject.js'; // <-- Import Mystical Stalagmite

// Eye of Judgment room prefabs
import { createEyeOfJudgmentObject } from '../generators/prefabs/eyeOfJudgmentObject.js'; // <-- Import Eye of Judgment
import { createRitualCircleFloorObject } from '../generators/prefabs/ritualCircleFloorObject.js'; // <-- Import Ritual Circle Floor
import { createJudgmentPillarObject } from '../generators/prefabs/judgmentPillarObject.js'; // <-- Import Judgment Pillar
import { createSoulLanternObject } from '../generators/prefabs/soulLanternObject.js'; // <-- Import Soul Lantern
import { createConfessionStoneObject } from '../generators/prefabs/confessionStoneObject.js'; // <-- Import Confession Stone
import { createMoralCrystalObject } from '../generators/prefabs/moralCrystalObject.js'; // <-- Import Moral Crystal

// Devil's Chess Room prefabs
import { createObsidianChessTableObject } from '../generators/prefabs/obsidianChessTableObject.js'; // <-- Import Obsidian Chess Table
import { createDevilThroneObject } from '../generators/prefabs/devilThroneObject.js'; // <-- Import Devil Throne
import { createHellishTorchObject } from '../generators/prefabs/hellishTorchObject.js'; // <-- Import Hellish Torch
import { createObsidianPillarObject } from '../generators/prefabs/obsidianPillarObject.js'; // <-- Import Obsidian Pillar

// Chess piece prefabs
import { createChessPawnObject, createWhiteChessPawn, createBlackChessPawn } from '../generators/prefabs/chessPawnObject.js'; // <-- Import Chess Pawns
import { createChessRookObject, createWhiteChessRook, createBlackChessRook } from '../generators/prefabs/chessRookObject.js'; // <-- Import Chess Rooks
import { createChessKnightObject, createWhiteChessKnight, createBlackChessKnight } from '../generators/prefabs/chessKnightObject.js'; // <-- Import Chess Knights
import { createChessBishopObject, createWhiteChessBishop, createBlackChessBishop } from '../generators/prefabs/chessBishopObject.js'; // <-- Import Chess Bishops
import { createChessQueenObject, createWhiteChessQueen, createBlackChessQueen } from '../generators/prefabs/chessQueenObject.js'; // <-- Import Chess Queens
import { createChessKingObject, createWhiteChessKing, createBlackChessKing } from '../generators/prefabs/chessKingObject.js'; // <-- Import Chess Kings


// ... other interior object imports

const prefabMap = {
    wall: {
        'stonebrick': createStonebrickWallSegment,
        'stonebrick_with_water': createStonebrickWaterWallSegment,
        'sandstone_brick': createSandstoneBrickWallSegment,
        'mystical_stone_brick': createMysticalStoneBrickWallSegment,
        'ancient_stone_with_vines': createAncientStoneBrickWallWithVines,
        'circular_chamber': createCircularChamberWallSegment,
        // ... other walls
    },
    floor: {
        'cave_floor': createCaveFloor,
        'stone_floor': createStoneFloor,
        'sandstone_floor': createSandstoneFloor,
        'mystical_stone_floor': createMysticalStoneFloor,
        'ancient_stone_with_vines': createAncientStoneFloorWithVines,
        // ... other floors
    },
    door: {
        'stone_archway': createStoneArchwayDoor,
        'closed_dungeon_door': createClosedDungeonDoor,
        'wooden_dungeon_door': createWoodenDungeonDoor,
        'reinforced_dungeon_door': createReinforcedDungeonDoor,
        'iron_dungeon_door': createIronDungeonDoor,
        // ... other doors
    },
    interior: {
        'vine': createVineObject,
        'torch': createTorchObject,
        'stone_vase': createStoneVaseObject,
        'stone_pillar': createStonePillarObject, // <-- Register Pillar
        'stone_rubble': createStoneRubbleObject, // <-- Register Rubble
        'ritual_circle': createRitualCircleObject, // <-- Register Ritual Circle
        'aether_torch': createAetherTorchObject, // <-- Register Aether Torch
        'soul_orb': createSoulOrbObject, // <-- Register Soul Orb
        'ominous_angel_statue': createOminousAngelStatueObject, // <-- Register Angel Statue
        'large_angel_statue': createLargeAngelStatueObject, // <-- Register Large Angel Statue
        'ancient_stone_pillar': createAncientStonePillarObject, // <-- Register Ancient Stone Pillar
        'ancient_sandstone_temple_pillar': createAncientSandstoneTemplePillar, // <-- Register Ancient Sandstone Temple Pillar
        'ancient_stone_path': createAncientStonePath, // <-- Register Ancient Stone Path
        'treasure_chest': createTreasureChestObject, // <-- Register Treasure Chest
        'egyptian_vase': createEgyptianVaseObject, // <-- Register Egyptian Vase
        'golden_pillar': createGoldenPillarObject, // <-- Register Golden Pillar
        'glowing_pond': createGlowingPondObject, // <-- Register Glowing Pond
        'fishing_rod': createFishingRodObject, // <-- Register Fishing Rod
        'mysterious_crystal': createMysteriousCrystalObject, // <-- Register Mysterious Crystal
        'crystal_cave': createCrystalCaveObject, // <-- Register Crystal Cave
        'improved_fishing_rod': createImprovedFishingRodObject, // <-- Register Improved Fishing Rod

        // Cave atmosphere objects
        'stalactite': createStalactiteObject, // <-- Register Stalactite
        'stalagmite': createStalagmiteObject, // <-- Register Stalagmite
        'moss_patch': createMossPatchObject, // <-- Register Moss Patch
        'cave_debris': createCaveDebrisObject, // <-- Register Cave Debris
        'rock_wall_formation': createRockWallFormationObject, // <-- Register Rock Wall Formation

        'mystical_stalagmite': createMysticalStalagmiteObject, // <-- Register Mystical Stalagmite
        'stone_path': createStonePathObject, // <-- Register Stone Path
        'simple_stone_path': createSimpleStonePathObject, // <-- Register Simple Stone Path
        'old_path_stone': createOldPathStoneObject, // <-- Register Old Path Stone

        // Temporal/Chronal objects for Chronal Anomaly event room
        'temporal_rift': createTemporalRiftObject, // <-- Register Temporal Rift
        'chrono_archaeologist_statue': createChronoArchaeologistStatueObject, // <-- Register Chrono Archaeologist Statue
        'time_crystal_large': createTimeCrystalLargeObject, // <-- Register Large Time Crystal
        'time_crystal_small': createTimeCrystalSmallObject, // <-- Register Small Time Crystal
        'temporal_device': createTemporalDeviceObject, // <-- Register Temporal Device
        'temporal_artifact': createTemporalArtifactObject, // <-- Register Temporal Artifact
        'time_worn_pillar': createTimeWornPillarObject, // <-- Register Time Worn Pillar
        
        // Eye of Judgment room objects
        'eye_of_judgment': createEyeOfJudgmentObject, // <-- Register Eye of Judgment
        'ritual_circle_floor': createRitualCircleFloorObject, // <-- Register Ritual Circle Floor
        'judgment_pillar': createJudgmentPillarObject, // <-- Register Judgment Pillar
        'soul_lantern': createSoulLanternObject, // <-- Register Soul Lantern
        'confession_stone': createConfessionStoneObject, // <-- Register Confession Stone
        'moral_crystal': createMoralCrystalObject, // <-- Register Moral Crystal
        
        // Devil's Chess Room objects
        'obsidian_chess_table': createObsidianChessTableObject, // <-- Register Obsidian Chess Table
        'devil_throne': createDevilThroneObject, // <-- Register Devil Throne
        'hellish_torch': createHellishTorchObject, // <-- Register Hellish Torch
        'obsidian_pillar': createObsidianPillarObject, // <-- Register Obsidian Pillar
        
        // Chess piece objects
        'chess_pawn_white': createWhiteChessPawn, // <-- Register White Chess Pawn
        'chess_pawn_black': createBlackChessPawn, // <-- Register Black Chess Pawn
        'chess_rook_white': createWhiteChessRook, // <-- Register White Chess Rook
        'chess_rook_black': createBlackChessRook, // <-- Register Black Chess Rook
        'chess_knight_white': createWhiteChessKnight, // <-- Register White Chess Knight
        'chess_knight_black': createBlackChessKnight, // <-- Register Black Chess Knight
        'chess_bishop_white': createWhiteChessBishop, // <-- Register White Chess Bishop
        'chess_bishop_black': createBlackChessBishop, // <-- Register Black Chess Bishop
        'chess_queen_white': createWhiteChessQueen, // <-- Register White Chess Queen
        'chess_queen_black': createBlackChessQueen, // <-- Register Black Chess Queen
        'chess_king_white': createWhiteChessKing, // <-- Register White Chess King
        'chess_king_black': createBlackChessKing, // <-- Register Black Chess King
        
        // Guardians of Lies room objects
        'medieval_gate': createMedievalGateObject, // <-- Register Medieval Gate
        'treasure_gate': createTreasureGateObject, // <-- Register Treasure Gate
        'death_gate': createDeathGateObject, // <-- Register Death Gate
        'truth_knight': createTruthKnightObject, // <-- Register Truth Knight 
        'lie_knight': createLieKnightObject, // <-- Register Lie Knight
        
        // Dungeon door objects (also registered as interior objects for room placement)
        'closed_dungeon_door': createClosedDungeonDoor, // <-- Register Closed Dungeon Door
        'wooden_dungeon_door': createWoodenDungeonDoor, // <-- Register Wooden Dungeon Door
        'reinforced_dungeon_door': createReinforcedDungeonDoor, // <-- Register Reinforced Dungeon Door
        'iron_dungeon_door': createIronDungeonDoor, // <-- Register Iron Dungeon Door
        'arcade_machine': createArcadeMachineObject, // <-- Register Arcade Machine
        
        // ... other interior objects
    },
    // Potentially other categories like 'enemy', 'item'
};

// Debug: Log all available prefabs
console.log('Available prefabs:', {
    wall: Object.keys(prefabMap.wall),
    floor: Object.keys(prefabMap.floor),
    door: Object.keys(prefabMap.door),
    interior: Object.keys(prefabMap.interior)
});

/**
 * Gets the prefab generator function for a given type and category.
 * @param {string} type - The specific type name (e.g., 'stonebrick', 'torch').
 * @param {string} category - The category ('wall', 'floor', 'door', 'interior').
 * @returns {function|null} - The generator function or null if not found.
 */
export function getPrefabFunction(type, category) {
    if (prefabMap[category] && prefabMap[category][type]) {
        const func = prefabMap[category][type];
        console.log(`[getPrefabFunction] Found prefab function for ${category}/${type}`);
        return func;
    }

    // Log the available prefabs for debugging
    console.warn(`[getPrefabFunction] Prefab not found for category '${category}', type '${type}'`);
    console.log(`Available prefabs for category '${category}':`, prefabMap[category] ? Object.keys(prefabMap[category]) : 'Category not found');

    return null;
}