// src/workers/dungeonWorker.js
// Web worker for dungeon generation - runs heavy computation off main thread

// Since ES6 imports don't work in standard web workers, we need to inline
// the DungeonGeneratorCore functionality or disable this worker until needed

class DungeonWorker {
    constructor() {
        // For now, create a stub generator since DungeonGeneratorCore uses ES6 exports
        // This worker would need DungeonGeneratorCore to be refactored for web worker compatibility
        this.generator = {
            generateLayout: async (areaConfig) => {
                console.warn('[DungeonWorker] DungeonGeneratorCore not available in worker context');
                throw new Error('DungeonGeneratorCore requires ES6 modules which are not available in standard web workers');
            }
        };
        this.activeRequests = new Map();
        this.performanceMetrics = {
            totalRequests: 0,
            totalTime: 0,
            averageTime: 0
        };
        
        this.setupMessageHandler();
        this._log('Dungeon worker initialized');
    }

    _log(message, ...args) {
        console.log(`[DungeonWorker] ${message}`, ...args);
    }

    setupMessageHandler() {
        self.onmessage = async (event) => {
            const { type, data, requestId, options = {} } = event.data;
            const startTime = performance.now();

            try {
                this._log(`Processing request: ${type} (ID: ${requestId})`);
                
                let result;
                switch (type) {
                    case 'GENERATE_DUNGEON':
                        result = await this.generateDungeon(data, options);
                        break;
                    case 'VALIDATE_LAYOUT':
                        result = await this.validateLayout(data);
                        break;
                    case 'CALCULATE_PATH_DISTANCES':
                        result = await this.calculatePathDistances(data);
                        break;
                    case 'GET_PERFORMANCE_METRICS':
                        result = this.getPerformanceMetrics();
                        break;
                    default:
                        throw new Error(`Unknown request type: ${type}`);
                }

                const processingTime = performance.now() - startTime;
                this.updatePerformanceMetrics(processingTime);

                self.postMessage({
                    requestId,
                    success: true,
                    result,
                    metadata: {
                        processingTime,
                        timestamp: Date.now()
                    }
                });

                this._log(`Request completed: ${type} (${processingTime.toFixed(2)}ms)`);

            } catch (error) {
                const processingTime = performance.now() - startTime;
                
                self.postMessage({
                    requestId,
                    success: false,
                    error: {
                        message: error.message,
                        stack: error.stack,
                        type: error.constructor.name
                    },
                    metadata: {
                        processingTime,
                        timestamp: Date.now()
                    }
                });

                this._log(`Request failed: ${type} - ${error.message}`);
            }
        };
    }

    async generateDungeon(params, options = {}) {
        const {
            areaId = 'catacombs',
            areaData = null,
            minRooms = 12,
            maxRooms = 25,
            seed = null,
            debugMode = false
        } = params;

        // Configure generator
        this.generator.debugMode = debugMode;
        
        // Set random seed if provided
        if (seed !== null) {
            Math.seed = seed; // Note: This would need a seeded random implementation
        }

        const areaConfig = {
            id: areaId,
            data: areaData,
            minRooms,
            maxRooms
        };

        this._log(`Generating dungeon for area: ${areaId} (${minRooms}-${maxRooms} rooms), allowedShapes: ${areaData?.allowedShapes?.length}`);

        const layout = await this.generator.generateLayout(areaConfig);

        this._log(`Dungeon generated: ${layout.roomCount} rooms`);

        return {
            layout: layout.rooms,
            roomCount: layout.roomCount,
            areaId,
            metadata: {
                ...layout.metadata,
                generatedAt: Date.now(),
                workerId: 'dungeon-worker'
            }
        };
    }

    async validateLayout(layoutData) {
        const { rooms } = layoutData;
        
        if (!rooms || typeof rooms !== 'object') {
            throw new Error('Invalid layout data: missing rooms');
        }

        const validation = {
            isValid: true,
            errors: [],
            warnings: [],
            roomCount: Object.keys(rooms).length,
            statistics: {
                roomTypes: {},
                connectionCounts: {},
                unreachableRooms: 0
            }
        };

        // Validate room structure and collect statistics
        for (const [roomId, room] of Object.entries(rooms)) {
            // Type statistics
            validation.statistics.roomTypes[room.type] = 
                (validation.statistics.roomTypes[room.type] || 0) + 1;

            // Connection count statistics
            const connectionCount = Object.values(room.connections || {})
                .filter(conn => conn !== null).length;
            validation.statistics.connectionCounts[connectionCount] = 
                (validation.statistics.connectionCounts[connectionCount] || 0) + 1;

            // Validate required properties
            if (!room.hasOwnProperty('id') || !room.hasOwnProperty('type') || 
                !room.hasOwnProperty('coords') || !room.hasOwnProperty('connections')) {
                validation.errors.push(`Room ${roomId}: Missing required properties`);
                validation.isValid = false;
            }

            // Validate connections
            if (room.connections) {
                for (const [direction, connectedRoomId] of Object.entries(room.connections)) {
                    if (connectedRoomId !== null && !rooms[connectedRoomId]) {
                        validation.errors.push(`Room ${roomId}: Invalid connection to room ${connectedRoomId}`);
                        validation.isValid = false;
                    }
                }
            }
        }

        // Check for start room
        const startRoom = Object.values(rooms).find(room => room.type === 'Start');
        if (!startRoom) {
            validation.errors.push('No Start room found');
            validation.isValid = false;
        }

        // Check for boss room
        const bossRoom = Object.values(rooms).find(room => room.type === 'Boss');
        if (!bossRoom) {
            validation.warnings.push('No Boss room found');
        }

        // Check reachability from start room
        if (startRoom) {
            const reachable = this.findReachableRooms(rooms, startRoom.id);
            const totalRooms = Object.keys(rooms).length;
            validation.statistics.unreachableRooms = totalRooms - reachable.size;
            
            if (validation.statistics.unreachableRooms > 0) {
                validation.warnings.push(`${validation.statistics.unreachableRooms} unreachable rooms found`);
            }
        }

        return validation;
    }

    findReachableRooms(rooms, startRoomId) {
        const reachable = new Set();
        const queue = [startRoomId];
        
        while (queue.length > 0) {
            const roomId = queue.shift();
            if (reachable.has(roomId)) continue;
            
            reachable.add(roomId);
            const room = rooms[roomId];
            
            if (room && room.connections) {
                for (const connectedRoomId of Object.values(room.connections)) {
                    if (connectedRoomId !== null && !reachable.has(connectedRoomId)) {
                        queue.push(connectedRoomId);
                    }
                }
            }
        }
        
        return reachable;
    }

    async calculatePathDistances(layoutData) {
        const { rooms, startRoomId = '0' } = layoutData;
        
        if (!rooms[startRoomId]) {
            throw new Error(`Start room ${startRoomId} not found in layout`);
        }

        const distances = new Map();
        const queue = [{ roomId: startRoomId, distance: 0 }];
        const visited = new Set([startRoomId]);
        
        while (queue.length > 0) {
            const { roomId, distance } = queue.shift();
            distances.set(roomId, distance);
            
            const room = rooms[roomId];
            if (room && room.connections) {
                for (const connectedRoomId of Object.values(room.connections)) {
                    if (connectedRoomId !== null && !visited.has(connectedRoomId)) {
                        visited.add(connectedRoomId);
                        queue.push({ roomId: connectedRoomId, distance: distance + 1 });
                    }
                }
            }
        }
        
        // Convert Map to object for transfer
        const distanceObj = {};
        distances.forEach((distance, roomId) => {
            distanceObj[roomId] = distance;
        });
        
        return {
            distances: distanceObj,
            maxDistance: Math.max(...distances.values()),
            roomCount: distances.size
        };
    }

    updatePerformanceMetrics(processingTime) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.totalTime += processingTime;
        this.performanceMetrics.averageTime = 
            this.performanceMetrics.totalTime / this.performanceMetrics.totalRequests;
    }

    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            memoryUsage: self.performance && self.performance.memory ? {
                used: self.performance.memory.usedJSHeapSize,
                total: self.performance.memory.totalJSHeapSize,
                limit: self.performance.memory.jsHeapSizeLimit
            } : null,
            timestamp: Date.now()
        };
    }
}

// Initialize the worker
new DungeonWorker();