// src/workers/meshProcessingWorker.js
// High-performance mesh processing worker for geometry operations

/**
 * Lightweight Vector3 implementation for worker context
 */
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    clone() {
        return new Vector3(this.x, this.y, this.z);
    }
    
    copy(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    }
    
    add(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    }
    
    multiplyScalar(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    }
    
    normalize() {
        const length = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        if (length > 0) {
            this.multiplyScalar(1 / length);
        }
        return this;
    }
}

/**
 * Lightweight Matrix4 implementation for transformations
 */
class Matrix4 {
    constructor() {
        this.elements = [
            1, 0, 0, 0,
            0, 1, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        ];
    }
    
    makeTranslation(x, y, z) {
        this.elements[12] = x;
        this.elements[13] = y;
        this.elements[14] = z;
        return this;
    }
    
    makeScale(x, y, z) {
        this.elements[0] = x;
        this.elements[5] = y;
        this.elements[10] = z;
        return this;
    }
    
    setPosition(v) {
        this.elements[12] = v.x;
        this.elements[13] = v.y;
        this.elements[14] = v.z;
        return this;
    }
}

/**
 * High-performance mesh processing worker
 */
class MeshProcessingWorker {
    constructor() {
        this.performanceMetrics = {
            totalRequests: 0,
            totalTime: 0,
            averageTime: 0,
            memoryUsage: 0
        };
        
        // Memory management
        this.geometryCache = new Map();
        this.maxCacheSize = 50;
        
        console.log('[MeshProcessingWorker] Initialized');
    }

    /**
     * Process voxel data into optimized mesh data
     * @param {Object} data - Voxel processing request
     * @returns {Object} Processed mesh data
     */
    processVoxelMesh(data) {
        const { voxels, voxelScale = 1, optimizeForLowEnd = false } = data;
        
        if (!voxels || !Array.isArray(voxels)) {
            throw new Error('Invalid voxel data provided');
        }
        
        const startTime = performance.now();
        
        // Group voxels by material/color for batching
        const materialGroups = new Map();
        
        voxels.forEach(voxel => {
            const colorKey = this._getColorKey(voxel.color);
            if (!materialGroups.has(colorKey)) {
                materialGroups.set(colorKey, []);
            }
            materialGroups.get(colorKey).push(voxel);
        });
        
        // Process each material group
        const meshData = [];
        
        for (const [colorKey, voxelGroup] of materialGroups) {
            const groupMeshData = this._processVoxelGroup(
                voxelGroup, 
                voxelScale, 
                colorKey,
                optimizeForLowEnd
            );
            
            if (groupMeshData) {
                meshData.push(groupMeshData);
            }
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime);
        
        return {
            meshData,
            metadata: {
                processingTime,
                voxelCount: voxels.length,
                materialGroups: materialGroups.size,
                optimized: optimizeForLowEnd
            }
        };
    }

    /**
     * Merge multiple geometry data arrays efficiently
     * @param {Object} data - Geometry merge request
     * @returns {Object} Merged geometry data
     */
    mergeGeometries(data) {
        const { geometries, mergeByMaterial = true } = data;
        
        if (!geometries || !Array.isArray(geometries)) {
            throw new Error('Invalid geometry data provided');
        }
        
        const startTime = performance.now();
        
        let mergedData;
        
        if (mergeByMaterial) {
            mergedData = this._mergeByMaterial(geometries);
        } else {
            mergedData = this._mergeAll(geometries);
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime);
        
        return {
            mergedGeometry: mergedData,
            metadata: {
                processingTime,
                originalCount: geometries.length,
                mergedCount: mergedData.length || 1
            }
        };
    }

    /**
     * Create instanced mesh data from voxel array
     * @param {Object} data - Instance creation request
     * @returns {Object} Instanced mesh data
     */
    createInstancedMeshData(data) {
        const { voxels, maxInstancesPerMesh = 1000 } = data;
        
        if (!voxels || !Array.isArray(voxels)) {
            throw new Error('Invalid voxel data for instancing');
        }
        
        const startTime = performance.now();
        
        // Group by color and create instance matrices
        const colorGroups = new Map();
        
        voxels.forEach(voxel => {
            const colorKey = this._getColorKey(voxel.color);
            if (!colorGroups.has(colorKey)) {
                colorGroups.set(colorKey, []);
            }
            colorGroups.get(colorKey).push(voxel);
        });
        
        const instancedMeshes = [];
        
        for (const [colorKey, voxelGroup] of colorGroups) {
            // Split large groups into multiple instanced meshes
            const chunks = this._chunkArray(voxelGroup, maxInstancesPerMesh);
            
            chunks.forEach((chunk, chunkIndex) => {
                const matrices = this._createInstanceMatrices(chunk);
                
                instancedMeshes.push({
                    colorKey,
                    instanceCount: chunk.length,
                    matrices,
                    chunkIndex
                });
            });
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime);
        
        return {
            instancedMeshes,
            metadata: {
                processingTime,
                totalInstances: voxels.length,
                meshCount: instancedMeshes.length,
                colorGroups: colorGroups.size
            }
        };
    }

    /**
     * Optimize mesh data for low-end devices
     * @param {Object} data - Optimization request
     * @returns {Object} Optimized mesh data
     */
    optimizeForLowEnd(data) {
        const { meshData, reductionFactor = 0.5, simplifyGeometry = true } = data;
        
        const startTime = performance.now();
        
        const optimizedData = meshData.map(mesh => {
            let optimized = { ...mesh };
            
            if (simplifyGeometry && mesh.vertices) {
                // Reduce vertex count
                optimized.vertices = this._reduceVertices(mesh.vertices, reductionFactor);
            }
            
            if (mesh.indices) {
                // Simplify indices
                optimized.indices = this._simplifyIndices(mesh.indices, optimized.vertices);
            }
            
            return optimized;
        });
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime);
        
        return {
            optimizedMeshData: optimizedData,
            metadata: {
                processingTime,
                reductionFactor,
                originalSize: meshData.length,
                optimizedSize: optimizedData.length
            }
        };
    }

    /**
     * Process a group of voxels with the same material
     * @private
     */
    _processVoxelGroup(voxels, voxelScale, colorKey, optimizeForLowEnd) {
        if (voxels.length === 0) return null;
        
        // For low-end devices, use simpler processing
        if (optimizeForLowEnd && voxels.length > 100) {
            return this._processVoxelGroupSimple(voxels, voxelScale, colorKey);
        }
        
        const vertices = [];
        const indices = [];
        const normals = [];
        const uvs = [];
        
        let vertexIndex = 0;
        
        voxels.forEach(voxel => {
            const { position, size = voxelScale } = voxel;
            
            // Create box vertices (8 vertices per voxel)
            const halfSize = size * 0.5;
            const boxVertices = [
                // Front face
                [-halfSize, -halfSize,  halfSize],
                [ halfSize, -halfSize,  halfSize],
                [ halfSize,  halfSize,  halfSize],
                [-halfSize,  halfSize,  halfSize],
                // Back face
                [-halfSize, -halfSize, -halfSize],
                [-halfSize,  halfSize, -halfSize],
                [ halfSize,  halfSize, -halfSize],
                [ halfSize, -halfSize, -halfSize]
            ];
            
            // Add vertices with position offset
            boxVertices.forEach(vertex => {
                vertices.push(
                    position.x + vertex[0],
                    position.y + vertex[1],
                    position.z + vertex[2]
                );
            });
            
            // Add box indices (12 triangles per voxel)
            const boxIndices = [
                0,1,2, 0,2,3,    // front
                4,5,6, 4,6,7,    // back
                5,0,3, 5,3,6,    // left
                1,4,7, 1,7,2,    // right
                3,2,6, 3,6,5,    // top
                0,4,1, 1,4,7     // bottom
            ];
            
            boxIndices.forEach(index => {
                indices.push(vertexIndex + index);
            });
            
            vertexIndex += 8;
        });
        
        return {
            colorKey,
            vertices: new Float32Array(vertices),
            indices: new Uint32Array(indices),
            vertexCount: vertices.length / 3,
            triangleCount: indices.length / 3
        };
    }

    /**
     * Simplified voxel processing for low-end devices
     * @private
     */
    _processVoxelGroupSimple(voxels, voxelScale, colorKey) {
        // Use point-based representation for very low-end devices
        const positions = [];
        
        voxels.forEach(voxel => {
            positions.push(voxel.position.x, voxel.position.y, voxel.position.z);
        });
        
        return {
            colorKey,
            positions: new Float32Array(positions),
            isPointCloud: true,
            pointCount: voxels.length
        };
    }

    /**
     * Get color key for grouping
     * @private
     */
    _getColorKey(color) {
        if (typeof color === 'string') return color;
        if (color.r !== undefined) {
            return `${Math.floor(color.r * 255)}_${Math.floor(color.g * 255)}_${Math.floor(color.b * 255)}`;
        }
        return 'default';
    }

    /**
     * Create instance matrices for instanced rendering
     * @private
     */
    _createInstanceMatrices(voxels) {
        const matrices = new Float32Array(voxels.length * 16);
        
        voxels.forEach((voxel, index) => {
            const matrix = new Matrix4();
            matrix.setPosition(voxel.position);
            
            if (voxel.size && voxel.size !== 1) {
                const scale = voxel.size;
                matrix.makeScale(scale, scale, scale);
                matrix.setPosition(voxel.position);
            }
            
            // Copy matrix elements to array
            const offset = index * 16;
            for (let i = 0; i < 16; i++) {
                matrices[offset + i] = matrix.elements[i];
            }
        });
        
        return matrices;
    }

    /**
     * Split array into chunks
     * @private
     */
    _chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(processingTime) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.totalTime += processingTime;
        this.performanceMetrics.averageTime = 
            this.performanceMetrics.totalTime / this.performanceMetrics.totalRequests;
        
        // Estimate memory usage
        if (performance.memory) {
            this.performanceMetrics.memoryUsage = performance.memory.usedJSHeapSize;
        }
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.geometryCache.size,
            timestamp: Date.now()
        };
    }

    /**
     * Clear geometry cache to free memory
     */
    clearCache() {
        this.geometryCache.clear();
        console.log('[MeshProcessingWorker] Cache cleared');
    }
}

// Initialize worker
const meshWorker = new MeshProcessingWorker();

// Handle messages from main thread
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        let result;
        
        switch (type) {
            case 'processVoxelMesh':
                result = meshWorker.processVoxelMesh(data);
                break;
            case 'mergeGeometries':
                result = meshWorker.mergeGeometries(data);
                break;
            case 'createInstancedMeshData':
                result = meshWorker.createInstancedMeshData(data);
                break;
            case 'optimizeForLowEnd':
                result = meshWorker.optimizeForLowEnd(data);
                break;
            case 'getPerformanceMetrics':
                result = meshWorker.getPerformanceMetrics();
                break;
            case 'clearCache':
                meshWorker.clearCache();
                result = { success: true };
                break;
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
        
        // Send result back to main thread
        self.postMessage({
            requestId: requestId,
            result: result,
            success: true
        });
        
    } catch (error) {
        console.error('[MeshProcessingWorker] Error processing task:', error);
        
        // Send error back to main thread
        self.postMessage({
            requestId: requestId,
            error: error.message,
            success: false
        });
    }
};
