// src/workers/pathfindingWorker.js
// High-performance pathfinding worker for AI navigation

/**
 * Lightweight Vector3 implementation for worker context
 */
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    clone() {
        return new Vector3(this.x, this.y, this.z);
    }
    
    copy(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    }
    
    add(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    }
    
    sub(v) {
        this.x -= v.x;
        this.y -= v.y;
        this.z -= v.z;
        return this;
    }
    
    multiplyScalar(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    }
    
    distanceTo(v) {
        const dx = this.x - v.x;
        const dy = this.y - v.y;
        const dz = this.z - v.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
    
    normalize() {
        const length = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        if (length > 0) {
            this.multiplyScalar(1 / length);
        }
        return this;
    }
}

/**
 * Priority queue implementation for A* algorithm
 */
class PriorityQueue {
    constructor() {
        this.elements = [];
    }
    
    enqueue(element, priority) {
        this.elements.push({ element, priority });
        this.elements.sort((a, b) => a.priority - b.priority);
    }
    
    dequeue() {
        return this.elements.shift()?.element;
    }
    
    isEmpty() {
        return this.elements.length === 0;
    }
    
    clear() {
        this.elements = [];
    }
}

/**
 * High-performance pathfinding worker
 */
class PathfindingWorker {
    constructor() {
        this.performanceMetrics = {
            totalRequests: 0,
            totalTime: 0,
            averageTime: 0,
            pathsFound: 0,
            pathsFailed: 0
        };
        
        // Pathfinding cache for repeated requests
        this.pathCache = new Map();
        this.maxCacheSize = 100;
        
        console.log('[PathfindingWorker] Initialized');
    }

    /**
     * Find path using A* algorithm
     * @param {Object} data - Pathfinding request
     * @returns {Object} Path result
     */
    findPath(data) {
        const { 
            start, 
            end, 
            obstacles = [], 
            gridSize = 1.0,
            maxIterations = 1000,
            allowDiagonal = true,
            optimizeForLowEnd = false
        } = data;
        
        const startTime = performance.now();
        
        // Check cache first
        const cacheKey = this._getCacheKey(start, end, obstacles.length);
        if (this.pathCache.has(cacheKey)) {
            const cachedPath = this.pathCache.get(cacheKey);
            this._updateMetrics(performance.now() - startTime, true);
            return {
                path: cachedPath,
                fromCache: true,
                metadata: { processingTime: performance.now() - startTime }
            };
        }
        
        let path;
        
        if (optimizeForLowEnd) {
            // Use simplified pathfinding for low-end devices
            path = this._findSimplePath(start, end, obstacles, gridSize);
        } else {
            // Use full A* algorithm
            path = this._findAStarPath(start, end, obstacles, gridSize, maxIterations, allowDiagonal);
        }
        
        const processingTime = performance.now() - startTime;
        const success = path && path.length > 0;
        
        // Cache successful paths
        if (success && this.pathCache.size < this.maxCacheSize) {
            this.pathCache.set(cacheKey, path);
        }
        
        this._updateMetrics(processingTime, success);
        
        return {
            path: path || [],
            success,
            metadata: {
                processingTime,
                pathLength: path ? path.length : 0,
                iterations: path ? path.iterations : 0
            }
        };
    }

    /**
     * Batch process multiple pathfinding requests
     * @param {Object} data - Batch pathfinding request
     * @returns {Object} Batch results
     */
    findPathsBatch(data) {
        const { requests, optimizeForLowEnd = false } = data;
        
        if (!requests || !Array.isArray(requests)) {
            throw new Error('Invalid batch pathfinding requests');
        }
        
        const startTime = performance.now();
        const results = [];
        
        requests.forEach((request, index) => {
            try {
                const result = this.findPath({
                    ...request,
                    optimizeForLowEnd
                });
                results.push({
                    index,
                    success: true,
                    ...result
                });
            } catch (error) {
                results.push({
                    index,
                    success: false,
                    error: error.message,
                    path: []
                });
            }
        });
        
        const processingTime = performance.now() - startTime;
        
        return {
            results,
            metadata: {
                processingTime,
                totalRequests: requests.length,
                successfulPaths: results.filter(r => r.success).length
            }
        };
    }

    /**
     * Check line of sight between two points
     * @param {Object} data - Line of sight request
     * @returns {Object} Line of sight result
     */
    checkLineOfSight(data) {
        const { start, end, obstacles = [], stepSize = 0.5 } = data;
        
        const startTime = performance.now();
        
        const startVec = new Vector3(start.x, start.y, start.z);
        const endVec = new Vector3(end.x, end.y, end.z);
        
        const direction = endVec.clone().sub(startVec);
        const distance = direction.length();
        direction.normalize();
        
        const steps = Math.ceil(distance / stepSize);
        const stepVector = direction.clone().multiplyScalar(stepSize);
        
        let currentPos = startVec.clone();
        
        for (let i = 0; i < steps; i++) {
            // Check collision with obstacles
            for (const obstacle of obstacles) {
                if (this._pointIntersectsObstacle(currentPos, obstacle)) {
                    const processingTime = performance.now() - startTime;
                    return {
                        hasLineOfSight: false,
                        blockedAt: currentPos.clone(),
                        metadata: { processingTime, stepsChecked: i + 1 }
                    };
                }
            }
            
            currentPos.add(stepVector);
        }
        
        const processingTime = performance.now() - startTime;
        return {
            hasLineOfSight: true,
            metadata: { processingTime, stepsChecked: steps }
        };
    }

    /**
     * A* pathfinding implementation
     * @private
     */
    _findAStarPath(start, end, obstacles, gridSize, maxIterations, allowDiagonal) {
        const startNode = this._createNode(start.x, start.y, start.z);
        const endNode = this._createNode(end.x, end.y, end.z);
        
        const openSet = new PriorityQueue();
        const closedSet = new Set();
        const cameFrom = new Map();
        const gScore = new Map();
        const fScore = new Map();
        
        const startKey = this._getNodeKey(startNode);
        const endKey = this._getNodeKey(endNode);
        
        gScore.set(startKey, 0);
        fScore.set(startKey, this._heuristic(startNode, endNode));
        openSet.enqueue(startNode, fScore.get(startKey));
        
        let iterations = 0;
        
        while (!openSet.isEmpty() && iterations < maxIterations) {
            iterations++;
            
            const current = openSet.dequeue();
            const currentKey = this._getNodeKey(current);
            
            if (currentKey === endKey) {
                // Path found, reconstruct it
                const path = this._reconstructPath(cameFrom, current);
                path.iterations = iterations;
                return path;
            }
            
            closedSet.add(currentKey);
            
            // Get neighbors
            const neighbors = this._getNeighbors(current, gridSize, allowDiagonal);
            
            for (const neighbor of neighbors) {
                const neighborKey = this._getNodeKey(neighbor);
                
                if (closedSet.has(neighborKey)) continue;
                
                // Check if neighbor collides with obstacles
                if (this._nodeCollidesWithObstacles(neighbor, obstacles)) continue;
                
                const tentativeGScore = gScore.get(currentKey) + this._distance(current, neighbor);
                
                if (!gScore.has(neighborKey) || tentativeGScore < gScore.get(neighborKey)) {
                    cameFrom.set(neighborKey, current);
                    gScore.set(neighborKey, tentativeGScore);
                    fScore.set(neighborKey, tentativeGScore + this._heuristic(neighbor, endNode));
                    
                    openSet.enqueue(neighbor, fScore.get(neighborKey));
                }
            }
        }
        
        // No path found
        return [];
    }

    /**
     * Simplified pathfinding for low-end devices
     * @private
     */
    _findSimplePath(start, end, obstacles, gridSize) {
        const startVec = new Vector3(start.x, start.y, start.z);
        const endVec = new Vector3(end.x, end.y, end.z);
        
        // Try direct path first
        if (this._isPathClear(startVec, endVec, obstacles)) {
            return [startVec, endVec];
        }
        
        // Try simple waypoint navigation
        const waypoints = this._findSimpleWaypoints(startVec, endVec, obstacles, gridSize);
        
        if (waypoints.length > 0) {
            return [startVec, ...waypoints, endVec];
        }
        
        return [];
    }

    /**
     * Find simple waypoints around obstacles
     * @private
     */
    _findSimpleWaypoints(start, end, obstacles, gridSize) {
        const waypoints = [];
        const maxWaypoints = 5; // Limit for performance
        
        // Try different angles around obstacles
        const testAngles = [Math.PI/4, -Math.PI/4, Math.PI/2, -Math.PI/2];
        
        for (let i = 0; i < maxWaypoints; i++) {
            let bestWaypoint = null;
            let bestDistance = Infinity;
            
            for (const angle of testAngles) {
                const testPoint = this._getPointAtAngle(start, end, angle, gridSize * 2);
                
                if (!this._pointCollidesWithObstacles(testPoint, obstacles)) {
                    const distanceToEnd = testPoint.distanceTo(end);
                    if (distanceToEnd < bestDistance) {
                        bestDistance = distanceToEnd;
                        bestWaypoint = testPoint;
                    }
                }
            }
            
            if (bestWaypoint) {
                waypoints.push(bestWaypoint);
                start = bestWaypoint; // Continue from this waypoint
            } else {
                break;
            }
        }
        
        return waypoints;
    }

    /**
     * Create a pathfinding node
     * @private
     */
    _createNode(x, y, z) {
        return { x: Math.round(x), y: Math.round(y), z: Math.round(z) };
    }

    /**
     * Get unique key for a node
     * @private
     */
    _getNodeKey(node) {
        return `${node.x},${node.y},${node.z}`;
    }

    /**
     * Get neighbors of a node
     * @private
     */
    _getNeighbors(node, gridSize, allowDiagonal) {
        const neighbors = [];
        const directions = [
            { x: gridSize, y: 0, z: 0 },
            { x: -gridSize, y: 0, z: 0 },
            { x: 0, y: 0, z: gridSize },
            { x: 0, y: 0, z: -gridSize }
        ];
        
        if (allowDiagonal) {
            directions.push(
                { x: gridSize, y: 0, z: gridSize },
                { x: gridSize, y: 0, z: -gridSize },
                { x: -gridSize, y: 0, z: gridSize },
                { x: -gridSize, y: 0, z: -gridSize }
            );
        }
        
        for (const dir of directions) {
            neighbors.push({
                x: node.x + dir.x,
                y: node.y + dir.y,
                z: node.z + dir.z
            });
        }
        
        return neighbors;
    }

    /**
     * Heuristic function for A* (Manhattan distance)
     * @private
     */
    _heuristic(a, b) {
        return Math.abs(a.x - b.x) + Math.abs(a.y - b.y) + Math.abs(a.z - b.z);
    }

    /**
     * Distance between two nodes
     * @private
     */
    _distance(a, b) {
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        const dz = a.z - b.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * Check if node collides with obstacles
     * @private
     */
    _nodeCollidesWithObstacles(node, obstacles) {
        const point = new Vector3(node.x, node.y, node.z);
        return this._pointCollidesWithObstacles(point, obstacles);
    }

    /**
     * Check if point collides with obstacles
     * @private
     */
    _pointCollidesWithObstacles(point, obstacles) {
        for (const obstacle of obstacles) {
            if (this._pointIntersectsObstacle(point, obstacle)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if point intersects with obstacle
     * @private
     */
    _pointIntersectsObstacle(point, obstacle) {
        if (!obstacle.box) return false;
        
        const { min, max } = obstacle.box;
        return (
            point.x >= min.x && point.x <= max.x &&
            point.y >= min.y && point.y <= max.y &&
            point.z >= min.z && point.z <= max.z
        );
    }

    /**
     * Check if path is clear between two points
     * @private
     */
    _isPathClear(start, end, obstacles) {
        const direction = end.clone().sub(start);
        const distance = direction.length();
        direction.normalize();
        
        const steps = Math.ceil(distance / 0.5);
        const stepSize = distance / steps;
        
        for (let i = 1; i < steps; i++) {
            const testPoint = start.clone().add(direction.clone().multiplyScalar(stepSize * i));
            if (this._pointCollidesWithObstacles(testPoint, obstacles)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Get point at angle from start towards end
     * @private
     */
    _getPointAtAngle(start, end, angle, distance) {
        const direction = end.clone().sub(start).normalize();
        const perpendicular = new Vector3(-direction.z, 0, direction.x);
        
        const rotatedDir = direction.clone()
            .multiplyScalar(Math.cos(angle))
            .add(perpendicular.multiplyScalar(Math.sin(angle)));
        
        return start.clone().add(rotatedDir.multiplyScalar(distance));
    }

    /**
     * Reconstruct path from A* result
     * @private
     */
    _reconstructPath(cameFrom, current) {
        const path = [new Vector3(current.x, current.y, current.z)];
        let currentKey = this._getNodeKey(current);
        
        while (cameFrom.has(currentKey)) {
            current = cameFrom.get(currentKey);
            path.unshift(new Vector3(current.x, current.y, current.z));
            currentKey = this._getNodeKey(current);
        }
        
        return path;
    }

    /**
     * Get cache key for pathfinding request
     * @private
     */
    _getCacheKey(start, end, obstacleCount) {
        return `${Math.round(start.x)},${Math.round(start.y)},${Math.round(start.z)}_${Math.round(end.x)},${Math.round(end.y)},${Math.round(end.z)}_${obstacleCount}`;
    }

    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(processingTime, success) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.totalTime += processingTime;
        this.performanceMetrics.averageTime = 
            this.performanceMetrics.totalTime / this.performanceMetrics.totalRequests;
        
        if (success) {
            this.performanceMetrics.pathsFound++;
        } else {
            this.performanceMetrics.pathsFailed++;
        }
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.pathCache.size,
            successRate: this.performanceMetrics.pathsFound / 
                        Math.max(1, this.performanceMetrics.totalRequests),
            timestamp: Date.now()
        };
    }

    /**
     * Clear path cache
     */
    clearCache() {
        this.pathCache.clear();
        console.log('[PathfindingWorker] Cache cleared');
    }
}

// Initialize worker
const pathfindingWorker = new PathfindingWorker();

// Handle messages from main thread
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        let result;
        
        switch (type) {
            case 'findPath':
                result = pathfindingWorker.findPath(data);
                break;
            case 'findPathsBatch':
                result = pathfindingWorker.findPathsBatch(data);
                break;
            case 'checkLineOfSight':
                result = pathfindingWorker.checkLineOfSight(data);
                break;
            case 'getPerformanceMetrics':
                result = pathfindingWorker.getPerformanceMetrics();
                break;
            case 'clearCache':
                pathfindingWorker.clearCache();
                result = { success: true };
                break;
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
        
        // Send result back to main thread
        self.postMessage({
            requestId: requestId,
            result: result,
            success: true
        });
        
    } catch (error) {
        console.error('[PathfindingWorker] Error processing task:', error);
        
        // Send error back to main thread
        self.postMessage({
            requestId: requestId,
            error: error.message,
            success: false
        });
    }
};
