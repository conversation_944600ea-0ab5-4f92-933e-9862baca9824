// src/workers/bulletPatternWorker.js
// High-performance bullet pattern generation worker

/**
 * Lightweight Vector3 implementation for worker context
 */
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    clone() {
        return new Vector3(this.x, this.y, this.z);
    }
    
    copy(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    }
    
    add(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    }
    
    multiplyScalar(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    }
    
    normalize() {
        const length = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        if (length > 0) {
            this.multiplyScalar(1 / length);
        }
        return this;
    }
    
    distanceTo(v) {
        const dx = this.x - v.x;
        const dy = this.y - v.y;
        const dz = this.z - v.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }
}

/**
 * High-performance bullet pattern generation worker
 */
class BulletPatternWorker {
    constructor() {
        this.performanceMetrics = {
            totalRequests: 0,
            totalTime: 0,
            averageTime: 0,
            bulletsGenerated: 0
        };
        
        // Pattern cache for repeated requests
        this.patternCache = new Map();
        this.maxCacheSize = 50;
        
        console.log('[BulletPatternWorker] Initialized');
    }

    /**
     * Generate bullet pattern data
     * @param {Object} data - Pattern generation request
     * @returns {Object} Generated bullet pattern
     */
    generatePattern(data) {
        const { 
            patternType, 
            position, 
            targetPosition, 
            intensity = 1.0, 
            speedMultiplier = 1.0,
            optimizeForLowEnd = false,
            customParams = {}
        } = data;
        
        const startTime = performance.now();
        
        // Check cache first
        const cacheKey = this._getCacheKey(patternType, intensity, optimizeForLowEnd);
        if (this.patternCache.has(cacheKey)) {
            const cachedPattern = this.patternCache.get(cacheKey);
            const adaptedPattern = this._adaptCachedPattern(cachedPattern, position, targetPosition, speedMultiplier);
            
            this._updateMetrics(performance.now() - startTime, adaptedPattern.bullets.length);
            return {
                ...adaptedPattern,
                fromCache: true,
                metadata: { processingTime: performance.now() - startTime }
            };
        }
        
        let pattern;
        
        switch (patternType) {
            case 'spiral':
                pattern = this._generateSpiralPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'grid':
                pattern = this._generateGridPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'circle':
                pattern = this._generateCirclePattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'wave':
                pattern = this._generateWavePattern(position, targetPosition, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'burst':
                pattern = this._generateBurstPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'chain':
                pattern = this._generateChainPattern(position, targetPosition, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            case 'vortex':
                pattern = this._generateVortexPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
                break;
            default:
                throw new Error(`Unknown pattern type: ${patternType}`);
        }
        
        // Cache successful patterns
        if (pattern && this.patternCache.size < this.maxCacheSize) {
            this.patternCache.set(cacheKey, this._createCacheablePattern(pattern));
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, pattern.bullets.length);
        
        return {
            ...pattern,
            metadata: {
                processingTime,
                bulletCount: pattern.bullets.length,
                patternType
            }
        };
    }

    /**
     * Generate multiple patterns in batch
     * @param {Object} data - Batch pattern request
     * @returns {Object} Batch results
     */
    generatePatternBatch(data) {
        const { patterns, optimizeForLowEnd = false } = data;
        
        if (!patterns || !Array.isArray(patterns)) {
            throw new Error('Invalid batch pattern data');
        }
        
        const startTime = performance.now();
        const results = [];
        let totalBullets = 0;
        
        patterns.forEach((patternData, index) => {
            try {
                const result = this.generatePattern({
                    ...patternData,
                    optimizeForLowEnd
                });
                results.push({
                    index,
                    success: true,
                    ...result
                });
                totalBullets += result.bullets.length;
            } catch (error) {
                results.push({
                    index,
                    success: false,
                    error: error.message,
                    bullets: []
                });
            }
        });
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, totalBullets);
        
        return {
            results,
            metadata: {
                processingTime,
                totalPatterns: patterns.length,
                totalBullets,
                successfulPatterns: results.filter(r => r.success).length
            }
        };
    }

    /**
     * Generate spiral pattern
     * @private
     */
    _generateSpiralPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const arms = customParams.arms || 4;
        const projectilesPerArm = optimizeForLowEnd ? Math.max(3, Math.floor(8 * intensity * 0.5)) : Math.floor(12 * intensity);
        const currentTime = Date.now() * 0.001;

        // Convert serialized position to Vector3 if needed
        const centerPosition = position.x !== undefined ?
            new Vector3(position.x, position.y, position.z) :
            position; // Already a Vector3

        for (let arm = 0; arm < arms; arm++) {
            const armOffset = (arm / arms) * Math.PI * 2;

            for (let i = 0; i < projectilesPerArm; i++) {
                const distance = 0.5 + (i / projectilesPerArm) * 2.0;
                const angle = armOffset + (i / projectilesPerArm) * Math.PI * 2 * (1 + intensity);

                const direction = new Vector3(
                    Math.cos(angle + currentTime * 2),
                    0.05 * Math.sin(currentTime * 3),
                    Math.sin(angle + currentTime * 2)
                ).normalize();

                const bulletPosition = centerPosition.clone().add(direction.clone().multiplyScalar(0.2 * i));
                
                bullets.push({
                    position: bulletPosition,
                    direction: direction,
                    speed: (0.6 + (intensity * 0.8)) * speedMultiplier,
                    type: 'spiral'
                });
            }
        }
        
        return { bullets, patternType: 'spiral' };
    }

    /**
     * Generate grid pattern
     * @private
     */
    _generateGridPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const baseGridSize = customParams.gridSize || 8;
        const gridSize = optimizeForLowEnd ? Math.max(4, Math.floor(baseGridSize * 0.7)) : baseGridSize;
        const scaleFactor = optimizeForLowEnd ? 0.5 : 1.0;

        // Convert serialized position to Vector3 if needed
        const centerPosition = position.x !== undefined ?
            new Vector3(position.x, position.y, position.z) :
            position; // Already a Vector3

        for (let x = 0; x < gridSize; x++) {
            for (let z = 0; z < gridSize; z++) {
                // Skip some projectiles for performance
                if ((x + z) % 2 === 0 || Math.random() > (0.5 * scaleFactor)) continue;

                const nx = (x / (gridSize - 1)) * 2 - 1;
                const nz = (z / (gridSize - 1)) * 2 - 1;

                const direction = new Vector3(nx, 0, nz).normalize();

                bullets.push({
                    position: centerPosition.clone(),
                    direction: direction,
                    speed: (1.0 + (intensity * 1.0)) * speedMultiplier,
                    type: 'grid'
                });
            }
        }
        
        return { bullets, patternType: 'grid' };
    }

    /**
     * Generate circle pattern
     * @private
     */
    _generateCirclePattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const baseCount = customParams.bulletCount || 16;
        const bulletCount = optimizeForLowEnd ? Math.max(8, Math.floor(baseCount * 0.6)) : Math.floor(baseCount * intensity);
        const radius = customParams.radius || 1.0;

        // Convert serialized position to Vector3 if needed
        const centerPosition = position.x !== undefined ?
            new Vector3(position.x, position.y, position.z) :
            position; // Already a Vector3

        for (let i = 0; i < bulletCount; i++) {
            const angle = (i / bulletCount) * Math.PI * 2;
            const direction = new Vector3(
                Math.cos(angle),
                0,
                Math.sin(angle)
            ).normalize();

            const bulletPosition = centerPosition.clone().add(direction.clone().multiplyScalar(radius));
            
            bullets.push({
                position: bulletPosition,
                direction: direction,
                speed: (0.8 + intensity * 0.4) * speedMultiplier,
                type: 'circle'
            });
        }
        
        return { bullets, patternType: 'circle' };
    }

    /**
     * Generate wave pattern
     * @private
     */
    _generateWavePattern(position, targetPosition, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const waveCount = optimizeForLowEnd ? 3 : 5;
        const bulletsPerWave = optimizeForLowEnd ? Math.max(4, Math.floor(8 * intensity * 0.5)) : Math.floor(12 * intensity);
        const waveSpread = customParams.spread || Math.PI / 3;

        // Convert serialized positions to Vector3 if needed
        const centerPosition = position.x !== undefined ?
            new Vector3(position.x, position.y, position.z) :
            position; // Already a Vector3

        const targetPos = targetPosition && targetPosition.x !== undefined ?
            new Vector3(targetPosition.x, targetPosition.y, targetPosition.z) :
            targetPosition; // Already a Vector3 or null

        const baseDirection = targetPos ?
            new Vector3().copy(targetPos).add(centerPosition.clone().multiplyScalar(-1)).normalize() :
            new Vector3(0, 0, 1);

        for (let wave = 0; wave < waveCount; wave++) {
            const waveOffset = (wave / waveCount - 0.5) * waveSpread;

            for (let i = 0; i < bulletsPerWave; i++) {
                const spreadAngle = (i / bulletsPerWave - 0.5) * waveSpread * 0.5;
                const totalAngle = waveOffset + spreadAngle;

                const direction = new Vector3(
                    baseDirection.x * Math.cos(totalAngle) - baseDirection.z * Math.sin(totalAngle),
                    baseDirection.y,
                    baseDirection.x * Math.sin(totalAngle) + baseDirection.z * Math.cos(totalAngle)
                ).normalize();

                bullets.push({
                    position: centerPosition.clone(),
                    direction: direction,
                    speed: (0.7 + intensity * 0.6) * speedMultiplier,
                    type: 'wave',
                    waveIndex: wave
                });
            }
        }
        
        return { bullets, patternType: 'wave' };
    }

    /**
     * Generate burst pattern
     * @private
     */
    _generateBurstPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const baseBulletCount = customParams.bulletCount || 24;
        const bulletCount = optimizeForLowEnd ? Math.max(12, Math.floor(baseBulletCount * 0.5)) : Math.floor(baseBulletCount * intensity);
        const layers = optimizeForLowEnd ? 1 : 2;
        
        for (let layer = 0; layer < layers; layer++) {
            const layerRadius = 0.5 + layer * 0.3;
            const layerBullets = Math.floor(bulletCount / layers);
            
            for (let i = 0; i < layerBullets; i++) {
                const angle = (i / layerBullets) * Math.PI * 2;
                const elevation = (Math.random() - 0.5) * 0.4;
                
                const direction = new Vector3(
                    Math.cos(angle),
                    elevation,
                    Math.sin(angle)
                ).normalize();
                
                bullets.push({
                    position: position.clone(),
                    direction: direction,
                    speed: (0.5 + intensity * 0.8 + layer * 0.2) * speedMultiplier,
                    type: 'burst',
                    layer: layer
                });
            }
        }
        
        return { bullets, patternType: 'burst' };
    }

    /**
     * Generate chain pattern
     * @private
     */
    _generateChainPattern(position, targetPosition, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        
        if (!targetPosition) {
            // Fallback to burst pattern if no target
            return this._generateBurstPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams);
        }
        
        const baseChainLength = customParams.chainLength || 8;
        const chainLength = optimizeForLowEnd ? Math.max(4, Math.floor(baseChainLength * 0.6)) : baseChainLength;
        
        const toTarget = new Vector3().copy(targetPosition).add(position.clone().multiplyScalar(-1));
        
        for (let i = 0; i < chainLength; i++) {
            const t = i / (chainLength - 1);
            const chainPos = new Vector3().copy(position).add(toTarget.clone().multiplyScalar(t));
            
            // Add vertical oscillation
            chainPos.y += Math.sin(t * Math.PI) * 0.5;
            
            bullets.push({
                position: chainPos,
                direction: toTarget.clone().normalize(),
                speed: speedMultiplier * (0.5 + t * 1.0),
                type: 'chain',
                chainIndex: i
            });
        }
        
        return { bullets, patternType: 'chain' };
    }

    /**
     * Generate vortex pattern
     * @private
     */
    _generateVortexPattern(position, intensity, speedMultiplier, optimizeForLowEnd, customParams) {
        const bullets = [];
        const spirals = optimizeForLowEnd ? 2 : 3;
        const bulletsPerSpiral = optimizeForLowEnd ? Math.max(6, Math.floor(10 * intensity * 0.5)) : Math.floor(15 * intensity);
        const currentTime = Date.now() * 0.001;
        
        for (let spiral = 0; spiral < spirals; spiral++) {
            const spiralOffset = (spiral / spirals) * Math.PI * 2;
            
            for (let i = 0; i < bulletsPerSpiral; i++) {
                const t = i / bulletsPerSpiral;
                const angle = spiralOffset + t * Math.PI * 4 + currentTime;
                const radius = 0.2 + t * 1.5;
                
                const direction = new Vector3(
                    Math.cos(angle) * radius,
                    Math.sin(t * Math.PI) * 0.3,
                    Math.sin(angle) * radius
                ).normalize();
                
                bullets.push({
                    position: position.clone(),
                    direction: direction,
                    speed: (0.4 + intensity * 0.6) * speedMultiplier,
                    type: 'vortex',
                    spiralIndex: spiral
                });
            }
        }
        
        return { bullets, patternType: 'vortex' };
    }

    /**
     * Adapt cached pattern to new parameters
     * @private
     */
    _adaptCachedPattern(cachedPattern, position, targetPosition, speedMultiplier) {
        const adaptedBullets = cachedPattern.bullets.map(bullet => ({
            ...bullet,
            position: position.clone().add(bullet.relativePosition || new Vector3()),
            speed: bullet.baseSpeed * speedMultiplier
        }));
        
        return {
            bullets: adaptedBullets,
            patternType: cachedPattern.patternType
        };
    }

    /**
     * Create cacheable pattern
     * @private
     */
    _createCacheablePattern(pattern) {
        const cacheableBullets = pattern.bullets.map(bullet => ({
            relativePosition: bullet.position.clone().add(new Vector3().multiplyScalar(-1)), // Relative to origin
            direction: bullet.direction,
            baseSpeed: bullet.speed,
            type: bullet.type
        }));
        
        return {
            bullets: cacheableBullets,
            patternType: pattern.patternType
        };
    }

    /**
     * Get cache key for pattern
     * @private
     */
    _getCacheKey(patternType, intensity, optimizeForLowEnd) {
        return `${patternType}_${Math.floor(intensity * 10)}_${optimizeForLowEnd}`;
    }

    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(processingTime, bulletCount) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.totalTime += processingTime;
        this.performanceMetrics.averageTime = 
            this.performanceMetrics.totalTime / this.performanceMetrics.totalRequests;
        this.performanceMetrics.bulletsGenerated += bulletCount;
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.patternCache.size,
            averageBulletsPerPattern: this.performanceMetrics.bulletsGenerated / 
                                    Math.max(1, this.performanceMetrics.totalRequests),
            timestamp: Date.now()
        };
    }

    /**
     * Clear pattern cache
     */
    clearCache() {
        this.patternCache.clear();
        console.log('[BulletPatternWorker] Cache cleared');
    }
}

// Initialize worker
const bulletPatternWorker = new BulletPatternWorker();

// Handle messages from main thread
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        let result;
        
        switch (type) {
            case 'generatePattern':
                result = bulletPatternWorker.generatePattern(data);
                break;
            case 'generatePatternBatch':
                result = bulletPatternWorker.generatePatternBatch(data);
                break;
            case 'getPerformanceMetrics':
                result = bulletPatternWorker.getPerformanceMetrics();
                break;
            case 'clearCache':
                bulletPatternWorker.clearCache();
                result = { success: true };
                break;
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
        
        // Send result back to main thread
        self.postMessage({
            requestId: requestId,
            result: result,
            success: true
        });
        
    } catch (error) {
        console.error('[BulletPatternWorker] Error processing task:', error);
        
        // Send error back to main thread
        self.postMessage({
            requestId: requestId,
            error: error.message,
            success: false
        });
    }
};
