/**
 * Destruction Worker - Handles debris creation calculations off main thread
 * This worker processes enemy and object destruction physics calculations
 * and returns debris data for the main thread to create actual meshes.
 */

// Minimal Three.js-like math utilities for worker
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    clone() {
        return new Vector3(this.x, this.y, this.z);
    }
    
    add(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    }
    
    sub(v) {
        this.x -= v.x;
        this.y -= v.y;
        this.z -= v.z;
        return this;
    }
    
    multiplyScalar(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    }
    
    normalize() {
        const length = Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
        if (length > 0) {
            this.x /= length;
            this.y /= length;
            this.z /= length;
        }
        return this;
    }
    
    length() {
        return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
    }
    
    dot(v) {
        return this.x * v.x + this.y * v.y + this.z * v.z;
    }
    
    addScaledVector(v, s) {
        this.x += v.x * s;
        this.y += v.y * s;
        this.z += v.z * s;
        return this;
    }
    
    subVectors(a, b) {
        this.x = a.x - b.x;
        this.y = a.y - b.y;
        this.z = a.z - b.z;
        return this;
    }
    
    lerp(v, alpha) {
        this.x += (v.x - this.x) * alpha;
        this.y += (v.y - this.y) * alpha;
        this.z += (v.z - this.z) * alpha;
        return this;
    }
    
    set(x, y, z) {
        this.x = x;
        this.y = y;
        this.z = z;
        return this;
    }
}

class Box3 {
    constructor(min, max) {
        this.min = min || new Vector3(Infinity, Infinity, Infinity);
        this.max = max || new Vector3(-Infinity, -Infinity, -Infinity);
    }
    
    setFromCenterAndSize(center, size) {
        const halfSize = size.clone().multiplyScalar(0.5);
        this.min = center.clone().sub(halfSize);
        this.max = center.clone().add(halfSize);
        return this;
    }
    
    intersectsBox(box) {
        return !(box.max.x < this.min.x || box.min.x > this.max.x ||
                box.max.y < this.min.y || box.min.y > this.max.y ||
                box.max.z < this.min.z || box.min.z > this.max.z);
    }
}

// Create THREE-like namespace for compatibility
const THREE = {
    Vector3: Vector3,
    Box3: Box3
};

class DestructionWorker {
    constructor() {
        this.VOXEL_SIZE = 30; // Matches shared.js constant
        
        // Destruction parameters cache
        this.destructionParamsCache = new Map();
        
        console.log('[DestructionWorker] Initialized');
    }

    /**
     * Convert serialized vector to Vector3
     */
    _toVector3(serializedVector) {
        if (!serializedVector) return new Vector3();
        return new Vector3(serializedVector.x, serializedVector.y, serializedVector.z);
    }

    /**
     * Get destruction parameters for a specific enemy type
     * Matches _getDestructionParamsForEnemy in DungeonHandler
     */
    _getDestructionParamsForEnemy(enemyType) {
        if (this.destructionParamsCache.has(`enemy_${enemyType}`)) {
            return this.destructionParamsCache.get(`enemy_${enemyType}`);
        }

        let destructionParams = {
            explosionForce: 1.2,
            upwardForce: 1.5,
            tumbleSpeed: 2.0,
            fallbackExplosionForce: 1.0,
            fallbackUpwardForce: 1.8,
            fallbackTumbleSpeed: 2.2,
            fallbackDebrisPerGroup: 3,
            fallbackDebrisExplosionForce: 1.5,
            fallbackDebrisUpwardForce: 1.2,
            fallbackDebrisTumbleSpeed: 2.5,
            debrisLifeTime: [3, 5]
        };

        switch (enemyType) {
            case 'nairabos':
            case 'chronarch':
                destructionParams = {
                    explosionForce: 2.0,
                    upwardForce: 2.5,
                    tumbleSpeed: 3.5,
                    fallbackExplosionForce: 1.8,
                    fallbackUpwardForce: 3.0,
                    fallbackTumbleSpeed: 3.2,
                    fallbackDebrisPerGroup: 2,
                    fallbackDebrisExplosionForce: 1.7,
                    fallbackDebrisUpwardForce: 2.5,
                    fallbackDebrisTumbleSpeed: 3.2,
                    debrisLifeTime: [3, 6]
                };
                break;

            case 'magma_golem':
            case 'skeleton':
            case 'zombie':
                destructionParams = {
                    explosionForce: 1.8,
                    upwardForce: 2.2,
                    tumbleSpeed: 3.0,
                    fallbackExplosionForce: 1.6,
                    fallbackUpwardForce: 2.8,
                    fallbackTumbleSpeed: 2.8,
                    fallbackDebrisPerGroup: 2,
                    fallbackDebrisExplosionForce: 1.5,
                    fallbackDebrisUpwardForce: 2.2,
                    fallbackDebrisTumbleSpeed: 2.8,
                    debrisLifeTime: [4, 7]
                };
                break;

            case 'tomb_guardian':
                destructionParams = {
                    explosionForce: 0.8,
                    upwardForce: 0.5,
                    tumbleSpeed: 1.0,
                    fallbackExplosionForce: 0.6,
                    fallbackUpwardForce: 0.8,
                    fallbackTumbleSpeed: 1.0,
                    fallbackDebrisPerGroup: 4,
                    fallbackDebrisExplosionForce: 0.5,
                    fallbackDebrisUpwardForce: 0.6,
                    fallbackDebrisTumbleSpeed: 1.0,
                    debrisLifeTime: [5, 8]
                };
                break;
        }

        this.destructionParamsCache.set(`enemy_${enemyType}`, destructionParams);
        return destructionParams;
    }

    /**
     * Get destruction parameters for floor objects
     * Matches _getDestructionParamsForObject in DungeonHandler
     */
    _getDestructionParamsForObject(objectType) {
        if (this.destructionParamsCache.has(`object_${objectType}`)) {
            return this.destructionParamsCache.get(`object_${objectType}`);
        }

        let destructionParams = {
            explosionForce: 1.8,
            upwardForce: 2.2,
            tumbleSpeed: 2.8,
            fallbackExplosionForce: 1.5,
            fallbackUpwardForce: 2.2,
            fallbackTumbleSpeed: 2.2,
            fallbackDebrisPerGroup: 3,
            fallbackDebrisExplosionForce: 1.4,
            fallbackDebrisUpwardForce: 2.0,
            fallbackDebrisTumbleSpeed: 2.2,
            debrisLifeTime: [3, 5]
        };

        switch (objectType) {
            case 'stone_vase':
                destructionParams = {
                    explosionForce: 1.5,
                    upwardForce: 2.0,
                    tumbleSpeed: 2.8,
                    fallbackExplosionForce: 1.2,
                    fallbackUpwardForce: 2.0,
                    fallbackTumbleSpeed: 2.2,
                    fallbackDebrisPerGroup: 3,
                    fallbackDebrisExplosionForce: 1.0,
                    fallbackDebrisUpwardForce: 1.8,
                    fallbackDebrisTumbleSpeed: 2.2,
                    debrisLifeTime: [3, 6]
                };
                break;

            case 'stone_pillar':
                destructionParams = {
                    explosionForce: 1.8,
                    upwardForce: 2.2,
                    tumbleSpeed: 2.5,
                    fallbackExplosionForce: 1.5,
                    fallbackUpwardForce: 2.5,
                    fallbackTumbleSpeed: 2.0,
                    fallbackDebrisPerGroup: 3,
                    fallbackDebrisExplosionForce: 0.8,
                    fallbackDebrisUpwardForce: 1.2,
                    fallbackDebrisTumbleSpeed: 2.2,
                    debrisLifeTime: [3, 6]
                };
                break;
        }

        this.destructionParamsCache.set(`object_${objectType}`, destructionParams);
        return destructionParams;
    }

    /**
     * Calculate debris collision normal
     * Matches _calculateDebrisCollisionNormal in DungeonHandler
     */
    _calculateDebrisCollisionNormal(debrisPosition, collisionObject) {
        const collisionCenter = this._toVector3(collisionObject.center) || new Vector3();
        const collisionSize = this._toVector3(collisionObject.size) || new Vector3(1, 1, 1);
        
        // Calculate relative position
        const relativePos = new Vector3().subVectors(debrisPosition, collisionCenter);
        
        // Find dominant axis and return appropriate normal
        const absX = Math.abs(relativePos.x);
        const absY = Math.abs(relativePos.y);
        const absZ = Math.abs(relativePos.z);
        
        if (absX > absY && absX > absZ) {
            return new Vector3(Math.sign(relativePos.x), 0, 0);
        } else if (absY > absZ) {
            return new Vector3(0, Math.sign(relativePos.y), 0);
        } else {
            return new Vector3(0, 0, Math.sign(relativePos.z));
        }
    }

    /**
     * Check collision between debris and collision objects
     */
    _checkDebrisCollision(debrisPosition, debrisSize, collisionObjects) {
        const debrisBox = new Box3().setFromCenterAndSize(
            debrisPosition, 
            new Vector3(debrisSize, debrisSize, debrisSize)
        );

        for (const cObject of collisionObjects) {
            if (!cObject || !cObject.box) continue;
            
            // Create Box3 from serialized data
            const collisionBox = new Box3(
                this._toVector3(cObject.box.min),
                this._toVector3(cObject.box.max)
            );
            
            if (debrisBox.intersectsBox(collisionBox)) {
                return {
                    collides: true,
                    normal: this._calculateDebrisCollisionNormal(debrisPosition, cObject)
                };
            }
        }
        
        return { collides: false, normal: null };
    }

    /**
     * Process enemy destruction - creates debris data for enemy death
     */
    processEnemyDestruction(data) {
        const {
            enemyData,
            enemyType,
            projectileVelocity,
            groundLevel,
            collisionObjects,
            creationTime
        } = data;

        console.log(`[DestructionWorker] Processing enemy destruction: ${enemyType}`);

        const destructionParams = this._getDestructionParamsForEnemy(enemyType);
        const debris = [];

        // Extract physics parameters
        const explosionForce = destructionParams.explosionForce || 1.8;
        const upwardForce = destructionParams.upwardForce || 1.5;
        const tumbleSpeed = destructionParams.tumbleSpeed || 2.0;
        const debrisPerGroup = destructionParams.fallbackDebrisPerGroup || 3;
        const debrisExplosionForce = destructionParams.fallbackDebrisExplosionForce || 1.5;
        const debrisUpwardForce = destructionParams.fallbackDebrisUpwardForce || 1.2;
        const debrisTumbleSpeed = destructionParams.fallbackDebrisTumbleSpeed || 2.5;

        // Process body part groups
        if (enemyData.bodyPartGroups && enemyData.bodyPartGroups.length > 0) {
            enemyData.bodyPartGroups.forEach((group, groupIndex) => {
                const groupName = group.name || `group_${groupIndex}`;
                
                // Create body part debris
                group.meshes.forEach((meshData, meshIndex) => {
                    const bodyPartId = `bodypart_${enemyData.id}_${groupName}_${meshIndex}_${creationTime}`;
                    
                    // Calculate directional flow
                    let velocity = new Vector3(
                        (Math.random() - 0.5) * 2,
                        0,
                        (Math.random() - 0.5) * 2
                    ).normalize().multiplyScalar(explosionForce);
                    
                    // Add projectile momentum if available
                    if (projectileVelocity) {
                        const projectileBoost = new Vector3(projectileVelocity.x, projectileVelocity.y, projectileVelocity.z).multiplyScalar(0.3);
                        velocity.add(projectileBoost);
                    }
                    
                    velocity.y += upwardForce * (0.8 + Math.random() * 0.4);
                    
                    // Check for wall collision and adjust
                    const testPosition = this._toVector3(meshData.worldPosition).addScaledVector(velocity, 0.15);
                    const collision = this._checkDebrisCollision(testPosition, this.VOXEL_SIZE * 3, collisionObjects);
                    
                    if (collision.collides) {
                        const velocityDotNormal = velocity.dot(collision.normal);
                        if (velocityDotNormal < 0) {
                            const reflection = collision.normal.clone().multiplyScalar(velocityDotNormal * 2);
                            velocity.sub(reflection);
                        }
                    }
                    
                    const angularVelocity = new Vector3(
                        (Math.random() - 0.5) * tumbleSpeed,
                        (Math.random() - 0.5) * tumbleSpeed,
                        (Math.random() - 0.5) * tumbleSpeed
                    );
                    
                    debris.push({
                        type: 'bodyPart',
                        id: bodyPartId,
                        position: this._toVector3(meshData.worldPosition),
                        quaternion: meshData.worldQuaternion,
                        velocity: velocity,
                        angularVelocity: angularVelocity,
                        material: meshData.material,
                        geometryType: enemyType === 'bat' ? 'batDebris' : 'standardDebris',
                        userData: {
                            isBonePiece: false,
                            isDebrisPiece: true,
                            debrisId: bodyPartId,
                            sourceEnemyId: enemyData.id,
                            isFalling: true,
                            persistUntilRoomChange: true,
                            needsStorage: true,
                            velocity: velocity,
                            angularVelocity: angularVelocity,
                            creationTime: creationTime
                        }
                    });
                });
                
                // Create small debris pieces for this group
                for (let i = 0; i < debrisPerGroup; i++) {
                    const debrisId = `debris_${enemyData.id}_${groupName}_${i}_${creationTime}`;
                    
                    // Enhanced directional flow for small debris
                    const baseDirection = new Vector3(
                        (Math.random() - 0.5) * 2,
                        0,
                        (Math.random() - 0.5) * 2
                    ).normalize();
                    
                    let bestDirection = baseDirection.clone();
                    let bestDirectionSafe = false;
                    
                    // Try different spread variations
                    for (let attempt = 0; attempt < 3; attempt++) {
                        const spreadFactor = 0.6 + (attempt * 0.3);
                        const testDir = baseDirection.clone();
                        testDir.x += (Math.random() - 0.5) * spreadFactor;
                        testDir.z += (Math.random() - 0.5) * spreadFactor;
                        testDir.normalize();
                        
                        const testVelocity = testDir.clone().multiplyScalar(debrisExplosionForce);
                        const testPosition = this._toVector3(group.worldPosition).addScaledVector(testVelocity, 0.15);
                        
                        const collision = this._checkDebrisCollision(testPosition, this.VOXEL_SIZE * 3, collisionObjects);
                        
                        if (!collision.collides) {
                            bestDirection = testDir;
                            bestDirectionSafe = true;
                            break;
                        } else if (!bestDirectionSafe) {
                            bestDirection = testDir;
                        }
                    }
                    
                    const debrisVelocity = bestDirection.multiplyScalar(debrisExplosionForce + (Math.random() - 0.5) * 0.4);
                    
                    // Add projectile momentum
                    if (projectileVelocity) {
                        const projectileBoost = this._toVector3(projectileVelocity).multiplyScalar(0.15);
                        debrisVelocity.add(projectileBoost);
                    }
                    
                    debrisVelocity.y += debrisUpwardForce * (0.7 + Math.random() * 0.6);
                    
                    // Wall collision check for small debris
                    const debrisTestPosition = this._toVector3(group.worldPosition).addScaledVector(debrisVelocity, 0.1);
                    const debrisCollision = this._checkDebrisCollision(debrisTestPosition, this.VOXEL_SIZE * 3, collisionObjects);
                    
                    if (debrisCollision.collides) {
                        const velocityDotNormal = debrisVelocity.dot(debrisCollision.normal);
                        if (velocityDotNormal < 0) {
                            const reflection = debrisCollision.normal.clone().multiplyScalar(velocityDotNormal * 2);
                            debrisVelocity.sub(reflection);
                            debrisVelocity.multiplyScalar(0.7);
                        }
                    }
                    
                    const debrisAngularVelocity = new Vector3(
                        (Math.random() - 0.5) * debrisTumbleSpeed,
                        (Math.random() - 0.5) * debrisTumbleSpeed,
                        (Math.random() - 0.5) * debrisTumbleSpeed
                    );
                    
                    debris.push({
                        type: 'smallDebris',
                        id: debrisId,
                        position: this._toVector3(group.worldPosition),
                        velocity: debrisVelocity,
                        angularVelocity: debrisAngularVelocity,
                        material: group.material,
                        geometryType: enemyType === 'bat' ? 'batDebris' : 'standardDebris',
                        userData: {
                            isBonePiece: false,
                            isDebrisPiece: true,
                            debrisId: debrisId,
                            sourceEnemyId: enemyData.id,
                            isFalling: true,
                            persistUntilRoomChange: true,
                            needsStorage: true,
                            velocity: debrisVelocity,
                            angularVelocity: debrisAngularVelocity,
                            creationTime: creationTime
                        }
                    });
                }
            });
        }

        return {
            type: 'enemyDestruction',
            debris: debris,
            enemyId: enemyData.id
        };
    }

    /**
     * Process object destruction - creates debris data for destructible objects
     */
    processObjectDestruction(data) {
        const {
            objectData,
            objectType,
            pointOfImpact,
            projectileVelocity,
            collisionObjects
        } = data;

        console.log(`[DestructionWorker] Processing object destruction: ${objectType}`);

        const destructionParams = this._getDestructionParamsForObject(objectType);
        const debris = [];

        // Physics parameters
        const explosionForce = destructionParams.explosionForce;
        const upwardForce = destructionParams.upwardForce;
        const tumbleSpeed = destructionParams.tumbleSpeed;

        // Check if object has voxel data for true voxel destruction
        if (objectData.originalVoxels && objectData.originalVoxels.length > 0) {
            console.log(`[DestructionWorker] Using TRUE VOXEL destruction. Count: ${objectData.originalVoxels.length}`);
            
            const voxelScale = objectData.voxelScale;
            let debrisVoxelScale = voxelScale;
            
            // Make vase debris pieces bigger
            if (objectType === 'stone_vase') {
                debrisVoxelScale = voxelScale * 1.5;
            }

            // Performance optimization: reduce debris count
            let voxelStep, maxDebrisPieces;
            if (objectType === 'stone_vase') {
                voxelStep = 8; // Only every 8th voxel
                maxDebrisPieces = Math.ceil(objectData.originalVoxels.length / 8);
            } else {
                voxelStep = 4; // Only every 4th voxel
                maxDebrisPieces = Math.ceil(objectData.originalVoxels.length / 4);
            }

            let debrisCount = 0;
            objectData.originalVoxels.forEach((voxel, index) => {
                if (index % voxelStep !== 0 || debrisCount >= maxDebrisPieces) {
                    return;
                }
                debrisCount++;

                const voxelWorldPos = this._toVector3(voxel.worldPosition);
                
                // Calculate directional velocity
                let direction;
                if (pointOfImpact) {
                    direction = voxelWorldPos.clone().sub(this._toVector3(pointOfImpact)).normalize();
                } else {
                    direction = new Vector3(
                        (Math.random() - 0.5) * 2,
                        0,
                        (Math.random() - 0.5) * 2
                    ).normalize();
                }

                const velocity = direction.multiplyScalar(explosionForce * (0.7 + Math.random() * 0.6));
                
                // Add projectile momentum
                if (projectileVelocity) {
                    const momentum = this._toVector3(projectileVelocity).multiplyScalar(0.2);
                    velocity.add(momentum);
                }
                
                velocity.y += upwardForce * (0.8 + Math.random() * 0.4);

                const angularVelocity = new Vector3(
                    (Math.random() - 0.5) * tumbleSpeed,
                    (Math.random() - 0.5) * tumbleSpeed,
                    (Math.random() - 0.5) * tumbleSpeed
                );

                const debrisId = `voxelDebris_${objectData.name}_${index}_${Date.now()}`;

                debris.push({
                    type: 'voxelDebris',
                    id: debrisId,
                    position: voxelWorldPos,
                    velocity: velocity,
                    angularVelocity: angularVelocity,
                    material: voxel.material,
                    geometryType: 'voxelDebris',
                    voxelScale: debrisVoxelScale,
                    userData: {
                        isDebrisPiece: true,
                        debrisId: debrisId,
                        isFalling: true,
                        persistUntilRoomChange: true,
                        needsStorage: true,
                        velocity: velocity,
                        angularVelocity: angularVelocity,
                        creationTime: Date.now()
                    }
                });
            });
        } else {
            // Fallback destruction
            console.log(`[DestructionWorker] Using fallback destruction for ${objectData.name}`);
            
            const fallbackDebrisPerGroup = Math.min(destructionParams.fallbackDebrisPerGroup, 2);
            const fallbackExplosionForce = destructionParams.fallbackExplosionForce;
            const fallbackUpwardForce = destructionParams.fallbackUpwardForce;
            const fallbackTumbleSpeed = destructionParams.fallbackTumbleSpeed;

            for (let i = 0; i < fallbackDebrisPerGroup; i++) {
                const direction = new Vector3(
                    (Math.random() - 0.5) * 2,
                    0,
                    (Math.random() - 0.5) * 2
                ).normalize();

                const velocity = direction.multiplyScalar(fallbackExplosionForce * (0.8 + Math.random() * 0.4));
                velocity.y += fallbackUpwardForce * (0.7 + Math.random() * 0.6);

                const angularVelocity = new Vector3(
                    (Math.random() - 0.5) * fallbackTumbleSpeed,
                    (Math.random() - 0.5) * fallbackTumbleSpeed,
                    (Math.random() - 0.5) * fallbackTumbleSpeed
                );

                debris.push({
                    type: 'fallbackDebris',
                    id: `fallbackDebris_${objectData.name}_${i}_${Date.now()}`,
                    position: this._toVector3(objectData.position),
                    velocity: velocity,
                    angularVelocity: angularVelocity,
                    material: objectData.material,
                    geometryType: 'standardDebris',
                    userData: {
                        isDebrisPiece: true,
                        isFalling: true,
                        persistUntilRoomChange: true,
                        velocity: velocity,
                        angularVelocity: angularVelocity
                    }
                });
            }
        }

        return {
            type: 'objectDestruction',
            debris: debris,
            objectId: objectData.id
        };
    }
}

// Initialize worker
const destructionWorker = new DestructionWorker();

// Handle messages from main thread
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        let result;
        
        switch (type) {
            case 'processEnemyDestruction':
                result = destructionWorker.processEnemyDestruction(data);
                break;
            case 'processObjectDestruction':
                result = destructionWorker.processObjectDestruction(data);
                break;
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
        
        // Send result back to main thread
        self.postMessage({
            requestId: requestId,
            result: result,
            success: true
        });
        
    } catch (error) {
        console.error('[DestructionWorker] Error processing task:', error);
        
        // Send error back to main thread
        self.postMessage({
            requestId: requestId,
            error: error.message,
            success: false
        });
    }
};

console.log('[DestructionWorker] Worker script loaded and ready');