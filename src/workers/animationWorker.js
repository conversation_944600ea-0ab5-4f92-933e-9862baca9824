// src/workers/animationWorker.js
// High-performance animation calculation worker

/**
 * Lightweight Vector3 implementation for worker context
 */
class Vector3 {
    constructor(x = 0, y = 0, z = 0) {
        this.x = x;
        this.y = y;
        this.z = z;
    }
    
    clone() {
        return new Vector3(this.x, this.y, this.z);
    }
    
    copy(v) {
        this.x = v.x;
        this.y = v.y;
        this.z = v.z;
        return this;
    }
    
    add(v) {
        this.x += v.x;
        this.y += v.y;
        this.z += v.z;
        return this;
    }
    
    multiplyScalar(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        this.z *= scalar;
        return this;
    }
    
    lerp(v, alpha) {
        this.x += (v.x - this.x) * alpha;
        this.y += (v.y - this.y) * alpha;
        this.z += (v.z - this.z) * alpha;
        return this;
    }
}

/**
 * High-performance animation worker
 */
class AnimationWorker {
    constructor() {
        this.performanceMetrics = {
            totalRequests: 0,
            totalTime: 0,
            averageTime: 0,
            animationsProcessed: 0
        };
        
        // Animation state cache
        this.animationStates = new Map();
        this.maxCacheSize = 200;
        
        console.log('[AnimationWorker] Initialized');
    }

    /**
     * Calculate batch animation updates
     * @param {Object} data - Animation batch request
     * @returns {Object} Animation updates
     */
    calculateAnimationBatch(data) {
        const { 
            animations, 
            deltaTime, 
            globalTime, 
            optimizeForLowEnd = false 
        } = data;
        
        if (!animations || !Array.isArray(animations)) {
            throw new Error('Invalid animation data provided');
        }
        
        const startTime = performance.now();
        const results = [];
        
        // Process animations in batches for better performance
        const batchSize = optimizeForLowEnd ? 10 : 50;
        
        for (let i = 0; i < animations.length; i += batchSize) {
            const batch = animations.slice(i, i + batchSize);
            const batchResults = this._processBatch(batch, deltaTime, globalTime, optimizeForLowEnd);
            results.push(...batchResults);
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, animations.length);
        
        return {
            animationUpdates: results,
            metadata: {
                processingTime,
                animationCount: animations.length,
                batchCount: Math.ceil(animations.length / batchSize)
            }
        };
    }

    /**
     * Calculate particle system updates
     * @param {Object} data - Particle system request
     * @returns {Object} Particle updates
     */
    calculateParticleUpdates(data) {
        const { 
            particles, 
            deltaTime, 
            forces = [], 
            optimizeForLowEnd = false 
        } = data;
        
        if (!particles || !Array.isArray(particles)) {
            throw new Error('Invalid particle data provided');
        }
        
        const startTime = performance.now();
        const updates = [];
        
        // Skip some particles on low-end devices
        const skipFactor = optimizeForLowEnd ? 2 : 1;
        
        for (let i = 0; i < particles.length; i += skipFactor) {
            const particle = particles[i];
            const update = this._calculateParticleUpdate(particle, deltaTime, forces);
            
            if (update) {
                updates.push({
                    index: i,
                    ...update
                });
            }
        }
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, particles.length);
        
        return {
            particleUpdates: updates,
            metadata: {
                processingTime,
                particleCount: particles.length,
                updatedCount: updates.length
            }
        };
    }

    /**
     * Calculate smooth interpolation between animation states
     * @param {Object} data - Interpolation request
     * @returns {Object} Interpolated values
     */
    calculateInterpolation(data) {
        const { 
            fromState, 
            toState, 
            progress, 
            interpolationType = 'linear',
            properties = ['position', 'rotation', 'scale']
        } = data;
        
        const startTime = performance.now();
        const interpolated = {};
        
        properties.forEach(property => {
            if (fromState[property] && toState[property]) {
                interpolated[property] = this._interpolateProperty(
                    fromState[property],
                    toState[property],
                    progress,
                    interpolationType
                );
            }
        });
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, 1);
        
        return {
            interpolatedState: interpolated,
            metadata: {
                processingTime,
                interpolationType,
                properties: properties.length
            }
        };
    }

    /**
     * Calculate complex animation curves
     * @param {Object} data - Animation curve request
     * @returns {Object} Curve values
     */
    calculateAnimationCurves(data) {
        const { 
            curves, 
            time, 
            duration, 
            optimizeForLowEnd = false 
        } = data;
        
        if (!curves || !Array.isArray(curves)) {
            throw new Error('Invalid curve data provided');
        }
        
        const startTime = performance.now();
        const results = {};
        
        curves.forEach(curve => {
            const { name, type, keyframes, easing = 'linear' } = curve;
            
            if (optimizeForLowEnd && Math.random() > 0.7) {
                // Skip some curve calculations on low-end devices
                return;
            }
            
            results[name] = this._evaluateCurve(keyframes, time, duration, type, easing);
        });
        
        const processingTime = performance.now() - startTime;
        this._updateMetrics(processingTime, curves.length);
        
        return {
            curveValues: results,
            metadata: {
                processingTime,
                curveCount: curves.length,
                evaluatedCount: Object.keys(results).length
            }
        };
    }

    /**
     * Process animation batch
     * @private
     */
    _processBatch(animations, deltaTime, globalTime, optimizeForLowEnd) {
        const results = [];
        
        animations.forEach((animation, index) => {
            try {
                const update = this._calculateSingleAnimation(
                    animation, 
                    deltaTime, 
                    globalTime, 
                    optimizeForLowEnd
                );
                
                if (update) {
                    results.push({
                        animationId: animation.id || index,
                        ...update
                    });
                }
            } catch (error) {
                console.warn(`[AnimationWorker] Error processing animation ${index}:`, error);
            }
        });
        
        return results;
    }

    /**
     * Calculate single animation update
     * @private
     */
    _calculateSingleAnimation(animation, deltaTime, globalTime, optimizeForLowEnd) {
        const { type, properties, duration, loop = true, startTime = 0 } = animation;
        
        // Calculate animation progress
        const elapsed = globalTime - startTime;
        let progress = duration > 0 ? (elapsed % duration) / duration : 0;
        
        if (!loop && elapsed > duration) {
            progress = 1;
        }
        
        const update = {};
        
        switch (type) {
            case 'position':
                update.position = this._calculatePositionAnimation(properties, progress, deltaTime);
                break;
            case 'rotation':
                update.rotation = this._calculateRotationAnimation(properties, progress, deltaTime);
                break;
            case 'scale':
                update.scale = this._calculateScaleAnimation(properties, progress, deltaTime);
                break;
            case 'complex':
                Object.assign(update, this._calculateComplexAnimation(properties, progress, deltaTime, optimizeForLowEnd));
                break;
            default:
                return null;
        }
        
        return update;
    }

    /**
     * Calculate position animation
     * @private
     */
    _calculatePositionAnimation(properties, progress, deltaTime) {
        const { from, to, easing = 'linear', amplitude = 1, frequency = 1 } = properties;
        
        let easedProgress = this._applyEasing(progress, easing);
        
        // Add wave motion if specified
        if (properties.wave) {
            const wave = Math.sin(progress * Math.PI * 2 * frequency) * amplitude;
            easedProgress += wave * 0.1;
        }
        
        return {
            x: this._lerp(from.x, to.x, easedProgress),
            y: this._lerp(from.y, to.y, easedProgress),
            z: this._lerp(from.z, to.z, easedProgress)
        };
    }

    /**
     * Calculate rotation animation
     * @private
     */
    _calculateRotationAnimation(properties, progress, deltaTime) {
        const { from, to, easing = 'linear', speed = 1 } = properties;
        
        const easedProgress = this._applyEasing(progress, easing);
        
        return {
            x: this._lerpAngle(from.x, to.x, easedProgress) * speed,
            y: this._lerpAngle(from.y, to.y, easedProgress) * speed,
            z: this._lerpAngle(from.z, to.z, easedProgress) * speed
        };
    }

    /**
     * Calculate scale animation
     * @private
     */
    _calculateScaleAnimation(properties, progress, deltaTime) {
        const { from, to, easing = 'linear', pulse = false } = properties;
        
        let easedProgress = this._applyEasing(progress, easing);
        
        // Add pulse effect if specified
        if (pulse) {
            const pulseEffect = Math.sin(progress * Math.PI * 4) * 0.1 + 1;
            easedProgress *= pulseEffect;
        }
        
        return {
            x: this._lerp(from.x || 1, to.x || 1, easedProgress),
            y: this._lerp(from.y || 1, to.y || 1, easedProgress),
            z: this._lerp(from.z || 1, to.z || 1, easedProgress)
        };
    }

    /**
     * Calculate complex multi-property animation
     * @private
     */
    _calculateComplexAnimation(properties, progress, deltaTime, optimizeForLowEnd) {
        const update = {};
        
        // Process each property type
        Object.keys(properties).forEach(propertyType => {
            if (optimizeForLowEnd && Math.random() > 0.8) {
                // Skip some properties on low-end devices
                return;
            }
            
            const propertyData = properties[propertyType];
            
            switch (propertyType) {
                case 'position':
                    update.position = this._calculatePositionAnimation(propertyData, progress, deltaTime);
                    break;
                case 'rotation':
                    update.rotation = this._calculateRotationAnimation(propertyData, progress, deltaTime);
                    break;
                case 'scale':
                    update.scale = this._calculateScaleAnimation(propertyData, progress, deltaTime);
                    break;
                case 'opacity':
                    update.opacity = this._lerp(propertyData.from, propertyData.to, progress);
                    break;
                case 'color':
                    update.color = this._lerpColor(propertyData.from, propertyData.to, progress);
                    break;
            }
        });
        
        return update;
    }

    /**
     * Calculate particle update
     * @private
     */
    _calculateParticleUpdate(particle, deltaTime, forces) {
        const { position, velocity, acceleration, life, maxLife } = particle;
        
        if (life <= 0) return null;
        
        const newPosition = new Vector3(position.x, position.y, position.z);
        const newVelocity = new Vector3(velocity.x, velocity.y, velocity.z);
        
        // Apply forces
        forces.forEach(force => {
            switch (force.type) {
                case 'gravity':
                    newVelocity.y += force.strength * deltaTime;
                    break;
                case 'wind':
                    newVelocity.x += force.strength * deltaTime;
                    break;
                case 'drag':
                    newVelocity.multiplyScalar(1 - force.strength * deltaTime);
                    break;
            }
        });
        
        // Apply acceleration
        if (acceleration) {
            newVelocity.add(new Vector3(acceleration.x, acceleration.y, acceleration.z).multiplyScalar(deltaTime));
        }
        
        // Update position
        newPosition.add(new Vector3(newVelocity.x, newVelocity.y, newVelocity.z).multiplyScalar(deltaTime));
        
        // Update life
        const newLife = Math.max(0, life - deltaTime);
        const lifeRatio = newLife / maxLife;
        
        return {
            position: newPosition,
            velocity: newVelocity,
            life: newLife,
            opacity: lifeRatio,
            scale: 0.5 + lifeRatio * 0.5
        };
    }

    /**
     * Interpolate between two properties
     * @private
     */
    _interpolateProperty(from, to, progress, type) {
        switch (type) {
            case 'linear':
                return this._lerpVector(from, to, progress);
            case 'easeIn':
                return this._lerpVector(from, to, progress * progress);
            case 'easeOut':
                return this._lerpVector(from, to, 1 - (1 - progress) * (1 - progress));
            case 'easeInOut':
                const easedProgress = progress < 0.5 
                    ? 2 * progress * progress 
                    : 1 - 2 * (1 - progress) * (1 - progress);
                return this._lerpVector(from, to, easedProgress);
            default:
                return this._lerpVector(from, to, progress);
        }
    }

    /**
     * Evaluate animation curve
     * @private
     */
    _evaluateCurve(keyframes, time, duration, type, easing) {
        if (!keyframes || keyframes.length === 0) return 0;
        
        const normalizedTime = (time % duration) / duration;
        
        // Find surrounding keyframes
        let prevFrame = keyframes[0];
        let nextFrame = keyframes[keyframes.length - 1];
        
        for (let i = 0; i < keyframes.length - 1; i++) {
            if (normalizedTime >= keyframes[i].time && normalizedTime <= keyframes[i + 1].time) {
                prevFrame = keyframes[i];
                nextFrame = keyframes[i + 1];
                break;
            }
        }
        
        // Calculate local progress between keyframes
        const frameProgress = (normalizedTime - prevFrame.time) / (nextFrame.time - prevFrame.time);
        const easedProgress = this._applyEasing(frameProgress, easing);
        
        return this._lerp(prevFrame.value, nextFrame.value, easedProgress);
    }

    /**
     * Apply easing function
     * @private
     */
    _applyEasing(progress, easing) {
        switch (easing) {
            case 'easeIn':
                return progress * progress;
            case 'easeOut':
                return 1 - (1 - progress) * (1 - progress);
            case 'easeInOut':
                return progress < 0.5 
                    ? 2 * progress * progress 
                    : 1 - 2 * (1 - progress) * (1 - progress);
            case 'bounce':
                return this._bounceEasing(progress);
            default:
                return progress;
        }
    }

    /**
     * Bounce easing function
     * @private
     */
    _bounceEasing(t) {
        if (t < 1 / 2.75) {
            return 7.5625 * t * t;
        } else if (t < 2 / 2.75) {
            return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
        } else if (t < 2.5 / 2.75) {
            return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
        } else {
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        }
    }

    /**
     * Linear interpolation
     * @private
     */
    _lerp(a, b, t) {
        return a + (b - a) * t;
    }

    /**
     * Linear interpolation for angles
     * @private
     */
    _lerpAngle(a, b, t) {
        const diff = b - a;
        const wrappedDiff = ((diff + Math.PI) % (2 * Math.PI)) - Math.PI;
        return a + wrappedDiff * t;
    }

    /**
     * Linear interpolation for vectors
     * @private
     */
    _lerpVector(a, b, t) {
        return {
            x: this._lerp(a.x, b.x, t),
            y: this._lerp(a.y, b.y, t),
            z: this._lerp(a.z, b.z, t)
        };
    }

    /**
     * Linear interpolation for colors
     * @private
     */
    _lerpColor(a, b, t) {
        return {
            r: this._lerp(a.r, b.r, t),
            g: this._lerp(a.g, b.g, t),
            b: this._lerp(a.b, b.b, t)
        };
    }

    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(processingTime, animationCount) {
        this.performanceMetrics.totalRequests++;
        this.performanceMetrics.totalTime += processingTime;
        this.performanceMetrics.averageTime = 
            this.performanceMetrics.totalTime / this.performanceMetrics.totalRequests;
        this.performanceMetrics.animationsProcessed += animationCount;
    }

    /**
     * Get performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.animationStates.size,
            timestamp: Date.now()
        };
    }

    /**
     * Clear animation cache
     */
    clearCache() {
        this.animationStates.clear();
        console.log('[AnimationWorker] Cache cleared');
    }
}

// Initialize worker
const animationWorker = new AnimationWorker();

// Handle messages from main thread
self.onmessage = function(e) {
    const { type, data, requestId } = e.data;
    
    try {
        let result;
        
        switch (type) {
            case 'calculateAnimationBatch':
                result = animationWorker.calculateAnimationBatch(data);
                break;
            case 'calculateParticleUpdates':
                result = animationWorker.calculateParticleUpdates(data);
                break;
            case 'calculateInterpolation':
                result = animationWorker.calculateInterpolation(data);
                break;
            case 'calculateAnimationCurves':
                result = animationWorker.calculateAnimationCurves(data);
                break;
            case 'getPerformanceMetrics':
                result = animationWorker.getPerformanceMetrics();
                break;
            case 'clearCache':
                animationWorker.clearCache();
                result = { success: true };
                break;
            default:
                throw new Error(`Unknown task type: ${type}`);
        }
        
        // Send result back to main thread
        self.postMessage({
            requestId: requestId,
            result: result,
            success: true
        });
        
    } catch (error) {
        console.error('[AnimationWorker] Error processing task:', error);
        
        // Send error back to main thread
        self.postMessage({
            requestId: requestId,
            error: error.message,
            success: false
        });
    }
};
