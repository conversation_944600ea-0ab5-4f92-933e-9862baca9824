import * as THREE from 'three';

const PROJECTILE_GRAVITY = -4.9; // Should match PlayerController

class Projectile {
    constructor(scene, startPosition, velocity, damage, range, size, isEnemyProjectile = false) {
        this.scene = scene;
        this.damage = damage;
        this.range = range;
        this.isEnemyProjectile = isEnemyProjectile; // Flag to distinguish

        const geometry = new THREE.SphereGeometry(size, 8, 8); // Simple sphere
        const material = new THREE.MeshBasicMaterial({
            color: isEnemyProjectile ? 0xff0000 : 0x00ffff // Red for enemy, cyan for player
        });
        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(startPosition);
        // this.scene.add(this.mesh); // <<< REMOVED - DungeonHandler now adds projectile mesh

        this.velocity = velocity;
        const speed = this.velocity.length();
        this.lifeTime = 0;
        this.maxLifeTime = speed > 0.01 ? range / speed : range; // Avoid division by zero

        // <<< ADD MISSING INITIALIZATIONS >>>
        this.verticalVelocity = 0;
        this.distanceTraveled = 0;
        this.owner = isEnemyProjectile ? 'enemy' : 'player'; // Store owner type
        // <<< END MISSING INITIALIZATIONS >>>

        // Bounding box for simpler collision
        this.boundingBox = new THREE.Box3().setFromObject(this.mesh);
    }

    update(deltaTime, collisionObjects, playerMesh, activeEnemies) {
        this.lifeTime += deltaTime;

        // Apply gravity
        this.velocity.y += PROJECTILE_GRAVITY * deltaTime;

        // Update position
        this.mesh.position.addScaledVector(this.velocity, deltaTime);

        // Update bounding box
        this.boundingBox.setFromObject(this.mesh);

        // Check lifetime
        if (this.lifeTime >= this.maxLifeTime) {
            this.destroy();
            return true; // Indicate projectile should be removed
        }

        // --- Simplified Collision Check ---
        if (this._checkCollision(collisionObjects, playerMesh, activeEnemies)) {
            this.destroy();
            return true;
        }
        // --- End Simplified Check ---

        return false; // Projectile still active
    }

    _checkCollision(collisionObjects, playerMesh, activeEnemies) {
        // Check against static collision objects (walls, etc.)
        for (const obj of collisionObjects) {
            const objectBox = new THREE.Box3().setFromObject(obj);
            if (this.boundingBox.intersectsBox(objectBox)) {
                return true; // Hit static wall
            }
        }

        if (this.isEnemyProjectile) {
            // Enemy projectile checking against Player
            if (playerMesh) {
                const playerBox = new THREE.Box3().setFromObject(playerMesh);
                if (this.boundingBox.intersectsBox(playerBox)) {
                    return true;
                }
            }
        } else {
            // Player projectile checking against Enemies
            for (const enemy of activeEnemies) {
                // FIXED: Handle both old system (enemy.mesh) and new system (enemy is the mesh/group)
                const enemyObject = enemy.mesh || enemy;

                // Create bounding box for enemy
                const enemyBox = new THREE.Box3().setFromObject(enemyObject);

                if (this.boundingBox.intersectsBox(enemyBox)) {
                    console.log("Hit enemy:", enemy); // Debug log

                    // Apply damage - ALWAYS use takeDamage method for proper flash effects
                    const enemyUserData = enemyObject.userData;
                    if (enemyUserData && typeof enemyUserData.takeDamage === 'function') {
                        // PRIORITY: Use the proper damage handler for flash effects and other systems
                        console.log("Found takeDamage method in userData, calling it..."); // Debug log
                        enemyUserData.takeDamage(this.damage);

                        // Apply immediate AI brain knockback for smooth animation (only if enemy survives)
                        // 20% chance for knockback per shot
                        if (enemyUserData.health > 0 && enemyUserData.aiBrain && Math.random() < 0.2) {
                            console.log("Found AI brain, applying immediate knockback..."); // Debug log
                            const knockbackDirection = enemyObject.position.clone().sub(this.mesh.position).normalize();
                            const knockbackStrength = 20.0; // Use same strength as DungeonHandler
                            enemyUserData.aiBrain.applyKnockback(knockbackDirection, knockbackStrength);
                        }
                    } else if (enemyUserData && typeof enemyUserData.health === 'number') {
                        // FALLBACK: Direct health modification if no takeDamage method
                        enemyUserData.health -= this.damage;

                        // Apply immediate AI brain knockback for smooth animation (only if enemy survives)
                        // 20% chance for knockback per shot
                        if (enemyUserData.health > 0 && enemyUserData.aiBrain && Math.random() < 0.2) {
                            console.log("Found AI brain, applying immediate knockback..."); // Debug log
                            const knockbackDirection = enemyObject.position.clone().sub(this.mesh.position).normalize();
                            const knockbackStrength = 20.0; // Use same strength as DungeonHandler
                            enemyUserData.aiBrain.applyKnockback(knockbackDirection, knockbackStrength);
                        }

                        // Check if enemy is defeated - let DungeonHandler handle death properly
                        if (enemyUserData.health <= 0) {
                            enemyUserData.health = 0; // Ensure health doesn't go negative
                            // Mark enemy for death processing in next DungeonHandler update
                            enemyUserData.markedForDeath = true;
                        }
                    } else {
                        console.error("Enemy object is missing health or userData!", enemy); // Debug log
                    }
                    return true;
                }
            }
        }

        return false; // No collision detected
    }

    destroy() {
        if (this.mesh) {
            this.scene.remove(this.mesh);
            this.mesh.geometry.dispose();
            this.mesh.material.dispose();
            this.mesh = null; // Release reference
        }
    }
}

export default Projectile;