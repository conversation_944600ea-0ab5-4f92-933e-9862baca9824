import * as THREE from 'three';
import { createSkeletonEnemyModel } from '../generators/prefabs/skeletonEnemy.js';
import { createBatEnemyModel } from '../generators/prefabs/batEnemy.js';
import { createZombieEnemyModel } from '../generators/prefabs/zombieEnemy.js';
import { createMagmaGolemEnemyModel } from '../generators/prefabs/magmaGolemEnemy.js';
import { createCatacombOverlordModel } from '../generators/prefabs/catacombOverlordBoss.js';
import { createFishEnemyModel } from '../generators/prefabs/fishEnemy.js';
import { createNairabosEnemyModel } from '../generators/prefabs/nairabosEnemy.js';
import { createFireflyEnemyModel } from '../generators/prefabs/fireflyEnemy.js';
import { shadowMinionObject } from '../generators/prefabs/shadowMinionObject.js';
import { createChronarchEnemyBossModel } from '../generators/prefabs/chronarchEnemyBoss.js';
import { createTombGuardianEnemyModel } from '../generators/prefabs/tombGuardianEnemy.js';
import { createVoidLordEnemyModel } from '../generators/prefabs/voidLordEnemy.js';


// --- AI Brain Types ---
export const AI_BRAIN_TYPES = {
    RANGED: 'ranged',
    MELEE: 'melee',
    MIRROR: 'mirror',
    ASSASSIN: 'assassin',
    FLEEING: 'fleeing',
    FLYING: 'flying',
    BOSS: 'boss'
};

// --- Enemy Type Definitions ---
export const ENEMY_TYPES = {
    SKELETON: 'skeleton',
    SKELETON_ARCHER: 'skeleton_archer',
    SKELETON_WARRIOR: 'skeleton_warrior',
    SKELETON_ASSASSIN: 'skeleton_assassin',
    BAT: 'bat',
    GHOST: 'ghost',
    SKELETON_BOSS: 'skeleton_boss',
    ZOMBIE: 'zombie',
    MAGMA_GOLEM: 'magma_golem',
    CATACOMBS_OVERLORD: 'catacombs_overlord',
    FISH: 'fish',
    NAIRABOS: 'nairabos',
    FIREFLY: 'firefly',
    SHADOW_MINION: 'shadow_minion',
    CHRONARCH: 'chronarch',
    CRYSTAL_TOWER: 'crystal_tower',
    TRANSMUTATION_REACTOR: 'transmutation_reactor',
    TOMB_GUARDIAN: 'tomb_guardian',
    VOID_LORD: 'void_lord'
};

const ENEMY_TYPES_DATA = {
    // --- Skeleton Archer (Enhanced Ranged Combat AI) ---
    skeleton_archer: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 30,
        baseSpeed: 2.5, // Increased from 1.5 for faster movement
        speedVariation: 0.3, // Increased from 0.2 for more varied movement
        mass: 1.0, // For knockback calculations
        // AI Parameters
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 3, // Increased to 3 for enhanced AI capabilities
        attackCooldown: 2.4, // Doubled from 1.2 to make skeletons shoot half as frequently
        preferredRange: 12.0, // Increased by 1.5x from 8.0 for longer shooting distance
        moveAwayRange: 4.5, // Increased by 1.5x from 3.0 for longer retreat distance
        maxRange: 45.0, // Increased by 1.5x from 30.0 for longer detection range
        // Projectile Info
        projectileType: 'arrow',
        projectileDamage: 1, // Reduced from 5 to 1
        projectileSpeed: 7.0, // Reduced from 15.0 to make arrows slower and more dodgeable
        projectileRange: 45.0, // Increased by 1.5x from 30.0 to match detection range
        projectileSize: 0.05,
        // Dodge Parameters
        canDodge: true,
        dodgeChance: 0.2,
        dodgeCooldown: 3.0,
        dodgeDuration: 0.5,
        dodgeTriggerDistance: 5.0
    },

    // --- Skeleton Warrior (Melee Combat AI) ---
    skeleton_warrior: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 45,
        baseSpeed: 2.8, // Increased from 1.8 for faster movement
        speedVariation: 0.25, // Increased from 0.15 for more varied movement
        mass: 1.2, // Heavier than archer
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.0, // Reduced from 1.5 for faster attacks
        preferredRange: 2.0,
        attackRange: 2.5,
        chargeRange: 6.0,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 8 to 1
        // Block Parameters
        canBlock: true,
        blockChance: 0.3,
        blockCooldown: 2.0,
        blockDuration: 1.0
    },

    // --- Skeleton Assassin (Assassin Combat AI) ---
    skeleton_assassin: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 35,
        baseSpeed: 3.2, // Increased from 2.0 for faster movement
        speedVariation: 0.3, // Increased from 0.1 for more varied movement
        mass: 0.9, // Lighter than warrior
        // AI Parameters
        aiType: AI_BRAIN_TYPES.ASSASSIN,
        difficulty: 1, // Default difficulty (1-5)
        attackCooldown: 1.0, // Reduced from 1.8 for faster attacks
        preferredRange: 2.0,
        attackRange: 2.5,
        stalkRange: 10.0,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 6 to 1
        backstabMultiplier: 1.0, // Reduced from 2.0 to 1.0 (no multiplier)
        // Stealth Parameters
        stealthLevel: 0.7
    },

    // --- Bat (Enhanced Flying Combat AI) ---
    bat: {
        // Visuals
        modelPrefab: createBatEnemyModel,
        size: 0.5,
        // Base Stats
        health: 20,
        baseSpeed: 6.0, // Extremely fast base speed
        speedVariation: 0.1, // Less variation for more consistent speed
        mass: 0.5, // Very light
        // AI Parameters
        aiType: AI_BRAIN_TYPES.FLYING,
        difficulty: 3, // Increased to 3 for enhanced AI capabilities
        attackCooldown: 0.8, // Extremely short cooldown for very frequent attacks
        preferredRange: 1.5, // Extremely close preferred range
        attackRange: 3.5, // REDUCED: Back to reasonable range for bats
        // Flying Parameters
        minHoverHeight: 1.2, // Very low minimum height
        maxHoverHeight: 2.5, // Lower maximum height to stay closer to player
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 4 to 1
        // Swooping Parameters
        swoopChance: 0.9, // Almost always swoop when possible
        swoopCooldown: 1.5, // Very short cooldown between swoops
        // Animation Parameters
        animationStates: {
            idle: 'flying',
            moving: 'flying',
            attacking: 'biting',
            swooping: 'swooping'
        }
    },

    // --- Shadow Mage (Ranged Combat AI) ---
    shadow_mage: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel, // Use skeleton model for now
        size: 0.9, // Slightly larger than skeleton archer
        // Base Stats
        health: 40, // More health than skeleton archer
        baseSpeed: 2.0, // Slower than skeleton archer
        speedVariation: 0.2,
        mass: 1.2, // Heavier than skeleton archer
        // AI Parameters
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 3, // Same as skeleton archer
        attackCooldown: 2.0, // Slightly faster than skeleton archer
        preferredRange: 10.0, // Shorter range than skeleton archer
        moveAwayRange: 4.0, // Similar to skeleton archer
        maxRange: 35.0, // Shorter range than skeleton archer
        // Projectile Info
        projectileType: 'shadow_bolt',
        projectileDamage: 1, // Same as other enemies
        projectileSpeed: 8.0, // Same as boss shadow bolts
        projectileRange: 22.5, // Same as updated skeleton boss range
        projectileSize: 0.25,
        // Dodge Parameters
        canDodge: true,
        dodgeChance: 0.15, // Lower than skeleton archer
        dodgeCooldown: 3.5,
        dodgeDuration: 0.4,
        dodgeTriggerDistance: 4.5
    },

    // --- Ghost (Fleeing Combat AI) ---
    ghost: {
        // Visuals (placeholder, need to create ghost model)
        modelPrefab: null, // TODO: Create ghost model
        size: 0.7,
        // Base Stats
        health: 15,
        baseSpeed: 1.7,
        speedVariation: 0.25,
        mass: 0.3, // Almost no mass
        // AI Parameters
        aiType: AI_BRAIN_TYPES.FLEEING,
        difficulty: 1, // Default difficulty (1-5)
        fleeThreshold: 8.0,
        panicThreshold: 5.0,
        safeDistance: 12.0
    },

    // --- Skeleton Boss (Boss Combat AI) ---
    skeleton_boss: {
        // Visuals (placeholder, need to create boss model)
        modelPrefab: createSkeletonEnemyModel, // TODO: Create boss model
        size: 1.5, // Larger than normal skeleton
        // Base Stats
        health: 150,
        baseSpeed: 1.6,
        speedVariation: 0.1,
        mass: 2.0, // Heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.BOSS,
        difficulty: 3, // Higher default difficulty
        attackCooldown: 1.5,
        // Projectile Info
        projectileType: 'shadow_bolt',
        projectileDamage: 1, // Reduced from 10 to 1
        projectileSpeed: 8.0,
        projectileRange: 22.5, // Increased by 1.5x from 15.0 for longer range
        projectileSize: 0.3,
        // Melee Attack Info
        meleeDamage: 1, // Reduced from 12 to 1
        // Special Attack Info
        specialAttackCooldown: 15.0,
        specialAttackDamage: 1 // Reduced from 20 to 1
    },

    // --- Catacombs Overlord (Music-Reactive Boss) ---
    catacombs_overlord: {
        // Visuals
        modelPrefab: createCatacombOverlordModel, // Using custom overlord model
        size: 8.0, // Reduced from 12.5 to make the boss a bit smaller
        // Base Stats
        health: 300, // Increased health for stronger boss
        baseSpeed: 1.8,
        speedVariation: 0.1,
        mass: 3.0, // Extremely heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.BOSS,
        difficulty: 5, // Maximum difficulty
        attackCooldown: 1.2,
        // Music-reactive flag
        musicReactive: true,
        // Projectile Info
        projectileType: 'shadow_bolt', // Default projectile type
        projectileDamage: 1,
        projectileSpeed: 8.0,
        projectileRange: 30.0, // Increased by 1.5x from 20.0 for longer range
        projectileSize: 0.3,
        // Melee Attack Info
        meleeDamage: 1,
        // Special Attack Info
        specialAttackCooldown: 10.0, // Shorter cooldown for more frequent special attacks
        specialAttackDamage: 1
    },

    // --- Legacy skeleton type for backward compatibility ---
    skeleton: {
        // Visuals
        modelPrefab: createSkeletonEnemyModel,
        size: 0.8,
        // Base Stats
        health: 30,
        baseSpeed: 1.5,
        speedVariation: 0.2,
        mass: 1.0,
        // AI Parameters (maps to skeleton_archer)
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 1,
        attackCooldown: 2.4, // Doubled from 1.2 to make skeletons shoot half as frequently
        preferredRange: 12.0, // Increased by 1.5x from 8.0 for longer shooting distance
        moveAwayRange: 4.5, // Increased by 1.5x from 3.0 for longer retreat distance
        maxRange: 45.0, // Increased by 1.5x from 30.0 for longer detection range
        // Projectile Info
        projectileType: 'arrow',
        projectileDamage: 1, // Reduced from 5 to 1
        projectileSpeed: 7.0, // Reduced from 15.0 to make arrows slower and more dodgeable
        projectileRange: 45.0, // Increased by 1.5x from 30.0 to match detection range
        projectileSize: 0.05
    },

    // --- Zombie (Regular Melee Combat AI) ---
    zombie: {
        // Visuals
        modelPrefab: createZombieEnemyModel,
        size: 1.5, // Larger size for the new zombie model
        // Base Stats
        health: 60, // Increased health for more durability
        baseSpeed: 3.5, // INCREASED: Much faster for aggressive zombies
        speedVariation: 0.3, // More variation in speed
        mass: 1.8, // Much heavier than before
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 3, // Increased to 3 to ensure enhanced AI usage
        attackCooldown: 0.15, // REDUCED: Even faster attacks for very aggressive zombies
        preferredRange: 2.0, // Slightly increased for better positioning
        attackRange: 5.0, // INCREASED: More forgiving attack range (vs 3.5 hitbox)
        chargeRange: 8.0, // Longer charge range
        // Melee Attack Info
        meleeDamage: 1, // 1 damage as per user preference
        // Animation Parameters
        animationStates: {
            idle: 'idle',
            moving: 'walking',
            attacking: 'attacking'
        }
    },

    // --- Magma Golem (Regular Melee Combat AI) ---
    magma_golem: {
        // Visuals
        modelPrefab: createMagmaGolemEnemyModel,
        size: 2.5, // Much larger size to make it more visible
        // Base Stats
        health: 80, // High health for a tank-like enemy
        baseSpeed: 2.8, // INCREASED: Much faster for aggressive golems
        speedVariation: 0.2, // Some variation in speed
        mass: 2.5, // Very heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 4, // Increased to 4 for advanced enhanced AI
        attackCooldown: 0.8, // REDUCED: Much faster attacks (was 2.0)
        preferredRange: 2.5, // Slightly increased for better positioning
        attackRange: 5.5, // INCREASED: More forgiving attack range (vs 4.0 hitbox)
        chargeRange: 7.0, // Good charge range
        // Melee Attack Info
        meleeDamage: 1, // 1 damage as per user preference
        // Animation Parameters
        animationStates: {
            idle: 'idle',
            moving: 'walking',
            attacking: 'attacking'
        }
    },

    // --- Fish (Flying/Jumping Combat AI) ---
    fish: {
        // Visuals
        modelPrefab: createFishEnemyModel,
        size: 1.2, // Medium size fish
        // Base Stats
        health: 25,
        baseSpeed: 4.0, // Fast swimming/jumping speed
        speedVariation: 0.2,
        mass: 0.8, // Light but not as light as bat
        // AI Parameters
        aiType: AI_BRAIN_TYPES.FLYING, // Use flying AI for jumping behavior
        difficulty: 2,
        attackCooldown: 1.5,
        preferredRange: 2.0,
        attackRange: 3.0,
        // Flying/Jumping Parameters
        minHoverHeight: 0.5, // Can be on ground
        maxHoverHeight: 4.0, // Can jump high
        // Melee Attack Info
        meleeDamage: 1,
        // Jumping Parameters
        jumpChance: 0.8, // High chance to jump when attacking
        jumpCooldown: 2.0,
        jumpHeight: 4.0,
        jumpDuration: 1.0,
        // Animation Parameters
        animationStates: {
            idle: 'swimming',
            moving: 'swimming',
            attacking: 'biting',
            jumping: 'jumping'
        }
    },

    // --- Nairabos (Horrifying Shadow Creature) ---
    nairabos: {
        // Visuals
        modelPrefab: createNairabosEnemyModel,
        size: 3.0, // Twice the size of a zombie (1.5 * 2 = 3.0)
        collisionRadius: 0.8, // Reduced further to prevent getting stuck
        // Base Stats
        health: 400, // 10x increased health - was 40, now 400 for epic boss battle
        baseSpeed: 5.5, // Increased speed for more fluid movement
        speedVariation: 0.1, // Less variation for consistent movement
        mass: 0.5, // Lighter mass for better movement
        // AI Parameters - use BOSS AI for better movement control
        aiType: AI_BRAIN_TYPES.BOSS, // Changed from FLYING to BOSS for better movement
        difficulty: 3, // High difficulty for enhanced AI
        attackCooldown: 1.2, // Slightly faster than fish
        preferredRange: 4.0, // Increased preferred range
        attackRange: 5.0, // Larger attack range due to long limbs
        // Movement parameters
        avoidanceStrength: 2.0, // Strong avoidance to prevent getting stuck
        // Melee Attack Info
        meleeDamage: 1, // Same damage as other enemies
        // Jumping Parameters - more dramatic than fish
        jumpChance: 0.9, // Very high chance to jump (terrifying)
        jumpCooldown: 1.8,
        jumpHeight: 5.0, // Higher jumps than fish
        jumpDuration: 1.2, // Longer hang time for fear effect
        // Animation Parameters
        animationStates: {
            idle: 'shadow_floating',
            moving: 'shadow_gliding',
            attacking: 'shadow_striking',
            jumping: 'shadow_leaping'
        },
        // Special shadow creature properties
        isHorrifying: true,
        shadowCreature: true
    },

    // --- Chronarch (Temporal Alchemist Boss) ---
    chronarch: {
        // Visuals
        modelPrefab: createChronarchEnemyBossModel,
        size: 3.0, // Large boss size (same as Nairabos)
        collisionRadius: 0.8, // Reduced to prevent getting stuck (matches Nairabos)
        // Base Stats
        health: 750, // 5x increased health for epic boss battle (150 -> 750)
        baseSpeed: 5.5, // Fast, fluid movement (matches Nairabos)
        speedVariation: 0.1, // Consistent movement
        mass: 0.5, // Lighter mass for better movement (matches Nairabos)
        // AI Parameters - use BOSS AI for specialized behavior
        aiType: AI_BRAIN_TYPES.BOSS,
        // Movement parameters for proper wall avoidance and chase behavior
        avoidanceStrength: 2.0, // Strong avoidance to prevent getting stuck
        difficulty: 5, // Maximum difficulty for epic boss
        attackCooldown: 2.5, // Moderate attack speed
        preferredRange: 6.0, // Mid-range spellcaster
        attackRange: 8.0, // Long range magical attacks
        maxRange: 12.0, // Detection range
        // Combat Parameters
        meleeDamage: 25, // High melee damage if player gets close
        // Projectile Parameters
        projectileType: 'alchemical_symbols',
        projectileSpeed: 12.0,
        projectileDamage: 25,
        projectileRange: 10.0,
        projectileCount: 1,
        // Special Attack Parameters
        specialAttackCooldown: 8.0,
        specialAttackDamage: 40,
        // Animation Parameters
        animationStates: {
            idle: 'mystical_floating',
            moving: 'hovering_approach',
            attacking: 'alchemical_casting',
            casting: 'temporal_manipulation'
        },
        // Boss-specific properties
        isBoss: true,
        temporalManipulation: true,
        alchemicalMaster: true,
        hasShield: true,
        shieldCooldown: 20.0,
        shieldDuration: 4.0,
        // Phase system
        hasPhases: true,
        phaseCount: 3,
        phaseThresholds: [1.0, 0.6, 0.25], // 100%, 60%, 25% (epic environmental phases)
        // EPIC ENVIRONMENTAL ATTACK PATTERNS - Large-scale reality manipulation!
        attackPatterns: {
            // 🔮 PHASE 1: THE UNRAVELING (100% - 60% Health) - Environmental Control
            phase1: ['crystalline_eruption', 'shard_volley', 'temporal_device_assault', 'pillar_slam', 'crystal_barrier'],
            // 🔮 PHASE 2: BENDING TIME (60% - 25% Health) - Time Manipulation  
            phase2: ['time_dilation_field', 'echoes_of_the_past', 'chronostatic_prison', 'temporal_rift_pulse', 'reality_fracture'],
            // 🔮 PHASE 3: REALITY COLLAPSE (25% - 0% Health) - Ultimate Environmental Chaos
            phase3: ['rift_collapse_ultimate', 'platform_shard_volley', 'crystal_cascade', 'temporal_storm', 'final_transmutation']
        }
    },

    // --- Firefly (Small Fleeing Creature) ---
    firefly: {
        // Visuals
        modelPrefab: createFireflyEnemyModel,
        size: 0.3, // Very small size
        // Base Stats
        health: 5, // Very low health
        baseSpeed: 5.0, // Fast movement for fleeing
        speedVariation: 0.2,
        mass: 0.2, // Very light
        // AI Parameters - use fleeing AI
        aiType: AI_BRAIN_TYPES.FLEEING,
        difficulty: 1, // Simple AI
        attackCooldown: 3.0, // Rarely attacks
        preferredRange: 8.0, // Prefers to stay far from player
        attackRange: 1.5, // Very short attack range
        // Fleeing Parameters
        fleeThreshold: 6.0, // Start fleeing when player gets within 6 units
        panicThreshold: 3.0, // Panic when player gets within 3 units
        safeDistance: 10.0, // Consider safe when 10 units away
        // Melee Attack Info (minimal)
        meleeDamage: 1, // Minimal damage
        // Animation Parameters
        animationStates: {
            idle: 'hovering',
            moving: 'flying',
            attacking: 'quick_strike',
            fleeing: 'panicked_flying'
        },
        // Special firefly properties
        isGlowing: true,
        fleeFromPlayer: true
    },

    // --- Shadow Minion (Boss Summoned Creature) ---
    shadow_minion: {
        // Visuals
        modelPrefab: shadowMinionObject,
        size: 1.2, // Smaller than Nairabos but larger than regular enemies
        // Base Stats
        health: 25, // Low health for swarm tactics
        baseSpeed: 4.0, // Fast scuttling movement
        speedVariation: 0.3,
        mass: 0.8, // Light and agile
        // AI Parameters - use fleeing AI for skittish behavior
        aiType: AI_BRAIN_TYPES.FLEEING,
        difficulty: 2, // Simple but aggressive
        attackCooldown: 2.0, // Attack every 2 seconds
        preferredRange: 6.0, // Mid-range harassment
        attackRange: 8.0, // Long range projectile attacks
        // Fleeing Parameters (skittish when low health)
        fleeThreshold: 3.0, // Flee when player gets close
        panicThreshold: 1.5, // Panic at very close range
        safeDistance: 8.0, // Safe distance from player
        // Projectile Attack Info
        projectileType: 'shadow_bolt',
        projectileSpeed: 6.0,
        projectileDamage: 1,
        projectileRange: 12.0,
        projectileCount: 3, // Triple shot pattern
        // Melee Attack Info (backup)
        meleeDamage: 2, // Low melee damage
        // Animation Parameters
        animationStates: {
            idle: 'scuttling',
            moving: 'scuttling',
            attacking: 'attacking',
            fleeing: 'scuttling'
        },
        // Special shadow minion properties
        isBossMinion: true,
        emergingFromGround: true,
        shadowCreature: true,
        lifetime: 15000, // 15 seconds lifetime
        // Spawning behavior
        spawnEffect: 'ground_emergence',
        deathEffect: 'shadow_fade'
    },

    // --- Crystal Tower (Chronarch Phase 2 Summon) ---
    crystal_tower: {
        // Visuals (will use direct voxel creation like shadow_minion)
        modelPrefab: null, // Will be created directly in the boss controller
        size: 1.5, // Medium size structure
        // Base Stats
        health: 50, // Moderate health
        baseSpeed: 0.0, // Stationary structure
        speedVariation: 0.0,
        mass: 10.0, // Heavy, immovable
        // AI Parameters - stationary turret
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 3,
        attackCooldown: 1.0, // Fire every second
        preferredRange: 12.0, // Long range
        attackRange: 12.0,
        // Projectile Attack Info
        projectileType: 'sacred_geometry',
        projectileSpeed: 8.0,
        projectileDamage: 15,
        projectileRange: 12.0,
        projectileCount: 1,
        // No melee attack (structure)
        meleeDamage: 0,
        // Animation Parameters
        animationStates: {
            idle: 'crystal_humming',
            moving: 'crystal_humming', // Doesn't move
            attacking: 'crystal_firing',
            emerging: 'crystal_rising'
        },
        // Special crystal tower properties
        isBossMinion: true,
        isStructure: true,
        crystallineStructure: true,
        lifetime: 8000, // 8 seconds lifetime
        spawnEffect: 'crystal_emergence',
        deathEffect: 'crystal_shatter'
    },

    // --- Transmutation Reactor (Chronarch Phase 3 Summon) ---
    transmutation_reactor: {
        // Visuals (will use direct voxel creation like shadow_minion)
        modelPrefab: null, // Will be created directly in the boss controller
        size: 2.0, // Large reactor structure
        // Base Stats
        health: 75, // High health for finale
        baseSpeed: 0.0, // Stationary structure
        speedVariation: 0.0,
        mass: 15.0, // Very heavy, immovable
        // AI Parameters - advanced turret
        aiType: AI_BRAIN_TYPES.RANGED,
        difficulty: 4,
        attackCooldown: 1.5, // Fire every 1.5 seconds
        preferredRange: 15.0, // Very long range
        attackRange: 15.0,
        // Projectile Attack Info (will use pattern system)
        projectileType: 'alchemical_symbols',
        projectileSpeed: 8.0,
        projectileDamage: 20,
        projectileRange: 15.0,
        projectileCount: 1, // Will be overridden by pattern system
        // No melee attack (structure)
        meleeDamage: 0,
        // Animation Parameters
        animationStates: {
            idle: 'reactor_pulsing',
            moving: 'reactor_pulsing', // Doesn't move
            attacking: 'reactor_firing',
            emerging: 'reactor_building'
        },
        // Special transmutation reactor properties
        isBossMinion: true,
        isStructure: true,
        transmutationDevice: true,
        escalatingPower: true,
        lifetime: 8000, // 8 seconds lifetime
        spawnEffect: 'energy_buildup',
        deathEffect: 'reality_explosion',
        // Pattern system
        hasPatterns: true,
        patternTypes: ['pentagram', 'spiral', 'cross', 'hexagon']
    },

    // --- Tomb Guardian (Stone Statue with Shield Bash) ---
    tomb_guardian: {
        // Visuals
        modelPrefab: createTombGuardianEnemyModel,
        size: 2.5, // Extra large stone statue size (bigger than regular enemies)
        // Base Stats
        health: 100, // High health for a tanky enemy
        baseSpeed: 1.2, // Very slow movement
        speedVariation: 0.1, // Little variation for consistent slow movement
        mass: 3.0, // Very heavy stone statue
        // AI Parameters
        aiType: AI_BRAIN_TYPES.MELEE,
        difficulty: 4, // Advanced AI for strategic shield bash
        attackCooldown: 2.0, // Slow but powerful attacks
        preferredRange: 2.5,
        attackRange: 3.5, // Slightly longer reach with sword
        chargeRange: 5.0, // Short charge range
        // Melee Attack Info
        meleeDamage: 1, // Standard damage as per user preference
        // Shield Bash Special Attack
        hasShieldBash: true,
        shieldBashCooldown: 5.0, // 5 second cooldown
        shieldBashRange: 4.0, // Range to trigger shield bash
        shieldBashStunDuration: 1.5, // 1.5 second stun
        shieldBashKnockback: 20, // Strong knockback
        // Block Parameters
        canBlock: true,
        blockChance: 0.5, // High block chance with shield
        blockCooldown: 1.5,
        blockDuration: 1.5,
        // Animation Parameters
        animationStates: {
            idle: 'stone_idle',
            moving: 'stone_walking',
            attacking: 'sword_slash',
            shieldBash: 'shield_bash',
            crumbling: 'death_crumble'
        },
        // Death Effect - Area damage on crumble
        deathEffect: {
            type: 'crumble',
            damageRadius: 5.0, // 5 unit radius
            damage: 15, // Area damage on death
            duration: 1.0 // How long the crumble takes
        }
    },

    // --- Void Lord (Ultimate Boss with Black Hole Head) ---
    void_lord: {
        // Visuals
        modelPrefab: createVoidLordEnemyModel,
        size: 8.0, // Double the massive boss size
        // Base Stats
        health: 200, // Very high health for epic boss battle
        baseSpeed: 1.8, // Moderate speed
        speedVariation: 0.3,
        mass: 5.0, // Extremely heavy
        // AI Parameters
        aiType: AI_BRAIN_TYPES.BOSS,
        difficulty: 5, // Maximum difficulty
        attackCooldown: 1.5,
        // Boss Controller
        bossController: 'VoidLordBossController',
        animationHandler: 'VoidLordAnimationHandler',
        // Music-reactive flag
        musicReactive: false, // Uses its own timing system
        // Projectile Info
        projectileType: 'energy_bolt',
        projectileDamage: 1,
        projectileSpeed: 10.0,
        projectileRange: 40.0,
        projectileSize: 0.5,
        // Melee Attack Info
        meleeDamage: 2, // Higher damage for boss
        // Special Attack Info
        specialAttackCooldown: 5.0,
        specialAttackDamage: 2,
        // Boss Properties
        hasBlackHoleHead: true,
        hasEnergyStrains: true,
        phaseCount: 3,
        // Phase-based health thresholds
        phaseThresholds: {
            phase2: 0.7, // Enter phase 2 at 70% health
            phase3: 0.35 // Enter phase 3 at 35% health
        }
    }
};

// --- Helper to get enemy data ---
// Ensures we don't modify the original definition object
export function getEnemyData(enemyType) {
    const data = ENEMY_TYPES_DATA[enemyType];
    if (!data) {
        console.warn(`Enemy type "${enemyType}" not found!`);
        return null;
    }
    // Return a copy to prevent accidental modification of the definition
    // Note: This is a shallow copy, geometry/material are still references
    return { ...data };
}

// Pre-create geometry/material instances if they are shared often
// (Currently simple, but could use functions from voxelPrefabs later)
// REMOVED OLD PRE-CREATION BLOCK FOR SKELETON BOX

// Make the main definitions read-only (optional, good practice)
Object.freeze(ENEMY_TYPES_DATA);