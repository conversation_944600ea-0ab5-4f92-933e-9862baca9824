/**
 * Defines all item types, rarities, and properties for the game.
 * This serves as the master definition for all items that can be found in the game.
 */

// --- Item Rarity Definitions ---
export const ITEM_RARITY = {
    COMMON: 'common',
    RARE: 'rare',
    EPIC: 'epic',
    LEGENDARY: 'legendary'
};

// --- Item Category Definitions ---
export const ITEM_CATEGORY = {
    WEAPON: 'weapon',       // Modifies player's attack
    CONSUMABLE: 'consumable', // One-time use items
    RELIC: 'relic',         // Passive effects
    UTILITY: 'utility',     // Keys, bombs, etc.
    SOUL: 'soul',           // Soul-related items
    HEALTH: 'health',       // Health-related items
    CARD: 'card'            // Magic cards
};

// --- Item Type Definitions ---
export const ITEM_TYPES = {
    // Health Items
    SOUL_POTION: 'soul_potion',
    SOUL_HEART: 'soul_heart',
    HEART_CONTAINER: 'heart_container',
    
    // Utility Items
    KEY: 'key',
    SOUL_BOMB: 'soul_bomb',
    TREASURE_MAP: 'treasure_map',
    
    // Weapons/Attack Modifiers
    SOUL_BLADE: 'soul_blade',
    SPECTRAL_BOW: 'spectral_bow',
    FROST_WAND: 'frost_wand',
    HERO_SWORD: 'hero_sword',
    MYSTIC_STAFF: 'mystic_staff',
    
    // Relics
    ZEITUHR: 'zeituhr',
    GOLDEN_MIRROR: 'golden_mirror',
    FAMILY_PHOTO: 'family_photo',
    
    // Soul Items
    LIGHT_ESSENCE: 'light_essence',
    DARK_ESSENCE: 'dark_essence',
    BALANCED_ESSENCE: 'balanced_essence',

    // Magic Cards
    CALL_OF_ASCENSION: 'call_of_ascension',
    FOREST_BLESSING: 'forest_blessing',
    PHANTOM_STEP: 'phantom_step',
    SPECTRAL_BOLT: 'spectral_bolt',
    TEMPORAL_RIFT: 'temporal_rift',
    NECROMANTIC_GRASP: 'necromantic_grasp',
    ELEMENTAL_CONVERGENCE: 'elemental_convergence',
    VOID_RUPTURE: 'void_rupture',
    CRYSTAL_SANCTUARY: 'crystal_sanctuary',
    SOUL_ANCHOR: 'soul_anchor',
    MIRROR_PORTAL: 'mirror_portal',
    TIME_ECHO: 'time_echo',
    SOUL_SIGHT: 'soul_sight',
    METEOR_STORM: 'meteor_storm',
    LIGHTNING_CHAIN: 'lightning_chain',
    SHADOW_CLONE_STRIKE: 'shadow_clone_strike',
    SANCTUARY_WARD: 'sanctuary_ward',
    PHOENIX_REBIRTH: 'phoenix_rebirth',
    STONE_SKIN: 'stone_skin',
    DRAGON_FORM: 'dragon_form',
    GHOST_WALK: 'ghost_walk',
    GIANTS_MIGHT: 'giants_might',
    FROST_PRISON: 'frost_prison',
    FLAME_BARRIER: 'flame_barrier',
    EARTHQUAKE: 'earthquake',
    VOLCANO: 'volcano',
    TORNADO: 'tornado',
    SHADOW_ARMY: 'shadow_army',
    SPIRIT_WOLF_PACK: 'spirit_wolf_pack',
    BONE_GUARDIAN: 'bone_guardian',
    BLINK_STRIKE: 'blink_strike',
    TIME_FREEZE: 'time_freeze',
    GRAVITY_WELL: 'gravity_well',
    LIFE_STEAL_AURA: 'life_steal_aura',
    SOUL_HARVEST: 'soul_harvest',
    WILD_MAGIC: 'wild_magic',
    GAMBLERS_LUCK: 'gamblers_luck',
    ASTRAL_RECALL: 'astral_recall',
    DUST_DEVIL: 'dust_devil',
    BIOHAZARD: 'biohazard',
    ELEMENTAL_SHIELD: 'elemental_shield',
    SPECTRAL_GUARDIAN: 'spectral_guardian',
    SHADOW_WEAVE: 'shadow_weave',
    VOID_REALM: 'void_realm',
    ARCANE_MASTERY: 'arcane_mastery',
    SAND_BLAST: 'sand_blast',
    BLADE_MISSILE: 'blade_missile',
    
    // Magic Cards (Set 8)
    HOLY_FIRE: 'holy_fire',
    GOODBERRIES: 'goodberries',
    HOLY_HAND_GRENADE: 'holy_hand_grenade',
    CALL_LIGHTNING: 'call_lightning',
    MEGAVOLT: 'megavolt',
    BLOODLUST: 'bloodlust',
    
    // New Cards (Set 1)
    BLOOD_PACT: 'blood_pact',
    CELESTIAL_STORM: 'celestial_storm',
    CHRONO_SHIFT: 'chrono_shift',
    NATURES_WRATH: 'natures_wrath',
    SOUL_MIRROR: 'soul_mirror',
    
    // New Cards (Set 2)
    ELEMENTAL_FUSION: 'elemental_fusion',
    SHADOW_CLONE: 'shadow_clone',
    ASTRAL_PROJECTION: 'astral_projection',
    PRIMAL_RAGE: 'primal_rage',
    DIVINE_INTERVENTION: 'divine_intervention',
    
    // New Cards (Set 3)
    VOID_WALKER: 'void_walker',
    CRYSTAL_STORM: 'crystal_storm',
    SOUL_REAPER: 'soul_reaper',
    TITAN_STRENGTH: 'titan_strength',
    ARCANE_OVERLOAD: 'arcane_overload',
    
    // New Cards (Set 4)
    MIND_CONTROL: 'mind_control',
    REALITY_TEAR: 'reality_tear',
    PHOENIX_ASCENSION: 'phoenix_ascension',
    VOID_MASTERY: 'void_mastery',
    ELEMENTAL_AVATAR: 'elemental_avatar',
    
    // New Cards (Set 5)
    SHADOW_REALM: 'shadow_realm',
    TEMPORAL_MASTERY: 'temporal_mastery',
    COSMIC_CONVERGENCE: 'cosmic_convergence',
    SOUL_DOMINION: 'soul_dominion',
    CHAOS_INCARNATE: 'chaos_incarnate',
    
    // World-Changing Spells (Set 6)
    FIRESNAKE: 'firesnake',
    CURSE_OF_MIDAS: 'curse_of_midas',
    BASILISK: 'basilisk',
    
    // Alchemical Formulae (Set 7)
    ETERNAL_WINTERS_EMBRACE: 'eternal_winters_embrace',
    EARTHS_SUNDERING_FORMULA: 'earths_sundering_formula',
    VERDANT_OVERGROWTH_ELIXIR: 'verdant_overgrowth_elixir',
    GRAVITATIONAL_INVERSION_CATALYST: 'gravitational_inversion_catalyst',
    ETHEREAL_PHASE_TRANSMUTATION: 'ethereal_phase_transmutation',
    STORM_CALLERS_GRAND_WORKING: 'storm_callers_grand_working',
    MAGMA_HEART_AWAKENING: 'magma_heart_awakening',
    ABSOLUTE_ZERO_DISTILLATION: 'absolute_zero_distillation',
    ZEPHYRS_BINDING_RITUAL: 'zephyrs_binding_ritual',
    PLASMA_GENESIS_CHAMBER: 'plasma_genesis_chamber',
    TEMPORAL_FRACTURING_SOLUTION: 'temporal_fracturing_solution',
    MIRROR_REALM_SYNTHESIS: 'mirror_realm_synthesis',
    VOID_APERTURE_CREATION: 'void_aperture_creation',
    REALITY_BINDING_ANCHORS: 'reality_binding_anchors',
    DIMENSIONAL_CONFLUENCE_BREW: 'dimensional_confluence_brew',
    FORTRESS_MANIFESTATION_RITUAL: 'fortress_manifestation_ritual',
    SHADOW_LEGION_SUMMONING: 'shadow_legion_summoning',
    ARCANE_SIEGE_ENGINE_FORMULA: 'arcane_siege_engine_formula',
    SPATIAL_GATEWAY_NETWORK: 'spatial_gateway_network',
    DEATHS_REVERSAL_THEOREM: 'deaths_reversal_theorem'
};

// --- Item Data Definitions ---
const ITEM_DATA = {
    // --- Health Items ---
    soul_potion: {
        name: 'Soul Potion',
        description: 'A mystical elixir that channels the power of souls to restore 6 health points. The liquid glows with ethereal energy.',
        category: ITEM_CATEGORY.CONSUMABLE,
        rarity: ITEM_RARITY.COMMON,
        effect: 'heal',
        effectValue: 6,
        voxelModel: 'soul_potion',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9966ff,
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        // Card-specific properties for UI display
        cardBorderColor: 0x9966ff, // Purple border to match soul theme
        cardAnimation: 'floating_potion'
    },
    
    soul_heart: {
        name: 'Soul Heart',
        description: 'Adds a temporary heart that disappears when damaged',
        category: ITEM_CATEGORY.HEALTH,
        rarity: ITEM_RARITY.RARE,
        effect: 'add_soul_heart',
        effectValue: 1,
        voxelModel: 'soul_heart',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any', 'soul_door'],
        glow: {
            color: 0x00ffff,
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced' // More likely with balanced soul weight
    },
    
    heart_container: {
        name: 'Heart Container',
        description: 'Permanently increases maximum health by 1',
        category: ITEM_CATEGORY.HEALTH,
        rarity: ITEM_RARITY.EPIC,
        effect: 'increase_max_health',
        effectValue: 1,
        voxelModel: 'heart_container',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xff00ff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    // --- Utility Items ---
    key: {
        name: 'Key',
        description: 'Opens locked doors and chests',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.COMMON,
        effect: 'add_key',
        effectValue: 1,
        voxelModel: 'key',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xffff00,
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    soul_bomb: {
        name: 'Soul Bomb',
        description: 'Explosive that damages enemies and can destroy certain obstacles',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.COMMON,
        effect: 'add_bomb',
        effectValue: 1,
        voxelModel: 'soul_bomb',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xff6600,
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    treasure_map: {
        name: 'Treasure Map',
        description: 'Reveals the entire floor layout',
        category: ITEM_CATEGORY.UTILITY,
        rarity: ITEM_RARITY.RARE,
        effect: 'reveal_map',
        effectValue: 1,
        voxelModel: 'treasure_map',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['secret', 'shop'],
        glow: {
            color: 0xaa7722,
            intensity: 0.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral'
    },
    
    // --- Weapons/Attack Modifiers ---
    soul_blade: {
        name: 'Soul Blade',
        description: 'Increases projectile damage by 5',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_damage',
        effectValue: 5,
        voxelModel: 'soul_blade',
        allowedBiomes: ['catacombs', 'ancient_library'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x0066ff,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'dark' // More likely with dark soul weight
    },
    
    spectral_bow: {
        name: 'Spectral Bow',
        description: 'Increases projectile range by 5',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_range',
        effectValue: 5,
        voxelModel: 'spectral_bow',
        allowedBiomes: ['crystal_caves', 'flooded_ruins'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x66ffaa,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light' // More likely with light soul weight
    },
    
    frost_wand: {
        name: 'Frost Wand',
        description: 'Increases projectile size by 0.1',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.RARE,
        effect: 'increase_size',
        effectValue: 0.1,
        voxelModel: 'frost_wand',
        allowedBiomes: ['crystal_caves', 'lava_tubes'],
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        glow: {
            color: 0x00ffff,
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    },
    
    hero_sword: {
        name: 'Hero Sword',
        description: 'A legendary sword once wielded by a great hero. Its blade gleams with ancient power.',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'increase_damage',
        effectValue: 2,
        voxelModel: 'hero_sword',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffd700,
            intensity: 2.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        
        // Weapon-specific properties
        weaponType: 'melee',
        damage: 2,
        range: 4,
        attackSpeed: 1.2,
        attackPattern: {
            type: 'frontal_arc',
            range: 3.0,
            angle: 90,
            description: 'Sword swing in a frontal arc'
        }
    },
    
    mystic_staff: {
        name: 'Mystic Staff',
        description: 'A powerful staff topped with a glowing crystal. When slammed into the ground, it unleashes magical energy in all directions.',
        category: ITEM_CATEGORY.WEAPON,
        rarity: ITEM_RARITY.EPIC,
        effect: 'area_damage',
        effectValue: 3,
        voxelModel: 'mystic_staff',
        allowedBiomes: ['ancient_library', 'crystal_caves', 'astral_plane'],
        allowedRoomTypes: ['elite', 'boss', 'secret'],
        glow: {
            color: 0x9932CC,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        
        // Weapon-specific properties
        weaponType: 'melee',
        damage: 3,
        range: 5,
        attackSpeed: 0.8,
        attackPattern: {
            type: 'circular',
            range: 5.0,
            description: 'Ground slam with circular area damage'
        }
    },
    
    // --- Relics ---
    zeituhr: {
        name: 'Zeituhr',
        description: 'Slows down enemies when your health is low',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.EPIC,
        effect: 'slow_time',
        effectValue: 0.5, // 50% speed reduction
        voxelModel: 'zeituhr',
        allowedBiomes: ['ancient_library', 'obsidian_fortress'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffcc00,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    },
    
    golden_mirror: {
        name: 'Golden Mirror',
        description: 'Chance to reflect enemy projectiles',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.EPIC,
        effect: 'reflect_projectiles',
        effectValue: 0.3, // 30% chance to reflect
        voxelModel: 'golden_mirror',
        allowedBiomes: ['crystal_caves', 'astral_plane'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffff00,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'light'
    },
    
    family_photo: {
        name: 'Family Photo',
        description: 'Increases all stats when in danger',
        category: ITEM_CATEGORY.RELIC,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'danger_boost',
        effectValue: 1.5, // 50% boost to all stats
        voxelModel: 'family_photo',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['boss', 'secret'],
        glow: {
            color: 0xffffff,
            intensity: 2.5
        },
        blockedBy: ['story_progression_1'], // Requires story progression
        soulWeightInfluence: 'neutral'
    },
    
    // --- Soul Items ---
    light_essence: {
        name: 'Light Essence',
        description: 'Shifts your soul weight toward light',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.RARE,
        effect: 'shift_soul_weight',
        effectValue: 'light',
        voxelModel: 'light_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0xffffff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'light'
    },
    
    dark_essence: {
        name: 'Dark Essence',
        description: 'Shifts your soul weight toward dark',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.RARE,
        effect: 'shift_soul_weight',
        effectValue: 'dark',
        voxelModel: 'dark_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0x330066,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'dark'
    },
    
    balanced_essence: {
        name: 'Balanced Essence',
        description: 'Balances your soul weight',
        category: ITEM_CATEGORY.SOUL,
        rarity: ITEM_RARITY.EPIC,
        effect: 'balance_soul_weight',
        effectValue: 'balanced',
        voxelModel: 'balanced_essence',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['soul_door', 'secret'],
        glow: {
            color: 0x9966ff,
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced'
    },

    // --- Magic Cards ---
    call_of_ascension: {
        name: 'Call of Ascension',
        description: 'Summons ethereal wings that lift the caster above earthly bounds, granting temporary flight and revealing hidden paths through the spiritual realm.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'summon_wings',
        effectValue: 'flight',
        cardType: 'summon',
        voxelModel: 'call_of_ascension_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xffd700, // Golden glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        // Card-specific properties
        cardBorderColor: 0xffd700, // Golden border
        cardAnimation: 'floating_wings'
    },

    forest_blessing: {
        name: 'Forest Blessing',
        description: 'Channels the ancient power of the forest, causing vines to emerge from the ground and entangle enemies while healing the caster.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'forest_entangle',
        effectValue: 'nature',
        cardType: 'spell',
        voxelModel: 'forest_blessing_card',
        allowedBiomes: ['forest', 'nature'], // Only drops in forest areas
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00ff00, // Green glow
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        // Card-specific properties
        cardBorderColor: 0x00ff00, // Green border
        cardAnimation: 'growing_vines'
    },

    phantom_step: {
        name: 'Phantom Step',
        description: 'Dissolves your form into shadow and mist, allowing passage through the ethereal realm. Emerge at your destination cloaked in darkness, unseen by mortal eyes.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'phantom_teleport',
        effectValue: 'teleport',
        cardType: 'mobility',
        voxelModel: 'phantom_step_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x696969, // Dark gray glow
            intensity: 1.0
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x2F2F2F, // Dark gray border
        cardAnimation: 'fading_shadow'
    },

    spectral_bolt: {
        name: 'Spectral Bolt',
        description: 'Channels devastating ethereal magic to unleash three consecutive salvos of ten bolts each, raining thirty piercing projectiles in all directions over six seconds of sustained destruction.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'spectral_projectile',
        effectValue: 25, // High damage per bolt (30 bolts total)
        cardType: 'attack',
        voxelModel: 'spectral_bolt_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4DC8FF, // Bright ethereal blue
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4DC8FF, // Ethereal blue border
        cardAnimation: 'energy_surge'
    },

    temporal_rift: {
        name: 'Temporal Rift',
        description: 'Manipulates the flow of time itself, creating a temporal bubble that slows all enemies to 20% speed for 8 seconds while you move at normal speed, allowing tactical repositioning and strategic advantage.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'time_manipulation',
        effectValue: 8, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'temporal_rift_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden glow
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xB87333, // Bronze border
        cardAnimation: 'clockwork_gears'
    },

    necromantic_grasp: {
        name: 'Necromantic Grasp',
        description: 'Summons three skeletal hands from the underworld to grasp and damage nearby enemies while draining their life force to heal the caster, combining offense with powerful sustain.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'necromantic_summon',
        effectValue: 3, // Number of skeletal hands summoned
        cardType: 'attack',
        voxelModel: 'necromantic_grasp_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Dark purple glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Dark purple border
        cardAnimation: 'grasping_hands'
    },

    elemental_convergence: {
        name: 'Elemental Convergence',
        description: 'Unleashes the ultimate elemental storm by converging fire, ice, and lightning into a devastating area attack that deals massive damage to all enemies in a large radius, representing the pinnacle of elemental magic.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'elemental_storm',
        effectValue: 50, // Base damage per element (150 total damage)
        cardType: 'attack',
        voxelModel: 'elemental_convergence_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFFD700, // Golden border (legendary)
        cardAnimation: 'elemental_storm'
    },

    void_rupture: {
        name: 'Void Rupture',
        description: 'Tears open a rift in reality itself, creating a swirling void that pulls enemies toward its center while dealing continuous damage. The dark energy consumes all light in its vicinity.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'void_tear',
        effectValue: 35, // Damage per second
        cardType: 'attack',
        voxelModel: 'void_rupture_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2E0066, // Deep purple glow
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Indigo border (epic)
        cardAnimation: 'void_swirl'
    },

    crystal_sanctuary: {
        name: 'Crystal Sanctuary',
        description: 'Summons 8 protective crystal shields for 40 seconds. Each hit breaks one crystal shield, protecting you completely until all crystals are destroyed.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'crystal_barrier',
        effectValue: 8, // Number of crystal shields
        cardType: 'defense',
        voxelModel: 'crystal_sanctuary_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4DC8FF, // Crystal blue glow
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        // Card-specific properties
        cardBorderColor: 0x4DC8FF, // Crystal blue border (rare)
        cardAnimation: 'crystal_shimmer'
    },

    soul_anchor: {
        name: 'Soul Anchor',
        description: 'Places a mystical anchor that creates a permanent teleportation point. Use again to instantly teleport back to the anchor location, bridging vast distances through soul magic.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'teleport_anchor',
        effectValue: 1, // Number of anchor placements
        cardType: 'utility',
        voxelModel: 'soul_anchor_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9966FF, // Soul purple glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x9966FF, // Soul purple border (epic)
        cardAnimation: 'soul_anchor'
    },

    mirror_portal: {
        name: 'Mirror Portal',
        description: 'Creates two linked portals for instant travel. First use places Portal A, second use places Portal B, then travel instantly between them.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'mirror_portal',
        effectValue: 2, // Number of portals created
        cardType: 'utility',
        voxelModel: 'mirror_portal_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00BFFF, // Deep sky blue glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x00BFFF, // Deep sky blue border (epic)
        cardAnimation: 'mirror_portal'
    },

    time_echo: {
        name: 'Time Echo',
        description: 'Creates a temporal shadow copy that mimics and repeats your last significant action with a slight delay, effectively doubling your presence on the battlefield.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'time_echo',
        effectValue: 1, // Number of echo shadows created
        cardType: 'utility',
        voxelModel: 'time_echo_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9932CC, // Dark orchid glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'time_echo'
    },

    soul_sight: {
        name: 'Soul Sight',
        description: 'Grants mystical vision for 60 seconds that reveals all hidden rooms, secret passages, and concealed treasures on the current floor, illuminating the unseen paths through divine insight.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'soul_sight',
        effectValue: 60, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'soul_sight_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4169E1, // Royal blue glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4169E1, // Royal blue border (epic)
        cardAnimation: 'soul_sight'
    },

    meteor_storm: {
        name: 'Meteor Storm',
        description: 'Calls down a devastating rain of fiery meteors at multiple target locations, each dealing massive impact and fire damage to enemies within the blast radius.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'meteor_storm',
        effectValue: 5, // Number of meteors summoned
        cardType: 'attack',
        voxelModel: 'meteor_storm_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Orange red glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFF4500, // Orange red border (epic)
        cardAnimation: 'meteor_storm'
    },

    lightning_chain: {
        name: 'Lightning Chain',
        description: 'Unleashes a devastating chain lightning that arcs between enemies, dealing electrical damage and jumping up to 6 targets with each jump dealing reduced damage.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'lightning_chain',
        effectValue: 6, // Maximum number of chain jumps
        cardType: 'attack',
        voxelModel: 'lightning_chain_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x0080FF, // Electric blue glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x0080FF, // Electric blue border (epic)
        cardAnimation: 'lightning_chain'
    },

    shadow_clone_strike: {
        name: 'Shadow Clone Strike',
        description: 'Summons shadow clones that appear around enemies and strike simultaneously, dealing dark damage from multiple directions with perfect coordination.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'shadow_clone_strike',
        effectValue: 4, // Number of shadow clones summoned
        cardType: 'attack',
        voxelModel: 'shadow_clone_strike_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Dark purple glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Dark purple border (epic)
        cardAnimation: 'shadow_clone_strike'
    },

    sanctuary_ward: {
        name: 'Sanctuary Ward',
        description: 'Creates a protective sanctuary for 30 seconds that absorbs incoming damage and slowly heals the caster while providing temporary invulnerability.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'sanctuary_ward',
        effectValue: 30, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'sanctuary_ward_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Divine gold glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        // Card-specific properties
        cardBorderColor: 0xFFD700, // Divine gold border (epic)
        cardAnimation: 'sanctuary_ward'
    },

    phoenix_rebirth: {
        name: 'Phoenix Rebirth',
        description: 'Grants the power of the phoenix - upon death, resurrect with full health surrounded by flames that damage nearby enemies.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'phoenix_rebirth',
        effectValue: 1, // Number of resurrections granted
        cardType: 'defense',
        voxelModel: 'phoenix_rebirth_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Flame red glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFF4500, // Flame red border (epic)
        cardAnimation: 'phoenix_rebirth'
    },

    stone_skin: {
        name: 'Stone Skin',
        description: 'Transforms your skin into living stone for 45 seconds, providing significant damage reduction and immunity to certain status effects while maintaining mobility.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'stone_skin',
        effectValue: 45, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'stone_skin_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x696969, // Stone gray glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x696969, // Stone gray border (epic)
        cardAnimation: 'stone_skin'
    },

    dragon_form: {
        name: 'Dragon Form',
        description: 'Transform into a mighty dragon for 60 seconds, gaining the ability to fly, breathe fire, and increased damage while maintaining combat mobility.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'dragon_form',
        effectValue: 60, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'dragon_form_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDC143C, // Crimson red glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0xDC143C, // Crimson red border (epic)
        cardAnimation: 'dragon_form'
    },

    ghost_walk: {
        name: 'Ghost Walk',
        description: 'Transform into an ethereal spirit for 45 seconds, gaining the ability to phase through walls, become invisible to enemies, and move silently through the spectral realm.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'ghost_walk',
        effectValue: 45, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'ghost_walk_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4169E1, // Spectral blue glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4169E1, // Spectral blue border (epic)
        cardAnimation: 'ghost_walk'
    },

    giants_might: {
        name: "Giant's Might",
        description: 'Transform into a colossal titan for 50 seconds, gaining massive size increase, overwhelming physical strength, and earth-shaking power that devastates enemies through sheer force.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'giants_might',
        effectValue: 50, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'giants_might_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xCD7F32, // Titan bronze glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xCD7F32, // Titan bronze border (epic)
        cardAnimation: 'giants_might'
    },

    frost_prison: {
        name: 'Frost Prison',
        description: 'Creates ice walls that block enemy movement and projectiles for 40 seconds. Enemies touching walls take frost damage and are slowed.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'frost_prison',
        effectValue: 40, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'frost_prison_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x87CEEB, // Ice blue glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x87CEEB, // Ice blue border (epic)
        cardAnimation: 'frost_prison'
    },

    flame_barrier: {
        name: 'Flame Barrier',
        description: 'Summons a ring of fire around the player for 35 seconds that damages approaching enemies and provides light in dark areas.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'flame_barrier',
        effectValue: 35, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'flame_barrier_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Fire red glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFF4500, // Fire red border (epic)
        cardAnimation: 'flame_barrier'
    },

    earthquake: {
        name: 'Earthquake',
        description: 'Causes ground to shake violently, dealing massive damage to all enemies and destroying destructible walls with seismic force.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'earthquake',
        effectValue: 100, // Base damage to all enemies
        cardType: 'attack',
        voxelModel: 'earthquake_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B4513, // Earth brown glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x8B4513, // Earth brown border (epic)
        cardAnimation: 'earthquake'
    },
    
    volcano: {
        name: 'Volcano',
        description: 'Erupts a massive volcano from the ground, dealing devastating fire damage to all enemies in a large area with molten lava and volcanic debris.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'volcano_eruption',
        effectValue: 120, // Base damage to enemies in eruption area
        cardType: 'attack',
        voxelModel: 'volcano_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Lava red glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'aggressive',
        // Card-specific properties
        cardBorderColor: 0xFF4500, // Lava red border (epic)
        cardAnimation: 'volcano'
    },
    
    tornado: {
        name: 'Tornado',
        description: 'Summons a powerful tornado that moves across the battlefield, dealing wind damage to enemies it touches and hurling debris with devastating force.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'tornado',
        effectValue: 80, // Base damage per tornado hit
        cardType: 'attack',
        voxelModel: 'tornado_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4682B4, // Storm blue glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4682B4, // Storm blue border (epic)
        cardAnimation: 'tornado'
    },
    
    shadow_army: {
        name: 'Shadow Army',
        description: 'Summons an army of shadow warriors that fight alongside you for 30 seconds, each dealing damage to nearby enemies with ethereal weapons.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'shadow_army',
        effectValue: 5, // Number of shadow warriors summoned
        cardType: 'summoning',
        voxelModel: 'shadow_army_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Void purple glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Void purple border (legendary)
        cardAnimation: 'shadow_army'
    },
    
    spirit_wolf_pack: {
        name: 'Spirit Wolf Pack',
        description: 'Summons a pack of 3 spirit wolves that hunt enemies with supernatural speed and coordination, each dealing damage with ethereal fangs.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'spirit_wolf_pack',
        effectValue: 3, // Number of spirit wolves summoned
        cardType: 'summoning',
        voxelModel: 'spirit_wolf_pack_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x87CEEB, // Spirit blue glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x87CEEB, // Spirit blue border (epic)
        cardAnimation: 'spirit_wolf_pack'
    },

    bone_guardian: {
        name: 'Bone Guardian',
        description: 'Summons a skeletal guardian that blocks enemy attacks and retaliates with bone magic, providing strong defensive presence.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'bone_guardian',
        effectValue: 1, // Number of bone guardians summoned
        cardType: 'summoning',
        voxelModel: 'bone_guardian_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8A2BE2, // Soul purple glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x8A2BE2, // Soul purple border (epic)
        cardAnimation: 'bone_guardian'
    },

    blink_strike: {
        name: 'Blink Strike',
        description: 'Instantly teleports to target enemy and performs a devastating strike, dealing high damage and briefly stunning the target.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'blink_strike',
        effectValue: 50, // Strike damage
        cardType: 'attack',
        voxelModel: 'blink_strike_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x1E90FF, // Teleport blue glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x1E90FF, // Teleport blue border (rare)
        cardAnimation: 'blink_strike'
    },

    time_freeze: {
        name: 'Time Freeze',
        description: 'Freezes all enemies in time for 8 seconds, stopping their movement and actions while allowing the player to move freely.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'time_freeze',
        effectValue: 8, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'time_freeze_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Time gold glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFFD700, // Time gold border (legendary)
        cardAnimation: 'time_freeze'
    },

    gravity_well: {
        name: 'Gravity Well',
        description: 'Creates a gravitational field at target location that pulls enemies toward the center for 6 seconds, dealing damage over time.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'gravity_well',
        effectValue: 6, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'gravity_well_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Void purple glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Void purple border (epic)
        cardAnimation: 'gravity_well'
    },

    life_steal_aura: {
        name: 'Life Steal Aura',
        description: 'Creates a vampiric aura around the player for 20 seconds that steals health from nearby enemies and heals the player.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'life_steal_aura',
        effectValue: 20, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'life_steal_aura_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDC143C, // Crimson red glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xDC143C, // Crimson red border (epic)
        cardAnimation: 'life_steal_aura'
    },

    soul_harvest: {
        name: 'Soul Harvest',
        description: 'Absorbs souls from defeated enemies for 15 seconds, increasing damage by 10% per soul (max 100% bonus damage).',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'soul_harvest',
        effectValue: 15, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'soul_harvest_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4169E1, // Soul blue glow
            intensity: 1.7
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4169E1, // Soul blue border (epic)
        cardAnimation: 'soul_harvest'
    },

    wild_magic: {
        name: 'Wild Magic',
        description: 'Triggers a random magical effect from a pool of unpredictable spells with varying power levels.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'wild_magic',
        effectValue: 1, // Number of effects to trigger
        cardType: 'utility',
        voxelModel: 'wild_magic_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Chaos orange glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFF4500, // Chaos orange border (epic)
        cardAnimation: 'wild_magic'
    },

    gamblers_luck: {
        name: "Gambler's Luck",
        description: 'Roll the dice for risk/reward effects. Can grant powerful rewards or dangerous penalties based on chance.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'gamblers_luck',
        effectValue: 1, // Number of gambles to make
        cardType: 'utility',
        voxelModel: 'gamblers_luck_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Fortune gold glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFFD700, // Fortune gold border (epic)
        cardAnimation: 'gamblers_luck'
    },

    astral_recall: {
        name: 'Astral Recall',
        description: 'Instantly teleports you back to the entrance of the current dungeon floor through astral projection. Emergency escape when overwhelmed by danger.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'astral_recall',
        effectValue: 1, // Single use teleportation
        cardType: 'mobility',
        voxelModel: 'astral_recall_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9370DB, // Astral purple glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x9370DB, // Astral purple border (rare)
        cardAnimation: 'astral_recall'
    },

    dust_devil: {
        name: 'Dust Devil',
        description: 'Summons a small but fierce whirlwind of dust and debris that spirals erratically across the battlefield, dealing earth damage to enemies in its chaotic path.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.COMMON,
        effect: 'dust_devil',
        effectValue: 45, // Base damage per dust devil hit
        cardType: 'attack',
        voxelModel: 'dust_devil_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDEB887, // Dusty tan glow
            intensity: 1.0
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        // Card-specific properties
        cardBorderColor: 0xDEB887, // Dusty tan border (common)
        cardAnimation: 'dust_devil'
    },

    biohazard: {
        name: 'Biohazard',
        description: 'Releases a spreading cloud of toxic chemicals that creates a hazardous area, dealing continuous poison damage to all enemies within the contaminated zone.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'toxic_cloud',
        effectValue: 25, // Damage per second
        cardType: 'attack',
        voxelModel: 'biohazard_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x32CD32, // Toxic green glow
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x32CD32, // Toxic green border (rare)
        cardAnimation: 'biohazard'
    },

    blink_strike: {
        name: 'Blink Strike',
        description: 'Instantly teleports you behind the nearest enemy and delivers a devastating surprise attack, combining mobility with high damage in one swift motion.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'blink_strike',
        effectValue: 80, // High damage from surprise attack
        cardType: 'mobility',
        voxelModel: 'blink_strike_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Deep indigo (blink magic)
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Deep indigo border (epic)
        cardAnimation: 'blink_strike'
    },

    time_freeze: {
        name: 'Time Freeze',
        description: 'Stops time itself, freezing all enemies in temporal stasis while you move freely. The ultimate legendary spell that grants absolute control over the battlefield.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'time_freeze',
        effectValue: 8, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'time_freeze_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden time magic
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFFD700, // Golden border (legendary)
        cardAnimation: 'time_freeze'
    },

    gravity_well: {
        name: 'Gravity Well',
        description: 'Creates a powerful gravitational vortex that pulls all enemies toward its center, dealing crushing damage and disrupting their movements with intense spatial distortion.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'gravity_well',
        effectValue: 60, // Damage per pull + continuous damage
        cardType: 'attack',
        voxelModel: 'gravity_well_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8A2BE2, // Violet gravitational energy
            intensity: 1.7
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x8A2BE2, // Violet border (epic)
        cardAnimation: 'gravity_well'
    },

    life_steal_aura: {
        name: 'Life Steal Aura',
        description: 'Surrounds you with a vampiric aura that continuously drains life from nearby enemies and transfers it to you, creating a sustaining field of dark magic.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'life_steal_aura',
        effectValue: 15, // Life steal per second
        cardType: 'utility',
        voxelModel: 'life_steal_aura_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDC143C, // Crimson blood magic
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0xDC143C, // Crimson border (epic)
        cardAnimation: 'life_steal_aura'
    },

    soul_harvest: {
        name: 'Soul Harvest',
        description: 'Unleashes a reaper\'s scythe that harvests souls from defeated enemies in a large radius, converting them into health restoration and temporary power increases.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'soul_harvest',
        effectValue: 25, // Health per soul + damage bonus
        cardType: 'utility',
        voxelModel: 'soul_harvest_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4169E1, // Soul blue magic
            intensity: 1.7
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x4169E1, // Soul blue border (epic)
        cardAnimation: 'soul_harvest'
    },

    elemental_shield: {
        name: 'Elemental Shield',
        description: 'Creates rotating elemental barriers around you that block incoming attacks and reflect damage back to enemies. Each barrier represents a different element.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'elemental_shield',
        effectValue: 30, // Shield duration in seconds
        cardType: 'utility',
        voxelModel: 'elemental_shield_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00CED1, // Elemental cyan glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x00CED1, // Elemental cyan border (epic)
        cardAnimation: 'elemental_shield'
    },

    spectral_guardian: {
        name: 'Spectral Guardian',
        description: 'Summons a loyal ghostly guardian that follows you and automatically attacks nearby enemies. The guardian phases through walls and provides constant protection.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'spectral_guardian',
        effectValue: 45, // Guardian duration in seconds
        cardType: 'utility',
        voxelModel: 'spectral_guardian_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9370DB, // Spectral purple glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0x9370DB, // Spectral purple border (epic)
        cardAnimation: 'spectral_guardian'
    },

    shadow_weave: {
        name: 'Shadow Weave',
        description: 'Weaves multiple shadow copies of yourself that appear around enemies and attack simultaneously. Each shadow deals massive damage before vanishing.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'shadow_weave',
        effectValue: 150, // Base damage per shadow
        cardType: 'attack',
        voxelModel: 'shadow_weave_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2F2F2F, // Dark shadow glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x2F2F2F, // Dark shadow border (legendary)
        cardAnimation: 'shadow_weave'
    },

    void_realm: {
        name: 'Void Realm',
        description: 'Banishes you to a safe void dimension outside reality. While in the void, time moves slowly and you can observe the battlefield from above. Return at will.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'void_realm',
        effectValue: 60, // Maximum time in void (seconds)
        cardType: 'utility',
        voxelModel: 'void_realm_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Void purple glow
            intensity: 1.9
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        // Card-specific properties
        cardBorderColor: 0x4B0082, // Void purple border (legendary)
        cardAnimation: 'void_realm'
    },

    arcane_mastery: {
        name: 'Arcane Mastery',
        description: 'Grants temporary mastery over arcane forces. All card effects are enhanced, casting costs reduced, and magical energy regenerates faster.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'arcane_mastery',
        effectValue: 30, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'arcane_mastery_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF6B35, // Arcane orange glow
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        // Card-specific properties
        cardBorderColor: 0xFF6B35, // Arcane orange border (legendary)
        cardAnimation: 'arcane_mastery'
    },

    sand_blast: {
        name: 'Sand Blast',
        description: 'Unleashes a powerful stream of razor-sharp sand particles that tears through enemies and creates a spreading cloud of abrasive dust.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.COMMON,
        effect: 'sand_blast',
        effectValue: 35, // Base damage
        cardType: 'attack',
        voxelModel: 'sand_blast_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDEB887, // Dusty tan glow
            intensity: 1.0
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        // Card-specific properties
        cardBorderColor: 0xDEB887, // Dusty tan border (common)
        cardAnimation: 'sand_blast'
    },

    blade_missile: {
        name: 'Blade Missile',
        description: 'Launches a volley of razor-sharp spectral blades that home in on enemies, dealing piercing damage and causing bleeding effects.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'blade_missile',
        effectValue: 45, // Base damage per blade
        cardType: 'attack',
        voxelModel: 'blade_missile_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xC0C0C0, // Silver metallic glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        // Card-specific properties
        cardBorderColor: 0xC0C0C0, // Silver border (rare)
        cardAnimation: 'blade_missile'
    },

    // === MAGIC CARDS SET 8 ===

    holy_fire: {
        name: 'Holy Fire',
        description: 'Calls down divine flames that purify the battlefield, dealing holy damage to enemies while healing allies within the sacred blaze.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'holy_fire',
        effectValue: 60, // Base damage/healing
        cardType: 'attack',
        voxelModel: 'holy_fire_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden divine glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'positive',
        cardBorderColor: 0xFFD700, // Gold border (epic)
        cardAnimation: 'holy_fire'
    },

    goodberries: {
        name: 'Goodberries',
        description: 'Conjures magical berries that restore health and provide temporary regeneration. Each berry heals wounds and grants vitality.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.COMMON,
        effect: 'goodberries',
        effectValue: 25, // Base healing per berry
        cardType: 'utility',
        voxelModel: 'goodberries_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x228B22, // Forest green glow
            intensity: 0.8
        },
        blockedBy: [],
        soulWeightInfluence: 'positive',
        cardBorderColor: 0x8B8B83, // Gray border (common)
        cardAnimation: 'goodberries'
    },

    holy_hand_grenade: {
        name: 'Holy Hand Grenade',
        description: 'The sacred explosive of legend. Count to three, no more, no less. Deals massive holy damage in a wide area after a brief delay.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'holy_hand_grenade',
        effectValue: 150, // Base explosive damage
        cardType: 'attack',
        voxelModel: 'holy_hand_grenade_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFFFE0, // Holy light glow
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'positive',
        cardBorderColor: 0xFFD700, // Gold border (legendary)
        cardAnimation: 'holy_hand_grenade'
    },

    call_lightning: {
        name: 'Call Lightning',
        description: 'Summons bolts of lightning from the sky to strike multiple enemies. Each bolt carries the fury of the storm.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'call_lightning',
        effectValue: 70, // Base damage per bolt
        cardType: 'attack',
        voxelModel: 'call_lightning_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x87CEEB, // Electric blue glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xC0C0C0, // Silver border (rare)
        cardAnimation: 'call_lightning'
    },

    megavolt: {
        name: 'Megavolt',
        description: 'Unleashes a massive electrical surge that chains between enemies, growing stronger with each jump. Pure electrical destruction.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'megavolt',
        effectValue: 80, // Base damage that increases per chain
        cardType: 'attack',
        voxelModel: 'megavolt_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00FFFF, // Cyan electric glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFFD700, // Gold border (epic)
        cardAnimation: 'megavolt'
    },

    bloodlust: {
        name: 'Bloodlust',
        description: 'Enters a savage frenzy that increases attack speed and damage based on enemies killed. The more blood spilled, the stronger you become.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'bloodlust',
        effectValue: 30, // Base damage bonus percentage
        cardType: 'enhancement',
        voxelModel: 'bloodlust_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B0000, // Dark red blood glow
            intensity: 1.2
        },
        blockedBy: [],
        soulWeightInfluence: 'negative',
        cardBorderColor: 0xC0C0C0, // Silver border (rare)
        cardAnimation: 'bloodlust'
    },

    // === NEW CARDS ===

    blood_pact: {
        name: 'Blood Pact',
        description: 'Sacrifice health to gain immense magical power. Converts all remaining health into devastating blood magic that obliterates enemies.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'blood_pact',
        effectValue: 8, // Damage multiplier per health point sacrificed
        cardType: 'attack',
        voxelModel: 'blood_pact_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B0000, // Dark red blood glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x8B0000, // Dark red border (legendary)
        cardAnimation: 'blood_pact'
    },

    celestial_storm: {
        name: 'Celestial Storm',
        description: 'Calls down divine lightning from the heavens. Multiple bolts of celestial energy strike all enemies while healing the caster.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'celestial_storm',
        effectValue: 65, // Base damage per bolt
        cardType: 'attack',
        voxelModel: 'celestial_storm_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden celestial glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFFD700, // Golden border (epic)
        cardAnimation: 'celestial_storm'
    },

    chrono_shift: {
        name: 'Chrono Shift',
        description: 'Manipulates the flow of time in your favor. Slows down all enemies while accelerating your movement and attack speed.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'chrono_shift',
        effectValue: 15, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'chrono_shift_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9400D3, // Violet time energy glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        cardBorderColor: 0x9400D3, // Violet border (rare)
        cardAnimation: 'chrono_shift'
    },

    natures_wrath: {
        name: "Nature's Wrath",
        description: 'Unleashes the fury of nature itself. Vines emerge from the ground, thorns rain from above, and poisonous spores fill the air.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'natures_wrath',
        effectValue: 45, // Base damage from nature effects
        cardType: 'attack',
        voxelModel: 'natures_wrath_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x228B22, // Forest green nature glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0x228B22, // Forest green border (epic)
        cardAnimation: 'natures_wrath'
    },

    soul_mirror: {
        name: 'Soul Mirror',
        description: 'Creates a mystical mirror that reflects incoming damage back to attackers. Also reveals hidden enemies and their weaknesses.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'soul_mirror',
        effectValue: 20, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'soul_mirror_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xE6E6FA, // Lavender mirror glow
            intensity: 1.3
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        cardBorderColor: 0xE6E6FA, // Lavender border (rare)
        cardAnimation: 'soul_mirror'
    },
    
    // Second Set of New Cards
    elemental_fusion: {
        name: 'Elemental Fusion',
        description: 'Combines fire, ice, and lightning into a devastating tri-elemental blast that adapts to enemy weaknesses and deals bonus damage.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'elemental_fusion',
        effectValue: 12, // Multiplier for tri-elemental damage
        cardType: 'attack',
        voxelModel: 'elemental_fusion_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF6B35, // Fusion orange glow
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        cardBorderColor: 0xFF6B35, // Fusion orange border (legendary)
        cardAnimation: 'elemental_fusion'
    },
    
    shadow_clone: {
        name: 'Shadow Clone',
        description: 'Creates multiple shadow duplicates that confuse enemies and deal independent damage from different angles.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'shadow_clone',
        effectValue: 3, // Number of shadow clones
        cardType: 'summoning',
        voxelModel: 'shadow_clone_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2F2F2F, // Shadow gray glow
            intensity: 1.4
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x2F2F2F, // Shadow gray border (epic)
        cardAnimation: 'shadow_clone'
    },
    
    astral_projection: {
        name: 'Astral Projection',
        description: 'Projects your soul outside your body, becoming temporarily invulnerable while attacking from the astral plane.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'astral_projection',
        effectValue: 18, // Duration in seconds
        cardType: 'defense',
        voxelModel: 'astral_projection_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9370DB, // Medium orchid glow
            intensity: 1.5
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0x9370DB, // Medium orchid border (rare)
        cardAnimation: 'astral_projection'
    },
    
    primal_rage: {
        name: 'Primal Rage',
        description: 'Enters a berserker state that dramatically increases attack speed and damage while sacrificing defense for raw power.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'primal_rage',
        effectValue: 40, // Damage and speed boost percentage
        cardType: 'enhancement',
        voxelModel: 'primal_rage_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B0000, // Dark red glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x8B0000, // Dark red border (epic)
        cardAnimation: 'primal_rage'
    },
    
    divine_intervention: {
        name: 'Divine Intervention',
        description: 'Calls upon divine protection that prevents death once and fully heals when health reaches zero, granting a second chance.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'divine_intervention',
        effectValue: 1, // Number of interventions
        cardType: 'defense',
        voxelModel: 'divine_intervention_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Divine gold glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFFD700, // Divine gold border (legendary)
        cardAnimation: 'divine_intervention'
    },
    
    // New Cards (Set 3)
    void_walker: {
        name: 'Void Walker',
        description: 'Grants the ability to phase through walls and enemies for a short duration, becoming untouchable while walking through the void dimension.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'void_walker',
        effectValue: 12, // Duration in seconds
        cardType: 'utility',
        voxelModel: 'void_walker_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x6A0DAD, // Dark purple void glow
            intensity: 1.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'void_walker'
    },
    
    crystal_storm: {
        name: 'Crystal Storm',
        description: 'Summons a devastating storm of razor-sharp crystal shards that orbit around you, automatically attacking nearby enemies with piercing damage.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.RARE,
        effect: 'crystal_storm',
        effectValue: 20, // Duration in seconds
        cardType: 'attack',
        voxelModel: 'crystal_storm_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00CED1, // Dark turquoise crystal glow
            intensity: 1.6
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0x4169E1, // Royal blue border (rare)
        cardAnimation: 'crystal_storm'
    },
    
    soul_reaper: {
        name: 'Soul Reaper',
        description: 'Instantly kills enemies below 25% health and harvests their souls to permanently increase your maximum health and damage for the rest of the run.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'soul_reaper',
        effectValue: 25, // Health threshold percentage
        cardType: 'attack',
        voxelModel: 'soul_reaper_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B0000, // Dark red soul reaper glow
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'soul_reaper'
    },
    
    titan_strength: {
        name: 'Titan Strength',
        description: 'Temporarily transforms you into a colossal titan, increasing your size, damage, and knockback while making you immune to small enemy attacks.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'titan_strength',
        effectValue: 15, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'titan_strength_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDAA520, // Goldenrod titan glow
            intensity: 1.9
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'titan_strength'
    },
    
    arcane_overload: {
        name: 'Arcane Overload',
        description: 'Channels unstable arcane energy that creates random magical explosions around you for massive damage, but also damages you slightly with each blast.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'arcane_overload',
        effectValue: 18, // Duration in seconds
        cardType: 'chaos',
        voxelModel: 'arcane_overload_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF00FF, // Magenta arcane glow
            intensity: 2.1
        },
        blockedBy: [],
        soulWeightInfluence: 'chaotic',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'arcane_overload'
    },

    // Set 4 Cards
    mind_control: {
        name: 'Mind Control',
        description: 'Temporarily takes control of nearby enemies, making them fight for you while draining their will to resist your psychic domination.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'mind_control',
        effectValue: 10, // Duration in seconds
        cardType: 'control',
        voxelModel: 'mind_control_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9370DB, // Medium purple mind glow
            intensity: 1.7
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'mind_control'
    },

    reality_tear: {
        name: 'Reality Tear',
        description: 'Rips a hole in the fabric of reality, creating a dangerous rift that pulls enemies into the void while warping space around you.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'reality_tear',
        effectValue: 8, // Duration in seconds
        cardType: 'chaos',
        voxelModel: 'reality_tear_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x000080, // Navy blue reality glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'chaotic',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'reality_tear'
    },

    phoenix_ascension: {
        name: 'Phoenix Ascension',
        description: 'Triggers a glorious rebirth that fully heals you, grants temporary flight, and causes your attacks to leave trails of purifying flames.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'phoenix_ascension',
        effectValue: 20, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'phoenix_ascension_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Orange red phoenix glow
            intensity: 2.3
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'phoenix_ascension'
    },

    void_mastery: {
        name: 'Void Mastery',
        description: 'Grants complete control over void energy, allowing you to phase at will, create void projectiles, and become one with the darkness.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'void_mastery',
        effectValue: 25, // Duration in seconds
        cardType: 'mastery',
        voxelModel: 'void_mastery_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2F004F, // Dark purple void glow
            intensity: 1.9
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'void_mastery'
    },

    elemental_avatar: {
        name: 'Elemental Avatar',
        description: 'Transforms you into a living embodiment of the elements, cycling through fire, water, earth, and air forms with unique powers for each.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'elemental_avatar',
        effectValue: 30, // Duration in seconds
        cardType: 'transformation',
        voxelModel: 'elemental_avatar_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x32CD32, // Lime green elemental glow
            intensity: 2.0
        },
        blockedBy: [],
        soulWeightInfluence: 'balanced',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'elemental_avatar'
    },

    // Set 5 Cards
    shadow_realm: {
        name: 'Shadow Realm',
        description: 'Opens a gateway to the shadow dimension, allowing you to command shadow creatures and manipulate darkness itself to confuse and terrify enemies.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'shadow_realm',
        effectValue: 18, // Duration in seconds
        cardType: 'dimensional',
        voxelModel: 'shadow_realm_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2F2F2F, // Dark gray shadow glow
            intensity: 2.1
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'shadow_realm'
    },

    temporal_mastery: {
        name: 'Temporal Mastery',
        description: 'Grants complete control over time itself, allowing you to slow enemies, accelerate your actions, and create temporal echoes of your attacks.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'temporal_mastery',
        effectValue: 22, // Duration in seconds
        cardType: 'temporal',
        voxelModel: 'temporal_mastery_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4682B4, // Steel blue temporal glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'temporal_mastery'
    },

    cosmic_convergence: {
        name: 'Cosmic Convergence',
        description: 'Channels the power of celestial bodies to rain down cosmic energy, creating a devastating area of stellar destruction around you.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'cosmic_convergence',
        effectValue: 15, // Duration in seconds
        cardType: 'cosmic',
        voxelModel: 'cosmic_convergence_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9400D3, // Dark violet cosmic glow
            intensity: 2.4
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'cosmic_convergence'
    },

    soul_dominion: {
        name: 'Soul Dominion',
        description: 'Establishes absolute dominion over all souls in the area, draining life from enemies while empowering yourself with their spiritual essence.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.EPIC,
        effect: 'soul_dominion',
        effectValue: 20, // Duration in seconds
        cardType: 'dominion',
        voxelModel: 'soul_dominion_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x800080, // Purple soul glow
            intensity: 1.9
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0x9932CC, // Dark orchid border (epic)
        cardAnimation: 'soul_dominion'
    },

    chaos_incarnate: {
        name: 'Chaos Incarnate',
        description: 'Becomes the living embodiment of pure chaos, causing random magical effects to constantly occur while making you unpredictable and extremely dangerous.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'chaos_incarnate',
        effectValue: 25, // Duration in seconds
        cardType: 'chaos',
        voxelModel: 'chaos_incarnate_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF1493, // Deep pink chaos glow
            intensity: 2.5
        },
        blockedBy: [],
        soulWeightInfluence: 'chaotic',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'chaos_incarnate'
    },
    
    // World-Changing Spells (Set 6)
    firesnake: {
        name: 'Firesnake',
        description: 'Summons a massive serpent of living flame that slithers across the battlefield, burning everything in its path and reshaping the terrain with molten trails.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'firesnake',
        effectValue: 20,
        cardType: 'world_transformation',
        voxelModel: 'firesnake_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Blazing orange fire glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'chaotic',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'firesnake'
    },
    
    curse_of_midas: {
        name: 'Curse of Midas',
        description: 'Transforms everything touched into gold - enemies become golden statues, walls turn to precious metal, and the entire battlefield becomes a glittering treasure chamber.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'curse_of_midas',
        effectValue: 15,
        cardType: 'world_transformation',
        voxelModel: 'curse_of_midas_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Golden transformation glow
            intensity: 2.4
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'curse_of_midas'
    },
    
    basilisk: {
        name: 'Basilisk',
        description: 'Summons an ancient basilisk whose petrifying gaze turns enemies to stone and whose venomous presence corrupts the environment, spreading toxic pools and stone gardens.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'basilisk',
        effectValue: 18,
        cardType: 'world_transformation',
        voxelModel: 'basilisk_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9ACD32, // Venomous green glow
            intensity: 2.3
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Flame orange border (legendary)
        cardAnimation: 'basilisk'
    },

    // Alchemical Formulae (Set 7) - Environmental Transmutation
    eternal_winters_embrace: {
        name: "Eternal Winter's Embrace",
        description: 'Transmutes all surfaces into perpetual ice for 25 seconds. All movement becomes slippery, enemies move 50% slower, crystalline barriers form.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'eternal_winters_embrace',
        effectValue: 25,
        cardType: 'world_transformation',
        voxelModel: 'eternal_winters_embrace_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x87CEEB, // Ice blue glow
            intensity: 2.4
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'eternal_winters_embrace'
    },

    earths_sundering_formula: {
        name: "Earth's Sundering Formula",
        description: 'Catalyzes massive geological fractures that reshape terrain. Splits rooms into sections, creates impassable chasms, enemies fall into depths.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'earths_sundering_formula',
        effectValue: 20,
        cardType: 'world_transformation',
        voxelModel: 'earths_sundering_formula_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8B4513, // Earth brown glow
            intensity: 2.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'earths_sundering_formula'
    },

    verdant_overgrowth_elixir: {
        name: 'Verdant Overgrowth Elixir',
        description: 'Accelerates plant growth beyond natural limits through bio-alchemy. Creates living walls, entangles enemies, generates healing herb patches.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'verdant_overgrowth_elixir',
        effectValue: 22,
        cardType: 'world_transformation',
        voxelModel: 'verdant_overgrowth_elixir_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x32CD32, // Lime green glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'verdant_overgrowth_elixir'
    },

    gravitational_inversion_catalyst: {
        name: 'Gravitational Inversion Catalyst',
        description: 'Alters fundamental forces through celestial alchemy. Enemies float helplessly or get crushed, projectiles follow impossible arcs.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'gravitational_inversion_catalyst',
        effectValue: 18,
        cardType: 'world_transformation',
        voxelModel: 'gravitational_inversion_catalyst_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9370DB, // Medium purple glow
            intensity: 2.6
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'gravitational_inversion_catalyst'
    },

    ethereal_phase_transmutation: {
        name: 'Ethereal Phase Transmutation',
        description: 'Shifts matter between physical and spectral states. Creates phasing barriers that trap or release enemies unpredictably.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'ethereal_phase_transmutation',
        effectValue: 16,
        cardType: 'world_transformation',
        voxelModel: 'ethereal_phase_transmutation_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xE6E6FA, // Lavender glow
            intensity: 2.3
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'ethereal_phase_transmutation'
    },

    // Primordial Element Mastery
    storm_callers_grand_working: {
        name: "Storm-Caller's Grand Working",
        description: 'Binds atmospheric electricity through storm alchemy. Constant lightning strikes, metal conducts deadly current, electromagnetic chaos.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'storm_callers_grand_working',
        effectValue: 24,
        cardType: 'world_transformation',
        voxelModel: 'storm_callers_grand_working_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x1E90FF, // Dodger blue glow
            intensity: 2.7
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'storm_callers_grand_working'
    },

    magma_heart_awakening: {
        name: 'Magma Heart Awakening',
        description: 'Channels earth\'s molten core through volcanic alchemy. Lava rivers reshape battlefield, persistent fire damage, illuminates darkness.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'magma_heart_awakening',
        effectValue: 26,
        cardType: 'world_transformation',
        voxelModel: 'magma_heart_awakening_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFF4500, // Orange red glow
            intensity: 2.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'magma_heart_awakening'
    },

    absolute_zero_distillation: {
        name: 'Absolute Zero Distillation',
        description: 'Extracts all thermal energy through cryogenic alchemy. Flash-freezes enemies solid, creates ice ammunition, halts all motion.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'absolute_zero_distillation',
        effectValue: 20,
        cardType: 'world_transformation',
        voxelModel: 'absolute_zero_distillation_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xB0E0E6, // Powder blue glow
            intensity: 2.4
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'absolute_zero_distillation'
    },

    zephyrs_binding_ritual: {
        name: "Zephyr's Binding Ritual",
        description: 'Commands wind spirits through aerial alchemy. Tornado vortexes redirect attacks, provide lift, scatter formations.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'zephyrs_binding_ritual',
        effectValue: 19,
        cardType: 'world_transformation',
        voxelModel: 'zephyrs_binding_ritual_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xF0F8FF, // Alice blue glow
            intensity: 2.1
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'zephyrs_binding_ritual'
    },

    plasma_genesis_chamber: {
        name: 'Plasma Genesis Chamber',
        description: 'Artificially creates fourth state of matter through advanced alchemy. Ionizes air, disrupts magical fields, creates chain reactions.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'plasma_genesis_chamber',
        effectValue: 22,
        cardType: 'world_transformation',
        voxelModel: 'plasma_genesis_chamber_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x00FFFF, // Cyan glow
            intensity: 2.9
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'plasma_genesis_chamber'
    },

    // Reality Reconstruction Arts
    temporal_fracturing_solution: {
        name: 'Temporal Fracturing Solution',
        description: 'Dissolves linear time flow through chronological alchemy. Time dilation zones affect all entities differently.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'temporal_fracturing_solution',
        effectValue: 15,
        cardType: 'world_transformation',
        voxelModel: 'temporal_fracturing_solution_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xFFD700, // Gold glow
            intensity: 2.5
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'temporal_fracturing_solution'
    },

    mirror_realm_synthesis: {
        name: 'Mirror Realm Synthesis',
        description: 'Duplicates reality through reflective alchemy. Mirror enemies attack from impossible angles, reverses battlefield layout.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'mirror_realm_synthesis',
        effectValue: 21,
        cardType: 'world_transformation',
        voxelModel: 'mirror_realm_synthesis_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xC0C0C0, // Silver glow
            intensity: 2.3
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'mirror_realm_synthesis'
    },

    void_aperture_creation: {
        name: 'Void Aperture Creation',
        description: 'Opens controlled breaches to the null dimension. Permanent enemy removal, projectile negation, creates safe vacuum zones.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'void_aperture_creation',
        effectValue: 17,
        cardType: 'world_transformation',
        voxelModel: 'void_aperture_creation_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x2F2F2F, // Dark gray glow
            intensity: 3.0
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'void_aperture_creation'
    },

    reality_binding_anchors: {
        name: 'Reality Binding Anchors',
        description: 'Stabilizes magical fields through metaphysical alchemy. Nullifies supernatural abilities, grounds ethereal entities.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'reality_binding_anchors',
        effectValue: 14,
        cardType: 'world_transformation',
        voxelModel: 'reality_binding_anchors_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0xDAA520, // Goldenrod glow
            intensity: 2.2
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'reality_binding_anchors'
    },

    dimensional_confluence_brew: {
        name: 'Dimensional Confluence Brew',
        description: 'Merges multiple planes of existence through planar alchemy. Random terrain manifestation, cross-dimensional enemy spawning.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'dimensional_confluence_brew',
        effectValue: 23,
        cardType: 'world_transformation',
        voxelModel: 'dimensional_confluence_brew_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x9932CC, // Dark orchid glow
            intensity: 2.6
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'dimensional_confluence_brew'
    },

    // Strategic Transmutation Workings
    fortress_manifestation_ritual: {
        name: 'Fortress Manifestation Ritual',
        description: 'Instant stone transmutation through architectural alchemy. Creates defensive structures, elevated positions, choke points.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'fortress_manifestation_ritual',
        effectValue: 30,
        cardType: 'world_transformation',
        voxelModel: 'fortress_manifestation_ritual_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x696969, // Dim gray glow
            intensity: 2.1
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'fortress_manifestation_ritual'
    },

    shadow_legion_summoning: {
        name: 'Shadow Legion Summoning',
        description: 'Binds shadow essence into warrior forms through necromantic alchemy. Allied shadow army with specialized unit types.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'shadow_legion_summoning',
        effectValue: 12,
        cardType: 'world_transformation',
        voxelModel: 'shadow_legion_summoning_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x4B0082, // Indigo glow
            intensity: 2.4
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'shadow_legion_summoning'
    },

    arcane_siege_engine_formula: {
        name: 'Arcane Siege Engine Formula',
        description: 'Crystallizes raw magic into autonomous weapons through military alchemy. Self-targeting magical artillery with sustained fire.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'arcane_siege_engine_formula',
        effectValue: 28,
        cardType: 'world_transformation',
        voxelModel: 'arcane_siege_engine_formula_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x8A2BE2, // Blue violet glow
            intensity: 2.7
        },
        blockedBy: [],
        soulWeightInfluence: 'neutral',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'arcane_siege_engine_formula'
    },

    spatial_gateway_network: {
        name: 'Spatial Gateway Network',
        description: 'Folds space through dimensional alchemy. Instant transportation hubs with tactical repositioning.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'spatial_gateway_network',
        effectValue: 16,
        cardType: 'world_transformation',
        voxelModel: 'spatial_gateway_network_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x7B68EE, // Medium slate blue glow
            intensity: 2.5
        },
        blockedBy: [],
        soulWeightInfluence: 'light',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'spatial_gateway_network'
    },

    deaths_reversal_theorem: {
        name: "Death's Reversal Theorem",
        description: 'Inverts the life-death equation through necromantic alchemy. Reanimates fallen enemies as loyal undead servants.',
        category: ITEM_CATEGORY.CARD,
        rarity: ITEM_RARITY.LEGENDARY,
        effect: 'deaths_reversal_theorem',
        effectValue: 25,
        cardType: 'world_transformation',
        voxelModel: 'deaths_reversal_theorem_card',
        allowedBiomes: ['any'],
        allowedRoomTypes: ['any'],
        glow: {
            color: 0x800080, // Purple glow
            intensity: 2.8
        },
        blockedBy: [],
        soulWeightInfluence: 'dark',
        cardBorderColor: 0xFF6B35, // Legendary border
        cardAnimation: 'deaths_reversal_theorem'
    }
};

// --- Helper Functions ---

/**
 * Get item data by type
 * @param {string} itemType - The type of item to get data for
 * @returns {object|null} The item data or null if not found
 */
export function getItemData(itemType) {
    const data = ITEM_DATA[itemType];
    if (!data) {
        console.warn(`Item type "${itemType}" not found!`);
        return null;
    }
    // Return a copy to prevent accidental modification
    return { ...data };
}

/**
 * Get all items of a specific rarity
 * @param {string} rarity - The rarity to filter by
 * @returns {Array} Array of item types matching the rarity
 */
export function getItemsByRarity(rarity) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => data.rarity === rarity)
        .map(([type, _]) => type);
}

/**
 * Get all items of a specific category
 * @param {string} category - The category to filter by
 * @returns {Array} Array of item types matching the category
 */
export function getItemsByCategory(category) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => data.category === category)
        .map(([type, _]) => type);
}

/**
 * Get all items allowed in a specific biome
 * @param {string} biome - The biome to filter by
 * @returns {Array} Array of item types allowed in the biome
 */
export function getItemsByBiome(biome) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => 
            data.allowedBiomes.includes(biome) || 
            data.allowedBiomes.includes('any')
        )
        .map(([type, _]) => type);
}

/**
 * Get all items allowed in a specific room type
 * @param {string} roomType - The room type to filter by
 * @returns {Array} Array of item types allowed in the room type
 */
export function getItemsByRoomType(roomType) {
    return Object.entries(ITEM_DATA)
        .filter(([_, data]) => 
            data.allowedRoomTypes.includes(roomType) || 
            data.allowedRoomTypes.includes('any')
        )
        .map(([type, _]) => type);
}

// Make the main definitions read-only
Object.freeze(ITEM_DATA);
Object.freeze(ITEM_RARITY);
Object.freeze(ITEM_CATEGORY);
Object.freeze(ITEM_TYPES);
