import { BaseCutscene } from '../BaseCutscene.js';

/**
 * First-Person Inspection Cutscene
 * 
 * A simple cutscene where the player stays in place but the camera
 * moves to first-person and looks around. Useful for examining objects
 * or showing the player something specific.
 */
export class FirstPersonInspectionCutscene extends BaseCutscene {
    constructor() {
        super();
        this.id = 'first_person_inspection';
        this.name = 'First-Person Inspection';
        this.description = 'Player examines surroundings in first-person';
    }
    
    getConfig(context) {
        // Get inspection parameters from context
        const lookAt = context.lookAt || { x: 0, y: 0 }; // Camera rotation target
        const duration = context.duration || 3.0; // How long to look
        const returnToThirdPerson = context.returnToThirdPerson !== false; // Default true
        
        // Create a simple path that keeps player in place
        const currentPos = context.startPosition || { x: 0, y: 0.3, z: 0 };
        const path = [
            currentPos,
            currentPos, // Stay in place
            currentPos  // Still in place
        ];
        
        // Camera modes
        const cameraModes = [
            'first-person',
            'first-person',
            returnToThirdPerson ? 'third-person' : 'first-person'
        ];
        
        // Camera rotations - smoothly look at target
        const cameraRotations = [
            { x: 0, y: 0 },              // Start looking forward
            lookAt,                       // Look at target
            returnToThirdPerson ? null : lookAt  // Hold or return
        ];
        
        return {
            mode: 'first-person',
            path: path,
            cameraModes: cameraModes,
            cameraRotations: cameraRotations,
            speed: 1.0, // Doesn't matter since we're not moving
            onComplete: () => {
                if (context.onCutsceneComplete) {
                    context.onCutsceneComplete();
                }
            }
        };
    }
    
    onStart(context) {
        super.onStart(context);
        if (context.message) {
            console.log(`[${this.id}] ${context.message}`);
        }
    }
}

// Export a helper function to quickly create inspection cutscenes
export function createInspectionCutscene(lookAt, options = {}) {
    const cutscene = new FirstPersonInspectionCutscene();
    return {
        play: (dungeonHandler) => {
            const context = {
                lookAt: lookAt,
                duration: options.duration || 3.0,
                returnToThirdPerson: options.returnToThirdPerson !== false,
                startPosition: options.startPosition,
                message: options.message,
                onCutsceneComplete: options.onComplete
            };
            cutscene.play(dungeonHandler, context);
        }
    };
}