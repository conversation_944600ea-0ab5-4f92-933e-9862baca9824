import { BaseCutscene } from '../BaseCutscene.js';

/**
 * Catacombs Boss Room Entrance Cutscene
 * 
 * Simple entrance where the player walks from the door to the center 
 * of the boss arena in third-person view.
 */
export class CatacombsBossEntranceCutscene extends BaseCutscene {
    constructor() {
        super();
        this.id = 'catacombs_boss_entrance';
        this.name = 'Catacombs Boss Entrance';
        this.description = 'Dramatic entrance into the catacombs boss chamber';
    }
    
    getConfig(context) {
        // Define the path - walk from door to center of room
        const path = [
            { x: 0, y: 0.3, z: 25 },      // Near the south door
            { x: 0, y: 0.3, z: 15 },      // Moving toward center
            { x: 0.27, y: 0.3, z: 8.2 }   // Center position
        ];
        
        return {
            mode: 'third-person', // Stay in third-person the whole time
            path: path,
            speed: 2.5, // Slightly slower for dramatic effect
            onComplete: () => {
                // Trigger any post-cutscene logic
                if (context.onCutsceneComplete) {
                    context.onCutsceneComplete();
                }
            }
        };
    }
    
    onStart(context) {
        super.onStart(context);
        
        // Optional: Add atmospheric music or sound effects
        console.log(`[${this.id}] The ancient chamber awaits...`);
    }
    
    onComplete(context) {
        super.onComplete(context);
        
        // Optional: Spawn the boss or trigger next event
        console.log(`[${this.id}] Ready for battle!`);
    }
}