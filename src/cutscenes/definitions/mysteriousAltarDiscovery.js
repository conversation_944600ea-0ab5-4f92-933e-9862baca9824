import { BaseCutscene } from '../BaseCutscene.js';

/**
 * Mysterious Altar Discovery Cutscene
 * 
 * Used when discovering the mysterious altar event room.
 * Player walks to the altar, camera switches to first-person
 * to examine it closely, then returns to third-person.
 */
export class MysteriousAltarDiscoveryCutscene extends BaseCutscene {
    constructor() {
        super();
        this.id = 'mysterious_altar_discovery';
        this.name = 'Mysterious Altar Discovery';
        this.description = 'Player discovers and examines a mysterious altar';
    }
    
    getConfig(context) {
        // Path from door to altar
        const path = [
            { x: 0, y: 0.3, z: 20 },     // Start near door
            { x: 0, y: 0.3, z: 10 },     // Walk forward
            { x: 0, y: 0.3, z: -2 },     // Approach altar
            { x: 0, y: 0.3, z: -2 },     // Stay at altar (examine)
            { x: -3, y: 0.3, z: -2 },    // Walk around altar
            { x: -3, y: 0.3, z: 2 },     // Continue circling
            { x: 0, y: 0.3, z: 5 }       // Step back to center
        ];
        
        // Switch between camera modes for dramatic effect
        const cameraModes = [
            'third-person',   // Walking in
            'third-person',   // Continue approach
            'first-person',   // Switch to examine altar closely
            'first-person',   // Look around
            'third-person',   // Back to third for movement
            'third-person',   // Continue movement
            'third-person'    // Final position
        ];
        
        // Camera rotations for first-person examination
        const cameraRotations = [
            null,                           // Third-person
            null,                           // Third-person
            { x: -0.2, y: 0 },             // Look slightly down at altar
            { x: 0.1, y: Math.PI / 4 },    // Look up and to the right
            null,                           // Third-person
            null,                           // Third-person
            null                            // Third-person
        ];
        
        return {
            mode: 'third-person',
            path: path,
            cameraModes: cameraModes,
            cameraRotations: cameraRotations,
            speed: 2.0, // Slow, cautious approach
            onComplete: () => {
                if (context.onCutsceneComplete) {
                    context.onCutsceneComplete();
                }
            }
        };
    }
    
    onStart(context) {
        super.onStart(context);
        console.log(`[${this.id}] A mysterious presence fills the room...`);
    }
    
    onComplete(context) {
        super.onComplete(context);
        console.log(`[${this.id}] The altar awaits your decision.`);
    }
}