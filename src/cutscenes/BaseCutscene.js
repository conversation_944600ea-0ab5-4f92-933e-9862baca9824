/**
 * Base Cutscene Class
 * 
 * All cutscene definitions should extend this class.
 * Provides a standard structure for cutscene data.
 */
export class BaseCutscene {
    constructor() {
        this.id = 'base_cutscene';
        this.name = 'Base Cutscene';
        this.description = 'Base cutscene template';
    }
    
    /**
     * Get the cutscene configuration
     * Override this method in child classes
     * @param {Object} context - Context object with dungeonHandler, room data, etc.
     * @returns {Object} Cutscene configuration
     */
    getConfig(context = {}) {
        return {
            mode: 'third-person',
            path: [
                { x: 0, y: 0.3, z: 0 }
            ],
            speed: 3.0,
            onComplete: () => {
                console.log(`[${this.id}] Cutscene complete`);
            }
        };
    }
    
    /**
     * Optional: Called before the cutscene starts
     * @param {Object} context - Context object
     */
    onStart(context) {
        console.log(`[${this.id}] Starting cutscene: ${this.name}`);
    }
    
    /**
     * Optional: Called after the cutscene completes
     * @param {Object} context - Context object
     */
    onComplete(context) {
        console.log(`[${this.id}] Completed cutscene: ${this.name}`);
    }
    
    /**
     * Play this cutscene
     * @param {Object} dungeonHandler - The dungeon handler instance
     * @param {Object} additionalContext - Additional context data
     */
    play(dungeonHandler, additionalContext = {}) {
        const cutsceneSystem = dungeonHandler.cutsceneSystem;
        if (!cutsceneSystem) {
            console.error(`[${this.id}] No cutscene system available`);
            return;
        }
        
        // Create context object
        const context = {
            dungeonHandler,
            cutsceneSystem,
            ...additionalContext
        };
        
        // Call onStart
        this.onStart(context);
        
        // Get configuration
        const config = this.getConfig(context);
        
        // Wrap onComplete to call our method
        const originalOnComplete = config.onComplete;
        config.onComplete = () => {
            if (originalOnComplete) {
                originalOnComplete();
            }
            this.onComplete(context);
        };
        
        // Play the cutscene
        cutsceneSystem.play(config);
    }
}