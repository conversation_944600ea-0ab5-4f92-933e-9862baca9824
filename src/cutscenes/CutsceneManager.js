/**
 * Cutscene Manager
 * 
 * Manages loading and playing of cutscene definitions.
 * Provides a central registry for all available cutscenes.
 */
export class CutsceneManager {
    constructor() {
        this.cutscenes = new Map();
        this.loadedModules = new Map();
        
        console.log('[CutsceneManager] Initialized');
    }
    
    /**
     * Register a cutscene definition
     * @param {string} id - Unique cutscene ID
     * @param {string} modulePath - Path to the cutscene module
     */
    register(id, modulePath) {
        this.cutscenes.set(id, modulePath);
        console.log(`[CutsceneManager] Registered cutscene: ${id}`);
    }
    
    /**
     * Load and play a cutscene by ID
     * @param {string} id - Cutscene ID
     * @param {Object} dungeonHandler - Dungeon handler instance
     * @param {Object} context - Additional context for the cutscene
     */
    async play(id, dungeonHandler, context = {}) {
        console.log(`[CutsceneManager] Playing cutscene: ${id}`);
        
        // Check if cutscene is registered
        if (!this.cutscenes.has(id)) {
            console.error(`[CutsceneManager] Cutscene not found: ${id}`);
            return;
        }
        
        try {
            // Load the cutscene module if not already loaded
            let cutsceneInstance = this.loadedModules.get(id);
            
            if (!cutsceneInstance) {
                const modulePath = this.cutscenes.get(id);
                const module = await import(modulePath);
                
                // Find the cutscene class (first exported class or default export)
                const CutsceneClass = module.default || Object.values(module)[0];
                
                if (!CutsceneClass) {
                    throw new Error(`No cutscene class found in module: ${modulePath}`);
                }
                
                // Create instance
                cutsceneInstance = new CutsceneClass();
                this.loadedModules.set(id, cutsceneInstance);
                
                console.log(`[CutsceneManager] Loaded cutscene module: ${id}`);
            }
            
            // Play the cutscene
            cutsceneInstance.play(dungeonHandler, context);
            
        } catch (error) {
            console.error(`[CutsceneManager] Failed to play cutscene ${id}:`, error);
        }
    }
    
    /**
     * Get a list of all registered cutscenes
     * @returns {Array} Array of cutscene IDs
     */
    getRegisteredCutscenes() {
        return Array.from(this.cutscenes.keys());
    }
}

// Create singleton instance
export const cutsceneManager = new CutsceneManager();

// Register built-in cutscenes
cutsceneManager.register('catacombs_boss_entrance', '../cutscenes/definitions/catacombsBossEntrance.js');
cutsceneManager.register('mysterious_altar_discovery', '../cutscenes/definitions/mysteriousAltarDiscovery.js');