/**
 * Voxel Shadow Manager
 * Manages voxel-optimized shadow mapping with cascade shadows and mobile performance scaling
 */
import * as THREE from 'three';
import { VoxelShadowShader } from '../shaders/VoxelShadowShader.js';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js';

class VoxelShadowManager {
    constructor(renderer, scene, camera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;

        // Shadow configuration
        this.enabled = true;
        this.cascadeCount = 0; // Disable cascades for now, use simple shadows
        this.shadowMapSize = this._determineShadowMapSize();
        this.shadowDistance = 100.0;
        this.shadowIntensity = 0.8;
        this.shadowHardness = 1.0; // Full hard edges for voxel aesthetic

        // Hybrid lighting modes
        this.lightingMode = 'hybrid'; // 'original', 'voxel', 'hybrid'
        this.originalMaterialBlend = 0.7; // How much original lighting to blend in (0.0 = none, 1.0 = full)

        // Performance settings
        this.isMobile = this._detectMobile();
        this.shadowQuality = this.isMobile ? 0.5 : 1.0;

        // Cascade splits (near, mid, far)
        this.cascadeSplits = [8.0, 25.0, 100.0];

        // Shadow cameras and render targets
        this.shadowCameras = [];
        this.shadowRenderTargets = [];
        this.shadowMatrices = [];

        // Main directional light for shadows
        this.mainLight = null;

        // Voxel-specific settings
        this.voxelSize = VOXEL_SIZE;
        this.voxelBiasMultiplier = 2.0;

        this._initializeShadowSystem();

        console.log(`[VoxelShadowManager] Initialized with ${this.cascadeCount} cascades, quality: ${this.shadowQuality}, mobile: ${this.isMobile}`);
    }

    /**
     * Detect if running on mobile device
     */
    _detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (window.innerWidth <= 768 && window.innerHeight <= 1024);
    }

    /**
     * Determine appropriate shadow map size based on device capabilities
     */
    _determineShadowMapSize() {
        if (this._detectMobile()) {
            return 512; // Lower resolution for mobile
        } else {
            // Check WebGL capabilities
            const gl = this.renderer.getContext();
            const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);

            if (maxTextureSize >= 4096) {
                return 1024; // Reduced from 2048 for better performance
            } else {
                return 1024; // Medium quality fallback
            }
        }
    }

    /**
     * Initialize the cascade shadow mapping system
     */
    _initializeShadowSystem() {
        // Create shadow cameras and render targets for each cascade
        for (let i = 0; i < this.cascadeCount; i++) {
            // Create orthographic camera for this cascade
            const shadowCamera = new THREE.OrthographicCamera(-50, 50, 50, -50, 0.1, this.cascadeSplits[i]);
            this.shadowCameras.push(shadowCamera);

            // Create render target for this cascade
            const renderTarget = new THREE.WebGLRenderTarget(this.shadowMapSize, this.shadowMapSize, {
                minFilter: THREE.NearestFilter, // Use nearest for hard edges
                magFilter: THREE.NearestFilter,
                format: THREE.RGBAFormat,
                type: THREE.FloatType
            });
            this.shadowRenderTargets.push(renderTarget);

            // Create shadow matrix
            this.shadowMatrices.push(new THREE.Matrix4());
        }

        // Configure renderer for voxel shadows
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.BasicShadowMap; // Use basic for hard edges
        this.renderer.shadowMap.autoUpdate = false; // Manual control for performance

        console.log(`[VoxelShadowManager] Created ${this.cascadeCount} shadow cascades with ${this.shadowMapSize}x${this.shadowMapSize} resolution`);
    }

    /**
     * Set the main directional light for shadow casting
     */
    setMainLight(light) {
        this.mainLight = light;

        // Configure light for voxel shadows
        light.castShadow = true;
        light.shadow.mapSize.width = this.shadowMapSize;
        light.shadow.mapSize.height = this.shadowMapSize;
        light.shadow.camera.near = 0.1;
        light.shadow.camera.far = this.shadowDistance;

        // Use voxel-aware bias
        light.shadow.bias = -0.0005 * this.voxelBiasMultiplier;
        light.shadow.normalBias = 0.02;

        console.log(`[VoxelShadowManager] Configured main light for voxel shadows`);
    }

    /**
     * Update shadow cameras based on main camera position and light direction
     */
    updateShadowCameras() {
        if (!this.mainLight || !this.enabled || this.cascadeCount === 0) return;

        const lightDirection = this.mainLight.position.clone().normalize();
        const cameraPosition = this.camera.position.clone();
        const cameraForward = new THREE.Vector3(0, 0, -1).applyQuaternion(this.camera.quaternion);

        // Update each cascade
        for (let i = 0; i < this.cascadeCount; i++) {
            const shadowCamera = this.shadowCameras[i];
            const cascadeDistance = this.cascadeSplits[i];

            // Calculate cascade center based on camera position and forward direction
            const cascadeCenter = cameraPosition.clone().add(
                cameraForward.clone().multiplyScalar(cascadeDistance * 0.5)
            );

            // Position shadow camera
            shadowCamera.position.copy(cascadeCenter).add(
                lightDirection.clone().multiplyScalar(-cascadeDistance)
            );
            shadowCamera.lookAt(cascadeCenter);

            // Adjust orthographic camera size based on cascade distance
            const size = cascadeDistance * 0.8;
            shadowCamera.left = -size;
            shadowCamera.right = size;
            shadowCamera.top = size;
            shadowCamera.bottom = -size;
            shadowCamera.far = cascadeDistance * 2;

            shadowCamera.updateProjectionMatrix();

            // Update shadow matrix for shader
            const shadowMatrix = this.shadowMatrices[i];
            shadowMatrix.multiplyMatrices(shadowCamera.projectionMatrix, shadowCamera.matrixWorldInverse);
        }
    }

    /**
     * Render shadow maps for all cascades
     */
    renderShadowMaps() {
        if (!this.mainLight || !this.enabled || this.cascadeCount === 0) return;

        // Store original renderer state
        const originalRenderTarget = this.renderer.getRenderTarget();
        const originalClearColor = this.renderer.getClearColor(new THREE.Color());
        const originalClearAlpha = this.renderer.getClearAlpha();

        // Set up for shadow rendering
        this.renderer.setClearColor(0xffffff, 1.0);

        // Render each cascade
        for (let i = 0; i < this.cascadeCount; i++) {
            this.renderer.setRenderTarget(this.shadowRenderTargets[i]);
            this.renderer.clear();
            this.renderer.render(this.scene, this.shadowCameras[i]);
        }

        // Restore original renderer state
        this.renderer.setRenderTarget(originalRenderTarget);
        this.renderer.setClearColor(originalClearColor, originalClearAlpha);
    }

    /**
     * Apply voxel shadow material to a mesh
     */
    applyVoxelShadowMaterial(mesh, originalMaterial) {
        // Clone the shader uniforms to avoid shared references
        const clonedUniforms = THREE.UniformsUtils.clone(VoxelShadowShader.uniforms);

        // Override with our settings
        clonedUniforms.voxelSize.value = this.voxelSize;
        clonedUniforms.shadowHardness.value = this.shadowHardness;
        clonedUniforms.shadowQuality.value = this.shadowQuality;
        clonedUniforms.shadowIntensity.value = this.shadowIntensity;
        clonedUniforms.cascadeCount.value = this.cascadeCount;
        clonedUniforms.cascadeSplits.value = this.cascadeSplits;

        // Set cascade textures and matrices with safety checks
        clonedUniforms.cascadeMap0.value = (this.shadowRenderTargets[0] && this.shadowRenderTargets[0].texture) ? this.shadowRenderTargets[0].texture : null;
        clonedUniforms.cascadeMap1.value = (this.shadowRenderTargets[1] && this.shadowRenderTargets[1].texture) ? this.shadowRenderTargets[1].texture : null;
        clonedUniforms.cascadeMap2.value = (this.shadowRenderTargets[2] && this.shadowRenderTargets[2].texture) ? this.shadowRenderTargets[2].texture : null;
        clonedUniforms.cascadeMatrix0.value.copy(this.shadowMatrices[0] || new THREE.Matrix4());
        clonedUniforms.cascadeMatrix1.value.copy(this.shadowMatrices[1] || new THREE.Matrix4());
        clonedUniforms.cascadeMatrix2.value.copy(this.shadowMatrices[2] || new THREE.Matrix4());

        // Create voxel shadow material
        const voxelShadowMaterial = new THREE.ShaderMaterial({
            uniforms: clonedUniforms,
            vertexShader: VoxelShadowShader.vertexShader,
            fragmentShader: VoxelShadowShader.fragmentShader
        });

        // Copy properties from original material
        if (originalMaterial.color) {
            clonedUniforms.diffuse.value.copy(originalMaterial.color);
        }
        if (originalMaterial.map) {
            clonedUniforms.map.value = originalMaterial.map;
            clonedUniforms.hasMap.value = 1.0;
        } else {
            clonedUniforms.hasMap.value = 0.0;
        }
        if (originalMaterial.opacity !== undefined) {
            clonedUniforms.opacity.value = originalMaterial.opacity;
        }
        if (originalMaterial.transparent !== undefined) {
            voxelShadowMaterial.transparent = originalMaterial.transparent;
        }

        mesh.material = voxelShadowMaterial;
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        return voxelShadowMaterial;
    }

    /**
     * Apply subtle voxel color enhancement while preserving original lighting
     */
    applySubtleVoxelEnhancement(mesh) {
        const originalMaterial = mesh.material;

        // Store reference to original material for potential restoration
        if (!mesh.userData.originalMaterial) {
            mesh.userData.originalMaterial = originalMaterial;
        }

        // Create enhanced material based on original type
        let enhancedMaterial;

        if (originalMaterial.isMeshLambertMaterial || originalMaterial.isMeshStandardMaterial) {
            // Clone the original material to preserve all properties
            enhancedMaterial = originalMaterial.clone();

            // Apply subtle voxel-style color modifications
            this._applyVoxelColorEnhancement(enhancedMaterial, originalMaterial);

        } else if (originalMaterial.isMeshBasicMaterial) {
            // Convert basic material to Lambert for better lighting
            enhancedMaterial = new THREE.MeshLambertMaterial({
                color: originalMaterial.color,
                map: originalMaterial.map,
                transparent: originalMaterial.transparent,
                opacity: originalMaterial.opacity
            });

            this._applyVoxelColorEnhancement(enhancedMaterial, originalMaterial);
        } else {
            // For other material types, just ensure shadow casting
            enhancedMaterial = originalMaterial;
        }

        // Apply the enhanced material
        mesh.material = enhancedMaterial;
        mesh.castShadow = true;
        mesh.receiveShadow = true;

        return enhancedMaterial;
    }

    /**
     * Apply subtle color enhancements for voxel aesthetic
     */
    _applyVoxelColorEnhancement(material, originalMaterial) {
        // Slightly increase contrast and saturation for voxel look
        if (material.color) {
            const originalColor = originalMaterial.color.clone();

            // Convert to HSL for easier manipulation
            const hsl = {};
            originalColor.getHSL(hsl);

            // Subtle enhancements:
            // 1. Slightly increase saturation for more vibrant voxel colors
            hsl.s = Math.min(1.0, hsl.s * 1.1);

            // 2. Slightly adjust lightness based on original value
            if (hsl.l < 0.5) {
                // Darken dark colors slightly for better contrast
                hsl.l = hsl.l * 0.95;
            } else {
                // Brighten light colors slightly
                hsl.l = Math.min(1.0, hsl.l * 1.05);
            }

            // Apply the enhanced color
            material.color.setHSL(hsl.h, hsl.s, hsl.l);
        }

        // Enhance material properties for voxel aesthetic
        if (material.isMeshStandardMaterial) {
            // Slightly increase roughness for more matte voxel look
            material.roughness = Math.min(1.0, (material.roughness || 0.5) * 1.2);

            // Reduce metalness for more consistent voxel appearance
            material.metalness = (material.metalness || 0.0) * 0.8;
        }
    }

    /**
     * Update the shadow system (call each frame)
     */
    update() {
        if (!this.enabled) return;

        this.updateShadowCameras();
        this.renderShadowMaps();
    }

    /**
     * Enable/disable the shadow system
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log(`[VoxelShadowManager] Shadow system ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Adjust shadow quality (0.5 = mobile, 1.0 = desktop)
     */
    setShadowQuality(quality) {
        this.shadowQuality = Math.max(0.1, Math.min(1.0, quality));
        console.log(`[VoxelShadowManager] Shadow quality set to ${this.shadowQuality}`);
    }

    /**
     * Set shadow intensity (0.0 = no shadows, 1.0 = full shadows)
     */
    setShadowIntensity(intensity) {
        this.shadowIntensity = Math.max(0.0, Math.min(1.0, intensity));
        console.log(`[VoxelShadowManager] Shadow intensity set to ${this.shadowIntensity}`);
    }

    /**
     * Cleanup resources
     */
    dispose() {
        this.shadowRenderTargets.forEach(rt => rt.dispose());
        this.shadowRenderTargets = [];
        this.shadowCameras = [];
        this.shadowMatrices = [];
        console.log(`[VoxelShadowManager] Disposed shadow resources`);
    }
}

export { VoxelShadowManager };
