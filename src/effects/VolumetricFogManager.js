import * as THREE from 'three';

/**
 * Advanced Volumetric Fog System
 * Inspired by Unreal Engine 5's volumetric fog with realistic light scattering
 */
export class VolumetricFogSystem {
    constructor(scene, params = {}) {
        this.scene = scene;
        this.fogMeshes = [];
        this.lightShafts = [];
        
        // Default fog configuration
        this.config = {
            // Fog type presets
            preset: params.preset || 'swamp', // 'swamp', 'cave', 'mystical', 'dungeon', 'custom'
            
            // Basic fog properties
            color: params.color || 0x4a6b8a,
            density: params.density || 0.02,
            height: params.height || 8,
            
            // Advanced fog properties
            scattering: params.scattering || 0.15,
            absorption: params.absorption || 0.1,
            anisotropy: params.anisotropy || 0.8, // Forward scattering for realism
            
            // Layer configuration
            layers: params.layers || 12,
            layerFalloff: params.layerFalloff || 2.0,
            
            // Light interaction
            lightIntensity: params.lightIntensity || 1.0,
            godrayIntensity: params.godrayIntensity || 0.3,
            
            // Animation
            windSpeed: params.windSpeed || 0.5,
            turbulence: params.turbulence || 0.3,
            
            // Performance
            quality: params.quality || 'high', // 'low', 'medium', 'high'
            
            // New: Custom masking and distribution
            customMask: params.customMask || null, // Function to exclude fog from certain areas
            organicDistribution: params.organicDistribution !== false, // More natural fog distribution
            pondAware: params.pondAware !== false // Special handling for pond rooms
        };
        
        // Apply preset if specified
        this.applyPreset(this.config.preset);
    }
    
    applyPreset(preset) {
        const presets = {
            swamp: {
                color: 0xC0C0C0, // Natural white-gray fog instead of green-tinted
                density: 0.018, // Reduced for more transparency
                scattering: 0.18,
                absorption: 0.12,
                anisotropy: 0.75,
                layers: 18,
                layerFalloff: 1.6,
                godrayIntensity: 0.35,
                turbulence: 0.45,
                organicDistribution: true,
                pondAware: true
            },
            cave: {
                color: 0x4a5f7a,
                density: 0.03,
                scattering: 0.1,
                absorption: 0.2,
                anisotropy: 0.85,
                layers: 10,
                layerFalloff: 2.2,
                godrayIntensity: 0.2,
                turbulence: 0.2
            },
            mystical: {
                color: 0x6a4f9f,
                density: 0.015,
                scattering: 0.25,
                absorption: 0.05,
                anisotropy: 0.9,
                layers: 20,
                layerFalloff: 1.5,
                godrayIntensity: 0.6,
                turbulence: 0.5
            },
            dungeon: {
                color: 0x5a5a5a,
                density: 0.02,
                scattering: 0.15,
                absorption: 0.1,
                anisotropy: 0.8,
                layers: 8,
                layerFalloff: 2.0,
                godrayIntensity: 0.3,
                turbulence: 0.3
            },
            // New: Natural pond mist preset
            pondMist: {
                color: 0xC4C4C4, // Natural white-gray fog
                density: 0.012, // Reduced for more transparency
                scattering: 0.22,
                absorption: 0.08,
                anisotropy: 0.7,
                layers: 15,
                layerFalloff: 1.4,
                godrayIntensity: 0.4,
                turbulence: 0.5,
                organicDistribution: true,
                pondAware: true
            }
        };
        
        if (presets[preset] && preset !== 'custom') {
            Object.assign(this.config, presets[preset]);
        }
    }
    
    createFogInRoom(roomGroup) {
    if (!roomGroup) return;
        
        // Get room bounds
        const roomBounds = new THREE.Box3().setFromObject(roomGroup);
        const roomSize = new THREE.Vector3();
        roomBounds.getSize(roomSize);
        
        // Detect if this is a pond room and set up custom masking
        if (this.config.pondAware) {
            this.setupPondMasking(roomGroup);
        }
        
        // Create fog layers with improved distribution
        this.createAdvancedFogLayers(roomGroup, roomSize);
        
        // Special handling for pond rooms - ensure fog covers hole area
        this.ensureFogInPondHole(roomGroup);
        
        // Create light shafts for godrays
        this.createLightShafts(roomGroup, roomSize);
        
        // Update scene fog for depth
        this.updateSceneFog();
        
        // Set up render order
        this.setupRenderOrder();
    }
    
    setupPondMasking(roomGroup) {
        // Find pond objects in the room
        const pondObjects = [];
        roomGroup.traverse(child => {
            if (child.userData?.objectType === 'glowing_pond' || 
                child.name === 'glowing_pond' ||
                child.userData?.isPond) {
                pondObjects.push(child);
            }
        });
        
        if (pondObjects.length > 0) {
            console.log(`[VolumetricFog] Found ${pondObjects.length} pond objects, but using uniform fog distribution`);
            
            // No masking - let fog cover everything uniformly including water
            this.config.customMask = null;
        }
    }
    
    ensureFogInPondHole(roomGroup) {
        // Check if this appears to be a pond room by looking for pond-related objects
        let isPondRoom = false;
        let pondCenter = new THREE.Vector3(0, 0, 0);
        
        roomGroup.traverse(child => {
            if (child.userData?.objectType === 'glowing_pond' || 
                child.name === 'glowing_pond' ||
                child.userData?.isPond ||
                child.name === 'pondHolePatch') {
                isPondRoom = true;
                pondCenter.copy(child.position);
            }
        });
        
        if (!isPondRoom) return;
        
        console.log(`[VolumetricFog] Adding specific fog coverage for pond hole area at`, pondCenter);
        
        // Create multiple fog layers specifically for the pond hole area
        const holeRadius = 3.5; // Slightly larger than the actual hole (3.0-3.2)
        const layerCount = 8;
        
        for (let i = 0; i < layerCount; i++) {
            const y = i * 0.8; // Create layers from ground level up to 6.4 units
            const opacity = this.config.density * (1.0 - i * 0.1); // Gradually fade with height
            
            // Create circular fog patch for the hole area
            const holeGeometry = new THREE.CircleGeometry(holeRadius, 24);
            
            const holeFogMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    fogColor: { value: new THREE.Color(this.config.color) },
                    opacity: { value: opacity },
                    time: { value: 0 },
                    windSpeed: { value: this.config.windSpeed },
                    turbulence: { value: this.config.turbulence }
                },
                vertexShader: `
                    varying vec3 vWorldPosition;
                    varying vec2 vUv;
                    varying float vDepth;
                    
                    void main() {
                        vUv = uv;
                        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                        vWorldPosition = worldPosition.xyz;
                        vec4 mvPosition = viewMatrix * worldPosition;
                        vDepth = -mvPosition.z;
                        gl_Position = projectionMatrix * mvPosition;
                    }
                `,
                fragmentShader: `
                    uniform vec3 fogColor;
                    uniform float opacity;
                    uniform float time;
                    uniform float windSpeed;
                    uniform float turbulence;
                    
                    varying vec3 vWorldPosition;
                    varying vec2 vUv;
                    varying float vDepth;
                    
                    float noise(vec2 p) {
                        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                    }
                    
                    float fbm(vec2 p) {
                        float value = 0.0;
                        float amplitude = 0.5;
                        for (int i = 0; i < 3; i++) {
                            value += amplitude * noise(p);
                            p *= 2.0;
                            amplitude *= 0.5;
                        }
                        return value;
                    }
                    
                    void main() {
                        vec2 animUv = vUv + vec2(time * windSpeed * 0.05, time * windSpeed * 0.03);
                        float fogNoise = fbm(animUv * 3.0 + time * 0.1) * turbulence;
                        
                        // Circular fade from center to edge
                        float dist = distance(vUv, vec2(0.5, 0.5)) * 2.0;
                        float circularFade = 1.0 - smoothstep(0.7, 1.0, dist);
                        
                        float finalOpacity = opacity * (0.7 + fogNoise * 0.3) * circularFade;
                        finalOpacity *= 1.0 - smoothstep(20.0, 50.0, vDepth);
                        
                        gl_FragColor = vec4(fogColor, finalOpacity);
                    }
                `,
                transparent: true,
                depthWrite: false,
                side: THREE.DoubleSide,
                blending: THREE.NormalBlending
            });
            
            const holeFogPatch = new THREE.Mesh(holeGeometry, holeFogMaterial);
            holeFogPatch.rotation.x = -Math.PI / 2; // Lay flat
            holeFogPatch.position.set(pondCenter.x, y, pondCenter.z);
            holeFogPatch.name = `PondHoleFog_L${i}`;
            
            roomGroup.add(holeFogPatch);
            this.fogMeshes.push(holeFogPatch);
        }
        
        console.log(`[VolumetricFog] Added ${layerCount} fog layers specifically for pond hole area`);
    }
    
    createAdvancedFogLayers(roomGroup, roomSize) {
        const layerCount = this.getLayerCount();
        const layerHeight = this.config.height / layerCount;
        
        for (let i = 0; i < layerCount; i++) {
            const t = i / (layerCount - 1);
            const y = t * this.config.height;
            
            // Calculate opacity with exponential falloff
            const falloff = Math.exp(-Math.pow(t * this.config.layerFalloff, 2));
            const baseOpacity = this.config.density * falloff;
            
            // Create organic fog distribution instead of uniform planes
            if (this.config.organicDistribution) {
                this.createOrganicFogLayer(roomGroup, roomSize, y, baseOpacity, i);
            } else {
                this.createUniformFogLayer(roomGroup, roomSize, y, baseOpacity, i);
            }
        }
    }
    
    createOrganicFogLayer(roomGroup, roomSize, y, baseOpacity, layerIndex) {
        // Create multiple smaller fog patches for more organic look
        const patchCount = 8 + Math.floor(Math.random() * 6); // 8-13 patches per layer
        const maxPatchSize = Math.max(roomSize.x, roomSize.z) * 0.6;
        const minPatchSize = maxPatchSize * 0.3;
        
        for (let p = 0; p < patchCount; p++) {
            // Random patch position within room bounds
            const patchX = (Math.random() - 0.5) * roomSize.x * 0.8;
            const patchZ = (Math.random() - 0.5) * roomSize.z * 0.8;
            const patchSize = minPatchSize + Math.random() * (maxPatchSize - minPatchSize);
            
            // Apply custom masking if available
            let maskValue = 1.0;
            if (this.config.customMask) {
                maskValue = this.config.customMask(patchX, patchZ, y);
            }
            
            // Skip this patch if masked out
            if (maskValue < 0.1) continue;
            
            // Create organic patch geometry
            const patchGeometry = new THREE.CircleGeometry(patchSize, 16);
            
            // Add organic vertex displacement
            const positions = patchGeometry.attributes.position;
            for (let v = 0; v < positions.count; v++) {
                const x = positions.getX(v);
                const z = positions.getY(v); // Note: CircleGeometry uses Y as the "forward" axis
                const noise = this.noise2D(x * 0.03 + layerIndex, z * 0.03 + layerIndex) * 0.3;
                const distance = Math.sqrt(x * x + z * z);
                const edgeFactor = Math.max(0, 1.0 - (distance / patchSize));
                positions.setZ(v, noise * edgeFactor);
            }
            patchGeometry.computeVertexNormals();
            
            // Create improved fog material with natural colors
            const fogMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    fogColor: { value: new THREE.Color(this.config.color) },
                    opacity: { value: baseOpacity * maskValue * (0.7 + Math.random() * 0.3) },
                    time: { value: 0 },
                    windSpeed: { value: this.config.windSpeed },
                    turbulence: { value: this.config.turbulence },
                    scattering: { value: this.config.scattering },
                    lightPositions: { value: [] },
                    lightColors: { value: [] },
                    lightCount: { value: 0 },
                    patchCenter: { value: new THREE.Vector2(patchX, patchZ) },
                    patchSize: { value: patchSize }
                },
                vertexShader: `
                    varying vec3 vWorldPosition;
                    varying vec2 vUv;
                    varying float vDepth;
                    varying float vDistanceFromCenter;
                    
                    uniform vec2 patchCenter;
                    uniform float patchSize;
                    
                    void main() {
                        vUv = uv;
                        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                        vWorldPosition = worldPosition.xyz;
                        
                        // Calculate distance from patch center for organic falloff
                        vec2 worldXZ = worldPosition.xz;
                        vDistanceFromCenter = distance(worldXZ, patchCenter) / patchSize;
                        
                        vec4 mvPosition = viewMatrix * worldPosition;
                        vDepth = -mvPosition.z;
                        gl_Position = projectionMatrix * mvPosition;
                    }
                `,
                fragmentShader: `
                    uniform vec3 fogColor;
                    uniform float opacity;
                    uniform float time;
                    uniform float windSpeed;
                    uniform float turbulence;
                    uniform float scattering;
                    uniform vec3 lightPositions[16];
                    uniform vec3 lightColors[16];
                    uniform int lightCount;
                    
                    varying vec3 vWorldPosition;
                    varying vec2 vUv;
                    varying float vDepth;
                    varying float vDistanceFromCenter;
                    
                    // Improved noise functions
                    float noise(vec2 p) {
                        return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                    }
                    
                    float fbm(vec2 p) {
                        float value = 0.0;
                        float amplitude = 0.5;
                        for (int i = 0; i < 4; i++) {
                            value += amplitude * noise(p);
                            p *= 2.0;
                            amplitude *= 0.5;
                        }
                        return value;
                    }
                    
                    void main() {
                        // Animated UV for wind effect
                        vec2 animUv = vUv + vec2(time * windSpeed * 0.08, time * windSpeed * 0.05);
                        
                        // Multi-octave noise for natural fog
                        float n1 = fbm(animUv * 2.0 + time * 0.1);
                        float n2 = fbm(animUv * 4.0 - time * 0.12) * 0.5;
                        float n3 = fbm(animUv * 8.0 + time * 0.15) * 0.25;
                        float turbulentNoise = (n1 + n2 + n3) * turbulence;
                        
                        // Organic edge fade using distance from patch center
                        float organicFade = 1.0 - smoothstep(0.6, 1.0, vDistanceFromCenter);
                        organicFade *= 1.0 - smoothstep(0.8, 1.0, vDistanceFromCenter); // Double falloff for smoother edges
                        
                        // Calculate light contribution
                        vec3 lightContribution = vec3(0.0);
                        for (int i = 0; i < lightCount; i++) {
                            if (i >= 16) break;
                            float dist = distance(vWorldPosition, lightPositions[i]);
                            float attenuation = 1.0 / (1.0 + dist * 0.1 + dist * dist * 0.01);
                            lightContribution += lightColors[i] * attenuation * scattering;
                        }
                        
                        // Enhanced color mixing for more natural appearance
                        vec3 baseColor = fogColor;
                        vec3 finalColor = mix(baseColor, baseColor + lightContribution, 0.7);
                        
                        // Add subtle color variation for realism
                        float colorNoise = fbm(vWorldPosition.xz * 0.01 + time * 0.02) * 0.1;
                        finalColor *= (1.0 + colorNoise);
                        
                        float finalOpacity = opacity * (0.6 + turbulentNoise * 0.4) * organicFade;
                        
                        // Depth fade for atmospheric perspective
                        finalOpacity *= 1.0 - smoothstep(25.0, 60.0, vDepth);
                        
                        // Ensure minimum visibility
                        finalOpacity = max(finalOpacity, 0.0);
                        
                        gl_FragColor = vec4(finalColor, finalOpacity);
                    }
                `,
                transparent: true,
                depthWrite: false,
                side: THREE.DoubleSide,
                blending: THREE.NormalBlending // Changed from AdditiveBlending for more natural look
            });
            
            const fogPatch = new THREE.Mesh(patchGeometry, fogMaterial);
            fogPatch.rotation.x = -Math.PI / 2; // Lay flat
            fogPatch.position.set(patchX, y, patchZ);
            fogPatch.name = `OrganicFogPatch_L${layerIndex}_P${p}`;
            
            roomGroup.add(fogPatch);
            this.fogMeshes.push(fogPatch);
        }
    }
    
    createUniformFogLayer(roomGroup, roomSize, y, baseOpacity, layerIndex) {
        // Original uniform plane creation (fallback for non-organic distribution)
        const planeSize = Math.max(roomSize.x, roomSize.z) * 1.2;
        const planeGeometry = new THREE.PlaneGeometry(planeSize, planeSize, 32, 32);
        
        // Add vertex displacement for organic look
        const positions = planeGeometry.attributes.position;
        for (let j = 0; j < positions.count; j++) {
            const x = positions.getX(j);
            const z = positions.getZ(j);
            const noise = this.noise2D(x * 0.05, z * 0.05) * 0.5;
            positions.setY(j, noise);
        }
        planeGeometry.computeVertexNormals();
        
        // Create material with custom shader for realistic fog
        const fogMaterial = new THREE.ShaderMaterial({
            uniforms: {
                fogColor: { value: new THREE.Color(this.config.color) },
                opacity: { value: baseOpacity },
                time: { value: 0 },
                windSpeed: { value: this.config.windSpeed },
                turbulence: { value: this.config.turbulence },
                scattering: { value: this.config.scattering },
                lightPositions: { value: [] },
                lightColors: { value: [] },
                lightCount: { value: 0 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                varying vec2 vUv;
                varying float vDepth;
                
                void main() {
                    vUv = uv;
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    vec4 mvPosition = viewMatrix * worldPosition;
                    vDepth = -mvPosition.z;
                    gl_Position = projectionMatrix * mvPosition;
                }
            `,
            fragmentShader: `
                uniform vec3 fogColor;
                uniform float opacity;
                uniform float time;
                uniform float windSpeed;
                uniform float turbulence;
                uniform float scattering;
                uniform vec3 lightPositions[16];
                uniform vec3 lightColors[16];
                uniform int lightCount;
                
                varying vec3 vWorldPosition;
                varying vec2 vUv;
                varying float vDepth;
                
                // Simple noise function
                float noise(vec2 p) {
                    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                }
                
                void main() {
                    // Animated UV for wind effect
                    vec2 animUv = vUv + vec2(time * windSpeed * 0.1, 0.0);
                    
                    // Multi-octave noise for organic fog
                    float n1 = noise(animUv * 3.0 + time * 0.1);
                    float n2 = noise(animUv * 8.0 - time * 0.15) * 0.5;
                    float n3 = noise(animUv * 16.0 + time * 0.2) * 0.25;
                    float turbulentNoise = (n1 + n2 + n3) * turbulence;
                    
                    // Edge fade
                    vec2 center = vUv - 0.5;
                    float edgeFade = 1.0 - smoothstep(0.3, 0.5, length(center));
                    
                    // Calculate light contribution
                    vec3 lightContribution = vec3(0.0);
                    for (int i = 0; i < lightCount; i++) {
                        if (i >= 16) break;
                        float dist = distance(vWorldPosition, lightPositions[i]);
                        float attenuation = 1.0 / (1.0 + dist * 0.1 + dist * dist * 0.01);
                        lightContribution += lightColors[i] * attenuation * scattering;
                    }
                    
                    // Final color with light scattering
                    vec3 finalColor = fogColor + lightContribution;
                    float finalOpacity = opacity * (0.5 + turbulentNoise) * edgeFade;
                    
                    // Depth fade
                    finalOpacity *= 1.0 - smoothstep(20.0, 50.0, vDepth);
                    
                    gl_FragColor = vec4(finalColor, finalOpacity);
                }
            `,
            transparent: true,
            depthWrite: false,
            side: THREE.DoubleSide,
            blending: THREE.AdditiveBlending
        });
        
        const fogPlane = new THREE.Mesh(planeGeometry, fogMaterial);
        fogPlane.rotation.x = -Math.PI / 2;
        fogPlane.position.y = y;
        fogPlane.name = `FogLayer_${layerIndex}`;
        
        roomGroup.add(fogPlane);
        this.fogMeshes.push(fogPlane);
    }
    
    createLightShafts(roomGroup, roomSize) {
        // Find all lights in the room
        const lights = [];
        roomGroup.traverse(child => {
            if (child.isLight && (child.isPointLight || child.isSpotLight)) {
                lights.push(child);
            }
        });
        
        // Update fog lighting with found lights
        this.updateFogLighting(lights);
        
        // Create volumetric light shafts for dramatic godrays
        if (this.config.godrayIntensity > 0 && lights.length > 0) {
            lights.forEach((light, index) => {
                this.createGodrayShaft(light, roomSize, index);
            });
        }
    }
    
    createGodrayShaft(light, roomSize, index) {
        const shaftGeometry = new THREE.ConeGeometry(1, this.config.height, 8, 4, true);
        const shaftMaterial = new THREE.ShaderMaterial({
            uniforms: {
                lightColor: { value: light.color || new THREE.Color(0xffffff) },
                intensity: { value: this.config.godrayIntensity },
                time: { value: 0 }
            },
            vertexShader: `
                varying vec3 vPosition;
                varying float vDistance;
                void main() {
                    vPosition = position;
                    vDistance = length(position);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 lightColor;
                uniform float intensity;
                uniform float time;
                varying vec3 vPosition;
                varying float vDistance;
                
                void main() {
                    float fade = 1.0 - smoothstep(0.0, 1.0, vDistance);
                    float pulse = 0.5 + 0.5 * sin(time * 2.0);
                    float opacity = intensity * fade * pulse * 0.1;
                    gl_FragColor = vec4(lightColor, opacity);
                }
            `,
            transparent: true,
            depthWrite: false,
            blending: THREE.AdditiveBlending
        });
        
        const shaft = new THREE.Mesh(shaftGeometry, shaftMaterial);
        shaft.position.copy(light.position);
        shaft.name = `GodrayShaft_${index}`;
        
        roomGroup.add(shaft);
        this.lightShafts.push(shaft);
    }
    
    updateFogLighting(lights) {
        const lightPositions = [];
        const lightColors = [];
        
        lights.forEach(light => {
            lightPositions.push(light.position.x, light.position.y, light.position.z);
            const color = light.color || new THREE.Color(0xffffff);
            lightColors.push(color.r, color.g, color.b);
        });
        
        // Update all fog materials with light data
        this.fogMeshes.forEach(fogMesh => {
            if (fogMesh.material && fogMesh.material.uniforms) {
                fogMesh.material.uniforms.lightPositions.value = lightPositions;
                fogMesh.material.uniforms.lightColors.value = lightColors;
                fogMesh.material.uniforms.lightCount.value = lights.length;
            }
        });
    }
    
    updateSceneFog() {
        // Set up distance fog to complement volumetric fog
        this.scene.fog = new THREE.Fog(
            this.config.color,
            this.config.height * 2, // Start fog at twice the volumetric height
            this.config.height * 6  // End fog at 6x the volumetric height
        );
    }
    
    setupRenderOrder() {
        // Ensure fog renders in the correct order
        this.fogMeshes.forEach(mesh => {
            mesh.renderOrder = 1000; // Render after most objects
        });
        
        this.lightShafts.forEach(shaft => {
            shaft.renderOrder = 1001; // Render after fog
        });
    }
    
    getLayerCount() {
        const qualityMap = {
            'low': Math.max(4, Math.floor(this.config.layers * 0.5)),
            'medium': Math.max(6, Math.floor(this.config.layers * 0.75)),
            'high': this.config.layers
        };
        return qualityMap[this.config.quality] || this.config.layers;
    }
    
    noise2D(x, y) {
        return Math.sin(x * 12.9898 + y * 78.233) * 43758.5453 % 1;
    }
    
    update(deltaTime) {
        const time = performance.now() * 0.001;
        
        // Update fog animation
        this.fogMeshes.forEach(mesh => {
            if (mesh.material && mesh.material.uniforms && mesh.material.uniforms.time) {
                mesh.material.uniforms.time.value = time;
            }
        });
        
        // Update godray animation
        this.lightShafts.forEach(shaft => {
            if (shaft.material && shaft.material.uniforms && shaft.material.uniforms.time) {
                shaft.material.uniforms.time.value = time;
            }
        });
    }
    
    dispose() {
        // Clean up fog meshes
        this.fogMeshes.forEach(mesh => {
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) mesh.material.dispose();
            if (mesh.parent) mesh.parent.remove(mesh);
        });
        
        // Clean up light shafts
        this.lightShafts.forEach(shaft => {
            if (shaft.geometry) shaft.geometry.dispose();
            if (shaft.material) shaft.material.dispose();
            if (shaft.parent) shaft.parent.remove(shaft);
        });
        
        this.fogMeshes = [];
        this.lightShafts = [];
        
        // Remove scene fog
        if (this.scene.fog) {
            this.scene.fog = null;
        }
    }
}

/**
 * Create volumetric fog system for a room
 * @param {THREE.Scene} scene - The THREE.js scene
 * @param {THREE.Group} roomGroup - Room group to add fog to
 * @param {Object} config - Fog configuration
 * @returns {VolumetricFogSystem} Fog system instance
 */
export function createUnrealFog(scene, roomGroup, config = {}) {
    const fogSystem = new VolumetricFogSystem(scene, config);
    fogSystem.createFogInRoom(roomGroup);
    return fogSystem;
} 