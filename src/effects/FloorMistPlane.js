import * as THREE from 'three';

/**
 * Creates layered, semi-transparent planes to simulate floor mist.
 * Uses floor geometry to create a stencil mask for proper fog placement.
 *
 * @param {object} roomBounds - Object with minX, maxX, minZ, maxZ defining the floor area.
 * @param {number} [mistHeight=0.5] - The maximum height range for the mist planes.
 * @param {number} [layerCount=3] - How many layers of mist planes.
 * @param {number} [opacity=0.15] - Base opacity for the mist.
 * @param {Array} floorSegments - Array of floor segments defining actual walkable areas.
 * @param {THREE.Scene} [scene=null] - Scene to sample floor geometry from.
 * @returns {THREE.Group} A group containing the mist plane meshes.
 */
export function createFloorMistPlanes(roomBounds, mistHeight = 0.5, layerCount = 3, opacity = 0.15, floorSegments = [], scene = null) {
    const mistGroup = new THREE.Group();
    mistGroup.name = "floorMist";

    // Try to use floor segments first (more reliable during room generation)
    if (floorSegments && floorSegments.length > 0) {
        console.log(`[FloorMist] Using ${floorSegments.length} floor segments for fog placement`);
        return createSegmentBasedMistPlanes(roomBounds, mistHeight, layerCount, opacity, floorSegments);
    }

    // Get floor meshes from the scene to create a proper stencil
    const floorMeshes = [];
    if (scene) {
        scene.traverse(child => {
            if (child.isMesh && child.userData && child.userData.isFloor) {
                floorMeshes.push(child);
            }
        });
    }

    // If floor meshes found, use geometry stencil
    if (floorMeshes.length > 0) {
        console.log(`[FloorMist] Using ${floorMeshes.length} floor meshes for fog stencil`);
        return createStenciledMistPlanes(roomBounds, mistHeight, layerCount, opacity, floorMeshes);
    }

    // Fall back to simple room bounds
    console.warn("[FloorMist] No floor data found, using simple room bounds");
    return createSimpleMistPlane(roomBounds, mistHeight, layerCount, opacity);
}

/**
 * Creates mist planes based on floor segment data using a single continuous fog plane with alpha mask
 */
function createSegmentBasedMistPlanes(roomBounds, mistHeight, layerCount, opacity, floorSegments) {
    const mistGroup = new THREE.Group();

    // Create a single large fog plane that covers the entire room
    const roomWidth = roomBounds.maxX - roomBounds.minX;
    const roomDepth = roomBounds.maxZ - roomBounds.minZ;
    const roomCenterX = (roomBounds.minX + roomBounds.maxX) / 2;
    const roomCenterZ = (roomBounds.minZ + roomBounds.maxZ) / 2;

    // Create an alpha mask texture based on floor segments
    const maskTexture = createFloorMaskTexture(roomBounds, floorSegments);

    // Create the fog plane geometry
    const planeGeo = new THREE.PlaneGeometry(roomWidth, roomDepth);

    for (let i = 0; i < layerCount; i++) {
        const layerOpacity = opacity / layerCount;
        const layerMat = new THREE.MeshBasicMaterial({
            color: 0xffffff, // White mist color (original)
            transparent: true,
            opacity: layerOpacity,
            depthWrite: false,
            blending: THREE.AdditiveBlending,
            side: THREE.DoubleSide,
            alphaMap: maskTexture, // Use the floor mask as alpha map
        });

        const planeMesh = new THREE.Mesh(planeGeo, layerMat);
        planeMesh.position.set(
            roomCenterX,
            (i + 1) * (mistHeight / layerCount) * 0.6,
            roomCenterZ
        );
        planeMesh.rotation.x = -Math.PI / 2;
        planeMesh.rotation.z = (Math.random() - 0.5) * 0.02;

        // Mark as floor mist to exclude from voxel shadow processing
        planeMesh.userData.isFloorMist = true;
        planeMesh.name = `floorMistLayer${i}`;

        mistGroup.add(planeMesh);
    }

    console.log(`[FloorMist] Created continuous fog plane with floor mask for ${floorSegments.length} segments`);
    return mistGroup;
}

/**
 * Creates an alpha mask texture based on floor segments
 */
function createFloorMaskTexture(roomBounds, floorSegments) {
    // Create a canvas to draw the floor mask
    const textureSize = 128; // Resolution of the mask texture - reduced for performance (was 512)
    const canvas = document.createElement('canvas');
    canvas.width = textureSize;
    canvas.height = textureSize;
    const ctx = canvas.getContext('2d');

    // Clear canvas to black (transparent areas)
    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, textureSize, textureSize);

    // Calculate room dimensions for scaling
    const roomWidth = roomBounds.maxX - roomBounds.minX;
    const roomDepth = roomBounds.maxZ - roomBounds.minZ;
    const scaleX = textureSize / roomWidth;
    const scaleZ = textureSize / roomDepth;

    // Draw floor segments as white areas (visible fog areas)
    ctx.fillStyle = 'white';
    floorSegments.forEach((segment, index) => {
        // Convert world coordinates to texture coordinates
        const segmentMinX = segment.position.x - segment.width / 2;
        const segmentMaxX = segment.position.x + segment.width / 2;
        const segmentMinZ = segment.position.z - segment.depth / 2;
        const segmentMaxZ = segment.position.z + segment.depth / 2;

        // Convert to texture space - FLIP Z-axis to fix north/south orientation
        const texMinX = (segmentMinX - roomBounds.minX) * scaleX;
        const texMaxX = (segmentMaxX - roomBounds.minX) * scaleX;
        const texMinZ = (roomBounds.maxZ - segmentMaxZ) * scaleZ; // Flipped Z mapping
        const texMaxZ = (roomBounds.maxZ - segmentMinZ) * scaleZ; // Flipped Z mapping

        // Debug logging for L-shaped rooms
        console.log(`[FloorMist] Segment ${index}: world(${segmentMinX.toFixed(1)},${segmentMinZ.toFixed(1)}) to (${segmentMaxX.toFixed(1)},${segmentMaxZ.toFixed(1)}) → texture(${texMinX.toFixed(1)},${texMinZ.toFixed(1)}) to (${texMaxX.toFixed(1)},${texMaxZ.toFixed(1)})`);

        // Draw rectangle for this floor segment
        ctx.fillRect(
            texMinX,
            texMinZ,
            texMaxX - texMinX,
            texMaxZ - texMinZ
        );
    });

    // Apply blur for softer fog edges
    ctx.filter = 'blur(8px)';
    ctx.globalCompositeOperation = 'source-over';
    ctx.drawImage(canvas, 0, 0);

    // Create Three.js texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.flipY = false; // Important for proper orientation

    return texture;
}

/**
 * Creates a simple mist plane for fallback when no floor geometry is available
 */
function createSimpleMistPlane(roomBounds, mistHeight, layerCount, opacity) {
    const mistGroup = new THREE.Group();
    const WALL_BUFFER = 0.3;

    const roomCenterX = (roomBounds.minX + roomBounds.maxX) / 2;
    const roomCenterZ = (roomBounds.minZ + roomBounds.maxZ) / 2;
    const roomWidth = (roomBounds.maxX - roomBounds.minX) - (WALL_BUFFER * 2);
    const roomDepth = (roomBounds.maxZ - roomBounds.minZ) - (WALL_BUFFER * 2);

    const planeGeo = new THREE.PlaneGeometry(roomWidth, roomDepth);

    for (let i = 0; i < layerCount; i++) {
        const layerOpacity = opacity / layerCount;
        const layerMat = new THREE.MeshBasicMaterial({
            color: 0xffffff, // White mist color (original)
            transparent: true,
            opacity: layerOpacity,
            depthWrite: false,
            blending: THREE.AdditiveBlending,
            side: THREE.DoubleSide,
        });

        const planeMesh = new THREE.Mesh(planeGeo, layerMat);
        planeMesh.position.set(roomCenterX, (i + 1) * (mistHeight / layerCount) * 0.6, roomCenterZ);
        planeMesh.rotation.x = -Math.PI / 2;
        planeMesh.rotation.z = (Math.random() - 0.5) * 0.02;

        // Mark as floor mist to exclude from voxel shadow processing
        planeMesh.userData.isFloorMist = true;
        planeMesh.name = `simpleMistLayer${i}`;

        mistGroup.add(planeMesh);
    }

    return mistGroup;
}

/**
 * Creates mist planes using floor geometry as a stencil mask
 */
function createStenciledMistPlanes(roomBounds, mistHeight, layerCount, opacity, floorMeshes) {
    const mistGroup = new THREE.Group();

    // Create a single large fog plane that covers the entire room
    const roomWidth = roomBounds.maxX - roomBounds.minX;
    const roomDepth = roomBounds.maxZ - roomBounds.minZ;
    const roomCenterX = (roomBounds.minX + roomBounds.maxX) / 2;
    const roomCenterZ = (roomBounds.minZ + roomBounds.maxZ) / 2;

    // Create an alpha mask texture based on floor geometry
    const maskTexture = createFloorGeometryMaskTexture(roomBounds, floorMeshes);

    // Create the fog plane geometry
    const planeGeo = new THREE.PlaneGeometry(roomWidth, roomDepth);

    for (let i = 0; i < layerCount; i++) {
        const layerOpacity = opacity / layerCount;
        const layerMat = new THREE.MeshBasicMaterial({
            color: 0xffffff, // White mist color (original)
            transparent: true,
            opacity: layerOpacity,
            depthWrite: false,
            blending: THREE.AdditiveBlending,
            side: THREE.DoubleSide,
            alphaMap: maskTexture, // Use the floor mask as alpha map
        });

        const planeMesh = new THREE.Mesh(planeGeo, layerMat);
        planeMesh.position.set(
            roomCenterX,
            (i + 1) * (mistHeight / layerCount) * 0.6,
            roomCenterZ
        );
        planeMesh.rotation.x = -Math.PI / 2;
        planeMesh.rotation.z = (Math.random() - 0.5) * 0.02;

        // Mark as floor mist to exclude from voxel shadow processing
        planeMesh.userData.isFloorMist = true;
        planeMesh.name = `stenciledMistLayer${i}`;

        mistGroup.add(planeMesh);
    }

    console.log(`[FloorMist] Created continuous fog plane with geometry mask`);
    return mistGroup;
}

/**
 * Creates an alpha mask texture based on floor geometry using raycasting
 */
function createFloorGeometryMaskTexture(roomBounds, floorMeshes) {
    // Create a canvas to draw the floor mask
    const textureSize = 128; // Resolution of the mask texture - reduced for performance (was 512)
    const canvas = document.createElement('canvas');
    canvas.width = textureSize;
    canvas.height = textureSize;
    const ctx = canvas.getContext('2d');

    // Clear canvas to black (transparent areas)
    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, textureSize, textureSize);

    // Calculate room dimensions for scaling
    const roomWidth = roomBounds.maxX - roomBounds.minX;
    const roomDepth = roomBounds.maxZ - roomBounds.minZ;
    const scaleX = textureSize / roomWidth;
    const scaleZ = textureSize / roomDepth;

    // Sample floor geometry using raycasting
    const raycaster = new THREE.Raycaster();
    raycaster.far = 10;
    const sampleStep = Math.min(roomWidth, roomDepth) / textureSize; // Sample resolution

    // Create image data for pixel-level control
    const imageData = ctx.createImageData(textureSize, textureSize);
    const data = imageData.data;

    for (let x = 0; x < textureSize; x++) {
        for (let z = 0; z < textureSize; z++) {
            // Convert texture coordinates to world coordinates
            const worldX = roomBounds.minX + (x / textureSize) * roomWidth;
            const worldZ = roomBounds.minZ + (z / textureSize) * roomDepth;

            // Cast ray downward to check for floor
            raycaster.set(new THREE.Vector3(worldX, 5, worldZ), new THREE.Vector3(0, -1, 0));
            const intersects = raycaster.intersectObjects(floorMeshes, false);

            const pixelIndex = (z * textureSize + x) * 4;
            if (intersects.length > 0) {
                // Found floor - make this pixel white (visible fog)
                data[pixelIndex] = 255;     // R
                data[pixelIndex + 1] = 255; // G
                data[pixelIndex + 2] = 255; // B
                data[pixelIndex + 3] = 255; // A
            } else {
                // No floor - make this pixel black (transparent fog)
                data[pixelIndex] = 0;       // R
                data[pixelIndex + 1] = 0;   // G
                data[pixelIndex + 2] = 0;   // B
                data[pixelIndex + 3] = 255; // A
            }
        }
    }

    // Put the image data on the canvas
    ctx.putImageData(imageData, 0, 0);

    // Apply blur for softer fog edges
    ctx.filter = 'blur(4px)';
    ctx.globalCompositeOperation = 'source-over';
    ctx.drawImage(canvas, 0, 0);

    // Create Three.js texture from canvas
    const texture = new THREE.CanvasTexture(canvas);
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    texture.flipY = false; // Important for proper orientation

    return texture;
}