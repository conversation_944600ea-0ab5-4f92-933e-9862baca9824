// Lightweight volumetric fog volume for WebGL2 / Three.js
// Designed to be updated cheaply each frame (optional) and consumed by a ray-march post-processing pass.
// Focuses on directional-light scattering for performance.

import * as THREE from 'three';

export class VolumetricFogVolume {
    /**
     * @param {Object} opts
     * @param {THREE.Box3} opts.bounds - world-space bounding box that the volume occupies.
     * @param {number[]} [opts.size=[64,32,64]] - voxel resolution [x,y,z]. Keep small for performance.
     * @param {number} [opts.density=0.04] - base density multiplier.
     * @param {THREE.Color|number|string} [opts.albedo=0xffffff] - fog base colour.
     * @param {number} [opts.anisotropy=0.0] - Henyey-<PERSON>tein g parameter (-1 back scatter, 0 isotropic, 1 forward).
     */
    constructor(opts={}){
        this.bounds = opts.bounds || new THREE.Box3(new THREE.Vector3(-4,0,-4), new THREE.Vector3(4,4,4));
        const size = opts.size || [64,32,64];
        this.resX=size[0]; this.resY=size[1]; this.resZ=size[2];
        this.density = opts.density!==undefined?opts.density:0.04;
        this.albedo = new THREE.Color(opts.albedo!==undefined?opts.albedo:0xffffff);
        this.anisotropy = opts.anisotropy!==undefined?opts.anisotropy:0.0;

        // 3D texture stores density in R channel; G/B/A unused for now.
        const voxelCount = this.resX*this.resY*this.resZ;
        const data = new Float32Array(voxelCount*4);
        // Fill with initial noise
        for(let i=0;i<voxelCount;i++){
            const n = Math.random();
            data[i*4] = n; // density
            data[i*4+1]=0; data[i*4+2]=0; data[i*4+3]=1;
        }
        const tex = new THREE.Data3DTexture(data,this.resX,this.resY,this.resZ);
        tex.format = THREE.RGBAFormat;
        tex.type = THREE.FloatType;
        tex.minFilter = THREE.LinearFilter;
        tex.magFilter = THREE.LinearFilter;
        tex.unpackAlignment = 1;
        tex.needsUpdate = true;
        this.texture = tex;

        // Helper box (wireframe) to show volume bounds in editor/debug mode
        const geo = new THREE.BoxGeometry(1,1,1);
        const mat = new THREE.MeshBasicMaterial({wireframe:true, color:0x00ffff, transparent:true, opacity:0.1});
        this.debugMesh = new THREE.Mesh(geo, mat);
        this.debugMesh.name = 'VolumetricFogDebug';
        this.debugMesh.visible = false;
        this._updateDebugTransform();
    }

    /** call per-frame to animate density with a cheap dynamic pattern */
    updateDensity(time){
        // cheap sine-wave modulation along Y to simulate rolling fog; update a single slice per frame for performance
        const sliceY = Math.floor(((time*0.1)%1)*this.resY);
        const start = sliceY*this.resX*this.resZ*4;
        for(let z=0;z<this.resZ;z++){
            for(let x=0;x<this.resX;x++){
                const i = start + (z*this.resX + x)*4;
                const noise = 0.5 + 0.5*Math.sin( (x*0.2+time*0.5) + (z*0.3) );
                this.texture.image.data[i] = noise;
            }
        }
        this.texture.needsUpdate = true; // partial update ok
    }

    /** Light injection stub – for perf we encode sunlight direction only */
    updateDirectionalLight(dir, intensity){
        // Could precompute phase contribution into density; skip for now.
        this.sunDirection = dir.clone().normalize();
        this.sunIntensity = intensity;
    }

    getUniformData(){
        return {
            fogTexture: {value: this.texture},
            fogBoundsMin: {value: this.bounds.min},
            fogBoundsMax: {value: this.bounds.max},
            fogDensity: {value: this.density},
            fogAlbedo: {value: this.albedo},
            fogAnisotropy: {value: this.anisotropy}
        };
    }

    _updateDebugTransform(){
        const size = new THREE.Vector3();
        this.bounds.getSize(size);
        const center = new THREE.Vector3();
        this.bounds.getCenter(center);
        this.debugMesh.scale.copy(size);
        this.debugMesh.position.copy(center);
    }
} 