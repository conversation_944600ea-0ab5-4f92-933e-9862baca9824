import * as THREE from 'three';
// Use local three build (consistent with other effect managers)
import { Pass } from '/libs/three/examples/jsm/postprocessing/Pass.js';

// Very lightweight volumetric-fog ray-march post-process pass.
// Requires the renderer/composer renderTarget to have depthTexture enabled (THREE.DepthTexture).
// Performs at most 32 ray-march steps (define FOG_STEPS to customise).
export class VolumetricFogPass extends Pass {
    /**
     * @param {THREE.Camera} camera
     * @param {VolumetricFogVolume} fogVolume
     */
    constructor(camera, fogVolume) {
        super();
        this.camera = camera;
        this.fogVolume = fogVolume;

        const uniforms = {
            tDiffuse: { value: null },
            tDepth: { value: null },
            cameraNear: { value: camera.near },
            cameraFar: { value: camera.far },
            invProjection: { value: new THREE.Matrix4().copy(camera.projectionMatrix).invert() },
            invView: { value: new THREE.Matrix4().copy(camera.matrixWorld) },
            // Fog params injected below
            ...fogVolume.getUniformData()
        };

        // GLSL ray-march shader inlined to avoid separate file loader issues
        const VOLUMETRIC_FOG_FRAG = /* glsl */`
        #define FOG_STEPS 32

        uniform sampler2D tDiffuse;
        uniform sampler2D tDepth;

        uniform sampler3D fogTexture;
        uniform vec3  fogBoundsMin;
        uniform vec3  fogBoundsMax;
        uniform float fogDensity;
        uniform vec3  fogAlbedo;
        uniform float fogAnisotropy;

        uniform float cameraNear;
        uniform float cameraFar;
        uniform mat4  invProjection;
        uniform mat4  invView;

        varying vec2 vUv;

        float depthToViewZ(float z){return cameraNear*cameraFar/(cameraFar - z*(cameraFar-cameraNear))*(-1.0);} 
        vec3 getWorldPos(vec2 uv, float depth){
            float viewZ = depthToViewZ(depth);
            vec4 pClip = vec4(uv*2.0-1.0, depth, 1.0);
            vec4 pView = invProjection * pClip; pView /= pView.w; pView.z = viewZ;
            vec4 pWorld = invView * pView;
            return pWorld.xyz;
        }

        void main(){
            float depth = texture2D(tDepth, vUv).x;
            vec3 camPos = (invView * vec4(0.0,0.0,0.0,1.0)).xyz;

            // If depth far plane (1.0) treat as skybox (no fog)
            if(depth>=1.0){gl_FragColor = texture2D(tDiffuse,vUv); return;}

            vec3 worldPos = getWorldPos(vUv, depth);
            vec3 dir = normalize(worldPos - camPos);
            float totalDist = length(worldPos - camPos);

            // AABB intersection
            vec3 invDir = 1.0/dir;
            vec3 tMin = (fogBoundsMin - camPos)*invDir;
            vec3 tMax = (fogBoundsMax - camPos)*invDir;
            vec3 t1 = min(tMin,tMax);
            vec3 t2 = max(tMin,tMax);
            float tNear = max(max(t1.x,t1.y),t1.z);
            float tFar  = min(min(t2.x,t2.y),t2.z);
            if(tFar<0.0||tNear>tFar){gl_FragColor=texture2D(tDiffuse,vUv);return;}
            tNear = max(tNear,0.0);
            tFar  = min(tFar,totalDist);

            float stepLen = (tFar - tNear)/float(FOG_STEPS);
            vec3 marchPos = camPos + dir*(tNear+stepLen*0.5);
            float trans = 1.0;
            vec3 accum = vec3(0.0);
            for(int i=0;i<FOG_STEPS;i++){
                vec3 local = (marchPos - fogBoundsMin)/(fogBoundsMax - fogBoundsMin);
                float dens = texture(fogTexture, local).r * fogDensity;
                float opa  = 1.0 - exp(-dens*stepLen);
                accum += trans*opa*fogAlbedo;
                trans *= 1.0-opa;
                if(trans<0.01) break; // early exit
                marchPos += dir*stepLen;
            }
            vec3 sceneColor = texture2D(tDiffuse,vUv).rgb;
            vec3 finalCol = sceneColor*trans + accum;
            gl_FragColor = vec4(finalCol,1.0);
        }`;

        const material = new THREE.ShaderMaterial({
            name: 'VolumetricFogMaterial',
            uniforms,
            vertexShader: /* glsl */`varying vec2 vUv; void main(){vUv = uv; gl_Position = vec4( position, 1.0 );}`,
            fragmentShader: VOLUMETRIC_FOG_FRAG,
            depthWrite: false,
            depthTest: false,
            transparent: true
        });

        this.fsQuad = new Pass.FullScreenQuad(material);
    }

    setSize(){}

    render(renderer, writeBuffer, readBuffer /*, deltaTime, maskActive */) {
        this.fsQuad.material.uniforms.tDiffuse.value = readBuffer.texture;
        this.fsQuad.material.uniforms.tDepth.value = readBuffer.depthTexture;
        // Update matrices if camera moved
        this.fsQuad.material.uniforms.invProjection.value.copy(this.camera.projectionMatrix).invert();
        this.fsQuad.material.uniforms.invView.value.copy(this.camera.matrixWorld);

        if (this.renderToScreen) {
            this.fsQuad.render(renderer);
        } else {
            renderer.setRenderTarget(writeBuffer);
            this.fsQuad.render(renderer);
        }
    }
} 