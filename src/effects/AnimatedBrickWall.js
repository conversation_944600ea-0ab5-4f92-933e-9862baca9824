/**
 * AnimatedBrickWall - Creates <PERSON>agon Alley style brick wall animation
 * Individual voxels slide, rotate, and rearrange to create an opening
 */

import * as THREE from 'three';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js';

export class AnimatedBrickWall {
    constructor(wallPosition, wallRotation, wallLength, scene) {
        this.wallPosition = wallPosition.clone();
        this.wallRotation = wallRotation;
        this.wallLength = wallLength;
        this.scene = scene;
        
        // Animation state
        this.isAnimating = false;
        this.isRevealed = false;
        this.animationProgress = 0;
        this.animationDuration = 2.5; // seconds
        
        // Brick components
        this.brickGroup = new THREE.Group();
        this.bricks = [];
        this.originalPositions = [];
        this.targetPositions = [];
        
        // Particle system for magical effects
        this.particles = null;
        
        this.createBrickWall();
    }

    /**
     * Create the individual brick voxels that make up the wall
     */
    createBrickWall() {
        // Calculate wall dimensions
        const wallHeight = 4.0; // WALL_HEIGHT
        const wallDepth = 0.5;  // WALL_DEPTH
        
        // Number of bricks along width and height
        const bricksWidth = Math.ceil(this.wallLength / VOXEL_SIZE);
        const bricksHeight = Math.ceil(wallHeight / VOXEL_SIZE);
        
        // Create brick material (stone brick)
        const brickMaterial = new THREE.MeshLambertMaterial({
            color: 0x666666,
            transparent: false
        });
        
        // Create individual brick voxels
        for (let y = 0; y < bricksHeight; y++) {
            for (let x = 0; x < bricksWidth; x++) {
                const brickGeometry = new THREE.BoxGeometry(VOXEL_SIZE, VOXEL_SIZE, wallDepth);
                const brick = new THREE.Mesh(brickGeometry, brickMaterial);
                
                // Position brick
                const brickX = (x - bricksWidth / 2) * VOXEL_SIZE + VOXEL_SIZE / 2;
                const brickY = y * VOXEL_SIZE + VOXEL_SIZE / 2;
                const brickZ = 0;
                
                brick.position.set(brickX, brickY, brickZ);
                brick.castShadow = true;
                brick.receiveShadow = true;
                
                // Store original position
                this.originalPositions.push(brick.position.clone());
                
                // Calculate target position for animation
                this.calculateTargetPosition(brick, x, y, bricksWidth, bricksHeight);
                
                this.bricks.push(brick);
                this.brickGroup.add(brick);
            }
        }
        
        // Position and rotate the entire wall group
        this.brickGroup.position.copy(this.wallPosition);
        this.brickGroup.rotation.y = this.wallRotation;
        
        // Add to scene
        this.scene.add(this.brickGroup);
        
        console.log(`[AnimatedBrickWall] Created wall with ${this.bricks.length} bricks`);
    }

    /**
     * Calculate where each brick should move during the animation
     */
    calculateTargetPosition(brick, x, y, bricksWidth, bricksHeight) {
        const centerX = Math.floor(bricksWidth / 2);
        const centerY = Math.floor(bricksHeight / 2);
        
        // Distance from center
        const distanceFromCenterX = Math.abs(x - centerX);
        const distanceFromCenterY = Math.abs(y - centerY);
        
        // Bricks in the center area (door opening) move away
        const doorWidth = Math.ceil(2.5 / VOXEL_SIZE); // DOOR_WIDTH in voxels
        const doorHeight = Math.ceil(3.0 / VOXEL_SIZE); // Door height in voxels
        
        let targetPos = brick.position.clone();
        
        if (distanceFromCenterX <= doorWidth / 2 && distanceFromCenterY <= doorHeight / 2) {
            // This brick is in the door opening area - move it away
            const direction = new THREE.Vector3(
                x < centerX ? -1 : (x > centerX ? 1 : 0),
                y < centerY ? -1 : (y > centerY ? 1 : 0),
                Math.random() > 0.5 ? 1 : -1 // Some bricks move forward, some backward
            );
            
            // Move bricks outward from the center
            const moveDistance = VOXEL_SIZE * (2 + Math.random() * 3);
            targetPos.add(direction.multiplyScalar(moveDistance));
            
            // Add some randomness to make it look more magical
            targetPos.x += (Math.random() - 0.5) * VOXEL_SIZE;
            targetPos.y += (Math.random() - 0.5) * VOXEL_SIZE;
            targetPos.z += (Math.random() - 0.5) * VOXEL_SIZE * 2;
        } else {
            // Bricks outside the door area slide to form the archway
            if (x < centerX - doorWidth / 2) {
                // Left side bricks slide left
                targetPos.x -= VOXEL_SIZE * (1 + Math.random());
            } else if (x > centerX + doorWidth / 2) {
                // Right side bricks slide right
                targetPos.x += VOXEL_SIZE * (1 + Math.random());
            }
            
            if (y > centerY + doorHeight / 2) {
                // Top bricks slide up
                targetPos.y += VOXEL_SIZE * (1 + Math.random());
            }
            
            // Add slight randomness
            targetPos.x += (Math.random() - 0.5) * VOXEL_SIZE * 0.5;
            targetPos.y += (Math.random() - 0.5) * VOXEL_SIZE * 0.5;
        }
        
        this.targetPositions.push(targetPos);
    }

    /**
     * Start the brick wall animation
     */
    async startAnimation() {
        if (this.isAnimating || this.isRevealed) {
            return;
        }
        
        console.log('[AnimatedBrickWall] Starting brick animation...');
        this.isAnimating = true;
        this.animationProgress = 0;
        
        // Create particle effects
        this.createParticleEffects();
        
        // Animate bricks
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                this.animationProgress = Math.min(elapsed / this.animationDuration, 1);
                
                // Easing function for smooth animation
                const easeInOutCubic = (t) => {
                    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                };
                
                const easedProgress = easeInOutCubic(this.animationProgress);
                
                // Update brick positions
                this.bricks.forEach((brick, index) => {
                    const originalPos = this.originalPositions[index];
                    const targetPos = this.targetPositions[index];
                    
                    // Interpolate position
                    brick.position.lerpVectors(originalPos, targetPos, easedProgress);
                    
                    // Add rotation for more dynamic effect
                    const rotationAmount = easedProgress * Math.PI * (Math.random() > 0.5 ? 1 : -1);
                    brick.rotation.x = rotationAmount * 0.3;
                    brick.rotation.y = rotationAmount * 0.5;
                    brick.rotation.z = rotationAmount * 0.2;
                    
                    // Fade out bricks that are moving away
                    const distanceFromOriginal = brick.position.distanceTo(originalPos);
                    if (distanceFromOriginal > VOXEL_SIZE * 2) {
                        brick.material.opacity = 1 - (easedProgress * 0.7);
                        brick.material.transparent = true;
                    }
                });
                
                // Update particles
                if (this.particles) {
                    this.updateParticles(easedProgress);
                }
                
                if (this.animationProgress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.completeAnimation();
                    resolve();
                }
            };
            
            animate();
        });
    }

    /**
     * Complete the animation and create the door opening
     */
    completeAnimation() {
        console.log('[AnimatedBrickWall] Animation complete - creating door opening');
        
        this.isAnimating = false;
        this.isRevealed = true;
        
        // Hide bricks that were in the door area
        this.bricks.forEach((brick, index) => {
            const originalPos = this.originalPositions[index];
            const targetPos = this.targetPositions[index];
            const distanceFromOriginal = targetPos.distanceTo(originalPos);
            
            if (distanceFromOriginal > VOXEL_SIZE * 1.5) {
                brick.visible = false;
            }
        });
        
        // Clean up particles
        if (this.particles) {
            this.scene.remove(this.particles);
            this.particles = null;
        }
        
        // TODO: Create actual door/archway geometry here
        this.createDoorOpening();
    }

    /**
     * Create the door opening after animation
     */
    createDoorOpening() {
        // Create a simple black archway opening (similar to existing doors)
        const doorGeometry = new THREE.PlaneGeometry(2.5, 3.0); // DOOR_WIDTH x door height
        const doorMaterial = new THREE.MeshBasicMaterial({
            color: 0x000000,
            side: THREE.DoubleSide
        });
        
        const doorOpening = new THREE.Mesh(doorGeometry, doorMaterial);
        doorOpening.position.set(0, 1.5, 0); // Center the opening
        doorOpening.userData.isDoorOpening = true;
        
        this.brickGroup.add(doorOpening);
        
        console.log('[AnimatedBrickWall] Door opening created');
    }

    /**
     * Create magical particle effects during animation
     */
    createParticleEffects() {
        const particleCount = 50;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        
        for (let i = 0; i < particleCount; i++) {
            // Position particles around the wall
            positions[i * 3] = (Math.random() - 0.5) * this.wallLength;
            positions[i * 3 + 1] = Math.random() * 4.0;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 2.0;
            
            // Golden magical color
            colors[i * 3] = 1.0;     // R
            colors[i * 3 + 1] = 0.8; // G
            colors[i * 3 + 2] = 0.2; // B
        }
        
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        
        const particleMaterial = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });
        
        this.particles = new THREE.Points(particles, particleMaterial);
        this.particles.position.copy(this.wallPosition);
        this.particles.rotation.y = this.wallRotation;
        
        this.scene.add(this.particles);
    }

    /**
     * Update particle effects during animation
     */
    updateParticles(progress) {
        if (!this.particles) return;
        
        const positions = this.particles.geometry.attributes.position.array;
        
        for (let i = 0; i < positions.length; i += 3) {
            // Make particles swirl and fade
            positions[i + 1] += 0.02; // Move up
            positions[i] += Math.sin(Date.now() * 0.01 + i) * 0.01; // Swirl
            positions[i + 2] += Math.cos(Date.now() * 0.01 + i) * 0.01;
        }
        
        this.particles.geometry.attributes.position.needsUpdate = true;
        this.particles.material.opacity = 0.8 * (1 - progress * 0.5);
    }

    /**
     * Reverse the animation (close the door)
     */
    async reverseAnimation() {
        if (this.isAnimating || !this.isRevealed) {
            return;
        }
        
        console.log('[AnimatedBrickWall] Reversing brick animation...');
        this.isAnimating = true;
        
        // Show all bricks again
        this.bricks.forEach(brick => {
            brick.visible = true;
            brick.material.opacity = 1;
            brick.material.transparent = false;
        });
        
        // Remove door opening
        const doorOpening = this.brickGroup.children.find(child => child.userData.isDoorOpening);
        if (doorOpening) {
            this.brickGroup.remove(doorOpening);
        }
        
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                const progress = Math.min(elapsed / this.animationDuration, 1);
                const reverseProgress = 1 - progress;
                
                // Move bricks back to original positions
                this.bricks.forEach((brick, index) => {
                    const originalPos = this.originalPositions[index];
                    const targetPos = this.targetPositions[index];
                    
                    brick.position.lerpVectors(targetPos, originalPos, progress);
                    
                    // Reverse rotation
                    const rotationAmount = reverseProgress * Math.PI * (Math.random() > 0.5 ? 1 : -1);
                    brick.rotation.x = rotationAmount * 0.3;
                    brick.rotation.y = rotationAmount * 0.5;
                    brick.rotation.z = rotationAmount * 0.2;
                });
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.completeReverseAnimation();
                    resolve();
                }
            };
            
            animate();
        });
    }

    /**
     * Complete the reverse animation
     */
    completeReverseAnimation() {
        console.log('[AnimatedBrickWall] Reverse animation complete');
        
        this.isAnimating = false;
        this.isRevealed = false;
        this.animationProgress = 0;
        
        // Reset all brick rotations
        this.bricks.forEach(brick => {
            brick.rotation.set(0, 0, 0);
        });
    }

    /**
     * Clean up the animated wall
     */
    dispose() {
        if (this.particles) {
            this.scene.remove(this.particles);
        }
        
        if (this.brickGroup) {
            this.scene.remove(this.brickGroup);
        }
        
        // Dispose of geometries and materials
        this.bricks.forEach(brick => {
            brick.geometry.dispose();
            brick.material.dispose();
        });
        
        console.log('[AnimatedBrickWall] Disposed');
    }
}
