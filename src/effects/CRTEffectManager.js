/**
 * CRT Effect Manager
 * Controls the CRT shader effect with different manufacturer presets
 */
import * as THREE from 'three';
import { EffectComposer } from '/libs/three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from '/libs/three/examples/jsm/postprocessing/RenderPass.js';
import { ShaderPass } from '/libs/three/examples/jsm/postprocessing/ShaderPass.js';
import { CRTShader } from '../shaders/CRTShader.js';

class CRTEffectManager {
    constructor(renderer, scene, camera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;
        this.clock = new THREE.Clock();
        this.enabled = false;
        this.currentPreset = 'none';

        // Initialize renderer properties to ensure compatibility
        renderer.outputEncoding = THREE.sRGBEncoding;
        renderer.autoClear = true;

        // Set tone mapping for better brightness
        if (!renderer.toneMapping) {
            renderer.toneMapping = THREE.ACESFilmicToneMapping;
            renderer.toneMappingExposure = 1.2; // Slightly brighter default
        }

        // Create render targets with proper format
        const renderTargetParameters = {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
        };

        // Initialize composer with proper render target
        // Ensure dimensions are never zero to prevent WebGL errors
        const renderTarget = new THREE.WebGLRenderTarget(
            Math.max(1, window.innerWidth),
            Math.max(1, window.innerHeight),
            renderTargetParameters
        );
        this.composer = new EffectComposer(renderer, renderTarget);

        // Create a dedicated render pass that will always use the current camera
        this.renderPass = new RenderPass(scene, camera);
        this.composer.addPass(this.renderPass);

        // Store the original camera properties to ensure they're preserved
        if (camera) {
            this.originalCameraPosition = camera.position.clone();
            this.originalCameraQuaternion = camera.quaternion.clone();
            this.originalCameraUp = camera.up.clone();
        } else {
            console.warn('CRTEffectManager: Camera is null or undefined');
            this.originalCameraPosition = new THREE.Vector3();
            this.originalCameraQuaternion = new THREE.Quaternion();
            this.originalCameraUp = new THREE.Vector3(0, 1, 0);
        }

        // Initialize error tracking variables
        this._hadRenderError = false;
        this._composerResetAttempted = false;
        
        // Flag to skip camera restoration (used by minigames that control their own camera)
        this._skipCameraRestore = false;

        // Create CRT pass
        this.crtPass = new ShaderPass(CRTShader);
        this.composer.addPass(this.crtPass);

        // Set initial resolution
        this.updateResolution();

        // Define manufacturer presets
        this.presets = {
            'none': {
                enabled: false
            },
            'sony_trinitron': {
                enabled: true,
                phosphorProfile: 0,
                maskType: 0, // Aperture grille
                maskSize: 3.0,
                maskIntensity: 0.3,
                scanlineIntensity: 0.4,
                scanlineCount: 240.0,
                curvature: 4.0,
                vignetteIntensity: 0.2,
                bleedingAmount: 0.4,
                ghostingAmount: 0.15,
                convergenceFailureX: 0.4,
                convergenceFailureY: 0.1,
                noiseAmount: 0.02,
                flickerAmount: 0.03
            },
            'panasonic': {
                enabled: true,
                phosphorProfile: 1,
                maskType: 1, // Shadow mask
                maskSize: 2.5,
                maskIntensity: 0.25,
                scanlineIntensity: 0.3,
                scanlineCount: 200.0,
                curvature: 5.0,
                vignetteIntensity: 0.3,
                bleedingAmount: 0.3,
                ghostingAmount: 0.1,
                convergenceFailureX: 0.2,
                convergenceFailureY: 0.2,
                noiseAmount: 0.03,
                flickerAmount: 0.04
            },
            'arcade_monitor': {
                enabled: true,
                phosphorProfile: 1, // Panasonic profile
                maskType: 0, // Aperture Grille mask type
                maskSize: 2.40,
                maskIntensity: 0.75,
                scanlineIntensity: 0.04,
                scanlineCount: 100.0,
                curvature: 10.0,
                vignetteIntensity: 0.07,
                bleedingAmount: 0.58,
                ghostingAmount: 0.333,
                convergenceFailureX: 1.30,
                convergenceFailureY: 0.80,
                noiseAmount: 0.02,
                flickerAmount: 0.06
            },
            'commodore_1084': {
                enabled: true,
                phosphorProfile: 2,
                maskType: 1, // Shadow mask
                maskSize: 2.2,
                maskIntensity: 0.35,
                scanlineIntensity: 0.45,
                scanlineCount: 200.0,
                curvature: 5.5,
                vignetteIntensity: 0.25,
                bleedingAmount: 0.35,
                ghostingAmount: 0.15,
                convergenceFailureX: 0.3,
                convergenceFailureY: 0.2,
                noiseAmount: 0.04,
                flickerAmount: 0.05
            },
            'apple_iie_monitor': {
                enabled: true,
                phosphorProfile: 2,
                maskType: 1, // Shadow mask
                maskSize: 1.8,
                maskIntensity: 0.3,
                scanlineIntensity: 0.5,
                scanlineCount: 192.0,
                curvature: 7.0,
                vignetteIntensity: 0.35,
                bleedingAmount: 0.25,
                ghostingAmount: 0.1,
                convergenceFailureX: 0.5,
                convergenceFailureY: 0.1,
                noiseAmount: 0.03,
                flickerAmount: 0.04
            }
        };

        // Add window resize listener
        window.addEventListener('resize', this.updateResolution.bind(this));
    }

    /**
     * Update shader resolution when window is resized
     */
    updateResolution() {
        // Get current dimensions
        const width = window.innerWidth;
        const height = window.innerHeight;
        const aspect = width / height;

        // Ensure we have valid dimensions to prevent framebuffer errors
        if (width <= 0 || height <= 0) {
            console.warn('Invalid dimensions for CRT effect:', width, height);
            return;
        }

        // Update camera aspect ratio if it's a perspective camera
        if (this.camera.isPerspectiveCamera) {
            this.camera.aspect = aspect;
            this.camera.updateProjectionMatrix();
        }
        // Update orthographic camera frustum if it's an orthographic camera
        else if (this.camera.isOrthographicCamera) {
            const frustumSize = 12; // Match the value used in DungeonHandler
            this.camera.left = frustumSize * aspect / -2;
            this.camera.right = frustumSize * aspect / 2;
            this.camera.top = frustumSize / 2;
            this.camera.bottom = frustumSize / -2;
            this.camera.updateProjectionMatrix();
        }

        // Update renderer size
        this.renderer.setSize(width, height);

        // Create new render target with proper dimensions
        const renderTargetParameters = {
            minFilter: THREE.LinearFilter,
            magFilter: THREE.LinearFilter,
            format: THREE.RGBAFormat,
            stencilBuffer: false
        };

        const newRenderTarget = new THREE.WebGLRenderTarget(
            Math.max(1, width),
            Math.max(1, height),
            renderTargetParameters
        );

        // Reset the composer with new render target
        this.composer.reset(newRenderTarget);

        // Update composer size after reset
        this.composer.setSize(Math.max(1, width), Math.max(1, height));

        // Recreate passes if needed
        if (!this.composer.passes.includes(this.renderPass)) {
            this.composer.addPass(this.renderPass);
        }

        if (!this.composer.passes.includes(this.crtPass)) {
            this.composer.addPass(this.crtPass);
        }

        // Update resolution uniform for the shader
        this.crtPass.uniforms.resolution.value.set(width, height);

        // Make sure the render pass is using the current camera
        this.renderPass.camera = this.camera;
    }

    /**
     * Apply a preset configuration
     * @param {string} presetName - Name of the preset to apply
     */
    applyPreset(presetName) {
        if (!this.presets[presetName]) {
            console.warn(`CRT preset "${presetName}" not found`);
            return;
        }

        const preset = this.presets[presetName];
        this.currentPreset = presetName;
        this.enabled = preset.enabled;

        // Apply all preset values to shader uniforms
        if (preset.enabled) {
            for (const [key, value] of Object.entries(preset)) {
                if (key !== 'enabled' && this.crtPass.uniforms[key] !== undefined) {
                    this.crtPass.uniforms[key].value = value;
                }
            }
        }
    }

    /**
     * Toggle the CRT effect on/off
     */
    toggle() {
        this.enabled = !this.enabled;
        return this.enabled;
    }

    /**
     * Enable the CRT effect
     */
    enable() {
        this.enabled = true;
        return this.enabled;
    }

    /**
     * Enable line-by-line refresh visualization
     * @param {boolean} enable - Whether to enable the visualization
     * @param {number} speed - Speed of the refresh (0.0-1.0)
     */
    enableRefreshVisualization(enable, speed = 0.1) {
        this.refreshVisualizationEnabled = enable;
        this.crtPass.uniforms.refreshSpeed.value = speed;
    }

    /**
     * Update the effect (call this in your animation loop)
     * @param {boolean} [skipRendering=false] - Skip rendering and just update uniforms
     */
    update(skipRendering = false) {
        if (!this.enabled && !skipRendering) {
            // If disabled and not just updating uniforms, render normally
            this.renderer.render(this.scene, this.camera);
            return;
        }

        // Update time uniform
        const delta = this.clock.getDelta();
        this.crtPass.uniforms.time.value += delta;

        // Update refresh scanline position if visualization is enabled
        if (this.refreshVisualizationEnabled) {
            // Scanline moves from top to bottom
            const currentPos = this.crtPass.uniforms.refreshScanline.value;
            this.crtPass.uniforms.refreshScanline.value =
                (currentPos + delta * this.crtPass.uniforms.refreshSpeed.value) % 1.0;
        }

        // If we're just updating uniforms, don't render
        if (skipRendering) {
            return;
        }

        // IMPORTANT: Make sure the render pass is using the current camera
        // This ensures the perspective doesn't change when the CRT effect is enabled
        if (this.camera) {
            this.renderPass.camera = this.camera;

            try {
                // Store ALL camera properties before rendering
                // Position, rotation, and up vector
                this.originalCameraPosition.copy(this.camera.position);
                this.originalCameraQuaternion.copy(this.camera.quaternion);
                this.originalCameraUp.copy(this.camera.up);

                // Store projection matrix and other properties
                const originalZoom = this.camera.zoom || 1;
                const originalNear = this.camera.near || 0.1;
                const originalFar = this.camera.far || 1000;
                const originalFov = this.camera.fov || 75;
                const originalAspect = this.camera.aspect || (window.innerWidth / window.innerHeight);
                const originalLeft = this.camera.left || -1;
                const originalRight = this.camera.right || 1;
                const originalTop = this.camera.top || 1;
                const originalBottom = this.camera.bottom || -1;

                // Update CRT pass uniforms with camera properties
                if (this.crtPass && this.crtPass.uniforms) {
                    if (this.crtPass.uniforms.cameraZoom) {
                        this.crtPass.uniforms.cameraZoom.value = originalZoom;
                    }
                }
            } catch (error) {
                console.warn('Error storing camera properties:', error);
            }
        } else {
            console.warn('Camera is null or undefined in CRTEffectManager.update');
        }

        // Store the camera's projection matrix
        const originalProjectionMatrix = this.camera ? this.camera.projectionMatrix.clone() : new THREE.Matrix4();

        // Check for valid dimensions to prevent framebuffer errors
        const viewportWidth = this.renderer.getSize(new THREE.Vector2()).width;
        const viewportHeight = this.renderer.getSize(new THREE.Vector2()).height;

        if (viewportWidth <= 0 || viewportHeight <= 0) {
            console.warn('Invalid viewport dimensions:', viewportWidth, viewportHeight);
            // Fall back to normal rendering
            this.renderer.render(this.scene, this.camera);
            return;
        }

        // Check if the framebuffer is valid before rendering
        const gl = this.renderer.getContext();

        try {
            // Render with post-processing
            this.composer.render();

            // If we get here, rendering was successful, clear any error flags
            if (this._hadRenderError) {
                console.log('CRT effect rendering recovered successfully');
                this._hadRenderError = false;
            }
        } catch (error) {
            // Only log the first error to avoid console spam
            if (!this._hadRenderError) {
                console.warn('Error in CRT effect rendering, falling back to normal rendering');
                this._hadRenderError = true;
            }

            // Fall back to normal rendering
            this.renderer.render(this.scene, this.camera);

            // Try to reset the composer if this is the first error
            if (!this._composerResetAttempted) {
                try {
                    console.log('Attempting to reset CRT effect composer...');
                    this.updateResolution();
                    this._composerResetAttempted = true;

                    // Schedule another reset attempt after a delay
                    setTimeout(() => {
                        this._composerResetAttempted = false;
                    }, 5000); // Wait 5 seconds before allowing another reset
                } catch (resetError) {
                    console.error('Failed to reset CRT effect');
                }
            }
        }

        // Restore ALL camera properties after rendering (unless disabled for minigames)
        // Position, rotation, and up vector
        if (!this._skipCameraRestore) {
            this.camera.position.copy(this.originalCameraPosition);
            this.camera.quaternion.copy(this.originalCameraQuaternion);
            this.camera.up.copy(this.originalCameraUp);
        }

        // Restore projection matrix and other properties
        this.camera.zoom = originalZoom;
        this.camera.near = originalNear;
        this.camera.far = originalFar;

        if (this.camera.isPerspectiveCamera) {
            this.camera.fov = originalFov;
            this.camera.aspect = originalAspect;
        } else if (this.camera.isOrthographicCamera) {
            this.camera.left = originalLeft;
            this.camera.right = originalRight;
            this.camera.top = originalTop;
            this.camera.bottom = originalBottom;
        }

        // Restore the camera's projection matrix
        this.camera.projectionMatrix.copy(originalProjectionMatrix);
    }

    /**
     * Create a custom preset
     * @param {string} name - Name for the new preset
     * @param {Object} settings - Preset settings
     */
    createCustomPreset(name, settings) {
        this.presets[name] = {
            enabled: true,
            ...settings
        };
    }

    /**
     * Apply a custom preset from saved settings
     * @param {Object} settings - The preset settings to apply
     */
    applyCustomPreset(settings) {
        if (!settings) return;

        // Set enabled state
        this.enabled = settings.enabled !== undefined ? settings.enabled : true;

        // Apply all settings to shader uniforms
        for (const [key, value] of Object.entries(settings)) {
            if (key !== 'enabled' && this.crtPass.uniforms[key] !== undefined) {
                this.crtPass.uniforms[key].value = value;
            }
        }

        // Set current preset to indicate a custom preset is active
        this.currentPreset = 'saved:custom';
    }

    /**
     * Get current preset name
     * @returns {string} Current preset name
     */
    getCurrentPreset() {
        return this.currentPreset;
    }

    /**
     * Get list of available presets
     * @returns {string[]} Array of preset names
     */
    getPresetList() {
        return Object.keys(this.presets);
    }

    /**
     * Adjust a specific parameter
     * @param {string} param - Parameter name
     * @param {number} value - New value
     */
    adjustParameter(param, value) {
        if (this.crtPass.uniforms[param] !== undefined) {
            this.crtPass.uniforms[param].value = value;
        }
    }

    /**
     * Find the DungeonHandler instance in the scene
     * @returns {Object|null} The DungeonHandler instance or null if not found
     */
    findDungeonHandler() {
        // Try to find the DungeonHandler by looking for the player object
        const player = this.scene.getObjectByName('player');
        if (player && player.userData && player.userData.dungeonHandler) {
            return player.userData.dungeonHandler;
        }

        // Alternative approach: check if there's a global reference
        if (window.sceneManager && window.sceneManager.activeSceneHandler) {
            const handler = window.sceneManager.activeSceneHandler;
            if (handler.isTopDownView !== undefined) {
                return handler;
            }
        }

        return null;
    }

    /**
     * Trigger anime-style lens distortion for heavy attacks (Dragon Ball Z style)
     * @param {number} strength - Distortion strength (0.0 to 1.0)
     * @param {number} duration - Duration in milliseconds
     * @param {number} type - Distortion type (0=barrel, 1=pincushion, 2=wave)
     * @param {Object} center - Center point {x, y} (0-1 range), defaults to screen center
     */
    triggerLensDistortion(strength = 0.5, duration = 800, type = 0, center = {x: 0.5, y: 0.5}) {
        if (!this.crtPass || !this.enabled) return;

        console.log(`[CRTEffectManager] 🌊 Triggering anime-style lens distortion: strength=${strength}, duration=${duration}ms, type=${type}`);

        const uniforms = this.crtPass.uniforms;
        const maxStrength = Math.min(1.0, strength);
        
        // Set distortion parameters
        uniforms.lensDistortionCenter.value.set(center.x, center.y);
        uniforms.lensDistortionType.value = type;
        uniforms.lensDistortionRadius.value = 0.6; // Wide radius for dramatic effect
        
        // Set zoom compensation based on distortion strength to prevent black edges
        const zoomCompensation = 1.0 + maxStrength * 0.08; // Zoom in by up to 8% for maximum distortion
        uniforms.lensDistortionZoom.value = zoomCompensation;
        
        // Animate distortion strength
        const startTime = performance.now();
        
        const animateDistortion = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(1.0, elapsed / duration);
            
            // Smooth in-out curve for natural anime effect
            const easedProgress = progress < 0.5 
                ? 2 * progress * progress 
                : 1 - Math.pow(-2 * progress + 2, 2) / 2;
            
            // Peak distortion at 30% of duration, then smooth out
            let distortionAmount;
            if (progress < 0.3) {
                distortionAmount = maxStrength * (progress / 0.3);
            } else {
                distortionAmount = maxStrength * (1.0 - (progress - 0.3) / 0.7);
            }
            
            uniforms.lensDistortionStrength.value = Math.max(0, distortionAmount);
            
            if (progress < 1.0) {
                requestAnimationFrame(animateDistortion);
            } else {
                // Ensure distortion is completely off
                uniforms.lensDistortionStrength.value = 0.0;
            }
        };
        
        requestAnimationFrame(animateDistortion);
    }

    /**
     * Trigger specific distortion effects for different attack types
     */
    triggerLaserDistortion() {
        // Intense pincushion effect from center (laser focusing energy)
        this.triggerLensDistortion(0.8, 1200, 1, {x: 0.5, y: 0.5});
    }

    triggerVoidTearDistortion(center = {x: 0.5, y: 0.5}) {
        // Wave ripple effect from impact point (reality tearing)
        this.triggerLensDistortion(0.6, 1000, 2, center);
    }

    triggerEnergyBlastDistortion(center = {x: 0.5, y: 0.5}) {
        // Barrel distortion from blast point (energy compression)
        this.triggerLensDistortion(0.7, 900, 0, center);
    }
}

export { CRTEffectManager };
