/**
 * Volumetric Fog Configuration Examples
 * 
 * This file contains example configurations for the volumetric fog system
 * to be used in event rooms and special dungeon areas.
 */

export const FOG_PRESETS = {
    // Natural pond mist for mysterious pond (NEW: More natural colors)
    mysteriousPond: {
        preset: 'pondMist', // Use the new pondMist preset
        color: 0xC4C4C4, // Natural white-gray fog instead of greenish
        density: 0.012, // Reduced for more transparency
        godrayIntensity: 0.4,
        windSpeed: 0.3,
        turbulence: 0.5,
        organicDistribution: true,
        pondAware: true,
        quality: 'high'
    },
    
    // Alternative pond fog - more mystical purple
    mysticalPondFog: {
        preset: 'mystical',
        color: 0x7A6B9A, // Soft mystical purple-gray
        density: 0.011, // Reduced for more transparency
        godrayIntensity: 0.45,
        windSpeed: 0.25,
        turbulence: 0.4,
        organicDistribution: true,
        pondAware: true,
        quality: 'high'
    },
    
    // Dense cave fog for underground areas
    deepCave: {
        preset: 'cave',
        color: 0x3a4a5a,
        density: 0.04,
        godrayIntensity: 0.15,
        turbulence: 0.2,
        quality: 'medium'
    },
    
    // Mystical fog for magical areas
    enchantedForest: {
        preset: 'mystical',
        color: 0x7a5fa5,
        density: 0.012,
        godrayIntensity: 0.7,
        turbulence: 0.6,
        windSpeed: 0.8,
        quality: 'high'
    },
    
    // Standard dungeon fog
    dungeonCorridor: {
        preset: 'dungeon',
        density: 0.015,
        godrayIntensity: 0.25,
        quality: 'medium'
    },
    
    // Natural swamp fog
    swampMist: {
        preset: 'swamp',
        color: 0x6B7B5A, // Natural gray-green
        density: 0.018, // Reduced for more transparency
        godrayIntensity: 0.35,
        turbulence: 0.45,
        organicDistribution: true,
        quality: 'high'
    },
    
    // Custom toxic fog
    toxicChamber: {
        preset: 'custom',
        color: 0x4fa545, // Toxic green
        density: 0.03,
        scattering: 0.3,
        absorption: 0.2,
        anisotropy: 0.6,
        layers: 10,
        layerFalloff: 2.5,
        godrayIntensity: 0.2,
        turbulence: 0.5,
        windSpeed: 0.4,
        quality: 'medium'
    },
    
    // Custom golden mist for treasure rooms
    goldenMist: {
        preset: 'custom',
        color: 0xffd700, // Gold
        density: 0.008,
        scattering: 0.4,
        absorption: 0.05,
        anisotropy: 0.95,
        layers: 25,
        layerFalloff: 1.2,
        godrayIntensity: 0.8,
        turbulence: 0.2,
        windSpeed: 0.2,
        quality: 'high'
    },
    
    // Spooky fog for haunted areas
    hauntedFog: {
        preset: 'custom',
        color: 0x6a6a8a,
        density: 0.025,
        scattering: 0.1,
        absorption: 0.25,
        anisotropy: 0.7,
        layers: 12,
        layerFalloff: 2.8,
        godrayIntensity: 0.1,
        turbulence: 0.7,
        windSpeed: 1.0,
        quality: 'high'
    },
    
    // Minimal fog for visibility
    lightMist: {
        preset: 'custom',
        color: 0x8a8a9a,
        density: 0.005,
        scattering: 0.1,
        layers: 5,
        layerFalloff: 3.0,
        godrayIntensity: 0.4,
        turbulence: 0.1,
        windSpeed: 0.1,
        quality: 'low'
    },
    
    // NEW: Pure pond mist preset
    pondMist: {
        preset: 'pondMist',
        color: 0xC4C4C4, // Natural white-gray fog
        density: 0.012, // Reduced for more transparency
        scattering: 0.22,
        absorption: 0.08,
        anisotropy: 0.7,
        layers: 15,
        layerFalloff: 1.4,
        godrayIntensity: 0.4,
        turbulence: 0.5,
        organicDistribution: true,
        pondAware: true,
        quality: 'high'
    }
};

/**
 * Helper function to get fog configuration
 */
export function getFogConfig(presetName) {
    return FOG_PRESETS[presetName] || FOG_PRESETS.dungeonCorridor;
}

/**
 * Example usage in an event room:
 * 
 * import { createUnrealFog } from '../../effects/VolumetricFogManager.js';
 * import { getFogConfig } from '../../effects/VolumetricFogPresets.js';
 * 
 * // In your room's initFog() method:
 * this.state.fogSystem = createUnrealFog(
 *     this.dungeonHandler.scene,
 *     this.dungeonHandler.currentRoomGroup,
 *     getFogConfig('mysteriousPond')
 * );
 * 
 * // Alternative - Custom configuration:
 * this.state.fogSystem = createUnrealFog(
 *     this.dungeonHandler.scene,
 *     this.dungeonHandler.currentRoomGroup,
 *     {
 *         preset: 'pondMist',
 *         color: 0x8A9B7A,          // Natural gray-green
 *         density: 0.018,           // Slightly denser
 *         organicDistribution: true, // Use organic patches
 *         pondAware: true,          // Avoid pond areas
 *         quality: 'high'
 *     }
 * );
 */ 