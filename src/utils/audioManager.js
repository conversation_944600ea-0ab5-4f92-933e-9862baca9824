import { MusicConductor } from '../audio/MusicConductor.js';

class AudioManager {
    constructor() {
        this.sounds = {};
        this.isMuted = false;
        this.audioContext = null;
        this.masterGain = null;
        this.isMobile = this._detectMobile();
        this.mobileAudioUnlocked = false;

        // Initialize the music conductor
        this.musicConductor = new MusicConductor();

        this._loadSounds();
        this._initAudioContext();

        // Setup mobile audio unlock if needed
        if (this.isMobile) {
            this._setupMobileAudioUnlock();
        }
    }

    _initAudioContext() {
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            console.log("AudioContext initialized.");
        } catch (e) {
            console.error("Web Audio API is not supported in this browser.");
            // Fallback or disable audio features
        }
    }

    _loadSounds() {
        // Map keys to the corresponding HTMLAudioElement IDs
        const soundMap = {
            'chat': 'chat-sound',
            'footstep': 'footstep-sound',
            'bg_ambient_surround': 'bg-music',
            'bg_ambient_music': 'ambient-music-track',
            'reveal': 'reveal-sound',
            'select': 'select-sound',
            'start_button': 'start-button-sound',
            'creepy_noise': 'creepy-noise-sound',
            'stuckinmydreams': 'stuckinmydreams-sound',
            'erasure': 'erasure-sound',
            'erasure2': 'erasure2-sound',
            'flicker_laugh': 'flicker_laugh_sound',
            'flicker_laugh2': 'flicker_laugh2_sound',
            'flicker_craft': 'flicker_craft_sound',
            'drink': 'drink_sound',
            'flicker_command': 'flicker_command_sound',
            'opening': 'opening_sound',
            'main_theme': 'main-theme-sound'
        };

        for (const key in soundMap) {
            const element = document.getElementById(soundMap[key]);
            if (element) {
                this.sounds[key] = element;
                console.log(`[AudioManager] Loaded sound '${key}' - src: ${element.src}`);

                // Add mobile debugging for large files (main_theme has special handling)
                if (this.isMobile && key === 'bg_ambient_music') {
                    console.log(`[AudioManager] Mobile large file ${key} - readyState: ${element.readyState}, networkState: ${element.networkState}`);

                    element.addEventListener('loadstart', () => {
                        console.log(`[AudioManager] ${key} loadstart event`);
                    });

                    element.addEventListener('loadedmetadata', () => {
                        console.log(`[AudioManager] ${key} loadedmetadata event - duration: ${element.duration}`);
                    });

                    element.addEventListener('canplay', () => {
                        console.log(`[AudioManager] ${key} canplay event`);
                    });

                    element.addEventListener('error', (e) => {
                        console.error(`[AudioManager] ${key} error event:`, e, element.error);
                    });
                }

                // Special debugging for main theme on mobile
                if (this.isMobile && key === 'main_theme') {
                    console.log(`[AudioManager] Main theme setup - readyState: ${element.readyState}, muted: ${element.muted}`);

                    element.addEventListener('play', () => {
                        console.log(`[AudioManager] Main theme play event - muted: ${element.muted}`);
                    });

                    element.addEventListener('pause', () => {
                        console.log(`[AudioManager] Main theme pause event`);
                    });
                }
            } else {
                console.warn(`[AudioManager] Audio element with ID '${soundMap[key]}' not found for key '${key}'.`);
            }
        }
    }

    _detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }

    _setupMobileAudioUnlock() {
        // Just prepare for mobile audio unlock, don't do it immediately
        console.log("[AudioManager] Mobile device detected - audio unlock will happen on first play");
    }

    _unlockMobileAudio() {
        console.log('[AudioManager] Mobile audio unlock triggered');

        // Main theme should be handled by first-touch listeners in HTML
        // Just prepare other large music files for mobile
        const largeMusicFiles = ['bg_ambient_music'];

        largeMusicFiles.forEach(key => {
            const sound = this.sounds[key];
            if (sound) {
                console.log(`[AudioManager] Preparing ${key} for mobile`);

                // Set up load on demand for mobile
                sound.addEventListener('loadstart', () => {
                    console.log(`[AudioManager] ${key} started loading`);
                });

                sound.addEventListener('canplaythrough', () => {
                    console.log(`[AudioManager] ${key} can play through`);
                });

                sound.addEventListener('error', (e) => {
                    console.error(`[AudioManager] ${key} loading error:`, e);
                });
            }
        });
    }

    _resumeContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            console.log("Attempting to resume suspended AudioContext...");
            this.audioContext.resume().then(() => {
                console.log("AudioContext resumed successfully.");
            }).catch(e => console.error("Error resuming AudioContext:", e));
        }
    }

    playSound(key, loop = false, volume = 1.0, customLoopEnd = null) {
        this._resumeContext(); // Attempt to resume context on interaction

        // Mobile audio unlock on first play attempt
        if (this.isMobile && !this.mobileAudioUnlocked) {
            this._unlockMobileAudio();
            this.mobileAudioUnlocked = true;
        }

        const sound = this.sounds[key];
        if (sound && !this.isMuted) {
            try {
                // Main theme handling - prioritize HTML autoplay
                if (key === 'main_theme') {
                    console.log(`[AudioManager] Main theme playback requested - Mobile: ${this.isMobile}, Paused: ${sound.paused}`);

                    // Check if HTML autoplay is already working
                    if (!sound.paused) {
                        console.log('[AudioManager] Main theme already playing from HTML autoplay - NOT interfering');
                        // Just ensure correct settings without stopping/restarting
                        sound.volume = volume;
                        sound.loop = loop;
                        return;
                    }

                    // Check if this is mobile and we should wait for HTML autoplay
                    if (this.isMobile) {
                        console.log('[AudioManager] Mobile detected - checking if HTML autoplay will handle this');

                        // Give HTML autoplay a chance to work first
                        setTimeout(() => {
                            if (sound.paused) {
                                console.log('[AudioManager] HTML autoplay did not start - trying AudioManager play');
                                sound.volume = volume;
                                sound.loop = loop;

                                const playPromise = sound.play();
                                if (playPromise !== undefined) {
                                    playPromise.then(() => {
                                        console.log('[AudioManager] Main theme started via AudioManager fallback');
                                    }).catch(e => {
                                        console.log('[AudioManager] AudioManager fallback also failed:', e.message);
                                    });
                                }
                            } else {
                                console.log('[AudioManager] HTML autoplay started working - AudioManager will not interfere');
                                sound.volume = volume;
                                sound.loop = loop;
                            }
                        }, 500); // Wait 500ms for HTML autoplay to kick in

                        return;
                    }

                    // Desktop: normal approach
                    sound.volume = volume;
                    sound.loop = loop;

                    console.log('[AudioManager] Desktop - attempting normal play for main theme');
                    const playPromise = sound.play();
                    if (playPromise !== undefined) {
                        playPromise.then(() => {
                            console.log('[AudioManager] Main theme started successfully on desktop');
                        }).catch(e => {
                            console.log('[AudioManager] Main theme play failed on desktop:', e.message);
                        });
                    }
                    return;
                }

                // Mobile-specific handling for other large music files
                if (this.isMobile && key === 'bg_ambient_music') {
                    // Ensure the audio is loaded before trying to play
                    if (sound.readyState < 2) { // HAVE_CURRENT_DATA
                        console.log(`[AudioManager] Loading ${key} on mobile before playing`);
                        sound.load();

                        // Wait for it to be ready to play
                        const canPlayHandler = () => {
                            sound.removeEventListener('canplay', canPlayHandler);
                            this._playAudioElement(sound, key, loop, volume, customLoopEnd);
                        };
                        sound.addEventListener('canplay', canPlayHandler);
                        return;
                    }
                }

                this._playAudioElement(sound, key, loop, volume, customLoopEnd);
            } catch (error) {
                console.error(`Error trying to play sound '${key}':`, error);
            }
        } else if (!sound) {
            console.warn(`[AudioManager] Sound key '${key}' not found.`);
        }
    }

    _playAudioElement(sound, key, loop = false, volume = 1.0, customLoopEnd = null) {
        // For main theme, use custom loop timing
        if (key === 'main_theme' && loop && customLoopEnd) {
            sound.loop = false; // Disable native loop
            sound.volume = volume;
            sound.currentTime = 0;

            // Set up custom loop with precise timing
            const handleTimeUpdate = () => {
                if (sound.currentTime >= customLoopEnd) {
                    sound.currentTime = 0; // Loop back to start
                }
            };

            // Remove any existing listener to avoid duplicates
            sound.removeEventListener('timeupdate', sound._customLoopHandler);
            sound._customLoopHandler = handleTimeUpdate;
            sound.addEventListener('timeupdate', handleTimeUpdate);

            // Also handle when the track ends naturally
            const handleEnded = () => {
                if (!this.isMuted && this.sounds[key] === sound) {
                    sound.currentTime = 0;
                    sound.play().catch(e => console.warn(`Error restarting ${key}:`, e));
                }
            };

            sound.removeEventListener('ended', sound._customEndHandler);
            sound._customEndHandler = handleEnded;
            sound.addEventListener('ended', handleEnded);
        } else {
            // Standard playback
            sound.loop = loop;
            sound.volume = volume;
            sound.currentTime = 0;
        }

        const playPromise = sound.play();
        if (playPromise !== undefined) {
            playPromise.catch(error => {
                console.error(`Error playing sound '${key}':`, error);

                // Mobile-specific retry for large files (excluding main_theme which has special handling)
                if (this.isMobile && key === 'bg_ambient_music') {
                    console.log(`[AudioManager] Retrying ${key} on mobile after error`);
                    setTimeout(() => {
                        sound.load();
                        setTimeout(() => {
                            sound.play().catch(e => console.warn(`Retry failed for ${key}:`, e));
                        }, 500);
                    }, 100);
                }
            });
        }
    }

    stopSound(key) {
        const sound = this.sounds[key];
        if (sound) {
            // Clean up custom loop handlers
            if (sound._customLoopHandler) {
                sound.removeEventListener('timeupdate', sound._customLoopHandler);
                sound._customLoopHandler = null;
            }
            if (sound._customEndHandler) {
                sound.removeEventListener('ended', sound._customEndHandler);
                sound._customEndHandler = null;
            }

            sound.pause();
            sound.currentTime = 0;
        }
    }

    stopAllSounds() {
        console.log("[AudioManager] Stopping all sounds.");
        for (const key in this.sounds) {
             this.stopSound(key);
        }
    }

    // Example fade function (more robust fading needed)
    fadeVolume(key, targetVolume, duration = 1000) {
        const sound = this.sounds[key];
        if (!sound) return;

        const startVolume = sound.volume;
        const startTime = performance.now();

        const fade = (currentTime) => {
            const elapsedTime = currentTime - startTime;
            const progress = Math.min(elapsedTime / duration, 1);
            // Clamp volume to valid range [0, 1] to prevent errors
            const newVolume = startVolume + (targetVolume - startVolume) * progress;
            sound.volume = Math.max(0, Math.min(1, newVolume));

            if (progress < 1) {
                requestAnimationFrame(fade);
            } else {
                 if (targetVolume === 0) {
                      this.stopSound(key);
                 }
                 console.log(`[AudioManager] Fade complete for '${key}' to volume ${targetVolume}`);
            }
        };
        requestAnimationFrame(fade);
    }

    toggleMute(buttonElement) {
        this.isMuted = !this.isMuted;
        console.log(`[AudioManager] Mute toggled: ${this.isMuted}`);
        if (this.masterGain) {
             this.masterGain.gain.setValueAtTime(this.isMuted ? 0 : 1, this.audioContext.currentTime);
        }
        // Also pause/resume looping sounds
        for (const key in this.sounds) {
            if (this.sounds[key].loop) {
                 if (this.isMuted) {
                     this.sounds[key].pause();
                 } else {
                     this.sounds[key].play().catch(e => console.warn(`Could not resume loop for ${key}:`, e));
                 }
            }
        }
        if (buttonElement) {
             buttonElement.textContent = this.isMuted ? 'Unmute' : 'Mute';
        }

        // Also mute/unmute the music conductor
        if (this.musicConductor && this.musicConductor.initialized) {
            if (this.isMuted) {
                // Mute music conductor
                if (this.musicConductor.effects && this.musicConductor.effects.mainVolume) {
                    this.musicConductor.effects.mainVolume.volume.value = -Infinity;
                }
            } else {
                // Unmute music conductor
                if (this.musicConductor.effects && this.musicConductor.effects.mainVolume) {
                    this.musicConductor.effects.mainVolume.volume.value = 0;
                }
            }
        }
    }

    // Basic Mute Button UI
     _createMuteButton() {
         const button = document.createElement('button');
         button.id = 'mute-button'; // Use ID from HTML if exists, or style new one
         button.textContent = 'Mute';
         button.style.position = 'absolute';
         button.style.top = '10px';
         button.style.right = '10px';
         button.style.padding = '5px 10px';
         button.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
         button.style.border = '1px solid #fff';
         button.style.borderRadius = '5px';
         button.style.color = '#fff';
         button.style.pointerEvents = 'auto';
         button.style.cursor = 'pointer';
         button.style.zIndex = '100';
         button.onclick = () => this.toggleMute(button);
         document.body.appendChild(button);
     }
    // --- Music System Methods ---

    /**
     * Initialize the music system
     * Must be called after user interaction
     */
    async initMusicSystem() {
        // Block music system initialization during dialogue (unless in debug mode)
        if (window.dungeonEntryDialogue && !window.debugSkipDungeonDialogue) {
            console.log("[AudioManager] Blocking music system initialization - dialogue active");
            return false;
        }

        if (this.musicConductor) {
            return await this.musicConductor.init();
        }
        return false;
    }

    /**
     * Start playing music for an area
     * @param {string} areaName - The area ID to start music for
     */
    async startAreaMusic(areaName) {
        // Check if we should delay music system interactions during dialogue (unless in debug mode)
        if (window.dungeonHandler && window.dungeonHandler.waitingForEntryDialogue && !window.debugSkipDungeonDialogue) {
            console.log("[AudioManager] Skipping area music start during entry dialogue");
            return false;
        }

        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.start(areaName);
        } else if (this.musicConductor) {
            // Try to initialize first
            await this.musicConductor.init();
            return await this.musicConductor.start(areaName);
        }
        return false;
    }

    /**
     * Transition to a new area with proper musical transition
     * @param {string} areaName - The area ID to transition to
     */
    async transitionAreaMusic(areaName) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.transitionTo(areaName);
        }
        return false;
    }

    /**
     * Enter a special room within the current area
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     * @param {boolean} immediate - Whether to transition immediately (default: true for event rooms)
     */
    async enterSpecialRoomMusic(area, room, immediate = true) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.enterSpecialRoom(area, room, immediate);
        }
        return false;
    }

    /**
     * Exit a special room and return to the main area music
     * @param {string} area - The area ID
     * @param {string} room - The room type (e.g., "shop", "altar")
     * @param {boolean} immediate - Whether to transition immediately (default: true for event rooms)
     */
    async exitSpecialRoomMusic(area, room, immediate = true) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.exitSpecialRoom(area, room, immediate);
        }
        return false;
    }

    /**
     * Update combat state based on enemy count and player health
     * @param {number} enemyCount - Number of active enemies
     * @param {number} currentHealth - Current player health
     * @param {number} maxHealth - Maximum player health
     * @param {boolean} enemyKilled - Whether an enemy was just killed
     * @param {boolean} enteredRoom - Whether the player just entered a room
     */
    updateCombatState(enemyCount, currentHealth, maxHealth, enemyKilled = false, enteredRoom = false) {
        // Check if we should delay music system interactions during dialogue (unless in debug mode)
        if (window.dungeonHandler && window.dungeonHandler.waitingForEntryDialogue && !window.debugSkipDungeonDialogue) {
            console.log("[AudioManager] Skipping combat state update during entry dialogue");
            return false;
        }

        if (this.musicConductor && this.musicConductor.initialized) {
            return this.musicConductor.updateCombatState(enemyCount, currentHealth, maxHealth, enemyKilled, enteredRoom);
        }
        return false;
    }

    /**
     * Handle player being hit
     * Plays hit stinger and applies temporary effects
     */
    onPlayerHit() {
        // Check if we should delay music system interactions during dialogue (unless in debug mode)
        if (window.dungeonHandler && window.dungeonHandler.waitingForEntryDialogue && !window.debugSkipDungeonDialogue) {
            console.log("[AudioManager] Skipping player hit music event during entry dialogue");
            return false;
        }

        if (this.musicConductor && this.musicConductor.initialized) {
            return this.musicConductor.onPlayerHit();
        }
        return false;
    }

    /**
     * Toggle debug mode for the music system
     */
    toggleMusicDebug() {
        if (this.musicConductor) {
            return this.musicConductor.toggleDebug();
        }
        return false;
    }

    /**
     * Enter miniboss music within the current area
     * @param {string} area - The area ID
     * @param {string} minibossType - The miniboss type (e.g., "nairabos", "sairabos")
     */
    async enterMinibossMusic(area, minibossType) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.enterMiniboss(area, minibossType, true); // Always immediate for miniboss
        }
        return false;
    }

    /**
     * Exit miniboss music and return to previous music (event room or area music)
     * @param {string} area - The area ID
     * @param {string} minibossType - The miniboss type (e.g., "nairabos", "sairabos")
     */
    async exitMinibossMusic(area, minibossType) {
        if (this.musicConductor && this.musicConductor.initialized) {
            return await this.musicConductor.exitMiniboss(area, minibossType, true); // Always immediate for miniboss
        }
        return false;
    }

    /**
     * Clean up music system resources
     */
    disposeMusicSystem() {
        if (this.musicConductor) {
            this.musicConductor.dispose();
        }
    }
}

export default AudioManager;