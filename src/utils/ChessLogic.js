/**
 * ChessLogic - Pure functions for core chess game logic.
 * These functions operate on a given board state and other game parameters,
 * without relying on 'this' context, making them reusable for both ChessGame and ChessAI.
 */

export class ChessLogic {
    /**
     * Check if coordinates are on the board
     */
    static isOnBoard(x, y) {
        return x >= 0 && x < 8 && y >= 0 && y < 8;
    }

    /**
     * Check if a square is attacked by the enemy
     */
    static isSquareAttacked(board, x, y, byColor, kingPositions) {
        for (let boardX = 0; boardX < 8; boardX++) {
            for (let boardY = 0; boardY < 8; boardY++) {
                const piece = board[boardX][boardY];
                if (piece && piece.color === byColor) {
                    // Temporarily set the king position for the purpose of this check
                    // This is crucial for the AI's simulation of moves
                    const originalKingPos = kingPositions[piece.color];
                    if (piece.type === 'king') {
                        kingPositions[piece.color] = { x: boardX, y: boardY };
                    }

                    const moves = ChessLogic.getPieceMoves(board, piece, false, kingPositions); // Don't check for check to avoid recursion
                    
                    // Restore original king position
                    if (piece.type === 'king') {
                        kingPositions[piece.color] = originalKingPos;
                    }

                    if (moves.some(move => move.x === x && move.y === y)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * Check if a color is in check
     */
    static isInCheck(board, color, kingPositions) {
        const kingPos = kingPositions[color];
        if (!kingPos) {
            // This should not happen in a valid game state
            console.error('[ChessLogic] King not found for color:', color);
            return false;
        }
        return ChessLogic.isSquareAttacked(board, kingPos.x, kingPos.y, color === 'white' ? 'black' : 'white', kingPositions);
    }

    /**
     * Check if a move would put own king in check
     */
    static wouldBeInCheck(board, color, from, to, kingPositions, castlingRights, enPassantTarget) {
        // Simulate the move on a temporary board
        const tempBoard = board.map(row => row.slice()); // Deep copy
        const tempKingPositions = {
            white: { ...kingPositions.white },
            black: { ...kingPositions.black }
        };
        const tempCastlingRights = {
            white: { ...castlingRights.white },
            black: { ...castlingRights.black }
        };
        const tempEnPassantTarget = enPassantTarget ? { ...enPassantTarget } : null;

        const originalPiece = tempBoard[to.x][to.y];
        const movingPiece = { ...tempBoard[from.x][from.y] }; // Copy piece to avoid modifying original

        tempBoard[to.x][to.y] = movingPiece;
        tempBoard[from.x][from.y] = null;

        // Update piece's internal coordinates for the simulated move
        movingPiece.x = to.x;
        movingPiece.y = to.y;

        // Handle special moves for simulation (simplified for check detection)
        // Pawn promotion
        if (movingPiece.type === 'pawn' && (to.y === 0 || to.y === 7)) {
            movingPiece.type = 'queen';
        }
        // Castling (move rook)
        if (movingPiece.type === 'king' && Math.abs(to.x - from.x) === 2) {
            const isKingside = to.x > from.x;
            const rookFromX = isKingside ? 7 : 0;
            const rookToX = isKingside ? 5 : 3;
            const rook = { ...tempBoard[rookFromX][from.y] };
            tempBoard[rookToX][from.y] = rook;
            tempBoard[rookFromX][from.y] = null;
            rook.x = rookToX;
        }
        // En passant capture
        if (movingPiece.type === 'pawn' && to.x !== from.x && !originalPiece) {
            const capturedPawnY = movingPiece.color === 'white' ? to.y + 1 : to.y - 1;
            tempBoard[to.x][capturedPawnY] = null;
        }


        // Update king position if king moved
        if (movingPiece.type === 'king') {
            tempKingPositions[color] = { x: to.x, y: to.y };
        }

        const inCheck = ChessLogic.isInCheck(tempBoard, color, tempKingPositions);

        return inCheck;
    }

    /**
     * Get all valid moves for a piece on a given board state.
     * Filters out moves that would put own king in check.
     */
    static getValidMoves(board, piece, kingPositions, castlingRights, enPassantTarget) {
        const moves = [];
        const { x, y, type, color } = piece;

        switch (type) {
            case 'pawn':
                moves.push(...ChessLogic.getPawnMoves(board, x, y, color, enPassantTarget));
                break;
            case 'rook':
                moves.push(...ChessLogic.getRookMoves(board, x, y, color));
                break;
            case 'knight':
                moves.push(...ChessLogic.getKnightMoves(board, x, y, color));
                break;
            case 'bishop':
                moves.push(...ChessLogic.getBishopMoves(board, x, y, color));
                break;
            case 'queen':
                moves.push(...ChessLogic.getQueenMoves(board, x, y, color));
                break;
            case 'king':
                moves.push(...ChessLogic.getKingMoves(board, x, y, color, kingPositions, castlingRights));
                break;
        }

        // Filter out moves that would put own king in check
        return moves.filter(move =>
            !ChessLogic.wouldBeInCheck(board, color, { x, y }, move, kingPositions, castlingRights, enPassantTarget)
        );
    }

    /**
     * Get piece moves without check validation (to avoid recursion in isSquareAttacked)
     */
    static getPieceMoves(board, piece, checkForCheck = true, kingPositions, castlingRights, enPassantTarget) {
        const { x, y, type, color } = piece;

        let moves;
        switch (type) {
            case 'pawn': moves = ChessLogic.getPawnMoves(board, x, y, color, enPassantTarget); break;
            case 'rook': moves = ChessLogic.getRookMoves(board, x, y, color); break;
            case 'knight': moves = ChessLogic.getKnightMoves(board, x, y, color); break;
            case 'bishop': moves = ChessLogic.getBishopMoves(board, x, y, color); break;
            case 'queen': moves = ChessLogic.getQueenMoves(board, x, y, color); break;
            case 'king': moves = ChessLogic.getBasicKingMoves(board, x, y, color); break; // Basic king moves for attack check
            default: moves = []; break;
        }

        if (checkForCheck) {
            return moves.filter(move =>
                !ChessLogic.wouldBeInCheck(board, color, { x, y }, move, kingPositions, castlingRights, enPassantTarget)
            );
        }
        return moves;
    }

    /**
     * Get valid pawn moves
     */
    static getPawnMoves(board, x, y, color, enPassantTarget) {
        const moves = [];
        const direction = color === 'white' ? -1 : 1;
        const startRow = color === 'white' ? 6 : 1;

        // Forward moves
        const oneForward = { x, y: y + direction };
        if (ChessLogic.isOnBoard(oneForward.x, oneForward.y) && !board[oneForward.x][oneForward.y]) {
            moves.push(oneForward);

            // Two squares forward from starting position
            if (y === startRow) {
                const twoForward = { x, y: y + 2 * direction };
                if (!board[twoForward.x][twoForward.y]) {
                    moves.push(twoForward);
                }
            }
        }

        // Captures
        for (const dx of [-1, 1]) {
            const captureMove = { x: x + dx, y: y + direction };
            if (ChessLogic.isOnBoard(captureMove.x, captureMove.y)) {
                const targetPiece = board[captureMove.x][captureMove.y];
                if (targetPiece && targetPiece.color !== color) {
                    moves.push(captureMove);
                }

                // En passant
                if (enPassantTarget &&
                    captureMove.x === enPassantTarget.x &&
                    captureMove.y === enPassantTarget.y) {
                    // Verify that the piece to be captured is actually a pawn that just moved two squares
                    const enPassantPawnY = color === 'white' ? enPassantTarget.y + 1 : enPassantTarget.y - 1;
                    const enPassantPawn = board[enPassantTarget.x][enPassantPawnY];
                    if (enPassantPawn && enPassantPawn.type === 'pawn' && enPassantPawn.color !== color) {
                        moves.push(captureMove);
                    }
                }
            }
        }

        return moves;
    }

    /**
     * Get valid rook moves
     */
    static getRookMoves(board, x, y, color) {
        const moves = [];
        const directions = [[0, 1], [0, -1], [1, 0], [-1, 0]];

        for (const [dx, dy] of directions) {
            for (let i = 1; i < 8; i++) {
                const newX = x + dx * i;
                const newY = y + dy * i;

                if (!ChessLogic.isOnBoard(newX, newY)) break;

                const targetPiece = board[newX][newY];
                if (!targetPiece) {
                    moves.push({ x: newX, y: newY });
                } else {
                    if (targetPiece.color !== color) {
                        moves.push({ x: newX, y: newY });
                    }
                    break;
                }
            }
        }

        return moves;
    }

    /**
     * Get valid knight moves
     */
    static getKnightMoves(board, x, y, color) {
        const moves = [];
        const knightMoves = [
            [-2, -1], [-2, 1], [-1, -2], [-1, 2],
            [1, -2], [1, 2], [2, -1], [2, 1]
        ];

        for (const [dx, dy] of knightMoves) {
            const newX = x + dx;
            const newY = y + dy;

            if (ChessLogic.isOnBoard(newX, newY)) {
                const targetPiece = board[newX][newY];
                if (!targetPiece || targetPiece.color !== color) {
                    moves.push({ x: newX, y: newY });
                }
            }
        }

        return moves;
    }

    /**
     * Get valid bishop moves
     */
    static getBishopMoves(board, x, y, color) {
        const moves = [];
        const directions = [[1, 1], [1, -1], [-1, 1], [-1, -1]];

        for (const [dx, dy] of directions) {
            for (let i = 1; i < 8; i++) {
                const newX = x + dx * i;
                const newY = y + dy * i;

                if (!ChessLogic.isOnBoard(newX, newY)) break;

                const targetPiece = board[newX][newY];
                if (!targetPiece) {
                    moves.push({ x: newX, y: newY });
                } else {
                    if (targetPiece.color !== color) {
                        moves.push({ x: newX, y: newY });
                    }
                    break;
                }
            }
        }

        return moves;
    }

    /**
     * Get valid queen moves (combination of rook and bishop)
     */
    static getQueenMoves(board, x, y, color) {
        return [
            ...ChessLogic.getRookMoves(board, x, y, color),
            ...ChessLogic.getBishopMoves(board, x, y, color)
        ];
    }

    /**
     * Get valid king moves
     */
    static getKingMoves(board, x, y, color, kingPositions, castlingRights) {
        const moves = [];

        // Normal king moves
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                if (dx === 0 && dy === 0) continue;

                const newX = x + dx;
                const newY = y + dy;

                if (ChessLogic.isOnBoard(newX, newY)) {
                    const targetPiece = board[newX][newY];
                    if (!targetPiece || targetPiece.color !== color) {
                        moves.push({ x: newX, y: newY });
                    }
                }
            }
        }

        // Castling
        const kingPiece = board[x][y];
        if (kingPiece && !kingPiece.hasMoved && !ChessLogic.isInCheck(board, color, kingPositions)) {
            // Kingside castling
            if (castlingRights[color].kingside) {
                const rook = board[7][y];
                if (rook && rook.type === 'rook' && !rook.hasMoved &&
                    !board[x + 1][y] && !board[x + 2][y] &&
                    !ChessLogic.isSquareAttacked(board, x + 1, y, color === 'white' ? 'black' : 'white', kingPositions) &&
                    !ChessLogic.isSquareAttacked(board, x + 2, y, color === 'white' ? 'black' : 'white', kingPositions)) {
                    moves.push({ x: x + 2, y });
                }
            }

            // Queenside castling
            if (castlingRights[color].queenside) {
                const rook = board[0][y];
                if (rook && rook.type === 'rook' && !rook.hasMoved &&
                    !board[x - 1][y] && !board[x - 2][y] && !board[x - 3][y] &&
                    !ChessLogic.isSquareAttacked(board, x - 1, y, color === 'white' ? 'black' : 'white', kingPositions) &&
                    !ChessLogic.isSquareAttacked(board, x - 2, y, color === 'white' ? 'black' : 'white', kingPositions)) {
                    moves.push({ x: x - 2, y });
                }
            }
        }

        return moves;
    }

    /**
     * Get basic king moves without castling or check validation (for attack checks)
     */
    static getBasicKingMoves(board, x, y, color) {
        const moves = [];

        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                if (dx === 0 && dy === 0) continue;

                const newX = x + dx;
                const newY = y + dy;

                if (ChessLogic.isOnBoard(newX, newY)) {
                    const targetPiece = board[newX][newY];
                    if (!targetPiece || targetPiece.color !== color) {
                        moves.push({ x: newX, y: newY });
                    }
                }
            }
        }
        return moves;
    }

    /**
     * Apply a move to a new board state (creates a copy)
     */
    static simulateMove(board, move) {
        const newBoard = board.map(row => row.slice()); // Deep copy
        const piece = { ...newBoard[move.from.x][move.from.y] }; // Copy piece to avoid modifying original
        
        newBoard[move.to.x][move.to.y] = piece;
        newBoard[move.from.x][move.from.y] = null;
        
        // Update piece's internal coordinates for the simulated move
        piece.x = move.to.x;
        piece.y = move.to.y;

        // Handle pawn promotion for AI simulation
        if (piece.type === 'pawn' && (move.to.y === 0 || move.to.y === 7)) {
            piece.type = 'queen'; // AI always promotes to queen
        }

        // Handle castling for AI simulation
        if (piece.type === 'king' && Math.abs(move.to.x - move.from.x) === 2) {
            const isKingside = move.to.x > move.from.x;
            const rookFromX = isKingside ? 7 : 0;
            const rookToX = isKingside ? 5 : 3;
            const rook = { ...newBoard[rookFromX][move.from.y] };
            newBoard[rookToX][move.from.y] = rook;
            newBoard[rookFromX][move.from.y] = null;
            rook.x = rookToX;
        }

        // Handle en passant capture for AI simulation
        if (piece.type === 'pawn' && move.to.x !== move.from.x && !newBoard[move.to.x][move.to.y]) {
            const capturedPawnY = piece.color === 'white' ? move.to.y + 1 : move.to.y - 1;
            newBoard[move.to.x][capturedPawnY] = null;
        }
        
        return newBoard;
    }

    /**
     * Get all possible moves for a given player on a given board
     */
    static getAllPossibleMoves(board, playerColor, kingPositions, castlingRights, enPassantTarget) {
        const moves = [];
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = board[x][y];
                if (piece && piece.color === playerColor) {
                    const pieceMoves = ChessLogic.getValidMoves(board, piece, kingPositions, castlingRights, enPassantTarget);
                    for (const move of pieceMoves) {
                        moves.push({
                            from: { x, y },
                            to: move,
                            piece: piece.type,
                            captured: board[move.x][move.y] ? board[move.x][move.y].type : null
                        });
                    }
                }
            }
        }
        return moves;
    }
}