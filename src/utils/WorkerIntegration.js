// src/utils/WorkerIntegration.js
// Integration utilities for new worker implementations

import { getOptimizedWorkerConfig } from '../config/workerConfig.js';

/**
 * High-level integration class for worker-based operations
 */
export class WorkerIntegration {
    constructor(workerManager) {
        this.workerManager = workerManager;
        this.config = getOptimizedWorkerConfig();
        this.isLowEndDevice = this._detectLowEndDevice();
        
        // Initialize worker pools
        this._initializeWorkerPools();
        
        console.log('[WorkerIntegration] Initialized for', this.isLowEndDevice ? 'low-end' : 'high-end', 'device');
    }

    /**
     * Process mesh operations using worker
     * @param {Object} meshData - Mesh processing data
     * @param {string} operation - Operation type
     * @returns {Promise<Object>} Processed mesh data
     */
    async processMesh(meshData, operation = 'processVoxelMesh') {
        const workerId = await this.workerManager.getOrCreateWorker(
            'meshProcessing',
            './src/workers/meshProcessingWorker.js'
        );

        if (!workerId) {
            throw new Error('[WorkerIntegration] No mesh worker available - worker system must be properly initialized');
        }

        const response = await this.workerManager.sendMessage(workerId, {
            type: operation,
            data: {
                ...meshData,
                optimizeForLowEnd: this.isLowEndDevice
            }
        });

        return response.result;
    }

    /**
     * Find path using pathfinding worker
     * @param {Object} pathData - Pathfinding data
     * @returns {Promise<Array>} Path waypoints
     */
    async findPath(pathData) {
        const workerId = await this.workerManager.getOrCreateWorker(
            'pathfinding',
            './src/workers/pathfindingWorker.js'
        );

        if (!workerId) {
            throw new Error('[WorkerIntegration] No pathfinding worker available - worker system must be properly initialized');
        }

        const response = await this.workerManager.sendMessage(workerId, {
            type: 'findPath',
            data: {
                ...pathData,
                optimizeForLowEnd: this.isLowEndDevice
            }
        });

        return response.result.path || [];
    }

    /**
     * Process animations using animation worker
     * @param {Array} animations - Animation data array
     * @param {number} deltaTime - Delta time
     * @param {number} globalTime - Global time
     * @returns {Promise<Array>} Animation updates
     */
    async processAnimations(animations, deltaTime, globalTime) {
        const workerId = await this.workerManager.getOrCreateWorker(
            'animation',
            './src/workers/animationWorker.js'
        );

        if (!workerId) {
            throw new Error('[WorkerIntegration] No animation worker available - worker system must be properly initialized');
        }

        const response = await this.workerManager.sendMessage(workerId, {
            type: 'calculateAnimationBatch',
            data: {
                animations,
                deltaTime,
                globalTime,
                optimizeForLowEnd: this.isLowEndDevice
            }
        });

        return response.result.animationUpdates || [];
    }

    /**
     * Generate bullet pattern using worker
     * @param {Object} patternData - Pattern generation data
     * @returns {Promise<Object>} Generated pattern
     */
    async generateBulletPattern(patternData) {
        const workerId = await this.workerManager.getOrCreateWorker(
            'bulletPattern',
            './src/workers/bulletPatternWorker.js'
        );

        if (!workerId) {
            throw new Error('[WorkerIntegration] No bullet pattern worker available - worker system must be properly initialized');
        }

        const response = await this.workerManager.sendMessage(workerId, {
            type: 'generatePattern',
            data: {
                ...patternData,
                optimizeForLowEnd: this.isLowEndDevice
            }
        });

        return response.result;
    }

    /**
     * Process batch operations efficiently
     * @param {string} workerType - Type of worker
     * @param {string} operation - Operation type
     * @param {Array} batchData - Batch data array
     * @returns {Promise<Array>} Batch results
     */
    async processBatch(workerType, operation, batchData) {
        const workerScripts = {
            meshProcessing: './src/workers/meshProcessingWorker.js',
            pathfinding: './src/workers/pathfindingWorker.js',
            animation: './src/workers/animationWorker.js',
            bulletPattern: './src/workers/bulletPatternWorker.js'
        };
        
        try {
            const workerId = await this.workerManager.getOrCreateWorker(
                workerType,
                workerScripts[workerType]
            );
            
            if (!workerId) {
                throw new Error(`No ${workerType} worker available`);
            }
            
            const response = await this.workerManager.sendMessage(workerId, {
                type: operation,
                data: {
                    requests: batchData,
                    optimizeForLowEnd: this.isLowEndDevice
                }
            });
            
            return response.result.results || [];
            
        } catch (error) {
            console.error(`[WorkerIntegration] Batch ${workerType} processing failed:`, error);
            throw error;
        }
    }

    /**
     * Get performance metrics from all workers
     * @returns {Promise<Object>} Combined performance metrics
     */
    async getPerformanceMetrics() {
        const metrics = {
            timestamp: Date.now(),
            deviceType: this.isLowEndDevice ? 'low-end' : 'high-end',
            workers: {}
        };
        
        const workerTypes = ['meshProcessing', 'pathfinding', 'animation', 'bulletPattern'];
        
        for (const workerType of workerTypes) {
            try {
                const workerId = this.workerManager.getAvailableWorker(workerType);
                if (workerId) {
                    const response = await this.workerManager.sendMessage(workerId, {
                        type: 'getPerformanceMetrics',
                        data: {}
                    });
                    metrics.workers[workerType] = response.result;
                }
            } catch (error) {
                console.warn(`[WorkerIntegration] Failed to get ${workerType} metrics:`, error);
            }
        }
        
        return metrics;
    }

    /**
     * Initialize worker pools for all new worker types
     * @private
     */
    async _initializeWorkerPools() {
        const workerConfigs = [
            { type: 'meshProcessing', script: './src/workers/meshProcessingWorker.js' },
            { type: 'pathfinding', script: './src/workers/pathfindingWorker.js' },
            { type: 'animation', script: './src/workers/animationWorker.js' },
            { type: 'bulletPattern', script: './src/workers/bulletPatternWorker.js' }
        ];
        
        for (const config of workerConfigs) {
            try {
                const workerConfig = this.config.workerTypes[config.type];
                if (workerConfig && workerConfig.maxWorkers > 0) {
                    await this.workerManager.initializeWorkerPool(
                        config.type,
                        config.script,
                        { optimizeForLowEnd: this.isLowEndDevice },
                        workerConfig.maxWorkers
                    );
                    console.log(`[WorkerIntegration] Initialized ${config.type} worker pool`);
                }
            } catch (error) {
                console.warn(`[WorkerIntegration] Failed to initialize ${config.type} worker pool:`, error);
            }
        }
    }

    /**
     * Detect if device is low-end
     * @private
     */
    _detectLowEndDevice() {
        const hardwareConcurrency = navigator.hardwareConcurrency || 4;
        const deviceMemory = navigator.deviceMemory || 4;
        
        // Consider low-end if:
        // - Less than 4 CPU cores
        // - Less than 4GB RAM
        // - Low FPS detected
        const isLowCPU = hardwareConcurrency < 4;
        const isLowMemory = deviceMemory < 4;
        const isLowFPS = window.lastFps && window.lastFps < 30;
        
        return isLowCPU || isLowMemory || isLowFPS;
    }


}

/**
 * Create global worker integration instance
 * @param {WorkerManager} workerManager - Worker manager instance
 * @returns {WorkerIntegration} Worker integration instance
 */
export function createWorkerIntegration(workerManager) {
    return new WorkerIntegration(workerManager);
}

/**
 * Utility function to check if workers are supported
 * @returns {boolean} True if workers are supported
 */
export function areWorkersSupported() {
    return typeof Worker !== 'undefined';
}

/**
 * Utility function to get recommended worker configuration for current device
 * @returns {Object} Recommended configuration
 */
export function getRecommendedWorkerConfig() {
    const config = getOptimizedWorkerConfig();
    const hardwareConcurrency = navigator.hardwareConcurrency || 4;
    const deviceMemory = navigator.deviceMemory || 4;
    
    // Add device-specific recommendations
    config.recommendations = [];
    
    if (hardwareConcurrency >= 8) {
        config.recommendations.push('Consider enabling all worker types for maximum performance');
    } else if (hardwareConcurrency <= 2) {
        config.recommendations.push('Disable non-essential workers to preserve main thread performance');
        config.recommendations.push('Enable only pathfinding and animation workers');
    }
    
    if (deviceMemory <= 2) {
        config.recommendations.push('Reduce worker memory limits');
        config.recommendations.push('Disable mesh processing worker on very low memory devices');
    }
    
    return config;
}
