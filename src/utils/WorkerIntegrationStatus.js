// src/utils/WorkerIntegrationStatus.js
// Status checker and diagnostics for worker integration

/**
 * Worker integration status checker
 */
export class WorkerIntegrationStatus {
    constructor() {
        this.lastCheck = 0;
        this.status = null;
    }

    /**
     * Get comprehensive status of worker integration
     * @returns {Object} Status report
     */
    getStatus() {
        const now = Date.now();
        
        // Cache status for 5 seconds
        if (this.status && (now - this.lastCheck) < 5000) {
            return this.status;
        }
        
        this.lastCheck = now;
        this.status = this._generateStatus();
        return this.status;
    }

    /**
     * Generate comprehensive status report
     * @private
     */
    _generateStatus() {
        const status = {
            timestamp: Date.now(),
            overall: 'unknown',
            components: {},
            performance: {},
            recommendations: [],
            errors: []
        };

        try {
            // Check core worker integration
            status.components.workerIntegration = this._checkWorkerIntegration();
            
            // Check worker manager
            status.components.workerManager = this._checkWorkerManager();
            
            // Check performance monitor
            status.components.performanceMonitor = this._checkPerformanceMonitor();
            
            // Check individual workers
            status.components.workers = this._checkIndividualWorkers();
            
            // Check system integration
            status.components.systemIntegration = this._checkSystemIntegration();
            
            // Generate performance metrics
            status.performance = this._getPerformanceMetrics();
            
            // Generate recommendations
            status.recommendations = this._generateRecommendations(status);
            
            // Determine overall status
            status.overall = this._determineOverallStatus(status);
            
        } catch (error) {
            status.errors.push({
                component: 'status_checker',
                error: error.message,
                timestamp: Date.now()
            });
            status.overall = 'error';
        }

        return status;
    }

    /**
     * Check worker integration availability
     * @private
     */
    _checkWorkerIntegration() {
        const check = {
            available: false,
            initialized: false,
            globallyAccessible: false,
            errors: []
        };

        try {
            // Check if worker integration exists
            if (typeof window !== 'undefined' && window.workerIntegration) {
                check.available = true;
                check.globallyAccessible = true;
                
                // Check if properly initialized
                if (window.workerIntegration.workerManager && 
                    window.workerIntegration.isLowEndDevice !== undefined) {
                    check.initialized = true;
                }
            }
            
            if (!check.available) {
                check.errors.push('Worker integration not available globally');
            }
            
        } catch (error) {
            check.errors.push(`Worker integration check failed: ${error.message}`);
        }

        return check;
    }

    /**
     * Check worker manager status
     * @private
     */
    _checkWorkerManager() {
        const check = {
            available: false,
            poolsInitialized: false,
            activeWorkers: 0,
            poolStats: null,
            errors: []
        };

        try {
            if (window.workerIntegration && window.workerIntegration.workerManager) {
                check.available = true;
                
                const poolStats = window.workerIntegration.workerManager.getPoolStatistics();
                if (poolStats) {
                    check.poolStats = poolStats;
                    check.activeWorkers = poolStats.totalWorkers || 0;
                    check.poolsInitialized = Object.keys(poolStats.pools || {}).length > 0;
                }
            } else {
                check.errors.push('Worker manager not available');
            }
            
        } catch (error) {
            check.errors.push(`Worker manager check failed: ${error.message}`);
        }

        return check;
    }

    /**
     * Check performance monitor status
     * @private
     */
    _checkPerformanceMonitor() {
        const check = {
            available: false,
            monitoring: false,
            hasMetrics: false,
            errors: []
        };

        try {
            if (window.workerExample && window.workerExample.performanceMonitor) {
                check.available = true;
                check.monitoring = window.workerExample.performanceMonitor.isMonitoring;
                
                const report = window.workerExample.performanceMonitor.getPerformanceReport();
                if (report && report.pools) {
                    check.hasMetrics = true;
                }
            } else {
                check.errors.push('Performance monitor not available');
            }
            
        } catch (error) {
            check.errors.push(`Performance monitor check failed: ${error.message}`);
        }

        return check;
    }

    /**
     * Check individual worker types
     * @private
     */
    _checkIndividualWorkers() {
        const workerTypes = ['meshProcessing', 'pathfinding', 'animation', 'bulletPattern'];
        const checks = {};

        workerTypes.forEach(type => {
            checks[type] = {
                available: false,
                poolSize: 0,
                busy: 0,
                errors: []
            };

            try {
                if (window.workerIntegration && window.workerIntegration.workerManager) {
                    const poolStats = window.workerIntegration.workerManager.getPoolStatistics();
                    if (poolStats.pools && poolStats.pools[type]) {
                        const pool = poolStats.pools[type];
                        checks[type].available = true;
                        checks[type].poolSize = pool.maxSize || 0;
                        checks[type].busy = pool.busy || 0;
                    } else {
                        checks[type].errors.push(`${type} worker pool not found`);
                    }
                }
            } catch (error) {
                checks[type].errors.push(`${type} worker check failed: ${error.message}`);
            }
        });

        return checks;
    }

    /**
     * Check system integration
     * @private
     */
    _checkSystemIntegration() {
        const check = {
            dungeonHandlerIntegrated: false,
            pathfindingIntegrated: false,
            meshProcessingIntegrated: false,
            animationIntegrated: false,
            bulletPatternIntegrated: false,
            errors: []
        };

        try {
            // Check DungeonHandler integration
            if (window.sceneManager && 
                window.sceneManager.dungeonHandler && 
                window.sceneManager.dungeonHandler.workerIntegration) {
                check.dungeonHandlerIntegrated = true;
            }
            
            // Check if integration helpers are available
            check.pathfindingIntegrated = typeof window.workerIntegration?.findPath === 'function';
            check.meshProcessingIntegrated = typeof window.workerIntegration?.processMesh === 'function';
            check.animationIntegrated = typeof window.workerIntegration?.processAnimations === 'function';
            check.bulletPatternIntegrated = typeof window.workerIntegration?.generateBulletPattern === 'function';
            
        } catch (error) {
            check.errors.push(`System integration check failed: ${error.message}`);
        }

        return check;
    }

    /**
     * Get performance metrics
     * @private
     */
    _getPerformanceMetrics() {
        const metrics = {
            deviceInfo: {},
            workerMetrics: {},
            systemMetrics: {}
        };

        try {
            // Device information
            metrics.deviceInfo = {
                hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
                deviceMemory: navigator.deviceMemory || 'unknown',
                userAgent: navigator.userAgent.substring(0, 100) + '...'
            };

            // System metrics
            if (performance.memory) {
                metrics.systemMetrics.memory = {
                    used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
                    total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
                    limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
                };
            }

            // Worker metrics
            if (window.workerExample && window.workerExample.performanceMonitor) {
                const report = window.workerExample.performanceMonitor.getPerformanceReport();
                if (report) {
                    metrics.workerMetrics = {
                        alertCount: report.alerts?.total || 0,
                        criticalAlerts: report.alerts?.critical || 0,
                        recommendations: report.recommendations?.length || 0
                    };
                }
            }

        } catch (error) {
            metrics.error = error.message;
        }

        return metrics;
    }

    /**
     * Generate recommendations
     * @private
     */
    _generateRecommendations(status) {
        const recommendations = [];

        // Check if worker integration is available
        if (!status.components.workerIntegration?.available) {
            recommendations.push({
                priority: 'critical',
                message: 'Worker integration not available - ensure worker system is properly initialized',
                action: 'Check main.js initialization and browser console for errors'
            });
        }

        // Check worker pools
        if (status.components.workerManager?.available && !status.components.workerManager?.poolsInitialized) {
            recommendations.push({
                priority: 'high',
                message: 'Worker pools not initialized - performance benefits not available',
                action: 'Verify worker pool initialization in WorkerIntegrationExample.js'
            });
        }

        // Check performance monitoring
        if (status.components.performanceMonitor?.available && !status.components.performanceMonitor?.monitoring) {
            recommendations.push({
                priority: 'medium',
                message: 'Performance monitoring not active',
                action: 'Enable performance monitoring for better optimization insights'
            });
        }

        // Check individual workers
        const workerTypes = ['meshProcessing', 'pathfinding', 'animation', 'bulletPattern'];
        workerTypes.forEach(type => {
            const worker = status.components.workers?.[type];
            if (worker && !worker.available) {
                recommendations.push({
                    priority: 'medium',
                    message: `${type} worker not available`,
                    action: `Check ${type} worker initialization and configuration`
                });
            }
        });

        return recommendations;
    }

    /**
     * Determine overall status
     * @private
     */
    _determineOverallStatus(status) {
        // Check for critical errors
        if (status.errors.length > 0) {
            return 'error';
        }

        // Check core components
        if (!status.components.workerIntegration?.available) {
            return 'not_available';
        }

        if (!status.components.workerManager?.available) {
            return 'partially_available';
        }

        // Check if most workers are available
        const workers = status.components.workers || {};
        const workerTypes = Object.keys(workers);
        const availableWorkers = workerTypes.filter(type => workers[type]?.available).length;
        
        if (availableWorkers === 0) {
            return 'workers_unavailable';
        }

        if (availableWorkers < workerTypes.length) {
            return 'partially_functional';
        }

        return 'fully_functional';
    }

    /**
     * Get human-readable status message
     * @returns {string} Status message
     */
    getStatusMessage() {
        const status = this.getStatus();
        
        const messages = {
            'fully_functional': '✅ Worker integration fully functional',
            'partially_functional': '⚠️ Worker integration partially functional',
            'workers_unavailable': '❌ Workers unavailable',
            'partially_available': '⚠️ Worker system partially available',
            'not_available': '❌ Worker integration not available',
            'error': '💥 Worker integration error',
            'unknown': '❓ Worker integration status unknown'
        };

        return messages[status.overall] || messages.unknown;
    }

    /**
     * Print detailed status to console
     */
    printStatus() {
        const status = this.getStatus();
        
        console.group('🔧 Worker Integration Status');
        console.log('Overall Status:', this.getStatusMessage());
        console.log('Timestamp:', new Date(status.timestamp).toLocaleString());
        
        if (status.components) {
            console.group('Components');
            Object.entries(status.components).forEach(([name, component]) => {
                console.log(`${name}:`, component);
            });
            console.groupEnd();
        }
        
        if (status.performance) {
            console.group('Performance');
            console.log(status.performance);
            console.groupEnd();
        }
        
        if (status.recommendations.length > 0) {
            console.group('Recommendations');
            status.recommendations.forEach(rec => {
                console.log(`[${rec.priority.toUpperCase()}] ${rec.message}`);
                console.log(`  Action: ${rec.action}`);
            });
            console.groupEnd();
        }
        
        if (status.errors.length > 0) {
            console.group('Errors');
            status.errors.forEach(error => {
                console.error(`[${error.component}] ${error.error}`);
            });
            console.groupEnd();
        }
        
        console.groupEnd();
    }
}

// Create global instance
export const workerIntegrationStatus = new WorkerIntegrationStatus();

// Make available globally for debugging
if (typeof window !== 'undefined') {
    window.workerIntegrationStatus = workerIntegrationStatus;
}
