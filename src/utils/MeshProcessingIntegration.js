// src/utils/MeshProcessingIntegration.js
// Integration helper for mesh processing with workers

import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

/**
 * Enhanced mesh processing with worker integration
 */
export class MeshProcessingIntegration {
    constructor() {
        this.processingQueue = new Map();
        this.isProcessing = false;
    }

    /**
     * Process voxel data using workers
     * @param {Array} voxels - Array of voxel data
     * @param {number} voxelScale - Scale of voxels
     * @param {Object} options - Processing options
     * @returns {Promise<THREE.Group>} Processed mesh group
     */
    async processVoxelMesh(voxels, voxelScale = 1, options = {}) {
        if (!window.workerIntegration) {
            throw new Error('[MeshProcessingIntegration] Worker integration not available');
        }

        try {
            // Serialize voxel data for worker
            const serializedVoxels = voxels.map(voxel => ({
                position: {
                    x: voxel.position.x,
                    y: voxel.position.y,
                    z: voxel.position.z
                },
                color: this._serializeColor(voxel.color),
                size: voxel.size || voxelScale
            }));

            const result = await window.workerIntegration.processMesh({
                voxels: serializedVoxels,
                voxelScale,
                ...options
            }, 'processVoxelMesh');

            // Convert worker result back to THREE.js meshes
            return this._createMeshGroupFromWorkerResult(result);

        } catch (error) {
            console.error('[MeshProcessingIntegration] Voxel processing failed:', error);
            throw error;
        }
    }

    /**
     * Merge geometries using workers
     * @param {Array} geometries - Array of BufferGeometry objects
     * @param {Object} options - Merge options
     * @returns {Promise<THREE.BufferGeometry>} Merged geometry
     */
    async mergeGeometries(geometries, options = {}) {
        if (!window.workerIntegration) {
            throw new Error('[MeshProcessingIntegration] Worker integration not available');
        }

        try {
            // For small geometry counts, use direct merging
            if (geometries.length < 5) {
                return BufferGeometryUtils.mergeGeometries(geometries);
            }

            // Serialize geometries for worker
            const serializedGeometries = geometries.map(geo => this._serializeGeometry(geo));

            const result = await window.workerIntegration.processMesh({
                geometries: serializedGeometries,
                mergeByMaterial: options.mergeByMaterial !== false
            }, 'mergeGeometries');

            // Convert worker result back to BufferGeometry
            return this._deserializeGeometry(result.mergedGeometry);

        } catch (error) {
            console.error('[MeshProcessingIntegration] Geometry merging failed:', error);
            // Fallback to direct merging
            return BufferGeometryUtils.mergeGeometries(geometries);
        }
    }

    /**
     * Create instanced mesh data using workers
     * @param {Array} voxels - Array of voxel data
     * @param {Object} options - Instance options
     * @returns {Promise<Array>} Array of instanced mesh data
     */
    async createInstancedMeshData(voxels, options = {}) {
        if (!window.workerIntegration) {
            throw new Error('[MeshProcessingIntegration] Worker integration not available');
        }

        try {
            const serializedVoxels = voxels.map(voxel => ({
                position: {
                    x: voxel.position.x,
                    y: voxel.position.y,
                    z: voxel.position.z
                },
                color: this._serializeColor(voxel.color),
                size: voxel.size || 1
            }));

            const result = await window.workerIntegration.processMesh({
                voxels: serializedVoxels,
                maxInstancesPerMesh: options.maxInstancesPerMesh || 1000
            }, 'createInstancedMeshData');

            return result.instancedMeshes;

        } catch (error) {
            console.error('[MeshProcessingIntegration] Instanced mesh creation failed:', error);
            throw error;
        }
    }

    /**
     * Enhanced BufferGeometryUtils.mergeGeometries with worker support
     * @param {Array} geometries - Array of geometries to merge
     * @param {boolean} useGroups - Whether to use groups
     * @returns {Promise<THREE.BufferGeometry>} Merged geometry
     */
    static async mergeGeometriesEnhanced(geometries, useGroups = false) {
        if (!geometries || geometries.length === 0) {
            return new THREE.BufferGeometry();
        }

        if (geometries.length === 1) {
            return geometries[0];
        }

        // Use worker for large geometry sets
        if (geometries.length > 10 && window.workerIntegration) {
            try {
                const integration = new MeshProcessingIntegration();
                return await integration.mergeGeometries(geometries, { useGroups });
            } catch (error) {
                console.warn('[MeshProcessingIntegration] Worker merge failed, using direct merge:', error);
            }
        }

        // Direct merge for small sets or when workers unavailable
        return BufferGeometryUtils.mergeGeometries(geometries, useGroups);
    }

    /**
     * Serialize color for worker
     * @private
     */
    _serializeColor(color) {
        if (typeof color === 'number') {
            const c = new THREE.Color(color);
            return { r: c.r, g: c.g, b: c.b };
        }
        if (color && typeof color === 'object') {
            return { r: color.r || 0, g: color.g || 0, b: color.b || 0 };
        }
        return { r: 1, g: 1, b: 1 };
    }

    /**
     * Serialize geometry for worker
     * @private
     */
    _serializeGeometry(geometry) {
        const attributes = {};
        
        // Serialize position attribute
        if (geometry.attributes.position) {
            attributes.position = Array.from(geometry.attributes.position.array);
        }
        
        // Serialize normal attribute
        if (geometry.attributes.normal) {
            attributes.normal = Array.from(geometry.attributes.normal.array);
        }
        
        // Serialize UV attribute
        if (geometry.attributes.uv) {
            attributes.uv = Array.from(geometry.attributes.uv.array);
        }
        
        // Serialize index
        let index = null;
        if (geometry.index) {
            index = Array.from(geometry.index.array);
        }
        
        return {
            attributes,
            index,
            type: geometry.type
        };
    }

    /**
     * Deserialize geometry from worker
     * @private
     */
    _deserializeGeometry(data) {
        const geometry = new THREE.BufferGeometry();
        
        // Restore attributes
        if (data.attributes.position) {
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(data.attributes.position, 3));
        }
        
        if (data.attributes.normal) {
            geometry.setAttribute('normal', new THREE.Float32BufferAttribute(data.attributes.normal, 3));
        }
        
        if (data.attributes.uv) {
            geometry.setAttribute('uv', new THREE.Float32BufferAttribute(data.attributes.uv, 2));
        }
        
        // Restore index
        if (data.index) {
            geometry.setIndex(data.index);
        }
        
        return geometry;
    }

    /**
     * Create mesh group from worker result
     * @private
     */
    _createMeshGroupFromWorkerResult(result) {
        const group = new THREE.Group();
        
        if (!result.meshData || !Array.isArray(result.meshData)) {
            return group;
        }
        
        result.meshData.forEach(meshData => {
            if (meshData.isPointCloud) {
                // Create point cloud for low-end optimization
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(meshData.positions, 3));
                
                const material = new THREE.PointsMaterial({
                    color: this._parseColorKey(meshData.colorKey),
                    size: 0.1
                });
                
                const points = new THREE.Points(geometry, material);
                group.add(points);
            } else {
                // Create regular mesh
                const geometry = new THREE.BufferGeometry();
                geometry.setAttribute('position', new THREE.Float32BufferAttribute(meshData.vertices, 3));
                
                if (meshData.indices) {
                    geometry.setIndex(meshData.indices);
                }
                
                const material = new THREE.MeshStandardMaterial({
                    color: this._parseColorKey(meshData.colorKey)
                });
                
                const mesh = new THREE.Mesh(geometry, material);
                mesh.castShadow = true;
                mesh.receiveShadow = true;
                group.add(mesh);
            }
        });
        
        return group;
    }

    /**
     * Parse color key back to color
     * @private
     */
    _parseColorKey(colorKey) {
        if (colorKey.includes('_')) {
            const [r, g, b] = colorKey.split('_').map(Number);
            return new THREE.Color(r / 255, g / 255, b / 255);
        }
        return new THREE.Color(colorKey);
    }
}

// Create global instance
export const meshProcessingIntegration = new MeshProcessingIntegration();

/**
 * Enhanced mergeGeometries function that uses workers when beneficial
 * Drop-in replacement for BufferGeometryUtils.mergeGeometries
 */
export async function mergeGeometriesEnhanced(geometries, useGroups = false) {
    const integration = new MeshProcessingIntegration();
    return integration.mergeGeometries(geometries, { useGroups });
}
