/**
 * Shader Optimizer - Emergency uniform reduction for boss fights
 * Reduces GPU uniform usage to prevent shader compilation failures
 */
import * as THREE from 'three';

export class ShaderOptimizer {
    constructor(renderer, scene) {
        this.renderer = renderer;
        this.scene = scene;
        this.originalMaterials = new Map();
        this.optimizedMaterials = new Map();
        this.isOptimized = false;
        
        // GPU limits detection
        this.maxFragmentUniforms = renderer.capabilities.maxFragmentUniforms;
        this.maxVertexUniforms = renderer.capabilities.maxVertexUniforms;
        
        console.log(`[ShaderOptimizer] GPU Limits - Fragment: ${this.maxFragmentUniforms}, Vertex: ${this.maxVertexUniforms}`);
    }
    
    /**
     * Emergency optimization for boss fights
     */
    optimizeForBossFight() {
        if (this.isOptimized) return;
        
        console.log('[ShaderOptimizer] 🚨 EMERGENCY: Applying boss fight shader optimization');
        
        // 1. Disable all non-essential lights
        this.optimizeLighting();
        
        // 2. Consolidate materials
        this.consolidateMaterials();
        
        // 3. Disable complex post-processing
        this.disableComplexEffects();
        
        this.isOptimized = true;
        console.log('[ShaderOptimizer] ✅ Boss fight optimization complete');
    }
    
    /**
     * Disable non-essential lights to save uniforms
     */
    optimizeLighting() {
        const lightsDisabled = [];
        
        this.scene.traverse(child => {
            if (child.isLight) {
                // Keep only essential lights
                const isEssential = child.name.includes('player') || 
                                  child.name.includes('boss') || 
                                  child.name.includes('main');
                
                if (!isEssential) {
                    child.userData.wasVisible = child.visible;
                    child.visible = false;
                    lightsDisabled.push(child.name || 'unnamed');
                }
            }
        });
        
        console.log(`[ShaderOptimizer] Disabled ${lightsDisabled.length} non-essential lights:`, lightsDisabled);
    }
    
    /**
     * Consolidate similar materials to reduce shader variants
     */
    consolidateMaterials() {
        const materialMap = new Map();
        let materialsConsolidated = 0;
        
        // Create simplified shared materials
        const sharedBasic = new THREE.MeshBasicMaterial({ 
            color: 0x888888,
            fog: false 
        });
        
        const sharedLambert = new THREE.MeshLambertMaterial({ 
            color: 0x888888,
            fog: false 
        });
        
        this.scene.traverse(child => {
            if (child.isMesh && child.material) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                
                materials.forEach((material, index) => {
                    // Skip boss materials - keep them intact
                    if (child.name && child.name.toLowerCase().includes('nairabos')) {
                        return;
                    }
                    
                    // Store original material
                    const materialId = material.uuid;
                    if (!this.originalMaterials.has(materialId)) {
                        this.originalMaterials.set(materialId, material.clone());
                    }
                    
                    // Replace with simplified material
                    if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
                        if (Array.isArray(child.material)) {
                            child.material[index] = sharedLambert;
                        } else {
                            child.material = sharedLambert;
                        }
                        materialsConsolidated++;
                    } else if (material.isMeshLambertMaterial && !material.map) {
                        if (Array.isArray(child.material)) {
                            child.material[index] = sharedBasic;
                        } else {
                            child.material = sharedBasic;
                        }
                        materialsConsolidated++;
                    }
                });
            }
        });
        
        console.log(`[ShaderOptimizer] Consolidated ${materialsConsolidated} materials`);
    }
    
    /**
     * Disable complex effects that consume uniforms
     */
    disableComplexEffects() {
        // Disable post-processing effects
        if (window.gameInstance && window.gameInstance.sceneManager) {
            const sceneManager = window.gameInstance.sceneManager;
            
            // Disable CRT effects temporarily
            if (sceneManager.crtEffect) {
                sceneManager.crtEffect.userData = sceneManager.crtEffect.userData || {};
                sceneManager.crtEffect.userData.wasEnabled = sceneManager.crtEffect.enabled;
                sceneManager.crtEffect.setEnabled(false);
                console.log('[ShaderOptimizer] Disabled CRT effects');
            }
            
            // Disable HDR effects temporarily  
            if (sceneManager.hdrFramerateEffect) {
                sceneManager.hdrFramerateEffect.userData = sceneManager.hdrFramerateEffect.userData || {};
                sceneManager.hdrFramerateEffect.userData.wasEnabled = sceneManager.hdrFramerateEffect.enabled;
                sceneManager.hdrFramerateEffect.setEnabled(false);
                console.log('[ShaderOptimizer] Disabled HDR effects');
            }
        }
    }
    
    /**
     * Restore original settings after boss fight
     */
    restoreOptimization() {
        if (!this.isOptimized) return;
        
        console.log('[ShaderOptimizer] Restoring original shader settings');
        
        // Restore lights
        this.scene.traverse(child => {
            if (child.isLight && child.userData.wasVisible !== undefined) {
                child.visible = child.userData.wasVisible;
                delete child.userData.wasVisible;
            }
        });
        
        // Restore materials
        this.scene.traverse(child => {
            if (child.isMesh && child.material) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                
                materials.forEach((material, index) => {
                    const originalMaterial = this.originalMaterials.get(material.uuid);
                    if (originalMaterial) {
                        if (Array.isArray(child.material)) {
                            child.material[index] = originalMaterial;
                        } else {
                            child.material = originalMaterial;
                        }
                    }
                });
            }
        });
        
        // Restore effects
        if (window.gameInstance && window.gameInstance.sceneManager) {
            const sceneManager = window.gameInstance.sceneManager;
            
            if (sceneManager.crtEffect && sceneManager.crtEffect.userData.wasEnabled) {
                sceneManager.crtEffect.setEnabled(true);
            }
            
            if (sceneManager.hdrFramerateEffect && sceneManager.hdrFramerateEffect.userData.wasEnabled) {
                sceneManager.hdrFramerateEffect.setEnabled(true);
            }
        }
        
        this.isOptimized = false;
        console.log('[ShaderOptimizer] ✅ Optimization restored');
    }
    
    /**
     * Get current uniform usage estimate
     */
    getUniformUsage() {
        let estimatedUniforms = 0;
        const materials = new Set();
        
        this.scene.traverse(child => {
            if (child.isMesh && child.material) {
                const materialArray = Array.isArray(child.material) ? child.material : [child.material];
                materialArray.forEach(mat => materials.add(mat));
            }
        });
        
        materials.forEach(material => {
            if (material.uniforms) {
                estimatedUniforms += Object.keys(material.uniforms).length;
            } else {
                // Estimate based on material type
                if (material.isMeshStandardMaterial) estimatedUniforms += 15;
                else if (material.isMeshLambertMaterial) estimatedUniforms += 8;
                else if (material.isMeshBasicMaterial) estimatedUniforms += 3;
            }
        });
        
        return {
            estimated: estimatedUniforms,
            maxFragment: this.maxFragmentUniforms,
            percentage: (estimatedUniforms / this.maxFragmentUniforms) * 100
        };
    }
}