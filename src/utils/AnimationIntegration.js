// src/utils/AnimationIntegration.js
// Integration helper for animation processing with workers

import * as THREE from 'three';

/**
 * Enhanced animation processing with worker integration
 */
export class AnimationIntegration {
    constructor() {
        this.animationQueue = new Map();
        this.isProcessing = false;
        this.lastUpdateTime = 0;
        this.activeAnimations = new Map();
    }

    /**
     * Process animation batch using workers
     * @param {Array} animations - Array of animation data
     * @param {number} deltaTime - Delta time
     * @param {number} globalTime - Global time
     * @returns {Promise<Array>} Animation updates
     */
    async processAnimationBatch(animations, deltaTime, globalTime) {
        if (!window.workerIntegration) {
            throw new Error('[AnimationIntegration] Worker integration not available');
        }

        try {
            // Serialize animations for worker
            const serializedAnimations = animations.map(anim => this._serializeAnimation(anim));

            const result = await window.workerIntegration.processAnimations(
                serializedAnimations,
                deltaTime,
                globalTime
            );

            // Process worker results
            return this._processAnimationResults(result, animations);

        } catch (error) {
            console.error('[AnimationIntegration] Animation batch processing failed:', error);
            throw error;
        }
    }

    /**
     * Process particle system updates using workers
     * @param {Array} particles - Array of particle data
     * @param {number} deltaTime - Delta time
     * @param {Array} forces - Array of force objects
     * @returns {Promise<Array>} Particle updates
     */
    async processParticleUpdates(particles, deltaTime, forces = []) {
        if (!window.workerIntegration) {
            throw new Error('[AnimationIntegration] Worker integration not available');
        }

        try {
            // Serialize particles for worker
            const serializedParticles = particles.map(particle => ({
                position: this._serializeVector3(particle.position),
                velocity: this._serializeVector3(particle.velocity),
                acceleration: particle.acceleration ? this._serializeVector3(particle.acceleration) : null,
                life: particle.life,
                maxLife: particle.maxLife
            }));

            const workerId = await window.workerIntegration.workerManager.getOrCreateWorker(
                'animation',
                './src/workers/animationWorker.js'
            );

            const response = await window.workerIntegration.workerManager.sendMessage(workerId, {
                type: 'calculateParticleUpdates',
                data: {
                    particles: serializedParticles,
                    deltaTime,
                    forces
                }
            });

            return this._processParticleResults(response.result.particleUpdates, particles);

        } catch (error) {
            console.error('[AnimationIntegration] Particle processing failed:', error);
            throw error;
        }
    }

    /**
     * Calculate smooth interpolation using workers
     * @param {Object} fromState - Starting state
     * @param {Object} toState - Target state
     * @param {number} progress - Interpolation progress (0-1)
     * @param {string} interpolationType - Type of interpolation
     * @returns {Promise<Object>} Interpolated state
     */
    async calculateInterpolation(fromState, toState, progress, interpolationType = 'linear') {
        if (!window.workerIntegration) {
            throw new Error('[AnimationIntegration] Worker integration not available');
        }

        try {
            const workerId = await window.workerIntegration.workerManager.getOrCreateWorker(
                'animation',
                './src/workers/animationWorker.js'
            );

            const response = await window.workerIntegration.workerManager.sendMessage(workerId, {
                type: 'calculateInterpolation',
                data: {
                    fromState: this._serializeAnimationState(fromState),
                    toState: this._serializeAnimationState(toState),
                    progress,
                    interpolationType
                }
            });

            return this._deserializeAnimationState(response.result.interpolatedState);

        } catch (error) {
            console.error('[AnimationIntegration] Interpolation calculation failed:', error);
            throw error;
        }
    }

    /**
     * Enhanced animation handler update with worker support
     * @param {Object} animationHandler - Animation handler instance
     * @param {number} deltaTime - Delta time
     * @param {string} aiState - AI state
     * @returns {Promise<void>}
     */
    async enhanceAnimationHandler(animationHandler, deltaTime, aiState) {
        if (!animationHandler.mesh || !window.workerIntegration) {
            // Use original update method
            return animationHandler.originalUpdate ? 
                animationHandler.originalUpdate(deltaTime, aiState) : 
                animationHandler.update(deltaTime, aiState);
        }

        try {
            const globalTime = Date.now() * 0.001;
            
            // Create animation data for worker processing
            const animations = this._extractAnimationsFromHandler(animationHandler, aiState, globalTime);
            
            if (animations.length === 0) {
                // No animations to process, use original method
                return animationHandler.originalUpdate ? 
                    animationHandler.originalUpdate(deltaTime, aiState) : 
                    animationHandler.update(deltaTime, aiState);
            }

            // Process animations with worker
            const updates = await this.processAnimationBatch(animations, deltaTime, globalTime);
            
            // Apply updates to animation handler
            this._applyAnimationUpdates(animationHandler, updates);

        } catch (error) {
            console.warn('[AnimationIntegration] Worker animation failed, using original method:', error);
            // Fallback to original method
            return animationHandler.originalUpdate ? 
                animationHandler.originalUpdate(deltaTime, aiState) : 
                animationHandler.update(deltaTime, aiState);
        }
    }

    /**
     * Serialize animation for worker
     * @private
     */
    _serializeAnimation(animation) {
        return {
            id: animation.id || Math.random().toString(36),
            type: animation.type || 'complex',
            properties: animation.properties || {},
            duration: animation.duration || 1000,
            loop: animation.loop !== false,
            startTime: animation.startTime || 0
        };
    }

    /**
     * Serialize Vector3 for worker
     * @private
     */
    _serializeVector3(vector) {
        return {
            x: vector.x,
            y: vector.y,
            z: vector.z
        };
    }

    /**
     * Serialize animation state for worker
     * @private
     */
    _serializeAnimationState(state) {
        const serialized = {};
        
        Object.keys(state).forEach(key => {
            const value = state[key];
            if (value && typeof value === 'object' && value.x !== undefined) {
                // Vector3-like object
                serialized[key] = this._serializeVector3(value);
            } else {
                serialized[key] = value;
            }
        });
        
        return serialized;
    }

    /**
     * Deserialize animation state from worker
     * @private
     */
    _deserializeAnimationState(state) {
        const deserialized = {};
        
        Object.keys(state).forEach(key => {
            const value = state[key];
            if (value && typeof value === 'object' && value.x !== undefined) {
                // Vector3-like object
                deserialized[key] = new THREE.Vector3(value.x, value.y, value.z);
            } else {
                deserialized[key] = value;
            }
        });
        
        return deserialized;
    }

    /**
     * Process animation results from worker
     * @private
     */
    _processAnimationResults(results, originalAnimations) {
        return results.map((result, index) => ({
            ...result,
            originalAnimation: originalAnimations[index]
        }));
    }

    /**
     * Process particle results from worker
     * @private
     */
    _processParticleResults(results, originalParticles) {
        return results.map((result, index) => {
            if (!result) return null;
            
            return {
                index: result.index,
                position: new THREE.Vector3(result.position.x, result.position.y, result.position.z),
                velocity: new THREE.Vector3(result.velocity.x, result.velocity.y, result.velocity.z),
                life: result.life,
                opacity: result.opacity,
                scale: result.scale,
                originalParticle: originalParticles[result.index]
            };
        }).filter(Boolean);
    }

    /**
     * Extract animations from animation handler
     * @private
     */
    _extractAnimationsFromHandler(handler, aiState, globalTime) {
        const animations = [];
        
        // Extract common animation types
        if (handler.currentAnimationType) {
            animations.push({
                id: `${handler.constructor.name}_main`,
                type: 'complex',
                properties: {
                    position: handler.animationData || {},
                    rotation: handler.animationData || {},
                    scale: handler.animationData || {}
                },
                duration: 2000,
                loop: true,
                startTime: globalTime
            });
        }
        
        return animations;
    }

    /**
     * Apply animation updates to handler
     * @private
     */
    _applyAnimationUpdates(handler, updates) {
        updates.forEach(update => {
            if (update.position && handler.mesh) {
                // Apply position updates
                if (update.position.x !== undefined) handler.mesh.position.x = update.position.x;
                if (update.position.y !== undefined) handler.mesh.position.y = update.position.y;
                if (update.position.z !== undefined) handler.mesh.position.z = update.position.z;
            }
            
            if (update.rotation && handler.mesh) {
                // Apply rotation updates
                if (update.rotation.x !== undefined) handler.mesh.rotation.x = update.rotation.x;
                if (update.rotation.y !== undefined) handler.mesh.rotation.y = update.rotation.y;
                if (update.rotation.z !== undefined) handler.mesh.rotation.z = update.rotation.z;
            }
            
            if (update.scale && handler.mesh) {
                // Apply scale updates
                if (update.scale.x !== undefined) handler.mesh.scale.x = update.scale.x;
                if (update.scale.y !== undefined) handler.mesh.scale.y = update.scale.y;
                if (update.scale.z !== undefined) handler.mesh.scale.z = update.scale.z;
            }
        });
    }
}

// Create global instance
export const animationIntegration = new AnimationIntegration();

/**
 * Enhanced animation processing function for direct use
 * @param {Array} animations - Animation data
 * @param {number} deltaTime - Delta time
 * @param {number} globalTime - Global time
 * @returns {Promise<Array>} Animation updates
 */
export async function processAnimationsEnhanced(animations, deltaTime, globalTime) {
    const integration = new AnimationIntegration();
    return integration.processAnimationBatch(animations, deltaTime, globalTime);
}
