// src/utils/BulletPatternIntegration.js
// Integration helper for bullet pattern generation with workers

import * as THREE from 'three';

/**
 * Enhanced bullet pattern generation with worker integration
 */
export class BulletPatternIntegration {
    constructor() {
        this.patternQueue = new Map();
        this.isProcessing = false;
        this.lastPatternTime = 0;
    }

    /**
     * Generate bullet pattern using workers
     * @param {string} patternType - Type of pattern to generate
     * @param {THREE.Vector3} position - Origin position
     * @param {THREE.Vector3} targetPosition - Target position (optional)
     * @param {number} intensity - Pattern intensity (0-1)
     * @param {number} speedMultiplier - Speed multiplier
     * @param {Object} customParams - Custom parameters
     * @returns {Promise<Object>} Generated pattern data
     */
    async generatePattern(patternType, position, targetPosition, intensity = 1.0, speedMultiplier = 1.0, customParams = {}) {
        if (!window.workerIntegration) {
            throw new Error('[BulletPatternIntegration] Worker integration not available');
        }

        try {
            const patternData = {
                patternType,
                position: this._serializeVector3(position),
                targetPosition: targetPosition ? this._serializeVector3(targetPosition) : null,
                intensity,
                speedMultiplier,
                customParams
            };

            const result = await window.workerIntegration.generateBulletPattern(patternData);
            
            // Convert worker result to usable format
            return this._processWorkerResult(result);

        } catch (error) {
            console.error('[BulletPatternIntegration] Pattern generation failed:', error);
            throw error;
        }
    }

    /**
     * Generate multiple patterns in batch
     * @param {Array} patternRequests - Array of pattern request objects
     * @returns {Promise<Array>} Array of generated patterns
     */
    async generatePatternBatch(patternRequests) {
        if (!window.workerIntegration) {
            throw new Error('[BulletPatternIntegration] Worker integration not available');
        }

        try {
            const serializedRequests = patternRequests.map(request => ({
                patternType: request.patternType,
                position: this._serializeVector3(request.position),
                targetPosition: request.targetPosition ? this._serializeVector3(request.targetPosition) : null,
                intensity: request.intensity || 1.0,
                speedMultiplier: request.speedMultiplier || 1.0,
                customParams: request.customParams || {}
            }));

            const result = await window.workerIntegration.processBatch(
                'bulletPattern',
                'generatePatternBatch',
                serializedRequests
            );

            return result.map(r => r.success ? this._processWorkerResult(r) : null).filter(Boolean);

        } catch (error) {
            console.error('[BulletPatternIntegration] Batch pattern generation failed:', error);
            throw error;
        }
    }

    /**
     * Enhanced pattern spawning with worker support
     * @param {Object} bulletPatternManager - Reference to BulletPatternManager
     * @param {string} patternName - Name of pattern to spawn
     * @param {THREE.Vector3} position - Spawn position
     * @param {number} intensity - Pattern intensity
     * @param {string} projectileType - Type of projectile
     * @param {number} scaleFactor - Scale factor
     * @param {number} speedMultiplier - Speed multiplier
     * @returns {Promise<void>}
     */
    async enhancePatternSpawning(bulletPatternManager, patternName, position, intensity, projectileType, scaleFactor, speedMultiplier) {
        // Map pattern names to worker pattern types
        const patternMapping = {
            'petal_spread': 'circle',
            'circle_ripple': 'circle',
            'laser_grid': 'grid',
            'spiral_arms': 'spiral',
            'wave_burst': 'wave',
            'hellburst': 'burst',
            'soul_fire_toss': 'chain',
            'vortex_wings': 'vortex'
        };

        const workerPatternType = patternMapping[patternName];
        
        if (workerPatternType && window.workerIntegration) {
            try {
                // Get target position (player position)
                const targetPosition = bulletPatternManager.dungeonHandler?.playerController?.playerMesh?.position;
                
                // Generate pattern using worker
                const pattern = await this.generatePattern(
                    workerPatternType,
                    position,
                    targetPosition,
                    intensity / 100, // Normalize to 0-1
                    speedMultiplier,
                    { scaleFactor }
                );

                // Spawn projectiles from pattern
                this._spawnProjectilesFromPattern(bulletPatternManager, pattern, projectileType);
                return;

            } catch (error) {
                console.warn('[BulletPatternIntegration] Worker pattern failed, using original method:', error);
            }
        }

        // Use original pattern spawning method
        this._useOriginalPatternMethod(bulletPatternManager, patternName, position, intensity, projectileType, scaleFactor, speedMultiplier);
    }

    /**
     * Serialize Vector3 for worker
     * @private
     */
    _serializeVector3(vector) {
        return {
            x: vector.x,
            y: vector.y,
            z: vector.z
        };
    }

    /**
     * Process worker result into usable format
     * @private
     */
    _processWorkerResult(result) {
        if (!result.bullets || !Array.isArray(result.bullets)) {
            return { bullets: [], metadata: result.metadata || {} };
        }

        // Convert bullet data back to THREE.js format
        const bullets = result.bullets.map(bullet => ({
            position: new THREE.Vector3(bullet.position.x, bullet.position.y, bullet.position.z),
            direction: new THREE.Vector3(bullet.direction.x, bullet.direction.y, bullet.direction.z),
            speed: bullet.speed,
            type: bullet.type
        }));

        return {
            bullets,
            patternType: result.patternType,
            metadata: result.metadata || {},
            fromCache: result.fromCache || false
        };
    }

    /**
     * Spawn projectiles from worker-generated pattern
     * @private
     */
    _spawnProjectilesFromPattern(bulletPatternManager, pattern, projectileType) {
        if (!pattern.bullets || pattern.bullets.length === 0) {
            return;
        }

        pattern.bullets.forEach(bullet => {
            bulletPatternManager._spawnProjectile(
                bullet.position,
                bullet.direction,
                projectileType,
                bullet.speed
            );
        });

        console.log(`[BulletPatternIntegration] Spawned ${pattern.bullets.length} projectiles from worker pattern`);
    }

    /**
     * Use original pattern method as fallback
     * @private
     */
    _useOriginalPatternMethod(bulletPatternManager, patternName, position, intensity, projectileType, scaleFactor, speedMultiplier) {
        // Map to original methods
        const methodMap = {
            'petal_spread': '_spawnPetalSpread',
            'circle_ripple': '_spawnCircleRipple',
            'laser_grid': '_spawnLaserGrid',
            'spiral_arms': '_spawnSpiralArms',
            'wave_burst': '_spawnWaveBurst',
            'hellburst': '_spawnHellburst',
            'soul_fire_toss': '_spawnSoulFireToss',
            'vortex_wings': '_spawnBoomerangArcs'
        };

        const methodName = methodMap[patternName];
        if (methodName && typeof bulletPatternManager[methodName] === 'function') {
            bulletPatternManager[methodName](position, intensity / 100, projectileType, scaleFactor, speedMultiplier);
        } else {
            console.warn(`[BulletPatternIntegration] Unknown pattern method: ${patternName}`);
        }
    }

    /**
     * Get performance metrics
     * @returns {Promise<Object>} Performance metrics
     */
    async getPerformanceMetrics() {
        if (!window.workerIntegration) {
            return { error: 'Worker integration not available' };
        }

        try {
            const workerId = window.workerIntegration.workerManager.getAvailableWorker('bulletPattern');
            if (!workerId) {
                return { error: 'No bullet pattern worker available' };
            }

            const response = await window.workerIntegration.workerManager.sendMessage(workerId, {
                type: 'getPerformanceMetrics',
                data: {}
            });

            return response.result;

        } catch (error) {
            console.error('[BulletPatternIntegration] Failed to get performance metrics:', error);
            return { error: error.message };
        }
    }

    /**
     * Clear pattern cache
     * @returns {Promise<boolean>} Success status
     */
    async clearCache() {
        if (!window.workerIntegration) {
            return false;
        }

        try {
            const workerId = window.workerIntegration.workerManager.getAvailableWorker('bulletPattern');
            if (!workerId) {
                return false;
            }

            await window.workerIntegration.workerManager.sendMessage(workerId, {
                type: 'clearCache',
                data: {}
            });

            return true;

        } catch (error) {
            console.error('[BulletPatternIntegration] Failed to clear cache:', error);
            return false;
        }
    }
}

// Create global instance
export const bulletPatternIntegration = new BulletPatternIntegration();

/**
 * Enhanced pattern generation function for direct use
 * @param {string} patternType - Pattern type
 * @param {Object} options - Pattern options
 * @returns {Promise<Object>} Generated pattern
 */
export async function generateBulletPatternEnhanced(patternType, options = {}) {
    const integration = new BulletPatternIntegration();
    return integration.generatePattern(
        patternType,
        options.position || new THREE.Vector3(),
        options.targetPosition,
        options.intensity || 1.0,
        options.speedMultiplier || 1.0,
        options.customParams || {}
    );
}
