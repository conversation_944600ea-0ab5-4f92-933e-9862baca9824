// src/examples/WorkerIntegrationExample.js
// Example implementation showing how to integrate the new workers

import { WorkerManager } from '../core/WorkerManager.js';
import { WorkerPerformanceMonitor } from '../core/WorkerPerformanceMonitor.js';
import { createWorkerIntegration } from '../utils/WorkerIntegration.js';
import { getOptimizedWorkerConfig } from '../config/workerConfig.js';

/**
 * Example class showing complete worker integration
 */
export class WorkerIntegrationExample {
    constructor() {
        this.workerManager = null;
        this.performanceMonitor = null;
        this.workerIntegration = null;
        this.isInitialized = false;
    }

    /**
     * Initialize the worker system
     */
    async initialize() {
        try {
            console.log('[WorkerIntegrationExample] Initializing worker system...');
            
            // 1. Create worker manager with optimized configuration
            const config = getOptimizedWorkerConfig();
            this.workerManager = new WorkerManager(config);
            
            // 2. Create performance monitor
            this.performanceMonitor = new WorkerPerformanceMonitor(this.workerManager);
            this.performanceMonitor.startMonitoring(5000); // Monitor every 5 seconds
            
            // 3. Create worker integration helper
            this.workerIntegration = createWorkerIntegration(this.workerManager);
            
            // 4. Make worker integration globally available
            window.workerIntegration = this.workerIntegration;
            
            // 5. Set up performance monitoring callbacks
            this._setupPerformanceCallbacks();
            
            this.isInitialized = true;
            console.log('[WorkerIntegrationExample] ✅ Worker system initialized successfully');
            
            // 6. Run initial performance test
            await this._runPerformanceTest();
            
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Failed to initialize worker system:', error);
            throw error;
        }
    }

    /**
     * Example: Process mesh data using workers
     */
    async processMeshExample() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        console.log('[WorkerIntegrationExample] Processing mesh example...');

        // Example voxel data
        const voxelData = {
            voxels: [
                { position: { x: 0, y: 0, z: 0 }, color: { r: 1, g: 0, b: 0 }, size: 1 },
                { position: { x: 1, y: 0, z: 0 }, color: { r: 0, g: 1, b: 0 }, size: 1 },
                { position: { x: 0, y: 1, z: 0 }, color: { r: 0, g: 0, b: 1 }, size: 1 },
                { position: { x: 1, y: 1, z: 0 }, color: { r: 1, g: 1, b: 0 }, size: 1 }
            ],
            voxelScale: 1.0
        };

        try {
            const result = await this.workerIntegration.processMesh(voxelData, 'processVoxelMesh');
            console.log('[WorkerIntegrationExample] ✅ Mesh processing result:', result);
            return result;
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Mesh processing failed:', error);
            throw error;
        }
    }

    /**
     * Example: Find path using workers
     */
    async pathfindingExample() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        console.log('[WorkerIntegrationExample] Pathfinding example...');

        const pathData = {
            start: { x: 0, y: 0, z: 0 },
            end: { x: 10, y: 0, z: 10 },
            obstacles: [
                {
                    box: {
                        min: { x: 4, y: -1, z: 4 },
                        max: { x: 6, y: 1, z: 6 }
                    }
                }
            ],
            gridSize: 1.0,
            maxIterations: 1000
        };

        try {
            const path = await this.workerIntegration.findPath(pathData);
            console.log('[WorkerIntegrationExample] ✅ Pathfinding result:', path);
            return path;
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Pathfinding failed:', error);
            throw error;
        }
    }

    /**
     * Example: Process animations using workers
     */
    async animationExample() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        console.log('[WorkerIntegrationExample] Animation example...');

        const animations = [
            {
                id: 'rotation1',
                type: 'rotation',
                properties: {
                    from: { x: 0, y: 0, z: 0 },
                    to: { x: 0, y: Math.PI * 2, z: 0 },
                    easing: 'linear',
                    speed: 1.0
                },
                duration: 2000,
                loop: true,
                startTime: Date.now()
            },
            {
                id: 'position1',
                type: 'position',
                properties: {
                    from: { x: 0, y: 0, z: 0 },
                    to: { x: 5, y: 2, z: 5 },
                    easing: 'easeInOut',
                    wave: true,
                    amplitude: 0.5,
                    frequency: 2
                },
                duration: 3000,
                loop: true,
                startTime: Date.now()
            }
        ];

        try {
            const updates = await this.workerIntegration.processAnimations(
                animations, 
                0.016, // 60 FPS delta time
                Date.now() * 0.001
            );
            console.log('[WorkerIntegrationExample] ✅ Animation updates:', updates);
            return updates;
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Animation processing failed:', error);
            throw error;
        }
    }

    /**
     * Example: Generate bullet patterns using workers
     */
    async bulletPatternExample() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        console.log('[WorkerIntegrationExample] Bullet pattern example...');

        const patternData = {
            patternType: 'spiral',
            position: { x: 0, y: 1, z: 0 },
            targetPosition: { x: 5, y: 1, z: 5 },
            intensity: 0.8,
            speedMultiplier: 1.2,
            customParams: {
                arms: 6,
                projectilesPerArm: 10
            }
        };

        try {
            const pattern = await this.workerIntegration.generateBulletPattern(patternData);
            console.log('[WorkerIntegrationExample] ✅ Bullet pattern result:', pattern);
            return pattern;
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Bullet pattern generation failed:', error);
            throw error;
        }
    }

    /**
     * Example: Batch processing
     */
    async batchProcessingExample() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        console.log('[WorkerIntegrationExample] Batch processing example...');

        // Batch pathfinding requests
        const pathRequests = [
            { start: { x: 0, y: 0, z: 0 }, end: { x: 5, y: 0, z: 5 }, obstacles: [] },
            { start: { x: 2, y: 0, z: 2 }, end: { x: 8, y: 0, z: 8 }, obstacles: [] },
            { start: { x: -2, y: 0, z: -2 }, end: { x: 3, y: 0, z: 3 }, obstacles: [] }
        ];

        try {
            const results = await this.workerIntegration.processBatch(
                'pathfinding',
                'findPathsBatch',
                pathRequests
            );
            console.log('[WorkerIntegrationExample] ✅ Batch processing results:', results);
            return results;
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Batch processing failed:', error);
            throw error;
        }
    }

    /**
     * Get comprehensive performance report
     */
    async getPerformanceReport() {
        if (!this.isInitialized) {
            throw new Error('Worker system not initialized');
        }

        try {
            // Get worker integration metrics
            const workerMetrics = await this.workerIntegration.getPerformanceMetrics();
            
            // Get performance monitor report
            const monitorReport = this.performanceMonitor.getPerformanceReport();
            
            // Get worker manager statistics
            const poolStats = this.workerManager.getPoolStatistics();
            
            const report = {
                timestamp: Date.now(),
                workerMetrics,
                monitorReport,
                poolStats,
                systemInfo: {
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    deviceMemory: navigator.deviceMemory,
                    userAgent: navigator.userAgent
                }
            };
            
            console.log('[WorkerIntegrationExample] 📊 Performance Report:', report);
            return report;
            
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Failed to get performance report:', error);
            throw error;
        }
    }

    /**
     * Run comprehensive performance test
     * @private
     */
    async _runPerformanceTest() {
        console.log('[WorkerIntegrationExample] 🧪 Running performance test...');
        
        const startTime = performance.now();
        
        try {
            // Test all worker types
            await Promise.all([
                this.processMeshExample(),
                this.pathfindingExample(),
                this.animationExample(),
                this.bulletPatternExample()
            ]);
            
            // Test batch processing
            await this.batchProcessingExample();
            
            const totalTime = performance.now() - startTime;
            console.log(`[WorkerIntegrationExample] ✅ Performance test completed in ${totalTime.toFixed(2)}ms`);
            
            // Get final performance report
            await this.getPerformanceReport();
            
        } catch (error) {
            console.error('[WorkerIntegrationExample] ❌ Performance test failed:', error);
        }
    }

    /**
     * Set up performance monitoring callbacks
     * @private
     */
    _setupPerformanceCallbacks() {
        // Log performance alerts
        setInterval(() => {
            const report = this.performanceMonitor.getPerformanceReport();
            
            if (report.alerts.recent.length > 0) {
                console.warn('[WorkerIntegrationExample] ⚠️ Performance alerts:', report.alerts.recent);
            }
            
            if (report.recommendations.length > 0) {
                console.info('[WorkerIntegrationExample] 💡 Performance recommendations:', report.recommendations);
            }
        }, 30000); // Check every 30 seconds
    }

    /**
     * Cleanup and terminate all workers
     */
    cleanup() {
        if (this.performanceMonitor) {
            this.performanceMonitor.stopMonitoring();
        }
        
        if (this.workerManager) {
            this.workerManager.terminateAllWorkers();
        }
        
        // Remove global reference
        if (window.workerIntegration) {
            delete window.workerIntegration;
        }
        
        console.log('[WorkerIntegrationExample] 🧹 Cleanup completed');
    }
}

/**
 * Initialize worker system globally
 * Call this function during application startup
 */
export async function initializeWorkerSystem() {
    try {
        const example = new WorkerIntegrationExample();
        await example.initialize();
        
        // Make example available globally for debugging
        window.workerExample = example;

        // Import and initialize status checker
        try {
            const { workerIntegrationStatus } = await import('../utils/WorkerIntegrationStatus.js');
            setTimeout(() => {
                console.log('📊 [WorkerIntegrationExample] Worker integration status:');
                workerIntegrationStatus.printStatus();
            }, 500);
        } catch (error) {
            console.warn('[WorkerIntegrationExample] Could not load status checker:', error);
        }

        // Import and run integration tests
        try {
            const { workerIntegrationTest } = await import('../tests/WorkerIntegrationTest.js');
            setTimeout(async () => {
                console.log('🧪 [WorkerIntegrationExample] Running integration tests...');
                const testResults = await workerIntegrationTest.runAllTests();
                window.workerTestResults = testResults;
                console.log('📋 [WorkerIntegrationExample] Test results available at window.workerTestResults');
            }, 1000); // Wait 1 second for system to stabilize
        } catch (error) {
            console.warn('[WorkerIntegrationExample] Could not run integration tests:', error);
        }

        console.log('[WorkerIntegrationExample] 🚀 Worker system ready for use');
        return example;
        
    } catch (error) {
        console.error('[WorkerIntegrationExample] ❌ Failed to initialize worker system:', error);
        throw error;
    }
}

// Note: Auto-initialization is handled by main.js
// This prevents double initialization
