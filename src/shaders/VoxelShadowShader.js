/**
 * Voxel-Optimized Shadow Shader
 * Provides hard-edge shadows that complement the blocky voxel aesthetic
 * Includes cascade shadow mapping and voxel-aware bias calculations
 */
import * as THREE from 'three';

const VoxelShadowShader = {
    uniforms: {
        // Standard shadow uniforms
        'shadowMap': { value: null },
        'shadowMatrix': { value: new THREE.Matrix4() },
        'shadowCameraFar': { value: 100.0 },
        'shadowCameraNear': { value: 0.1 },

        // Voxel-specific uniforms
        'voxelSize': { value: 1.0 },
        'shadowHardness': { value: 1.0 }, // 0.0 = soft, 1.0 = hard edges
        'shadowBias': { value: 0.0005 },
        'voxelBiasMultiplier': { value: 2.0 }, // Extra bias for cubic geometry

        // Cascade shadow mapping
        'cascadeCount': { value: 3 },
        'cascadeSplits': { value: [8.0, 25.0, 100.0] },
        'cascadeMap0': { value: null },
        'cascadeMap1': { value: null },
        'cascadeMap2': { value: null },
        'cascadeMatrix0': { value: new THREE.Matrix4() },
        'cascadeMatrix1': { value: new THREE.Matrix4() },
        'cascadeMatrix2': { value: new THREE.Matrix4() },

        // Performance and quality
        'shadowQuality': { value: 1.0 }, // 0.5 = mobile, 1.0 = desktop
        'distanceFade': { value: 80.0 }, // Distance at which shadows fade out
        'shadowIntensity': { value: 0.8 }, // Overall shadow strength

        // Lighting uniforms
        'lightDirection': { value: new THREE.Vector3(0, -1, 0) },
        'lightColor': { value: new THREE.Color(1, 1, 1) },
        'ambientColor': { value: new THREE.Color(0.2, 0.2, 0.3) },

        // Material uniforms
        'diffuse': { value: new THREE.Color(1, 1, 1) },
        'map': { value: null },
        'hasMap': { value: 0.0 },
        'opacity': { value: 1.0 }
    },

    vertexShader: `
        varying vec3 vWorldPosition;
        varying vec3 vNormal;
        varying vec4 vShadowCoord;
        varying float vDepth;
        varying vec2 vUv;

        uniform mat4 shadowMatrix;
        uniform float shadowCameraNear;
        uniform float shadowCameraFar;

        void main() {
            // Standard vertex transformation
            vec4 worldPosition = modelMatrix * vec4(position, 1.0);
            vWorldPosition = worldPosition.xyz;
            vNormal = normalize(normalMatrix * normal);
            vUv = uv;

            // Calculate shadow coordinates
            vShadowCoord = shadowMatrix * worldPosition;

            // Calculate depth for cascade selection
            vec4 viewPosition = viewMatrix * worldPosition;
            vDepth = -viewPosition.z;

            gl_Position = projectionMatrix * viewPosition;
        }
    `,

    fragmentShader: `
        varying vec3 vWorldPosition;
        varying vec3 vNormal;
        varying vec4 vShadowCoord;
        varying float vDepth;
        varying vec2 vUv;

        uniform sampler2D shadowMap;
        uniform float shadowCameraFar;
        uniform float shadowCameraNear;
        uniform float voxelSize;
        uniform float shadowHardness;
        uniform float shadowBias;
        uniform float voxelBiasMultiplier;
        uniform float shadowQuality;
        uniform float distanceFade;
        uniform float shadowIntensity;

        uniform vec3 lightDirection;
        uniform vec3 lightColor;
        uniform vec3 ambientColor;

        // Material uniforms
        uniform vec3 diffuse;
        uniform sampler2D map;
        uniform float hasMap;
        uniform float opacity;

        // Cascade shadow mapping uniforms
        uniform int cascadeCount;
        uniform float cascadeSplits[3];
        uniform sampler2D cascadeMap0;
        uniform sampler2D cascadeMap1;
        uniform sampler2D cascadeMap2;
        uniform mat4 cascadeMatrix0;
        uniform mat4 cascadeMatrix1;
        uniform mat4 cascadeMatrix2;

        // Voxel-aware shadow bias calculation
        float calculateVoxelBias(vec3 normal, vec3 lightDir) {
            // Calculate bias based on surface angle and voxel size
            float cosTheta = dot(normal, -lightDir);
            float baseBias = shadowBias * voxelBiasMultiplier;

            // Increase bias for surfaces parallel to light direction
            float angleBias = baseBias * (1.0 - abs(cosTheta));

            // Add voxel-size dependent bias to prevent self-shadowing
            float voxelBias = (voxelSize * 0.001) / max(cosTheta, 0.1);

            return baseBias + angleBias + voxelBias;
        }

        // Hard-edge shadow sampling for voxel aesthetic
        float sampleVoxelShadow(sampler2D shadowMap, vec4 shadowCoord, float bias) {
            vec3 projCoords = shadowCoord.xyz / shadowCoord.w;
            projCoords = projCoords * 0.5 + 0.5;

            // Check if we're outside shadow map bounds
            if (projCoords.x < 0.0 || projCoords.x > 1.0 ||
                projCoords.y < 0.0 || projCoords.y > 1.0 ||
                projCoords.z > 1.0) {
                return 1.0; // No shadow outside bounds
            }

            float currentDepth = projCoords.z - bias;
            float shadowDepth = texture2D(shadowMap, projCoords.xy).r;

            // Hard-edge comparison for blocky shadows
            float shadow = step(currentDepth, shadowDepth);

            // Optional: Add slight softening based on shadowHardness
            if (shadowHardness < 1.0) {
                float softness = (1.0 - shadowHardness) * 0.002;
                float delta = currentDepth - shadowDepth;
                shadow = smoothstep(-softness, softness, -delta);
            }

            return shadow;
        }

        // Cascade shadow selection and sampling
        float sampleCascadeShadow(vec3 worldPos, vec3 normal, vec3 lightDir) {
            // Select appropriate cascade based on depth
            int cascadeIndex = 0;
            for (int i = 0; i < 3; i++) {
                if (vDepth < cascadeSplits[i]) {
                    cascadeIndex = i;
                    break;
                }
            }

            // Calculate shadow coordinates and sample based on cascade
            vec4 shadowCoord;
            float shadow = 1.0;
            float bias = calculateVoxelBias(normal, lightDir);

            if (cascadeIndex == 0) {
                shadowCoord = cascadeMatrix0 * vec4(worldPos, 1.0);
                shadow = sampleVoxelShadow(cascadeMap0, shadowCoord, bias);
            } else if (cascadeIndex == 1) {
                shadowCoord = cascadeMatrix1 * vec4(worldPos, 1.0);
                shadow = sampleVoxelShadow(cascadeMap1, shadowCoord, bias);
            } else if (cascadeIndex == 2) {
                shadowCoord = cascadeMatrix2 * vec4(worldPos, 1.0);
                shadow = sampleVoxelShadow(cascadeMap2, shadowCoord, bias);
            }

            // Apply distance fade
            float fadeFactor = 1.0 - smoothstep(distanceFade * 0.7, distanceFade, vDepth);
            shadow = mix(1.0, shadow, fadeFactor);

            return shadow;
        }

        void main() {
            vec3 normal = normalize(vNormal);
            vec3 lightDir = normalize(lightDirection);

            // Sample the original material color/texture
            vec4 materialColor = vec4(diffuse, opacity);
            if (hasMap > 0.5) {
                vec4 texColor = texture2D(map, vUv);
                materialColor *= texColor;
            }

            // Calculate basic lighting
            float NdotL = max(dot(normal, -lightDir), 0.0);
            vec3 lightContribution = lightColor * NdotL;

            // Calculate shadow factor
            float shadowFactor = 1.0;
            if (cascadeCount > 0) {
                shadowFactor = sampleCascadeShadow(vWorldPosition, normal, lightDir);
            } else {
                // Fallback to basic lighting without shadows for now
                shadowFactor = 1.0;
            }

            // Apply shadow intensity
            shadowFactor = mix(1.0, shadowFactor, shadowIntensity);

            // Combine material color with lighting and shadows
            vec3 finalColor = materialColor.rgb * (ambientColor + (lightContribution * shadowFactor));

            gl_FragColor = vec4(finalColor, materialColor.a);
        }
    `
};

export { VoxelShadowShader };
