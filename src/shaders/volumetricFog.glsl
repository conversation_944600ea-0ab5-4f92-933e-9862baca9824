#define FOG_STEPS 32

uniform sampler2D tDiffuse;
uniform sampler2D tDepth;

uniform sampler3D fogTexture;
uniform vec3  fogBoundsMin;
uniform vec3  fogBoundsMax;
uniform float fogDensity;
uniform vec3  fogAlbedo;
uniform float fogAnisotropy;

uniform float cameraNear;
uniform float cameraFar;
uniform mat4  invProjection;
uniform mat4  invView;

varying vec2 vUv;

// Helper: decode depth
float depthToViewZ(float z){return cameraNear*cameraFar/(cameraFar - z*(cameraFar-cameraNear))*(-1.0);} // perspective

vec3 getWorldPos(vec2 uv, float depth){
    float viewZ = depthToViewZ(depth);
    vec4 pClip = vec4(uv*2.0-1.0, depth, 1.0);
    vec4 pView = invProjection * pClip; pView /= pView.w; pView.z = viewZ;
    vec4 pWorld = invView * pView;
    return pWorld.xyz;
}

// Henyey–<PERSON>tein phase
float phaseHG(float cosTheta, float g){
    float denom = 1.0 + g*g - 2.0*g*cosTheta;
    return (1.0/(4.0*3.141592)) * (1.0 - g*g)/pow(denom,1.5);
}

void main(){
    float depth = texture2D(tDepth, vUv).x;
    vec3 worldPos = getWorldPos(vUv, depth);
    // Ray from camera to worldPos
    vec3 camPos = (invView * vec4(0.0,0.0,0.0,1.0)).xyz;
    vec3 dir = normalize(worldPos - camPos);
    float totalDist = length(worldPos - camPos);

    // Intersect with fog AABB
    vec3 invDir = 1.0/dir;
    vec3 tMin = (fogBoundsMin - camPos) * invDir;
    vec3 tMax = (fogBoundsMax - camPos) * invDir;
    vec3 t1 = min(tMin, tMax);
    vec3 t2 = max(tMin, tMax);
    float tNear = max(max(t1.x,t1.y), t1.z);
    float tFar  = min(min(t2.x,t2.y), t2.z);
    if(tFar < 0.0 || tNear > tFar){
        gl_FragColor = texture2D(tDiffuse, vUv);
        return; // No fog intersection
    }
    tNear = max(tNear, 0.0);
    tFar = min(tFar, totalDist);

    // March inside the fog only
    float stepLen = (tFar - tNear)/float(FOG_STEPS);
    vec3 marchPos = camPos + dir*(tNear + stepLen*0.5);

    vec3 fogColorAccum = vec3(0.0);
    float transmittance = 1.0;

    for(int i=0;i<FOG_STEPS;i++){
        // Normalised coord 0-1 inside volume
        vec3 local = (marchPos - fogBoundsMin)/(fogBoundsMax - fogBoundsMin);
        float density = texture(fogTexture, local).r * fogDensity;
        float stepOpacity = 1.0 - exp(-density*stepLen);
        fogColorAccum += transmittance * stepOpacity * fogAlbedo;
        transmittance *= 1.0 - stepOpacity;
        marchPos += dir*stepLen;
    }

    vec3 sceneColor = texture2D(tDiffuse,vUv).rgb;
    vec3 finalColor = mix(sceneColor, fogColorAccum + sceneColor*transmittance, 1.0 - transmittance);
    gl_FragColor = vec4(finalColor,1.0);
} 