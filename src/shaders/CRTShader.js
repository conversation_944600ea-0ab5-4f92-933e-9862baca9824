/**
 * CRT Shader with manufacturer-specific emulation
 * Simulates different CRT displays with physically accurate characteristics
 */
import * as THREE from 'three';

const CRTShader = {
    uniforms: {
        'tDiffuse': { value: null },  // Rendered scene texture
        'time': { value: 0.0 },       // Time for animation effects
        'resolution': { value: new THREE.Vector2(800, 600) },

        // CRT physical parameters
        'curvature': { value: 0.5 },        // Screen curvature amount
        'scanlineIntensity': { value: 0.5 }, // Intensity of scanlines
        'scanlineCount': { value: 800.0 },   // Number of scanlines
        'vignetteIntensity': { value: 0.3 }, // Darkening at corners

        // Manufacturer-specific parameters
        'phosphorProfile': { value: 0 },     // 0: Sony, 1: Panasonic, 2: Generic
        'maskType': { value: 0 },            // 0: Aperture grille (Sony), 1: Shadow mask (others)
        'maskSize': { value: 0.5 },          // Size of mask elements
        'maskIntensity': { value: 0.5 },     // Intensity of mask effect
        'bleedingAmount': { value: 0.3 },    // Light bleeding amount
        'ghostingAmount': { value: 0.2 },    // Ghosting/persistence
        'convergenceFailureX': { value: 0.0 }, // RGB convergence failure X
        'convergenceFailureY': { value: 0.0 }, // RGB convergence failure Y
        'noiseAmount': { value: 0.05 },      // Static noise amount
        'flickerAmount': { value: 0.05 },    // Screen flicker amount

        // Refresh visualization
        'refreshScanline': { value: 0.0 },   // Current scanline being refreshed (0.0-1.0)
        'refreshSpeed': { value: 0.1 },      // Speed of refresh visualization
        
        // Lens distortion effects for heavy attacks (anime-style screen warp)
        'lensDistortionStrength': { value: 0.0 }, // 0.0 = no distortion, 1.0 = maximum warp
        'lensDistortionCenter': { value: new THREE.Vector2(0.5, 0.5) }, // Center point of distortion
        'lensDistortionType': { value: 0 },   // 0 = barrel, 1 = pincushion, 2 = wave ripple
        'lensDistortionRadius': { value: 0.3 }, // Radius of distortion effect
        'lensDistortionZoom': { value: 1.0 }  // Zoom compensation to prevent black edges
    },

    vertexShader: /* glsl */`
        varying vec2 vUv;

        void main() {
            vUv = uv;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
    `,

    fragmentShader: /* glsl */`
        #define PI 3.14159265359

        uniform sampler2D tDiffuse;
        uniform float time;
        uniform vec2 resolution;
        uniform float curvature;
        uniform float scanlineIntensity;
        uniform float scanlineCount;
        uniform float vignetteIntensity;
        uniform int phosphorProfile;
        uniform int maskType;
        uniform float maskSize;
        uniform float maskIntensity;
        uniform float bleedingAmount;
        uniform float ghostingAmount;
        uniform float convergenceFailureX;
        uniform float convergenceFailureY;
        uniform float noiseAmount;
        uniform float flickerAmount;
        uniform float refreshScanline;
        uniform float refreshSpeed;
        
        // Lens distortion uniforms for anime-style screen warp
        uniform float lensDistortionStrength;
        uniform vec2 lensDistortionCenter;
        uniform int lensDistortionType;
        uniform float lensDistortionRadius;
        uniform float lensDistortionZoom; // Zoom compensation to prevent black edges

        varying vec2 vUv;

        // Random function
        float random(vec2 p) {
            return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453123);
        }

        // Apply screen curvature
        vec2 curveRemapUV(vec2 uv) {
            // Convert UV from 0-1 to -1 to +1
            vec2 cuv = uv * 2.0 - 1.0;

            // Apply curvature
            vec2 offset = abs(cuv.yx) / vec2(curvature, curvature);
            cuv = cuv + cuv * offset * offset;

            // Convert back to 0-1 range
            return cuv * 0.5 + 0.5;
        }

        // Anime-style lens distortion for heavy attacks (Dragon Ball Z style)
        vec2 applyLensDistortion(vec2 uv) {
            if (lensDistortionStrength <= 0.0) return uv;
            
            // Distance from distortion center
            vec2 delta = uv - lensDistortionCenter;
            float distance = length(delta);
            
            // Only apply distortion within radius
            if (distance > lensDistortionRadius) return uv;
            
            // Normalize distance within radius
            float normalizedDist = distance / lensDistortionRadius;
            
            // Apply different distortion types
            float distortionFactor = 0.0;
            
            if (lensDistortionType == 0) {
                // Barrel distortion (inward warp)
                distortionFactor = lensDistortionStrength * normalizedDist * normalizedDist;
            } else if (lensDistortionType == 1) {
                // Pincushion distortion (outward warp) 
                distortionFactor = -lensDistortionStrength * normalizedDist * normalizedDist;
            } else if (lensDistortionType == 2) {
                // Wave ripple effect
                float ripple = sin(normalizedDist * PI * 6.0) * 0.5 + 0.5;
                distortionFactor = lensDistortionStrength * ripple * normalizedDist;
            }
            
            // Apply distortion with smooth falloff at edges
            float falloff = smoothstep(0.8, 1.0, normalizedDist);
            distortionFactor *= (1.0 - falloff);
            
            return uv + delta * distortionFactor;
        }

        // Aperture grille (Sony Trinitron style)
        float apertureGrille(vec2 uv) {
            float x = uv.x * resolution.x;
            return 0.5 + 0.5 * sin(x * PI * maskSize);
        }

        // Shadow mask (Panasonic, other manufacturers)
        float shadowMask(vec2 uv) {
            float x = uv.x * resolution.x;
            float y = uv.y * resolution.y;

            // Create dot pattern
            float mask = 0.5 + 0.5 * sin(x * PI * maskSize) * sin(y * PI * maskSize);
            return mask;
        }

        // Apply manufacturer-specific mask
        float applyMask(vec2 uv) {
            if (maskType == 0) {
                return mix(1.0, apertureGrille(uv), maskIntensity);
            } else {
                return mix(1.0, shadowMask(uv), maskIntensity);
            }
        }

        // Scanline effect
        float scanline(vec2 uv) {
            float y = uv.y * resolution.y;
            return 0.5 + 0.5 * sin(y * 2.0 * PI / scanlineCount);
        }

        // Vignette effect (darkening at corners)
        float vignette(vec2 uv) {
            uv = uv * 2.0 - 1.0;
            return 1.0 - dot(uv, uv) * vignetteIntensity;
        }

        // Simulate refresh visualization (line by line)
        float refreshEffect(vec2 uv) {
            // Calculate distance from current refresh scanline
            float scanPos = mod(refreshScanline + time * refreshSpeed, 1.0);
            float dist = abs(uv.y - scanPos);

            // Create a bright line at the current scanline position
            float scanEffect = smoothstep(0.005, 0.0, dist);

            // Dim the areas that haven't been refreshed yet
            float dimming = 0.0;
            if (uv.y > scanPos) {
                dimming = 0.2; // Areas not yet refreshed are dimmer
            }

            return 1.0 - dimming + scanEffect * 0.5;
        }

        // Phosphor persistence/ghosting based on manufacturer
        vec3 applyPhosphorProfile(vec3 color, vec3 prevColor) {
            // Different phosphor profiles
            if (phosphorProfile == 0) {
                // Sony - stronger blue persistence
                return mix(color, prevColor, vec3(0.1, 0.1, 0.15) * ghostingAmount);
            } else if (phosphorProfile == 1) {
                // Panasonic - more balanced persistence
                return mix(color, prevColor, vec3(0.12, 0.12, 0.12) * ghostingAmount);
            } else {
                // Generic
                return mix(color, prevColor, vec3(0.1) * ghostingAmount);
            }
        }

        // RGB convergence failure simulation
        vec3 applyConvergenceFailure(vec2 uv, vec3 color) {
            // Sample the texture with slight offsets for R, G, B
            vec2 redOffset = vec2(convergenceFailureX, convergenceFailureY) / resolution;
            vec2 blueOffset = vec2(-convergenceFailureX, -convergenceFailureY) / resolution;

            float r = texture2D(tDiffuse, uv + redOffset).r;
            float g = texture2D(tDiffuse, uv).g;
            float b = texture2D(tDiffuse, uv + blueOffset).b;

            return vec3(r, g, b);
        }

        void main() {
            // Apply zoom compensation to prevent black edges during distortion
            vec2 zoomedUV = vUv;
            if (lensDistortionStrength > 0.0) {
                // Zoom in slightly to crop out potential black edges
                vec2 center = vec2(0.5);
                zoomedUV = (vUv - center) / lensDistortionZoom + center;
            }
            
            // Apply lens distortion (for anime-style heavy attack effects)
            vec2 distortedUV = applyLensDistortion(zoomedUV);
            
            // Then apply screen curvature
            vec2 uv = curveRemapUV(distortedUV);

            // Check if we're outside the curved screen
            if (uv.x < 0.0 || uv.x > 1.0 || uv.y < 0.0 || uv.y > 1.0) {
                gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);
                return;
            }

            // Get base color
            vec3 color = texture2D(tDiffuse, uv).rgb;

            // Apply RGB convergence failure
            if (convergenceFailureX > 0.0 || convergenceFailureY > 0.0) {
                color = applyConvergenceFailure(uv, color);
            }

            // Apply scanlines
            float scanlineEffect = mix(1.0, scanline(uv), scanlineIntensity);
            color *= scanlineEffect;

            // Apply manufacturer-specific mask
            float maskEffect = applyMask(uv);
            color *= maskEffect;

            // Apply vignette
            float vignetteEffect = vignette(uv);
            color *= vignetteEffect;

            // Apply refresh visualization
            float refreshFactor = refreshEffect(uv);
            color *= refreshFactor;

            // Apply noise
            float noise = random(vUv + vec2(time, 0.0));
            color += (noise - 0.5) * noiseAmount;

            // Apply flicker
            float flicker = 1.0 + (random(vec2(time, 0.0)) - 0.5) * flickerAmount;
            color *= flicker;

            // Boost overall brightness before applying effects
            color = color * 3.5; // Increase base brightness by 250%

            // Apply HDR-like phosphor glow with realistic brightness targets (80-300 nits range)
            // First create a high-intensity mask for bright areas
            vec3 brightPass = max(vec3(0.0), color - vec3(0.5)); // Lower threshold to capture more highlights

            // Apply phosphor-specific brightness characteristics
            vec3 phosphorBrightness;
            if (phosphorProfile == 0) { // Sony Trinitron
                // Sony had slightly brighter green, strong blue persistence
                phosphorBrightness = vec3(1.0, 1.2, 0.9) * brightPass * brightPass;
            } else if (phosphorProfile == 1) { // Panasonic
                // More balanced phosphors with slightly brighter green
                phosphorBrightness = vec3(1.0, 1.15, 0.95) * brightPass * brightPass;
            } else { // Generic/Arcade
                // Arcade monitors often had higher contrast and brighter phosphors
                phosphorBrightness = vec3(1.1, 1.3, 0.9) * brightPass * brightPass;
            }

            // Apply two-stage bloom for more realistic glow
            // Narrow bloom for the immediate phosphor glow
            vec3 narrowBloom = phosphorBrightness * 2.5 * bleedingAmount;
            // Wider bloom for general light bleeding
            vec3 wideBloom = color * color * bleedingAmount * 0.7;

            // Combine effects with brightness limiting to simulate realistic peak nits
            // Limit to ~5.0x standard white level (simulating ~700-800 nits on a calibrated display)
            color = min(vec3(5.0), color + narrowBloom + wideBloom);

            // Final color
            gl_FragColor = vec4(color, 1.0);
        }
    `
};

export { CRTShader };
