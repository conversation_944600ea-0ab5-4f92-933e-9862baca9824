// src/tests/WorkerIntegrationTest.js
// Comprehensive test suite for worker integration

import * as THREE from 'three';

/**
 * Test suite for worker integration functionality
 */
export class WorkerIntegrationTest {
    constructor() {
        this.testResults = [];
        this.isRunning = false;
    }

    /**
     * Run all integration tests
     * @returns {Promise<Object>} Test results
     */
    async runAllTests() {
        if (this.isRunning) {
            console.warn('[WorkerIntegrationTest] Tests already running');
            return { error: 'Tests already running' };
        }

        this.isRunning = true;
        this.testResults = [];
        
        console.log('🧪 [WorkerIntegrationTest] Starting comprehensive worker integration tests...');
        
        try {
            // Test 1: Worker system initialization
            await this._testWorkerInitialization();
            
            // Test 2: Mesh processing worker
            await this._testMeshProcessingWorker();
            
            // Test 3: Pathfinding worker
            await this._testPathfindingWorker();
            
            // Test 4: Animation worker
            await this._testAnimationWorker();
            
            // Test 5: Bullet pattern worker
            await this._testBulletPatternWorker();
            
            // Test 6: Performance monitoring
            await this._testPerformanceMonitoring();
            
            // Test 7: Error handling
            await this._testErrorHandling();
            
            // Test 8: Integration with existing systems
            await this._testSystemIntegration();
            
            const summary = this._generateTestSummary();
            console.log('✅ [WorkerIntegrationTest] All tests completed');
            return summary;
            
        } catch (error) {
            console.error('❌ [WorkerIntegrationTest] Test suite failed:', error);
            return { error: error.message, results: this.testResults };
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Test worker system initialization
     * @private
     */
    async _testWorkerInitialization() {
        const testName = 'Worker System Initialization';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            // Check if worker integration is available
            if (!window.workerIntegration) {
                throw new Error('Worker integration not available globally');
            }
            
            // Check if worker manager is initialized
            if (!window.workerIntegration.workerManager) {
                throw new Error('Worker manager not initialized');
            }
            
            // Check if performance monitor is available
            if (!window.workerExample || !window.workerExample.performanceMonitor) {
                throw new Error('Performance monitor not available');
            }
            
            // Get pool statistics
            const poolStats = window.workerIntegration.workerManager.getPoolStatistics();
            if (!poolStats || typeof poolStats !== 'object') {
                throw new Error('Pool statistics not available');
            }
            
            this._addTestResult(testName, true, 'Worker system properly initialized', poolStats);
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test mesh processing worker
     * @private
     */
    async _testMeshProcessingWorker() {
        const testName = 'Mesh Processing Worker';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            // Create test voxel data
            const testVoxels = [
                { position: { x: 0, y: 0, z: 0 }, color: { r: 1, g: 0, b: 0 }, size: 1 },
                { position: { x: 1, y: 0, z: 0 }, color: { r: 0, g: 1, b: 0 }, size: 1 },
                { position: { x: 0, y: 1, z: 0 }, color: { r: 0, g: 0, b: 1 }, size: 1 }
            ];
            
            const startTime = performance.now();
            const result = await window.workerIntegration.processMesh({
                voxels: testVoxels,
                voxelScale: 1.0
            }, 'processVoxelMesh');
            const processingTime = performance.now() - startTime;
            
            if (!result || !result.meshData) {
                throw new Error('Invalid mesh processing result');
            }
            
            this._addTestResult(testName, true, `Processed ${testVoxels.length} voxels in ${processingTime.toFixed(2)}ms`, {
                processingTime,
                meshCount: result.meshData.length,
                metadata: result.metadata
            });
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test pathfinding worker
     * @private
     */
    async _testPathfindingWorker() {
        const testName = 'Pathfinding Worker';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            const pathData = {
                start: { x: 0, y: 0, z: 0 },
                end: { x: 5, y: 0, z: 5 },
                obstacles: [],
                gridSize: 1.0,
                maxIterations: 100
            };
            
            const startTime = performance.now();
            const path = await window.workerIntegration.findPath(pathData);
            const processingTime = performance.now() - startTime;
            
            if (!Array.isArray(path)) {
                throw new Error('Invalid pathfinding result - not an array');
            }
            
            if (path.length === 0) {
                throw new Error('No path found');
            }
            
            this._addTestResult(testName, true, `Found path with ${path.length} waypoints in ${processingTime.toFixed(2)}ms`, {
                processingTime,
                pathLength: path.length,
                startPoint: pathData.start,
                endPoint: pathData.end
            });
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test animation worker
     * @private
     */
    async _testAnimationWorker() {
        const testName = 'Animation Worker';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            const animations = [
                {
                    id: 'test_rotation',
                    type: 'rotation',
                    properties: {
                        from: { x: 0, y: 0, z: 0 },
                        to: { x: 0, y: Math.PI, z: 0 },
                        easing: 'linear'
                    },
                    duration: 1000,
                    loop: true,
                    startTime: Date.now()
                }
            ];
            
            const startTime = performance.now();
            const updates = await window.workerIntegration.processAnimations(
                animations,
                0.016, // 60 FPS
                Date.now() * 0.001
            );
            const processingTime = performance.now() - startTime;
            
            if (!Array.isArray(updates)) {
                throw new Error('Invalid animation result - not an array');
            }
            
            this._addTestResult(testName, true, `Processed ${animations.length} animations in ${processingTime.toFixed(2)}ms`, {
                processingTime,
                animationCount: animations.length,
                updateCount: updates.length
            });
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test bullet pattern worker
     * @private
     */
    async _testBulletPatternWorker() {
        const testName = 'Bullet Pattern Worker';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            const patternData = {
                patternType: 'spiral',
                position: { x: 0, y: 1, z: 0 },
                intensity: 0.8,
                speedMultiplier: 1.0,
                customParams: { arms: 4 }
            };
            
            const startTime = performance.now();
            const pattern = await window.workerIntegration.generateBulletPattern(patternData);
            const processingTime = performance.now() - startTime;
            
            if (!pattern || !pattern.bullets || !Array.isArray(pattern.bullets)) {
                throw new Error('Invalid bullet pattern result');
            }
            
            if (pattern.bullets.length === 0) {
                throw new Error('No bullets generated');
            }
            
            this._addTestResult(testName, true, `Generated ${pattern.bullets.length} bullets in ${processingTime.toFixed(2)}ms`, {
                processingTime,
                bulletCount: pattern.bullets.length,
                patternType: pattern.patternType,
                fromCache: pattern.fromCache
            });
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test performance monitoring
     * @private
     */
    async _testPerformanceMonitoring() {
        const testName = 'Performance Monitoring';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            const metrics = await window.workerIntegration.getPerformanceMetrics();
            
            if (!metrics || typeof metrics !== 'object') {
                throw new Error('Invalid performance metrics');
            }
            
            if (!metrics.workers || typeof metrics.workers !== 'object') {
                throw new Error('Worker metrics not available');
            }
            
            const workerCount = Object.keys(metrics.workers).length;
            
            this._addTestResult(testName, true, `Performance monitoring active for ${workerCount} worker types`, {
                workerTypes: Object.keys(metrics.workers),
                deviceType: metrics.deviceType,
                timestamp: metrics.timestamp
            });
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test error handling
     * @private
     */
    async _testErrorHandling() {
        const testName = 'Error Handling';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            // Test invalid mesh data
            try {
                await window.workerIntegration.processMesh({ invalid: 'data' }, 'processVoxelMesh');
                throw new Error('Should have thrown error for invalid data');
            } catch (error) {
                if (!error.message.includes('Invalid') && !error.message.includes('voxel')) {
                    throw new Error('Unexpected error message: ' + error.message);
                }
            }
            
            // Test invalid pathfinding data
            try {
                await window.workerIntegration.findPath({ invalid: 'data' });
                throw new Error('Should have thrown error for invalid pathfinding data');
            } catch (error) {
                if (!error.message.includes('Invalid') && !error.message.includes('start') && !error.message.includes('end')) {
                    throw new Error('Unexpected error message: ' + error.message);
                }
            }
            
            this._addTestResult(testName, true, 'Error handling working correctly');
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Test integration with existing systems
     * @private
     */
    async _testSystemIntegration() {
        const testName = 'System Integration';
        console.log(`🔧 Testing: ${testName}`);
        
        try {
            // Check if DungeonHandler has worker integration
            if (window.sceneManager && window.sceneManager.dungeonHandler) {
                const dungeonHandler = window.sceneManager.dungeonHandler;
                if (!dungeonHandler.workerIntegration) {
                    throw new Error('DungeonHandler missing worker integration');
                }
            }
            
            // Check if global worker integration is properly set
            if (window.workerIntegration !== window.workerExample.workerIntegration) {
                console.warn('Global worker integration reference mismatch');
            }
            
            this._addTestResult(testName, true, 'System integration verified');
            
        } catch (error) {
            this._addTestResult(testName, false, error.message);
        }
    }

    /**
     * Add test result
     * @private
     */
    _addTestResult(testName, success, message, data = null) {
        const result = {
            testName,
            success,
            message,
            data,
            timestamp: Date.now()
        };
        
        this.testResults.push(result);
        
        const status = success ? '✅' : '❌';
        console.log(`${status} [${testName}] ${message}`);
        
        if (data) {
            console.log(`   Data:`, data);
        }
    }

    /**
     * Generate test summary
     * @private
     */
    _generateTestSummary() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        
        const summary = {
            totalTests,
            passedTests,
            failedTests,
            successRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
            results: this.testResults,
            timestamp: Date.now()
        };
        
        console.log(`📊 [WorkerIntegrationTest] Summary: ${passedTests}/${totalTests} tests passed (${summary.successRate.toFixed(1)}%)`);
        
        return summary;
    }
}

// Create global test instance
export const workerIntegrationTest = new WorkerIntegrationTest();

// Auto-run tests after worker system is initialized
if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
        // Wait for worker system to be ready
        setTimeout(async () => {
            if (window.workerIntegration && window.workerExample) {
                console.log('🚀 [WorkerIntegrationTest] Auto-running integration tests...');
                const results = await workerIntegrationTest.runAllTests();
                window.workerTestResults = results;
                console.log('📋 [WorkerIntegrationTest] Results available at window.workerTestResults');
            }
        }, 2000); // Wait 2 seconds for initialization
    });
}
