import { ChessLogic } from '../utils/ChessLogic.js';

/**
 * ChessAI - Simple AI for chess game
 */
export class ChessAI {
    constructor(difficulty) {
        this.difficulty = difficulty;
        this.maxDepth = this.getDepthFromDifficulty(difficulty);
        this.pieceValues = {
            pawn: 100,
            knight: 320,
            bishop: 330,
            rook: 500,
            queen: 900,
            king: 20000 // King value is high to prioritize not losing it
        };
        console.log(`[ChessAI] AI initialized with difficulty: ${difficulty}, maxDepth: ${this.maxDepth}`);
    }
    
    getDepthFromDifficulty(difficulty) {
        switch (difficulty) {
            case 'easy': return 1;
            case 'normal': return 2;
            case 'hard': return 3;
            case 'epic': return 4;
            default: return 2;
        }
    }
    
    /**
     * Get the best move using Minimax with Alpha-Beta Pruning
     */
    getBestMove(board, playerColor, difficulty, kingPositions, castlingRights, enPassantTarget) {
        this.maxDepth = this.getDepthFromDifficulty(difficulty); // Update depth in case difficulty changed
        console.log(`[ChessAI] Calculating best move for ${playerColor} with depth ${this.maxDepth}...`);
        
        let bestMove = null;
        let bestValue = playerColor === 'black' ? -Infinity : Infinity;
        
        const allPossibleMoves = ChessLogic.getAllPossibleMoves(board, playerColor, kingPositions, castlingRights, enPassantTarget);
        
        // Shuffle moves to add some randomness, especially for lower difficulties
        if (difficulty === 'easy' || difficulty === 'normal') {
            this.shuffleArray(allPossibleMoves);
        }
        
        for (const move of allPossibleMoves) {
            // Simulate the move
            const simulatedBoard = ChessLogic.simulateMove(board, move);
            
            // Evaluate board
            const value = this.minimax(simulatedBoard, this.maxDepth - 1, -Infinity, Infinity, playerColor === 'black' ? 'white' : 'black');
            
            if (playerColor === 'black') { // Maximizing player
                if (value > bestValue) {
                    bestValue = value;
                    bestMove = move;
                }
            } else { // Minimizing player
                if (value < bestValue) {
                    bestValue = value;
                    bestMove = move;
                }
            }
        }
        
        console.log(`[ChessAI] Best move found:`, bestMove, `with value: ${bestValue}`);
        return bestMove;
    }
    
    /**
     * Minimax algorithm with Alpha-Beta Pruning
     */
    minimax(board, depth, alpha, beta, currentPlayer) {
        if (depth === 0) {
            return this.evaluateBoard(board);
        }
        
        // For minimax, we need to pass the current game state (board, kingPositions, etc.)
        // to ChessLogic.getValidMoves. Since ChessAI doesn't hold these directly,
        // we'll need to derive them or pass them down.
        // For simplicity in this refactor, we'll assume a basic board state for move generation.
        // A more robust solution would involve passing a full game state object.
        
        // Dummy game state for ChessLogic calls within AI simulation
        const dummyKingPositions = this.extractKingPositions(board);
        const dummyCastlingRights = {
            white: { kingside: true, queenside: true },
            black: { kingside: true, queenside: true }
        };
        const dummyEnPassantTarget = null;

        const allPossibleMoves = ChessLogic.getAllPossibleMoves(board, currentPlayer, dummyKingPositions, dummyCastlingRights, dummyEnPassantTarget);
        
        if (currentPlayer === 'black') { // Maximizing player (AI)
            let maxEval = -Infinity;
            for (const move of allPossibleMoves) {
                const newBoard = ChessLogic.simulateMove(board, move);
                const evaluation = this.minimax(newBoard, depth - 1, alpha, beta, 'white');
                maxEval = Math.max(maxEval, evaluation);
                alpha = Math.max(alpha, evaluation);
                if (beta <= alpha) {
                    break; // Beta cut-off
                }
            }
            return maxEval;
        } else { // Minimizing player (Player)
            let minEval = Infinity;
            for (const move of allPossibleMoves) {
                const newBoard = ChessLogic.simulateMove(board, move);
                const evaluation = this.minimax(newBoard, depth - 1, alpha, beta, 'black');
                minEval = Math.min(minEval, evaluation);
                beta = Math.min(beta, evaluation);
                if (beta <= alpha) {
                    break; // Alpha cut-off
                }
            }
            return minEval;
        }
    }
    
    /**
     * Evaluate the board state
     */
    evaluateBoard(board) {
        let score = 0;
        
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = board[x][y];
                if (piece) {
                    let value = this.pieceValues[piece.type];
                    
                    // Add positional bonuses
                    value += this.getPositionalValue(piece, x, y);
                    
                    // Double value in endgame for kings
                    if (piece.type === 'king' && this.isEndgame(board)) {
                        value += 0.5;
                    }
                    
                    score += piece.color === 'black' ? value : -value;
                }
            }
        }
        
        // Add bonus for controlling center
        score += this.evaluateCenter(board);
        
        // Add bonus for pawn structure
        score += this.evaluatePawnStructure(board);
        
        return score;
    }
    
    getPositionalValue(piece, x, y) {
        // Center squares are more valuable
        const centerBonus = (3 - Math.abs(3.5 - x)) * (3 - Math.abs(3.5 - y)) * 0.1;
        
        switch (piece.type) {
            case 'pawn':
                // Pawns are better when advanced
                const advancement = piece.color === 'white' ? (6 - y) : (y - 1);
                return advancement * 0.1 + centerBonus;
            case 'knight':
                // Knights are better in center
                return centerBonus * 2;
            case 'bishop':
                // Bishops like long diagonals
                return centerBonus * 1.5;
            case 'rook':
                // Rooks like open files (simplified)
                return centerBonus;
            case 'queen':
                // Queen likes mobility (simplified)
                return centerBonus * 0.5;
            case 'king':
                // King should stay safe in early game, active in endgame
                return piece.color === 'white' ? 
                    (y > 5 ? 0.3 : -0.2) : (y < 2 ? 0.3 : -0.2);
            default:
                return 0;
        }
    }
    
    evaluateCenter(board) {
        let centerControl = 0;
        const centerSquares = [[3,3], [3,4], [4,3], [4,4]];
        
        for (const [x, y] of centerSquares) {
            const piece = board[x][y];
            if (piece) {
                centerControl += piece.color === 'black' ? 0.2 : -0.2;
            }
        }
        
        return centerControl;
    }
    
    evaluatePawnStructure(board) {
        let pawnScore = 0;
        
        // Count pawns in each file
        for (let x = 0; x < 8; x++) {
            let whitePawns = 0;
            let blackPawns = 0;
            
            for (let y = 0; y < 8; y++) {
                const piece = board[x][y];
                if (piece && piece.type === 'pawn') {
                    if (piece.color === 'white') whitePawns++;
                    else blackPawns++;
                }
            }
            
            // Penalize doubled pawns
            if (whitePawns > 1) pawnScore -= (whitePawns - 1) * 0.5;
            if (blackPawns > 1) pawnScore += (blackPawns - 1) * 0.5;
        }
        
        return pawnScore;
    }
    
    isEndgame(board) {
        let pieceCount = 0;
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                if (board[x][y] && board[x][y].type !== 'king' && board[x][y].type !== 'pawn') {
                    pieceCount++;
                }
            }
        }
        return pieceCount <= 6; // Endgame when few pieces remain
    }

    
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
    
    /**
     * Extract king positions from the current board state
     */
    extractKingPositions(board) {
        const kingPositions = { white: null, black: null };
        
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = board[x][y];
                if (piece && piece.type === 'king') {
                    kingPositions[piece.color] = { x, y };
                }
            }
        }
        
        return kingPositions;
    }
}
