/**
 * A simplified item drop manager that only handles soul orbs.
 * This is a temporary implementation until the full item system is implemented.
 */

import * as THREE from 'three';
import { getPrefabFunction } from '../prefabs/prefabs.js';

class SimpleSoulDropManager {
    constructor() {
        // List of active soul orbs in the world
        this.activeSoulOrbs = [];

        // Distance at which player can collect orbs
        this.collectibleDistance = 1.5;

        // Debug mode
        this.debugMode = true;

        // Audio manager reference
        this.audioManager = null;
    }

    /**
     * Initialize the manager with required dependencies
     * @param {AudioManager} audioManager - The audio manager instance
     */
    init(audioManager) {
        this.audioManager = audioManager;
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }

    /**
     * Get the chance of an enemy dropping a soul orb
     * @param {string} enemyType - The type of enemy
     * @param {string} roomType - The type of room
     * @returns {number} The drop chance (0-1)
     * @private
     */
    _getDropChance(enemyType, roomType) {
        // Base drop chances by enemy type
        let baseChance = 0;

        switch (enemyType) {
            case 'bat':
                baseChance = 0.6; // 60% chance for bats (increased from 30%)
                break;
            case 'skeleton_archer':
                baseChance = 0.7; // 70% chance for skeleton archers (increased from 40%)
                break;
            case 'zombie':
                baseChance = 0.65; // 65% chance for zombies (increased from 35%)
                break;
            case 'magma_golem':
                baseChance = 0.8; // 80% chance for magma golems (increased from 50%)
                break;
            case 'destructible':
                baseChance = 0.20; // 20% chance for destructible objects (vases, pillars, rubble)
                break;
            default:
                baseChance = 0.5; // 50% default chance (increased from 20%)
        }

        // Room type modifiers
        let roomModifier = 1.0;
        switch (roomType) {
            case 'elite':
                roomModifier = 1.2; // 20% more drops in elite rooms
                break;
            case 'boss':
                roomModifier = 1.5; // 50% more drops in boss rooms
                break;
            case 'secret':
                roomModifier = 1.5; // 50% more drops in secret rooms
                break;
            default:
                roomModifier = 1.0;
        }

        return Math.min(baseChance * roomModifier, 1.0); // Cap at 100%
    }

    /**
     * Roll for a soul orb drop from an enemy
     * @param {string} enemyType - The type of enemy
     * @param {string} roomType - The type of room
     * @param {THREE.Vector3} position - The position to spawn the orb
     * @param {object} scene - The scene to add the orb to
     * @returns {object|null} The created orb or null if no drop
     */
    rollForDrop(enemyType, roomType, position, scene) {
        // Check if we already have an orb at this position (prevent duplicate drops)
        const hasNearbyOrb = this.activeSoulOrbs.some(orb => {
            if (!orb.object) return false;
            const distance = position.distanceTo(orb.object.position);
            return distance < 0.5; // If there's an orb within 0.5 units, don't drop another
        });

        if (hasNearbyOrb) {
            if (this.debugMode) {
                console.log(`SimpleSoulDropManager: Skipping drop due to nearby orb`);
            }
            return null;
        }

        // Calculate drop chance
        const dropChance = this._getDropChance(enemyType, roomType);

        // Roll for drop
        if (Math.random() > dropChance) {
            if (this.debugMode) {
                console.log(`SimpleSoulDropManager: No drop from ${enemyType} (chance: ${(dropChance * 100).toFixed(1)}%)`);
            }
            return null; // No drop
        }

        // All enemies now drop exactly 1 soul
        const orbValue = 1;

        // Create and return the soul orb
        const orb = this.createSoulOrb(position, scene, orbValue);

        if (this.debugMode) {
            console.log(`SimpleSoulDropManager: Dropped soul orb (value: ${orbValue}) from ${enemyType}`);
        }

        return orb;
    }

    /**
     * Create a soul orb in the world
     * @param {THREE.Vector3} position - The position to spawn the orb
     * @param {object} scene - The scene to add the orb to
     * @param {number} value - The value of the orb
     * @returns {object|null} The created orb
     */
    createSoulOrb(position, scene, value = 1) {
        // Create a new position with a small offset to prevent clipping
        const spawnPos = position.clone();
        spawnPos.y += 0.5; // Lift off the ground

        // Add a small random offset in X and Z
        spawnPos.x += (Math.random() - 0.5) * 0.5;
        spawnPos.z += (Math.random() - 0.5) * 0.5;

        // Get the soul orb prefab function
        const prefabFunc = getPrefabFunction('soul_orb', 'interior');

        if (!prefabFunc) {
            console.error('SimpleSoulDropManager: Failed to get soul orb prefab function');
            return null;
        }

        // Create soul orb with random seed
        const seed = Math.random() * 10000;
        const orbObject = prefabFunc({
            position: spawnPos,
            value: value,
            seed: seed
        });

        if (!orbObject) {
            console.error('SimpleSoulDropManager: Failed to create soul orb');
            return null;
        }

        // Add to scene
        scene.add(orbObject);

        // Create orb data
        const orb = {
            type: 'soul_orb',
            object: orbObject,
            value: value,
            creationTime: Date.now()
        };

        // Add to active orbs
        this.activeSoulOrbs.push(orb);

        console.log(`SimpleSoulDropManager: Created soul orb (value: ${value}) at position ${spawnPos.x.toFixed(2)}, ${spawnPos.y.toFixed(2)}, ${spawnPos.z.toFixed(2)}`);

        return orb;
    }

    /**
     * Create a collection effect at the orb's position
     * @param {THREE.Vector3} position - Position to create the effect
     * @param {object} scene - The scene to add the effect to
     * @private
     */
    _createCollectionEffect(position, scene) {
        // Play collection sound
        if (this.audioManager) {
            this.audioManager.playSound('soul_collect', false, 0.5);
        }

        const particleCount = 12;
        const particles = [];
        const color = 0x00ffff; // Cyan color matching the orb

        // Create particles
        for (let i = 0; i < particleCount; i++) {
            const particleGeometry = new THREE.SphereGeometry(0.05, 4, 4);
            const particleMaterial = new THREE.MeshBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.8
            });
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);

            // Set initial position
            particle.position.copy(position);

            // Set random velocity in an upward cone shape
            const angle = (Math.PI * 2 * i) / particleCount;
            const speed = 2 + Math.random();
            particle.userData.velocity = new THREE.Vector3(
                Math.cos(angle) * speed * 0.5,
                speed,
                Math.sin(angle) * speed * 0.5
            );

            // Set lifetime
            particle.userData.lifetime = 0.5 + Math.random() * 0.3;
            particle.userData.age = 0;

            scene.add(particle);
            particles.push(particle);

            // Remove particles after lifetime
            setTimeout(() => {
                scene.remove(particle);
                const index = particles.indexOf(particle);
                if (index !== -1) {
                    particles.splice(index, 1);
                }
            }, particle.userData.lifetime * 1000);
        }

        // Add a flash of light
        const light = new THREE.PointLight(color, 2, 3);
        light.position.copy(position);
        scene.add(light);

        // Remove light after a short time
        setTimeout(() => {
            scene.remove(light);
        }, 300);

        // Update particle positions
        const updateParticles = () => {
            if (particles.length === 0) return;

            particles.forEach(particle => {
                // Update position based on velocity
                particle.position.add(particle.userData.velocity.clone().multiplyScalar(1/60));

                // Add some gravity
                particle.userData.velocity.y -= 5/60;

                // Update age and scale/opacity
                particle.userData.age += 1/60;
                const lifeRatio = particle.userData.age / particle.userData.lifetime;
                particle.scale.setScalar(1.0 - lifeRatio);
                particle.material.opacity = 0.8 * (1.0 - lifeRatio);
            });

            if (particles.length > 0) {
                requestAnimationFrame(updateParticles);
            }
        };

        // Start the animation
        updateParticles();
    }

    /**
     * Update all active soul orbs
     * @param {number} deltaTime - Time since last update in seconds
     * @param {THREE.Vector3} playerPosition - The player's current position
     * @param {function} onCollect - Callback when an orb is collected
     */
    update(deltaTime, playerPosition, onCollect) {
        const currentTime = Date.now() / 1000;
        const orbsToRemove = [];

        this.activeSoulOrbs.forEach((orb, index) => {
            const object = orb.object;

            if (!object) {
                // Object was removed from scene, mark for removal from array
                orbsToRemove.push(index);
                return;
            }

            // Apply floating animation
            const userData = object.userData;
            if (userData && userData.animation) {
                // Get animation parameters
                const baseHeight = userData.animation.baseHeight || 0.5;
                const bobHeight = userData.animation.bobHeight || 0.2;
                const bobSpeed = userData.animation.bobSpeed || 2.0;
                const rotationSpeed = userData.animation.rotationSpeed || 1.0;

                // Calculate bob offset
                const timeSinceCreation = (currentTime - userData.creationTime/1000);
                const bobOffset = Math.sin(timeSinceCreation * bobSpeed) * bobHeight;

                // Set position with base height and bob offset
                object.position.y = baseHeight + bobOffset;

                // Rotate
                object.rotation.y += deltaTime * rotationSpeed;

                // Pulse the light
                if (userData.light) {
                    const lightIntensity = 1.0 + Math.sin(timeSinceCreation * 2) * 0.3;
                    userData.light.intensity = lightIntensity;
                }
            }

            // Check for collection
            if (playerPosition && playerPosition.distanceTo(object.position) < this.collectibleDistance) {
                // Create collection effect
                this._createCollectionEffect(object.position.clone(), object.parent);

                // Orb collected
                if (onCollect) {
                    onCollect(orb);
                }

                // Remove from scene
                if (object.parent) {
                    object.parent.remove(object);
                }

                // Mark for removal from array
                orbsToRemove.push(index);

                console.log(`SimpleSoulDropManager: Player collected soul orb with value ${orb.value}`);
            }
        });

        // Remove collected orbs (in reverse order to avoid index issues)
        for (let i = orbsToRemove.length - 1; i >= 0; i--) {
            this.activeSoulOrbs.splice(orbsToRemove[i], 1);
        }
    }

    /**
     * Clear all active soul orbs
     * @param {object} scene - The scene to remove orbs from
     */
    clearOrbs(scene) {
        this.activeSoulOrbs.forEach(orb => {
            if (orb.object && orb.object.parent) {
                orb.object.parent.remove(orb.object);
            }
        });

        this.activeSoulOrbs = [];
        console.log('SimpleSoulDropManager: Cleared all soul orbs');
    }
}

// Create and export a singleton instance
const simpleSoulDropManager = new SimpleSoulDropManager();
export default simpleSoulDropManager;
