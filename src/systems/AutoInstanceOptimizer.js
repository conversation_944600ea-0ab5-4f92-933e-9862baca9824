import * as THREE from 'three';

/**
 * AutoInstanceOptimizer - Automatically converts existing scene geometry to instanced rendering
 * Drop-in solution that requires no changes to existing code
 */
export class AutoInstanceOptimizer {
    constructor() {
        this.enabled = true;
        this.debug = true; // Set to false to disable console logs
        this.stats = {
            originalDrawCalls: 0,
            optimizedDrawCalls: 0,
            meshesConverted: 0
        };
    }

    /**
     * Optimize an entire scene or group recursively
     * @param {THREE.Object3D} root - The root object to optimize
     * @param {Object} options - Optimization options
     * @returns {THREE.Object3D} The same object, optimized in-place
     */
    optimizeObject(root, options = {}) {
        const {
            preserveDynamic = true,  // Skip enemies, projectiles, etc.
            minInstanceCount = 5,    // Minimum identical meshes to trigger instancing
            maxInstanceSize = 10000  // Maximum instances per InstancedMesh
        } = options;

        // Collect all meshes grouped by geometry and material
        const meshGroups = new Map();
        const meshesToConvert = [];
        
        // First pass: collect meshes
        root.traverse(child => {
            if (child.isMesh && this.shouldOptimizeMesh(child, preserveDynamic)) {
                const key = this.getMeshKey(child);
                if (!meshGroups.has(key)) {
                    meshGroups.set(key, []);
                }
                meshGroups.get(key).push(child);
            }
        });

        // Second pass: convert groups to instanced meshes
        meshGroups.forEach((meshes, key) => {
            if (meshes.length >= minInstanceCount) {
                this.convertToInstanced(meshes, root, maxInstanceSize);
            }
        });

        return root;
    }

    /**
     * Check if a mesh should be optimized
     */
    shouldOptimizeMesh(mesh, preserveDynamic) {
        // Skip if already instanced
        if (mesh.isInstancedMesh) return false;
        
        // Skip if no geometry
        if (!mesh.geometry) return false;
        
        // Skip dynamic objects if requested
        if (preserveDynamic) {
            const userData = mesh.userData || {};
            const parentData = mesh.parent?.userData || {};
            
            // Skip interactive/dynamic objects
            if (userData.interactive || userData.animated || userData.dynamic ||
                userData.isEnemy || userData.isProjectile || userData.isItem ||
                parentData.objectType === 'enemy' || parentData.objectType === 'player' ||
                parentData.objectType === 'projectile' || parentData.isItem) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Generate a unique key for geometry + material combination
     */
    getMeshKey(mesh) {
        const geoId = mesh.geometry.uuid;
        const matId = Array.isArray(mesh.material) 
            ? mesh.material.map(m => m.uuid).join('_')
            : mesh.material.uuid;
        return `${geoId}_${matId}`;
    }

    /**
     * Convert a group of identical meshes to instanced rendering
     */
    convertToInstanced(meshes, root, maxInstanceSize) {
        if (meshes.length === 0) return;
        
        const firstMesh = meshes[0];
        const geometry = firstMesh.geometry;
        const material = firstMesh.material;
        
        // Create batches if exceeding max size
        const batches = [];
        for (let i = 0; i < meshes.length; i += maxInstanceSize) {
            batches.push(meshes.slice(i, i + maxInstanceSize));
        }

        batches.forEach(batch => {
            // Create instanced mesh
            const instancedMesh = new THREE.InstancedMesh(geometry, material, batch.length);
            instancedMesh.castShadow = firstMesh.castShadow;
            instancedMesh.receiveShadow = firstMesh.receiveShadow;
            
            // Copy transforms
            const matrix = new THREE.Matrix4();
            batch.forEach((mesh, index) => {
                mesh.updateMatrixWorld(true);
                matrix.copy(mesh.matrixWorld);
                instancedMesh.setMatrixAt(index, matrix);
                
                // Store reference for potential restoration
                mesh.userData._instancedMesh = instancedMesh;
                mesh.userData._instanceIndex = index;
            });
            
            instancedMesh.instanceMatrix.needsUpdate = true;
            
            // Add to the parent of the first mesh
            const parent = firstMesh.parent || root;
            parent.add(instancedMesh);
            
            // Remove original meshes
            batch.forEach(mesh => {
                if (mesh.parent) {
                    mesh.parent.remove(mesh);
                }
            });
            
            this.stats.meshesConverted += batch.length;
        });
    }

    /**
     * Optimize room geometry specifically
     * This is the main entry point for room optimization
     */
    optimizeRoom(roomGroup) {
        if (!this.enabled) return roomGroup;
        
        // Reset stats
        this.stats.originalDrawCalls = this.countDrawCalls(roomGroup);
        this.stats.meshesConverted = 0;
        
        // Optimize with room-specific settings
        this.optimizeObject(roomGroup, {
            preserveDynamic: true,
            minInstanceCount: 3,  // Lower threshold for rooms
            maxInstanceSize: 5000
        });
        
        this.stats.optimizedDrawCalls = this.countDrawCalls(roomGroup);
        
        // Log performance improvement
        if (this.debug) {
            const reduction = this.stats.originalDrawCalls - this.stats.optimizedDrawCalls;
            const percentage = (reduction / this.stats.originalDrawCalls * 100).toFixed(1);
            console.log(`[AutoInstanceOptimizer] Room optimized: ${this.stats.originalDrawCalls} → ${this.stats.optimizedDrawCalls} draw calls (${percentage}% reduction)`);
            console.log(`[AutoInstanceOptimizer] Converted ${this.stats.meshesConverted} meshes to instanced rendering`);
        }
        
        return roomGroup;
    }

    /**
     * Count draw calls in an object
     */
    countDrawCalls(object) {
        let count = 0;
        object.traverse(child => {
            if (child.isMesh || child.isInstancedMesh) {
                count++;
            }
        });
        return count;
    }

    /**
     * Restore original meshes (for debugging or dynamic needs)
     */
    restoreOriginalMeshes(instancedMesh, meshesData) {
        // Implementation for reverting instanced meshes back to individual meshes
        // Useful for objects that need to become dynamic
    }
}

// Singleton instance for easy access
export const autoInstanceOptimizer = new AutoInstanceOptimizer();

// Global toggle for easy testing
window.toggleInstanceOptimization = function(enabled) {
    autoInstanceOptimizer.enabled = enabled;
    console.log(`[AutoInstanceOptimizer] ${enabled ? 'Enabled' : 'Disabled'}`);
    return enabled;
};

// Global debug toggle
window.toggleInstanceDebug = function(debug) {
    autoInstanceOptimizer.debug = debug;
    console.log(`[AutoInstanceOptimizer] Debug ${debug ? 'Enabled' : 'Disabled'}`);
    return debug;
};