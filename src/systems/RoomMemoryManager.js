import * as THREE from 'three';

/**
 * RoomMemoryManager - <PERSON>perly disposes of room geometry while preserving game state
 */
export class RoomMemoryManager {
    constructor() {
        this.disposedRooms = new Set();
        this.preservedObjects = new WeakMap(); // Track objects that should be preserved
    }

    /**
     * Clean up a room's geometry while preserving game state
     * @param {THREE.Group} roomGroup - The room group to clean
     * @param {boolean} preserveState - Whether to preserve debris, items, etc.
     */
    cleanupRoomMemory(roomGroup, preserveState = true) {
        if (!roomGroup) return;
        
        const stats = {
            geometriesDisposed: 0,
            materialsDisposed: 0,
            texturesDisposed: 0,
            preserved: 0
        };
        
        // Traverse and clean up
        roomGroup.traverse(child => {
            // Skip objects that need to be preserved
            if (preserveState && this.shouldPreserveObject(child)) {
                stats.preserved++;
                return;
            }
            
            // Dispose of geometry
            if (child.geometry && !child.isInstancedMesh) {
                child.geometry.dispose();
                stats.geometriesDisposed++;
            }
            
            // Dispose of materials
            if (child.material) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                materials.forEach(material => {
                    // Dispose textures
                    if (material.map) {
                        material.map.dispose();
                        stats.texturesDisposed++;
                    }
                    if (material.normalMap) {
                        material.normalMap.dispose();
                        stats.texturesDisposed++;
                    }
                    if (material.roughnessMap) {
                        material.roughnessMap.dispose();
                        stats.texturesDisposed++;
                    }
                    // Dispose material
                    material.dispose();
                    stats.materialsDisposed++;
                });
            }
        });
        
        console.log(`[RoomMemoryManager] Cleaned room - Disposed: ${stats.geometriesDisposed} geometries, ${stats.materialsDisposed} materials, ${stats.texturesDisposed} textures. Preserved: ${stats.preserved} objects`);
        
        return stats;
    }
    
    /**
     * Check if an object should be preserved (debris, items, etc)
     */
    shouldPreserveObject(object) {
        if (!object.userData) return false;
        
        // Preserve debris pieces
        if (object.userData.isDebris || object.userData.isBonePiece) {
            return true;
        }
        
        // Preserve items
        if (object.userData.isItem || object.userData.itemType) {
            return true;
        }
        
        // Preserve soul orbs
        if (object.userData.isSoulOrb) {
            return true;
        }
        
        // Preserve chest states
        if (object.userData.objectType === 'treasure_chest') {
            return true;
        }
        
        // Preserve any object marked as persistent
        if (object.userData.persistent || object.userData.doNotDispose) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Mark objects for preservation before cleanup
     */
    markForPreservation(object) {
        this.preservedObjects.set(object, true);
    }
    
    /**
     * Get memory usage estimate for a room
     */
    estimateRoomMemory(roomGroup) {
        let geometryMemory = 0;
        let textureMemory = 0;
        
        roomGroup.traverse(child => {
            if (child.geometry) {
                // Rough estimate: 32 bytes per vertex (position + normal + uv)
                const vertexCount = child.geometry.attributes.position?.count || 0;
                geometryMemory += vertexCount * 32;
            }
            
            if (child.material) {
                const materials = Array.isArray(child.material) ? child.material : [child.material];
                materials.forEach(material => {
                    if (material.map && material.map.image) {
                        // Texture memory: width * height * 4 bytes (RGBA)
                        textureMemory += material.map.image.width * material.map.image.height * 4;
                    }
                });
            }
        });
        
        const totalMB = (geometryMemory + textureMemory) / (1024 * 1024);
        return {
            geometryMB: geometryMemory / (1024 * 1024),
            textureMB: textureMemory / (1024 * 1024),
            totalMB: totalMB
        };
    }
}

// Singleton instance
export const roomMemoryManager = new RoomMemoryManager();

// Global debug commands
window.checkRoomMemory = function() {
    const renderer = window.sceneManager?.renderer;
    if (renderer) {
        const info = renderer.info;
        console.log('=== MEMORY STATS ===');
        console.log(`Geometries: ${info.memory.geometries}`);
        console.log(`Textures: ${info.memory.textures}`);
        console.log(`Render calls: ${info.render.calls}`);
        console.log(`Triangles: ${info.render.triangles}`);
        console.log(`Points: ${info.render.points}`);
        console.log(`Lines: ${info.render.lines}`);
    }
    
    // Check current room memory
    const currentRoom = window.dungeonHandler?.currentRoomGroup;
    if (currentRoom) {
        const memStats = roomMemoryManager.estimateRoomMemory(currentRoom);
        console.log(`Current Room Memory: ${memStats.totalMB.toFixed(1)} MB`);
    }
    
    // Check preloaded rooms
    const preloaded = window.dungeonHandler?.preLoadedRooms;
    if (preloaded) {
        console.log(`Preloaded Rooms: ${preloaded.size}`);
        let totalPreloadMB = 0;
        preloaded.forEach((data, roomId) => {
            const memStats = roomMemoryManager.estimateRoomMemory(data.roomGroup);
            totalPreloadMB += memStats.totalMB;
            console.log(`  Room ${roomId}: ${memStats.totalMB.toFixed(1)} MB`);
        });
        console.log(`Total Preloaded Memory: ${totalPreloadMB.toFixed(1)} MB`);
    }
};