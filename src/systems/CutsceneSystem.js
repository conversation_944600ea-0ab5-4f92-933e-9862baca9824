import * as THREE from 'three';

/**
 * Cutscene System
 * 
 * A modular system for playing cutscenes in dungeon rooms.
 * Supports:
 * - First-person and third-person camera modes
 * - Movement along paths with local room coordinates
 * - Camera rotation in first-person mode
 * - Control locking during cutscenes
 * - Smooth transitions and movement
 */
export class CutsceneSystem {
    constructor(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
        this.scene = dungeonHandler.scene;
        this.camera = dungeonHandler.camera;
        this.player = dungeonHandler.player;
        // Don't cache playerController here - it might not exist yet
        // We'll get it from dungeonHandler when needed
        
        // Cutscene state
        this.isPlaying = false;
        this.currentCutscene = null;
        this.originalCameraParent = null;
        this.originalCameraPosition = new THREE.Vector3();
        this.originalCameraRotation = new THREE.Euler();
        this.originalControlsEnabled = true;
        
        // Movement state
        this.movementPath = [];
        this.currentPathIndex = 0;
        this.movementSpeed = 3.0; // Units per second
        this.rotationSpeed = 2.0; // Radians per second
        
        // Camera state for first-person
        this.cameraRotationX = 0;
        this.cameraRotationY = 0;
        
        // Callbacks
        this.onComplete = null;
        
        console.log('[CutsceneSystem] Initialized');
    }
    
    /**
     * Play a cutscene with the given configuration
     * @param {Object} config - Cutscene configuration
     * @param {string} config.mode - 'first-person' or 'third-person'
     * @param {Array} config.path - Array of positions [{x, y, z}, ...] in local room coordinates
     * @param {Array} [config.cameraRotations] - Array of camera rotations for first-person [{x, y}, ...]
     * @param {number} [config.speed] - Movement speed (units per second)
     * @param {Function} [config.onComplete] - Callback when cutscene finishes
     * @param {boolean} [config.smoothPath] - Whether to smooth the path with curves
     */
    async play(config) {
        if (this.isPlaying) {
            console.warn('[CutsceneSystem] Already playing a cutscene');
            return;
        }
        
        console.log('[CutsceneSystem] Starting cutscene:', config);
        
        // Validate config
        if (!config.mode || !config.path || config.path.length === 0) {
            console.error('[CutsceneSystem] Invalid cutscene config');
            return;
        }
        
        // Store cutscene config
        this.currentCutscene = config;
        this.isPlaying = true;
        this.currentPathIndex = 0;
        this.onComplete = config.onComplete || null;
        
        // Set movement speed
        this.movementSpeed = config.speed || 3.0;
        
        // Set rotation speed (for camera rotations)
        this.rotationSpeed = config.rotationSpeed || 2.0;
        
        // Convert local coordinates to world coordinates
        this.movementPath = this._convertPathToWorldCoordinates(config.path);
        
        // Store camera rotations if provided
        this.cameraRotations = config.cameraRotations || [];
        
        // Store camera modes for each waypoint (allows switching during cutscene)
        this.cameraModes = config.cameraModes || [];
        
        // If cameraModes array is provided, use the first mode, otherwise use config.mode
        const initialMode = (this.cameraModes.length > 0) ? this.cameraModes[0] : config.mode;
        this.currentMode = initialMode;
        
        // Lock player controls
        this._lockPlayerControls();
        
        // Setup camera based on initial mode
        if (this.currentMode === 'third-person') {
            this._setupThirdPersonCamera();
        } else if (this.currentMode === 'first-person') {
            this._setupFirstPersonCamera();
        }
        
        console.log('[CutsceneSystem] Cutscene started in', this.currentMode, 'mode');
    }
    
    /**
     * Stop the current cutscene and restore normal gameplay
     */
    stop() {
        if (!this.isPlaying) return;
        
        console.log('[CutsceneSystem] Stopping cutscene');
        
        // Restore camera
        this._restoreCamera();
        
        // Unlock player controls
        this._unlockPlayerControls();
        
        // Reset state
        this.isPlaying = false;
        this.currentCutscene = null;
        this.movementPath = [];
        this.currentPathIndex = 0;
        
        // Call completion callback if exists
        if (this.onComplete) {
            this.onComplete();
            this.onComplete = null;
        }
        
        console.log('[CutsceneSystem] Cutscene stopped');
    }
    
    /**
     * Update the cutscene system (called every frame)
     * @param {number} deltaTime - Time since last frame
     */
    update(deltaTime) {
        if (!this.isPlaying || !this.currentCutscene) return;
        
        // Update movement along path
        this._updateMovement(deltaTime);
        
        // Update camera based on current mode
        if (this.currentMode === 'third-person') {
            this._updateThirdPersonCamera();
        } else if (this.currentMode === 'first-person') {
            this._updateFirstPersonCamera(deltaTime);
        }
    }
    
    /**
     * Convert local room coordinates to world coordinates
     * @private
     */
    _convertPathToWorldCoordinates(localPath) {
        const worldPath = [];
        
        for (const point of localPath) {
            // Use the LocalCoordinateSystem if available
            if (this.dungeonHandler.localCoordinateSystem) {
                const worldPos = this.dungeonHandler.localCoordinateSystem.localToWorld(
                    new THREE.Vector3(point.x, point.y, point.z)
                );
                worldPath.push(worldPos);
            } else {
                // Fallback: just use the coordinates as-is
                worldPath.push(new THREE.Vector3(point.x, point.y, point.z));
            }
        }
        
        return worldPath;
    }
    
    /**
     * Lock player controls during cutscene
     * @private
     */
    _lockPlayerControls() {
        const playerController = this.dungeonHandler?.playerController;
        
        console.log('[CutsceneSystem] Attempting to lock player controls...');
        console.log('[CutsceneSystem] playerController exists:', !!playerController);
        console.log('[CutsceneSystem] dungeonHandler exists:', !!this.dungeonHandler);
        
        // Store original control state
        this.originalControlsEnabled = playerController ? playerController.inputEnabled : true;
        
        // Disable player controls
        if (playerController) {
            console.log('[CutsceneSystem] Current inputEnabled state:', playerController.inputEnabled);
            playerController.inputEnabled = false;
            
            // Also disable movement specifically (belt and suspenders approach)
            if (playerController.disableMovement) {
                playerController.disableMovement();
            }
            
            // Clear all key states to prevent stuck keys
            if (playerController.keys) {
                Object.keys(playerController.keys).forEach(key => {
                    playerController.keys[key] = false;
                });
            }
            
            console.log('[CutsceneSystem] Player controls locked successfully');
        } else {
            console.error('[CutsceneSystem] WARNING: No playerController found!');
        }
        
        // Also disable mobile controls if they exist
        if (this.dungeonHandler && this.dungeonHandler.sceneManager) {
            this.dungeonHandler.sceneManager.cutsceneMode = true;
            console.log('[CutsceneSystem] Mobile controls disabled');
        }
    }
    
    /**
     * Unlock player controls after cutscene
     * @private
     */
    _unlockPlayerControls() {
        const playerController = this.dungeonHandler?.playerController;
        
        // Restore original control state
        if (playerController) {
            playerController.inputEnabled = this.originalControlsEnabled;
            
            // Re-enable movement
            if (playerController.enableMovement) {
                playerController.enableMovement();
            }
            
            console.log('[CutsceneSystem] Player controls unlocked');
        }
        
        // Re-enable mobile controls
        if (this.dungeonHandler && this.dungeonHandler.sceneManager) {
            this.dungeonHandler.sceneManager.cutsceneMode = false;
        }
    }
    
    /**
     * Setup camera for third-person mode
     * @private
     */
    _setupThirdPersonCamera() {
        // Store original camera state
        this.originalCameraParent = this.camera.parent;
        this.originalCameraPosition.copy(this.camera.position);
        this.originalCameraRotation.copy(this.camera.rotation);
        
        // Remove camera from player (if attached)
        if (this.camera.parent === this.player) {
            this.player.remove(this.camera);
            this.scene.add(this.camera);
        }
        
        // Position camera for third-person view
        this._updateThirdPersonCamera();
    }
    
    /**
     * Setup camera for first-person mode
     * @private
     */
    _setupFirstPersonCamera() {
        // Store original camera state
        this.originalCameraParent = this.camera.parent;
        this.originalCameraPosition.copy(this.camera.position);
        this.originalCameraRotation.copy(this.camera.rotation);
        
        // Initialize camera rotation from player's current rotation
        this.cameraRotationX = 0;
        this.cameraRotationY = this.player.rotation.y;
        
        // Ensure camera is attached to player for first-person
        if (this.camera.parent !== this.player) {
            this.scene.remove(this.camera);
            this.player.add(this.camera);
        }
        
        // Position camera at player's eye level
        this.camera.position.set(0, 1.6, 0); // Eye height
        this.camera.rotation.set(0, 0, 0);
    }
    
    /**
     * Restore camera to original state
     * @private
     */
    _restoreCamera() {
        // Restore camera parent
        if (this.originalCameraParent === this.player) {
            if (this.camera.parent !== this.player) {
                this.scene.remove(this.camera);
                this.player.add(this.camera);
            }
        } else {
            if (this.camera.parent === this.player) {
                this.player.remove(this.camera);
                this.scene.add(this.camera);
            }
        }
        
        // Restore camera position and rotation
        this.camera.position.copy(this.originalCameraPosition);
        this.camera.rotation.copy(this.originalCameraRotation);
    }
    
    /**
     * Update movement along the path
     * @private
     */
    _updateMovement(deltaTime) {
        if (this.currentPathIndex >= this.movementPath.length) {
            // Reached end of path
            this.stop();
            return;
        }
        
        const targetPosition = this.movementPath[this.currentPathIndex];
        const currentPosition = this.player.position;
        
        // Calculate direction and distance
        const direction = new THREE.Vector3().subVectors(targetPosition, currentPosition);
        const distance = direction.length();
        
        if (distance < 0.1) {
            // Reached current waypoint, move to next
            this.currentPathIndex++;
            
            // Check if we need to switch camera mode at this waypoint
            if (this.cameraModes.length > 0 && this.currentPathIndex < this.cameraModes.length) {
                const newMode = this.cameraModes[this.currentPathIndex];
                if (newMode && newMode !== this.currentMode) {
                    console.log(`[CutsceneSystem] Switching camera mode from ${this.currentMode} to ${newMode}`);
                    this._switchCameraMode(newMode);
                }
            }
            
            return;
        }
        
        // Normalize direction
        direction.normalize();
        
        // Calculate movement
        const moveDistance = this.movementSpeed * deltaTime;
        const actualMoveDistance = Math.min(moveDistance, distance);
        
        // Move player
        this.player.position.add(direction.multiplyScalar(actualMoveDistance));
        
        // Rotate player to face movement direction (only in third-person mode)
        if (this.currentMode === 'third-person') {
            const horizontalDirection = new THREE.Vector3(direction.x, 0, direction.z).normalize();
            if (horizontalDirection.length() > 0.1) {
                const targetRotationY = Math.atan2(horizontalDirection.x, horizontalDirection.z);
                
                // Smooth rotation
                const rotationDiff = targetRotationY - this.player.rotation.y;
                const normalizedDiff = Math.atan2(Math.sin(rotationDiff), Math.cos(rotationDiff));
                this.player.rotation.y += normalizedDiff * this.rotationSpeed * deltaTime;
            }
        }
    }
    
    /**
     * Update third-person camera
     * @private
     */
    _updateThirdPersonCamera() {
        // Position camera behind and above player
        const cameraDistance = 10;
        const cameraHeight = 6;
        const cameraAngle = this.player.rotation.y;
        
        // Calculate camera position
        const cameraX = this.player.position.x - Math.sin(cameraAngle) * cameraDistance;
        const cameraY = this.player.position.y + cameraHeight;
        const cameraZ = this.player.position.z - Math.cos(cameraAngle) * cameraDistance;
        
        // Smooth camera movement
        this.camera.position.lerp(new THREE.Vector3(cameraX, cameraY, cameraZ), 0.1);
        
        // Look at player
        this.camera.lookAt(this.player.position.x, this.player.position.y + 1, this.player.position.z);
    }
    
    /**
     * Update first-person camera with rotation support
     * @private
     */
    _updateFirstPersonCamera(deltaTime) {
        // Check if we have camera rotations for this waypoint
        if (this.cameraRotations && this.currentPathIndex < this.cameraRotations.length) {
            const targetRotation = this.cameraRotations[this.currentPathIndex];
            
            if (targetRotation) {
                // Smoothly interpolate to target rotation
                const rotationSpeed = this.rotationSpeed * deltaTime;
                
                // Update Y rotation (left/right)
                if (targetRotation.y !== undefined) {
                    const yDiff = targetRotation.y - this.cameraRotationY;
                    const normalizedYDiff = Math.atan2(Math.sin(yDiff), Math.cos(yDiff));
                    this.cameraRotationY += normalizedYDiff * rotationSpeed;
                    this.player.rotation.y = this.cameraRotationY;
                }
                
                // Update X rotation (up/down)
                if (targetRotation.x !== undefined) {
                    const xDiff = targetRotation.x - this.cameraRotationX;
                    this.cameraRotationX += xDiff * rotationSpeed;
                    
                    // Clamp vertical rotation
                    this.cameraRotationX = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.cameraRotationX));
                    this.camera.rotation.x = this.cameraRotationX;
                }
            }
        }
    }
    
    /**
     * Switch camera mode during cutscene
     * @private
     */
    _switchCameraMode(newMode) {
        // Store current mode
        const oldMode = this.currentMode;
        this.currentMode = newMode;
        
        // Switch camera setup based on new mode
        if (newMode === 'third-person' && oldMode === 'first-person') {
            // Switching from first-person to third-person
            // Remove camera from player
            if (this.camera.parent === this.player) {
                this.player.remove(this.camera);
                this.scene.add(this.camera);
            }
            // Position camera for third-person view
            this._updateThirdPersonCamera();
        } else if (newMode === 'first-person' && oldMode === 'third-person') {
            // Switching from third-person to first-person
            // Attach camera to player
            if (this.camera.parent !== this.player) {
                this.scene.remove(this.camera);
                this.player.add(this.camera);
            }
            // Position camera at player's eye level
            this.camera.position.set(0, 1.6, 0);
            this.camera.rotation.set(0, 0, 0);
            
            // Initialize camera rotation from player's current rotation
            this.cameraRotationX = 0;
            this.cameraRotationY = this.player.rotation.y;
        }
    }
    
    /**
     * Helper method to create a cutscene configuration
     * @static
     */
    static createConfig(mode, path, options = {}) {
        return {
            mode: mode,
            path: path,
            speed: options.speed || 3.0,
            cameraRotations: options.cameraRotations || null,
            cameraModes: options.cameraModes || null,
            smoothPath: options.smoothPath || false,
            onComplete: options.onComplete || null
        };
    }
}