import * as THREE from 'three';

/**
 * Debug function to analyze room structure and find optimization opportunities
 */
export function debugRoomStructure(group) {
    const meshStats = new Map();
    let totalMeshes = 0;
    let totalGeometries = new Set();
    let totalMaterials = new Set();
    
    console.log("=== ROOM STRUCTURE ANALYSIS ===");
    
    group.traverse(child => {
        if (child.isMesh) {
            totalMeshes++;
            totalGeometries.add(child.geometry.uuid);
            totalMaterials.add(child.material.uuid);
            
            // Get geometry info
            const geoType = child.geometry.type;
            const vertexCount = child.geometry.attributes.position?.count || 0;
            const faceCount = child.geometry.index ? child.geometry.index.count / 3 : vertexCount / 3;
            
            // Create detailed key
            const key = `${geoType}_${vertexCount}verts_${Math.round(faceCount)}faces_${child.material.color?.getHexString() || 'nocolor'}`;
            
            if (!meshStats.has(key)) {
                meshStats.set(key, {
                    count: 0,
                    example: child,
                    geometry: child.geometry,
                    material: child.material,
                    names: []
                });
            }
            
            const stat = meshStats.get(key);
            stat.count++;
            stat.names.push(child.name || 'unnamed');
        }
    });
    
    console.log(`Total Meshes: ${totalMeshes}`);
    console.log(`Unique Geometries: ${totalGeometries.size}`);
    console.log(`Unique Materials: ${totalMaterials.size}`);
    console.log("\nMesh Groups:");
    
    // Sort by count to show most repeated meshes first
    const sortedStats = Array.from(meshStats.entries()).sort((a, b) => b[1].count - a[1].count);
    
    sortedStats.forEach(([key, stat]) => {
        console.log(`\n${key}:`);
        console.log(`  Count: ${stat.count}`);
        console.log(`  Geometry: ${stat.geometry.type}`);
        console.log(`  Material: ${stat.material.type} (${stat.material.color?.getHexString() || 'no color'})`);
        console.log(`  Names: ${stat.names.slice(0, 5).join(', ')}${stat.names.length > 5 ? '...' : ''}`);
        
        // Check if these are already merged
        if (stat.geometry.type === 'BufferGeometry' && stat.count === 1 && stat.example.geometry.attributes.position.count > 100) {
            console.log(`  ⚠️  This looks like already-merged geometry with ${stat.example.geometry.attributes.position.count} vertices`);
        }
    });
    
    console.log("\n=== OPTIMIZATION OPPORTUNITIES ===");
    
    // Find BoxGeometry meshes that could be instanced
    let boxMeshCount = 0;
    let boxGroups = new Map();
    
    group.traverse(child => {
        if (child.isMesh && child.geometry.type === 'BoxGeometry') {
            boxMeshCount++;
            const size = child.geometry.parameters.width;
            const color = child.material.color?.getHexString() || 'nocolor';
            const key = `${size}_${color}`;
            
            if (!boxGroups.has(key)) {
                boxGroups.set(key, []);
            }
            boxGroups.get(key).push(child);
        }
    });
    
    console.log(`\nFound ${boxMeshCount} BoxGeometry meshes`);
    boxGroups.forEach((meshes, key) => {
        if (meshes.length > 2) {
            console.log(`  ${key}: ${meshes.length} meshes - GOOD FOR INSTANCING`);
        }
    });
    
    return {
        totalMeshes,
        uniqueGeometries: totalGeometries.size,
        uniqueMaterials: totalMaterials.size,
        meshStats
    };
}