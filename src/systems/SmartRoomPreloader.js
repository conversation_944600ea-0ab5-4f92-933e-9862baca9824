// src/systems/SmartRoomPreloader.js
// Intelligent room preloading system for near-instantaneous room switching

/**
 * Smart Room Preloader - Preloads adjacent rooms for instant switching
 * Integrates with existing DungeonHandler and worker systems
 */
export class SmartRoomPreloader {
    constructor(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
        this.roomCache = new Map();
        this.preloadQueue = new Set();
        this.isPreloading = false;
        this.lastPreloadTime = 0;
        
        // Device-adaptive configuration
        this.config = this._detectDeviceCapabilities();
        
        // Performance metrics
        this.metrics = {
            cacheHits: 0,
            cacheMisses: 0,
            preloadedRooms: 0,
            averagePreloadTime: 0,
            memoryUsage: 0
        };
        
        // Player movement tracking for priority calculation
        this.playerMovementHistory = [];
        this.maxHistoryLength = 10;
        
        console.log('[SmartRoomPreloader] Initialized with config:', this.config);
    }
    
    /**
     * Detect device capabilities and set optimal configuration
     * @private
     */
    _detectDeviceCapabilities() {
        const isLowEnd = this._isLowEndDevice();
        
        return {
            maxCachedRooms: isLowEnd ? 4 : 8,
            preloadRadius: isLowEnd ? 1 : 1, // Start with 1 for both, can expand
            backgroundPreload: !isLowEnd,
            useWorkers: window.workerIntegration !== undefined,
            meshQuality: isLowEnd ? 'optimized' : 'full',
            preloadDelay: isLowEnd ? 500 : 200, // ms delay before preloading
            isLowEnd: isLowEnd
        };
    }
    
    /**
     * Detect if device is low-end based on hardware capabilities
     * @private
     */
    _isLowEndDevice() {
        // Check hardware concurrency (CPU cores)
        const cores = navigator.hardwareConcurrency || 2;
        
        // Check device memory if available
        const memory = navigator.deviceMemory || 2;
        
        // Check if worker integration detected low-end device
        const workerDetection = window.workerIntegration?.isLowEndDevice;
        
        return cores <= 2 || memory <= 2 || workerDetection === true;
    }
    
    /**
     * Main method to preload adjacent rooms
     * @param {number} currentRoomId - Current room ID
     * @param {string} entryDirection - Direction player entered from (for priority)
     */
    async preloadAdjacentRooms(currentRoomId, entryDirection = null) {
        if (this.isPreloading) {
            return; // Prevent concurrent preloading
        }
        
        try {
            this.isPreloading = true;
            const startTime = performance.now();
            
            // Get adjacent room IDs with priority
            const adjacentRooms = this._getAdjacentRoomsWithPriority(currentRoomId, entryDirection);
            
            console.log(`[SmartRoomPreloader] Preloading ${adjacentRooms.length} adjacent rooms for room ${currentRoomId}`);
            
            // Clean up cache if needed
            this._cleanupCache();
            
            // Preload rooms in priority order
            for (const roomInfo of adjacentRooms) {
                if (this.roomCache.size >= this.config.maxCachedRooms) {
                    break; // Cache full
                }
                
                await this._preloadRoom(roomInfo);
                
                // Yield control to prevent blocking
                if (this.config.backgroundPreload) {
                    await this._yieldToMain();
                }
            }
            
            const preloadTime = performance.now() - startTime;
            this._updateMetrics(preloadTime, adjacentRooms.length);
            
            console.log(`[SmartRoomPreloader] Preloading completed in ${preloadTime.toFixed(2)}ms`);
            
        } catch (error) {
            console.error('[SmartRoomPreloader] Error during preloading:', error);
        } finally {
            this.isPreloading = false;
        }
    }
    
    /**
     * Get adjacent rooms with priority scoring
     * @private
     */
    _getAdjacentRoomsWithPriority(currentRoomId, entryDirection) {
        const currentRoom = this.dungeonHandler.dungeonData?.rooms?.[currentRoomId];
        if (!currentRoom) {
            return [];
        }
        
        const adjacentRooms = [];
        const directions = ['north', 'south', 'east', 'west'];
        
        directions.forEach(direction => {
            const connection = currentRoom.connections?.[direction];
            if (connection && connection.roomId) {
                const roomInfo = {
                    roomId: connection.roomId,
                    direction: direction,
                    priority: this._calculateRoomPriority(direction, entryDirection, connection.roomId)
                };
                adjacentRooms.push(roomInfo);
            }
        });
        
        // Sort by priority (higher priority first)
        return adjacentRooms.sort((a, b) => b.priority - a.priority);
    }
    
    /**
     * Calculate room preload priority
     * @private
     */
    _calculateRoomPriority(direction, entryDirection, roomId) {
        let priority = 0;
        
        // Base priority: forward direction gets highest priority
        if (entryDirection) {
            const oppositeDirection = this._getOppositeDirection(entryDirection);
            if (direction === oppositeDirection) {
                priority += 10; // Forward direction
            } else if (direction === entryDirection) {
                priority += 2; // Backward direction (lowest)
            } else {
                priority += 6; // Side directions (medium)
            }
        } else {
            priority += 5; // No entry direction info, medium priority
        }
        
        // Bonus for unexplored rooms
        if (!this.dungeonHandler.visitedRooms?.has(roomId)) {
            priority += 3;
        }
        
        // Bonus for rooms in player movement pattern
        if (this._isInMovementPattern(direction)) {
            priority += 2;
        }
        
        // Penalty if already cached
        if (this.roomCache.has(roomId)) {
            priority -= 5;
        }
        
        return priority;
    }
    
    /**
     * Get opposite direction for priority calculation
     * @private
     */
    _getOppositeDirection(direction) {
        const opposites = {
            'north': 'south',
            'south': 'north',
            'east': 'west',
            'west': 'east'
        };
        return opposites[direction] || direction;
    }
    
    /**
     * Check if direction matches recent player movement pattern
     * @private
     */
    _isInMovementPattern(direction) {
        if (this.playerMovementHistory.length < 2) {
            return false;
        }
        
        const recentMoves = this.playerMovementHistory.slice(-3);
        return recentMoves.includes(direction);
    }
    
    /**
     * Preload a single room
     * @private
     */
    async _preloadRoom(roomInfo) {
        const { roomId, direction } = roomInfo;
        
        if (this.roomCache.has(roomId)) {
            return; // Already cached
        }
        
        try {
            console.log(`[SmartRoomPreloader] Preloading room ${roomId} (${direction})`);
            
            // Get room data
            const roomData = this.dungeonHandler.dungeonData?.rooms?.[roomId];
            if (!roomData) {
                console.warn(`[SmartRoomPreloader] Room data not found for room ${roomId}`);
                return;
            }
            
            // Generate room visuals using existing system
            const roomGroup = await this._generateRoomVisuals(roomData);
            
            // Cache the preloaded room
            this.roomCache.set(roomId, {
                roomGroup: roomGroup,
                roomData: roomData,
                timestamp: Date.now(),
                direction: direction,
                accessCount: 0
            });
            
            this.metrics.preloadedRooms++;
            
        } catch (error) {
            console.error(`[SmartRoomPreloader] Failed to preload room ${roomId}:`, error);
        }
    }
    
    /**
     * Generate room visuals using existing room generation system
     * @private
     */
    async _generateRoomVisuals(roomData) {
        // Use the existing room generation system
        const currentArea = this.dungeonHandler.currentArea;
        
        // Import the room generator function
        const { generateRoomVisuals } = await import('../scenes/roomGenerator.js');
        
        // Generate room visuals (this is already async in your system)
        const roomGroup = await generateRoomVisuals(roomData, currentArea);
        
        return roomGroup;
    }
    
    /**
     * Get cached room if available
     * @param {number} roomId - Room ID to retrieve
     * @returns {Object|null} Cached room data or null
     */
    getCachedRoom(roomId) {
        const cached = this.roomCache.get(roomId);
        if (cached) {
            cached.accessCount++;
            this.metrics.cacheHits++;
            console.log(`[SmartRoomPreloader] Cache HIT for room ${roomId}`);
            return cached;
        }
        
        this.metrics.cacheMisses++;
        console.log(`[SmartRoomPreloader] Cache MISS for room ${roomId}`);
        return null;
    }
    
    /**
     * Clean up old cached rooms to prevent memory bloat
     * @private
     */
    _cleanupCache() {
        if (this.roomCache.size <= this.config.maxCachedRooms) {
            return;
        }
        
        // Convert to array and sort by access count and age
        const cacheEntries = Array.from(this.roomCache.entries()).map(([roomId, data]) => ({
            roomId,
            data,
            score: data.accessCount - (Date.now() - data.timestamp) / 60000 // Favor recent and accessed
        }));
        
        // Sort by score (lower score = more likely to be removed)
        cacheEntries.sort((a, b) => a.score - b.score);
        
        // Remove oldest/least accessed rooms
        const toRemove = cacheEntries.slice(0, cacheEntries.length - this.config.maxCachedRooms + 1);
        
        toRemove.forEach(entry => {
            console.log(`[SmartRoomPreloader] Removing cached room ${entry.roomId} from cache`);
            
            // Dispose of Three.js objects to free memory
            if (entry.data.roomGroup) {
                this._disposeRoomGroup(entry.data.roomGroup);
            }
            
            this.roomCache.delete(entry.roomId);
        });
    }
    
    /**
     * Dispose of Three.js objects to prevent memory leaks
     * @private
     */
    _disposeRoomGroup(roomGroup) {
        if (!roomGroup) return;
        
        roomGroup.traverse((child) => {
            if (child.geometry) {
                child.geometry.dispose();
            }
            if (child.material) {
                if (Array.isArray(child.material)) {
                    child.material.forEach(material => material.dispose());
                } else {
                    child.material.dispose();
                }
            }
        });
    }
    
    /**
     * Track player movement for priority calculation
     * @param {string} direction - Direction player moved
     */
    trackPlayerMovement(direction) {
        this.playerMovementHistory.push(direction);
        if (this.playerMovementHistory.length > this.maxHistoryLength) {
            this.playerMovementHistory.shift();
        }
    }
    
    /**
     * Yield control to main thread
     * @private
     */
    async _yieldToMain() {
        return new Promise(resolve => {
            if (this.config.backgroundPreload && window.requestIdleCallback) {
                requestIdleCallback(resolve, { timeout: 50 });
            } else {
                setTimeout(resolve, 0);
            }
        });
    }
    
    /**
     * Update performance metrics
     * @private
     */
    _updateMetrics(preloadTime, roomCount) {
        this.metrics.averagePreloadTime = 
            (this.metrics.averagePreloadTime + preloadTime) / 2;
        
        // Estimate memory usage
        this.metrics.memoryUsage = this.roomCache.size * 1024 * 1024; // Rough estimate
        
        this.lastPreloadTime = preloadTime;
    }
    
    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            cacheSize: this.roomCache.size,
            maxCacheSize: this.config.maxCacheSize,
            hitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) * 100,
            lastPreloadTime: this.lastPreloadTime,
            config: this.config
        };
    }
    
    /**
     * Clear all cached rooms (for cleanup)
     */
    clearCache() {
        console.log('[SmartRoomPreloader] Clearing all cached rooms');
        
        this.roomCache.forEach((data, roomId) => {
            if (data.roomGroup) {
                this._disposeRoomGroup(data.roomGroup);
            }
        });
        
        this.roomCache.clear();
        this.metrics.memoryUsage = 0;
    }
    
    /**
     * Get cache status for debugging
     * @returns {Object} Cache status information
     */
    getCacheStatus() {
        const cachedRooms = Array.from(this.roomCache.keys());
        return {
            cachedRooms,
            cacheSize: this.roomCache.size,
            maxSize: this.config.maxCachedRooms,
            isPreloading: this.isPreloading,
            metrics: this.getMetrics()
        };
    }
}
