import * as THREE from 'three';
import { VOXEL_SIZE, materialCacheByHex } from '../generators/prefabs/shared.js';
import { 
    createWhiteChessPawn, createBlackChessPawn,
    createWhiteChessRook, createBlackChessRook,
    createWhiteChessKnight, createBlackChessKnight,
    createWhiteChessBishop, createBlackChessBishop,
    createWhiteChessQueen, createBlackChessQueen,
    createWhiteChessKing, createBlackChessKing,
    createHellishTorchObject
} from '../generators/prefabs/index.js';
import { ChessLogic } from '../utils/ChessLogic.js';

/**
 * Chess3DMinigame - Full 3D chess experience as overlay minigame
 * Creates a complete 3D scene with Devil's room aesthetics, first-person seated camera,
 * and full 3D voxel chess pieces with destruction effects
 */
export class Chess3DMinigame {
    constructor(sceneManager, chessGame) {
        this.sceneManager = sceneManager;
        this.chessGame = chessGame;
        
        // 3D Scene setup
        this.minigameScene = null;
        this.minigameCamera = null;
        this.minigameRenderer = null;
        
        // Camera state management
        this.originalCameraState = null;
        this.isInMinigame = false;
        
        // Chess elements
        this.chessTable = null;
        this.chessPieces = new Map(); // position -> piece object
        this.chessBoard = null;
        this.roomEnvironment = null;
        
        // Interaction
        this.raycaster = new THREE.Raycaster();
        this.raycaster.near = 0.1;  // Very close to camera
        this.raycaster.far = 1000;  // Very far from camera
        this.raycaster.layers.enableAll(); // Enable all layers for raycasting
        this.mouse = new THREE.Vector2();
        this.selectedPiece = null;
        this.validMoveIndicators = [];
        this.hoverIndicator = null;
        this.hoveredSquare = null;
        
        // Click handling
        this.lastClickTime = 0;
        this.clickDebounceDelay = 100; // 100ms debounce - reduced for better responsiveness
        this.isProcessingMove = false;
        
        // Camera controls
        // Fresh camera orbit system
        this.orbitCamera = {
            // Mouse state
            isDragging: false,
            lastMouseX: 0,
            lastMouseY: 0,
            
            // Rotation angles (in radians)
            theta: Math.PI * 0.25,  // Start at 45 degrees horizontal angle
            phi: Math.PI * 0.35,    // Start at ~63 degrees from top (nice 3/4 view)
            
            // Camera properties
            radius: 15,      // Good viewing distance for chess board
            target: new THREE.Vector3(0, 0, 0), // Board center
            
            // Keyboard state
            keys: {
                w: false,
                s: false,
                a: false,
                d: false
            }
        };
        
        // Visual effects
        this.lighting = null;
        this.atmosphere = null;
        this.flames = [];
        
        // UI elements
        this.gameStatusUI = null;
        this.devilCommentaryUI = null;
        this.surrenderButton = null;
        this.customCursor = null;
        this.originalMinimapDisplay = undefined;
        this.playerKeyDownHandler = null;
        this.playerKeyUpHandler = null;
    }
    /**
     * Force an object and all its children to layer 0 recursively
     */
    forceLayer0Recursively(object) {
        // Set the main object to layer 0
        object.layers.set(0);
        object.layers.mask = 1;
        
        // Recursively apply to all children
        object.traverse((child) => {
            child.layers.set(0);
            child.layers.mask = 1;
            
            // Additional checks for various object types
            if (child.isMesh) {
                child.layers.set(0);
                child.layers.mask = 1;
                child.renderOrder = 0;
                child.frustumCulled = false; // Disable frustum culling
            }
            
            if (child.isGroup) {
                child.layers.set(0);
                child.layers.mask = 1;
            }
            
            // Fix geometry if it exists
            if (child.geometry) {
                // Force render for debugging
                child.visible = true;
            }
        });
    }
    
    /**
     * Ultra-aggressive layer 0 forcing with multiple techniques
     */
    forceLayer0RecursivelyAggressively(object) {
        // Method 1: Standard approach
        object.layers.set(0);
        object.layers.mask = 1;
        
        // Method 2: Direct property manipulation
        if (object.layers) {
            object.layers.mask = 1;
        }
        
        // Method 3: Force via traverse with multiple approaches
        object.traverse((child) => {
            // Approach A: Standard set
            child.layers.set(0);
            child.layers.mask = 1;
            
            // Approach B: Direct manipulation
            if (child.layers) {
                child.layers.mask = 1;
            }
            
            // Approach C: For meshes, extra steps
            if (child.isMesh) {
                child.layers.set(0);
                child.layers.mask = 1;
                child.renderOrder = 0;
                child.frustumCulled = false; // Disable frustum culling
                
                // Force the layer state again
                if (child.layers) {
                    child.layers.mask = 1;
                }
            }
            
            // Approach D: Force layer 0 via enable/disable
            if (child.layers) {
                child.layers.disableAll();
                child.layers.enable(0);
            }
        });
        
        // Method 4: Final verification and re-force if needed
        object.traverse((child) => {
            if (child.layers && child.layers.mask !== 1) {
                console.warn(`[Chess3DMinigame] Layer forcing failed for object, retrying...`);
                child.layers.disableAll();
                child.layers.enable(0);
                child.layers.mask = 1;
            }
        });
    }
    
    /**
     * Restore area name visibility after minigame
     */
    restoreAreaNameVisibility() {
        try {
            if (this.sceneManager && this.sceneManager.dungeonHandler) {
                const dungeonHandler = this.sceneManager.dungeonHandler;
                if (dungeonHandler.areaNameElement) {
                    // Restore area name visibility
                    dungeonHandler.areaNameElement.style.visibility = 'visible';
                    dungeonHandler.areaNameElement.style.opacity = '1';
                    console.log('[Chess3DMinigame] Area name visibility restored');
                } else {
                    console.log('[Chess3DMinigame] No area name element found to restore');
                }
            } else {
                console.log('[Chess3DMinigame] No dungeon handler available for area name restoration');
            }
        } catch (error) {
            console.error('[Chess3DMinigame] Error restoring area name visibility:', error);
        }
    }
    
    /**
     * Debug piece analysis - shows current position and all possible moves
     */
    debugPieceAnalysis(piece, boardX, boardZ, validMoves) {
        const algebraicPos = this.getAlgebraicNotation(boardX, boardZ);
        const pieceType = piece.userData.pieceType;
        const pieceColor = piece.userData.pieceColor;
        
        console.log(`\n🔍 ===== PIECE DEBUG ANALYSIS =====`);
        console.log(`📍 PIECE: ${pieceColor} ${pieceType}`);
        console.log(`📍 POSITION: ${algebraicPos} (board coords: ${boardX},${boardZ})`);
        console.log(`📍 3D POSITION: (${piece.position.x.toFixed(1)}, ${piece.position.y.toFixed(1)}, ${piece.position.z.toFixed(1)})`);
        
        // Show all moves this piece type should theoretically be able to make
        const boardPiece = this.chessGame.board[boardX][boardZ];
        if (boardPiece) {
            console.log(`📍 BOARD PIECE: ${boardPiece.color} ${boardPiece.type} hasMoved=${boardPiece.hasMoved}`);
            
            // Get raw moves without check validation
            const rawMoves = this.getRawPieceMoves(boardPiece, boardX, boardZ);
            console.log(`📍 RAW MOVES (${rawMoves.length}): ${rawMoves.map(m => this.getAlgebraicNotation(m.x, m.y)).join(', ')}`);
            
            // Show valid moves after check validation
            console.log(`📍 VALID MOVES (${validMoves.length}): ${validMoves.map(m => this.getAlgebraicNotation(m.x, m.y)).join(', ')}`);
            
            // Show which moves were filtered out
            const filteredOut = rawMoves.filter(raw => 
                !validMoves.some(valid => valid.x === raw.x && valid.y === raw.y)
            );
            if (filteredOut.length > 0) {
                console.log(`⚠️ FILTERED OUT (${filteredOut.length}): ${filteredOut.map(m => this.getAlgebraicNotation(m.x, m.y)).join(', ')}`);
                console.log(`⚠️ REASON: These moves would put king in check or are blocked`);
            }
            
            // Special analysis for specific piece types
            this.debugPieceSpecificRules(boardPiece, boardX, boardZ);
        }
        
        console.log(`===== END PIECE DEBUG =====\n`);
    }
    
    /**
     * Get raw piece moves without check validation
     */
    getRawPieceMoves(piece, x, y) {
        // Get moves using ChessLogic but without check validation
        switch (piece.type) {
            case 'pawn':
                return ChessLogic.getPawnMoves(this.chessGame.board, x, y, piece.color, this.chessGame.enPassantTarget);
            case 'rook':
                return ChessLogic.getRookMoves(this.chessGame.board, x, y, piece.color);
            case 'knight':
                return ChessLogic.getKnightMoves(this.chessGame.board, x, y, piece.color);
            case 'bishop':
                return ChessLogic.getBishopMoves(this.chessGame.board, x, y, piece.color);
            case 'queen':
                return ChessLogic.getQueenMoves(this.chessGame.board, x, y, piece.color);
            case 'king':
                return ChessLogic.getBasicKingMoves(this.chessGame.board, x, y, piece.color);
            default:
                return [];
        }
    }
    
    /**
     * Debug specific piece type rules
     */
    debugPieceSpecificRules(piece, x, y) {
        const { type, color } = piece;
        
        switch (type) {
            case 'rook':
                console.log(`🏰 ROOK RULES: Should move along ranks (horizontal) and files (vertical)`);
                console.log(`🏰 FROM ${this.getAlgebraicNotation(x, y)}:`);
                console.log(`   - Along rank ${8-y}: a${8-y} to h${8-y}`);
                console.log(`   - Along file ${String.fromCharCode(97+x)}: ${String.fromCharCode(97+x)}1 to ${String.fromCharCode(97+x)}8`);
                break;
                
            case 'bishop':
                console.log(`⛪ BISHOP RULES: Should move diagonally`);
                break;
                
            case 'queen':
                console.log(`👑 QUEEN RULES: Should move like rook + bishop combined`);
                break;
                
            case 'knight':
                console.log(`🐎 KNIGHT RULES: Should move in L-shape (2+1 squares)`);
                break;
                
            case 'king':
                console.log(`👑 KING RULES: Should move one square in any direction`);
                if (!piece.hasMoved) {
                    console.log(`   - Can castle (hasn't moved yet)`);
                }
                break;
                
            case 'pawn':
                const direction = color === 'white' ? 'up (decreasing y)' : 'down (increasing y)';
                console.log(`♟️ PAWN RULES: Should move ${direction}`);
                if (!piece.hasMoved) {
                    console.log(`   - Can move 2 squares (first move)`);
                }
                console.log(`   - Can capture diagonally`);
                break;
        }
    }
    
    /**
     * Explain to player why they can't move a piece
     */
    async explainWhyNoValidMoves(piece, boardX, boardZ) {
        const { pieceType, pieceColor } = piece.userData;
        const boardPiece = this.chessGame.board[boardX][boardZ];
        
        // Check if king is in check
        const isInCheck = ChessLogic.isInCheck(this.chessGame.board, pieceColor, this.chessGame.kingPositions);
        
        // Get raw moves to see what the piece would normally be able to do
        const rawMoves = this.getRawPieceMoves(boardPiece, boardX, boardZ);
        
        let playerThought = "";
        
        if (isInCheck) {
            // King is in check - must address the check
            if (pieceType === 'king') {
                playerThought = "\"My king is in check! I need to move him to safety...\"";
            } else {
                playerThought = `"Can't move my ${pieceType} - my king is in check! I need to block, capture the attacker, or move my king."`;
            }
        } else if (rawMoves.length === 0) {
            // Piece has no moves due to positioning
            switch (pieceType) {
                case 'pawn':
                    playerThought = "\"My pawn is blocked. Can't move forward, and no diagonal captures available.\"";
                    break;
                case 'rook':
                    playerThought = "\"My rook has no clear paths. All ranks and files are blocked.\"";
                    break;
                case 'bishop':
                    playerThought = "\"My bishop can't move - all diagonals are blocked.\"";
                    break;
                case 'knight':
                    playerThought = "\"My knight has no available L-shaped moves.\"";
                    break;
                case 'queen':
                    playerThought = "\"My queen is completely blocked in all directions.\"";
                    break;
                case 'king':
                    playerThought = "\"My king has nowhere safe to move.\"";
                    break;
                default:
                    playerThought = `"My ${pieceType} can't move anywhere right now."`;
            }
        } else {
            // Piece has raw moves but they would expose king to check
            switch (pieceType) {
                case 'pawn':
                    playerThought = "\"If I move this pawn forward, my king would be in danger...\"";
                    break;
                case 'rook':
                    playerThought = "\"Moving my rook would expose my king to attack!\"";
                    break;
                case 'bishop':
                    playerThought = "\"This bishop is pinned - moving it would put my king in check.\"";
                    break;
                case 'knight':
                    playerThought = "\"Can't move this knight - it would leave my king vulnerable.\"";
                    break;
                case 'queen':
                    playerThought = "\"My queen can't move without exposing my king!\"";
                    break;
                default:
                    playerThought = `"Moving this ${pieceType} would put my king in danger."`;
            }
        }
        
        // Show the player's internal thoughts using the Devil commentary system
        await this.showPlayerThoughts(playerThought);
    }
    
    /**
     * Show player's internal thoughts using the commentary system
     */
    async showPlayerThoughts(thought) {
        console.log(`[Chess3DMinigame] Player thinks: ${thought}`);
        
        // Use the existing showDevilCommentary method but style it differently for player thoughts
        const originalCommentary = this.devilCommentaryUI;
        
        // Create or update the commentary UI with player styling
        if (!this.devilCommentaryUI) {
            this.createCommentaryUI();
        }
        
        // Defer DOM operations to prevent interference with raycasting
        requestAnimationFrame(() => {
            // Temporarily change the styling to indicate it's player thoughts
            const commentaryElement = this.devilCommentaryUI;
            if (commentaryElement) {
                // Add player thought styling
                commentaryElement.style.backgroundColor = 'rgba(70, 130, 180, 0.95)'; // Steel blue for player
                commentaryElement.style.borderColor = '#4682B4';
                
                // Show the thought without any avatar
                commentaryElement.innerHTML = `
                    <div style="color: #E6F3FF; font-style: italic; text-align: center; padding: 10px;">
                        💭 ${thought}
                    </div>
                `;
                
                commentaryElement.style.display = 'block';
                
                // Hide after delay and restore original styling
                setTimeout(() => {
                    requestAnimationFrame(() => {
                        commentaryElement.style.display = 'none';
                        // Restore Devil styling
                        commentaryElement.style.backgroundColor = 'rgba(139, 0, 0, 0.95)';
                        commentaryElement.style.borderColor = '#8B0000';
                    });
                }, 3000);
            }
        });
    }
    
    /**
     * Start the 3D chess minigame
     */
    async startMinigame() {
        console.log('[Chess3DMinigame] Starting 3D chess minigame...');
        
        try {
            // Save current camera state
            this.saveCurrentCameraState();
            
            // Create the minigame scene
            await this.createMinigameScene();
            
            // Transition camera to seated position (scene will switch during animation)
            await this.transitionToSeatedView();
            
            // Disable player controls FIRST before setting up chess controls
            this.disablePlayerControls();
            
            // Set up interaction handlers
            this.setupInteractionHandlers();
            
            // Create UI overlay
            this.createChessUI();
            
            // Create custom red cursor for minigame
            this.createCustomCursor();
            
            // Hide minimap during minigame
            this.hideMinimapTemporarily();
            
            // Show initial devil commentary
            setTimeout(() => {
                this.showDevilCommentary("\"Let's see if you can handle a children's game, mortal.\"");
            }, 1000);
            
            this.isInMinigame = true;
            
            // IMPORTANT: Disable CRT camera restoration during chess game
            // The CRT effect stores and restores camera position which interferes with our camera controls
            if (this.sceneManager.crtEffect) {
                this.originalCRTSkipRestore = this.sceneManager.crtEffect._skipCameraRestore;
                this.sceneManager.crtEffect._skipCameraRestore = true;
                console.log('[Chess3DMinigame] Disabled CRT camera restoration to prevent interference');
            }
            
            console.log('[Chess3DMinigame] 3D chess minigame started successfully');
            
        } catch (error) {
            console.error('[Chess3DMinigame] Failed to start minigame:', error);
            await this.exitMinigame();
            throw error;
        }
    }
    
    /**
     * Exit the minigame and return to dungeon
     */
    async exitMinigame() {
        console.log('[Chess3DMinigame] Exiting 3D chess minigame...');
        
        try {
            // Clean up interaction handlers
            this.removeInteractionHandlers();
            
            // Remove custom cursor
            this.removeCustomCursor();
            
            // Restore minimap visibility
            this.restoreMinimapVisibility();
            
            // Re-enable player controls
            this.enablePlayerControls();
            
            // Transition camera back
            await this.transitionBackToDungeon();
            
            // Clean up minigame scene
            this.cleanupMinigameScene();
            
            // Clean up UI
            this.cleanupChessUI();
            
            // Restore area name visibility if if was hidden
            this.restoreAreaNameVisibility();
            
            // Restore CRT camera restoration setting
            if (this.sceneManager.crtEffect && this.originalCRTSkipRestore !== undefined) {
                this.sceneManager.crtEffect._skipCameraRestore = this.originalCRTSkipRestore;
                console.log('[Chess3DMinigame] Restored CRT camera restoration setting');
            }
            
            this.isInMinigame = false;
            console.log('[Chess3DMinigame] Returned to dungeon successfully');
            
        } catch (error) {
            console.error('[Chess3DMinigame] Error during minigame exit:', error);
        }
    }
    
    /**
     * Create the complete 3D minigame scene
     */
    async createMinigameScene() {
        // Create new scene for the minigame
        this.minigameScene = new THREE.Scene();
        this.minigameScene.background = new THREE.Color(0x0a0a0a); // Dark hellish background
        
        // Create minigame camera
        this.minigameCamera = new THREE.PerspectiveCamera(
            75, // fov
            window.innerWidth / window.innerHeight, // aspect ratio
            0.1, // near
            1000 // far
        );
        
        console.log('[Chess3DMinigame] Created minigame scene and camera');
        
        // Create Devil's room environment
        await this.createDevilsRoomEnvironment();
        
        // Create chess table
        await this.createChessTable();
        
        // Create chess board
        await this.createChessBoard();
        
        // Place chess pieces
        await this.placeChessPieces();
        
        // Set up lighting
        this.setupMinigameLighting();
        
        // Add atmospheric effects
        try {
            console.log('[Chess3DMinigame] About to create atmosphere...');
            console.log('[Chess3DMinigame] Testing createHellishTorchObject import:', typeof createHellishTorchObject);
            this.createAtmosphere();
            console.log('[Chess3DMinigame] Atmosphere creation completed');
        } catch (error) {
            console.error('[Chess3DMinigame] Error creating atmosphere:', error);
            console.error('[Chess3DMinigame] Error stack:', error.stack);
        }
        
        console.log('[Chess3DMinigame] Minigame scene created');
    }
    
    /**
     * Create Devil's room environment (walls, floor)
     */
    async createDevilsRoomEnvironment() {
        this.roomEnvironment = new THREE.Group();
        
        const roomSize = 30;
        const wallHeight = 15;
        
        // Floor - hellish stone floor
        const floorGeometry = new THREE.PlaneGeometry(roomSize, roomSize);
        const floorMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a0505,
            roughness: 0.8,
            metalness: 0.2
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = -2;
        this.roomEnvironment.add(floor);
        console.log('[Chess3DMinigame] Created hellish floor');
        
        // Walls - dark obsidian walls
        const wallMaterial = new THREE.MeshStandardMaterial({
            color: 0x2a1010,
            roughness: 0.7,
            metalness: 0.3
        });
        
        // Back wall
        const backWallGeometry = new THREE.PlaneGeometry(roomSize, wallHeight);
        const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        backWall.position.set(0, wallHeight/2 - 2, -roomSize/2);
        this.roomEnvironment.add(backWall);
        
        // Side walls
        const sideWallGeometry = new THREE.PlaneGeometry(roomSize, wallHeight);
        
        const leftWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
        leftWall.rotation.y = Math.PI / 2;
        leftWall.position.set(-roomSize/2, wallHeight/2 - 2, 0);
        this.roomEnvironment.add(leftWall);
        
        const rightWall = new THREE.Mesh(sideWallGeometry, wallMaterial);
        rightWall.rotation.y = -Math.PI / 2;
        rightWall.position.set(roomSize/2, wallHeight/2 - 2, 0);
        this.roomEnvironment.add(rightWall);
        
        this.minigameScene.add(this.roomEnvironment);
        console.log('[Chess3DMinigame] Devil\'s room environment created');
    }
    
    /**
     * Create the chess table
     */
    async createChessTable() {
        this.chessTable = new THREE.Group();
        
        // Table surface - 3x larger for bigger board
        const tableGeometry = new THREE.BoxGeometry(36, 0.5, 36);
        const tableMaterial = new THREE.MeshStandardMaterial({
            color: 0x2a1a1a,
            roughness: 0.3,
            metalness: 0.7
        });
        const table = new THREE.Mesh(tableGeometry, tableMaterial);
        table.position.y = -0.25;
        this.chessTable.add(table);
        
        // Table legs
        const legGeometry = new THREE.BoxGeometry(0.5, 3, 0.5);
        const legMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a0a0a,
            roughness: 0.5,
            metalness: 0.5
        });
        
        const legPositions = [
            [-15, -1.5, -15],
            [15, -1.5, -15],
            [-15, -1.5, 15],
            [15, -1.5, 15]
        ];
        
        legPositions.forEach(pos => {
            const leg = new THREE.Mesh(legGeometry, legMaterial);
            leg.position.set(...pos);
            this.chessTable.add(leg);
        });
        
        this.minigameScene.add(this.chessTable);
        console.log('[Chess3DMinigame] Chess table created');
    }
    
    /**
     * Create the 3D chess board
     */
    async createChessBoard() {
        this.chessBoard = new THREE.Group();
        
        const squareSize = VOXEL_SIZE * 24; // 3x larger board
        const boardSize = squareSize * 8;
        
        // Create board squares
        for (let x = 0; x < 8; x++) {
            for (let z = 0; z < 8; z++) {
                const isLight = (x + z) % 2 === 0;
                const color = isLight ? 0x4a3a3a : 0x2a1a1a;
                
                const squareGeometry = new THREE.BoxGeometry(squareSize, 0.2, squareSize);
                const squareMaterial = new THREE.MeshStandardMaterial({
                    color: color,
                    roughness: 0.3,
                    metalness: 0.7
                });
                
                const square = new THREE.Mesh(squareGeometry, squareMaterial);
                square.position.set(
                    (x - 3.5) * squareSize,
                    0.1,
                    (z - 3.5) * squareSize
                );
                
                square.name = `board_square_${x}_${z}`;
                square.userData = { 
                    boardX: x, 
                    boardZ: z, 
                    squareType: 'board_square',
                    algebraicNotation: this.getAlgebraicNotation(x, z)
                };
                this.chessBoard.add(square);
            }
        }
        
        // Add board border
        const borderGeometry = new THREE.BoxGeometry(boardSize + 1, 0.3, boardSize + 1);
        const borderMaterial = new THREE.MeshStandardMaterial({
            color: 0x660000,
            roughness: 0.1,
            metalness: 0.9,
            emissive: 0x220000,
            emissiveIntensity: 0.3
        });
        const border = new THREE.Mesh(borderGeometry, borderMaterial);
        border.position.y = -0.05;
        this.chessBoard.add(border);
        
        this.minigameScene.add(this.chessBoard);
        console.log('[Chess3DMinigame] Chess board created');
    }
    
    /**
     * Convert board coordinates to algebraic notation (e.g., "e4")
     */
    getAlgebraicNotation(x, z) {
        const files = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'];
        const ranks = ['8', '7', '6', '5', '4', '3', '2', '1']; // z=0 is rank 8, z=7 is rank 1
        return files[x] + ranks[z];
    }
    
    /**
     * Place all chess pieces on the board
     */
    async placeChessPieces() {
        const pieceCreators = {
            white: {
                pawn: createWhiteChessPawn,
                rook: createWhiteChessRook,
                knight: createWhiteChessKnight,
                bishop: createWhiteChessBishop,
                queen: createWhiteChessQueen,
                king: createWhiteChessKing
            },
            black: {
                pawn: createBlackChessPawn,
                rook: createBlackChessRook,
                knight: createBlackChessKnight,
                bishop: createBlackChessBishop,
                queen: createBlackChessQueen,
                king: createBlackChessKing
            }
        };
        
        const board = this.chessGame.board;
        const squareSize = VOXEL_SIZE * 24; // 3x larger board
        
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = board[x][y];
                if (piece) {
                    const creator = pieceCreators[piece.color][piece.type];
                    if (creator) {
                        const piece3D = creator({ scale: 7.2 }); // 3x larger pieces to match board
                        
                        // IMMEDIATE layer fix - override any default layer settings
                        this.forceLayer0Recursively(piece3D);
                        
                        // Disable frustum culling and fix additional properties
                        piece3D.traverse((child) => {
                            if (child.isMesh) {
                                child.frustumCulled = false; // Disable frustum culling
                                child.layers.set(0);
                                child.layers.mask = 1;
                            }
                        });
                        
                        // Position on board
                        piece3D.position.set(
                            (x - 3.5) * squareSize,
                            0.1, // Very low Y position so pieces sit properly on board
                            (y - 3.5) * squareSize
                        );
                        
                        // Add metadata with consistent Z coordinate naming
                        piece3D.name = `${piece.color}_${piece.type}_${this.getAlgebraicNotation(x, y)}`;
                        piece3D.userData = {
                            boardX: x,
                            boardZ: y, // Use boardZ for consistency with board squares
                            pieceType: piece.type,
                            pieceColor: piece.color,
                            isChessPiece: true,
                            algebraicNotation: this.getAlgebraicNotation(x, y),
                            pieceDescription: `${piece.color} ${piece.type} on ${this.getAlgebraicNotation(x, y)}`
                        };
                        
                        // FIX: Set piece and all children to layer 0 for raycasting - AGGRESSIVE APPROACH
                        piece3D.layers.set(0);
                        piece3D.layers.mask = 1; // Force mask to layer 0
                        piece3D.traverse((child) => {
                            child.layers.set(0);
                            child.layers.mask = 1; // Force mask to layer 0
                            if (child.material) {
                                child.renderOrder = 0;
                            }
                        });
                        
                        this.chessPieces.set(`${x},${y}`, piece3D);
                        this.minigameScene.add(piece3D);
                        
                        // FINAL post-add layer fix
                        piece3D.traverse((child) => {
                            child.layers.set(0);
                            child.layers.mask = 1;
                            if (child.isMesh) {
                                child.frustumCulled = false;
                            }
                        });
                    }
                }
            }
        }
        
        console.log(`[Chess3DMinigame] Placed ${this.chessPieces.size} chess pieces`);
        
        // FIX: Clear material cache to prevent sharing issues between pieces
        // This ensures each piece gets fresh material instances
        const cacheSize = Object.keys(materialCacheByHex).length;
        Object.keys(materialCacheByHex).forEach(key => {
            delete materialCacheByHex[key];
        });
        console.log(`[Chess3DMinigame] Cleared ${cacheSize} cached materials to prevent sharing issues`);
        
        // FIX: BRUTE FORCE LAYER OVERRIDE - Force layer 0 by direct manipulation
        let layerFixAttempts = 0;
        let successfulFixes = 0;
        
        // Try multiple approaches to force layer 0
        this.chessPieces.forEach((piece, position) => {
            // Attempt 1: Standard layer setting
            piece.layers.set(0);
            layerFixAttempts++;
            
            // Attempt 2: Direct layer manipulation on all children
            piece.traverse((child) => {
                if (child.layers) {
                    child.layers.set(0);
                    // Verify it worked
                    if (child.layers.mask === 1) {
                        successfulFixes++;
                    }
                    layerFixAttempts++;
                }
            });
            
            // Attempt 3: Recreate layers object if needed
            piece.traverse((child) => {
                if (child.layers && child.layers.mask !== 1) {
                    // Force create new layers on layer 0
                    child.layers = new THREE.Layers();
                    child.layers.set(0);
                    layerFixAttempts++;
                    if (child.layers.mask === 1) {
                        successfulFixes++;
                    }
                }
            });
        });
        
        // Also set raycaster to check all layers to ensure it works regardless
        this.raycaster.layers.enableAll();
        console.log(`[Chess3DMinigame] 🔧 Applied ${layerFixAttempts} layer fixes, ${successfulFixes} successful, raycaster set to check all layers`);
        
        // ENHANCED MATERIAL ISOLATION: Ensure each piece gets unique materials (TEMPORARILY DISABLED)
        // Clear the material cache completely to force fresh material creation
        console.log(`[Chess3DMinigame] 🔧 ENHANCED MATERIAL ISOLATION: TEMPORARILY DISABLED to debug layer issues`);
        
        // TEMPORARILY DISABLED: Piece recreation to isolate layer issue
        console.log(`[Chess3DMinigame] ⚠️ PIECE RECREATION DISABLED - keeping original pieces to debug layer issues`);
        /*
        // Clear both the shared materialCacheByHex and individual piece material caches
        Object.keys(materialCacheByHex).forEach(key => {
            delete materialCacheByHex[key];
        });
        
        // Now force each piece to rebuild with fresh materials by recreating with unique seeds
        const piecesToRecreate = [];
        this.chessPieces.forEach((piece, position) => {
            piecesToRecreate.push({
                position,
                pieceType: piece.userData.pieceType,
                pieceColor: piece.userData.pieceColor,
                boardX: piece.userData.boardX,
                boardZ: piece.userData.boardZ,
                algebraicNotation: piece.userData.algebraicNotation,
                worldPosition: piece.position.clone()
            });
        });
        
        // Clear all existing pieces
        this.chessPieces.forEach((piece) => {
            this.minigameScene.remove(piece);
        });
        this.chessPieces.clear();
        
        // Recreate all pieces with fresh materials and unique seeds
        
        let recreatedCount = 0;
        piecesToRecreate.forEach(({ position, pieceType, pieceColor, boardX, boardZ, algebraicNotation, worldPosition }) => {
            const creator = pieceCreators[pieceColor][pieceType];
            if (creator) {
                // Use position-based seed to ensure uniqueness while being deterministic
                const uniqueSeed = (boardX * 8 + boardZ) * 1000 + Date.now() % 1000;
                const freshPiece = creator({ 
                    scale: 7.2, 
                    seed: uniqueSeed
                });
                
                // IMMEDIATE layer fix - override any default layer settings
                this.forceLayer0Recursively(freshPiece);
                
                // Set position and metadata
                freshPiece.position.copy(worldPosition);
                freshPiece.name = `${pieceColor}_${pieceType}_${algebraicNotation}_fresh`;
                freshPiece.userData = {
                    boardX,
                    boardZ,
                    pieceType,
                    pieceColor,
                    isChessPiece: true,
                    algebraicNotation,
                    pieceDescription: `${pieceColor} ${pieceType} on ${algebraicNotation}`,
                    freshlyCreated: true
                };
                
                // Force layer 0 for raycasting - AGGRESSIVE APPROACH
                freshPiece.layers.set(0);
                freshPiece.layers.mask = 1; // Force mask to layer 0
                freshPiece.traverse((child) => {
                    child.layers.set(0);
                    child.layers.mask = 1; // Force mask to layer 0
                    // Also set layer property directly on materials if they exist
                    if (child.material) {
                        child.renderOrder = 0;
                    }
                });
                
                // Add to collection and scene
                this.chessPieces.set(position, freshPiece);
                this.minigameScene.add(freshPiece);
                recreatedCount++;
            }
        });
        
        console.log(`[Chess3DMinigame] ✅ ENHANCED MATERIAL ISOLATION: Recreated ${recreatedCount} pieces with fresh materials and unique seeds`);
        */
        
        // FINAL LAYER CORRECTION - Force all pieces to layer 0 (now working with original pieces)
        console.log(`[Chess3DMinigame] 🔧 FINAL LAYER CORRECTION: Forcing all pieces to layer 0 after recreation`);
        let finalLayerFixCount = 0;
        this.chessPieces.forEach((piece, position) => {
            this.forceLayer0RecursivelyAggressively(piece);
            finalLayerFixCount++;
        });
        console.log(`[Chess3DMinigame] 🔧 Applied final layer correction to ${finalLayerFixCount} pieces`);
        
        // Final verification with raycasting test
        let verificationResults = [];
        let raycastTestResults = [];
        
        this.chessPieces.forEach((piece, position) => {
            let childLayers = [];
            let totalVertices = 0;
            
            piece.traverse((child) => {
                if (child.isMesh && child.geometry) {
                    const vertexCount = child.geometry.attributes.position?.count || 0;
                    totalVertices += vertexCount;
                    childLayers.push(`${vertexCount}v/L${child.layers.mask}`);
                }
            });
            
            verificationResults.push(`${position}:[${childLayers.join(',')}]`);
            
            // Test raycasting on each piece
            const pieceWorldPosition = piece.position;
            this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.minigameCamera);
            this.raycaster.ray.origin.copy(pieceWorldPosition);
            this.raycaster.ray.origin.y += 10; // Cast from above
            this.raycaster.ray.direction.set(0, -1, 0); // Cast downward
            
            const intersects = this.raycaster.intersectObject(piece, true);
            raycastTestResults.push(`${position}:${intersects.length}hits`);
        });
        
        console.log(`[Chess3DMinigame] 🔍 Final geometry verification: ${verificationResults.slice(0, 8).join(' ')}`);
        console.log(`[Chess3DMinigame] 🎯 Raycast test results: ${raycastTestResults.slice(0, 8).join(' ')}`);
        console.log(`[Chess3DMinigame] ✅ All pieces prepared with forced layer fixes, geometry validation, and all-layer raycasting`);
        
        // Debug: Show all piece positions at game start
        console.log('[Chess3DMinigame] 📍 ALL PIECE POSITIONS AT GAME START:');
        const sortedPieces = Array.from(this.chessPieces.entries()).sort((a, b) => {
            const [keyA] = a;
            const [keyB] = b;
            return keyA.localeCompare(keyB);
        });
        
        sortedPieces.forEach(([key, piece]) => {
            const { boardX, boardZ, pieceType, pieceColor, algebraicNotation } = piece.userData;
            const pos = piece.position;
            console.log(`  ${key} -> ${pieceColor} ${pieceType} at ${algebraicNotation}: 3D pos (${pos.x.toFixed(1)}, ${pos.y.toFixed(1)}, ${pos.z.toFixed(1)}) visible=${piece.visible} children=${piece.children.length}`);
        });
        
        // Debug: Show board square positions for comparison
        console.log('[Chess3DMinigame] 📋 BOARD SQUARE REFERENCE POSITIONS:');
        for (let x = 0; x < 8; x++) {
            for (let z = 0; z < 8; z++) {
                const algebraic = this.getAlgebraicNotation(x, z);
                const expectedX = (x - 3.5) * squareSize;
                const expectedZ = (z - 3.5) * squareSize;
                console.log(`  ${algebraic} (${x},${z}): expected 3D pos (${expectedX.toFixed(1)}, 0.1, ${expectedZ.toFixed(1)})`);
            }
        }
    }
    
    
    /**
     * Set up lighting for the minigame
     */
    setupMinigameLighting() {
        this.lighting = new THREE.Group();
        
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x663333, 0.4);
        this.lighting.add(ambientLight);
        
        // Key light (from above, slightly angled)
        const keyLight = new THREE.DirectionalLight(0xffffff, 1.0);
        keyLight.position.set(5, 10, 5);
        keyLight.target.position.set(0, 0, 0);
        this.lighting.add(keyLight);
        this.lighting.add(keyLight.target);
        
        // Fill light (softer, from opposite side)
        const fillLight = new THREE.DirectionalLight(0xff6666, 0.6);
        fillLight.position.set(-5, 8, -5);
        fillLight.target.position.set(0, 0, 0);
        this.lighting.add(fillLight);
        this.lighting.add(fillLight.target);
        
        // Table spotlight
        const tableLight = new THREE.SpotLight(0xffaaaa, 1.5);
        tableLight.position.set(0, 8, 0);
        tableLight.target.position.set(0, 0, 0);
        tableLight.angle = Math.PI / 4;
        tableLight.penumbra = 0.3;
        this.lighting.add(tableLight);
        this.lighting.add(tableLight.target);
        
        this.minigameScene.add(this.lighting);
        console.log('[Chess3DMinigame] Lighting setup complete');
        console.log('[Chess3DMinigame] Scene children after lighting:', this.minigameScene.children.length);
    }
    
    /**
     * Create atmospheric effects with hellish voxel torches
     */
    createAtmosphere() {
        this.atmosphere = new THREE.Group();
        this.flames = []; // Store torch references for animation
        
        // Torch positions near table edges
        const torchPositions = [
            [-18, 0, -18],
            [18, 0, -18],
            [-18, 0, 18],
            [18, 0, 18]
        ];
        
        torchPositions.forEach((pos, index) => {
            // Create hellish voxel torch
            console.log(`[Chess3DMinigame] Creating torch ${index} at position:`, pos);
            const torch = createHellishTorchObject({
                seed: index + 1,
                userData: { torchIndex: index }
            });
            
            console.log(`[Chess3DMinigame] Torch ${index} created with children:`, torch.children.length);
            
            torch.position.set(...pos);
            torch.scale.set(4, 4, 4); // Make torches much larger for visibility
            
            // Add a bright marker to help debug positioning
            const marker = new THREE.Mesh(
                new THREE.SphereGeometry(2, 8, 8),
                new THREE.MeshStandardMaterial({ color: 0xff0000, emissive: 0xff0000, emissiveIntensity: 0.5 })
            );
            marker.position.set(0, 10, 0); // Above the torch
            torch.add(marker);
            
            this.flames.push(torch);
            this.atmosphere.add(torch);
            
            console.log(`[Chess3DMinigame] Torch ${index} positioned at:`, torch.position.x, torch.position.y, torch.position.z);
        });
        
        this.minigameScene.add(this.atmosphere);
        
        // Start torch flame animation
        this.startTorchAnimation();
        
        console.log('[Chess3DMinigame] Atmospheric effects with hellish voxel torches added');
        console.log('[Chess3DMinigame] Final scene children count:', this.minigameScene.children.length);
    }
    
    /**
     * Start torch animation loop for hellish torches and move indicators
     */
    startTorchAnimation() {
        const animateTorches = () => {
            if (!this.isInMinigame || !this.flames) return;
            
            const time = Date.now() * 0.001;
            
            // Animate torch flames
            this.flames.forEach(torch => {
                // Animate all flame voxels in the torch
                torch.traverse(child => {
                    if (child.userData && child.userData.isFlickering) {
                        const baseIntensity = child.userData.baseIntensity || 0.6;
                        
                        if (child.material && child.material.emissive) {
                            // Flicker emissive intensity
                            const flicker = Math.sin(time * 3 + child.position.y) * 0.2;
                            child.material.emissiveIntensity = Math.max(0.2, baseIntensity + flicker);
                            
                            // Slight opacity variation for flame layers
                            if (child.material.transparent) {
                                const opacityFlicker = Math.sin(time * 4 + child.position.x) * 0.1;
                                child.material.opacity = Math.max(0.5, child.material.opacity + opacityFlicker);
                            }
                        }
                        
                        // Animate torch light flickering
                        if (child instanceof THREE.PointLight) {
                            const lightFlicker = Math.sin(time * 2 + torch.position.x) * 0.3;
                            child.intensity = Math.max(1.0, child.userData.originalIntensity + lightFlicker);
                        }
                    }
                });
            });
            
            
            requestAnimationFrame(animateTorches);
        };
        
        animateTorches();
        console.log('[Chess3DMinigame] Hellish torch and move indicator animation started');
    }
    
    /**
     * Save current camera state for restoration
     */
    saveCurrentCameraState() {
        const camera = this.sceneManager.camera;
        this.originalCameraState = {
            position: camera.position.clone(),
            rotation: camera.rotation.clone(),
            fov: camera.fov,
            controls: this.sceneManager.controls ? {
                enabled: this.sceneManager.controls.enabled,
                target: this.sceneManager.controls.target.clone()
            } : null
        };
        console.log('[Chess3DMinigame] Camera state saved');
    }
    
    /**
     * Transition camera to top-down view looking straight down at chess board
     */
    async transitionToSeatedView() {
        return new Promise((resolve) => {
            const camera = this.sceneManager.camera;
            
            // Disable controls during transition
            if (this.sceneManager.controls) {
                this.sceneManager.controls.enabled = false;
            }
            
            // Go directly to close position in one smooth movement  
            const finalPosition = new THREE.Vector3(0, 18, 3); // Higher position for 3x larger board
            const targetRotation = new THREE.Euler(-1.484, 0, 0); // 85 degrees down (almost straight down)
            
            // Set very narrow FOV for almost orthographic view (10% perspective)
            if (camera.isPerspectiveCamera) {
                camera.fov = 5; // Very narrow FOV for almost orthographic look
                camera.updateProjectionMatrix();
                console.log('[Chess3DMinigame] Set camera FOV to 5 for near-orthographic effect');
            }
            
            // Animate camera transition from current position
            const startPosition = camera.position.clone();
            const startRotation = camera.rotation.clone();
            
            console.log('[Chess3DMinigame] Starting camera transition from:', startPosition.x.toFixed(2), startPosition.y.toFixed(2), startPosition.z.toFixed(2));
            
            // Single smooth animation directly to final position
            const duration = 2500; // 2.5 seconds for smooth transition
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Smooth easing with slow start and end
                const eased = 0.5 * (1 - Math.cos(progress * Math.PI));
                
                // Interpolate position directly to final position
                camera.position.lerpVectors(startPosition, finalPosition, eased);
                
                // Interpolate rotation
                camera.rotation.x = THREE.MathUtils.lerp(startRotation.x, targetRotation.x, eased);
                camera.rotation.y = THREE.MathUtils.lerp(startRotation.y, targetRotation.y, eased);
                camera.rotation.z = THREE.MathUtils.lerp(startRotation.z, targetRotation.z, eased);
                
                // Switch to minigame scene early so user can see the movement
                if (progress > 0.1 && this.sceneManager.scene !== this.minigameScene) {
                    this.sceneManager.setRenderScene(this.minigameScene);
                    console.log('[Chess3DMinigame] Switched to minigame scene to show movement');
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    console.log('[Chess3DMinigame] Camera transition complete');
                    console.log('[Chess3DMinigame] Final camera position:', camera.position.x.toFixed(2), camera.position.y.toFixed(2), camera.position.z.toFixed(2));
                    console.log('[Chess3DMinigame] Final camera rotation:', camera.rotation.x.toFixed(2), camera.rotation.y.toFixed(2), camera.rotation.z.toFixed(2));
                    resolve();
                }
            };
            
            animate();
        });
    }
    
    /**
     * Transition camera back to dungeon
     */
    async transitionBackToDungeon() {
        return new Promise((resolve) => {
            const camera = this.sceneManager.camera;
            
            // Get current position
            const startPosition = camera.position.clone();
            const startRotation = camera.rotation.clone();
            
            // Target is original state
            const targetPosition = this.originalCameraState.position;
            const targetRotation = this.originalCameraState.rotation;
            
            const duration = 1500;
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const eased = 1 - Math.pow(1 - progress, 3);
                
                camera.position.lerpVectors(startPosition, targetPosition, eased);
                camera.rotation.x = THREE.MathUtils.lerp(startRotation.x, targetRotation.x, eased);
                camera.rotation.y = THREE.MathUtils.lerp(startRotation.y, targetRotation.y, eased);
                camera.rotation.z = THREE.MathUtils.lerp(startRotation.z, targetRotation.z, eased);
                
                // Switch back to dungeon scene partway through
                if (progress > 0.5) {
                    this.sceneManager.restoreOriginalScene();
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Restore controls
                    if (this.sceneManager.controls && this.originalCameraState.controls) {
                        this.sceneManager.controls.enabled = this.originalCameraState.controls.enabled;
                        this.sceneManager.controls.target.copy(this.originalCameraState.controls.target);
                        this.sceneManager.controls.update();
                    }
                    
                    console.log('[Chess3DMinigame] Camera transition back to dungeon complete');
                    resolve();
                }
            };
            
            animate();
        });
    }
    
    /**
     * Set up interaction handlers
     */
    setupInteractionHandlers() {
        this.boundMouseClick = this.handleMouseClick.bind(this);
        this.boundMouseMove = this.handleMouseMove.bind(this);
        
        window.addEventListener('click', this.boundMouseClick);
        window.addEventListener('mousemove', this.boundMouseMove);
        
        // Add touch events for mobile chess interaction
        this.boundTouchStart = this.handleChessTouchStart.bind(this);
        window.addEventListener('touchstart', this.boundTouchStart, { passive: false });
        
        console.log('[Chess3DMinigame] Interaction handlers set up');
        
        // Set up camera controls
        this.setupCameraControls();
    }
    
    /**
     * Remove interaction handlers
     */
    removeInteractionHandlers() {
        if (this.boundMouseClick) {
            window.removeEventListener('click', this.boundMouseClick);
        }
        if (this.boundMouseMove) {
            window.removeEventListener('mousemove', this.boundMouseMove);
        }
        if (this.boundTouchStart) {
            window.removeEventListener('touchstart', this.boundTouchStart);
        }
        
        // Remove custom cursor as backup cleanup
        this.removeCustomCursor();
        
        console.log('[Chess3DMinigame] Interaction handlers removed');
        
        // Remove camera control handlers
        this.removeCameraControls();
    }
    
    /**
     * Set up camera controls for orbit around chess board
     */
    setupCameraControls() {
        console.log('[Chess3DMinigame] Setting up fresh orbit camera controls');
        
        // Debug: Log orbit setup
        console.log('[Chess3DMinigame] Orbit target set to:', this.orbitCamera.target);
        
        // Initialize camera with better starting angles for full rotation
        const camera = this.sceneManager.camera;
        
        // Instead of calculating from current position, set good default angles
        // This gives us a nice 3/4 view with room to rotate in all directions
        this.orbitCamera.radius = 15; // Good viewing distance
        this.orbitCamera.theta = Math.PI * 0.25; // 45 degrees horizontal
        this.orbitCamera.phi = Math.PI * 0.35; // About 63 degrees from top (good 3/4 view)
        
        console.log('[Chess3DMinigame] Initial camera setup:',
                   'radius:', this.orbitCamera.radius.toFixed(2),
                   'theta:', this.orbitCamera.theta.toFixed(2),
                   'phi:', this.orbitCamera.phi.toFixed(2),
                   'phi degrees:', (this.orbitCamera.phi * 180 / Math.PI).toFixed(0));
        
        // Apply initial position
        this.updateOrbitCamera();
        
        // Bind camera control handlers
        this.boundMouseDown = this.handleCameraMouseDown.bind(this);
        this.boundMouseUp = this.handleCameraMouseUp.bind(this);
        this.boundMouseDrag = this.handleCameraMouseDrag.bind(this);
        this.boundKeyDown = this.handleCameraKeyDown.bind(this);
        this.boundKeyUp = this.handleCameraKeyUp.bind(this);
        this.boundTouchStart = this.handleCameraTouchStart.bind(this);
        this.boundTouchMove = this.handleCameraTouchMove.bind(this);
        this.boundTouchEnd = this.handleCameraTouchEnd.bind(this);
        
        // Mouse events for camera rotation
        window.addEventListener('mousedown', this.boundMouseDown);
        window.addEventListener('mouseup', this.boundMouseUp);
        window.addEventListener('mousemove', this.boundMouseDrag);
        
        // Single high-priority keyboard event listener for all WASD keys
        this.boundKeyDown = this.handleCameraKeyDown.bind(this);
        this.boundKeyUp = this.handleCameraKeyUp.bind(this);
        document.addEventListener('keydown', this.boundKeyDown, true);
        document.addEventListener('keyup', this.boundKeyUp, true);
        
        console.log('[Chess3DMinigame] Keyboard event listeners added for camera controls with capture priority');
        
        // Touch events for mobile
        window.addEventListener('touchstart', this.boundTouchStart, { passive: false });
        window.addEventListener('touchmove', this.boundTouchMove, { passive: false });
        window.addEventListener('touchend', this.boundTouchEnd);
        
        // Start camera update loop
        this.startCameraUpdateLoop();
        
        console.log('[Chess3DMinigame] Camera controls set up');
    }
    
    /**
     * Remove camera control handlers
     */
    removeCameraControls() {
        if (this.boundMouseDown) {
            window.removeEventListener('mousedown', this.boundMouseDown);
            window.removeEventListener('mouseup', this.boundMouseUp);
            window.removeEventListener('mousemove', this.boundMouseDrag);
        }
        if (this.boundKeyDown) {
            document.removeEventListener('keydown', this.boundKeyDown, true);
            document.removeEventListener('keyup', this.boundKeyUp, true);
        }
        if (this.boundTouchStart) {
            window.removeEventListener('touchstart', this.boundTouchStart);
            window.removeEventListener('touchmove', this.boundTouchMove);
            window.removeEventListener('touchend', this.boundTouchEnd);
        }
        
        // Stop camera update loop
        if (this.cameraUpdateLoop) {
            cancelAnimationFrame(this.cameraUpdateLoop);
            this.cameraUpdateLoop = null;
        }
        
        console.log('[Chess3DMinigame] Camera controls removed');
    }
    
    /**
     * Check if mouse is over the chess board area
     */
    isMouseOverBoard(x, y) {
        // Convert mouse coordinates to normalized device coordinates
        const mouse = {
            x: (x / window.innerWidth) * 2 - 1,
            y: -(y / window.innerHeight) * 2 + 1
        };
        
        // Simple board boundary check - chess board is roughly in center
        const boardSize = 0.6; // Approximate board size in normalized coordinates
        return (Math.abs(mouse.x) < boardSize && Math.abs(mouse.y) < boardSize);
    }
    
    /**
     * Handle mouse down for camera rotation
     */
    handleCameraMouseDown(event) {
        if (!this.isInMinigame) return;
        
        const isOverBoard = this.isMouseOverBoard(event.clientX, event.clientY);
        
        // Only start camera rotation if mouse is outside board area
        if (!isOverBoard) {
            this.orbitCamera.isDragging = true;
            this.orbitCamera.lastMouseX = event.clientX;
            this.orbitCamera.lastMouseY = event.clientY;
            event.preventDefault();
        }
    }
    
    /**
     * Handle mouse up for camera rotation
     */
    handleCameraMouseUp(event) {
        if (!this.isInMinigame) return;
        this.orbitCamera.isDragging = false;
    }
    
    /**
     * Handle mouse drag for camera rotation
     */
    handleCameraMouseDrag(event) {
        if (!this.isInMinigame || !this.orbitCamera.isDragging) return;
        
        const deltaX = event.clientX - this.orbitCamera.lastMouseX;
        const deltaY = event.clientY - this.orbitCamera.lastMouseY;
        
        // Update rotation angles
        this.orbitCamera.theta -= deltaX * 0.01;  // Horizontal rotation
        this.orbitCamera.phi += deltaY * 0.01;    // Vertical rotation
        
        // Clamp phi to allow full rotation from top-down to almost horizontal
        this.orbitCamera.phi = Math.max(0.05, Math.min(Math.PI * 0.85, this.orbitCamera.phi));
        
        // Update last position
        this.orbitCamera.lastMouseX = event.clientX;
        this.orbitCamera.lastMouseY = event.clientY;
        
        // Update camera
        this.updateOrbitCamera();
    }
    
    /**
     * Handle keyboard input for camera movement
     */
    handleCameraKeyDown(event) {
        if (!this.isInMinigame) return;
        
        // Handle camera control keys
        switch(event.code) {
            case 'KeyW':
            case 'ArrowUp':
                this.orbitCamera.keys.w = true;
                event.preventDefault();
                break;
            case 'KeyS':
            case 'ArrowDown':
                this.orbitCamera.keys.s = true;
                event.preventDefault();
                break;
            case 'KeyA':
            case 'ArrowLeft':
                this.orbitCamera.keys.a = true;
                event.preventDefault();
                break;
            case 'KeyD':
            case 'ArrowRight':
                this.orbitCamera.keys.d = true;
                event.preventDefault();
                break;
        }
    }
    
    /**
     * Handle keyboard input release for camera movement
     */
    handleCameraKeyUp(event) {
        console.log('[Chess3DMinigame] Key released:', event.code, 'isInMinigame:', this.isInMinigame);
        if (!this.isInMinigame) return;
        
        // Aggressively capture WASD key releases
        if (event.code === 'KeyW' || event.code === 'KeyS' || event.code === 'KeyA' || event.code === 'KeyD' || 
            event.code.startsWith('Arrow')) {
            console.log('[Chess3DMinigame] Capturing key release:', event.code);
            event.stopImmediatePropagation();
            event.preventDefault();
            
            switch(event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    this.orbitCamera.keys.w = false;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    this.orbitCamera.keys.s = false;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    this.orbitCamera.keys.a = false;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    this.orbitCamera.keys.d = false;
                    break;
            }
        }
    }
    
    /**
     * Handle touch start for mobile camera controls
     */
    handleCameraTouchStart(event) {
        if (!this.isInMinigame) return;
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            // Only start camera rotation if touch is outside board area
            if (!this.isMouseOverBoard(touch.clientX, touch.clientY)) {
                this.orbitCamera.isDragging = true;
                this.orbitCamera.lastMouseX = touch.clientX;
                this.orbitCamera.lastMouseY = touch.clientY;
                event.preventDefault();
            }
        }
    }
    
    /**
     * Handle touch move for mobile camera controls
     */
    handleCameraTouchMove(event) {
        if (!this.isInMinigame || !this.orbitCamera.isDragging) return;
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            const deltaX = touch.clientX - this.orbitCamera.lastMouseX;
            const deltaY = touch.clientY - this.orbitCamera.lastMouseY;
            
            // Update rotation
            this.orbitCamera.theta -= deltaX * 0.01;
            this.orbitCamera.phi += deltaY * 0.01;
            
            // Update last position
            this.orbitCamera.lastMouseX = touch.clientX;
            this.orbitCamera.lastMouseY = touch.clientY;
            
            // Update camera
            this.updateOrbitCamera();
            event.preventDefault();
        }
    }
    
    /**
     * Handle touch end for mobile camera controls
     */
    handleCameraTouchEnd(event) {
        if (!this.isInMinigame) return;
        this.orbitCamera.isDragging = false;
    }
    
    /**
     * Update camera position based on current rotation and distance
     */
    /**
     * Simple camera orbit - completely isolated from other systems
     */
    updateOrbitCamera() {
        if (!this.sceneManager.camera) return;
        
        const camera = this.sceneManager.camera;
        const orbit = this.orbitCamera;
        
        // Very simple orbit calculation
        const angleH = orbit.theta; // Horizontal angle
        const angleV = orbit.phi;   // Vertical angle
        
        // Calculate position on a sphere around orbit target
        const x = orbit.target.x + orbit.radius * Math.sin(angleH) * Math.sin(angleV);
        const y = orbit.target.y + orbit.radius * Math.cos(angleV);
        const z = orbit.target.z + orbit.radius * Math.cos(angleH) * Math.sin(angleV);
        
        // Position camera
        camera.position.x = x;
        camera.position.y = y;
        camera.position.z = z;
        
        // Look at orbit target
        camera.lookAt(orbit.target.x, orbit.target.y, orbit.target.z);
        
        // Force update matrices
        camera.updateMatrixWorld(true);
        
        // Optional debug logging (disabled by default)
        if (this.debugCameraUpdates) {
            console.log('[Chess3DMinigame] Orbit Debug:', 
                       `target(${orbit.target.x}, ${orbit.target.y}, ${orbit.target.z})`,
                       `camera(${x.toFixed(1)}, ${y.toFixed(1)}, ${z.toFixed(1)})`,
                       `angles: H=${angleH.toFixed(2)}, V=${angleV.toFixed(2)}`);
        }
    }
    
    /**
     * Start camera update loop for WASD movement
     */
    startCameraUpdateLoop() {
        if (this.cameraUpdateLoop) {
            cancelAnimationFrame(this.cameraUpdateLoop);
        }
        
        const updateCamera = () => {
            if (!this.isInMinigame) {
                return;
            }

            const orbit = this.orbitCamera;
            const rotationSpeed = 0.03; // Increased for more responsive keyboard rotation
            let changed = false;

            // Handle keyboard rotation
            if (orbit.keys.a) {
                orbit.theta += rotationSpeed;
                changed = true;
            }
            if (orbit.keys.d) {
                orbit.theta -= rotationSpeed;
                changed = true;
            }
            if (orbit.keys.w) {
                orbit.phi -= rotationSpeed;
                changed = true;
            }
            if (orbit.keys.s) {
                orbit.phi += rotationSpeed;
                changed = true;
            }
            
            // Clamp phi to allow full rotation from top-down to almost horizontal
            if (changed) {
                orbit.phi = Math.max(0.05, Math.min(Math.PI * 0.85, orbit.phi)); // 3° to 153° range
            }

            // Update camera if changed
            if (changed) {
                this.updateOrbitCamera();
            }

            // Continue the loop
            this.cameraUpdateLoop = requestAnimationFrame(updateCamera);
        };
        
        this.cameraUpdateLoop = requestAnimationFrame(updateCamera);
    }
    
    /**
     * Handle touch events for mobile chess interaction
     */
    handleChessTouchStart(event) {
        if (!this.isInMinigame) return;
        
        // Only handle single touch for chess interaction
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            
            // Check if touch is over the board area
            if (this.isMouseOverBoard(touch.clientX, touch.clientY)) {
                // Convert touch to click event for chess interaction
                const syntheticEvent = {
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    preventDefault: () => event.preventDefault()
                };
                
                // Call the existing mouse click handler
                this.handleMouseClick(syntheticEvent);
                event.preventDefault();
            }
        }
    }
    
    /**
     * Create and show custom red cursor for chess minigame
     */
    createCustomCursor() {
        // Create cursor element
        this.customCursor = document.createElement('div');
        this.customCursor.style.cssText = `
            position: fixed;
            width: 20px;
            height: 20px;
            background: #ff0000;
            border: 2px solid #darkred;
            border-radius: 50%;
            pointer-events: none;
            z-index: 10000;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
            transition: transform 0.1s ease;
        `;
        
        // Add to document
        document.body.appendChild(this.customCursor);
        
        // Hide system cursor
        document.body.style.cursor = 'none';
        
        // Bind cursor movement handler
        this.boundCursorMove = this.updateCursorPosition.bind(this);
        window.addEventListener('mousemove', this.boundCursorMove);
        
        console.log('[Chess3DMinigame] Custom red cursor created');
    }
    
    /**
     * Update custom cursor position
     */
    updateCursorPosition(event) {
        if (this.customCursor) {
            this.customCursor.style.left = event.clientX + 'px';
            this.customCursor.style.top = event.clientY + 'px';
        }
    }
    
    /**
     * Remove custom cursor and restore system cursor
     */
    removeCustomCursor() {
        if (this.customCursor) {
            document.body.removeChild(this.customCursor);
            this.customCursor = null;
        }
        
        if (this.boundCursorMove) {
            window.removeEventListener('mousemove', this.boundCursorMove);
            this.boundCursorMove = null;
        }
        
        // Restore system cursor (or hide it again for main game)
        document.body.style.cursor = 'none'; // Keep hidden for main game
        
        console.log('[Chess3DMinigame] Custom cursor removed');
    }
    
    /**
     * Hide minimap temporarily during minigame
     */
    hideMinimapTemporarily() {
        const minimapElement = document.getElementById('minimap-grid');
        if (minimapElement) {
            this.originalMinimapDisplay = minimapElement.style.display;
            minimapElement.style.display = 'none';
            console.log('[Chess3DMinigame] Minimap hidden during chess game');
        }
    }
    
    /**
     * Restore minimap visibility after minigame
     */
    restoreMinimapVisibility() {
        const minimapElement = document.getElementById('minimap-grid');
        if (minimapElement) {
            if (this.originalMinimapDisplay !== undefined) {
                minimapElement.style.display = this.originalMinimapDisplay;
            } else {
                minimapElement.style.display = 'grid'; // Default minimap display
            }
            console.log('[Chess3DMinigame] Minimap visibility restored');
        }
    }
    
    /**
     * Temporarily disable player controls during minigame
     */
    disablePlayerControls() {
        if (this.sceneManager && this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.playerController) {
            const playerController = this.sceneManager.activeSceneHandler.playerController;
            console.log('[Chess3DMinigame] Found player controller, disabling it properly');
            
            // Store the current enabled state and disable the player controller
            this.playerControllerWasEnabled = playerController.inputEnabled;
            playerController.disable();
            
            console.log('[Chess3DMinigame] Player controller disabled, was enabled:', this.playerControllerWasEnabled);
        } else {
            console.warn('[Chess3DMinigame] Could not find player controller to disable');
        }
    }
    
    /**
     * Re-enable player controls after minigame
     */
    enablePlayerControls() {
        if (this.sceneManager && this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.playerController) {
            const playerController = this.sceneManager.activeSceneHandler.playerController;
            
            // Re-enable the player controller if it was enabled before
            if (this.playerControllerWasEnabled) {
                playerController.enable();
                console.log('[Chess3DMinigame] Player controller re-enabled');
            } else {
                console.log('[Chess3DMinigame] Player controller was not enabled before, leaving disabled');
            }
            
            this.playerControllerWasEnabled = null;
        }
    }
    
    /**
     * Handle mouse click for piece selection and movement
     */
    handleMouseClick(event) {
        if (!this.isInMinigame) return;
        
        // Debounce clicks to prevent rapid clicking issues
        const currentTime = Date.now();
        if (currentTime - this.lastClickTime < this.clickDebounceDelay) {
            console.log('[Chess3DMinigame] Click ignored - too fast (debounced)');
            return;
        }
        this.lastClickTime = currentTime;
        
        // Prevent clicks while processing moves
        if (this.isProcessingMove) {
            console.log('[Chess3DMinigame] Click ignored - currently processing move');
            return;
        }
        
        // Update mouse coordinates
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // Create fresh raycaster without layer restrictions
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        
        console.log('[Chess3DMinigame] Click detected, mouse coords:', this.mouse.x.toFixed(3), this.mouse.y.toFixed(3));
        console.log('[Chess3DMinigame] Total pieces tracked:', this.chessPieces.size);
        
        
        // Priority 1: Check for move indicator clicks (when piece is selected)
        if (this.selectedPiece && this.validMoveIndicators.length > 0) {
            console.log('[Chess3DMinigame] Checking move indicators, count:', this.validMoveIndicators.length);
            
            // First try raycasting on move indicators
            const moveIntersects = this.raycaster.intersectObjects(this.validMoveIndicators);
            console.log('[Chess3DMinigame] Move indicator intersects:', moveIntersects.length);
            
            if (moveIntersects.length > 0) {
                const indicator = moveIntersects[0].object;
                const { moveX, moveY } = indicator.userData;
                const algebraic = this.getAlgebraicNotation(moveX, moveY);
                console.log(`[Chess3DMinigame] ✅ Clicked move indicator to ${algebraic} (${moveX},${moveY}) - Distance:`, moveIntersects[0].distance.toFixed(2));
                this.executeMove(this.selectedPiece, moveX, moveY);
                return;
            } else {
                console.log('[Chess3DMinigame] ⚠️ No move indicator intersects detected despite having indicators');
            }
        }
        
        // Priority 2: Direct geometric piece detection (bypassing Three.js layer system)
        const clickedPiece = this.findPieceByDirectIntersection();
        
        if (clickedPiece) {
            console.log('[Chess3DMinigame] ✅ Found piece via direct intersection:', clickedPiece.userData.pieceDescription);
            this.handlePieceClick(clickedPiece);
            return;
        }
        
        // Priority 3: Check what we actually hit before clearing selection
        const allObjects = [];
        allObjects.push(...Array.from(this.chessPieces.values()));
        allObjects.push(...this.validMoveIndicators);
        allObjects.push(...this.chessBoard.children);
        
        const allIntersects = this.raycaster.intersectObjects(allObjects, true);
        console.log('[Chess3DMinigame] All intersects:', allIntersects.length);
        if (allIntersects.length > 0) {
            const hitObject = allIntersects[0].object;
            const objectName = hitObject.name || 'unnamed_object';
            const userData = hitObject.userData || {};
            const algebraic = userData.algebraicNotation || 'no_position';
            console.log(`[Chess3DMinigame] Hit object: "${objectName}" at ${algebraic}`, {
                name: objectName,
                position: algebraic,
                type: userData.squareType || userData.pieceType || 'unknown',
                coordinates: { x: userData.boardX, z: userData.boardZ },
                distance: allIntersects[0].distance.toFixed(2)
            });
            
            // If we hit a board square, check if there should be a piece there and select it directly
            if (userData.squareType === 'board_square') {
                const expectedPieceKey = `${userData.boardX},${userData.boardZ}`;
                const expectedPiece = this.chessPieces.get(expectedPieceKey);
                if (expectedPiece) {
                    console.log(`[Chess3DMinigame] 🎯 DIRECT TILE-TO-PIECE SELECTION: Found piece at ${algebraic}:`, expectedPiece.userData.pieceDescription);
                    // Select the piece directly using tile-to-piece lookup
                    this.handlePieceClick(expectedPiece);
                    return;
                } else {
                    console.log(`[Chess3DMinigame] ✅ No piece expected at ${algebraic} - square correctly empty`);
                    
                    // Check if this empty square is a valid move destination for selected piece
                    if (this.selectedPiece && this.validMoveIndicators.length > 0) {
                        // Direct tile-to-move lookup: check if clicked tile matches any valid move
                        const clickedMoveKey = `${userData.boardX},${userData.boardZ}`;
                        const validMove = this.validMoveIndicators.find(indicator => {
                            if (indicator.userData.moveX !== undefined && indicator.userData.moveY !== undefined) {
                                const moveKey = `${indicator.userData.moveX},${indicator.userData.moveY}`;
                                return moveKey === clickedMoveKey;
                            }
                            return false;
                        });
                        
                        if (validMove) {
                            const { moveX, moveY } = validMove.userData;
                            const moveAlgebraic = this.getAlgebraicNotation(moveX, moveY);
                            console.log(`[Chess3DMinigame] 🎯 DIRECT TILE-TO-MOVE SELECTION: Moving to ${moveAlgebraic} (${moveX},${moveY})`);
                            this.executeMove(this.selectedPiece, moveX, moveY);
                            return;
                        }
                    }
                }
            }
        }
        
        // Clear selection if clicking empty space
        if (this.selectedPiece) {
            console.log('[Chess3DMinigame] ❌ Clicked empty space, clearing selection');
            this.clearSelection();
        }
    }
    
    /**
     * Handle mouse move for hover effects
     */
    handleMouseMove(event) {
        if (!this.isInMinigame) return;
        
        // Update mouse coordinates
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        // Raycast to check what we're hovering over
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        
        // Check for chess board squares first
        const boardSquares = this.chessBoard.children.filter(child => child.userData.boardX !== undefined);
        const squareIntersects = this.raycaster.intersectObjects(boardSquares);
        
        if (squareIntersects.length > 0) {
            const square = squareIntersects[0].object;
            const { boardX, boardZ } = square.userData;
            
            // Show hover indicator if not already on this square
            if (!this.hoveredSquare || this.hoveredSquare.x !== boardX || this.hoveredSquare.z !== boardZ) {
                this.showHoverIndicator(boardX, boardZ);
                this.hoveredSquare = { x: boardX, z: boardZ };
            }
        } else {
            // Clear hover indicator if not over any square
            this.clearHoverIndicator();
            this.hoveredSquare = null;
        }
    }
    
    /**
     * Handle piece click
     */
    handlePieceClick(piece) {
        const { boardX, boardZ, pieceColor } = piece.userData;
        
        // Only allow selecting player's pieces
        if (pieceColor !== this.chessGame.currentPlayer) {
            console.log('[Chess3DMinigame] Cannot select opponent piece');
            return;
        }
        
        // Select this piece
        this.selectPiece(piece, boardX, boardZ);
    }
    
    
    /**
     * Select a chess piece
     */
    selectPiece(piece, boardX, boardZ) {
        // Clear previous selection
        this.clearSelection();
        
        this.selectedPiece = piece;
        
        // Add visual selection indicator
        this.addSelectionIndicator(piece);
        
        // Get valid moves and show indicators
        const validMoves = this.chessGame.getValidMoves(boardX, boardZ);
        this.showValidMoveIndicators(validMoves);
        
        // 🔍 DEBUG: Show detailed piece analysis
        this.debugPieceAnalysis(piece, boardX, boardZ, validMoves);
        
        // Add player self-dialogue for invalid moves
        if (validMoves.length === 0) {
            this.explainWhyNoValidMoves(piece, boardX, boardZ);
        }
        
        console.log(`[Chess3DMinigame] Selected ${piece.userData.pieceDescription} at ${piece.userData.algebraicNotation}`);
    }
    
    /**
     * Clear current selection
     */
    clearSelection() {
        if (this.selectedPiece) {
            this.removeSelectionIndicator();
            this.clearValidMoveIndicators();
            this.selectedPiece = null;
        }
    }
    
    /**
     * Add visual selection indicator to piece
     */
    addSelectionIndicator(piece) {
        const squareSize = VOXEL_SIZE * 24; // 3x larger board
        const indicatorGeometry = new THREE.RingGeometry(squareSize * 0.3, squareSize * 0.4, 16);
        const indicatorMaterial = new THREE.MeshBasicMaterial({
            color: 0xffaa00,
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide
        });
        
        const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
        indicator.position.copy(piece.position);
        indicator.position.y = 0.3;
        indicator.rotation.x = -Math.PI / 2;
        indicator.name = 'selectionIndicator';
        
        this.minigameScene.add(indicator);
    }
    
    /**
     * Remove selection indicator
     */
    removeSelectionIndicator() {
        const indicator = this.minigameScene.getObjectByName('selectionIndicator');
        if (indicator) {
            this.minigameScene.remove(indicator);
        }
    }
    
    /**
     * Show valid move indicators
     */
    showValidMoveIndicators(moves) {
        this.clearValidMoveIndicators();
        
        const squareSize = VOXEL_SIZE * 24; // Updated for 3x larger board
        
        moves.forEach(move => {
            // Create ring geometry with hole in middle - slimmer with bigger hole
            const indicatorGeometry = new THREE.RingGeometry(squareSize * 0.25, squareSize * 0.4, 16);
            const indicatorMaterial = new THREE.MeshBasicMaterial({
                color: 0x00ff00,
                transparent: true,
                opacity: 0.4,
                side: THREE.DoubleSide
            });
            
            const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            indicator.position.set(
                (move.x - 3.5) * squareSize,
                0.3, // Lower position for better integration with board
                (move.y - 3.5) * squareSize
            );
            indicator.rotation.x = -Math.PI / 2;
            indicator.name = 'moveIndicator';
            indicator.userData = { moveX: move.x, moveY: move.y };
            
            // Ensure indicator is on layer 0 for raycasting
            indicator.layers.set(0);
            
            this.validMoveIndicators.push(indicator);
            this.minigameScene.add(indicator);
        });
    }
    
    /**
     * Clear valid move indicators
     */
    clearValidMoveIndicators() {
        this.validMoveIndicators.forEach(indicator => {
            this.minigameScene.remove(indicator);
        });
        this.validMoveIndicators = [];
    }
    
    /**
     * Show hover indicator on board square
     */
    showHoverIndicator(boardX, boardZ) {
        this.clearHoverIndicator();
        
        const squareSize = VOXEL_SIZE * 24; // 3x larger board
        
        // Create hover border indicator
        const borderGeometry = new THREE.RingGeometry(squareSize * 0.4, squareSize * 0.5, 4);
        const borderMaterial = new THREE.MeshBasicMaterial({
            color: 0x00aaff,
            transparent: true,
            opacity: 0.7,
            side: THREE.DoubleSide
        });
        
        this.hoverIndicator = new THREE.Mesh(borderGeometry, borderMaterial);
        this.hoverIndicator.position.set(
            (boardX - 3.5) * squareSize,
            0.4,
            (boardZ - 3.5) * squareSize
        );
        this.hoverIndicator.rotation.x = -Math.PI / 2;
        this.hoverIndicator.name = 'hoverIndicator';
        
        this.minigameScene.add(this.hoverIndicator);
    }
    
    /**
     * Clear hover indicator
     */
    clearHoverIndicator() {
        if (this.hoverIndicator) {
            this.minigameScene.remove(this.hoverIndicator);
            this.hoverIndicator = null;
        }
    }
    
    /**
     * Find the chess piece parent object
     */
    findChessPieceParent(object) {
        let parent = object;
        while (parent && !parent.userData.isChessPiece) {
            parent = parent.parent;
        }
        return parent;
    }
    
    /**
     * Execute a chess move
     */
    async executeMove(piece, toX, toY) {
        // Set processing flag to prevent multiple concurrent moves
        this.isProcessingMove = true;
        
        const fromX = piece.userData.boardX;
        const fromZ = piece.userData.boardZ; // Use consistent boardZ naming
        
        const fromAlgebraic = this.getAlgebraicNotation(fromX, fromZ);
        const toAlgebraic = this.getAlgebraicNotation(toX, toY);
        console.log(`[Chess3DMinigame] Executing move: ${piece.userData.pieceDescription} from ${fromAlgebraic} to ${toAlgebraic}`);
        
        // Check if there's a piece to capture
        const capturedPiece = this.chessPieces.get(`${toX},${toY}`);
        if (capturedPiece) {
            console.log(`[Chess3DMinigame] Capturing ${capturedPiece.userData.pieceDescription}`);
            await this.createDestructionEffect(capturedPiece);
            this.chessPieces.delete(`${toX},${toY}`);
        }
        
        // Move the piece with animation
        await this.movePiece(fromX, fromZ, toX, toY);
        
        // Update chess game logic
        this.chessGame.makeMove({ x: fromX, y: fromZ }, { x: toX, y: toY });
        
        // Check for checkmate/stalemate after the move
        await this.chessGame.checkGameState();
        
        // Clear selection
        this.clearSelection();
        
        // Update game status and show commentary (only for player moves, AI handles its own commentary)
        if (capturedPiece && this.chessGame.currentPlayer === 'white') {
            await this.showDevilCommentary("\"Another soul claimed...\"");
        }
        
        // Check if it's AI's turn
        if (this.chessGame.currentPlayer === 'black') {
            this.updateGameStatus("Devil's Turn");
            console.log('[Chess3DMinigame] AI turn starting...');
            
            // Show devil thinking commentary
            await this.showDevilCommentary("\"Hmm... so many ways to destroy you...\"");
            
            // Let AI make its move after delay
            setTimeout(() => this.handleAIMove(), 1500);
        } else {
            this.updateGameStatus("Your Turn");
            // Clear processing flag for player turns
            this.isProcessingMove = false;
        }
    }
    
    /**
     * Handle AI move
     */
    async handleAIMove() {
        // Get AI move using the chess game's AI system (wrapped in setTimeout to avoid blocking)
        const move = await new Promise(resolve => {
            setTimeout(() => {
                const aiMove = this.chessGame.ai.getBestMove(
                    this.chessGame.board, 
                    'black', 
                    this.chessGame.difficulty,
                    this.chessGame.kingPositions,
                    this.chessGame.castlingRights,
                    this.chessGame.enPassantTarget
                );
                resolve(aiMove);
            }, 10); // Small delay to prevent blocking
        });
        
        if (move && move.from && move.to) {
            const fromAlgebraic = this.getAlgebraicNotation(move.from.x, move.from.y);
            const toAlgebraic = this.getAlgebraicNotation(move.to.x, move.to.y);
            console.log(`[Chess3DMinigame] AI move: ${move.piece} from ${fromAlgebraic} to ${toAlgebraic} - coordinates (${move.from.x},${move.from.y}) to (${move.to.x},${move.to.y})`);
            
            // Find the AI piece to move
            const aiPiece = this.chessPieces.get(`${move.from.x},${move.from.y}`);
            if (aiPiece) {
                // Check if this is a capture move
                const capturedPiece = this.chessPieces.get(`${move.to.x},${move.to.y}`);
                
                // Add camera rotation for dramatic effect
                await this.createCameraRotation();
                
                // Execute AI move in 3D
                await this.executeMove(aiPiece, move.to.x, move.to.y);
                
                // Show devil commentary after move
                const devilComments = {
                    capture: [
                        "\"*devours piece* Delicious!\"",
                        "\"Your army grows thin, mortal.\"",
                        "\"Feed me more sacrifices!\"",
                        "\"*laughs* Was that important?\""
                    ],
                    move: [
                        "\"Checkmate approaches, mortal.\"",
                        "\"You cannot escape your fate.\"",
                        "\"Each move tightens the noose.\"",
                        "\"*grins* Beautiful, isn't it?\"",
                        "\"Perfect placement... you'll understand soon.\""
                    ]
                };
                
                const commentType = capturedPiece ? 'capture' : 'move';
                const comments = devilComments[commentType];
                const randomComment = comments[Math.floor(Math.random() * comments.length)];
                
                await this.showDevilCommentary(randomComment);
                
                // Update game status back to player turn
                this.updateGameStatus("Your Turn");
                
                // Clear processing flag after AI move
                this.isProcessingMove = false;
                
                console.log('[Chess3DMinigame] AI move completed');
            } else {
                console.error('[Chess3DMinigame] Could not find AI piece to move');
                this.isProcessingMove = false; // Clear flag on error
            }
        } else {
            console.error('[Chess3DMinigame] AI could not find a valid move');
            this.isProcessingMove = false; // Clear flag on error
        }
    }
    
    /**
     * Create dramatic earthquake screen shake for AI moves
     */
    async createCameraRotation() {
        return new Promise((resolve) => {
            const camera = this.sceneManager.camera;
            const originalPosition = camera.position.clone();
            
            const shakeIntensity = 0.3; // Intensity of the shake
            const shakeDuration = 600; // Total duration in ms
            const shakeFrequency = 20; // How fast the shake is (higher = more rapid)
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = elapsed / shakeDuration;
                
                if (progress < 1) {
                    // Create rapid earthquake-like shake
                    const shakeTime = elapsed * 0.01 * shakeFrequency;
                    
                    // Gradually reduce intensity over time for natural feel
                    const currentIntensity = shakeIntensity * (1 - progress) * (1 - progress);
                    
                    // Multi-directional shake with different frequencies
                    const shakeX = Math.sin(shakeTime * 1.3) * currentIntensity;
                    const shakeY = Math.sin(shakeTime * 1.7) * currentIntensity * 0.5; // Less vertical shake
                    const shakeZ = Math.sin(shakeTime * 1.1) * currentIntensity;
                    
                    // Apply shake to camera position
                    camera.position.set(
                        originalPosition.x + shakeX,
                        originalPosition.y + shakeY,
                        originalPosition.z + shakeZ
                    );
                    
                    requestAnimationFrame(animate);
                } else {
                    // Return to original position
                    camera.position.copy(originalPosition);
                    resolve();
                }
            };
            
            animate();
        });
    }
    
    /**
     * Move piece with animation
     */
    async movePiece(fromX, fromZ, toX, toZ) {
        const piece = this.chessPieces.get(`${fromX},${fromZ}`);
        if (!piece) {
            console.warn(`[Chess3DMinigame] No piece found at (${fromX},${fromZ})`);
            return;
        }
        
        const squareSize = VOXEL_SIZE * 24; // 3x larger board
        const targetPosition = new THREE.Vector3(
            (toX - 3.5) * squareSize,
            0.1, // Very low Y position so pieces sit properly on board
            (toZ - 3.5) * squareSize
        );
        
        const fromAlgebraic = this.getAlgebraicNotation(fromX, fromZ);
        const toAlgebraic = this.getAlgebraicNotation(toX, toZ);
        console.log(`[Chess3DMinigame] Moving piece from ${fromAlgebraic} to ${toAlgebraic}`);
        
        // Animate piece movement
        return new Promise((resolve) => {
            const startPosition = piece.position.clone();
            const duration = 800;
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Add slight arc to movement
                const arcHeight = 2;
                const arcProgress = Math.sin(progress * Math.PI);
                
                piece.position.lerpVectors(startPosition, targetPosition, progress);
                piece.position.y = targetPosition.y + arcProgress * arcHeight;
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    piece.position.copy(targetPosition);
                    
                    // Update tracking with consistent coordinate naming
                    this.chessPieces.delete(`${fromX},${fromZ}`);
                    this.chessPieces.set(`${toX},${toZ}`, piece);
                    piece.userData.boardX = toX;
                    piece.userData.boardZ = toZ; // Use consistent boardZ naming
                    piece.userData.algebraicNotation = this.getAlgebraicNotation(toX, toZ);
                    piece.userData.pieceDescription = `${piece.userData.pieceColor} ${piece.userData.pieceType} on ${piece.userData.algebraicNotation}`;
                    piece.name = `${piece.userData.pieceColor}_${piece.userData.pieceType}_${piece.userData.algebraicNotation}`;
                    
                    resolve();
                }
            };
            
            animate();
        });
    }
    
    /**
     * Create destruction effect for captured piece
     */
    async createDestructionEffect(piece) {
        console.log('[Chess3DMinigame] Creating destruction effect for captured piece');
        
        // Create debris particles
        const debrisCount = 15;
        const debris = [];
        
        for (let i = 0; i < debrisCount; i++) {
            const debrisGeometry = new THREE.BoxGeometry(0.3, 0.3, 0.3);
            const debrisMaterial = new THREE.MeshBasicMaterial({
                color: piece.userData.pieceColor === 'white' ? 0xcccccc : 0x333333
            });
            const debrisPiece = new THREE.Mesh(debrisGeometry, debrisMaterial);
            
            // Start at piece position
            debrisPiece.position.copy(piece.position);
            
            // Random velocity
            debrisPiece.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 10,
                Math.random() * 8 + 2,
                (Math.random() - 0.5) * 10
            );
            
            // Random rotation velocity
            debrisPiece.rotationVelocity = new THREE.Vector3(
                Math.random() * 0.3,
                Math.random() * 0.3,
                Math.random() * 0.3
            );
            
            debris.push(debrisPiece);
            this.minigameScene.add(debrisPiece);
        }
        
        // Remove original piece
        this.minigameScene.remove(piece);
        
        // Animate debris
        return new Promise((resolve) => {
            const duration = 2000; // 2 seconds
            const startTime = Date.now();
            const gravity = -20;
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = elapsed / duration;
                const deltaTime = 0.016; // ~60fps
                
                if (progress < 1) {
                    debris.forEach(debrisPiece => {
                        // Apply gravity
                        debrisPiece.velocity.y += gravity * deltaTime;
                        
                        // Update position
                        debrisPiece.position.add(debrisPiece.velocity.clone().multiplyScalar(deltaTime));
                        
                        // Update rotation
                        debrisPiece.rotation.x += debrisPiece.rotationVelocity.x * deltaTime;
                        debrisPiece.rotation.y += debrisPiece.rotationVelocity.y * deltaTime;
                        debrisPiece.rotation.z += debrisPiece.rotationVelocity.z * deltaTime;
                        
                        // Fade out
                        debrisPiece.material.opacity = 1 - progress;
                        debrisPiece.material.transparent = true;
                    });
                    
                    requestAnimationFrame(animate);
                } else {
                    // Clean up debris
                    debris.forEach(debrisPiece => {
                        this.minigameScene.remove(debrisPiece);
                        debrisPiece.geometry.dispose();
                        debrisPiece.material.dispose();
                    });
                    
                    resolve();
                }
            };
            
            animate();
        });
        
        // Add screen shake effect
        this.createScreenShake();
    }
    
    /**
     * Create screen shake effect
     */
    createScreenShake() {
        const camera = this.sceneManager.camera;
        const originalPosition = camera.position.clone();
        
        const shakeIntensity = 0.1;
        const shakeDuration = 300;
        const startTime = Date.now();
        
        const shake = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < shakeDuration) {
                camera.position.x = originalPosition.x + (Math.random() - 0.5) * shakeIntensity;
                camera.position.y = originalPosition.y + (Math.random() - 0.5) * shakeIntensity;
                camera.position.z = originalPosition.z + (Math.random() - 0.5) * shakeIntensity;
                
                requestAnimationFrame(shake);
            } else {
                camera.position.copy(originalPosition);
            }
        };
        
        shake();
    }
    
    /**
     * Clean up the minigame scene
     */
    cleanupMinigameScene() {
        if (this.minigameScene) {
            // Dispose of all geometries and materials
            this.minigameScene.traverse((object) => {
                if (object.geometry) {
                    object.geometry.dispose();
                }
                if (object.material) {
                    if (Array.isArray(object.material)) {
                        object.material.forEach(material => material.dispose());
                    } else {
                        object.material.dispose();
                    }
                }
            });
            
            this.minigameScene.clear();
            this.minigameScene = null;
        }
        
        this.chessPieces.clear();
        this.validMoveIndicators = [];
        this.hoverIndicator = null;
        this.hoveredSquare = null;
        this.flames = [];
        
        console.log('[Chess3DMinigame] Minigame scene cleaned up');
    }
    
    /**
     * Create chess UI overlay with status, commentary, and controls
     */
    createChessUI() {
        // Main UI container
        const uiContainer = document.createElement('div');
        uiContainer.id = 'chess-3d-ui';
        uiContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            pointer-events: none;
            z-index: 1000;
            font-family: 'Courier New', monospace;
        `;
        
        // Game status (top center)
        this.gameStatusUI = document.createElement('div');
        this.gameStatusUI.style.cssText = `
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #660000;
            border-radius: 10px;
            padding: 15px 25px;
            color: #ff6666;
            font-size: 1.2em;
            text-align: center;
            text-shadow: 0 0 10px #ff0000;
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.3);
            min-width: 200px;
        `;
        this.updateGameStatus("Your Turn");
        uiContainer.appendChild(this.gameStatusUI);
        
        // Devil commentary chat bubble (bottom center)
        this.devilCommentaryUI = document.createElement('div');
        this.devilCommentaryUI.style.cssText = `
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(25, 0, 0, 0.95));
            border: 3px solid #660000;
            border-radius: 20px;
            padding: 20px 30px;
            max-width: min(70vw, 600px);
            min-height: 80px;
            color: #ff6666;
            font-size: 1.1em;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 25px rgba(255, 0, 0, 0.4);
            text-shadow: 0 0 10px #ff0000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        // Add devil avatar to commentary
        const devilAvatar = document.createElement('div');
        devilAvatar.style.cssText = `
            position: absolute;
            bottom: -15px;
            left: 30px;
            width: 30px;
            height: 30px;
            background: #660000;
            border-radius: 50%;
            border: 2px solid #ff0000;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            color: #ff6666;
            text-shadow: 0 0 5px #ff0000;
        `;
        devilAvatar.textContent = '😈';
        this.devilCommentaryUI.appendChild(devilAvatar);
        
        uiContainer.appendChild(this.devilCommentaryUI);
        
        // Surrender button (top right)
        this.surrenderButton = document.createElement('button');
        this.surrenderButton.textContent = 'Surrender';
        this.surrenderButton.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #660000, #330000);
            border: 2px solid #ff0000;
            border-radius: 8px;
            color: #ff6666;
            font-family: 'Courier New', monospace;
            font-size: 1em;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-shadow: 0 0 5px #ff0000;
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
            pointer-events: auto;
        `;
        
        this.surrenderButton.addEventListener('mouseenter', () => {
            this.surrenderButton.style.background = 'linear-gradient(135deg, #990000, #550000)';
            this.surrenderButton.style.boxShadow = '0 0 15px rgba(255, 0, 0, 0.5)';
        });
        
        this.surrenderButton.addEventListener('mouseleave', () => {
            this.surrenderButton.style.background = 'linear-gradient(135deg, #660000, #330000)';
            this.surrenderButton.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.3)';
        });
        
        this.surrenderButton.addEventListener('click', () => {
            this.handleSurrender();
        });
        
        uiContainer.appendChild(this.surrenderButton);
        
        document.body.appendChild(uiContainer);
        console.log('[Chess3DMinigame] Chess UI created');
    }
    
    /**
     * Update game status text
     */
    updateGameStatus(status) {
        if (this.gameStatusUI) {
            this.gameStatusUI.textContent = status;
        }
    }
    
    /**
     * Show devil commentary with chat bubble animation
     */
    async showDevilCommentary(message) {
        if (!this.devilCommentaryUI) return;
        
        // Defer DOM operations to prevent interference with raycasting
        requestAnimationFrame(() => {
            // Update message without any avatar
            this.devilCommentaryUI.innerHTML = `
                <div style="color: #ff6666; text-align: center; padding: 10px;">
                    ${message}
                </div>
            `;
            
            this.devilCommentaryUI.style.opacity = '1';
            
            // Auto-hide after 4 seconds
            setTimeout(() => {
                if (this.devilCommentaryUI) {
                    this.devilCommentaryUI.style.opacity = '0.6';
                }
            }, 4000);
        });
        
        console.log('[Chess3DMinigame] Devil says:', message);
    }
    
    /**
     * Handle surrender button click
     */
    async handleSurrender() {
        console.log('[Chess3DMinigame] Player surrendered');
        
        // Show devil's victory comment
        await this.showDevilCommentary("\"Wise choice. Cowardice is better than annihilation.\"");
        
        // End the game
        if (this.chessGame.gameEndCallback) {
            this.chessGame.gameEndCallback('surrendered');
        }
        
        // Exit minigame after delay
        setTimeout(() => {
            this.exitMinigame();
        }, 2000);
    }
    
    /**
     * Clean up UI elements
     */
    cleanupChessUI() {
        const uiContainer = document.getElementById('chess-3d-ui');
        if (uiContainer) {
            document.body.removeChild(uiContainer);
            console.log('[Chess3DMinigame] Chess UI cleaned up');
        }
        
        this.gameStatusUI = null;
        this.devilCommentaryUI = null;
        this.surrenderButton = null;
    }

    /**
     * Direct geometric intersection - bypasses Three.js layer system entirely
     * Tests each piece individually using bounding box intersections
     */
    findPieceByDirectIntersection() {
        // Convert mouse coordinates to world ray
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        
        let closestPiece = null;
        let closestDistance = Infinity;
        
        // Test each piece individually
        for (const [position, piece] of this.chessPieces) {
            // Skip if piece is not visible or not in scene
            if (!piece.visible || !piece.parent) continue;
            
            // Test intersection with this specific piece
            const intersects = this.raycaster.intersectObject(piece, true);
            
            if (intersects.length > 0) {
                const distance = intersects[0].distance;
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestPiece = piece;
                }
                console.log(`[Chess3DMinigame] Direct hit on ${piece.userData.pieceDescription} at distance ${distance.toFixed(2)}`);
            }
        }
        
        return closestPiece;
    }
}