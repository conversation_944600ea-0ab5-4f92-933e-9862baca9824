import * as THREE from 'three';
import { VoxelInstanceManager } from './VoxelInstanceManager.js';

/**
 * RoomInstanceOptimizer - Optimizes entire room geometry using instanced rendering
 * This is where we'll see the biggest performance gains
 */
export class RoomInstanceOptimizer {
    constructor() {
        this.voxelManager = new VoxelInstanceManager();
    }

    /**
     * Optimize an entire room's static geometry
     * @param {THREE.Group} roomGroup - The room group to optimize
     * @returns {THREE.Group} Optimized room with instanced meshes
     */
    optimizeRoom(roomGroup) {
        const optimizedGroup = new THREE.Group();
        const staticVoxels = [];
        const dynamicObjects = [];
        
        // Traverse room and categorize objects
        roomGroup.traverse(child => {
            if (child.isMesh) {
                // Check if this is a static voxel (floor, wall, decoration)
                if (this.isStaticVoxel(child)) {
                    staticVoxels.push(this.extractVoxelData(child));
                } else {
                    // Keep dynamic objects as-is (enemies, items, interactive objects)
                    dynamicObjects.push(child);
                }
            }
        });
        
        // Build instanced meshes for static voxels
        if (staticVoxels.length > 0) {
            staticVoxels.forEach(voxel => {
                this.voxelManager.addVoxel(voxel.position, voxel.size, voxel.material);
            });
            
            const instancedGroup = this.voxelManager.build();
            optimizedGroup.add(instancedGroup);
        }
        
        // Add back dynamic objects
        dynamicObjects.forEach(obj => {
            const parent = obj.parent;
            if (parent) parent.remove(obj);
            optimizedGroup.add(obj);
        });
        
        // Copy room properties
        optimizedGroup.position.copy(roomGroup.position);
        optimizedGroup.rotation.copy(roomGroup.rotation);
        optimizedGroup.scale.copy(roomGroup.scale);
        optimizedGroup.userData = { ...roomGroup.userData, optimized: true };
        
        return optimizedGroup;
    }
    
    /**
     * Check if a mesh is a static voxel that can be instanced
     */
    isStaticVoxel(mesh) {
        // Check if it's a box geometry (voxel)
        if (!mesh.geometry || mesh.geometry.type !== 'BoxGeometry') {
            return false;
        }
        
        // Check if it's static (not animated or interactive)
        const userData = mesh.userData || {};
        if (userData.interactive || userData.animated || userData.dynamic) {
            return false;
        }
        
        // Check parent chain for dynamic indicators
        let parent = mesh.parent;
        while (parent) {
            const parentData = parent.userData || {};
            if (parentData.objectType === 'enemy' || 
                parentData.objectType === 'player' ||
                parentData.objectType === 'projectile' ||
                parentData.isItem) {
                return false;
            }
            parent = parent.parent;
        }
        
        return true;
    }
    
    /**
     * Extract voxel data from a mesh
     */
    extractVoxelData(mesh) {
        const worldPos = new THREE.Vector3();
        const worldScale = new THREE.Vector3();
        const worldQuat = new THREE.Quaternion();
        
        mesh.getWorldPosition(worldPos);
        mesh.getWorldScale(worldScale);
        mesh.getWorldQuaternion(worldQuat);
        
        // Assume uniform scale for voxels
        const size = worldScale.x * (mesh.geometry.parameters.width || 1);
        
        return {
            position: worldPos,
            size: size,
            material: mesh.material
        };
    }
    
    /**
     * Optimize floor tiles specifically (very common in rooms)
     */
    static optimizeFloorTiles(floorMeshes) {
        const manager = new VoxelInstanceManager();
        
        floorMeshes.forEach(mesh => {
            if (mesh.isMesh && mesh.geometry) {
                const worldPos = new THREE.Vector3();
                mesh.getWorldPosition(worldPos);
                
                const size = mesh.scale.x * (mesh.geometry.parameters.width || 1);
                manager.addVoxel(worldPos, size, mesh.material);
            }
        });
        
        return manager.build();
    }
    
    /**
     * Create instanced walls for a room
     */
    static createInstancedWalls(wallData) {
        const manager = new VoxelInstanceManager();
        
        wallData.forEach(wall => {
            const { position, size, material } = wall;
            manager.addVoxel(position, size, material);
        });
        
        return manager.build();
    }
}