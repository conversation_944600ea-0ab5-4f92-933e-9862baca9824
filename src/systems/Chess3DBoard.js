import * as THREE from 'three';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js';
import { 
    createWhiteChessPawn, createBlackChessPawn,
    createWhiteChessRook, createBlackChessRook,
    createWhiteChessKnight, createBlackChessKnight,
    createWhiteChessBishop, createBlackChessBishop,
    createWhiteChessQueen, createBlackChessQueen,
    createWhiteChessKing, createBlackChessKing
} from '../generators/prefabs/index.js';

/**
 * Chess3DBoard - Manages 3D chess pieces on the obsidian chess table
 * Handles positioning, piece creation, and 3D board coordinate system
 */
export class Chess3DBoard {
    constructor(scene, chessTable) {
        this.scene = scene;
        this.chessTable = chessTable;
        this.pieces3D = new Map(); // Map chess coordinates to 3D piece objects
        this.squareSize = VOXEL_SIZE * 4; // Match chess table scale
        this.boardCenter = chessTable.position.clone();
        this.selectedPiece = null;
        this.selectedSquareIndicator = null;
        this.validMoveIndicators = [];
        
        console.log('[Chess3DBoard] Initialized 3D chess board system');
        this.initializeBoardIndicators();
    }
    
    /**
     * Initialize visual indicators for board squares
     */
    initializeBoardIndicators() {
        // Create selection indicator (glowing cube)
        const selectionGeometry = new THREE.BoxGeometry(
            this.squareSize * 0.9, 
            VOXEL_SIZE * 0.5, 
            this.squareSize * 0.9
        );
        const selectionMaterial = new THREE.MeshBasicMaterial({
            color: 0xFFAA00,
            transparent: true,
            opacity: 0.6,
            side: THREE.DoubleSide
        });
        this.selectedSquareIndicator = new THREE.Mesh(selectionGeometry, selectionMaterial);
        this.selectedSquareIndicator.visible = false;
        this.scene.add(this.selectedSquareIndicator);
        
        console.log('[Chess3DBoard] Created board indicators');
    }
    
    /**
     * Convert chess coordinates (0-7) to 3D world positions
     */
    chessTo3D(chessX, chessY) {
        return {
            x: this.boardCenter.x + (chessX - 3.5) * this.squareSize,
            y: this.boardCenter.y + VOXEL_SIZE * 6, // Above table surface
            z: this.boardCenter.z + (chessY - 3.5) * this.squareSize
        };
    }
    
    /**
     * Convert 3D world position to chess coordinates
     */
    worldToChess(worldPos) {
        const chessX = Math.round((worldPos.x - this.boardCenter.x) / this.squareSize + 3.5);
        const chessY = Math.round((worldPos.z - this.boardCenter.z) / this.squareSize + 3.5);
        
        // Validate coordinates are on board
        if (chessX >= 0 && chessX < 8 && chessY >= 0 && chessY < 8) {
            return { x: chessX, y: chessY };
        }
        return null;
    }
    
    /**
     * Create a 3D chess piece at specified position
     */
    createPiece3D(pieceType, color, chessX, chessY) {
        const pieceCreators = {
            white: {
                pawn: createWhiteChessPawn,
                rook: createWhiteChessRook,
                knight: createWhiteChessKnight,
                bishop: createWhiteChessBishop,
                queen: createWhiteChessQueen,
                king: createWhiteChessKing
            },
            black: {
                pawn: createBlackChessPawn,
                rook: createBlackChessRook,
                knight: createBlackChessKnight,
                bishop: createBlackChessBishop,
                queen: createBlackChessQueen,
                king: createBlackChessKing
            }
        };
        
        const creator = pieceCreators[color][pieceType];
        if (!creator) {
            console.error(`[Chess3DBoard] No creator found for ${color} ${pieceType}`);
            return null;
        }
        
        // Create the piece with a unique seed based on position
        const piece3D = creator({ 
            scale: 2.4,
            seed: chessX * 8 + chessY 
        });
        
        // Position the piece on the board
        const worldPos = this.chessTo3D(chessX, chessY);
        piece3D.position.set(worldPos.x, worldPos.y, worldPos.z);
        
        // Add chess metadata
        piece3D.userData.chessX = chessX;
        piece3D.userData.chessY = chessY;
        piece3D.userData.chessPiece = pieceType;
        piece3D.userData.chessColor = color;
        piece3D.userData.isChessPiece = true;
        
        // Add to scene and track
        this.scene.add(piece3D);
        this.pieces3D.set(`${chessX},${chessY}`, piece3D);
        
        console.log(`[Chess3DBoard] Created ${color} ${pieceType} at (${chessX},${chessY})`);
        return piece3D;
    }
    
    /**
     * Initialize the chess board with starting positions
     */
    initializeBoard(boardState) {
        console.log('[Chess3DBoard] Initializing 3D chess board with pieces');
        
        // Clear any existing pieces
        this.clearBoard();
        
        // Create pieces based on board state
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = boardState[x][y];
                if (piece) {
                    this.createPiece3D(piece.type, piece.color, x, y);
                }
            }
        }
        
        console.log(`[Chess3DBoard] Created ${this.pieces3D.size} 3D chess pieces`);
    }
    
    /**
     * Move a piece from one position to another
     */
    movePiece(fromX, fromY, toX, toY) {
        const fromKey = `${fromX},${fromY}`;
        const toKey = `${toX},${toY}`;
        
        const piece3D = this.pieces3D.get(fromKey);
        if (!piece3D) {
            console.warn(`[Chess3DBoard] No piece found at (${fromX},${fromY})`);
            return false;
        }
        
        // Remove captured piece if present
        const capturedPiece = this.pieces3D.get(toKey);
        if (capturedPiece) {
            this.removePiece(toX, toY);
        }
        
        // Move the piece
        const newWorldPos = this.chessTo3D(toX, toY);
        piece3D.position.set(newWorldPos.x, newWorldPos.y, newWorldPos.z);
        
        // Update metadata
        piece3D.userData.chessX = toX;
        piece3D.userData.chessY = toY;
        
        // Update tracking
        this.pieces3D.delete(fromKey);
        this.pieces3D.set(toKey, piece3D);
        
        console.log(`[Chess3DBoard] Moved piece from (${fromX},${fromY}) to (${toX},${toY})`);
        return true;
    }
    
    /**
     * Remove a piece from the board
     */
    removePiece(chessX, chessY) {
        const key = `${chessX},${chessY}`;
        const piece3D = this.pieces3D.get(key);
        
        if (piece3D) {
            this.scene.remove(piece3D);
            this.pieces3D.delete(key);
            console.log(`[Chess3DBoard] Removed piece at (${chessX},${chessY})`);
            return true;
        }
        
        return false;
    }
    
    /**
     * Clear all pieces from the board
     */
    clearBoard() {
        for (const piece3D of this.pieces3D.values()) {
            this.scene.remove(piece3D);
        }
        this.pieces3D.clear();
        console.log('[Chess3DBoard] Cleared all pieces from board');
    }
    
    /**
     * Highlight a selected square
     */
    highlightSquare(chessX, chessY) {
        if (chessX >= 0 && chessX < 8 && chessY >= 0 && chessY < 8) {
            const worldPos = this.chessTo3D(chessX, chessY);
            this.selectedSquareIndicator.position.set(
                worldPos.x, 
                worldPos.y - VOXEL_SIZE * 2, 
                worldPos.z
            );
            this.selectedSquareIndicator.visible = true;
            
            console.log(`[Chess3DBoard] Highlighted square (${chessX},${chessY})`);
        } else {
            this.selectedSquareIndicator.visible = false;
        }
    }
    
    /**
     * Show valid moves as glowing indicators
     */
    showValidMoves(moves) {
        // Clear existing indicators
        this.clearValidMoveIndicators();
        
        for (const move of moves) {
            const worldPos = this.chessTo3D(move.x, move.y);
            
            // Create move indicator
            const indicatorGeometry = new THREE.RingGeometry(
                this.squareSize * 0.2, 
                this.squareSize * 0.4, 
                8
            );
            const indicatorMaterial = new THREE.MeshBasicMaterial({
                color: 0x00FF00,
                transparent: true,
                opacity: 0.7,
                side: THREE.DoubleSide
            });
            
            const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            indicator.position.set(worldPos.x, worldPos.y - VOXEL_SIZE, worldPos.z);
            indicator.rotation.x = -Math.PI / 2; // Lay flat on board
            
            this.scene.add(indicator);
            this.validMoveIndicators.push(indicator);
        }
        
        console.log(`[Chess3DBoard] Showing ${moves.length} valid moves`);
    }
    
    /**
     * Clear valid move indicators
     */
    clearValidMoveIndicators() {
        for (const indicator of this.validMoveIndicators) {
            this.scene.remove(indicator);
        }
        this.validMoveIndicators = [];
    }
    
    /**
     * Clear all visual indicators
     */
    clearAllIndicators() {
        this.selectedSquareIndicator.visible = false;
        this.clearValidMoveIndicators();
    }
    
    /**
     * Get piece at chess coordinates
     */
    getPieceAt(chessX, chessY) {
        return this.pieces3D.get(`${chessX},${chessY}`);
    }
    
    /**
     * Handle piece selection
     */
    selectPiece(chessX, chessY) {
        this.selectedPiece = this.getPieceAt(chessX, chessY);
        this.highlightSquare(chessX, chessY);
        
        if (this.selectedPiece) {
            console.log(`[Chess3DBoard] Selected ${this.selectedPiece.userData.chessColor} ${this.selectedPiece.userData.chessPiece} at (${chessX},${chessY})`);
        }
        
        return this.selectedPiece;
    }
    
    /**
     * Clear selection
     */
    clearSelection() {
        this.selectedPiece = null;
        this.clearAllIndicators();
    }
    
    /**
     * Cleanup - remove all 3D elements
     */
    cleanup() {
        this.clearBoard();
        this.clearAllIndicators();
        
        if (this.selectedSquareIndicator) {
            this.scene.remove(this.selectedSquareIndicator);
        }
        
        console.log('[Chess3DBoard] Cleaned up 3D chess board');
    }
}