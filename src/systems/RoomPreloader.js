import { generateRoomVisuals } from '../scenes/roomGenerator.js';
import { convertToInstancedMeshes } from './SimpleInstanceConverter.js';
import { roomMemoryManager } from './RoomMemoryManager.js';

/**
 * RoomPreloader - Preloads neighboring rooms for instant transitions
 */
export class RoomPreloader {
    constructor(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
        this.preloadDistance = 1; // Preload rooms 1 connection away
        this.maxPreloadedRooms = 4; // Limit memory usage
        this.isPreloading = false;
        this.preloadQueue = [];
    }

    /**
     * Start preloading neighbors of the current room
     */
    async preloadNeighbors(currentRoomId) {
        if (this.isPreloading) return;
        
        // Check if dungeonData exists
        if (!this.dungeonHandler.dungeonData || !this.dungeonHandler.dungeonData.rooms) {
            console.warn('[RoomPreloader] Dungeon data not available yet');
            return;
        }
        
        const currentRoom = this.dungeonHandler.dungeonData.rooms.get(currentRoomId);
        if (!currentRoom) return;
        
        // Get connected rooms
        const connectedRoomIds = [];
        if (currentRoom.connections.north > -1) connectedRoomIds.push(currentRoom.connections.north);
        if (currentRoom.connections.south > -1) connectedRoomIds.push(currentRoom.connections.south);
        if (currentRoom.connections.east > -1) connectedRoomIds.push(currentRoom.connections.east);
        if (currentRoom.connections.west > -1) connectedRoomIds.push(currentRoom.connections.west);
        
        // Filter out already loaded rooms
        const roomsToPreload = connectedRoomIds.filter(id => 
            !this.dungeonHandler.preLoadedRooms.has(id) && 
            id !== currentRoomId
        );
        
        if (roomsToPreload.length === 0) {
            console.log('[RoomPreloader] All neighboring rooms already loaded');
            return;
        }
        
        console.log(`[RoomPreloader] 🔄 Starting to preload ${roomsToPreload.length} neighboring rooms:`, roomsToPreload);
        this.isPreloading = true;
        
        let successCount = 0;
        
        // Preload rooms one by one to avoid performance hit
        for (const roomId of roomsToPreload) {
            const success = await this.preloadRoom(roomId);
            if (success) successCount++;
            
            // Clean up old preloaded rooms if we exceed the limit
            if (this.dungeonHandler.preLoadedRooms.size > this.maxPreloadedRooms) {
                this.cleanupOldestPreloadedRoom(currentRoomId, roomsToPreload);
            }
        }
        
        this.isPreloading = false;
        console.log(`[RoomPreloader] ✅ Finished preloading neighbors - ${successCount}/${roomsToPreload.length} successful`);
    }

    /**
     * Preload a single room
     */
    async preloadRoom(roomId) {
        try {
            // Check if dungeonData exists
            if (!this.dungeonHandler.dungeonData || !this.dungeonHandler.dungeonData.rooms) {
                console.warn('[RoomPreloader] Dungeon data not available for preloading');
                return;
            }
            
            const roomData = this.dungeonHandler.dungeonData.rooms.get(roomId);
            if (!roomData) return;
            
            console.log(`[RoomPreloader] Preloading room ${roomId}...`);
            
            // Generate room visuals (this is the expensive part)
            const result = generateRoomVisuals(roomData, this.dungeonHandler.currentArea);
            
            if (result.roomGroup) {
                // Apply instance optimization
                convertToInstancedMeshes(result.roomGroup);
                
                // Store in preloaded rooms map
                this.dungeonHandler.preLoadedRooms.set(roomId, {
                    roomGroup: result.roomGroup,
                    collisionMeshes: result.collisionMeshes,
                    lights: result.lights,
                    boundingBox: result.boundingBox,
                    doorCenterPoints: result.doorCenterPoints,
                    validFloorPositions: result.validFloorPositions
                });
                
                // Check memory usage
                const memoryStats = roomMemoryManager.estimateRoomMemory(result.roomGroup);
                console.log(`[RoomPreloader] ✅ Successfully preloaded room ${roomId} (${memoryStats.totalMB.toFixed(1)} MB)`);
                return true; // Success
            }
        } catch (error) {
            console.error(`[RoomPreloader] ❌ Failed to preload room ${roomId}:`, error);
            return false; // Failure
        }
        
        return false; // No room data
    }

    /**
     * Remove the oldest preloaded room that's not connected to current room
     */
    cleanupOldestPreloadedRoom(currentRoomId, keepRoomIds) {
        let oldestRoomId = null;
        
        // Find a preloaded room that's not in the keep list
        for (const [roomId, data] of this.dungeonHandler.preLoadedRooms) {
            if (roomId !== currentRoomId && !keepRoomIds.includes(roomId)) {
                oldestRoomId = roomId;
                break;
            }
        }
        
        if (oldestRoomId) {
            console.log(`[RoomPreloader] Cleaning up old preloaded room ${oldestRoomId}`);
            const preLoadData = this.dungeonHandler.preLoadedRooms.get(oldestRoomId);
            
            // Dispose of the room group properly
            if (preLoadData.roomGroup) {
                roomMemoryManager.cleanupRoomMemory(preLoadData.roomGroup, false); // Don't preserve state for preloaded rooms
            }
            
            this.dungeonHandler.preLoadedRooms.delete(oldestRoomId);
        }
    }

    /**
     * Start preloading in the background with a delay
     */
    startDelayedPreload(currentRoomId, delay = 1000) {
        // Extra safety check before scheduling
        if (!this.dungeonHandler.dungeonData) {
            console.log('[RoomPreloader] Skipping preload - dungeon not ready');
            return;
        }
        
        setTimeout(() => {
            this.preloadNeighbors(currentRoomId);
        }, delay);
    }
}

// Export singleton for easy access
export let roomPreloader = null;

export function initializeRoomPreloader(dungeonHandler) {
    roomPreloader = new RoomPreloader(dungeonHandler);
    return roomPreloader;
}