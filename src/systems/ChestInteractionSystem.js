import * as THREE from 'three';
import { lootPoolManager } from './LootPoolManager.js';
import { ITEM_CATEGORY } from '../entities/ItemTypes.js';

/**
 * Chest Interaction System
 * Handles chest opening animations, item display, and player interactions
 */

export class ChestInteractionSystem {
    constructor(scene, camera, dialogueSystem, audioManager, playerController = null, weaponSystem = null, cardSystem = null) {
        this.scene = scene;
        this.camera = camera;
        this.dialogueSystem = dialogueSystem;
        this.audioManager = audioManager;
        this.playerController = playerController;
        this.weaponSystem = weaponSystem;
        this.cardSystem = cardSystem;

        // Interaction state
        this.nearbyChests = new Set();
        this.activeChest = null;
        this.isAnimating = false;
        this.currentItem = null;
        this.currentItemModel = null;

        // Animation properties
        this.animationMixers = new Map();
        this.clock = new THREE.Clock();

        // Interaction range
        this.interactionRange = 3.0; // Same as door interaction range

        // Player movement state
        this.playerMovementDisabled = false;

        // Mobile dialogue state
        this.isMobileDialogueActive = false;

        console.log('[ChestInteractionSystem] Initialized');
    }

    /**
     * Update the chest interaction system
     * @param {THREE.Vector3} playerPosition - Current player position
     * @param {object} inputState - Current input state
     */
    update(playerPosition, inputState) {
        // Update animation mixers
        const delta = this.clock.getDelta();
        this.animationMixers.forEach(mixer => mixer.update(delta));
        
        // Update item floating animation
        if (this.currentItemModel && this.currentItemModel.userData.isFloating) {
            this.updateItemFloatingAnimation();
        }
        
        // Check for nearby chests
        this.updateNearbyChests(playerPosition);
        
        // Handle interaction input
        if (inputState.interact && !this.isAnimating && this.nearbyChests.size > 0) {
            this.handleChestInteraction();
        }
    }

    /**
     * Update nearby chests and interactable objects detection
     * @param {THREE.Vector3} playerPosition - Current player position
     */
    updateNearbyChests(playerPosition) {
        const previousNearbyCount = this.nearbyChests.size;
        this.nearbyChests.clear();

        // Find all interactable objects in the scene (chests, fishing rods, etc.)
        this.scene.traverse(object => {
            // Check for treasure chests
            if (object.userData?.objectType === 'treasure_chest' &&
                object.userData?.isInteractable &&
                !object.userData?.isEmptied) {

                const distance = playerPosition.distanceTo(object.position);
                if (distance <= this.interactionRange) {
                    this.nearbyChests.add(object);
                }
            }
            // Check for event objects (fishing rods, crystals, etc.)
            else if ((object.userData?.objectType === 'fishing_rod' ||
                      object.userData?.objectType === 'mysterious_crystal') &&
                     object.userData?.isInteractable &&
                     object.userData?.isEventObject) {

                const distance = playerPosition.distanceTo(object.position);
                if (distance <= this.interactionRange) {
                    this.nearbyChests.add(object);
                }
            }
            // Check for other event objects with generic interaction (but skip decorative objects)
            else if (object.userData?.isEventObject &&
                     object.userData?.isInteractable &&
                     !object.userData?.isUsed &&
                     !object.userData?.isDecorative) {

                const distance = playerPosition.distanceTo(object.position);
                if (distance <= this.interactionRange) {
                    this.nearbyChests.add(object);
                }
            }
        });

        // Show/hide interaction prompt
        if (this.nearbyChests.size > 0 && previousNearbyCount === 0) {
            this.showInteractionPrompt();
        } else if (this.nearbyChests.size === 0 && previousNearbyCount > 0) {
            this.hideInteractionPrompt();
        }
    }

    /**
     * Handle chest and object interaction
     */
    async handleChestInteraction() {
        if (this.nearbyChests.size === 0 || this.isAnimating) return;

        // Get the nearest interactable object
        const interactableObject = Array.from(this.nearbyChests)[0];

        // Check if it's already been used/emptied
        if (interactableObject.userData.isEmptied ||
            interactableObject.userData.isUsed ||
            !interactableObject.userData.isInteractable) {
            console.log('[ChestInteractionSystem] Object already used, ignoring interaction');
            return;
        }

        this.activeChest = interactableObject;
        this.isAnimating = true;

        // Disable player movement immediately when interaction starts
        this.disablePlayerMovement();

        try {
            // Hide interaction prompt
            this.hideInteractionPrompt();

            // Handle different object types
            if (interactableObject.userData.objectType === 'treasure_chest') {
                console.log('[ChestInteractionSystem] Opening chest:', interactableObject.userData.chestId);

                // Play chest opening sound
                this.playChestOpenSound();

                // Animate chest opening
                await this.animateChestOpening(interactableObject);

                // Generate and display loot
                await this.generateAndDisplayLoot(interactableObject);

                // Show dialogue with item options
                await this.showLootDialogue();

            } else if (interactableObject.userData.objectType === 'fishing_rod' ||
                       interactableObject.userData.objectType === 'mysterious_crystal' ||
                       interactableObject.userData.isEventObject) {
                console.log('[ChestInteractionSystem] Interacting with event object:', interactableObject.userData.objectType);

                // Handle event object interaction
                await this.handleEventObjectInteraction(interactableObject);
            }

        } catch (error) {
            console.error('[ChestInteractionSystem] Error during interaction:', error);
            this.isAnimating = false;
        }
    }

    /**
     * Animate chest opening
     * @param {THREE.Object3D} chest - The chest object
     */
    async animateChestOpening(chest) {
        return new Promise((resolve) => {
            const lidGroup = chest.userData.lidGroup;
            if (!lidGroup) {
                console.warn('[ChestInteractionSystem] No lid group found for chest');
                resolve();
                return;
            }

            // Create opening animation (rotate lid upward from front hinge)
            const startRotation = lidGroup.rotation.x;
            const endRotation = -Math.PI * 0.55; // 99 degrees open (slightly more than 90 degrees for natural look)
            const duration = 1000; // 1 second
            const startTime = Date.now();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Smooth easing function
                const easeProgress = 1 - Math.pow(1 - progress, 3);
                
                lidGroup.rotation.x = startRotation + (endRotation - startRotation) * easeProgress;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    chest.userData.isOpened = true;
                    // Save state when chest is opened
                    this.saveChestState(chest);
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Generate and display loot from chest
     * @param {THREE.Object3D} chest - The chest object
     */
    async generateAndDisplayLoot(chest) {
        // Generate loot using the loot pool manager
        const chestType = chest.userData.chestType || 'normal_chest';
        const seed = this.generateChestSeed(chest);

        // Create context for loot generation
        const context = {
            biome: 'catacombs', // TODO: Get from current area/biome
            roomType: 'normal', // TODO: Get from current room type
            playerLevel: 1, // TODO: Get from player progression
            rarityBoost: chestType === 'rare_chest' ? 2.0 : 1.0 // Boost rare items in rare chests
        };

        this.currentItem = lootPoolManager.generateLoot(chestType, seed, context);
        
        if (!this.currentItem) {
            console.warn('[ChestInteractionSystem] No loot generated for chest');
            return;
        }

        console.log('[ChestInteractionSystem] Generated loot:', this.currentItem);

        // Create 3D model of the item
        this.currentItemModel = this.currentItem.create3DModel();
        
        // Position item DEEP inside chest initially (to avoid lid collision)
        const chestPosition = chest.position.clone();
        this.currentItemModel.position.copy(chestPosition);
        this.currentItemModel.position.y -= 0.5; // Start deep inside chest

        // Start with smaller scale (will grow as it emerges)
        this.currentItemModel.scale.setScalar(0.7);

        // Add to scene
        this.scene.add(this.currentItemModel);

        // Animate item emerging from chest (rising up and scaling up)
        await this.animateItemEmerging();
        this.startItemFloating();
    }

    /**
     * Animate item emerging from chest (rising up and scaling up)
     */
    async animateItemEmerging() {
        return new Promise((resolve) => {
            if (!this.currentItemModel) {
                resolve();
                return;
            }

            console.log('[ChestInteractionSystem] Starting item emergence animation');

            const startY = this.currentItemModel.position.y; // Deep inside chest
            const endY = startY + 2.5; // Rise 2.5 units to floating position (more dramatic)
            const startScale = 0.7; // Starting scale (smaller)
            const endScale = 1.0; // Final scale (normal size)
            const duration = 1000; // 1 second for dramatic effect
            const startTime = Date.now();

            const animate = () => {
                if (!this.currentItemModel) {
                    resolve();
                    return;
                }

                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Smooth easing with slight bounce at the end
                const easeProgress = progress < 0.8 ?
                    1 - Math.pow(1 - progress / 0.8, 2) : // Ease out for first 80%
                    1 + Math.sin((progress - 0.8) * Math.PI * 5) * 0.05 * (1 - progress); // Slight bounce for last 20%

                // Animate position (rising)
                this.currentItemModel.position.y = startY + (endY - startY) * easeProgress;

                // Animate scale (growing)
                const currentScale = startScale + (endScale - startScale) * easeProgress;
                this.currentItemModel.scale.setScalar(currentScale);

                // Add gentle rotation during emergence
                this.currentItemModel.rotation.y += 0.02;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    console.log('[ChestInteractionSystem] Item emergence animation completed');
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Start item floating animation
     */
    startItemFloating() {
        if (!this.currentItemModel) return;
        
        this.currentItemModel.userData.isFloating = true;
        this.currentItemModel.userData.floatStartY = this.currentItemModel.position.y;
        this.currentItemModel.userData.floatTime = 0;
    }

    /**
     * Update item floating animation
     */
    updateItemFloatingAnimation() {
        if (!this.currentItemModel) return;
        
        this.currentItemModel.userData.floatTime += 0.02;
        
        // Gentle bobbing motion
        const bobAmount = 0.1;
        const bobSpeed = 2.0;
        this.currentItemModel.position.y = this.currentItemModel.userData.floatStartY + 
            Math.sin(this.currentItemModel.userData.floatTime * bobSpeed) * bobAmount;
        
        // Gentle rotation
        this.currentItemModel.rotation.y += 0.01;
    }

    /**
     * Show loot dialogue with weapon replacement options
     */
    async showLootDialogue() {
        if (!this.currentItem) return;

        const itemData = this.currentItem.itemData;

        // Check item type
        const isWeapon = itemData.type === 'weapon' || itemData.category === 'weapon';
        const isCard = itemData.category === ITEM_CATEGORY.CARD;

        let dialogueText = `${itemData.name}\n\n${itemData.description}`;

        // Cards have no additional information to display

        // Determine take text based on item type
        let takeText = 'Take Item';
        if (isWeapon && this.weaponSystem) {
            const currentWeapon = this.weaponSystem.getCurrentWeapon();
            const currentWeaponName = currentWeapon ? currentWeapon.name : 'None';
            takeText = `Take Item (replaces ${currentWeaponName})`;
        } else if (isCard && this.cardSystem) {
            const handStatus = this.cardSystem.getHandStatus();
            if (handStatus.cardCount >= handStatus.maxCards) {
                takeText = `Take Card (hand full: ${handStatus.cardCount}/${handStatus.maxCards})`;
            } else {
                takeText = `Take Card (${handStatus.cardCount + 1}/${handStatus.maxCards})`;
            }
        }

        const options = [
            { text: takeText, value: 'take' },
            { text: 'Leave Item', value: 'leave' }
        ];

        console.log('[ChestInteractionSystem] Showing loot dialogue');

        // Set mobile dialogue active flag
        this.isMobileDialogueActive = true;

        try {
            // Create simple dialogue interface for dungeon
            const choice = await this.showSimpleDialogue(dialogueText, options);

            if (choice === 'take' || choice === 'replace') {
                await this.handleItemTaken();
            } else {
                await this.handleItemLeft();
            }
        } finally {
            // Always re-enable player movement when dialogue ends
            this.enablePlayerMovement();
            this.isAnimating = false;

            // Clear mobile dialogue active flag
            this.isMobileDialogueActive = false;
        }
    }

    /**
     * Handle item being taken by player
     */
    async handleItemTaken() {
        console.log('[ChestInteractionSystem] Player took item:', this.currentItem.itemData.name);

        // Play pickup sound
        this.playItemPickupSound();

        // Check item type and handle accordingly
        const itemData = this.currentItem.itemData;
        const isWeapon = itemData.type === 'weapon' || itemData.category === 'weapon';
        const isCard = itemData.category === ITEM_CATEGORY.CARD;

        if (isWeapon && this.weaponSystem) {
            // Create weapon data from item
            const weaponData = {
                id: itemData.id,
                name: itemData.name,
                type: itemData.weaponType || 'melee',
                description: itemData.description,
                damage: itemData.damage || 1,
                range: itemData.range || 3,
                attackSpeed: itemData.attackSpeed || 1.0,
                model: this.currentItemModel ? this.currentItemModel.clone() : null // Clone the 3D model from the chest
            };

            // Equip the new weapon (this will replace current weapon)
            this.weaponSystem.equipWeapon(weaponData);
            console.log('[ChestInteractionSystem] Equipped weapon:', weaponData.name);
        } else if (isCard && this.cardSystem) {
            // Add card to player's hand
            const success = this.cardSystem.addCard(itemData.id || itemData.type);
            if (success) {
                console.log('[ChestInteractionSystem] Added card to hand:', itemData.name);
            } else {
                console.warn('[ChestInteractionSystem] Failed to add card - hand may be full');
                // TODO: Show "hand full" message to player
            }
        }

        // Animate item disappearing
        await this.animateItemPickup(this.currentItemModel);
        
        // Clear reference to the item model since it's been removed
        this.currentItemModel = null;

        // Add to player inventory (TODO: implement inventory system for non-weapons)
        // this.addToInventory(this.currentItem);

        // Check if this is an event room chest and trigger event mechanics
        if (this.activeChest && this.activeChest.userData.isEventChest) {
            console.log('[ChestInteractionSystem] Event chest detected, triggering event mechanics');
            this.triggerEventChestMechanics(this.activeChest);
        }

        // Mark chest as permanently emptied and non-interactable BEFORE cleanup
        if (this.activeChest) {
            this.activeChest.userData.isEmptied = true;
            this.activeChest.userData.isInteractable = false;
            this.activeChest.userData.isOpened = true; // Keep chest visually open when emptied
            console.log('[ChestInteractionSystem] Chest marked as emptied and non-interactable');

            // Also mark the chest as non-interactable at the object level
            this.activeChest.userData.canInteract = false;

            // Save chest state for persistence across room transitions
            this.saveChestState(this.activeChest);
        }

        // Clean up (this sets activeChest to null)
        this.cleanupCurrentItem();
    }

    /**
     * Handle item being left in chest
     */
    async handleItemLeft() {
        console.log('[ChestInteractionSystem] Player left item in chest - starting return animation');
        console.log('[ChestInteractionSystem] Current item model exists:', !!this.currentItemModel);
        console.log('[ChestInteractionSystem] Active chest exists:', !!this.activeChest);

        if (!this.currentItemModel) {
            console.error('[ChestInteractionSystem] No item model to animate back!');
            return;
        }

        // First: Animate item sinking back into chest
        console.log('[ChestInteractionSystem] Starting item return animation...');
        await this.animateItemReturn();

        // Then: Close the chest lid after item is inside
        console.log('[ChestInteractionSystem] Item is back inside, closing chest...');
        await this.animateChestClosing();

        console.log('[ChestInteractionSystem] Animations completed, cleaning up...');

        // Clean up item model after animation completes
        if (this.currentItemModel) {
            this.scene.remove(this.currentItemModel);
            this.currentItemModel = null;
            console.log('[ChestInteractionSystem] Item model removed from scene');
        }
        this.currentItem = null;

        // Reset chest state for reopening - IMPORTANT: Don't reset activeChest to null
        this.activeChest.userData.isOpened = false;

        // Save chest state (closed but still interactable)
        this.saveChestState(this.activeChest);

        // Reset animation state but keep chest reference for potential reopening
        this.isAnimating = false;

        console.log('[ChestInteractionSystem] Chest reset and ready for reopening');
    }

    /**
     * Animate item pickup (disappearing)
     */
    async animateItemPickup() {
        return new Promise((resolve) => {
            const duration = 500;
            const startTime = Date.now();
            const startScale = this.currentItemModel.scale.clone();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const scale = 1 - progress;
                this.currentItemModel.scale.setScalar(scale);
                this.currentItemModel.position.y += 0.02; // Rise while shrinking

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Generate a consistent seed for chest loot
     * @param {THREE.Object3D} chest - The chest object
     * @returns {number} Seed value
     */
    generateChestSeed(chest) {
        const pos = chest.position;
        const chestId = chest.userData.chestId || 'unknown';

        // For testing: use a more random seed that changes each time
        // In production, you might want this to be more deterministic
        const basePositionSeed = Math.abs(Math.floor(pos.x * 1000 + pos.y * 100 + pos.z * 10000));
        const chestIdSeed = this.hashString(chestId);
        const randomFactor = Math.floor(Math.random() * 1000000); // Add true randomness for testing

        const finalSeed = basePositionSeed + chestIdSeed + randomFactor;
        console.log(`[ChestSeed] Position: ${basePositionSeed}, ChestID: ${chestIdSeed}, Random: ${randomFactor}, Final: ${finalSeed}`);

        return finalSeed;
    }

    /**
     * Simple string hash function for chest ID
     * @param {string} str - String to hash
     * @returns {number} Hash value
     */
    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Play chest opening sound
     */
    playChestOpenSound() {
        // TODO: Implement audio system integration
        console.log('[ChestInteractionSystem] Playing chest open sound');
    }

    /**
     * Play item pickup sound
     */
    playItemPickupSound() {
        // TODO: Implement audio system integration
        console.log('[ChestInteractionSystem] Playing item pickup sound');
    }

    /**
     * Show dialogue interface matching fire intro scene design
     */
    async showSimpleDialogue(text, options) {
        return new Promise((resolve) => {
            let selectedIndex = 0;
            let isResolved = false;

            // Mobile input state tracking
            let lastLeftStickY = 0;
            let lastRightStickTap = false;

            // Declare variables that need to be accessible throughout the function
            let mobileInputInterval = null;
            let enhancedKeyHandler = null;

            // Create dialogue container matching fire intro scene
            const dialogueContainer = document.createElement('div');
            dialogueContainer.style.cssText = `
                position: absolute;
                bottom: 10%;
                left: 50%;
                transform: translateX(-50%);
                width: 80%;
                max-width: 600px;
                padding: 20px;
                background-color: rgba(0, 0, 0, 0.8);
                border: 4px solid #fff;
                border-radius: 5px;
                color: #fff;
                font-family: 'Courier New', Courier, monospace;
                font-size: 20px;
                line-height: 1.4;
                z-index: 1000;
                opacity: 1;
            `;

            // Add text element
            const textElement = document.createElement('p');
            textElement.style.cssText = `
                margin: 0;
                padding: 0;
                white-space: pre-wrap;
            `;
            textElement.textContent = text;
            dialogueContainer.appendChild(textElement);

            // Add options container
            const optionsContainer = document.createElement('div');
            optionsContainer.style.cssText = `
                margin-top: 15px;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
            `;

            const buttons = [];

            options.forEach((option, index) => {
                const button = document.createElement('button');
                button.className = 'dialogue-option-button';
                button.style.cssText = `
                    background: none;
                    border: 2px solid #fff;
                    border-radius: 3px;
                    color: #ffff00;
                    font-family: 'Courier New', Courier, monospace;
                    font-size: 18px;
                    padding: 5px 10px;
                    margin-bottom: 8px;
                    cursor: pointer;
                    text-align: left;
                    transition: background-color 0.2s, color 0.2s;
                    outline: none;
                `;
                button.textContent = `* ${option.text}`;

                const selectOption = () => {
                    if (isResolved) return;
                    isResolved = true;
                    // CRITICAL FIX: Clean up mobile interval when resolved via mouse click
                    if (mobileInputInterval) {
                        clearInterval(mobileInputInterval);
                    }
                    document.removeEventListener('keydown', enhancedKeyHandler, true);
                    if (document.body.contains(dialogueContainer)) {
                        document.body.removeChild(dialogueContainer);
                    }
                    resolve(option.value);
                };

                button.addEventListener('mouseenter', () => {
                    selectedIndex = index;
                    updateHighlight();
                });

                button.addEventListener('click', selectOption);

                buttons.push(button);
                optionsContainer.appendChild(button);
            });

            // Update highlight function
            const updateHighlight = () => {
                buttons.forEach((button, index) => {
                    if (index === selectedIndex) {
                        button.style.backgroundColor = '#fff';
                        button.style.color = '#000';
                        button.classList.add('highlighted');
                    } else {
                        button.style.backgroundColor = 'transparent';
                        button.style.color = '#ffff00';
                        button.classList.remove('highlighted');
                    }
                });
            };

            // Keyboard navigation
            const keyHandler = (event) => {
                if (isResolved) return;

                // Prevent all movement keys during dialogue
                const movementKeys = ['w', 'W', 'a', 'A', 's', 'S', 'd', 'D', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];

                switch(event.key) {
                    case 'ArrowUp':
                    case 'w':
                    case 'W':
                        event.preventDefault();
                        event.stopPropagation();
                        selectedIndex = Math.max(0, selectedIndex - 1);
                        updateHighlight();
                        break;
                    case 'ArrowDown':
                    case 's':
                    case 'S':
                        event.preventDefault();
                        event.stopPropagation();
                        selectedIndex = Math.min(options.length - 1, selectedIndex + 1);
                        updateHighlight();
                        break;
                    case 'Enter':
                    case ' ':
                        event.preventDefault();
                        event.stopPropagation();
                        if (isResolved) return;
                        isResolved = true;
                        document.removeEventListener('keydown', keyHandler, true);
                        document.body.removeChild(dialogueContainer);
                        resolve(options[selectedIndex].value);
                        break;
                    default:
                        // Block all other movement keys
                        if (movementKeys.includes(event.key)) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        break;
                }
            };

            dialogueContainer.appendChild(optionsContainer);
            document.body.appendChild(dialogueContainer);

            // Mobile input handler for joystick navigation
            const mobileInputHandler = () => {
                if (isResolved) return;

                // Get mobile input from SceneManager if available
                const sceneManager = this.playerController?.dungeonHandler?.sceneManager;
                if (!sceneManager) return;

                const leftStickData = sceneManager.leftStickData;
                const rightStickTapFlag = sceneManager.rightStickTapFlag;

                // Handle left stick navigation (up/down)
                if (leftStickData && leftStickData.active) {
                    const currentY = leftStickData.vector.y;
                    const threshold = 0.5;

                    // Check for up movement
                    if (currentY > threshold && lastLeftStickY <= threshold) {
                        selectedIndex = Math.max(0, selectedIndex - 1);
                        updateHighlight();
                        lastLeftStickY = currentY;
                    }
                    // Check for down movement
                    else if (currentY < -threshold && lastLeftStickY >= -threshold) {
                        selectedIndex = Math.min(options.length - 1, selectedIndex + 1);
                        updateHighlight();
                        lastLeftStickY = currentY;
                    }
                    // Reset when stick returns to center
                    else if (Math.abs(currentY) < threshold) {
                        lastLeftStickY = currentY;
                    }
                }

                // Handle right stick tap (selection)
                if (rightStickTapFlag && !lastRightStickTap) {
                    lastRightStickTap = true;
                    // Consume the tap flag immediately
                    sceneManager.rightStickTapFlag = false;

                    console.log('[ChestInteractionSystem] Right joystick tap detected - selecting option:', options[selectedIndex].text);

                    // Select current option
                    if (!isResolved) {
                        isResolved = true;
                        clearInterval(mobileInputInterval);
                        document.removeEventListener('keydown', keyHandler, true);
                        document.body.removeChild(dialogueContainer);
                        resolve(options[selectedIndex].value);
                    }
                } else if (!rightStickTapFlag) {
                    lastRightStickTap = false;
                }
            };

            // Start mobile input polling
            mobileInputInterval = setInterval(mobileInputHandler, 16); // ~60fps

            // CRITICAL FIX: Enhanced keyboard handler that properly cleans up mobile interval
            enhancedKeyHandler = (event) => {
                if (isResolved) return;

                // Call original key handler
                keyHandler(event);

                // If dialogue was resolved by keyboard, clean up mobile interval
                if (isResolved && mobileInputInterval) {
                    clearInterval(mobileInputInterval);
                    console.log('[ChestInteractionSystem] Cleaned up mobile input interval after keyboard resolution');
                }
            };

            // Set up keyboard controls with capture to intercept before PlayerController
            document.addEventListener('keydown', enhancedKeyHandler, true);

            // SAFETY: Add timeout to prevent infinite hanging
            const safetyTimeout = setTimeout(() => {
                if (!isResolved) {
                    console.warn('[ChestInteractionSystem] Dialogue safety timeout triggered - auto-resolving');
                    isResolved = true;
                    clearInterval(mobileInputInterval);
                    document.removeEventListener('keydown', enhancedKeyHandler, true);
                    if (document.body.contains(dialogueContainer)) {
                        document.body.removeChild(dialogueContainer);
                    }
                    resolve(options[0].value); // Default to first option
                }
            }, 30000); // 30 second safety timeout

            // Override the resolve function to clean up timeout
            const originalResolve = resolve;
            resolve = (value) => {
                if (safetyTimeout) {
                    clearTimeout(safetyTimeout);
                }
                originalResolve(value);
            };

            // Initialize highlight
            updateHighlight();

            // Focus container for keyboard events
            dialogueContainer.setAttribute('tabindex', '-1');
            dialogueContainer.focus();
        });
    }

    /**
     * Disable player movement
     */
    disablePlayerMovement() {
        if (this.playerController && !this.playerMovementDisabled) {
            this.playerController.disableMovement();
            this.playerMovementDisabled = true;
            console.log('[ChestInteractionSystem] Player movement disabled');
        }
    }

    /**
     * Enable player movement
     */
    enablePlayerMovement() {
        if (this.playerController && this.playerMovementDisabled) {
            this.playerController.enableMovement();
            this.playerMovementDisabled = false;
            console.log('[ChestInteractionSystem] Player movement enabled');
        }
    }

    /**
     * Show interaction prompt
     */
    showInteractionPrompt() {
        // TODO: Implement UI prompt
        console.log('[ChestInteractionSystem] Show interaction prompt: Press ENTER to open chest');
    }

    /**
     * Hide interaction prompt
     */
    hideInteractionPrompt() {
        // TODO: Implement UI prompt hiding
        console.log('[ChestInteractionSystem] Hide interaction prompt');
    }

    /**
     * Animate item returning to chest (exact reverse of emergence animation)
     */
    async animateItemReturn() {
        return new Promise((resolve) => {
            if (!this.currentItemModel || !this.activeChest) {
                console.warn('[ChestInteractionSystem] Missing item model or chest for return animation');
                resolve();
                return;
            }

            console.log('[ChestInteractionSystem] Starting item return animation (reverse emergence)');

            // Stop floating animation
            if (this.currentItemModel.userData) {
                this.currentItemModel.userData.isFloating = false;
            }

            const chestPosition = this.activeChest.position.clone();
            const targetY = chestPosition.y - 0.5; // Deep inside chest to avoid lid collision (same as emergence start)
            const startY = this.currentItemModel.position.y; // Current floating position
            const startScale = 1.0; // Current scale (normal size)
            const targetScale = 0.7; // Target scale (smaller, same as emergence start)
            const duration = 800; // Slightly faster than emergence for snappy return
            const startTime = Date.now();

            const animate = () => {
                // Check if item model still exists
                if (!this.currentItemModel) {
                    console.warn('[ChestInteractionSystem] Item model was removed during animation');
                    resolve();
                    return;
                }

                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Reverse easing - start slow, accelerate toward the end (opposite of emergence)
                const easeProgress = Math.pow(progress, 2);

                // Move item down into chest (reverse of rising)
                this.currentItemModel.position.y = startY + (targetY - startY) * easeProgress;

                // Scale down the item as it goes back (reverse of growing)
                const currentScale = startScale + (targetScale - startScale) * easeProgress;
                this.currentItemModel.scale.setScalar(currentScale);

                // Reverse rotation (slow down as it goes back)
                const rotationSpeed = 0.02 * (1 - progress);
                this.currentItemModel.rotation.y += rotationSpeed;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    console.log('[ChestInteractionSystem] Item return animation completed');
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Animate chest closing
     */
    async animateChestClosing() {
        return new Promise((resolve) => {
            const lidGroup = this.activeChest.userData.lidGroup;
            if (!lidGroup) {
                resolve();
                return;
            }

            const startRotation = lidGroup.rotation.x;
            const endRotation = 0; // Closed position
            const duration = 800;
            const startTime = Date.now();

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                const easeProgress = 1 - Math.pow(1 - progress, 2);
                lidGroup.rotation.x = startRotation + (endRotation - startRotation) * easeProgress;

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Clean up current item (for taking item)
     */
    cleanupCurrentItem() {
        if (this.currentItemModel) {
            this.scene.remove(this.currentItemModel);
            this.currentItemModel = null;
        }
        this.currentItem = null;
        this.activeChest = null; // Reset activeChest when item is taken
    }

    /**
     * Trigger event chest mechanics
     * @param {THREE.Object3D} chest - The event chest object
     */
    triggerEventChestMechanics(chest) {
        const eventMechanics = chest.userData.eventMechanics;
        const eventRoomId = chest.userData.eventRoomId;

        if (!eventMechanics || !eventRoomId) {
            console.warn('[ChestInteractionSystem] Missing event mechanics or room ID for event chest');
            return;
        }

        // Get the event room manager from dungeon handler
        const dungeonHandler = window.dungeonHandler;
        if (dungeonHandler && dungeonHandler.eventRoomManager) {
            console.log('[ChestInteractionSystem] Triggering event room mechanics');
            dungeonHandler.eventRoomManager.handleEventTrigger(
                eventRoomId,
                eventMechanics.triggerObjectId,
                { source: 'chest_interaction' }
            );
        } else {
            console.warn('[ChestInteractionSystem] Event room manager not available');
        }
    }

    /**
     * Save chest state for persistence across room transitions
     */
    saveChestState(chest) {
        // Get the dungeon handler to access the chest states map
        const dungeonHandler = this.playerController?.dungeonHandler;
        if (!dungeonHandler || !dungeonHandler.chestStates) {
            console.warn('[ChestInteractionSystem] Cannot save chest state - no dungeon handler or chest states map');
            return;
        }

        // Create state key using room ID and chest ID
        const roomId = dungeonHandler.currentRoomId;
        const chestStateKey = `${roomId}_${chest.userData.chestId}`;

        // Save current chest state
        const chestState = {
            isOpened: chest.userData.isOpened,
            isEmptied: chest.userData.isEmptied,
            isInteractable: chest.userData.isInteractable,
            roomId: roomId,
            chestId: chest.userData.chestId
        };

        dungeonHandler.chestStates.set(chestStateKey, chestState);
        console.log(`[ChestInteractionSystem] Saved chest state for ${chestStateKey}:`, chestState);
    }

    /**
     * Handle event object interaction (fishing rod, etc.)
     * @param {THREE.Object3D} eventObject - The event object to interact with
     */
    async handleEventObjectInteraction(eventObject) {
        console.log('[ChestInteractionSystem] Handling event object interaction');
        console.log('[ChestInteractionSystem] Event object userData:', eventObject.userData);

        try {
            // Mark object as used to prevent re-interaction (unless it should stay interactable)
            if (!eventObject.userData.staysInteractable) {
                eventObject.userData.isUsed = true;
            }

            // Check if this is an event room object
            if (eventObject.userData.isEventObject && eventObject.userData.eventRoomId) {
                console.log('[ChestInteractionSystem] Event object detected, triggering event mechanics');
                console.log('[ChestInteractionSystem] Event room ID:', eventObject.userData.eventRoomId);
                console.log('[ChestInteractionSystem] Trigger ID:', eventObject.userData.rodId || eventObject.userData.objectId);

                // Get the event room manager from dungeon handler
                const dungeonHandler = window.dungeonHandler;
                if (dungeonHandler && dungeonHandler.eventRoomManager) {
                    console.log('[ChestInteractionSystem] Event room manager found, using new object-based interaction...');

                    // Use new object-based interaction system
                    await dungeonHandler.eventRoomManager.handleEventRoomObjectInteraction(
                        eventObject,
                        { source: 'object_interaction', objectType: eventObject.userData.objectType }
                    );

                    console.log('[ChestInteractionSystem] Event object interaction completed');
                } else {
                    console.warn('[ChestInteractionSystem] Event room manager not available');
                    console.warn('[ChestInteractionSystem] dungeonHandler:', !!dungeonHandler);
                    console.warn('[ChestInteractionSystem] eventRoomManager:', !!dungeonHandler?.eventRoomManager);
                }
            } else {
                console.log('[ChestInteractionSystem] Generic event object interaction - no specific mechanics');
                console.log('[ChestInteractionSystem] isEventObject:', eventObject.userData.isEventObject);
                console.log('[ChestInteractionSystem] eventRoomId:', eventObject.userData.eventRoomId);
            }

        } catch (error) {
            console.error('[ChestInteractionSystem] Error during event object interaction:', error);
        } finally {
            // Re-enable player movement
            this.enablePlayerMovement();
            this.isAnimating = false;
        }
    }

    /**
     * Handle direct item interaction (items spawned without chests)
     * @param {THREE.Object3D} itemModel - The item model
     * @param {Object} lootItem - The loot item data
     * @param {Object} mockChestData - Mock chest data for compatibility
     * @param {THREE.Scene} scene - The scene
     */
    async handleDirectItemInteraction(itemModel, lootItem, mockChestData, scene) {
        console.log('[ChestInteractionSystem] 🎁 Handling direct item interaction');

        try {
            // Set up the current item context (use the same structure as regular chests)
            this.currentItem = lootItem;
            this.currentItemModel = itemModel;
            this.activeChest = mockChestData;

            // Start floating animation for the item
            this.startItemFloating(itemModel);

            // Show dialogue using the existing loot dialogue system
            await this.showLootDialogue();

        } catch (error) {
            console.error('[ChestInteractionSystem] Error handling direct item interaction:', error);
            
            // Fallback cleanup
            if (itemModel && itemModel.parent) {
                itemModel.parent.remove(itemModel);
            }
        }
    }

    /**
     * Animate item pickup (similar to existing but for direct items)
     */
    async animateItemPickup(itemModel) {
        return new Promise(resolve => {
            if (!itemModel) {
                console.warn('[ChestInteractionSystem] No item model provided for pickup animation');
                resolve();
                return;
            }
            
            const pickupDuration = 30; // frames
            let frame = 0;
            const startScale = itemModel.scale.x;
            const startY = itemModel.position.y;

            const animate = () => {
                frame++;
                const progress = frame / pickupDuration;
                const easeProgress = 1 - Math.pow(1 - progress, 2); // Ease out

                // Scale down while rising
                const scale = startScale * (1 - easeProgress);
                itemModel.scale.setScalar(Math.max(0.1, scale));
                
                // Rise up
                itemModel.position.y = startY + easeProgress * 2;
                
                // Faster rotation
                itemModel.rotation.y += 0.1;

                if (progress >= 1) {
                    // Remove the item model from the scene
                    if (itemModel.parent) {
                        itemModel.parent.remove(itemModel);
                    }
                    resolve();
                } else {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        });
    }

    /**
     * Animate item return (sink back into ground)
     */
    async animateItemReturn(itemModel) {
        return new Promise(resolve => {
            const returnDuration = 40; // frames
            let frame = 0;
            const startScale = itemModel.scale.x;
            const startY = itemModel.position.y;

            const animate = () => {
                frame++;
                const progress = frame / returnDuration;
                const easeProgress = Math.pow(progress, 2); // Ease in

                // Scale down while sinking
                const scale = startScale * (1 - easeProgress);
                itemModel.scale.setScalar(Math.max(0.1, scale));
                
                // Sink down
                itemModel.position.y = startY - easeProgress * 3;
                
                // Slower rotation
                itemModel.rotation.y += 0.02;

                if (progress >= 1) {
                    resolve();
                } else {
                    requestAnimationFrame(animate);
                }
            };

            animate();
        });
    }

    /**
     * Dispose of the chest interaction system
     */
    dispose() {
        this.cleanupCurrentItem();
        this.animationMixers.clear();
        this.nearbyChests.clear();
        console.log('[ChestInteractionSystem] Disposed');
    }
}
