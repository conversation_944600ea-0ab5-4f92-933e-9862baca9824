import * as THREE from 'three';

/**
 * Local Coordinate System for Room Debugging and Object Placement
 *
 * Provides consistent local coordinates for each room shape type that are:
 * - Independent of dungeon grid position
 * - Shape-aware (accounts for room geometry)
 * - Persistent across dungeon generations
 * - Suitable for precise object placement
 */

// Room world size constant (should match <PERSON>ngeonHandler)
const ROOM_WORLD_SIZE = 14;
const R = ROOM_WORLD_SIZE; // 14
const H = ROOM_WORLD_SIZE / 2; // 7

/**
 * Room shape definitions with their local coordinate systems
 * Origin (0,0,0) is at the center of each room shape at ground level
 */
const ROOM_SHAPE_DEFINITIONS = {
    'SQUARE_1X1': {
        name: 'Square 1x1',
        bounds: { minX: -R/2, maxX: R/2, minZ: -R/2, maxZ: R/2 },
        center: { x: 0, y: 0, z: 0 },
        description: 'Standard square room'
    },

    'SQUARE_2X2': {
        name: 'Square 2x2',
        bounds: { minX: -R, maxX: R, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'Large square room'
    },

    'RECTANGULAR': {
        name: 'Rectangular',
        bounds: { minX: -R, maxX: R, minZ: -R/2, maxZ: R/2 },
        center: { x: 0, y: 0, z: 0 },
        description: 'Rectangular room (2x1)'
    },

    'RECT_2X1': {
        name: 'Rectangle 2x1',
        bounds: { minX: -R, maxX: R, minZ: -R/2, maxZ: R/2 },
        center: { x: 0, y: 0, z: 0 },
        description: 'Rectangular room (2x1)'
    },

    'RECT_1X2': {
        name: 'Rectangle 1x2',
        bounds: { minX: -R/2, maxX: R/2, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'Rectangular room (1x2)'
    },

    'RECT_3X1': {
        name: 'Rectangle 3x1',
        bounds: { minX: -1.5*R, maxX: 1.5*R, minZ: -R/2, maxZ: R/2 },
        center: { x: 0, y: 0, z: 0 },
        description: 'Long rectangular room (3x1)'
    },

    'RECT_1X3': {
        name: 'Rectangle 1x3',
        bounds: { minX: -R/2, maxX: R/2, minZ: -1.5*R, maxZ: 1.5*R },
        center: { x: 0, y: 0, z: 0 },
        description: 'Tall rectangular room (1x3)'
    },

    'RECT_3X2': {
        name: 'Rectangle 3x2',
        bounds: { minX: -1.5*R, maxX: 1.5*R, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'Large rectangular room (3x2)'
    },

    'L_SHAPE': {
        name: 'L-Shape',
        bounds: { minX: -R, maxX: R, minZ: -R, maxZ: R },
        center: { x: -R/2, y: 0, z: R/2 }, // Offset to center of L
        description: 'L-shaped room',
        validAreas: [
            { minX: -R, maxX: 0, minZ: -R, maxZ: 0 }, // Top-left vertical part
            { minX: -R, maxX: R, minZ: 0, maxZ: R }    // Bottom horizontal part
        ]
    },

    'T_SHAPE': {
        name: 'T-Shape',
        bounds: { minX: -(R+H), maxX: R+H, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'T-shaped room',
        validAreas: [
            { minX: -(R+H), maxX: R+H, minZ: -R, maxZ: 0 }, // Top horizontal bar
            { minX: -H, maxX: H, minZ: 0, maxZ: R }          // Bottom vertical stem
        ]
    },

    'CROSS_SHAPE': {
        name: 'Cross-Shape',
        bounds: { minX: -(R+H), maxX: R+H, minZ: -(R+H), maxZ: R+H },
        center: { x: 0, y: 0, z: 0 },
        description: 'Cross-shaped room',
        validAreas: [
            { minX: -(R+H), maxX: R+H, minZ: -H, maxZ: H }, // Horizontal bar
            { minX: -H, maxX: H, minZ: -(R+H), maxZ: R+H }  // Vertical bar
        ]
    },

    'U_SHAPE_DOWN': {
        name: 'U-Shape (Down)',
        bounds: { minX: -(R+H), maxX: R+H, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'U-shaped room with opening at bottom',
        validAreas: [
            { minX: -(R+H), maxX: R+H, minZ: -R, maxZ: 0 }, // Top bar
            { minX: -(R+H), maxX: -H, minZ: 0, maxZ: R },    // Left leg
            { minX: H, maxX: R+H, minZ: 0, maxZ: R }         // Right leg
        ]
    },

    'U_SHAPE_UP': {
        name: 'U-Shape (Up)',
        bounds: { minX: -(R+H), maxX: R+H, minZ: -R, maxZ: R },
        center: { x: 0, y: 0, z: 0 },
        description: 'U-shaped room with opening at top',
        validAreas: [
            { minX: -(R+H), maxX: R+H, minZ: 0, maxZ: R },   // Bottom bar
            { minX: -(R+H), maxX: -H, minZ: -R, maxZ: 0 },   // Left leg
            { minX: H, maxX: R+H, minZ: -R, maxZ: 0 }        // Right leg
        ]
    },

    'U_SHAPE_LEFT': {
        name: 'U-Shape (Left)',
        bounds: { minX: -R, maxX: R, minZ: -(R+H), maxZ: R+H },
        center: { x: 0, y: 0, z: 0 },
        description: 'U-shaped room with opening at left',
        validAreas: [
            { minX: 0, maxX: R, minZ: -(R+H), maxZ: R+H },   // Right bar
            { minX: -R, maxX: 0, minZ: -(R+H), maxZ: -H },   // Top arm
            { minX: -R, maxX: 0, minZ: H, maxZ: R+H }        // Bottom arm
        ]
    },

    'U_SHAPE_RIGHT': {
        name: 'U-Shape (Right)',
        bounds: { minX: -R, maxX: R, minZ: -(R+H), maxZ: R+H },
        center: { x: 0, y: 0, z: 0 },
        description: 'U-shaped room with opening at right',
        validAreas: [
            { minX: -R, maxX: 0, minZ: -(R+H), maxZ: R+H },  // Left bar
            { minX: 0, maxX: R, minZ: -(R+H), maxZ: -H },    // Top arm
            { minX: 0, maxX: R, minZ: H, maxZ: R+H }         // Bottom arm
        ]
    },

    'CORRIDOR_LONG': {
        name: 'Long Corridor',
        bounds: { minX: -2*R, maxX: 2*R, minZ: -H, maxZ: H },
        center: { x: 0, y: 0, z: 0 },
        description: 'Long corridor (4x1)'
    },

    'CORRIDOR_SHORT': {
        name: 'Short Corridor',
        bounds: { minX: -R, maxX: R, minZ: -H, maxZ: H },
        center: { x: 0, y: 0, z: 0 },
        description: 'Short corridor (2x1)'
    },

    'BOSS_ARENA': {
        name: 'Boss Arena',
        bounds: { minX: -2*R, maxX: 2*R, minZ: -2*R, maxZ: 2*R },
        center: { x: 0, y: 0, z: 0 },
        description: 'Large boss arena (4x4)'
    }
};

export class LocalCoordinateSystem {
    constructor() {
        this.currentRoom = null;
        this.currentRoomShape = null;
        this.debugElement = null;
        this.isDebugVisible = false;

        this._createDebugDisplay();
    }

    /**
     * Create the debug display element
     * @private
     */
    _createDebugDisplay() {
        this.debugElement = document.createElement('div');
        this.debugElement.id = 'local-coordinates-debug';
        this.debugElement.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #00ff00;
            padding: 10px 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            z-index: 1000;
            display: none;
            text-align: center;
            border: 1px solid #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
        `;
        document.body.appendChild(this.debugElement);
    }

    /**
     * Set the current room for coordinate calculations
     * @param {Object} roomData - Room data object with id, shapeKey, etc.
     */
    setCurrentRoom(roomData) {
        this.currentRoom = roomData;
        this.currentRoomShape = roomData?.shapeKey || 'SQUARE_1X1';
    }

    /**
     * Convert world coordinates to local room coordinates
     * @param {THREE.Vector3} worldPosition - World position
     * @returns {Object} Local coordinates with validation info
     */
    worldToLocal(worldPosition) {
        // SAFETY CHECK: Prevent freeze by validating inputs
        if (!worldPosition || typeof worldPosition.x !== 'number' || typeof worldPosition.y !== 'number' || typeof worldPosition.z !== 'number') {
            console.warn('[LocalCoordinateSystem] Invalid worldPosition provided:', worldPosition);
            return {
                local: { x: 0, y: 0, z: 0 },
                isValid: false,
                error: 'Invalid world position'
            };
        }

        if (!this.currentRoom || !this.currentRoomShape) {
            console.warn('[LocalCoordinateSystem] No current room set. Room:', this.currentRoom, 'Shape:', this.currentRoomShape);
            return {
                local: { x: 0, y: 0, z: 0 },
                isValid: false,
                error: 'No current room set'
            };
        }

        const shapeDef = ROOM_SHAPE_DEFINITIONS[this.currentRoomShape];
        if (!shapeDef) {
            console.warn('[LocalCoordinateSystem] Unknown room shape:', this.currentRoomShape);
            return {
                local: { x: 0, y: 0, z: 0 },
                isValid: false,
                error: `Unknown room shape: ${this.currentRoomShape}`
            };
        }

        try {
            // Convert world position to local coordinates
            // World position is relative to room center, so local coordinates are the same
            const local = {
                x: parseFloat(worldPosition.x.toFixed(3)),
                y: parseFloat(worldPosition.y.toFixed(3)),
                z: parseFloat(worldPosition.z.toFixed(3))
            };

            // Check if position is within room bounds
            const withinBounds = (
                local.x >= shapeDef.bounds.minX && local.x <= shapeDef.bounds.maxX &&
                local.z >= shapeDef.bounds.minZ && local.z <= shapeDef.bounds.maxZ
            );

            // Check if position is within valid areas for complex shapes
            let withinValidArea = true;
            if (shapeDef.validAreas && Array.isArray(shapeDef.validAreas)) {
                withinValidArea = shapeDef.validAreas.some(area =>
                    area && typeof area.minX === 'number' && typeof area.maxX === 'number' &&
                    area && typeof area.minZ === 'number' && typeof area.maxZ === 'number' &&
                    local.x >= area.minX && local.x <= area.maxX &&
                    local.z >= area.minZ && local.z <= area.maxZ
                );
            }

            return {
                local,
                isValid: withinBounds && withinValidArea,
                withinBounds,
                withinValidArea,
                roomShape: this.currentRoomShape,
                roomId: this.currentRoom.id,
                shapeDef
            };
        } catch (error) {
            console.error('[LocalCoordinateSystem] Error in worldToLocal:', error);
            return {
                local: { x: 0, y: 0, z: 0 },
                isValid: false,
                error: `Calculation error: ${error.message}`
            };
        }
    }

    /**
     * Convert local room coordinates to world coordinates
     * @param {Object} localCoords - Local coordinates {x, y, z}
     * @returns {THREE.Vector3} World position
     */
    localToWorld(localCoords) {
        // Since room center is at world origin, local coordinates are the same as world coordinates
        return new THREE.Vector3(localCoords.x, localCoords.y, localCoords.z);
    }

    /**
     * Update debug display with current player position
     * @param {THREE.Vector3} playerPosition - Current player world position
     * @param {boolean} isVisible - Whether debug display should be visible
     */
    updateDebugDisplay(playerPosition, isVisible = false) {
        this.isDebugVisible = isVisible;

        if (!isVisible) {
            if (this.debugElement) {
                this.debugElement.style.display = 'none';
            }
            return;
        }

        // SAFETY CHECK: Ensure debug element exists
        if (!this.debugElement) {
            console.warn('[LocalCoordinateSystem] Debug element not found, recreating...');
            this._createDebugDisplay();
        }

        try {
            const coordInfo = this.worldToLocal(playerPosition);
            const shapeDef = coordInfo.shapeDef;

            let statusColor = coordInfo.isValid ? '#00ff00' : '#ff4444';
            let statusText = coordInfo.isValid ? 'VALID' : 'INVALID';

            if (coordInfo.error) {
                statusText = `ERROR: ${coordInfo.error}`;
                statusColor = '#ff0000';
            } else {
                if (!coordInfo.withinBounds) {
                    statusText += ' (OUT OF BOUNDS)';
                } else if (!coordInfo.withinValidArea) {
                    statusText += ' (OUTSIDE ROOM AREA)';
                }
            }

            this.debugElement.innerHTML = `
                <div style="margin-bottom: 5px; color: #ffffff; font-weight: bold;">
                    LOCAL COORDINATES DEBUG
                </div>
                <div style="margin-bottom: 3px;">
                    Room: <span style="color: #ffff00;">${coordInfo.roomId || 'Unknown'}</span> |
                    Shape: <span style="color: #ffff00;">${shapeDef?.name || 'Unknown'}</span>
                </div>
                <div style="margin-bottom: 3px;">
                    Local: <span style="color: #00ffff;">(${coordInfo.local.x}, ${coordInfo.local.y}, ${coordInfo.local.z})</span>
                </div>
                <div style="color: ${statusColor};">
                    Status: ${statusText}
                </div>
                ${shapeDef?.description ? `<div style="margin-top: 3px; color: #cccccc; font-size: 12px;">${shapeDef.description}</div>` : ''}
            `;

            this.debugElement.style.display = 'block';
            this.debugElement.style.borderColor = statusColor;
            this.debugElement.style.boxShadow = `0 0 10px ${statusColor}33`;
        } catch (error) {
            console.error('[LocalCoordinateSystem] Error updating debug display:', error);
            if (this.debugElement) {
                this.debugElement.innerHTML = `
                    <div style="color: #ff0000;">
                        LOCAL COORDINATES DEBUG - ERROR
                    </div>
                    <div style="color: #ff4444;">
                        ${error.message}
                    </div>
                `;
                this.debugElement.style.display = 'block';
            }
        }
    }

    /**
     * Get room shape definition
     * @param {string} shapeKey - Room shape key
     * @returns {Object} Shape definition or null
     */
    getShapeDefinition(shapeKey) {
        return ROOM_SHAPE_DEFINITIONS[shapeKey] || null;
    }

    /**
     * Get all available room shapes
     * @returns {Object} All room shape definitions
     */
    getAllShapeDefinitions() {
        return { ...ROOM_SHAPE_DEFINITIONS };
    }

    /**
     * Validate if a local coordinate is valid for a given room shape
     * @param {Object} localCoords - Local coordinates {x, y, z}
     * @param {string} shapeKey - Room shape key
     * @returns {Object} Validation result
     */
    validateLocalCoordinate(localCoords, shapeKey) {
        const shapeDef = ROOM_SHAPE_DEFINITIONS[shapeKey];
        if (!shapeDef) {
            return { isValid: false, error: `Unknown room shape: ${shapeKey}` };
        }

        // Check bounds
        const withinBounds = (
            localCoords.x >= shapeDef.bounds.minX && localCoords.x <= shapeDef.bounds.maxX &&
            localCoords.z >= shapeDef.bounds.minZ && localCoords.z <= shapeDef.bounds.maxZ
        );

        // Check valid areas for complex shapes
        let withinValidArea = true;
        if (shapeDef.validAreas) {
            withinValidArea = shapeDef.validAreas.some(area =>
                localCoords.x >= area.minX && localCoords.x <= area.maxX &&
                localCoords.z >= area.minZ && localCoords.z <= area.maxZ
            );
        }

        return {
            isValid: withinBounds && withinValidArea,
            withinBounds,
            withinValidArea,
            shapeDef
        };
    }

    /**
     * Generate a command string for object placement
     * @param {Object} localCoords - Local coordinates {x, y, z}
     * @param {number} roomId - Room ID
     * @param {string} objectType - Type of object to place
     * @returns {string} Placement command
     */
    generatePlacementCommand(localCoords, roomId, objectType = 'vase') {
        return `place ${objectType} in room ${roomId} at local coordinates (${localCoords.x}, ${localCoords.y}, ${localCoords.z})`;
    }

    /**
     * Get current player's local coordinates and generate placement command
     * @param {THREE.Vector3} playerPosition - Current player position
     * @param {string} objectType - Type of object to place
     * @returns {string|null} Placement command or null if invalid
     */
    getCurrentPlacementCommand(playerPosition, objectType = 'vase') {
        const coordInfo = this.worldToLocal(playerPosition);
        if (!coordInfo.isValid) {
            return null;
        }
        return this.generatePlacementCommand(coordInfo.local, coordInfo.roomId, objectType);
    }

    /**
     * Log current player coordinates to console for debugging
     * @param {THREE.Vector3} playerPosition - Current player position
     */
    logCurrentCoordinates(playerPosition) {
        const coordInfo = this.worldToLocal(playerPosition);
        console.log(`[LocalCoordinates] Room ${coordInfo.roomId} (${coordInfo.roomShape}): (${coordInfo.local.x}, ${coordInfo.local.y}, ${coordInfo.local.z}) - ${coordInfo.isValid ? 'VALID' : 'INVALID'}`);
        if (coordInfo.isValid) {
            console.log(`[PlacementCommand] ${this.generatePlacementCommand(coordInfo.local, coordInfo.roomId)}`);
        }
    }

    /**
     * Example utility function for placing objects at local coordinates
     * This demonstrates how to use the local coordinate system for precise placement
     * @param {Object} localCoords - Local coordinates {x, y, z}
     * @param {string} roomShape - Room shape key
     * @param {string} objectType - Type of object to place
     * @returns {Object} Placement result with world position and validation info
     */
    static createPlacementExample(localCoords, roomShape, objectType = 'vase') {
        const system = new LocalCoordinateSystem();

        // Validate the coordinates for the room shape
        const validation = system.validateLocalCoordinate(localCoords, roomShape);

        if (!validation.isValid) {
            return {
                success: false,
                error: validation.error || 'Invalid coordinates for room shape',
                localCoords,
                roomShape,
                objectType
            };
        }

        // Convert to world coordinates
        const worldPosition = system.localToWorld(localCoords);

        return {
            success: true,
            worldPosition,
            localCoords,
            roomShape,
            objectType,
            placementCommand: system.generatePlacementCommand(localCoords, 0, objectType),
            shapeDef: validation.shapeDef
        };
    }

    /**
     * Get example placement coordinates for each room shape
     * Useful for testing and demonstration purposes
     * @returns {Object} Example coordinates for each room shape
     */
    static getExamplePlacements() {
        return {
            'SQUARE_1X1': [
                { coords: { x: 0, y: 0.1, z: 0 }, description: 'Center of room' },
                { coords: { x: -5, y: 0.1, z: -5 }, description: 'Northwest corner' },
                { coords: { x: 5, y: 0.1, z: 5 }, description: 'Southeast corner' }
            ],
            'L_SHAPE': [
                { coords: { x: -10, y: 0.1, z: -10 }, description: 'Top-left corner of L' },
                { coords: { x: -7, y: 0.1, z: 7 }, description: 'Center of L shape' },
                { coords: { x: 10, y: 0.1, z: 10 }, description: 'Bottom-right of horizontal part' }
            ],
            'T_SHAPE': [
                { coords: { x: 0, y: 0.1, z: -10 }, description: 'Center of top bar' },
                { coords: { x: 0, y: 0.1, z: 10 }, description: 'Bottom of vertical stem' },
                { coords: { x: -15, y: 0.1, z: -5 }, description: 'Left end of top bar' }
            ],
            'CROSS_SHAPE': [
                { coords: { x: 0, y: 0.1, z: 0 }, description: 'Center intersection' },
                { coords: { x: -15, y: 0.1, z: 0 }, description: 'Left arm end' },
                { coords: { x: 0, y: 0.1, z: -15 }, description: 'Top arm end' }
            ]
        };
    }

    /**
     * Cleanup debug display
     */
    destroy() {
        if (this.debugElement && this.debugElement.parentNode) {
            this.debugElement.parentNode.removeChild(this.debugElement);
        }
    }
}

export default LocalCoordinateSystem;
