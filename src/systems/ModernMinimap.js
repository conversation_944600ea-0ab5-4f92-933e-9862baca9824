/**
 * Modern Minimap System
 * 
 * A high-performance, visually stunning minimap with smooth animations,
 * optimized rendering, and seamless player movement tracking.
 * 
 * Features:
 * - Smooth interpolated position updates
 * - Efficient SVG caching and rendering
 * - Modern glassmorphism UI design
 * - Real-time player position indicator
 * - Adaptive scaling and intelligent layout
 * - Performance monitoring and optimization
 */

import * as THREE from 'three';

class ModernMinimap {
    constructor(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
        
        // Core state
        this.isInitialized = false;
        this.isVisible = true;
        this.currentRoomId = null;
        this.playerPosition = new THREE.Vector3();
        this.lastPlayerPosition = new THREE.Vector3();
        
        // Visual elements
        this.container = null;
        this.svgElement = null;
        this.playerIndicator = null;
        this.roomElements = new Map(); // Cache for room SVG elements
        
        // Animation system
        this.animationFrame = null;
        this.lastUpdateTime = 0;
        this.updateInterval = 1000 / 30; // 30 FPS for smooth animations
        this.interpolationFactor = 0.15; // Smooth movement interpolation
        
        // Performance tracking
        this.performanceMetrics = {
            updateCount: 0,
            renderTime: 0,
            lastFrameTime: 0,
            averageFrameTime: 0
        };
        
        // Visual configuration
        this.config = {
            // Container styling
            containerSize: { width: 280, height: 220 },
            position: { top: 15, right: 15 },
            
            // Room styling
            roomSize: { base: 14, min: 6, max: 24 },
            roomSpacing: 1.6,
            roomBorderRadius: 3,
            
            // Colors (modern dark theme)
            colors: {
                background: 'rgba(10, 10, 15, 0.85)',
                border: 'rgba(255, 255, 255, 0.1)',
                
                // Room colors
                unvisited: 'rgba(60, 60, 70, 0.3)',
                visited: 'rgba(120, 130, 150, 0.8)',
                current: 'rgba(255, 255, 255, 0.95)',
                boss: 'rgba(255, 80, 80, 0.9)',
                event: 'rgba(80, 150, 255, 0.9)',
                start: 'rgba(80, 255, 120, 0.9)',
                secret: 'rgba(200, 120, 255, 0.8)',
                
                // Player indicator
                player: 'rgba(255, 220, 100, 1.0)',
                playerGlow: 'rgba(255, 220, 100, 0.4)'
            },
            
            // Animation settings
            transitions: {
                roomState: '0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                playerMove: '0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                scale: '0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                opacity: '0.2s ease-out'
            }
        };
        
        // Cached room data for efficient updates
        this.roomCache = new Map();
        this.layoutBounds = { minX: 0, maxX: 0, minY: 0, maxY: 0 };
        this.scaleFactor = 1.0;
        
        // Initialize the minimap
        this.init();
    }
    
    /**
     * Initialize the modern minimap system
     */
    init() {
        console.log('[ModernMinimap] Initializing modern minimap system...');
        
        try {
            this.createContainer();
            this.createSVGElement();
            this.createPlayerIndicator();
            this.setupEventListeners();
            this.startAnimationLoop();
            
            this.isInitialized = true;
            console.log('[ModernMinimap] ✅ Modern minimap initialized successfully');
            
            // Initial update
            this.scheduleUpdate();
            
        } catch (error) {
            console.error('[ModernMinimap] ❌ Failed to initialize:', error);
            this.fallbackToBasicMinimap();
        }
    }
    
    /**
     * Create the main container with modern styling
     */
    createContainer() {
        // Remove any existing minimap
        const existingMinimap = document.getElementById('minimap-grid');
        if (existingMinimap) {
            existingMinimap.remove();
        }
        
        // Create new container
        this.container = document.createElement('div');
        this.container.id = 'modern-minimap-container';
        this.container.className = 'modern-minimap';
        
        // Apply modern styling
        Object.assign(this.container.style, {
            position: 'fixed',
            top: `${this.config.position.top}px`,
            right: `${this.config.position.right}px`,
            width: `${this.config.containerSize.width}px`,
            height: `${this.config.containerSize.height}px`,
            
            // Modern glassmorphism design
            background: this.config.colors.background,
            backdropFilter: 'blur(12px) saturate(1.2)',
            border: `1px solid ${this.config.colors.border}`,
            borderRadius: '12px',
            
            // Visual enhancements
            boxShadow: `
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 2px 8px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1)
            `,
            
            // Layout and interaction
            padding: '12px',
            zIndex: '9999',
            pointerEvents: 'none',
            userSelect: 'none',
            
            // Smooth transitions
            transition: `
                opacity ${this.config.transitions.opacity},
                transform ${this.config.transitions.scale}
            `,
            
            // Initial state
            opacity: '0',
            transform: 'scale(0.9)'
        });
        
        // Add to DOM
        document.body.appendChild(this.container);
        
        // Animate in
        requestAnimationFrame(() => {
            this.container.style.opacity = '1';
            this.container.style.transform = 'scale(1)';
        });
        
        console.log('[ModernMinimap] Container created with modern styling');
    }
    
    /**
     * Create the SVG element for efficient room rendering
     */
    createSVGElement() {
        this.svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svgElement.setAttribute('width', '100%');
        this.svgElement.setAttribute('height', '100%');
        this.svgElement.setAttribute('viewBox', '0 0 256 184'); // Will be updated dynamically
        this.svgElement.style.overflow = 'visible';
        
        // Add subtle inner glow effect
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
        
        // Glow filter for player indicator
        const glowFilter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
        glowFilter.setAttribute('id', 'player-glow');
        glowFilter.innerHTML = `
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        `;
        
        // Room glow filter
        const roomGlowFilter = document.createElementNS('http://www.w3.org/2000/svg', 'filter');
        roomGlowFilter.setAttribute('id', 'room-glow');
        roomGlowFilter.innerHTML = `
            <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
            <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        `;
        
        defs.appendChild(glowFilter);
        defs.appendChild(roomGlowFilter);
        this.svgElement.appendChild(defs);
        
        this.container.appendChild(this.svgElement);
        console.log('[ModernMinimap] SVG element created with filters');
    }
    
    /**
     * Create the player position indicator
     */
    createPlayerIndicator() {
        this.playerIndicator = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        this.playerIndicator.setAttribute('r', '4');
        this.playerIndicator.setAttribute('fill', this.config.colors.player);
        this.playerIndicator.setAttribute('filter', 'url(#player-glow)');
        this.playerIndicator.style.transition = `
            cx ${this.config.transitions.playerMove},
            cy ${this.config.transitions.playerMove}
        `;
        
        // Add pulsing animation
        const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
        animate.setAttribute('attributeName', 'r');
        animate.setAttribute('values', '4;5;4');
        animate.setAttribute('dur', '2s');
        animate.setAttribute('repeatCount', 'indefinite');
        this.playerIndicator.appendChild(animate);
        
        this.svgElement.appendChild(this.playerIndicator);
        console.log('[ModernMinimap] Player indicator created');
    }
    
    /**
     * Setup event listeners for responsive behavior
     */
    setupEventListeners() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.scheduleUpdate();
        });
        
        // Handle visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
        
        console.log('[ModernMinimap] Event listeners setup complete');
    }
    
    /**
     * Start the animation loop for smooth updates
     */
    startAnimationLoop() {
        const animate = (currentTime) => {
            if (currentTime - this.lastUpdateTime >= this.updateInterval) {
                this.updateMinimap();
                this.lastUpdateTime = currentTime;
            }
            
            this.animationFrame = requestAnimationFrame(animate);
        };
        
        this.animationFrame = requestAnimationFrame(animate);
        console.log('[ModernMinimap] Animation loop started');
    }
    
    /**
     * Schedule an update (debounced)
     */
    scheduleUpdate() {
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
        }

        this.updateTimeout = setTimeout(() => {
            this.updateMinimap();
        }, 16); // ~60 FPS
    }

    /**
     * Main update method - called by animation loop
     */
    updateMinimap() {
        if (!this.isInitialized || !this.isVisible) return;

        const startTime = performance.now();

        try {
            // Update player position
            this.updatePlayerPosition();

            // Update room data if needed
            this.updateRoomData();

            // Update visual elements
            this.updateVisualElements();

            // Update performance metrics
            this.updatePerformanceMetrics(startTime);

        } catch (error) {
            console.error('[ModernMinimap] Update error:', error);
        }
    }

    /**
     * Update player position with smooth interpolation
     */
    updatePlayerPosition() {
        if (!this.dungeonHandler?.playerController?.playerMesh) return;

        const currentPos = this.dungeonHandler.playerController.playerMesh.position;
        const currentRoomId = this.dungeonHandler.currentRoomId;

        // Check if player moved to a new room
        if (currentRoomId !== this.currentRoomId) {
            this.currentRoomId = currentRoomId;
            this.onRoomChanged();
        }

        // Smooth position interpolation
        this.playerPosition.lerp(currentPos, this.interpolationFactor);

        // Update player indicator position
        this.updatePlayerIndicatorPosition();
    }

    /**
     * Update room data from dungeon handler
     */
    updateRoomData() {
        if (!this.dungeonHandler?.floorLayout) return;

        const floorLayout = this.dungeonHandler.floorLayout;
        let needsLayoutUpdate = false;

        // Check for new or updated rooms
        floorLayout.forEach((room, roomId) => {
            const cached = this.roomCache.get(roomId);

            if (!cached || this.hasRoomChanged(room, cached)) {
                this.roomCache.set(roomId, this.cloneRoomData(room));
                needsLayoutUpdate = true;
            }
        });

        // Update layout if needed
        if (needsLayoutUpdate) {
            this.updateLayout();
        }
    }

    /**
     * Update visual elements (rooms, connections, etc.)
     */
    updateVisualElements() {
        this.roomCache.forEach((room, roomId) => {
            if (room.visited) {
                this.updateRoomElement(roomId, room);
            }
        });
    }

    /**
     * Update the layout bounds and scaling
     */
    updateLayout() {
        const visitedRooms = Array.from(this.roomCache.values()).filter(room => room.visited);

        if (visitedRooms.length === 0) return;

        // Calculate bounds
        this.layoutBounds = this.calculateLayoutBounds(visitedRooms);

        // Calculate optimal scale
        this.scaleFactor = this.calculateOptimalScale();

        // Update SVG viewBox
        this.updateSVGViewBox();

        console.log('[ModernMinimap] Layout updated:', {
            bounds: this.layoutBounds,
            scale: this.scaleFactor,
            rooms: visitedRooms.length
        });
    }

    /**
     * Calculate layout bounds for all visited rooms
     */
    calculateLayoutBounds(rooms) {
        let minX = Infinity, maxX = -Infinity;
        let minY = Infinity, maxY = -Infinity;

        rooms.forEach(room => {
            if (room.coords) {
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            }
        });

        return { minX, maxX, minY, maxY };
    }

    /**
     * Calculate optimal scale factor for the current layout
     */
    calculateOptimalScale() {
        const { minX, maxX, minY, maxY } = this.layoutBounds;
        const layoutWidth = (maxX - minX + 1) * this.config.roomSize.base * this.config.roomSpacing;
        const layoutHeight = (maxY - minY + 1) * this.config.roomSize.base * this.config.roomSpacing;

        const containerWidth = this.config.containerSize.width - 24; // Account for padding
        const containerHeight = this.config.containerSize.height - 24;

        const scaleX = containerWidth / layoutWidth;
        const scaleY = containerHeight / layoutHeight;

        return Math.min(scaleX, scaleY, 1.0); // Don't scale up beyond 1.0
    }

    /**
     * Update SVG viewBox based on current layout
     */
    updateSVGViewBox() {
        const { minX, maxX, minY, maxY } = this.layoutBounds;
        const padding = 20;

        const viewBoxWidth = (maxX - minX + 1) * this.config.roomSize.base * this.config.roomSpacing + padding * 2;
        const viewBoxHeight = (maxY - minY + 1) * this.config.roomSize.base * this.config.roomSpacing + padding * 2;

        this.svgElement.setAttribute('viewBox', `0 0 ${viewBoxWidth} ${viewBoxHeight}`);
    }

    /**
     * Update or create a room element
     */
    updateRoomElement(roomId, room) {
        let roomElement = this.roomElements.get(roomId);

        if (!roomElement) {
            roomElement = this.createRoomElement(roomId, room);
            this.roomElements.set(roomId, roomElement);
            this.svgElement.appendChild(roomElement);
        }

        // Update room appearance
        this.updateRoomAppearance(roomElement, room);

        // Update room position
        this.updateRoomPosition(roomElement, room);
    }

    /**
     * Create a new room element
     */
    createRoomElement(roomId, room) {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.setAttribute('data-room-id', roomId);
        group.style.transition = `
            fill ${this.config.transitions.roomState},
            stroke ${this.config.transitions.roomState},
            transform ${this.config.transitions.roomState}
        `;

        // Create room shape based on shapeKey
        const shape = this.createRoomShape(room.shapeKey || 'SQUARE_1X1');
        group.appendChild(shape);

        return group;
    }

    /**
     * Create room shape SVG element
     */
    createRoomShape(shapeKey) {
        const size = this.config.roomSize.base * this.scaleFactor;
        const radius = this.config.roomBorderRadius;

        switch (shapeKey) {
            case 'L_SHAPE':
                return this.createLShape(size, radius);
            case 'U_SHAPE_DOWN':
            case 'U_SHAPE_UP':
            case 'U_SHAPE_LEFT':
            case 'U_SHAPE_RIGHT':
                return this.createUShape(shapeKey, size, radius);
            case 'RECT_2X1':
                return this.createRect(size * 2, size, radius);
            case 'RECT_3X1':
                return this.createRect(size * 3, size, radius);
            case 'RECT_1X2':
                return this.createRect(size, size * 2, radius);
            case 'RECT_1X3':
                return this.createRect(size, size * 3, radius);
            case 'BOSS_ARENA':
                return this.createBossArena(size * 2, radius);
            case 'SQUARE_1X1':
            default:
                return this.createRect(size, size, radius);
        }
    }

    /**
     * Create a rectangular room shape
     */
    createRect(width, height, radius) {
        const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        rect.setAttribute('width', width);
        rect.setAttribute('height', height);
        rect.setAttribute('rx', radius);
        rect.setAttribute('ry', radius);
        rect.setAttribute('stroke-width', '1');
        return rect;
    }

    /**
     * Create L-shaped room
     */
    createLShape(size, radius) {
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const d = `M0,0 L${size * 2},0 L${size * 2},${size} L${size},${size} L${size},${size * 2} L0,${size * 2} Z`;
        path.setAttribute('d', d);
        path.setAttribute('stroke-width', '1');
        return path;
    }

    /**
     * Create U-shaped room
     */
    createUShape(shapeKey, size, radius) {
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        let d;

        switch (shapeKey) {
            case 'U_SHAPE_DOWN':
                d = `M0,0 L${size * 2},0 L${size * 2},${size} L${size * 1.5},${size} L${size * 1.5},${size * 0.5} L${size * 0.5},${size * 0.5} L${size * 0.5},${size} L0,${size} Z`;
                break;
            case 'U_SHAPE_UP':
                d = `M0,${size * 0.5} L${size * 0.5},${size * 0.5} L${size * 0.5},0 L${size * 1.5},0 L${size * 1.5},${size * 0.5} L${size * 2},${size * 0.5} L${size * 2},${size} L0,${size} Z`;
                break;
            case 'U_SHAPE_LEFT':
                d = `M${size * 0.5},0 L${size},0 L${size},${size * 2} L${size * 0.5},${size * 2} L${size * 0.5},${size * 1.5} L0,${size * 1.5} L0,${size * 0.5} L${size * 0.5},${size * 0.5} Z`;
                break;
            case 'U_SHAPE_RIGHT':
                d = `M0,0 L${size * 0.5},0 L${size * 0.5},${size * 0.5} L${size},${size * 0.5} L${size},${size * 1.5} L${size * 0.5},${size * 1.5} L${size * 0.5},${size * 2} L0,${size * 2} Z`;
                break;
            default:
                d = `M0,0 L${size * 2},0 L${size * 2},${size} L${size * 1.5},${size} L${size * 1.5},${size * 0.5} L${size * 0.5},${size * 0.5} L${size * 0.5},${size} L0,${size} Z`;
        }

        path.setAttribute('d', d);
        path.setAttribute('stroke-width', '1');
        return path;
    }

    /**
     * Create boss arena shape
     */
    createBossArena(size, radius) {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');

        // Outer square
        const outer = this.createRect(size, size, radius);
        group.appendChild(outer);

        // Inner square for boss arena effect
        const inner = this.createRect(size * 0.7, size * 0.7, radius * 0.5);
        inner.setAttribute('transform', `translate(${size * 0.15}, ${size * 0.15})`);
        inner.setAttribute('fill', 'none');
        inner.setAttribute('stroke-width', '1');
        group.appendChild(inner);

        return group;
    }

    /**
     * Update room appearance based on state
     */
    updateRoomAppearance(roomElement, room) {
        const shape = roomElement.firstChild;
        const isCurrentRoom = room.id === this.currentRoomId;

        let fillColor, strokeColor, filter = '';

        if (isCurrentRoom) {
            fillColor = this.config.colors.current;
            strokeColor = this.config.colors.current;
            filter = 'url(#room-glow)';
        } else {
            switch (room.type) {
                case 'Boss':
                    fillColor = this.config.colors.boss;
                    strokeColor = this.config.colors.boss;
                    break;
                case 'Event':
                    fillColor = this.config.colors.event;
                    strokeColor = this.config.colors.event;
                    break;
                case 'Start':
                    fillColor = this.config.colors.start;
                    strokeColor = this.config.colors.start;
                    break;
                default:
                    if (room.isSecret) {
                        fillColor = this.config.colors.secret;
                        strokeColor = this.config.colors.secret;
                    } else {
                        fillColor = this.config.colors.visited;
                        strokeColor = this.config.colors.visited;
                    }
            }
        }

        shape.setAttribute('fill', fillColor);
        shape.setAttribute('stroke', strokeColor);
        shape.setAttribute('filter', filter);
    }

    /**
     * Update room position
     */
    updateRoomPosition(roomElement, room) {
        if (!room.coords) return;

        const { minX, minY } = this.layoutBounds;
        const size = this.config.roomSize.base * this.scaleFactor;
        const spacing = this.config.roomSpacing;

        const x = (room.coords.x - minX) * size * spacing + 20; // 20px padding
        const y = (room.coords.y - minY) * size * spacing + 20;

        roomElement.setAttribute('transform', `translate(${x}, ${y})`);
    }

    /**
     * Update player indicator position
     */
    updatePlayerIndicatorPosition() {
        if (!this.currentRoomId || !this.roomCache.has(this.currentRoomId)) return;

        const currentRoom = this.roomCache.get(this.currentRoomId);
        if (!currentRoom?.coords) return;

        const { minX, minY } = this.layoutBounds;
        const size = this.config.roomSize.base * this.scaleFactor;
        const spacing = this.config.roomSpacing;

        // Calculate room center position
        const roomX = (currentRoom.coords.x - minX) * size * spacing + 20;
        const roomY = (currentRoom.coords.y - minY) * size * spacing + 20;

        // Add room center offset
        const centerX = roomX + (size / 2);
        const centerY = roomY + (size / 2);

        this.playerIndicator.setAttribute('cx', centerX);
        this.playerIndicator.setAttribute('cy', centerY);
    }

    /**
     * Handle room change events
     */
    onRoomChanged() {
        console.log(`[ModernMinimap] Room changed to: ${this.currentRoomId}`);

        // Update room states
        this.roomCache.forEach((room, roomId) => {
            const roomElement = this.roomElements.get(roomId);
            if (roomElement) {
                this.updateRoomAppearance(roomElement, room);
            }
        });

        // Update player indicator
        this.updatePlayerIndicatorPosition();
    }

    /**
     * Utility methods
     */
    hasRoomChanged(room, cached) {
        return room.visited !== cached.visited ||
               room.type !== cached.type ||
               room.shapeKey !== cached.shapeKey ||
               JSON.stringify(room.coords) !== JSON.stringify(cached.coords);
    }

    cloneRoomData(room) {
        return {
            id: room.id,
            type: room.type,
            shapeKey: room.shapeKey,
            coords: room.coords ? { ...room.coords } : null,
            visited: room.visited,
            isSecret: room.isSecret || room.type === 'SECRET'
        };
    }

    /**
     * Performance tracking
     */
    updatePerformanceMetrics(startTime) {
        const endTime = performance.now();
        const frameTime = endTime - startTime;

        this.performanceMetrics.updateCount++;
        this.performanceMetrics.renderTime += frameTime;
        this.performanceMetrics.lastFrameTime = frameTime;

        // Calculate rolling average
        if (this.performanceMetrics.updateCount % 60 === 0) {
            this.performanceMetrics.averageFrameTime =
                this.performanceMetrics.renderTime / this.performanceMetrics.updateCount;

            // Log performance if it's getting slow
            if (this.performanceMetrics.averageFrameTime > 5) {
                console.warn(`[ModernMinimap] Performance warning: ${this.performanceMetrics.averageFrameTime.toFixed(2)}ms average frame time`);
            }
        }
    }

    /**
     * Animation control methods
     */
    pauseAnimations() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
            this.animationFrame = null;
        }
        console.log('[ModernMinimap] Animations paused');
    }

    resumeAnimations() {
        if (!this.animationFrame) {
            this.startAnimationLoop();
        }
        console.log('[ModernMinimap] Animations resumed');
    }

    /**
     * Visibility control
     */
    show() {
        if (!this.container) return;

        this.isVisible = true;
        this.container.style.opacity = '1';
        this.container.style.transform = 'scale(1)';
        this.container.style.pointerEvents = 'none';

        console.log('[ModernMinimap] Minimap shown');
    }

    hide() {
        if (!this.container) return;

        this.isVisible = false;
        this.container.style.opacity = '0';
        this.container.style.transform = 'scale(0.9)';

        console.log('[ModernMinimap] Minimap hidden');
    }

    /**
     * Cleanup and disposal
     */
    dispose() {
        console.log('[ModernMinimap] Disposing minimap...');

        // Stop animations
        this.pauseAnimations();

        // Clear timeouts
        if (this.updateTimeout) {
            clearTimeout(this.updateTimeout);
        }

        // Remove event listeners
        window.removeEventListener('resize', this.scheduleUpdate);
        document.removeEventListener('visibilitychange', this.pauseAnimations);

        // Remove DOM elements
        if (this.container && this.container.parentNode) {
            this.container.parentNode.removeChild(this.container);
        }

        // Clear caches
        this.roomCache.clear();
        this.roomElements.clear();

        // Reset state
        this.isInitialized = false;
        this.container = null;
        this.svgElement = null;
        this.playerIndicator = null;

        console.log('[ModernMinimap] ✅ Minimap disposed successfully');
    }

    /**
     * Fallback to basic minimap if modern version fails
     */
    fallbackToBasicMinimap() {
        console.warn('[ModernMinimap] Falling back to basic minimap implementation');

        // Clean up any partial initialization
        this.dispose();

        // Re-enable the original minimap system
        if (this.dungeonHandler && typeof this.dungeonHandler._updateMinimap === 'function') {
            // Force the original minimap to recreate itself
            setTimeout(() => {
                this.dungeonHandler._updateMinimap();
            }, 100);
        }
    }

    /**
     * Debug methods
     */
    getDebugInfo() {
        return {
            initialized: this.isInitialized,
            visible: this.isVisible,
            currentRoom: this.currentRoomId,
            roomCount: this.roomCache.size,
            visibleRooms: Array.from(this.roomCache.values()).filter(r => r.visited).length,
            layoutBounds: this.layoutBounds,
            scaleFactor: this.scaleFactor,
            performance: this.performanceMetrics
        };
    }

    /**
     * Force update method for external calls
     */
    forceUpdate() {
        console.log('[ModernMinimap] Force update requested');
        this.updateMinimap();
    }

    /**
     * Update room visited state (called externally)
     */
    markRoomVisited(roomId) {
        if (this.roomCache.has(roomId)) {
            const room = this.roomCache.get(roomId);
            room.visited = true;
            this.scheduleUpdate();
            console.log(`[ModernMinimap] Room ${roomId} marked as visited`);
        }
    }

    /**
     * Set current room (called externally)
     */
    setCurrentRoom(roomId) {
        if (this.currentRoomId !== roomId) {
            this.currentRoomId = roomId;
            this.onRoomChanged();
            console.log(`[ModernMinimap] Current room set to: ${roomId}`);
        }
    }
}

// Global debug functions
if (typeof window !== 'undefined') {
    window.debugModernMinimap = () => {
        if (window.modernMinimap) {
            console.log('=== MODERN MINIMAP DEBUG ===');
            console.log(window.modernMinimap.getDebugInfo());
        } else {
            console.log('Modern minimap not initialized');
        }
    };

    window.forceMinimapUpdate = () => {
        if (window.modernMinimap) {
            window.modernMinimap.forceUpdate();
        }
    };
}

export default ModernMinimap;
