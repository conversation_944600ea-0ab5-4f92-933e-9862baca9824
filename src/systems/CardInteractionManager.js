import * as THREE from 'three';

/**
 * CardInteractionManager - Handles card drag-and-drop interactions and targeting
 */
export class CardInteractionManager {
    constructor(cardSystem, cardUI, scene, camera) {
        this.cardSystem = cardSystem;
        this.cardUI = cardUI;
        this.scene = scene;
        this.camera = camera;
        
        // Interaction state
        this.isDragging = false;
        this.draggedCardId = null;
        this.dragStartPosition = null;
        this.currentMousePosition = null;
        
        // Target detection
        this.targetIndicator = null;
        this.validTargetAreas = [];
        this.currentTarget = null;
        
        // Mobile detection
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);
        
        // Input handling
        this.boundMouseMove = this.handleMouseMove.bind(this);
        this.boundMouseUp = this.handleMouseUp.bind(this);
        this.boundTouchMove = this.handleTouchMove.bind(this);
        this.boundTouchEnd = this.handleTouchEnd.bind(this);
        
        this.setupTargetIndicator();
        this.setupEventListeners();
        
        console.log('[CardInteractionManager] Initialized card interaction manager');
    }
    
    /**
     * Setup the target indicator for card placement
     */
    setupTargetIndicator() {
        // Debug: Check if THREE is available
        if (typeof THREE === 'undefined') {
            console.error('[CardInteractionManager] THREE is not defined!');
            return;
        }

        console.log('[CardInteractionManager] THREE is available, creating target indicator');

        // Create a visual indicator for valid target areas
        const geometry = new THREE.RingGeometry(0.8, 1.0, 16);
        const material = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.5,
            side: THREE.DoubleSide
        });

        this.targetIndicator = new THREE.Mesh(geometry, material);
        this.targetIndicator.name = 'cardTargetIndicator';
        this.targetIndicator.visible = false;
        this.targetIndicator.rotation.x = -Math.PI / 2; // Lay flat on ground

        // Position it far away initially to avoid any visual artifacts
        this.targetIndicator.position.set(1000, -1000, 1000);

        this.scene.add(this.targetIndicator);
        console.log('[CardInteractionManager] Target indicator created and positioned off-screen');
    }
    
    /**
     * Setup event listeners for card interactions
     */
    setupEventListeners() {
        // We'll handle events through the CardUI system
        // This manager focuses on the targeting and world interaction logic
    }
    
    /**
     * Start card dragging
     * @param {string} cardId - The card being dragged
     * @param {Object} startPosition - Starting screen position {x, y}
     */
    startDragging(cardId, startPosition) {
        console.log(`[CardInteractionManager] startDragging called with cardId: ${cardId}, position:`, startPosition);
        console.trace('[CardInteractionManager] startDragging call stack');

        this.isDragging = true;
        this.draggedCardId = cardId;
        this.dragStartPosition = startPosition;
        this.currentMousePosition = startPosition;

        // Show target indicator
        this.targetIndicator.visible = true;
        console.log('[CardInteractionManager] Target indicator made visible for card dragging');

        // Add global event listeners for drag tracking
        if (!this.isMobile) {
            document.addEventListener('mousemove', this.boundMouseMove);
            document.addEventListener('mouseup', this.boundMouseUp);
        } else {
            document.addEventListener('touchmove', this.boundTouchMove, { passive: false });
            document.addEventListener('touchend', this.boundTouchEnd);
        }

        console.log(`[CardInteractionManager] Started dragging card: ${cardId}`);
    }
    
    /**
     * Handle mouse move during drag
     */
    handleMouseMove(event) {
        if (!this.isDragging) return;
        
        this.currentMousePosition = { x: event.clientX, y: event.clientY };
        this.updateTargeting();
        event.preventDefault();
    }
    
    /**
     * Handle mouse up to end drag
     */
    handleMouseUp(event) {
        if (!this.isDragging) return;
        
        const endPosition = { x: event.clientX, y: event.clientY };
        this.endDragging(endPosition);
        event.preventDefault();
    }
    
    /**
     * Handle touch move during drag
     */
    handleTouchMove(event) {
        if (!this.isDragging || event.touches.length === 0) return;
        
        const touch = event.touches[0];
        this.currentMousePosition = { x: touch.clientX, y: touch.clientY };
        this.updateTargeting();
        event.preventDefault();
    }
    
    /**
     * Handle touch end to end drag
     */
    handleTouchEnd(event) {
        if (!this.isDragging) return;
        
        const touch = event.changedTouches[0];
        const endPosition = { x: touch.clientX, y: touch.clientY };
        this.endDragging(endPosition);
        event.preventDefault();
    }
    
    /**
     * Update targeting during drag
     */
    updateTargeting() {
        if (!this.isDragging || !this.currentMousePosition) return;
        
        // Convert screen position to world position
        const worldPosition = this.screenToWorldPosition(this.currentMousePosition);
        
        if (worldPosition) {
            // Update target indicator position
            this.targetIndicator.position.copy(worldPosition);
            this.targetIndicator.position.y = 0.1; // Slightly above ground
            
            // Check if position is valid for card placement
            const isValidTarget = this.isValidTargetPosition(worldPosition);
            
            // Update indicator color based on validity
            this.targetIndicator.material.color.setHex(isValidTarget ? 0x00ff00 : 0xff0000);
            this.targetIndicator.material.opacity = isValidTarget ? 0.7 : 0.3;
            
            this.currentTarget = isValidTarget ? worldPosition : null;
        }
    }
    
    /**
     * Convert screen position to world position
     * @param {Object} screenPos - Screen position {x, y}
     * @returns {THREE.Vector3|null} World position or null if invalid
     */
    screenToWorldPosition(screenPos) {
        // Convert screen coordinates to normalized device coordinates
        const mouse = new THREE.Vector2();
        mouse.x = (screenPos.x / window.innerWidth) * 2 - 1;
        mouse.y = -(screenPos.y / window.innerHeight) * 2 + 1;
        
        // Create raycaster
        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, this.camera);
        
        // Raycast against a ground plane at y=0
        const groundPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
        const intersectionPoint = new THREE.Vector3();
        
        if (raycaster.ray.intersectPlane(groundPlane, intersectionPoint)) {
            return intersectionPoint;
        }
        
        return null;
    }
    
    /**
     * Check if a world position is valid for card targeting
     * @param {THREE.Vector3} worldPos - World position to check
     * @returns {boolean} True if position is valid
     */
    isValidTargetPosition(worldPos) {
        // Basic validation - check if position is within reasonable bounds
        const maxDistance = 10; // Maximum distance from origin
        const distance = worldPos.length();
        
        if (distance > maxDistance) {
            return false;
        }
        
        // Check if position is not inside walls or obstacles
        // This is a simplified check - in a full implementation, you'd check against
        // the actual room geometry and obstacles
        
        // For now, just check if it's not too close to walls (simplified)
        const minDistanceFromWalls = 1.0;
        const absX = Math.abs(worldPos.x);
        const absZ = Math.abs(worldPos.z);
        
        // Assuming room bounds are roughly -8 to 8 in both X and Z
        if (absX > 8 - minDistanceFromWalls || absZ > 8 - minDistanceFromWalls) {
            return false;
        }
        
        return true;
    }
    
    /**
     * End card dragging
     * @param {Object} endPosition - Final screen position {x, y}
     */
    endDragging(endPosition) {
        console.log('[CardInteractionManager] endDragging called, hiding target indicator');

        // Remove global event listeners
        document.removeEventListener('mousemove', this.boundMouseMove);
        document.removeEventListener('mouseup', this.boundMouseUp);
        document.removeEventListener('touchmove', this.boundTouchMove);
        document.removeEventListener('touchend', this.boundTouchEnd);

        // Hide target indicator
        this.targetIndicator.visible = false;
        // Move it back off-screen
        this.targetIndicator.position.set(1000, -1000, 1000);
        console.log('[CardInteractionManager] Target indicator hidden and moved off-screen');

        let cardWasPlayed = false;

        // Check if card was dropped on a valid target
        if (this.currentTarget && this.draggedCardId) {
            cardWasPlayed = this.playCardAtTarget(this.draggedCardId, this.currentTarget);
        }

        // Reset state
        this.isDragging = false;
        this.draggedCardId = null;
        this.dragStartPosition = null;
        this.currentMousePosition = null;
        this.currentTarget = null;

        console.log(`[CardInteractionManager] Ended dragging, card played: ${cardWasPlayed}`);

        return cardWasPlayed;
    }
    
    /**
     * Play a card at the target position
     * @param {string} cardId - The card to play
     * @param {THREE.Vector3} targetPosition - The target world position
     * @returns {boolean} True if card was successfully played
     */
    playCardAtTarget(cardId, targetPosition) {
        const card = this.cardSystem.getCard(cardId);
        if (!card) {
            console.warn(`[CardInteractionManager] Card not found: ${cardId}`);
            return false;
        }
        
        console.log(`[CardInteractionManager] Playing card ${card.name} at position:`, targetPosition);
        
        // Convert world position to screen position for card system
        const screenPos = this.worldToScreenPosition(targetPosition);
        
        // Use card system to play the card
        return this.cardSystem.playCard(cardId, screenPos);
    }
    
    /**
     * Convert world position to screen position
     * @param {THREE.Vector3} worldPos - World position
     * @returns {Object} Screen position {x, y}
     */
    worldToScreenPosition(worldPos) {
        const vector = worldPos.clone();
        vector.project(this.camera);
        
        const x = (vector.x * 0.5 + 0.5) * window.innerWidth;
        const y = (vector.y * -0.5 + 0.5) * window.innerHeight;
        
        return { x, y };
    }
    
    /**
     * Update the interaction manager
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Update target indicator animation if visible
        if (this.targetIndicator.visible) {
            this.targetIndicator.rotation.z += deltaTime * 2; // Rotate the ring
            
            // Pulsing effect
            const pulseScale = 1 + Math.sin(Date.now() * 0.005) * 0.1;
            this.targetIndicator.scale.setScalar(pulseScale);
        }
    }
    
    /**
     * Check if currently dragging a card
     * @returns {boolean} True if dragging
     */
    isDraggingCard() {
        return this.isDragging;
    }
    
    /**
     * Get the currently dragged card ID
     * @returns {string|null} Card ID or null
     */
    getDraggedCardId() {
        return this.draggedCardId;
    }
    
    /**
     * Cancel current drag operation
     */
    cancelDrag() {
        if (this.isDragging) {
            this.endDragging(this.currentMousePosition || { x: 0, y: 0 });
        }
    }
    
    /**
     * Dispose of the interaction manager
     */
    dispose() {
        // Remove event listeners
        this.cancelDrag();
        
        // Remove target indicator
        if (this.targetIndicator) {
            this.scene.remove(this.targetIndicator);
            this.targetIndicator.geometry.dispose();
            this.targetIndicator.material.dispose();
            this.targetIndicator = null;
        }
        
        console.log('[CardInteractionManager] Card interaction manager disposed');
    }
}
