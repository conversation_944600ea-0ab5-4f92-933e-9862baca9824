import * as THREE from 'three';
import { VOXEL_SIZE } from '../generators/prefabs/shared.js';
import { 
    createSkeletonEnemyModel,
    createBatEnemyModel,
    createZombieEnemyModel,
    createFireflyEnemyModel
} from '../generators/prefabs/index.js';

/**
 * RhythmArcade3D - Crypt of the NecroDancer/Piano Tiles style rhythm game
 * Creates a complete 3D arcade room with full environment, animated camera transitions,
 * and voxel enemy icons falling down lanes for rhythm-based gameplay
 */
export class RhythmArcade3D {
    constructor(sceneManager, dungeonHandler) {
        this.sceneManager = sceneManager;
        this.dungeonHandler = dungeonHandler;
        
        // 3D Scene setup
        this.minigameScene = null;
        this.minigameCamera = null;
        this.minigameRenderer = null;
        
        // Camera state management
        this.originalCameraState = null;
        this.isInMinigame = false;
        
        // Game elements
        this.gameBoard = null;
        this.lanes = []; // 4 lanes for falling enemies
        this.fallingEnemies = [];
        this.beatIndicators = [];
        this.targetZones = [];
        this.keyIndicators = []; // Visual key indicators for each lane
        this.roomEnvironment = null;
        this.arcadeCabinet = null;
        
        // Game state
        this.isPlaying = false;
        this.isPaused = false;
        this.score = 0;
        this.health = 100;
        this.combo = 0;
        this.maxCombo = 0;
        this.level = 1;
        this.speed = 1.0;
        this.notesHit = 0;
        this.notesMissed = 0;
        this.perfectHits = 0;
        this.levelThresholds = [0, 500, 1500, 3000, 5000, 8000, 12000, 18000, 25000, 35000];
        this.pauseTime = 0;
        
        // Timing system
        this.gameTime = 0;
        this.lastBeatTime = 0;
        this.beatInterval = 0.5; // 500ms beats (120 BPM)
        this.currentBeat = 0;
        
        // Audio system for rhythm game
        this.audioContext = null;
        this.audioBuffer = null;
        this.audioSource = null;
        this.analyzer = null;
        this.musicStartTime = 0;
        this.musicLoaded = false;
        
        // Enemy patterns (simple rhythm patterns)
        this.enemyPatterns = [
            // Level 1: Simple patterns
            [
                { beat: 1, lane: 0 }, { beat: 3, lane: 1 }, 
                { beat: 5, lane: 2 }, { beat: 7, lane: 3 }
            ],
            // Level 2: Faster patterns
            [
                { beat: 1, lane: 0 }, { beat: 1.5, lane: 1 }, 
                { beat: 2, lane: 2 }, { beat: 2.5, lane: 3 },
                { beat: 3, lane: 1 }, { beat: 4, lane: 0 }
            ],
            // Level 3: Complex patterns
            [
                { beat: 1, lane: 0 }, { beat: 1.25, lane: 1 }, 
                { beat: 1.5, lane: 2 }, { beat: 1.75, lane: 3 },
                { beat: 2, lane: 0 }, { beat: 2.25, lane: 3 },
                { beat: 2.5, lane: 1 }, { beat: 2.75, lane: 2 }
            ]
        ];
        
        this.currentPattern = 0;
        this.patternStartBeat = 0;
        
        // Input handling
        this.raycaster = new THREE.Raycaster();
        this.raycaster.near = 0.1;
        this.raycaster.far = 1000;
        this.raycaster.layers.enableAll();
        this.mouse = new THREE.Vector2();
        this.keys = { w: false, a: false, s: false, d: false };
        
        // Mobile controls
        this.mobileControls = {
            isEnabled: false,
            touchZones: [],
            activeTouches: new Set()
        };
        
        // Visual effects
        this.lighting = null;
        this.particleSystem = null;
        this.hitEffects = [];
        this.hitFeedbacks = []; // For PERFECT/GOOD/MISS text
        
        // Camera shake
        this.cameraShake = {
            active: false,
            intensity: 0,
            duration: 0,
            startTime: 0,
            originalPosition: null
        };
        
        // Object pooling (reduced size to prevent memory issues)
        this.notePool = [];
        this.activeNotes = [];
        this.maxPoolSize = 20; // Reduced from 50 to prevent memory issues
        
        // UI elements
        this.gameUI = null;
        this.gameOverUI = null;
        this.originalMinimapDisplay = undefined;
        this.customCursor = null;
        this.lastDisplayedScore = 0;
        
        // Colors for different lanes/enemies (matching the image)
        this.laneColors = [0xFF69B4, 0x00CED1, 0xFFD700, 0x9370DB]; // Pink, Turquoise, Gold, Purple
        
        // Track animation frames and timeouts for cleanup
        this.animationFrameId = null;
        this.activeTimeouts = new Set();
        this.activeIntervals = new Set();
        this.isCleaningUp = false;
        
        console.log('[RhythmArcade3D] Rhythm arcade system initialized');
    }
    
    /**
     * Start the rhythm arcade minigame - main entry point
     * Creates complete 3D scene transition like chess game
     */
    async startGame() {
        console.log('[RhythmArcade3D] 🎮 Starting rhythm arcade minigame');
        
        try {
            // Save current camera state before transition
            this.saveCurrentCameraState();
            
            // Create the minigame scene with full 3D environment
            await this.createMinigameScene();
            
            // Transition camera to arcade playing position
            await this.transitionToArcadeView();
            
            // Disable player controls and setup arcade controls
            this.disablePlayerControls();
            this.setupInteractionHandlers();
            
            // Create UI and effects
            this.createArcadeUI();
            this.createCustomCursor();
            this.hideMinimapTemporarily();
            
            this.isInMinigame = true;
            
            // Load and start music
            await this.loadArcadeMusic();
            
            // Start the actual rhythm game
            return await this.runRhythmGame();
            
        } catch (error) {
            console.error('[RhythmArcade3D] Error starting minigame:', error);
            await this.cleanup();
            throw error;
        }
    }
    
    /**
     * Save current camera state for restoration later
     */
    saveCurrentCameraState() {
        console.log('[RhythmArcade3D] 💾 Saving current camera state');
        
        const camera = this.sceneManager.camera;
        this.originalCameraState = {
            position: camera.position.clone(),
            rotation: camera.rotation.clone(),
            fov: camera.fov,
            controls: this.sceneManager.controls ? {
                enabled: this.sceneManager.controls.enabled,
                target: this.sceneManager.controls.target.clone()
            } : null
        };
        
        console.log('[RhythmArcade3D] Camera state saved:', {
            position: this.originalCameraState.position,
            fov: this.originalCameraState.fov
        });
    }
    
    /**
     * Create the complete 3D minigame scene
     */
    async createMinigameScene() {
        console.log('[RhythmArcade3D] 🏗️ Creating minigame scene');
        
        // Create new scene with dark electronic background
        this.minigameScene = new THREE.Scene();
        this.minigameScene.background = new THREE.Color(0x0a0a1a); // Dark blue-purple electronic
        
        // Create dedicated minigame camera
        this.minigameCamera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        
        // Force all objects to layer 0 for proper rendering
        this.minigameScene.layers.enableAll();
        this.minigameCamera.layers.enableAll();
        
        // Build the complete arcade environment
        await this.createArcadeRoomEnvironment();
        await this.createArcadeCabinet();
        await this.createGameSurface();
        this.setupMinigameLighting();
        this.createAtmosphere();
        
        // Initialize object pool
        this.initializeNotePool();
        
        // Initialize cached effects for performance
        this.initializeCachedEffects();
        
        console.log('[RhythmArcade3D] ✅ Minigame scene created successfully');
    }
    
    /**
     * Create the full 3D arcade room environment
     */
    async createArcadeRoomEnvironment() {
        console.log('[RhythmArcade3D] 🏗️ Creating arcade room environment');
        
        this.roomEnvironment = new THREE.Group();
        const roomSize = 25;
        const wallHeight = 12;
        
        // Electronic neon floor
        const floorGeometry = new THREE.PlaneGeometry(roomSize, roomSize);
        const floorMaterial = new THREE.MeshStandardMaterial({
            color: 0x1a1a2a, // Dark blue-purple electronic floor
            roughness: 0.2,
            metalness: 0.8,
            emissive: 0x0a0a1a, // Subtle electronic glow
            emissiveIntensity: 0.1
        });
        const floor = new THREE.Mesh(floorGeometry, floorMaterial);
        floor.rotation.x = -Math.PI / 2;
        floor.position.y = -2;
        floor.userData.isArcadeFloor = true;
        this.forceLayer0Recursively(floor);
        this.roomEnvironment.add(floor);
        
        // Dark electronic walls
        const wallMaterial = new THREE.MeshStandardMaterial({
            color: 0x2a2a4a, // Dark blue-purple walls
            roughness: 0.3,
            metalness: 0.7,
            emissive: 0x0a0a2a,
            emissiveIntensity: 0.05
        });
        
        // Back wall
        const backWallGeometry = new THREE.PlaneGeometry(roomSize, wallHeight);
        const backWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        backWall.position.set(0, wallHeight/2 - 2, -roomSize/2);
        backWall.userData.isArcadeWall = true;
        this.forceLayer0Recursively(backWall);
        this.roomEnvironment.add(backWall);
        
        // Left wall  
        const leftWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        leftWall.position.set(-roomSize/2, wallHeight/2 - 2, 0);
        leftWall.rotation.y = Math.PI / 2;
        leftWall.userData.isArcadeWall = true;
        this.forceLayer0Recursively(leftWall);
        this.roomEnvironment.add(leftWall);
        
        // Right wall
        const rightWall = new THREE.Mesh(backWallGeometry, wallMaterial);
        rightWall.position.set(roomSize/2, wallHeight/2 - 2, 0);
        rightWall.rotation.y = -Math.PI / 2;
        rightWall.userData.isArcadeWall = true;
        this.forceLayer0Recursively(rightWall);
        this.roomEnvironment.add(rightWall);
        
        // Add electronic decorations (neon strips)
        this.addNeonDecorations();
        
        this.forceLayer0Recursively(this.roomEnvironment);
        this.minigameScene.add(this.roomEnvironment);
        
        console.log('[RhythmArcade3D] ✅ Arcade room environment created');
    }
    
    /**
     * Add neon decorations to the arcade room
     */
    addNeonDecorations() {
        const neonColors = [0x00FFFF, 0xFF0088, 0x8800FF, 0x00FF44];
        
        // Create neon strips along walls
        for (let i = 0; i < 4; i++) {
            const neonGeometry = new THREE.BoxGeometry(0.2, 0.2, 20);
            const neonMaterial = new THREE.MeshStandardMaterial({
                color: neonColors[i],
                emissive: neonColors[i],
                emissiveIntensity: 0.5
            });
            
            const neonStrip = new THREE.Mesh(neonGeometry, neonMaterial);
            neonStrip.position.set(
                Math.cos(i * Math.PI / 2) * 11,
                6,
                Math.sin(i * Math.PI / 2) * 11
            );
            neonStrip.userData.isNeonDecoration = true;
            this.forceLayer0Recursively(neonStrip);
            this.roomEnvironment.add(neonStrip);
        }
    }
    
    /**
     * Create the 3D arcade cabinet
     */
    async createArcadeCabinet() {
        console.log('[RhythmArcade3D] 🕹️ Creating arcade cabinet');
        
        this.arcadeCabinet = new THREE.Group();
        
        // Main cabinet body
        const cabinetGeometry = new THREE.BoxGeometry(8, 12, 4);
        const cabinetMaterial = new THREE.MeshStandardMaterial({
            color: 0x2a2a2a,
            roughness: 0.8,
            metalness: 0.2
        });
        const cabinet = new THREE.Mesh(cabinetGeometry, cabinetMaterial);
        cabinet.position.set(0, 6, -8);
        this.forceLayer0Recursively(cabinet);
        this.arcadeCabinet.add(cabinet);
        
        // Glowing screen
        const screenGeometry = new THREE.PlaneGeometry(6, 4);
        const screenMaterial = new THREE.MeshStandardMaterial({
            color: 0x00DDFF,
            emissive: 0x00DDFF,
            emissiveIntensity: 0.3,
            transparent: true,
            opacity: 0.9
        });
        const screen = new THREE.Mesh(screenGeometry, screenMaterial);
        screen.position.set(0, 8, -5.9);
        screen.userData.isGlowing = true;
        screen.userData.baseEmissiveIntensity = 0.3;
        this.forceLayer0Recursively(screen);
        this.arcadeCabinet.add(screen);
        
        // Control panel
        const controlsGeometry = new THREE.BoxGeometry(6, 1, 2);
        const controlsMaterial = new THREE.MeshStandardMaterial({
            color: 0x3a3a3a,
            roughness: 0.6,
            metalness: 0.4
        });
        const controls = new THREE.Mesh(controlsGeometry, controlsMaterial);
        controls.position.set(0, 3, -5);
        this.forceLayer0Recursively(controls);
        this.arcadeCabinet.add(controls);
        
        this.forceLayer0Recursively(this.arcadeCabinet);
        this.minigameScene.add(this.arcadeCabinet);
        
        console.log('[RhythmArcade3D] ✅ Arcade cabinet created');
    }
    
    /**
     * Create the game surface where enemies fall
     */
    async createGameSurface() {
        console.log('[RhythmArcade3D] 🎮 Creating game surface');
        
        this.gameBoard = new THREE.Group();
        
        // Create voxel-based game track with purple/violet theme
        const trackWidth = 32;
        const trackLength = 40;
        const laneWidth = trackWidth / 4;
        
        // Create the main track using voxels (purple/violet base)
        const voxelSize = VOXEL_SIZE;
        const trackVoxelsX = Math.floor(trackWidth / voxelSize);
        const trackVoxelsZ = Math.floor(trackLength / voxelSize);
        
        // Track material with purple/violet gradient
        const trackMaterial = new THREE.MeshStandardMaterial({
            color: 0x4A148C, // Deep purple base
            roughness: 0.4,
            metalness: 0.3,
            emissive: 0x6A1B9A,
            emissiveIntensity: 0.15
        });
        
        // PERFORMANCE FIX: Create single plane instead of 512,000 voxels
        const trackGeometry = new THREE.PlaneGeometry(trackWidth, trackLength);
        const trackMesh = new THREE.Mesh(trackGeometry, trackMaterial);
        trackMesh.rotation.x = -Math.PI / 2;
        trackMesh.position.y = 0;
        this.forceLayer0Recursively(trackMesh);
        this.gameBoard.add(trackMesh);
        
        // Add some visual interest with a few larger voxels
        const decorVoxelSize = voxelSize * 10; // Much larger voxels
        const decorVoxelsX = 8; // Only 8x10 = 80 voxels total
        const decorVoxelsZ = 10;
        
        for (let x = 0; x < decorVoxelsX; x++) {
            for (let z = 0; z < decorVoxelsZ; z++) {
                if ((x + z) % 3 === 0) { // Only every 3rd position
                    const isAlternate = (x + z) % 2 === 0;
                    const decorGeometry = new THREE.BoxGeometry(decorVoxelSize * 0.8, voxelSize * 2, decorVoxelSize * 0.8);
                    const decorMaterial = trackMaterial.clone();
                    decorMaterial.color.setHex(isAlternate ? 0x4A148C : 0x6A1B9A);
                    decorMaterial.transparent = true;
                    decorMaterial.opacity = 0.3;
                    
                    const decorVoxel = new THREE.Mesh(decorGeometry, decorMaterial);
                    decorVoxel.position.set(
                        -trackWidth/2 + x * decorVoxelSize * 1.2 + decorVoxelSize/2,
                        voxelSize,
                        -trackLength/2 + z * decorVoxelSize * 1.2 + decorVoxelSize/2
                    );
                    this.gameBoard.add(decorVoxel);
                }
            }
        }
        
        // PERFORMANCE FIX: Create simple lane dividers instead of 2400 voxels
        const dividerMaterial = new THREE.MeshStandardMaterial({
            color: 0xE1BEE7, // Light purple
            emissive: 0xCE93D8,
            emissiveIntensity: 0.5,
            roughness: 0.2,
            metalness: 0.5
        });
        
        for (let i = 0; i < 3; i++) {
            const dividerX = -trackWidth/2 + (i + 1) * laneWidth;
            
            // Create a single long box for each divider
            const dividerGeometry = new THREE.BoxGeometry(voxelSize * 2, voxelSize * 3, trackLength);
            const divider = new THREE.Mesh(dividerGeometry, dividerMaterial);
            divider.position.set(dividerX, voxelSize * 1.5, 0);
            this.forceLayer0Recursively(divider);
            this.gameBoard.add(divider);
            
            // Add a few decorative voxels along the divider
            for (let j = 0; j < 5; j++) {
                const decorZ = -trackLength/2 + (j + 1) * (trackLength / 6);
                const decorGeometry = new THREE.BoxGeometry(voxelSize * 4, voxelSize * 6, voxelSize * 4);
                const decorVoxel = new THREE.Mesh(decorGeometry, dividerMaterial.clone());
                decorVoxel.position.set(dividerX, voxelSize * 3, decorZ);
                this.gameBoard.add(decorVoxel);
            }
        }
        
        // Create voxel-based target zones with star shapes
        for (let i = 0; i < 4; i++) {
            const targetGroup = new THREE.Group();
            const laneX = -trackWidth/2 + i * laneWidth + laneWidth/2;
            const targetZ = 16;
            
            // Create voxel-based star target zone
            this.createVoxelStarTarget(targetGroup, laneX, 0.2, targetZ, this.laneColors[i], i);
            
            targetGroup.userData.isTargetZone = true;
            targetGroup.userData.lane = i;
            this.forceLayer0Recursively(targetGroup);
            this.gameBoard.add(targetGroup);
            this.targetZones.push(targetGroup);
        }
        
        // PERFORMANCE: Skip side decorations and hit zone indicators
        // These add 6 star groups + multiple indicator meshes
        // The target zones already provide visual guidance
        
        // Create key indicators for each lane
        this.createKeyIndicators();
        
        this.forceLayer0Recursively(this.gameBoard);
        this.minigameScene.add(this.gameBoard);
        
        // Create gameSurface wrapper for positioning
        this.gameSurface = new THREE.Group();
        this.gameSurface.add(this.gameBoard);
        this.gameSurface.position.set(0, 10, 0);
        
        console.log('[RhythmArcade3D] ✅ Game surface created');
    }
    
    /**
     * Create voxel-based star target zone
     */
    createVoxelStarTarget(group, x, y, z, color, lane) {
        // PERFORMANCE: Simplified target zone - single larger box instead of 14 voxels
        const targetSize = VOXEL_SIZE * 20; // Large single target
        
        const starMaterial = new THREE.MeshStandardMaterial({
            color: color,
            emissive: color,
            emissiveIntensity: 0.6,
            roughness: 0.3,
            metalness: 0.5,
            transparent: true,
            opacity: 0.8
        });
        
        const targetGeometry = new THREE.BoxGeometry(targetSize, VOXEL_SIZE * 2, targetSize);
        const targetMesh = new THREE.Mesh(targetGeometry, starMaterial);
        targetMesh.position.set(x, y + VOXEL_SIZE, z);
        targetMesh.userData.isTargetVoxel = true;
        targetMesh.userData.lane = lane;
        group.add(targetMesh);
        
        // Add simple glow plane underneath
        const glowGeometry = new THREE.PlaneGeometry(targetSize * 1.2, targetSize * 1.2);
        const glowMaterial = new THREE.MeshBasicMaterial({
            color: color,
            transparent: true,
            opacity: 0.3,
            side: THREE.DoubleSide
        });
        const glowPlane = new THREE.Mesh(glowGeometry, glowMaterial);
        glowPlane.rotation.x = -Math.PI / 2;
        glowPlane.position.set(x, y + 0.01, z);
        group.add(glowPlane);
    }
    
    /**
     * Create side star decorations
     */
    createSideStarDecorations() {
        const starPositions = [
            // Left side stars
            {x: -20, z: -10}, {x: -20, z: 0}, {x: -20, z: 10},
            // Right side stars
            {x: 20, z: -10}, {x: 20, z: 0}, {x: 20, z: 10}
        ];
        
        starPositions.forEach((pos, index) => {
            const starGroup = new THREE.Group();
            const color = index % 2 === 0 ? 0xFFD700 : 0xC0C0C0; // Gold and silver
            this.createVoxelStarTarget(starGroup, pos.x, 2, pos.z, color, -1);
            
            // Add rotation animation
            starGroup.userData.isDecorationStar = true;
            starGroup.userData.rotationSpeed = 0.01 + Math.random() * 0.01;
            
            this.forceLayer0Recursively(starGroup);
            this.gameBoard.add(starGroup);
        });
    }
    
    /**
     * Create hit zone indicator
     */
    createHitZoneIndicator() {
        const trackWidth = 32;
        const hitZoneStart = 10;
        const hitZoneEnd = 22;
        const voxelSize = VOXEL_SIZE;
        
        // Create glowing lines to indicate hit zone
        const lineMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFFFFF,
            emissive: 0xFFFFFF,
            emissiveIntensity: 0.3,
            transparent: true,
            opacity: 0.5
        });
        
        // Start line of hit zone
        const startLineGeometry = new THREE.BoxGeometry(trackWidth, voxelSize * 0.3, voxelSize * 0.5);
        const startLine = new THREE.Mesh(startLineGeometry, lineMaterial);
        startLine.position.set(0, voxelSize * 0.15, hitZoneStart);
        this.gameBoard.add(startLine);
        
        // End line of hit zone
        const endLine = new THREE.Mesh(startLineGeometry, lineMaterial.clone());
        endLine.position.set(0, voxelSize * 0.15, hitZoneEnd);
        this.gameBoard.add(endLine);
        
        // Side indicators
        for (let side = -1; side <= 1; side += 2) {
            const sideGeometry = new THREE.BoxGeometry(voxelSize * 0.5, voxelSize * 0.3, hitZoneEnd - hitZoneStart);
            const sideLine = new THREE.Mesh(sideGeometry, lineMaterial.clone());
            sideLine.position.set(side * trackWidth/2, voxelSize * 0.15, (hitZoneStart + hitZoneEnd) / 2);
            this.gameBoard.add(sideLine);
        }
    }
    
    /**
     * Create key indicators for each lane
     */
    createKeyIndicators() {
        console.log('[RhythmArcade3D] 🎯 Creating key indicators');
        
        // Check if mobile for different key labels
        const isMobile = this.mobileControls.isEnabled || 
                         /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         window.innerWidth <= 768;
        
        const keyLabels = isMobile ? ['TAP', 'TAP', 'TAP', 'TAP'] : ['W', 'A', 'S', 'D'];
        
        for (let i = 0; i < 4; i++) {
            // Create key indicator background
            const indicatorGeometry = new THREE.PlaneGeometry(3, 3);
            const indicatorMaterial = new THREE.MeshStandardMaterial({
                color: 0x000000,
                emissive: this.laneColors[i],
                emissiveIntensity: 0.1,
                transparent: true,
                opacity: 0.8,
                side: THREE.DoubleSide
            });
            const indicator = new THREE.Mesh(indicatorGeometry, indicatorMaterial);
            indicator.position.set(-12 + i * 8, 3, 18); // Above target zones
            indicator.userData.isKeyIndicator = true;
            indicator.userData.lane = i;
            indicator.userData.baseEmissiveIntensity = 0.1;
            this.forceLayer0Recursively(indicator);
            this.gameBoard.add(indicator);
            
            // Create text sprite for key label
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 256;
            
            // Clear canvas
            context.clearRect(0, 0, canvas.width, canvas.height);
            
            // Set font and style
            context.font = 'Bold 120px Arial';
            context.fillStyle = 'white';
            context.strokeStyle = 'black';
            context.lineWidth = 6;
            context.textAlign = 'center';
            context.textBaseline = 'middle';
            
            // Draw text with outline
            const text = keyLabels[i];
            context.strokeText(text, canvas.width / 2, canvas.height / 2);
            context.fillText(text, canvas.width / 2, canvas.height / 2);
            
            // Create texture and sprite
            const texture = new THREE.CanvasTexture(canvas);
            texture.minFilter = THREE.LinearFilter;
            const spriteMaterial = new THREE.SpriteMaterial({ 
                map: texture,
                transparent: true,
                opacity: 0.9
            });
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(2, 2, 1);
            sprite.position.set(-12 + i * 8, 3.1, 18);
            sprite.userData.isKeyText = true;
            sprite.userData.lane = i;
            this.forceLayer0Recursively(sprite);
            this.gameBoard.add(sprite);
            
            // Store references
            this.keyIndicators.push({
                background: indicator,
                text: sprite,
                lane: i,
                isActive: false,
                activationTime: 0
            });
        }
        
        console.log('[RhythmArcade3D] ✅ Key indicators created');
    }
    
    /**
     * Setup lighting for the minigame scene
     */
    setupMinigameLighting() {
        console.log('[RhythmArcade3D] 💡 Setting up minigame lighting');
        
        // Ambient light (increased to compensate for fewer point lights)
        const ambientLight = new THREE.AmbientLight(0x3a3a5a, 0.5);
        this.minigameScene.add(ambientLight);
        
        // Main arcade cabinet light
        const cabinetLight = new THREE.PointLight(0x00DDFF, 6.0, 25, 1.0);
        cabinetLight.position.set(0, 10, -8);
        cabinetLight.userData.isArcadeLight = true;
        this.minigameScene.add(cabinetLight);
        
        // Reduce neon lights from 4 to 2 for performance
        const neonColors = [0x00FFFF, 0xFF0088];
        for (let i = 0; i < 2; i++) {
            const neonLight = new THREE.PointLight(neonColors[i], 2.0, 15, 1.5);
            neonLight.position.set(
                i === 0 ? -10 : 10,
                6,
                0
            );
            neonLight.userData.isNeonLight = true;
            neonLight.userData.baseIntensity = 2.0;
            this.minigameScene.add(neonLight);
        }
        
        // Use directional light instead of point light for game surface
        const gameLight = new THREE.DirectionalLight(0xFFFFFF, 2.0);
        gameLight.position.set(0, 10, 5);
        gameLight.target.position.set(0, 0, 0);
        this.minigameScene.add(gameLight);
        this.minigameScene.add(gameLight.target);
        
        console.log('[RhythmArcade3D] ✅ Minigame lighting setup complete (optimized)');
    }
    
    /**
     * Create atmospheric effects
     */
    createAtmosphere() {
        console.log('[RhythmArcade3D] 🌫️ Creating atmosphere effects');
        // Atmospheric effects can be added here
        console.log('[RhythmArcade3D] ✅ Atmosphere created');
    }
    
    /**
     * Load arcade music for rhythm gameplay
     */
    async loadArcadeMusic() {
        console.log('[RhythmArcade3D] 🎵 Loading arcade music');
        
        try {
            // Create audio context only if it doesn't exist
            if (!this.audioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                console.log('[RhythmArcade3D] Created new AudioContext');
            }
            
            // Resume audio context if suspended
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
                console.log('[RhythmArcade3D] AudioContext resumed');
            }
            
            // Load the arcade music file (try .mp3 as fallback)
            let response = await fetch('/assets/music/events/arcade_game.wav');
            if (!response.ok) {
                console.log('[RhythmArcade3D] WAV file not found, trying MP3 fallback');
                response = await fetch('/assets/music/events/arcade_game_placeholder.mp3');
                if (!response.ok) {
                    console.warn(`[RhythmArcade3D] Music files not found, continuing without music`);
                    this.musicLoaded = false;
                    return;
                }
            }
            
            const arrayBuffer = await response.arrayBuffer();
            this.audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            // Create analyzer for beat detection
            this.analyzer = this.audioContext.createAnalyser();
            this.analyzer.fftSize = 2048;
            this.analyzer.smoothingTimeConstant = 0.8;
            
            this.musicLoaded = true;
            console.log('[RhythmArcade3D] ✅ Arcade music loaded successfully');
            
        } catch (error) {
            console.error('[RhythmArcade3D] ❌ Failed to load arcade music:', error);
            // Continue without music
            this.musicLoaded = false;
        }
    }
    
    /**
     * Start playing the arcade music
     */
    async startArcadeMusic() {
        if (!this.musicLoaded || !this.audioBuffer) {
            console.warn('[RhythmArcade3D] Music not loaded, starting without audio');
            return;
        }
        
        try {
            // Resume audio context if suspended (browser autoplay policy)
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
                console.log('[RhythmArcade3D] Audio context resumed');
            }
            
            // Create audio source
            this.audioSource = this.audioContext.createBufferSource();
            this.audioSource.buffer = this.audioBuffer;
            this.audioSource.loop = true;
            
            // Connect to analyzer and destination
            this.audioSource.connect(this.analyzer);
            this.analyzer.connect(this.audioContext.destination);
            
            // Start playing
            this.audioSource.start(0);
            this.musicStartTime = this.audioContext.currentTime;
            
            console.log('[RhythmArcade3D] 🎵 Arcade music started');
            
        } catch (error) {
            console.error('[RhythmArcade3D] Error starting music:', error);
        }
    }
    
    /**
     * Stop the arcade music
     */
    stopArcadeMusic() {
        if (this.audioSource) {
            try {
                this.audioSource.stop();
                this.audioSource.disconnect();
                this.audioSource = null;
                console.log('[RhythmArcade3D] 🎵 Arcade music stopped');
            } catch (error) {
                console.error('[RhythmArcade3D] Error stopping music:', error);
            }
        }
    }
    
    /**
     * Get current beat information from music analysis
     */
    getCurrentBeat() {
        // Simplified beat calculation - no audio analysis for performance
        // Just use time-based beats consistently
        return Math.floor(this.gameTime / (this.beatInterval * 1000));
    }
    
    /**
     * Transition camera to arcade playing position
     */
    async transitionToArcadeView() {
        console.log('[RhythmArcade3D] 🎥 Transitioning to arcade view');
        
        const camera = this.sceneManager.camera;
        
        // Disable controls during transition
        if (this.sceneManager.controls) {
            this.sceneManager.controls.enabled = false;
        }
        
        // Save starting position
        const startPosition = camera.position.clone();
        const startRotation = camera.rotation.clone();
        const startFov = camera.fov;
        
        // Set target position (standing in front of arcade cabinet, scaled properly)
        const finalPosition = new THREE.Vector3(0, 25, 40); // Much higher and further back to see whole arcade
        const targetRotation = new THREE.Euler(-0.5, 0, 0); // Looking down at game surface
        
        // Set FOV for arcade view
        const finalFov = 45; // Narrower FOV for better focus on game area
        
        return new Promise((resolve) => {
            const duration = 2500; // 2.5 second transition
            const startTime = Date.now();
            
            // Handle window resize
            this.handleResize = () => {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
            };
            window.addEventListener('resize', this.handleResize);
            
            const animateTransition = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Smooth easing function
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                
                // Interpolate position
                camera.position.lerpVectors(startPosition, finalPosition, easedProgress);
                
                // Interpolate rotation
                camera.rotation.x = startRotation.x + (targetRotation.x - startRotation.x) * easedProgress;
                camera.rotation.y = startRotation.y + (targetRotation.y - startRotation.y) * easedProgress;
                camera.rotation.z = startRotation.z + (targetRotation.z - startRotation.z) * easedProgress;
                
                // Interpolate FOV
                camera.fov = startFov + (finalFov - startFov) * easedProgress;
                camera.updateProjectionMatrix();
                
                // TEMPORARILY DISABLED: Scene switching causes game freeze
                // TODO: Fix the setRenderScene method to properly handle minigame updates
                // Switch to minigame scene at 10% progress
                /*
                if (progress > 0.1 && this.sceneManager.scene !== this.minigameScene) {
                    console.log('[RhythmArcade3D] Switching to minigame scene');
                    this.sceneManager.setRenderScene(this.minigameScene);
                }
                */
                
                // For now, add minigame objects to main scene
                if (progress > 0.1 && !this.minigameAddedToMainScene) {
                    console.log('[RhythmArcade3D] Adding minigame objects to main scene');
                    
                    // Hide all dungeon room objects temporarily
                    if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
                        this.dungeonHandler.currentRoomGroup.visible = false;
                        console.log('[RhythmArcade3D] Hid dungeon room objects');
                    }
                    
                    // Hide the player mesh
                    if (this.dungeonHandler && this.dungeonHandler.playerController && this.dungeonHandler.playerController.mesh) {
                        this.dungeonHandler.playerController.mesh.visible = false;
                    }
                    
                    // Add minigame objects to the current scene instead
                    if (this.arcadeRoom) this.sceneManager.scene.add(this.arcadeRoom);
                    if (this.arcadeCabinet) this.sceneManager.scene.add(this.arcadeCabinet);
                    if (this.gameSurface) this.sceneManager.scene.add(this.gameSurface);
                    if (this.keyIndicatorGroup) this.sceneManager.scene.add(this.keyIndicatorGroup);
                    
                    // Add lights
                    this.minigameScene.traverse((child) => {
                        if (child.isLight) {
                            this.sceneManager.scene.add(child.clone());
                        }
                    });
                    
                    this.minigameAddedToMainScene = true;
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animateTransition);
                } else {
                    console.log('[RhythmArcade3D] ✅ Camera transition complete');
                    resolve();
                }
            };
            
            animateTransition();
        });
    }
    
    /**
     * Disable player controls during minigame
     */
    disablePlayerControls() {
        console.log('[RhythmArcade3D] 🔒 Disabling player controls');
        
        if (this.sceneManager.controls) {
            this.sceneManager.controls.enabled = false;
        }
        
        // Store original key handlers if they exist
        if (this.dungeonHandler && this.dungeonHandler.playerController) {
            const playerController = this.dungeonHandler.playerController;
            
            // CRITICAL: Actually disable the player controller
            playerController.enabled = false;
            console.log('[RhythmArcade3D] Player controller disabled');
            
            // Store original handlers
            this.playerKeyDownHandler = playerController.onKeyDown?.bind(playerController);
            this.playerKeyUpHandler = playerController.onKeyUp?.bind(playerController);
            
            // Temporarily disable player key handlers
            playerController.onKeyDown = null;
            playerController.onKeyUp = null;
        }
    }
    
    /**
     * Setup interaction handlers for arcade game
     */
    setupInteractionHandlers() {
        console.log('[RhythmArcade3D] 🎮 Setting up interaction handlers');
        
        this.handleKeyDown = this.handleKeyDown.bind(this);
        this.handleKeyUp = this.handleKeyUp.bind(this);
        this.handleMouseClick = this.handleMouseClick.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleTouchStart = this.handleTouchStart.bind(this);
        this.handleTouchEnd = this.handleTouchEnd.bind(this);
        this.handleTouchMove = this.handleTouchMove.bind(this);
        
        document.addEventListener('keydown', this.handleKeyDown);
        document.addEventListener('keyup', this.handleKeyUp);
        document.addEventListener('click', this.handleMouseClick);
        document.addEventListener('mousemove', this.handleMouseMove);
        
        // Mobile touch events
        document.addEventListener('touchstart', this.handleTouchStart, { passive: false });
        document.addEventListener('touchend', this.handleTouchEnd, { passive: false });
        document.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    }
    
    /**
     * Create arcade UI overlay
     */
    createArcadeUI() {
        console.log('[RhythmArcade3D] 🖥️ Creating arcade UI');
        
        // Create main UI container with arcade style
        this.gameUI = document.createElement('div');
        this.gameUI.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1000;
        `;
        
        // Add CSS for performance optimizations
        if (!document.getElementById('arcade-performance-styles')) {
            const style = document.createElement('style');
            style.id = 'arcade-performance-styles';
            style.textContent = `
                .arcade-stat-value {
                    transition: color 0.2s ease;
                }
                .arcade-stat-value.pulse {
                    transform: scale(1.2);
                    transition: transform 0.2s ease;
                }
                .arcade-stat-value.combo-low {
                    color: #FFD700 !important;
                }
                .arcade-stat-value.combo-medium {
                    color: #00FFFF !important;
                }
                .arcade-stat-value.combo-high {
                    color: #FF00FF !important;
                }
                /* GPU acceleration for transforms */
                #arcade-score, #arcade-combo {
                    will-change: transform;
                    transform: translateZ(0);
                }
            `;
            document.head.appendChild(style);
        }
        
        // Create score display at top center
        const scoreDisplay = document.createElement('div');
        scoreDisplay.style.cssText = `
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            font-family: monospace;
            color: #FFFFFF;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8), 0 0 20px #FF00FF;
            pointer-events: none;
        `;
        scoreDisplay.innerHTML = `
            <div style="font-size: clamp(32px, 5vw, 48px); font-weight: bold; margin-bottom: 10px;">
                <span id="arcade-score" class="arcade-stat-value">0</span>
            </div>
            <div style="font-size: clamp(18px, 3vw, 24px); color: #FFD700;">
                COMBO: <span id="arcade-combo" class="arcade-stat-value">0</span>
            </div>
            <div style="font-size: clamp(16px, 2.5vw, 20px); color: #00FFFF; margin-top: 5px;">
                LEVEL: <span id="arcade-level" class="arcade-stat-value">1</span>
            </div>
        `;
        this.gameUI.appendChild(scoreDisplay);
        
        // Create star indicators on sides
        this.createStarIndicators();
        
        // Create health bar at bottom
        const healthBar = document.createElement('div');
        healthBar.style.cssText = `
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            width: min(300px, 80vw);
            height: 30px;
            background: rgba(0,0,0,0.7);
            border: 3px solid #FFD700;
            border-radius: 15px;
            overflow: hidden;
            pointer-events: none;
        `;
        
        const healthFill = document.createElement('div');
        healthFill.id = 'arcade-health-fill';
        healthFill.style.cssText = `
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #FF0000, #FFFF00, #00FF00);
            transition: width 0.3s ease;
        `;
        healthBar.appendChild(healthFill);
        this.gameUI.appendChild(healthBar);
        
        // Create controls hint
        const controlsHint = document.createElement('div');
        controlsHint.style.cssText = `
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            font-family: monospace;
            font-size: 16px;
            color: #FFFFFF;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
            opacity: 0.7;
            pointer-events: none;
        `;
        controlsHint.textContent = 'W A S D or Touch/Click lanes';
        this.gameUI.appendChild(controlsHint);
        
        document.body.appendChild(this.gameUI);
        
        // Setup mobile controls if on mobile device
        this.setupMobileControls();
        
        // Create exit button with arcade style
        const exitButton = document.createElement('button');
        exitButton.textContent = 'EXIT';
        exitButton.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 30px;
            background: linear-gradient(135deg, #FF0044 0%, #AA0033 100%);
            color: white;
            border: 3px solid #FFFFFF;
            border-radius: 10px;
            cursor: pointer;
            font-family: monospace;
            font-size: 18px;
            font-weight: bold;
            z-index: 1001;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            transition: transform 0.2s;
        `;
        exitButton.addEventListener('mouseenter', () => {
            exitButton.style.transform = 'scale(1.1)';
        });
        exitButton.addEventListener('mouseleave', () => {
            exitButton.style.transform = 'scale(1)';
        });
        exitButton.addEventListener('click', () => this.exitGame());
        document.body.appendChild(exitButton);
        this.exitButton = exitButton;
    }
    
    /**
     * Create star indicators on sides
     */
    createStarIndicators() {
        // Left side stars
        const leftStars = document.createElement('div');
        leftStars.style.cssText = `
            position: absolute;
            left: max(20px, 2vw);
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: clamp(15px, 2vh, 20px);
            pointer-events: none;
        `;
        
        // Right side stars
        const rightStars = document.createElement('div');
        rightStars.style.cssText = `
            position: absolute;
            right: max(20px, 2vw);
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: clamp(15px, 2vh, 20px);
            pointer-events: none;
        `;
        
        // Create 3 stars on each side
        for (let i = 0; i < 3; i++) {
            const leftStar = this.createStarElement(i);
            const rightStar = this.createStarElement(i);
            leftStars.appendChild(leftStar);
            rightStars.appendChild(rightStar);
        }
        
        this.gameUI.appendChild(leftStars);
        this.gameUI.appendChild(rightStars);
        
        // Store references for score-based activation
        this.starElements = {
            left: leftStars.children,
            right: rightStars.children
        };
    }
    
    /**
     * Create a star UI element
     */
    createStarElement(index) {
        const star = document.createElement('div');
        star.style.cssText = `
            width: clamp(35px, 5vw, 50px);
            height: clamp(35px, 5vw, 50px);
            font-size: clamp(28px, 4vw, 40px);
            text-align: center;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666666;
            text-shadow: 0 0 10px rgba(255,215,0,0.5);
            transition: all 0.3s ease;
        `;
        star.textContent = '★';
        star.dataset.starIndex = index;
        star.dataset.active = 'false';
        return star;
    }
    
    /**
     * Create custom cursor for arcade mode
     */
    createCustomCursor() {
        console.log('[RhythmArcade3D] 🖱️ Creating custom cursor');
        
        document.body.style.cursor = 'crosshair';
    }
    
    /**
     * Hide minimap temporarily
     */
    hideMinimapTemporarily() {
        console.log('[RhythmArcade3D] 🗺️ Hiding minimap');
        
        const minimap = document.querySelector('.minimap, #minimap, [class*=\"minimap\"], [id*=\"minimap\"]');
        if (minimap) {
            this.originalMinimapDisplay = minimap.style.display;
            minimap.style.display = 'none';
        }
    }
    
    /**
     * Force an object and all its children to layer 0 recursively
     */
    forceLayer0Recursively(object) {
        // Optimized version - single pass
        object.layers.set(0);
        object.layers.mask = 1;
        
        object.traverse((child) => {
            child.layers.set(0);
            child.layers.mask = 1;
            
            if (child.isMesh) {
                child.renderOrder = 0;
                child.frustumCulled = false;
                child.visible = true;
            }
        });
    }
    
    /**
     * Run the actual rhythm game
     */
    async runRhythmGame() {
        console.log('[RhythmArcade3D] 🎵 Starting rhythm game loop');
        
        this.isPlaying = true;
        this.gameTime = 0;
        this.score = 0;
        this.combo = 0;
        this.health = 100;
        
        // Start the music
        this.startArcadeMusic();
        
        let lastTime = performance.now();
        
        return new Promise((resolve) => {
            const gameLoop = () => {
                // Safety check for cleanup
                if (this.isCleaningUp) {
                    console.log('[RhythmArcade3D] Game loop stopped due to cleanup');
                    return;
                }
                
                if (!this.isPlaying) {
                    // Cancel any pending animation frame
                    if (this.animationFrameId) {
                        cancelAnimationFrame(this.animationFrameId);
                        this.animationFrameId = null;
                    }
                    
                    resolve({
                        won: this.health > 0,
                        score: this.score,
                        maxCombo: this.maxCombo,
                        health: this.health,
                        perfectHits: this.perfectHits,
                        notesHit: this.notesHit,
                        notesMissed: this.notesMissed
                    });
                    return;
                }
                
                const currentTime = performance.now();
                
                if (!this.isPaused) {
                    const deltaTime = Math.min(currentTime - lastTime, 100); // Cap at 100ms to prevent huge jumps
                    this.gameTime += deltaTime;
                    
                    this.updateGame(deltaTime);
                    this.updateUI();
                    
                    // End game conditions
                    if (this.health <= 0) {
                        this.isPlaying = false;
                    } else if (this.gameTime > 90000) { // 1.5 minute game
                        this.isPlaying = false;
                    }
                }
                
                lastTime = currentTime;
                
                if (this.isPlaying && !this.isCleaningUp) {
                    this.animationFrameId = requestAnimationFrame(gameLoop);
                } else {
                    // Cancel any pending animation frame
                    if (this.animationFrameId) {
                        cancelAnimationFrame(this.animationFrameId);
                        this.animationFrameId = null;
                    }
                    
                    resolve({
                        won: this.health > 0,
                        score: this.score,
                        maxCombo: this.maxCombo,
                        health: this.health,
                        perfectHits: this.perfectHits,
                        notesHit: this.notesHit,
                        notesMissed: this.notesMissed
                    });
                }
            };
            
            this.animationFrameId = requestAnimationFrame(gameLoop);
        });
    }
    
    /**
     * Update game logic
     */
    updateGame(deltaTime) {
        // Increment frame count for performance optimizations
        this.frameCount = (this.frameCount || 0) + 1;
        
        // Update difficulty level (check every 30 frames)
        if (this.frameCount % 30 === 0) {
            this.updateDifficultyLevel();
        }
        
        // Update falling enemies
        this.updateFallingEnemies(deltaTime);
        
        // Spawn new enemies based on beat
        this.updateEnemySpawning();
        
        // Update visual effects
        this.updateVisualEffects(deltaTime);
        
        // Update key indicators (every 2 frames)
        if (this.frameCount % 2 === 0) {
            this.updateKeyIndicators(deltaTime);
        }
    }
    
    /**
     * Update difficulty level based on score
     */
    updateDifficultyLevel() {
        const oldLevel = this.level;
        
        // Find current level based on score
        for (let i = this.levelThresholds.length - 1; i >= 0; i--) {
            if (this.score >= this.levelThresholds[i]) {
                this.level = i + 1;
                break;
            }
        }
        
        // Level up effects
        if (this.level > oldLevel) {
            console.log(`[RhythmArcade3D] LEVEL UP! Now level ${this.level}`);
            this.createLevelUpEffect();
            
            // Increase speed
            this.speed = 1.0 + (this.level - 1) * 0.15;
            
            // Adjust beat interval for faster rhythm
            this.beatInterval = Math.max(0.3, 0.5 - (this.level - 1) * 0.03);
        }
    }
    
    /**
     * Create level up visual effect
     */
    createLevelUpEffect() {
        if (!this.minigameScene) return;
        
        // Create expanding ring effect
        const ringGeometry = new THREE.TorusGeometry(10, 0.5, 8, 32);
        const ringMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFD700,
            emissive: 0xFFD700,
            emissiveIntensity: 1.0,
            transparent: true,
            opacity: 1.0
        });
        
        const ring = new THREE.Mesh(ringGeometry, ringMaterial);
        ring.position.set(0, 5, 0);
        ring.rotation.x = -Math.PI / 2;
        
        this.minigameScene.add(ring);
        
        // Animate the ring with proper cleanup tracking
        const startTime = Date.now();
        let ringAnimationId = null;
        const animateRing = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / 1000; // 1 second animation
            
            if (progress >= 1 || this.isCleaningUp || !this.isPlaying) {
                if (this.minigameScene && ring.parent) {
                    this.minigameScene.remove(ring);
                }
                if (ring.geometry) ring.geometry.dispose();
                if (ringMaterial) ringMaterial.dispose();
                if (ringAnimationId) {
                    cancelAnimationFrame(ringAnimationId);
                    // Remove from tracked animations if we add that feature
                }
                return;
            }
            
            ring.scale.setScalar(1 + progress * 3);
            ringMaterial.opacity = 1 - progress;
            
            if (!this.isCleaningUp && this.isPlaying) {
                ringAnimationId = requestAnimationFrame(animateRing);
            }
        };
        
        ringAnimationId = requestAnimationFrame(animateRing);
        
        // Flash the screen
        this.createScreenFlash(0xFFD700, 0.5);
    }
    
    /**
     * Create screen flash effect
     */
    createScreenFlash(color, intensity) {
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: ${new THREE.Color(color).getStyle()};
            opacity: ${intensity};
            pointer-events: none;
            z-index: 2000;
            transition: opacity 0.3s ease-out;
        `;
        
        document.body.appendChild(flash);
        
        // Fade out with tracked timeout
        const fadeTimeout = setTimeout(() => {
            flash.style.opacity = '0';
            const removeTimeout = setTimeout(() => {
                if (document.body.contains(flash)) {
                    document.body.removeChild(flash);
                }
                this.activeTimeouts.delete(removeTimeout);
            }, 300);
            this.activeTimeouts.add(removeTimeout);
            this.activeTimeouts.delete(fadeTimeout);
        }, 50);
        this.activeTimeouts.add(fadeTimeout);
    }
    
    /**
     * Update falling enemies
     */
    updateFallingEnemies(deltaTime) {
        for (let i = this.fallingEnemies.length - 1; i >= 0; i--) {
            const enemy = this.fallingEnemies[i];
            enemy.position.z += this.speed * deltaTime / 30;
            
            // Check if enemy passed target zone
            if (enemy.position.z >= 22) {
                // Missed enemy - take damage
                this.health -= 10;
                this.combo = 0;
                this.notesMissed++;
                this.removeFallingEnemy(i);
                
                // Screen shake on miss
                this.createCameraShake(0.2, 200);
            }
        }
    }
    
    /**
     * Update enemy spawning based on beat
     */
    updateEnemySpawning() {
        const currentBeat = this.getCurrentBeat();
        
        if (currentBeat > this.currentBeat) {
            this.currentBeat = currentBeat;
            
            // Check if we should spawn an enemy this beat
            const pattern = this.enemyPatterns[this.currentPattern % this.enemyPatterns.length];
            const relativeBeat = currentBeat - this.patternStartBeat;
            
            for (const spawn of pattern) {
                if (spawn.beat === relativeBeat) {
                    this.spawnEnemy(spawn.lane);
                }
            }
            
            // Move to next pattern
            if (relativeBeat >= 8) {
                this.patternStartBeat = currentBeat;
                this.currentPattern++;
            }
        }
    }
    
    /**
     * Initialize object pool for notes
     */
    initializeNotePool() {
        console.log('[RhythmArcade3D] Initializing note object pool');
        
        // Pre-create notes for each lane
        for (let i = 0; i < this.maxPoolSize; i++) {
            const lane = i % 4;
            const note = this.createVoxelNote(lane);
            if (note) {
                note.visible = false;
                note.userData.pooled = true;
                note.userData.originalLane = lane;
                this.forceLayer0Recursively(note);
                // Add to gameBoard instead of minigameScene for proper positioning
                if (this.gameBoard) {
                    this.gameBoard.add(note);
                } else {
                    this.minigameScene.add(note);
                }
                this.notePool.push(note);
            }
        }
        
        console.log(`[RhythmArcade3D] Created ${this.notePool.length} notes in pool`);
    }
    
    /**
     * Get note from pool
     */
    getNoteFromPool(lane) {
        // Try to find a note for the specific lane first
        for (let i = 0; i < this.notePool.length; i++) {
            const note = this.notePool[i];
            if (note.userData.originalLane === lane) {
                this.notePool.splice(i, 1);
                return note;
            }
        }
        
        // If no specific lane note available, use any note and update its appearance
        if (this.notePool.length > 0) {
            const note = this.notePool.pop();
            this.updateNoteAppearance(note, lane);
            return note;
        }
        
        // If pool is empty, create new note
        console.warn('[RhythmArcade3D] Note pool empty, creating new note');
        return this.createVoxelNote(lane);
    }
    
    /**
     * Return note to pool
     */
    returnNoteToPool(note) {
        if (this.notePool.length < this.maxPoolSize) {
            note.visible = false;
            note.position.set(0, -100, 0); // Move off-screen
            note.userData.velocity = null;
            note.userData.spawnTime = null;
            note.userData.noteType = 'normal';
            
            // CRITICAL: Remove from scene to prevent memory buildup
            if (note.parent) {
                note.parent.remove(note);
            }
            
            this.notePool.push(note);
        } else {
            // Pool is full, dispose of the note
            if (note.parent) {
                note.parent.remove(note);
            }
            note.traverse(child => {
                if (child.geometry) child.geometry.dispose();
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(m => m.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
        }
    }
    
    /**
     * Update note appearance for different lane
     */
    updateNoteAppearance(note, newLane) {
        const color = this.laneColors[newLane];
        note.userData.lane = newLane;
        note.userData.originalLane = newLane;
        
        // Update colors - cache children to avoid traverse
        if (!note.userData.cachedChildren) {
            note.userData.cachedChildren = [];
            note.traverse(child => {
                if ((child.isMesh && child.material && child.material.emissive) || child.isPointLight) {
                    note.userData.cachedChildren.push(child);
                }
            });
        }
        
        // Update cached children only
        note.userData.cachedChildren.forEach(child => {
            if (child.isMesh && child.material) {
                child.material.color.setHex(color);
                child.material.emissive.setHex(color);
            } else if (child.isPointLight) {
                child.color.setHex(color);
            }
        });
    }
    
    /**
     * Spawn a falling enemy in the specified lane
     */
    spawnEnemy(lane) {
        console.log(`[RhythmArcade3D] Spawning note in lane ${lane}`);
        
        // Determine if this should be a special note
        const specialChance = Math.random();
        let noteType = 'normal';
        
        if (specialChance < 0.05 && this.level >= 3) {
            noteType = 'golden'; // 5% chance for golden notes at level 3+
        } else if (specialChance < 0.10 && this.level >= 2) {
            noteType = 'health'; // 5% chance for health notes at level 2+
        } else if (specialChance < 0.15 && this.combo >= 10) {
            noteType = 'multiplier'; // 5% chance for multiplier when combo is high
        }
        
        // Get note from pool
        const note = this.getNoteFromPool(lane);
        
        if (note) {
            const trackWidth = 32;
            const laneWidth = trackWidth / 4;
            const laneX = -trackWidth/2 + lane * laneWidth + laneWidth/2;
            
            // CRITICAL: Re-add note to scene if it was pooled
            if (!note.parent && this.gameBoard) {
                this.gameBoard.add(note);
            }
            
            note.position.set(laneX, 2, -20); // Start position
            note.visible = true;
            note.userData.lane = lane;
            note.userData.isEnemy = true; // Keep for compatibility
            note.userData.isNote = true;
            note.userData.noteType = noteType;
            note.userData.spawnTime = Date.now();
            
            // Apply special note effects
            this.applySpecialNoteEffects(note, noteType);
            
            this.fallingEnemies.push(note);
            this.activeNotes.push(note);
            
            // Activate key indicator for this lane
            this.activateKeyIndicator(lane);
            
            console.log(`[RhythmArcade3D] ${noteType} note spawned at position:`, note.position);
        } else {
            console.warn(`[RhythmArcade3D] Failed to get note for lane ${lane}`);
        }
    }
    
    /**
     * Apply special effects to power-up notes
     */
    applySpecialNoteEffects(note, noteType) {
        switch (noteType) {
            case 'golden':
                // Golden notes worth 3x points
                note.traverse(child => {
                    if (child.isMesh && child.material) {
                        child.material.color.setHex(0xFFD700);
                        child.material.emissive.setHex(0xFFD700);
                        child.material.emissiveIntensity = 0.8;
                    }
                    if (child.isPointLight) {
                        child.color.setHex(0xFFD700);
                        child.intensity = 1.0;
                    }
                });
                note.userData.pulseSpeed = 0.005;
                break;
                
            case 'health':
                // Health notes restore 20 HP
                note.traverse(child => {
                    if (child.isMesh && child.material) {
                        child.material.color.setHex(0x00FF00);
                        child.material.emissive.setHex(0x00FF00);
                        child.material.emissiveIntensity = 0.6;
                    }
                    if (child.isPointLight) {
                        child.color.setHex(0x00FF00);
                        child.intensity = 0.8;
                    }
                });
                // Add cross symbol
                this.addHealthSymbol(note);
                break;
                
            case 'multiplier':
                // Multiplier notes double combo points
                note.traverse(child => {
                    if (child.isMesh && child.material) {
                        child.material.color.setHex(0xFF00FF);
                        child.material.emissive.setHex(0xFF00FF);
                        child.material.emissiveIntensity = 0.7;
                    }
                    if (child.isPointLight) {
                        child.color.setHex(0xFF00FF);
                        child.intensity = 0.9;
                    }
                });
                note.userData.rotationSpeed = 0.04; // Faster rotation
                break;
        }
    }
    
    /**
     * Add health symbol to health note
     */
    addHealthSymbol(note) {
        const voxelSize = VOXEL_SIZE * 0.3;
        const symbolMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFFFFF,
            emissive: 0xFFFFFF,
            emissiveIntensity: 0.8
        });
        
        // Create cross shape
        const horizontalBar = new THREE.BoxGeometry(voxelSize * 3, voxelSize, voxelSize);
        const verticalBar = new THREE.BoxGeometry(voxelSize, voxelSize * 3, voxelSize);
        
        const hBar = new THREE.Mesh(horizontalBar, symbolMaterial);
        const vBar = new THREE.Mesh(verticalBar, symbolMaterial);
        
        hBar.position.y = voxelSize * 3;
        vBar.position.y = voxelSize * 3;
        
        note.add(hBar);
        note.add(vBar);
    }
    
    /**
     * Create a voxel-based note
     */
    createVoxelNote(lane) {
        const noteGroup = new THREE.Group();
        const voxelSize = VOXEL_SIZE * 0.8;
        const color = this.laneColors[lane];
        
        // Simplified note shape for performance - single cube instead of 10 voxels
        const noteSize = voxelSize * 2.5;
        
        // Material for the note
        const noteMaterial = new THREE.MeshStandardMaterial({
            color: color,
            emissive: color,
            emissiveIntensity: 0.3,
            roughness: 0.2,
            metalness: 0.6
        });
        
        // Create single optimized mesh instead of multiple voxels
        const noteGeometry = new THREE.BoxGeometry(noteSize, noteSize * 0.8, noteSize);
        const noteMesh = new THREE.Mesh(noteGeometry, noteMaterial);
        noteMesh.position.y = voxelSize;
        noteGroup.add(noteMesh);
        
        // PERFORMANCE: Skip complex voxel icons - use simple emissive indicator
        // The color already differentiates the lanes
        
        // PERFORMANCE: No individual lights on notes - use emissive materials only
        // This significantly reduces the number of lights in the scene
        noteMaterial.emissiveIntensity = 0.5; // Increase emissive to compensate
        
        // Animation properties
        noteGroup.userData.rotationSpeed = 0.02;
        noteGroup.userData.floatOffset = Math.random() * Math.PI * 2;
        
        return noteGroup;
    }
    
    /**
     * Create voxel icon for note (simple character representation)
     */
    createVoxelNoteIcon(lane) {
        const iconGroup = new THREE.Group();
        const voxelSize = VOXEL_SIZE * 0.3;
        
        // Different icons for each lane
        const iconPatterns = [
            // Lane 0 - Heart shape
            [
                {x: -1, y: 1, z: 0}, {x: 1, y: 1, z: 0},
                {x: -1, y: 0, z: 0}, {x: 0, y: 0, z: 0}, {x: 1, y: 0, z: 0},
                {x: 0, y: -1, z: 0}
            ],
            // Lane 1 - Diamond shape
            [
                {x: 0, y: 2, z: 0},
                {x: -1, y: 1, z: 0}, {x: 1, y: 1, z: 0},
                {x: -1, y: 0, z: 0}, {x: 1, y: 0, z: 0},
                {x: 0, y: -1, z: 0}
            ],
            // Lane 2 - Star shape
            [
                {x: 0, y: 2, z: 0},
                {x: -1, y: 1, z: 0}, {x: 0, y: 1, z: 0}, {x: 1, y: 1, z: 0},
                {x: 0, y: 0, z: 0},
                {x: -1, y: -1, z: 0}, {x: 1, y: -1, z: 0}
            ],
            // Lane 3 - Circle shape (approximated)
            [
                {x: -1, y: 1, z: 0}, {x: 0, y: 1, z: 0}, {x: 1, y: 1, z: 0},
                {x: -1, y: 0, z: 0}, {x: 1, y: 0, z: 0},
                {x: -1, y: -1, z: 0}, {x: 0, y: -1, z: 0}, {x: 1, y: -1, z: 0}
            ]
        ];
        
        const iconMaterial = new THREE.MeshStandardMaterial({
            color: 0xFFFFFF,
            emissive: 0xFFFFFF,
            emissiveIntensity: 0.5,
            roughness: 0.1,
            metalness: 0.8
        });
        
        const pattern = iconPatterns[lane];
        pattern.forEach(point => {
            const voxelGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            const voxel = new THREE.Mesh(voxelGeometry, iconMaterial);
            voxel.position.set(
                point.x * voxelSize,
                point.y * voxelSize,
                point.z * voxelSize
            );
            iconGroup.add(voxel);
        });
        
        return iconGroup;
    }
    
    /**
     * Remove a falling enemy
     */
    removeFallingEnemy(index) {
        const enemy = this.fallingEnemies[index];
        if (enemy) {
            // Return to pool if it's a note
            if (enemy.userData.isNote) {
                this.returnNoteToPool(enemy);
                const activeIndex = this.activeNotes.indexOf(enemy);
                if (activeIndex > -1) {
                    this.activeNotes.splice(activeIndex, 1);
                }
            } else {
                // Old cleanup for non-pooled objects
                this.minigameScene.remove(enemy);
            }
            this.fallingEnemies.splice(index, 1);
        }
    }
    
    /**
     * Initialize cached effect objects for performance
     */
    initializeCachedEffects() {
        this.cachedEffectObjects = {
            noteLights: [],
            decorationStars: [],
            glowingObjects: [],
            neonLights: []
        };
        
        // Cache decoration and static effect objects only
        if (this.gameBoard) {
            this.gameBoard.traverse(child => {
                if (child.userData?.isDecorationStar) {
                    this.cachedEffectObjects.decorationStars.push(child);
                }
            });
        }
        
        if (this.minigameScene) {
            this.minigameScene.traverse(child => {
                if (child.userData?.isGlowing) {
                    this.cachedEffectObjects.glowingObjects.push(child);
                }
                if (child.userData?.isNeonLight) {
                    this.cachedEffectObjects.neonLights.push(child);
                }
            });
        }
    }
    
    /**
     * Update visual effects
     */
    updateVisualEffects(deltaTime) {
        // Update screen flickering and neon pulsing
        const currentTime = Date.now();
        const frameCount = this.frameCount || 0;
        
        // Initialize cached objects once on scene creation instead of during gameplay
        if (!this.cachedEffectObjects) {
            this.initializeCachedEffects();
        }
        
        // Update falling notes animation
        for (let i = 0; i < this.fallingEnemies.length; i++) {
            const note = this.fallingEnemies[i];
            if (note.userData.isNote) {
                // Rotate the note
                note.rotation.y += note.userData.rotationSpeed || 0.02;
                
                // Floating animation (update less frequently)
                if (frameCount % 2 === 0) {
                    const floatOffset = note.userData.floatOffset || 0;
                    note.position.y = 2 + Math.sin(currentTime * 0.002 + floatOffset) * 0.3;
                }
            }
        }
        
        // PERFORMANCE: Skip light updates since notes no longer have individual lights
        // This was causing unnecessary overhead
        
        // Update decoration stars
        for (let i = 0; i < this.cachedEffectObjects.decorationStars.length; i++) {
            const star = this.cachedEffectObjects.decorationStars[i];
            star.rotation.y += star.userData.rotationSpeed;
        }
        
        // Screen flicker and neon effects (update less frequently)
        if (frameCount % 4 === 0) {
            // Glowing objects
            const flickerValue = Math.sin(currentTime * 0.01) * 0.2;
            for (let i = 0; i < this.cachedEffectObjects.glowingObjects.length; i++) {
                const obj = this.cachedEffectObjects.glowingObjects[i];
                if (obj.material) {
                    const baseIntensity = obj.userData.baseEmissiveIntensity || 0.3;
                    obj.material.emissiveIntensity = Math.max(0.1, baseIntensity + flickerValue);
                }
            }
            
            // Neon lights
            const pulseValue = Math.sin(currentTime * 0.005) * 0.3;
            for (let i = 0; i < this.cachedEffectObjects.neonLights.length; i++) {
                const light = this.cachedEffectObjects.neonLights[i];
                const baseIntensity = light.userData.baseIntensity || 3.0;
                light.intensity = Math.max(1.0, baseIntensity + pulseValue);
            }
        }
        
        // Update hit feedback texts
        this.updateHitFeedbacks(deltaTime);
        
        // Update hit effects
        this.updateHitEffects(deltaTime);
        
        // Update camera shake
        this.updateCameraShake(deltaTime);
    }
    
    /**
     * Create camera shake effect
     */
    createCameraShake(intensity, duration) {
        this.cameraShake.active = true;
        this.cameraShake.intensity = intensity;
        this.cameraShake.duration = duration;
        this.cameraShake.startTime = Date.now();
        
        if (!this.cameraShake.originalPosition && this.sceneManager.camera) {
            this.cameraShake.originalPosition = this.sceneManager.camera.position.clone();
        }
    }
    
    /**
     * Update camera shake
     */
    updateCameraShake(deltaTime) {
        if (!this.cameraShake.active || !this.sceneManager.camera) return;
        
        const elapsed = Date.now() - this.cameraShake.startTime;
        const progress = elapsed / this.cameraShake.duration;
        
        if (progress >= 1) {
            // Reset camera position
            if (this.cameraShake.originalPosition) {
                this.sceneManager.camera.position.copy(this.cameraShake.originalPosition);
            }
            this.cameraShake.active = false;
            return;
        }
        
        // Apply shake
        const intensity = this.cameraShake.intensity * (1 - progress);
        const offsetX = (Math.random() - 0.5) * intensity;
        const offsetY = (Math.random() - 0.5) * intensity;
        const offsetZ = (Math.random() - 0.5) * intensity * 0.5;
        
        this.sceneManager.camera.position.x = this.cameraShake.originalPosition.x + offsetX;
        this.sceneManager.camera.position.y = this.cameraShake.originalPosition.y + offsetY;
        this.sceneManager.camera.position.z = this.cameraShake.originalPosition.z + offsetZ;
    }
    
    /**
     * Update hit effects animation
     */
    updateHitEffects(deltaTime) {
        const currentTime = Date.now();
        
        for (let i = this.hitEffects.length - 1; i >= 0; i--) {
            const effect = this.hitEffects[i];
            let shouldRemove = true;
            
            effect.traverse(child => {
                if (child.userData.velocity) {
                    // Update particle position
                    child.position.x += child.userData.velocity.x * deltaTime / 100;
                    child.position.y += child.userData.velocity.y * deltaTime / 100;
                    child.position.z += child.userData.velocity.z * deltaTime / 100;
                    
                    // Apply gravity
                    child.userData.velocity.y -= 0.2;
                    
                    // Update opacity
                    if (child.userData.startTime) {
                        const elapsed = currentTime - child.userData.startTime;
                        const progress = elapsed / child.userData.lifetime;
                        if (progress < 1) {
                            shouldRemove = false;
                            if (child.userData.material) {
                                child.userData.material.opacity = 1 - progress;
                            }
                        }
                    }
                }
                
                if (child.userData.expandSpeed) {
                    // Expand star burst
                    child.position.x *= 1 + child.userData.expandSpeed * deltaTime / 100;
                    child.position.z *= 1 + child.userData.expandSpeed * deltaTime / 100;
                    
                    if (child.userData.material) {
                        child.userData.material.opacity *= 0.98;
                        if (child.userData.material.opacity < 0.01) {
                            shouldRemove = true;
                        }
                    }
                }
            });
            
            if (shouldRemove) {
                // Properly dispose of effect resources
                effect.traverse(child => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
                this.minigameScene.remove(effect);
                this.hitEffects.splice(i, 1);
            }
        }
    }
    
    /**
     * Activate key indicator for a lane
     */
    activateKeyIndicator(lane) {
        if (lane >= 0 && lane < this.keyIndicators.length) {
            const indicator = this.keyIndicators[lane];
            indicator.isActive = true;
            indicator.activationTime = Date.now();
            
            // Make it glow brighter
            indicator.background.material.emissiveIntensity = 0.8;
            indicator.text.material.opacity = 1.0;
            
            console.log(`[RhythmArcade3D] Activated key indicator for lane ${lane}`);
        }
    }
    
    /**
     * Deactivate key indicator for a lane
     */
    deactivateKeyIndicator(lane) {
        if (lane >= 0 && lane < this.keyIndicators.length) {
            const indicator = this.keyIndicators[lane];
            indicator.isActive = false;
            
            // Return to normal glow
            indicator.background.material.emissiveIntensity = 0.1;
            indicator.text.material.opacity = 0.9;
        }
    }
    
    /**
     * Update key indicators animation
     */
    updateKeyIndicators(deltaTime) {
        const currentTime = Date.now();
        
        for (let i = 0; i < this.keyIndicators.length; i++) {
            const indicator = this.keyIndicators[i];
            
            if (indicator.isActive) {
                // Check if there are still enemies in this lane
                const hasEnemiesInLane = this.fallingEnemies.some(enemy => enemy.userData.lane === i);
                
                if (!hasEnemiesInLane) {
                    // No more enemies in this lane, deactivate indicator
                    this.deactivateKeyIndicator(i);
                    continue;
                }
                
                // Pulsing animation for active indicators
                const pulseSpeed = 0.01;
                const pulseAmplitude = 0.4;
                const basePulse = Math.sin(currentTime * pulseSpeed) * pulseAmplitude;
                
                indicator.background.material.emissiveIntensity = 0.8 + basePulse;
                
                // Extra bright pulse when enemy is near target zone
                const nearbyEnemies = this.fallingEnemies.filter(enemy => 
                    enemy.userData.lane === i && enemy.position.z >= 10 && enemy.position.z <= 20
                );
                
                if (nearbyEnemies.length > 0) {
                    // Fast pulse when enemy is close
                    const fastPulse = Math.sin(currentTime * 0.02) * 0.3;
                    indicator.background.material.emissiveIntensity = 1.0 + fastPulse;
                    
                    // Scale text slightly for emphasis
                    const scaleBoost = 1 + Math.sin(currentTime * 0.02) * 0.1;
                    indicator.text.scale.set(2 * scaleBoost, 2 * scaleBoost, 1);
                } else {
                    // Reset text scale
                    indicator.text.scale.set(2, 2, 1);
                }
            }
        }
    }
    
    /**
     * Update UI elements
     */
    updateUI() {
        if (!this.gameUI) return;
        
        // Cache DOM elements if not already cached
        if (!this.uiElements) {
            this.uiElements = {
                score: document.getElementById('arcade-score'),
                combo: document.getElementById('arcade-combo'),
                healthFill: document.getElementById('arcade-health-fill'),
                level: document.getElementById('arcade-level')
            };
        }
        
        // Update score (only if changed)
        if (this.uiElements.score && this.lastDisplayedScore !== this.score) {
            this.uiElements.score.textContent = this.score;
            // Simple class toggle instead of inline styles
            this.uiElements.score.classList.add('pulse');
            const pulseTimeout = setTimeout(() => {
                if (this.uiElements.score) {
                    this.uiElements.score.classList.remove('pulse');
                }
                this.activeTimeouts.delete(pulseTimeout);
            }, 200);
            this.activeTimeouts.add(pulseTimeout);
            this.lastDisplayedScore = this.score;
        }
        
        // Update combo (only if changed)
        if (this.uiElements.combo && this.lastDisplayedCombo !== this.combo) {
            this.uiElements.combo.textContent = this.combo;
            // Use CSS classes instead of inline styles
            this.uiElements.combo.className = 'arcade-stat-value';
            if (this.combo >= 50) {
                this.uiElements.combo.classList.add('combo-high');
            } else if (this.combo >= 25) {
                this.uiElements.combo.classList.add('combo-medium');
            } else if (this.combo >= 10) {
                this.uiElements.combo.classList.add('combo-low');
            }
            this.lastDisplayedCombo = this.combo;
        }
        
        // Update health bar (only if changed)
        if (this.uiElements.healthFill && this.lastDisplayedHealth !== this.health) {
            const healthPercent = Math.max(0, Math.min(100, this.health));
            this.uiElements.healthFill.style.width = healthPercent + '%';
            this.lastDisplayedHealth = this.health;
        }
        
        // Update star indicators less frequently
        if (this.frameCount % 10 === 0) {
            this.updateStarIndicators();
        }
        
        // Update level display (only if changed)
        if (this.uiElements.level && this.lastDisplayedLevel !== this.level) {
            this.uiElements.level.textContent = this.level;
            this.lastDisplayedLevel = this.level;
        }
        
        // Check combo milestones less frequently
        if (this.frameCount % 5 === 0) {
            this.checkComboMilestones();
        }
        
        this.frameCount = (this.frameCount || 0) + 1;
    }
    
    /**
     * Check for combo milestones and provide bonuses
     */
    checkComboMilestones() {
        const milestones = [10, 25, 50, 100, 200];
        
        for (const milestone of milestones) {
            if (this.combo === milestone) {
                // Bonus score for reaching milestone
                const bonus = milestone * 50;
                this.score += bonus;
                
                // Create celebration effect
                this.createHitFeedback(`${milestone} COMBO! +${bonus}`, 0, 0xFFD700);
                this.createScreenFlash(0xFFD700, 0.3);
                
                // Health bonus at higher milestones
                if (milestone >= 50) {
                    this.health = Math.min(100, this.health + 10);
                }
                
                break;
            }
        }
    }
    
    /**
     * Update star indicators based on score
     */
    updateStarIndicators() {
        if (!this.starElements) return;
        
        // Activate stars based on score thresholds
        const starThresholds = [1000, 2500, 5000]; // Score thresholds for each star
        let activeStars = 0;
        
        for (let i = 0; i < starThresholds.length; i++) {
            if (this.score >= starThresholds[i]) {
                activeStars++;
            }
        }
        
        // Update left and right stars
        ['left', 'right'].forEach(side => {
            const stars = this.starElements[side];
            for (let i = 0; i < stars.length; i++) {
                const star = stars[i];
                if (i < activeStars && star.dataset.active === 'false') {
                    // Activate star with animation
                    star.dataset.active = 'true';
                    star.style.color = '#FFD700';
                    star.style.transform = 'scale(1.5) rotate(360deg)';
                    star.style.textShadow = '0 0 20px #FFD700, 0 0 40px #FFD700';
                    
                    const starTimeout = setTimeout(() => {
                        if (star) {
                            star.style.transform = 'scale(1) rotate(360deg)';
                        }
                        this.activeTimeouts.delete(starTimeout);
                    }, 500);
                    this.activeTimeouts.add(starTimeout);
                } else if (i >= activeStars && star.dataset.active === 'true') {
                    // Deactivate star
                    star.dataset.active = 'false';
                    star.style.color = '#666666';
                    star.style.transform = 'scale(1) rotate(0deg)';
                    star.style.textShadow = '0 0 10px rgba(255,215,0,0.5)';
                }
            }
        });
    }
    
    /**
     * Handle key down events
     */
    handleKeyDown(event) {
        if (!this.isInMinigame || !this.isPlaying) return;
        
        const key = event.key.toLowerCase();
        
        // Prevent key repeat
        if (this.keys[key]) return;
        
        // WASD controls for lanes
        if (key === 'w') {
            this.hitLane(0); // Lane 1
        } else if (key === 'a') {
            this.hitLane(1); // Lane 2  
        } else if (key === 's') {
            this.hitLane(2); // Lane 3
        } else if (key === 'd') {
            this.hitLane(3); // Lane 4
        }
        
        // Legacy 1234 support
        if (['1', '2', '3', '4'].includes(key)) {
            const lane = parseInt(key) - 1;
            this.hitLane(lane);
        }
        
        if (key === 'escape') {
            this.exitGame();
        }
        
        if (key === 'p' || key === ' ') {
            this.togglePause();
        }
        
        // Update key state
        if (['w', 'a', 's', 'd'].includes(key)) {
            this.keys[key] = true;
        }
    }
    
    /**
     * Handle key up events
     */
    handleKeyUp(event) {
        const key = event.key.toLowerCase();
        
        // Update key state
        if (['w', 'a', 's', 'd'].includes(key)) {
            this.keys[key] = false;
        }
    }
    
    /**
     * Handle mouse clicks
     */
    handleMouseClick(event) {
        if (!this.isInMinigame) return;
        
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        const intersects = this.raycaster.intersectObjects(this.targetZones, true);
        
        if (intersects.length > 0) {
            const targetZone = intersects[0].object;
            if (targetZone.userData.isTargetZone) {
                this.hitLane(targetZone.userData.lane);
            }
        }
    }
    
    /**
     * Handle mouse movement
     */
    handleMouseMove(event) {
        // Handle mouse movement for hover effects if needed
    }
    
    /**
     * Setup mobile controls
     */
    setupMobileControls() {
        // Check if device is mobile
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         window.innerWidth <= 768;
        
        if (!isMobile) {
            console.log('[RhythmArcade3D] Desktop detected, skipping mobile controls');
            return;
        }
        
        console.log('[RhythmArcade3D] 📱 Setting up mobile controls');
        this.mobileControls.isEnabled = true;
        
        // Create touch zones overlay
        this.createMobileTouchZones();
    }
    
    /**
     * Create mobile touch zones for lanes
     */
    createMobileTouchZones() {
        // Create overlay container
        const touchOverlay = document.createElement('div');
        touchOverlay.id = 'arcade-touch-overlay';
        touchOverlay.style.cssText = `
            position: fixed;
            bottom: 120px;
            left: 10px;
            right: 10px;
            height: 40vh;
            max-height: 300px;
            pointer-events: auto;
            z-index: 500;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 5px;
            box-sizing: border-box;
        `;
        
        // Create 4 touch zones for WASD lanes
        const laneLabels = ['W', 'A', 'S', 'D'];
        const laneColors = ['#FF4444', '#44FF44', '#4444FF', '#FFFF44'];
        
        for (let i = 0; i < 4; i++) {
            const touchZone = document.createElement('div');
            touchZone.style.cssText = `
                background: rgba(${parseInt(laneColors[i].slice(1, 3), 16)}, ${parseInt(laneColors[i].slice(3, 5), 16)}, ${parseInt(laneColors[i].slice(5, 7), 16)}, 0.3);
                border: 2px solid ${laneColors[i]};
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                font-weight: bold;
                color: white;
                text-shadow: 0 0 10px ${laneColors[i]};
                user-select: none;
                touch-action: manipulation;
            `;
            touchZone.textContent = laneLabels[i];
            touchZone.dataset.lane = i;
            
            // Add touch event listeners to each zone
            touchZone.addEventListener('touchstart', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.hitLane(parseInt(touchZone.dataset.lane));
                // Visual feedback
                touchZone.style.background = laneColors[i];
                const feedbackTimeout = setTimeout(() => {
                    if (touchZone) {
                        touchZone.style.background = `rgba(${parseInt(laneColors[i].slice(1, 3), 16)}, ${parseInt(laneColors[i].slice(3, 5), 16)}, ${parseInt(laneColors[i].slice(5, 7), 16)}, 0.3)`;
                    }
                    this.activeTimeouts.delete(feedbackTimeout);
                }, 150);
                this.activeTimeouts.add(feedbackTimeout);
            }, { passive: false });
            
            touchOverlay.appendChild(touchZone);
            this.mobileControls.touchZones.push(touchZone);
        }
        
        document.body.appendChild(touchOverlay);
        this.mobileControls.overlay = touchOverlay;
        
        console.log('[RhythmArcade3D] ✅ Mobile touch zones created');
    }
    
    /**
     * Handle touch start events
     */
    handleTouchStart(event) {
        if (!this.isInMinigame || !this.mobileControls.isEnabled) return;
        
        event.preventDefault();
        
        for (let touch of event.changedTouches) {
            this.mobileControls.activeTouches.add(touch.identifier);
        }
    }
    
    /**
     * Handle touch end events
     */
    handleTouchEnd(event) {
        if (!this.isInMinigame || !this.mobileControls.isEnabled) return;
        
        event.preventDefault();
        
        for (let touch of event.changedTouches) {
            this.mobileControls.activeTouches.delete(touch.identifier);
        }
    }
    
    /**
     * Handle touch move events
     */
    handleTouchMove(event) {
        if (!this.isInMinigame || !this.mobileControls.isEnabled) return;
        
        event.preventDefault();
    }
    
    /**
     * Hit a lane (destroy enemies if present)
     */
    hitLane(lane) {
        console.log(`[RhythmArcade3D] Hit lane ${lane}`);
        
        const trackWidth = 32;
        const laneWidth = trackWidth / 4;
        const laneX = -trackWidth/2 + lane * laneWidth + laneWidth/2;
        
        // Find enemies in this lane near target zone
        let closestNote = null;
        let closestDistance = Infinity;
        
        for (let i = this.fallingEnemies.length - 1; i >= 0; i--) {
            const note = this.fallingEnemies[i];
            if (note.userData.lane === lane) {
                const distance = Math.abs(note.position.z - 16); // Target zone at z=16
                
                if (distance < closestDistance && note.position.z >= 10 && note.position.z <= 22) {
                    closestNote = note;
                    closestDistance = distance;
                }
            }
        }
        
        if (closestNote) {
            // Calculate timing accuracy
            let rating = "MISS";
            let scoreMultiplier = 0;
            let feedbackColor = 0xFF0000; // Red
            
            if (closestDistance <= 2) {
                rating = "PERFECT";
                scoreMultiplier = 3;
                feedbackColor = 0xFFD700; // Gold
                this.combo++;
            } else if (closestDistance <= 4) {
                rating = "GOOD";
                scoreMultiplier = 2;
                feedbackColor = 0x00FF00; // Green
                this.combo++;
            } else if (closestDistance <= 6) {
                rating = "OK";
                scoreMultiplier = 1;
                feedbackColor = 0xFFFF00; // Yellow
                this.combo++;
            } else {
                // Too far - miss
                this.combo = 0;
            }
            
            if (scoreMultiplier > 0) {
                // Handle special note effects
                const noteType = closestNote.userData.noteType || 'normal';
                let baseScore = 100 * scoreMultiplier;
                
                switch (noteType) {
                    case 'golden':
                        baseScore *= 3; // Triple points
                        this.createHitFeedback("GOLDEN!", laneX, 0xFFD700);
                        this.createScreenFlash(0xFFD700, 0.3);
                        break;
                        
                    case 'health':
                        this.health = Math.min(100, this.health + 20);
                        this.createHitFeedback("+20 HP", laneX, 0x00FF00);
                        this.createScreenFlash(0x00FF00, 0.2);
                        break;
                        
                    case 'multiplier':
                        this.combo *= 2; // Double combo
                        this.createHitFeedback("x2 COMBO!", laneX, 0xFF00FF);
                        this.createScreenFlash(0xFF00FF, 0.3);
                        break;
                }
                
                // Score calculation
                this.score += baseScore + (this.combo * 10);
                this.maxCombo = Math.max(this.maxCombo, this.combo);
                this.notesHit++;
                
                if (rating === "PERFECT") {
                    this.perfectHits++;
                }
                
                // Create hit effect
                this.createHitEffect(closestNote.position, 
                    noteType === 'normal' ? this.laneColors[lane] : closestNote.material?.color || this.laneColors[lane]);
                
                // Remove the note
                const index = this.fallingEnemies.indexOf(closestNote);
                if (index > -1) {
                    this.removeFallingEnemy(index);
                }
                
                // Flash key indicator on successful hit
                this.flashKeyIndicator(lane, true);
            } else {
                // Flash key indicator on miss
                this.flashKeyIndicator(lane, false);
            }
            
            // Create feedback text
            this.createHitFeedback(rating, laneX, feedbackColor);
            
        } else {
            // No note to hit - miss
            this.combo = 0;
            this.flashKeyIndicator(lane, false);
            this.createHitFeedback("MISS", laneX, 0xFF0000);
        }
    }
    
    /**
     * Flash key indicator for feedback
     */
    flashKeyIndicator(lane, success) {
        if (lane >= 0 && lane < this.keyIndicators.length) {
            const indicator = this.keyIndicators[lane];
            const originalColor = this.laneColors[lane];
            const flashColor = success ? 0x00FF00 : 0xFF0000; // Green for hit, red for miss
            
            // Flash the background
            indicator.background.material.emissive.setHex(flashColor);
            indicator.background.material.emissiveIntensity = 1.0;
            
            // Return to original color after 200ms with tracked timeout
            const restoreTimeout = setTimeout(() => {
                if (indicator && indicator.background && indicator.background.material) {
                    indicator.background.material.emissive.setHex(originalColor);
                    indicator.background.material.emissiveIntensity = indicator.isActive ? 0.8 : 0.1;
                }
                this.activeTimeouts.delete(restoreTimeout);
            }, 200);
            this.activeTimeouts.add(restoreTimeout);
        }
    }
    
    /**
     * Create hit feedback text (PERFECT/GOOD/MISS)
     */
    createHitFeedback(text, laneX, color) {
        const feedbackGroup = new THREE.Group();
        
        // Create voxel-based text
        const voxelSize = VOXEL_SIZE * 0.5;
        const textMaterial = new THREE.MeshStandardMaterial({
            color: color,
            emissive: color,
            emissiveIntensity: 0.8,
            transparent: true,
            opacity: 1.0
        });
        
        // Simple voxel text representation
        const textLength = text.length;
        for (let i = 0; i < textLength; i++) {
            const letterGroup = new THREE.Group();
            const char = text[i];
            
            // Create a simple box for each letter (in real implementation, would use proper voxel letters)
            const letterGeometry = new THREE.BoxGeometry(voxelSize * 2, voxelSize * 3, voxelSize);
            const letterMesh = new THREE.Mesh(letterGeometry, textMaterial);
            letterMesh.position.x = (i - textLength/2) * voxelSize * 3;
            letterGroup.add(letterMesh);
            
            feedbackGroup.add(letterGroup);
        }
        
        // Position the feedback
        feedbackGroup.position.set(laneX, 5, 16);
        feedbackGroup.scale.set(1.5, 1.5, 1.5);
        
        // Add to feedback array
        const feedback = {
            group: feedbackGroup,
            startTime: Date.now(),
            duration: 1000, // 1 second
            startY: 5,
            targetY: 8,
            material: textMaterial
        };
        
        this.hitFeedbacks.push(feedback);
        this.forceLayer0Recursively(feedbackGroup);
        this.minigameScene.add(feedbackGroup);
    }
    
    /**
     * Update hit feedback animations
     */
    updateHitFeedbacks(deltaTime) {
        const currentTime = Date.now();
        
        for (let i = this.hitFeedbacks.length - 1; i >= 0; i--) {
            const feedback = this.hitFeedbacks[i];
            const elapsed = currentTime - feedback.startTime;
            const progress = elapsed / feedback.duration;
            
            if (progress >= 1) {
                // Remove finished feedback with proper cleanup
                feedback.group.traverse(child => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
                this.minigameScene.remove(feedback.group);
                this.hitFeedbacks.splice(i, 1);
                continue;
            }
            
            // Animate position (float up)
            feedback.group.position.y = feedback.startY + (feedback.targetY - feedback.startY) * progress;
            
            // Animate opacity (fade out)
            feedback.material.opacity = 1 - progress;
            
            // Scale animation (grow slightly)
            const scale = 1.5 + progress * 0.5;
            feedback.group.scale.set(scale, scale, scale);
        }
    }
    
    /**
     * Create hit effect at position
     */
    createHitEffect(position, color) {
        if (!this.minigameScene) return;
        
        const effect = new THREE.Group();
        
        // Create voxel particle explosion - reduced particle count for performance
        const voxelSize = VOXEL_SIZE * 0.3;
        for (let i = 0; i < 8; i++) { // Reduced from 15 to 8
            const particleGeometry = new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize);
            const particleMaterial = new THREE.MeshStandardMaterial({
                color: color,
                emissive: color,
                emissiveIntensity: 0.5,
                transparent: true,
                opacity: 1.0
            });
            
            const particle = new THREE.Mesh(particleGeometry, particleMaterial);
            particle.position.copy(position);
            particle.userData.velocity = new THREE.Vector3(
                (Math.random() - 0.5) * 8,
                Math.random() * 5 + 2,
                (Math.random() - 0.5) * 8
            );
            particle.userData.material = particleMaterial;
            particle.userData.lifetime = 1000; // 1 second
            particle.userData.startTime = Date.now();
            
            this.forceLayer0Recursively(particle);
            effect.add(particle);
        }
        
        // Add star burst effect
        const starBurst = this.createStarBurstEffect(position, color);
        effect.add(starBurst);
        
        this.minigameScene.add(effect);
        this.hitEffects.push(effect);
    }
    
    /**
     * Create star burst effect
     */
    createStarBurstEffect(position, color) {
        const burstGroup = new THREE.Group();
        const voxelSize = VOXEL_SIZE * 0.5;
        
        // Create expanding star ring
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const starVoxel = new THREE.Mesh(
                new THREE.BoxGeometry(voxelSize, voxelSize, voxelSize),
                new THREE.MeshStandardMaterial({
                    color: color,
                    emissive: color,
                    emissiveIntensity: 1.0,
                    transparent: true,
                    opacity: 1.0
                })
            );
            
            starVoxel.position.x = Math.cos(angle) * 2;
            starVoxel.position.z = Math.sin(angle) * 2;
            starVoxel.userData.expandSpeed = 0.1;
            starVoxel.userData.material = starVoxel.material;
            
            burstGroup.add(starVoxel);
        }
        
        burstGroup.position.copy(position);
        return burstGroup;
    }
    
    /**
     * Toggle pause state
     */
    togglePause() {
        if (!this.isPlaying || this.health <= 0) return;
        
        this.isPaused = !this.isPaused;
        
        if (this.isPaused) {
            // Pause music
            if (this.audioSource && this.audioContext) {
                this.pauseTime = this.audioContext.currentTime;
                this.audioSource.playbackRate.value = 0;
            }
            
            // Show pause overlay
            this.showPauseOverlay();
        } else {
            // Resume music
            if (this.audioSource && this.audioContext) {
                this.audioSource.playbackRate.value = 1;
            }
            
            // Hide pause overlay
            this.hidePauseOverlay();
        }
    }
    
    /**
     * Show pause overlay
     */
    showPauseOverlay() {
        if (this.pauseOverlay) return;
        
        this.pauseOverlay = document.createElement('div');
        this.pauseOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1500;
            backdrop-filter: blur(5px);
        `;
        
        const pauseText = document.createElement('div');
        pauseText.style.cssText = `
            font-family: monospace;
            font-size: clamp(48px, 8vw, 72px);
            color: #FFFFFF;
            text-shadow: 0 0 20px #FF00FF, 0 0 40px #00FFFF;
            text-align: center;
        `;
        pauseText.innerHTML = `
            PAUSED<br>
            <span style="font-size: clamp(20px, 3vw, 30px); opacity: 0.8;">
                Press P or SPACE to resume
            </span>
        `;
        
        this.pauseOverlay.appendChild(pauseText);
        document.body.appendChild(this.pauseOverlay);
    }
    
    /**
     * Hide pause overlay
     */
    hidePauseOverlay() {
        if (this.pauseOverlay) {
            document.body.removeChild(this.pauseOverlay);
            this.pauseOverlay = null;
        }
    }
    
    /**
     * Exit the arcade game
     */
    async exitGame() {
        console.log('[RhythmArcade3D] 🚪 Exiting arcade game');
        
        this.isPlaying = false;
        this.isPaused = false;
        this.hidePauseOverlay();
        
        await this.cleanup();
        
        return {
            won: this.health > 0,
            score: this.score,
            maxCombo: this.maxCombo,
            health: this.health,
            perfectHits: this.perfectHits,
            notesHit: this.notesHit,
            notesMissed: this.notesMissed
        };
    }
    
    /**
     * Clean up the minigame
     */
    async cleanup() {
        console.log('[RhythmArcade3D] 🧹 Cleaning up minigame');
        
        this.isCleaningUp = true;
        this.isInMinigame = false;
        this.isPlaying = false;
        
        // Cancel any running animation frame
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
        
        // Clear all timeouts
        this.activeTimeouts.forEach(id => clearTimeout(id));
        this.activeTimeouts.clear();
        
        // Clear all intervals
        this.activeIntervals.forEach(id => clearInterval(id));
        this.activeIntervals.clear();
        
        // Stop music
        this.stopArcadeMusic();
        
        // Remove event listeners
        document.removeEventListener('keydown', this.handleKeyDown);
        document.removeEventListener('keyup', this.handleKeyUp);
        document.removeEventListener('click', this.handleMouseClick);
        document.removeEventListener('mousemove', this.handleMouseMove);
        document.removeEventListener('touchstart', this.handleTouchStart);
        document.removeEventListener('touchend', this.handleTouchEnd);
        document.removeEventListener('touchmove', this.handleTouchMove);
        
        // Remove resize handler
        if (this.handleResize) {
            window.removeEventListener('resize', this.handleResize);
        }
        
        // Restore player controls
        this.restorePlayerControls();
        
        // Restore UI elements
        this.restoreUI();
        
        // Transition camera back to dungeon
        await this.transitionBackToDungeon();
        
        // Dispose of minigame scene
        this.disposeMinigameScene();
        
        console.log('[RhythmArcade3D] ✅ Cleanup complete');
    }
    
    /**
     * Restore player controls
     */
    restorePlayerControls() {
        console.log('[RhythmArcade3D] 🔓 Restoring player controls');
        
        if (this.sceneManager.controls && this.originalCameraState?.controls) {
            this.sceneManager.controls.enabled = this.originalCameraState.controls.enabled;
            this.sceneManager.controls.target.copy(this.originalCameraState.controls.target);
        }
        
        // Restore player key handlers
        if (this.dungeonHandler && this.dungeonHandler.playerController) {
            const playerController = this.dungeonHandler.playerController;
            
            // CRITICAL: Re-enable the player controller
            playerController.enabled = true;
            console.log('[RhythmArcade3D] Player controller re-enabled');
            
            if (this.playerKeyDownHandler) {
                playerController.onKeyDown = this.playerKeyDownHandler;
            }
            if (this.playerKeyUpHandler) {
                playerController.onKeyUp = this.playerKeyUpHandler;
            }
        }
    }
    
    /**
     * Restore UI elements
     */
    restoreUI() {
        console.log('[RhythmArcade3D] 🖥️ Restoring UI');
        
        // Remove arcade UI
        if (this.gameUI) {
            document.body.removeChild(this.gameUI);
            this.gameUI = null;
        }
        
        if (this.exitButton) {
            document.body.removeChild(this.exitButton);
            this.exitButton = null;
        }
        
        // Remove mobile touch overlay
        if (this.mobileControls.overlay) {
            document.body.removeChild(this.mobileControls.overlay);
            this.mobileControls.overlay = null;
            this.mobileControls.touchZones = [];
            this.mobileControls.isEnabled = false;
        }
        
        // Restore cursor
        document.body.style.cursor = '';
        
        // Restore minimap
        const minimap = document.querySelector('.minimap, #minimap, [class*=\"minimap\"], [id*=\"minimap\"]');
        if (minimap && this.originalMinimapDisplay !== undefined) {
            minimap.style.display = this.originalMinimapDisplay;
        }
    }
    
    /**
     * Transition camera back to dungeon
     */
    async transitionBackToDungeon() {
        console.log('[RhythmArcade3D] 🎥 Transitioning back to dungeon');
        
        if (!this.originalCameraState) {
            console.warn('[RhythmArcade3D] No original camera state to restore');
            return;
        }
        
        const camera = this.sceneManager.camera;
        
        // Save current minigame camera state
        const startPosition = camera.position.clone();
        const startRotation = camera.rotation.clone();
        const startFov = camera.fov;
        
        // Target is the original state
        const targetPosition = this.originalCameraState.position;
        const targetRotation = this.originalCameraState.rotation;
        const targetFov = this.originalCameraState.fov;
        
        return new Promise((resolve) => {
            const duration = 1500; // 1.5 second transition
            const startTime = Date.now();
            
            const animateTransition = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // Smooth easing
                const easedProgress = 1 - Math.pow(1 - progress, 3);
                
                // Interpolate position
                camera.position.lerpVectors(startPosition, targetPosition, easedProgress);
                
                // Interpolate rotation
                camera.rotation.x = startRotation.x + (targetRotation.x - startRotation.x) * easedProgress;
                camera.rotation.y = startRotation.y + (targetRotation.y - startRotation.y) * easedProgress;
                camera.rotation.z = startRotation.z + (targetRotation.z - startRotation.z) * easedProgress;
                
                // Interpolate FOV
                camera.fov = startFov + (targetFov - startFov) * easedProgress;
                camera.updateProjectionMatrix();
                
                // TEMPORARILY DISABLED: Scene switching causes issues
                // Remove minigame objects from main scene at 50% progress
                if (progress > 0.5 && this.minigameAddedToMainScene) {
                    console.log('[RhythmArcade3D] Removing minigame objects from main scene');
                    
                    // Remove minigame objects from main scene
                    if (this.arcadeRoom && this.arcadeRoom.parent) {
                        this.sceneManager.scene.remove(this.arcadeRoom);
                    }
                    if (this.arcadeCabinet && this.arcadeCabinet.parent) {
                        this.sceneManager.scene.remove(this.arcadeCabinet);
                    }
                    if (this.gameSurface && this.gameSurface.parent) {
                        this.sceneManager.scene.remove(this.gameSurface);
                    }
                    if (this.keyIndicatorGroup && this.keyIndicatorGroup.parent) {
                        this.sceneManager.scene.remove(this.keyIndicatorGroup);
                    }
                    
                    // Restore dungeon room visibility
                    if (this.dungeonHandler && this.dungeonHandler.currentRoomGroup) {
                        this.dungeonHandler.currentRoomGroup.visible = true;
                        console.log('[RhythmArcade3D] Restored dungeon room visibility');
                    }
                    
                    // Restore player mesh visibility
                    if (this.dungeonHandler && this.dungeonHandler.playerController && this.dungeonHandler.playerController.mesh) {
                        this.dungeonHandler.playerController.mesh.visible = true;
                    }
                    
                    this.minigameAddedToMainScene = false;
                    
                    // Original scene switching code (disabled)
                    // this.sceneManager.restoreOriginalScene();
                }
                
                if (progress < 1) {
                    requestAnimationFrame(animateTransition);
                } else {
                    console.log('[RhythmArcade3D] ✅ Camera transition back complete');
                    resolve();
                }
            };
            
            animateTransition();
        });
    }
    
    /**
     * Dispose of minigame scene resources
     */
    disposeMinigameScene() {
        console.log('[RhythmArcade3D] 🗑️ Disposing minigame scene');
        
        // Dispose of note pool first
        if (this.notePool && this.notePool.length > 0) {
            this.notePool.forEach(note => {
                if (note.parent) note.parent.remove(note);
                note.traverse((child) => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(m => m.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
            });
            this.notePool = [];
        }
        
        if (this.minigameScene) {
            // Dispose of all geometries and materials
            this.minigameScene.traverse((child) => {
                if (child.geometry) {
                    child.geometry.dispose();
                }
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(material => material.dispose());
                    } else {
                        child.material.dispose();
                    }
                }
            });
            
            // Clear the scene
            this.minigameScene.clear();
            this.minigameScene = null;
        }
        
        // Dispose of audio context
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        this.minigameCamera = null;
        this.roomEnvironment = null;
        this.arcadeCabinet = null;
        this.gameBoard = null;
        this.fallingEnemies = [];
        this.activeNotes = [];
        this.targetZones = [];
        this.keyIndicators = [];
        this.hitEffects = [];
        this.hitFeedbacks = [];
        
        // Clear cached objects
        this.cachedEffectObjects = null;
        this.uiElements = null;
        this.neonLights = null;
        this.frameCount = 0;
        
        // Remove performance styles
        const perfStyles = document.getElementById('arcade-performance-styles');
        if (perfStyles) {
            perfStyles.remove();
        }
    }
}