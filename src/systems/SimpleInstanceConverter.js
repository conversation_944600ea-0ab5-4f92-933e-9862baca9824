import * as THREE from 'three';

/**
 * Simple converter that takes any group and converts identical meshes to instanced meshes
 */
export function convertToInstancedMeshes(group) {
    // Collect all meshes by geometry+material combination
    const meshGroups = new Map();
    const meshesToRemove = [];
    
    group.traverse(child => {
        if (child.isMesh && !child.isInstancedMesh) {
            // Skip animated or interactive objects
            if (child.userData?.interactive || 
                child.userData?.animated || 
                child.userData?.isEnemy || 
                child.userData?.isItem ||
                child.parent?.userData?.objectType === 'enemy') {
                return;
            }
            
            // Create key from geometry and material
            const geoId = child.geometry.uuid;
            const matId = child.material.uuid;
            const key = `${geoId}_${matId}`;
            
            if (!meshGroups.has(key)) {
                meshGroups.set(key, {
                    geometry: child.geometry,
                    material: child.material,
                    meshes: []
                });
            }
            
            meshGroups.get(key).meshes.push(child);
        }
    });
    
    // Convert groups with multiple meshes to instanced meshes
    meshGroups.forEach(({ geometry, material, meshes }) => {
        if (meshes.length > 1) { // Convert if we have 2+ identical meshes
            // Create instanced mesh
            const instancedMesh = new THREE.InstancedMesh(geometry, material, meshes.length);
            
            // Copy properties from first mesh
            const firstMesh = meshes[0];
            instancedMesh.castShadow = firstMesh.castShadow;
            instancedMesh.receiveShadow = firstMesh.receiveShadow;
            
            // Set transforms for each instance
            const matrix = new THREE.Matrix4();
            meshes.forEach((mesh, index) => {
                mesh.updateMatrixWorld(true);
                matrix.copy(mesh.matrixWorld);
                instancedMesh.setMatrixAt(index, matrix);
                
                // Mark for removal
                meshesToRemove.push(mesh);
            });
            
            instancedMesh.instanceMatrix.needsUpdate = true;
            
            // Add to the parent of first mesh
            const parent = firstMesh.parent || group;
            parent.add(instancedMesh);
        }
    });
    
    // Remove original meshes
    meshesToRemove.forEach(mesh => {
        if (mesh.parent) {
            mesh.parent.remove(mesh);
        }
    });
    
    console.log(`[InstanceConverter] Converted ${meshesToRemove.length} meshes into ${meshGroups.size} instanced meshes`);
    
    return group;
}