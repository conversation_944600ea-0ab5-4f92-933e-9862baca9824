/**
 * Minimap Performance Monitor
 * 
 * Monitors and optimizes minimap performance, providing real-time metrics
 * and automatic performance adjustments based on system capabilities.
 */

class MinimapPerformanceMonitor {
    constructor(minimap) {
        this.minimap = minimap;
        
        // Performance metrics
        this.metrics = {
            frameTime: {
                current: 0,
                average: 0,
                max: 0,
                samples: []
            },
            updateFrequency: {
                target: 30, // Target FPS
                actual: 0,
                lastUpdate: 0
            },
            memoryUsage: {
                domNodes: 0,
                svgElements: 0,
                cachedRooms: 0
            },
            renderingStats: {
                roomsRendered: 0,
                elementsCreated: 0,
                elementsReused: 0,
                cacheHitRate: 0
            }
        };
        
        // Performance thresholds
        this.thresholds = {
            frameTimeWarning: 16.67, // 60 FPS threshold
            frameTimeCritical: 33.33, // 30 FPS threshold
            memoryWarning: 100, // DOM nodes
            memoryCritical: 200
        };
        
        // Optimization settings
        this.optimizations = {
            adaptiveUpdateRate: true,
            aggressiveCaching: false,
            reducedAnimations: false,
            simplifiedShapes: false
        };
        
        // Monitoring state
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.performanceLevel = 'high'; // high, medium, low
        
        this.startMonitoring();
    }
    
    /**
     * Start performance monitoring
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        
        // Monitor every 2 seconds
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
            this.analyzePerformance();
            this.applyOptimizations();
        }, 2000);
        
        console.log('[MinimapPerformanceMonitor] Started monitoring');
    }
    
    /**
     * Stop performance monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('[MinimapPerformanceMonitor] Stopped monitoring');
    }
    
    /**
     * Collect current performance metrics
     */
    collectMetrics() {
        // Frame time metrics
        const frameTime = this.minimap.performanceMetrics.lastFrameTime;
        this.updateFrameTimeMetrics(frameTime);
        
        // Update frequency
        this.updateFrequencyMetrics();
        
        // Memory usage
        this.updateMemoryMetrics();
        
        // Rendering stats
        this.updateRenderingStats();
    }
    
    /**
     * Update frame time metrics
     */
    updateFrameTimeMetrics(frameTime) {
        this.metrics.frameTime.current = frameTime;
        this.metrics.frameTime.max = Math.max(this.metrics.frameTime.max, frameTime);
        
        // Keep rolling average of last 30 samples
        this.metrics.frameTime.samples.push(frameTime);
        if (this.metrics.frameTime.samples.length > 30) {
            this.metrics.frameTime.samples.shift();
        }
        
        // Calculate average
        this.metrics.frameTime.average = 
            this.metrics.frameTime.samples.reduce((a, b) => a + b, 0) / 
            this.metrics.frameTime.samples.length;
    }
    
    /**
     * Update frequency metrics
     */
    updateFrequencyMetrics() {
        const now = performance.now();
        if (this.metrics.updateFrequency.lastUpdate > 0) {
            const deltaTime = now - this.metrics.updateFrequency.lastUpdate;
            this.metrics.updateFrequency.actual = 1000 / deltaTime;
        }
        this.metrics.updateFrequency.lastUpdate = now;
    }
    
    /**
     * Update memory usage metrics
     */
    updateMemoryMetrics() {
        if (this.minimap.container) {
            this.metrics.memoryUsage.domNodes = this.minimap.container.querySelectorAll('*').length;
            this.metrics.memoryUsage.svgElements = this.minimap.container.querySelectorAll('svg *').length;
        }
        
        this.metrics.memoryUsage.cachedRooms = this.minimap.roomCache.size;
    }
    
    /**
     * Update rendering statistics
     */
    updateRenderingStats() {
        this.metrics.renderingStats.roomsRendered = this.minimap.roomElements.size;
        this.metrics.renderingStats.cachedRooms = this.minimap.roomCache.size;
        
        // Calculate cache hit rate
        if (this.metrics.renderingStats.cachedRooms > 0) {
            this.metrics.renderingStats.cacheHitRate = 
                this.metrics.renderingStats.elementsReused / 
                (this.metrics.renderingStats.elementsCreated + this.metrics.renderingStats.elementsReused);
        }
    }
    
    /**
     * Analyze performance and determine optimization level
     */
    analyzePerformance() {
        const avgFrameTime = this.metrics.frameTime.average;
        const memoryUsage = this.metrics.memoryUsage.domNodes;
        
        let newPerformanceLevel = 'high';
        
        // Determine performance level based on metrics
        if (avgFrameTime > this.thresholds.frameTimeCritical || 
            memoryUsage > this.thresholds.memoryCritical) {
            newPerformanceLevel = 'low';
        } else if (avgFrameTime > this.thresholds.frameTimeWarning || 
                   memoryUsage > this.thresholds.memoryWarning) {
            newPerformanceLevel = 'medium';
        }
        
        // Update performance level if changed
        if (newPerformanceLevel !== this.performanceLevel) {
            console.log(`[MinimapPerformanceMonitor] Performance level changed: ${this.performanceLevel} → ${newPerformanceLevel}`);
            this.performanceLevel = newPerformanceLevel;
        }
    }
    
    /**
     * Apply performance optimizations based on current performance level
     */
    applyOptimizations() {
        switch (this.performanceLevel) {
            case 'low':
                this.applyLowPerformanceOptimizations();
                break;
            case 'medium':
                this.applyMediumPerformanceOptimizations();
                break;
            case 'high':
                this.applyHighPerformanceOptimizations();
                break;
        }
    }
    
    /**
     * Apply optimizations for low-performance systems
     */
    applyLowPerformanceOptimizations() {
        // Reduce update frequency
        this.minimap.updateInterval = 1000 / 15; // 15 FPS
        
        // Disable expensive animations
        this.optimizations.reducedAnimations = true;
        
        // Use simplified room shapes
        this.optimizations.simplifiedShapes = true;
        
        // Enable aggressive caching
        this.optimizations.aggressiveCaching = true;
        
        console.log('[MinimapPerformanceMonitor] Applied low-performance optimizations');
    }
    
    /**
     * Apply optimizations for medium-performance systems
     */
    applyMediumPerformanceOptimizations() {
        // Moderate update frequency
        this.minimap.updateInterval = 1000 / 20; // 20 FPS
        
        // Keep most animations but reduce complexity
        this.optimizations.reducedAnimations = false;
        this.optimizations.simplifiedShapes = false;
        this.optimizations.aggressiveCaching = true;
        
        console.log('[MinimapPerformanceMonitor] Applied medium-performance optimizations');
    }
    
    /**
     * Apply optimizations for high-performance systems
     */
    applyHighPerformanceOptimizations() {
        // Full update frequency
        this.minimap.updateInterval = 1000 / 30; // 30 FPS
        
        // Enable all visual features
        this.optimizations.reducedAnimations = false;
        this.optimizations.simplifiedShapes = false;
        this.optimizations.aggressiveCaching = false;
        
        console.log('[MinimapPerformanceMonitor] Applied high-performance optimizations');
    }
    
    /**
     * Get current performance report
     */
    getPerformanceReport() {
        return {
            level: this.performanceLevel,
            metrics: { ...this.metrics },
            optimizations: { ...this.optimizations },
            recommendations: this.getRecommendations()
        };
    }
    
    /**
     * Get performance recommendations
     */
    getRecommendations() {
        const recommendations = [];
        
        if (this.metrics.frameTime.average > this.thresholds.frameTimeWarning) {
            recommendations.push('Consider reducing minimap update frequency');
        }
        
        if (this.metrics.memoryUsage.domNodes > this.thresholds.memoryWarning) {
            recommendations.push('Enable aggressive caching to reduce DOM nodes');
        }
        
        if (this.metrics.renderingStats.cacheHitRate < 0.8) {
            recommendations.push('Improve caching strategy for better performance');
        }
        
        return recommendations;
    }
    
    /**
     * Force performance level (for testing)
     */
    setPerformanceLevel(level) {
        this.performanceLevel = level;
        this.applyOptimizations();
        console.log(`[MinimapPerformanceMonitor] Performance level manually set to: ${level}`);
    }
    
    /**
     * Cleanup
     */
    dispose() {
        this.stopMonitoring();
        console.log('[MinimapPerformanceMonitor] Disposed');
    }
}

export default MinimapPerformanceMonitor;
