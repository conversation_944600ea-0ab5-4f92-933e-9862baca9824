/**
 * Manages the environment object system, including floor and wall object generation and placement.
 *
 * CURRENT IMPLEMENTATION NOTE:
 * This system is currently configured for the catacombs area, with support for:
 * - Floor Objects: stone_vase, stone_pillar, stone_rubble
 * - Wall Objects: vine, torch
 *
 * Other object types and biomes are defined but not actively used until they are fully implemented.
 */

import * as THREE from 'three';
import { mulberry32 } from '../generators/prefabs/shared.js';
import { getPrefabFunction } from '../prefabs/prefabs.js';

// --- Object Role Definitions ---
const OBJECT_ROLES = {
    DECORATION: 'decoration',     // Visual elements with no gameplay impact
    LIGHT: 'light',               // Objects that provide light
    DESTRUCTIBLE: 'destructible', // Objects that can be destroyed
    INTERACTIVE: 'interactive',   // Objects that can be interacted with
    HAZARD: 'hazard',             // Objects that can harm the player
    SPECIAL: 'special'            // Special objects with unique behaviors
};

// --- Object Placement Types ---
const PLACEMENT_TYPES = {
    WALL: 'wall',       // Placed on walls
    FLOOR: 'floor',     // Placed on floors
    CEILING: 'ceiling', // Placed on ceilings
    CORNER: 'corner',   // Placed in corners
    CENTER: 'center'    // Placed in the center of the room
};

// --- Object Role Assignments ---
const OBJECT_ROLE_MAP = {
    // Wall objects
    vine: OBJECT_ROLES.DECORATION,
    torch: OBJECT_ROLES.LIGHT,

    // Floor objects
    stone_vase: OBJECT_ROLES.DESTRUCTIBLE,
    stone_pillar: OBJECT_ROLES.DESTRUCTIBLE,
    stone_rubble: OBJECT_ROLES.DESTRUCTIBLE,
    ritual_circle: OBJECT_ROLES.SPECIAL,
    aether_torch: OBJECT_ROLES.LIGHT
};

// --- Individual Object Spawn Definitions ---
// These define spawn parameters for each object type independently
const OBJECT_SPAWN_DEFINITIONS = {
    // Wall Objects
    torch: {
        role: OBJECT_ROLES.LIGHT,
        placement: PLACEMENT_TYPES.WALL,
        placementDetail: 'wall',
        spawnProbability: 0.95, // 95% chance to spawn in any room
        minCount: 3,
        maxCount: 8,
        allowedRoomTypes: ['normal', 'elite', 'boss', 'start'], // Can spawn in any room type
        maxPerFloor: null, // No floor limit
        biomes: ['catacombs']
    },
    vine: {
        role: OBJECT_ROLES.DECORATION,
        placement: PLACEMENT_TYPES.WALL,
        placementDetail: 'wall',
        spawnProbability: 0.70, // 70% chance to spawn
        minCount: 2,
        maxCount: 6,
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        maxPerFloor: null,
        biomes: ['catacombs']
    },

    // Floor Objects
    stone_vase: {
        role: OBJECT_ROLES.DESTRUCTIBLE,
        placement: PLACEMENT_TYPES.FLOOR,
        placementDetail: 'random',
        spawnProbability: 0.70, // 70% chance to spawn in allowed room types
        minCount: 2,
        maxCount: 5,
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        maxPerFloor: null,
        biomes: ['catacombs']
    },
    stone_pillar: {
        role: OBJECT_ROLES.DESTRUCTIBLE,
        placement: PLACEMENT_TYPES.FLOOR,
        placementDetail: 'random',
        spawnProbability: 0.60, // 60% chance to spawn - increased for more floor objects
        minCount: 1,
        maxCount: 3,
        allowedRoomTypes: ['normal', 'elite', 'boss'],
        maxPerFloor: null,
        biomes: ['catacombs']
    },
    stone_rubble: {
        role: OBJECT_ROLES.DESTRUCTIBLE,
        placement: PLACEMENT_TYPES.FLOOR,
        placementDetail: 'random',
        spawnProbability: 0.50, // 50% chance to spawn - increased for more floor objects
        minCount: 1,
        maxCount: 4,
        allowedRoomTypes: ['normal', 'elite'],
        maxPerFloor: null,
        biomes: ['catacombs']
    },
    aether_torch: {
        role: OBJECT_ROLES.LIGHT,
        placement: PLACEMENT_TYPES.FLOOR,
        placementDetail: 'corners',
        spawnProbability: 0.15, // 15% chance to spawn
        minCount: 1,
        maxCount: 2,
        allowedRoomTypes: ['elite', 'boss'],
        maxPerFloor: 2, // Maximum 2 aether torches per floor
        biomes: ['catacombs']
    },

    // Special Objects
    ritual_circle: {
        role: OBJECT_ROLES.SPECIAL,
        placement: PLACEMENT_TYPES.FLOOR,
        placementDetail: 'center',
        spawnProbability: 1.0, // Always spawn in start rooms
        minCount: 1,
        maxCount: 1,
        allowedRoomTypes: ['start'],
        maxPerFloor: 1, // Only one ritual circle per floor
        biomes: ['catacombs']
    }
};



/**
 * Check if a position is within the valid area of a room shape
 * @param {number} x - X coordinate
 * @param {number} z - Z coordinate
 * @param {string} roomShape - Room shape key
 * @returns {boolean} True if position is valid for the room shape
 */
function _isPositionInRoomShape(x, z, roomShape) {
    if (!roomShape) {
        return true; // If no shape specified, allow all positions
    }

    // Room constants (matching DungeonHandler.js)
    const R = 14; // ROOM_WORLD_SIZE
    const H = 7;  // Half of ROOM_WORLD_SIZE

    switch (roomShape) {
        case 'SQUARE_1X1':
        case 'RECTANGULAR':
        case 'RECT_2X1':
        case 'RECT_1X2':
        case 'SQUARE_2X2':
        case 'RECT_3X1':
        case 'RECT_1X3':
        case 'RECT_3X2':
            // Simple rectangular shapes - use bounding box validation
            return true; // These are already handled by bounding box

        case 'L_SHAPE':
            // CORRECTED L-shape: vertical part (top-left) and horizontal part (bottom)
            // Based on floor segments: (-H,0,-H) with R×R and (0,0,H) with 2R×R
            // Valid areas:
            // - Vertical part (top-left): x from -R to 0, z from -R to 0
            // - Horizontal part (bottom): x from -R to R, z from 0 to R
            const inVerticalPart = (x >= -R && x <= 0 && z >= -R && z <= 0);
            const inHorizontalPart = (x >= -R && x <= R && z >= 0 && z <= R);
            return inVerticalPart || inHorizontalPart;

        case 'T_SHAPE':
            // T-shape: horizontal bar at top (3x1) and vertical stem at bottom (1x1)
            // Top bar: (-R-H to R+H, -R to 0)
            // Bottom stem: (-H to H, 0 to R)
            const inTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inBottomStem = (x >= -H && x <= H && z >= 0 && z <= R);
            return inTopBar || inBottomStem;

        case 'U_SHAPE_DOWN':
            // U-shape with opening at bottom: top bar (3x1) and two side legs (1x1 each)
            // Top bar: (-R-H to R+H, -R to 0)
            // Left leg: (-R-H to -H, 0 to R)
            // Right leg: (H to R+H, 0 to R)
            const inUTopBar = (x >= -(R + H) && x <= (R + H) && z >= -R && z <= 0);
            const inULeftLeg = (x >= -(R + H) && x <= -H && z >= 0 && z <= R);
            const inURightLeg = (x >= H && x <= (R + H) && z >= 0 && z <= R);
            return inUTopBar || inULeftLeg || inURightLeg;

        case 'U_SHAPE_UP':
            // U-shape with opening at top: bottom bar (3x1) and two side legs (1x1 each)
            const inUUpBottomBar = (x >= -(R + H) && x <= (R + H) && z >= 0 && z <= R);
            const inUUpLeftLeg = (x >= -(R + H) && x <= -H && z >= -R && z <= 0);
            const inUUpRightLeg = (x >= H && x <= (R + H) && z >= -R && z <= 0);
            return inUUpBottomBar || inUUpLeftLeg || inUUpRightLeg;

        case 'U_SHAPE_LEFT':
            // U-shape with opening at left: right bar (1x3) and two horizontal arms (1x1 each)
            const inULeftRightBar = (x >= 0 && x <= R && z >= -(R + H) && z <= (R + H));
            const inULeftTopArm = (x >= -R && x <= 0 && z >= -(R + H) && z <= -H);
            const inULeftBottomArm = (x >= -R && x <= 0 && z >= H && z <= (R + H));
            return inULeftRightBar || inULeftTopArm || inULeftBottomArm;

        case 'U_SHAPE_RIGHT':
            // U-shape with opening at right: left bar (1x3) and two horizontal arms (1x1 each)
            const inURightLeftBar = (x >= -R && x <= 0 && z >= -(R + H) && z <= (R + H));
            const inURightTopArm = (x >= 0 && x <= R && z >= -(R + H) && z <= -H);
            const inURightBottomArm = (x >= 0 && x <= R && z >= H && z <= (R + H));
            return inURightLeftBar || inURightTopArm || inURightBottomArm;

        case 'CORNER_SHAPE':
            // Small L-shape: two 1x1 squares
            const inCornerLeft = (x >= -R && x <= 0 && z >= -H && z <= H);
            const inCornerRight = (x >= 0 && x <= R && z >= -R && z <= 0);
            return inCornerLeft || inCornerRight;

        case 'CORRIDOR_LONG':
            // Long corridor: 4x1 rectangle
            return (x >= -2*R && x <= 2*R && z >= -H && z <= H);











        case 'CORRIDOR_SHORT':
            // Short corridor: 2x1 rectangle
            return (x >= -R && x <= R && z >= -H && z <= H);

        case 'SQUARE_2X2':
            // Larger square: 2x2 rectangle
            return (x >= -R && x <= R && z >= -R && z <= R);

        case 'CROSS_SHAPE':
            // Cross shape: horizontal bar (3x1) and vertical bar (1x3) intersecting at center
            // Horizontal: (-R-H to R+H, -H to H)
            // Vertical: (-H to H, -R-H to R+H)
            const inHorizontalBar = (x >= -(R + H) && x <= (R + H) && z >= -H && z <= H);
            const inVerticalBar = (x >= -H && x <= H && z >= -(R + H) && z <= (R + H));
            return inHorizontalBar || inVerticalBar;

        case 'BOSS_ARENA':
            // Boss arena is typically much larger - use bounding box validation
            return true;

        default:
            console.warn(`[RoomShapeValidation] Unknown room shape: ${roomShape}, allowing position`);
            return true;
    }
}

// --- Object Placement Details ---
// These define specific placement strategies for objects
const PLACEMENT_DETAILS = {
    random: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        // ALWAYS require valid floor positions to prevent spawning in void
        if (!validFloorPositions || validFloorPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions provided for room shape ${roomShape}, skipping object`);
            return null; // Return null to indicate no valid placement
        }
        
        // Filter positions to ensure proper spacing from walls
        // This prevents objects from spawning too close to walls or in corners
        const minWallDistance = 1.5; // Minimum distance from walls
        
        // For all room types, filter positions that are too close to edges
        const validPositions = validFloorPositions.filter(pos => {
            // Check distance from room bounds based on shape
            if (roomShape && _isPositionInRoomShape) {
                // Check if position is sufficiently inside the room shape
                // by testing points around it
                const testRadius = minWallDistance;
                const testPoints = [
                    { x: pos.position.x + testRadius, z: pos.position.z },
                    { x: pos.position.x - testRadius, z: pos.position.z },
                    { x: pos.position.x, z: pos.position.z + testRadius },
                    { x: pos.position.x, z: pos.position.z - testRadius }
                ];
                
                // All test points must be inside the room shape
                return testPoints.every(pt => _isPositionInRoomShape(pt.x, pt.z, roomShape, false));
            } else {
                // Fallback for rectangular rooms
                const halfWidth = room.width / 2 - minWallDistance;
                const halfDepth = room.depth / 2 - minWallDistance;
                return Math.abs(pos.position.x) < halfWidth &&
                       Math.abs(pos.position.z) < halfDepth;
            }
        });
        
        console.log(`[EnvironmentObjectManager] Filtered ${validFloorPositions.length} positions to ${validPositions.length} with wall clearance`);
        
        if (validPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions after edge filtering for room shape ${roomShape}`);
            return null; // No valid positions found
        }
        
        // Pick a random valid floor position
        const randomIndex = Math.floor(Math.random() * validPositions.length);
        const chosen = validPositions[randomIndex].position;
        // CRITICAL: Use the actual floor Y position from the scan, not 0
        return { x: chosen.x, y: chosen.y, z: chosen.z };
    },
    corners: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        // ALWAYS require valid floor positions to prevent spawning in void
        if (!validFloorPositions || validFloorPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions provided for corners in room shape ${roomShape}, skipping object`);
            return null;
        }
        
        const allCorners = [
            { x: -1, z: -1, name: 'SW' }, // Southwest (bottom-left)
            { x: 1, z: -1, name: 'SE' },  // Southeast (bottom-right)
            { x: -1, z: 1, name: 'NW' },  // Northwest (top-left)
            { x: 1, z: 1, name: 'NE' }    // Northeast (top-right)
        ];

        // Filter corners to only include those that have actual floor nearby
        const validCorners = allCorners.filter(corner => {
            const offset = objectSize * 1.5;
            const x = corner.x * (room.width / 2 - offset);
            const z = corner.z * (room.depth / 2 - offset);
            
            // Check if any valid floor position is near this corner
            const cornerThreshold = objectSize * 2.5; // Slightly larger threshold
            const hasNearbyFloor = validFloorPositions.some(floorPos => {
                const dist = Math.sqrt(
                    Math.pow(floorPos.position.x - x, 2) + 
                    Math.pow(floorPos.position.z - z, 2)
                );
                return dist < cornerThreshold;
            });
            
            return hasNearbyFloor;
        });

        if (validCorners.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid corners with floor found for room shape ${roomShape}`);
            return null;
        }

        // Use modulo to cycle through valid corners
        const cornerIndex = index % validCorners.length;
        const selectedCorner = validCorners[cornerIndex];
        const offset = objectSize * 1.5;
        const x = selectedCorner.x * (room.width / 2 - offset);
        const z = selectedCorner.z * (room.depth / 2 - offset);

        // Final validation: find the closest actual floor position to use
        let closestFloorPos = null;
        let minDist = Infinity;
        for (const floorPos of validFloorPositions) {
            const dist = Math.sqrt(
                Math.pow(floorPos.position.x - x, 2) + 
                Math.pow(floorPos.position.z - z, 2)
            );
            if (dist < minDist) {
                minDist = dist;
                closestFloorPos = floorPos.position;
            }
        }
        
        if (closestFloorPos && minDist < objectSize * 2.5) {
            console.log(`[EnvironmentObjectManager] Placing corner object ${index} in ${selectedCorner.name} corner at validated position for room shape ${roomShape}`);
            return { x: closestFloorPos.x, y: closestFloorPos.y, z: closestFloorPos.z };
        }
        
        return null;
    },
    center: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        // ALWAYS require valid floor positions to prevent spawning in void
        if (!validFloorPositions || validFloorPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions provided for center placement in room shape ${roomShape}, skipping object`);
            return null;
        }
        
        // Find the floor position closest to center
        let closestToCenter = null;
        let minDistToCenter = Infinity;
        
        for (const floorPos of validFloorPositions) {
            const dist = Math.sqrt(
                Math.pow(floorPos.position.x, 2) + 
                Math.pow(floorPos.position.z, 2)
            );
            if (dist < minDistToCenter) {
                minDistToCenter = dist;
                closestToCenter = floorPos.position;
            }
        }
        
        if (closestToCenter) {
            return { x: closestToCenter.x, y: closestToCenter.y, z: closestToCenter.z };
        }
        
        console.warn(`[EnvironmentObjectManager] Could not find center floor position for room shape ${roomShape}`);
        return null;
    },
    triangle_center: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        // ALWAYS require valid floor positions to prevent spawning in void
        if (!validFloorPositions || validFloorPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions provided for triangle_center placement in room shape ${roomShape}, skipping object`);
            return null;
        }
        
        // Calculate triangle positions around center
        const triangleRadius = Math.min(room.width, room.depth) * 0.2; // 20% of room size
        const angleOffset = (Math.PI * 2) / 3; // 120 degrees between each monolith
        const startAngle = -Math.PI / 2; // Start with one monolith at the top
        
        // Calculate position for this monolith (index 0, 1, or 2)
        const angle = startAngle + (index * angleOffset);
        const x = Math.cos(angle) * triangleRadius;
        const z = Math.sin(angle) * triangleRadius;
        
        // Find the closest valid floor position to our calculated triangle position
        let closestFloorPos = null;
        let minDist = Infinity;
        
        for (const floorPos of validFloorPositions) {
            const dist = Math.sqrt(
                Math.pow(floorPos.position.x - x, 2) + 
                Math.pow(floorPos.position.z - z, 2)
            );
            if (dist < minDist) {
                minDist = dist;
                closestFloorPos = floorPos.position;
            }
        }
        
        if (closestFloorPos && minDist < objectSize * 3) {
            console.log(`[EnvironmentObjectManager] Placing triangle_center object ${index} at angle ${(angle * 180/Math.PI).toFixed(0)}° for room shape ${roomShape}`);
            return { x: closestFloorPos.x, y: closestFloorPos.y, z: closestFloorPos.z };
        }
        
        console.warn(`[EnvironmentObjectManager] Could not find valid floor position for triangle_center placement`);
        return null;
    },
    walls: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        // ALWAYS require valid floor positions to prevent spawning in void
        if (!validFloorPositions || validFloorPositions.length === 0) {
            console.warn(`[EnvironmentObjectManager] No valid floor positions provided for wall placement in room shape ${roomShape}, skipping object`);
            return null;
        }
        
        // Skip the invisible south wall (index 2)
        // 0: north, 1: east, 3: west
        let wallIndex = index % 3;
        if (wallIndex === 2) wallIndex = 3; // Map 2 to 3 (west) to skip south wall

        const positionAlongWall = (index / totalCount) * 2 - 1; // -1 to 1

        let x = 0, z = 0;
        let wallName = '';
        let inwardNormal = new THREE.Vector3(); // Normal pointing into the room

        switch (wallIndex) {
            case 0: // North wall
                x = positionAlongWall * (room.width / 2 - objectSize);
                z = -room.depth / 2 + objectSize / 2;
                wallName = 'North';
                inwardNormal.set(0, 0, 1); // Facing south (into room)
                break;
            case 1: // East wall
                x = room.width / 2 - objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                wallName = 'East';
                inwardNormal.set(-1, 0, 0); // Facing west (into room)
                break;
            case 3: // West wall (skipping South wall)
                x = -room.width / 2 + objectSize / 2;
                z = positionAlongWall * (room.depth / 2 - objectSize);
                wallName = 'West';
                inwardNormal.set(1, 0, 0); // Facing east (into room)
                break;
        }

        // Find the closest valid floor position to the calculated wall position
        let closestFloorPos = null;
        let minDist = Infinity;
        const searchRadius = objectSize * 2; // Search radius for nearby floor
        
        for (const floorPos of validFloorPositions) {
            const dist = Math.sqrt(
                Math.pow(floorPos.position.x - x, 2) + 
                Math.pow(floorPos.position.z - z, 2)
            );
            if (dist < minDist && dist < searchRadius) {
                minDist = dist;
                closestFloorPos = floorPos.position;
            }
        }
        
        if (closestFloorPos) {
            console.log(`[EnvironmentObjectManager] Placing wall object on ${wallName} wall at validated floor position for room shape ${roomShape}`);
            return { 
                x: closestFloorPos.x, 
                y: 0, 
                z: closestFloorPos.z, 
                wallIndex, 
                wallName, 
                inwardNormal 
            };
        }

        // If no valid position found, try alternative walls
        const alternativeWalls = [
            { index: 0, name: 'North' },
            { index: 1, name: 'East' },
            { index: 3, name: 'West' }
        ];

        for (const altWall of alternativeWalls) {
            if (altWall.index === wallIndex) continue;

            let altX = 0, altZ = 0;
            switch (altWall.index) {
                case 0: // North wall
                    altX = 0; // Center of wall
                    altZ = -room.depth / 2 + objectSize / 2;
                    break;
                case 1: // East wall
                    altX = room.width / 2 - objectSize / 2;
                    altZ = 0; // Center of wall
                    break;
                case 3: // West wall
                    altX = -room.width / 2 + objectSize / 2;
                    altZ = 0; // Center of wall
                    break;
            }

            // Find closest floor position to alternative wall
            for (const floorPos of validFloorPositions) {
                const dist = Math.sqrt(
                    Math.pow(floorPos.position.x - altX, 2) + 
                    Math.pow(floorPos.position.z - altZ, 2)
                );
                if (dist < searchRadius) {
                    console.log(`[EnvironmentObjectManager] Found valid alternative wall position on ${altWall.name} wall`);
                    let altInwardNormal = new THREE.Vector3();
                    switch (altWall.index) {
                        case 0: altInwardNormal.set(0, 0, 1); break;
                        case 1: altInwardNormal.set(-1, 0, 0); break;
                        case 3: altInwardNormal.set(1, 0, 0); break;
                    }
                    return { 
                        x: floorPos.position.x, 
                        y: 0, 
                        z: floorPos.position.z, 
                        wallIndex: altWall.index, 
                        wallName: altWall.name, 
                        inwardNormal: altInwardNormal 
                    };
                }
            }
        }

        console.warn(`[EnvironmentObjectManager] No valid wall positions found for room shape ${roomShape}`);
        return null;
    },

    // Alias for 'walls' for compatibility
    wall: (room, objectSize, index, totalCount, roomShape, validFloorPositions) => {
        return PLACEMENT_DETAILS.walls(room, objectSize, index, totalCount, roomShape, validFloorPositions);
    }
};

class EnvironmentObjectManager {
    constructor() {
        // Debug mode
        this.debugMode = false;

        // Floor-level object tracking for per-floor limits
        this.floorObjectTracker = {};
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode should be enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }

    /**
     * Initialize or reset floor object tracking for a new floor
     * @param {number} floorLevel - The floor level to initialize tracking for
     */
    initializeFloorTracking(floorLevel) {
        this.floorObjectTracker[floorLevel] = {};
        console.log(`[EnvironmentObjectManager] Initialized object tracking for floor ${floorLevel}`);
    }

    /**
     * Get the floor object tracker for a specific floor
     * @param {number} floorLevel - The floor level
     * @returns {object} The object tracker for the floor
     */
    getFloorTracker(floorLevel) {
        if (!this.floorObjectTracker[floorLevel]) {
            this.initializeFloorTracking(floorLevel);
        }
        return this.floorObjectTracker[floorLevel];
    }



    /**
     * Choose objects for a room based on individual object spawn probabilities
     * @param {object} context - The context for object placement
     * @param {string} context.biome - The current biome
     * @param {string} context.roomType - The type of room
     * @param {object} context.roomData - The room data
     * @param {number} context.seed - Random seed for deterministic placement
     * @param {object} context.floorObjectTracker - Tracker for floor-limited objects (optional)
     * @returns {Array} Array of objects to place
     */
    chooseObjectGroup(context) {
        const { biome, roomType, roomData, seed = Date.now(), floorObjectTracker = {} } = context;

        // Create seeded random function
        const random = mulberry32(seed);
        
        // Burn a few random calls to avoid potential patterns in the sequence
        // This helps when seeds are sequential (1073, 2073, 3073...)
        const burnCount = seed % 5; // Burn 0-4 calls based on seed
        for (let i = 0; i < burnCount; i++) {
            random();
        }

        const objectsToPlace = [];
        const roomTypeLower = roomType.toLowerCase();

        console.log(`[EnvironmentObjectManager] Choosing objects for ${roomTypeLower} room in ${biome} biome`);

        // Iterate through all object definitions
        for (const [objectType, definition] of Object.entries(OBJECT_SPAWN_DEFINITIONS)) {
            // Check if this object can spawn in this biome
            if (!definition.biomes.includes(biome)) {
                continue;
            }

            // Check if this object can spawn in this room type
            if (!definition.allowedRoomTypes.includes(roomTypeLower)) {
                continue;
            }

            // Check floor limits
            if (definition.maxPerFloor !== null) {
                const currentCount = floorObjectTracker[objectType] || 0;
                if (currentCount >= definition.maxPerFloor) {
                    console.log(`[EnvironmentObjectManager] Skipping ${objectType} - floor limit reached (${currentCount}/${definition.maxPerFloor})`);
                    continue;
                }
            }

            // Roll for spawn probability
            const spawnRoll = random();
            console.log(`[EnvironmentObjectManager] ${objectType} spawn roll: ${spawnRoll.toFixed(3)} vs ${definition.spawnProbability} (${spawnRoll <= definition.spawnProbability ? 'PASS' : 'FAIL'})`);
            if (spawnRoll > definition.spawnProbability) {
                continue;
            }

            // Object will spawn - determine quantity
            const minCount = definition.minCount;
            const maxCount = definition.maxCount;
            const spawnCount = minCount + Math.floor(random() * (maxCount - minCount + 1));

            console.log(`[EnvironmentObjectManager] ${objectType} will spawn ${spawnCount} instances (${minCount}-${maxCount} range)`);

            // Add objects to spawn list
            for (let i = 0; i < spawnCount; i++) {
                objectsToPlace.push({
                    type: objectType,
                    placement: definition.placement,
                    placementDetail: definition.placementDetail,
                    index: i,
                    totalCount: spawnCount
                });
            }

            // Update floor tracker if this object has floor limits
            if (definition.maxPerFloor !== null) {
                floorObjectTracker[objectType] = (floorObjectTracker[objectType] || 0) + spawnCount;
            }
        }

        console.log(`[EnvironmentObjectManager] Final objects to place:`, objectsToPlace.map(obj => `${obj.type} (${obj.placement})`));

        return objectsToPlace;
    }

    /**
     * Get placement positions for objects in a room
     * @param {Array} objectsToPlace - Array of objects to place
     * @param {object} roomDimensions - The dimensions of the room
     * @param {number} seed - Random seed for deterministic placement
     * @param {string} roomShape - The shape of the room for validation
     * @returns {Array} Array of objects with position information
     */
    getObjectPlacements(objectsToPlace, roomDimensions, seed = Date.now(), roomShape = null, validFloorPositions = null) {
        console.log(`[EnvironmentObjectManager] Getting placements for ${objectsToPlace.length} objects`);

        // Create seeded random function
        const random = mulberry32(seed);

        // Default object size
        const defaultObjectSize = 1.0;

        // Process each object and filter out null results
        return objectsToPlace.map(object => {
            const { type, placement, placementDetail, index, totalCount } = object;

            console.log(`[EnvironmentObjectManager] Placing object: type=${type}, placement=${placement}, detail=${placementDetail}`);

            // Get placement function based on placement detail
            const placementFunc = PLACEMENT_DETAILS[placementDetail] || PLACEMENT_DETAILS.random;

            // Calculate position
            console.log(`[EnvironmentObjectManager] Using placement function: ${placementDetail || 'random'} for room shape: ${roomShape || 'unknown'}`);
            const placementResult = placementFunc(roomDimensions, defaultObjectSize, index, totalCount, roomShape, validFloorPositions);

            // CRITICAL FIX: Handle null placement result (when no valid position found)
            if (placementResult === null) {
                console.warn(`[EnvironmentObjectManager] Placement function returned null for ${type}, skipping object`);
                return null; // Skip this object
            }

            // Extract position and additional wall info if available
            let position;
            let wallInfo = {};

            if (typeof placementResult === 'object' && 'x' in placementResult && 'y' in placementResult && 'z' in placementResult) {
                // Extract position
                position = {
                    x: placementResult.x,
                    y: placementResult.y,
                    z: placementResult.z
                };

                // Extract wall info if available
                if ('inwardNormal' in placementResult) {
                    wallInfo.inwardNormal = placementResult.inwardNormal;
                }
                if ('wallName' in placementResult) {
                    wallInfo.wallName = placementResult.wallName;
                }
                if ('wallIndex' in placementResult) {
                    wallInfo.wallIndex = placementResult.wallIndex;
                }
            } else {
                // Legacy support for functions that just return position
                position = placementResult;
            }

            console.log(`[EnvironmentObjectManager] Initial position: x=${position.x}, y=${position.y}, z=${position.z}`);
            if (Object.keys(wallInfo).length > 0) {
                console.log(`[EnvironmentObjectManager] Wall info:`, wallInfo);
            }

            // Add some randomness to position if not using specific placement
            if (placementDetail === 'random') {
                // Small randomness just to avoid perfect grid alignment
                position.x += (random() - 0.5) * 0.3;
                position.z += (random() - 0.5) * 0.3;
                console.log(`[EnvironmentObjectManager] Added randomness: x=${position.x}, y=${position.y}, z=${position.z}`);
            }

            // Add rotation
            const rotation = random() * Math.PI * 2;

            return {
                ...object,
                position,
                rotation,
                options: {
                    ...object,
                    position,
                    rotation,
                    placement: object.placement,
                    ...wallInfo  // Include wall info in options
                }
            };
        }).filter(Boolean); // CRITICAL FIX: Remove null results from failed placements
    }

    /**
     * Create object instances for placement in a room
     * @param {Array} objectPlacements - Array of objects with position information
     * @returns {Array} Array of object instances
     */
    createObjectInstances(objectPlacements) {
        console.log('Creating object instances for', objectPlacements.length, 'objects');

        return objectPlacements.map(object => {
            const { type, placement, position, rotation } = object;

            // Get the prefab function for this object type
            const prefabFunc = getPrefabFunction(type, 'interior');

            if (!prefabFunc) {
                console.warn(`No prefab function found for object type: ${type}`);
                return null;
            }

            console.log(`Found prefab function for ${type}:`, prefabFunc ? 'YES' : 'NO');

            // Create options for the prefab function
            const isDestructible = OBJECT_ROLE_MAP[type] === OBJECT_ROLES.DESTRUCTIBLE;
            console.log(`Object ${type} isDestructible: ${isDestructible}, role: ${OBJECT_ROLE_MAP[type]}`);

            // Get any additional options from the object
            const additionalOptions = object.options || {};

            const options = {
                position,
                rotation,
                placement,
                isDestructible: isDestructible,
                destructionEffect: 'collapse',
                health: 1,
                ...additionalOptions // Include any additional options like inwardNormal, wallName, etc.
            };

            return {
                type,
                prefabFunc,
                options
            };
        }).filter(Boolean);
    }
}

// Create and export a singleton instance
const environmentObjectManager = new EnvironmentObjectManager();
export default environmentObjectManager;
