/**
 * SecretRoomManager - <PERSON><PERSON> secret room generation and brick wall entrance mechanics
 * Inspired by the Harry <PERSON> Diagon Alley entrance
 */

import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';
import { AnimatedBrickWall } from '../effects/AnimatedBrickWall.js';
import { createStonebrickWallSegment } from '../generators/prefabs/stonebrickWall.js';
import { ROOM_WORLD_SIZE, WALL_HEIGHT, WALL_DEPTH } from '../scenes/roomGenerator.js';
import {
    VOXEL_SIZE,
    ENVIRONMENT_PIXEL_SCALE,
    mulberry32,
    stonebrickMaterialPrimary,
    stonebrickMaterialSecondary,
    stonebrickCenterMaterial,
    stonebrickMortarMaterial,
    mossMaterialPrimary,
    mossMaterialSecondary,
    _getMaterialByHex_Cached,
    getOrCreateGeometry
} from '../generators/prefabs/shared.js';

export class SecretRoomManager {
    constructor(dungeonHandler) {
        this.dungeonHandler = dungeonHandler;
        this.secretRooms = new Map(); // Map of areaName -> secretRoomData
        this.secretWalls = new Map(); // Map of areaName -> secretWallData
        this.activeAnimations = new Map(); // Track ongoing animations
        this.cooldowns = new Map(); // Track cooldowns for reopening
        this.animatedWalls = new Map(); // Map of areaName -> AnimatedBrickWall instance
        this.lastRoomId = null; // Track previous room for detecting secret room exits
        this.playerInSecretRoom = false; // Track if player is currently in a secret room
        this.magicalStars = new Map(); // Map of areaName -> star mesh for visual cues

        // Voice recognition setup
        this.isListening = false;
        this.recognition = null;
        this.microphonePermissionGranted = false;

        // Setup voice recognition asynchronously
        this.setupVoiceRecognition().catch(error => {
            console.error('[SecretRoomManager] Failed to setup voice recognition:', error);
        });

        // Setup keyboard fallback for secret activation
        this.setupKeyboardFallback();

        // Setup mobile touch gestures
        this.setupMobileTouchGestures();

        // Player proximity tracking
        this.proximityCheckInterval = null;
        this.lastProximityCheck = 0;
        this.isPlayerNearSecret = false;

        console.log('[SecretRoomManager] Initialized');
    }

    /**
     * Generate a secret room for the current area
     * @param {string} areaName - Name of the area (e.g., "The Catacombs")
     * @param {Map} floorLayout - Current floor layout
     * @param {Object} areaData - Area configuration data
     */
    generateSecretRoom(areaName, floorLayout, areaData) {
        if (this.secretRooms.has(areaName)) {
            console.log(`[SecretRoomManager] Secret room already exists for ${areaName}`);
            return;
        }

        // GUARANTEE: Try multiple strategies to ensure secret room creation
        let secretWallData = null;
        let attempt = 0;
        const maxAttempts = 3;

        while (!secretWallData && attempt < maxAttempts) {
            attempt++;
            console.log(`[SecretRoomManager] Secret room placement attempt ${attempt}/${maxAttempts} for ${areaName}`);

            if (attempt === 1) {
                // First attempt: Standard placement (normal rooms only)
                secretWallData = this.findSuitableSecretWall(floorLayout);
            } else if (attempt === 2) {
                // Second attempt: Relaxed placement (allow any room except Room 0, Boss, Event)
                secretWallData = this.findSuitableSecretWallRelaxed(floorLayout);
            } else if (attempt === 3) {
                // Third attempt: Force placement (create space if needed)
                secretWallData = this.forceSecretWallPlacement(floorLayout);
            }

            if (secretWallData) {
                console.log(`[SecretRoomManager] ✅ Secret wall placement successful on attempt ${attempt}`);
                break;
            } else {
                console.warn(`[SecretRoomManager] ❌ Secret wall placement failed on attempt ${attempt}`);
            }
        }

        if (!secretWallData) {
            console.error(`[SecretRoomManager] CRITICAL: Failed to place secret room after ${maxAttempts} attempts in ${areaName}`);
            console.error(`[SecretRoomManager] This should never happen - secret rooms are required per floor`);
            return;
        }

        // Generate secret room ID (use negative ID to avoid conflicts)
        const secretRoomId = -Math.abs(Math.floor(Math.random() * 1000000));

        // Create secret room data
        const secretRoomData = {
            id: secretRoomId,
            coords: {
                x: secretWallData.hostRoom.coords.x + secretWallData.offsetX,
                y: secretWallData.hostRoom.coords.y + secretWallData.offsetY
            },
            shapeKey: 'SQUARE_1X1',
            type: 'SECRET',
            connections: {
                [secretWallData.entranceDirection]: secretWallData.hostRoom.id
            },
            isSecret: true,
            isDiscovered: false,
            visited: false, // Add visited property for minimap
            state: {
                area: areaData.name.toLowerCase().replace(/\s+/g, '_').replace(/^the_/, ''),
                enemiesCleared: true, // Secret rooms start cleared
                itemTaken: false
            }
        };

        // Add secret room connection to host room BUT mark it as secret
        if (!secretWallData.hostRoom.connections) {
            secretWallData.hostRoom.connections = {};
        }
        secretWallData.hostRoom.connections[secretWallData.direction] = secretRoomId;

        // FIXED: Mark this connection as secret so door trigger creation can handle it specially
        if (!secretWallData.hostRoom.secretConnections) {
            secretWallData.hostRoom.secretConnections = {};
        }
        secretWallData.hostRoom.secretConnections[secretWallData.direction] = true;

        // CRITICAL: Also mark in roomData for consistency
        if (secretWallData.hostRoom.roomData) {
            if (!secretWallData.hostRoom.roomData.secretConnections) {
                secretWallData.hostRoom.roomData.secretConnections = {};
            }
            secretWallData.hostRoom.roomData.secretConnections[secretWallData.direction] = true;
        }

        // Add to floor layout (but don't make visible until discovered)
        floorLayout.set(secretRoomId, secretRoomData);

        // Store secret room and wall data
        this.secretRooms.set(areaName, secretRoomData);
        this.secretWalls.set(areaName, secretWallData);

        console.log(`[SecretRoomManager] Generated secret room ${secretRoomId} for ${areaName}`);
        console.log(`[SecretRoomManager] Secret entrance in room ${secretWallData.hostRoom.id} on ${secretWallData.direction} wall`);
        console.log(`[SecretRoomManager] Host room connections updated:`, secretWallData.hostRoom.connections);

        // Start proximity monitoring
        this.startProximityMonitoring();
    }

    /**
     * Find a suitable wall segment for the secret entrance
     * @param {Map} floorLayout - Current floor layout
     * @returns {Object|null} Secret wall data or null if none found
     */
    findSuitableSecretWall(floorLayout) {
        // FIXED: Look for pre-assigned secret room host first (from DungeonGenerator)
        const preAssignedHost = Array.from(floorLayout.values()).find(room =>
            room.hasSecretRoom === true || room.roomData?.hasSecretRoom === true
        );

        if (preAssignedHost) {
            console.log(`[SecretRoomManager] ✅ Found pre-assigned secret room host: Room ${preAssignedHost.id}`);
            console.log(`[SecretRoomManager] Host room type: ${preAssignedHost.type}, hasSecretRoom: ${preAssignedHost.hasSecretRoom}`);

            // Try to use the pre-assigned host
            const secretWallData = this.tryRoomForSecretWall(preAssignedHost, floorLayout);
            if (secretWallData) {
                console.log(`[SecretRoomManager] ✅ Successfully using pre-assigned host Room ${preAssignedHost.id}`);
                return secretWallData;
            } else {
                console.warn(`[SecretRoomManager] ⚠️ Pre-assigned host Room ${preAssignedHost.id} has no suitable walls, falling back to random selection`);
            }
        } else {
            console.warn(`[SecretRoomManager] ❌ No pre-assigned secret room host found from DungeonGenerator`);
            console.log(`[SecretRoomManager] Available rooms:`, Array.from(floorLayout.values()).map(r =>
                `${r.id}(${r.type}, hasSecret: ${r.hasSecretRoom || r.roomData?.hasSecretRoom || false})`
            ));
        }

        // Fallback: Use original random selection logic with enhanced filtering
        const rooms = Array.from(floorLayout.values()).filter(room => {
            // Basic exclusions
            if (room.id === 0) {
                console.log(`[SecretRoomManager] Excluding room ${room.id}: Starting room`);
                return false;
            }
            if (room.isSecret) {
                console.log(`[SecretRoomManager] Excluding room ${room.id}: Already a secret room`);
                return false;
            }
            
            // CRITICAL: Exclude boss rooms (multiple type variations)
            if (room.type === 'Boss' || room.type === 'BOSS' || room.type === 'boss') {
                console.log(`[SecretRoomManager] Excluding room ${room.id}: Boss room (${room.type})`);
                return false;
            }
            
            // CRITICAL: Exclude event rooms (multiple type variations)
            if (room.type === 'Event' || room.type === 'EVENT' || room.type === 'event') {
                console.log(`[SecretRoomManager] Excluding room ${room.id}: Event room (${room.type})`);
                return false;
            }
            
            console.log(`[SecretRoomManager] Room ${room.id} (${room.type}) is eligible for secret wall placement`);
            return true;
        });

        if (rooms.length === 0) {
            console.log(`[SecretRoomManager] No suitable rooms found for secret wall placement`);
            return null;
        }

        console.log(`[SecretRoomManager] Found ${rooms.length} candidate rooms for secret wall placement`);
        console.log(`[SecretRoomManager] Candidate room types:`, rooms.map(r => `${r.id}(${r.type})`));

        // IMPROVED: Try multiple rooms until we find one with suitable walls
        const maxAttempts = Math.min(rooms.length, 10); // Try up to 10 rooms or all available rooms
        const triedRooms = new Set();

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            // Select a random room that we haven't tried yet
            const availableRooms = rooms.filter(room => !triedRooms.has(room.id));
            if (availableRooms.length === 0) {
                console.log(`[SecretRoomManager] Exhausted all available rooms after ${attempt} attempts`);
                break;
            }

            const randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)];
            triedRooms.add(randomRoom.id);

            console.log(`[SecretRoomManager] Attempt ${attempt + 1}: Trying room ${randomRoom.id}`);

            // Use the helper method to try this room
            const secretWallData = this.tryRoomForSecretWall(randomRoom, floorLayout);
            if (secretWallData) {
                return secretWallData;
            } else {
                console.log(`[SecretRoomManager] Room ${randomRoom.id} has no suitable walls, trying next room...`);
            }
        }

        console.warn(`[SecretRoomManager] ❌ Could not find any suitable room/wall combination for secret wall after ${maxAttempts} attempts`);
        console.warn(`[SecretRoomManager] This may be due to event room protection or all walls having existing connections`);
        return null;
    }

    /**
     * Try to use a specific room for secret wall placement
     * @param {Object} room - Room to try
     * @param {Map} floorLayout - Current floor layout
     * @returns {Object|null} Secret wall data or null if room is not suitable
     */
    tryRoomForSecretWall(room, floorLayout) {
        console.log(`[SecretRoomManager] Trying room ${room.id} for secret wall placement`);

        // Define possible wall directions (exclude south walls as they're invisible)
        const possibleDirections = ['n', 'e', 'w'].filter(dir => {
            // CRITICAL FIX: Ensure the wall doesn't already have a door connection
            if (room.connections && room.connections[dir] !== null && room.connections[dir] !== undefined) {
                console.log(`[SecretRoomManager] Room ${room.id} direction ${dir} already has door connection to room ${room.connections[dir]} - BLOCKED`);
                return false;
            }

            // ADDITIONAL SAFETY: Check room neighbors to ensure no adjacent room exists in this direction
            if (room.neighbors && room.neighbors[dir] !== null && room.neighbors[dir] !== undefined) {
                const neighborId = room.neighbors[dir];
                const neighborRoom = floorLayout.get(neighborId);
                if (neighborRoom) {
                    console.log(`[SecretRoomManager] Room ${room.id} direction ${dir} has neighbor room ${neighborId} (${neighborRoom.type}) - BLOCKED`);
                    return false;
                }
            }

            return true;
        });

        console.log(`[SecretRoomManager] Room ${room.id} has ${possibleDirections.length} suitable directions: [${possibleDirections.join(', ')}]`);

        // If this room has suitable directions, use it
        if (possibleDirections.length > 0) {
            // Randomly select a direction
            const direction = possibleDirections[Math.floor(Math.random() * possibleDirections.length)];
            console.log(`[SecretRoomManager] ✅ Selected room ${room.id} with direction ${direction} for secret wall`);

            // Calculate offset for secret room position
            let offsetX = 0, offsetY = 0;
            let entranceDirection = '';

            switch (direction) {
                case 'n':
                    offsetY = -1;
                    entranceDirection = 's';
                    break;
                case 's':
                    offsetY = 1;
                    entranceDirection = 'n';
                    break;
                case 'e':
                    offsetX = 1;
                    entranceDirection = 'w';
                    break;
                case 'w':
                    offsetX = -1;
                    entranceDirection = 'e';
                    break;
            }

            return {
                hostRoom: room,
                direction: direction,
                entranceDirection: entranceDirection,
                offsetX: offsetX,
                offsetY: offsetY,
                wallPosition: this.calculateWallPosition(room, direction),
                isRevealed: false,
                isAnimating: false
            };
        } else {
            console.log(`[SecretRoomManager] Room ${room.id} has no suitable walls`);
            return null;
        }
    }

    /**
     * Relaxed secret wall placement - allows any room except Room 0, Boss, and Event rooms
     * @param {Map} floorLayout - Current floor layout
     * @returns {Object|null} Secret wall data or null if no suitable room found
     */
    findSuitableSecretWallRelaxed(floorLayout) {
        console.log(`[SecretRoomManager] RELAXED PLACEMENT: Attempting more flexible secret wall placement`);
        
        // More flexible filtering - still exclude critical rooms
        const rooms = Array.from(floorLayout.values()).filter(room => {
            // Basic exclusions - same as strict mode
            if (room.id === 0) {
                console.log(`[SecretRoomManager] RELAXED: Excluding room ${room.id}: Starting room`);
                return false;
            }
            if (room.isSecret) {
                console.log(`[SecretRoomManager] RELAXED: Excluding room ${room.id}: Already a secret room`);
                return false;
            }
            
            // STILL CRITICAL: Exclude boss rooms (multiple type variations)
            if (room.type === 'Boss' || room.type === 'BOSS' || room.type === 'boss') {
                console.log(`[SecretRoomManager] RELAXED: Excluding room ${room.id}: Boss room (${room.type})`);
                return false;
            }
            
            // STILL CRITICAL: Exclude event rooms (multiple type variations)
            if (room.type === 'Event' || room.type === 'EVENT' || room.type === 'event') {
                console.log(`[SecretRoomManager] RELAXED: Excluding room ${room.id}: Event room (${room.type})`);
                return false;
            }
            
            console.log(`[SecretRoomManager] RELAXED: Room ${room.id} (${room.type}) is eligible for secret wall placement`);
            return true;
        });

        if (rooms.length === 0) {
            console.log(`[SecretRoomManager] RELAXED: No suitable rooms found for secret wall placement`);
            return null;
        }

        console.log(`[SecretRoomManager] RELAXED: Found ${rooms.length} candidate rooms for secret wall placement`);
        console.log(`[SecretRoomManager] RELAXED: Candidate room types:`, rooms.map(r => `${r.id}(${r.type})`));

        // Try all rooms until we find one with suitable walls
        const maxAttempts = rooms.length;
        const triedRooms = new Set();

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            const availableRooms = rooms.filter(room => !triedRooms.has(room.id));
            
            if (availableRooms.length === 0) {
                console.log(`[SecretRoomManager] RELAXED: Exhausted all available rooms after ${attempt} attempts`);
                break;
            }

            const randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)];
            triedRooms.add(randomRoom.id);

            console.log(`[SecretRoomManager] RELAXED: Attempt ${attempt + 1}: Trying room ${randomRoom.id}`);

            const secretWallData = this.tryRoomForSecretWall(randomRoom, floorLayout);
            if (secretWallData) {
                return secretWallData;
            } else {
                console.log(`[SecretRoomManager] RELAXED: Room ${randomRoom.id} has no suitable walls, trying next room...`);
            }
        }

        console.warn(`[SecretRoomManager] RELAXED: Could not find any suitable room/wall combination for secret wall after ${maxAttempts} attempts`);
        return null;
    }

    /**
     * Force secret wall placement by creating space if needed
     * @param {Map} floorLayout - Current floor layout
     * @returns {Object|null} Secret wall data or null if absolutely impossible
     */
    forceSecretWallPlacement(floorLayout) {
        console.log(`[SecretRoomManager] FORCE PLACEMENT: Last resort - attempting to force secret wall placement`);
        
        // Get all normal rooms (still respect boss/event room restrictions)
        const normalRooms = Array.from(floorLayout.values()).filter(room => {
            if (room.id === 0 || room.isSecret) return false;
            
            // NEVER compromise on boss/event room protection
            if (room.type === 'Boss' || room.type === 'BOSS' || room.type === 'boss') {
                console.log(`[SecretRoomManager] FORCE: Still excluding boss room ${room.id}`);
                return false;
            }
            if (room.type === 'Event' || room.type === 'EVENT' || room.type === 'event') {
                console.log(`[SecretRoomManager] FORCE: Still excluding event room ${room.id}`);
                return false;
            }
            
            return room.type === 'Normal';
        });
        
        if (normalRooms.length === 0) {
            console.error(`[SecretRoomManager] FORCE: No normal rooms available - cannot force secret wall placement`);
            return null;
        }
        
        console.log(`[SecretRoomManager] FORCE: Found ${normalRooms.length} normal rooms to try`);
        
        // Try each normal room and find ANY available direction (even if it might have loose connections)
        for (const room of normalRooms) {
            console.log(`[SecretRoomManager] FORCE: Examining room ${room.id} for any possible wall placement`);
            
            // Check all directions for any possibility
            const directions = ['n', 'e', 'w']; // Still exclude south (invisible)
            
            for (const direction of directions) {
                // RELAXED: Only check for major blocking connections
                const hasStrongConnection = room.connections && 
                    room.connections[direction] !== null && 
                    room.connections[direction] !== undefined;
                
                if (!hasStrongConnection) {
                    console.log(`[SecretRoomManager] FORCE: Found potential wall at room ${room.id} direction ${direction}`);
                    
                    // Calculate basic placement data
                    let offsetX = 0, offsetY = 0, entranceDirection = '';
                    
                    switch (direction) {
                        case 'n':
                            offsetY = -1;
                            entranceDirection = 's';
                            break;
                        case 'e':
                            offsetX = 1;
                            entranceDirection = 'w';
                            break;
                        case 'w':
                            offsetX = -1;
                            entranceDirection = 'e';
                            break;
                    }
                    
                    console.log(`[SecretRoomManager] FORCE: Successfully forced secret wall placement at room ${room.id} direction ${direction}`);
                    
                    return {
                        hostRoom: room,
                        direction: direction,
                        entranceDirection: entranceDirection,
                        offsetX: offsetX,
                        offsetY: offsetY,
                        wallPosition: this.calculateWallPosition(room, direction),
                        isRevealed: false,
                        isAnimating: false,
                        forced: true // Mark as forced placement
                    };
                }
            }
        }
        
        console.error(`[SecretRoomManager] FORCE: Could not force secret wall placement - all normal rooms have blocking connections`);
        return null;
    }

    /**
     * Calculate the world position of a wall in a room
     * @param {Object} room - Room data
     * @param {string} direction - Wall direction (n, e, s, w)
     * @returns {THREE.Vector3} Wall position
     */
    calculateWallPosition(room, direction) {
        console.log(`[SecretRoomManager] Calculating wall position for room:`, room);
        console.log(`[SecretRoomManager] Room coordinates: x=${room.coords?.x}, y=${room.coords?.y}`);

        // FIXED: Use consistent coordinate system with room generation
        // Room coordinates are in grid units, convert to world coordinates
        const roomWorldX = (room.coords?.x || 0) * ROOM_WORLD_SIZE * 2; // Use ROOM_WORLD_SIZE constant
        const roomWorldZ = (room.coords?.y || 0) * ROOM_WORLD_SIZE * 2;
        const R = ROOM_WORLD_SIZE / 2; // Half room size for wall positioning

        let wallX = roomWorldX;
        let wallZ = roomWorldZ;

        console.log(`[SecretRoomManager] Base room world position: (${roomWorldX}, ${roomWorldZ})`);

        // FIXED: Position walls at room boundaries, not center offsets
        switch (direction) {
            case 'n':
                wallZ -= R; // North wall at room's north boundary
                break;
            case 'e':
                wallX += R; // East wall at room's east boundary
                break;
            case 's':
                wallZ += R; // South wall at room's south boundary
                break;
            case 'w':
                wallX -= R; // West wall at room's west boundary
                break;
        }

        const wallPosition = new THREE.Vector3(wallX, WALL_HEIGHT / 2, wallZ);
        console.log(`[SecretRoomManager] FIXED: Calculated wall position: (${wallX}, ${WALL_HEIGHT / 2}, ${wallZ})`);

        return wallPosition;
    }

    /**
     * DEPRECATED: Create separate secret wall (creates visual seams)
     * This method should NOT be used - secret walls should be integrated during wall generation
     * @param {string} areaName - Area name
     * @param {Object} secretWallData - Secret wall data
     * @param {THREE.Group} roomGroup - Room group to add wall to
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {THREE.Vector3} doorPosition - Exact door position
     * @param {number} doorRotation - Exact door rotation
     * @returns {boolean} Always returns false - this method is disabled
     */
    createAnimatedSecretWall(areaName, secretWallData, roomGroup, collisionMeshes, doorPosition, doorRotation) {
        console.error(`[SecretRoomManager] ❌ DEPRECATED: createAnimatedSecretWall called for room ${secretWallData.hostRoom.id}`);
        console.error(`[SecretRoomManager] ❌ This method creates overlapping geometry and visual seams`);
        console.error(`[SecretRoomManager] ❌ Secret walls should be integrated during wall segment creation only`);

        // DISABLED: Do not create overlapping secret walls
        return false;

        // Create a secret wall with individual voxel meshes (not merged) for animation
        const secretWallGroup = this.createAnimatableSecretWall(
            2.5, // DOOR_WIDTH - same width as door opening (NOT SEAMLESS)
            WALL_HEIGHT,
            1.0, // Wall depth
            secretWallData.hostRoom
        );

        if (!secretWallGroup) {
            console.error('[SecretRoomManager] Failed to create animatable secret wall');
            return false;
        }

        // FIXED: Position the wall at the exact wall boundary, not door position
        const direction = secretWallData.direction;
        const wallPosition = new THREE.Vector3();

        // Calculate wall position based on room center and direction
        const roomCenter = new THREE.Vector3(0, 0, 0); // Room is generated at origin
        const R = ROOM_WORLD_SIZE / 2; // Half room size

        // Position wall at room boundary
        switch (direction) {
            case 'n':
                wallPosition.set(roomCenter.x, WALL_HEIGHT / 2, roomCenter.z - R);
                break;
            case 's':
                wallPosition.set(roomCenter.x, WALL_HEIGHT / 2, roomCenter.z + R);
                break;
            case 'e':
                wallPosition.set(roomCenter.x + R, WALL_HEIGHT / 2, roomCenter.z);
                break;
            case 'w':
                wallPosition.set(roomCenter.x - R, WALL_HEIGHT / 2, roomCenter.z);
                break;
        }

        secretWallGroup.position.copy(wallPosition);
        secretWallGroup.rotation.y = doorRotation;

        console.log(`[SecretRoomManager] ⚠️  Secret wall positioned at: (${wallPosition.x.toFixed(2)}, ${wallPosition.y.toFixed(2)}, ${wallPosition.z.toFixed(2)})`);
        console.log(`[SecretRoomManager] ⚠️  This will NOT be seamless with the existing wall`);

        // Store reference to the wall group
        secretWallData.wallGroup = secretWallGroup;
        secretWallData.wallPosition = doorPosition.clone();
        secretWallData.wallRotation = doorRotation;

        // Mark the wall group for identification
        secretWallGroup.userData.isSecretWall = true;
        secretWallGroup.userData.areaName = areaName;
        secretWallGroup.userData.hostRoomId = secretWallData.hostRoom.id;
        secretWallGroup.userData.direction = secretWallData.direction;
        secretWallGroup.name = `secret_wall_${secretWallData.hostRoom.id}_${secretWallData.direction}`;

        // Add to room group and collision
        roomGroup.add(secretWallGroup);
        secretWallGroup.traverse(child => {
            if (child.isMesh) {
                collisionMeshes.push(child);
            }
        });

        // Add SUBTLE visual markers (much less obvious than before)
        this.addSubtleSecretWallMarkers(secretWallGroup);

        // IMPORTANT: Do NOT mark as revealed or create door trigger yet
        // This should only happen after the animation completes
        secretWallData.isRevealed = false;
        secretWallData.isAnimating = false;

        console.log(`[SecretRoomManager] DEBUG: Explicitly setting isRevealed = false for area ${areaName}`);

        console.log(`[SecretRoomManager] ⚠️  Secret wall created (FALLBACK - not seamless) in room ${secretWallData.hostRoom.id}`);
        console.log(`[SecretRoomManager] ⚠️  This should be fixed to use wall integration instead`);

        return true;
    }

    /**
     * REMOVED: Fallback method that created overlapping walls
     * Secret walls should ONLY be integrated during wall segment creation,
     * not created as separate overlapping geometry.
     *
     * @param {number} roomId - Room ID being generated
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {Object} doorPositions - Door positions for the room
     * @returns {Object|null} Always returns null - integration should happen earlier
     */
    checkAndCreateSecretWallDuringGeneration(roomId, roomGroup, collisionMeshes, doorPositions) {
        // Check if secret wall was already integrated during wall segment creation
        for (const [areaName, secretWallData] of this.secretWalls.entries()) {
            if (secretWallData.hostRoom.id === roomId) {
                const direction = secretWallData.direction;
                const requestedDirection = Object.keys(doorPositions)[0];

                if (direction === requestedDirection && secretWallData.wallGroup) {
                    console.log(`[SecretRoomManager] ✅ Secret wall already integrated for room ${roomId} direction ${direction}`);
                    return {
                        direction: secretWallData.direction,
                        position: secretWallData.wallPosition,
                        rotation: secretWallData.wallRotation,
                        skipDoor: true
                    };
                }
            }
        }

        // REMOVED: Fallback creation of separate secret walls
        // If we reach here, integration failed and we should NOT create overlapping geometry
        console.log(`[SecretRoomManager] No secret wall integration found for room ${roomId} - this is normal for most rooms`);
        return null;
    }

    /**
     * Integrate secret wall seamlessly into existing wall segment
     * @param {number} roomId - Room ID being generated
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {THREE.Group} wallSegmentGroup - The existing wall segment group
     * @param {Object} doorPositions - Door positions for the room
     * @param {Object} wallInfo - Wall segment info (position, rotation, length, direction)
     * @returns {Object|null} Integration result
     */
    integrateSecretWallIntoWallSegment(roomId, roomGroup, collisionMeshes, wallSegmentGroup, doorPositions, wallInfo) {
        console.log(`[SecretRoomManager] === WALL INTEGRATION DEBUG ===`);
        console.log(`[SecretRoomManager] Room ${roomId}: Checking for secret wall integration`);
        console.log(`[SecretRoomManager] Door positions:`, Object.keys(doorPositions));
        console.log(`[SecretRoomManager] Wall info:`, { direction: wallInfo.direction, position: wallInfo.position, rotation: wallInfo.rotation });

        // Check all areas for secret walls that belong to this room
        for (const [areaName, secretWallData] of this.secretWalls.entries()) {
            console.log(`[SecretRoomManager] Checking area ${areaName}: host room ${secretWallData.hostRoom.id}, direction ${secretWallData.direction}`);

            if (secretWallData.hostRoom.id === roomId) {
                const direction = secretWallData.direction;

                // FIXED: Check if this wall segment matches the secret wall direction
                const requestedDirection = Object.keys(doorPositions)[0];
                const wallSegmentDirection = wallInfo.direction;

                console.log(`[SecretRoomManager] 🎯 Found secret wall for room ${roomId}!`);
                console.log(`[SecretRoomManager] Secret direction: ${direction}`);
                console.log(`[SecretRoomManager] Requested direction: ${requestedDirection}`);
                console.log(`[SecretRoomManager] Wall segment direction: ${wallSegmentDirection}`);

                // Only integrate secret wall if this is the correct direction AND wall segment
                if (direction !== requestedDirection || direction !== wallSegmentDirection) {
                    console.log(`[SecretRoomManager] ❌ Direction mismatch: secret(${direction}) vs requested(${requestedDirection}) vs wall(${wallSegmentDirection})`);
                    return null; // Not the right direction or wall segment
                }

                console.log(`[SecretRoomManager] ✅ Direction match confirmed for ${direction} wall`);

                // Check if wall already integrated to prevent duplicates
                if (secretWallData.wallGroup) {
                    console.log(`[SecretRoomManager] Secret wall already integrated for room ${roomId} direction ${direction}`);
                    return { integrated: true };
                }

                const doorPosition = doorPositions[direction];

                if (doorPosition && wallSegmentGroup) {
                    console.log(`[SecretRoomManager] Integrating secret wall into full wall segment for room ${roomId} on ${direction} wall`);

                    // Convert the existing wall segment into an animatable secret wall
                    const success = this.convertWallSegmentToSecretWall(
                        areaName,
                        secretWallData,
                        wallSegmentGroup,
                        collisionMeshes,
                        doorPosition,
                        wallInfo
                    );

                    if (success) {
                        return {
                            integrated: true,
                            direction: direction,
                            position: doorPosition
                        };
                    }
                }
            }
        }

        return null;
    }

    /**
     * Convert the existing wall segment into an animatable secret wall
     * @param {string} areaName - Area name
     * @param {Object} secretWallData - Secret wall data
     * @param {THREE.Group} wallSegmentGroup - Existing wall segment group
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {THREE.Vector3} doorPosition - Door position
     * @param {Object} wallInfo - Wall segment info (position, rotation, length, direction)
     * @returns {boolean} Success status
     */
    convertWallSegmentToSecretWall(areaName, secretWallData, wallSegmentGroup, collisionMeshes, doorPosition, wallInfo) {
        console.log(`[SecretRoomManager] Converting existing wall segment to secret wall for room ${secretWallData.hostRoom.id}`);
        console.log(`[SecretRoomManager] Wall segment dimensions: ${wallInfo.length}w x ${WALL_HEIGHT}h x ${WALL_DEPTH}d`);

        // Mark the ENTIRE wall segment as a secret wall
        wallSegmentGroup.userData.isSecretWall = true;
        wallSegmentGroup.userData.areaName = areaName;
        wallSegmentGroup.userData.hostRoomId = secretWallData.hostRoom.id;
        wallSegmentGroup.userData.direction = secretWallData.direction;
        wallSegmentGroup.userData.originalDimensions = {
            width: wallInfo.length,
            height: WALL_HEIGHT,
            depth: WALL_DEPTH
        };
        wallSegmentGroup.userData.roomData = secretWallData.hostRoom;
        wallSegmentGroup.name = `secret_wall_${secretWallData.hostRoom.id}_${secretWallData.direction}`;

        // Mark all meshes in the wall segment as secret wall components AND add collision
        let meshCount = 0;
        let collisionAddedCount = 0;

        wallSegmentGroup.traverse((child) => {
            if (child.isMesh) {
                meshCount++;
                child.userData.isSecretWallMesh = true;
                child.userData.canBeAnimated = true;

                // FIXED: Add collision to existing wall meshes with detailed debugging
                const dungeonHandler = window.dungeonHandler || this.dungeonHandler;
                console.log(`[SecretRoomManager] DEBUG: dungeonHandler exists: ${!!dungeonHandler}`);
                console.log(`[SecretRoomManager] DEBUG: collisionMeshes exists: ${!!(dungeonHandler && dungeonHandler.collisionMeshes)}`);
                console.log(`[SecretRoomManager] DEBUG: collisionMeshes length: ${dungeonHandler?.collisionMeshes?.length || 'N/A'}`);

                if (dungeonHandler && dungeonHandler.collisionMeshes) {
                    // Check if already in collision array to avoid duplicates
                    if (!dungeonHandler.collisionMeshes.includes(child)) {
                        dungeonHandler.collisionMeshes.push(child);
                        collisionAddedCount++;
                        console.log(`[SecretRoomManager] ✅ Added collision to existing wall mesh (${collisionAddedCount}/${meshCount})`);
                        console.log(`[SecretRoomManager] Total collision meshes now: ${dungeonHandler.collisionMeshes.length}`);
                    } else {
                        console.log('[SecretRoomManager] Mesh already in collision array');
                    }
                } else {
                    console.warn('[SecretRoomManager] ❌ Could not access collision system - wall may not have collision!');
                    console.warn(`[SecretRoomManager] dungeonHandler: ${!!dungeonHandler}, collisionMeshes: ${!!(dungeonHandler && dungeonHandler.collisionMeshes)}`);
                }
            }
        });

        console.log(`[SecretRoomManager] SUMMARY: Found ${meshCount} meshes, added collision to ${collisionAddedCount} meshes`);

        // FIXED: Store references with proper positioning
        secretWallData.wallGroup = wallSegmentGroup;
        secretWallData.wallPosition = wallSegmentGroup.position.clone(); // Use actual wall position, not door position
        secretWallData.wallRotation = wallInfo.rotation;

        // IMPORTANT: Preserve existing revealed state during wall integration
        // Only set to false if not already revealed (e.g., from teleport)
        if (secretWallData.isRevealed === undefined) {
            secretWallData.isRevealed = false;
        }
        secretWallData.isAnimating = false;

        console.log(`[SecretRoomManager] ✅ Successfully converted ENTIRE wall segment to secret wall`);
        console.log(`[SecretRoomManager] Secret wall uses EXACT same dimensions as regular wall: ${wallInfo.length}w x ${WALL_HEIGHT}h`);
        console.log(`[SecretRoomManager] Secret wall seamlessly integrated - looks IDENTICAL to regular walls`);
        console.log(`[SecretRoomManager] Wall is NOT revealed yet - player must activate it first`);

        return true;
    }

    /**
     * Find existing wall segment in the specified direction
     * @param {THREE.Group} roomGroup - Room group
     * @param {string} direction - Wall direction (n, e, s, w)
     * @returns {THREE.Object3D|null} Existing wall segment or null
     */
    findExistingWallSegment(roomGroup, direction) {
        const R = ROOM_WORLD_SIZE / 2; // Half room size
        let targetPosition = new THREE.Vector3(0, 0, 0);

        // FIXED: Calculate expected wall position more precisely
        switch (direction) {
            case 'n':
                targetPosition.set(0, WALL_HEIGHT / 2, -R);
                break;
            case 'e':
                targetPosition.set(R, WALL_HEIGHT / 2, 0);
                break;
            case 's':
                targetPosition.set(0, WALL_HEIGHT / 2, R);
                break;
            case 'w':
                targetPosition.set(-R, WALL_HEIGHT / 2, 0);
                break;
        }

        console.log(`[SecretRoomManager] Looking for wall segment near position (${targetPosition.x}, ${targetPosition.y}, ${targetPosition.z}) for direction ${direction}`);

        // FIXED: Search specifically for wall segments with proper userData
        let closestWall = null;
        let closestDistance = Infinity;
        let wallCandidates = [];

        roomGroup.traverse((child) => {
            // FIXED: Look specifically for wall segments marked by room generation
            if (child.userData && child.userData.isWallSegment) {
                const distance = child.position.distanceTo(targetPosition);

                wallCandidates.push({
                    name: child.name || 'unnamed',
                    position: child.position,
                    distance: distance.toFixed(2),
                    userData: child.userData,
                    type: child.type,
                    isWallSegment: true
                });

                // FIXED: Use tighter distance tolerance for wall segments
                if (distance < closestDistance && distance < 2.0) {
                    closestDistance = distance;
                    closestWall = child;
                }
            }
        });

        console.log(`[SecretRoomManager] Found ${wallCandidates.length} wall segment candidates:`, wallCandidates);

        if (closestWall) {
            console.log(`[SecretRoomManager] ✅ Selected wall segment at distance ${closestDistance.toFixed(2)} from target position`);
            console.log(`[SecretRoomManager] Wall segment details:`, {
                name: closestWall.name,
                position: closestWall.position,
                userData: closestWall.userData
            });
        } else {
            console.warn(`[SecretRoomManager] ❌ No wall segment found near position (${targetPosition.x}, ${targetPosition.y}, ${targetPosition.z}) for direction ${direction}`);
            console.warn(`[SecretRoomManager] This may indicate a wall integration timing issue`);
        }

        return closestWall;
    }

    /**
     * Find wall segment at a specific position for integration
     * @param {THREE.Group} roomGroup - Room group
     * @param {THREE.Vector3} position - Position to search near
     * @param {string} direction - Wall direction
     * @returns {THREE.Object3D|null} Wall segment or null
     */
    findWallSegmentAtPosition(roomGroup, position, direction) {
        console.log(`[SecretRoomManager] Looking for wall segment near position (${position.x.toFixed(2)}, ${position.y.toFixed(2)}, ${position.z.toFixed(2)}) in direction ${direction}`);

        let closestWall = null;
        let closestDistance = Infinity;

        roomGroup.traverse((child) => {
            // Look for wall segments (groups with wall meshes)
            if (child.userData && child.userData.isWallSegment) {
                const distance = child.position.distanceTo(position);

                if (distance < closestDistance && distance < 3.0) {
                    closestDistance = distance;
                    closestWall = child;
                }
            }
        });

        if (closestWall) {
            console.log(`[SecretRoomManager] Found wall segment at distance ${closestDistance.toFixed(2)}`);
        } else {
            console.warn(`[SecretRoomManager] No wall segment found near position`);
        }

        return closestWall;
    }

    /**
     * DEPRECATED: Carve a hole in an existing wall segment
     * This method is no longer used since we convert the entire wall segment to a secret wall
     * @param {THREE.Object3D} wallSegment - Wall segment to carve
     * @param {THREE.Vector3} position - Position of the hole
     * @param {number} rotation - Rotation of the hole
     * @param {number} width - Width of the hole
     * @param {number} height - Height of the hole
     * @returns {boolean} Success status
     */
    carveSecretWallHole(wallSegment, position, rotation, width, height) {
        console.log(`[SecretRoomManager] ⚠️  DEPRECATED: carveSecretWallHole called - this should not happen`);
        console.log(`[SecretRoomManager] ⚠️  Secret walls should use entire wall segments, not carved holes`);

        // Do nothing - we don't want to carve holes anymore
        // The entire wall segment should be converted to a secret wall instead

        return false;
    }

    /**
     * Setup voice recognition for "reveal secret" command
     */
    async setupVoiceRecognition() {
        // Use microphone permission from character creation
        this.microphonePermissionGranted = window.microphonePermissionGranted || false;
        console.log('[SecretRoomManager] Using microphone permission from character creation:', this.microphonePermissionGranted);

        // Check for speech recognition support
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('[SecretRoomManager] Speech recognition not supported');
            this.showSpeechRecognitionNotSupportedMessage();
            return;
        }

        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        this.recognition.continuous = true;
        this.recognition.interimResults = false;
        this.recognition.lang = 'en-US';

        this.recognition.onresult = (event) => {
            const lastResult = event.results[event.results.length - 1];
            if (lastResult.isFinal) {
                const transcript = lastResult[0].transcript.toLowerCase().trim();
                console.log(`[SecretRoomManager] Voice input: "${transcript}"`);

                if (transcript.includes('reveal secret')) {
                    this.handleSecretRevealCommand();
                }
            }
        };

        this.recognition.onerror = (event) => {
            console.warn('[SecretRoomManager] Speech recognition error:', event.error);
            if (event.error === 'not-allowed') {
                this.showMicrophonePermissionMessage();
            }
        };

        this.recognition.onend = () => {
            if (this.isListening) {
                // Restart recognition if we're still supposed to be listening
                setTimeout(() => {
                    if (this.isListening) {
                        try {
                            this.recognition.start();
                        } catch (error) {
                            console.warn('[SecretRoomManager] Failed to restart speech recognition:', error);
                        }
                    }
                }, 100);
            }
        };

        console.log('[SecretRoomManager] Voice recognition setup complete');
    }

    /**
     * Setup keyboard fallback for secret activation
     */
    setupKeyboardFallback() {
        this.keyboardHandler = (event) => {
            // Use 'R' key as fallback for "Reveal secret"
            if (event.key.toLowerCase() === 'r' && this.isPlayerNearSecret) {
                console.log('[SecretRoomManager] Keyboard fallback activated (R key)');
                this.handleSecretRevealCommand();
            }
            // Use 'D' key for debug door trigger status
            else if (event.key.toLowerCase() === 'd' && event.ctrlKey) {
                console.log('[SecretRoomManager] Debug key activated (Ctrl+D)');
                this.debugDoorTriggerStatus();
            }
        };

        document.addEventListener('keydown', this.keyboardHandler);
        console.log('[SecretRoomManager] Keyboard fallback setup complete (Press R near secret walls, Ctrl+D for debug)');
    }

    /**
     * Setup mobile touch gestures for secret activation
     */
    setupMobileTouchGestures() {
        // Detect if we're on a mobile device
        this.isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                       ('ontouchstart' in window) ||
                       (navigator.maxTouchPoints > 0);

        if (!this.isMobile) {
            console.log('[SecretRoomManager] Desktop detected - touch gestures not needed');
            return;
        }

        console.log('[SecretRoomManager] Mobile device detected - setting up touch gestures');

        // Touch gesture variables (minimal - only for preventing default behaviors)
        this.touchStartTime = 0;

        // Touch event handlers
        this.touchStartHandler = (event) => {
            if (!this.isPlayerNearSecret) return;

            this.touchStartTime = Date.now();

            // Prevent default to avoid scrolling/zooming
            event.preventDefault();
        };

        this.touchEndHandler = (event) => {
            if (!this.isPlayerNearSecret) return;

            // Prevent default touch behaviors but don't activate secret doors
            // Secret doors are now only activated by voice commands, R key, or double-tap joystick
            event.preventDefault();
        };

        // Add touch event listeners
        document.addEventListener('touchstart', this.touchStartHandler, { passive: false });
        document.addEventListener('touchend', this.touchEndHandler, { passive: false });

        console.log('[SecretRoomManager] Mobile touch gestures setup complete (Touch prevention only - use double-tap joystick for activation)');
    }

    /**
     * Start listening for voice commands
     */
    startListening() {
        if (!this.recognition || this.isListening) return;

        try {
            this.isListening = true;
            this.recognition.start();
            console.log('[SecretRoomManager] Started listening for voice commands');
        } catch (error) {
            console.warn('[SecretRoomManager] Failed to start voice recognition:', error);
            this.isListening = false;
        }
    }

    /**
     * Stop listening for voice commands
     */
    stopListening() {
        if (!this.recognition || !this.isListening) return;

        this.isListening = false;
        this.recognition.stop();
        console.log('[SecretRoomManager] Stopped listening for voice commands');
    }

    /**
     * Start monitoring player proximity to secret walls
     */
    startProximityMonitoring() {
        if (this.proximityCheckInterval) return;

        this.proximityCheckInterval = setInterval(() => {
            this.checkPlayerProximity();
        }, 500); // Check every 500ms

        console.log('[SecretRoomManager] Started proximity monitoring');
    }

    /**
     * Stop monitoring player proximity
     */
    stopProximityMonitoring() {
        if (this.proximityCheckInterval) {
            clearInterval(this.proximityCheckInterval);
            this.proximityCheckInterval = null;
        }

        this.stopListening();
        console.log('[SecretRoomManager] Stopped proximity monitoring');
    }

    /**
     * Check if player is near any secret walls
     */
    checkPlayerProximity() {
        if (!this.dungeonHandler.player) {
            console.log('[SecretRoomManager] No player reference found');
            return;
        }

        const playerPosition = this.dungeonHandler.player.position;
        const currentArea = this.dungeonHandler.currentArea?.name || 'The Catacombs';
        const secretWallData = this.secretWalls.get(currentArea);

        if (!secretWallData) {
            console.log(`[SecretRoomManager] No secret wall data for area: ${currentArea}`);
            return;
        }

        // Allow proximity detection for both revealed and closed secret walls
        // (so player can reopen closed secret doors)

        // Check if player is in the correct room
        if (this.dungeonHandler.currentRoomId !== secretWallData.hostRoom.id) {
            return; // Player not in secret wall room
        }

        // Use actual wall position from animated wall or stored position
        let wallPosition;
        if (secretWallData.animatedWall && secretWallData.animatedWall.wallPosition) {
            wallPosition = secretWallData.animatedWall.wallPosition;
        } else if (secretWallData.wallPosition) {
            wallPosition = secretWallData.wallPosition;
        } else {
            // Calculate expected wall position for proximity checking
            const R = ROOM_WORLD_SIZE / 2;
            wallPosition = new THREE.Vector3(0, WALL_HEIGHT / 2, 0);

            switch (secretWallData.direction) {
                case 'n':
                    wallPosition.z = -R;
                    break;
                case 'e':
                    wallPosition.x = R;
                    break;
                case 's':
                    wallPosition.z = R;
                    break;
                case 'w':
                    wallPosition.x = -R;
                    break;
            }
        }

        const distance = playerPosition.distanceTo(wallPosition);
        const wasNearSecret = this.isPlayerNearSecret;
        this.isPlayerNearSecret = distance <= 3.0;

        // Debug proximity checking (only when very close)
        if (distance <= 2.0) { // Only show when very close
            console.log(`[SecretRoomManager] Player near secret wall: ${distance.toFixed(2)} units`);
        }

        // Player entered proximity
        if (this.isPlayerNearSecret && !wasNearSecret) {
            console.log(`[SecretRoomManager] Player near secret wall in room ${secretWallData.hostRoom.id} (distance: ${distance.toFixed(2)})`);

            // Show appropriate activation instructions based on device and door state
            const doorState = secretWallData.isRevealed ? 'open' : 'closed';
            const action = secretWallData.isRevealed ? 'reopen' : 'reveal';

            if (this.isMobile) {
                console.log(`[SecretRoomManager] 📱 Secret door is ${doorState} - Double-tap right joystick to ${action} secret`);
            } else {
                console.log(`[SecretRoomManager] 🎤 Secret door is ${doorState} - Say "reveal secret" or press R key to ${action}`);
            }

            // Add magical blinking star visual cue when player is near a closed secret door
            // Star appears every time the sound plays (every time player enters proximity)
            if (!secretWallData.isRevealed) {
                console.log(`[SecretRoomManager] ✨ Adding star for closed secret door in area: ${currentArea}`);
                this.addMagicalStarCue(currentArea, secretWallData);
            } else {
                console.log(`[SecretRoomManager] ⚠️ Secret door is open, not adding star`);
            }

            this.startListening();
        }
        // Debug: Show why star might not be appearing
        else if (this.isPlayerNearSecret && wasNearSecret) {
            // Player is still near but star was already shown
            console.log(`[SecretRoomManager] 🔍 Player still near secret (${distance.toFixed(2)} units), door state: ${secretWallData.isRevealed ? 'open' : 'closed'}`);
        }
        // Player left proximity
        else if (!this.isPlayerNearSecret && wasNearSecret) {
            console.log('[SecretRoomManager] Player left secret wall proximity');

            // Remove magical star visual cue when player leaves
            this.removeMagicalStarCue(currentArea);

            this.stopListening();
        }
    }

    /**
     * Handle the "reveal secret" voice command
     */
    handleSecretRevealCommand() {
        if (!this.isPlayerNearSecret) {
            console.log('[SecretRoomManager] Voice command detected but player not near secret wall');
            return;
        }

        const currentArea = this.dungeonHandler.currentArea?.name || 'The Catacombs';
        console.log(`[SecretRoomManager] Looking for secret in area: "${currentArea}"`);
        console.log(`[SecretRoomManager] Available secret areas:`, Array.from(this.secretWalls.keys()));

        const secretWallData = this.secretWalls.get(currentArea);
        const secretRoomData = this.secretRooms.get(currentArea);

        if (!secretWallData || !secretRoomData) {
            console.log(`[SecretRoomManager] No secret data found for area "${currentArea}"`);
            return;
        }

        // DEBUG: Check current door trigger status
        this.debugDoorTriggerStatus();

        if (secretWallData.isRevealed) {
            console.log('[SecretRoomManager] Secret door is currently open - testing teleportation...');
            // Test if door trigger is working
            this.testDoorTriggerFunctionality(currentArea);
            return;
        } else {
            console.log('[SecretRoomManager] Secret door is currently closed - opening...');
        }

        // Check if player is actually near the secret wall
        if (!this.isPlayerNearSecret) {
            console.log('[SecretRoomManager] Player not near secret wall, cannot activate');
            return;
        }

        // Check cooldown
        const cooldownKey = `${currentArea}_${secretWallData.hostRoom.id}`;
        const now = Date.now();
        const lastActivation = this.cooldowns.get(cooldownKey) || 0;

        if (now - lastActivation < 5000) { // 5 second cooldown
            console.log('[SecretRoomManager] Secret wall on cooldown');
            return;
        }

        console.log('[SecretRoomManager] Revealing secret wall!');
        this.revealSecretWall(currentArea);
    }

    /**
     * Animate the secret wall revelation (like Diagon Alley)
     * @param {string} areaName - Area name
     */
    async revealSecretWall(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        if (!secretWallData || !secretRoomData || secretWallData.isAnimating) return;

        secretWallData.isAnimating = true;

        console.log('[SecretRoomManager] Starting Harry Potter-style brick wall animation...');

        // Play door opening sound when animation starts
        this.playSecretDoorSound('open');

        // Convert the normal stonebrick wall into individual animated voxels
        if (secretWallData.wallGroup) {
            try {
                await this.animateStonebrickWallToArchway(secretWallData);
                console.log('[SecretRoomManager] Stonebrick wall animation complete');
                this.completeSecretReveal(areaName);
            } catch (error) {
                console.error('[SecretRoomManager] Error during stonebrick wall animation:', error);
                secretWallData.isAnimating = false;
            }
        } else {
            console.warn('[SecretRoomManager] No wall group found, completing immediately');
            this.completeSecretReveal(areaName);
        }
    }

    /**
     * Close a revealed secret wall (reverse the opening animation)
     * @param {string} areaName - Area name
     */
    closeSecretWall(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        if (!secretWallData || !secretRoomData || !secretWallData.isRevealed) {
            console.log('[SecretRoomManager] Cannot close secret wall - not revealed or no data');
            return;
        }

        console.log('[SecretRoomManager] Closing secret wall...');

        // Mark as not revealed
        secretWallData.isRevealed = false;
        secretRoomData.isDiscovered = false;

        // Remove door trigger
        if (this.dungeonHandler && this.dungeonHandler.doorTriggers) {
            const triggerName = `secretDoorTrigger_${secretWallData.direction.toUpperCase()}`;
            const triggerIndex = this.dungeonHandler.doorTriggers.findIndex(trigger =>
                trigger.name === triggerName && trigger.userData?.areaName === areaName
            );
            if (triggerIndex > -1) {
                const trigger = this.dungeonHandler.doorTriggers[triggerIndex];
                this.dungeonHandler.scene.remove(trigger);
                this.dungeonHandler.doorTriggers.splice(triggerIndex, 1);
                console.log('[SecretRoomManager] Removed door trigger for closed secret wall');
            }
        }

        // Restore wall collision if needed
        if (secretWallData.wallGroup && this.dungeonHandler && this.dungeonHandler.collisionMeshes) {
            secretWallData.wallGroup.traverse((child) => {
                if (child.isMesh && child.userData.isSecretWallMesh) {
                    // Add back to collision meshes
                    if (!this.dungeonHandler.collisionMeshes.includes(child)) {
                        this.dungeonHandler.collisionMeshes.push(child);
                        console.log('[SecretRoomManager] Restored wall collision for closed door');
                    }
                }
            });
        }

        // Play door closing sound
        this.playSecretDoorSound('close');

        console.log('[SecretRoomManager] Secret wall closed');
    }

    /**
     * Complete the secret wall revelation
     * @param {string} areaName - Area name
     */
    completeSecretReveal(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        if (!secretWallData || !secretRoomData) return;

        // Mark as revealed
        secretWallData.isRevealed = true;
        secretWallData.isAnimating = false;
        secretRoomData.isDiscovered = true;

        // Update room connections
        const hostRoom = this.dungeonHandler.floorLayout.get(secretWallData.hostRoom.id);
        if (hostRoom) {
            hostRoom.connections = hostRoom.connections || {};
            hostRoom.connections[secretWallData.direction] = secretRoomData.id;
        }

        // Set cooldown
        const cooldownKey = `${areaName}_${secretWallData.hostRoom.id}`;
        this.cooldowns.set(cooldownKey, Date.now());

        console.log(`[SecretRoomManager] DEBUG: Setting isRevealed = true for area ${areaName}`);

        console.log('[SecretRoomManager] Secret wall revealed! Door created.');
        console.log(`[SecretRoomManager] Secret room ${secretRoomData.id} is now accessible`);

        // Remove collision from the secret wall so player can walk through
        if (secretWallData.wallGroup && this.dungeonHandler && this.dungeonHandler.collisionMeshes) {
            secretWallData.wallGroup.traverse((child) => {
                if (child.isMesh) {
                    // Remove from collision meshes
                    const collisionIndex = this.dungeonHandler.collisionMeshes.indexOf(child);
                    if (collisionIndex > -1) {
                        this.dungeonHandler.collisionMeshes.splice(collisionIndex, 1);
                        console.log('[SecretRoomManager] Removed wall collision for door opening');
                    }
                }
            });
        }

        // Create door trigger for room transition (but keep it inactive until animation completes)
        this.createSecretDoorTrigger(areaName);

        // FIXED: Don't create access zone yet - wait for animation to complete
        // Access zone will be created after animation finishes

        console.log(`[SecretRoomManager] Secret reveal completed for area: ${areaName}, door trigger created but not yet activated`);
        console.log(`[SecretRoomManager] Animation will activate the door trigger when complete`);


        // Play magical sound effect
        if (this.dungeonHandler.audioManager) {
            // TODO: Add magical reveal sound effect
            console.log('[SecretRoomManager] Playing magical reveal sound effect');
        }
    }

    /**
     * Clean up when changing areas
     */
    cleanup() {
        this.stopProximityMonitoring();
        this.activeAnimations.clear();

        // Remove keyboard event listener
        if (this.keyboardHandler) {
            document.removeEventListener('keydown', this.keyboardHandler);
            this.keyboardHandler = null;
        }

        // Remove touch event listeners
        if (this.touchStartHandler) {
            document.removeEventListener('touchstart', this.touchStartHandler);
            this.touchStartHandler = null;
        }
        if (this.touchEndHandler) {
            document.removeEventListener('touchend', this.touchEndHandler);
            this.touchEndHandler = null;
        }

        console.log('[SecretRoomManager] Cleaned up');
    }

    /**
     * Get secret room data for an area
     * @param {string} areaName - Area name
     * @returns {Object|null} Secret room data
     */
    getSecretRoom(areaName) {
        return this.secretRooms.get(areaName) || null;
    }

    /**
     * Get secret wall data for an area
     * @param {string} areaName - Area name
     * @returns {Object|null} Secret wall data
     */
    getSecretWall(areaName) {
        return this.secretWalls.get(areaName) || null;
    }

    /**
     * Check if a room is a secret room
     * @param {number} roomId - Room ID
     * @returns {boolean} True if room is secret
     */
    isSecretRoom(roomId) {
        for (const secretRoom of this.secretRooms.values()) {
            if (secretRoom.id === roomId) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get wall rotation from direction
     * @param {string} direction - Wall direction (n, e, s, w)
     * @returns {number} Rotation in radians
     */
    getWallRotationFromDirection(direction) {
        // FIXED: Use consistent rotation system with room generation
        switch (direction) {
            case 'n': return 0;                    // North wall faces south (0 radians)
            case 'e': return Math.PI / 2;          // East wall faces west (90 degrees)
            case 's': return Math.PI;              // South wall faces north (180 degrees)
            case 'w': return (3 * Math.PI) / 2;    // FIXED: West wall faces east (270 degrees, not -90)
            default:
                console.warn(`[SecretRoomManager] Unknown direction: ${direction}, defaulting to 0`);
                return 0;
        }
    }

    /**
     * Create door trigger for secret room transition
     * @param {string} areaName - Area name
     */
    createSecretDoorTrigger(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        if (!secretWallData || !secretRoomData) return;

        // Create door trigger at the wall position using same system as regular doors
        const triggerGeometry = new THREE.BoxGeometry(3.0, 3.5, 2.0); // Larger trigger for better detection
        const triggerMaterial = new THREE.MeshBasicMaterial({
            color: 0x00ff00,
            transparent: true,
            opacity: 0.0, // Invisible trigger
            visible: false
        });

        const doorTrigger = new THREE.Mesh(triggerGeometry, triggerMaterial);

        // Position trigger at the exact wall position
        if (secretWallData.wallPosition) {
            doorTrigger.position.copy(secretWallData.wallPosition);
            doorTrigger.position.y = 1.75; // Center trigger vertically (slightly higher)

            // Apply larger offset into the room for better detection
            const offsetFactor = 0.8; // Increased from 0.2 for better detection
            const offsetDir = new THREE.Vector3();
            const direction = secretWallData.direction;

            // FIXED: Correct offset directions for all wall orientations
            if (direction === 'n') offsetDir.set(0, 0, 1);  // North wall: Move South (into room)
            else if (direction === 's') offsetDir.set(0, 0, -1); // South wall: Move North (into room)
            else if (direction === 'w') offsetDir.set(1, 0, 0);  // West wall: Move East (into room)
            else if (direction === 'e') offsetDir.set(-1, 0, 0); // East wall: Move West (into room)

            doorTrigger.position.addScaledVector(offsetDir, offsetFactor);

            console.log(`[SecretRoomManager] DEBUG: Secret door trigger positioned at: (${doorTrigger.position.x.toFixed(2)}, ${doorTrigger.position.y.toFixed(2)}, ${doorTrigger.position.z.toFixed(2)})`);
            console.log(`[SecretRoomManager] DEBUG: Direction: ${direction}, Offset: ${offsetFactor}, OffsetDir: (${offsetDir.x}, ${offsetDir.y}, ${offsetDir.z})`);
            console.log(`[SecretRoomManager] DEBUG: Wall position: (${secretWallData.wallPosition.x.toFixed(2)}, ${secretWallData.wallPosition.y.toFixed(2)}, ${secretWallData.wallPosition.z.toFixed(2)})`);
        } else {
            console.error('[SecretRoomManager] ERROR: No wallPosition found for secret door trigger!');
        }

        doorTrigger.userData = {
            isDoorTrigger: true,
            direction: secretWallData.direction,
            targetRoomId: secretRoomData.id,
            isSecretDoor: true,
            // FIXED: Add reveal status check - only active after animation completes
            requiresReveal: true,
            isRevealed: false,
            animationComplete: false,
            areaName: areaName
        };

        doorTrigger.name = `secretDoorTrigger_${secretWallData.direction.toUpperCase()}`;

        // Add to scene and dungeon handler's door triggers
        this.dungeonHandler.scene.add(doorTrigger);
        this.dungeonHandler.doorTriggers.push(doorTrigger);

        console.log(`[SecretRoomManager] Created door trigger for secret room ${secretRoomData.id}`);
        console.log(`[SecretRoomManager] Door trigger position: (${doorTrigger.position.x.toFixed(2)}, ${doorTrigger.position.y.toFixed(2)}, ${doorTrigger.position.z.toFixed(2)})`);
    }







    /**
     * Create invisible collision-free access zone for direct access to secret room
     * @param {Object} secretWallData - Secret wall data
     */
    createInvisibleAccessZone(secretWallData) {
        if (!secretWallData.wallGroup) return;

        console.log('[SecretRoomManager] Creating invisible collision-free access zone...');

        // Create an invisible box that removes collision in the archway area
        const accessZoneGeometry = new THREE.BoxGeometry(2.5, 3.0, 1.5); // Width, Height, Depth
        const accessZoneMaterial = new THREE.MeshBasicMaterial({
            transparent: true,
            opacity: 0.0,
            visible: false // Completely invisible
        });

        const accessZone = new THREE.Mesh(accessZoneGeometry, accessZoneMaterial);
        accessZone.position.set(0, 1.5, 0); // Center in the archway opening
        accessZone.userData.isAccessZone = true;
        accessZone.userData.isSecretRoomAccess = true;
        accessZone.name = 'secret_room_access_zone';

        // Add to the wall group
        secretWallData.wallGroup.add(accessZone);

        console.log('[SecretRoomManager] ✅ Created invisible collision-free access zone');
    }

    /**
     * Activate secret door trigger after animation completes
     * @param {string} areaName - Area name
     */
    activateSecretDoorTrigger(areaName) {
        console.log(`[SecretRoomManager] Activating door trigger for area: ${areaName}`);

        // Find the door trigger for this area
        if (this.dungeonHandler && this.dungeonHandler.doorTriggers) {
            console.log(`[SecretRoomManager] DEBUG: Searching through ${this.dungeonHandler.doorTriggers.length} door triggers`);

            // Debug: List all door triggers
            this.dungeonHandler.doorTriggers.forEach((trigger, index) => {
                console.log(`[SecretRoomManager] DEBUG: Trigger ${index}: name=${trigger.name}, isSecretDoor=${trigger.userData.isSecretDoor}, areaName=${trigger.userData.areaName}`);
            });

            const doorTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
                trigger.userData.isSecretDoor && trigger.userData.areaName === areaName
            );

            if (doorTrigger) {
                console.log(`[SecretRoomManager] DEBUG: Found trigger, current flags: isRevealed=${doorTrigger.userData.isRevealed}, animationComplete=${doorTrigger.userData.animationComplete}`);

                // FIXED: Force both flags to true and ensure they stick
                doorTrigger.userData.isRevealed = true;
                doorTrigger.userData.animationComplete = true;
                doorTrigger.userData.requiresReveal = true; // Keep this true for the collision check

                // FIXED: Add a verification step to ensure flags are properly set
                setTimeout(() => {
                    if (doorTrigger.userData.isRevealed && doorTrigger.userData.animationComplete) {
                        console.log(`[SecretRoomManager] ✅ Door trigger VERIFIED activated for area: ${areaName}`);
                        console.log(`[SecretRoomManager] DEBUG: Final flags: isRevealed=${doorTrigger.userData.isRevealed}, animationComplete=${doorTrigger.userData.animationComplete}`);
                    } else {
                        console.error(`[SecretRoomManager] ❌ Door trigger flags failed to stick! Retrying...`);
                        doorTrigger.userData.isRevealed = true;
                        doorTrigger.userData.animationComplete = true;
                    }
                }, 50);

                console.log(`[SecretRoomManager] ✅ Door trigger activated for area: ${areaName}`);
                console.log(`[SecretRoomManager] DEBUG: Updated flags: isRevealed=${doorTrigger.userData.isRevealed}, animationComplete=${doorTrigger.userData.animationComplete}`);
            } else {
                console.warn(`[SecretRoomManager] Could not find door trigger for area: ${areaName}`);
                console.warn(`[SecretRoomManager] DEBUG: Looking for isSecretDoor=true AND areaName="${areaName}"`);

                // FIXED: Try to find the trigger by other means if the first search fails
                console.log(`[SecretRoomManager] DEBUG: Attempting alternative search...`);
                const alternativeTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
                    trigger.userData.isSecretDoor === true
                );

                if (alternativeTrigger) {
                    console.log(`[SecretRoomManager] DEBUG: Found alternative trigger, updating areaName and activating...`);
                    alternativeTrigger.userData.areaName = areaName;
                    alternativeTrigger.userData.isRevealed = true;
                    alternativeTrigger.userData.animationComplete = true;
                    console.log(`[SecretRoomManager] ✅ Alternative door trigger activated for area: ${areaName}`);
                } else {
                    console.error(`[SecretRoomManager] ❌ No secret door triggers found at all!`);
                }
            }
        } else {
            console.warn('[SecretRoomManager] Could not access door triggers system');
        }
    }

    /**
     * Verify that door trigger activation was successful
     * @param {string} areaName - Area name
     */
    verifyDoorTriggerActivation(areaName) {
        console.log(`[SecretRoomManager] Verifying door trigger activation for area: ${areaName}`);

        if (this.dungeonHandler && this.dungeonHandler.doorTriggers) {
            const doorTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
                trigger.userData.isSecretDoor && trigger.userData.areaName === areaName
            );

            if (doorTrigger) {
                const isRevealed = doorTrigger.userData.isRevealed;
                const animationComplete = doorTrigger.userData.animationComplete;

                console.log(`[SecretRoomManager] VERIFICATION: isRevealed=${isRevealed}, animationComplete=${animationComplete}`);

                if (isRevealed && animationComplete) {
                    console.log(`[SecretRoomManager] ✅ Door trigger verification PASSED - ready for teleportation`);
                } else {
                    console.warn(`[SecretRoomManager] ❌ Door trigger verification FAILED - forcing activation`);
                    doorTrigger.userData.isRevealed = true;
                    doorTrigger.userData.animationComplete = true;
                    console.log(`[SecretRoomManager] 🔧 Forced door trigger activation complete`);
                }
            } else {
                console.error(`[SecretRoomManager] ❌ Door trigger not found during verification for area: ${areaName}`);
            }
        } else {
            console.error('[SecretRoomManager] ❌ Cannot verify - door triggers system not accessible');
        }
    }

    /**
     * Handle room transition - called when player moves to a new room
     * @param {number} newRoomId - The room ID the player is entering
     * @param {number} previousRoomId - The room ID the player is leaving
     */
    onRoomTransition(newRoomId, previousRoomId) {
        console.log(`[SecretRoomManager] 🔄 Room transition: ${previousRoomId} → ${newRoomId}`);
        console.log(`[SecretRoomManager] 🔄 Current state: playerInSecretRoom=${this.playerInSecretRoom}`);

        // CRITICAL FIX: Reset proximity state on room transition
        console.log(`[SecretRoomManager] 🔄 Resetting proximity state for room transition`);
        this.isPlayerNearSecret = false;

        // Debug: Check if rooms are secret rooms
        const isPreviousSecret = previousRoomId ? this.isSecretRoom(previousRoomId) : false;
        const isNewSecret = this.isSecretRoom(newRoomId);
        console.log(`[SecretRoomManager] 🔄 Room types: previous=${isPreviousSecret ? 'SECRET' : 'NORMAL'}, new=${isNewSecret ? 'SECRET' : 'NORMAL'}`);

        // Check if player was in a secret room and is now leaving
        // Note: playerInSecretRoom is updated BEFORE this check, so we need to check if previousRoom was secret
        if (previousRoomId && this.isSecretRoom(previousRoomId) && !this.isSecretRoom(newRoomId)) {
            console.log(`[SecretRoomManager] 🚪 Player leaving secret room ${previousRoomId} to normal room ${newRoomId}`);
            this.handleSecretRoomExit(previousRoomId, newRoomId);
        }

        // Check if player is entering a secret room
        if (this.isSecretRoom(newRoomId)) {
            console.log(`[SecretRoomManager] 🚪 Player entering secret room ${newRoomId}`);
            this.playerInSecretRoom = true;
        } else {
            console.log(`[SecretRoomManager] 🚪 Player entering normal room ${newRoomId}`);
            this.playerInSecretRoom = false;

            // FIXED: Force minimap update when leaving secret room
            if (isPreviousSecret) {
                console.log(`[SecretRoomManager] 🗺️ Forcing minimap update after leaving secret room`);
                setTimeout(() => {
                    if (this.dungeonHandler && typeof this.dungeonHandler._updateMinimap === 'function') {
                        this.dungeonHandler._updateMinimap();
                    }
                }, 100); // Small delay to ensure room is fully loaded
            }
        }

        // CRITICAL FIX: Ensure proximity monitoring is running after room transition
        if (!this.proximityCheckInterval) {
            console.log(`[SecretRoomManager] 🔄 Proximity monitoring not running, restarting...`);
            this.startProximityMonitoring();
        }

        // CRITICAL FIX: Check if the new room has a secret wall and ensure it's properly set up
        const currentArea = this.dungeonHandler.currentArea?.name || 'The Catacombs';
        const secretWallData = this.secretWalls.get(currentArea);

        if (secretWallData && secretWallData.hostRoom.id === newRoomId) {
            console.log(`[SecretRoomManager] 🔄 Entered room ${newRoomId} with secret wall, ensuring wall is created...`);

            // Small delay to ensure room is fully loaded before checking/creating secret wall
            setTimeout(() => {
                this.checkAndCreateSecretWallForRoom(newRoomId);
                console.log(`[SecretRoomManager] 🔄 Secret wall check completed for room ${newRoomId}`);
            }, 100);
        }

        this.lastRoomId = previousRoomId;
        console.log(`[SecretRoomManager] 🔄 Updated state: playerInSecretRoom=${this.playerInSecretRoom}, lastRoomId=${this.lastRoomId}, proximityReset=true`);
    }

    /**
     * Handle when player exits a secret room
     * @param {number} secretRoomId - The secret room being exited
     * @param {number} hostRoomId - The host room being entered
     */
    handleSecretRoomExit(secretRoomId, hostRoomId) {
        console.log(`[SecretRoomManager] 🔍 Handling secret room exit: ${secretRoomId} → ${hostRoomId}`);

        // Debug: List all secret rooms
        console.log(`[SecretRoomManager] 🔍 Available secret rooms:`, Array.from(this.secretRooms.entries()).map(([area, room]) => `${area}: ${room.id}`));

        // Find which area this secret room belongs to
        let targetAreaName = null;
        for (const [areaName, secretRoomData] of this.secretRooms.entries()) {
            console.log(`[SecretRoomManager] 🔍 Checking area ${areaName}: room ID ${secretRoomData.id} vs ${secretRoomId}`);
            if (secretRoomData.id === secretRoomId) {
                targetAreaName = areaName;
                console.log(`[SecretRoomManager] 🔍 Found matching area: ${targetAreaName}`);
                break;
            }
        }

        if (!targetAreaName) {
            console.warn(`[SecretRoomManager] ❌ Could not find area for secret room ${secretRoomId}`);
            console.warn(`[SecretRoomManager] ❌ Available secret room IDs:`, Array.from(this.secretRooms.values()).map(room => room.id));
            return;
        }

        const secretWallData = this.secretWalls.get(targetAreaName);
        if (!secretWallData) {
            console.warn(`[SecretRoomManager] ❌ Could not find secret wall data for area ${targetAreaName}`);
            return;
        }

        console.log(`[SecretRoomManager] 🔍 Secret wall host room: ${secretWallData.hostRoom.id}, target host room: ${hostRoomId}`);

        // Check if the host room matches
        if (secretWallData.hostRoom.id !== hostRoomId) {
            console.log(`[SecretRoomManager] ⚠️ Player exited to different room (${hostRoomId} vs ${secretWallData.hostRoom.id}), not closing secret door`);
            return;
        }

        console.log(`[SecretRoomManager] ✅ Player returned to host room ${hostRoomId}, closing secret door for area ${targetAreaName}`);

        // CRITICAL: Position player in front of secret wall before closing
        this.positionPlayerInFrontOfSecretWall(targetAreaName);

        // Close the secret door with reverse animation
        this.closeSecretDoor(targetAreaName);
    }

    /**
     * Close secret door with reverse animation
     * @param {string} areaName - Area name
     */
    async closeSecretDoor(areaName) {
        console.log(`[SecretRoomManager] 🚪 closeSecretDoor called for area: ${areaName}`);

        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        console.log(`[SecretRoomManager] 🚪 Secret wall data exists: ${!!secretWallData}`);
        console.log(`[SecretRoomManager] 🚪 Secret room data exists: ${!!secretRoomData}`);

        if (!secretWallData || !secretRoomData) {
            console.warn(`[SecretRoomManager] ❌ Cannot close secret door - missing data for area ${areaName}`);
            return;
        }

        console.log(`[SecretRoomManager] 🚪 Secret wall state: isRevealed=${secretWallData.isRevealed}, isAnimating=${secretWallData.isAnimating}`);

        if (!secretWallData.isRevealed || secretWallData.isAnimating) {
            console.log(`[SecretRoomManager] ⚠️ Secret door not revealed or already animating for area ${areaName}`);
            return;
        }

        console.log(`[SecretRoomManager] 🎬 Starting reverse animation to close secret door for area ${areaName}`);

        // Play door closing sound when reverse animation starts
        this.playSecretDoorSound('close');

        secretWallData.isAnimating = true;

        try {
            await this.animateArchwayToWall(secretWallData);
            console.log('[SecretRoomManager] ✅ Reverse animation complete - secret door closed');
            this.completeSecretClose(areaName);
        } catch (error) {
            console.error('[SecretRoomManager] ❌ Error during reverse animation:', error);
            secretWallData.isAnimating = false;
        }
    }

    /**
     * Complete the secret door closing
     * @param {string} areaName - Area name
     */
    completeSecretClose(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        const secretRoomData = this.secretRooms.get(areaName);

        if (!secretWallData || !secretRoomData) return;

        // Mark as closed but keep discovered status (so content persists)
        secretWallData.isRevealed = false;
        secretWallData.isAnimating = false;
        // Keep secretRoomData.isDiscovered = true (content persists)

        // CRITICAL: Restore original wall appearance by replacing animated voxels with merged geometry
        this.restoreOriginalWallAppearance(secretWallData);

        // Remove room connection from host room
        const hostRoom = this.dungeonHandler.floorLayout.get(secretWallData.hostRoom.id);
        if (hostRoom && hostRoom.connections) {
            delete hostRoom.connections[secretWallData.direction];
        }

        // Deactivate door trigger
        this.deactivateSecretDoorTrigger(areaName);

        // Restore collision to the secret wall
        this.restoreSecretWallCollision(secretWallData);

        console.log(`[SecretRoomManager] Secret door closed for area ${areaName}`);
        console.log(`[SecretRoomManager] Secret room content preserved - can be reopened with 'R' key`);
    }

    /**
     * Position player in front of secret wall when exiting secret room
     * @param {string} areaName - Area name
     */
    positionPlayerInFrontOfSecretWall(areaName) {
        const secretWallData = this.secretWalls.get(areaName);
        if (!secretWallData || !this.dungeonHandler.player) {
            console.warn(`[SecretRoomManager] Cannot position player - missing data for area ${areaName}`);
            return;
        }

        console.log(`[SecretRoomManager] 📍 Positioning player in front of secret wall for area ${areaName}`);

        // Get the wall position
        let wallPosition;
        if (secretWallData.wallPosition) {
            wallPosition = secretWallData.wallPosition.clone();
        } else {
            // Fallback: calculate wall position based on direction
            const R = ROOM_WORLD_SIZE / 2;
            wallPosition = new THREE.Vector3(0, WALL_HEIGHT / 2, 0);

            switch (secretWallData.direction) {
                case 'n': wallPosition.z = -R; break;
                case 's': wallPosition.z = R; break;
                case 'e': wallPosition.x = R; break;
                case 'w': wallPosition.x = -R; break;
            }
        }

        // Calculate position in front of the wall (same logic as magical star positioning)
        const playerPosition = wallPosition.clone();
        const offsetDistance = 1.5; // Distance in front of wall
        const direction = secretWallData.direction;

        // Offset INTO the room from the wall
        if (direction === 'n') playerPosition.z += offsetDistance;      // North wall - move south (into room)
        else if (direction === 's') playerPosition.z -= offsetDistance; // South wall - move north (into room)
        else if (direction === 'e') playerPosition.x -= offsetDistance; // East wall - move west (into room)
        else if (direction === 'w') playerPosition.x += offsetDistance; // West wall - move east (into room)

        // Set player Y to floor level
        playerPosition.y = Math.max(0.5, this.dungeonHandler.player.position.y);

        // Position the player
        this.dungeonHandler.player.position.copy(playerPosition);

        // Make player look at the secret wall
        const lookAtPosition = wallPosition.clone();
        lookAtPosition.y = playerPosition.y; // Same Y level as player
        this.dungeonHandler.player.lookAt(lookAtPosition);

        // Update player controller bounds
        if (this.dungeonHandler.playerController) {
            this.dungeonHandler.playerController._updatePlayerBounds();
        }

        console.log(`[SecretRoomManager] ✅ Player positioned at (${playerPosition.x.toFixed(2)}, ${playerPosition.y.toFixed(2)}, ${playerPosition.z.toFixed(2)}) facing ${direction} wall`);
    }

    /**
     * Restore original wall appearance by replacing animated voxels with merged geometry
     * @param {Object} secretWallData - Secret wall data
     */
    restoreOriginalWallAppearance(secretWallData) {
        const wallGroup = secretWallData.wallGroup;
        if (!wallGroup) {
            console.warn('[SecretRoomManager] No wall group found for restoration');
            return;
        }

        console.log('[SecretRoomManager] 🔧 Restoring original wall appearance...');

        // Remove all animated voxel meshes and clean up collision
        const voxelMeshes = [];
        wallGroup.traverse((child) => {
            if (child.isMesh && child.userData.isAnimatableVoxel) {
                voxelMeshes.push(child);
            }
        });

        console.log(`[SecretRoomManager] 🔧 Removing ${voxelMeshes.length} animated voxel meshes`);

        // Remove voxels from collision system
        const dungeonHandler = window.dungeonHandler || this.dungeonHandler;
        voxelMeshes.forEach(mesh => {
            // Remove from collision meshes
            if (dungeonHandler && dungeonHandler.collisionMeshes) {
                const index = dungeonHandler.collisionMeshes.indexOf(mesh);
                if (index > -1) {
                    dungeonHandler.collisionMeshes.splice(index, 1);
                }
            }

            // Remove from wall group
            wallGroup.remove(mesh);

            // Dispose of geometry and material to free memory
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach(mat => {
                        if (mat.map) mat.map.dispose();
                        mat.dispose();
                    });
                } else {
                    if (mesh.material.map) mesh.material.map.dispose();
                    mesh.material.dispose();
                }
            }
        });

        // Recreate the original merged geometry wall
        const { width, height, depth } = wallGroup.userData.originalDimensions;
        const roomData = wallGroup.userData.roomData;

        console.log(`[SecretRoomManager] 🔧 Recreating original merged wall: ${width.toFixed(2)}w x ${height.toFixed(2)}h x ${depth.toFixed(2)}d`);

        // Use the EXACT same function that creates regular walls
        const wallResult = createStonebrickWallSegment(width, height, depth, roomData);
        const newWallGroup = wallResult.group;

        // Copy all meshes from the new wall group to the existing wall group
        const newMeshes = [];
        newWallGroup.traverse((child) => {
            if (child.isMesh) {
                newMeshes.push(child);
            }
        });

        // Add new meshes to the existing wall group
        newMeshes.forEach(mesh => {
            // Remove from new group first
            newWallGroup.remove(mesh);

            // Mark as secret wall mesh
            mesh.userData.isSecretWallMesh = true;
            mesh.userData.canBeAnimated = true;

            // Add collision to the restored wall meshes
            if (dungeonHandler && dungeonHandler.collisionMeshes) {
                dungeonHandler.collisionMeshes.push(mesh);
            }

            // Add to existing wall group
            wallGroup.add(mesh);
        });

        // Dispose of the temporary new wall group
        if (newWallGroup.geometry) newWallGroup.geometry.dispose();
        if (newWallGroup.material) {
            if (Array.isArray(newWallGroup.material)) {
                newWallGroup.material.forEach(mat => mat.dispose());
            } else {
                newWallGroup.material.dispose();
            }
        }

        console.log(`[SecretRoomManager] ✅ Original wall appearance restored - looks exactly like before secret was revealed`);
        console.log(`[SecretRoomManager] 🔧 Wall now contains ${wallGroup.children.length} merged mesh(es) for optimal performance`);
    }

    /**
     * Debug method to check door trigger status
     */
    debugDoorTriggerStatus() {
        const currentArea = this.dungeonHandler.currentArea?.name || 'The Catacombs';
        console.log(`[SecretRoomManager] 🔍 DEBUG: Door trigger status for area: ${currentArea}`);

        if (this.dungeonHandler && this.dungeonHandler.doorTriggers) {
            console.log(`[SecretRoomManager] 🔍 DEBUG: Total door triggers: ${this.dungeonHandler.doorTriggers.length}`);

            // List all door triggers
            this.dungeonHandler.doorTriggers.forEach((trigger, index) => {
                const userData = trigger.userData;
                console.log(`[SecretRoomManager] 🔍 DEBUG: Trigger ${index}:`);
                console.log(`  - Name: ${trigger.name}`);
                console.log(`  - Position: (${trigger.position.x.toFixed(2)}, ${trigger.position.y.toFixed(2)}, ${trigger.position.z.toFixed(2)})`);
                console.log(`  - isSecretDoor: ${userData.isSecretDoor}`);
                console.log(`  - areaName: ${userData.areaName}`);
                console.log(`  - requiresReveal: ${userData.requiresReveal}`);
                console.log(`  - isRevealed: ${userData.isRevealed}`);
                console.log(`  - animationComplete: ${userData.animationComplete}`);
                console.log(`  - targetRoomId: ${userData.targetRoomId}`);
            });

            // Find secret door trigger for current area
            const secretTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
                trigger.userData.isSecretDoor && trigger.userData.areaName === currentArea
            );

            if (secretTrigger) {
                console.log(`[SecretRoomManager] 🔍 DEBUG: Found secret trigger for ${currentArea}:`);
                console.log(`  - Ready for teleportation: ${secretTrigger.userData.isRevealed && secretTrigger.userData.animationComplete}`);
                console.log(`  - Player position: (${this.dungeonHandler.player.position.x.toFixed(2)}, ${this.dungeonHandler.player.position.y.toFixed(2)}, ${this.dungeonHandler.player.position.z.toFixed(2)})`);
                console.log(`  - Distance to trigger: ${this.dungeonHandler.player.position.distanceTo(secretTrigger.position).toFixed(2)}`);
            } else {
                console.log(`[SecretRoomManager] 🔍 DEBUG: No secret trigger found for area: ${currentArea}`);
            }
        } else {
            console.log(`[SecretRoomManager] 🔍 DEBUG: No door triggers system available`);
        }

        // Check secret wall status
        const secretWallData = this.secretWalls.get(currentArea);
        if (secretWallData) {
            console.log(`[SecretRoomManager] 🔍 DEBUG: Secret wall status for ${currentArea}:`);
            console.log(`  - isRevealed: ${secretWallData.isRevealed}`);
            console.log(`  - isAnimating: ${secretWallData.isAnimating}`);
            console.log(`  - direction: ${secretWallData.direction}`);
            console.log(`  - hostRoom: ${secretWallData.hostRoom.id}`);
        } else {
            console.log(`[SecretRoomManager] 🔍 DEBUG: No secret wall data for area: ${currentArea}`);
        }
    }

    /**
     * Test door trigger functionality for secret room teleportation
     * @param {string} areaName - Area name
     */
    testDoorTriggerFunctionality(areaName) {
        console.log(`[SecretRoomManager] 🧪 Testing door trigger functionality for area: ${areaName}`);

        if (!this.dungeonHandler || !this.dungeonHandler.doorTriggers) {
            console.error('[SecretRoomManager] ❌ Cannot test - door triggers system not available');
            return;
        }

        // Find the secret door trigger for this area
        const secretTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
            trigger.userData.isSecretDoor && trigger.userData.areaName === areaName
        );

        if (!secretTrigger) {
            console.error(`[SecretRoomManager] ❌ No secret door trigger found for area: ${areaName}`);
            console.log('[SecretRoomManager] 🔧 Attempting to create door trigger...');
            this.createSecretDoorTrigger(areaName);
            return;
        }

        console.log(`[SecretRoomManager] ✅ Found secret door trigger for ${areaName}`);
        console.log(`[SecretRoomManager] 🔍 Trigger status:`);
        console.log(`  - Position: (${secretTrigger.position.x.toFixed(2)}, ${secretTrigger.position.y.toFixed(2)}, ${secretTrigger.position.z.toFixed(2)})`);
        console.log(`  - Is revealed: ${secretTrigger.userData.isRevealed}`);
        console.log(`  - Animation complete: ${secretTrigger.userData.animationComplete}`);
        console.log(`  - Target room: ${secretTrigger.userData.targetRoomId}`);

        // Check if trigger is ready for teleportation
        const isReady = secretTrigger.userData.isRevealed && secretTrigger.userData.animationComplete;
        console.log(`[SecretRoomManager] 🎯 Trigger ready for teleportation: ${isReady}`);

        if (!isReady) {
            console.log('[SecretRoomManager] 🔧 Trigger not ready - forcing activation...');
            this.activateSecretDoorTrigger(areaName);

            // Verify activation
            setTimeout(() => {
                this.verifyDoorTriggerActivation(areaName);
            }, 100);
        } else {
            console.log('[SecretRoomManager] ✅ Trigger is ready for teleportation');

            // Test player proximity to trigger
            if (this.dungeonHandler.player) {
                const playerPos = this.dungeonHandler.player.position;
                const distance = playerPos.distanceTo(secretTrigger.position);
                console.log(`[SecretRoomManager] 📏 Player distance to trigger: ${distance.toFixed(2)} units`);

                if (distance < 3.0) {
                    console.log('[SecretRoomManager] ✅ Player is close enough to trigger teleportation');
                    console.log('[SecretRoomManager] 💡 Try walking through the secret door to teleport');
                } else {
                    console.log('[SecretRoomManager] ⚠️ Player needs to move closer to the secret door');
                    console.log(`[SecretRoomManager] 💡 Move to position: (${secretTrigger.position.x.toFixed(2)}, ${secretTrigger.position.y.toFixed(2)}, ${secretTrigger.position.z.toFixed(2)})`);
                }
            }
        }
    }

    /**
     * Add magical blinking star visual cue for closed secret doors
     * @param {string} areaName - Area name
     * @param {Object} secretWallData - Secret wall data
     */
    addMagicalStarCue(areaName, secretWallData) {
        console.log(`[SecretRoomManager] 🌟 addMagicalStarCue called for area: ${areaName}`);
        console.log(`[SecretRoomManager] 🌟 Star already exists: ${this.magicalStars.has(areaName)}`);
        console.log(`[SecretRoomManager] 🌟 Door is revealed: ${secretWallData.isRevealed}`);

        // Remove existing star first if it exists
        if (this.magicalStars.has(areaName)) {
            console.log(`[SecretRoomManager] 🔄 Removing existing star to create new one`);
            this.removeMagicalStarCue(areaName);
        }

        if (secretWallData.isRevealed) {
            console.log(`[SecretRoomManager] ⚠️ Door is revealed for area: ${areaName}, skipping star`);
            return;
        }

        console.log(`[SecretRoomManager] ✨ Adding magical star cue for area: ${areaName}`);

        const wallGroup = secretWallData.wallGroup;
        if (!wallGroup) {
            console.warn('[SecretRoomManager] No wall group found for star cue');
            return;
        }

        // Create a proper star shape
        const starGeometry = this.createStarGeometry(0.3); // Small star, 0.3 units

        // Create bright golden material for the star
        const starMaterial = new THREE.MeshBasicMaterial({
            color: 0xffd700, // Golden color
            transparent: true,
            opacity: 1.0,
            side: THREE.DoubleSide // Visible from both sides
        });

        // Create star mesh
        const starMesh = new THREE.Mesh(starGeometry, starMaterial);

        // Position star at the exact door opening location
        const wallBounds = new THREE.Box3().setFromObject(wallGroup);
        const wallCenter = wallBounds.getCenter(new THREE.Vector3());

        // Calculate door position based on wall direction
        const doorPosition = wallCenter.clone();
        const direction = secretWallData.direction;

        // FIXED: Offset in front of the wall with correct directions and larger distance
        const offsetDistance = 1.2; // Larger offset to ensure visibility in front of wall
        if (direction === 'n') doorPosition.z += offsetDistance;      // North wall - move south (into room)
        else if (direction === 's') doorPosition.z -= offsetDistance; // South wall - move north (into room)
        else if (direction === 'e') doorPosition.x -= offsetDistance; // East wall - move west (into room)
        else if (direction === 'w') doorPosition.x += offsetDistance; // West wall - move east (into room)

        // Position at door height (center of where door opening will be)
        doorPosition.y = wallCenter.y; // Keep at wall center height

        starMesh.position.copy(doorPosition);

        // Set up animation data
        starMesh.userData.rotationSpeed = 2.0; // Faster rotation for star
        starMesh.userData.blinkPhase = 0; // Start at full opacity
        starMesh.userData.isSecretStar = true;
        starMesh.userData.startTime = Date.now(); // Track when star was created
        starMesh.userData.blinkDuration = 1000; // Blink for 1 second

        // Add to scene (not player, so it stays at the wall location)
        this.dungeonHandler.scene.add(starMesh);

        // Store reference
        this.magicalStars.set(areaName, starMesh);

        // Play magical discovery sound
        this.playMagicalDiscoverySound();

        console.log(`[SecretRoomManager] ✨ Magical star added at door position: (${starMesh.position.x.toFixed(2)}, ${starMesh.position.y.toFixed(2)}, ${starMesh.position.z.toFixed(2)})`);
        console.log(`[SecretRoomManager] ✨ Wall center was: (${wallCenter.x.toFixed(2)}, ${wallCenter.y.toFixed(2)}, ${wallCenter.z.toFixed(2)})`);
        console.log(`[SecretRoomManager] ✨ Direction: ${secretWallData.direction}`);
    }

    /**
     * Remove magical star visual cue
     * @param {string} areaName - Area name
     */
    removeMagicalStarCue(areaName) {
        const starMesh = this.magicalStars.get(areaName);
        if (!starMesh) {
            return;
        }

        console.log(`[SecretRoomManager] ✨ Removing magical star cue for area: ${areaName}`);

        // FIXED: Remove from correct parent (player or scene)
        if (starMesh.parent) {
            starMesh.parent.remove(starMesh);
        } else {
            // Fallback: try removing from scene
            this.dungeonHandler.scene.remove(starMesh);
        }

        // Dispose of geometry and material
        if (starMesh.geometry) starMesh.geometry.dispose();
        if (starMesh.material) starMesh.material.dispose();

        // Remove from map
        this.magicalStars.delete(areaName);
    }

    /**
     * Play magical discovery sound when star appears
     */
    playMagicalDiscoverySound() {
        try {
            // Play the secretappear.mp3 sound file
            const audio = new Audio('assets/sounds/secretappear.mp3');
            audio.volume = 0.6; // Set volume to 60%
            audio.play().then(() => {
                console.log('[SecretRoomManager] 🔊 Playing magical discovery sound: secretappear.mp3');
            }).catch(error => {
                console.warn('[SecretRoomManager] Could not play secretappear.mp3:', error);
            });
        } catch (error) {
            console.warn('[SecretRoomManager] Error creating audio for secretappear.mp3:', error);
        }
    }

    /**
     * Play secret door sound effects
     * @param {string} action - 'open' or 'close'
     */
    playSecretDoorSound(action) {
        try {
            let soundFile;
            if (action === 'open') {
                soundFile = 'assets/sounds/secretdooropen.mp3';
            } else if (action === 'close') {
                soundFile = 'assets/sounds/secretdoorclose.mp3';
            } else {
                console.warn(`[SecretRoomManager] Unknown door sound action: ${action}`);
                return;
            }

            const audio = new Audio(soundFile);
            audio.volume = 0.7; // Set volume to 70%
            audio.play().then(() => {
                console.log(`[SecretRoomManager] 🔊 Playing secret door ${action} sound: ${soundFile}`);
            }).catch(error => {
                console.warn(`[SecretRoomManager] Could not play ${soundFile}:`, error);
            });
        } catch (error) {
            console.warn(`[SecretRoomManager] Error creating audio for secret door ${action} sound:`, error);
        }
    }

    /**
     * Create a star-shaped geometry
     * @param {number} size - Size of the star
     * @returns {THREE.BufferGeometry} Star geometry
     */
    createStarGeometry(size) {
        const shape = new THREE.Shape();
        const outerRadius = size;
        const innerRadius = size * 0.4;
        const spikes = 5;

        for (let i = 0; i < spikes * 2; i++) {
            const angle = (i / (spikes * 2)) * Math.PI * 2;
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;

            if (i === 0) {
                shape.moveTo(x, y);
            } else {
                shape.lineTo(x, y);
            }
        }
        shape.closePath();

        const geometry = new THREE.ShapeGeometry(shape);
        return geometry;
    }

    /**
     * Update magical star animations (called from update loop)
     */
    updateMagicalStars() {
        const currentTime = Date.now();
        const starsToRemove = [];

        for (const [areaName, starMesh] of this.magicalStars.entries()) {
            if (!starMesh || !starMesh.userData) continue;

            // Check if star should be removed after 1 second
            const elapsedTime = currentTime - starMesh.userData.startTime;
            if (elapsedTime >= starMesh.userData.blinkDuration) {
                starsToRemove.push(areaName);
                continue;
            }

            // Rotate the star
            starMesh.rotation.z += starMesh.userData.rotationSpeed * 0.016; // ~60fps

            // Blink effect - fast blinking for 1 second
            const blinkSpeed = 8.0; // Fast blinking
            const normalizedTime = elapsedTime / starMesh.userData.blinkDuration; // 0 to 1
            const blinkIntensity = Math.sin(elapsedTime * 0.01 * blinkSpeed) * 0.5 + 0.5; // 0 to 1

            // Fade out towards the end
            const fadeOut = Math.max(0, 1 - normalizedTime * 2); // Start fading at 50% through
            const opacity = blinkIntensity * fadeOut;

            if (starMesh.material) {
                starMesh.material.opacity = Math.max(0.2, opacity); // Minimum 20% opacity
            }

            // Subtle floating motion
            const floatOffset = Math.sin(elapsedTime * 0.005) * 0.02; // Gentle floating
            if (starMesh.userData.originalY === undefined) {
                starMesh.userData.originalY = starMesh.position.y;
            }
            starMesh.position.y = starMesh.userData.originalY + floatOffset;
        }

        // Remove expired stars
        starsToRemove.forEach(areaName => {
            console.log(`[SecretRoomManager] ✨ Removing expired star for area: ${areaName}`);
            this.removeMagicalStarCue(areaName);
        });
    }

    /**
     * Check if a room ID is a secret room
     * @param {number} roomId - Room ID to check
     * @returns {boolean} True if the room is a secret room
     */
    isSecretRoom(roomId) {
        // Check if this room ID exists in any of our secret rooms
        for (const [areaName, secretRoomData] of this.secretRooms.entries()) {
            if (secretRoomData.id === roomId) {
                console.log(`[SecretRoomManager] 🔍 Room ${roomId} is a secret room for area: ${areaName}`);
                return true;
            }
        }
        return false;
    }

    /**
     * Update player secret room status based on current room
     * @param {number} currentRoomId - Current room ID
     */
    updatePlayerSecretRoomStatus(currentRoomId) {
        const wasInSecretRoom = this.playerInSecretRoom;
        this.playerInSecretRoom = this.isSecretRoom(currentRoomId);

        if (wasInSecretRoom !== this.playerInSecretRoom) {
            console.log(`[SecretRoomManager] 🔄 Player secret room status changed: ${wasInSecretRoom} → ${this.playerInSecretRoom} (room ${currentRoomId})`);
        }
    }

    /**
     * Check if a room should have a secret wall and create it if needed
     * @param {number} roomId - Room ID that was just loaded
     */
    checkAndCreateSecretWallForRoom(roomId) {
        // Check all areas for secret walls that belong to this room
        for (const [areaName, secretWallData] of this.secretWalls.entries()) {
            if (secretWallData.hostRoom.id === roomId) {
                if (!secretWallData.wallGroup) {
                    console.log(`[SecretRoomManager] Creating secret wall for room ${roomId} (area: ${areaName})`);
                    const success = this.createSecretWallInScene(areaName, secretWallData);
                    if (success) {
                        console.log(`[SecretRoomManager] ✅ Secret wall successfully created in room ${roomId}`);
                        console.log(`[SecretRoomManager] 🔍 Look for a ${secretWallData.direction} wall (${this.getDirectionName(secretWallData.direction)}) to find the secret entrance`);

                        // FIXED: If secret is already revealed, activate the door trigger immediately
                        if (secretWallData.isRevealed) {
                            console.log(`[SecretRoomManager] Secret already revealed, activating door trigger for ${areaName}`);
                            this.activateSecretDoorTrigger(areaName);
                        }
                    } else {
                        console.warn(`[SecretRoomManager] ❌ Failed to create secret wall in room ${roomId}`);
                    }
                } else {
                    console.log(`[SecretRoomManager] Secret wall already exists in room ${roomId}`);
                }
            }
        }
    }

    /**
     * Create a secret wall in the scene after room generation
     * @param {string} areaName - Area name
     * @param {Object} secretWallData - Secret wall data
     * @returns {boolean} Success status
     */
    createSecretWallInScene(areaName, secretWallData) {
        console.log(`[SecretRoomManager] Creating secret wall in scene for area ${areaName}`);

        // Get the current room group
        const roomGroup = this.dungeonHandler.currentRoomGroup;
        if (!roomGroup) {
            console.error('[SecretRoomManager] No current room group found');
            return false;
        }

        // Get collision meshes
        const collisionMeshes = this.dungeonHandler.collisionMeshes || [];

        // Calculate wall position and dimensions
        const direction = secretWallData.direction;
        const R = ROOM_WORLD_SIZE / 2;
        const wallPosition = new THREE.Vector3(0, WALL_HEIGHT / 2, 0);
        let wallRotation = 0;

        // Position wall at room boundary based on direction
        switch (direction) {
            case 'n':
                wallPosition.set(0, WALL_HEIGHT / 2, -R);
                wallRotation = 0;
                break;
            case 's':
                wallPosition.set(0, WALL_HEIGHT / 2, R);
                wallRotation = Math.PI;
                break;
            case 'e':
                wallPosition.set(R, WALL_HEIGHT / 2, 0);
                wallRotation = Math.PI / 2;
                break;
            case 'w':
                wallPosition.set(-R, WALL_HEIGHT / 2, 0);
                wallRotation = -Math.PI / 2;
                break;
        }

        // Create the secret wall using the same method as regular walls
        const wallWidth = ROOM_WORLD_SIZE; // Full wall width
        const wallHeight = WALL_HEIGHT;
        const wallDepth = WALL_DEPTH;

        const secretWallGroup = this.createAnimatableSecretWall(
            wallWidth,
            wallHeight,
            wallDepth,
            secretWallData.hostRoom
        );

        // Position and orient the wall
        secretWallGroup.position.copy(wallPosition);
        secretWallGroup.rotation.y = wallRotation;

        // Mark the wall group for identification
        secretWallGroup.userData.isSecretWall = true;
        secretWallGroup.userData.areaName = areaName;
        secretWallGroup.userData.hostRoomId = secretWallData.hostRoom.id;
        secretWallGroup.userData.direction = direction;
        secretWallGroup.userData.originalDimensions = {
            width: wallWidth,
            height: wallHeight,
            depth: wallDepth
        };
        secretWallGroup.userData.roomData = secretWallData.hostRoom;
        secretWallGroup.name = `secret_wall_${secretWallData.hostRoom.id}_${direction}`;

        // Store references
        secretWallData.wallGroup = secretWallGroup;
        secretWallData.wallPosition = wallPosition.clone();
        secretWallData.wallRotation = wallRotation;

        // Set initial state
        if (secretWallData.isRevealed === undefined) {
            secretWallData.isRevealed = false;
        }
        secretWallData.isAnimating = false;

        // Add to room group
        roomGroup.add(secretWallGroup);

        console.log(`[SecretRoomManager] ✅ Secret wall created at position (${wallPosition.x.toFixed(2)}, ${wallPosition.y.toFixed(2)}, ${wallPosition.z.toFixed(2)})`);
        console.log(`[SecretRoomManager] Wall direction: ${direction}, rotation: ${wallRotation.toFixed(2)}`);

        return true;
    }

    /**
     * Get human-readable direction name
     * @param {string} direction - Direction code (n, e, s, w)
     * @returns {string} Human-readable direction
     */
    getDirectionName(direction) {
        const directions = {
            'n': 'North',
            'e': 'East',
            's': 'South',
            'w': 'West'
        };
        return directions[direction] || direction;
    }

    /**
     * Create a secret wall that looks EXACTLY like regular stonebrick walls
     * Initially uses merged geometry for performance, can be converted to individual voxels for animation
     * @param {number} width - Wall width
     * @param {number} height - Wall height
     * @param {number} depth - Wall depth
     * @param {Object} roomData - Room data for seeding
     * @returns {THREE.Group} Wall group with merged geometry (visually identical to regular walls)
     */
    createAnimatableSecretWall(width, height, depth, roomData) {
        console.log(`[SecretRoomManager] Creating secret wall with EXACT regular wall appearance: ${width.toFixed(2)}w x ${height.toFixed(2)}h x ${depth.toFixed(2)}d`);

        // Use the EXACT same function that creates regular walls
        const wallResult = createStonebrickWallSegment(width, height, depth, roomData);
        const wallGroup = wallResult.group;

        // Mark all meshes in the wall group as secret wall components AND add collision
        wallGroup.traverse((child) => {
            if (child.isMesh) {
                child.userData.isSecretWallMesh = true;
                child.userData.canBeAnimated = true;

                // FIXED: Add collision to secret wall meshes
                const dungeonHandler = window.dungeonHandler || this.dungeonHandler;
                if (dungeonHandler && dungeonHandler.collisionMeshes) {
                    dungeonHandler.collisionMeshes.push(child);
                    console.log('[SecretRoomManager] Added collision to secret wall mesh');
                } else {
                    console.warn('[SecretRoomManager] Could not access collision system for secret wall mesh!');
                }
            }
        });

        // Store metadata for later animation conversion
        wallGroup.userData.isSecretWall = true;
        wallGroup.userData.originalDimensions = { width, height, depth };
        wallGroup.userData.roomData = roomData;

        console.log(`[SecretRoomManager] ✅ Created secret wall using regular wall generation - VISUALLY IDENTICAL`);
        console.log(`[SecretRoomManager] Wall contains ${wallGroup.children.length} merged mesh(es) for optimal performance`);

        return wallGroup;
    }

    /**
     * Convert merged geometry secret wall to individual voxels for animation
     * This is called ONLY when animation is about to start
     * @param {Object} secretWallData - Secret wall data
     * @returns {boolean} Success status
     */
    convertMergedWallToAnimatableVoxels(secretWallData) {
        const wallGroup = secretWallData.wallGroup;
        if (!wallGroup) {
            console.error('[SecretRoomManager] No wall group found for animation conversion');
            return false;
        }

        console.log('[SecretRoomManager] Converting merged secret wall to individual animatable voxels...');

        // Find all merged meshes in the wall group
        const mergedMeshes = [];
        wallGroup.traverse((child) => {
            if (child.isMesh && child.userData.isSecretWallMesh) {
                mergedMeshes.push(child);
            }
        });

        if (mergedMeshes.length === 0) {
            console.warn('[SecretRoomManager] No secret wall meshes found for conversion');
            return false;
        }

        console.log(`[SecretRoomManager] Converting ${mergedMeshes.length} merged meshes to individual voxels`);

        // Get original dimensions and room data
        const { width, height, depth } = wallGroup.userData.originalDimensions;
        const roomData = wallGroup.userData.roomData;

        // Use seeded random for consistency
        const roomSeed = roomData ? roomData.id * 31 + 17 : Date.now();
        const random = mulberry32(roomSeed);

        // FIXED: Use larger scale for bigger voxels that match visual appearance
        const WALL_SCALE = ENVIRONMENT_PIXEL_SCALE * 3.0; // Increased from 1.5 to 3.0
        const wallVoxelSize = VOXEL_SIZE * WALL_SCALE;

        // Calculate grid dimensions using EXACT same method as regular walls
        const numX = Math.ceil(width / VOXEL_SIZE);
        const numY = Math.ceil(height / VOXEL_SIZE);
        const gridWidth = Math.ceil(numX / WALL_SCALE);
        const gridHeight = Math.ceil(numY / WALL_SCALE);

        const offsetX = (gridWidth - 1) * wallVoxelSize / 2;
        const offsetY = (gridHeight - 1) * wallVoxelSize / 2;

        // FIXED: Create proper cubic voxels (not flat pancakes)
        const voxelGeometry = getOrCreateGeometry(
            `secret_wall_cube_${wallVoxelSize.toFixed(4)}`,
            () => new THREE.BoxGeometry(wallVoxelSize, wallVoxelSize, wallVoxelSize)
        );

        // Use EXACT same materials as regular walls
        const stonebrickMaterials = [
            stonebrickMaterialPrimary,
            stonebrickMaterialSecondary,
            stonebrickCenterMaterial,
            stonebrickMortarMaterial
        ];

        // Remove old merged meshes
        mergedMeshes.forEach(mesh => {
            wallGroup.remove(mesh);
            // Dispose of geometry and material to free memory
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach(mat => mat.dispose());
                } else {
                    mesh.material.dispose();
                }
            }
        });

        // Create individual voxels using the same pattern as regular walls
        for (let y = 0; y < gridHeight; y++) {
            for (let x = 0; x < gridWidth; x++) {
                // FIXED: Use exact same brick pattern logic as regular walls
                const isOffsetRow = y % 2 === 1;
                let brickX = x;

                if (isOffsetRow) {
                    brickX = x + 0.5;
                    if (brickX >= gridWidth) continue;
                }

                // Choose material using same logic as regular walls
                const materialIndex = Math.floor(random() * stonebrickMaterials.length);
                const brickMaterial = stonebrickMaterials[materialIndex];

                // FIXED: Calculate position using exact same method as regular walls
                const finalX = brickX * wallVoxelSize - offsetX;
                const finalY = y * wallVoxelSize - offsetY;

                // Create individual voxel mesh
                const voxelMesh = new THREE.Mesh(voxelGeometry, brickMaterial);
                voxelMesh.position.set(finalX, finalY, 0);
                voxelMesh.castShadow = true;
                voxelMesh.receiveShadow = true;

                // Mark as animatable voxel
                voxelMesh.userData.isAnimatableVoxel = true;
                voxelMesh.userData.originalPosition = voxelMesh.position.clone();

                // FIXED: Add collision to individual voxels
                const dungeonHandler = window.dungeonHandler || this.dungeonHandler;
                if (dungeonHandler && dungeonHandler.collisionMeshes) {
                    dungeonHandler.collisionMeshes.push(voxelMesh);
                } else {
                    console.warn('[SecretRoomManager] Could not access collision system for voxel mesh!');
                }

                wallGroup.add(voxelMesh);
            }
        }

        console.log(`[SecretRoomManager] ✅ Successfully converted to ${wallGroup.children.length} individual voxels for animation`);
        return true;
    }

    /**
     * Show message when microphone permission is needed
     */
    showMicrophonePermissionMessage() {
        console.warn('[SecretRoomManager] Microphone permission required for voice commands');

        // Create a temporary UI message
        const messageDiv = document.createElement('div');
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '50%';
        messageDiv.style.left = '50%';
        messageDiv.style.transform = 'translate(-50%, -50%)';
        messageDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        messageDiv.style.color = '#ffffff';
        messageDiv.style.padding = '20px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.fontFamily = '"Press Start 2P", monospace';
        messageDiv.style.fontSize = '12px';
        messageDiv.style.textAlign = 'center';
        messageDiv.style.zIndex = '10000';
        messageDiv.style.maxWidth = '400px';
        messageDiv.innerHTML = `
            <div style="margin-bottom: 15px;">🎤 MICROPHONE ACCESS REQUIRED</div>
            <div style="font-size: 10px; line-height: 1.4;">
                Secret rooms require voice commands.<br>
                Please allow microphone access to use<br>
                "reveal secret" voice commands.
            </div>
            <button id="retry-microphone" style="
                margin-top: 15px;
                padding: 8px 16px;
                background: #444;
                color: white;
                border: 1px solid #666;
                border-radius: 4px;
                font-family: inherit;
                font-size: 8px;
                cursor: pointer;
            ">RETRY PERMISSION</button>
        `;

        document.body.appendChild(messageDiv);

        // Add retry button functionality
        document.getElementById('retry-microphone').addEventListener('click', () => {
            document.body.removeChild(messageDiv);
            this.setupVoiceRecognition().catch(error => {
                console.error('[SecretRoomManager] Retry failed:', error);
            });
        });

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (document.body.contains(messageDiv)) {
                document.body.removeChild(messageDiv);
            }
        }, 10000);
    }

    /**
     * Show message when speech recognition is not supported
     */
    showSpeechRecognitionNotSupportedMessage() {
        console.warn('[SecretRoomManager] Speech recognition not supported in this browser');

        // Create a temporary UI message
        const messageDiv = document.createElement('div');
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '50%';
        messageDiv.style.left = '50%';
        messageDiv.style.transform = 'translate(-50%, -50%)';
        messageDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        messageDiv.style.color = '#ffffff';
        messageDiv.style.padding = '20px';
        messageDiv.style.borderRadius = '10px';
        messageDiv.style.fontFamily = '"Press Start 2P", monospace';
        messageDiv.style.fontSize = '12px';
        messageDiv.style.textAlign = 'center';
        messageDiv.style.zIndex = '10000';
        messageDiv.style.maxWidth = '400px';
        const isMobile = this.isMobile || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        messageDiv.innerHTML = `
            <div style="margin-bottom: 15px;">🚫 VOICE COMMANDS NOT SUPPORTED</div>
            <div style="font-size: 10px; line-height: 1.4;">
                ${isMobile ?
                    'Mobile devices have limited voice support.<br>Use double-tap right joystick<br>near secret walls instead.' :
                    'Your browser doesn\'t support speech recognition.<br>Use the R key near secret walls instead.<br>Try Chrome or Edge for voice commands.'
                }
            </div>
        `;

        document.body.appendChild(messageDiv);

        // Auto-remove after 8 seconds
        setTimeout(() => {
            if (document.body.contains(messageDiv)) {
                document.body.removeChild(messageDiv);
            }
        }, 8000);
    }

    /**
     * Animate secret wall into archway (Harry Potter style)
     * @param {Object} secretWallData - Secret wall data
     * @returns {Promise} Animation promise
     */
    async animateStonebrickWallToArchway(secretWallData) {
        const wallGroup = secretWallData.wallGroup;

        console.log('[SecretRoomManager] Starting Harry Potter-style secret wall animation...');

        // First, convert merged geometry to individual voxels if needed
        let voxelMeshes = [];
        wallGroup.traverse((child) => {
            if (child.isMesh && child.userData.isAnimatableVoxel) {
                voxelMeshes.push(child);
            }
        });

        // If no individual voxels found, convert from merged geometry
        if (voxelMeshes.length === 0) {
            console.log('[SecretRoomManager] Converting merged geometry to individual voxels for animation...');
            const conversionSuccess = this.convertMergedWallToAnimatableVoxels(secretWallData);

            if (!conversionSuccess) {
                console.error('[SecretRoomManager] Failed to convert wall for animation');
                return;
            }

            // Collect the newly created voxel meshes
            wallGroup.traverse((child) => {
                if (child.isMesh && child.userData.isAnimatableVoxel) {
                    voxelMeshes.push(child);
                }
            });
        }

        console.log(`[SecretRoomManager] Found ${voxelMeshes.length} voxel meshes to animate`);

        if (voxelMeshes.length === 0) {
            console.warn('[SecretRoomManager] No voxel meshes found for animation');
            return;
        }

        // Calculate door opening area (center of wall)
        const doorWidth = 2.5; // DOOR_WIDTH
        const doorHeight = 3.0; // Door height
        const wallCenter = new THREE.Vector3(0, doorHeight / 2, 0);

        // Animate each voxel
        return new Promise((resolve) => {
            const animationDuration = 2500; // 2.5 seconds
            const startTime = Date.now();

            // Store original positions and calculate target positions
            const voxelData = voxelMeshes.map(mesh => {
                const worldPos = new THREE.Vector3();
                mesh.getWorldPosition(worldPos);

                // Convert to wall-relative position
                const localPos = wallGroup.worldToLocal(worldPos.clone());

                // Determine if this voxel is in the door opening area (proper archway shape)
                const archRadius = doorWidth / 2;
                const isInDoorArea = this.isVoxelInArchwayArea(localPos, wallCenter, doorWidth, doorHeight, archRadius);

                // Debug logging for center and bottom center voxels
                if (Math.abs(localPos.x) < 0.5 && Math.abs(localPos.y - wallCenter.y) < 1.5) {
                    console.log(`[SecretRoomManager] Center area voxel at (${localPos.x.toFixed(2)}, ${localPos.y.toFixed(2)}) - isInDoorArea: ${isInDoorArea}`);
                }

                // Special check for bottom center voxels
                if (Math.abs(localPos.x) < 0.5 && localPos.y < wallCenter.y - doorHeight / 4) {
                    console.log(`[SecretRoomManager] BOTTOM CENTER voxel at (${localPos.x.toFixed(2)}, ${localPos.y.toFixed(2)}) - isInDoorArea: ${isInDoorArea}`);
                }

                // Calculate target position for animation with wall boundary constraints
                let targetPos = localPos.clone();
                if (isInDoorArea) {
                    // Voxels in door area move away from center
                    const direction = localPos.clone().sub(wallCenter).normalize();
                    if (direction.length() === 0) {
                        direction.set(Math.random() - 0.5, Math.random() - 0.5, Math.random() - 0.5).normalize();
                    }
                    targetPos.add(direction.multiplyScalar(3 + Math.random() * 2));
                } else {
                    // FIXED: Voxels outside door area slide to form archway with boundary constraints
                    const wallGroup = secretWallData.wallGroup;
                    const wallWidth = wallGroup.userData.originalDimensions?.width || 5.0;
                    const maxX = wallWidth / 2 - 0.5; // Stay within wall boundaries

                    if (localPos.x < -doorWidth / 2) {
                        // Slide left but don't go beyond wall edge
                        const slideAmount = Math.min(1 + Math.random(), maxX - Math.abs(localPos.x));
                        targetPos.x -= slideAmount;
                    } else if (localPos.x > doorWidth / 2) {
                        // Slide right but don't go beyond wall edge
                        const slideAmount = Math.min(1 + Math.random(), maxX - Math.abs(localPos.x));
                        targetPos.x += slideAmount;
                    }
                    if (localPos.y > wallCenter.y + doorHeight / 2) {
                        targetPos.y += 1 + Math.random();
                    }
                }

                return {
                    mesh: mesh,
                    originalPos: localPos.clone(),
                    targetPos: targetPos,
                    isInDoorArea: isInDoorArea
                };
            });

            // Animation loop
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / animationDuration, 1);

                // Easing function
                const easeInOutCubic = (t) => {
                    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                };

                const easedProgress = easeInOutCubic(progress);

                // Update voxel positions
                voxelData.forEach(data => {
                    const { mesh, originalPos, targetPos, isInDoorArea } = data;

                    // Interpolate position
                    const currentPos = originalPos.clone().lerp(targetPos, easedProgress);
                    mesh.position.copy(currentPos);

                    // Add rotation for dynamic effect
                    const rotationAmount = easedProgress * Math.PI * (Math.random() > 0.5 ? 1 : -1);
                    mesh.rotation.x = rotationAmount * 0.3;
                    mesh.rotation.y = rotationAmount * 0.5;
                    mesh.rotation.z = rotationAmount * 0.2;

                    // FIXED: Fade out voxels moving into door area, keep frame voxels opaque
                    if (isInDoorArea) {
                        // Voxels moving into door area should fade out
                        const opacity = 1 - (easedProgress * 0.8);
                        if (mesh.material) {
                            if (Array.isArray(mesh.material)) {
                                mesh.material.forEach(mat => {
                                    mat.transparent = true;
                                    mat.opacity = opacity;
                                });
                            } else {
                                mesh.material.transparent = true;
                                mesh.material.opacity = opacity;
                            }
                        }
                    } else {
                        // Frame voxels stay completely opaque
                        if (mesh.material) {
                            if (Array.isArray(mesh.material)) {
                                mesh.material.forEach(mat => {
                                    mat.transparent = false;
                                    mat.opacity = 1.0;
                                });
                            } else {
                                mesh.material.transparent = false;
                                mesh.material.opacity = 1.0;
                            }
                        }
                    }
                });

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Animation complete - hide voxels that moved far away and remove their collision
                    let hiddenCount = 0;
                    voxelData.forEach(data => {
                        if (data.isInDoorArea) {
                            data.mesh.visible = false;
                            hiddenCount++;

                            // FIXED: Remove collision from hidden door area voxels
                            const dungeonHandler = window.dungeonHandler || this.dungeonHandler;
                            if (dungeonHandler && dungeonHandler.collisionMeshes) {
                                const collisionIndex = dungeonHandler.collisionMeshes.indexOf(data.mesh);
                                if (collisionIndex > -1) {
                                    dungeonHandler.collisionMeshes.splice(collisionIndex, 1);
                                    console.log('[SecretRoomManager] Removed collision from door area voxel');
                                }
                            }
                        }
                    });

                    console.log(`[SecretRoomManager] Voxel animation complete - ${hiddenCount} voxels hidden in door area`);

                    // FIXED: Create access zone AFTER animation completes
                    console.log('[SecretRoomManager] Creating access zone after animation completion...');
                    this.createInvisibleAccessZone(secretWallData);

                    // FIXED: Activate door trigger after animation completes
                    // Use the area name from the secret wall data instead of relying on wallGroup userData
                    const areaName = Array.from(this.secretWalls.entries()).find(([name, data]) => data === secretWallData)?.[0];
                    if (areaName) {
                        console.log('[SecretRoomManager] Animation complete, activating door trigger...');
                        // Add a small delay to ensure everything is properly set up
                        setTimeout(() => {
                            this.activateSecretDoorTrigger(areaName);
                            // Double-check activation after a brief moment
                            setTimeout(() => {
                                this.verifyDoorTriggerActivation(areaName);
                            }, 200);
                        }, 100);
                    } else {
                        console.error('[SecretRoomManager] Could not find area name for secret wall activation');
                    }

                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Animate archway back to wall (reverse animation)
     * @param {Object} secretWallData - Secret wall data
     * @returns {Promise} Animation promise
     */
    async animateArchwayToWall(secretWallData) {
        const wallGroup = secretWallData.wallGroup;

        console.log('[SecretRoomManager] Starting reverse animation - closing secret door...');

        // Get all voxel meshes
        let voxelMeshes = [];
        wallGroup.traverse((child) => {
            if (child.isMesh && child.userData.isAnimatableVoxel) {
                voxelMeshes.push(child);
            }
        });

        if (voxelMeshes.length === 0) {
            console.warn('[SecretRoomManager] No voxel meshes found for reverse animation');
            return;
        }

        console.log(`[SecretRoomManager] Found ${voxelMeshes.length} voxel meshes for reverse animation`);

        // Calculate door opening area (same as forward animation)
        const doorWidth = 2.5;
        const doorHeight = 3.0;
        const wallCenter = new THREE.Vector3(0, doorHeight / 2, 0);

        // Animate each voxel back to its original position
        return new Promise((resolve) => {
            const animationDuration = 2500; // Same duration as forward animation
            const startTime = Date.now();

            // Store current positions and calculate target positions (original positions)
            const voxelData = voxelMeshes.map(mesh => {
                const currentPos = mesh.position.clone();
                const originalPos = mesh.userData.originalPosition ? mesh.userData.originalPosition.clone() : currentPos.clone();

                // Determine if this voxel should be in the door area when closed
                const isInDoorArea = this.isVoxelInArchwayArea(originalPos, wallCenter, doorWidth, doorHeight, doorWidth / 2);

                return {
                    mesh: mesh,
                    currentPos: currentPos,
                    originalPos: originalPos,
                    isInDoorArea: isInDoorArea
                };
            });

            // Animation loop
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / animationDuration, 1);

                // Easing function (same as forward)
                const easeInOutCubic = (t) => {
                    return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
                };

                const easedProgress = easeInOutCubic(progress);

                // Update voxel positions
                voxelData.forEach(data => {
                    const { mesh, currentPos, originalPos, isInDoorArea } = data;

                    // Interpolate back to original position
                    const newPos = currentPos.clone().lerp(originalPos, easedProgress);
                    mesh.position.copy(newPos);

                    // Reverse rotation animation
                    const rotationAmount = (1 - easedProgress) * Math.PI * (Math.random() > 0.5 ? 1 : -1);
                    mesh.rotation.x = rotationAmount * 0.3;
                    mesh.rotation.y = rotationAmount * 0.5;
                    mesh.rotation.z = rotationAmount * 0.2;

                    // Restore opacity for voxels that were in door area
                    if (isInDoorArea) {
                        // Fade back in as they return to original positions
                        const opacity = 0.2 + (easedProgress * 0.8); // Reverse of fade out
                        if (mesh.material) {
                            if (Array.isArray(mesh.material)) {
                                mesh.material.forEach(mat => {
                                    mat.transparent = opacity < 1.0;
                                    mat.opacity = opacity;
                                });
                            } else {
                                mesh.material.transparent = opacity < 1.0;
                                mesh.material.opacity = opacity;
                            }
                        }

                        // Make visible again
                        mesh.visible = true;
                    } else {
                        // Frame voxels stay opaque
                        if (mesh.material) {
                            if (Array.isArray(mesh.material)) {
                                mesh.material.forEach(mat => {
                                    mat.transparent = false;
                                    mat.opacity = 1.0;
                                });
                            } else {
                                mesh.material.transparent = false;
                                mesh.material.opacity = 1.0;
                            }
                        }
                    }
                });

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    // Animation complete - restore all voxels to original state
                    voxelData.forEach(data => {
                        const { mesh, originalPos, isInDoorArea } = data;

                        // Reset position and rotation
                        mesh.position.copy(originalPos);
                        mesh.rotation.set(0, 0, 0);

                        // Reset material properties
                        if (mesh.material) {
                            if (Array.isArray(mesh.material)) {
                                mesh.material.forEach(mat => {
                                    mat.transparent = false;
                                    mat.opacity = 1.0;
                                });
                            } else {
                                mesh.material.transparent = false;
                                mesh.material.opacity = 1.0;
                            }
                        }

                        // Make all voxels visible
                        mesh.visible = true;
                    });

                    console.log(`[SecretRoomManager] Reverse animation complete - wall restored`);
                    resolve();
                }
            };

            animate();
        });
    }

    /**
     * Deactivate secret door trigger
     * @param {string} areaName - Area name
     */
    deactivateSecretDoorTrigger(areaName) {
        console.log(`[SecretRoomManager] Deactivating door trigger for area: ${areaName}`);

        if (this.dungeonHandler && this.dungeonHandler.doorTriggers) {
            const doorTrigger = this.dungeonHandler.doorTriggers.find(trigger =>
                trigger.userData.isSecretDoor && trigger.userData.areaName === areaName
            );

            if (doorTrigger) {
                doorTrigger.userData.isRevealed = false;
                doorTrigger.userData.animationComplete = false;
                console.log(`[SecretRoomManager] ✅ Door trigger deactivated for area: ${areaName}`);
            } else {
                console.warn(`[SecretRoomManager] Could not find door trigger to deactivate for area: ${areaName}`);
            }
        }
    }

    /**
     * Restore collision to secret wall
     * @param {Object} secretWallData - Secret wall data
     */
    restoreSecretWallCollision(secretWallData) {
        console.log('[SecretRoomManager] Restoring collision to secret wall...');

        if (secretWallData.wallGroup && this.dungeonHandler && this.dungeonHandler.collisionMeshes) {
            secretWallData.wallGroup.traverse((child) => {
                if (child.isMesh && child.userData.isAnimatableVoxel) {
                    // Add back to collision meshes if not already there
                    if (!this.dungeonHandler.collisionMeshes.includes(child)) {
                        this.dungeonHandler.collisionMeshes.push(child);
                        console.log('[SecretRoomManager] Restored collision for wall voxel');
                    }
                }
            });
        }
    }

    /**
     * Check if a voxel is in the archway door opening area (improved detection)
     * @param {THREE.Vector3} voxelPos - Voxel position
     * @param {THREE.Vector3} wallCenter - Wall center position
     * @param {number} doorWidth - Door width
     * @param {number} doorHeight - Door height
     * @param {number} archRadius - Arch radius
     * @returns {boolean} True if voxel is in archway area
     */
    isVoxelInArchwayArea(voxelPos, wallCenter, doorWidth, doorHeight, archRadius) {
        const relativeX = voxelPos.x;
        const relativeY = voxelPos.y - (wallCenter.y - doorHeight / 2);

        // Expand the door width slightly to catch edge voxels
        const expandedDoorWidth = doorWidth + 0.5; // Add 0.5 units buffer

        // Check if within expanded door width
        if (Math.abs(relativeX) > expandedDoorWidth / 2) {
            return false;
        }

        // FIXED: Better floor detection for bottom center voxels
        const floorLevel = -doorHeight / 2; // Bottom of the door opening
        const straightHeight = doorHeight * 0.7; // 70% of door height is straight

        // Check if in rectangular part of door (bottom section) - IMPROVED
        if (relativeY <= straightHeight) {
            return relativeY >= floorLevel - 0.5; // Much more generous floor detection
        }

        // Check if in arch part (top section) - expanded radius
        const archCenterY = straightHeight;
        const expandedArchRadius = archRadius + 0.3; // Slightly larger arch
        const distanceFromArchCenter = Math.sqrt(
            relativeX * relativeX +
            Math.pow(relativeY - archCenterY, 2)
        );

        return distanceFromArchCenter <= expandedArchRadius;
    }

    /**
     * Add subtle visual markers to make the secret wall barely distinguishable
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addSubtleSecretWallMarkers(wallGroup) {
        // Add very subtle floating particles (much fewer and smaller)
        this.addSubtleFloatingParticles(wallGroup);

        // Add very subtle color tint (barely noticeable)
        this.addSubtleSecretWallTint(wallGroup);
    }

    /**
     * Add visual markers to make the secret wall distinguishable
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addSecretWallVisualMarkers(wallGroup) {
        // Add subtle glowing outline
        this.addGlowingOutline(wallGroup);

        // Add floating magical particles
        this.addFloatingParticles(wallGroup);

        // Add subtle color tint
        this.addSecretWallTint(wallGroup);
    }

    /**
     * Add glowing outline to secret wall
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addGlowingOutline(wallGroup) {
        // Create a slightly larger wireframe version of the wall
        const outlineGeometry = new THREE.BoxGeometry(ROOM_WORLD_SIZE + 0.2, WALL_HEIGHT + 0.2, 1.2);
        const outlineMaterial = new THREE.MeshBasicMaterial({
            color: 0x4444ff,
            wireframe: true,
            transparent: true,
            opacity: 0.3
        });

        const outline = new THREE.Mesh(outlineGeometry, outlineMaterial);
        outline.position.set(0, 0, 0);
        outline.userData.isSecretWallMarker = true;

        wallGroup.add(outline);

        // Animate the glow
        const animateGlow = () => {
            const time = Date.now() * 0.002;
            outline.material.opacity = 0.2 + Math.sin(time) * 0.1;
            requestAnimationFrame(animateGlow);
        };
        animateGlow();
    }

    /**
     * Add floating particles around secret wall
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addFloatingParticles(wallGroup) {
        const particleCount = 20;
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            // Position particles around the wall
            positions[i * 3] = (Math.random() - 0.5) * ROOM_WORLD_SIZE * 1.2;
            positions[i * 3 + 1] = Math.random() * WALL_HEIGHT;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 2.0;

            // Magical blue-purple color
            colors[i * 3] = 0.3 + Math.random() * 0.3;     // R
            colors[i * 3 + 1] = 0.2 + Math.random() * 0.3; // G
            colors[i * 3 + 2] = 0.8 + Math.random() * 0.2; // B
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.6,
            blending: THREE.AdditiveBlending
        });

        const particleSystem = new THREE.Points(particles, particleMaterial);
        particleSystem.userData.isSecretWallMarker = true;

        wallGroup.add(particleSystem);

        // Animate particles
        const animateParticles = () => {
            const positions = particleSystem.geometry.attributes.position.array;
            const time = Date.now() * 0.001;

            for (let i = 0; i < positions.length; i += 3) {
                positions[i + 1] += Math.sin(time + i) * 0.01; // Gentle floating motion
                positions[i] += Math.cos(time + i * 0.1) * 0.005; // Slight horizontal drift

                // Reset particles that float too high
                if (positions[i + 1] > WALL_HEIGHT + 1) {
                    positions[i + 1] = -0.5;
                }
            }

            particleSystem.geometry.attributes.position.needsUpdate = true;
            requestAnimationFrame(animateParticles);
        };
        animateParticles();
    }

    /**
     * Add subtle color tint to secret wall
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addSecretWallTint(wallGroup) {
        // Traverse all meshes in the wall group and add a subtle tint
        wallGroup.traverse((child) => {
            if (child.isMesh && child.material) {
                // Clone the material to avoid affecting other walls
                const originalMaterial = child.material;
                const tintedMaterial = originalMaterial.clone();

                // Add subtle blue tint
                if (tintedMaterial.color) {
                    tintedMaterial.color.multiplyScalar(0.95); // Slightly darker
                    tintedMaterial.color.r *= 0.9; // Less red
                    tintedMaterial.color.g *= 0.9; // Less green
                    tintedMaterial.color.b *= 1.1; // More blue
                }

                child.material = tintedMaterial;
                child.userData.hasSecretTint = true;
            }
        });
    }

    /**
     * Add very subtle floating particles around secret wall
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addSubtleFloatingParticles(wallGroup) {
        const particleCount = 5; // Much fewer particles
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            // Position particles around the wall
            positions[i * 3] = (Math.random() - 0.5) * 2.5 * 1.2;
            positions[i * 3 + 1] = Math.random() * WALL_HEIGHT;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 1.0;

            // Very subtle blue color
            colors[i * 3] = 0.4 + Math.random() * 0.2;     // R
            colors[i * 3 + 1] = 0.4 + Math.random() * 0.2; // G
            colors[i * 3 + 2] = 0.6 + Math.random() * 0.2; // B
        }

        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const particleMaterial = new THREE.PointsMaterial({
            size: 0.02, // Much smaller
            vertexColors: true,
            transparent: true,
            opacity: 0.3, // Much more transparent
            blending: THREE.AdditiveBlending
        });

        const particleSystem = new THREE.Points(particles, particleMaterial);
        particleSystem.userData.isSecretWallMarker = true;

        wallGroup.add(particleSystem);

        // Animate particles
        const animateParticles = () => {
            const positions = particleSystem.geometry.attributes.position.array;
            const time = Date.now() * 0.0005; // Slower animation

            for (let i = 0; i < positions.length; i += 3) {
                positions[i + 1] += Math.sin(time + i) * 0.005; // Gentler floating motion
                positions[i] += Math.cos(time + i * 0.1) * 0.002; // Slight horizontal drift

                // Reset particles that float too high
                if (positions[i + 1] > WALL_HEIGHT + 0.5) {
                    positions[i + 1] = -0.2;
                }
            }

            particleSystem.geometry.attributes.position.needsUpdate = true;
            requestAnimationFrame(animateParticles);
        };
        animateParticles();
    }

    /**
     * Add very subtle color tint to secret wall
     * @param {THREE.Group} wallGroup - The secret wall group
     */
    addSubtleSecretWallTint(wallGroup) {
        // Traverse all meshes in the wall group and add a barely noticeable tint
        wallGroup.traverse((child) => {
            if (child.isMesh && child.material) {
                // Clone the material to avoid affecting other walls
                const originalMaterial = child.material;
                const tintedMaterial = originalMaterial.clone();

                // Add very subtle blue tint (barely noticeable)
                if (tintedMaterial.color) {
                    tintedMaterial.color.multiplyScalar(0.98); // Very slightly darker
                    tintedMaterial.color.r *= 0.97; // Barely less red
                    tintedMaterial.color.g *= 0.97; // Barely less green
                    tintedMaterial.color.b *= 1.02; // Barely more blue
                }

                child.material = tintedMaterial;
                child.userData.hasSecretTint = true;
            }
        });
    }
}
