import * as THREE from 'three';
import { HERO_SWORD_DATA } from '../generators/prefabs/heroSwordItem.js';
import { CALL_OF_ASCENSION_CARD_DATA } from '../generators/prefabs/callOfAscensionCard.js';
import { FOREST_BLESSING_CARD_DATA } from '../generators/prefabs/forestBlessingCard.js';
import { PHANTOM_STEP_CARD_DATA } from '../generators/prefabs/phantomStepCard.js';
import { SPECTRAL_BOLT_CARD_DATA } from '../generators/prefabs/spectralBoltCard.js';
import { TEMPORAL_RIFT_CARD_DATA } from '../generators/prefabs/temporalRiftCard.js';
import { NECROMANTIC_GRASP_CARD_DATA } from '../generators/prefabs/necromanticGraspCard.js';
import { ELEMENTAL_CONVERGENCE_CARD_DATA } from '../generators/prefabs/elementalConvergenceCard.js';
import { VOID_RUPTURE_CARD_DATA } from '../generators/prefabs/voidRuptureCard.js';
import { CRYSTAL_SANCTUARY_CARD_DATA } from '../generators/prefabs/crystalSanctuaryCard.js';
import { SOUL_ANCHOR_CARD_DATA } from '../generators/prefabs/soulAnchorCard.js';
import { MIRROR_PORTAL_CARD_DATA } from '../generators/prefabs/mirrorPortalCard.js';
import { TIME_ECHO_CARD_DATA } from '../generators/prefabs/timeEchoCard.js';
import { SOUL_SIGHT_CARD_DATA } from '../generators/prefabs/soulSightCard.js';
import { METEOR_STORM_CARD_DATA } from '../generators/prefabs/meteorStormCard.js';
import { LIGHTNING_CHAIN_CARD_DATA } from '../generators/prefabs/lightningChainCard.js';
import { SHADOW_CLONE_STRIKE_CARD_DATA } from '../generators/prefabs/shadowCloneStrikeCard.js';
import { SANCTUARY_WARD_CARD_DATA } from '../generators/prefabs/sanctuaryWardCard.js';
import { PHOENIX_REBIRTH_CARD_DATA } from '../generators/prefabs/phoenixRebirthCard.js';
import { STONE_SKIN_CARD_DATA } from '../generators/prefabs/stoneSkinCard.js';
import { DRAGON_FORM_CARD_DATA } from '../generators/prefabs/dragonFormCard.js';
import { GHOST_WALK_CARD_DATA } from '../generators/prefabs/ghostWalkCard.js';
import { GIANTS_MIGHT_CARD_DATA } from '../generators/prefabs/giantsMightCard.js';
import { FROST_PRISON_CARD_DATA } from '../generators/prefabs/frostPrisonCard.js';
import { FLAME_BARRIER_CARD_DATA } from '../generators/prefabs/flameBarrierCard.js';
import { EARTHQUAKE_CARD_DATA } from '../generators/prefabs/earthquakeCard.js';
import { TORNADO_CARD_DATA } from '../generators/prefabs/tornadoCard.js';
import { SHADOW_ARMY_CARD_DATA } from '../generators/prefabs/shadowArmyCard.js';
import { SPIRIT_WOLF_PACK_CARD_DATA } from '../generators/prefabs/spiritWolfPackCard.js';
import { BONE_GUARDIAN_CARD_DATA } from '../generators/prefabs/boneGuardianCard.js';
import { BLINK_STRIKE_CARD_DATA } from '../generators/prefabs/blinkStrikeCard.js';
import { TIME_FREEZE_CARD_DATA } from '../generators/prefabs/timeFreezeCard.js';
import { GRAVITY_WELL_CARD_DATA } from '../generators/prefabs/gravityWellCard.js';
import { LIFE_STEAL_AURA_CARD_DATA } from '../generators/prefabs/lifeStealAuraCard.js';
import { SOUL_HARVEST_CARD_DATA } from '../generators/prefabs/soulHarvestCard.js';
import { WILD_MAGIC_CARD_DATA } from '../generators/prefabs/wildMagicCard.js';
import { GAMBLERS_LUCK_CARD_DATA } from '../generators/prefabs/gamblersLuckCard.js';
import { SOUL_POTION_DATA } from '../generators/prefabs/soulPotionItem.js';
import { mulberry32 } from '../generators/prefabs/shared.js';

/**
 * Loot Pool Manager
 * Handles treasure chest loot generation with configurable drop rates
 */

// Define all available items for loot pools
export const LOOT_ITEMS = {
    soul_potion: {
        ...SOUL_POTION_DATA,
        rarity: 'common' // Override rarity for loot system
    },
    hero_sword: {
        ...HERO_SWORD_DATA,
        rarity: 'common' // Override rarity for loot system
    },
    call_of_ascension: {
        ...CALL_OF_ASCENSION_CARD_DATA,
        rarity: 'common' // Override rarity for loot system
    },
    forest_blessing: {
        ...FOREST_BLESSING_CARD_DATA,
        rarity: 'rare' // Override rarity for loot system
    },
    phantom_step: {
        ...PHANTOM_STEP_CARD_DATA,
        rarity: 'rare' // Rare teleportation magic
    },
    spectral_bolt: {
        ...SPECTRAL_BOLT_CARD_DATA,
        rarity: 'rare' // Rare offensive magic
    },
    temporal_rift: {
        ...TEMPORAL_RIFT_CARD_DATA,
        rarity: 'epic' // Epic time manipulation magic
    },
    necromantic_grasp: {
        ...NECROMANTIC_GRASP_CARD_DATA,
        rarity: 'epic' // Epic necromantic summon magic
    },
    elemental_convergence: {
        ...ELEMENTAL_CONVERGENCE_CARD_DATA,
        rarity: 'legendary' // Legendary ultimate elemental magic
    },
    void_rupture: {
        ...VOID_RUPTURE_CARD_DATA,
        rarity: 'epic' // Epic void tear magic
    },
    crystal_sanctuary: {
        ...CRYSTAL_SANCTUARY_CARD_DATA,
        rarity: 'rare' // Rare protective crystal magic
    },
    soul_anchor: {
        ...SOUL_ANCHOR_CARD_DATA,
        rarity: 'epic' // Epic teleportation anchor magic
    },
    mirror_portal: {
        ...MIRROR_PORTAL_CARD_DATA,
        rarity: 'epic' // Epic portal travel magic
    },
    time_echo: {
        ...TIME_ECHO_CARD_DATA,
        rarity: 'epic' // Epic temporal shadow magic
    },
    soul_sight: {
        ...SOUL_SIGHT_CARD_DATA,
        rarity: 'epic' // Epic mystical vision magic
    },
    meteor_storm: {
        ...METEOR_STORM_CARD_DATA,
        rarity: 'epic' // Epic meteor summoning magic
    },
    lightning_chain: {
        ...LIGHTNING_CHAIN_CARD_DATA,
        rarity: 'epic' // Epic chain lightning magic
    },
    shadow_clone_strike: {
        ...SHADOW_CLONE_STRIKE_CARD_DATA,
        rarity: 'epic' // Epic shadow clone magic
    },
    sanctuary_ward: {
        ...SANCTUARY_WARD_CARD_DATA,
        rarity: 'epic' // Epic sanctuary protection magic
    },
    phoenix_rebirth: {
        ...PHOENIX_REBIRTH_CARD_DATA,
        rarity: 'epic' // Epic phoenix resurrection magic
    },
    stone_skin: {
        ...STONE_SKIN_CARD_DATA,
        rarity: 'epic' // Epic stone armor magic
    },
    dragon_form: {
        ...DRAGON_FORM_CARD_DATA,
        rarity: 'epic' // Epic dragon transformation magic
    },
    ghost_walk: {
        ...GHOST_WALK_CARD_DATA,
        rarity: 'epic' // Epic ghost transformation magic
    },
    giants_might: {
        ...GIANTS_MIGHT_CARD_DATA,
        rarity: 'epic' // Epic titan transformation magic
    },
    frost_prison: {
        ...FROST_PRISON_CARD_DATA,
        rarity: 'epic' // Epic ice wall defense magic
    },
    flame_barrier: {
        ...FLAME_BARRIER_CARD_DATA,
        rarity: 'epic' // Epic fire ring defense magic
    },
    earthquake: {
        ...EARTHQUAKE_CARD_DATA,
        rarity: 'epic' // Epic seismic destruction magic
    },
    tornado: {
        ...TORNADO_CARD_DATA,
        rarity: 'epic' // Epic wind storm magic
    },
    shadow_army: {
        ...SHADOW_ARMY_CARD_DATA,
        rarity: 'legendary' // Legendary shadow summoning magic
    },
    spirit_wolf_pack: {
        ...SPIRIT_WOLF_PACK_CARD_DATA,
        rarity: 'epic' // Epic spirit animal summoning magic
    },
    bone_guardian: {
        ...BONE_GUARDIAN_CARD_DATA,
        rarity: 'epic' // Epic skeletal guardian summoning magic
    },
    blink_strike: {
        ...BLINK_STRIKE_CARD_DATA,
        rarity: 'rare' // Rare teleportation attack magic
    },
    time_freeze: {
        ...TIME_FREEZE_CARD_DATA,
        rarity: 'legendary' // Legendary time manipulation magic
    },
    gravity_well: {
        ...GRAVITY_WELL_CARD_DATA,
        rarity: 'epic' // Epic gravitational field magic
    },
    life_steal_aura: {
        ...LIFE_STEAL_AURA_CARD_DATA,
        rarity: 'epic' // Epic vampiric healing magic
    },
    soul_harvest: {
        ...SOUL_HARVEST_CARD_DATA,
        rarity: 'epic' // Epic necromantic damage boost magic
    },
    wild_magic: {
        ...WILD_MAGIC_CARD_DATA,
        rarity: 'epic' // Epic random effect utility magic
    },
    gamblers_luck: {
        ...GAMBLERS_LUCK_CARD_DATA,
        rarity: 'epic' // Epic risk/reward utility magic
    },
    // Future items will be added here
    // healing_potion: { rarity: 'common', ... },
    // magic_ring: { rarity: 'uncommon', ... },
    // legendary_sword: { rarity: 'legendary', ... },
    // etc.
};

// Define rarity drop chances for different chest types
export const RARITY_DROP_RATES = {
    normal_chest: {
        common: 60,
        uncommon: 25,
        rare: 12,
        epic: 3,
        legendary: 0
    },
    rare_chest: {
        common: 30,
        uncommon: 35,
        rare: 25,
        epic: 8,
        legendary: 2
    },
    boss_chest: {
        common: 10,
        uncommon: 20,
        rare: 30,
        epic: 25,
        legendary: 15
    },
    secret_room: {
        common: 20,
        uncommon: 30,
        rare: 35,
        epic: 12,
        legendary: 3
    },
    event_chest: {
        common: 25,
        uncommon: 30,
        rare: 30,
        epic: 12,
        legendary: 3
    }
};

// Define loot pools for different chest types (now just lists items by rarity)
export const LOOT_POOLS = {
    normal_chest: {
        name: 'Normal Chest',
        description: 'Standard treasure chest found throughout the dungeon',
        items: [
            // Items are now organized by rarity, not individual drop chances
            { itemId: 'soul_potion', quantity: { min: 1, max: 1 } },
            { itemId: 'hero_sword', quantity: { min: 1, max: 1 } },
            { itemId: 'call_of_ascension', quantity: { min: 1, max: 1 } },
            { itemId: 'forest_blessing', quantity: { min: 1, max: 1 } },
            { itemId: 'phantom_step', quantity: { min: 1, max: 1 } },
            { itemId: 'spectral_bolt', quantity: { min: 1, max: 1 } },
            { itemId: 'temporal_rift', quantity: { min: 1, max: 1 } },
            { itemId: 'necromantic_grasp', quantity: { min: 1, max: 1 } },
            { itemId: 'elemental_convergence', quantity: { min: 1, max: 1 } },
            { itemId: 'void_rupture', quantity: { min: 1, max: 1 } },
            { itemId: 'crystal_sanctuary', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_anchor', quantity: { min: 1, max: 1 } },
            { itemId: 'mirror_portal', quantity: { min: 1, max: 1 } },
            { itemId: 'time_echo', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_sight', quantity: { min: 1, max: 1 } },
            { itemId: 'meteor_storm', quantity: { min: 1, max: 1 } },
            { itemId: 'lightning_chain', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_clone_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'sanctuary_ward', quantity: { min: 1, max: 1 } },
            { itemId: 'phoenix_rebirth', quantity: { min: 1, max: 1 } },
            { itemId: 'stone_skin', quantity: { min: 1, max: 1 } },
            { itemId: 'dragon_form', quantity: { min: 1, max: 1 } },
            { itemId: 'ghost_walk', quantity: { min: 1, max: 1 } },
            { itemId: 'giants_might', quantity: { min: 1, max: 1 } },
            { itemId: 'frost_prison', quantity: { min: 1, max: 1 } },
            { itemId: 'flame_barrier', quantity: { min: 1, max: 1 } },
            { itemId: 'earthquake', quantity: { min: 1, max: 1 } },
            { itemId: 'tornado', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_army', quantity: { min: 1, max: 1 } },
            { itemId: 'spirit_wolf_pack', quantity: { min: 1, max: 1 } },
            { itemId: 'bone_guardian', quantity: { min: 1, max: 1 } },
            { itemId: 'blink_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'time_freeze', quantity: { min: 1, max: 1 } },
            { itemId: 'gravity_well', quantity: { min: 1, max: 1 } },
            { itemId: 'life_steal_aura', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_harvest', quantity: { min: 1, max: 1 } },
            { itemId: 'wild_magic', quantity: { min: 1, max: 1 } },
            { itemId: 'gamblers_luck', quantity: { min: 1, max: 1 } }
            // Future items just need to be added here with their rarity defined in LOOT_ITEMS
        ],
        guaranteedDrop: true, // Always drops at least one item
        maxItems: 1 // Only one item per chest
    },
    
    rare_chest: {
        name: 'Rare Chest',
        description: 'Rare treasure chest with better loot',
        items: [
            // Same items, but rare chests use different rarity drop rates
            { itemId: 'soul_potion', quantity: { min: 1, max: 1 } },
            { itemId: 'hero_sword', quantity: { min: 1, max: 1 } },
            { itemId: 'call_of_ascension', quantity: { min: 1, max: 1 } },
            { itemId: 'forest_blessing', quantity: { min: 1, max: 1 } },
            { itemId: 'phantom_step', quantity: { min: 1, max: 1 } },
            { itemId: 'spectral_bolt', quantity: { min: 1, max: 1 } },
            { itemId: 'temporal_rift', quantity: { min: 1, max: 1 } },
            { itemId: 'necromantic_grasp', quantity: { min: 1, max: 1 } },
            { itemId: 'elemental_convergence', quantity: { min: 1, max: 1 } },
            { itemId: 'void_rupture', quantity: { min: 1, max: 1 } },
            { itemId: 'crystal_sanctuary', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_anchor', quantity: { min: 1, max: 1 } },
            { itemId: 'mirror_portal', quantity: { min: 1, max: 1 } },
            { itemId: 'time_echo', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_sight', quantity: { min: 1, max: 1 } },
            { itemId: 'meteor_storm', quantity: { min: 1, max: 1 } },
            { itemId: 'lightning_chain', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_clone_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'sanctuary_ward', quantity: { min: 1, max: 1 } },
            { itemId: 'phoenix_rebirth', quantity: { min: 1, max: 1 } },
            { itemId: 'stone_skin', quantity: { min: 1, max: 1 } },
            { itemId: 'dragon_form', quantity: { min: 1, max: 1 } },
            { itemId: 'ghost_walk', quantity: { min: 1, max: 1 } },
            { itemId: 'giants_might', quantity: { min: 1, max: 1 } },
            { itemId: 'frost_prison', quantity: { min: 1, max: 1 } },
            { itemId: 'flame_barrier', quantity: { min: 1, max: 1 } },
            { itemId: 'earthquake', quantity: { min: 1, max: 1 } },
            { itemId: 'tornado', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_army', quantity: { min: 1, max: 1 } },
            { itemId: 'spirit_wolf_pack', quantity: { min: 1, max: 1 } },
            { itemId: 'bone_guardian', quantity: { min: 1, max: 1 } },
            { itemId: 'blink_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'time_freeze', quantity: { min: 1, max: 1 } },
            { itemId: 'gravity_well', quantity: { min: 1, max: 1 } },
            { itemId: 'life_steal_aura', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_harvest', quantity: { min: 1, max: 1 } },
            { itemId: 'wild_magic', quantity: { min: 1, max: 1 } },
            { itemId: 'gamblers_luck', quantity: { min: 1, max: 1 } }
        ],
        guaranteedDrop: true,
        maxItems: 1
    },
    
    boss_chest: {
        name: 'Boss Chest',
        description: 'Special chest found after defeating bosses',
        items: [
            // Boss chests have the best rarity distribution
            { itemId: 'soul_potion', quantity: { min: 1, max: 1 } },
            { itemId: 'hero_sword', quantity: { min: 1, max: 1 } },
            { itemId: 'call_of_ascension', quantity: { min: 1, max: 1 } },
            { itemId: 'forest_blessing', quantity: { min: 1, max: 1 } },
            { itemId: 'phantom_step', quantity: { min: 1, max: 1 } },
            { itemId: 'spectral_bolt', quantity: { min: 1, max: 1 } },
            { itemId: 'temporal_rift', quantity: { min: 1, max: 1 } },
            { itemId: 'necromantic_grasp', quantity: { min: 1, max: 1 } },
            { itemId: 'elemental_convergence', quantity: { min: 1, max: 1 } },
            { itemId: 'void_rupture', quantity: { min: 1, max: 1 } },
            { itemId: 'crystal_sanctuary', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_anchor', quantity: { min: 1, max: 1 } },
            { itemId: 'mirror_portal', quantity: { min: 1, max: 1 } },
            { itemId: 'time_echo', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_sight', quantity: { min: 1, max: 1 } },
            { itemId: 'meteor_storm', quantity: { min: 1, max: 1 } },
            { itemId: 'lightning_chain', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_clone_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'sanctuary_ward', quantity: { min: 1, max: 1 } },
            { itemId: 'phoenix_rebirth', quantity: { min: 1, max: 1 } },
            { itemId: 'stone_skin', quantity: { min: 1, max: 1 } },
            { itemId: 'dragon_form', quantity: { min: 1, max: 1 } },
            { itemId: 'ghost_walk', quantity: { min: 1, max: 1 } },
            { itemId: 'giants_might', quantity: { min: 1, max: 1 } },
            { itemId: 'frost_prison', quantity: { min: 1, max: 1 } },
            { itemId: 'flame_barrier', quantity: { min: 1, max: 1 } },
            { itemId: 'earthquake', quantity: { min: 1, max: 1 } },
            { itemId: 'tornado', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_army', quantity: { min: 1, max: 1 } },
            { itemId: 'spirit_wolf_pack', quantity: { min: 1, max: 1 } },
            { itemId: 'bone_guardian', quantity: { min: 1, max: 1 } },
            { itemId: 'blink_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'time_freeze', quantity: { min: 1, max: 1 } },
            { itemId: 'gravity_well', quantity: { min: 1, max: 1 } },
            { itemId: 'life_steal_aura', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_harvest', quantity: { min: 1, max: 1 } },
            { itemId: 'wild_magic', quantity: { min: 1, max: 1 } },
            { itemId: 'gamblers_luck', quantity: { min: 1, max: 1 } }
        ],
        guaranteedDrop: true,
        maxItems: 1
    },

    event_chest: {
        name: 'Event Chest',
        description: 'Special chest found in event rooms with good loot',
        items: [
            // Event chests have good rarity distribution (better than normal, not as good as boss)
            { itemId: 'soul_potion', quantity: { min: 1, max: 1 } },
            { itemId: 'hero_sword', quantity: { min: 1, max: 1 } },
            { itemId: 'call_of_ascension', quantity: { min: 1, max: 1 } },
            { itemId: 'forest_blessing', quantity: { min: 1, max: 1 } },
            { itemId: 'phantom_step', quantity: { min: 1, max: 1 } },
            { itemId: 'spectral_bolt', quantity: { min: 1, max: 1 } },
            { itemId: 'temporal_rift', quantity: { min: 1, max: 1 } },
            { itemId: 'necromantic_grasp', quantity: { min: 1, max: 1 } },
            { itemId: 'elemental_convergence', quantity: { min: 1, max: 1 } },
            { itemId: 'void_rupture', quantity: { min: 1, max: 1 } },
            { itemId: 'crystal_sanctuary', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_anchor', quantity: { min: 1, max: 1 } },
            { itemId: 'mirror_portal', quantity: { min: 1, max: 1 } },
            { itemId: 'time_echo', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_sight', quantity: { min: 1, max: 1 } },
            { itemId: 'meteor_storm', quantity: { min: 1, max: 1 } },
            { itemId: 'lightning_chain', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_clone_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'sanctuary_ward', quantity: { min: 1, max: 1 } },
            { itemId: 'phoenix_rebirth', quantity: { min: 1, max: 1 } },
            { itemId: 'stone_skin', quantity: { min: 1, max: 1 } },
            { itemId: 'dragon_form', quantity: { min: 1, max: 1 } },
            { itemId: 'ghost_walk', quantity: { min: 1, max: 1 } },
            { itemId: 'giants_might', quantity: { min: 1, max: 1 } },
            { itemId: 'frost_prison', quantity: { min: 1, max: 1 } },
            { itemId: 'flame_barrier', quantity: { min: 1, max: 1 } },
            { itemId: 'earthquake', quantity: { min: 1, max: 1 } },
            { itemId: 'tornado', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_army', quantity: { min: 1, max: 1 } },
            { itemId: 'spirit_wolf_pack', quantity: { min: 1, max: 1 } },
            { itemId: 'bone_guardian', quantity: { min: 1, max: 1 } },
            { itemId: 'blink_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'time_freeze', quantity: { min: 1, max: 1 } },
            { itemId: 'gravity_well', quantity: { min: 1, max: 1 } },
            { itemId: 'life_steal_aura', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_harvest', quantity: { min: 1, max: 1 } },
            { itemId: 'wild_magic', quantity: { min: 1, max: 1 } },
            { itemId: 'gamblers_luck', quantity: { min: 1, max: 1 } }
        ],
        guaranteedDrop: true,
        maxItems: 1
    },

    secret_room: {
        name: 'Secret Room Chest',
        description: 'Mystical treasure chest found in hidden crystal caves',
        items: [
            // Secret room chests have good rarity distribution with focus on rare items
            { itemId: 'soul_potion', quantity: { min: 1, max: 1 } },
            { itemId: 'hero_sword', quantity: { min: 1, max: 1 } },
            { itemId: 'call_of_ascension', quantity: { min: 1, max: 1 } },
            { itemId: 'forest_blessing', quantity: { min: 1, max: 1 } },
            { itemId: 'phantom_step', quantity: { min: 1, max: 1 } },
            { itemId: 'spectral_bolt', quantity: { min: 1, max: 1 } },
            { itemId: 'temporal_rift', quantity: { min: 1, max: 1 } },
            { itemId: 'necromantic_grasp', quantity: { min: 1, max: 1 } },
            { itemId: 'elemental_convergence', quantity: { min: 1, max: 1 } },
            { itemId: 'void_rupture', quantity: { min: 1, max: 1 } },
            { itemId: 'crystal_sanctuary', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_anchor', quantity: { min: 1, max: 1 } },
            { itemId: 'mirror_portal', quantity: { min: 1, max: 1 } },
            { itemId: 'time_echo', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_sight', quantity: { min: 1, max: 1 } },
            { itemId: 'meteor_storm', quantity: { min: 1, max: 1 } },
            { itemId: 'lightning_chain', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_clone_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'sanctuary_ward', quantity: { min: 1, max: 1 } },
            { itemId: 'phoenix_rebirth', quantity: { min: 1, max: 1 } },
            { itemId: 'stone_skin', quantity: { min: 1, max: 1 } },
            { itemId: 'dragon_form', quantity: { min: 1, max: 1 } },
            { itemId: 'ghost_walk', quantity: { min: 1, max: 1 } },
            { itemId: 'giants_might', quantity: { min: 1, max: 1 } },
            { itemId: 'frost_prison', quantity: { min: 1, max: 1 } },
            { itemId: 'flame_barrier', quantity: { min: 1, max: 1 } },
            { itemId: 'earthquake', quantity: { min: 1, max: 1 } },
            { itemId: 'tornado', quantity: { min: 1, max: 1 } },
            { itemId: 'shadow_army', quantity: { min: 1, max: 1 } },
            { itemId: 'spirit_wolf_pack', quantity: { min: 1, max: 1 } },
            { itemId: 'bone_guardian', quantity: { min: 1, max: 1 } },
            { itemId: 'blink_strike', quantity: { min: 1, max: 1 } },
            { itemId: 'time_freeze', quantity: { min: 1, max: 1 } },
            { itemId: 'gravity_well', quantity: { min: 1, max: 1 } },
            { itemId: 'life_steal_aura', quantity: { min: 1, max: 1 } },
            { itemId: 'soul_harvest', quantity: { min: 1, max: 1 } },
            { itemId: 'wild_magic', quantity: { min: 1, max: 1 } },
            { itemId: 'gamblers_luck', quantity: { min: 1, max: 1 } }
        ],
        guaranteedDrop: true,
        maxItems: 1
    }
};

/**
 * Loot Pool Manager Class
 */
export class LootPoolManager {
    constructor() {
        this.lootItems = LOOT_ITEMS;
        this.lootPools = LOOT_POOLS;
    }

    /**
     * Generate loot for a chest
     * @param {string} chestType - Type of chest (normal_chest, rare_chest, boss_chest)
     * @param {number} seed - Seed for consistent randomization
     * @param {object} options - Additional options (biome, roomType, playerLevel, etc.)
     * @returns {object|null} Generated loot item or null if no loot
     */
    generateLoot(chestType = 'normal_chest', seed = Date.now(), options = {}) {
        const pool = this.lootPools[chestType];
        if (!pool) {
            console.warn(`[LootPoolManager] Unknown chest type: ${chestType}`);
            return null;
        }

        console.log(`[LootPoolManager] Generating loot for ${chestType} with seed ${seed}`);
        console.log(`[LootPoolManager] Context:`, options);

        // Filter items based on context (biome, room type, etc.)
        const eligibleItems = this.filterItemsByContext(pool.items, options);
        console.log(`[LootPoolManager] Filtered items:`, eligibleItems.map(item => `${item.itemId} (${this.lootItems[item.itemId]?.rarity})`));

        if (eligibleItems.length === 0) {
            console.warn(`[LootPoolManager] No eligible items for context:`, options);
            return null;
        }

        // Create seeded random number generator
        const rng = mulberry32(seed);

        // Step 1: Determine rarity based on chest type
        const rarityRates = RARITY_DROP_RATES[chestType];
        if (!rarityRates) {
            console.warn(`[LootPoolManager] No rarity rates defined for ${chestType}`);
            return null;
        }

        const selectedRarity = this.selectRarityByWeight(rarityRates, rng);
        console.log(`[LootPoolManager] Selected rarity: ${selectedRarity}`);

        // Step 2: Filter eligible items by selected rarity
        const itemsOfRarity = eligibleItems.filter(item => {
            const itemData = this.lootItems[item.itemId];
            return itemData && itemData.rarity === selectedRarity;
        });

        console.log(`[LootPoolManager] Items of rarity '${selectedRarity}':`, itemsOfRarity.map(item => item.itemId));

        if (itemsOfRarity.length === 0) {
            console.warn(`[LootPoolManager] No items of rarity '${selectedRarity}' available, falling back to any rarity`);
            // Fallback: select from any eligible item
            const selectedItem = eligibleItems[Math.floor(rng() * eligibleItems.length)];
            console.log(`[LootPoolManager] Fallback selected: ${selectedItem.itemId}`);
            return this.createLootItem(selectedItem.itemId, selectedItem.quantity, seed);
        }

        // Step 3: Randomly select from items of the chosen rarity
        const selectedItem = itemsOfRarity[Math.floor(rng() * itemsOfRarity.length)];
        console.log(`[LootPoolManager] Selected item: ${selectedItem.itemId} (${selectedRarity})`);

        return this.createLootItem(selectedItem.itemId, selectedItem.quantity, seed);
    }

    /**
     * Select a rarity based on weighted probabilities
     * @param {object} rarityRates - Object with rarity -> percentage mappings
     * @param {function} rng - Random number generator function
     * @returns {string} Selected rarity
     */
    selectRarityByWeight(rarityRates, rng) {
        const totalWeight = Object.values(rarityRates).reduce((sum, weight) => sum + weight, 0);
        const randomValue = rng() * totalWeight;

        let cumulativeWeight = 0;
        for (const [rarity, weight] of Object.entries(rarityRates)) {
            cumulativeWeight += weight;
            if (randomValue <= cumulativeWeight) {
                return rarity;
            }
        }

        // Fallback to first rarity
        return Object.keys(rarityRates)[0];
    }

    /**
     * Filter items based on context (biome, room type, rarity, etc.)
     * @param {Array} items - Array of items to filter
     * @param {object} context - Context object with biome, roomType, etc.
     * @returns {Array} Filtered array of items
     */
    filterItemsByContext(items, context = {}) {
        const { biome = 'any', roomType = 'any', playerLevel = 1 } = context;

        return items.filter(item => {
            const itemData = this.lootItems[item.itemId];
            if (!itemData) return false;

            // Check biome restrictions
            if (itemData.allowedBiomes && !itemData.allowedBiomes.includes('any')) {
                if (!itemData.allowedBiomes.includes(biome)) {
                    return false;
                }
            }

            // Check room type restrictions
            if (itemData.allowedRoomTypes && !itemData.allowedRoomTypes.includes('any')) {
                if (!itemData.allowedRoomTypes.includes(roomType)) {
                    return false;
                }
            }

            // Check player level requirements (if item has level requirement)
            if (itemData.requiredLevel && playerLevel < itemData.requiredLevel) {
                return false;
            }

            return true;
        });
    }

    /**
     * Create a loot item instance
     * @param {string} itemId - ID of the item to create
     * @param {object} quantityRange - Min/max quantity range
     * @param {number} seed - Seed for item generation
     * @returns {object} Loot item data
     */
    createLootItem(itemId, quantityRange, seed) {
        const itemData = this.lootItems[itemId];
        if (!itemData) {
            console.warn(`[LootPoolManager] Unknown item ID: ${itemId}`);
            return null;
        }

        const rng = mulberry32(seed + 1000); // Offset seed for quantity
        const quantity = quantityRange.min + Math.floor(rng() * (quantityRange.max - quantityRange.min + 1));

        return {
            itemId: itemId,
            itemData: itemData,
            quantity: quantity,
            seed: seed,
            
            // Create the 3D model
            create3DModel: (options = {}) => {
                return itemData.createFunction({
                    seed: seed,
                    ...options
                });
            }
        };
    }

    /**
     * Get item data by ID
     * @param {string} itemId - Item ID
     * @returns {object|null} Item data or null if not found
     */
    getItemData(itemId) {
        return this.lootItems[itemId] || null;
    }

    /**
     * Get loot pool data by type
     * @param {string} poolType - Pool type
     * @returns {object|null} Pool data or null if not found
     */
    getLootPool(poolType) {
        return this.lootPools[poolType] || null;
    }

    /**
     * Add a new item to the loot system
     * @param {string} itemId - Unique item ID
     * @param {object} itemData - Item data object
     */
    addItem(itemId, itemData) {
        this.lootItems[itemId] = itemData;
        console.log(`[LootPoolManager] Added new item: ${itemId}`);
    }

    /**
     * Add a new loot pool
     * @param {string} poolType - Unique pool type
     * @param {object} poolData - Pool data object
     */
    addLootPool(poolType, poolData) {
        this.lootPools[poolType] = poolData;
        console.log(`[LootPoolManager] Added new loot pool: ${poolType}`);
    }

    /**
     * Get all available chest types
     * @returns {Array} Array of chest type strings
     */
    getAvailableChestTypes() {
        return Object.keys(this.lootPools);
    }

    /**
     * Get all available item IDs
     * @returns {Array} Array of item ID strings
     */
    getAvailableItems() {
        return Object.keys(this.lootItems);
    }
}

// Create and export singleton instance
export const lootPoolManager = new LootPoolManager();

// Export for easy access
export default lootPoolManager;
