import * as THREE from 'three';
import { Chess3DBoard } from './Chess3DBoard.js';
import { Chess3DMinigame } from './Chess3DMinigame.js';
import { ChessLogic } from '../utils/ChessLogic.js';
import { ChessAI } from './ChessAI.js';

/**
 * Devil's Chess Game - A full chess implementation with AI and Devil commentary
 * Features:
 * - Full chess rules implementation
 * - Mobile touch support
 * - Variable AI difficulty
 * - Devil's dark humor commentary
 * - Black/red themed board
 */
export class ChessGame {
    constructor(sceneManager, difficulty = 'normal', dungeonHandler = null) {
        if (!sceneManager) {
            console.warn('[ChessGame] No sceneManager provided, some features may not work');
        }
        this.sceneManager = sceneManager;
        this.difficulty = difficulty;
        this.providedDungeonHandler = dungeonHandler; // Direct dungeon handler if provided
        this.isActive = false;
        this.currentPlayer = 'white'; // Player is white, Devil is black
        this.gameState = 'playing'; // playing, check, checkmate, stalemate, draw
        
        // Chess board state - 8x8 array, null = empty, objects = pieces
        this.board = this.initializeBoard();
        
        // Game state tracking
        this.moveHistory = [];
        this.capturedPieces = { white: [], black: [] };
        this.selectedSquare = null;
        this.validMoves = [];
        this.lastMove = null;
        this.kingPositions = { white: { x: 4, y: 7 }, black: { x: 4, y: 0 } };
        this.castlingRights = {
            white: { kingside: true, queenside: true },
            black: { kingside: true, queenside: true }
        };
        this.enPassantTarget = null;
        this.halfMoveClock = 0;
        this.fullMoveNumber = 1;
        
        // UI elements
        this.chessUI = null;
        this.boardElement = null;
        this.commentaryElement = null;
        this.devilCommentary = new DevilCommentary();
        
        // 3D Chess system
        this.chess3DBoard = null;
        this.chessTable = null;
        this.chess3DMinigame = null;
        
        // Touch/mouse handling
        this.isDragging = false;
        this.draggedPiece = null;
        this.dragOffset = { x: 0, y: 0 };
        
        // AI system
        this.ai = new ChessAI(this.difficulty);
        
        // Game end callback for promise resolution
        this.gameEndCallback = null;
        this.gameErrorCallback = null;
        
        // Keyboard event handlers
        this.keyboardHandler = null;
        
        // Status display control
        this.statusVisible = false;
        
        console.log(`[ChessGame] Chess game initialized with difficulty: ${difficulty}`);
    }
    
    /**
     * Initialize the chess board with starting positions
     */
    initializeBoard() {
        const board = Array(8).fill().map(() => Array(8).fill(null));
        
        // Place pawns
        for (let x = 0; x < 8; x++) {
            board[x][1] = { type: 'pawn', color: 'black', x, y: 1, hasMoved: false };
            board[x][6] = { type: 'pawn', color: 'white', x, y: 6, hasMoved: false };
        }
        
        // Place other pieces
        const pieceOrder = ['rook', 'knight', 'bishop', 'queen', 'king', 'bishop', 'knight', 'rook'];
        for (let x = 0; x < 8; x++) {
            board[x][0] = { type: pieceOrder[x], color: 'black', x, y: 0, hasMoved: false };
            board[x][7] = { type: pieceOrder[x], color: 'white', x, y: 7, hasMoved: false };
        }
        
        return board;
    }
    
    /**
     * Initialize 3D voxel pieces for 2D chess board rendering
     */
    async initialize3DVoxelPieces() {
        console.log('[ChessGame] Initializing 3D voxel piece library for 2D board...');
        
        if (!this.sceneManager || !this.sceneManager.renderer) {
            console.error('[ChessGame] No renderer available for voxel piece rendering');
            console.error('[ChessGame] SceneManager:', this.sceneManager);
            console.error('[ChessGame] Renderer:', this.sceneManager?.renderer);
            return false;
        }
        
        console.log('[ChessGame] Renderer available, starting voxel piece generation...');
        
        // Create a hidden scene for rendering 3D voxel pieces to images
        this.voxelRenderScene = new THREE.Scene();
        this.voxelRenderScene.background = new THREE.Color(0x000000); // Black background for better visibility
        this.voxelRenderCamera = new THREE.PerspectiveCamera(45, 1, 0.1, 100); // Match card system camera
        this.voxelRenderCamera.position.set(0, 0, 2.5); // Match card system positioning
        this.voxelRenderCamera.lookAt(0, 0, 0);
        
        // Add bright lighting for piece rendering
        const ambientLight = new THREE.AmbientLight(0x808080, 1.0); // Brighter ambient
        this.voxelRenderScene.add(ambientLight);
        
        const directionalLight1 = new THREE.DirectionalLight(0xffffff, 1.2);
        directionalLight1.position.set(5, 8, 5);
        this.voxelRenderScene.add(directionalLight1);
        
        const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight2.position.set(-3, 5, 2);
        this.voxelRenderScene.add(directionalLight2);
        
        // Create render target for piece images
        this.pieceRenderTarget = new THREE.WebGLRenderTarget(128, 128, {
            format: THREE.RGBAFormat,
            type: THREE.UnsignedByteType
        });
        
        // Pre-render all piece types to create piece image library
        this.voxelPieceImages = {};
        await this.preRenderAllVoxelPieces();
        
        // Create 3D voxel chess board background
        await this.create3DVoxelBoard();
        
        console.log('[ChessGame] 3D voxel piece library initialized');
        console.log('[ChessGame] Generated piece images:', Object.keys(this.voxelPieceImages));
        console.log('[ChessGame] Board image available:', !!this.voxelBoardImage);
        return true;
    }

    /**
     * Pre-render all voxel pieces to create image library
     */
    async preRenderAllVoxelPieces() {
        const pieceTypes = ['pawn', 'rook', 'knight', 'bishop', 'queen', 'king'];
        const colors = ['white', 'black'];
        
        for (const color of colors) {
            for (const pieceType of pieceTypes) {
                this.voxelPieceImages[`${color}_${pieceType}`] = await this.renderVoxelPieceToImage(pieceType, color);
            }
        }
    }

    /**
     * Render a voxel piece to an image data URL
     */
    async renderVoxelPieceToImage(pieceType, color) {
        try {
            const pieceCreators = {
                white: {
                    pawn: async () => (await import('../generators/prefabs/chessPawnObject.js')).createWhiteChessPawn({ scale: 2.4 }),
                    rook: async () => (await import('../generators/prefabs/chessRookObject.js')).createWhiteChessRook({ scale: 2.4 }),
                    knight: async () => (await import('../generators/prefabs/chessKnightObject.js')).createWhiteChessKnight({ scale: 2.4 }),
                    bishop: async () => (await import('../generators/prefabs/chessBishopObject.js')).createWhiteChessBishop({ scale: 2.4 }),
                    queen: async () => (await import('../generators/prefabs/chessQueenObject.js')).createWhiteChessQueen({ scale: 2.4 }),
                    king: async () => (await import('../generators/prefabs/chessKingObject.js')).createWhiteChessKing({ scale: 2.4 })
                },
                black: {
                    pawn: async () => (await import('../generators/prefabs/chessPawnObject.js')).createBlackChessPawn({ scale: 2.4 }),
                    rook: async () => (await import('../generators/prefabs/chessRookObject.js')).createBlackChessRook({ scale: 2.4 }),
                    knight: async () => (await import('../generators/prefabs/chessKnightObject.js')).createBlackChessKnight({ scale: 2.4 }),
                    bishop: async () => (await import('../generators/prefabs/chessBishopObject.js')).createBlackChessBishop({ scale: 2.4 }),
                    queen: async () => (await import('../generators/prefabs/chessQueenObject.js')).createBlackChessQueen({ scale: 2.4 }),
                    king: async () => (await import('../generators/prefabs/chessKingObject.js')).createBlackChessKing({ scale: 2.4 })
                }
            };
            
            console.log(`[ChessGame] Rendering ${color} ${pieceType}...`);
            
            // Create the 3D piece
            let piece;
            try {
                piece = await pieceCreators[color][pieceType]();
                console.log(`[ChessGame] Created 3D piece:`, piece);
            } catch (error) {
                console.error(`[ChessGame] Error creating ${color} ${pieceType}:`, error);
                return null;
            }
            
            // Clear the render scene
            while (this.voxelRenderScene.children.length > 2) { // Keep lights
                this.voxelRenderScene.remove(this.voxelRenderScene.children[2]);
            }
            
            // Add the piece to the render scene
            this.voxelRenderScene.add(piece);
            
            // Render to the render target
            const renderer = this.sceneManager.renderer;
            const originalRenderTarget = renderer.getRenderTarget();
            
            renderer.setRenderTarget(this.pieceRenderTarget);
            renderer.clear();
            renderer.render(this.voxelRenderScene, this.voxelRenderCamera);
            
            // Read the pixels and create a canvas with the image
            const canvas = document.createElement('canvas');
            canvas.width = 128;
            canvas.height = 128;
            const ctx = canvas.getContext('2d');
            
            // Use a different approach - render directly to canvas
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, 128, 128);
            
            // Try alternative method: render to screen then copy to canvas
            const tempCanvas = renderer.domElement;
            const tempContext = tempCanvas.getContext('2d');
            
            // Simple approach: create image data from render target  
            try {
                const pixelBuffer = new Uint8Array(128 * 128 * 4);
                renderer.readRenderTargetPixels(this.pieceRenderTarget, 0, 0, 128, 128, pixelBuffer);
                
                // Check if we got any non-zero pixels
                let hasPixels = false;
                for (let i = 3; i < pixelBuffer.length; i += 4) { // Check alpha channel
                    if (pixelBuffer[i] > 0) {
                        hasPixels = true;
                        break;
                    }
                }
                console.log(`[ChessGame] Render target has visible pixels:`, hasPixels);
                
                // Create ImageData and draw to canvas
                const imageData = new ImageData(new Uint8ClampedArray(pixelBuffer), 128, 128);
                ctx.putImageData(imageData, 0, 0);
                
                // Flip the canvas vertically since WebGL is bottom-up
                const flippedImageData = ctx.getImageData(0, 0, 128, 128);
                const flippedData = new Uint8ClampedArray(flippedImageData.data.length);
                
                for (let y = 0; y < 128; y++) {
                    for (let x = 0; x < 128; x++) {
                        const srcIndex = ((127 - y) * 128 + x) * 4;
                        const dstIndex = (y * 128 + x) * 4;
                        flippedData[dstIndex] = flippedImageData.data[srcIndex];
                        flippedData[dstIndex + 1] = flippedImageData.data[srcIndex + 1];
                        flippedData[dstIndex + 2] = flippedImageData.data[srcIndex + 2];
                        flippedData[dstIndex + 3] = flippedImageData.data[srcIndex + 3];
                    }
                }
                
                const flippedImageDataObj = new ImageData(flippedData, 128, 128);
                ctx.putImageData(flippedImageDataObj, 0, 0);
                
            } catch (renderError) {
                console.error(`[ChessGame] Error reading render target pixels:`, renderError);
                // Fill with a test pattern
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(10, 10, 108, 108);
                ctx.fillStyle = '#ffffff';
                ctx.fillText(pieceType, 20, 70);
            }
            
            // Restore original render target
            renderer.setRenderTarget(originalRenderTarget);
            
            // Remove the piece from render scene
            this.voxelRenderScene.remove(piece);
            
            // Return the data URL
            const dataURL = canvas.toDataURL('image/png');
            console.log(`[ChessGame] Generated data URL for ${color} ${pieceType}, length:`, dataURL.length, 'first 100 chars:', dataURL.substring(0, 100));
            
            
            return dataURL;
            
        } catch (error) {
            console.error(`[ChessGame] Error rendering ${color} ${pieceType}:`, error);
            return null;
        }
    }
    
    /**
     * Create 3D voxel chess board background image
     */
    async create3DVoxelBoard() {
        try {
            // Import VOXEL_SIZE
            const { VOXEL_SIZE } = await import('../generators/prefabs/shared.js');
            
            // Clear the render scene
            while (this.voxelRenderScene.children.length > 2) {
                this.voxelRenderScene.remove(this.voxelRenderScene.children[2]);
            }
            
            // Create a voxel chess board
            const boardGroup = new THREE.Group();
            const voxelScale = VOXEL_SIZE * 4.0; // Make board bigger
            
            // Board colors (alternating dark and light squares)
            const darkColor = 0x2a1a1a; // Dark obsidian
            const lightColor = 0x4a3a3a; // Light obsidian
            
            // Create 8x8 chess board with voxels
            for (let x = 0; x < 8; x++) {
                for (let z = 0; z < 8; z++) {
                    const isLight = (x + z) % 2 === 0;
                    const color = isLight ? lightColor : darkColor;
                    
                    // Create a thick square base
                    for (let y = 0; y < 2; y++) {
                        const geometry = new THREE.BoxGeometry(voxelScale, voxelScale * 0.5, voxelScale);
                        const material = new THREE.MeshStandardMaterial({
                            color: color,
                            roughness: 0.3,
                            metalness: 0.7
                        });
                        
                        const voxel = new THREE.Mesh(geometry, material);
                        voxel.position.set(
                            (x - 3.5) * voxelScale,
                            y * voxelScale * 0.5,
                            (z - 3.5) * voxelScale
                        );
                        
                        boardGroup.add(voxel);
                    }
                    
                    // Add border/edge details
                    if (x === 0 || x === 7 || z === 0 || z === 7) {
                        const borderGeometry = new THREE.BoxGeometry(voxelScale * 0.1, voxelScale * 0.3, voxelScale * 0.1);
                        const borderMaterial = new THREE.MeshStandardMaterial({
                            color: 0x660000,
                            roughness: 0.1,
                            metalness: 0.9,
                            emissive: 0x220000,
                            emissiveIntensity: 0.2
                        });
                        
                        const border = new THREE.Mesh(borderGeometry, borderMaterial);
                        border.position.set(
                            (x - 3.5) * voxelScale,
                            voxelScale,
                            (z - 3.5) * voxelScale
                        );
                        
                        boardGroup.add(border);
                    }
                }
            }
            
            // Position the board in the render scene
            boardGroup.position.y = -voxelScale;
            this.voxelRenderScene.add(boardGroup);
            
            // Position camera for top-down board view
            this.voxelRenderCamera.position.set(0, 20, 0);
            this.voxelRenderCamera.lookAt(0, 0, 0);
            
            // Adjust orthographic camera bounds for larger board
            this.voxelRenderCamera.left = -8;
            this.voxelRenderCamera.right = 8;
            this.voxelRenderCamera.top = 8;
            this.voxelRenderCamera.bottom = -8;
            this.voxelRenderCamera.updateProjectionMatrix();
            
            // Render the board to create background image
            const renderer = this.sceneManager.renderer;
            const originalRenderTarget = renderer.getRenderTarget();
            
            // Use a larger render target for the board
            const boardRenderTarget = new THREE.WebGLRenderTarget(1024, 1024, {
                format: THREE.RGBAFormat,
                type: THREE.UnsignedByteType
            });
            
            renderer.setRenderTarget(boardRenderTarget);
            renderer.clear();
            renderer.render(this.voxelRenderScene, this.voxelRenderCamera);
            
            // Create canvas and get board image
            const canvas = document.createElement('canvas');
            canvas.width = 1024;
            canvas.height = 1024;
            const ctx = canvas.getContext('2d');
            
            const pixelBuffer = new Uint8Array(1024 * 1024 * 4);
            renderer.readRenderTargetPixels(boardRenderTarget, 0, 0, 1024, 1024, pixelBuffer);
            
            const imageData = new ImageData(new Uint8ClampedArray(pixelBuffer), 1024, 1024);
            ctx.save();
            ctx.scale(1, -1);
            ctx.translate(0, -1024);
            ctx.putImageData(imageData, 0, 0);
            ctx.restore();
            
            // Store the board background image
            this.voxelBoardImage = canvas.toDataURL('image/png');
            console.log('[ChessGame] Generated board data URL, length:', this.voxelBoardImage.length);
            
            // For debugging - temporarily show board image for 3 seconds
            console.log(`[ChessGame] Board debug - creating test image element`);
            const testBoardImg = document.createElement('img');
            testBoardImg.src = this.voxelBoardImage;
            testBoardImg.style.cssText = `
                position: fixed;
                top: 10px;
                left: 10px;
                width: 256px;
                height: 256px;
                border: 2px solid #00ff00;
                z-index: 99999;
                background: #ffffff;
            `;
            document.body.appendChild(testBoardImg);
            setTimeout(() => {
                if (testBoardImg.parentNode) {
                    document.body.removeChild(testBoardImg);
                }
            }, 3000);
            
            // Restore original render target and camera
            renderer.setRenderTarget(originalRenderTarget);
            this.voxelRenderCamera.position.set(1, 6, 3);
            this.voxelRenderCamera.lookAt(0, 2, 0);
            
            // Reset camera bounds for piece rendering
            this.voxelRenderCamera.left = -4;
            this.voxelRenderCamera.right = 4;
            this.voxelRenderCamera.top = 4;
            this.voxelRenderCamera.bottom = -4;
            this.voxelRenderCamera.updateProjectionMatrix();
            
            // Clean up
            this.voxelRenderScene.remove(boardGroup);
            boardRenderTarget.dispose();
            
            console.log('[ChessGame] 3D voxel chess board background created');
            
        } catch (error) {
            console.error('[ChessGame] Error creating 3D voxel board:', error);
        }
    }

    /**
     * Initialize 3D chess board
     */
    initialize3DChessBoard() {
        console.log('[ChessGame] Initializing 3D chess board...');
        console.log('[ChessGame] Scene manager:', !!this.sceneManager);
        
        if (!this.sceneManager) {
            console.error('[ChessGame] No scene manager available for 3D chess board');
            return false;
        }

        console.log('[ChessGame] Active scene handler:', !!this.sceneManager.activeSceneHandler);
        console.log('[ChessGame] Active scene handler type:', this.sceneManager.activeSceneHandler?.constructor?.name);
        console.log('[ChessGame] Scene manager keys:', Object.keys(this.sceneManager));
        
        // Try multiple ways to get the dungeon handler
        let dungeonHandler = null;
        
        // Method 0: Use provided dungeon handler if available
        if (this.providedDungeonHandler) {
            dungeonHandler = this.providedDungeonHandler;
            console.log('[ChessGame] Using provided dungeon handler');
        }
        // Method 1: Check if activeSceneHandler has dungeonHandler
        else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.dungeonHandler) {
            dungeonHandler = this.sceneManager.activeSceneHandler.dungeonHandler;
            console.log('[ChessGame] Found dungeon handler via activeSceneHandler.dungeonHandler');
        }
        // Method 2: Check if activeSceneHandler IS the dungeon handler
        else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.type === 'dungeon') {
            dungeonHandler = this.sceneManager.activeSceneHandler;
            console.log('[ChessGame] Found dungeon handler - activeSceneHandler is dungeon type');
        }
        // Method 3: Check if activeSceneHandler has scene property (might be DungeonHandler)
        else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.scene) {
            dungeonHandler = this.sceneManager.activeSceneHandler;
            console.log('[ChessGame] Found dungeon handler - activeSceneHandler has scene');
        }
        // Method 4: Direct dungeonHandler property
        else if (this.sceneManager.dungeonHandler) {
            dungeonHandler = this.sceneManager.dungeonHandler;
            console.log('[ChessGame] Found dungeon handler via sceneManager.dungeonHandler');
        }
        // Method 5: Check scene handlers
        else if (this.sceneManager.sceneHandlers) {
            const handlers = Object.values(this.sceneManager.sceneHandlers);
            dungeonHandler = handlers.find(h => h && (h.type === 'dungeon' || h.scene));
            if (dungeonHandler) {
                console.log('[ChessGame] Found dungeon handler in sceneHandlers');
            }
        }

        console.log('[ChessGame] Dungeon handler found:', !!dungeonHandler);
        console.log('[ChessGame] Dungeon handler type:', dungeonHandler?.constructor?.name);
        
        if (!dungeonHandler || !dungeonHandler.scene) {
            console.error('[ChessGame] No dungeon scene available for 3D chess board');
            console.log('[ChessGame] Available handlers:', Object.keys(this.sceneManager.sceneHandlers || {}));
            return false;
        }

        console.log('[ChessGame] Scene available:', !!dungeonHandler.scene);

        // Find the chess table in the scene
        this.chessTable = this.findChessTable(dungeonHandler.scene);
        if (!this.chessTable) {
            console.error('[ChessGame] No chess table found in scene');
            console.log('[ChessGame] Scene children count:', dungeonHandler.scene.children.length);
            return false;
        }

        console.log('[ChessGame] Chess table found:', this.chessTable.userData);

        // Initialize 3D chess board
        this.chess3DBoard = new Chess3DBoard(dungeonHandler.scene, this.chessTable);
        this.chess3DBoard.initializeBoard(this.board);
        
        console.log('[ChessGame] 3D chess board initialized successfully');
        return true;
    }

    /**
     * Find the chess table object in the scene
     */
    findChessTable(scene) {
        let chessTable = null;
        let tablesFound = [];
        
        scene.traverse((object) => {
            // Check for chess table by userData
            if (object.userData && object.userData.type === 'obsidian_chess_table') {
                chessTable = object;
                tablesFound.push('obsidian_chess_table');
            }
            // Also check for other chess-related objects as fallback
            else if (object.userData && object.userData.type && object.userData.type.includes('chess')) {
                tablesFound.push(object.userData.type);
                if (!chessTable) {
                    chessTable = object;
                }
            }
            // Also check for objects with specific names
            else if (object.name && (object.name.includes('chess') || object.name.includes('Chess'))) {
                tablesFound.push(`name:${object.name}`);
                if (!chessTable) {
                    chessTable = object;
                }
            }
        });
        
        console.log('[ChessGame] Chess-related objects found:', tablesFound);
        console.log('[ChessGame] Chess table selected:', chessTable ? chessTable.userData : null);
        
        return chessTable;
    }

    /**
     * Start the chess game - initialize 3D board and begin gameplay
     */
    async startGame() {
        console.log('[ChessGame] Starting 3D chess game');
        
        // Prevent multiple games from running
        if (this.isActive) {
            console.warn('[ChessGame] Game already active, ending current game');
            await this.forfeitGame();
        }
        
        // Clean up any existing elements
        this.cleanup();
        
        this.isActive = true;
        
        try {
            // Create and start the 3D minigame
            this.chess3DMinigame = new Chess3DMinigame(this.sceneManager, this);
            await this.chess3DMinigame.startMinigame();
            
            // Play game start audio
            this.playAudio('chess_start');
            
            // Show initial devil commentary
            await this.devilCommentary.showComment('game_start', this.difficulty);
            
            // Player goes first (white)
            if (this.chess3DBoard) {
                console.log('[ChessGame] 3D chess game ready - player can interact with pieces');
            } else {
                console.log('[ChessGame] 2D chess game ready - player can start moving');
            }
            
            // Return a promise that resolves when the game ends
            return new Promise((resolve, reject) => {
                this.gameEndCallback = resolve;
                this.gameErrorCallback = reject;
                
                // Add escape key listener for emergency exit
                this.addKeyboardListeners();
            });
        } catch (error) {
            console.error('[ChessGame] Error starting chess game:', error);
            this.cleanup();
            throw error;
        }
    }
    
    /**
     * Create minimal commentary overlay for 3D chess
     */
    createCommentaryOverlay() {
        // Create minimal overlay for devil commentary only
        this.chessUI = document.createElement('div');
        this.chessUI.id = 'chess-commentary-overlay';
        this.chessUI.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: min(80vw, 600px);
            height: 100px;
            background: rgba(0, 0, 0, 0.9);
            border: 3px solid #660000;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            color: #ff6666;
            font-size: 1.1em;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
            z-index: 1000;
            pointer-events: none;
        `;
        
        // Create commentary text element
        this.commentaryElement = document.createElement('div');
        this.commentaryElement.id = 'devil-commentary-3d';
        this.commentaryElement.style.cssText = `
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-shadow: 0 0 10px #ff0000;
        `;
        this.chessUI.appendChild(this.commentaryElement);
        
        // Create exit hint
        const hintElement = document.createElement('div');
        hintElement.style.cssText = `
            position: absolute;
            top: -30px;
            right: 0;
            font-size: 0.8em;
            color: rgba(255, 102, 102, 0.7);
            font-family: 'Courier New', monospace;
        `;
        hintElement.textContent = 'ESC to forfeit';
        this.chessUI.appendChild(hintElement);
        
        document.body.appendChild(this.chessUI);
        console.log('[ChessGame] Created commentary overlay for 3D chess');
    }

    /**
     * Add 3D chess piece interaction
     */
    addChess3DInteraction() {
        if (!this.sceneManager) {
            console.warn('[ChessGame] No scene manager for 3D interaction');
            return;
        }
        
        // Try multiple ways to get the dungeon handler
        let dungeonHandler = null;
        
        // Use provided dungeon handler if available
        if (this.providedDungeonHandler) {
            dungeonHandler = this.providedDungeonHandler;
        } else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.dungeonHandler) {
            dungeonHandler = this.sceneManager.activeSceneHandler.dungeonHandler;
        } else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.type === 'dungeon') {
            dungeonHandler = this.sceneManager.activeSceneHandler;
        } else if (this.sceneManager.activeSceneHandler && this.sceneManager.activeSceneHandler.scene) {
            dungeonHandler = this.sceneManager.activeSceneHandler;
        } else if (this.sceneManager.dungeonHandler) {
            dungeonHandler = this.sceneManager.dungeonHandler;
        }
        
        if (!dungeonHandler) {
            console.warn('[ChessGame] No dungeon handler for 3D interaction');
            return;
        }
        
        // Add raycasting interaction for chess pieces
        this.chessRaycaster = new THREE.Raycaster();
        this.chessMouse = new THREE.Vector2();
        
        // Store original interaction handlers
        this.originalInteractionHandlers = dungeonHandler.interactionHandlers || {};
        
        // Override interaction handlers for chess mode
        dungeonHandler.interactionHandlers = {
            onPointerDown: (event) => this.handleChess3DPointerDown(event),
            onPointerMove: (event) => this.handleChess3DPointerMove(event),
            onPointerUp: (event) => this.handleChess3DPointerUp(event)
        };
        
        console.log('[ChessGame] Added 3D chess interaction handlers');
    }

    /**
     * Handle 3D chess pointer down
     */
    handleChess3DPointerDown(event) {
        if (this.currentPlayer !== 'white') return; // Only allow interaction on player's turn
        
        this.updateMousePosition(event);
        
        const dungeonHandler = this.sceneManager.activeSceneHandler.dungeonHandler;
        if (!dungeonHandler.camera) return;
        
        this.chessRaycaster.setFromCamera(this.chessMouse, dungeonHandler.camera);
        
        // Get all chess pieces for raycasting
        const chessPieces = [];
        dungeonHandler.scene.traverse((object) => {
            if (object.userData && object.userData.isChessPiece) {
                chessPieces.push(object);
            }
        });
        
        const intersects = this.chessRaycaster.intersectObjects(chessPieces, true);
        
        if (intersects.length > 0) {
            // Find the chess piece (might be a child mesh)
            let chessPiece = intersects[0].object;
            while (chessPiece && !chessPiece.userData.isChessPiece) {
                chessPiece = chessPiece.parent;
            }
            
            if (chessPiece && chessPiece.userData.isChessPiece) {
                const chessX = chessPiece.userData.chessX;
                const chessY = chessPiece.userData.chessY;
                const piece = this.board[chessX][chessY];
                
                if (piece && piece.color === 'white') {
                    // Select own piece
                    this.selectedSquare = { x: chessX, y: chessY };
                    this.validMoves = this.getValidMoves(piece);
                    
                    // Update 3D visual feedback
                    this.chess3DBoard.selectPiece(chessX, chessY);
                    this.chess3DBoard.showValidMoves(this.validMoves);
                    
                    console.log(`[ChessGame] Selected ${piece.color} ${piece.type} at (${chessX},${chessY})`);
                } else if (this.selectedSquare) {
                    // Try to move to this square
                    this.tryMove3D(this.selectedSquare, { x: chessX, y: chessY });
                }
            }
        } else {
            // Check if clicking on board square for movement
            this.handleBoardSquareClick(event);
        }
    }

    /**
     * Handle clicking on board squares
     */
    handleBoardSquareClick(event) {
        if (!this.selectedSquare || !this.chess3DBoard) return;
        
        const dungeonHandler = this.sceneManager.activeSceneHandler.dungeonHandler;
        if (!dungeonHandler.camera) return;
        
        // Raycast against the chess table to get board position
        const tableObjects = [];
        dungeonHandler.scene.traverse((object) => {
            if (object.userData && object.userData.type === 'obsidian_chess_table') {
                tableObjects.push(object);
            }
        });
        
        const intersects = this.chessRaycaster.intersectObjects(tableObjects, true);
        
        if (intersects.length > 0) {
            const intersectionPoint = intersects[0].point;
            const chessCoords = this.chess3DBoard.worldToChess(intersectionPoint);
            
            if (chessCoords) {
                this.tryMove3D(this.selectedSquare, chessCoords);
            }
        }
    }

    /**
     * Handle 3D pointer move (for hover effects)
     */
    handleChess3DPointerMove(event) {
        this.updateMousePosition(event);
        // Could add hover effects here
    }

    /**
     * Handle 3D pointer up
     */
    handleChess3DPointerUp(event) {
        // Handle any drag completion here
    }

    /**
     * Update mouse position for raycasting
     */
    updateMousePosition(event) {
        const canvas = event.target;
        const rect = canvas.getBoundingClientRect();
        
        this.chessMouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.chessMouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    /**
     * Try to make a 3D move
     */
    async tryMove3D(from, to) {
        if (!this.isValidMove(from, to)) {
            this.chess3DBoard.clearSelection();
            this.selectedSquare = null;
            this.validMoves = [];
            return false;
        }
        
        // Make the move in game logic
        const capturedPiece = this.board[to.x][to.y];
        const success = this.makeMove(from, to);
        
        if (success) {
            // Update 3D board
            this.chess3DBoard.movePiece(from.x, from.y, to.x, to.y);
            this.chess3DBoard.clearSelection();
            
            // Play appropriate sound
            if (capturedPiece) {
                this.playAudio('chess_capture');
            } else {
                this.playAudio('chess_move');
            }
            
            this.selectedSquare = null;
            this.validMoves = [];
            
            // Check game state
            await this.checkGameState();
            
            // Devil's turn
            if (this.gameState === 'playing' && this.currentPlayer === 'black') {
                await this.devilMove();
            }
        }
        
        return success;
    }

    /**
     * Create the chess game UI
     */
    createChessUI() {
        // Create fullscreen overlay
        this.chessUI = document.createElement('div');
        this.chessUI.id = 'chess-game-ui';
        this.chessUI.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a0000, #2d0000, #1a0000);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-family: 'Courier New', monospace;
            color: #ff6666;
        `;
        
        // Game title
        const title = document.createElement('div');
        title.style.cssText = `
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 0 10px #ff0000;
            text-align: center;
        `;
        title.textContent = "DEVIL'S CHESS";
        this.chessUI.appendChild(title);
        
        // Create chess board container
        const boardContainer = document.createElement('div');
        boardContainer.style.cssText = `
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        `;
        
        // Create chess board
        this.boardElement = document.createElement('div');
        this.boardElement.id = 'chess-board';
        
        // Use voxel board background if available
        const backgroundStyle = this.voxelBoardImage ? 
            `background-image: url('${this.voxelBoardImage}'); background-size: contain; background-repeat: no-repeat; background-position: center;` :
            'background: #220000;';
            
        console.log('[ChessGame] Board background style:', backgroundStyle.substring(0, 100));
        
        // Calculate and log the expected square size for debugging
        const boardSize = Math.min(window.innerWidth * 0.7, window.innerHeight * 0.7);
        const squareSize = boardSize / 8;
        console.log('[ChessGame] Calculated board size:', boardSize, 'px, square size:', squareSize, 'px');
            
        this.boardElement.style.cssText = `
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            grid-template-rows: repeat(8, 1fr);
            width: min(70vw, 70vh);
            height: min(70vw, 70vh);
            aspect-ratio: 1;
            border: 2px solid #660000;
            box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
            ${backgroundStyle}
            image-rendering: pixelated;
            transition: all 0.3s ease;
            position: relative;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        `;
        
        boardContainer.appendChild(this.boardElement);
        this.chessUI.appendChild(boardContainer);
        
        // Create commentary area
        this.commentaryElement = document.createElement('div');
        this.commentaryElement.id = 'devil-commentary';
        this.commentaryElement.style.cssText = `
            width: min(70vw, 500px);
            height: 80px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #660000;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1em;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.3);
        `;
        this.chessUI.appendChild(this.commentaryElement);
        
        // Create game status indicator (hidden by default)
        this.statusElement = document.createElement('div');
        this.statusElement.id = 'chess-status';
        this.statusElement.style.cssText = `
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #660000;
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 1em;
            color: #ff6666;
            font-family: 'Courier New', monospace;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            pointer-events: none;
        `;
        this.statusVisible = false;
        this.updateGameStatus();
        this.chessUI.appendChild(this.statusElement);
        
        // Create exit button
        const exitButton = document.createElement('button');
        exitButton.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            background: #660000;
            color: #ff6666;
            border: 2px solid #ff0000;
            padding: 10px 20px;
            font-size: 1em;
            cursor: pointer;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            transition: all 0.3s;
        `;
        exitButton.textContent = 'FORFEIT';
        exitButton.addEventListener('click', () => this.forfeitGame());
        exitButton.addEventListener('mouseenter', () => {
            exitButton.style.background = '#990000';
            exitButton.style.boxShadow = '0 0 10px rgba(255, 0, 0, 0.5)';
        });
        exitButton.addEventListener('mouseleave', () => {
            exitButton.style.background = '#660000';
            exitButton.style.boxShadow = 'none';
        });
        this.chessUI.appendChild(exitButton);
        
        // Add subtle hint about Tab key
        const hintElement = document.createElement('div');
        hintElement.style.cssText = `
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 0.8em;
            color: rgba(255, 102, 102, 0.6);
            font-family: 'Courier New', monospace;
            text-align: right;
        `;
        hintElement.innerHTML = 'Press <strong>TAB</strong> for game info<br>Press <strong>ESC</strong> to forfeit';
        this.chessUI.appendChild(hintElement);
        
        // Add to document
        document.body.appendChild(this.chessUI);
        
        // Create board squares and pieces
        this.renderBoard();
        
        // Add event listeners for interaction
        this.addBoardEventListeners();
    }
    
    /**
     * Render the chess board and pieces
     */
    renderBoard() {
        this.boardElement.innerHTML = '';
        
        for (let y = 0; y < 8; y++) {
            for (let x = 0; x < 8; x++) {
                const square = document.createElement('div');
                square.className = 'chess-square';
                square.dataset.x = x;
                square.dataset.y = y;
                
                // Transparent squares to show voxel board underneath
                square.style.cssText = `
                    background: transparent;
                    border: none;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s;
                    position: relative;
                `;
                
                // Add piece if present
                const piece = this.board[x][y];
                if (piece) {
                    const pieceElement = this.createPieceElement(piece);
                    square.appendChild(pieceElement);
                }
                
                // Add selection highlighting overlay
                if (this.selectedSquare && this.selectedSquare.x === x && this.selectedSquare.y === y) {
                    const selectionOverlay = document.createElement('div');
                    selectionOverlay.className = 'selection-overlay';
                    selectionOverlay.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: rgba(255, 170, 0, 0.4);
                        border: 3px solid #ffaa00;
                        border-radius: 4px;
                        box-shadow: 0 0 15px rgba(255, 170, 0, 0.8);
                        pointer-events: none;
                        z-index: 1;
                        animation: pulse 1.5s infinite;
                    `;
                    square.appendChild(selectionOverlay);
                    
                    // Add pulsing animation
                    if (!document.getElementById('chess-animations')) {
                        const style = document.createElement('style');
                        style.id = 'chess-animations';
                        style.textContent = `
                            @keyframes pulse {
                                0%, 100% { opacity: 0.6; }
                                50% { opacity: 1; }
                            }
                        `;
                        document.head.appendChild(style);
                    }
                }
                
                // Add valid move highlighting
                if (this.validMoves.some(move => move.x === x && move.y === y)) {
                    const moveOverlay = document.createElement('div');
                    moveOverlay.className = 'move-overlay';
                    
                    if (!piece) {
                        // Empty square - show move indicator
                        moveOverlay.style.cssText = `
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 25%;
                            height: 25%;
                            background: rgba(0, 255, 0, 0.8);
                            border-radius: 50%;
                            border: 2px solid #00ff00;
                            box-shadow: 0 0 10px rgba(0, 255, 0, 0.6);
                            pointer-events: none;
                            z-index: 2;
                        `;
                    } else {
                        // Occupied square - show capture indicator
                        moveOverlay.style.cssText = `
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            background: rgba(255, 0, 0, 0.3);
                            border: 2px solid #ff0000;
                            border-radius: 4px;
                            box-shadow: 0 0 10px rgba(255, 0, 0, 0.6);
                            pointer-events: none;
                            z-index: 1;
                        `;
                    }
                    
                    square.appendChild(moveOverlay);
                }
                
                // Highlight last move
                if (this.lastMove && 
                    ((this.lastMove.from.x === x && this.lastMove.from.y === y) ||
                     (this.lastMove.to.x === x && this.lastMove.to.y === y))) {
                    square.style.boxShadow = 'inset 0 0 10px rgba(255, 255, 0, 0.4)';
                }
                
                this.boardElement.appendChild(square);
            }
        }
    }
    
    /**
     * Create a piece element with appropriate symbol
     */
    createPieceElement(piece) {
        const pieceElement = document.createElement('div');
        pieceElement.className = 'chess-piece';
        pieceElement.dataset.type = piece.type;
        pieceElement.dataset.color = piece.color;
        
        // Check if we have a rendered voxel image for this piece
        const pieceKey = `${piece.color}_${piece.type}`;
        const voxelImageUrl = this.voxelPieceImages && this.voxelPieceImages[pieceKey];
        
        console.log(`[ChessGame] Creating piece ${pieceKey}, voxel image available:`, !!voxelImageUrl);
        console.log(`[ChessGame] Voxel piece images:`, Object.keys(this.voxelPieceImages || {}));
        
        if (voxelImageUrl) {
            // Use 3D voxel piece image
            const img = document.createElement('img');
            img.src = voxelImageUrl;
            img.draggable = false;
            img.style.cssText = `
                width: 100%;
                height: 100%;
                object-fit: contain;
                pointer-events: none;
                image-rendering: pixelated;
                filter: drop-shadow(0 0 4px ${piece.color === 'white' ? '#ffffff88' : '#ff000088'});
            `;
            
            pieceElement.style.cssText = `
                width: 100%;
                height: 100%;
                cursor: ${piece.color === 'white' ? 'grab' : 'default'};
                user-select: none;
                transition: all 0.3s ease;
                z-index: 10;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                box-sizing: border-box;
            `;
            
            pieceElement.appendChild(img);
        } else {
            // Fallback to Unicode symbols if voxel images aren't available
            const symbols = {
                white: {
                    king: '♔', queen: '♕', rook: '♖', 
                    bishop: '♗', knight: '♘', pawn: '♙'
                },
                black: {
                    king: '♚', queen: '♛', rook: '♜', 
                    bishop: '♝', knight: '♞', pawn: '♟'
                }
            };
            
            pieceElement.style.cssText = `
                font-size: min(4vw, 4vh, 40px);
                cursor: ${piece.color === 'white' ? 'grab' : 'default'};
                user-select: none;
                text-shadow: 0 0 5px ${piece.color === 'white' ? '#ffffff' : '#ff0000'};
                transition: all 0.2s;
                z-index: 10;
            `;
            
            pieceElement.textContent = symbols[piece.color][piece.type];
        }
        
        return pieceElement;
    }
    
    /**
     * Add event listeners for board interaction
     */
    addBoardEventListeners() {
        // Mouse events
        this.boardElement.addEventListener('mousedown', (e) => this.handleInteractionStart(e));
        this.boardElement.addEventListener('mousemove', (e) => this.handleInteractionMove(e));
        this.boardElement.addEventListener('mouseup', (e) => this.handleInteractionEnd(e));
        
        // Touch events for mobile
        this.boardElement.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handleInteractionStart(e.touches[0]);
        });
        this.boardElement.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.handleInteractionMove(e.touches[0]);
        });
        this.boardElement.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.handleInteractionEnd(e.changedTouches[0]);
        });
    }
    
    /**
     * Handle start of interaction (mouse down / touch start)
     */
    handleInteractionStart(event) {
        if (this.currentPlayer !== 'white') return; // Only allow interaction on player's turn
        
        const square = event.target.closest('.chess-square');
        if (!square) return;
        
        const x = parseInt(square.dataset.x);
        const y = parseInt(square.dataset.y);
        const piece = this.board[x][y];
        
        if (piece && piece.color === 'white') {
            // Select own piece
            this.selectedSquare = { x, y };
            this.validMoves = this.getValidMoves(piece);
            this.renderBoard();
            
            // Start dragging
            this.isDragging = true;
            this.draggedPiece = square.querySelector('.chess-piece');
            
            const rect = square.getBoundingClientRect();
            this.dragOffset = {
                x: event.clientX - rect.left - rect.width / 2,
                y: event.clientY - rect.top - rect.height / 2
            };
        } else if (this.selectedSquare) {
            // Try to move to this square
            this.tryMove(this.selectedSquare, { x, y });
        }
    }
    
    /**
     * Handle interaction move (mouse move / touch move)
     */
    handleInteractionMove(event) {
        if (!this.isDragging || !this.draggedPiece) return;
        
        // Update piece position to follow cursor/touch
        const rect = this.boardElement.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        this.draggedPiece.style.position = 'absolute';
        this.draggedPiece.style.left = `${x - 32}px`; // Center the piece
        this.draggedPiece.style.top = `${y - 32}px`;
        this.draggedPiece.style.transform = 'scale(1.2) rotateZ(5deg)';
        this.draggedPiece.style.zIndex = '1000';
        this.draggedPiece.style.pointerEvents = 'none';
        
        // Add glow effect while dragging
        if (this.draggedPiece.querySelector('img')) {
            const img = this.draggedPiece.querySelector('img');
            img.style.filter = `drop-shadow(0 0 15px ${this.draggedPiece.dataset.color === 'white' ? '#ffffffaa' : '#ff0000aa'})`;
        }
    }
    
    /**
     * Handle end of interaction (mouse up / touch end)
     */
    handleInteractionEnd(event) {
        if (!this.isDragging) return;
        
        this.isDragging = false;
        
        if (this.draggedPiece && this.selectedSquare) {
            const square = document.elementFromPoint(event.clientX, event.clientY)?.closest('.chess-square');
            if (square) {
                const x = parseInt(square.dataset.x);
                const y = parseInt(square.dataset.y);
                
                // Reset piece styling and try the move
                this.resetPieceStyling(this.draggedPiece);
                this.tryMove(this.selectedSquare, { x, y });
            } else {
                // No valid target, reset piece and cancel selection
                this.resetPieceStyling(this.draggedPiece);
                this.selectedSquare = null;
                this.validMoves = [];
                this.renderBoard();
            }
        }
        
        this.draggedPiece = null;
    }
    
    /**
     * Reset piece styling after drag
     */
    resetPieceStyling(pieceElement) {
        pieceElement.style.position = '';
        pieceElement.style.left = '';
        pieceElement.style.top = '';
        pieceElement.style.transform = '';
        pieceElement.style.zIndex = '';
        pieceElement.style.pointerEvents = '';
        pieceElement.style.transition = '';
        
        // Reset image filter
        const img = pieceElement.querySelector('img');
        if (img) {
            img.style.filter = `drop-shadow(0 0 8px ${pieceElement.dataset.color === 'white' ? '#ffffff88' : '#ff000088'})`;
        }
    }
    
    /**
     * Create destruction particle effect when piece is captured
     */
    createDestructionEffect(pieceElement, targetSquare) {
        const boardRect = this.boardElement.getBoundingClientRect();
        const squareSize = boardRect.width / 8;
        const centerX = targetSquare.x * squareSize + squareSize / 2;
        const centerY = targetSquare.y * squareSize + squareSize / 2;
        
        // Create debris particles
        for (let i = 0; i < 12; i++) {
            const debris = document.createElement('div');
            debris.style.cssText = `
                position: absolute;
                left: ${centerX}px;
                top: ${centerY}px;
                width: 4px;
                height: 4px;
                background: ${pieceElement.dataset.color === 'white' ? '#ffffff' : '#660000'};
                border-radius: 2px;
                pointer-events: none;
                z-index: 999;
                box-shadow: 0 0 4px ${pieceElement.dataset.color === 'white' ? '#ffffff' : '#ff0000'};
            `;
            
            this.boardElement.appendChild(debris);
            
            // Animate debris
            const angle = (i / 12) * Math.PI * 2;
            const distance = 40 + Math.random() * 30;
            const finalX = centerX + Math.cos(angle) * distance;
            const finalY = centerY + Math.sin(angle) * distance;
            
            debris.style.transition = 'all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            debris.style.opacity = '0';
            debris.style.transform = `translate(${finalX - centerX}px, ${finalY - centerY}px) scale(0.1)`;
            
            // Remove debris after animation
            setTimeout(() => {
                if (debris.parentNode) {
                    debris.parentNode.removeChild(debris);
                }
            }, 800);
        }
        
        // Flash effect
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: absolute;
            left: ${centerX - 30}px;
            top: ${centerY - 30}px;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, ${pieceElement.dataset.color === 'white' ? '#ffffff' : '#ff0000'} 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 998;
            opacity: 0.8;
        `;
        
        this.boardElement.appendChild(flash);
        
        // Animate flash
        flash.style.transition = 'all 0.4s ease-out';
        flash.style.opacity = '0';
        flash.style.transform = 'scale(2)';
        
        setTimeout(() => {
            if (flash.parentNode) {
                flash.parentNode.removeChild(flash);
            }
        }, 400);
    }
    
    /**
     * Try to make a move from one square to another
     */
    async tryMove(from, to) {
        if (!this.isValidMove(from, to)) {
            this.selectedSquare = null;
            this.validMoves = [];
            this.renderBoard();
            return false;
        }
        
        // Make the move
        const capturedPiece = this.board[to.x][to.y];
        
        // Create destruction effect if capturing a piece
        if (capturedPiece) {
            const capturedSquare = this.boardElement.querySelector(`[data-x="${to.x}"][data-y="${to.y}"]`);
            const capturedPieceElement = capturedSquare?.querySelector('.chess-piece');
            if (capturedPieceElement) {
                this.createDestructionEffect(capturedPieceElement, to);
            }
        }
        
        const success = this.makeMove(from, to);
        if (success) {
            // Play appropriate sound (capture sound is played in makeMove)
            if (!capturedPiece) {
                this.playAudio('chess_move');
            }
            
            this.selectedSquare = null;
            this.validMoves = [];
            this.renderBoard();
            
            // Check game state
            await this.checkGameState();
            
            // Devil's turn
            if (this.gameState === 'playing' && this.currentPlayer === 'black') {
                await this.devilMove();
            }
        }
        
        return success;
    }
    
    /**
     * Check if a move is valid
     */
    isValidMove(from, to) {
        const piece = this.board[from.x][from.y];
        if (!piece) {
            console.warn('[ChessGame] No piece at source position for validation:', from);
            return false;
        }
        
        if (piece.color !== this.currentPlayer) {
            console.warn('[ChessGame] Cannot move opponent piece:', piece.color, 'current player:', this.currentPlayer);
            return false;
        }
        
        const validMoves = this.getValidMoves(from.x, from.y);
        const isValid = validMoves.some(move => move.x === to.x && move.y === to.y);
        
        if (!isValid) {
            console.warn('[ChessGame] Invalid move for', piece.type, 'from', from, 'to', to);
            console.warn('[ChessGame] Valid moves for this piece:', validMoves);
        }
        
        return isValid;
    }
    
    /**
     * Make a move on the board
     */
    makeMove(from, to) {
        const piece = this.board[from.x][from.y];
        if (!piece) {
            console.warn('[ChessGame] No piece at source position:', from);
            return false;
        }
        
        // Validate coordinates are on board
        if (!ChessLogic.isOnBoard(from.x, from.y) || !ChessLogic.isOnBoard(to.x, to.y)) {
            console.warn('[ChessGame] Move coordinates out of bounds:', from, to);
            return false;
        }
        
        // CRITICAL: Validate the move is legal according to chess rules
        if (!this.isValidMove(from, to)) {
            console.warn('[ChessGame] ❌ Invalid move attempted:', from, to, 'for piece:', piece.type, piece.color);
            return false;
        }
        
        console.log(`[ChessGame] ✅ Valid move: ${piece.color} ${piece.type} from (${from.x},${from.y}) to (${to.x},${to.y})`);
        
        const capturedPiece = this.board[to.x][to.y];
        
        // Handle special moves
        this.handleSpecialMoves(piece, from, to);
        
        // Move the piece
        this.board[to.x][to.y] = piece;
        this.board[from.x][from.y] = null;
        piece.x = to.x;
        piece.y = to.y;
        piece.hasMoved = true;
        
        // Update king position
        if (piece.type === 'king') {
            this.kingPositions[piece.color] = { x: to.x, y: to.y };
        }
        
        // Handle capture
        if (capturedPiece) {
            this.capturedPieces[capturedPiece.color].push(capturedPiece);
            // Play capture sound instead of regular move sound
            this.playAudio('chess_capture');
        }
        
        // Record move
        this.lastMove = { from: { ...from }, to: { ...to }, piece: piece.type, captured: capturedPiece?.type };
        this.moveHistory.push(this.lastMove);
        
        // Switch turns
        this.currentPlayer = this.currentPlayer === 'white' ? 'black' : 'white';
        if (this.currentPlayer === 'white') {
            this.fullMoveNumber++;
        }
        
        // Update game status
        this.updateGameStatus();
        
        console.log(`[ChessGame] ✅ Move completed: ${piece.type} from (${from.x},${from.y}) to (${to.x},${to.y})`);
        return true;
    }
    
    /**
     * Handle special chess moves (castling, en passant, pawn promotion)
     */
    handleSpecialMoves(piece, from, to) {
        // Castling
        if (piece.type === 'king' && Math.abs(to.x - from.x) === 2) {
            const isKingside = to.x > from.x;
            const rookFromX = isKingside ? 7 : 0;
            const rookToX = isKingside ? 5 : 3;
            const rook = this.board[rookFromX][from.y];
            
            this.board[rookToX][from.y] = rook;
            this.board[rookFromX][from.y] = null;
            rook.x = rookToX;
            rook.hasMoved = true;
            
            // Update castling rights
            this.castlingRights[piece.color].kingside = false;
            this.castlingRights[piece.color].queenside = false;
        }
        
        // En passant
        if (piece.type === 'pawn' && to.x !== from.x && !this.board[to.x][to.y]) {
            const capturedPawnY = piece.color === 'white' ? to.y + 1 : to.y - 1;
            const capturedPawn = this.board[to.x][capturedPawnY];
            if (capturedPawn && capturedPawn.type === 'pawn' && capturedPawn.color !== piece.color) {
                this.board[to.x][capturedPawnY] = null;
                this.capturedPieces[capturedPawn.color].push(capturedPawn);
            }
        }
        
        // Pawn promotion (auto-promote to queen for simplicity)
        if (piece.type === 'pawn' && (to.y === 0 || to.y === 7)) {
            piece.type = 'queen';
        }
        
        // Update en passant target
        if (piece.type === 'pawn' && Math.abs(to.y - from.y) === 2) {
            this.enPassantTarget = { x: to.x, y: (from.y + to.y) / 2 };
        } else {
            this.enPassantTarget = null;
        }
        
        // Update castling rights
        if (piece.type === 'king') {
            this.castlingRights[piece.color].kingside = false;
            this.castlingRights[piece.color].queenside = false;
        } else if (piece.type === 'rook') {
            if (from.x === 0) this.castlingRights[piece.color].queenside = false;
            if (from.x === 7) this.castlingRights[piece.color].kingside = false;
        }
    }
    
    /**
     * Get all valid moves for a piece
     */
    getValidMoves(pieceOrX, boardY = null) {
        // Handle both piece object and coordinate parameters
        let piece;
        if (typeof pieceOrX === 'object') {
            piece = pieceOrX;
        } else {
            // Called with coordinates
            piece = this.board[pieceOrX][boardY];
            if (!piece) return [];
        }
        
        return ChessLogic.getValidMoves(this.board, piece, this.kingPositions, this.castlingRights, this.enPassantTarget);
    }
    
    
    
    
    
    /**
     * Check game state (check, checkmate, stalemate)
     */
    async checkGameState() {
        const opponentColor = this.currentPlayer;
        const inCheck = ChessLogic.isInCheck(this.board, opponentColor, this.kingPositions);
        
        // Get all possible moves for current player
        const allMoves = [];
        for (let x = 0; x < 8; x++) {
            for (let y = 0; y < 8; y++) {
                const piece = this.board[x][y];
                if (piece && piece.color === opponentColor) {
                    const validMoves = this.getValidMoves(piece);
                    allMoves.push(...validMoves);
                }
            }
        }
        
        if (allMoves.length === 0) {
            if (inCheck) {
                this.gameState = 'checkmate';
                const winner = opponentColor === 'white' ? 'black' : 'white';
                console.log(`[ChessGame] Checkmate! ${winner} wins`);
                this.playAudio('chess_checkmate');
                await this.devilCommentary.showComment('checkmate', winner);
                await this.endGame(winner);
            } else {
                this.gameState = 'stalemate';
                console.log(`[ChessGame] Stalemate!`);
                this.playAudio('chess_stalemate');
                await this.devilCommentary.showComment('stalemate');
                await this.endGame('draw');
            }
        } else if (inCheck) {
            this.gameState = 'check';
            console.log(`[ChessGame] ${opponentColor} is in check`);
            this.playAudio('chess_check');
            this.updateGameStatus();
            await this.devilCommentary.showComment('check', opponentColor);
        } else {
            this.gameState = 'playing';
            this.updateGameStatus();
        }
    }
    
    /**
     * Devil makes a move (AI)
     */
    async devilMove() {
        console.log('[ChessGame] Devil is thinking...');
        
        // Show devil thinking commentary
        await this.devilCommentary.showComment('thinking');
        
        // Add thinking delay for drama
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        // Get AI move
        const move = this.ai.getBestMove(this.board, this.currentPlayer, this.difficulty);
        
        if (move && move.from && move.to && 
            ChessLogic.isOnBoard(move.from.x, move.from.y) && 
            ChessLogic.isOnBoard(move.to.x, move.to.y)) {
            
            console.log(`[ChessGame] Devil moves: ${move.piece} from (${move.from.x},${move.from.y}) to (${move.to.x},${move.to.y})`);
            
            // Check if it's a capture
            const isCapture = this.board[move.to.x][move.to.y] !== null;
            
            // Make the move
            const success = this.makeMove(move.from, move.to);
            if (success) {
                // Update board visuals based on mode
                if (this.chess3DBoard) {
                    // Update 3D board
                    this.chess3DBoard.movePiece(move.from.x, move.from.y, move.to.x, move.to.y);
                } else {
                    // Update 2D board
                    this.renderBoard();
                }
            } else {
                console.error('[ChessGame] Devil move failed, ending game');
                await this.endGame('white'); // Player wins if AI fails
                return;
            }
            
            // Devil commentary on the move
            if (isCapture) {
                await this.devilCommentary.showComment('capture', move);
            } else {
                await this.devilCommentary.showComment('move', move);
            }
            
            // Check game state
            await this.checkGameState();
        } else {
            console.error('[ChessGame] Devil could not find a valid move');
            await this.devilCommentary.showComment('forfeit');
            await this.endGame('white'); // Player wins if Devil can't move
        }
    }
    
    /**
     * Highlight valid moves for current piece
     */
    highlightValidMoves() {
        if (!this.boardElement || !this.isActive || this.currentPlayer !== 'white') {
            return;
        }
        
        try {
            // Highlight all player pieces that can move
            for (let x = 0; x < 8; x++) {
                for (let y = 0; y < 8; y++) {
                    const piece = this.board[x][y];
                    if (piece && piece.color === 'white') {
                        const moves = this.getValidMoves(piece);
                        if (moves.length > 0) {
                            const square = this.boardElement.children[y * 8 + x];
                            if (square) {
                                square.style.border = '2px solid #00ff00';
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('[ChessGame] Error highlighting moves:', error);
        }
    }
    
    /**
     * Forfeit the game
     */
    async forfeitGame() {
        console.log('[ChessGame] Player forfeited');
        await this.devilCommentary.showComment('forfeit');
        await this.endGame('black');
    }
    
    /**
     * End the game
     */
    async endGame(winner) {
        this.isActive = false;
        
        // Wait a moment for final commentary
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Complete cleanup (3D and UI)
        this.cleanup();
        
        // Create result object
        const result = {
            won: winner === 'white',
            difficulty: this.difficulty,
            winner: winner,
            moveCount: this.moveHistory.length
        };
        
        // Resolve the game promise with the result
        if (this.gameEndCallback) {
            this.gameEndCallback(result);
        }
        
        return result;
    }
    
    /**
     * Clean up UI and event listeners
     */
    cleanupUI() {
        // Remove chess UI if it exists (commentary overlay)
        const existingUI = document.getElementById('chess-game-ui');
        if (existingUI) {
            document.body.removeChild(existingUI);
        }
        
        const existingCommentary = document.getElementById('chess-commentary-overlay');
        if (existingCommentary) {
            document.body.removeChild(existingCommentary);
        }
        
        // Remove keyboard listeners
        if (this.keyboardHandler) {
            document.removeEventListener('keydown', this.keyboardHandler);
            this.keyboardHandler = null;
        }
        
        this.chessUI = null;
        this.boardElement = null;
        this.commentaryElement = null;
        this.statusElement = null;
    }

    /**
     * Complete cleanup including 3D elements
     */
    cleanup() {
        // Clean up 3D minigame
        if (this.chess3DMinigame) {
            this.chess3DMinigame.exitMinigame();
            this.chess3DMinigame = null;
        }
        
        // Clean up 3D chess board
        if (this.chess3DBoard) {
            this.chess3DBoard.cleanup();
            this.chess3DBoard = null;
        }

        // Restore original interaction handlers
        if (this.originalInteractionHandlers && this.sceneManager && this.sceneManager.activeSceneHandler) {
            const dungeonHandler = this.sceneManager.activeSceneHandler.dungeonHandler;
            if (dungeonHandler) {
                dungeonHandler.interactionHandlers = this.originalInteractionHandlers;
                this.originalInteractionHandlers = null;
            }
        }

        // Clean up raycasting
        this.chessRaycaster = null;
        this.chessMouse = null;

        // Clean up UI
        this.cleanupUI();

        console.log('[ChessGame] Complete cleanup performed');
    }
    
    /**
     * Update the game status indicator
     */
    updateGameStatus() {
        if (!this.statusElement) return;
        
        const playerTurn = this.currentPlayer === 'white' ? 'YOUR TURN' : 'DEVIL\'S TURN';
        const difficulty = this.difficulty.toUpperCase();
        const moveCount = Math.floor(this.moveHistory.length / 2) + 1;
        
        let statusText = `Turn: ${moveCount} | ${playerTurn}`;
        if (this.gameState === 'check') {
            statusText += ' | CHECK!';
        }
        statusText += `\nDifficulty: ${difficulty}`;
        
        this.statusElement.textContent = statusText;
        
        // Highlight when it's player's turn
        if (this.currentPlayer === 'white') {
            this.statusElement.style.borderColor = '#00ff00';
            this.statusElement.style.color = '#66ff66';
        } else {
            this.statusElement.style.borderColor = '#660000';
            this.statusElement.style.color = '#ff6666';
        }
    }
    
    /**
     * Add keyboard event listeners
     */
    addKeyboardListeners() {
        this.keyboardHandler = (event) => {
            if (event.key === 'Escape') {
                console.log('[ChessGame] Escape pressed, forfeiting game');
                this.forfeitGame();
            } else if (event.key === 'Tab') {
                event.preventDefault(); // Prevent default tab behavior
                this.toggleStatusDisplay();
            }
        };
        
        document.addEventListener('keydown', this.keyboardHandler);
    }
    
    /**
     * Toggle the status display visibility
     */
    toggleStatusDisplay() {
        if (!this.statusElement) return;
        
        this.statusVisible = !this.statusVisible;
        
        if (this.statusVisible) {
            this.statusElement.style.opacity = '1';
            this.statusElement.style.transform = 'translateY(0)';
        } else {
            this.statusElement.style.opacity = '0';
            this.statusElement.style.transform = 'translateY(-10px)';
        }
        
        console.log(`[ChessGame] Status display ${this.statusVisible ? 'shown' : 'hidden'}`);
    }
    
    /**
     * Play audio through the scene manager's audio system
     */
    playAudio(audioKey) {
        try {
            if (this.sceneManager && 
                this.sceneManager.activeSceneHandler && 
                this.sceneManager.activeSceneHandler.dungeonHandler && 
                this.sceneManager.activeSceneHandler.dungeonHandler.audioManager) {
                
                const audioManager = this.sceneManager.activeSceneHandler.dungeonHandler.audioManager;
                if (audioManager.playSound) {
                    console.log(`[ChessGame] Playing audio: ${audioKey}`);
                    audioManager.playSound(audioKey);
                } else {
                    console.warn(`[ChessGame] Audio manager has no playSound method`);
                }
            } else {
                console.warn(`[ChessGame] Audio system not available for sound: ${audioKey}`);
            }
        } catch (error) {
            console.error(`[ChessGame] Error playing audio ${audioKey}:`, error);
        }
    }
}

/**
 * Devil's Commentary System - Provides dark humor during chess
 */
class DevilCommentary {
    constructor() {
        this.lastCommentType = null;
        this.commentCount = 0;
    }
    
    async showComment(type, data = null) {
        const commentary = this.getCommentary(type, data);
        
        // Try 3D commentary overlay first, then fallback to old UI
        let element = document.getElementById('devil-commentary-3d');
        if (!element) {
            element = document.getElementById('devil-commentary');
        }
        
        if (element && commentary) {
            element.style.opacity = '0';
            element.textContent = commentary;
            
            // Fade in
            setTimeout(() => {
                element.style.opacity = '1';
            }, 100);
            
            // Auto-fade after delay
            setTimeout(() => {
                if (element.textContent === commentary) {
                    element.style.opacity = '0.5';
                }
            }, 4000);
        }
        
        console.log(`[DevilCommentary] ${commentary}`);
    }
    
    getCommentary(type, data) {
        this.commentCount++;
        
        const comments = {
            game_start: {
                easy: ["\"Let's see if you can handle a children's game, mortal.\""],
                normal: ["\"Interesting... you show some confidence.\"", "\"I do enjoy a proper challenge.\""],
                hard: ["\"Greedy, aren't we? This will be... educational.\"", "\"Your ambition exceeds your wisdom.\""],
                nightmare: ["\"FIFTY HEARTS? *laughs* Prepare for annihilation!\"", "\"Such greed... I will savor this destruction.\""]
            },
            
            thinking: [
                "\"Hmm... so many ways to destroy you...\"",
                "\"Which piece shall be your downfall?\"",
                "\"Patience, mortal. Perfection takes time.\"",
                "\"Your doom approaches with calculated precision.\"",
                "\"*chuckles darkly* This will hurt.\"",
                "\"Even chess masters weep before me.\"",
                "\"Your move was... quaint.\"",
                "\"I'm calculating seventeen moves ahead...\"",
                "\"Mortals think they understand strategy...\"",
                "\"*taps fingers impatiently* Soon...\"",
                "\"Your desperation is... amusing.\"",
                "\"I've played this exact position 1,000 times.\""
            ],
            
            move: [
                "\"Checkmate approaches, mortal.\"",
                "\"You cannot escape your fate.\"",
                "\"Each move tightens the noose.\"",
                "\"*grins* Beautiful, isn't it?\"",
                "\"Your pieces fall like autumn leaves.\"",
                "\"This is almost too easy.\"",
                "\"I've played this game for millennia.\"",
                "\"Perfect placement... you'll understand soon.\"",
                "\"*cracks knuckles* Your king trembles.\"",
                "\"Mortals always leave the same weaknesses...\"",
                "\"One step closer to your doom.\"",
                "\"This position... I've seen it in nightmares.\""
            ],
            
            capture: [
                "\"Another soul claimed...\"",
                "\"*devours piece* Delicious!\"",
                "\"Your army grows thin, mortal.\"",
                "\"Feed me more sacrifices!\"",
                "\"*laughs* Was that important?\"",
                "\"I collect chess pieces like souls.\"",
                "\"That piece served you poorly.\""
            ],
            
            player_good_move: [
                "\"Impressive... for a mortal.\"",
                "\"*raises eyebrow* Unexpected.\"",
                "\"You show promise... it won't save you.\"",
                "\"Hmm. Perhaps you're not hopeless.\"",
                "\"Clever... but I have seventeen counters.\"",
                "\"*nods approvingly* Still doomed, though.\""
            ],
            
            check: {
                white: ["\"Check! Feel the pressure, mortal.\"", "\"Your king trembles before me.\""],
                black: ["\"Impressive... but futile.\"", "\"A clever trick, but I've seen better.\""]
            },
            
            checkmate: {
                white: ["\"Victory! Your greed has been rewarded with defeat.\""],
                black: ["\"Impossible! How did you...? *seethes* Fine. Take your prize.\""]
            },
            
            stalemate: ["\"A draw? How... disappointing. No reward for you.\""],
            
            forfeit: ["\"Wise choice. Cowardice is better than annihilation.\""]
        };
        
        let commentArray = comments[type];
        
        if (typeof commentArray === 'object' && !Array.isArray(commentArray)) {
            // Handle nested comments (like difficulty-based or color-based)
            if (data && commentArray[data]) {
                commentArray = commentArray[data];
            } else {
                commentArray = Object.values(commentArray).flat();
            }
        }
        
        if (Array.isArray(commentArray)) {
            const index = Math.floor(Math.random() * commentArray.length);
            return commentArray[index];
        }
        
        return commentArray || "\"...\"";
    }
}

