import * as THREE from 'three';
import { EVENT_ROOMS, getAvailableEventRooms, getEventRoom, validateEventRoom } from '../gameData/eventRooms/index.js';
import { getPrefabFunction } from '../prefabs/prefabs.js';

/**
 * Event Room Manager
 * 
 * Handles:
 * - Event room selection and tracking
 * - Event room generation and placement
 * - Event room mechanics and interactions
 * - Event room state persistence
 */
export class EventRoomManager {
    constructor(dungeonHandler, audioManager) {
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;

        // Event room tracking
        this.usedEventRooms = new Set(); // Track used event rooms per game session
        this.currentFloorEventRoom = null; // Current floor's event room
        this.eventRoomStates = new Map(); // Track event room states
        this.roomHandlers = new Map(); // Store room handler instances (DEPRECATED - using lazy instantiation)

        // LAZY INSTANTIATION: Only create handler when player enters room
        this.activeRoomHandler = null; // Single active handler instance
        this.currentEventRoomId = null; // Track current event room ID

        // Event room data per floor
        this.floorEventRooms = new Map(); // floorNumber -> eventRoomKey

        console.log('[EventRoomManager] Initialized');
    }
    
    /**
     * Select a random unused event room for the current floor
     * @param {number} floorNumber - Current floor number
     * @returns {string|null} Event room key or null if none available
     */
    selectEventRoomForFloor(floorNumber, requiredEntranceDirection = null) {
        const availableRooms = getAvailableEventRooms();

        // Filter out already used rooms and invalid keys like 'handler'
        let unusedRooms = availableRooms.filter(roomKey =>
            !this.usedEventRooms.has(roomKey) &&
            roomKey !== 'handler' && // Explicitly exclude 'handler' key
            typeof roomKey === 'string' &&
            roomKey.length > 0
        );

        // If a specific entrance direction is required, filter by door availability
        if (requiredEntranceDirection) {
            console.log(`[EventRoomManager] Filtering event rooms for entrance direction: ${requiredEntranceDirection}`);
            
            unusedRooms = unusedRooms.filter(roomKey => {
                const roomData = getEventRoom(roomKey);
                if (!roomData || !roomData.availableConnections) {
                    console.warn(`[EventRoomManager] Room ${roomKey} has no door configuration, skipping`);
                    return false;
                }
                
                // Check if this room supports the required entrance
                const canEnterFrom = roomData.availableConnections[requiredEntranceDirection];
                console.log(`[EventRoomManager] Room ${roomKey} can be entered from ${requiredEntranceDirection}: ${canEnterFrom}`);
                return canEnterFrom;
            });
            
            console.log(`[EventRoomManager] ${unusedRooms.length} rooms available for entrance from ${requiredEntranceDirection}`);
        }

        if (unusedRooms.length === 0) {
            console.warn('[EventRoomManager] No unused event rooms available with required entrance');
            return null;
        }

        // Select random unused room
        const selectedRoom = unusedRooms[Math.floor(Math.random() * unusedRooms.length)];

        // Double-check that the selected room is valid
        if (selectedRoom === 'handler' || !getEventRoom(selectedRoom)) {
            console.error(`[EventRoomManager] Invalid room selected: ${selectedRoom}, retrying...`);
            // Remove invalid room from available list and try again
            const validRooms = unusedRooms.filter(id => id !== selectedRoom && id !== 'handler');
            if (validRooms.length === 0) {
                console.error('[EventRoomManager] No valid event rooms available after filtering');
                return null;
            }
            const fallbackRoom = validRooms[Math.floor(Math.random() * validRooms.length)];
            this.usedEventRooms.add(fallbackRoom);
            this.floorEventRooms.set(floorNumber, fallbackRoom);
            console.log(`[EventRoomManager] Selected fallback event room '${fallbackRoom}' for floor ${floorNumber}`);
            return fallbackRoom;
        }

        // Mark as used
        this.usedEventRooms.add(selectedRoom);
        this.floorEventRooms.set(floorNumber, selectedRoom);

        console.log(`[EventRoomManager] Selected event room '${selectedRoom}' for floor ${floorNumber}`);
        if (requiredEntranceDirection) {
            console.log(`[EventRoomManager] Room supports required entrance from ${requiredEntranceDirection}`);
        }
        return selectedRoom;
    }
    
    /**
     * Get event room for specific floor
     * @param {number} floorNumber - Floor number
     * @returns {string|null} Event room key
     */
    getEventRoomForFloor(floorNumber) {
        return this.floorEventRooms.get(floorNumber) || null;
    }

    /**
     * Get event room data by key
     * @param {string} eventRoomKey - Event room key
     * @returns {Object|null} Event room data
     */
    getEventRoomData(eventRoomKey) {
        return getEventRoom(eventRoomKey);
    }
    
    /**
     * Check if a room should be an event room during generation
     * @param {Object} roomData - Room data being generated
     * @param {number} floorNumber - Current floor number
     * @returns {string|null} Event room key if this should be an event room
     */
    checkForEventRoom(roomData, floorNumber) {
        // Skip Room 0 (starting room) and boss rooms
        if (roomData.id === 0 || roomData.type === 'Boss') {
            return null;
        }
        
        // Skip if secret room
        if (roomData.type === 'SECRET' || roomData.isSecret) {
            return null;
        }
        
        // Check if this floor already has an event room assigned
        const floorEventRoom = this.getEventRoomForFloor(floorNumber);
        if (!floorEventRoom) {
            return null;
        }
        
        // Check if this is the designated event room for this floor
        // For now, we'll use a simple approach: make the last normal room the event room
        const allRooms = Array.from(this.dungeonHandler.currentFloorLayout.values());
        const normalRooms = allRooms.filter(room => 
            room.type === 'Normal' && 
            room.id !== 0 && 
            !room.isSecret
        );
        
        // Make the highest ID normal room the event room
        const highestIdRoom = normalRooms.reduce((highest, room) => 
            room.id > highest.id ? room : highest, { id: -1 });
        
        if (roomData.id === highestIdRoom.id) {
            console.log(`[EventRoomManager] Room ${roomData.id} designated as event room: ${floorEventRoom}`);
            return floorEventRoom;
        }
        
        return null;
    }
    
    /**
     * Apply event room configuration to room data
     * @param {Object} roomData - Room data to modify
     * @param {string} eventRoomKey - Event room key
     */
    applyEventRoomConfig(roomData, eventRoomKey) {
        console.log(`[EventRoomManager] 🔧 Applying event room config '${eventRoomKey}' to room ${roomData.id}`);

        const eventRoom = getEventRoom(eventRoomKey);
        if (!eventRoom) {
            console.error(`[EventRoomManager] ❌ Event room '${eventRoomKey}' not found`);
            return;
        }

        console.log(`[EventRoomManager] ✅ Found event room data:`, eventRoom);

        // Validate event room data
        const validation = validateEventRoom(eventRoom);
        if (!validation.valid) {
            console.error(`[EventRoomManager] ❌ Invalid event room '${eventRoomKey}':`, validation.error);
            return;
        }

        console.log(`[EventRoomManager] ✅ Event room validation passed`);

        // Apply event room configuration
        roomData.type = 'EVENT';  // Use uppercase for consistency
        roomData.eventRoomKey = eventRoomKey;
        roomData.eventRoomName = eventRoom.name;
        roomData.shapeKey = eventRoom.shape;

        // Store event room data for generation
        roomData.eventRoomData = eventRoom;

        console.log(`[EventRoomManager] ✅ Applied event room config '${eventRoomKey}' to room ${roomData.id}`);
        console.log(`[EventRoomManager] Room data after config:`, {
            id: roomData.id,
            type: roomData.type,
            eventRoomKey: roomData.eventRoomKey,
            eventRoomName: roomData.eventRoomName,
            shapeKey: roomData.shapeKey,
            hasEventRoomData: !!roomData.eventRoomData
        });
    }
    
    /**
     * Generate event room visuals and mechanics
     * @param {THREE.Group} roomGroup - Room group to add objects to
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {Array} lights - Lights array
     * @param {Object} roomData - Room data
     */
    generateEventRoom(roomGroup, collisionMeshes, lights, roomData) {
        console.log(`[EventRoomManager] 🎯 generateEventRoom called for room ${roomData.id}`);
        console.log(`[EventRoomManager] Room data:`, roomData);
        console.log(`[EventRoomManager] Room type: ${roomData.type}`);
        console.log(`[EventRoomManager] Event room data exists: ${!!roomData.eventRoomData}`);
        console.log(`[EventRoomManager] 🔥 THIS IS AN EVENT ROOM BEING GENERATED! 🔥`);

        if (!roomData.eventRoomData) {
            console.error('[EventRoomManager] ❌ No event room data found in room data');
            console.error('[EventRoomManager] Available room data keys:', Object.keys(roomData));
            return;
        }

        const eventRoom = roomData.eventRoomData;
        console.log(`[EventRoomManager] ✅ Generating event room: ${eventRoom.name} (${eventRoom.id})`);
        console.log(`[EventRoomManager] 🎉 EVENT ROOM SHAPE: ${eventRoom.shape}`);

        // LAZY INSTANTIATION: Do NOT create room handler here
        // Handler will be created when player enters the room
        console.log(`[EventRoomManager] 🔄 Using lazy instantiation - handler will be created when player enters room`);

        // Apply custom lighting
        this.applyEventRoomLighting(roomGroup, lights, eventRoom);

        // Place event room objects
        this.placeEventRoomObjects(roomGroup, collisionMeshes, eventRoom, roomData);

        // Note: Event room mechanics are now handled by individual room handler classes

        // Initialize event room state
        this.initializeEventRoomState(roomData.id, eventRoom);

        console.log(`[EventRoomManager] 🎊 EVENT ROOM ${roomData.id} GENERATION COMPLETE! 🎊`);
    }

    /**
     * Convert room ID to filename (handle naming conventions)
     * @param {string} roomId - Room ID (e.g., "ominous_treasure")
     * @returns {string} Filename (e.g., "ominousTreasure.js")
     */
    convertRoomIdToFilename(roomId) {
        // Map of known room ID to filename conversions
        const roomIdToFilename = {
            'arcade_game': 'arcadeGame.js',
            'ominous_treasure': 'ominousTreasure.js',
            'mysterious_pond': 'mysteriousPond.js',
            'chronal_anomaly': 'chronalAnomaly.js',
            'haunted_library': 'hauntedLibrary.js',
            'crystal_cave': 'crystalCave.js',
            'devils_chess_room': 'devilsChessRoom.js',
            'eye_of_judgment': 'eyeOfJudgment.js',
            'guardians_of_lies': 'guardiansOfLies.js'
        };

        // Check if we have a specific mapping
        if (roomIdToFilename[roomId]) {
            return roomIdToFilename[roomId];
        }

        // Fallback: convert snake_case to camelCase and add .js
        const camelCase = roomId.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        return `${camelCase}.js`;
    }

    /**
     * Create room handler instance for the event room
     * @param {number} roomId - Room ID
     * @param {Object} eventRoom - Event room data
     */
    async createRoomHandler(roomId, eventRoom) {
        console.log(`[EventRoomManager] Creating room handler for room ${roomId}, event: ${eventRoom.id}`);

        try {
            // Convert room ID to filename (handle naming conventions)
            const filename = this.convertRoomIdToFilename(eventRoom.id);

            // Import the event room module with timeout to prevent hanging
            console.log(`[EventRoomManager] Importing module: ../gameData/eventRooms/${filename}`);
            
            const importPromise = import(`../gameData/eventRooms/${filename}`);
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Import timeout')), 10000)
            );
            
            const eventRoomModule = await Promise.race([importPromise, timeoutPromise]);

            console.log(`[EventRoomManager] Module imported:`, eventRoomModule);
            console.log(`[EventRoomManager] Module default:`, eventRoomModule.default);

            // Get the handler class from the module
            const handlerClass = eventRoomModule.default?.handler;

            if (handlerClass) {
                // Create handler instance
                const handler = new handlerClass(roomId, this.dungeonHandler, eventRoom);
                this.roomHandlers.set(roomId, handler);

                console.log(`[EventRoomManager] ✅ Created handler for room ${roomId}: ${handlerClass.name}`);
            } else {
                console.warn(`[EventRoomManager] ❌ No handler class found for event room: ${eventRoom.id}`);
                console.warn(`[EventRoomManager] Available keys in module.default:`, Object.keys(eventRoomModule.default || {}));
            }
        } catch (error) {
            console.error(`[EventRoomManager] ❌ Failed to create room handler for ${eventRoom.id}:`, error);
            // Room can still function without handler, just no special interactions
            console.warn(`[EventRoomManager] Room ${roomId} will function as basic room without event handler`);
        }
    }
    
    /**
     * Apply custom lighting for event room
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} lights - Lights array
     * @param {Object} eventRoom - Event room data
     */
    applyEventRoomLighting(roomGroup, lights, eventRoom) {
        if (!eventRoom.lighting) return;
        
        // Apply ambient lighting changes
        if (eventRoom.lighting.ambient) {
            // This would need to be handled at the scene level
            console.log('[EventRoomManager] Custom ambient lighting requested:', eventRoom.lighting.ambient);
        }
        
        // Add spotlight if specified
        if (eventRoom.lighting.spotlight) {
            const spotConfig = eventRoom.lighting.spotlight;
            const spotlight = new THREE.SpotLight(
                spotConfig.color,
                spotConfig.intensity,
                spotConfig.distance,
                spotConfig.angle,
                spotConfig.penumbra
            );
            
            spotlight.position.set(spotConfig.position.x, spotConfig.position.y, spotConfig.position.z);
            spotlight.target.position.set(spotConfig.target.x, spotConfig.target.y, spotConfig.target.z);
            spotlight.castShadow = spotConfig.castShadow || false;
            
            roomGroup.add(spotlight);
            roomGroup.add(spotlight.target);
            lights.push(spotlight);
            
            console.log('[EventRoomManager] Added spotlight to event room');
        }
        
        // Add point lights (like water glow)
        if (eventRoom.lighting.waterGlow) {
            const glowConfig = eventRoom.lighting.waterGlow;
            const pointLight = new THREE.PointLight(
                glowConfig.color,
                glowConfig.intensity,
                glowConfig.distance,
                glowConfig.decay
            );
            
            pointLight.position.set(glowConfig.position.x, glowConfig.position.y, glowConfig.position.z);
            roomGroup.add(pointLight);
            lights.push(pointLight);
            
            console.log('[EventRoomManager] Added water glow light to event room');
        }
    }
    
    /**
     * Place event room objects using local coordinates
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {Object} eventRoom - Event room data
     * @param {Object} roomData - Room data
     */
    placeEventRoomObjects(roomGroup, collisionMeshes, eventRoom, roomData) {
        if (!eventRoom.objects || eventRoom.objects.length === 0) return;
        
        console.log(`[EventRoomManager] Placing ${eventRoom.objects.length} event room objects`);
        
        eventRoom.objects.forEach((objConfig, index) => {
            try {
                // Create object based on type
                const objectGroup = this.createEventRoomObject(objConfig, roomData, eventRoom);
                
                if (objectGroup) {
                    // Apply position, rotation, and scale
                    objectGroup.position.set(objConfig.position.x, objConfig.position.y, objConfig.position.z);
                    
                    if (objConfig.rotation) {
                        objectGroup.rotation.set(objConfig.rotation.x, objConfig.rotation.y, objConfig.rotation.z);
                    }
                    
                    if (objConfig.scale) {
                        objectGroup.scale.set(objConfig.scale.x, objConfig.scale.y, objConfig.scale.z);
                    }
                    
                    // Add to room
                    roomGroup.add(objectGroup);
                    
                    // Add collision meshes and propagate destructible properties 
                    // Match the catacombs system: add parent group AND child meshes
                    if (objectGroup.userData?.isDestructible) {
                        // Add the parent group to collision array
                        collisionMeshes.push(objectGroup);
                        
                        // Also add child meshes for precise collision detection
                        objectGroup.traverse(child => {
                            if (child.isMesh) {
                                // Child meshes inherit destructible properties from parent
                                child.userData = child.userData || {};
                                child.userData.isDestructible = true;
                                child.userData.parentDestructible = objectGroup;
                                child.userData.objectType = objectGroup.userData.objectType;
                                child.userData.health = objectGroup.userData.health;
                                // CRITICAL FIX: Also propagate interactive properties
                                child.userData.isEventObject = objectGroup.userData.isEventObject;
                                child.userData.isInteractable = objectGroup.userData.isInteractable;
                                child.userData.eventRoomId = objectGroup.userData.eventRoomId;
                                child.userData.parentEventObject = objectGroup;
                                collisionMeshes.push(child);
                            }
                        });
                    } else {
                        // For non-destructible objects, add child meshes and propagate interaction properties
                        objectGroup.traverse(child => {
                            if (child.isMesh) {
                                // CRITICAL FIX: Propagate interactive properties to child meshes
                                child.userData = child.userData || {};
                                child.userData.isEventObject = objectGroup.userData.isEventObject;
                                child.userData.isInteractable = objectGroup.userData.isInteractable;
                                child.userData.eventRoomId = objectGroup.userData.eventRoomId;
                                child.userData.objectType = objectGroup.userData.objectType;
                                child.userData.parentEventObject = objectGroup;
                                collisionMeshes.push(child);
                            }
                        });
                    }
                    
                    console.log(`[EventRoomManager] Placed object ${index + 1}: ${objConfig.type}`);
                } else {
                    console.warn(`[EventRoomManager] Failed to create object: ${objConfig.type}`);
                }
            } catch (error) {
                console.error(`[EventRoomManager] Error placing object ${objConfig.type}:`, error);
            }
        });
    }
    
    /**
     * Create an event room object based on type
     *
     * ARCHITECTURE & HOW TO ADD NEW EVENT ROOMS:
     *
     * 1. CREATE NEW PREFABS (if needed):
     *    - Create new object files in src/generators/prefabs/ (e.g., magicAltarObject.js)
     *    - Follow existing prefab patterns (use shared.js, proper voxel data, materials)
     *    - Register in src/prefabs/prefabs.js under interior category
     *
     * 2. CREATE EVENT ROOM:
     *    - Copy template: cp _template.js newRoomName.js in src/gameData/eventRooms/
     *    - Edit room data: change id, name, shape, materials, lighting
     *    - Define objects array using prefab types (treasure_chest, egyptian_vase, etc.)
     *    - Set up mechanics (dialogue, enemy spawning, object interactions)
     *
     * 3. REGISTER ROOM:
     *    - Add to index.js in src/gameData/eventRooms/
     *    - Room automatically becomes available in game
     *
     * BENEFITS:
     * - ALL objects use prefab system for consistency and reusability
     * - EventRoomManager stays clean (only handles placement logic)
     * - Easy to maintain and scale to 100+ rooms with 1000s of objects
     * - Objects can be reused across multiple event rooms
     *
     * @param {Object} objConfig - Object configuration
     * @param {Object} roomData - Room data
     * @param {Object} eventRoom - Event room data
     * @returns {THREE.Group|null} Created object group
     */
    createEventRoomObject(objConfig, roomData, eventRoom) {
        console.log(`[EventRoomManager] Creating object type: ${objConfig.type}`);

        try {
            let objectGroup = null;

            // ALL objects now use the prefab system for consistency
            const prefabFunc = getPrefabFunction(objConfig.type, 'interior');

            if (prefabFunc) {
                // Prepare enhanced user data for event room objects
                const enhancedUserData = {
                    ...(objConfig.userData || {}),
                    // Event room specific properties
                    eventRoomId: roomData.id,
                    isEventObject: true,
                    isInteractable: true
                };

                // Special handling for treasure chests
                if (objConfig.type === 'treasure_chest') {
                    enhancedUserData.isEventChest = true;
                    enhancedUserData.isInteractable = true;
                    enhancedUserData.containsItem = true;

                    objectGroup = prefabFunc({
                        userData: enhancedUserData,
                        chestId: objConfig.userData?.chestId || `event_chest_${roomData.id}_${Date.now()}`
                    });
                } else {
                    // Standard prefab creation for all other objects
                    const result = prefabFunc({
                        userData: enhancedUserData,
                        seed: Math.random(),
                        // Pass destructible property as top-level option
                        isDestructible: objConfig.userData?.isDestructible,
                        health: objConfig.userData?.health
                    });
                    
                    // Handle special torch object format {group, flameLocalPosition}
                    if (objConfig.type === 'torch' && result && result.group) {
                        objectGroup = result.group;
                    } else {
                        objectGroup = result;
                    }
                }

                console.log(`[EventRoomManager] Created ${objConfig.type} using prefab system`);
            } else {
                console.error(`[EventRoomManager] Prefab not found for object type: ${objConfig.type}`);
                return null;
            }

            if (objectGroup) {
                // Apply common properties
                objectGroup.userData = {
                    ...objectGroup.userData,
                    ...objConfig.userData,
                    eventRoomObject: true,
                    objectType: objConfig.type,
                    isEventObject: true,
                    isInteractable: true,
                    // CRITICAL: Add event room ID here so it's available for interaction
                    eventRoomId: roomData.id
                };

                console.log(`[EventRoomManager] Successfully created object: ${objConfig.type}`);
                console.log(`[EventRoomManager] Object userData.isDestructible: ${objectGroup.userData.isDestructible}`);
                
                // Special logging for pillars
                if (objConfig.type === 'ancient_stone_pillar') {
                    console.log(`[EventRoomManager] 🏛️ ANCIENT STONE PILLAR CREATED:`);
                    console.log(`  - Position: (${objConfig.position.x}, ${objConfig.position.y}, ${objConfig.position.z})`);
                    console.log(`  - isDestructible: ${objectGroup.userData.isDestructible}`);
                    console.log(`  - Child mesh count: ${objectGroup.children.filter(c => c.isMesh).length}`);
                    console.log(`  - Added parent group to collision array (dungeon system)`);
                }
            }

            return objectGroup;

        } catch (error) {
            console.error(`[EventRoomManager] Error creating object ${objConfig.type}:`, error);
            return null;
        }
    }













    /**
     * Handle event room trigger (called from interaction systems)
     * @param {number} roomId - Room ID
     * @param {string} triggerId - Trigger object ID
     * @param {Object} triggerData - Additional trigger data
     */
    async handleEventTrigger(roomId, triggerId, triggerData = {}) {
        console.log(`[EventRoomManager] handleEventTrigger called - Room: ${roomId}, Trigger: ${triggerId}`);
        console.log(`[EventRoomManager] Current event room ID: ${this.currentEventRoomId}`);
        console.log(`[EventRoomManager] Active room handler exists: ${!!this.activeRoomHandler}`);

        // Use the active room handler (LAZY INSTANTIATION FIX)
        if (!this.activeRoomHandler) {
            console.warn(`[EventRoomManager] No active room handler found for room ${roomId}`);
            return;
        }

        // Verify we're in the correct room
        if (this.currentEventRoomId !== roomId) {
            console.warn(`[EventRoomManager] Room ID mismatch - Current: ${this.currentEventRoomId}, Requested: ${roomId}`);
            return;
        }

        // Delegate to active room handler
        try {
            await this.activeRoomHandler.handleInteraction(triggerId, triggerData);
        } catch (error) {
            console.error(`[EventRoomManager] Error handling interaction in room ${roomId}:`, error);
        }
    }



    /**
     * Handle enemy defeat in event room
     * @param {number} roomId - Room ID
     * @param {string} enemyId - Enemy ID that was defeated
     */
    handleEventEnemyDefeat(roomId, enemyId) {
        console.log(`[EventRoomManager] Enemy ${enemyId} defeated in event room ${roomId}`);
        console.log(`[EventRoomManager] Available room handlers:`, Array.from(this.roomHandlers.keys()));
        console.log(`[EventRoomManager] Active room handler:`, this.activeRoomHandler?.constructor.name);
        console.log(`[EventRoomManager] Current event room ID:`, this.currentEventRoomId);

        // Get room handler
        const roomHandler = this.roomHandlers.get(roomId);
        if (roomHandler) {
            console.log(`[EventRoomManager] Found room handler for ${roomId}, calling handleEnemyDefeat`);
            roomHandler.handleEnemyDefeat(enemyId);
        } else {
            console.warn(`[EventRoomManager] No room handler found for enemy defeat in room ${roomId}`);
            console.warn(`[EventRoomManager] Trying active room handler instead...`);
            if (this.activeRoomHandler) {
                console.log(`[EventRoomManager] Using active room handler: ${this.activeRoomHandler.constructor.name}`);
                this.activeRoomHandler.handleEnemyDefeat(enemyId);
            } else {
                console.error(`[EventRoomManager] No active room handler available either!`);
            }
        }
    }


    
    /**
     * Initialize event room state
     * @param {number} roomId - Room ID
     * @param {Object} eventRoom - Event room data
     */
    initializeEventRoomState(roomId, eventRoom) {
        const state = {
            triggered: false,
            enemiesSpawned: false,
            enemiesDefeated: false,
            dialogueCompleted: false,
            objectsRemoved: new Set(),
            chestsSpawned: new Set()
        };
        
        this.eventRoomStates.set(roomId, state);
        console.log(`[EventRoomManager] Initialized state for event room ${roomId}`);
    }
    
    /**
     * Get event room state
     * @param {number} roomId - Room ID
     * @returns {Object|null} Event room state
     */
    getEventRoomState(roomId) {
        return this.eventRoomStates.get(roomId) || null;
    }
    
    /**
     * Update event room state
     * @param {number} roomId - Room ID
     * @param {Object} updates - State updates
     */
    updateEventRoomState(roomId, updates) {
        const state = this.getEventRoomState(roomId);
        if (state) {
            Object.assign(state, updates);
            console.log(`[EventRoomManager] Updated state for room ${roomId}:`, updates);
        }
    }

    /**
     * Hide all doors in the current event room
     * @param {number} roomId - Room ID
     */
    hideEventRoomDoors(roomId) {
        console.log(`[EventRoomManager] 🚪❌ Hiding doors in event room ${roomId}`);

        if (!this.dungeonHandler || !this.dungeonHandler.doorTriggers) {
            console.warn(`[EventRoomManager] Cannot hide doors - dungeon handler not available`);
            return;
        }

        // Find all door triggers in the current room and disable them
        this.dungeonHandler.doorTriggers.forEach(trigger => {
            if (trigger.userData && trigger.userData.isDoorTrigger) {
                // Store original state if not already stored
                if (trigger.userData.originallyActive === undefined) {
                    trigger.userData.originallyActive = true;
                }

                // Disable the door trigger
                trigger.userData.isEventRoomBlocked = true;
                trigger.visible = false; // Hide the trigger

                console.log(`[EventRoomManager] 🚪❌ Blocked door trigger: ${trigger.name}`);
            }
        });

        // Also hide door visual elements
        if (this.dungeonHandler.scene) {
            // First look for door groups in the current room
            const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
            if (currentRoomGroup) {
                console.log(`[EventRoomManager] 🔍 Searching for doors in current room group`);
                
                currentRoomGroup.traverse((child) => {
                    if (child.userData && (child.userData.isDoor || child.userData.isArchway)) {
                        // Store original visibility
                        if (child.userData.originallyVisible === undefined) {
                            child.userData.originallyVisible = child.visible;
                        }

                        // Hide the door visual
                        child.visible = false;
                        child.userData.isEventRoomHidden = true;

                        // Also hide all child meshes
                        child.traverse((subChild) => {
                            if (subChild !== child && subChild.visible !== false) {
                                subChild.userData.originallyVisible = subChild.visible;
                                subChild.visible = false;
                            }
                        });

                        console.log(`[EventRoomManager] 🚪❌ Hidden door visual: ${child.name || child.type} (and all children)`);
                    }
                });
            } else {
                console.warn(`[EventRoomManager] No current room group found, searching entire scene`);
                
                // Fallback to searching entire scene
                this.dungeonHandler.scene.traverse((child) => {
                    if (child.userData && (child.userData.isDoor || child.userData.isArchway)) {
                        // Store original visibility
                        if (child.userData.originallyVisible === undefined) {
                            child.userData.originallyVisible = child.visible;
                        }

                        // Hide the door visual
                        child.visible = false;
                        child.userData.isEventRoomHidden = true;

                        console.log(`[EventRoomManager] 🚪❌ Hidden door visual: ${child.name || child.type}`);
                    }
                });
            }
        }

        console.log(`[EventRoomManager] 🚪❌ All doors hidden in event room ${roomId}`);
    }

    /**
     * Show all doors in the current event room
     * @param {number} roomId - Room ID
     */
    showEventRoomDoors(roomId) {
        console.log(`[EventRoomManager] 🚪✅ Showing doors in event room ${roomId}`);

        if (!this.dungeonHandler || !this.dungeonHandler.doorTriggers) {
            console.warn(`[EventRoomManager] Cannot show doors - dungeon handler not available`);
            return;
        }

        // Re-enable all door triggers that were blocked
        this.dungeonHandler.doorTriggers.forEach(trigger => {
            if (trigger.userData && trigger.userData.isEventRoomBlocked) {
                // Restore original state
                trigger.userData.isEventRoomBlocked = false;
                trigger.visible = trigger.userData.originallyActive !== false;

                console.log(`[EventRoomManager] 🚪✅ Restored door trigger: ${trigger.name}`);
            }
        });

        // Show door visual elements
        if (this.dungeonHandler.scene) {
            // First look for door groups in the current room
            const currentRoomGroup = this.dungeonHandler.currentRoomGroup;
            if (currentRoomGroup) {
                console.log(`[EventRoomManager] 🔍 Searching for doors to restore in current room group`);
                
                currentRoomGroup.traverse((child) => {
                    if (child.userData && child.userData.isEventRoomHidden) {
                        // Restore original visibility
                        child.visible = child.userData.originallyVisible !== false;
                        child.userData.isEventRoomHidden = false;

                        // Also restore all child meshes
                        child.traverse((subChild) => {
                            if (subChild !== child && subChild.userData.originallyVisible !== undefined) {
                                subChild.visible = subChild.userData.originallyVisible;
                                delete subChild.userData.originallyVisible;
                            }
                        });

                        console.log(`[EventRoomManager] 🚪✅ Restored door visual: ${child.name || child.type} (and all children)`);
                    }
                });
            } else {
                console.warn(`[EventRoomManager] No current room group found, searching entire scene`);
                
                // Fallback to searching entire scene
                this.dungeonHandler.scene.traverse((child) => {
                    if (child.userData && child.userData.isEventRoomHidden) {
                        // Restore original visibility
                        child.visible = child.userData.originallyVisible !== false;
                        child.userData.isEventRoomHidden = false;

                        console.log(`[EventRoomManager] 🚪✅ Restored door visual: ${child.name || child.type}`);
                    }
                });
            }
        }

        console.log(`[EventRoomManager] 🚪✅ All doors restored in event room ${roomId}`);
    }

    /**
     * Handle enemy spawn in event room - hide doors
     * @param {number} roomId - Room ID
     */
    onEventRoomEnemySpawn(roomId) {
        console.log(`[EventRoomManager] 👹 Enemy spawned in event room ${roomId} - hiding doors`);
        this.hideEventRoomDoors(roomId);
    }

    /**
     * Handle enemy defeat in event room - show doors
     * @param {number} roomId - Room ID
     */
    onEventRoomEnemyDefeat(roomId) {
        console.log(`[EventRoomManager] ✅ All enemies defeated in event room ${roomId} - showing doors`);
        this.showEventRoomDoors(roomId);
    }

    /**
     * LAZY INSTANTIATION: Create and activate room handler when player enters event room
     * @param {number} roomId - Room ID
     * @param {Object} roomData - Room data
     */
    async onEventRoomEnter(roomId, roomData) {
        console.log(`[EventRoomManager] 🚪➡️ Player entering event room ${roomId}`);
        console.log(`[EventRoomManager] Room data type:`, roomData?.type);
        console.log(`[EventRoomManager] Has event room data:`, !!roomData?.eventRoomData);

        // Clean up any existing active handler first
        if (this.activeRoomHandler) {
            console.log(`[EventRoomManager] 🧹 Cleaning up previous event room handler`);
            this.activeRoomHandler.cleanup();
            this.activeRoomHandler = null;
        }

        // Check if this is an event room (handle both 'EVENT' and 'Event' for consistency)
        if ((roomData.type !== 'EVENT' && roomData.type !== 'Event') || !roomData.eventRoomData) {
            console.log(`[EventRoomManager] ℹ️ Room ${roomId} is not an event room`);
            console.log(`[EventRoomManager] Room type: '${roomData.type}', Has eventRoomData: ${!!roomData.eventRoomData}`);
            return;
        }

        const eventRoom = roomData.eventRoomData;
        console.log(`[EventRoomManager] 🎯 Creating handler for event room: ${eventRoom.name} (${eventRoom.id})`);

        try {
            // Create room handler instance (lazy instantiation)
            this.activeRoomHandler = await this.createRoomHandlerLazy(roomId, eventRoom);
            this.currentEventRoomId = roomId;

            // Initialize the handler
            if (this.activeRoomHandler && this.activeRoomHandler.initialize) {
                console.log(`[EventRoomManager] 🔥 Initializing event room handler`);
                this.activeRoomHandler.initialize();
            }

            console.log(`[EventRoomManager] ✅ Event room handler activated for room ${roomId}`);
        } catch (error) {
            console.error(`[EventRoomManager] ❌ Failed to activate event room handler:`, error);
        }
    }

    /**
     * LAZY INSTANTIATION: Clean up and deactivate room handler when player leaves event room
     * @param {number} roomId - Room ID
     */
    onEventRoomExit(roomId) {
        console.log(`[EventRoomManager] 🚪⬅️ Player leaving event room ${roomId}`);

        // Clean up active handler
        if (this.activeRoomHandler) {
            console.log(`[EventRoomManager] 🧹 Cleaning up event room handler for room ${roomId}`);
            
            // Call cleanup method to stop timers and dispose resources
            this.activeRoomHandler.cleanup();
            this.activeRoomHandler = null;
            this.currentEventRoomId = null;

            console.log(`[EventRoomManager] ✅ Event room handler deactivated and cleaned up`);
        } else {
            console.log(`[EventRoomManager] ℹ️ No active handler to clean up`);
        }
    }

    /**
     * LAZY INSTANTIATION: Create room handler instance for the event room
     * @param {number} roomId - Room ID
     * @param {Object} eventRoom - Event room data
     * @returns {Object|null} Room handler instance or null if failed
     */
    async createRoomHandlerLazy(roomId, eventRoom) {
        console.log(`[EventRoomManager] 🏗️ Creating lazy room handler for room ${roomId}, event: ${eventRoom.id}`);

        try {
            // Convert room ID to filename (handle naming conventions)
            const filename = this.convertRoomIdToFilename(eventRoom.id);

            // Import the event room module with timeout to prevent hanging
            console.log(`[EventRoomManager] 📦 Importing module: ../gameData/eventRooms/${filename}`);
            
            const importPromise = import(`../gameData/eventRooms/${filename}`);
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Import timeout')), 10000)
            );
            
            const eventRoomModule = await Promise.race([importPromise, timeoutPromise]);

            console.log(`[EventRoomManager] ✅ Module imported:`, eventRoomModule);

            // Get the handler class from the module
            const handlerClass = eventRoomModule.default?.handler;

            if (handlerClass) {
                // Create handler instance
                const handler = new handlerClass(roomId, this.dungeonHandler, eventRoom);

                console.log(`[EventRoomManager] ✅ Created lazy handler for room ${roomId}: ${handlerClass.name}`);
                return handler;
            } else {
                console.warn(`[EventRoomManager] ❌ No handler class found for event room: ${eventRoom.id}`);
                console.warn(`[EventRoomManager] Available keys in module.default:`, Object.keys(eventRoomModule.default || {}));
                return null;
            }
        } catch (error) {
            console.error(`[EventRoomManager] ❌ Failed to create lazy room handler for ${eventRoom.id}:`, error);
            return null;
        }
    }

    /**
     * Handle interaction with objects in the current active event room
     * @param {string} objectId - Object ID (legacy)
     * @param {Object} context - Interaction context
     */
    async handleEventRoomInteraction(objectId, context) {
        if (!this.activeRoomHandler) {
            console.warn(`[EventRoomManager] No active event room handler for interaction with ${objectId}`);
            return;
        }

        if (this.activeRoomHandler.handleInteraction) {
            await this.activeRoomHandler.handleInteraction(objectId, context);
        } else {
            console.warn(`[EventRoomManager] Active handler does not support interactions`);
        }
    }

    /**
     * Handle interaction with objects using the new data-driven system
     * @param {THREE.Object3D} intersectedObject - The intersected object from raycaster
     * @param {Object} context - Interaction context
     */
    async handleEventRoomObjectInteraction(intersectedObject, context) {
        if (!this.activeRoomHandler) {
            console.warn(`[EventRoomManager] No active event room handler for object interaction`);
            return;
        }

        if (this.activeRoomHandler.handleObjectInteraction) {
            await this.activeRoomHandler.handleObjectInteraction(intersectedObject, context);
        } else {
            console.warn(`[EventRoomManager] Active handler does not support object interactions`);
        }
    }
}
