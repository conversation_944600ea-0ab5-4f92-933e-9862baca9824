import * as THREE from 'three';

/**
 * VoxelInstanceManager - Optimizes voxel rendering using InstancedMesh
 * Reduces draw calls from thousands to just a few per material
 */
export class VoxelInstanceManager {
    constructor() {
        // Map of material hex color to instance data
        this.instanceGroups = new Map();
        
        // Reusable objects for performance
        this.tempMatrix = new THREE.Matrix4();
        this.tempVector = new THREE.Vector3();
        this.tempQuaternion = new THREE.Quaternion();
        this.tempScale = new THREE.Vector3();
        this.tempColor = new THREE.Color();
    }

    /**
     * Add a voxel to be rendered
     * @param {THREE.Vector3} position - Position of the voxel
     * @param {number} size - Size of the voxel
     * @param {THREE.Material} material - Material to use
     * @param {THREE.Color} color - Optional color override
     */
    addVoxel(position, size, material, color = null) {
        // Use material UUID as key to ensure we group by actual material instance
        const materialKey = material.uuid;
        
        if (!this.instanceGroups.has(materialKey)) {
            this.instanceGroups.set(materialKey, {
                material: material,
                positions: [],
                scales: [],
                colors: []
            });
        }
        
        const group = this.instanceGroups.get(materialKey);
        group.positions.push(position.clone());
        group.scales.push(size);
        group.colors.push(color ? color.clone() : material.color.clone());
    }

    /**
     * Build all InstancedMesh objects from accumulated voxels
     * @returns {THREE.Group} Group containing all instanced meshes
     */
    build() {
        const group = new THREE.Group();
        
        // Shared geometry for all voxels
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        
        // Create an InstancedMesh for each material
        for (const [materialKey, data] of this.instanceGroups) {
            const count = data.positions.length;
            if (count === 0) continue;
            
            // Create instanced mesh
            const instancedMesh = new THREE.InstancedMesh(
                geometry,
                data.material,
                count
            );
            
            // Set up each instance
            for (let i = 0; i < count; i++) {
                // Set position and scale
                this.tempMatrix.makeScale(data.scales[i], data.scales[i], data.scales[i]);
                this.tempMatrix.setPosition(data.positions[i]);
                instancedMesh.setMatrixAt(i, this.tempMatrix);
                
                // Set color if material supports it
                if (data.material.vertexColors) {
                    instancedMesh.setColorAt(i, data.colors[i]);
                }
            }
            
            // Update instance matrices
            instancedMesh.instanceMatrix.needsUpdate = true;
            if (instancedMesh.instanceColor) {
                instancedMesh.instanceColor.needsUpdate = true;
            }
            
            // Enable shadows
            instancedMesh.castShadow = true;
            instancedMesh.receiveShadow = true;
            
            group.add(instancedMesh);
        }
        
        // Clear data after building
        this.clear();
        
        return group;
    }

    /**
     * Clear all accumulated voxel data
     */
    clear() {
        this.instanceGroups.clear();
    }

    /**
     * Create instanced mesh from an array of voxel data
     * @param {Array} voxels - Array of {position, size, color} objects
     * @param {THREE.Material} baseMaterial - Base material to use
     * @returns {THREE.Group} Group containing instanced meshes
     */
    static createFromVoxelArray(voxels, baseMaterial) {
        const manager = new VoxelInstanceManager();
        
        // Group voxels by color
        const colorGroups = new Map();
        
        voxels.forEach(voxel => {
            const colorKey = voxel.color.getHexString();
            if (!colorGroups.has(colorKey)) {
                colorGroups.set(colorKey, []);
            }
            colorGroups.get(colorKey).push(voxel);
        });
        
        // Create instanced mesh for each color group
        const group = new THREE.Group();
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        
        for (const [colorKey, voxelGroup] of colorGroups) {
            const count = voxelGroup.length;
            const material = baseMaterial.clone();
            material.color.setHex(parseInt(colorKey, 16));
            
            const instancedMesh = new THREE.InstancedMesh(geometry, material, count);
            
            voxelGroup.forEach((voxel, i) => {
                manager.tempMatrix.makeScale(voxel.size, voxel.size, voxel.size);
                manager.tempMatrix.setPosition(voxel.position);
                instancedMesh.setMatrixAt(i, manager.tempMatrix);
            });
            
            instancedMesh.instanceMatrix.needsUpdate = true;
            instancedMesh.castShadow = true;
            instancedMesh.receiveShadow = true;
            
            group.add(instancedMesh);
        }
        
        return group;
    }

    /**
     * Convert existing merged geometry to instanced mesh
     * @param {THREE.Mesh} mesh - Mesh with merged geometry
     * @returns {THREE.InstancedMesh} Optimized instanced mesh
     */
    static convertMergedToInstanced(mesh) {
        if (!mesh.geometry || !mesh.geometry.isBufferGeometry) {
            console.warn('Cannot convert non-buffer geometry to instanced mesh');
            return mesh;
        }
        
        // This is a placeholder for more complex conversion logic
        // In practice, merged geometry is already optimized
        return mesh;
    }
}

// Singleton instance for global use
export const voxelInstanceManager = new VoxelInstanceManager();