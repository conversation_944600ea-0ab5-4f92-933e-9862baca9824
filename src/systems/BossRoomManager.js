import * as THREE from 'three';
import { getPrefabFunction } from '../prefabs/prefabs.js';

/**
 * Boss Room Manager
 * 
 * Handles:
 * - Boss room generation and placement
 * - Boss room mechanics and interactions
 * - Boss room state persistence
 * - Boss spawning and boss battle management
 */
export class BossRoomManager {
    constructor(dungeonHand<PERSON>, audioManager) {
        this.dungeonHandler = dungeonHandler;
        this.audioManager = audioManager;

        // Boss room tracking
        this.activeBossRoomHandler = null; // Single active handler instance
        this.currentBossRoomId = null; // Track current boss room ID
        this.bossRoomStates = new Map(); // Track boss room states

        console.log('[<PERSON>RoomManager] Initialized');
    }
    
    /**
     * Get boss room configuration for an area
     * @param {string} areaKey - Area key
     * @returns {Object|null} Boss room configuration
     */
    getBossRoomConfig(areaKey) {
        // Define boss room configurations with objects
        const bossRoomConfigs = {
            'catacombs': {
                id: 'catacombs_boss_room',
                name: 'Catacombs Boss Chamber',
                type: 'boss',
                shape: 'BOSS_ARENA',
                // Door configuration - boss room only has south door
                availableConnections: {
                    north: false,   // Cannot be entered from north (no north door)
                    south: true,    // Can be entered from south (has south door)
                    east: false,    // Cannot be entered from east (no west door)
                    west: false     // Cannot be entered from west (no east door)
                },
                primaryEntrance: 'south', // Must enter from south
                // Door positions for the boss arena
                doorPositions: {
                    north: null,  // No door
                    south: { x: 0, y: 0, z: 28 },   // South door position for BOSS_ARENA
                    east: null,   // No door
                    west: null    // No door
                },
                // Lighting configuration - extremely dark to highlight monolith glow
                staticBrightness: 1, // Extremely dark (1/10) - darkest possible setting
                lighting: {
                    ambient: {
                        intensity: 0.05,  // Extremely low ambient light
                        color: 0x0a0a1f  // Very dark blue
                    },
                    // Remove torches for this room
                    removeTorches: true
                },
                // Define objects to place in the room (like event rooms)
                objects: [
                    // Three rune monoliths in triangle formation
                    {
                        type: 'rune_monolith',
                        position: { x: 0, y: 0, z: -15 }, // Top of triangle - increased radius
                        rotation: { x: 0, y: Math.PI, z: 0 },
                        userData: {
                            id: 'monolith_north',
                            monolithIndex: 0,
                            isInteractable: true
                        }
                    },
                    {
                        type: 'rune_monolith',
                        position: { x: 13, y: 0, z: 7.5 }, // Bottom right - increased radius
                        rotation: { x: 0, y: Math.PI / 3, z: 0 },
                        userData: {
                            id: 'monolith_southeast',
                            monolithIndex: 1,
                            isInteractable: true
                        }
                    },
                    {
                        type: 'rune_monolith',
                        position: { x: -13, y: 0, z: 7.5 }, // Bottom left - increased radius
                        rotation: { x: 0, y: -Math.PI / 3, z: 0 },
                        userData: {
                            id: 'monolith_southwest',
                            monolithIndex: 2,
                            isInteractable: true
                        }
                    }
                ]
            }
        };
        
        return bossRoomConfigs[areaKey] || {
            id: `${areaKey}_boss_room`,
            name: `${areaKey} Boss Chamber`,
            type: 'boss',
            shape: 'BOSS_ARENA',
            objects: []
        };
    }
    
    /**
     * Apply boss room configuration to room data
     * @param {Object} roomData - Room data to modify
     * @param {string} areaKey - Area key
     */
    applyBossRoomConfig(roomData, areaKey) {
        console.log(`[BossRoomManager] 🔧 Applying boss room config for area '${areaKey}' to room ${roomData.id}`);

        const bossConfig = this.getBossRoomConfig(areaKey);
        if (!bossConfig) {
            console.error(`[BossRoomManager] ❌ Boss room config not found for area '${areaKey}'`);
            return;
        }

        console.log(`[BossRoomManager] ✅ Found boss room config:`, bossConfig);

        // Apply boss room configuration
        roomData.type = 'Boss';  // Use uppercase for consistency
        roomData.bossRoomKey = bossConfig.id;
        roomData.bossRoomName = bossConfig.name;
        roomData.shapeKey = bossConfig.shape;
        roomData.areaKey = areaKey;

        // Store boss room data for generation
        roomData.bossRoomData = bossConfig;

        console.log(`[BossRoomManager] ✅ Applied boss room config '${bossConfig.id}' to room ${roomData.id}`);
    }
    
    /**
     * Generate boss room visuals and mechanics
     * @param {THREE.Group} roomGroup - Room group to add objects to
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {Array} lights - Lights array
     * @param {Object} roomData - Room data
     */
    generateBossRoom(roomGroup, collisionMeshes, lights, roomData) {
        console.log(`[BossRoomManager] 🎯 generateBossRoom called for room ${roomData.id}`);
        console.log(`[BossRoomManager] Room data:`, roomData);
        console.log(`[BossRoomManager] Room type: ${roomData.type}`);
        console.log(`[BossRoomManager] Boss room data exists: ${!!roomData.bossRoomData}`);
        console.log(`[BossRoomManager] 🔥 THIS IS A BOSS ROOM BEING GENERATED! 🔥`);

        if (!roomData.bossRoomData) {
            console.error('[BossRoomManager] ❌ No boss room data found in room data');
            return;
        }

        const bossRoom = roomData.bossRoomData;
        console.log(`[BossRoomManager] ✅ Generating boss room: ${bossRoom.name} (${bossRoom.id})`);
        console.log(`[BossRoomManager] 🎉 BOSS ROOM SHAPE: ${bossRoom.shape}`);
        console.log(`[BossRoomManager] 📦 Boss room objects:`, bossRoom.objects);

        // LAZY INSTANTIATION: Do NOT create room handler here
        // Handler will be created when player enters the room
        console.log(`[BossRoomManager] 🔄 Using lazy instantiation - handler will be created when player enters room`);

        // Apply custom lighting
        this.applyBossRoomLighting(roomGroup, lights, bossRoom);

        // Place boss room objects (following event room pattern exactly)
        this.placeBossRoomObjects(roomGroup, collisionMeshes, bossRoom, roomData);

        // Initialize boss room state
        this.initializeBossRoomState(roomData.id, bossRoom);

        console.log(`[BossRoomManager] 🎊 BOSS ROOM ${roomData.id} GENERATION COMPLETE! 🎊`);
    }

    /**
     * Apply custom lighting for boss room
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} lights - Lights array
     * @param {Object} bossRoom - Boss room data
     */
    applyBossRoomLighting(roomGroup, lights, bossRoom) {
        // Check if boss room has custom lighting configuration
        if (bossRoom.lighting) {
            console.log('[BossRoomManager] Applying custom lighting configuration');
            
            // Apply ambient lighting if specified
            if (bossRoom.lighting.ambient) {
                // Note: Ambient light is handled by DungeonHandler via staticBrightness
                // This is just for documentation
                console.log('[BossRoomManager] Boss room requests ambient:', bossRoom.lighting.ambient);
            }
            
            // Check if torches should be removed
            if (bossRoom.lighting.removeTorches) {
                console.log('[BossRoomManager] Boss room requests torch removal');
                // Torches are handled during room generation, this is just a flag
            }
            
            // The monolith lights themselves provide the primary illumination
            console.log('[BossRoomManager] Boss room lighting relies on monolith glow');
        } else {
            // Fallback to default dramatic lighting
            const centerLight = new THREE.PointLight(0x4a90e2, 0.5, 20);
            centerLight.position.set(0, 5, 0);
            roomGroup.add(centerLight);
            lights.push(centerLight);
            
            console.log('[BossRoomManager] Added default dramatic lighting to boss room');
        }
    }
    
    /**
     * Place boss room objects using local coordinates
     * @param {THREE.Group} roomGroup - Room group
     * @param {Array} collisionMeshes - Collision meshes array
     * @param {Object} bossRoom - Boss room data
     * @param {Object} roomData - Room data
     */
    placeBossRoomObjects(roomGroup, collisionMeshes, bossRoom, roomData) {
        // Check if boss room has objects defined
        if (!bossRoom.objects || bossRoom.objects.length === 0) {
            console.log(`[BossRoomManager] No predefined objects for boss room ${bossRoom.id}`);
            return;
        }
        
        console.log(`[BossRoomManager] Placing ${bossRoom.objects.length} boss room objects`);
        
        bossRoom.objects.forEach((objConfig, index) => {
            try {
                // Create object based on type
                const objectGroup = this.createBossRoomObject(objConfig, roomData, bossRoom);
                
                if (objectGroup) {
                    // Apply position, rotation, and scale
                    objectGroup.position.set(objConfig.position.x, objConfig.position.y, objConfig.position.z);
                    
                    if (objConfig.rotation) {
                        objectGroup.rotation.set(objConfig.rotation.x, objConfig.rotation.y, objConfig.rotation.z);
                    }
                    
                    if (objConfig.scale) {
                        objectGroup.scale.set(objConfig.scale.x, objConfig.scale.y, objConfig.scale.z);
                    }
                    
                    // Add to room group (this is the key - objects are added to the scene here)
                    roomGroup.add(objectGroup);
                    
                    // Add collision meshes
                    objectGroup.traverse(child => {
                        if (child.isMesh) {
                            // Propagate boss room properties to child meshes
                            child.userData = child.userData || {};
                            child.userData.isBossRoomObject = true;
                            child.userData.isInteractable = objectGroup.userData.isInteractable;
                            child.userData.bossRoomId = roomData.id;
                            child.userData.objectType = objectGroup.userData.objectType;
                            child.userData.parentBossObject = objectGroup;
                            
                            collisionMeshes.push(child);
                        }
                    });
                    
                    console.log(`[BossRoomManager] Placed object ${index + 1}: ${objConfig.type} at (${objConfig.position.x}, ${objConfig.position.y}, ${objConfig.position.z})`);
                } else {
                    console.warn(`[BossRoomManager] Failed to create object: ${objConfig.type}`);
                }
            } catch (error) {
                console.error(`[BossRoomManager] Error placing object ${objConfig.type}:`, error);
            }
        });
    }
    
    /**
     * Create a boss room object based on type
     * @param {Object} objConfig - Object configuration
     * @param {Object} roomData - Room data
     * @param {Object} bossRoom - Boss room data
     * @returns {THREE.Group|null} Created object group
     */
    createBossRoomObject(objConfig, roomData, bossRoom) {
        console.log(`[BossRoomManager] Creating object type: ${objConfig.type}`);

        try {
            let objectGroup = null;

            // Use the prefab system for consistency (same as event rooms)
            const prefabFunc = getPrefabFunction(objConfig.type, 'interior');

            if (prefabFunc) {
                // Prepare enhanced user data for boss room objects
                const enhancedUserData = {
                    ...(objConfig.userData || {}),
                    // Boss room specific properties
                    bossRoomId: roomData.id,
                    isBossRoomObject: true,
                    isInteractable: objConfig.isInteractable !== false,
                    objectType: objConfig.type
                };

                // Create the object using the prefab
                objectGroup = prefabFunc();

                // Update the userData after creation
                if (objectGroup) {
                    objectGroup.userData = {
                        ...objectGroup.userData,
                        ...enhancedUserData
                    };
                }

                console.log(`[BossRoomManager] Created ${objConfig.type} using prefab system`);
            } else {
                console.error(`[BossRoomManager] Prefab not found for object type: ${objConfig.type}`);
                return null;
            }

            return objectGroup;

        } catch (error) {
            console.error(`[BossRoomManager] Error creating object ${objConfig.type}:`, error);
            return null;
        }
    }

    /**
     * Initialize boss room state
     * @param {number} roomId - Room ID
     * @param {Object} bossRoom - Boss room data
     */
    initializeBossRoomState(roomId, bossRoom) {
        const state = {
            bossSpawned: false,
            bossDefeated: false,
            rewardsGiven: false,
            objectsSpawned: false
        };
        
        this.bossRoomStates.set(roomId, state);
        console.log(`[BossRoomManager] Initialized state for boss room ${roomId}`);
    }
    
    /**
     * Get boss room state
     * @param {number} roomId - Room ID
     * @returns {Object|null} Boss room state
     */
    getBossRoomState(roomId) {
        return this.bossRoomStates.get(roomId) || null;
    }
    
    /**
     * Update boss room state
     * @param {number} roomId - Room ID
     * @param {Object} updates - State updates
     */
    updateBossRoomState(roomId, updates) {
        const state = this.getBossRoomState(roomId);
        if (state) {
            Object.assign(state, updates);
            console.log(`[BossRoomManager] Updated state for room ${roomId}:`, updates);
        }
    }

    /**
     * Convert room ID to filename (handle naming conventions)
     * @param {string} roomId - Room ID (e.g., "catacombs_boss_room")
     * @returns {string} Filename (e.g., "catacombsBossRoom.js")
     */
    convertRoomIdToFilename(roomId) {
        // Map of known room ID to filename conversions
        const roomIdToFilename = {
            'catacombs_boss_room': 'catacombsBossRoom.js'
        };

        // Check if we have a specific mapping
        if (roomIdToFilename[roomId]) {
            return roomIdToFilename[roomId];
        }

        // Fallback: convert snake_case to camelCase and add .js
        const camelCase = roomId.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        return `${camelCase}.js`;
    }

    /**
     * LAZY INSTANTIATION: Create and activate room handler when player enters boss room
     * @param {number} roomId - Room ID
     * @param {Object} roomData - Room data
     */
    async onBossRoomEnter(roomId, roomData) {
        console.log(`[BossRoomManager] 🚪➡️ Player entering boss room ${roomId}`);
        console.log(`[BossRoomManager] Room data type:`, roomData?.type);
        console.log(`[BossRoomManager] Has boss room data:`, !!roomData?.bossRoomData);

        // Clean up any existing active handler first
        if (this.activeBossRoomHandler) {
            console.log(`[BossRoomManager] 🧹 Cleaning up previous boss room handler`);
            this.activeBossRoomHandler.cleanup();
            this.activeBossRoomHandler = null;
        }

        // Check if this is a boss room
        if (roomData.type !== 'Boss') {
            console.log(`[BossRoomManager] ℹ️ Room ${roomId} is not a boss room (type: ${roomData.type})`);
            return;
        }
        
        // Apply boss room config if not already done
        if (!roomData.bossRoomData && roomData.areaKey) {
            console.log(`[BossRoomManager] 🔧 Boss room data missing, applying config for area: ${roomData.areaKey}`);
            this.applyBossRoomConfig(roomData, roomData.areaKey);
        }
        
        if (!roomData.bossRoomData) {
            console.log(`[BossRoomManager] ❌ Failed to apply boss room config for room ${roomId}`);
            return;
        }

        const bossRoom = roomData.bossRoomData;
        console.log(`[BossRoomManager] 🎯 Creating handler for boss room: ${bossRoom.name} (${bossRoom.id})`);

        try {
            // Create room handler instance (lazy instantiation)
            this.activeBossRoomHandler = await this.createBossRoomHandlerLazy(roomId, bossRoom, roomData);
            this.currentBossRoomId = roomId;

            // Initialize the handler
            if (this.activeBossRoomHandler && this.activeBossRoomHandler.initialize) {
                console.log(`[BossRoomManager] 🔥 Initializing boss room handler`);
                this.activeBossRoomHandler.initialize();
            }

            console.log(`[BossRoomManager] ✅ Boss room handler activated for room ${roomId}`);
        } catch (error) {
            console.error(`[BossRoomManager] ❌ Failed to activate boss room handler:`, error);
        }
    }

    /**
     * LAZY INSTANTIATION: Clean up and deactivate room handler when player leaves boss room
     * @param {number} roomId - Room ID
     */
    onBossRoomExit(roomId) {
        console.log(`[BossRoomManager] 🚪⬅️ Player leaving boss room ${roomId}`);

        // Clean up active handler
        if (this.activeBossRoomHandler) {
            console.log(`[BossRoomManager] 🧹 Cleaning up boss room handler for room ${roomId}`);
            
            // Call cleanup method to stop timers and dispose resources
            this.activeBossRoomHandler.cleanup();
            this.activeBossRoomHandler = null;
            this.currentBossRoomId = null;

            console.log(`[BossRoomManager] ✅ Boss room handler deactivated and cleaned up`);
        } else {
            console.log(`[BossRoomManager] ℹ️ No active handler to clean up`);
        }
    }

    /**
     * LAZY INSTANTIATION: Create room handler instance for the boss room
     * @param {number} roomId - Room ID
     * @param {Object} bossRoom - Boss room data
     * @param {Object} roomData - Full room data
     * @returns {Object|null} Room handler instance or null if failed
     */
    async createBossRoomHandlerLazy(roomId, bossRoom, roomData) {
        console.log(`[BossRoomManager] 🏗️ Creating lazy room handler for room ${roomId}, boss: ${bossRoom.id}`);

        try {
            // Convert room ID to filename (handle naming conventions)
            const filename = this.convertRoomIdToFilename(bossRoom.id);

            // Import the boss room module with timeout to prevent hanging
            console.log(`[BossRoomManager] 📦 Importing module: ../gameData/bossRooms/${filename}`);
            
            const importPromise = import(`../gameData/bossRooms/${filename}`);
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Import timeout')), 10000)
            );
            
            const bossRoomModule = await Promise.race([importPromise, timeoutPromise]);

            console.log(`[BossRoomManager] ✅ Module imported:`, bossRoomModule);

            // Get the handler class from the module
            const HandlerClass = bossRoomModule.CatacombsBossRoom || bossRoomModule.default;

            if (HandlerClass) {
                // Create handler instance
                const handler = new HandlerClass(roomId, this.dungeonHandler, roomData);

                console.log(`[BossRoomManager] ✅ Created lazy handler for room ${roomId}: ${HandlerClass.name}`);
                return handler;
            } else {
                console.warn(`[BossRoomManager] ❌ No handler class found for boss room: ${bossRoom.id}`);
                return null;
            }
        } catch (error) {
            console.error(`[BossRoomManager] ❌ Failed to create lazy room handler for ${bossRoom.id}:`, error);
            return null;
        }
    }

    /**
     * Handle interaction with objects in the current active boss room
     * @param {THREE.Object3D} intersectedObject - The intersected object from raycaster
     * @param {Object} context - Interaction context
     */
    async handleBossRoomObjectInteraction(intersectedObject, context) {
        if (!this.activeBossRoomHandler) {
            console.warn(`[BossRoomManager] No active boss room handler for object interaction`);
            return;
        }

        if (this.activeBossRoomHandler.handleInteraction) {
            await this.activeBossRoomHandler.handleInteraction(intersectedObject, context);
        } else {
            console.warn(`[BossRoomManager] Active handler does not support interactions`);
        }
    }

    /**
     * Handle boss defeat
     * @param {number} roomId - Room ID
     * @param {string} bossId - Boss ID that was defeated
     */
    handleBossDefeat(roomId, bossId) {
        console.log(`[BossRoomManager] Boss ${bossId} defeated in room ${roomId}`);
        
        // Update state
        this.updateBossRoomState(roomId, { bossDefeated: true });
        
        // Notify active handler
        if (this.activeBossRoomHandler && this.activeBossRoomHandler.handleBossDefeat) {
            this.activeBossRoomHandler.handleBossDefeat(bossId);
        }
    }
}