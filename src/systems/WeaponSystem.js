import * as THREE from 'three';

/**
 * Weapon System - Manages player weapons and combat
 */
export class WeaponSystem {
    constructor(scene, player, audioManager) {
        this.scene = scene;
        this.player = player;
        this.audioManager = audioManager;
        
        // Current weapon state
        this.currentWeapon = null;
        this.weaponModel = null; // 3D model held by player
        
        // Combat state
        this.isAttacking = false;
        this.attackCooldown = 0;
        this.attackCooldownTime = 400; // 0.4 seconds between attacks (more reasonable fire rate)
        
        // Weapon attachment point on player
        this.weaponAttachPoint = new THREE.Group();
        this.weaponAttachPoint.name = 'weaponAttachPoint';
        
        // Position weapon attach point relative to right arm (hand position)
        this.weaponAttachPoint.position.set(0, -0.7, -0.1); // Further down the arm to hand position
        this.weaponAttachPoint.rotation.y = Math.PI / 8; // Slight angle for natural grip

        // Find and attach to the right arm group for animated movement
        this.rightArmGroup = null;
        if (this.player) {
            this.rightArmGroup = this.player.getObjectByName('rightArm');
            if (this.rightArmGroup) {
                this.rightArmGroup.add(this.weaponAttachPoint);
                console.log('[WeaponSystem] Weapon attach point added to right arm');
            } else {
                // Fallback to main player if no right arm found
                this.player.add(this.weaponAttachPoint);
                console.warn('[WeaponSystem] Right arm not found, attached to main player');
            }
        } else {
            console.warn('[WeaponSystem] Could not attach weapon point - no player');
        }
        
        // Initialize with default Soul Blast weapon
        this.initializeDefaultWeapon();
        
        console.log('[WeaponSystem] Initialized with Soul Blast as default weapon');
    }
    
    /**
     * Initialize default Soul Blast weapon
     */
    initializeDefaultWeapon() {
        const soulBlastWeapon = {
            id: 'soul_blast',
            name: 'Soul Blast',
            type: 'ranged',
            description: 'Your innate magical projectile ability',
            damage: 1,
            range: 50,
            attackSpeed: 1.0,
            model: null, // No physical model, uses soul orb effect
            attackPattern: {
                type: 'projectile',
                range: 50,
                spread: 0
            }
        };

        this.equipWeapon(soulBlastWeapon);
    }
    
    /**
     * Equip a new weapon
     */
    equipWeapon(weaponData) {
        console.log(`[WeaponSystem] Equipping weapon: ${weaponData.name} (${weaponData.type})`);
        console.log('[WeaponSystem] Weapon data:', weaponData);

        // Remove current weapon model if exists
        this.removeCurrentWeaponModel();

        // Set new weapon
        this.currentWeapon = weaponData;

        // Add weapon model for melee weapons
        if (weaponData.type === 'melee') {
            if (weaponData.model) {
                console.log('[WeaponSystem] Attaching melee weapon model');
                this.attachWeaponModel(weaponData.model);
            } else {
                console.warn('[WeaponSystem] Melee weapon has no model to attach');
            }
        } else {
            console.log('[WeaponSystem] Ranged weapon - no model to attach');
        }

        console.log(`[WeaponSystem] Successfully equipped weapon: ${weaponData.name} (${weaponData.type})`);
    }
    
    /**
     * Attach weapon 3D model to player
     */
    attachWeaponModel(weaponModel) {
        if (!weaponModel) {
            console.warn('[WeaponSystem] No weapon model provided for attachment');
            return;
        }

        // Clone the weapon model
        this.weaponModel = weaponModel.clone();
        this.weaponModel.name = 'playerWeapon';

        // Scale down weapon for player holding (slightly smaller for better proportions)
        this.weaponModel.scale.setScalar(0.7);

        // Position weapon in hand (relative to arm attach point)
        this.weaponModel.position.set(-0.1, 0, -0.2); // Centered in hand with adjusted grip offset

        // Rotate weapon for natural sword holding position - FINAL: X=80°, Y=90°
        this.weaponModel.rotation.set(
            THREE.MathUtils.degToRad(80),   // X: 80° forward tilt for sword angle
            THREE.MathUtils.degToRad(90),   // Y: 90° rotation for proper orientation
            0                               // Z: No side angle - straight grip
        );

        // Add to weapon attach point
        this.weaponAttachPoint.add(this.weaponModel);

        console.log('[WeaponSystem] Attached weapon model to player at attach point');
        console.log('[WeaponSystem] Weapon position:', this.weaponModel.position);
        console.log('[WeaponSystem] Weapon rotation:', this.weaponModel.rotation);
    }
    
    /**
     * Remove current weapon model
     */
    removeCurrentWeaponModel() {
        if (this.weaponModel) {
            this.weaponAttachPoint.remove(this.weaponModel);
            this.weaponModel = null;
        }
    }
    
    /**
     * Get current weapon info
     */
    getCurrentWeapon() {
        return this.currentWeapon;
    }
    
    /**
     * Check if current weapon is ranged
     */
    isRangedWeapon() {
        return this.currentWeapon && this.currentWeapon.type === 'ranged';
    }
    
    /**
     * Check if current weapon is melee
     */
    isMeleeWeapon() {
        return this.currentWeapon && this.currentWeapon.type === 'melee';
    }
    
    /**
     * Check if player can attack (not in cooldown)
     */
    canAttack() {
        const canAttack = !this.isAttacking && this.attackCooldown <= 0;
        if (!canAttack) {
            console.log(`[WeaponSystem] Cannot attack - isAttacking: ${this.isAttacking}, cooldown: ${this.attackCooldown}ms`);
        }
        return canAttack;
    }
    
    /**
     * Perform attack based on current weapon type
     */
    attack(playerController) {
        if (!this.currentWeapon) {
            console.log('[WeaponSystem] No current weapon');
            return false;
        }

        console.log(`[WeaponSystem] Attack called - weapon: ${this.currentWeapon.name}, type: ${this.currentWeapon.type}`);

        // For ranged weapons, check our own cooldown too
        if (this.currentWeapon.type === 'ranged') {
            if (!this.canAttack()) {
                return false; // Don't attack if on cooldown
            }
            return this.performRangedAttack(playerController);
        }
        // For melee weapons, check our own cooldown
        else if (this.currentWeapon.type === 'melee') {
            if (!this.canAttack()) {
                return false; // Don't attack if on cooldown
            }
            console.log('[WeaponSystem] Performing melee attack');
            return this.performMeleeAttack(playerController);
        }

        return false;
    }
    
    /**
     * Perform ranged attack (Soul Blast)
     */
    performRangedAttack(playerController) {
        // Use existing projectile system
        if (playerController && playerController.shootProjectile) {
            const success = playerController.shootProjectile();
            if (success) {
                this.startAttackCooldown();
            }
            return success;
        }
        return false;
    }
    
    /**
     * Perform melee attack
     */
    performMeleeAttack(playerController) {
        if (!playerController) {
            console.log('[WeaponSystem] No playerController provided');
            return false;
        }

        this.isAttacking = true;
        console.log('[WeaponSystem] Set isAttacking = true');

        try {
            // Play melee attack animation
            this.playMeleeAnimation();

            // Detect targets in front of player (enemies and destructibles)
            console.log('[WeaponSystem] About to detect melee targets');
            let hitTargets = [];
            try {
                hitTargets = this.detectMeleeTargets(playerController);
                console.log('[WeaponSystem] detectMeleeTargets returned:', hitTargets);
            } catch (detectionError) {
                console.error('[WeaponSystem] Error in detectMeleeTargets:', detectionError);
                hitTargets = [];
            }

            // Deal damage to hit targets (enemies and destructibles)
            if (hitTargets.length > 0) {
                console.log(`[WeaponSystem] Processing ${hitTargets.length} hit targets`);
                hitTargets.forEach(target => {
                    if (target.type === 'enemy') {
                        this.dealMeleeDamage(target.object);
                    } else if (target.type === 'destructible') {
                        this.dealDestructibleDamage(target.object);
                    }
                });
            } else {
                console.log('[WeaponSystem] No targets hit - checking why...');
            }

            // Play attack sound
            this.playAttackSound();

            // Start cooldown
            this.startAttackCooldown();

            console.log(`[WeaponSystem] Melee attack completed, hit ${hitTargets.length} targets, cooldown: ${this.attackCooldown}ms`);
        } catch (error) {
            console.error('[WeaponSystem] Error during melee attack:', error);
        }

        // End attack after animation - use a more reliable method
        setTimeout(() => {
            this.isAttacking = false;
            console.log('[WeaponSystem] Set isAttacking = false (attack finished)');
        }, 300); // 300ms attack animation

        return true; // Always return true for successful melee attack attempt
    }
    
    /**
     * Play melee attack animation with hand movement
     */
    playMeleeAnimation() {
        if (!this.weaponModel || !this.rightArmGroup) return;

        // Store original rotations
        const originalWeaponRotation = this.weaponModel.rotation.z;
        const originalArmRotationX = this.rightArmGroup.rotation.x;

        // Define swing parameters
        const weaponSwingAngle = Math.PI / 3; // 60 degree weapon swing
        const armSwingAngle = Math.PI / 4; // 45 degree arm swing on X-axis (forward/backward like walk animation)

        // Animation settings
        const duration = 300;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Create swing motion: fast forward, slower back
            let swingProgress;
            if (progress < 0.4) {
                // Fast forward swing (0 to 1 in first 40% of time)
                swingProgress = progress / 0.4;
            } else {
                // Slower return (1 to 0 in remaining 60% of time)
                swingProgress = 1 - ((progress - 0.4) / 0.6);
            }

            // Apply easing for more natural motion
            const easedProgress = swingProgress * swingProgress * (3 - 2 * swingProgress); // Smoothstep

            // Animate weapon swing
            this.weaponModel.rotation.z = originalWeaponRotation + (weaponSwingAngle * easedProgress);

            // Animate arm rotation on X-axis (forward/backward like walk animation)
            this.rightArmGroup.rotation.x = originalArmRotationX + (armSwingAngle * easedProgress);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Reset to original rotations
                this.weaponModel.rotation.z = originalWeaponRotation;
                this.rightArmGroup.rotation.x = originalArmRotationX;
            }
        };

        animate();
    }
    
    /**
     * Detect enemies and destructible objects based on weapon attack pattern
     */
    detectMeleeTargets(playerController) {
        const hitTargets = [];
        const playerPosition = playerController.playerMesh.position;
        const playerRotation = playerController.playerMesh.rotation;

        // Get weapon attack pattern or use default
        const attackPattern = this.currentWeapon.attackPattern || {
            type: 'frontal_arc',
            range: 3.0, // Reduced melee range for more realistic sword reach
            angle: 90, // 90-degree frontal arc
            width: 2.0
        };

        console.log(`[WeaponSystem] Current weapon:`, this.currentWeapon);
        console.log(`[WeaponSystem] Using attack pattern:`, attackPattern);

        console.log(`[WeaponSystem] Detecting targets with pattern:`, attackPattern);
        console.log(`[WeaponSystem] Player position:`, playerPosition);
        console.log(`[WeaponSystem] Player rotation:`, playerRotation);

        let totalEnemiesFound = 0;
        let totalDestructiblesFound = 0;
        let targetsInRange = 0;
        let totalObjectsChecked = 0;

        // Check all targets in scene (enemies and destructible objects)
        this.scene.traverse(object => {
            totalObjectsChecked++;

            // Check for enemies by name pattern (Enemy_*) or userData.isEnemy
            const isEnemyByName = object.name && object.name.startsWith('Enemy_');
            const isEnemyByUserData = object.userData?.isEnemy;
            const isEnemy = isEnemyByName || isEnemyByUserData;

            // Check for destructible objects
            const isDestructible = object.userData?.isDestructible;

            if (isEnemy) {
                totalEnemiesFound++;
                console.log(`[WeaponSystem] Found enemy:`, object.name, 'health:', object.userData?.health || object.health);

                // Check if enemy has health (either in userData or as a property)
                const enemyHealth = object.userData?.health || object.health;

                if (enemyHealth && enemyHealth > 0) {
                    const distance = playerPosition.distanceTo(object.position);
                    console.log(`[WeaponSystem] Checking enemy ${object.name} at distance ${distance.toFixed(2)}`);

                    if (this.isTargetInAttackPattern(object.position, playerPosition, playerRotation, attackPattern)) {
                        targetsInRange++;
                        hitTargets.push({ object, type: 'enemy' });
                        console.log(`[WeaponSystem] ✅ Enemy HIT - distance: ${distance.toFixed(2)}, health: ${enemyHealth}, pattern: ${attackPattern.type}`);
                    } else {
                        console.log(`[WeaponSystem] ❌ Enemy MISSED - distance: ${distance.toFixed(2)}, pattern: ${attackPattern.type}`);
                    }
                }
            } else if (isDestructible) {
                totalDestructiblesFound++;
                console.log(`[WeaponSystem] Found destructible:`, object.name || object.userData.objectType, 'health:', object.userData.health);

                // Check if destructible has health
                const objectHealth = object.userData.health;

                if (objectHealth && objectHealth > 0) {
                    if (this.isTargetInAttackPattern(object.position, playerPosition, playerRotation, attackPattern)) {
                        targetsInRange++;
                        hitTargets.push({ object, type: 'destructible' });
                        const distance = playerPosition.distanceTo(object.position);
                        console.log(`[WeaponSystem] Destructible HIT - distance: ${distance.toFixed(2)}, health: ${objectHealth}, pattern: ${attackPattern.type}`);
                    }
                }
            }
        });

        console.log(`[WeaponSystem] Melee detection summary:`);
        console.log(`  - Total objects checked: ${totalObjectsChecked}`);
        console.log(`  - Total enemies in scene: ${totalEnemiesFound}`);
        console.log(`  - Total destructibles in scene: ${totalDestructiblesFound}`);
        console.log(`  - Targets in range (${attackPattern.range}): ${targetsInRange}`);
        console.log(`  - Total hits: ${hitTargets.length}`);

        return hitTargets;
    }

    /**
     * Check if a target is within the weapon's attack pattern
     * @param {THREE.Vector3} targetPosition - Position of the target
     * @param {THREE.Vector3} playerPosition - Position of the player
     * @param {THREE.Euler} playerRotation - Rotation of the player
     * @param {Object} attackPattern - Weapon attack pattern configuration
     * @returns {boolean} True if target is within attack pattern
     */
    isTargetInAttackPattern(targetPosition, playerPosition, playerRotation, attackPattern) {
        const distance = playerPosition.distanceTo(targetPosition);

        console.log(`[WeaponSystem] Pattern check - distance: ${distance.toFixed(2)}, max range: ${attackPattern.range}`);

        // First check if target is within range
        if (distance > attackPattern.range) {
            console.log(`[WeaponSystem] Target out of range: ${distance.toFixed(2)} > ${attackPattern.range}`);
            return false;
        }

        // TEMPORARY: Add fallback for testing
        let result = false;

        switch (attackPattern.type) {
            case 'frontal_arc':
                result = this.isInFrontalArc(targetPosition, playerPosition, playerRotation, attackPattern);
                break;

            case 'circular':
                result = this.isInCircularArea(targetPosition, playerPosition, attackPattern);
                break;

            case 'line':
                result = this.isInLineAttack(targetPosition, playerPosition, playerRotation, attackPattern);
                break;

            case 'cone':
                result = this.isInConeAttack(targetPosition, playerPosition, playerRotation, attackPattern);
                break;

            default:
                console.warn(`[WeaponSystem] Unknown attack pattern: ${attackPattern.type}`);
                result = distance <= attackPattern.range; // Fallback to simple distance
                break;
        }

        // NO FALLBACK - strict directional detection only
        console.log(`[WeaponSystem] Final pattern result: ${result ? 'HIT' : 'MISS'}`);

        return result;
    }

    /**
     * Check if target is in frontal arc (sword swing)
     */
    isInFrontalArc(targetPosition, playerPosition, playerRotation, attackPattern) {
        // CRITICAL FIX: Use 2D collision detection for melee attacks (ignore Y height differences)
        // This makes it much easier to hit enemies at different heights with melee attacks
        const playerX = playerPosition.x;
        const playerZ = playerPosition.z;
        const targetX = targetPosition.x;
        const targetZ = targetPosition.z;

        // Calculate 2D distance (X and Z only)
        const horizontalDistance = Math.sqrt(
            Math.pow(playerX - targetX, 2) +
            Math.pow(playerZ - targetZ, 2)
        );

        // Check if target is within melee range (2D distance only)
        if (horizontalDistance > attackPattern.range) {
            console.log(`[WeaponSystem] Target out of 2D range: ${horizontalDistance.toFixed(2)} > ${attackPattern.range}`);
            return false;
        }

        // Don't auto-hit extremely close targets
        if (horizontalDistance < 0.1) {
            console.log(`[WeaponSystem] Target too close horizontally (${horizontalDistance.toFixed(2)}), rejecting`);
            return false;
        }

        // Calculate 2D direction vector (ignore Y)
        const toTarget = new THREE.Vector3(targetX - playerX, 0, targetZ - playerZ);
        toTarget.normalize();

        // Calculate player's forward direction - TESTING: Try positive Z as forward
        const playerForward = new THREE.Vector3(0, 0, 1); // INVERTED: Try positive Z as forward

        // CRITICAL FIX: Only use Y rotation for horizontal direction
        const yRotation = playerRotation.y;
        playerForward.applyAxisAngle(new THREE.Vector3(0, 1, 0), yRotation);
        playerForward.y = 0; // Ensure horizontal
        playerForward.normalize();

        // Calculate angle between player forward and direction to target
        const dot = playerForward.dot(toTarget);
        const angleRadians = Math.acos(Math.max(-1, Math.min(1, dot)));
        const angleDegrees = THREE.MathUtils.radToDeg(angleRadians);

        // Check if target is within the arc angle
        const halfAngle = attackPattern.angle / 2;
        const isInArc = angleDegrees <= halfAngle;

        // TESTING: Check if target is actually in front (trying positive dot product with inverted forward vector)
        const isInFront = dot > 0; // TESTING: positive dot product means target is in front
        const finalResult = isInArc && isInFront;

        // Simplified debug output with 2D distance
        console.log(`[WeaponSystem] 2D Arc check: distance=${horizontalDistance.toFixed(1)}, angle=${angleDegrees.toFixed(1)}°/${halfAngle.toFixed(1)}°, dot=${dot.toFixed(2)} (${isInFront ? 'FRONT' : 'BEHIND'}) → ${finalResult ? 'HIT' : 'MISS'}`);

        return finalResult;
    }

    /**
     * Check if target is in circular area (staff ground slam)
     */
    isInCircularArea(targetPosition, playerPosition, attackPattern) {
        const distance = playerPosition.distanceTo(targetPosition);
        return distance <= attackPattern.range;
    }

    /**
     * Check if target is in line attack (spear thrust)
     */
    isInLineAttack(targetPosition, playerPosition, playerRotation, attackPattern) {
        // Calculate player's forward direction
        const playerForward = new THREE.Vector3(0, 0, -1);
        playerForward.applyEuler(playerRotation);
        playerForward.y = 0;
        playerForward.normalize();

        // Calculate direction from player to target
        const toTarget = new THREE.Vector3().subVectors(targetPosition, playerPosition);
        toTarget.y = 0;

        // Project target position onto the forward line
        const projectionLength = toTarget.dot(playerForward);

        // Check if target is in front of player and within range
        if (projectionLength <= 0 || projectionLength > attackPattern.range) {
            return false;
        }

        // Calculate perpendicular distance from the line
        const projectedPoint = playerForward.clone().multiplyScalar(projectionLength);
        const perpendicular = toTarget.clone().sub(projectedPoint);
        const perpendicularDistance = perpendicular.length();

        // Check if target is within the line width
        const lineWidth = attackPattern.width || 1.0;
        return perpendicularDistance <= lineWidth;
    }

    /**
     * Check if target is in cone attack (wide sweep)
     */
    isInConeAttack(targetPosition, playerPosition, playerRotation, attackPattern) {
        // Similar to frontal arc but with distance-based width expansion
        const distance = playerPosition.distanceTo(targetPosition);

        // Calculate direction from player to target
        const toTarget = new THREE.Vector3().subVectors(targetPosition, playerPosition);
        toTarget.y = 0;
        toTarget.normalize();

        // Calculate player's forward direction
        const playerForward = new THREE.Vector3(0, 0, -1);
        playerForward.applyEuler(playerRotation);
        playerForward.y = 0;
        playerForward.normalize();

        // Calculate angle between player forward and direction to target
        const dot = playerForward.dot(toTarget);
        const angleRadians = Math.acos(Math.max(-1, Math.min(1, dot)));
        const angleDegrees = THREE.MathUtils.radToDeg(angleRadians);

        // Cone expands with distance
        const baseAngle = attackPattern.angle || 30;
        const expansion = attackPattern.expansion || 0.5;
        const effectiveAngle = baseAngle + (distance * expansion);
        const halfAngle = effectiveAngle / 2;

        return angleDegrees <= halfAngle;
    }
    
    /**
     * Deal melee damage to enemy (same effects as projectile hits)
     */
    dealMeleeDamage(enemy) {
        console.log(`[WeaponSystem] dealMeleeDamage called for enemy:`, enemy.name);
        console.log(`[WeaponSystem] Enemy userData:`, enemy.userData);

        if (!enemy.userData) {
            console.warn(`[WeaponSystem] Enemy has no userData!`);
            return;
        }

        if (enemy.userData.health <= 0) {
            console.warn(`[WeaponSystem] Enemy already dead (health: ${enemy.userData.health})`);
            return;
        }

        const damage = this.currentWeapon.damage || 1;
        console.log(`[WeaponSystem] Applying ${damage} melee damage to ${enemy.name}`);

        // Use the same damage system as projectiles for consistent effects
        if (enemy.userData && typeof enemy.userData.takeDamage === 'function') {
            console.log(`[WeaponSystem] Using takeDamage method for proper flash effects`);

            // Create impact data for melee attack (from player position)
            const pointOfImpact = enemy.position.clone();
            const meleeVelocity = new THREE.Vector3(0, 0, 0); // No velocity for melee

            try {
                // Try enhanced takeDamage with impact data
                enemy.userData.takeDamage(damage, pointOfImpact, meleeVelocity);
                console.log(`[WeaponSystem] Enhanced takeDamage successful for ${enemy.name}`);
            } catch (error) {
                console.log(`[WeaponSystem] Fallback to legacy takeDamage for ${enemy.name}:`, error);
                enemy.userData.takeDamage(damage);
            }

            // Apply knockback chance (20% same as projectiles)
            if (enemy.userData.health > 0 && enemy.userData.aiBrain && Math.random() < 0.2) {
                console.log(`[WeaponSystem] Applying knockback to ${enemy.name}`);
                // Calculate knockback direction (away from player) - get player position from scene
                const playerMesh = this.scene.getObjectByName('player');
                const playerPosition = playerMesh ? playerMesh.position : new THREE.Vector3(0, 0, 0);
                const knockbackDirection = enemy.position.clone().sub(playerPosition).normalize();
                const knockbackStrength = 20.0; // Same strength as projectiles
                enemy.userData.aiBrain.applyKnockback(knockbackDirection, knockbackStrength);
            }
        } else if (enemy.userData && typeof enemy.userData.health === 'number') {
            console.log(`[WeaponSystem] Fallback: Direct health modification for ${enemy.name}`);
            const oldHealth = enemy.userData.health;
            enemy.userData.health -= damage;
            console.log(`[WeaponSystem] Enemy health: ${oldHealth} -> ${enemy.userData.health}`);

            // Apply knockback chance even with fallback method
            if (enemy.userData.health > 0 && enemy.userData.aiBrain && Math.random() < 0.2) {
                console.log(`[WeaponSystem] Applying fallback knockback to ${enemy.name}`);
                const playerMesh = this.scene.getObjectByName('player');
                const playerPosition = playerMesh ? playerMesh.position : new THREE.Vector3(0, 0, 0);
                const knockbackDirection = enemy.position.clone().sub(playerPosition).normalize();
                const knockbackStrength = 20.0;
                enemy.userData.aiBrain.applyKnockback(knockbackDirection, knockbackStrength);
            }

            // Mark for death if health depleted
            if (enemy.userData.health <= 0) {
                enemy.userData.health = 0;
                enemy.userData.markedForDeath = true;
                console.log(`[WeaponSystem] Enemy ${enemy.name} marked for death`);
            }
        } else if (typeof enemy.takeHit === 'function') {
            console.log(`[WeaponSystem] Using direct takeHit method for ${enemy.name}`);
            const playerMesh = this.scene.getObjectByName('player');
            const playerPosition = playerMesh ? playerMesh.position : new THREE.Vector3(0, 0, 0);
            enemy.takeHit(damage, playerPosition, 6);
        } else {
            console.warn(`[WeaponSystem] No damage method found for enemy ${enemy.name}`);
        }

        console.log(`[WeaponSystem] Successfully dealt ${damage} melee damage to enemy`);
    }

    /**
     * Deal melee damage to destructible object
     */
    dealDestructibleDamage(destructibleObject) {
        console.log(`[WeaponSystem] dealDestructibleDamage called for object:`, destructibleObject.name || destructibleObject.userData.objectType);
        console.log(`[WeaponSystem] Object userData:`, destructibleObject.userData);

        if (!destructibleObject.userData) {
            console.warn(`[WeaponSystem] Destructible object has no userData!`);
            return;
        }

        if (!destructibleObject.userData.isDestructible) {
            // SACRED OBJECTS: Special feedback for sacred/indestructible objects
            if (destructibleObject.userData.isSacred) {
                console.log(`[WeaponSystem] 🛡️ SACRED OBJECT: Cannot destroy sacred ${destructibleObject.userData.objectType || 'object'}`);

                // Create a visual effect to show the object is protected
                this.createSacredProtectionEffect(destructibleObject);

                // Play a special sound effect (if available)
                this.playSacredProtectionSound();

                return;
            }

            console.warn(`[WeaponSystem] Object is not marked as destructible!`);
            return;
        }

        if (destructibleObject.userData.health <= 0) {
            console.warn(`[WeaponSystem] Object already destroyed (health: ${destructibleObject.userData.health})`);
            return;
        }

        const damage = this.currentWeapon.damage || 1;
        const oldHealth = destructibleObject.userData.health;
        destructibleObject.userData.health -= damage;

        console.log(`[WeaponSystem] Object health: ${oldHealth} -> ${destructibleObject.userData.health} (damage: ${damage})`);

        // Check if object should be destroyed
        if (destructibleObject.userData.health <= 0) {
            console.log(`[WeaponSystem] DESTRUCTIBLE OBJECT DESTROYED: ${destructibleObject.name || destructibleObject.userData.objectType}`);

            // Trigger destruction through global event system (same as projectiles)
            const destructionEvent = new CustomEvent('objectDestroyed', {
                detail: {
                    object: destructibleObject,
                    pointOfImpact: destructibleObject.position.clone(),
                    projectileVelocity: new THREE.Vector3(0, 0, 0) // No velocity for melee attacks
                }
            });
            console.log(`[WeaponSystem] Dispatching objectDestroyed event:`, destructionEvent.detail);
            window.dispatchEvent(destructionEvent);
        }

        console.log(`[WeaponSystem] Successfully dealt ${damage} melee damage to destructible object`);
    }

    /**
     * Create visual effect for sacred object protection
     */
    createSacredProtectionEffect(sacredObject) {
        try {
            // Create a golden shield-like effect around the object
            const shieldGeometry = new THREE.SphereGeometry(2.0, 16, 8);
            const shieldMaterial = new THREE.MeshBasicMaterial({
                color: 0xFFD700, // Golden color
                transparent: true,
                opacity: 0.3,
                wireframe: true
            });

            const shieldEffect = new THREE.Mesh(shieldGeometry, shieldMaterial);
            shieldEffect.position.copy(sacredObject.position);
            shieldEffect.position.y += 1.0; // Slightly above the object

            // Add to scene
            this.scene.add(shieldEffect);

            // Animate the shield effect
            const startTime = Date.now();
            const duration = 1000; // 1 second

            const animateShield = () => {
                const elapsed = Date.now() - startTime;
                const progress = elapsed / duration;

                if (progress < 1.0) {
                    // Pulse effect
                    const pulse = Math.sin(progress * Math.PI * 4) * 0.2 + 0.3;
                    shieldMaterial.opacity = pulse;

                    // Slight rotation
                    shieldEffect.rotation.y += 0.05;

                    requestAnimationFrame(animateShield);
                } else {
                    // Remove the effect
                    this.scene.remove(shieldEffect);
                    shieldGeometry.dispose();
                    shieldMaterial.dispose();
                }
            };

            animateShield();

        } catch (error) {
            console.warn('[WeaponSystem] Failed to create sacred protection effect:', error);
        }
    }

    /**
     * Play sound effect for sacred object protection
     */
    playSacredProtectionSound() {
        try {
            // Try to play a protection sound if audio manager is available
            if (this.audioManager && typeof this.audioManager.playSound === 'function') {
                // Use a metallic clang or shield sound if available
                this.audioManager.playSound('shield_block', 0.3);
            } else {
                console.log('[WeaponSystem] 🛡️ *Sacred protection sound*');
            }
        } catch (error) {
            console.warn('[WeaponSystem] Failed to play sacred protection sound:', error);
        }
    }

    /**
     * Start attack cooldown
     */
    startAttackCooldown() {
        this.attackCooldown = this.attackCooldownTime;
        console.log(`[WeaponSystem] Started attack cooldown: ${this.attackCooldown}ms`);
    }
    
    /**
     * Play attack sound
     */
    playAttackSound() {
        // TODO: Implement audio system integration
        console.log('[WeaponSystem] Playing attack sound');
    }
    
    /**
     * Update weapon system (call from game loop)
     */
    update(deltaTime) {
        // Update attack cooldown
        if (this.attackCooldown > 0) {
            const previousCooldown = this.attackCooldown;
            this.attackCooldown -= deltaTime * 1000; // Convert to milliseconds
            if (this.attackCooldown < 0) {
                this.attackCooldown = 0;
            }

            // Debug logging for cooldown updates
            if (previousCooldown > 0 && this.attackCooldown <= 0) {
                console.log(`[WeaponSystem] Attack cooldown finished, ready to attack again`);
            }
        }
    }
    
    /**
     * Get default attack pattern for weapon type
     */
    static getDefaultAttackPattern(weaponType) {
        switch (weaponType) {
            case 'melee':
                return {
                    type: 'frontal_arc',
                    range: 4.0,
                    angle: 90,
                    description: 'Standard melee swing'
                };
            case 'ranged':
                return {
                    type: 'projectile',
                    range: 50,
                    spread: 0,
                    description: 'Projectile attack'
                };
            default:
                return {
                    type: 'frontal_arc',
                    range: 3.0,
                    angle: 60,
                    description: 'Default attack pattern'
                };
        }
    }

    /**
     * Create weapon data from item
     */
    static createWeaponFromItem(item) {
        if (!item || !item.itemData) return null;
        
        const itemData = item.itemData;
        
        // Determine weapon type based on item type
        let weaponType = 'melee'; // Default to melee for most items
        
        if (itemData.type === 'weapon') {
            weaponType = itemData.weaponType || 'melee';
        }
        
        return {
            id: itemData.id,
            name: itemData.name,
            type: weaponType,
            description: itemData.description,
            damage: itemData.damage || 1,
            range: itemData.range || (weaponType === 'melee' ? 3 : 50),
            attackSpeed: itemData.attackSpeed || 1.0,
            model: item.model || null, // 3D model for visual representation
            attackPattern: itemData.attackPattern || WeaponSystem.getDefaultAttackPattern(weaponType)
        };
    }
}
