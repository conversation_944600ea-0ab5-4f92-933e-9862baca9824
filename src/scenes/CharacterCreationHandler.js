import * as THREE from 'three';
import { STATE } from '../constants.js';
import AudioManager from '../utils/audioManager.js'; // Make sure AudioManager is imported if used directly
import { DungeonGenerator } from '../generators/DungeonGenerator.js'; // <<< Import DungeonGenerator
import { createElementalPlayerModel } from '../generators/prefabs/elementalPlayer.js'; // <<< UPDATED PATH
// Import utils later (textUtils)

// Microphone permission dialogue
const microphonePermissionData = {
    prompt: "In certain parts of the game, optional voice input can be used to enhance gameplay—for example, to cast spells or interact with specific elements. This feature is entirely optional and can be turned off at any time in the settings.\n\nDo you wish to enable voice input?",
    options: [
        { text: "Accept", response: "accept" },
        { text: "Deny", response: "deny" }
    ]
};

// New dialogue structure organized into 6 phases
const blackScreenNarration = [
    "In the space between sleeping and waking...",
    "A flickering campfire under a vast dark sky.",
    "The air is still."
];

const flickerIntroDialogue = [
    "Huh? Oh!",
    "Didn't see you there… you traveled far, huh?",
    "Come and warm yourself by my flames." // Trigger for walk
];

const flickerRevealDialogue = [
    "Ahem. Down here, sparky.",
    "Yeah, the fire. Don't look so surprised. Happens all the time.",
    "Name's Flicker.",
    "First time? I can tell. You've got that 'new save file' energy.",
    "I'm in the business of... second chances.",
    "Or third. Or fourth. Who's counting?",
    "Fancy a change of scenery? A new life? Less… this?",
    "I can make that happen."
];

const nameInputDialogue = [
    "Oh before I forget, what is your name?"
];

const nameEraseDialogue = [
    "It looks like you forgot... Names are just words",
    "Words are not worth of any until we say them out loud",
    "It is called spelling for a reason."
];

const strangerDialogue = [
    "The strong, silent type? Fine. I'll call you... 'Stranger.'",
    "Words are not worth of any until we say them out loud",
    "It is called spelling for a reason."
];

// Special name reactions and cheat system
const specialNameReactions = {
    "Rick Fel": {
        dialogue: [
            "I... That name sounds familiar...",
            "Words are not worth of any until we say them out loud",
            "It's called spelling for a reason."
        ],
        cheat: "spawn_chests" // Cheat code to activate
    },
    "Frisk": {
        dialogue: [
            "Ah, a fellow traveler of determination. I've heard that name before...",
            "Words are not worth of any until we say them out loud",
            "It is called spelling for a reason."
        ],
        cheat: null
    },
    "Chara": {
        dialogue: [
            "...That name carries weight. Heavy weight. Interesting choice.",
            "Words are not worth of any until we say them out loud",
            "It is called spelling for a reason."
        ],
        cheat: null
    },
    "Sans": {
        dialogue: [
            "heh. good one, kid. though i think you're in the wrong game.",
            "Words are not worth of any until we say them out loud",
            "It is called spelling for a reason."
        ],
        cheat: null
    }
};

const cupIntroDialogue = [
    "But before we continue... first… a toast. Tradition, you know.",
    "Cups of solid gold filled with liquid silver..."
];

const cupChoiceData = {
    prompt: "Do you drink from the cup?",
    options: [
        { text: "Take the drink.", response: "Attaboy. It'll only sting a little." },
        { text: "Refuse, but stay seated.", response: null }, // Will be converted to option 1
        { text: "Close your eyes.", response: null } // Will be converted to option 1
    ]
};

const transitionDialogue = [
    "Hmm. You've got a shape now. A name. A path… maybe.",
    "Don't take the part where you die personally.",
    "Souls always remember what minds forget.",
    "And if you hear a voice calling you home…",
    "...",
    "Just pretend it's the wind.",
    "The soul is too heavy for where you're about to go.",
    "It is time.",
    "Time for my favorite song.",
    "...",
    "Let it go now. The soul's too heavy for where you're going.",
    "I'm off for now.",
    "See you… deeper down."
];

const dungeonEntryDialogue = [
    "You shouldn't be here. The walls are thin here.",
    "Thin enough to hear them scratching. Thin enough to...",
    "...help me. Please.",
    "Just for a second. I can't...",
    "...hold the door much longer.",
    "They're almost through.",
    "Do you understand?",
    "Do you...",
    "this is the actual me.",
    "I need your help.",
    "Before he takes over again."
];

// Visual effects data for dialogue lines
const dungeonDialogueEffects = {
    1: { type: 'static', intensity: 0.3, duration: 800 }, // "Thin enough to..."
    3: { type: 'distortion', intensity: 0.5, duration: 1000 }, // "I can't..."
    7: { type: 'glitch', intensity: 0.8, duration: 1200 } // "Do you..."
};

// Camera breathing effect parameters - enhanced but balanced
const breathFrequency = 0.22; // Slightly faster breathing
const breathAmplitudeY = 0.04; // Slightly reduced vertical movement
const breathAmplitudeX = 0.02; // Subtle horizontal sway
const breathAmplitudeZ = 0.01; // Very subtle forward/backward movement

class CharacterCreationHandler {
    // --- Define Methods Accessed in Constructor FIRST ---
    onKeyDown(event) {
        // Block input if rotate message is showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            return;
        }

        // --- Debug Shortcut Check FIRST ---
        if (event.key === 'ArrowDown') { // Check using event.key for consistency
            // Prevent multiple triggers
            if (this.debugShortcutTriggered) {
                console.log("Debug shortcut already triggered, ignoring...");
                event.preventDefault();
                return;
            }

            const now = performance.now();
            if (now - this.lastDebugDownPressTime > this.DEBUG_PRESS_INTERVAL) {
                this.debugDownPressCount = 1;
                // Removed log
            } else {
                this.debugDownPressCount++;
                // Removed log
            }
            this.lastDebugDownPressTime = now;

            if (this.debugDownPressCount >= 3) {
                console.log("Debug shortcut triggered!");
                this.debugShortcutTriggered = true; // Set flag to prevent multiple triggers
                this._triggerDebugDungeonTransition(); // Skip videos and go directly to dungeon with full setup
                this.debugDownPressCount = 0; // Reset after triggering
                event.preventDefault(); // Prevent normal ArrowDown processing
                return; // Stop further execution in this handler
            }
            // If not triggered, fall through to normal ArrowDown processing below
        } else {
            // Reset count if any *other* key is pressed
            this.debugDownPressCount = 0;
        }
        // --- End Debug Shortcut Check ---

        // If camera is animating, ignore regular input
        if (this.cameraAnimation.active || this.lookAroundAnimation.active) return;

        // Handle microphone permission navigation
        if (this.currentPhase === 'microphonePermission' && this.isWaitingForAdvanceInput) {
            const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
            if (event.key === 'ArrowDown' || event.key === 'ArrowRight') {
                if (options && options.length > 0) {
                    this.selectedMicrophoneChoice = (this.selectedMicrophoneChoice + 1) % options.length;
                    this.highlightMicrophoneOption(this.selectedMicrophoneChoice);
                    event.preventDefault();
                }
            } else if (event.key === 'ArrowUp' || event.key === 'ArrowLeft') {
                if (options && options.length > 0) {
                    this.selectedMicrophoneChoice = (this.selectedMicrophoneChoice - 1 + options.length) % options.length;
                    this.highlightMicrophoneOption(this.selectedMicrophoneChoice);
                    event.preventDefault();
                }
            } else if (event.key === 'Enter') {
                if (options && options.length > 0 && this.selectedMicrophoneChoice >= 0) {
                    console.log("[Desktop Enter] Selecting microphone option.");
                    this.selectMicrophoneOption(this.selectedMicrophoneChoice);
                    event.preventDefault();
                }
            }
            return; // Don't process other navigation during microphone permission
        }

        // Normal Dialogue/Option Navigation
        if (this.dialogueContainer && !this.dialogueContainer.classList.contains('hidden')) {
            const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
            if (event.key === 'ArrowDown') {
                if (options && options.length > 0) {
                    this.highlightedOptionIndex = (this.highlightedOptionIndex + 1) % options.length;
                    this._updateHighlight();
                    event.preventDefault();
                }
            } else if (event.key === 'ArrowUp') {
                if (options && options.length > 0) {
                    this.highlightedOptionIndex = (this.highlightedOptionIndex - 1 + options.length) % options.length;
                    this._updateHighlight();
                    event.preventDefault();
                }
            } else if (event.key === 'Enter') {
                event.preventDefault();
                const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');

                // Check if options are currently displayed and one is highlighted
                if (options && options.length > 0 && this.highlightedOptionIndex >= 0) {
                    // During name confirmation, check if Enter is temporarily blocked
                    if (this.isShowingNameConfirmation && !this.nameConfirmationEnterBlocked) {
                        console.log("[Desktop Enter] Clicking highlighted confirmation button.");
                        options[this.highlightedOptionIndex].click();
                    } else if (this.isShowingNameConfirmation && this.nameConfirmationEnterBlocked) {
                        console.log("[Desktop Enter] Blocked - waiting for confirmation delay.");
                    } else {
                        // Normal dialogue options
                        console.log("[Desktop Enter] Clicking highlighted option.");
                        options[this.highlightedOptionIndex].click();
                    }
                } else if (!this.isShowingNameConfirmation) {
                    // Otherwise (no options shown, or none highlighted), treat as advance click (but not during name confirmation)
                    console.log("[Desktop Enter] No options selected, calling onClickAdvance.");
                    this.onClickAdvance();
                } else {
                    console.log("[Desktop Enter] Name confirmation active but no option highlighted.");
                }
            }
        }
    }

    // Method bound in constructor
    onClickAdvance() {
        // Block input if rotate message is showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            return;
        }

         // --- Handle Input During Video Playback FIRST ---
         if (this.waitingForInputAfterVideo) {
            const elapsed = this.flickerVideoStartTime ? performance.now() - this.flickerVideoStartTime : Infinity;
            // Removed log
            // Check if 2 seconds (2000 ms) have passed
            if (elapsed >= 2000) {
                 // Removed log
                 this.waitingForInputAfterVideo = false; // Clear the flag
                 this.hideFlickerVideo(); // Hide video and then proceed
             } else {
                 // Removed log
             }
             return; // Always return after handling video input
         }

        this._handleAdvanceInput();
    }

    // MOBILE FIX: Touch event handler
    onTouchEnd(event) {
        // Block input if rotate message is showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            event.preventDefault();
            return;
        }

        // Prevent default to avoid double-firing with click events
        event.preventDefault();

        console.log("[CharacterCreationHandler] Touch end detected");

        // Use the same logic as click advance
        this.onClickAdvance();
    }

    // Shared advance input logic
    _handleAdvanceInput() {
         // --- End Video Input Check ---

         // Block input during automatic sequence
         if (this.automaticSequenceActive) {
             console.log("Input blocked - automatic sequence is active");
             return;
         }

         if (this.currentTypingJob.instance && !this.currentTypingJob.isComplete) {
             // Check if dialogue can be skipped (text finished typing)
             if (!this.canSkipDialogue) {
                 console.log("❌ Cannot skip typing - text is still typing");
                 return;
             }

             console.log("⚡ Skipping typing animation - completing instantly");
             clearTimeout(this.currentTypingJob.timeoutId);
             if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
             this.chatSoundInterval = null;
             this.audioManager?.stopSound('chat');
             if (this.dialogueTextElement) this.dialogueTextElement.textContent = this.currentTypingJob.text;
             this.currentTypingJob.isComplete = true;
             const callback = this.currentTypingJob.callback;
             this.currentTypingJob.instance = null;
             this.currentTypingJob.timeoutId = null;

             // Play flicker_command sound when "It'll only sting a little" is skipped
             const skippedText = this.currentTypingJob.text;
             if (skippedText && skippedText.includes("It'll only sting a little")) {
                 console.log("🎭 Playing flicker_command sound after skipping 'It'll only sting a little'...");
                 this.audioManager?.playSound('flicker_command', false, 0.7);
             }

             // Update typing state - typing is now complete
             this.isTyping = false;
             this.canSkipDialogue = true;
             this.isWaitingForAdvanceInput = true;

             // Store callback for next advance instead of executing immediately
             this.currentTypingJob.callback = callback;
             console.log("⚡ Typing skip complete - dialogue can now be advanced");
             return;
         }

         // Handle new phase system input
         if (this.currentPhase === 'nameInput') {
             // During name input, check if dialogue options are shown (input field or confirmation is active)
             if (this.dialogueOptionsContainer && this.dialogueOptionsContainer.children.length > 0) {
                 // Input field or confirmation dialogue is active, don't advance - let the buttons handle it
                 return;
             }
         }

         if (this.currentPhase === 'cupChoice') {
             // During cup choice phase, check if dialogue options are shown
             if (this.dialogueOptionsContainer && this.dialogueOptionsContainer.children.length > 0) {
                 // Options are shown, don't advance - let the option selection handle it
                 return;
             }
         }

         // Legacy handlers for compatibility
         if (this.isWaitingPostWalkInput) {
             this.handlePostWalkAdvance();
         } else if (this.isWaitingPostEllipsisInput) {
             this.handlePostEllipsisAdvance();
         } else if (this.isWaitingForAdvanceInput) {
              // Check if dialogue can be skipped (text finished typing)
              console.log(`🔍 Skip check - canSkipDialogue: ${this.canSkipDialogue}, isTyping: ${this.isTyping}, isWaitingForAdvanceInput: ${this.isWaitingForAdvanceInput}`);
              if (!this.canSkipDialogue) {
                  console.log("❌ Cannot skip dialogue - text is still typing");
                  return;
              }
              console.log("✅ Dialogue can be skipped - proceeding with advance");

              // Removed log
              this.isWaitingForAdvanceInput = false;
              this.canSkipDialogue = false; // Reset for next dialogue
              this._executeAdvanceCallback();
         }
     }

    // --- Constructor ---
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.audioManager = sceneManager.audioManager;
        this.scene = null;
        // Removed unused this.font assignment

        // DOM Elements
        this.dialogueContainer = document.getElementById('dialogue-container');
        this.dialogueTextElement = document.getElementById('dialogue-text');
        this.dialogueOptionsContainer = document.getElementById('dialogue-options');
        this.dialogueProgressElement = document.getElementById('dialogue-progress');

        // Mobile orientation elements
        this.rotateMessageElement = document.getElementById('rotate-message');
        this.backgroundBlurOverlay = document.getElementById('background-blur-overlay');
        this.boundHandleOrientationChange = this._handleOrientationChange.bind(this);
        // Video elements
        this.videoOverlay = document.getElementById('video-overlay');
        this.introVideo = document.getElementById('intro-video');
        this.rebornVideo = document.getElementById('reborn-video');
        this.endIntroVideo = document.getElementById('endintro-video');

        // New Phase State Management
        this.currentPhase = 'microphonePermission'; // microphonePermission, blackScreen, intro, reveal, nameInput, cupChoice, transition
        this.currentBlackScreenIndex = 0;
        this.currentIntroIndex = 0;
        this.currentRevealIndex = 0;
        this.currentNameEraseIndex = 0;
        this.currentTransitionIndex = 0;
        this.isSceneRevealed = false;

        // Name Input System
        this.nameInputField = null;
        this.playerName = '';
        this.nameDisplayElement = null;
        this.isNameErasing = false;
        this.hasUserInput = false; // Track if user has typed anything
        this.isShowingNameConfirmation = false; // Track if name confirmation is active
        this.nameConfirmationEnterBlocked = false; // Block Enter briefly after showing confirmation

        // Microphone Permission System
        this.microphonePermissionGranted = false;
        this.selectedMicrophoneChoice = -1;

        // Cup Choice System
        this.cupMesh = null;
        this.cupAnimation = { active: false, startTime: 0, duration: 4000, phase: 'rising' };
        this.selectedCupChoice = -1;

        // Legacy state (keeping for compatibility)
        this.highlightedOptionIndex = -1;
        this.isWaitingForAdvanceInput = false;
        this.isWaitingPostWalkInput = false;
        this.isWaitingPostEllipsisInput = false;
        this.waitingForInputAfterVideo = false; // Flag for when flicker video is playing
        this.flickerVideoStartTime = null; // Timestamp for flicker video start
        this.hasIntroChatSoundPlayed = false;
        this.canSkipDialogue = false; // NEW: Track if dialogue can be skipped
        this.isTyping = false; // NEW: Track if text is currently typing
        this.automaticSequenceActive = false; // Flag for automatic face sequence
        this.hasFlickerCraftSoundPlayed = false; // Track if flicker_craft sound has been played
        this.earthquakeEffect = { active: false, intensity: 0, basePosition: null }; // Earthquake camera shake
        this.corruptionEffect = {
            active: false,
            startTime: 0,
            shadowCircles: [],
            originalSkyColor: null,
            originalFogColor: null,
            originalObjectOpacities: new Map(),
            dissolvingObjects: []
        }; // World dissolution effect
        this.pendingVideoStart = false; // Flag to indicate video should start when animation completes
        this.currentTypingJob = { text: '', index: 0, speed: 15, callback: null, instance: null, isComplete: false, timeoutId: null };
        this.chatSoundInterval = null;

        // 3D Objects & Environment
        this.environmentObjects = [];
        this.fireGroup = null;
        this.campfireLight = null;

        // Animations - CORRECTED: Walk toward campfire (from far to close)
        // Animation should move FROM current position (far) TO close position (near campfire)
        this.cameraAnimation = { active: false, startTime: 0, duration: 3000, startPosition: new THREE.Vector3(), endPosition: new THREE.Vector3(0, 1.8, 3.0), startLookAt: new THREE.Vector3(), endLookAt: new THREE.Vector3(0, -0.5, 0) };
        this.lookAroundAnimation = { active: false, startTime: 0, phase: 0, durationLeft: 800, durationRight: 1200, durationCenter: 500, pauseDuration: 200, maxAngleY: Math.PI / 6, targetLookAt: new THREE.Vector3() };
        this.groundCameraAnimation = { active: false, startTime: 0, duration: 15000, startPosition: new THREE.Vector3(), endPosition: new THREE.Vector3(), startLookAt: new THREE.Vector3(), endLookAt: new THREE.Vector3() };
        this.fireDanceAnimation = { active: false, startTime: 0, duration: 800, maxScaleY: 1.3, minScaleXZ: 0.8 };
        // Fire face animation removed - replaced with endintro video
        this.drinkingAnimation = {
            active: false,
            startTime: 0,
            duration: 2000, // 2 seconds for drinking animation
            phase: 'tilting', // 'tilting', 'holding', 'returning'
            originalRotation: new THREE.Euler(),
            tiltAngle: -Math.PI / 6 // 30 degrees down (negative for downward rotation)
        };
        this.obfuscationInterval = null;

        // Interaction
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();

        // Bound event listeners - NOW `this.onKeyDown` and `this.onClickAdvance` are defined above
        this.boundOnKeyDown = this.onKeyDown.bind(this);
        this.boundOnClickAdvance = this.onClickAdvance.bind(this);
        this.boundOnTouchEnd = this.onTouchEnd.bind(this); // Mobile touch support

        // Store original global light intensities
        this.originalGlobalAmbientIntensity = 0.5;
        this.originalGlobalDirectionalIntensity = 0.5;

        // Debugging shortcut state
        this.debugDownPressCount = 0;
        this.lastDebugDownPressTime = 0;
        this.DEBUG_PRESS_INTERVAL = 500; // Max ms between presses for sequence
        this.debugShortcutTriggered = false; // Prevent multiple triggers

        // CRITICAL FIX: Add questions_data for debug transition
        this.questions_data = [
            { text: "Where do you awaken?", options: ["Mystic Woods", "Desert Ruins", "Starlit Peaks"] },
            { text: "Who stands by your side?", options: ["Fire Spirit", "Shadow Cat", "Sky Drone"] },
            { text: "What power flows through you?", options: ["Flame Burst", "Ice Veil", "Thunder Strike"] },
            { text: "What drives your journey?", options: ["Revenge", "Discovery", "Honor"] },
            { text: "What do you seek?", options: ["Ancient Relic", "Lost Knowledge", "Inner Peace"] },
            { text: "How do you face danger?", options: ["Head-on", "With Caution", "Through Cunning"] },
            { text: "What is your greatest strength?", options: ["Courage", "Wisdom", "Compassion"] },
            { text: "What haunts your past?", options: ["Betrayal", "Loss", "Failure"] },
            { text: "What guides your choices?", options: ["Instinct", "Logic", "Heart"] },
            { text: "How will your story end?", options: ["In Glory", "In Peace", "In Mystery"] }
        ];
    }

    async init(scene) {
        console.log("CharacterCreationHandler: Initializing...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x050a20); // Darker night sky for more atmosphere
        this.scene.fog = new THREE.FogExp2(0x050a20, 0.015); // More transparent fog (reduced from 0.03 to 0.015)

        // --- Dim Global Lights ---
        const globalAmbLight = this.sceneManager.scene.getObjectByName("globalAmbientLight");
        const globalDirLight = this.sceneManager.scene.getObjectByName("globalDirectionalLight");
        if (globalAmbLight) {
            this.originalGlobalAmbientIntensity = globalAmbLight.intensity; // Store original
            globalAmbLight.intensity = 0; // Make it very dim or off for this scene
        }
        if (globalDirLight) {
            this.originalGlobalDirectionalIntensity = globalDirLight.intensity; // Store original
            globalDirLight.intensity = 0; // Turn off directional light
        }
        // --- End Dim Global Lights ---

        // Reset state
        this.currentPhase = 'blackScreen';
        this.currentBlackScreenIndex = 0;
        this.currentIntroIndex = 0;
        this.currentRevealIndex = 0;
        this.currentNameEraseIndex = 0;
        this.currentTransitionIndex = 0;
        this.isSceneRevealed = false;
        this.playerName = '';
        this.isNameErasing = false;
        this.isTextErasing = false; // CRITICAL FIX: Initialize text erasure flag
        this.isAdvancingBlackScreen = false; // CRITICAL FIX: Initialize black screen advancement flag
        this.selectedCupChoice = -1;
        this.highlightedOptionIndex = -1;
        this.isWaitingForAdvanceInput = false;
        this.isWaitingPostWalkInput = false;
        this.isWaitingPostEllipsisInput = false;
        this.waitingForInputAfterVideo = false; // Reset on init
        this.flickerVideoStartTime = null; // Reset on init
        this.hasIntroChatSoundPlayed = false;
        this.automaticSequenceActive = false; // Reset on init
        this.hasFlickerCraftSoundPlayed = false; // Reset on init
        this.earthquakeEffect = { active: false, intensity: 0, basePosition: null }; // Reset earthquake effect
        this.corruptionEffect = {
            active: false,
            startTime: 0,
            shadowCircles: [],
            originalSkyColor: null,
            originalFogColor: null,
            originalObjectOpacities: new Map(),
            dissolvingObjects: []
        }; // Reset dissolution effect
        this.pendingVideoStart = false; // Reset pending video flag
        this.cameraAnimation.active = false;
        this.lookAroundAnimation.active = false;
        this.groundCameraAnimation.active = false;
        this.fireDanceAnimation.active = false;
        this.cupAnimation.active = false;

        if (!this.font) {
            console.warn("CharacterCreationHandler: Font not available on init.");
        }

        this._createEnvironment(); // Adds scene-specific lights
        this._setupCamera();

        if (this.dialogueContainer) this.dialogueContainer.classList.remove('hidden');
        if (this.dialogueTextElement) this.dialogueTextElement.textContent = '';
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = '';

        this._addEventListeners();

        this.audioManager?.playSound('bg_ambient_surround', true, 0.2);

        // Add multiple orientation detection methods for better reliability
        window.addEventListener('orientationchange', this.boundHandleOrientationChange);
        window.addEventListener('resize', this.boundHandleOrientationChange);

        // Create media query listener for more reliable detection
        this.portraitMediaQuery = window.matchMedia("(orientation: portrait)");
        if (this.portraitMediaQuery.addListener) {
            this.portraitMediaQuery.addListener(this.boundHandleOrientationChange);
        } else if (this.portraitMediaQuery.addEventListener) {
            this.portraitMediaQuery.addEventListener('change', this.boundHandleOrientationChange);
        }

        // Preload the endintro video
        this._preloadEndIntroVideo();

        // Preload the logo fade video for mobile performance
        this._preloadLogoFadeVideo();

        // Store reference for DungeonHandler to access pre-buffered video
        window.characterCreationHandler = this;

        // Start with microphone permission phase
        this._setupMicrophonePermission();
        setTimeout(() => {
            this.startMicrophonePermissionPhase();
        }, 50);

        console.log("CharacterCreationHandler: Initialized.");
    }

    cleanup() {
        console.log("CharacterCreationHandler: Cleaning up...");
        this._removeEventListeners();

        // Remove orientation change listeners
        window.removeEventListener('orientationchange', this.boundHandleOrientationChange);
        window.removeEventListener('resize', this.boundHandleOrientationChange);

        // Remove media query listener
        if (this.portraitMediaQuery) {
            if (this.portraitMediaQuery.removeListener) {
                this.portraitMediaQuery.removeListener(this.boundHandleOrientationChange);
            } else if (this.portraitMediaQuery.removeEventListener) {
                this.portraitMediaQuery.removeEventListener('change', this.boundHandleOrientationChange);
            }
        }

        // Hide mobile rotate message and blur overlay
        if (this.rotateMessageElement) {
            this.rotateMessageElement.classList.remove('active');
        }
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.remove('active');
        }

        // Hide dialogue and video overlay immediately
        if (this.dialogueContainer) this.dialogueContainer.classList.add('hidden');
        if (this.videoOverlay) {
             this.videoOverlay.classList.remove('visible', 'fading-in');
             this.videoOverlay.style.opacity = 0; // Ensure it's visually gone
        }

        // Stop any active animations or sounds
        this.cameraAnimation.active = false;
        this.lookAroundAnimation.active = false;
        this.fireDanceAnimation.active = false;
        this.cupAnimation.active = false;
        this.audioManager?.stopSound('bg_ambient_surround');
        this.audioManager?.stopSound('chat');

        // NEVER stop stuckinmydreams during cleanup - let it play for full 15 seconds
        console.log("🎵 NEVER stopping stuckinmydreams during cleanup - let it play until timer ends");

        if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
        this.chatSoundInterval = null;
        if (this.obfuscationInterval) clearInterval(this.obfuscationInterval);
        this.obfuscationInterval = null;

        // Clean up new phase system elements
        if (this.nameDisplayElement) {
            document.body.removeChild(this.nameDisplayElement);
            this.nameDisplayElement = null;
        }
        if (this.cupMesh) {
            this.scene?.remove(this.cupMesh);
            this.cupMesh = null;
        }
        if (this.cupLight) {
            this.cupLight = null; // Light is removed with cupMesh since it's a child
        }

        // Clean up dialogue system
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }

        // Clean up any remaining choice UI elements
        const cupChoicesContainer = document.querySelector('.cup-choices-container');
        if (cupChoicesContainer) {
            document.body.removeChild(cupChoicesContainer);
        }

        // Stop video if playing
        if (this.introVideo && !this.introVideo.paused) {
            this.introVideo.pause();
        }

        // Restore global lights
        const globalAmbLight = this.sceneManager.scene?.getObjectByName("globalAmbientLight");
        const globalDirLight = this.sceneManager.scene?.getObjectByName("globalDirectionalLight");
        if (globalAmbLight) globalAmbLight.intensity = this.originalGlobalAmbientIntensity;
        if (globalDirLight) globalDirLight.intensity = this.originalGlobalDirectionalIntensity;

        // Proper disposal of all forest scene objects
        console.log("Disposing forest scene objects for memory cleanup...");

        // Dispose environment objects (trees, grass, mist, etc.)
        this.environmentObjects.forEach(obj => {
            if (obj) {
                this._disposeObject(obj);
                this.scene?.remove(obj);
            }
        });

        // Dispose fire group
        if (this.fireGroup) {
            this._disposeObject(this.fireGroup);
            this.scene?.remove(this.fireGroup);
        }

        // Dispose volumetric mist
        if (this.volumetricMist) {
            this._disposeObject(this.volumetricMist);
            this.scene?.remove(this.volumetricMist);
        }

        // Clear arrays and references
        this.environmentObjects = [];
        this.fireGroup = null;
        this.campfireLight = null;
        this.rimLight = null;
        this.hemisphereLight = null;
        this.volumetricMist = null;

        console.log("Forest scene memory cleanup complete.");

        // NEVER clean up endintro video during cleanup - let it play for full 15 seconds
        if (this.endIntroVideo) {
            console.log("🎬 NEVER cleaning up endintro video during cleanup - let it play until timer ends");
        }

        // Nullify DOM references (though they persist in the actual DOM)
        this.dialogueContainer = null;
        this.dialogueTextElement = null;
        this.dialogueOptionsContainer = null;
        this.dialogueProgressElement = null;
        this.videoOverlay = null;
        this.introVideo = null;
        this.rebornVideo = null;

        console.log("CharacterCreationHandler: Cleaned up.");
    }

    // Helper method for proper memory disposal
    _disposeObject(obj) {
        if (!obj) return;

        // Recursively dispose of all children
        if (obj.children && obj.children.length > 0) {
            // Create a copy of children array since we'll be modifying it
            const children = [...obj.children];
            children.forEach(child => {
                this._disposeObject(child);
                obj.remove(child);
            });
        }

        // Dispose geometry
        if (obj.geometry) {
            obj.geometry.dispose();
        }

        // Dispose material(s)
        if (obj.material) {
            if (Array.isArray(obj.material)) {
                obj.material.forEach(material => {
                    this._disposeMaterial(material);
                });
            } else {
                this._disposeMaterial(obj.material);
            }
        }

        // Dispose textures if object has them
        if (obj.texture) {
            obj.texture.dispose();
        }

        // Clear userData
        if (obj.userData) {
            obj.userData = {};
        }
    }

    // Helper method for material disposal
    _disposeMaterial(material) {
        if (!material) return;

        // Dispose all textures in the material
        Object.keys(material).forEach(key => {
            const value = material[key];
            if (value && value.isTexture) {
                value.dispose();
            }
        });

        // Dispose the material itself
        if (material.dispose) {
            material.dispose();
        }
    }

    _addEventListeners() {
        // Correct binding, relies on constructor correctly setting boundOnKeyDown/boundOnClickAdvance
        window.addEventListener('keydown', this.boundOnKeyDown, false);
        this.dialogueContainer?.addEventListener('click', this.boundOnClickAdvance);

        // MOBILE FIX: Add touch event listeners for video overlay and dialogue
        this.dialogueContainer?.addEventListener('touchend', this.boundOnTouchEnd, false);
        if (this.videoOverlay) {
            this.videoOverlay.addEventListener('touchend', this.boundOnTouchEnd, false);
            this.videoOverlay.addEventListener('click', this.boundOnClickAdvance, false);
            console.log("[CharacterCreationHandler] Added touch and click listeners to video overlay");
        }
    }

    _removeEventListeners() {
        // Correct removal
        window.removeEventListener('keydown', this.boundOnKeyDown, false);
        this.dialogueContainer?.removeEventListener('click', this.boundOnClickAdvance);

        // MOBILE FIX: Remove touch event listeners
        this.dialogueContainer?.removeEventListener('touchend', this.boundOnTouchEnd, false);
        if (this.videoOverlay) {
            this.videoOverlay.removeEventListener('touchend', this.boundOnTouchEnd, false);
            this.videoOverlay.removeEventListener('click', this.boundOnClickAdvance, false);
        }
    }

    _setupCamera() {
        // Check if this is a mobile device for closer camera positioning
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                         ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);

        // Start at the position where the walk animation begins (far from campfire)
        // This makes the transition seamless when "come and warm yourself" triggers
        if (isMobile) {
            // Mobile: Start closer for better visibility on smaller screens
            this.sceneManager.camera.position.set(0, 2.2, 9); // Closer starting position for mobile
            console.log("Mobile device detected - using closer camera position for intro scene");
        } else {
            // Desktop: Original position
            this.sceneManager.camera.position.set(0, 2.5, 12); // Original desktop position
        }

        this.sceneManager.camera.lookAt(0, 0.5, 0);
        this.sceneManager.camera.rotation.set(0, 0, 0);
    }

    _createEnvironment() {
        // This function should add the dim blue questAmbientLight and the campfireLight
        // Ensure it's not adding *another* global light
        console.log("Creating questionnaire environment...");
        this.environmentObjects = [];

        // Darker ambient light for more atmospheric mood
        const ambientLight = new THREE.AmbientLight(0x3040a0, 0.4); // Reduced intensity and more blue tint
        ambientLight.name = "questAmbientLight";
        this.scene.add(ambientLight);
        this.environmentObjects.push(ambientLight);

        // Add a hemisphere light with stronger contrast
        const hemiLight = new THREE.HemisphereLight(0xffaa60, 0x101840, 0.5); // Warmer top color, darker bottom color
        hemiLight.name = "questHemiLight";
        this.scene.add(hemiLight);
        this.environmentObjects.push(hemiLight);

        // Enhanced campfire light with dramatic warm glow
        this.campfireLight = new THREE.PointLight(0xff4400, 12.0, 35, 1.8); // Warmer orange, higher intensity, longer range
        this.campfireLight.position.set(0, 0.8, 0); // Slightly higher for better illumination
        this.campfireLight.castShadow = true;
        this.campfireLight.shadow.radius = 3; // Softer shadows
        this.campfireLight.shadow.mapSize.width = 2048; // Higher resolution shadows
        this.campfireLight.shadow.mapSize.height = 2048;
        this.campfireLight.shadow.bias = -0.0001; // Reduce shadow acne
        this.campfireLight.shadow.camera.near = 0.1;
        this.campfireLight.shadow.camera.far = 35;
        this.campfireLight.name = "campfireLight";
        this.scene.add(this.campfireLight);
        this.environmentObjects.push(this.campfireLight);

        // Atmospheric rim light for depth and mystical feel
        this.rimLight = new THREE.DirectionalLight(0x4466bb, 1.2); // Cool blue rim light
        this.rimLight.position.set(-12, 10, -8);
        this.rimLight.target.position.set(0, 0, 0);
        this.rimLight.castShadow = true;
        this.rimLight.shadow.mapSize.width = 1024;
        this.rimLight.shadow.mapSize.height = 1024;
        this.rimLight.shadow.camera.left = -20;
        this.rimLight.shadow.camera.right = 20;
        this.rimLight.shadow.camera.top = 20;
        this.rimLight.shadow.camera.bottom = -20;
        this.rimLight.shadow.camera.near = 1;
        this.rimLight.shadow.camera.far = 35;
        this.rimLight.shadow.bias = -0.0002;
        this.rimLight.name = "rimLight";
        this.scene.add(this.rimLight);
        this.scene.add(this.rimLight.target);
        this.environmentObjects.push(this.rimLight);

        // Enhanced hemisphere light for natural sky/ground lighting (replaces duplicate ambient light)
        this.hemisphereLight = new THREE.HemisphereLight(0x4466aa, 0x112244, 0.4); // Slightly brighter
        this.scene.add(this.hemisphereLight);
        this.environmentObjects.push(this.hemisphereLight);

        // Enhanced Grassland Forest Floor with Natural Terrain
        const groundSize = 80;
        const segments = 60; // Increased for more detail
        const groundGeometry = new THREE.PlaneGeometry(groundSize, groundSize, segments, segments);
        const positions = groundGeometry.attributes.position;
        const colors = [];

        // Enhanced color palette for forest floor
        const colorBrown = new THREE.Color(0x3c2a1a);      // Dark earth
        const colorDarkGreen = new THREE.Color(0x1a4d2e);  // Moss patches
        const colorLightBrown = new THREE.Color(0x5a4332); // Lighter soil
        const colorMossyGreen = new THREE.Color(0x2d5a3d); // Brighter moss
        const colorRichEarth = new THREE.Color(0x2a1f15);  // Very dark rich soil

        // Create natural terrain with multiple noise layers
        for (let i = 0; i < positions.count; i++) {
            const x = positions.getX(i);
            const z = positions.getZ(i);

            // Layer 1: Large rolling hills (low frequency, high amplitude)
            const hills = Math.sin(x * 0.08) * Math.cos(z * 0.06) * 0.8;

            // Layer 2: Medium undulations (medium frequency, medium amplitude)
            const undulations = (Math.sin(x * 0.15) + Math.cos(z * 0.18)) * 0.4;

            // Layer 3: Small bumps and dips (high frequency, low amplitude)
            const details = (Math.sin(x * 0.4) * Math.cos(z * 0.35) +
                           Math.sin(x * 0.6) * Math.cos(z * 0.55)) * 0.15;

            // Layer 4: Random micro-variations
            const randomNoise = (Math.random() - 0.5) * 0.2;

            // Layer 5: Subtle ridges and valleys
            const ridges = Math.sin(x * 0.12 + z * 0.08) * 0.3;

            // Combine all layers for natural terrain
            const totalDisplacement = hills + undulations + details + randomNoise + ridges;

            // Apply displacement
            positions.setY(i, positions.getY(i) + totalDisplacement);

            // Enhanced color variation based on height and position
            let color;
            const heightFactor = totalDisplacement;
            const distanceFromCenter = Math.sqrt(x * x + z * z);

            // Color selection based on height and features
            if (heightFactor > 0.6) {
                // High areas - lighter earth with some moss
                color = Math.random() < 0.3 ? colorMossyGreen.clone() : colorLightBrown.clone();
            } else if (heightFactor > 0.2) {
                // Medium areas - mixed earth tones
                const rand = Math.random();
                if (rand < 0.4) color = colorBrown.clone();
                else if (rand < 0.7) color = colorDarkGreen.clone();
                else color = colorLightBrown.clone();
            } else if (heightFactor > -0.2) {
                // Lower areas - darker, richer soil
                color = Math.random() < 0.6 ? colorBrown.clone() : colorRichEarth.clone();
            } else {
                // Lowest areas - very dark, wet earth with moss
                color = Math.random() < 0.7 ? colorRichEarth.clone() : colorDarkGreen.clone();
            }

            // Add subtle distance-based variation
            const distanceFactor = THREE.MathUtils.mapLinear(distanceFromCenter, 0, groundSize/2, 1.0, 0.7);

            // Apply height-based lighting variation
            const lightingFactor = THREE.MathUtils.mapLinear(heightFactor, -1.0, 1.5, 0.6, 1.3);

            // Combine factors
            color.multiplyScalar(lightingFactor * distanceFactor);

            colors.push(color.r, color.g, color.b);
        }

        groundGeometry.computeVertexNormals();
        groundGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        const groundMaterial = new THREE.MeshStandardMaterial({
            roughness: 0.95,
            metalness: 0.05, // Slightly less metallic for more natural look
            vertexColors: true
        });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -1.5;
        ground.receiveShadow = true;
        ground.name = "ground";
        this.scene.add(ground);
        this.environmentObjects.push(ground);

        // Starfield
        const starGeometry = new THREE.BufferGeometry();
        const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 0.05, transparent: true, opacity: 0.8 });
        const starVertices = [];
        for (let i = 0; i < 5000; i++) {
            const x = THREE.MathUtils.randFloatSpread(200); const y = THREE.MathUtils.randFloatSpread(200); const z = THREE.MathUtils.randFloatSpread(200);
            if (Math.sqrt(x*x + y*y + z*z) > 30) starVertices.push(x, y, z);
        }
        starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
        const stars = new THREE.Points(starGeometry, starMaterial); stars.name = "stars";
        this.scene.add(stars);
        this.environmentObjects.push(stars);

        // Trees (Dense Forest) - Multiple rings for forest density
        const treeLeafColors = [new THREE.Color(0x1a4d2e), new THREE.Color(0x225522), new THREE.Color(0x2e6b3a)];
        const treeTrunkColors = [new THREE.Color(0x6a4f3a), new THREE.Color(0x5c4431), new THREE.Color(0x785b46)];
        const leafMaterial = new THREE.MeshStandardMaterial({ roughness: 0.85, metalness: 0.1, vertexColors: true });
        const leafBlockGeo = new THREE.BoxGeometry(0.75, 0.75, 0.75); // 1.5x bigger for mature trees (was 0.5)

        // Create multiple rings of trees with better spacing
        const treeRings = [
            { minRadius: 8, maxRadius: 12, count: 12 },   // Inner ring - fewer trees for better spacing
            { minRadius: 12, maxRadius: 18, count: 15 },  // Middle ring - reduced density
            { minRadius: 18, maxRadius: 28, count: 18 },  // Outer ring - more spaced out
            { minRadius: 28, maxRadius: 40, count: 15 },  // Far ring - less crowded
        ];

        // Calculate camera viewing angles for optimization
        const cameraStartPos = new THREE.Vector3(0, 2.5, 12); // Camera start position
        const cameraEndPos = new THREE.Vector3(0, 1.8, 3.0);  // Camera end position
        const firePos = new THREE.Vector3(0, 0, 0);           // Fire position

        let treeIndex = 0;
        const placedTreePositions = []; // Track placed tree positions for spacing
        const minTreeDistance = 4.0; // Minimum distance between trees

        treeRings.forEach(ring => {
            for (let i = 0; i < ring.count; i++) {
                let treePos;
                let treeX, treeZ;
                let attempts = 0;
                let validPosition = false;

                // Try to find a valid position with proper spacing
                while (!validPosition && attempts < 20) {
                    // Generate potential tree position
                    const angle = Math.random() * Math.PI * 2;
                    const radius = THREE.MathUtils.randFloat(ring.minRadius, ring.maxRadius);
                    treeX = Math.cos(angle) * radius;
                    treeZ = Math.sin(angle) * radius;
                    treePos = new THREE.Vector3(treeX, 0, treeZ);

                    // Check distance to all previously placed trees
                    validPosition = true;
                    for (let placedPos of placedTreePositions) {
                        const distance = treePos.distanceTo(placedPos);
                        if (distance < minTreeDistance) {
                            validPosition = false;
                            break;
                        }
                    }

                    attempts++;
                }

                // Skip this tree if we couldn't find a valid position
                if (!validPosition) {
                    continue;
                }

                // Check if tree is potentially visible from camera positions
                const isVisibleFromStart = this._isTreeVisibleFromCamera(treePos, cameraStartPos, firePos);
                const isVisibleFromEnd = this._isTreeVisibleFromCamera(treePos, cameraEndPos, firePos);
                const isVisibleFromWalk = this._isTreeVisibleDuringWalk(treePos, cameraStartPos, cameraEndPos);

                // Check if tree would block camera sight lines to fire
                const blocksFireView = this._wouldTreeBlockFireView(treePos, cameraStartPos, cameraEndPos);

                // Only create tree if it's potentially visible AND doesn't block fire view
                if ((isVisibleFromStart || isVisibleFromEnd || isVisibleFromWalk) && !blocksFireView) {
                    // Store this position for future spacing checks
                    placedTreePositions.push(treePos.clone());
                const treeGroup = new THREE.Group();
                const trunkHeight = THREE.MathUtils.randFloat(6.0, 15.0); // Towering forest giants (1.5x taller: was 4.0-10.0)
                const trunkRadius = THREE.MathUtils.randFloat(0.4, 0.8); // Proportionally thicker trunks for stability
                const trunkColorBase = treeTrunkColors[Math.floor(Math.random() * treeTrunkColors.length)];
                const trunkGeometry = new THREE.CylinderGeometry(trunkRadius * 0.8, trunkRadius, trunkHeight, 8);
                const trunkColors = [];
                const trunkPositions = trunkGeometry.attributes.position;
                for (let j = 0; j < trunkPositions.count; j++) {
                    const y = trunkPositions.getY(j);
                    const color = trunkColorBase.clone();
                    color.multiplyScalar(THREE.MathUtils.mapLinear(y, -trunkHeight/2, trunkHeight/2, 0.7, 1.1) * THREE.MathUtils.randFloat(0.95, 1.05));
                    trunkColors.push(color.r, color.g, color.b);
                }
                trunkGeometry.setAttribute('color', new THREE.Float32BufferAttribute(trunkColors, 3));
                const trunkMaterial = new THREE.MeshStandardMaterial({ roughness: 0.9, metalness: 0.1, vertexColors: true });
                const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
                trunk.position.y = trunkHeight / 2;
                trunk.castShadow = true;
                treeGroup.add(trunk);

                const leafCluster = new THREE.Group();
                const maxLeafAttempts = 120; // Reasonable attempt count
                const minLeafDistance = 0.4; // Smaller distance - allows touching/slight overlap for full coverage
                const leafPositions = []; // Track placed leaf positions
                const leafColorBase = treeLeafColors[Math.floor(Math.random() * treeLeafColors.length)];

                for (let l = 0; l < maxLeafAttempts; l++) {
                     const blockMesh = new THREE.Mesh(leafBlockGeo.clone(), leafMaterial.clone());
                     const scale = THREE.MathUtils.randFloat(0.8, 1.0); // Reduced scale range to prevent clipping
                     blockMesh.scale.setScalar(scale);
                     const radius = trunkHeight * 0.35 * Math.pow(Math.random(), 0.3); // Tighter clustering for denser canopy
                     const phi = Math.acos(1 - 2 * Math.random());
                     const theta = Math.random() * Math.PI * 2;
                     blockMesh.position.setFromSphericalCoords(radius, phi, theta);
                     blockMesh.position.y += trunkHeight * 0.9;

                     // Check if this position conflicts with existing leaves
                     let tooClose = false;
                     for (let existing of leafPositions) {
                         const distance = blockMesh.position.distanceTo(existing);
                         if (distance < minLeafDistance) {
                             tooClose = true;
                             break;
                         }
                     }

                     // Skip this leaf if it's too close to existing ones
                     if (tooClose) {
                         continue;
                     }

                     // Store this position for future collision checks
                     leafPositions.push(blockMesh.position.clone());
                     blockMesh.rotation.set(Math.random() * Math.PI, Math.random() * Math.PI, Math.random() * Math.PI);
                     blockMesh.castShadow = true;
                     const blockColors = [];
                     for (let k = 0; k < blockMesh.geometry.attributes.position.count; k++) {
                        const color = leafColorBase.clone();
                        color.multiplyScalar(THREE.MathUtils.randFloat(0.9, 1.1));
                        blockColors.push(color.r, color.g, color.b);
                     }
                     blockMesh.geometry.setAttribute('color', new THREE.Float32BufferAttribute(blockColors, 3));
                     leafCluster.add(blockMesh);
                }
                treeGroup.add(leafCluster);

                // Log leaf placement efficiency for debugging
                if (treeIndex === 0) { // Only log for first tree to avoid spam
                    console.log(`Tree leaf placement: ${leafPositions.length} leaves placed out of ${maxLeafAttempts} attempts`);
                }

                // Position tree at the calculated position
                treeGroup.position.set(treeX, ground.position.y, treeZ);
                treeGroup.rotation.y = Math.random() * Math.PI * 2;
                treeGroup.userData.swaySpeed = Math.random() * 0.004 + 0.001;
                treeGroup.userData.swayAmount = Math.random() * 0.008 + 0.003;
                treeGroup.name = `tree_${treeIndex}`;
                this.scene.add(treeGroup);
                this.environmentObjects.push(treeGroup);
                treeIndex++;
                }
            }
        });

        console.log(`Created ${treeIndex} optimized trees (reduced from ${treeRings.reduce((sum, ring) => sum + ring.count, 0)} potential trees)`);

        // Logs
        const logColorBase = new THREE.Color(0x8b4513);
        const logGeometry = new THREE.CylinderGeometry(0.1, 0.12, 1.5, 8);
        const logPositionsArr = logGeometry.attributes.position; const logColors = [];
        for(let j=0; j<logPositionsArr.count; j++) {
            const y = logPositionsArr.getY(j); const color = logColorBase.clone(); const endFade = Math.abs(y) / (1.5 / 2);
            color.multiplyScalar(THREE.MathUtils.mapLinear(endFade, 0, 1, 1.0, 0.6)); logColors.push(color.r, color.g, color.b);
        }
        logGeometry.setAttribute('color', new THREE.Float32BufferAttribute(logColors, 3));
        const logMaterial = new THREE.MeshStandardMaterial({ roughness: 0.85, metalness: 0.1, vertexColors: true });
        const campfireLogGroup = new THREE.Group();
        const logPositionsData = [
            { pos: [0, 0.1, 0.4], rot: [Math.PI / 2, 0, Math.PI / 6] }, { pos: [0.3, 0.1, -0.2], rot: [Math.PI / 2, 0, -Math.PI / 8] },
            { pos: [-0.3, 0.1, -0.2], rot: [Math.PI / 2, 0, Math.PI / -9] }, { pos: [0, 0.3, 0], rot: [Math.PI / 2 + 0.1, 0, Math.PI / 2] }
        ];
        logPositionsData.forEach(p => {
            const log = new THREE.Mesh(logGeometry, logMaterial); log.position.set(...p.pos); log.rotation.set(...p.rot); log.castShadow = true;
            campfireLogGroup.add(log);
        });
        campfireLogGroup.position.y = ground.position.y; campfireLogGroup.scale.setScalar(1.1); campfireLogGroup.name = "campfireLogs";
        this.scene.add(campfireLogGroup);
        this.environmentObjects.push(campfireLogGroup);

        // Fire - enhanced with more vibrant colors and emissive materials
        this.fireGroup = new THREE.Group(); this.fireGroup.name = "fireGroup";
        // More vibrant and varied fire colors
        const fireColorsArr = [
            new THREE.Color(0xff0000), // Red
            new THREE.Color(0xff3000), // Orange-red
            new THREE.Color(0xff6000), // Orange
            new THREE.Color(0xff9000), // Amber
            new THREE.Color(0xffb000), // Gold
            new THREE.Color(0xffd700), // Yellow
            new THREE.Color(0xffffff)  // White (for the hottest parts)
        ];
        const fireBlockGeo = new THREE.BoxGeometry(0.1, 0.1, 0.1);
        const totalParticles = 180; // Increased number of particles for a more dramatic fire effect
        for (let i = 0; i < totalParticles; i++) {
            // Randomly select emissive color for more varied fire appearance
            const emissiveColor = fireColorsArr[Math.floor(Math.random() * fireColorsArr.length)];
            const fireMaterial = new THREE.MeshStandardMaterial({
                vertexColors: true,
                emissive: emissiveColor,
                emissiveIntensity: 2.0, // Increased emissive intensity for more glow
                roughness: 0.1,
                metalness: 0.0
            });
            const fireParticle = new THREE.Mesh(fireBlockGeo.clone(), fireMaterial);
            const particleColors = []; const baseColor = fireColorsArr[Math.floor(Math.random() * fireColorsArr.length)];
            for(let k=0; k<fireParticle.geometry.attributes.position.count; k++) {
                const color = baseColor.clone(); color.multiplyScalar(THREE.MathUtils.randFloat(0.9, 1.5)); particleColors.push(color.r, color.g, color.b);
            }
            fireParticle.geometry.setAttribute('color', new THREE.Float32BufferAttribute(particleColors, 3));
            const heightRatio = Math.random(); const maxRadius = 0.25; const radius = maxRadius * (1.0 - heightRatio * heightRatio); const angle = Math.random() * Math.PI * 2;
            const height = THREE.MathUtils.randFloat(0.1, 0.7);
            fireParticle.position.set(Math.cos(angle) * radius, height, Math.sin(angle) * radius);
            fireParticle.userData.initialPos = fireParticle.position.clone();
            fireParticle.userData.speedY = THREE.MathUtils.randFloat(0.05, 0.2);
            fireParticle.userData.life = THREE.MathUtils.randFloat(0.4, 1.4);
            fireParticle.userData.age = Math.random() * fireParticle.userData.life;
            fireParticle.userData.oscillationSpeed = THREE.MathUtils.randFloat(2.0, 4.0);
            fireParticle.userData.oscillationAmplitude = THREE.MathUtils.randFloat(0.05, 0.15);
            this.fireGroup.add(fireParticle);
        }
        this.fireGroup.position.y = ground.position.y; this.fireGroup.position.z = 0.15;
        this.fireGroup.userData.basePositionY = this.fireGroup.position.y;
        this.scene.add(this.fireGroup);

        // Mythical Mist - Add atmospheric fog throughout the forest
        this._createMythicalMist();

        // Forest Grass - Add scattered grass throughout the scene
        this._createForestGrass();

        console.log("Environment created.");
    }

    _createMythicalMist() {
        // Create a large 3D volume for volumetric mist effect that covers the entire view
        const mistSize = 120; // Large enough to cover entire forest area
        const mistHeight = 20; // Much taller to ensure it covers all tree leaves (trees ~5 units + leaves)
        const mistGeometry = new THREE.BoxGeometry(mistSize, mistHeight, mistSize, 32, 20, 32);

        // Create a custom shader material for volumetric ground mist
        const mistMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0.0 },
                opacity: { value: 0.06 }, // Balanced mist opacity
                mistColor: { value: new THREE.Color(0x8899bb) }, // Soft blue-gray mist
                noiseScale: { value: 0.02 },
                mistHeight: { value: 20.0 }, // Much taller - covers all tree leaves (trees ~5 units + leaves)
                fadeDistance: { value: 40.0 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                varying vec3 vLocalPosition;
                varying vec2 vUv;
                varying float vDistanceToCamera;

                void main() {
                    vUv = uv;
                    vLocalPosition = position; // Local position within the box
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    vDistanceToCamera = distance(cameraPosition, worldPosition.xyz);
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                uniform vec3 mistColor;
                uniform float noiseScale;
                uniform float mistHeight;
                uniform float fadeDistance;

                varying vec3 vWorldPosition;
                varying vec3 vLocalPosition;
                varying vec2 vUv;
                varying float vDistanceToCamera;

                // Simple noise function
                float noise(vec2 p) {
                    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
                }

                float smoothNoise(vec2 p) {
                    vec2 i = floor(p);
                    vec2 f = fract(p);
                    f = f * f * (3.0 - 2.0 * f);

                    float a = noise(i);
                    float b = noise(i + vec2(1.0, 0.0));
                    float c = noise(i + vec2(0.0, 1.0));
                    float d = noise(i + vec2(1.0, 1.0));

                    return mix(mix(a, b, f.x), mix(c, d, f.x), f.y);
                }

                void main() {
                    // Create flowing mist pattern
                    vec2 mistUv = vWorldPosition.xz * noiseScale;
                    float timeOffset = time * 0.1;

                    // Multiple octaves of noise for realistic mist
                    float mist = smoothNoise(mistUv + vec2(timeOffset, 0.0)) * 0.5;
                    mist += smoothNoise(mistUv * 2.0 + vec2(timeOffset * 1.3, timeOffset * 0.7)) * 0.25;
                    mist += smoothNoise(mistUv * 4.0 + vec2(timeOffset * 0.8, timeOffset * 1.1)) * 0.125;

                    // Distance-based fading
                    float distanceFade = 1.0 - smoothstep(0.0, fadeDistance, vDistanceToCamera);

                    // Height-based fading using local position within the box (-6 to +6 becomes 0 to 1)
                    float normalizedHeight = (vLocalPosition.y + mistHeight * 0.5) / mistHeight;
                    float heightFade = 1.0 - smoothstep(0.0, 1.0, normalizedHeight);

                    // Combine all factors
                    float finalOpacity = mist * opacity * distanceFade * heightFade;

                    gl_FragColor = vec4(mistColor, finalOpacity);
                }
            `,
            transparent: true,
            depthWrite: false,
            blending: THREE.AdditiveBlending,
            side: THREE.DoubleSide
        });

        // Create the 3D volumetric mist
        const mistVolume = new THREE.Mesh(mistGeometry, mistMaterial);
        mistVolume.position.y = mistHeight / 2; // Center the volume vertically
        mistVolume.name = "volumetricMist";
        this.scene.add(mistVolume);
        this.environmentObjects.push(mistVolume);

        // Store reference for animation
        this.volumetricMist = mistVolume;

        // Enhanced volumetric fog effect
        this.scene.fog = new THREE.FogExp2(0x050a20, 0.01); // More transparent atmospheric fog (reduced from 0.02 to 0.01)

        console.log("Volumetric ground mist created");
    }

    _createForestGrass() {
        // Create scattered grass blades throughout the forest floor
        const grassColors = [
            new THREE.Color(0x4a6741), // Dark forest green
            new THREE.Color(0x5c7a52), // Medium green
            new THREE.Color(0x6b8a62), // Light green
            new THREE.Color(0x3d5a36), // Very dark green
            new THREE.Color(0x7a9471)  // Bright green
        ];

        // Voxel-style grass blade geometry
        const grassBladeGeo = new THREE.BoxGeometry(0.05, 0.8, 0.05); // Thin, tall voxel blades
        const grassMaterial = new THREE.MeshStandardMaterial({
            roughness: 0.9,
            metalness: 0.0,
            vertexColors: true
        });

        // Create grass in multiple areas around the campfire
        const grassAreas = [
            { centerX: 0, centerZ: 0, radius: 15, density: 200 },    // Around fire area
            { centerX: 10, centerZ: 10, radius: 8, density: 120 },  // Scattered patches
            { centerX: -8, centerZ: 12, radius: 6, density: 100 },
            { centerX: 15, centerZ: -5, radius: 7, density: 110 },
            { centerX: -12, centerZ: -8, radius: 9, density: 130 },
            { centerX: 5, centerZ: -15, radius: 6, density: 90 }
        ];

        grassAreas.forEach((area, areaIndex) => {
            for (let i = 0; i < area.density; i++) {
                // Random position within the area
                const angle = Math.random() * Math.PI * 2;
                const distance = Math.random() * area.radius;
                const grassX = area.centerX + Math.cos(angle) * distance;
                const grassZ = area.centerZ + Math.sin(angle) * distance;

                // Skip if too close to fire (within 3 units)
                const distanceToFire = Math.sqrt(grassX * grassX + grassZ * grassZ);
                if (distanceToFire < 3) continue;

                // Create grass blade
                const grassBlade = new THREE.Mesh(grassBladeGeo, grassMaterial);

                // Random height variation
                const heightScale = THREE.MathUtils.randFloat(0.6, 1.4);
                grassBlade.scale.y = heightScale;

                // Random color
                const grassColor = grassColors[Math.floor(Math.random() * grassColors.length)];
                const positions = grassBladeGeo.attributes.position.array;
                const colors = new Float32Array(positions.length);
                for (let j = 0; j < colors.length; j += 3) {
                    colors[j] = grassColor.r;
                    colors[j + 1] = grassColor.g;
                    colors[j + 2] = grassColor.b;
                }
                grassBladeGeo.setAttribute('color', new THREE.BufferAttribute(colors, 3));

                // Position grass base at ground level for proper pivot point
                grassBlade.position.set(grassX, -1.5, grassZ); // Base at ground level
                grassBlade.rotation.y = Math.random() * Math.PI * 2;
                grassBlade.rotation.x = THREE.MathUtils.randFloat(-0.05, 0.05); // Minimal base lean
                grassBlade.rotation.z = THREE.MathUtils.randFloat(-0.05, 0.05);

                // Adjust geometry pivot to bottom of blade for proper rotation
                grassBlade.geometry = grassBladeGeo.clone();
                grassBlade.geometry.translate(0, 0.4 * heightScale, 0); // Move pivot to base

                // Add swaying animation data
                grassBlade.userData.swaySpeed = Math.random() * 0.008 + 0.002;
                grassBlade.userData.swayAmount = Math.random() * 0.15 + 0.05;
                grassBlade.userData.baseRotationX = grassBlade.rotation.x;
                grassBlade.userData.baseRotationZ = grassBlade.rotation.z;

                grassBlade.name = `grass_${areaIndex}_${i}`;
                this.scene.add(grassBlade);
                this.environmentObjects.push(grassBlade);
            }
        });

        console.log("Forest grass created");
    }

    _createFlamingHand() {
        console.log("Creating flaming hand emerging from fire with cup...");

        // Create hand group
        this.flamingHand = new THREE.Group();
        this.flamingHand.name = "flamingHand";

        // Create hand shape using fire particles
        // Check if blue fire mode is active (cheat activated)
        const isBlueFireMode = window.gameState?.blueFireMode || false;

        const handFireColors = isBlueFireMode ? [
            new THREE.Color(0x0066ff), // Bright blue
            new THREE.Color(0x0080ff), // Light blue
            new THREE.Color(0x00aaff), // Cyan-blue
            new THREE.Color(0x00ccff), // Light cyan
            new THREE.Color(0x66ddff), // Very light blue
        ] : [
            new THREE.Color(0xff0000), // Red
            new THREE.Color(0xff3000), // Orange-red
            new THREE.Color(0xff6000), // Orange
            new THREE.Color(0xff9000), // Amber
            new THREE.Color(0xffb000), // Gold
        ];

        if (isBlueFireMode) {
            console.log("🔥 Creating flaming hand with blue fire colors due to cheat");
        }

        const fireBlockGeo = new THREE.BoxGeometry(0.06, 0.06, 0.06);

        // Hand structure: forearm + wrist + palm + fingers (holding cup)
        const handStructure = [
            // Forearm (coming from fire)
            { x: 0, y: -0.8, z: 0, count: 6 },
            { x: 0, y: -0.6, z: 0, count: 6 },
            { x: 0, y: -0.4, z: 0, count: 6 },
            { x: 0, y: -0.2, z: 0, count: 6 },

            // Wrist
            { x: 0, y: 0, z: 0, count: 8 },

            // Palm (cupped to hold something)
            { x: 0, y: 0.1, z: 0.05, count: 6 },
            { x: 0.05, y: 0.1, z: 0.05, count: 4 },
            { x: -0.05, y: 0.1, z: 0.05, count: 4 },
            { x: 0, y: 0.1, z: 0.1, count: 4 },

            // Thumb (curved around cup)
            { x: -0.1, y: 0.15, z: 0.08, count: 3 },
            { x: -0.12, y: 0.2, z: 0.1, count: 2 },

            // Index finger (curved around cup)
            { x: 0.08, y: 0.18, z: 0.12, count: 3 },
            { x: 0.1, y: 0.25, z: 0.15, count: 2 },

            // Middle finger (curved around cup)
            { x: 0.05, y: 0.2, z: 0.12, count: 3 },
            { x: 0.06, y: 0.28, z: 0.15, count: 2 },

            // Ring finger (curved around cup)
            { x: 0.02, y: 0.18, z: 0.12, count: 3 },
            { x: 0.02, y: 0.25, z: 0.15, count: 2 },

            // Pinky (curved around cup)
            { x: -0.02, y: 0.16, z: 0.1, count: 2 },
            { x: -0.03, y: 0.22, z: 0.12, count: 2 },
        ];

        // Create fire particles for hand structure
        handStructure.forEach((segment, segmentIndex) => {
            for (let i = 0; i < segment.count; i++) {
                const emissiveColor = handFireColors[Math.floor(Math.random() * handFireColors.length)];
                const fireMaterial = new THREE.MeshStandardMaterial({
                    vertexColors: true,
                    emissive: emissiveColor,
                    emissiveIntensity: 2.0,
                    roughness: 0.1,
                    metalness: 0.0,
                    transparent: true,
                    opacity: 0.9
                });

                const fireParticle = new THREE.Mesh(fireBlockGeo.clone(), fireMaterial);

                // Add some randomness to particle positions within segment
                const randomOffset = 0.02;
                fireParticle.position.set(
                    segment.x + (Math.random() - 0.5) * randomOffset,
                    segment.y + (Math.random() - 0.5) * randomOffset,
                    segment.z + (Math.random() - 0.5) * randomOffset
                );

                // Random colors for each particle
                const particleColors = [];
                const baseColor = handFireColors[Math.floor(Math.random() * handFireColors.length)];
                for (let k = 0; k < fireParticle.geometry.attributes.position.count; k++) {
                    const color = baseColor.clone();
                    color.multiplyScalar(THREE.MathUtils.randFloat(0.8, 1.2));
                    particleColors.push(color.r, color.g, color.b);
                }
                fireParticle.geometry.setAttribute('color', new THREE.Float32BufferAttribute(particleColors, 3));

                // Animation data
                fireParticle.userData.initialPos = fireParticle.position.clone();
                fireParticle.userData.flickerSpeed = THREE.MathUtils.randFloat(3.0, 6.0);
                fireParticle.userData.flickerAmount = THREE.MathUtils.randFloat(0.01, 0.03);
                fireParticle.userData.segmentIndex = segmentIndex;

                this.flamingHand.add(fireParticle);
            }
        });

        // Create fire trail particles (following the hand movement)
        this.fireTrail = [];
        this.maxTrailLength = 20;

        // Add hand light for atmospheric lighting
        this.handLight = new THREE.PointLight(0xff4400, 3.0, 12, 2);
        this.handLight.position.set(0, 0.2, 0);
        this.flamingHand.add(this.handLight);

        // Position hand at fire initially (will emerge from fire)
        this.flamingHand.position.set(0, -1.5, 0); // Start in the fire
        this.flamingHand.rotation.x = -Math.PI * 0.3; // Angled to hold cup properly

        // Animation properties for emergence
        this.flamingHand.userData.emergenceProgress = 0;
        this.flamingHand.userData.emergenceSpeed = 0.8; // Slow emergence
        this.flamingHand.userData.targetPosition = new THREE.Vector3(0, 0.5, 1.5); // Final position holding cup
        this.flamingHand.userData.startPosition = new THREE.Vector3(0, -1.5, 0); // Start in fire

        this.scene.add(this.flamingHand);
        this.environmentObjects.push(this.flamingHand);

        console.log("Flaming hand created with", this.flamingHand.children.length - 1, "fire particles");
    }

    // Helper function to check if tree is visible from a camera position
    _isTreeVisibleFromCamera(treePos, cameraPos, lookAtPos) {
        // Calculate camera direction
        const cameraDir = new THREE.Vector3().subVectors(lookAtPos, cameraPos).normalize();

        // Calculate direction to tree
        const treeDir = new THREE.Vector3().subVectors(treePos, cameraPos).normalize();

        // Calculate angle between camera direction and tree direction
        const angle = Math.acos(Math.max(-1, Math.min(1, cameraDir.dot(treeDir))));

        // Much more restrictive - only spawn trees in front 120-degree cone (60 degrees each side)
        const maxAngle = Math.PI / 3; // 60 degrees (120-degree total cone)

        return angle <= maxAngle;
    }

    // Helper function to check if tree is visible during camera walk
    _isTreeVisibleDuringWalk(treePos, startPos, endPos) {
        // Check visibility from key points along the walk path (fewer checks for efficiency)
        const checkPoints = 3; // Reduced from 5 for better performance
        for (let i = 0; i <= checkPoints; i++) {
            const t = i / checkPoints;
            const walkPos = new THREE.Vector3().lerpVectors(startPos, endPos, t);
            const firePos = new THREE.Vector3(0, 0, 0);

            if (this._isTreeVisibleFromCamera(treePos, walkPos, firePos)) {
                return true;
            }
        }
        return false;
    }

    // Helper function to check if tree would block camera view of fire
    _wouldTreeBlockFireView(treePos, cameraStartPos, cameraEndPos) {
        const firePos = new THREE.Vector3(0, 0, 0);
        const treeRadius = 4.0; // Larger blocking radius to keep trees further from sight lines
        const safetyMargin = 1.5; // Additional safety margin for camera comfort

        // Check multiple camera positions for comprehensive blocking detection
        const checkPositions = [
            cameraStartPos,
            cameraEndPos,
            new THREE.Vector3().lerpVectors(cameraStartPos, cameraEndPos, 0.25),
            new THREE.Vector3().lerpVectors(cameraStartPos, cameraEndPos, 0.5),
            new THREE.Vector3().lerpVectors(cameraStartPos, cameraEndPos, 0.75)
        ];

        for (let camPos of checkPositions) {
            const camToFire = new THREE.Vector3().subVectors(firePos, camPos);
            const camToTree = new THREE.Vector3().subVectors(treePos, camPos);
            const fireDistance = camToFire.length();
            const treeDistance = camToTree.length();

            // If tree is closer than fire, check if it's in the sight cone
            if (treeDistance < fireDistance) {
                const angle = camToFire.angleTo(camToTree);
                const blockingAngle = Math.atan((treeRadius + safetyMargin) / treeDistance);

                if (angle < blockingAngle) {
                    return true; // Tree blocks this camera position
                }
            }

            // Also check if tree is too close to camera (within 6 units) and in front
            if (treeDistance < 6.0) {
                const angle = camToFire.angleTo(camToTree);
                if (angle < Math.PI / 3) { // Within 60-degree front cone
                    return true; // Tree too close to camera front
                }
            }
        }

        return false; // Tree doesn't block any camera view
    }

    update(deltaTime, scene, camera) {
        const time = this.sceneManager.clock.getElapsedTime();

        // Performance optimization: Update LOD based on camera distance
        this._updateLevelOfDetail(camera);

        this._updateFireAnimation(deltaTime, time);
        this._updateTreeAnimation(deltaTime, time);
        this._updateMistAnimation(deltaTime, time);
        this._updateGrassAnimation(deltaTime, time);
        this._updateCameraWalkAnimation(deltaTime, time, camera);
        this._updateLookAroundAnimation(deltaTime, time, camera);
        this._updateGroundCameraAnimation(deltaTime, time, camera);
        this._updateFireDanceAnimation(deltaTime, time);
        this._updateDrinkingAnimation(deltaTime, time, camera);
        this._updateCupAnimation(deltaTime, time);
        this._updateFlamingHandAnimation(deltaTime, time, camera);
        this._updateEarthquakeEffect(deltaTime, time, camera);
        this._updateCorruptionEffect(deltaTime, time, camera);
        if (!this.cameraAnimation.active && !this.lookAroundAnimation.active && !this.groundCameraAnimation.active && !this.earthquakeEffect.active) {
            this._updateCameraBreathing(deltaTime, time, camera);
        }
    }

    // Performance optimization: Level of Detail system
    _updateLevelOfDetail(camera) {
        this.environmentObjects.forEach(obj => {
            if (obj.name && obj.name.startsWith('tree_')) {
                const distance = camera.position.distanceTo(obj.position);

                // Adjust tree detail based on distance
                if (distance > 25) {
                    // Far trees: Reduce leaf animation frequency
                    obj.userData.lodLevel = 'far';
                    obj.children.forEach(child => {
                        if (child instanceof THREE.Group) { // Leaf cluster
                            child.visible = distance < 35; // Hide very distant leaves
                        }
                    });
                } else if (distance > 15) {
                    // Medium trees: Normal detail
                    obj.userData.lodLevel = 'medium';
                    obj.children.forEach(child => {
                        if (child instanceof THREE.Group) {
                            child.visible = true;
                        }
                    });
                } else {
                    // Close trees: Full detail
                    obj.userData.lodLevel = 'close';
                    obj.children.forEach(child => {
                        if (child instanceof THREE.Group) {
                            child.visible = true;
                        }
                    });
                }
            }

            // Optimize grass rendering based on distance
            if (obj.name && obj.name.startsWith('grass_')) {
                const distance = camera.position.distanceTo(obj.position);
                obj.visible = distance < 20; // Hide distant grass
            }
        });
    }

    _updateFireAnimation(deltaTime, time) {
        if (!this.fireGroup) return;
        const groundLevel = -1.5;

        // Increase flame height dramatically during corruption effect
        let maxFlameHeight = groundLevel + 2.3; // Normal height
        if (this.corruptionEffect.active) {
            const elapsed = Date.now() - this.corruptionEffect.startTime;
            const corruptionProgress = Math.min(elapsed / 8000, 1);
            // Flames grow up to 3x higher during corruption
            const heightMultiplier = 1 + (corruptionProgress * 2); // 1x to 3x height
            maxFlameHeight = groundLevel + (2.3 * heightMultiplier);
        }
        this.fireGroup.children.forEach(particle => {
            particle.userData.age += deltaTime;
            if (particle.userData.age >= particle.userData.life) {
                particle.position.copy(particle.userData.initialPos);
                particle.position.x += THREE.MathUtils.randFloatSpread(0.1);
                particle.position.z += THREE.MathUtils.randFloatSpread(0.1);
                particle.userData.age = 0;
                particle.scale.setScalar(1.0);
            } else {
                const lifeRatio = particle.userData.age / particle.userData.life;
                const ageSlowdown = Math.max(0, 1.0 - lifeRatio * lifeRatio);
                const currentSpeedY = particle.userData.speedY * ageSlowdown;
                particle.position.y += currentSpeedY * deltaTime;
                const heightRatioDrift = Math.max(0, (particle.position.y - groundLevel) / 2.3);
                const driftStrength = heightRatioDrift * 0.04;
                const dx = -particle.position.x; const dz = -particle.position.z;
                const distance = Math.sqrt(dx * dx + dz * dz);
                if (distance > 0.01) {
                    particle.position.x += (dx / distance) * driftStrength * deltaTime;
                    particle.position.z += (dz / distance) * driftStrength * deltaTime;
                }
                particle.position.x += Math.sin(time * particle.userData.oscillationSpeed) * particle.userData.oscillationAmplitude * 0.3 * deltaTime;
                particle.position.z += Math.cos(time * particle.userData.oscillationSpeed) * particle.userData.oscillationAmplitude * 0.3 * deltaTime;
                if (particle.position.y > maxFlameHeight) {
                    particle.position.copy(particle.userData.initialPos);
                    particle.userData.age = 0;
                }
                const shrinkFactor = Math.max(0.05, 1.0 - lifeRatio * 0.7);
                particle.scale.setScalar(shrinkFactor);
            }
        });
        if (this.campfireLight) {
            // Enhanced dramatic flickering with multiple frequency layers
            const primaryFlicker = Math.sin(time * 4.5) * 2.0; // Main flicker
            const secondaryFlicker = Math.sin(time * 8.2) * 1.0; // Fast flicker
            const randomFlicker = (Math.random() - 0.5) * 1.5; // Random variation
            const gustFlicker = Math.sin(time * 1.2) * 0.8; // Slow wind gusts

            const totalFlicker = primaryFlicker + secondaryFlicker + randomFlicker + gustFlicker;
            this.campfireLight.intensity = Math.max(8.0, 12.0 + totalFlicker); // Enhanced base intensity

            // Dynamic color temperature variation for realistic fire
            const colorTemp = Math.sin(time * 3.7) * 0.08 + Math.sin(time * 6.1) * 0.04;
            const heatVariation = Math.sin(time * 2.3) * 0.06;

            this.campfireLight.color.r = 1.0;
            this.campfireLight.color.g = Math.max(0.3, 0.45 + colorTemp + heatVariation);
            this.campfireLight.color.b = Math.max(0.1, 0.2 + colorTemp * 0.6 + heatVariation * 0.4);
        }

        // Animate rim light for atmospheric breathing effect
        if (this.rimLight) {
            const breathe = Math.sin(time * 0.8) * 0.15;
            this.rimLight.intensity = 1.2 + breathe;
        }
    }

    _updateTreeAnimation(deltaTime, time) {
        // Dynamic wind system - constantly changing wind speed and direction
        const baseWindSpeed = 1.0;
        const windVariation1 = Math.sin(time * 0.3) * 0.5; // Slow wind changes
        const windVariation2 = Math.sin(time * 0.7) * 0.3; // Medium wind changes
        const windVariation3 = Math.sin(time * 1.2) * 0.2; // Fast wind changes
        const windGusts = Math.sin(time * 0.1) * 0.4; // Very slow gusts

        // Combined wind intensity (0.2 to 1.8 range)
        const currentWindIntensity = baseWindSpeed + windVariation1 + windVariation2 + windVariation3 + windGusts;
        const windIntensity = Math.max(0.2, Math.min(1.8, currentWindIntensity));

        // Wind direction changes
        const windDirectionX = Math.sin(time * 0.15) * 0.6;
        const windDirectionZ = Math.cos(time * 0.23) * 0.8;

        this.environmentObjects.forEach(obj => {
            if (obj.name.startsWith('tree_')) {
                // Tree swaying affected by wind intensity
                const baseSwaySpeed = obj.userData.swaySpeed || 0.002;
                const baseSwayAmount = obj.userData.swayAmount || 0.005;

                const windAffectedSwaySpeed = baseSwaySpeed * (0.5 + windIntensity * 0.5);
                const windAffectedSwayAmount = baseSwayAmount * windIntensity;

                // Multi-directional tree sway with wind direction
                obj.rotation.z = Math.sin(time * windAffectedSwaySpeed) * windAffectedSwayAmount * (0.5 + windDirectionX * 0.3);
                obj.rotation.x = Math.cos(time * windAffectedSwaySpeed * 0.7) * windAffectedSwayAmount * (0.25 + windDirectionZ * 0.2);

                // Optimize leaf animation based on LOD level with wind effects
                const leafCluster = obj.children.find(child => child instanceof THREE.Group);
                if (leafCluster && leafCluster.visible) {
                    const lodLevel = obj.userData.lodLevel || 'close';

                    if (lodLevel === 'close') {
                        // Full leaf animation for close trees with wind variation
                        leafCluster.children.forEach(leafBlock => {
                            if (leafBlock instanceof THREE.Mesh) {
                                // Base leaf movement speeds affected by wind
                                const baseLeafSpeed = 5;
                                const windAffectedLeafSpeed = baseLeafSpeed * (0.3 + windIntensity * 0.7);
                                const windAffectedLeafAmount = 0.05 * (0.5 + windIntensity * 0.5);

                                // Wind direction influence on leaf movement
                                const leafWindX = windAffectedLeafAmount * (1 + windDirectionX * 0.4);
                                const leafWindY = windAffectedLeafAmount * (1 + windDirectionZ * 0.4);

                                leafBlock.rotation.x += Math.sin(time * windAffectedLeafSpeed + leafBlock.position.x) * leafWindX * deltaTime;
                                leafBlock.rotation.y += Math.cos(time * windAffectedLeafSpeed * 0.8 + leafBlock.position.z) * leafWindY * deltaTime;
                            }
                        });
                    } else if (lodLevel === 'medium') {
                        // Reduced leaf animation for medium distance trees with wind
                        leafCluster.children.forEach((leafBlock, index) => {
                            if (leafBlock instanceof THREE.Mesh && index % 2 === 0) { // Animate every other leaf
                                const windAffectedLeafSpeed = 3 * (0.4 + windIntensity * 0.6);
                                const windAffectedLeafAmount = 0.03 * (0.6 + windIntensity * 0.4);

                                leafBlock.rotation.x += Math.sin(time * windAffectedLeafSpeed + leafBlock.position.x) * windAffectedLeafAmount * deltaTime;
                                leafBlock.rotation.y += Math.cos(time * windAffectedLeafSpeed * 0.7 + leafBlock.position.z) * windAffectedLeafAmount * deltaTime;
                            }
                        });
                    }
                    // Far trees: No individual leaf animation (trunk sway only)
                }
            }
        });
    }

    _updateMistAnimation(deltaTime, time) {
        // Update volumetric ground mist shader
        if (this.volumetricMist && this.volumetricMist.material.uniforms) {
            this.volumetricMist.material.uniforms.time.value = time;

            // Subtle opacity pulsing for breathing effect
            const baseOpacity = 0.06; // Balanced mist opacity
            const opacityPulse = Math.sin(time * 0.3) * 0.015; // Gentle pulse
            this.volumetricMist.material.uniforms.opacity.value = baseOpacity + opacityPulse;
        }
    }

    _updateGrassAnimation(deltaTime, time) {
        // Animate grass with unified wind direction and base pivoting
        this.environmentObjects.forEach(obj => {
            if (obj.name && obj.name.startsWith('grass_')) {
                // Unified wind direction (northeast)
                const windDirection = Math.PI * 0.25; // Northeast wind direction (45 degrees)
                const windSpeed = 1.2; // Faster wind speed (was 0.6)
                const waveLength = 10.0; // Shorter wave length for more frequent waves

                // Calculate position along wind direction for wave effect
                const windX = obj.position.x * Math.cos(windDirection) + obj.position.z * Math.sin(windDirection);
                const windWave = Math.sin((windX / waveLength) + (time * windSpeed)) * 0.5 + 0.5;

                // Wind strength varies with wave (reduced range for less rotation)
                const windStrength = 0.15 + (windWave * 0.35); // 0.15 to 0.5 (was 0.2 to 0.8)

                // Calculate wind force components (reduced rotation amount)
                const windForceX = Math.sin(windDirection) * windStrength * 0.2; // Reduced from 0.3 to 0.2
                const windForceZ = Math.cos(windDirection) * windStrength * 0.2; // Reduced from 0.3 to 0.2

                // Add subtle individual variation (faster but smaller)
                const individualVariationX = Math.sin(time * obj.userData.swaySpeed * 2 + obj.position.x * 0.1) * obj.userData.swayAmount * 0.06; // Faster, smaller
                const individualVariationZ = Math.cos(time * obj.userData.swaySpeed * 2 + obj.position.z * 0.1) * obj.userData.swayAmount * 0.06; // Faster, smaller

                // Add faster gusts that affect all grass together
                const gustPhase = Math.sin(time * windSpeed * 2.5) * 0.5 + 0.5; // Faster gusts
                const gustMultiplier = 0.85 + (gustPhase * 0.3); // Smaller range: 0.85 to 1.15

                // Final rotation (all grass leans in wind direction with waves and gusts)
                const finalRotationX = obj.userData.baseRotationX + (windForceX * gustMultiplier) + individualVariationX;
                const finalRotationZ = obj.userData.baseRotationZ + (windForceZ * gustMultiplier) + individualVariationZ;

                obj.rotation.x = finalRotationX;
                obj.rotation.z = finalRotationZ;
            }
        });
    }

     _updateCameraWalkAnimation(deltaTime, time, camera) {
         if (!this.cameraAnimation.active) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.cameraAnimation.startTime;
         let progress = Math.min(elapsed / (this.cameraAnimation.duration / 1000), 1);
         progress = progress * progress * (3 - 2 * progress);
         camera.position.lerpVectors(this.cameraAnimation.startPosition, this.cameraAnimation.endPosition, progress);
         const stepFrequency = progress * Math.PI * 2 * 3.5;
         const bobbleAmountY = 0.07;
         const swayAmountX = 0.03;
         const currentY = camera.position.y;
         camera.position.y += Math.sin(stepFrequency) * bobbleAmountY;
         camera.position.x += Math.cos(stepFrequency * 0.5) * swayAmountX * (1-progress);
         const currentLookAt = new THREE.Vector3().lerpVectors(this.cameraAnimation.startLookAt, this.cameraAnimation.endLookAt, progress);
         camera.lookAt(currentLookAt);
         if (progress >= 1) {
             console.log("Camera walk animation finished.");
             this.cameraAnimation.active = false;
             camera.position.copy(this.cameraAnimation.endPosition);
             camera.lookAt(this.cameraAnimation.endLookAt);
             camera.rotation.set(0,0,0);
             console.log("Walk finished. Starting look around animation...");
             if(this.dialogueTextElement) this.dialogueTextElement.textContent = '';
             // Start look-around animation after walk, before reveal phase
             this._startLookAroundAnimation();
         }
     }

     _updateLookAroundAnimation(deltaTime, time, camera) {
         if (!this.lookAroundAnimation.active) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.lookAroundAnimation.startTime;
         const baseLookAt = this.cameraAnimation.endLookAt;
         let lookProgress = 0;
         switch (this.lookAroundAnimation.phase) {
             case 0:
                 if (elapsed >= this.lookAroundAnimation.pauseDuration / 1000) {
                     this.lookAroundAnimation.phase = 1;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 1:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationLeft / 1000), 1);
                 lookProgress = lookProgress * lookProgress;
                 const angleLeft = THREE.MathUtils.lerp(0, -this.lookAroundAnimation.maxAngleY, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleLeft), baseLookAt.y, baseLookAt.z + Math.cos(angleLeft) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.phase = 2;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 2:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationRight / 1000), 1);
                 lookProgress = 0.5 * (1 - Math.cos(lookProgress * Math.PI));
                 const angleRight = THREE.MathUtils.lerp(-this.lookAroundAnimation.maxAngleY, this.lookAroundAnimation.maxAngleY, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleRight), baseLookAt.y, baseLookAt.z + Math.cos(angleRight) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.phase = 3;
                     this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
                 }
                 break;
             case 3:
                 lookProgress = Math.min(elapsed / (this.lookAroundAnimation.durationCenter / 1000), 1);
                 lookProgress = 1 - (1 - lookProgress) * (1 - lookProgress);
                 const angleCenter = THREE.MathUtils.lerp(this.lookAroundAnimation.maxAngleY, 0, lookProgress);
                 this.lookAroundAnimation.targetLookAt.set(baseLookAt.x + Math.sin(angleCenter), baseLookAt.y, baseLookAt.z + Math.cos(angleCenter) - 1);
                 camera.lookAt(this.lookAroundAnimation.targetLookAt);
                 if (lookProgress >= 1) {
                     this.lookAroundAnimation.active = false;
                     camera.lookAt(baseLookAt);
                     console.log("Look around finished. Starting reveal phase...");
                     this.startRevealPhase();
                 }
                 break;
         }
     }

     _updateGroundCameraAnimation(deltaTime, time, camera) {
         if (!this.groundCameraAnimation.active) return;

         const elapsed = this.sceneManager.clock.getElapsedTime() - this.groundCameraAnimation.startTime;
         const totalDuration = this.groundCameraAnimation.duration / 1000; // 15 seconds
         const fastMoveDuration = 0.5; // 500ms for fast movement

         let progress = Math.min(elapsed / totalDuration, 1);
         let moveProgress = 0;

         // Fast movement phase: complete camera movement within first 500ms
         if (elapsed <= fastMoveDuration) {
             // Fast movement progress (0 to 1 in 500ms)
             moveProgress = elapsed / fastMoveDuration;
             // Apply easing for smooth but fast movement
             moveProgress = moveProgress * moveProgress * (3 - 2 * moveProgress);
         } else {
             // Movement complete, stay in final position
             moveProgress = 1;
         }

         // Interpolate camera position (moving down and back) - fast movement
         camera.position.lerpVectors(this.groundCameraAnimation.startPosition, this.groundCameraAnimation.endPosition, moveProgress);

         // Interpolate look-at target (looking up at the fire face) - fast movement
         const currentLookAt = new THREE.Vector3().lerpVectors(this.groundCameraAnimation.startLookAt, this.groundCameraAnimation.endLookAt, moveProgress);
         camera.lookAt(currentLookAt);

         // Debug logging to track camera position
         if (Math.floor(elapsed * 10) % 10 === 0) { // Log every 0.1 seconds
             console.log(`🎥 Ground camera progress: ${(progress * 100).toFixed(1)}% - Move progress: ${(moveProgress * 100).toFixed(1)}% - Position: ${camera.position.x.toFixed(2)}, ${camera.position.y.toFixed(2)}, ${camera.position.z.toFixed(2)}`);
         }

         // End animation when complete (after 15 seconds)
         if (progress >= 1) {
             this.groundCameraAnimation.active = false;
             console.log("🎥 Ground camera animation complete - player stayed on the ground for full duration");
         }
     }

     _updateFireDanceAnimation(deltaTime, time) {
         if (!this.fireDanceAnimation.active || !this.fireGroup) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.fireDanceAnimation.startTime;
         let progress = Math.min(elapsed / (this.fireDanceAnimation.duration / 1000), 1);
         const danceProgress = Math.sin(progress * Math.PI);
         const scaleY = THREE.MathUtils.lerp(1.0, this.fireDanceAnimation.maxScaleY, danceProgress);
         const scaleXZ = THREE.MathUtils.lerp(1.0, this.fireDanceAnimation.minScaleXZ, danceProgress);
         const basePositionY = this.fireGroup.userData.basePositionY;
         const scaledPositionY = basePositionY + (scaleY - 1.0) * 0.5;
         this.fireGroup.scale.set(scaleXZ, scaleY, scaleXZ);
         this.fireGroup.position.y = scaledPositionY;
         if (progress >= 1) {
             this.fireDanceAnimation.active = false;
             this.fireGroup.scale.set(1, 1, 1);
             this.fireGroup.position.y = basePositionY;
         }
     }

     // Fire face animation removed - replaced with endintro video

     _updateDrinkingAnimation(deltaTime, time, camera) {
         if (!this.drinkingAnimation.active) return;

         const elapsed = this.sceneManager.clock.getElapsedTime() - this.drinkingAnimation.startTime;
         const totalDuration = this.drinkingAnimation.duration / 1000;
         let progress = Math.min(elapsed / totalDuration, 1);

         // Animation phases: tilt down (0-0.3), hold (0.3-0.7), tilt back up (0.7-1.0)
         let tiltProgress = 0;

         if (progress <= 0.3) {
             // Tilting down to drink
             tiltProgress = progress / 0.3;
             this.drinkingAnimation.phase = 'tilting';
         } else if (progress <= 0.7) {
             // Holding drinking position
             tiltProgress = 1.0;
             this.drinkingAnimation.phase = 'holding';
         } else {
             // Tilting back up
             tiltProgress = 1.0 - ((progress - 0.7) / 0.3);
             this.drinkingAnimation.phase = 'returning';
         }

         // Apply smooth easing
         tiltProgress = tiltProgress * tiltProgress * (3 - 2 * tiltProgress);

         // Apply camera rotation for drinking motion
         const currentTilt = this.drinkingAnimation.tiltAngle * tiltProgress;
         camera.rotation.x = this.drinkingAnimation.originalRotation.x + currentTilt;

         // End animation
         if (progress >= 1) {
             this.drinkingAnimation.active = false;
             console.log("Drinking animation ended - camera returned to normal");

             // Restore original camera rotation
             camera.rotation.copy(this.drinkingAnimation.originalRotation);
         }
     }

     _updateCameraBreathing(deltaTime, time, camera) {
         // Determine breathing intensity based on current phase
         let intensityMultiplier = 1.0;

         // More intense breathing after walking to campfire and during dialogue
         if (this.currentPhase === 'reveal' || this.currentPhase === 'cupChoice' ||
             this.currentPhase === 'nameInput' || this.currentPhase === 'toastChoice') {
             intensityMultiplier = 2.5; // Much stronger after campfire approach
         } else if (this.currentPhase === 'intro') {
             intensityMultiplier = 1.8; // Stronger during intro dialogue
         }

         // Use current camera position as base when ground animation is active
         let baseY, baseX, baseZ;
         if (this.groundCameraAnimation.active) {
             // During ground animation, use current camera position as base for breathing
             baseY = camera.position.y;
             baseX = camera.position.x;
             baseZ = camera.position.z;
         } else {
             // Normal breathing uses the walk animation end position as base
             baseY = this.cameraAnimation.endPosition.y;
             baseX = this.cameraAnimation.endPosition.x;
             baseZ = this.cameraAnimation.endPosition.z;
         }

         // Enhanced breathing with multiple axes and layered frequencies
         const primaryBreath = Math.sin(time * Math.PI * 2 * breathFrequency) * intensityMultiplier;
         const secondaryBreath = Math.sin(time * Math.PI * 2 * breathFrequency * 1.3) * 0.3 * intensityMultiplier;

         // Vertical breathing (main effect)
         const breathOffsetY = primaryBreath * breathAmplitudeY + secondaryBreath * breathAmplitudeY * 0.5;

         // Horizontal sway (subtle side-to-side)
         const breathOffsetX = Math.sin(time * Math.PI * 2 * breathFrequency * 0.7) * breathAmplitudeX * intensityMultiplier;

         // Forward/backward movement (very subtle)
         const breathOffsetZ = Math.sin(time * Math.PI * 2 * breathFrequency * 0.5) * breathAmplitudeZ * intensityMultiplier;

         // Apply breathing only if camera is in reasonable range AND ground animation is not active
         if (!this.groundCameraAnimation.active && Math.abs(camera.position.y - baseY) < breathAmplitudeY * intensityMultiplier * 3) {
            camera.position.y = baseY + breathOffsetY;
            camera.position.x = baseX + breathOffsetX;
            camera.position.z = baseZ + breathOffsetZ;
         }
     }

     _updateCupAnimation(deltaTime, time) {
         if (!this.cupAnimation.active || !this.cupMesh) return;
         const elapsed = this.sceneManager.clock.getElapsedTime() - this.cupAnimation.startTime;
         let progress = Math.min(elapsed / (this.cupAnimation.duration / 1000), 1);

         // Smooth easing for the floating animation
         progress = progress * progress * (3 - 2 * progress);

         if (this.cupAnimation.phase === 'rising') {
             // Phase 1: Cup rises from fire (0-0.4)
             const riseProgress = Math.min(progress / 0.4, 1);
             const startY = -2;
             const midY = 0.8; // Higher above the fire
             this.cupMesh.position.y = THREE.MathUtils.lerp(startY, midY, riseProgress);
             this.cupMesh.position.x = 0;
             this.cupMesh.position.z = 0;

             // Gentle rotation while rising
             this.cupMesh.rotation.y = riseProgress * Math.PI * 0.3;

             if (riseProgress >= 1) {
                 this.cupAnimation.phase = 'floating';
                 console.log("Cup rising complete, starting float to player");
             }
         } else if (this.cupAnimation.phase === 'floating') {
             // Phase 2: Cup floats toward player (0.4-1.0)
             const floatProgress = Math.max(0, (progress - 0.4) / 0.6);

             // Float from fire position to player's holding position (directly in front/below camera)
             const startPos = { x: 0, y: 0.8, z: 0 }; // Above fire
             const endPos = { x: 0, y: 1.2, z: 2.8 }; // Directly in front of player, slightly below eye level

             this.cupMesh.position.x = THREE.MathUtils.lerp(startPos.x, endPos.x, floatProgress);
             this.cupMesh.position.y = THREE.MathUtils.lerp(startPos.y, endPos.y, floatProgress);
             this.cupMesh.position.z = THREE.MathUtils.lerp(startPos.z, endPos.z, floatProgress);

             // Rotate to face upward (like player is holding it properly)
             this.cupMesh.rotation.x = THREE.MathUtils.lerp(0, 0, floatProgress); // Keep upright
             this.cupMesh.rotation.y = THREE.MathUtils.lerp(Math.PI * 0.3, 0, floatProgress); // Face forward

             // Add gentle bobbing motion for magical floating effect
             const bobbing = Math.sin(time * 3) * 0.03; // Smaller bobbing when in hand position
             this.cupMesh.position.y += bobbing;
         }

         if (progress >= 1) {
             this.cupAnimation.active = false;
             console.log("Cup floating animation finished - cup is now in player's hand position");
         }
     }

     _updateFlamingHandAnimation(deltaTime, time, camera) {
         if (!this.flamingHand || !camera) return;

         // Only animate hand during cup phase
         if (this.currentPhase !== 'cupChoice') {
             this.flamingHand.visible = false;
             return;
         }

         this.flamingHand.visible = true;

         // Hand emergence animation from fire
         if (this.flamingHand.userData.emergenceProgress < 1.0) {
             // Play flicker_craft sound when hand starts emerging (only once)
             if (!this.hasFlickerCraftSoundPlayed && this.flamingHand.userData.emergenceProgress === 0) {
                 console.log("🔥 Playing flicker_craft sound as hand emerges from fire...");
                 this.audioManager?.playSound('flicker_craft', false, 0.7);
                 this.hasFlickerCraftSoundPlayed = true;
             }

             this.flamingHand.userData.emergenceProgress += deltaTime * this.flamingHand.userData.emergenceSpeed;
             this.flamingHand.userData.emergenceProgress = Math.min(1.0, this.flamingHand.userData.emergenceProgress);

             // Smooth emergence from fire to cup position
             const progress = this.flamingHand.userData.emergenceProgress;
             const easedProgress = progress * progress * (3 - 2 * progress); // Smooth easing

             this.flamingHand.position.lerpVectors(
                 this.flamingHand.userData.startPosition,
                 this.flamingHand.userData.targetPosition,
                 easedProgress
             );

             // Rotate hand to proper cup-holding position
             const startRotation = -Math.PI * 0.1; // Slight upward angle from fire
             const endRotation = -Math.PI * 0.3; // Angled to hold cup
             this.flamingHand.rotation.x = THREE.MathUtils.lerp(startRotation, endRotation, easedProgress);
         }

         // Create fire trail effect
         this._updateFireTrail(time);

         // Handle feeding animation when player drinks
         if (this.flamingHand.userData.isFeeding) {
             this._updateHandFeedingAnimation(time, camera);
         } else if (this.flamingHand.userData.emergenceProgress >= 1.0) {
             // Gentle floating motion when holding cup (normal state)
             const bobbing = Math.sin(time * 1.5) * 0.05; // Gentle bobbing
             const swaying = Math.sin(time * 0.8) * 0.03; // Gentle swaying

             this.flamingHand.position.y = this.flamingHand.userData.targetPosition.y + bobbing;
             this.flamingHand.position.x = this.flamingHand.userData.targetPosition.x + swaying;
         }

         // Animate individual fire particles
         this.flamingHand.children.forEach((child, index) => {
             if (child.isMesh && child.userData.initialPos) {
                 // Flickering animation
                 const flickerSpeed = child.userData.flickerSpeed;
                 const flickerAmount = child.userData.flickerAmount;

                 const flickerX = Math.sin(time * flickerSpeed + index) * flickerAmount;
                 const flickerY = Math.sin(time * flickerSpeed * 1.3 + index) * flickerAmount;
                 const flickerZ = Math.sin(time * flickerSpeed * 0.8 + index) * flickerAmount;

                 child.position.x = child.userData.initialPos.x + flickerX;
                 child.position.y = child.userData.initialPos.y + flickerY;
                 child.position.z = child.userData.initialPos.z + flickerZ;

                 // Opacity flickering for fire effect
                 if (child.material) {
                     const opacityFlicker = 0.8 + Math.sin(time * flickerSpeed * 2 + index) * 0.2;
                     child.material.opacity = Math.max(0.6, Math.min(1.0, opacityFlicker));
                 }
             }
         });

         // Update hand light intensity with dramatic flickering
         if (this.handLight) {
             const lightFlicker = 2.5 + Math.sin(time * 4.5) * 0.6 + Math.sin(time * 8.2) * 0.3;
             this.handLight.intensity = Math.max(2.0, lightFlicker);
         }
     }

     _updateFireTrail(time) {
         if (!this.flamingHand) return;

         // Add new trail particle at hand position
         const handWorldPos = new THREE.Vector3();
         this.flamingHand.getWorldPosition(handWorldPos);

         // Only add trail particles if hand is moving/emerged
         if (this.flamingHand.userData.emergenceProgress > 0.2) {
             const trailParticle = {
                 position: handWorldPos.clone(),
                 age: 0,
                 maxAge: 1.5, // Trail particles last 1.5 seconds
                 size: THREE.MathUtils.randFloat(0.04, 0.08),
                 color: new THREE.Color().setHSL(
                     THREE.MathUtils.randFloat(0.0, 0.15), // Red to orange hues
                     THREE.MathUtils.randFloat(0.8, 1.0),  // High saturation
                     THREE.MathUtils.randFloat(0.5, 0.8)   // Medium to bright
                 )
             };

             this.fireTrail.push(trailParticle);

             // Limit trail length
             if (this.fireTrail.length > this.maxTrailLength) {
                 this.fireTrail.shift();
             }
         }

         // Update existing trail particles
         this.fireTrail.forEach((particle, index) => {
             particle.age += 0.016; // Approximate deltaTime

             // Fade and shrink over time
             const ageRatio = particle.age / particle.maxAge;
             const opacity = Math.max(0, 1.0 - ageRatio);
             const scale = Math.max(0.1, 1.0 - ageRatio * 0.5);

             // Drift upward slightly
             particle.position.y += 0.02;

             // Remove old particles
             if (particle.age >= particle.maxAge) {
                 this.fireTrail.splice(index, 1);
             }
         });
     }

     // Iris effects removed for now

     // Eye positions method removed with iris effects

     _updateHandFeedingAnimation(time, camera) {
         if (!this.flamingHand || !camera) return;

         const elapsed = this.sceneManager.clock.getElapsedTime() - this.flamingHand.userData.feedingStartTime;
         const totalDuration = this.flamingHand.userData.feedingDuration;
         let progress = Math.min(elapsed / totalDuration, 1);

         // Match the drinking animation phases: tilt down (0-0.3), hold (0.3-0.7), tilt back up (0.7-1.0)
         let feedingProgress = 0;
         let feedingPhase = 'approaching';

         if (progress <= 0.3) {
             // Phase 1: Hand moves cup toward player's mouth
             feedingProgress = progress / 0.3;
             feedingPhase = 'approaching';
         } else if (progress <= 0.7) {
             // Phase 2: Hold cup at mouth for drinking
             feedingProgress = 1.0;
             feedingPhase = 'feeding';
         } else {
             // Phase 3: Hand moves cup away from mouth
             feedingProgress = 1.0 - ((progress - 0.7) / 0.3);
             feedingPhase = 'returning';
         }

         // Apply smooth easing
         feedingProgress = feedingProgress * feedingProgress * (3 - 2 * feedingProgress);

         // Calculate target positions based on camera (player) position and rotation
         const cameraPos = camera.position;
         const cameraRot = camera.rotation;

         // Don't rotate cup and hand with camera - keep them in fixed position for drinking

         // Original position (holding cup in front of player)
         const originalPos = this.flamingHand.userData.originalHandPosition;

         // Target position (cup at player's mouth/face position when looking down)
         // Calculate where the player's mouth would be relative to camera
         const mouthPos = new THREE.Vector3(
             cameraPos.x, // Same X as camera (centered)
             cameraPos.y - 1.0, // Even lower - below camera (mouth level when looking down)
             cameraPos.z - 0.15  // Much closer to camera (very close to face)
         );

         // When camera tilts down, the "mouth position" moves forward in world space
         // This makes the cup appear right where the mouth would be when looking down
         const tiltForwardOffset = Math.abs(Math.sin(cameraRot.x)) * 0.3;
         mouthPos.z -= tiltForwardOffset; // Move cup forward when camera tilts down

         // Interpolate hand position
         this.flamingHand.position.lerpVectors(originalPos, mouthPos, feedingProgress);

         // Rotate hand and cup for feeding motion
         if (this.cupMesh) {
             const originalCupPos = this.flamingHand.userData.originalCupPosition;
             const originalCupRot = this.flamingHand.userData.originalCupRotation;

             // Cup position relative to hand (moves closer to hand during feeding)
             const feedingCupPos = new THREE.Vector3(0, 0.2, 0.25); // Closer to hand center
             this.cupMesh.position.lerpVectors(originalCupPos, feedingCupPos, feedingProgress);

             // Cup rotation stays fixed for drinking (slight forward tilt only)
             const feedingCupRot = new THREE.Euler(
                 originalCupRot.x - Math.PI * 0.1, // Slight forward tilt for pouring
                 originalCupRot.y,
                 originalCupRot.z
             );
             this.cupMesh.rotation.x = THREE.MathUtils.lerp(originalCupRot.x, feedingCupRot.x, feedingProgress);
         }

         // Hand rotation stays fixed for stable feeding position
         const originalHandRot = this.flamingHand.userData.originalHandRotation;
         const feedingHandRot = new THREE.Euler(
             originalHandRot.x - Math.PI * 0.1, // Slight upward angle for feeding
             originalHandRot.y + Math.PI * 0.05, // Slight turn toward player
             originalHandRot.z
         );
         this.flamingHand.rotation.x = THREE.MathUtils.lerp(originalHandRot.x, feedingHandRot.x, feedingProgress);
         this.flamingHand.rotation.y = THREE.MathUtils.lerp(originalHandRot.y, feedingHandRot.y, feedingProgress);

         // End feeding animation
         if (progress >= 1) {
             this.flamingHand.userData.isFeeding = false;

             // Restore original positions
             this.flamingHand.position.copy(this.flamingHand.userData.originalHandPosition);
             this.flamingHand.rotation.copy(this.flamingHand.userData.originalHandRotation);

             if (this.cupMesh) {
                 this.cupMesh.position.copy(this.flamingHand.userData.originalCupPosition);
                 this.cupMesh.rotation.copy(this.flamingHand.userData.originalCupRotation);
             }

             console.log("Hand feeding animation complete - hand returned to original position");
         }
     }

    // === NEW PHASE SYSTEM METHODS ===

    _setupMicrophonePermission() {
        // Hide the 3D scene initially and show blur overlay
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.domElement.style.opacity = '0';
        }

        // Show background blur overlay
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.add('active');
        }

        // Ensure dialogue container is visible for microphone permission
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
            this.dialogueContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)'; // Semi-transparent for blur effect
        }
    }

    showMicrophonePermissionChoice() {
        console.log("Showing microphone permission choice...");

        // Mobile detection at the top of the function
        const isMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        const isSmallScreen = window.innerHeight < 700; // Detect smaller phones

        // Clear any existing content
        if (this.dialogueTextElement) {
            this.dialogueTextElement.textContent = '';
        }
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Center the dialogue container and add special styling for microphone permission
        if (this.dialogueContainer) {
            // Add a special class to identify this as microphone permission dialogue
            this.dialogueContainer.classList.add('microphone-permission-dialogue');

            // Show mouse cursor for this dialogue
            document.body.style.cursor = 'default';
            this.dialogueContainer.style.cursor = 'default';

            this.dialogueContainer.style.position = 'fixed';
            this.dialogueContainer.style.left = '50%';
            this.dialogueContainer.style.transform = 'translate(-50%, -50%)';

            // Adjust vertical position based on device type
            if (isMobile) {
                this.dialogueContainer.style.top = '50%'; // More down on mobile
            } else {
                this.dialogueContainer.style.top = '40%'; // Original desktop position
            }

            // Mobile-responsive sizing ONLY for microphone permission box (20% smaller)
            if (isMobile) {
                this.dialogueContainer.style.width = '76%'; // 95% * 0.8 = 76%
                this.dialogueContainer.style.maxWidth = isSmallScreen ? '304px' : '520px'; // 380px * 0.8 = 304px, 650px * 0.8 = 520px
                this.dialogueContainer.style.minHeight = isSmallScreen ? '320px' : '360px'; // Increased to ensure buttons are fully visible
                this.dialogueContainer.style.maxHeight = isSmallScreen ? '340px' : '380px'; // Increased to match min height + some buffer
                this.dialogueContainer.style.padding = isSmallScreen ? '16px 16px 40px 16px' : '24px'; // 20px * 0.8 = 16px, 50px * 0.8 = 40px, 30px * 0.8 = 24px
                this.dialogueContainer.style.fontSize = isSmallScreen ? '13px' : '14px'; // 16px * 0.8 = 13px, 18px * 0.8 = 14px
                this.dialogueContainer.style.lineHeight = isSmallScreen ? '1.3' : '1.4';
            } else {
                this.dialogueContainer.style.width = '85%';
                this.dialogueContainer.style.maxWidth = '650px';
                this.dialogueContainer.style.minHeight = '350px'; // Ensure minimum height for buttons
                this.dialogueContainer.style.maxHeight = '400px'; // Tighter max height to reduce empty space
                this.dialogueContainer.style.padding = '30px';
            }

            this.dialogueContainer.style.textAlign = 'center';
            this.dialogueContainer.style.zIndex = '10000';
            this.dialogueContainer.style.boxSizing = 'border-box'; // Include padding in height calculation
            this.dialogueContainer.style.overflowY = 'auto'; // Allow scrolling if needed
        }

        // Show the prompt text with centered styling
        if (this.dialogueTextElement) {
            this.dialogueTextElement.textContent = microphonePermissionData.prompt;
            this.dialogueTextElement.style.textAlign = 'center';
            this.dialogueTextElement.style.marginBottom = '20px';
            this.dialogueTextElement.style.lineHeight = '1.4';
        }

        // Create option buttons using the same style as fire scene dialogue
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.style.display = 'flex';
            this.dialogueOptionsContainer.style.flexDirection = 'row';
            this.dialogueOptionsContainer.style.justifyContent = 'center';
            this.dialogueOptionsContainer.style.alignItems = 'center';
            this.dialogueOptionsContainer.style.width = '100%';

            // Mobile-responsive container spacing (20% smaller)
            if (isMobile && isSmallScreen) {
                this.dialogueOptionsContainer.style.gap = '12px'; // 15px * 0.8 = 12px
                this.dialogueOptionsContainer.style.marginTop = '16px'; // 20px * 0.8 = 16px
                this.dialogueOptionsContainer.style.marginBottom = '16px'; // 20px * 0.8 = 16px
                this.dialogueOptionsContainer.style.flexWrap = 'wrap'; // Allow wrapping if needed
            } else if (isMobile) {
                this.dialogueOptionsContainer.style.gap = '16px'; // 20px * 0.8 = 16px
                this.dialogueOptionsContainer.style.marginTop = '20px'; // 25px * 0.8 = 20px
                this.dialogueOptionsContainer.style.marginBottom = '4px'; // 5px * 0.8 = 4px
            } else {
                this.dialogueOptionsContainer.style.gap = '30px';
                this.dialogueOptionsContainer.style.marginTop = '30px';
            }

            microphonePermissionData.options.forEach((option, index) => {
                const button = document.createElement('button');
                button.textContent = option.text;
                button.className = 'dialogue-option-button'; // Use same class as fire scene
                button.setAttribute('data-index', index);

                // Add mouse hover effects for desktop
                button.addEventListener('mouseenter', () => {
                    this.selectedMicrophoneChoice = index;
                    this.highlightMicrophoneOption(index);
                });

                button.addEventListener('mouseleave', () => {
                    // Keep the selection but remove hover effect
                    this.highlightMicrophoneOption(this.selectedMicrophoneChoice);
                });

                // Add both click and touch event listeners
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.selectedMicrophoneChoice = index;
                    this.selectMicrophoneOption(index);
                });

                button.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.selectedMicrophoneChoice = index;
                    this.highlightMicrophoneOption(index);
                });

                button.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.selectMicrophoneOption(index);
                });

                // Apply fire scene dialogue button styling with mobile responsiveness (20% smaller)
                if (isMobile && isSmallScreen) {
                    // Small phone button styling (20% smaller)
                    button.style.padding = '8px 16px'; // 10px * 0.8 = 8px, 20px * 0.8 = 16px
                    button.style.fontSize = '13px'; // 16px * 0.8 = 13px
                    button.style.minWidth = '80px'; // 100px * 0.8 = 80px
                    button.style.minHeight = '35px'; // 44px * 0.8 = 35px
                } else if (isMobile) {
                    // Regular mobile button styling (20% smaller)
                    button.style.padding = '10px 18px'; // 12px * 0.8 = 10px, 22px * 0.8 = 18px
                    button.style.fontSize = '14px'; // 17px * 0.8 = 14px
                    button.style.minWidth = '88px'; // 110px * 0.8 = 88px
                    button.style.minHeight = '35px'; // 44px * 0.8 = 35px
                } else {
                    // Desktop button styling (unchanged)
                    button.style.padding = '12px 24px';
                    button.style.fontSize = '18px';
                    button.style.minWidth = '120px';
                }

                // Common button styling
                button.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                button.style.color = '#fff';
                button.style.border = '2px solid #fff';
                button.style.borderRadius = '5px';
                button.style.fontFamily = '"Courier New", Courier, monospace';
                button.style.cursor = 'pointer';
                button.style.transition = 'all 0.3s ease';
                button.style.textAlign = 'center';
                button.style.userSelect = 'none';
                button.style.webkitUserSelect = 'none';
                button.style.touchAction = 'manipulation';

                // Ensure button is interactive
                button.style.pointerEvents = 'auto';
                button.style.zIndex = '10001'; // Above dialogue container

                this.dialogueOptionsContainer.appendChild(button);
            });
        }

        // Set up keyboard navigation
        this.selectedMicrophoneChoice = 0;
        this.highlightMicrophoneOption(0);
        this.isWaitingForAdvanceInput = true;
    }

    selectMicrophoneOption(index) {
        console.log(`Microphone option selected: ${index} (${microphonePermissionData.options[index].text})`);

        this.selectedMicrophoneChoice = index;
        const choice = microphonePermissionData.options[index];

        // Handle the choice
        if (choice.response === 'accept') {
            this.microphonePermissionGranted = true;
            console.log("Microphone permission accepted - will request permission");
            this.requestMicrophonePermission();
        } else {
            this.microphonePermissionGranted = false;
            console.log("Microphone permission denied");
            this.proceedToBlackScreen();
        }
    }

    async requestMicrophonePermission() {
        try {
            console.log('Requesting microphone permission...');
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            // Stop the stream immediately - we just needed permission
            stream.getTracks().forEach(track => track.stop());
            console.log('Microphone permission granted successfully');

            // Store permission for later use
            window.microphonePermissionGranted = true;

        } catch (error) {
            console.warn('Microphone permission denied or not available:', error);
            // Continue anyway - permission was attempted
            window.microphonePermissionGranted = false;
        }

        // Proceed to black screen regardless of permission result
        this.proceedToBlackScreen();
    }

    proceedToBlackScreen() {
        console.log("Proceeding to black screen phase...");

        // FIRST: Clear all text content immediately to prevent visual glitch
        if (this.dialogueTextElement) {
            this.dialogueTextElement.textContent = '';
            this.dialogueTextElement.innerHTML = '';
        }

        // Hide microphone permission UI
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
            // Reset options container styling
            this.dialogueOptionsContainer.style.display = '';
            this.dialogueOptionsContainer.style.flexDirection = '';
            this.dialogueOptionsContainer.style.justifyContent = '';
            this.dialogueOptionsContainer.style.alignItems = '';
            this.dialogueOptionsContainer.style.gap = '';
            this.dialogueOptionsContainer.style.marginTop = '';
            this.dialogueOptionsContainer.style.marginBottom = '';
            this.dialogueOptionsContainer.style.flexWrap = '';
            this.dialogueOptionsContainer.style.width = '';
        }

        // Reset dialogue container styling to normal
        if (this.dialogueContainer) {
            // Remove microphone permission specific class
            this.dialogueContainer.classList.remove('microphone-permission-dialogue');

            // Reset cursor to hidden for normal game
            document.body.style.cursor = 'none';
            this.dialogueContainer.style.cursor = '';

            // Reset all custom styling
            this.dialogueContainer.style.position = '';
            this.dialogueContainer.style.top = '';
            this.dialogueContainer.style.left = '';
            this.dialogueContainer.style.transform = '';
            this.dialogueContainer.style.width = '';
            this.dialogueContainer.style.maxWidth = '';
            this.dialogueContainer.style.minHeight = ''; // Reset min-height
            this.dialogueContainer.style.maxHeight = ''; // Reset max-height
            this.dialogueContainer.style.textAlign = '';
            this.dialogueContainer.style.zIndex = '';
            this.dialogueContainer.style.padding = ''; // Reset padding
            this.dialogueContainer.style.fontSize = ''; // Reset font size
            this.dialogueContainer.style.lineHeight = ''; // Reset line height
            this.dialogueContainer.style.boxSizing = ''; // Reset box-sizing
            this.dialogueContainer.style.overflow = ''; // Reset overflow
            this.dialogueContainer.style.overflowY = ''; // Reset overflow-Y
        }

        // Reset dialogue text styling
        if (this.dialogueTextElement) {
            this.dialogueTextElement.style.textAlign = '';
            this.dialogueTextElement.style.marginBottom = '';
            this.dialogueTextElement.style.lineHeight = '';
        }

        // Fade out blur overlay
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.remove('active');
        }

        // Set up black screen and start narration
        this._setupBlackScreen();
        setTimeout(() => {
            this.startBlackScreenNarration();
        }, 500); // Small delay for blur fade out
    }

    highlightMicrophoneOption(index) {
        const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
        if (options) {
            options.forEach((option, i) => {
                if (i === index) {
                    // Highlight style - same as fire scene dialogue
                    option.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                    option.style.borderColor = '#ffff00'; // Yellow border for highlight
                    option.style.transform = 'scale(1.05)';
                    option.classList.add('highlighted');
                } else {
                    // Normal style
                    option.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                    option.style.borderColor = '#fff';
                    option.style.transform = 'scale(1)';
                    option.classList.remove('highlighted');
                }
            });
        }
    }

    _setupBlackScreen() {
        // Hide the 3D scene initially
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.domElement.style.opacity = '0';
        }
        // Ensure dialogue container is visible for black screen text
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
            this.dialogueContainer.style.backgroundColor = 'rgba(0, 0, 0, 1)'; // Solid black background
        }
    }

    startMicrophonePermissionPhase() {
        console.log("Starting microphone permission phase...");

        // Check for mobile portrait orientation before starting dialogue
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        const isPortrait = window.matchMedia("(orientation: portrait)").matches;

        if (likelyMobile && isPortrait) {
            console.log("Mobile portrait detected - showing rotate message instead of microphone permission");
            this.showMobileRotateMessage();
            return;
        }

        this.currentPhase = 'microphonePermission';
        this.showMicrophonePermissionChoice();
    }

    startBlackScreenNarration() {
        console.log("Starting black screen narration...");

        this.currentPhase = 'blackScreen';
        this.currentBlackScreenIndex = 0;
        this.typeDialogueLine(blackScreenNarration[this.currentBlackScreenIndex], this.nextBlackScreenLine.bind(this), undefined, 'blackScreen');
    }

    nextBlackScreenLine() {
        // CRITICAL FIX: Prevent spamming of black screen narration lines
        if (this.isAdvancingBlackScreen) {
            console.log("Black screen advancement already in progress, ignoring additional attempts");
            return;
        }

        // Set flag to prevent multiple advances
        this.isAdvancingBlackScreen = true;
        console.log("Starting black screen advancement, blocking further attempts");

        this.currentBlackScreenIndex++;
        if (this.currentBlackScreenIndex < blackScreenNarration.length) {
            this.typeDialogueLine(blackScreenNarration[this.currentBlackScreenIndex], () => {
                // Reset flag when typing completes and ready for next advance
                this.isAdvancingBlackScreen = false;
                console.log("Black screen advancement flag reset, allowing next advance");
                this.nextBlackScreenLine.bind(this)();
            }, 50, 'blackScreen');
        } else {
            // Black screen narration complete, reveal the scene
            this.isAdvancingBlackScreen = false; // Reset flag before scene transition
            console.log("Black screen narration complete, resetting flag and revealing scene");
            this.revealScene();
        }
    }

    revealScene() {
        console.log("Revealing scene...");
        this.isSceneRevealed = true;

        // Fade in the 3D scene
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.domElement.style.transition = 'opacity 2s ease-in-out';
            this.sceneManager.renderer.domElement.style.opacity = '1';
        }

        // Remove black background from dialogue container
        if (this.dialogueContainer) {
            this.dialogueContainer.style.backgroundColor = 'transparent';
        }

        // Clear dialogue and start intro phase
        if (this.dialogueTextElement) this.dialogueTextElement.textContent = '';

        setTimeout(() => {
            this.startIntroPhase();
        }, 2000); // Wait for scene fade-in to complete
    }

    startIntroPhase() {
        console.log("Starting intro phase...");
        this.currentPhase = 'intro';
        this.currentIntroIndex = 0;
        this.typeDialogueLine(flickerIntroDialogue[this.currentIntroIndex], this.nextIntroLine.bind(this), undefined, 'intro');
    }

    nextIntroLine() {
        // Check if the current dialogue line triggers the camera walk
        if (flickerIntroDialogue[this.currentIntroIndex]?.includes("warm yourself")) {
            console.log("Triggering camera walk animation toward campfire...");

            // Check if this is a mobile device for closer end position
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                             ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);

            // Initiate the animation by setting its state, not calling a missing function
            this.cameraAnimation.active = true;
            this.cameraAnimation.startTime = this.sceneManager.clock.getElapsedTime();
            // Camera is already at the correct start position, so just set the animation targets
            this.cameraAnimation.startPosition.copy(this.sceneManager.camera.position); // Use current position as start

            if (isMobile) {
                // Mobile: End even closer to the fire for better visibility
                this.cameraAnimation.endPosition.set(0, 1.6, 2.2); // Closer end position for mobile
                console.log("Mobile device detected - using closer end position for campfire scene");
            } else {
                // Desktop: Original end position
                this.cameraAnimation.endPosition.set(0, 1.8, 3.0); // Original desktop end position
            }

            this.cameraAnimation.startLookAt.set(0, 0.5, 0); // Look at campfire from distance
            this.cameraAnimation.endLookAt.set(0, -0.5, 0); // Look down at campfire when close
            // No need to move camera - it's already in the right position for seamless animation

            // Potentially play sounds associated with walk start
            const numSteps = 3;
            const totalDuration = this.cameraAnimation.duration;
            const delayBetweenSteps = totalDuration / (numSteps + 1);
            for (let i = 1; i <= numSteps; i++) {
                 setTimeout(() => {
                     this.audioManager?.playSound('footstep');
                 }, delayBetweenSteps * i);
             }
             this.audioManager?.fadeVolume('bg_ambient_surround', 1.0, totalDuration);

             // Prevent advancing dialogue further until animation finishes
             // The animation's update loop will handle the next step (waiting for input)
             return;
        }

        // If not triggering walk, advance to the next line
        this.currentIntroIndex++;
        if (this.currentIntroIndex < flickerIntroDialogue.length) {
            this.typeDialogueLine(flickerIntroDialogue[this.currentIntroIndex], this.nextIntroLine.bind(this), undefined, 'intro');
        } else {
            console.log("Intro phase complete, starting reveal phase...");
            this.startRevealPhase();
        }
    }

    startRevealPhase() {
        console.log("Starting reveal phase...");
        this.currentPhase = 'reveal';
        this.currentRevealIndex = 0;

        // Restore image fade-in and music that were accidentally removed
        this.audioManager?.playSound('bg_ambient_music', true, 0.4);

        this.typeDialogueLine(flickerRevealDialogue[this.currentRevealIndex], this.nextRevealLine.bind(this), undefined, 'reveal');
    }

    nextRevealLine() {
        this.currentRevealIndex++;

        // Trigger Flicker video and image effects at the right moment
        if (this.currentRevealIndex === 1) { // After "Ahem. Down here, sparky."
            console.log("Triggering Flicker video and image effects...");
            this.isWaitingForAdvanceInput = false; // Not waiting for text advance
            this.showFlickerVideo();
            return; // Stop here, video logic will take over
        }

        if (this.currentRevealIndex < flickerRevealDialogue.length) {
            // Keep fire normal during dialogue - face animation only comes after drinking
            const currentLine = flickerRevealDialogue[this.currentRevealIndex];
            this.typeDialogueLine(currentLine, this.nextRevealLine.bind(this), undefined, 'reveal');
        } else {
            console.log("Reveal phase complete, starting name input phase...");
            this.startNameInputPhase();
        }
    }

    startNameInputPhase() {
        console.log("Starting name input phase...");
        this.currentPhase = 'nameInput';
        this.hasUserInput = false; // Reset user input flag
        this.typeDialogueLine(nameInputDialogue[0], this.showNameInput.bind(this), undefined, 'nameInput');
    }

    showNameInput() {
        console.log("Showing name input field...");

        // Prevent multiple name input fields
        if (this.nameInputField) {
            console.log("Name input field already exists, skipping...");
            return;
        }

        this.showNameInputDialogue();
    }

    showNameInputDialogue() {
        // Show the dialogue container
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
        }

        // Clear any existing options
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Create name input field in the dialogue box style
        const inputContainer = document.createElement('div');
        inputContainer.style.cssText = `
            margin-top: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;

        // Create the input field with device-specific approach
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        this.nameInputField = document.createElement('input');
        this.nameInputField.type = 'text';
        this.nameInputField.placeholder = isMobile ? 'Tap here to enter your name...' : 'Enter your name...';
        this.nameInputField.value = '';

        // Input attributes
        this.nameInputField.setAttribute('autocomplete', 'off');
        this.nameInputField.setAttribute('autocorrect', 'off');
        this.nameInputField.setAttribute('autocapitalize', 'off');
        this.nameInputField.setAttribute('spellcheck', 'false');
        this.nameInputField.setAttribute('inputmode', 'text');
        if (isMobile) {
            this.nameInputField.setAttribute('enterkeyhint', 'done');
        }

        // Device-specific styling
        if (isMobile) {
            // Mobile styling - larger touch targets
            this.nameInputField.style.cssText = `
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: 2px solid #fff;
                border-radius: 3px;
                font-family: 'Courier New', Courier, monospace;
                font-size: 16px;
                padding: 12px 16px;
                text-align: center;
                width: 100%;
                box-sizing: border-box;
                -webkit-appearance: none;
                -webkit-border-radius: 3px;
                -webkit-user-select: text;
                user-select: text;
                touch-action: manipulation;
            `;
        } else {
            // Desktop styling - original design
            this.nameInputField.style.cssText = `
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: 2px solid #fff;
                border-radius: 3px;
                font-family: 'Courier New', Courier, monospace;
                font-size: 18px;
                padding: 8px 12px;
                text-align: center;
                -webkit-user-select: text;
                user-select: text;
            `;
        }

        // Create submit button in dialogue style
        const submitButton = document.createElement('button');
        submitButton.textContent = '* Submit Name';
        submitButton.classList.add('dialogue-option-button');
        submitButton.onclick = () => this.submitName();

        // Add touch support for mobile
        submitButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.submitName();
        });

        inputContainer.appendChild(this.nameInputField);
        inputContainer.appendChild(submitButton);

        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.appendChild(inputContainer);
        }

        // Handle input behavior for fallback to "Stranger"
        this.nameInputField.addEventListener('input', (e) => {
            console.log("🎭 Input event triggered:", e.target.value);
            if (e.target.value.length > 0) {
                this.hasUserInput = true;
                console.log("🎭 hasUserInput set to true");
            } else {
                this.hasUserInput = false;
                console.log("🎭 hasUserInput set to false (empty input)");
            }
        });

        // Device-specific keyboard handling
        if (isMobile) {
            const isIOS = /iPhone|iPad|iPod/.test(navigator.userAgent);

            // Create a visible prompt for mobile users
            const mobilePrompt = document.createElement('div');
            mobilePrompt.textContent = '👆 Tap the input field above to enter your name';
            mobilePrompt.style.cssText = `
                color: #ccc;
                font-size: 14px;
                text-align: center;
                margin-top: 5px;
                font-style: italic;
            `;
            inputContainer.appendChild(mobilePrompt);

            // Enhanced mobile touch handling
            this.nameInputField.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.nameInputField.focus();
            });

            this.nameInputField.addEventListener('touchend', (e) => {
                e.preventDefault();
                setTimeout(() => {
                    this.nameInputField.focus();
                    this.nameInputField.click();
                }, 10);
            });
        } else {
            // Desktop: Auto-focus like the original system
            setTimeout(() => {
                this.nameInputField.focus();
            }, 100);
        }

        // Standard focus handling
        this.nameInputField.addEventListener('focus', () => {
            // Ensure cursor is positioned correctly
            setTimeout(() => {
                const len = this.nameInputField.value.length;
                this.nameInputField.setSelectionRange(len, len);
            }, 10);
        });

        // Click handling for all devices
        this.nameInputField.addEventListener('click', (e) => {
            if (!this.nameInputField.matches(':focus')) {
                this.nameInputField.focus();
            }
        });

        // Add Enter key support
        this.nameInputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.submitName();
            }
        });
    }

    createNameInputField() {
        // Create name input field
        this.nameInputField = document.createElement('input');
        this.nameInputField.type = 'text';
        this.nameInputField.placeholder = 'Enter your name...';
        this.nameInputField.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 10px 20px;
            font-size: 18px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border: 2px solid #ff8040;
            border-radius: 5px;
            text-align: center;
            z-index: 1000;
        `;

        // Add event listeners
        this.nameInputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.submitName();
            }
        });

        // Mobile support: Add a submit button
        const submitButton = document.createElement('button');
        submitButton.textContent = 'Submit';
        submitButton.style.cssText = `
            position: fixed;
            top: 60%;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            font-size: 16px;
            background: rgba(255, 128, 64, 0.8);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 1000;
        `;

        submitButton.addEventListener('click', () => {
            this.submitName();
        });

        // Store reference to remove later
        this.nameSubmitButton = submitButton;

        document.body.appendChild(this.nameInputField);
        document.body.appendChild(submitButton);
        this.nameInputField.focus();
    }

    submitName() {
        // Use input value if user typed something, otherwise default to "Stranger"
        const inputValue = this.nameInputField.value.trim();
        const proposedName = (inputValue && this.hasUserInput) ? inputValue : 'Stranger';

        // Debug logging for desktop issue
        console.log("🎭 submitName() called:");
        console.log("  - inputValue:", inputValue);
        console.log("  - hasUserInput:", this.hasUserInput);
        console.log("  - proposedName:", proposedName);
        console.log("  - About to show confirmation dialogue");

        // Store the proposed name temporarily
        this.proposedPlayerName = proposedName;

        // Show confirmation dialogue
        this.showNameConfirmation();
    }

    showNameConfirmation() {
        console.log("🎭 showNameConfirmation() called for:", this.proposedPlayerName);

        // Set flag to prevent automatic Enter key handling
        this.isShowingNameConfirmation = true;
        this.nameConfirmationEnterBlocked = true; // Block Enter initially

        // Clear the input field and button
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
            console.log("🎭 Cleared dialogueOptionsContainer");
        } else {
            console.log("❌ dialogueOptionsContainer not found!");
        }

        // Show confirmation prompt
        const confirmationPrompt = `Your name is "${this.proposedPlayerName}"?`;
        if (this.dialogueTextElement) {
            this.dialogueTextElement.textContent = confirmationPrompt;
            console.log("🎭 Set confirmation prompt:", confirmationPrompt);
        } else {
            console.log("❌ dialogueTextElement not found!");
        }

        // Create confirmation buttons
        const confirmButton = document.createElement('button');
        confirmButton.textContent = '* Yes, that\'s correct.';
        confirmButton.classList.add('dialogue-option-button');
        confirmButton.onclick = () => this.confirmName();

        const editButton = document.createElement('button');
        editButton.textContent = '* No, let me change it.';
        editButton.classList.add('dialogue-option-button');
        editButton.onclick = () => this.editName();

        // Add touch support for mobile
        confirmButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("🎭 Confirm button touched");
            this.confirmName();
        });

        editButton.addEventListener('touchend', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("🎭 Edit button touched");
            this.editName();
        });

        // Add buttons to container
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.appendChild(confirmButton);
            this.dialogueOptionsContainer.appendChild(editButton);
            console.log("🎭 Added confirmation buttons to container");
            console.log("🎭 Container children count:", this.dialogueOptionsContainer.children.length);
        } else {
            console.log("❌ Cannot add buttons - dialogueOptionsContainer not found!");
        }

        // Set up keyboard navigation
        this.highlightedOptionIndex = 0;
        this._updateHighlight();
        this.isWaitingForAdvanceInput = false; // Allow arrow key navigation

        // Unblock Enter key after a short delay to prevent immediate triggering
        setTimeout(() => {
            this.nameConfirmationEnterBlocked = false;
            console.log("🎭 Enter key unblocked for name confirmation");
        }, 300); // 300ms delay

        console.log("🎭 Name confirmation dialogue setup complete");
    }

    confirmName() {
        // Prevent multiple calls
        if (!this.proposedPlayerName) {
            console.log("🎭 confirmName() called but no proposed name - ignoring");
            return;
        }

        console.log("🎭 confirmName() called - THIS SHOULD ONLY HAPPEN WHEN USER CLICKS 'YES'");
        console.log("🎭 Stack trace:", new Error().stack);
        console.log("Name confirmed:", this.proposedPlayerName);

        // Clear the confirmation flags
        this.isShowingNameConfirmation = false;
        this.nameConfirmationEnterBlocked = false;

        // Set the confirmed name
        this.playerName = this.proposedPlayerName;
        this.proposedPlayerName = null; // Clean up

        // Clear the dialogue options
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Hide the dialogue container
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }

        // Clean up input field reference
        this.nameInputField = null;

        // Show name on screen and then erase it
        this.showAndEraseName();
    }

    editName() {
        console.log("Player wants to edit name, returning to input");

        // Clear the confirmation flags
        this.isShowingNameConfirmation = false;
        this.nameConfirmationEnterBlocked = false;

        // Clean up any existing name display element that might be stuck on screen
        if (this.nameDisplayElement) {
            console.log("🎭 Removing stuck name display element");
            document.body.removeChild(this.nameDisplayElement);
            this.nameDisplayElement = null;
        }

        // Stop any name erasure animation
        this.isNameErasing = false;

        // Clear confirmation dialogue
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Reset highlight
        this.highlightedOptionIndex = -1;

        // Show the name input dialogue again
        this.showNameInputDialogue();

        // Pre-fill the input with the proposed name for editing
        if (this.nameInputField && this.proposedPlayerName !== 'Stranger') {
            this.nameInputField.value = this.proposedPlayerName;
            // Set cursor to end of text
            setTimeout(() => {
                this.nameInputField.focus();
                const len = this.nameInputField.value.length;
                this.nameInputField.setSelectionRange(len, len);
            }, 100);
        }

        // Clean up proposed name
        this.proposedPlayerName = null;
    }

    showAndEraseName() {
        console.log("🎭 showAndEraseName() called - THIS SHOULD ONLY HAPPEN AFTER NAME CONFIRMATION");
        console.log("🎭 Stack trace:", new Error().stack);
        console.log("🎭 Player name:", this.playerName);

        // Create name display element
        this.nameDisplayElement = document.createElement('div');
        this.nameDisplayElement.textContent = this.playerName;
        this.nameDisplayElement.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: #ff8040;
            font-weight: bold;
            text-align: center;
            z-index: 1000;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        `;

        document.body.appendChild(this.nameDisplayElement);

        // Start erasure animation after a brief pause
        setTimeout(() => {
            this.startNameErasureAnimation();
        }, 2000);
    }

    startNameErasureAnimation() {
        console.log("Starting name erasure animation...");

        // Prevent multiple erasure animations
        if (this.isNameErasing) {
            console.log("Name erasure already in progress, skipping...");
            return;
        }

        if (!this.nameDisplayElement) {
            console.log("No name display element found, skipping erasure...");
            return;
        }

        this.isNameErasing = true;

        const originalText = this.playerName || '';
        let currentText = originalText;
        let shakeIntensity = 1;

        // Safety check - if no text to erase, finish immediately
        if (!currentText || currentText.length === 0) {
            console.log("No text to erase, finishing name erasure immediately");
            this.finishNameErasure();
            return;
        }

        const eraseInterval = setInterval(() => {
            // Add shaking effect
            const shakeX = (Math.random() - 0.5) * shakeIntensity * 10;
            const shakeY = (Math.random() - 0.5) * shakeIntensity * 10;
            this.nameDisplayElement.style.transform = `translate(calc(-50% + ${shakeX}px), calc(-50% + ${shakeY}px))`;

            // Randomly remove characters - with null safety check
            if (currentText && currentText.length > 0 && Math.random() < 0.3) {
                const randomIndex = Math.floor(Math.random() * currentText.length);
                currentText = currentText.slice(0, randomIndex) + currentText.slice(randomIndex + 1);
                this.nameDisplayElement.textContent = currentText;

                // Play erasure sound for each character removed
                this.audioManager?.playSound('erasure', false, 0.4);
            }

            // Increase shake intensity over time
            shakeIntensity += 0.1;

            // When all characters are gone, finish the animation
            if (!currentText || currentText.length === 0) {
                clearInterval(eraseInterval);
                this.finishNameErasure();
            }
        }, 100);
    }

    finishNameErasure() {
        // Remove name display element
        if (this.nameDisplayElement) {
            document.body.removeChild(this.nameDisplayElement);
            this.nameDisplayElement = null;
        }

        this.isNameErasing = false;

        // Show the dialogue container for the name erasure dialogue
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
        }

        // Clear any existing options
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Choose dialogue based on player name (special names, stranger, or default)
        const isStranger = (this.playerName === 'Stranger');
        const specialReaction = specialNameReactions[this.playerName];

        let dialogueToUse;
        let cheatToActivate = null;

        if (specialReaction) {
            // Special name detected - use custom dialogue and activate cheat
            dialogueToUse = specialReaction.dialogue;
            cheatToActivate = specialReaction.cheat;
            console.log(`🎮 Special name detected: "${this.playerName}" - Cheat: ${cheatToActivate}`);
        } else if (isStranger) {
            // No name entered - use stranger dialogue
            dialogueToUse = strangerDialogue;
        } else {
            // Regular name - use default erasure dialogue
            dialogueToUse = nameEraseDialogue;
        }

        // Activate cheat if specified
        if (cheatToActivate) {
            this._activateCheat(cheatToActivate);
            // Change fire to blue when any cheat is activated
            this._changeFireToBlue();
        }

        // Start the appropriate dialogue sequence
        this.currentNameEraseIndex = 0;
        this.currentNameEraseDialogue = dialogueToUse; // Store reference for nextNameEraseLine
        this.typeDialogueLine(dialogueToUse[this.currentNameEraseIndex], this.nextNameEraseLine.bind(this), undefined, 'nameErase');
    }

    nextNameEraseLine() {
        this.currentNameEraseIndex++;
        if (this.currentNameEraseIndex < this.currentNameEraseDialogue.length) {
            this.typeDialogueLine(this.currentNameEraseDialogue[this.currentNameEraseIndex], this.nextNameEraseLine.bind(this), undefined, 'nameErase');
        } else {
            console.log("Name erase dialogue complete, starting cup phase...");
            this.startCupPhase();
        }
    }

    startCupPhase() {
        console.log("Starting cup phase...");
        this.currentPhase = 'cupChoice';

        // Show the dialogue container for the cup intro
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
        }

        // Clear any existing options
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Initialize cup intro dialogue index
        this.currentCupIntroIndex = 0;
        this.typeDialogueLine(cupIntroDialogue[this.currentCupIntroIndex], this.nextCupIntroLine.bind(this), 50, 'cupIntro');
    }

    nextCupIntroLine() {
        this.currentCupIntroIndex++;
        if (this.currentCupIntroIndex < cupIntroDialogue.length) {
            // Keep fire normal during cup dialogue - face animation only comes after drinking
            const currentLine = cupIntroDialogue[this.currentCupIntroIndex];

            // Continue with next cup intro dialogue line
            this.typeDialogueLine(currentLine, this.nextCupIntroLine.bind(this), 50, 'cupIntro');
        } else {
            // All cup intro dialogue complete, show cup rising
            this.showCupRising();
        }
    }

    showCupRising() {
        console.log("Showing cup rising animation...");

        // Prevent multiple cup animations
        if (this.cupAnimation.active) {
            console.log("Cup animation already active, skipping...");
            return;
        }

        this.createCupMesh();
        this.startCupAnimation();

        // Show choices immediately after a short delay, don't wait for full animation
        setTimeout(() => {
            console.log("Showing cup choices after short delay...");
            this.showCupChoices();
        }, 1000); // Reduced from 2000ms to 1000ms for faster progression
    }

    createCupMesh() {
        // Create a simple cup mesh with enhanced visibility
        const cupGeometry = new THREE.CylinderGeometry(0.15, 0.1, 0.3, 8);
        const cupMaterial = new THREE.MeshStandardMaterial({
            color: 0x4a4a4a, // Lighter silver for better visibility
            metalness: 0.8, // High metalness for realistic metal look
            roughness: 0.2, // Low roughness for shiny surface
            emissive: 0x111111, // Subtle self-illumination to prevent pure black shadows
            emissiveIntensity: 0.1 // Gentle glow to maintain visibility
        });

        this.cupMesh = new THREE.Mesh(cupGeometry, cupMaterial);
        this.cupMesh.position.set(0, -2, 0); // Start below ground
        this.cupMesh.castShadow = true;
        this.cupMesh.receiveShadow = false; // Prevent cup from receiving dark shadows

        // Add liquid inside with enhanced glow
        const liquidGeometry = new THREE.CylinderGeometry(0.12, 0.08, 0.25, 8);
        const liquidMaterial = new THREE.MeshStandardMaterial({
            color: 0xffd700, // Liquid gold
            transparent: true,
            opacity: 0.9, // Slightly more opaque
            emissive: 0x664400, // Stronger golden glow
            emissiveIntensity: 0.3, // Brighter self-illumination
            metalness: 0.1,
            roughness: 0.1
        });

        const liquidMesh = new THREE.Mesh(liquidGeometry, liquidMaterial);
        liquidMesh.position.y = 0.02; // Slightly above cup bottom
        liquidMesh.castShadow = false; // Liquid doesn't cast shadows
        liquidMesh.receiveShadow = false; // Liquid doesn't receive shadows
        this.cupMesh.add(liquidMesh);

        // Add a subtle point light attached to the cup for better illumination
        this.cupLight = new THREE.PointLight(0xffd700, 0.8, 3, 2);
        this.cupLight.position.set(0, 0.1, 0); // Slightly above cup center
        this.cupMesh.add(this.cupLight);

        // Create flaming hand first
        this._createFlamingHand();

        // Attach cup to the flaming hand instead of scene
        this.cupMesh.position.set(0, 0.3, 0.15); // Position relative to hand
        this.cupMesh.scale.setScalar(0.8); // Slightly smaller to fit in hand
        this.flamingHand.add(this.cupMesh); // Attach cup to hand
    }

    startCupAnimation() {
        // Cup is now held by flaming hand, no separate animation needed
        this.cupAnimation.active = false;
        console.log("Cup is held by flaming hand - no separate cup animation");
    }

    _removeCupFromHand() {
        console.log("Removing cup from flaming hand...");

        if (this.cupMesh && this.flamingHand) {
            // Remove cup from flaming hand
            this.flamingHand.remove(this.cupMesh);

            // Optionally add cup back to scene or just dispose of it
            // For now, we'll just dispose of it since the player has "drunk" from it
            if (this.cupMesh.geometry) this.cupMesh.geometry.dispose();
            if (this.cupMesh.material) {
                if (Array.isArray(this.cupMesh.material)) {
                    this.cupMesh.material.forEach(material => material.dispose());
                } else {
                    this.cupMesh.material.dispose();
                }
            }

            this.cupMesh = null;
            this.cupLight = null; // Light was attached to cup
            console.log("Cup removed and disposed from flaming hand");
        }
    }

    _startEarthquakeEffect() {
        console.log("🌍 Starting earthquake camera shake effect");

        // Store the original camera position if not already stored
        if (!this.earthquakeEffect.basePosition) {
            this.earthquakeEffect.basePosition = this.sceneManager.camera.position.clone();
        }

        this.earthquakeEffect.active = true;
        this.earthquakeEffect.intensity = 0.25; // Much stronger shake intensity
        this.earthquakeEffect.startTime = Date.now();
    }

    _stopEarthquakeEffect() {
        console.log("🌍 Stopping earthquake camera shake effect");

        this.earthquakeEffect.active = false;
        this.earthquakeEffect.intensity = 0;

        // Restore original camera position
        if (this.earthquakeEffect.basePosition) {
            this.sceneManager.camera.position.copy(this.earthquakeEffect.basePosition);
        }
    }

    _updateEarthquakeEffect(deltaTime, time, camera) {
        if (!this.earthquakeEffect.active) return;

        // Store base position when earthquake starts
        if (!this.earthquakeEffect.basePosition) {
            this.earthquakeEffect.basePosition = camera.position.clone();
            console.log("🌍 Earthquake base position stored:", this.earthquakeEffect.basePosition);
        }

        // Calculate elapsed time since earthquake started
        const elapsed = Date.now() - this.earthquakeEffect.startTime;

        // Gradually increase intensity over first 1 second, then maintain at full intensity
        let currentIntensity = this.earthquakeEffect.intensity;
        if (elapsed < 1000) {
            currentIntensity = this.earthquakeEffect.intensity * (elapsed / 1000);
        }

        // Make shake much more intense and visible
        const intensityMultiplier = 3.0; // Make it 3x more intense
        currentIntensity *= intensityMultiplier;

        // Multi-layered shake for dramatic earthquake feel
        const primaryShake = Math.sin(time * 20) * currentIntensity; // Faster, more intense
        const secondaryShake = Math.sin(time * 12) * currentIntensity * 0.8; // Medium shake
        const tertiaryShake = Math.sin(time * 35) * currentIntensity * 0.5; // Very fast shake
        const randomShake = (Math.random() - 0.5) * currentIntensity * 1.2; // More random

        // Apply dramatic shake to all axes
        const shakeX = (primaryShake + secondaryShake * 0.7 + randomShake) * 1.0;
        const shakeY = (secondaryShake + tertiaryShake + randomShake * 0.6) * 0.8;
        const shakeZ = (primaryShake * 0.8 + tertiaryShake + randomShake * 1.0) * 1.0;

        // Apply shake relative to base position
        camera.position.x = this.earthquakeEffect.basePosition.x + shakeX;
        camera.position.y = this.earthquakeEffect.basePosition.y + shakeY;
        camera.position.z = this.earthquakeEffect.basePosition.z + shakeZ;
    }

    _startCorruptionEffect() {
        console.log("🌑 Starting world dissolution effect - everything fades into the void!");

        this.corruptionEffect.active = true;
        this.corruptionEffect.startTime = Date.now();

        // Store original sky and fog colors
        if (this.scene.fog) {
            this.corruptionEffect.originalFogColor = this.scene.fog.color.clone();
        }
        if (this.scene.background) {
            this.corruptionEffect.originalSkyColor = this.scene.background.clone();
        }

        // Create shadow circles emanating from fire
        this._createShadowCircles();

        // Collect all world objects for dissolution (except campfire)
        this._collectWorldObjectsForDissolution();
    }

    _stopCorruptionEffect() {
        console.log("🌑 Stopping world dissolution effect");

        this.corruptionEffect.active = false;

        // Restore original colors
        if (this.scene.fog && this.corruptionEffect.originalFogColor) {
            this.scene.fog.color.copy(this.corruptionEffect.originalFogColor);
        }
        if (this.scene.background && this.corruptionEffect.originalSkyColor) {
            this.scene.background.copy(this.corruptionEffect.originalSkyColor);
        }

        // Clean up shadow circles
        this.corruptionEffect.shadowCircles.forEach(circle => {
            if (circle.parent) circle.parent.remove(circle);
            if (circle.geometry) circle.geometry.dispose();
            if (circle.material) circle.material.dispose();
        });
        this.corruptionEffect.shadowCircles = [];

        // Restore object opacities and colors
        this.corruptionEffect.dissolvingObjects.forEach(obj => {
            const originalOpacity = this.corruptionEffect.originalObjectOpacities.get(obj);
            if (originalOpacity !== undefined && obj.material) {
                if (Array.isArray(obj.material)) {
                    obj.material.forEach(mat => {
                        // Restore opacity
                        mat.opacity = originalOpacity;
                        mat.transparent = originalOpacity < 1.0;

                        // Restore original colors if they were stored
                        if (mat.userData.originalColor) {
                            mat.color.copy(mat.userData.originalColor);
                        }
                        if (mat.userData.originalEmissive) {
                            mat.emissive.copy(mat.userData.originalEmissive);
                        }

                        mat.needsUpdate = true;
                    });
                } else {
                    // Restore opacity
                    obj.material.opacity = originalOpacity;
                    obj.material.transparent = originalOpacity < 1.0;

                    // Restore original colors if they were stored
                    if (obj.material.userData.originalColor) {
                        obj.material.color.copy(obj.material.userData.originalColor);
                    }
                    if (obj.material.userData.originalEmissive) {
                        obj.material.emissive.copy(obj.material.userData.originalEmissive);
                    }

                    obj.material.needsUpdate = true;
                }
            }
            obj.visible = true;
        });

        this.corruptionEffect.dissolvingObjects = [];
        this.corruptionEffect.originalObjectOpacities.clear();
    }

    _createShadowCircles() {
        // Create expanding shadow circles emanating from the fire
        for (let i = 0; i < 5; i++) {
            const circleGeometry = new THREE.RingGeometry(0.1, 0.2, 32);
            const circleMaterial = new THREE.ShaderMaterial({
                uniforms: {
                    time: { value: 0 },
                    opacity: { value: 0.8 },
                    innerRadius: { value: 0.1 },
                    outerRadius: { value: 0.2 }
                },
                vertexShader: `
                    varying vec2 vUv;
                    void main() {
                        vUv = uv;
                        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                    }
                `,
                fragmentShader: `
                    uniform float time;
                    uniform float opacity;
                    uniform float innerRadius;
                    uniform float outerRadius;
                    varying vec2 vUv;

                    void main() {
                        vec2 center = vec2(0.5);
                        float dist = distance(vUv, center) * 2.0;

                        // Create ring effect
                        float ring = smoothstep(innerRadius - 0.1, innerRadius, dist) *
                                   (1.0 - smoothstep(outerRadius, outerRadius + 0.1, dist));

                        // Pulsing effect
                        float pulse = sin(time * 3.0) * 0.3 + 0.7;

                        gl_FragColor = vec4(0.0, 0.0, 0.0, ring * opacity * pulse);
                    }
                `,
                transparent: true,
                side: THREE.DoubleSide,
                depthWrite: false,
                blending: THREE.MultiplyBlending
            });

            const shadowCircle = new THREE.Mesh(circleGeometry, circleMaterial);
            shadowCircle.position.set(0, -1.4, 0); // At ground level near fire
            shadowCircle.rotation.x = -Math.PI / 2; // Lay flat on ground
            shadowCircle.userData = {
                startTime: Date.now() + i * 500, // Stagger the circles
                maxRadius: 15 + i * 5, // Different max sizes
                speed: 2 + i * 0.5 // Different expansion speeds
            };

            this.scene.add(shadowCircle);
            this.corruptionEffect.shadowCircles.push(shadowCircle);
        }
    }

    _collectWorldObjectsForDissolution() {
        console.log("🌑 Collecting world objects for dissolution...");

        // Traverse the scene and collect all objects except the campfire and firewood
        this.scene.traverse((object) => {
            // Skip the campfire, fire flames, and firewood logs
            if (object === this.fireGroup ||
                (this.fireGroup && this.fireGroup.children.includes(object)) ||
                object.name === 'campfire' ||
                object.name === 'fire' ||
                object.name === 'fireGroup' ||
                object.name === 'campfireLogs' ||
                object.name === 'firewood' ||
                object.name?.includes('log') ||
                object.name?.includes('wood') ||
                object.userData?.isFireRelated ||
                object.userData?.isFirewood) {
                console.log("🔥 Preserving fire-related object:", object.name, object.constructor.name);
                return; // Skip fire-related objects and firewood
            }

            // Skip cameras, lights, and other non-visual objects
            if (object instanceof THREE.Camera ||
                object instanceof THREE.Light ||
                object instanceof THREE.Scene ||
                !object.material) {
                return;
            }

            // Include stars and sky objects for dissolution
            if (object.name?.includes('star') ||
                object.userData?.isStar ||
                object.userData?.isSkyObject) {
                console.log("🌟 Found star object to dissolve:", object.name);
            }

            // Collect objects that should dissolve (including stars, meshes, and points)
            if ((object instanceof THREE.Mesh || object instanceof THREE.Points) && object.visible) {
                // Store original opacity and prepare materials for luma fade
                if (Array.isArray(object.material)) {
                    // For multi-material objects, store the first material's opacity
                    const firstMat = object.material[0];
                    this.corruptionEffect.originalObjectOpacities.set(object, firstMat.opacity || 1.0);

                    // Prepare all materials for luma fade
                    object.material.forEach(mat => {
                        this._prepareMaterialForLumaFade(mat);
                    });
                } else {
                    this.corruptionEffect.originalObjectOpacities.set(object, object.material.opacity || 1.0);
                    this._prepareMaterialForLumaFade(object.material);
                }

                this.corruptionEffect.dissolvingObjects.push(object);

                // Log star objects specifically
                if (object.name === "stars" || object instanceof THREE.Points) {
                    console.log("🌟 Added stars to dissolution list:", object.name, object.constructor.name);
                }
            }
        });

        console.log(`🌑 Found ${this.corruptionEffect.dissolvingObjects.length} objects to dissolve`);

        // Log what we're preserving vs dissolving
        console.log("🔥 Preserving: fireGroup, campfireLogs, and their children");
        console.log("🌑 Dissolving: trees, grass, floor, walls, stars, and all other objects");
    }

    _updateCorruptionEffect(deltaTime, time, camera) {
        if (!this.corruptionEffect.active) return;

        const elapsed = Date.now() - this.corruptionEffect.startTime;
        const progress = Math.min(elapsed / 8000, 1); // 8 second progression

        // Phase 1 (0-0.375): Earthquake shake + sky turns red (0-3 seconds)
        // Phase 2 (0.375-0.75): Pause phase - no luma fade (3-6 seconds)
        // Phase 3 (0.75-1.0): Everything turns to pure black void (6-8 seconds)

        // Debug timing every 500ms
        if (Math.floor(elapsed / 500) !== Math.floor((elapsed - 16) / 500)) {
            console.log(`🌑 Corruption progress: ${(progress * 100).toFixed(1)}% (${(elapsed/1000).toFixed(1)}s) - Phase: ${progress < 0.375 ? '1 (Red Sky + Earthquake)' : progress < 0.75 ? '2 (Pause)' : '3 (Void)'}`);
        }

        if (progress < 0.375) {
            // Phase 1: Gradually turn sky red and fog dark (0-3 seconds)
            const redProgress = progress / 0.375;
            if (this.scene.fog) {
                const targetFogColor = new THREE.Color(0x330000); // Dark red
                if (this.corruptionEffect.originalFogColor) {
                    this.scene.fog.color.lerpColors(this.corruptionEffect.originalFogColor, targetFogColor, redProgress);
                }
            }

            if (this.scene.background) {
                const targetSkyColor = new THREE.Color(0x660000); // Red sky
                if (this.corruptionEffect.originalSkyColor) {
                    this.scene.background.lerpColors(this.corruptionEffect.originalSkyColor, targetSkyColor, redProgress);
                }
            }
        } else if (progress < 0.75) {
            // Phase 2: Pause phase - maintain red sky, no object dissolution (3-6 seconds)
            // Keep sky and fog at red color, no changes to objects
            if (this.scene.fog) {
                const targetFogColor = new THREE.Color(0x330000); // Keep dark red
                this.scene.fog.color.copy(targetFogColor);
            }

            if (this.scene.background) {
                const targetSkyColor = new THREE.Color(0x660000); // Keep red sky
                this.scene.background.copy(targetSkyColor);
            }
        } else {
            // Phase 3: Complete fade to black void (6-8 seconds)
            const voidProgress = (progress - 0.75) / 0.25; // 0 to 1 over phase 3

            // Fade sky and fog to black quickly (first 25% of void phase)
            const skyFadeProgress = Math.min(voidProgress * 4, 1); // 4x speed for first quarter

            if (this.scene.fog) {
                const blackColor = new THREE.Color(0x000000);
                const redColor = new THREE.Color(0x330000);
                this.scene.fog.color.lerpColors(redColor, blackColor, skyFadeProgress);
            }

            if (this.scene.background) {
                const blackColor = new THREE.Color(0x000000);
                const redColor = new THREE.Color(0x660000);
                this.scene.background.lerpColors(redColor, blackColor, skyFadeProgress);
            }

            // Fade objects to black (starts after sky is mostly black)
            const objectFadeProgress = Math.max(0, (voidProgress - 0.25) / 0.75); // Start after 25% of void phase

            this.corruptionEffect.dissolvingObjects.forEach(obj => {
                if (obj.material) {
                    if (Array.isArray(obj.material)) {
                        obj.material.forEach(mat => {
                            this._applyFinalVoidFade(mat, objectFadeProgress);
                        });
                    } else {
                        this._applyFinalVoidFade(obj.material, objectFadeProgress);
                    }
                }
            });

            // Stop earthquake effect when void phase completes (after 8 seconds total)
            if (voidProgress >= 1.0 && this.earthquakeEffect.active) {
                console.log("🌍 Void phase complete - stopping earthquake effect");
                this._stopEarthquakeEffect();
            }
        }

        // Check if animation is complete and video is pending
        if (progress >= 1.0 && this.pendingVideoStart) {
            console.log("🌑 Corruption animation complete - starting pending video");
            this.pendingVideoStart = false;
            this._stopCorruptionEffect();
            this._startEndIntroVideoSequence();
        }

        // Update shadow circles
        this.corruptionEffect.shadowCircles.forEach((circle, index) => {
            const circleElapsed = Date.now() - circle.userData.startTime;
            if (circleElapsed > 0) {
                const circleProgress = Math.min(circleElapsed / 5000, 1); // 5 second expansion
                const currentRadius = circleProgress * circle.userData.maxRadius;

                // Update geometry
                circle.geometry.dispose();
                circle.geometry = new THREE.RingGeometry(currentRadius * 0.8, currentRadius, 32);

                // Update shader uniforms
                circle.material.uniforms.time.value = time;
                circle.material.uniforms.innerRadius.value = 0.8;
                circle.material.uniforms.outerRadius.value = 1.0;
                circle.material.uniforms.opacity.value = (1 - circleProgress) * 0.8;
            }
        });
    }

    _applyLumaFade(material, fadeProgress, originalOpacity) {
        // Store original colors if not already stored
        if (!material.userData.originalColor) {
            material.userData.originalColor = material.color ? material.color.clone() : new THREE.Color(1, 1, 1);
        }
        if (!material.userData.originalEmissive) {
            material.userData.originalEmissive = material.emissive ? material.emissive.clone() : new THREE.Color(0, 0, 0);
        }

        // Smooth easing for more cinematic fade
        const easedProgress = this._easeInOutCubic(fadeProgress);

        // Luma fade: gradually reduce brightness while maintaining color relationships
        const lumaFactor = 1 - easedProgress;

        // Apply luma fade to main color
        if (material.color) {
            material.color.copy(material.userData.originalColor);
            material.color.multiplyScalar(lumaFactor);
        }

        // Apply luma fade to emissive (for fire particles and glowing objects)
        if (material.emissive) {
            material.emissive.copy(material.userData.originalEmissive);
            material.emissive.multiplyScalar(lumaFactor);
        }

        // Gradually reduce opacity for transparency fade
        material.transparent = true;
        material.opacity = originalOpacity * lumaFactor;

        // Ensure material updates
        material.needsUpdate = true;
    }

    _applyFinalVoidFade(material, voidProgress) {
        // Final phase: fade everything to pure black
        const easedProgress = this._easeInOutCubic(voidProgress);
        const remainingBrightness = 1 - easedProgress;

        // Fade to black
        if (material.color) {
            if (!material.userData.originalColor) {
                material.userData.originalColor = material.color.clone();
            }
            material.color.copy(material.userData.originalColor);
            material.color.multiplyScalar(remainingBrightness);
        }

        // Fade emissive to black
        if (material.emissive) {
            if (!material.userData.originalEmissive) {
                material.userData.originalEmissive = material.emissive.clone();
            }
            material.emissive.copy(material.userData.originalEmissive);
            material.emissive.multiplyScalar(remainingBrightness);
        }

        // Final opacity fade
        material.transparent = true;
        material.opacity = remainingBrightness;

        // Make completely invisible when fully faded
        if (easedProgress >= 0.95) {
            material.opacity = 0;
            if (material.parent) {
                material.parent.visible = false;
            }
        }

        material.needsUpdate = true;
    }

    _prepareMaterialForLumaFade(material) {
        // Store original colors for luma fade
        if (material.color && !material.userData.originalColor) {
            material.userData.originalColor = material.color.clone();
        }
        if (material.emissive && !material.userData.originalEmissive) {
            material.userData.originalEmissive = material.emissive.clone();
        }

        // Ensure material can be made transparent
        if (material.opacity === undefined) {
            material.opacity = 1.0;
        }
    }

    _easeInOutCubic(t) {
        // Smooth cubic easing for cinematic effect
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    showCupChoices() {
        console.log("Showing cup choices...");
        console.log("Cup choice data:", cupChoiceData);

        // Prevent multiple choice button sets
        if (this.dialogueOptionsContainer && this.dialogueOptionsContainer.children.length > 0) {
            console.log("Cup choices already displayed, skipping...");
            return;
        }

        this.createCupChoiceDialogue();
    }

    createCupChoiceDialogue() {
        console.log("Creating cup choice dialogue...");

        // Show the dialogue container
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.remove('hidden');
        }

        // Set the prompt text in the dialogue box
        if (this.dialogueTextElement) {
            this.dialogueTextElement.textContent = cupChoiceData.prompt;
        }

        // Clear any existing options
        if (this.dialogueOptionsContainer) {
            this.dialogueOptionsContainer.innerHTML = '';
        }

        // Create dialogue option buttons in the old style
        this.highlightedOptionIndex = -1;
        cupChoiceData.options.forEach((option, index) => {
            console.log(`Creating dialogue option ${index}:`, option.text);
            const button = document.createElement('button');
            button.classList.add('dialogue-option-button');
            button.textContent = `* ${option.text}`;

            // Add hover events for highlighting (desktop)
            button.addEventListener('mouseenter', () => {
                this.highlightedOptionIndex = index;
                this._updateHighlight();
            });
            button.addEventListener('mouseleave', () => {
                this.highlightedOptionIndex = -1;
                this._updateHighlight();
            });

            // Add click handler for desktop
            button.onclick = () => this.selectCupChoiceDialogue(index);

            // Add touch support for mobile
            button.addEventListener('touchstart', (e) => {
                e.preventDefault();
                this.highlightedOptionIndex = index;
                this._updateHighlight();
            });

            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.selectCupChoiceDialogue(index);
            });

            if (this.dialogueOptionsContainer) {
                this.dialogueOptionsContainer.appendChild(button);
            }
        });

        // Auto-highlight first option after a brief delay
        setTimeout(() => {
            if (this.dialogueOptionsContainer && this.dialogueOptionsContainer.children.length > 0) {
                this.highlightedOptionIndex = 0;
                this._updateHighlight();
            }
        }, 100);

        // Set waiting state to false so arrow keys work
        this.isWaitingForAdvanceInput = false;
    }

    selectCupChoiceDialogue(choiceIndex) {
        console.log("Cup choice selected via dialogue:", choiceIndex);

        if (choiceIndex === 0) {
            // Option 1 selected - proceed normally
            this.selectedCupChoice = choiceIndex;

            // Clear the options
            if (this.dialogueOptionsContainer) {
                this.dialogueOptionsContainer.innerHTML = '';
            }

            // Reset highlight
            this.highlightedOptionIndex = -1;

            // Start drinking animation to simulate player drinking
            this._startDrinkingAnimation();

            // Show Flicker's response
            const response = cupChoiceData.options[choiceIndex].response;

            // Remove cup from flaming hand when showing "Attaboy. It'll only sting a little"
            if (response && response.includes("Attaboy")) {
                this._removeCupFromHand();
            }

            this.typeDialogueLine(response, this.startTransitionPhase.bind(this), 50, 'cupResponse');
        } else {
            // Option 2 or 3 selected - trigger forced choice mechanism
            console.log("Non-first option selected, starting text erasure...");

            // Play erasure2 sound when player selects "Refuse, but stay seated" or "Close your eyes"
            this.audioManager?.playSound('erasure2', false, 0.6);

            this.startOptionTextErasure(choiceIndex);
        }
    }

    startOptionTextErasure(choiceIndex) {
        // CRITICAL FIX: Prevent multiple erasure animations from being triggered
        if (this.isTextErasing) {
            console.log("Text erasure already in progress, ignoring additional attempts");
            return;
        }

        // Set flag to prevent multiple erasures
        this.isTextErasing = true;
        console.log("Starting text erasure, blocking further attempts");

        // Disable input during erasure
        this.isWaitingForAdvanceInput = false;

        // Get the selected option button
        const optionButtons = this.dialogueOptionsContainer.children;
        const selectedButton = optionButtons[choiceIndex];

        if (!selectedButton) {
            console.error("Could not find selected option button for erasure");
            this.isTextErasing = false; // Reset flag on error
            return;
        }

        // Store original text and start erasure
        const originalText = cupChoiceData.options[choiceIndex].text;
        const targetText = "Take the drink.";

        console.log(`Starting erasure of "${originalText}" to "${targetText}"`);

        // Start the erasure animation
        this.eraseAndReplaceOptionText(selectedButton, originalText, targetText, () => {
            // After erasure is complete, proceed as if option 1 was selected
            console.log("Text erasure complete, proceeding as option 1");
            this.selectedCupChoice = 0; // Force to option 1

            // Clear all options
            if (this.dialogueOptionsContainer) {
                this.dialogueOptionsContainer.innerHTML = '';
            }

            // Reset highlight
            this.highlightedOptionIndex = -1;

            // Reset erasure flag
            this.isTextErasing = false;
            console.log("Text erasure flag reset, allowing future erasures");

            // Start drinking animation since player is forced to drink
            this._startDrinkingAnimation();

            // Show Flicker's response for option 1
            const response = cupChoiceData.options[0].response;

            // Remove cup from flaming hand when showing "Attaboy. It'll only sting a little"
            if (response && response.includes("Attaboy")) {
                this._removeCupFromHand();
            }

            this.typeDialogueLine(response, this.startTransitionPhase.bind(this), 50, 'cupResponse');
        });
    }

    eraseAndReplaceOptionText(buttonElement, originalText, targetText, onComplete) {
        let currentText = originalText;
        const eraseSpeed = 100; // Slower for creepy effect
        const replaceSpeed = 80; // Slightly faster for replacement

        // Phase 1: Erase the original text character by character
        const eraseChar = () => {
            if (currentText.length > 0) {
                currentText = currentText.slice(0, -1);
                buttonElement.textContent = `* ${currentText}`;
                setTimeout(eraseChar, eraseSpeed);
            } else {
                // Phase 2: Replace with target text character by character
                let replaceIndex = 0;
                const replaceChar = () => {
                    if (replaceIndex < targetText.length) {
                        currentText = targetText.slice(0, replaceIndex + 1);
                        buttonElement.textContent = `* ${currentText}`;
                        replaceIndex++;
                        setTimeout(replaceChar, replaceSpeed);
                    } else {
                        // Erasure and replacement complete
                        setTimeout(onComplete, 500); // Brief pause before proceeding
                    }
                };
                setTimeout(replaceChar, 200); // Brief pause between erase and replace
            }
        };

        setTimeout(eraseChar, 300); // Brief pause before starting erasure
    }

    createCupChoiceButtons() {
        console.log("Creating cup choice buttons...");
        // Clear any existing dialogue
        if (this.dialogueTextElement) this.dialogueTextElement.textContent = '';

        // Show prompt
        const promptElement = document.createElement('div');
        promptElement.textContent = cupChoiceData.prompt;
        promptElement.style.cssText = `
            position: fixed;
            top: 30%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #ff8040;
            text-align: center;
            z-index: 1000;
            margin-bottom: 20px;
        `;
        console.log("Adding prompt element:", promptElement.textContent);
        document.body.appendChild(promptElement);

        // Create choice buttons
        const choicesContainer = document.createElement('div');
        choicesContainer.className = 'cup-choices-container';
        choicesContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        `;

        console.log("Creating buttons for", cupChoiceData.options.length, "options");
        cupChoiceData.options.forEach((option, index) => {
            console.log(`Creating button ${index}:`, option.text);
            const button = document.createElement('button');
            button.textContent = option.text;
            button.style.cssText = `
                padding: 15px 30px;
                font-size: 18px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border: 2px solid #ff8040;
                border-radius: 5px;
                cursor: pointer;
                transition: all 0.3s ease;
            `;

            button.addEventListener('mouseenter', () => {
                button.style.backgroundColor = 'rgba(255, 128, 64, 0.2)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
            });

            button.addEventListener('click', () => {
                console.log(`Cup choice ${index} clicked`);
                this.selectCupChoice(index, promptElement, choicesContainer);
            });

            choicesContainer.appendChild(button);
        });

        console.log("Adding choices container to document body");
        document.body.appendChild(choicesContainer);
    }

    selectCupChoice(choiceIndex, promptElement, choicesContainer) {
        console.log("Cup choice selected:", choiceIndex);
        this.selectedCupChoice = choiceIndex;

        // Remove choice UI
        if (promptElement) document.body.removeChild(promptElement);
        if (choicesContainer) document.body.removeChild(choicesContainer);

        // Show Flicker's response
        const response = cupChoiceData.options[choiceIndex].response;
        this.typeDialogueLine(response, this.startTransitionPhase.bind(this), 50, 'cupResponse');
    }

    startTransitionPhase() {
        console.log("Starting transition phase...");
        this.currentPhase = 'transition';
        this.currentTransitionIndex = 0;

        // Remove cup from scene
        if (this.cupMesh) {
            this.sceneManager.scene.remove(this.cupMesh);
            this.cupMesh = null;
        }

        // Remove flaming hand when player drinks
        if (this.flamingHand) {
            this.scene.remove(this.flamingHand);
            this.flamingHand = null;
            this.handLight = null;
            console.log("Flaming hand removed after drinking");
        }

        // Fire face will be triggered during the final "See you… deeper down." line

        // Play drink sound and fade out ambient music when the first transition line starts ("Hmm. You've got a shape now...")
        const firstLine = transitionDialogue[this.currentTransitionIndex];
        if (firstLine && firstLine.includes("Hmm. You've got a shape now")) {
            console.log("🍷 Playing drink sound for transition start...");
            this.audioManager?.playSound('drink', false, 0.6);

            // Rapidly fade out and permanently stop ambient music when "Hmm. You've got a shape now..." starts
            console.log("🎵 Rapidly fading out ambient music as 'Hmm. You've got a shape now...' line starts...");
            this.audioManager?.fadeVolume('bg_ambient_music', 0, 1000); // 1 second rapid fade

            // Permanently stop the music after fade completes
            setTimeout(() => {
                this.audioManager?.stopSound('bg_ambient_music');
                console.log("🎵 Ambient music permanently stopped after 'Hmm. You've got a shape now...' line");
            }, 1000);
        }

        this.typeDialogueLine(transitionDialogue[this.currentTransitionIndex], this.nextTransitionLine.bind(this), 50, 'transition');
    }

    nextTransitionLine() {
        this.currentTransitionIndex++;
        if (this.currentTransitionIndex < transitionDialogue.length) {
            const currentLine = transitionDialogue[this.currentTransitionIndex];

            // Play opening sound when the "..." line after "favorite song" starts
            if (currentLine === "..." && this.currentTransitionIndex === 9) {
                console.log("🎵 Playing opening sound as ellipsis after favorite song starts...");
                this.audioManager?.playSound('opening', false, 0.7);
            }

            // Note: Ambient music is now stopped earlier during "Attaboy..." line
            // No need to fade out music here since it's already been stopped

            // Type the line first, then check for special effects
            this.typeDialogueLine(currentLine, () => {
                // Check if this is "The soul is too heavy..." line - start earthquake effect
                if (currentLine.includes("The soul is too heavy for where you're about to go.")) {
                    console.log("🌍 Starting earthquake and corruption effects - the shadow dimension is invading!");
                    this._startEarthquakeEffect();
                    this._startCorruptionEffect();
                }

                // Check if this is the "..." line after "favorite song" - trigger new video sequence
                if (currentLine === "..." && this.currentTransitionIndex === 9) {
                    console.log("Ellipsis after favorite song line reached - checking if animation needs to complete!");

                    // Check if corruption effect is still active (animation still running)
                    if (this.corruptionEffect.active) {
                        console.log("🌑 Corruption animation still running - waiting for completion before starting video");

                        // Set flag to indicate video should start when animation completes
                        this.pendingVideoStart = true;

                        // Disable player input during wait
                        this.isWaitingForAdvanceInput = false;
                        this.automaticSequenceActive = true;

                        // Don't start video yet - wait for animation to complete
                        // The _updateCorruptionEffect method will check for pendingVideoStart
                    } else {
                        console.log("🌑 Corruption animation already complete - starting video immediately");
                        this._startEndIntroVideoSequence();
                    }

                    // Don't call nextTransitionLine - sequence is automatic now
                } else if (currentLine.includes("The soul's too heavy for where you're going.")) {
                    // This line is now shown automatically, but keep the logic for safety
                    console.log("Let it go line reached - effects should already be active");

                    // Effects should already be running, just continue to final lines
                    setTimeout(() => {
                        this._showFinalLineAutomatically();
                    }, 3000);

                    // Don't call nextTransitionLine - sequence is automatic now
                } else {
                    // Normal line - proceed to next
                    this.nextTransitionLine();
                }
            }, 50, 'transition');
        } else {
            console.log("Transition phase complete, moving to dungeon...");
            this.transitionToDungeon();
        }
    }

    async transitionToDungeon() {
        console.log("Transitioning to dungeon...");

        // Stop all intro music before transitioning
        console.log("Stopping intro music...");
        this.audioManager?.stopSound('bg_ambient_music');
        this.audioManager?.stopSound('bg_ambient_surround');
        this.audioManager?.fadeVolume('bg_ambient_music', 0, 500);
        this.audioManager?.fadeVolume('bg_ambient_surround', 0, 500);

        // Store the dungeon entry dialogue for the DungeonHandler to use
        window.dungeonEntryDialogue = dungeonEntryDialogue;
        window.dungeonDialogueEffects = dungeonDialogueEffects;

        // Generate dungeon data like the old questionnaire system did
        console.log("Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        let dungeonGenerator = null;
        try {
            dungeonGenerator = new DungeonGenerator();

            // CRITICAL FIX: Import EventRoomManager and BossRoomManager synchronously to ensure they're always connected
            const { EventRoomManager } = await import('../systems/EventRoomManager.js');
            const { BossRoomManager } = await import('../systems/BossRoomManager.js');
            
            const tempEventManager = new EventRoomManager(null, this.audioManager);
            const tempBossManager = new BossRoomManager(null, this.audioManager);
            
            dungeonGenerator.eventRoomManager = tempEventManager;
            dungeonGenerator.bossRoomManager = tempBossManager;
            dungeonGenerator.currentFloorNumber = 1;
            
            console.log('[CharacterCreation] ✅ Connected EventRoomManager to dungeon generator');
            console.log('[CharacterCreation] ✅ Connected BossRoomManager to dungeon generator');

            // Generate layout after EventRoomManager is connected
            floorLayout = await dungeonGenerator.generateLayout();
            playerMesh = createElementalPlayerModel();
            console.log("Dungeon data pre-generation complete.");

            // Create state parameters and continue
            const stateParams = {
                userAnswers: ['Default Answer'],
                floorLayout: floorLayout,
                playerMesh: playerMesh,
                dungeonGenerator: dungeonGenerator
            };

            // Keep the existing transition video functionality with proper parameters
            this.playRebornVideo(stateParams);
            return;
        } catch (error) {
            console.error("Error during pre-generation:", error);
            // Handle error - maybe prevent transition or show error message
            return;
        }
    }

    async _transitionToDungeonDirectly() {
        console.log("Transitioning directly to dungeon (skipping reborn video)...");

        // DON'T stop intro music - let it continue during video
        // DON'T stop stuckinmydreams - let it continue playing
        console.log("Keeping music playing during transition...");

        // Store the dungeon entry dialogue for the DungeonHandler to use
        window.dungeonEntryDialogue = dungeonEntryDialogue;
        window.dungeonDialogueEffects = dungeonDialogueEffects;

        // CRITICAL: Detach video from DOM to prevent cleanup from affecting it
        console.log("🎬 Detaching video from DOM to prevent cleanup interference");

        if (this.endIntroVideo && this.endIntroVideo.parentNode) {
            this.endIntroVideo.parentNode.removeChild(this.endIntroVideo);
            console.log("🎬 Video detached from DOM");
        }

        if (this.videoOverlay && this.videoOverlay.parentNode) {
            this.videoOverlay.parentNode.removeChild(this.videoOverlay);
            console.log("🎬 Video overlay detached from DOM");
        }

        // Store video state to preserve it across scene transition
        window.preserveEndIntroVideo = {
            element: this.endIntroVideo,
            timer: this.endIntroVideoTimer,
            dungeonTimer: this.dungeonTransitionTimer,
            isPlaying: !this.endIntroVideo.paused,
            currentTime: this.endIntroVideo.currentTime,
            src: this.endIntroVideo.src,
            videoOverlay: this.videoOverlay
        };

        // Prevent the video from being cleaned up during scene transition
        console.log("🎬 Preserving video across scene transition - current time:", this.endIntroVideo.currentTime);

        // Generate dungeon data like the old questionnaire system did
        console.log("Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        let dungeonGenerator = null;
        try {
            dungeonGenerator = new DungeonGenerator();

            // CRITICAL FIX: Import EventRoomManager and BossRoomManager synchronously to ensure they're always connected
            const { EventRoomManager } = await import('../systems/EventRoomManager.js');
            const { BossRoomManager } = await import('../systems/BossRoomManager.js');
            
            const tempEventManager = new EventRoomManager(null, this.audioManager);
            const tempBossManager = new BossRoomManager(null, this.audioManager);
            
            dungeonGenerator.eventRoomManager = tempEventManager;
            dungeonGenerator.bossRoomManager = tempBossManager;
            dungeonGenerator.currentFloorNumber = 1;
            
            console.log('[CharacterCreation] ✅ Connected EventRoomManager to dungeon generator');
            console.log('[CharacterCreation] ✅ Connected BossRoomManager to dungeon generator');

            // Generate layout after EventRoomManager is connected
            floorLayout = await dungeonGenerator.generateLayout();
            playerMesh = createElementalPlayerModel();
            console.log("Dungeon data pre-generation complete.");

            // Create state parameters and continue
            const stateParams = {
                userAnswers: ['Default Answer'],
                floorLayout: floorLayout,
                playerMesh: playerMesh,
                dungeonGenerator: dungeonGenerator
            };

            // Directly change to dungeon state without playing reborn video
            this.sceneManager.changeState(STATE.DUNGEON, stateParams);
            return;
        } catch (error) {
            console.error("Error during pre-generation:", error);
            // Handle error - maybe prevent transition or show error message
            return;
        }
    }

    // Determine appropriate typing speed based on dialogue content
    _getTypingSpeedForText(text, defaultSpeed = 15) {
        // Convert text to lowercase for pattern matching
        const lowerText = text.toLowerCase().trim();

        // ONLY ellipses get special treatment - moderate dramatic effect
        if (lowerText === '...' || lowerText === '….') {
            return 200; // Reduced from 400ms - still dramatic but not excessive
        }

        // ONLY the opening atmospheric line gets special treatment
        if (lowerText.includes('in the space between sleeping and waking')) {
            return 35; // Much faster - was 120ms, now 35ms for better pacing
        }

        // Keep original speed for all other dialogue
        return defaultSpeed;
    }

    // Enhanced typing system with pauses and variable word speeds
    _parseTextForDynamicTyping(text, baseSpeed) {
        const segments = [];

        // Debug: Log the text being processed
        if (text.toLowerCase().includes('business')) {
            console.log(`🎭 Processing text with "business": "${text}"`);
        }

        // Check if this is the "business of..." line and handle it specially
        const isBusinessLine = /business.*of\.\.\./i.test(text);
        if (isBusinessLine) {
            console.log(`🎭 SPECIAL HANDLING: Business line detected`);
            // Split the text at "of..." to create the pause
            const parts = text.split(/(\.\.\.)/, 2);
            if (parts.length >= 2) {
                // Part 1: "I'm in the business of"
                segments.push({ text: parts[0], speed: baseSpeed, pauseAfter: 0 });
                // Part 2: "..." with the shorter pause
                segments.push({ text: parts[1], speed: baseSpeed, pauseAfter: 500 });
                // Part 3: " second chances." (everything after the ellipses)
                const remaining = text.substring(parts[0].length + parts[1].length);
                if (remaining) {
                    segments.push({ text: remaining, speed: baseSpeed, pauseAfter: 0 });
                }
                console.log(`🎭 Business line split into ${segments.length} segments with 2000ms pause`);
                return segments;
            }
        }

        // Check if this is the "forgot... Names are just words" line and handle it specially
        const isForgotLine = /forgot\.\.\.\s*Names are just words/i.test(text);
        if (isForgotLine) {
            console.log(`🎭 SPECIAL HANDLING: Forgot line detected`);
            // Split the text at "..." to create the pause
            const parts = text.split(/(\.\.\.)/, 2);
            if (parts.length >= 2) {
                // Part 1: "It looks like you forgot"
                segments.push({ text: parts[0], speed: baseSpeed, pauseAfter: 0 });
                // Part 2: "..." with pause and sound effect
                segments.push({ text: parts[1], speed: baseSpeed, pauseAfter: 800, soundEffect: 'flicker_laugh2' });
                // Part 3: " Names are just words" (everything after the ellipses)
                const remaining = text.substring(parts[0].length + parts[1].length);
                if (remaining) {
                    segments.push({ text: remaining, speed: baseSpeed, pauseAfter: 0 });
                }
                console.log(`🎭 Forgot line split into ${segments.length} segments with 800ms pause and laugh sound`);
                return segments;
            }
        }

        // Define pause patterns - meaningful mid-sentence pauses (for other lines)
        const pausePatterns = [
            { pattern: /home…/i, pauseAfter: 500 }, // "voice calling you home…" → 0.5 second pause
            { pattern: /familiar\.\.\./i, pauseAfter: 500 }, // "familiar..." → 0.5 second pause
            { pattern: /path…/i, pauseAfter: 500 }, // "A path… maybe" → 0.5 second pause
            { pattern: /going\./i, pauseAfter: 500 }, // "where you're going." → 0.5 second pause
            { pattern: /down\./i, pauseAfter: 500 }, // "deeper down." → 0.5 second pause
        ];

        // Define words - ONLY the most essential dramatic moments
        const wordSpeeds = {
            // Only 2-3 truly special words - moderate dramatic emphasis
            'flicker': baseSpeed * 3, // Character name reveal - was 8x, now 3x (45ms instead of 120ms)
            'familiar': baseSpeed * 3, // Name recognition - was 8x, now 3x (45ms instead of 120ms)

            // Everything else uses normal speed for consistency
        };

        // Split text into words while preserving spaces and punctuation
        const words = text.split(/(\s+|[.!?,:;])/);

        for (let word of words) {
            if (word.trim() === '') {
                // Handle spaces and empty strings
                segments.push({ text: word, speed: baseSpeed, pauseAfter: 0 });
                continue;
            }

            const cleanWord = word.toLowerCase().replace(/[.!?,:;]/g, '');
            let wordSpeed = wordSpeeds[cleanWord] || baseSpeed;
            let pauseAfter = 0;

            // Log word speed changes for debugging
            if (wordSpeeds[cleanWord]) {
                console.log(`🎭 Word speed adjusted: "${cleanWord}" ${baseSpeed}ms → ${Math.round(wordSpeed)}ms`);
            }

            // Special debug for "flicker" word
            if (cleanWord === 'flicker') {
                console.log(`🎭 FLICKER DEBUG: cleanWord="${cleanWord}", wordSpeed=${Math.round(wordSpeed)}ms, baseSpeed=${baseSpeed}ms`);
            }

            // Check for pause patterns in the current word
            for (let pattern of pausePatterns) {
                if (pattern.pattern.test(word)) {
                    pauseAfter = pattern.pauseAfter;
                    console.log(`🎭 Pause detected after "${word}": ${pauseAfter}ms`);
                    break;
                }
            }

            // Special debug for business line
            if (word.toLowerCase().includes('of') || word.toLowerCase().includes('business')) {
                console.log(`🎭 Business debug - word: "${word}", pauseAfter: ${pauseAfter}ms`);
            }

            segments.push({ text: word, speed: Math.round(wordSpeed), pauseAfter: pauseAfter });
        }

        return segments;
    }

    typeDialogueLine(text, callback, speed = 15, typeInstanceIdentifier = 'default') {
        // Automatically determine base speed based on text content if default speed is used
        const baseSpeed = (speed === 15) ? this._getTypingSpeedForText(text, speed) : speed;

        // Parse text for dynamic typing with pauses and variable speeds
        const typingSegments = this._parseTextForDynamicTyping(text, baseSpeed);

        // Log speed changes for debugging
        if (baseSpeed !== speed) {
            console.log(`🎭 Typing speed adjusted for "${text.substring(0, 30)}...": ${speed}ms → ${baseSpeed}ms`);
        }

         clearTimeout(this.currentTypingJob.timeoutId);
         if (this.chatSoundInterval) clearInterval(this.chatSoundInterval);
         this.chatSoundInterval = null;
         this.audioManager?.stopSound('chat');
         if (!this.dialogueTextElement) return;
         this.dialogueTextElement.textContent = '';

         let segmentIndex = 0;
         let charIndex = 0;
         this.currentTypingJob = {
             text: text,
             segments: typingSegments,
             segmentIndex: 0,
             charIndex: 0,
             speed: baseSpeed,
             callback: callback,
             instance: typeInstanceIdentifier,
             isComplete: false,
             timeoutId: null
         };

         // Set typing state - dialogue cannot be skipped while typing
         this.isTyping = true;
         this.canSkipDialogue = false;
         this.isWaitingForAdvanceInput = false; // Don't wait for input until typing is complete

         if (segmentIndex === 0 && charIndex === 0) this._tryPlayIntroChatSound();
         this.chatSoundInterval = setInterval(() => {
            this.audioManager?.playSound('chat', false, 0.3);
         }, 110);

         const typeNextChar = () => {
             if (segmentIndex >= typingSegments.length) {
                 // All segments complete
                 clearInterval(this.chatSoundInterval);
                 this.chatSoundInterval = null;
                 this.currentTypingJob.isComplete = true;
                 this.currentTypingJob.instance = null;
                 this.currentTypingJob.timeoutId = null;

                 // Typing complete - now dialogue can be skipped
                 this.isTyping = false;
                 this.canSkipDialogue = true;
                 this.isWaitingForAdvanceInput = true; // Only set to true when typing is complete

                 // Play flicker_command sound when "It'll only sting a little" finishes typing
                 if (text && text.includes("It'll only sting a little")) {
                     console.log("🎭 Playing flicker_command sound after 'It'll only sting a little' completes...");
                     this.audioManager?.playSound('flicker_command', false, 0.7);
                 }

                 console.log(`🎯 TYPING COMPLETE for [${typeInstanceIdentifier}]: "${text.substring(0,20)}..."`);
                 console.log(`🎯 canSkipDialogue: ${this.canSkipDialogue}, isWaitingForAdvanceInput: ${this.isWaitingForAdvanceInput}`);
                 if (callback) {
                      console.log(`Callback ${callback.name || '(anonymous)'} is set for next advance.`);
                 }
                 return;
             }

             const currentSegment = typingSegments[segmentIndex];

             if (charIndex < currentSegment.text.length) {
                 // Type next character in current segment
                 this.dialogueTextElement.textContent += currentSegment.text[charIndex];
                 this.currentTypingJob.segmentIndex = segmentIndex;
                 this.currentTypingJob.charIndex = charIndex;
                 charIndex++;

                 // Use segment-specific speed
                 this.currentTypingJob.timeoutId = setTimeout(typeNextChar, currentSegment.speed);
             } else {
                 // Current segment complete, check for pause
                 if (currentSegment.pauseAfter > 0) {
                     console.log(`🎭 Pausing for ${currentSegment.pauseAfter}ms after "${currentSegment.text}"`);
                     // STOP typing sound during pause
                     clearInterval(this.chatSoundInterval);
                     this.chatSoundInterval = null;
                     this.audioManager?.stopSound('chat');

                     // Play sound effect if specified
                     if (currentSegment.soundEffect) {
                         console.log(`🎭 Playing sound effect: ${currentSegment.soundEffect}`);
                         this.audioManager?.playSound(currentSegment.soundEffect, false, 0.6);
                     }

                     // Pause before moving to next segment
                     this.currentTypingJob.timeoutId = setTimeout(() => {
                         // RESTART typing sound after pause
                         this.chatSoundInterval = setInterval(() => {
                             this.audioManager?.playSound('chat', false, 0.3);
                         }, 110);

                         segmentIndex++;
                         charIndex = 0;
                         typeNextChar();
                     }, currentSegment.pauseAfter);
                 } else {
                     // No pause, move to next segment immediately
                     segmentIndex++;
                     charIndex = 0;
                     this.currentTypingJob.timeoutId = setTimeout(typeNextChar, 10);
                 }
             }
         };

         this.currentTypingJob.timeoutId = setTimeout(typeNextChar, baseSpeed);
     }

    _tryPlayIntroChatSound() {
        if (!this.hasIntroChatSoundPlayed) {
            this.audioManager?.playSound('chat', false, 0.3);
            this.hasIntroChatSoundPlayed = true;
        }
    }

    _executeAdvanceCallback() {
         const callback = this.currentTypingJob.callback;
         this.currentTypingJob.callback = null;
         if (callback) {
             console.log(`Executing advance callback: ${callback.name || '(anonymous)'}`);
             callback();
         } else {
             console.warn("Attempted to execute advance callback, but none was set.");
         }
     }

    nextEnvironmentDialogueLine() {
        console.log("Advancing environment dialogue...");
        // Check if the current dialogue line triggers the camera walk
        if (originalIntroDialogue[this.currentOriginalDialogueIndex]?.includes("warm yourself")) {
            console.log("Triggering camera walk animation toward campfire...");

            // Check if this is a mobile device for closer end position
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                             ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);

            // Initiate the animation by setting its state, not calling a missing function
            this.cameraAnimation.active = true;
            this.cameraAnimation.startTime = this.sceneManager.clock.getElapsedTime();
            // Camera is already at the correct start position, so just set the animation targets
            this.cameraAnimation.startPosition.copy(this.sceneManager.camera.position); // Use current position as start

            if (isMobile) {
                // Mobile: End even closer to the fire for better visibility
                this.cameraAnimation.endPosition.set(0, 1.6, 2.2); // Closer end position for mobile
                console.log("Mobile device detected - using closer end position for campfire scene");
            } else {
                // Desktop: Original end position
                this.cameraAnimation.endPosition.set(0, 1.8, 3.0); // Original desktop end position
            }
            this.cameraAnimation.startLookAt.set(0, 0.5, 0); // Look at campfire from distance
            this.cameraAnimation.endLookAt.set(0, -0.5, 0); // Look down at campfire when close
            // No need to move camera - it's already in the right position for seamless animation

            // Potentially play sounds associated with walk start
            const numSteps = 3;
            const totalDuration = this.cameraAnimation.duration;
            const delayBetweenSteps = totalDuration / (numSteps + 1);
            for (let i = 1; i <= numSteps; i++) {
                 setTimeout(() => {
                     this.audioManager?.playSound('footstep');
                 }, delayBetweenSteps * i);
             }
             this.audioManager?.fadeVolume('bg_ambient_surround', 1.0, totalDuration);

             // Prevent advancing dialogue further until animation finishes
             // The animation's update loop will handle the next step (waiting for input)
             return;
        }

        // If not triggering walk, advance to the next line
        this.currentOriginalDialogueIndex++;
        if (this.currentOriginalDialogueIndex < originalIntroDialogue.length) {
            this.typeDialogueLine(originalIntroDialogue[this.currentOriginalDialogueIndex], this.nextEnvironmentDialogueLine.bind(this), 25, 'env');
        } else {
            console.warn("End of original intro reached unexpectedly? Starting fire reveal.");
            this.startFireRevealDialogue();
        }
    }

    startFireRevealDialogue() {
        console.log("Starting fire reveal dialogue...");
        this.currentOriginalDialogueIndex = -1;
        this.currentFireDialogueIndex = 0;
        this.typeDialogueLine(fireRevealDialogue[this.currentFireDialogueIndex], this.nextFireRevealLine.bind(this), 25, 'fire');
    }

    nextFireRevealLine() {
        this.currentFireDialogueIndex++;
        console.log(`[nextFireRevealLine] Advancing fire dialogue. Index is now: ${this.currentFireDialogueIndex}`);

        // --- Trigger Music & Video ---
        if (this.currentFireDialogueIndex === 1) { // <<< Changed condition from 2 to 1
            console.log("[nextFireRevealLine] Index is 1. Starting ambient music and triggering Flicker video..."); // <<< Updated Log
            this.audioManager?.playSound('bg_ambient_music', true, 0.4);
            this.isWaitingForAdvanceInput = false; // Not waiting for text advance
            this.showFlickerVideo();
            console.log("[nextFireRevealLine] showFlickerVideo() called. Returning now.");
            return; // Stop here, video logic will take over
        }
        // --- Original Video Trigger Removed ---
        /*
        if (this.currentFireDialogueIndex === 2) { ... }
        */
         else {
             console.log(`[nextFireRevealLine] Index is ${this.currentFireDialogueIndex} (NOT 1). Proceeding with normal text line.`); // <<< Updated Log
        }
        // --- End Video Trigger Check ---

        if (this.currentFireDialogueIndex < fireRevealDialogue.length) {
            const line = fireRevealDialogue[this.currentFireDialogueIndex];
            this.typeDialogueLine(line, this.nextFireRevealLine.bind(this));
        } else {
            console.log("Fire reveal dialogue finished. Starting questionnaire.");
            this.startQuestionnaire();
        }
    }

    startQuestionnaire() {
        console.log("Starting questionnaire...");
        this.currentQuestionIndex = 0;
        this.userAnswers = [];
        this.displayQuestion(this.currentQuestionIndex);
    }

    displayQuestion(index) {
        console.log(`Displaying question ${index}`);
        if (index >= questions_data.length) {
            this.showFinalPrompt();
            return;
        }
        const questionData = questions_data[index];
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = `Question ${index + 1}/${questions_data.length}`;
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        const displayOptionsCallback = () => {
             console.log("Displaying options for question", index);
             if (!this.dialogueOptionsContainer) return;
             this.dialogueOptionsContainer.innerHTML = '';
             this.highlightedOptionIndex = -1;
             questionData.options.forEach((optionText, i) => {
                 const button = document.createElement('button');
                 button.classList.add('dialogue-option-button');
                 button.textContent = `* ${optionText}`;
                 button.addEventListener('mouseenter', () => { this.highlightedOptionIndex = i; this._updateHighlight(); });
                 button.addEventListener('mouseleave', () => { this.highlightedOptionIndex = -1; this._updateHighlight(); });
                 button.onclick = () => this.selectOption(i);
                 this.dialogueOptionsContainer.appendChild(button);
             });
             setTimeout(() => {
                 if (this.dialogueOptionsContainer.children.length > 0) {
                     this.highlightedOptionIndex = 0;
                     this._updateHighlight();
                 }
             }, 0);
             this.isWaitingForAdvanceInput = false;
        };
        this.typeDialogueLine(questionData.text, displayOptionsCallback, 20, 'question');
    }

    _updateHighlight(container = this.dialogueOptionsContainer) {
        if (!container) return;
        const buttons = container.querySelectorAll('.dialogue-option-button');
        buttons.forEach((button, index) => {
            button.classList.toggle('highlighted', index === this.highlightedOptionIndex);
        });
    }

    selectOption(optionIndex) {
        console.log(`Selected option ${optionIndex} for question ${this.currentQuestionIndex}`);
        if (this.currentQuestionIndex >= questions_data.length) return;
        this.highlightedOptionIndex = -1;
        this._updateHighlight();
        this.audioManager?.playSound('select');
        const questionData = questions_data[this.currentQuestionIndex];
        const chosenOptionText = questionData.options[optionIndex];
        this.userAnswers.push({ question: questionData.text, answer: chosenOptionText });
        if (this.dialogueOptionsContainer) this.dialogueOptionsContainer.innerHTML = '';
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = '';
        this._startFireDanceAnimation();
        const feedbackText = feedbackResponses[this.currentQuestionIndex]?.[optionIndex] || "Flicker: Hmm...";
        const feedbackCallback = () => {
            this.proceedToNextQuestion();
        };
        this.typeDialogueLine(feedbackText, feedbackCallback, 25, 'feedback');
    }

    proceedToNextQuestion() {
        console.log("Proceeding to next question...");
        this.isWaitingForAdvanceInput = false;
        this.currentQuestionIndex++;
        this.displayQuestion(this.currentQuestionIndex);
    }

    showFinalPrompt() {
        console.log("Showing final prompt...");
        if (this.dialogueProgressElement) this.dialogueProgressElement.textContent = 'Complete!';
        const optionsContainer = document.getElementById('dialogue-options');
        if (optionsContainer) optionsContainer.innerHTML = '';
        this.highlightedOptionIndex = -1;

        const finalPromptCallback = () => {
             console.log("Displaying final button...");
             if (!optionsContainer) return;
             optionsContainer.innerHTML = ''; // Clear just in case
             this.highlightedOptionIndex = -1;
             const button = document.createElement('button');
             button.id = 'final-button-id'; // Assign an ID for hiding later
             button.classList.add('dialogue-option-button');
             button.textContent = '* Get Born';
             button.addEventListener('mouseenter', () => { this.highlightedOptionIndex = 0; this._updateHighlight(optionsContainer); });
             button.addEventListener('mouseleave', () => { this.highlightedOptionIndex = -1; this._updateHighlight(optionsContainer); });
             button.onclick = () => this.finishQuestionnaire(); // <<< Call finishQuestionnaire, NOT triggerDungeonTransition
             optionsContainer.appendChild(button);

             // Need to re-query options after adding the button
             setTimeout(() => {
                  const finalButton = optionsContainer.querySelector('#final-button-id');
                  if (finalButton) {
                      this.highlightedOptionIndex = 0;
                      this._updateHighlight(optionsContainer);
                  }
             }, 0);
              this.isWaitingForAdvanceInput = false;
        };
        this.typeDialogueLine(outroDialogue, finalPromptCallback, 25, 'outro');
    }

    triggerDungeonTransition(answersArray = null) {
        // If no specific answers array is provided (manual flow),
        // extract answers from the stored userAnswers objects.
        let finalAnswers;
        if (answersArray) {
            finalAnswers = answersArray; // Use the provided array (debug flow)
            console.log("Triggering Dungeon Transition with provided answers (debug flow):", finalAnswers);
        } else {
            // Ensure this.userAnswers is an array of objects with 'answer' property
            if (!Array.isArray(this.userAnswers) || this.userAnswers.some(item => typeof item !== 'object' || !item.hasOwnProperty('answer'))) {
                console.error("Manual answers format is incorrect. Expected array of {question, answer} objects.", this.userAnswers);
                this.transitioning = false; // Reset flag if format is wrong
                return; // Prevent transition
            }
            finalAnswers = this.userAnswers.map(item => item.answer);
            console.log("Triggering Dungeon Transition with extracted manual answers:", finalAnswers);
        }

        // Check if transition already started (prevent double trigger)
        if (this.transitioning) return;
        this.transitioning = true;

        this.highlightedOptionIndex = -1;
        this._updateHighlight();
        this.audioManager?.playSound('creepy_noise', false, 0.1);
        this.audioManager?.fadeVolume('bg_ambient_surround', 0, 500);
        this.audioManager?.fadeVolume('bg_ambient_music', 0, 500);

        // Simpler: Hide UI and start fade immediately
        // Use optional chaining for robustness
        this.dialogueManager?.hide();
        document.getElementById('dialogue-options')?.replaceChildren(); // Clear options
        document.getElementById('final-button-id')?.classList.add('hidden'); // Assuming an ID for the final button container
        document.getElementById('dialogue-container')?.classList.add('hidden');

        this.sceneManager.startFade(() => {
            // Double-check finalAnswers format before changing state
            if (!Array.isArray(finalAnswers) || finalAnswers.some(item => typeof item !== 'string')) {
                 console.error("Attempted to change state with invalid answers format:", finalAnswers);
                 this.transitioning = false; // Allow trying again if needed
                 // Potentially revert fade or show an error message
                 return;
            }
            this.sceneManager.changeState(STATE.DUNGEON, { userAnswers: finalAnswers });
        });
    }

    // New method to trigger debug transition to video position
    _triggerDebugVideoTransition() {
        console.log("DEBUG SHORTCUT: Moving to video transition position...");

        // Hide any current UI elements
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }

        // Clear any current dialogue or questionnaire state
        this.currentPhase = 'transition';
        this.currentTransitionIndex = transitionDialogue.length - 1; // Move to final line

        // Generate default answers for the dungeon system
        const defaultAnswers = [
            "Mystic Woods", "Fire Spirit", "Flame Burst", "Discovery", "Ancient Relic",
            "Head-on", "Courage", "Betrayal", "Instinct", "In Glory"
        ];

        // Set up the scene for the final transition
        this._setupFinalTransitionState();

        // Create UFO beam effect before video transition
        this._createUFOBeamEffect(() => {
            // Start the video transition sequence after beam animation
            console.log("DEBUG SHORTCUT: Starting video transition sequence...");
            this.transitionToDungeon();
        });
    }

    // Helper method to set up the scene state for final transition
    _setupFinalTransitionState() {
        console.log("Setting up final transition state...");

        // Remove cup from scene if it exists
        if (this.cupMesh) {
            this.sceneManager.scene.remove(this.cupMesh);
            this.cupMesh = null;
        }

        // Remove flaming hand if it exists
        if (this.flamingHand) {
            this.scene.remove(this.flamingHand);
            this.flamingHand = null;
            this.handLight = null;
            console.log("Flaming hand removed for debug transition");
        }

        // Ensure fire is blue for the transition
        this._changeFireToBlue();

        // Stop any current audio
        this.audioManager?.stopSound('bg_ambient_music');
        this.audioManager?.stopSound('bg_ambient_surround');
        this.audioManager?.fadeVolume('bg_ambient_music', 0, 500);
        this.audioManager?.fadeVolume('bg_ambient_surround', 0, 500);

        console.log("Final transition state setup complete");
    }

    // Create UFO beam effect that comes down from the sky
    _createUFOBeamEffect(onComplete) {
        console.log("🛸 Creating UFO beam effect...");

        // Create beam geometry - a cylinder that extends from high above to the ground
        const beamRadius = 1.5;
        const beamHeight = 20; // Very tall to come from "sky"
        const beamGeometry = new THREE.CylinderGeometry(beamRadius, beamRadius * 0.8, beamHeight, 16, 1, true);

        // Create animated beam material with blue glow
        const beamMaterial = new THREE.ShaderMaterial({
            uniforms: {
                time: { value: 0 },
                opacity: { value: 0 },
                color: { value: new THREE.Color(0x66DDFF) }
            },
            vertexShader: `
                varying vec2 vUv;
                varying vec3 vPosition;
                void main() {
                    vUv = uv;
                    vPosition = position;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform float time;
                uniform float opacity;
                uniform vec3 color;
                varying vec2 vUv;
                varying vec3 vPosition;

                void main() {
                    // Create vertical flowing effect
                    float flow = sin(vPosition.y * 0.5 + time * 8.0) * 0.5 + 0.5;

                    // Create radial fade from center
                    float radial = 1.0 - length(vUv - vec2(0.5)) * 2.0;
                    radial = max(0.0, radial);

                    // Combine effects
                    float alpha = flow * radial * opacity;

                    gl_FragColor = vec4(color, alpha);
                }
            `,
            transparent: true,
            side: THREE.DoubleSide,
            depthWrite: false,
            blending: THREE.AdditiveBlending
        });

        // Create the beam mesh
        const beamMesh = new THREE.Mesh(beamGeometry, beamMaterial);
        beamMesh.position.set(0, beamHeight / 2, 0); // Position so bottom touches ground
        beamMesh.name = "ufoBeam";

        // Add to scene
        this.scene.add(beamMesh);

        // Create particle effects inside the beam
        this._createBeamParticles(beamMesh);

        // Animate the beam
        this._animateUFOBeam(beamMesh, onComplete);
    }

    // Create floating particles inside the beam
    _createBeamParticles(beamMesh) {
        const particleCount = 50;
        const positions = new Float32Array(particleCount * 3);
        const velocities = [];

        for (let i = 0; i < particleCount; i++) {
            const i3 = i * 3;

            // Random position within beam cylinder
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * 1.2; // Slightly smaller than beam radius
            const height = Math.random() * 18 - 9; // Within beam height

            positions[i3] = Math.cos(angle) * radius;
            positions[i3 + 1] = height;
            positions[i3 + 2] = Math.sin(angle) * radius;

            // Upward velocity for floating effect
            velocities.push({
                x: (Math.random() - 0.5) * 0.02,
                y: Math.random() * 0.05 + 0.02,
                z: (Math.random() - 0.5) * 0.02
            });
        }

        const particleGeometry = new THREE.BufferGeometry();
        particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        const particleMaterial = new THREE.PointsMaterial({
            color: 0x66DDFF,
            size: 0.1,
            transparent: true,
            opacity: 0.8,
            blending: THREE.AdditiveBlending,
            depthWrite: false
        });

        const particles = new THREE.Points(particleGeometry, particleMaterial);
        particles.name = "beamParticles";
        particles.userData.velocities = velocities;

        beamMesh.add(particles);
    }

    // Animate the UFO beam effect
    _animateUFOBeam(beamMesh, onComplete) {
        const animationDuration = 3000; // 3 seconds
        const startTime = Date.now();

        console.log("🛸 Starting UFO beam animation...");

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / animationDuration, 1);

            // Update beam material uniforms
            if (beamMesh.material.uniforms) {
                beamMesh.material.uniforms.time.value = elapsed * 0.001;

                // Fade in, hold, then fade out
                if (progress < 0.3) {
                    // Fade in
                    beamMesh.material.uniforms.opacity.value = progress / 0.3;
                } else if (progress < 0.7) {
                    // Hold at full opacity
                    beamMesh.material.uniforms.opacity.value = 1.0;
                } else {
                    // Fade out
                    beamMesh.material.uniforms.opacity.value = 1.0 - ((progress - 0.7) / 0.3);
                }
            }

            // Animate particles
            const particles = beamMesh.getObjectByName("beamParticles");
            if (particles && particles.userData.velocities) {
                const positions = particles.geometry.attributes.position.array;
                const velocities = particles.userData.velocities;

                for (let i = 0; i < velocities.length; i++) {
                    const i3 = i * 3;

                    // Update positions
                    positions[i3] += velocities[i].x;
                    positions[i3 + 1] += velocities[i].y;
                    positions[i3 + 2] += velocities[i].z;

                    // Reset particles that go too high
                    if (positions[i3 + 1] > 9) {
                        positions[i3 + 1] = -9;
                        const angle = Math.random() * Math.PI * 2;
                        const radius = Math.random() * 1.2;
                        positions[i3] = Math.cos(angle) * radius;
                        positions[i3 + 2] = Math.sin(angle) * radius;
                    }
                }

                particles.geometry.attributes.position.needsUpdate = true;
            }

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Animation complete - clean up and call callback
                console.log("🛸 UFO beam animation complete, cleaning up...");
                this.scene.remove(beamMesh);

                if (onComplete) {
                    onComplete();
                }
            }
        };

        animate();
    }

    // Ensure _triggerDebugDungeonTransition still calls correctly
     _triggerDebugDungeonTransition() {
         // Ensure questions_data is assigned in the constructor
         if (!this.questions_data || this.questions_data.length === 0) {
             console.error("Cannot trigger debug transition: questions_data is missing or empty. Check constructor.");
             return;
         }

         console.log("Generating random answers for debug transition...");
         const randomAnswers = this.questions_data.map(q => {
             if (q.options && q.options.length > 0) {
                 const randomIndex = Math.floor(Math.random() * q.options.length);
                 return q.options[randomIndex];
             } else {
                 return "Debug Fallback Answer";
             }
         });

         console.log("Debug Random Answers:", randomAnswers);
         this.triggerDungeonTransition(randomAnswers); // Call WITH args
     }

     // Add helper methods for hiding UI elements if they don't exist
     hideOptions() {
         const optionsContainer = document.getElementById('dialogue-options');
         if (optionsContainer) optionsContainer.replaceChildren(); // Clear content
     }

     hideFinalButton() {
         const finalButton = document.getElementById('final-button-id');
         if (finalButton) finalButton.classList.add('hidden'); // Or remove it
     }

    // --- Add back missing helper methods for post-animation flow ---
    handlePostWalkAdvance() {
        console.log("Handling post-walk advance...");
        if (!this.isWaitingPostWalkInput) return;
        this.isWaitingPostWalkInput = false;

        // Type the ellipsis and set up the next state in the callback
        this.typeDialogueLine("...", () => {
            console.log("Ellipsis typed. Waiting for post-ellipsis advance.");
            this.isWaitingPostEllipsisInput = true; // Set flag for the *next* click
        }, 150, 'ellipsis');
    }

    handlePostEllipsisAdvance() {
        console.log("Handling post-ellipsis advance...");
        if (!this.isWaitingPostEllipsisInput) return;
        this.isWaitingPostEllipsisInput = false;

        // Clear the ellipsis text
        if(this.dialogueTextElement) this.dialogueTextElement.textContent = '';

        // Start the next animation sequence
        this._startLookAroundAnimation(); // Assuming this method exists or needs creating
    }

    // Placeholder or ensure this method exists for the look-around animation
     _startLookAroundAnimation() {
         console.log("Starting look around animation...");
         this.lookAroundAnimation.active = true;
         this.lookAroundAnimation.startTime = this.sceneManager.clock.getElapsedTime();
         this.lookAroundAnimation.phase = 0; // Reset animation phase
     }
     // --- End missing methods ---

    // Add back missing animation start method
    _startFireDanceAnimation() {
        if (this.fireGroup) {
            console.log("Starting fire dance animation.");
            this.fireDanceAnimation.active = true;
            this.fireDanceAnimation.startTime = this.sceneManager.clock.getElapsedTime();
        }
    }

    // Fire face animation removed - replaced with endintro video

    // New method to trigger drinking animation
    _startDrinkingAnimation() {
        console.log("Starting drinking animation - player tilts head to drink from cup");

        // Store original camera rotation
        this.drinkingAnimation.originalRotation.copy(this.sceneManager.camera.rotation);

        // Start animation
        this.drinkingAnimation.active = true;
        this.drinkingAnimation.startTime = this.sceneManager.clock.getElapsedTime();
        this.drinkingAnimation.phase = 'tilting';

        // Start flaming hand feeding animation
        if (this.flamingHand) {
            this.flamingHand.userData.isFeeding = true;
            this.flamingHand.userData.feedingStartTime = this.sceneManager.clock.getElapsedTime();
            this.flamingHand.userData.feedingDuration = this.drinkingAnimation.duration / 1000; // Match drinking duration
            this.flamingHand.userData.originalHandPosition = this.flamingHand.position.clone();
            this.flamingHand.userData.originalHandRotation = this.flamingHand.rotation.clone();

            // Store original cup position and rotation relative to hand
            if (this.cupMesh) {
                this.flamingHand.userData.originalCupPosition = this.cupMesh.position.clone();
                this.flamingHand.userData.originalCupRotation = this.cupMesh.rotation.clone();
            }

            console.log("Flaming hand feeding animation started - hand will move cup to player's mouth");
        }
    }

    // New method to start ground camera animation (getting on the ground)
    _startGroundCameraAnimation() {
        console.log("🎥 Starting ground camera animation - player getting on the ground");

        const camera = this.sceneManager.camera;

        // Store current camera position and look-at as starting points
        this.groundCameraAnimation.startPosition.copy(camera.position);

        // Calculate current look-at direction
        const currentLookAt = new THREE.Vector3();
        camera.getWorldDirection(currentLookAt);
        currentLookAt.multiplyScalar(5).add(camera.position); // Extend direction to get look-at point
        this.groundCameraAnimation.startLookAt.copy(currentLookAt);

        // Calculate end position (lower and further back)
        const currentPos = camera.position;
        this.groundCameraAnimation.endPosition.set(
            currentPos.x,
            currentPos.y - 1.5, // Move down 1.5 units (moved up a bit from 1.8)
            currentPos.z + 1.5   // Move back 1.5 units (backing away slightly)
        );

        // Calculate end look-at (looking up at the fire face, but less extreme angle)
        this.groundCameraAnimation.endLookAt.set(0, 1.5, 0); // Look up less (reduced from 2.0 to 1.5)

        // Start the animation
        this.groundCameraAnimation.active = true;
        this.groundCameraAnimation.startTime = this.sceneManager.clock.getElapsedTime();

        console.log("🎥 Ground camera animation started:", {
            from: this.groundCameraAnimation.startPosition,
            to: this.groundCameraAnimation.endPosition,
            lookFrom: this.groundCameraAnimation.startLookAt,
            lookTo: this.groundCameraAnimation.endLookAt
        });
    }

    // Face generation method removed - replaced with endintro video

    // --- Method to SHOW Video ---
    showFlickerVideo() {
        console.log("[showFlickerVideo] Function called.");
        // Ensure correct video element is visible and others are hidden
        if (this.introVideo) this.introVideo.style.display = 'block';
        if (this.rebornVideo) this.rebornVideo.style.display = 'none';

        if (!this.videoOverlay || !this.introVideo) {
            console.error("[showFlickerVideo] Video overlay or introVideo elements NOT found! Skipping video.");
            // If video can't play, immediately proceed to the next line
            this.nextFireRevealLine();
            return;
        }
        console.log("[showFlickerVideo] Video elements found.", { overlay: this.videoOverlay, video: this.introVideo });

        console.log("[showFlickerVideo] Showing video overlay...");
        // Ensure dialogue is hidden behind video
        if (this.dialogueContainer) {
            console.log("[showFlickerVideo] Setting dialogue zIndex to 999.");
            this.dialogueContainer.style.zIndex = '999';
        } else {
            console.warn("[showFlickerVideo] Dialogue container not found for zIndex adjustment.");
        }

        this.videoOverlay.classList.add('visible');
        this.videoOverlay.offsetHeight; // Trigger reflow
        this.videoOverlay.classList.add('fading-in');
        console.log("[showFlickerVideo] Overlay classes added: visible, fading-in.");

        // Remove the 'ended' listener logic
        /*
        const onVideoEnd = () => { ... };
        this.introVideo.addEventListener('ended', onVideoEnd, { once: true });
        */

        console.log("[showFlickerVideo] Attempting to play intro video (looping)...");
        this.introVideo.currentTime = 0;
        const playPromise = this.introVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(_ => {
                 // Video started playing, now wait for input to hide it
                 console.log("[showFlickerVideo] play().then: Video playback started successfully. Setting waitingForInputAfterVideo = true and recording start time.");
                 this.waitingForInputAfterVideo = true;
                 this.flickerVideoStartTime = performance.now(); // <<< Record start time
                 // <<< Play Flicker Laugh Sound >>>
                 console.log("[showFlickerVideo] Playing flicker laugh sound...");
                 this.audioManager?.playSound('flicker_laugh'); // <<< Changed key back to underscore
             }).catch(error => {
                console.error("[showFlickerVideo] play().catch: Video auto-play failed:", error);
                // Fallback: Hide overlay, immediately proceed to next line
                this.videoOverlay.classList.remove('visible', 'fading-in');
                if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';
                console.log("[showFlickerVideo] play().catch: Calling nextFireRevealLine() as fallback.");
                this.nextFireRevealLine(); // Proceed dialogue
            });
        } else {
            // If play() doesn't return a promise (older browsers?), assume it failed
             console.error("[showFlickerVideo] Video play() did not return a promise. Skipping video.");
             this.videoOverlay.classList.remove('visible', 'fading-in');
             if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';
             console.log("[showFlickerVideo] No promise: Calling nextFireRevealLine() as fallback.");
             this.nextFireRevealLine(); // Proceed dialogue
        }
    }

    // --- New Method to HIDE Video ---
    hideFlickerVideo() {
        console.log("[hideFlickerVideo] Hiding Flicker video...");
        this.flickerVideoStartTime = null; // <<< Reset start time
        if (!this.videoOverlay || !this.introVideo) {
            console.error("Video overlay or introVideo elements not found during hide! Proceeding dialogue anyway.");
            // Continue with reveal dialogue as fallback
            if (this.currentRevealIndex < flickerRevealDialogue.length) {
                this.typeDialogueLine(flickerRevealDialogue[this.currentRevealIndex], this.nextRevealLine.bind(this), 50, 'reveal');
            } else {
                this.startNameInputPhase();
            }
            return;
        }

        console.log("Fading out video overlay...");
        this.videoOverlay.classList.remove('fading-in');

        // Wait for fade-out transition before hiding, pausing, and typing next line
        setTimeout(() => {
            console.log("Video overlay faded out. Hiding flicker video, pausing, and advancing dialogue.");
            this.videoOverlay.classList.remove('visible');
            if (this.introVideo) {
                 this.introVideo.pause(); // Pause the looping video
                 this.introVideo.style.display = 'none'; // Hide it
            }
            // Restore dialogue z-index
            if (this.dialogueContainer) this.dialogueContainer.style.zIndex = '1000';

            // --- Continue with reveal dialogue after video ---
            console.log("[hideFlickerVideo] Continuing with reveal dialogue after video...");
            if (this.currentRevealIndex < flickerRevealDialogue.length) {
                this.typeDialogueLine(flickerRevealDialogue[this.currentRevealIndex], this.nextRevealLine.bind(this), 50, 'reveal');
            } else {
                console.log("Reveal phase complete after video, starting name input phase...");
                this.startNameInputPhase();
            }
        }, 500); // Match the CSS opacity transition duration
    }

    // --- New Method to Play Reborn Video ---
    playRebornVideo(stateParams) {
        console.log("--- [playRebornVideo] START --- ");

        // Log element finding
        console.log("[playRebornVideo] Finding elements:", {
            overlay: this.videoOverlay,
            intro: this.introVideo,
            reborn: this.rebornVideo
        });

        // Ensure correct video element is visible and others are hidden
        if (this.introVideo) {
            console.log("[playRebornVideo] Setting introVideo display to 'none'.");
            this.introVideo.style.display = 'none';
        } else {
            console.warn("[playRebornVideo] introVideo element NOT found for hiding.");
        }

        if (this.rebornVideo) {
             console.log("[playRebornVideo] Setting rebornVideo display to 'block'.");
            this.rebornVideo.style.display = 'block';
        } else {
            console.error("[playRebornVideo] rebornVideo element not found! Cannot play.");
            // Fallback: Directly change state
            this.sceneManager.changeState(STATE.DUNGEON, stateParams);
            return;
        }

        if (!this.videoOverlay) {
            console.error("[playRebornVideo] videoOverlay element not found! Cannot play.");
             // Fallback: Directly change state
             this.sceneManager.changeState(STATE.DUNGEON, stateParams);
             return;
        }

        console.log("[playRebornVideo] Showing video overlay (adding classes visible, fading-in)...");
        this.videoOverlay.classList.add('visible');
        this.videoOverlay.offsetHeight; // Trigger reflow
        this.videoOverlay.classList.add('fading-in');

        // Add event listener for when this specific video ends
        const onRebornVideoEnd = () => {
            console.log("--- [onRebornVideoEnd] START --- ");
            console.log("[playRebornVideo] Reborn video ended.");
            if (this.rebornVideo) {
                this.rebornVideo.removeEventListener('ended', onRebornVideoEnd);
                console.log("[onRebornVideoEnd] Removed 'ended' listener.");
            } else {
                 console.warn("[onRebornVideoEnd setTimeout] rebornVideo not found for listener removal?");
            }

            // Fade out video overlay
            console.log("[onRebornVideoEnd setTimeout] Fading out video overlay (removing fading-in class)...");
            if (this.videoOverlay) {
                 this.videoOverlay.classList.remove('fading-in');
            } else {
                 console.warn("[onRebornVideoEnd setTimeout] videoOverlay not found for fade out.");
            }

            // Wait for fade-out transition before changing state
            setTimeout(() => {
                console.log("--- [onRebornVideoEnd setTimeout] START --- ");
                console.log("[playRebornVideo] Video overlay faded out. Changing state to Dungeon.");
                 if (this.videoOverlay) {
                     console.log("[onRebornVideoEnd setTimeout] Hiding overlay (removing visible class).");
                     this.videoOverlay.classList.remove('visible');
                 } else {
                     console.warn("[onRebornVideoEnd setTimeout] videoOverlay not found for hiding.");
                 }
                 if (this.rebornVideo) {
                     console.log("[onRebornVideoEnd setTimeout] Hiding rebornVideo (display: none).");
                     this.rebornVideo.style.display = 'none'; // Hide video
                 } else {
                     console.warn("[onRebornVideoEnd setTimeout] rebornVideo not found for hiding.");
                 }

                // Now proceed to the Dungeon state with the pre-generated data
                 console.log("[onRebornVideoEnd setTimeout] Calling sceneManager.changeState(STATE.DUNGEON) with params...");
                this.sceneManager.changeState(STATE.DUNGEON, stateParams);
                 console.log("--- [onRebornVideoEnd setTimeout] END --- ");
            }, 500); // Match the CSS opacity transition duration
             console.log("--- [onRebornVideoEnd] END (setTimeout scheduled) --- ");
        };
        console.log("[playRebornVideo] Adding 'ended' event listener to rebornVideo.");
        this.rebornVideo.addEventListener('ended', onRebornVideoEnd, { once: true });

        // Attempt to play the video
        console.log("[playRebornVideo] Setting rebornVideo currentTime = 0.");
        this.rebornVideo.currentTime = 0; // Ensure video starts from the beginning
        console.log("[playRebornVideo] Calling rebornVideo.play()...");
        const playPromise = this.rebornVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(_ => {
                 console.log("[playRebornVideo] play().then: Playback started or will start soon.");
                 // Don't play creepy noise again - it was already played during fire face
                 console.log("[playRebornVideo] Video started - no additional sound needed");

                 // Set up early fade-out timer (4 seconds before video naturally ends)
                 if (this.rebornVideo && this.rebornVideo.duration) {
                     const videoDuration = this.rebornVideo.duration;
                     const earlyFadeTime = Math.max(0, videoDuration - 4); // 4 seconds before end
                     console.log(`[playRebornVideo] Video duration: ${videoDuration}s, will fade out at: ${earlyFadeTime}s`);

                     setTimeout(() => {
                         console.log("[playRebornVideo] Starting early cut to dungeon (4 seconds before natural end)");
                         if (this.rebornVideo && !this.rebornVideo.paused && !this.rebornVideo.ended) {
                             // Remove the natural end listener
                             this.rebornVideo.removeEventListener('ended', onRebornVideoEnd);

                             // Immediately hide video and go to dungeon (no fade-out)
                             console.log("[playRebornVideo] Cutting directly to dungeon - no fade");
                             if (this.videoOverlay) {
                                 this.videoOverlay.classList.remove('visible', 'fading-in');
                             }
                             if (this.rebornVideo) {
                                 this.rebornVideo.style.display = 'none';
                             }

                             // Go directly to dungeon state
                             this.sceneManager.changeState(STATE.DUNGEON, stateParams);
                         }
                     }, earlyFadeTime * 1000);
                 }
             }).catch(error => {
                console.error("[playRebornVideo] play().catch: Video auto-play failed:", error);
                // Fallback: Hide overlay, directly change state
                if (this.rebornVideo) this.rebornVideo.removeEventListener('ended', onRebornVideoEnd);
                if (this.videoOverlay) this.videoOverlay.classList.remove('visible', 'fading-in');
                if (this.rebornVideo) this.rebornVideo.style.display = 'none';
                 console.log("[playRebornVideo] play().catch: Calling sceneManager.changeState as fallback with params.");
                this.sceneManager.changeState(STATE.DUNGEON, stateParams);
            });
        } else {
            console.error("[playRebornVideo] video.play() did not return a promise. Skipping video.");
            // Fallback: Directly change state if play API is unusable with params
            console.log("[playRebornVideo] No promise: Calling sceneManager.changeState as fallback with params.");
            this.sceneManager.changeState(STATE.DUNGEON, stateParams);
        }
         console.log("--- [playRebornVideo] END --- (play initiated, waiting for ended event) --- ");
    }

    async finishQuestionnaire(debugAnswersArray = null) { // <<< Accept optional debug answers
        console.log("--- [finishQuestionnaire] START ---");

        // Determine which answers to use
        let answersToUse;
        if (debugAnswersArray) {
            answersToUse = debugAnswersArray;
            console.log("Using DEBUG answers:", answersToUse);
        } else {
            answersToUse = this.userAnswers.map(item => item.answer);
            console.log("Using collected user answers:", answersToUse);
        }

        // Ensure answers are in the correct format (array of strings)
        if (!Array.isArray(answersToUse) || answersToUse.some(item => typeof item !== 'string')) {
             console.error("Cannot proceed: Invalid answer format provided to finishQuestionnaire.", answersToUse);
             return; // Stop if format is wrong
        }

        // console.log("All questions answered. User answers:", this.userAnswers);
        if (this.dialogueContainer) {
            console.log("[finishQuestionnaire] Hiding dialogue container.");
            this.dialogueContainer.classList.add('hidden');
        }

        // --- Pre-generate Dungeon Assets ---
        console.log("[finishQuestionnaire] Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        try {
            const generator = new DungeonGenerator();

            // CRITICAL FIX: Import EventRoomManager synchronously to ensure it's always connected
            const { EventRoomManager } = await import('../systems/EventRoomManager.js');
            const tempEventManager = new EventRoomManager(null, this.audioManager);
            generator.eventRoomManager = tempEventManager;
            generator.currentFloorNumber = 1;
            console.log('[finishQuestionnaire] ✅ Connected EventRoomManager to dungeon generator');

            // Generate layout after EventRoomManager is connected
            floorLayout = await generator.generateLayout();
            playerMesh = createElementalPlayerModel();
            console.log("[finishQuestionnaire] Dungeon data pre-generation complete.");

            // Store final answers for passing to next state
            const finalStateParams = {
                userAnswers: answersToUse,
                floorLayout: floorLayout,
                playerMesh: playerMesh
            };

            // Play reborn video with the prepared params
            this.playRebornVideo(finalStateParams);
            console.log("--- [finishQuestionnaire] END --- (playRebornVideo is asynchronous)");
            return;
        } catch (error) {
            console.error("[finishQuestionnaire] Error during pre-generation:", error);
            // Handle error - maybe prevent transition or show error message?
            // For now, we'll log it and proceed, DungeonHandler might fail later.
        }
    }

    // --- NEW: Mobile Dialogue Control Methods ---
    navigateDialogue(direction) {
        // Block input if rotate message is showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            return;
        }

        // Handle microphone permission navigation
        if (this.currentPhase === 'microphonePermission' && this.isWaitingForAdvanceInput) {
            const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
            if (options && options.length > 0) {
                if (direction === 'up' || direction === 'left') {
                    this.selectedMicrophoneChoice = (this.selectedMicrophoneChoice - 1 + options.length) % options.length;
                } else if (direction === 'down' || direction === 'right') {
                    this.selectedMicrophoneChoice = (this.selectedMicrophoneChoice + 1) % options.length;
                }
                this.highlightMicrophoneOption(this.selectedMicrophoneChoice);
                console.log(`[Joystick] Microphone option navigation: ${direction}, selected: ${this.selectedMicrophoneChoice}`);
            }
            return;
        }

        // Normal dialogue navigation for other phases
        const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
        if (options && options.length > 0) {
            if (direction === 'up') {
                this.highlightedOptionIndex = (this.highlightedOptionIndex - 1 + options.length) % options.length;
            } else if (direction === 'down') {
                this.highlightedOptionIndex = (this.highlightedOptionIndex + 1) % options.length;
            }
            this._updateHighlight();
        } else {
            // console.log("Navigate called but no options visible.");
        }
    }

    confirmDialogue() {
        // Block input if rotate message is showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            return;
        }

        // Handle microphone permission confirmation
        if (this.currentPhase === 'microphonePermission' && this.isWaitingForAdvanceInput) {
            const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
            if (options && options.length > 0 && this.selectedMicrophoneChoice >= 0) {
                console.log(`[Joystick] Confirming microphone option: ${this.selectedMicrophoneChoice}`);
                this.selectMicrophoneOption(this.selectedMicrophoneChoice);
                return;
            }
        }

        // Normal dialogue confirmation for other phases
        const options = this.dialogueOptionsContainer?.querySelectorAll('.dialogue-option-button');
        if (options && options.length > 0 && this.highlightedOptionIndex >= 0) {
            // Simulate clicking the highlighted button
            options[this.highlightedOptionIndex].click();
        } else {
            // If no options, simulate the general advance click
            this.onClickAdvance();
        }
    }
    // --- END Mobile Dialogue Control Methods ---

    // Add helper methods for hiding UI elements if they don't exist
    hideOptions() {
        const optionsContainer = document.getElementById('dialogue-options');
        if (optionsContainer) optionsContainer.replaceChildren(); // Clear content
    }

    // Ensure _triggerDebugDungeonTransition still calls correctly
    async _triggerDebugDungeonTransition() {
        console.log("DEBUG SHORTCUT: Fast dungeon initialization - skipping intro, videos, and dialogue...");

        // Stop any current intro music
        console.log("DEBUG SHORTCUT: Stopping intro music...");
        this.audioManager?.stopSound('bg_ambient_music');
        this.audioManager?.stopSound('bg_ambient_surround');
        this.audioManager?.fadeVolume('bg_ambient_music', 0, 100);
        this.audioManager?.fadeVolume('bg_ambient_surround', 0, 100);

        // Store the dungeon entry dialogue for the DungeonHandler to use (even though we'll skip it)
        window.dungeonEntryDialogue = dungeonEntryDialogue;
        window.dungeonDialogueEffects = dungeonDialogueEffects;

        // Generate default answers for the dungeon system
        const defaultAnswers = [
            "Mystic Woods", "Fire Spirit", "Flame Burst", "Discovery", "Ancient Relic",
            "Head-on", "Courage", "Betrayal", "Instinct", "In Glory"
        ];

        console.log("DEBUG SHORTCUT: Pre-generating dungeon data...");
        let floorLayout = null;
        let playerMesh = null;
        let dungeonGenerator = null;
        try {
            dungeonGenerator = new DungeonGenerator();

            // CRITICAL FIX: Import EventRoomManager and BossRoomManager synchronously to ensure they're always connected
            const { EventRoomManager } = await import('../systems/EventRoomManager.js');
            const { BossRoomManager } = await import('../systems/BossRoomManager.js');
            
            const tempEventManager = new EventRoomManager(null, this.audioManager);
            const tempBossManager = new BossRoomManager(null, this.audioManager);
            
            dungeonGenerator.eventRoomManager = tempEventManager;
            dungeonGenerator.bossRoomManager = tempBossManager;
            dungeonGenerator.currentFloorNumber = 1;
            console.log('[DEBUG SHORTCUT] ✅ Connected EventRoomManager to dungeon generator');
            console.log('[DEBUG SHORTCUT] ✅ Connected BossRoomManager to dungeon generator');

            // Generate layout after EventRoomManager is connected
            floorLayout = await dungeonGenerator.generateLayout();
            playerMesh = createElementalPlayerModel();
            console.log("DEBUG SHORTCUT: Pre-generation complete.");

            // Continue with the rest of the debug shortcut logic
            this._continueDebugShortcut(defaultAnswers, floorLayout, playerMesh, dungeonGenerator);
            return;
        } catch (error) {
            console.error("DEBUG SHORTCUT: Error during pre-generation:", error);
            // Stop transition if pre-generation fails crucial step
            return;
        }
    }

    // Helper method to continue debug shortcut after EventRoomManager is loaded
    _continueDebugShortcut(defaultAnswers, floorLayout, playerMesh, dungeonGenerator) {
        // CRITICAL FIX: Include all the same setup as normal transition
        console.log("DEBUG SHORTCUT: Performing full scene cleanup and setup...");

        // Hide dialogue container like normal flow
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }

        // Reset camera and renderer to clean state (same as normal flow)
        this._resetCameraAndRenderer();

        // Clean up character creation listeners before starting fade
        this._removeEventListeners();

        // Set debug flags to skip dialogue and start music immediately
        console.log("DEBUG SHORTCUT: Setting flags to skip dialogue and start music immediately...");
        window.debugSkipDungeonDialogue = true;
        window.debugStartMusicImmediately = true;

        // Package data with same structure as normal flow, plus dungeon generator
        const finalStateParams = {
            userAnswers: defaultAnswers,
            floorLayout: floorLayout,
            playerMesh: playerMesh,
            dungeonGenerator: dungeonGenerator,
            skipDialogue: true,  // Additional flag for the DungeonHandler
            startMusicImmediately: true  // Additional flag for music system
        };

        console.log("DEBUG SHORTCUT: Starting fade transition with full initialization and skip flags...");

        // Use the same fade transition as normal flow
        this.sceneManager.startFade(() => {
            console.log("DEBUG SHORTCUT: Fade complete, changing state to Dungeon with debug flags set.");
            // The SceneManager cleanup for CharacterCreationHandler will run AFTER this callback
            // but BEFORE the fade-in completes, which is the same as normal flow.
            this.sceneManager.changeState(STATE.DUNGEON, finalStateParams);
        });
    }

    // Nightmare effects removed - replaced with endintro video

    _startCampfireEarthquake() {
        if (this.nightmareEarthquake?.active) return;

        console.log("🌍 Starting campfire earthquake");
        this.nightmareEarthquake = { active: true, intervalId: null, originalPosition: null, originalRotation: null };

        if (!this.sceneManager.camera) return;

        // Store original camera state
        this.nightmareEarthquake.originalPosition = this.sceneManager.camera.position.clone();
        this.nightmareEarthquake.originalRotation = this.sceneManager.camera.rotation.clone();

        // Heavy earthquake shaking
        this.nightmareEarthquake.intervalId = setInterval(() => {
            if (!this.nightmareEarthquake?.active || !this.sceneManager.camera) return;

            // Extremely intense shake for nightmare activation
            const baseIntensity = 0.25; // Very strong
            const timeVariation = Math.sin(Date.now() * 0.015) * 0.08; // Faster sine wave
            const randomSpike = Math.random() > 0.7 ? Math.random() * 0.15 : 0; // More frequent spikes
            const totalIntensity = baseIntensity + timeVariation + randomSpike;

            // Dramatic movement in all directions
            const randomX = (Math.random() - 0.5) * totalIntensity;
            const randomY = (Math.random() - 0.5) * totalIntensity;
            const randomZ = (Math.random() - 0.5) * totalIntensity;

            // Heavy rotational shake
            const rotationShake = totalIntensity * 0.03;
            const randomRotX = (Math.random() - 0.5) * rotationShake;
            const randomRotY = (Math.random() - 0.5) * rotationShake;
            const randomRotZ = (Math.random() - 0.5) * rotationShake;

            // Apply shake
            this.sceneManager.camera.position.x = this.nightmareEarthquake.originalPosition.x + randomX;
            this.sceneManager.camera.position.y = this.nightmareEarthquake.originalPosition.y + randomY;
            this.sceneManager.camera.position.z = this.nightmareEarthquake.originalPosition.z + randomZ;

            this.sceneManager.camera.rotation.x = this.nightmareEarthquake.originalRotation.x + randomRotX;
            this.sceneManager.camera.rotation.y = this.nightmareEarthquake.originalRotation.y + randomRotY;
            this.sceneManager.camera.rotation.z = this.nightmareEarthquake.originalRotation.z + randomRotZ;

        }, 25); // Very fast shake
    }

    _darkenSceneForNightmare() {
        console.log("🌑 Darkening scene for nightmare activation");

        // Store original light intensities
        this.originalLightStates = [];

        // Dim all lights in the scene to create darkness
        this.scene.traverse((object) => {
            if (object.isLight) {
                this.originalLightStates.push({
                    light: object,
                    originalIntensity: object.intensity
                });

                // Make extremely dark (keep some light for the fire face to be visible)
                object.intensity = object.intensity * 0.1; // 10% of original
            }
        });

        // Darken environment objects
        this.environmentObjects.forEach(obj => {
            if (obj.material) {
                if (!obj.userData.originalEmissive) {
                    obj.userData.originalEmissive = obj.material.emissive ? obj.material.emissive.clone() : new THREE.Color(0x000000);
                }
                if (obj.material.emissive) {
                    obj.material.emissive.multiplyScalar(0.2); // Much darker
                }
            }
        });
    }



    _deactivateNightmareEffects() {
        console.log("💀 Deactivating nightmare effects");

        // Stop earthquake
        if (this.nightmareEarthquake?.active) {
            this.nightmareEarthquake.active = false;

            if (this.nightmareEarthquake.intervalId) {
                clearInterval(this.nightmareEarthquake.intervalId);
            }

            // Restore camera position and rotation
            if (this.sceneManager.camera && this.nightmareEarthquake.originalPosition) {
                this.sceneManager.camera.position.copy(this.nightmareEarthquake.originalPosition);
            }
            if (this.sceneManager.camera && this.nightmareEarthquake.originalRotation) {
                this.sceneManager.camera.rotation.copy(this.nightmareEarthquake.originalRotation);
            }
        }

        // Restore lighting
        if (this.originalLightStates) {
            this.originalLightStates.forEach(state => {
                if (state.light) {
                    state.light.intensity = state.originalIntensity;
                }
            });
            this.originalLightStates = null;
        }

        // Restore environment object materials
        this.environmentObjects.forEach(obj => {
            if (obj.material && obj.userData.originalEmissive) {
                if (obj.material.emissive) {
                    obj.material.emissive.copy(obj.userData.originalEmissive);
                }
            }
        });


    }

    // --- Fire Face Dissolve Darkness ---
    _applyDissolveDarkness(dissolveProgress) {
        // Make scene progressively darker as fire dissolves
        const darknessIntensity = dissolveProgress * 0.9; // Up to 90% darker

        // Darken all lights in the scene
        this.scene.traverse((object) => {
            if (object.isLight) {
                if (!object.userData.originalDissolveIntensity) {
                    object.userData.originalDissolveIntensity = object.intensity;
                }

                // Make lights progressively dimmer
                const targetIntensity = object.userData.originalDissolveIntensity * (1 - darknessIntensity);
                object.intensity = targetIntensity;
            }
        });

        // Darken environment objects
        this.environmentObjects.forEach(obj => {
            if (obj.material) {
                if (!obj.userData.originalDissolveEmissive) {
                    obj.userData.originalDissolveEmissive = obj.material.emissive ? obj.material.emissive.clone() : new THREE.Color(0x000000);
                }
                if (obj.material.emissive) {
                    const targetEmissive = obj.userData.originalDissolveEmissive.clone();
                    targetEmissive.multiplyScalar(1 - darknessIntensity);
                    obj.material.emissive.copy(targetEmissive);
                }
            }
        });
    }

    // --- Cheat System Methods ---
    _activateCheat(cheatCode) {
        console.log(`🎮 Activating cheat: ${cheatCode}`);

        // Store cheat globally for other systems to access
        if (!window.gameState) {
            window.gameState = {};
        }
        if (!window.gameState.cheats) {
            window.gameState.cheats = {};
        }

        switch (cheatCode) {
            case 'spawn_chests':
                console.log("🎮 Cheat activated: Spawn chests in room 0");
                window.gameState.cheats.spawnChests = true;
                // Also store for immediate dungeon access
                window.spawnChestsCheat = true;
                break;

            // Future cheats can be added here
            case 'god_mode':
                console.log("🎮 Cheat activated: God mode");
                window.gameState.cheats.godMode = true;
                break;

            case 'infinite_ammo':
                console.log("🎮 Cheat activated: Infinite ammo");
                window.gameState.cheats.infiniteAmmo = true;
                break;

            default:
                console.warn(`🎮 Unknown cheat code: ${cheatCode}`);
        }

        // Log all active cheats
        console.log("🎮 Active cheats:", window.gameState.cheats);
    }

    _changeFireToBlue() {
        console.log("🔥 Changing fire to blue due to cheat activation");

        // Blue fire color palette
        const blueFireColors = [
            new THREE.Color(0x0066ff), // Bright blue
            new THREE.Color(0x0080ff), // Light blue
            new THREE.Color(0x00aaff), // Cyan-blue
            new THREE.Color(0x00ccff), // Light cyan
            new THREE.Color(0x66ddff), // Very light blue
            new THREE.Color(0x99eeff), // Almost white-blue
            new THREE.Color(0xffffff)  // White (for the hottest parts)
        ];

        // Change campfire particles to blue
        if (this.fireGroup && this.fireGroup.children) {
            this.fireGroup.children.forEach(particle => {
                if (particle.material) {
                    // Pick a random blue color for each particle
                    const blueColor = blueFireColors[Math.floor(Math.random() * blueFireColors.length)];
                    particle.material.emissive.copy(blueColor);
                    particle.material.color.copy(blueColor);

                    // Store blue state for fire face animation
                    particle.userData.isBlueFlame = true;
                    particle.userData.blueEmissive = blueColor.clone();
                }
            });
        }

        // Change flaming hand to blue if it exists
        if (this.flamingHand && this.flamingHand.children) {
            this.flamingHand.children.forEach(particle => {
                if (particle.material && particle.isMesh) {
                    const blueColor = blueFireColors[Math.floor(Math.random() * blueFireColors.length)];
                    particle.material.emissive.copy(blueColor);
                    particle.material.color.copy(blueColor);
                    particle.userData.isBlueFlame = true;
                    particle.userData.blueEmissive = blueColor.clone();
                }
            });
        }

        // Store global blue fire state for future fire effects
        if (!window.gameState) {
            window.gameState = {};
        }
        window.gameState.blueFireMode = true;
        console.log("🔥 Fire changed to blue - all future fire effects will be blue");
    }

    // --- Preload End Intro Video ---
    _preloadEndIntroVideo() {
        if (!this.endIntroVideo) {
            console.warn("🎬 End intro video element not found for preloading");
            return;
        }

        console.log("🎬 Preloading endintro video...");

        // Set up event listeners for preloading
        const onCanPlayThrough = () => {
            console.log("🎬 End intro video fully preloaded and ready to play");
            this.endIntroVideo.removeEventListener('canplaythrough', onCanPlayThrough);
        };

        const onLoadedData = () => {
            console.log("🎬 End intro video data loaded");
            this.endIntroVideo.removeEventListener('loadeddata', onLoadedData);
        };

        const onError = (error) => {
            console.error("🎬 Error preloading end intro video:", error);
            this.endIntroVideo.removeEventListener('error', onError);
        };

        this.endIntroVideo.addEventListener('canplaythrough', onCanPlayThrough);
        this.endIntroVideo.addEventListener('loadeddata', onLoadedData);
        this.endIntroVideo.addEventListener('error', onError);

        // Force load the video
        this.endIntroVideo.load();
    }

    // --- Preload Logo Fade Video ---
    _preloadLogoFadeVideo() {
        console.log("🎨 Preloading logofade video for mobile performance...");

        // Create a hidden video element for preloading
        this.logoFadeVideoBuffer = document.createElement('video');
        this.logoFadeVideoBuffer.src = 'assets/textures/animations/logofade.mp4';
        this.logoFadeVideoBuffer.muted = true;
        this.logoFadeVideoBuffer.playsInline = true;
        this.logoFadeVideoBuffer.style.display = 'none';
        this.logoFadeVideoBuffer.preload = 'auto';

        // Set up event listeners for preloading
        const onCanPlayThrough = () => {
            console.log("🎨 Logo fade video fully preloaded and ready");
            this.logoFadeVideoBuffer.removeEventListener('canplaythrough', onCanPlayThrough);
        };

        const onLoadedData = () => {
            console.log("🎨 Logo fade video data loaded");
            this.logoFadeVideoBuffer.removeEventListener('loadeddata', onLoadedData);
        };

        const onError = (error) => {
            console.error("🎨 Error preloading logo fade video:", error);
            this.logoFadeVideoBuffer.removeEventListener('error', onError);
        };

        this.logoFadeVideoBuffer.addEventListener('canplaythrough', onCanPlayThrough);
        this.logoFadeVideoBuffer.addEventListener('loadeddata', onLoadedData);
        this.logoFadeVideoBuffer.addEventListener('error', onError);

        // Add to DOM for preloading (hidden)
        document.body.appendChild(this.logoFadeVideoBuffer);

        // Force load the video
        this.logoFadeVideoBuffer.load();
    }

    // --- Helper method to start video sequence ---
    _startEndIntroVideoSequence() {
        console.log("🎬 Starting end intro video sequence");

        // Stop corruption effects when video starts, but let earthquake continue until void phase ends
        this._stopCorruptionEffect();

        // Disable player input and start video sequence
        this.isWaitingForAdvanceInput = false;
        this.automaticSequenceActive = true;

        // Start new video sequence immediately
        this._playEndIntroVideo();
    }

    // --- New End Intro Video Sequence ---
    _playEndIntroVideo() {
        console.log("🎬 Starting endintro.mp4 video sequence...");

        // Hide dialogue immediately
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }

        if (!this.endIntroVideo) {
            console.error("🎬 End intro video element not found!");
            return;
        }

        // Hide other videos and show endintro video
        if (this.introVideo) this.introVideo.style.display = 'none';
        if (this.rebornVideo) this.rebornVideo.style.display = 'none';

        // Make the video fullscreen and show it
        this.endIntroVideo.classList.add('fullscreen');
        this.endIntroVideo.style.display = 'block';
        this.endIntroVideo.currentTime = 0;

        // Show the video overlay
        if (this.videoOverlay) {
            this.videoOverlay.classList.add('visible');
            this.videoOverlay.classList.add('fading-in');
        }

        // Play the favorite song MP3 alongside the video
        console.log("🎵 Playing favorite song MP3...");
        this.audioManager?.playSound('stuckinmydreams', false, 0.8);

        // Set up 12-second timer to start dungeon transition (while video continues)
        const dungeonTransitionTimer = setTimeout(() => {
            console.log("🎬 12 seconds elapsed - starting dungeon transition while video continues");
            this._startDungeonTransitionDuringVideo();
        }, 12000); // 12 seconds

        // Set up 13-second timer to start fade (video is 15 seconds, fade takes 2 seconds)
        const videoTimer = setTimeout(() => {
            console.log("🎬 13 seconds elapsed - starting video fade");
            this._endVideoOnly();
        }, 13000); // 13 seconds

        // Check if video is ready to play, if not wait for it
        if (this.endIntroVideo.readyState >= 3) { // HAVE_FUTURE_DATA or better
            console.log("🎬 Video is ready, playing immediately");
            this._playEndIntroVideoNow();
        } else {
            console.log("🎬 Video not ready, waiting for canplaythrough event");
            const onCanPlay = () => {
                console.log("🎬 Video ready event fired, playing now");
                this.endIntroVideo.removeEventListener('canplaythrough', onCanPlay);
                this._playEndIntroVideoNow();
            };
            this.endIntroVideo.addEventListener('canplaythrough', onCanPlay);
        }

        // Store timer references for cleanup
        this.endIntroVideoTimer = videoTimer;
        this.dungeonTransitionTimer = dungeonTransitionTimer;
    }

    _playEndIntroVideoNow() {
        console.log("🎬 Actually playing the end intro video now");

        // Log video duration for debugging
        console.log(`🎬 Video duration: ${this.endIntroVideo.duration} seconds`);

        // Handle video ending naturally (if shorter than our timer)
        const onVideoEnded = (event) => {
            console.log("🎬 Video ended naturally at", this.endIntroVideo.currentTime, "seconds");
            console.log("🎬 Video duration:", this.endIntroVideo.duration, "seconds");
            // If video ends naturally before our 23-second timer, just hide it but keep timers running
            if (this.endIntroVideo) {
                this.endIntroVideo.style.display = 'none';
                this.endIntroVideo.classList.remove('fullscreen');
            }
            if (this.videoOverlay) {
                this.videoOverlay.classList.remove('visible', 'fading-in');
            }
        };

        // Remove any existing ended listeners and add our custom one
        this.endIntroVideo.removeEventListener('ended', onVideoEnded);
        this.endIntroVideo.addEventListener('ended', onVideoEnded);

        // Play the video
        const playPromise = this.endIntroVideo.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log("🎬 End intro video started playing successfully");
            }).catch(error => {
                console.error("🎬 End intro video failed to play:", error);
                // If video fails, still proceed with timers
            });
        }
    }

    _startDungeonTransitionDuringVideo() {
        console.log("🎬 Starting dungeon transition while video continues playing...");

        // Generate dungeon data and transition to dungeon state (but keep video playing)
        this._transitionToDungeonDirectly();
    }

    _endVideoOnly() {
        console.log("🎬 Ending endintro video with luma fade (dungeon already loaded)...");

        // Create luma fade effect - fade black areas to transparent
        if (this.endIntroVideo) {
            console.log("🎬 Starting luma fade-out effect");

            // Add CSS transition for smooth luma fade
            this.endIntroVideo.style.transition = 'filter 2s ease-out, opacity 2s ease-out';

            // Apply luma fade: increase brightness to make black transparent, then fade opacity
            this.endIntroVideo.style.filter = 'brightness(2) contrast(0.5)';
            this.endIntroVideo.style.opacity = '0';

            // After fade completes, hide the video
            setTimeout(() => {
                if (this.endIntroVideo) {
                    this.endIntroVideo.pause();
                    this.endIntroVideo.style.display = 'none';
                    this.endIntroVideo.classList.remove('fullscreen');
                    this.endIntroVideo.style.transition = '';
                    this.endIntroVideo.style.filter = '';
                    this.endIntroVideo.style.opacity = '';
                }
            }, 2000); // Wait for 2-second fade to complete
        }

        // Fade out video overlay with same timing
        if (this.videoOverlay) {
            this.videoOverlay.style.transition = 'opacity 2s ease-out';
            this.videoOverlay.style.opacity = '0';

            setTimeout(() => {
                if (this.videoOverlay) {
                    this.videoOverlay.classList.remove('visible', 'fading-in');
                    this.videoOverlay.style.transition = '';
                    this.videoOverlay.style.opacity = '';
                }
            }, 2000);
        }

        // DON'T stop stuckinmydreams - let it play until it naturally ends
        console.log("🎵 Letting stuckinmydreams continue playing until it naturally ends");

        // Clear timers
        if (this.endIntroVideoTimer) {
            clearTimeout(this.endIntroVideoTimer);
            this.endIntroVideoTimer = null;
        }
        if (this.dungeonTransitionTimer) {
            clearTimeout(this.dungeonTransitionTimer);
            this.dungeonTransitionTimer = null;
        }
    }

    _endVideoAndTransition() {
        console.log("🎬 Ending endintro video and transitioning to dungeon...");

        // Stop and hide video
        if (this.endIntroVideo) {
            this.endIntroVideo.pause();
            this.endIntroVideo.style.display = 'none';
        }

        // Stop the music
        this.audioManager?.stopSound('stuckinmydreams');

        // Clear timer
        if (this.endIntroVideoTimer) {
            clearTimeout(this.endIntroVideoTimer);
            this.endIntroVideoTimer = null;
        }

        // Transition to dungeon immediately
        this.transitionToDungeon();
    }

    _fadeOutDialogue() {
        console.log("🌑 Fading out dialogue for automatic sequence");

        if (this.dialogueContainer) {
            this.dialogueContainer.style.transition = 'opacity 2s ease-out';
            this.dialogueContainer.style.opacity = '0';

            // Hide completely after fade
            setTimeout(() => {
                this.dialogueContainer.classList.add('hidden');
                // Reset styles for next scene
                this.dialogueContainer.style.transition = '';
                this.dialogueContainer.style.opacity = '';
            }, 2000);
        }
    }

    // Old video transition methods removed - replaced with endintro video

    _resetCameraAndRenderer() {
        console.log("🎥 Resetting camera and renderer to clean state");

        // Stop campfire earthquake effect
        this._stopCampfireEarthquake();

        // Reset renderer only (don't touch camera - let dungeon handle that)
        if (this.sceneManager.renderer) {
            this.sceneManager.renderer.setSize(window.innerWidth, window.innerHeight);
            this.sceneManager.renderer.domElement.style.transform = '';
            this.sceneManager.renderer.domElement.style.transition = '';
        }

        // Clear any automatic sequence flags
        this.automaticSequenceActive = false;
    }

    _stopCampfireEarthquake() {
        if (this.nightmareEarthquake?.active) {
            console.log("🌍 Stopping campfire earthquake before dungeon transition");
            this.nightmareEarthquake.active = false;

            if (this.nightmareEarthquake.intervalId) {
                clearInterval(this.nightmareEarthquake.intervalId);
                this.nightmareEarthquake.intervalId = null;
            }

            // Restore camera position and rotation if they were stored
            if (this.sceneManager.camera && this.nightmareEarthquake.originalPosition) {
                this.sceneManager.camera.position.copy(this.nightmareEarthquake.originalPosition);
            }
            if (this.sceneManager.camera && this.nightmareEarthquake.originalRotation) {
                this.sceneManager.camera.rotation.copy(this.nightmareEarthquake.originalRotation);
            }

            this.nightmareEarthquake = null;
        }
    }

    // --- Mobile Orientation Methods ---
    showMobileRotateMessage() {
        console.log("Showing mobile rotate message with blur overlay");

        // Show blur overlay and rotate message
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.add('active');
        }
        if (this.rotateMessageElement) {
            this.rotateMessageElement.classList.add('active');
        }

        // Hide dialogue container while rotate message is shown
        if (this.dialogueContainer) {
            this.dialogueContainer.classList.add('hidden');
        }
    }

    _handleOrientationChange() {
        // Add a small delay to ensure orientation has fully changed
        setTimeout(() => {
            this._doOrientationCheck();
        }, 100);
    }

    _doOrientationCheck() {
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);

        if (likelyMobile && this.rotateMessageElement) {
            const isPortrait = window.matchMedia("(orientation: portrait)").matches;
            console.log(`[CharacterCreationHandler] Orientation check: ${isPortrait ? 'Portrait' : 'Landscape'}`);

            if (isPortrait) {
                // Show rotate message and blur overlay
                this.showMobileRotateMessage();
                console.log("Orientation: Portrait (Mobile) - Showing rotate message with blur.");
            } else {
                // Hide rotate message and blur overlay, resume dialogue
                if (this.backgroundBlurOverlay) {
                    this.backgroundBlurOverlay.classList.remove('active');
                }
                if (this.rotateMessageElement) {
                    this.rotateMessageElement.classList.remove('active');
                }

                // Resume or start dialogue when rotating to landscape
                console.log("Orientation: Landscape (Mobile) - Starting/resuming dialogue.");

                // Show dialogue container
                if (this.dialogueContainer) {
                    this.dialogueContainer.classList.remove('hidden');
                }

                // Start dialogue if not already started (this is the key fix)
                if (!this.currentPhase || this.currentPhase === 'microphonePermission') {
                    console.log("Starting microphone permission phase after orientation change");
                    this.currentPhase = 'microphonePermission';
                    this.showMicrophonePermissionChoice();
                } else if (this.currentPhase === 'blackScreen') {
                    console.log("Starting black screen narration after orientation change");
                    this.currentBlackScreenIndex = 0;
                    this.typeDialogueLine(blackScreenNarration[this.currentBlackScreenIndex], this.nextBlackScreenLine.bind(this), 50, 'blackScreen');
                }

                console.log("Orientation: Landscape (Mobile) - Hiding rotate message and blur.");
            }
        } else if (this.rotateMessageElement) {
            // Ensure message and blur are hidden on desktop
            if (this.backgroundBlurOverlay) {
                this.backgroundBlurOverlay.classList.remove('active');
            }
            this.rotateMessageElement.classList.remove('active');
        }
    }
}

export default CharacterCreationHandler;