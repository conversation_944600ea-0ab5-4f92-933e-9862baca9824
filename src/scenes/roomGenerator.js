import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

// Imports from shared prefabs
import {
    VOXEL_SIZE,
    ENVIRONMENT_PIXEL_SCALE,
    mulberry32
} from '../generators/prefabs/shared.js';

// --- Import prefab functions ---
import { getPrefabFunction } from '../prefabs/prefabs.js';

// --- Import environment systems ---
import environmentObjectManager from '../systems/EnvironmentObjectManager.js';
import environmentTypeManager from '../systems/EnvironmentTypeManager.js';

// --- Import area data ---
import { getAreaData } from '../gameData/areas.js';

// --- NEW: Import pillar and rubble directly ---
import { createStonePillarObject } from '../generators/prefabs/stonePillarObject.js';
import { createStoneRubbleObject } from '../generators/prefabs/stoneRubbleObject.js';

// --- WALL OFFSET CONFIGURATION ---
// Configurable offsets to prevent clipping and z-fighting with walls
const WALL_OFFSETS = {
    // Anti-clipping epsilon to prevent exact overlap
    EPSILON: 0.01,
    
    // Object-specific offsets (negative = into wall, positive = away from wall)
    torch: -0.15,           // Torches slightly embedded
    aether_torch: -0.15,    // Aether torches slightly embedded
    vine: -0.10,           // Vines less embedded to prevent clipping
    default: -0.12         // Default moderate embedding
};
import { createRitualCircleObject } from '../generators/prefabs/ritualCircleObject.js'; // <-- Import Ritual Circle
import { createAetherTorchObject } from '../generators/prefabs/aetherTorchObject.js'; // <-- Import Aether Torch

// --- Import Effects ---
// import { createFloorFogParticles } from '../effects/FloorFog.js'; // Remove old import
import { createFloorMistPlanes } from '../effects/FloorMistPlane.js';

// --- Import Event Room Door Prefabs ---
import { createWaterArchwayDoor, createSandstoneArchwayDoor } from '../generators/prefabs/waterArchwayDoor.js';
import { createStoneArchwayDoor } from '../generators/prefabs/stoneArchwayDoor.js';
import { _getMaterialByHex_Cached } from '../generators/prefabs/shared.js';

// --- Constants for room generation ---
export const ROOM_WORLD_SIZE = 14; // Size of a standard room in world units
export const WALL_HEIGHT = 4.0;   // Standard wall height
export const WALL_DEPTH = 0.5;    // Standard wall thickness
export const DOOR_DEPTH = 1.5;    // Door thickness (much thicker than walls for prominent 3D appearance)
export const DOOR_WIDTH = 2.5;    // Standard door width
export const DARK_ROOM_PROBABILITY = 0.0; // Probability of a room being dark (0.0 = never)

/**
 * Apply material tint to all meshes in a group
 * @param {THREE.Object3D} object - Object to apply tint to
 * @param {number} tintColor - Hex color to tint with
 */
function applyMaterialTint(object, tintColor) {
    if (!object || !tintColor) return;

    const tintColorObj = new THREE.Color(tintColor);

    object.traverse(child => {
        if (child.isMesh && child.material) {
            // Clone material to avoid affecting other instances
            child.material = child.material.clone();

            // Apply tint by multiplying with the tint color
            if (child.material.color) {
                child.material.color.multiply(tintColorObj);
            }

            // Also apply to emissive for subtle glow effect
            if (child.material.emissive) {
                const emissiveTint = tintColorObj.clone();
                emissiveTint.multiplyScalar(0.1); // Much darker for emissive
                child.material.emissive.add(emissiveTint);
            }
        }
    });
}

// Helper function to get object radius for collision detection
function getObjectRadius(objectType) {
    switch (objectType) {
        // Environment objects
        case 'stone_pillar': return VOXEL_SIZE * 3;
        case 'stone_vase': return VOXEL_SIZE * 1.5;
        case 'stone_rubble': return VOXEL_SIZE * 2.5;
        case 'ritual_circle': return VOXEL_SIZE * 5;
        case 'aether_torch': return VOXEL_SIZE * 1.5;

        // Other objects
        case 'large_chest': return VOXEL_SIZE * 3;
        case 'table': return VOXEL_SIZE * 2.5;
        case 'torch': return VOXEL_SIZE * 6; // Increased for larger minimum distance
        case 'vine': return VOXEL_SIZE * 0.5; // Very small radius for vines

        // Default
        default: return DEFAULT_OBJECT_RADIUS;
    }
}

// --- NEW: Constants for Object Placement ---
const MIN_DISTANCE_FROM_DOOR = DOOR_WIDTH; // Minimum distance object center can be from door center
const DEFAULT_OBJECT_RADIUS = VOXEL_SIZE * 2; // Default approximate radius for collision checks
const MAX_PLACEMENT_ATTEMPTS = 20; // Max attempts to find a spot for an object
const DOOR_HEIGHT = WALL_HEIGHT * 0.9;



// --- NEW: Room Area Thresholds for Torch Scaling ---
const SMALL_AREA_THRESHOLD = 14 * 14;  // 196 (14x14)
const MEDIUM_AREA_THRESHOLD = 28 * 14; // 392 (28x14)

// These constants are already declared above

/**
 * Calculate the expected floor height at a given position accounting for curvature
 * Uses the same algorithm as the floor generation to ensure objects sit on the highest points
 * @param {number} x - World X coordinate
 * @param {number} z - World Z coordinate
 * @param {number} baseY - Base floor Y coordinate
 * @returns {number} Adjusted Y coordinate accounting for curvature
 * @private
 */
function _calculateFloorCurvatureHeight(x, z, baseY, roomData = null) {
    // Use the same unified algorithm as in caveFloor.js generation
    // FIXED: Apply the same U-shaped room coordinate handling as floor generation
    const worldX = x;
    let worldZ = -z; // Default: Invert Z coordinate for proper north/south alignment

    // FIXED: For U-shaped rooms, apply the same coordinate transformation as floor generation
    if (roomData && roomData.shapeKey === 'U_SHAPE_DOWN') {
        worldZ = z; // Don't invert for U-shaped rooms - use original Z to match floor generation
    }

    // Large-scale room curvature (similar to fog stencil approach)
    const largeWaves = Math.sin(worldX * 0.08) * Math.cos(worldZ * 0.06) * 0.2;
    const mediumWaves = (Math.sin(worldX * 0.15) + Math.cos(worldZ * 0.18)) * 0.12;
    const smallDetails = (Math.sin(worldX * 0.4) * Math.cos(worldZ * 0.35)) * 0.06;
    const ridges = Math.sin(worldX * 0.1 + worldZ * 0.08) * 0.15;

    // Combine all layers for unified terrain (excluding tile variation since we need consistency)
    const heightVariation = largeWaves + mediumWaves + smallDetails + ridges;

    // Add the height variation to the base floor Y
    // Add a small buffer (0.1) to ensure objects sit slightly above the surface
    return baseY + heightVariation + 0.1;
}

// --- Helper function to add floor segments ---
// Accepts the specific floor prefab FUNCTION
export function addFloorSegment(roomGroup, collisionMeshes, width, depth, position, roomData, floorPrefabFunc, walkableTiles) {
    if (!floorPrefabFunc) { // Check if function was provided
        console.error("[addFloorSegment] Missing floorPrefabFunc. Skipping floor.");
        return null;
    }
    // CRITICAL FIX: Pass segment position to floor generation for global coordinate calculation
    const roomDataWithSegmentPos = {
        ...roomData,
        segmentPosition: position // Add segment position for global coordinate calculation
    };
    // const floorMesh = themeData.floorPrefab(width, depth, roomData); // OLD
    const floorMesh = floorPrefabFunc(width, depth, roomDataWithSegmentPos); // NEW - Call passed function with segment position
    
    console.log(`[addFloorSegment] Floor created for room ${roomData.id}: type=${floorMesh?.constructor?.name}, name="${floorMesh?.name}", isGroup=${floorMesh?.isGroup}, isMesh=${floorMesh?.isMesh}`);
    
    // DEBUG: Show the floor mesh structure
    if (floorMesh) {
        console.log(`[addFloorSegment] Floor mesh details:`, {
            name: floorMesh.name,
            type: floorMesh.type,
            isGroup: floorMesh.isGroup,
            isMesh: floorMesh.isMesh,
            childrenCount: floorMesh.children?.length || 0,
            userData: floorMesh.userData
        });
    }
    
    if (floorMesh) {
        floorMesh.position.copy(position);
        roomGroup.add(floorMesh);

        const addColliders = (mesh) => {
            if (mesh.isGroup) {
                console.log(`[addFloorSegment] Processing floor GROUP "${mesh.name}" with ${mesh.children.length} children`);
                mesh.children.forEach((child, index) => {
                    if (child.isMesh) {
                        child.receiveShadow = true;
                        // Mark as floor for collision detection
                        if (!child.userData) child.userData = {};
                        child.userData.isFloor = true;
                        collisionMeshes.push(child);
                        const tileWorldPosition = new THREE.Vector3();
                        child.getWorldPosition(tileWorldPosition);
                        walkableTiles.push(tileWorldPosition);
                        console.log(`[addFloorSegment]   - Child ${index}: marked as floor, name="${child.name}"`);
                    }
                });
            } else if (mesh.isMesh) {
                console.log(`[addFloorSegment] Processing floor MESH "${mesh.name}"`);
                mesh.receiveShadow = true;
                // Mark as floor for collision detection
                if (!mesh.userData) mesh.userData = {};
                mesh.userData.isFloor = true;
                collisionMeshes.push(mesh);
                const tileWorldPosition = new THREE.Vector3();
                mesh.getWorldPosition(tileWorldPosition);
                walkableTiles.push(tileWorldPosition);
            }
        };
        addColliders(floorMesh);
        return floorMesh; // Return the floor mesh for potential tinting
    }
    return null;
}

// --- Helper to create a single torch light instance ---
function _createTorchLight() {
    // Significantly increased brightness and range for better illumination
    const pointLight = new THREE.PointLight(0xffbb60, 1.5, 12.0);
    pointLight.castShadow = false;
    pointLight.name = "torchLight";
    pointLight.userData = { baseIntensity: 1.5, baseRange: 12.0 };
    return pointLight;
}

// --- Helper function to add wall segments ---
// Accepts the specific wall prefab FUNCTION
export function addWallSegment(roomGroup, collisionMeshes, lights, segmentWidth, position, rotationY, isOuterBoundary, isDarkRoom, roomData, roomMaxZ, wallPrefabFunc, isVisible = true) {
    if (!segmentWidth || typeof segmentWidth !== 'number' || segmentWidth <= 0 || !position || !roomData) {
        console.error(`[addWallSegment] Invalid parameters received: segmentWidth=${segmentWidth}. Skipping.`);
        return;
    }
    // if (!themeData || !themeData.wallPrefab) { // OLD Check
    if (!wallPrefabFunc) { // NEW Check
        console.error("[addWallSegment] Missing wallPrefabFunc. Skipping wall segment.");
        return;
    }

    // const wallData = themeData.wallPrefab(...); // OLD
    const wallData = wallPrefabFunc(segmentWidth, WALL_HEIGHT, WALL_DEPTH, roomData, isDarkRoom, position, rotationY); // NEW

    if (!wallData || !wallData.group) {
         console.error(`[addWallSegment] Wall prefab function failed for segmentWidth=${segmentWidth}.`);
         return;
    }

    const wallSegmentGroup = wallData.group;
    wallSegmentGroup.position.copy(position);
    wallSegmentGroup.rotation.y = rotationY;
    wallSegmentGroup.traverse(child => { if (child.isMesh) child.castShadow = true; });

    wallSegmentGroup.userData.isWallSegment = true;
    wallSegmentGroup.userData.width = segmentWidth; // Store width for potential use (e.g., decorations)

    // CRITICAL FIX: Use passed isVisible parameter, but still check for south wall override
    let finalVisibility = isVisible;
    const epsilon = 0.1;
    if (position.z >= roomMaxZ - epsilon) {
        finalVisibility = false; // South walls are always invisible
    }
    wallSegmentGroup.visible = finalVisibility;

    roomGroup.add(wallSegmentGroup);

    // Always add collision meshes for walls (even invisible ones like south walls)
    if (wallSegmentGroup.isMesh) {
        // CRITICAL FIX: Mark wall collision meshes with proper metadata
        wallSegmentGroup.userData.isWallCollision = true;
        wallSegmentGroup.userData.wallDirection = wallSegmentGroup.userData.direction || 'unknown';
        wallSegmentGroup.userData.isVisible = finalVisibility;
        collisionMeshes.push(wallSegmentGroup);
    } else {
        wallSegmentGroup.traverse(child => {
            if (child.isMesh) {
                // CRITICAL FIX: Mark wall collision meshes with proper metadata
                child.userData.isWallCollision = true;
                child.userData.wallDirection = wallSegmentGroup.userData.direction || 'unknown';
                child.userData.isVisible = finalVisibility;
                collisionMeshes.push(child);
            }
        });
    }

    // Only add torch lights for visible walls
    if (wallSegmentGroup.visible) {
        (wallData.torchPositions || []).forEach(localPos => { // Add default empty array
            const torchLight = _createTorchLight();
            const worldPos = wallSegmentGroup.localToWorld(localPos.clone());
            torchLight.position.copy(worldPos);
            lights.push(torchLight);
        });
    }

    // Return the wall segment group for potential secret wall integration
    return wallSegmentGroup;
}

/**
 * Extract materials from wall prefab for use in door archways
 * @param {string} wallType - Wall type (e.g., 'stonebrick_with_water', 'sandstone_brick')
 * @returns {Array|null} Array of materials or null if not found
 */
function extractWallMaterialsForDoor(wallType) {
    try {
        // Use the imported material function

        switch (wallType) {
            case 'stonebrick_with_water':
                // Use the same stone materials as water walls
                return [
                    _getMaterialByHex_Cached('8b7355'), // Brown stone
                    _getMaterialByHex_Cached('696969'), // Dim gray
                    _getMaterialByHex_Cached('708090')  // Slate gray
                ];

            case 'sandstone_brick':
                // Use sandstone colors for treasure room doors
                return [
                    _getMaterialByHex_Cached('f4a460'), // Sandy brown
                    _getMaterialByHex_Cached('deb887'), // Burlywood
                    _getMaterialByHex_Cached('d2b48c')  // Tan
                ];

            case 'mystical_stone_brick':
                // Use mystical colors for pond room doors
                return [
                    _getMaterialByHex_Cached('8A7CA8'), // Mystical purple-gray
                    _getMaterialByHex_Cached('9B7EBD'), // Lighter mystical purple
                    _getMaterialByHex_Cached('6B5B95')  // Darker mystical purple
                ];

            default:
                console.log(`[extractWallMaterialsForDoor] No custom materials defined for wall type: ${wallType}`);
                return null;
        }
    } catch (error) {
        console.error('[extractWallMaterialsForDoor] Error extracting materials:', error);
        return null;
    }
}

/**
 * Create custom door archway for event rooms based on wall type
 * @param {string} wallType - Wall type (e.g., 'stonebrick_with_water', 'sandstone_brick')
 * @param {number} width - Door width
 * @param {number} height - Door height
 * @param {number} depth - Door depth
 * @returns {THREE.Group} Custom door archway group
 */
function createEventRoomDoor(wallType, width, height, depth) {
    console.log(`[createEventRoomDoor] Creating door for wall type: ${wallType}`);
    
    switch (wallType) {
        case 'stonebrick_with_water':
            console.log(`[createEventRoomDoor] Using water archway door`);
            return createWaterArchwayDoor(width, height, depth);

        case 'sandstone_brick':
            console.log(`[createEventRoomDoor] Using sandstone archway door`);
            return createSandstoneArchwayDoor(width, height, depth);
            
        case 'mystical_stone_brick':
            console.log(`[createEventRoomDoor] Using water archway door for mystical stone (pond room)`);
            // Mystical stone brick rooms (like mysterious pond) should use water-themed doors
            return createWaterArchwayDoor(width, height, depth);

        default:
            console.warn(`[createEventRoomDoor] No custom door defined for wall type: ${wallType}, using default stone archway`);
            // Use imported default door
            const door = createStoneArchwayDoor(width, height, depth);
            if (!door) {
                console.error(`[createEventRoomDoor] Failed to create default stone archway door!`);
            }
            return door;
    }
}

// --- Function to add door/archway prefab ---
// Accepts the specific door prefab FUNCTION
export function addDoorPrefab(roomGroup, collisionMeshes, position, rotationY, roomData, doorPrefabFunc, connectionDirection = null) {
    // if (!themeData) { // OLD Check
    if (!doorPrefabFunc) { // NEW Check
        console.warn(`[addDoorPrefab] Missing doorPrefabFunc for room ${roomData.id}. Skipping door.`);
        return null;
    }

    // ENHANCED: Use custom door archway for event rooms
    let doorGroup;
    if (roomData.type === 'EVENT' && roomData.eventRoomData && roomData.eventRoomData.materials && roomData.eventRoomData.materials.walls) {
        const wallType = roomData.eventRoomData.materials.walls;
        console.log(`[addDoorPrefab] Creating EVENT room door for wall type: ${wallType}`);
        doorGroup = createEventRoomDoor(wallType, DOOR_WIDTH, DOOR_HEIGHT, DOOR_DEPTH);
        console.log(`[addDoorPrefab] EVENT room door created:`, doorGroup);
        if (doorGroup) {
            console.log(`[addDoorPrefab] Door has ${doorGroup.children.length} children`);
            let meshCount = 0;
            doorGroup.traverse(child => {
                if (child.isMesh) {
                    meshCount++;
                    console.log(`[addDoorPrefab] Door mesh ${meshCount}:`, {
                        name: child.name,
                        visible: child.visible,
                        material: child.material,
                        geometry: child.geometry,
                        position: child.position,
                        scale: child.scale
                    });
                }
            });
            console.log(`[addDoorPrefab] Total meshes in door: ${meshCount}`);
        } else {
            console.error(`[addDoorPrefab] createEventRoomDoor returned null/undefined for wall type: ${wallType}`);
        }
    } else {
        // Use default door prefab
        console.log(`[addDoorPrefab] Using default door prefab for room type: ${roomData.type}`);
        doorGroup = doorPrefabFunc(DOOR_WIDTH, DOOR_HEIGHT, DOOR_DEPTH);
    }

    if (doorGroup) {
        // CRITICAL FIX: Position door properly accounting for wall thickness
        // Doors should be flush with the wall surface, not floating in front
        doorGroup.position.set(position.x, 0, position.z);
        doorGroup.rotation.y = rotationY;

        // Check door orientation for special handling
        const epsilon = 0.1;
        const normalizedRotation = ((rotationY % (2 * Math.PI)) + 2 * Math.PI) % (2 * Math.PI);
        const isNorthDoor = Math.abs(normalizedRotation) < epsilon;
        const isSouthDoor = Math.abs(normalizedRotation - Math.PI) < epsilon;
        
        // Apply outward offset to make door protrude from wall
        // The door geometry extends from -depth/2 to +depth/2 in its local Z axis
        // We want the door to protrude outward from the wall surface
        
        // Calculate the total outward offset for the door
        // We want the door to be mostly embedded in the wall with just a small protrusion
        // Start with the door centered on the wall, then push it out slightly
        
        // Base offset to center the thicker door on the thinner wall
        const centeringOffset = (DOOR_DEPTH - WALL_DEPTH) / 2;
        
        // How much we want the door to protrude from the wall surface
        // This is the actual visible protrusion amount
        // North and south doors protrude less than east and west doors
        const visibleProtrusion = (isNorthDoor || isSouthDoor) ? 0.06 : 0.1;
        
        // Total offset: center the door, then push it out by the protrusion amount
        const totalOutwardOffset = visibleProtrusion;
        
        // The outward direction depends on whether this is a north door or not
        let outwardDirection;
        if (isNorthDoor) {
            // North doors need to face the opposite direction (towards the player)
            // So we use positive Z instead of negative Z
            outwardDirection = new THREE.Vector3(0, 0, 1);
            outwardDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), rotationY);
        } else {
            // All other doors use the standard direction
            outwardDirection = new THREE.Vector3(0, 0, -1);
            outwardDirection.applyAxisAngle(new THREE.Vector3(0, 1, 0), rotationY);
        }
        
        // Move the door outward from the wall
        doorGroup.position.addScaledVector(outwardDirection, totalOutwardOffset);

        // NEW: Check if this door leads to an event room and add cyan sigil
        if (connectionDirection && roomData.connections && roomData.connections[connectionDirection]) {
            const targetRoomId = roomData.connections[connectionDirection];
            // Check if target room is an event room (we'll need to access this from the dungeon handler)
            if (window.dungeonHandler && window.dungeonHandler.floorLayout) {
                const targetRoom = window.dungeonHandler.floorLayout.get(targetRoomId);
                if (targetRoom && targetRoom.type === 'EVENT') {
                    console.log(`[addDoorPrefab] Adding event room sigil to door leading to room ${targetRoomId}`);
                    const eventSigil = createEventRoomSigil();
                    if (eventSigil) {
                        // Position sigil at the top of the archway
                        eventSigil.position.set(0, WALL_HEIGHT * 0.85, -WALL_DEPTH * 0.6); // Above door, slightly forward
                        doorGroup.add(eventSigil);
                        console.log(`[addDoorPrefab] Event room sigil added to door`);
                    }
                }
            }
        }

        doorGroup.traverse(child => {
            if (child.isMesh) {
                child.castShadow = true;
                collisionMeshes.push(child);
                // CRITICAL: Also mark child meshes as doors for event room visibility system
                child.userData.isDoor = true;
                child.userData.parentDoorGroup = doorGroup;
            }
        });
        doorGroup.userData.isDoor = true;
        
        // Additional debugging for event room doors
        if (roomData.type === 'EVENT') {
            console.log(`[addDoorPrefab] EVENT room door final setup complete:`);
            console.log(`[addDoorPrefab] - Position:`, doorGroup.position);
            console.log(`[addDoorPrefab] - Rotation:`, doorGroup.rotation.y);
            console.log(`[addDoorPrefab] - Children count:`, doorGroup.children.length);
            console.log(`[addDoorPrefab] - Visible:`, doorGroup.visible);
        }
        
        return doorGroup;
    } else {
        console.error(`[addDoorPrefab] Door prefab function failed (returned falsy value) for room ${roomData.id}`);
        console.error(`[addDoorPrefab] Room type: ${roomData.type}, Has eventRoomData: ${!!roomData.eventRoomData}`);
        if (roomData.eventRoomData) {
            console.error(`[addDoorPrefab] Event room materials:`, roomData.eventRoomData.materials);
        }
        return null;
    }
}

// --- Function to create cyan blue pulsing sigil for event room doors ---
function createEventRoomSigil() {
    try {
        const sigilGroup = new THREE.Group();

        // Create a simple geometric sigil (diamond shape with inner pattern)
        const sigilSize = VOXEL_SIZE * 8; // Size of the sigil

        // Outer diamond shape
        const outerShape = new THREE.Shape();
        outerShape.moveTo(0, sigilSize * 0.5);
        outerShape.lineTo(sigilSize * 0.3, 0);
        outerShape.lineTo(0, -sigilSize * 0.5);
        outerShape.lineTo(-sigilSize * 0.3, 0);
        outerShape.closePath();

        // Inner diamond (smaller)
        const innerHole = new THREE.Path();
        innerHole.moveTo(0, sigilSize * 0.25);
        innerHole.lineTo(sigilSize * 0.15, 0);
        innerHole.lineTo(0, -sigilSize * 0.25);
        innerHole.lineTo(-sigilSize * 0.15, 0);
        innerHole.closePath();
        outerShape.holes.push(innerHole);

        // Create geometry
        const sigilGeometry = new THREE.ShapeGeometry(outerShape);

        // Create pulsing material
        const sigilMaterial = new THREE.MeshStandardMaterial({
            color: 0x00FFFF, // Cyan blue
            transparent: true,
            opacity: 0.8,
            side: THREE.DoubleSide,
            emissive: 0x004444,
            emissiveIntensity: 0.5,
            roughness: 0.1,
            metalness: 0.0
        });

        const sigilMesh = new THREE.Mesh(sigilGeometry, sigilMaterial);
        sigilGroup.add(sigilMesh);

        // Add pulsing animation
        sigilGroup.userData.isPulsing = true;
        sigilGroup.userData.pulseSpeed = 2.0;
        sigilGroup.userData.pulseIntensity = 0.5;
        sigilGroup.userData.originalEmissiveIntensity = 0.5;
        sigilGroup.userData.material = sigilMaterial;

        // Store animation function for update loop
        sigilGroup.userData.updatePulse = function(time) {
            const pulse = Math.sin(time * this.pulseSpeed) * this.pulseIntensity + this.originalEmissiveIntensity;
            this.material.emissiveIntensity = pulse;
            const opacity = 0.6 + Math.sin(time * this.pulseSpeed * 0.5) * 0.3;
            this.material.opacity = opacity;
        };

        sigilGroup.name = 'eventRoomSigil';
        console.log(`[createEventRoomSigil] Created cyan pulsing sigil`);
        return sigilGroup;
    } catch (error) {
        console.error(`[createEventRoomSigil] Error creating event room sigil:`, error);
        return null;
    }
}

// TODO: Wall decorations functionality could be implemented here if needed
// Would integrate with areas.js decoration definitions

// --- Helper to define wall AND floor segments based on room shape ---
function _getRoomGeometrySegments(shapeKey, roomWidth, roomDepth, halfWidth, halfDepth, connections) {
    let wallSegments = [];
    let floorSegments = [];
    const R = ROOM_WORLD_SIZE; // e.g., 14
    const H = R / 2;       // e.g., 7

    switch (shapeKey) {
        case 'RECTANGULAR':
        case 'RECT_2X1':
            // Walls
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -halfDepth), rotation: 0, length: roomWidth, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(halfWidth, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, halfDepth), rotation: Math.PI, length: roomWidth, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-halfWidth, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'w' }); // W

            // CRITICAL FIX: Add corner reinforcement walls to prevent gaps at corners
            const cornerOffset = WALL_DEPTH * 0.5;
            wallSegments.push({ position: new THREE.Vector3(halfWidth - cornerOffset, WALL_HEIGHT / 2, -halfDepth + cornerOffset), rotation: Math.PI / 4, length: cornerOffset * 2, isOuterBoundary: true, direction: 'n', isVisible: false }); // NE corner
            wallSegments.push({ position: new THREE.Vector3(halfWidth - cornerOffset, WALL_HEIGHT / 2, halfDepth - cornerOffset), rotation: -Math.PI / 4, length: cornerOffset * 2, isOuterBoundary: true, direction: 's', isVisible: false }); // SE corner
            wallSegments.push({ position: new THREE.Vector3(-halfWidth + cornerOffset, WALL_HEIGHT / 2, halfDepth - cornerOffset), rotation: Math.PI * 3/4, length: cornerOffset * 2, isOuterBoundary: true, direction: 's', isVisible: false }); // SW corner
            wallSegments.push({ position: new THREE.Vector3(-halfWidth + cornerOffset, WALL_HEIGHT / 2, -halfDepth + cornerOffset), rotation: -Math.PI * 3/4, length: cornerOffset * 2, isOuterBoundary: true, direction: 'n', isVisible: false }); // NW corner

            // Floor (Single piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: roomWidth, depth: roomDepth });
            break;

        case 'L_SHAPE':
            // L-Shape: A room in the shape of an L
            // The L is positioned with the vertical part on the left and horizontal part on the bottom
            wallSegments = [
                // North walls
                // Top of the vertical part of the L
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // East walls
                // Right side of the horizontal part
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // South walls
                // Bottom of the horizontal part
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' },

                // West walls
                // Left side of the vertical part
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Left side of the horizontal part
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // Inner corner walls
                // Top wall of the horizontal part (connecting to vertical part)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Right wall of the vertical part (connecting to horizontal part)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },


            ];

            // Floor segments - create the L shape with two rectangles
            // Vertical part (left side)
            floorSegments.push({ position: new THREE.Vector3(-H, 0, -H), width: R, depth: R });
            // Horizontal part (bottom)
            floorSegments.push({ position: new THREE.Vector3(0, 0, H), width: 2*R, depth: R });
            break;

        case 'T_SHAPE':
            // Redefined based on floor perimeter (Origin at junction)
            // CRITICAL FIX: Add invisible collision walls to prevent player walking through gaps
            wallSegments = [
                // Outer Perimeter (Remain the same)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }, // 1. Top of bar
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 2. Right of top-right square
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },    // 3. Right of stem square
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },    // 4. Bottom of stem square
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },   // 5. Left of stem square
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 6. Left of top-left square
                // Inner Walls (Now treated as potential connection points)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },  // 7. Bottom of top-right square (South)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }   // 8. Bottom of top-left square (South)
            ];
            // Floor (Remains the same)
            floorSegments.push({ position: new THREE.Vector3(0, 0, -H), width: 3*R, depth: R }); // Top bar (3x1). Z spans [-R, 0]
            floorSegments.push({ position: new THREE.Vector3(0, 0, H), width: R, depth: R }); // Stem (1x1). Z spans [0, R]
            break;

        case 'CROSS_SHAPE':
             // Corrected: Mark all inner walls as potential outer boundaries with directions
            wallSegments = [
                // Outer Walls (Remain the same)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }, // 1. Top face
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 4. Right face
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },   // 7. Bottom face
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 10. Left face
                // Inner Walls (Now treated as potential connection points)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 2. Top-right inner vertical (East)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, -H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },      // 3. Top-right inner horizontal (North)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },   // 5. Bottom-right inner horizontal (South)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 6. Bottom-right inner vertical (West)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },// 8. Bottom-left inner vertical (East)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },  // 9. Bottom-left inner horizontal (South)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },     // 11. Top-left inner horizontal (North)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },// 12. Top-left inner vertical (West)


            ];
            // Floor (Remains the same)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: R });   // Center
            floorSegments.push({ position: new THREE.Vector3(0, 0, -R), width: R, depth: R });  // Top
            floorSegments.push({ position: new THREE.Vector3(0, 0, R), width: R, depth: R });   // Bottom
            floorSegments.push({ position: new THREE.Vector3(-R, 0, 0), width: R, depth: R }); // Left
            floorSegments.push({ position: new THREE.Vector3(R, 0, 0), width: R, depth: R });  // Right
            break;

        case 'BOSS_ARENA':
            // A large square arena for boss fights (similar to SQUARE_2X2 but larger)
            const B = 2 * R; // Boss room size multiplier (2x normal room size)

            // Walls - Following the same pattern as SQUARE_2X2 and other rectangular rooms
            wallSegments = [
                // North wall (top)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -B), rotation: 0, length: 2*B, isOuterBoundary: true, direction: 'n' },
                // East wall (right)
                { position: new THREE.Vector3(B, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*B, isOuterBoundary: true, direction: 'e' },
                // South wall (bottom)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, B), rotation: Math.PI, length: 2*B, isOuterBoundary: true, direction: 's' },
                // West wall (left)
                { position: new THREE.Vector3(-B, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*B, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor (Single large piece, similar to SQUARE_2X2)
            floorSegments = [
                { position: new THREE.Vector3(0, 0, 0), width: 2*B, depth: 2*B }
            ];
            break;

        case 'SQUARE_2X2':
            // Walls (Standard 4 walls for 2Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 2*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 2Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 2*R, depth: 2*R });
            break;

        case 'RECT_3X1':
            // Walls (Standard 4 walls for 3Rx1R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, H), rotation: Math.PI, length: 3*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 3Rx1R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 3*R, depth: R });
            break;

        case 'RECT_1X2':
             // Walls (Standard 4 walls for 1Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-H, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 1Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: 2*R });
            break;

        case 'RECT_1X3':
            // Walls (Standard 4 walls for 1Rx3R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-H, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 1Rx3R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: R, depth: 3*R });
            break;

        case 'RECT_3X2':
            // Walls (Standard 4 walls for 3Rx2R)
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' }); // N
            wallSegments.push({ position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' }); // E
            wallSegments.push({ position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 3*R, isOuterBoundary: true, direction: 's' }); // S
            wallSegments.push({ position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }); // W
            // Floor (Single 3Rx2R piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 3*R, depth: 2*R });
            break;

        case 'U_SHAPE_DOWN': // U-shaped room with opening at the bottom
            // Define the room as a U shape with the opening at the bottom (south)
            // The room consists of a top horizontal bar (3x1) and two vertical legs (1x1 each)
            wallSegments = [
                // Outer perimeter walls
                // North wall (top of the U)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 3*R, isOuterBoundary: true, direction: 'n' },

                // East walls
                // Top-right corner to middle-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Middle-right to bottom-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // South walls (bottom of the legs)
                // Bottom of right leg
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },
                // Bottom of left leg
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, R), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },

                // West walls
                // Bottom-left to middle-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Middle-left to top-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // Inner walls that form the sides of the U opening - VISIBLE
                // Right inner wall (left edge of right leg) - faces west into the opening
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Left inner wall (right edge of left leg) - faces east into the opening
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // CRITICAL FIX: Add invisible collision walls to completely block the U opening
                // The opening is between the two bottom legs, from X=-H to X=+H, Z=0 to Z=R

                // Horizontal wall across the top of the opening (at Z=0, between top bar and legs)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, 0), rotation: 0, length: 2*H, isOuterBoundary: true, direction: 'n', isVisible: false },

                // Horizontal wall across the bottom of the opening (at Z=R, between the two legs)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: 0, length: 2*H, isOuterBoundary: true, direction: 'n', isVisible: false },

                // Vertical wall on left side of opening (at X=-H, from Z=0 to Z=R)
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e', isVisible: false },

                // Vertical wall on right side of opening (at X=+H, from Z=0 to Z=R)
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w', isVisible: false }
            ];

            // Floor segments
            // Top horizontal bar (3x1)
            floorSegments.push({ position: new THREE.Vector3(0, 0, -H), width: 3*R, depth: R });
            // Bottom-left leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(-R, 0, H), width: R, depth: R });
            // Bottom-right leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(R, 0, H), width: R, depth: R });
            break;

        case 'U_SHAPE_UP': // U-shaped room with opening at the top
            // Define the room as a U shape with the opening at the top (north)
            // The room consists of a bottom horizontal bar (3x1) and two vertical legs (1x1 each)
            wallSegments = [
                // Outer perimeter walls
                // South wall (bottom of the U)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 3*R, isOuterBoundary: true, direction: 's' },

                // West walls
                // Bottom-left to middle-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Middle-left to top-left
                { position: new THREE.Vector3(-(R+H), WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // North walls (top of the legs)
                // Top of left leg
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Top of right leg
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, -R), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // East walls
                // Top-right to middle-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Middle-right to bottom-right
                { position: new THREE.Vector3(R+H, WALL_HEIGHT / 2, H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // Inner walls that form the sides of the U opening - VISIBLE
                // Left inner wall (right edge of left leg) - faces east into the opening
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -H), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Right inner wall (left edge of right leg) - faces west into the opening
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -H), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // MISSING WALL: Horizontal wall across the bottom of the opening (at Z=0, between bottom bar and legs)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, 0), rotation: Math.PI, length: 2*H, isOuterBoundary: true, direction: 's' }
            ];

            // Floor segments
            // Bottom horizontal bar (3x1)
            floorSegments.push({ position: new THREE.Vector3(0, 0, H), width: 3*R, depth: R });
            // Top-left leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(-R, 0, -H), width: R, depth: R });
            // Top-right leg (1x1)
            floorSegments.push({ position: new THREE.Vector3(R, 0, -H), width: R, depth: R });
            break;

        case 'U_SHAPE_LEFT': // U-shaped room with opening at the left
            // Define the room as a U shape with the opening at the left (west)
            // The room consists of a right vertical bar (1x3) and two horizontal arms (1x1 each)
            wallSegments = [
                // Outer perimeter walls
                // East wall (right side of the U)
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'e' },

                // North walls
                // Top-right to middle-top
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Middle-top to top-left
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // West walls (left side of the arms)
                // Left of top arm
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, -R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },
                // Left of bottom arm
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, R), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' },

                // South walls
                // Bottom-left to middle-bottom
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },
                // Middle-bottom to bottom-right
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },

                // Inner walls that form the sides of the U opening - VISIBLE
                // Top inner wall (bottom edge of top arm) - faces south into the opening
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },
                // Bottom inner wall (top edge of bottom arm) - faces north into the opening
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // MISSING WALL: Vertical wall across the right of the opening (at X=0, between right bar and arms)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*H, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor segments
            // Right vertical bar (1x3)
            floorSegments.push({ position: new THREE.Vector3(H, 0, 0), width: R, depth: 3*R });
            // Top-left arm (1x1)
            floorSegments.push({ position: new THREE.Vector3(-H, 0, -R), width: R, depth: R });
            // Bottom-left arm (1x1)
            floorSegments.push({ position: new THREE.Vector3(-H, 0, R), width: R, depth: R });
            break;

        case 'U_SHAPE_RIGHT': // U-shaped room with opening at the right
            // Define the room as a U shape with the opening at the right (east)
            // The room consists of a left vertical bar (1x3) and two horizontal arms (1x1 each)
            wallSegments = [
                // Outer perimeter walls
                // West wall (left side of the U)
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 3*R, isOuterBoundary: true, direction: 'w' },

                // South walls
                // Bottom-left to middle-bottom
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },
                // Middle-bottom to bottom-right
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, R+H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },

                // East walls (right side of the arms)
                // Right of bottom arm
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // Right of top arm
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, -R), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },

                // North walls
                // Top-right to middle-top
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Middle-top to top-left
                { position: new THREE.Vector3(-H, WALL_HEIGHT / 2, -(R+H)), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },

                // Inner walls that form the sides of the U opening - VISIBLE
                // Bottom inner wall (top edge of bottom arm) - faces north into the opening
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, H), rotation: 0, length: R, isOuterBoundary: true, direction: 'n' },
                // Top inner wall (bottom edge of top arm) - faces south into the opening
                { position: new THREE.Vector3(H, WALL_HEIGHT / 2, -H), rotation: Math.PI, length: R, isOuterBoundary: true, direction: 's' },

                // MISSING WALL: Vertical wall across the left of the opening (at X=0, between left bar and arms)
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*H, isOuterBoundary: true, direction: 'e' }
            ];

            // Floor segments
            // Left vertical bar (1x3)
            floorSegments.push({ position: new THREE.Vector3(-H, 0, 0), width: R, depth: 3*R });
            // Top-right arm (1x1)
            floorSegments.push({ position: new THREE.Vector3(H, 0, -R), width: R, depth: R });
            // Bottom-right arm (1x1)
            floorSegments.push({ position: new THREE.Vector3(H, 0, R), width: R, depth: R });
            break;



        case 'CORRIDOR_LONG': // Long narrow corridor (4x1)
            // Define a long narrow corridor extending east-west
            wallSegments = [
                // North wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: 0, length: 4*R, isOuterBoundary: true, direction: 'n' },
                // East wall
                { position: new THREE.Vector3(2*R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // South wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, H), rotation: Math.PI, length: 4*R, isOuterBoundary: true, direction: 's' },
                // West wall
                { position: new THREE.Vector3(-2*R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor (Single long piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 4*R, depth: R });
            break;













        case 'CORRIDOR_SHORT': // Short corridor (2x1)
            // Define a short corridor extending east-west
            wallSegments = [
                // North wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -H), rotation: 0, length: 2*R, isOuterBoundary: true, direction: 'n' },
                // East wall
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: R, isOuterBoundary: true, direction: 'e' },
                // South wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, H), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' },
                // West wall
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: R, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor (Single piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 2*R, depth: R });
            break;

        case 'SQUARE_2X2': // Larger square (2x2)
            // Define a 2x2 square room
            wallSegments = [
                // North wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -R), rotation: 0, length: 2*R, isOuterBoundary: true, direction: 'n' },
                // East wall
                { position: new THREE.Vector3(R, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'e' },
                // South wall
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, R), rotation: Math.PI, length: 2*R, isOuterBoundary: true, direction: 's' },
                // West wall
                { position: new THREE.Vector3(-R, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: 2*R, isOuterBoundary: true, direction: 'w' }
            ];

            // Floor (Single 2x2 piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: 2*R, depth: 2*R });
            break;

        case 'SQUARE_1X1':
        default:
            // Walls
            wallSegments = [
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, -halfDepth), rotation: 0, length: roomWidth, isOuterBoundary: true, direction: 'n' }, // N
                { position: new THREE.Vector3(halfWidth, WALL_HEIGHT / 2, 0), rotation: Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'e' }, // E
                { position: new THREE.Vector3(0, WALL_HEIGHT / 2, halfDepth), rotation: Math.PI, length: roomWidth, isOuterBoundary: true, direction: 's' }, // S
                { position: new THREE.Vector3(-halfWidth, WALL_HEIGHT / 2, 0), rotation: -Math.PI / 2, length: roomDepth, isOuterBoundary: true, direction: 'w' } // W
            ];
            // Floor (Single piece)
            floorSegments.push({ position: new THREE.Vector3(0, 0, 0), width: roomWidth, depth: roomDepth });
            break;
    }
    return { wallSegments, floorSegments };
}

// Helper function has been moved to the top of the file

/**
 * Check if a position is within the valid area of a room shape
 * @param {number} x - X coordinate
 * @param {number} z - Z coordinate
 * @param {string} roomShape - Room shape key
 * @param {boolean} flipZ - Whether to flip Z coordinate (for floor geometry compatibility)
 * @returns {boolean} True if position is valid for the room shape
 */
function _isPositionInRoomShape(x, z, roomShape, flipZ = false) {
    if (!roomShape) {
        return true; // If no shape specified, allow all positions
    }

    // Apply Z-axis flip if needed (to match floor geometry coordinate system)
    const actualZ = flipZ ? -z : z;

    // Room constants (matching DungeonHandler.js)
    const R = 14; // ROOM_WORLD_SIZE
    const H = 7;  // Half of ROOM_WORLD_SIZE

    switch (roomShape) {
        case 'SQUARE_1X1':
        case 'RECTANGULAR':
        case 'RECT_2X1':
        case 'RECT_1X2':
        case 'SQUARE_2X2':
        case 'RECT_3X1':
        case 'RECT_1X3':
        case 'RECT_3X2':
            // Simple rectangular shapes - use bounding box validation
            return true; // These are already handled by bounding box

        case 'L_SHAPE':
            // CORRECTED L-shape: vertical part (top-left) and horizontal part (bottom)
            // Based on floor segments: (-H,0,-H) with R×R and (0,0,H) with 2R×R
            // Valid areas:
            // - Vertical part (top-left): x from -R to 0, z from -R to 0
            // - Horizontal part (bottom): x from -R to R, z from 0 to R
            const inVerticalPart = (x >= -R && x <= 0 && actualZ >= -R && actualZ <= 0);
            const inHorizontalPart = (x >= -R && x <= R && actualZ >= 0 && actualZ <= R);
            return inVerticalPart || inHorizontalPart;

        case 'T_SHAPE':
            // T-shape: horizontal bar at top (3x1) and vertical stem at bottom (1x1)
            // Top bar: (-R-H to R+H, -R to 0)
            // Bottom stem: (-H to H, 0 to R)
            const inTopBar = (x >= -(R + H) && x <= (R + H) && actualZ >= -R && actualZ <= 0);
            const inBottomStem = (x >= -H && x <= H && actualZ >= 0 && actualZ <= R);
            return inTopBar || inBottomStem;

        case 'U_SHAPE_DOWN':
            // U-shape with opening at bottom: top bar (3x1) and two side legs (1x1 each)
            // Top bar: (-R-H to R+H, -R to 0)
            // Left leg: (-R-H to -H, 0 to R)
            // Right leg: (H to R+H, 0 to R)
            const inUTopBar = (x >= -(R + H) && x <= (R + H) && actualZ >= -R && actualZ <= 0);
            const inULeftLeg = (x >= -(R + H) && x <= -H && actualZ >= 0 && actualZ <= R);
            const inURightLeg = (x >= H && x <= (R + H) && actualZ >= 0 && actualZ <= R);
            return inUTopBar || inULeftLeg || inURightLeg;

        case 'U_SHAPE_UP':
            // U-shape with opening at top: bottom bar (3x1) and two side legs (1x1 each)
            // Bottom bar: (-R-H to R+H, 0 to R)
            // Left leg: (-R-H to -H, -R to 0)
            // Right leg: (H to R+H, -R to 0)
            const inUUpBottomBar = (x >= -(R + H) && x <= (R + H) && actualZ >= 0 && actualZ <= R);
            const inUUpLeftLeg = (x >= -(R + H) && x <= -H && actualZ >= -R && actualZ <= 0);
            const inUUpRightLeg = (x >= H && x <= (R + H) && actualZ >= -R && actualZ <= 0);
            return inUUpBottomBar || inUUpLeftLeg || inUUpRightLeg;

        case 'U_SHAPE_LEFT':
            // U-shape with opening at left: right bar (1x3) and two horizontal arms (1x1 each)
            // Right bar: (0 to R, -R-H to R+H)
            // Top arm: (-R to 0, -R-H to -H)
            // Bottom arm: (-R to 0, H to R+H)
            const inULeftRightBar = (x >= 0 && x <= R && actualZ >= -(R + H) && actualZ <= (R + H));
            const inULeftTopArm = (x >= -R && x <= 0 && actualZ >= -(R + H) && actualZ <= -H);
            const inULeftBottomArm = (x >= -R && x <= 0 && actualZ >= H && actualZ <= (R + H));
            return inULeftRightBar || inULeftTopArm || inULeftBottomArm;

        case 'U_SHAPE_RIGHT':
            // U-shape with opening at right: left bar (1x3) and two horizontal arms (1x1 each)
            // Left bar: (-R to 0, -R-H to R+H)
            // Top arm: (0 to R, -R-H to -H)
            // Bottom arm: (0 to R, H to R+H)
            const inURightLeftBar = (x >= -R && x <= 0 && actualZ >= -(R + H) && actualZ <= (R + H));
            const inURightTopArm = (x >= 0 && x <= R && actualZ >= -(R + H) && actualZ <= -H);
            const inURightBottomArm = (x >= 0 && x <= R && actualZ >= H && actualZ <= (R + H));
            return inURightLeftBar || inURightTopArm || inURightBottomArm;



        case 'CORRIDOR_LONG':
            // Long corridor: 4x1 rectangle
            // Area: (-2R to 2R, -H to H)
            return (x >= -2*R && x <= 2*R && actualZ >= -H && actualZ <= H);











        case 'CORRIDOR_SHORT':
            // Short corridor: 2x1 rectangle
            // Area: (-R to R, -H to H)
            return (x >= -R && x <= R && actualZ >= -H && actualZ <= H);

        case 'SQUARE_2X2':
            // Larger square: 2x2 rectangle
            // Area: (-R to R, -R to R)
            return (x >= -R && x <= R && actualZ >= -R && actualZ <= R);

        case 'CROSS_SHAPE':
            // Cross shape: horizontal bar (3x1) and vertical bar (1x3) intersecting at center
            // Horizontal: (-R-H to R+H, -H to H)
            // Vertical: (-H to H, -R-H to R+H)
            const inHorizontalBar = (x >= -(R + H) && x <= (R + H) && actualZ >= -H && actualZ <= H);
            const inVerticalBar = (x >= -H && x <= H && actualZ >= -(R + H) && actualZ <= (R + H));
            return inHorizontalBar || inVerticalBar;

        case 'BOSS_ARENA':
            // Boss arena is typically much larger - use bounding box validation
            return true;

        default:
            console.warn(`[RoomShapeValidation] Unknown room shape: ${roomShape}, allowing position`);
            return true;
    }
}

// Helper function for door proximity check
function isTooCloseToDoors(position, doorPositions, minDistance) {
    for (const dir in doorPositions) {
        const doorPos = doorPositions[dir];
        if (doorPos && position.distanceTo(doorPos) < minDistance) {
            return true; // Too close to this door
        }
    }
    return false; // Far enough from all doors
}

// Helper function for collision check with already placed objects
function collidesWithPlacedObjects(position, radius, placedObjects) {
    for (const placed of placedObjects) {
        const distance = position.distanceTo(placed.position);
        if (distance < radius + placed.radius) {
            return true; // Collision detected
        }
    }
    return false; // No collision
}

// --- NEW: Helper function to place interior objects ---
/**
 * Get valid floor positions by scanning the actual floor meshes
 * @param {THREE.Group} roomGroup - The room group containing floor meshes
 * @param {number} gridSpacing - Spacing between scan points
 * @returns {Array} Array of valid floor positions
 */
function _scanForValidFloorPositions(roomGroup, gridSpacing = VOXEL_SIZE * 2, roomShape = null) {
    const validPositions = [];
    const raycaster = new THREE.Raycaster();
    
    // Early exit if no room group
    if (!roomGroup) {
        console.warn("[FloorScan] No room group provided");
        return validPositions;
    }
    
    // Find all floor meshes in the room - be more thorough
    const floorMeshes = [];
    let floorGroupCount = 0;
    let totalMeshCount = 0;
    
    roomGroup.traverse(child => {
        if (child.isMesh) {
            totalMeshCount++;
            // CRITICAL: Only accept textured cave floors, not black grid floors
            // Cave floors are named "caveFloorMesh_X" or similar textured floors
            const isCaveFloor = child.name?.toLowerCase().includes('cavefloor');
            const isMysticalFloor = child.name?.toLowerCase().includes('mysticalfloor'); 
            const isEventFloor = child.name?.toLowerCase().includes('eventfloor');
            const isSandstoneFloor = child.name?.toLowerCase().includes('sandstonefloor');
            const isAncientFloor = child.name?.toLowerCase().includes('ancientstone');
            // IMPORTANT: Exclude generic "stoneFloor" which is the black grid
            const isStoneFloor = child.name?.toLowerCase() === 'stonefloor';
            const isTexturedFloor = (isCaveFloor || isMysticalFloor || isEventFloor || 
                                    isSandstoneFloor || isAncientFloor) && !isStoneFloor;
            
            if (child.userData?.isFloor && isTexturedFloor) {
                floorMeshes.push(child);
                console.log(`[FloorScan] Found textured floor mesh with isFloor=true: ${child.name || 'unnamed'}`);
            } else if (isTexturedFloor) {
                // Only add textured floors, not generic "floor" meshes
                floorMeshes.push(child);
                console.log(`[FloorScan] Found textured floor mesh by name: ${child.name}`);
            } else if (child.name?.toLowerCase().includes('floor') && !isTexturedFloor) {
                if (isStoneFloor) {
                    console.log(`[FloorScan] Skipping stone floor (black grid): ${child.name}`);
                } else {
                    console.log(`[FloorScan] Skipping non-textured floor: ${child.name}`);
                }
            }
        } else if (child.isGroup && child.name?.toLowerCase().includes('floor')) {
            floorGroupCount++;
            console.log(`[FloorScan] Found floor GROUP: ${child.name}, has ${child.children.length} direct children`);
        }
    });
    
    console.log(`[FloorScan] Total meshes in room: ${totalMeshCount}, floor groups: ${floorGroupCount}, floor meshes found: ${floorMeshes.length}`);
    
    if (floorMeshes.length === 0) {
        console.warn("[FloorScan] No floor meshes found in room - this will cause objects to spawn in void!");
        return validPositions;
    }
    
    // Get bounds of all floor meshes combined
    const combinedBox = new THREE.Box3();
    floorMeshes.forEach(mesh => {
        mesh.updateMatrixWorld();
        const box = new THREE.Box3().setFromObject(mesh);
        combinedBox.union(box);
    });
    
    // Safety check for valid bounds
    if (combinedBox.isEmpty() || !isFinite(combinedBox.min.x) || !isFinite(combinedBox.max.x)) {
        console.warn("[FloorScan] Invalid bounds calculated for floor meshes");
        return validPositions;
    }
    
    // Limit scan area to prevent excessive computation
    const maxScanPoints = 500; // Increased for much better coverage
    let scanCount = 0;
    
    // Use larger grid spacing if area is too large
    const width = combinedBox.max.x - combinedBox.min.x;
    const depth = combinedBox.max.z - combinedBox.min.z;
    const area = width * depth;
    
    // Adjust grid spacing based on area - use finer grid for object placement
    if (area > 400) { // Large rooms
        gridSpacing = Math.max(gridSpacing, VOXEL_SIZE * 20); // About 1 unit spacing
    } else if (area > 200) { // Medium rooms
        gridSpacing = Math.max(gridSpacing, VOXEL_SIZE * 15); // About 0.75 unit spacing
    } else {
        gridSpacing = Math.max(gridSpacing, VOXEL_SIZE * 10); // About 0.5 unit spacing for small rooms
    }
    
    // Scan grid within bounds - reduced margin to capture more positions
    const scanMargin = gridSpacing / 2; // Smaller margin
    for (let x = combinedBox.min.x + scanMargin; x < combinedBox.max.x - scanMargin && scanCount < maxScanPoints; x += gridSpacing) {
        for (let z = combinedBox.min.z + scanMargin; z < combinedBox.max.z - scanMargin && scanCount < maxScanPoints; z += gridSpacing) {
            // Cast ray downward from above
            const origin = new THREE.Vector3(x, combinedBox.max.y + 5, z);
            raycaster.set(origin, new THREE.Vector3(0, -1, 0));
            
            const intersects = raycaster.intersectObjects(floorMeshes, false);
            if (intersects.length > 0) {
                // Check if this position is within the valid room shape
                if (roomShape && !_isPositionInRoomShape(x, z, roomShape, false)) {
                    // Skip positions outside the room shape (e.g., middle of U-shape)
                    continue;
                }
                
                // Valid floor position found
                const floorY = intersects[0].point.y;
                validPositions.push({
                    position: new THREE.Vector3(x, floorY, z),
                    normal: intersects[0].face.normal
                });
                scanCount++;
            }
        }
    }
    
    console.log(`[FloorScan] Found ${validPositions.length} valid floor positions (max: ${maxScanPoints})`);
    return validPositions;
}

function _placeInteriorObjects(roomGroup, areaData, roomData, collisionMeshes, lights, wallSegments, floorSegments, doorPositions, isDarkRoom) {
    // --- NEW: Prevent spawning in room 0 ---
    if (roomData.id === 0) {
        console.log(`[PlaceObjects][${roomData.id}] Skipping interior object placement for room 0.`);
        return; // Do not place any objects in the starting room
    }
    // --- END Prevent spawning in room 0 ---

    // --- NEW LOGGING: Inspect incoming areaData ---
    console.log(`[PlaceObjects][${roomData.id}] Received areaData.interiorObjects:`, JSON.stringify(areaData.interiorObjects, null, 2));
    // --- END LOGGING ---

    const random = mulberry32(roomData.id * 97 + 53);
    const objectsToPlace = [];
    const placedObjectData = []; // --- NEW: Track placed objects ---

    // --- NEW: Check if this is a boss room with torch removal requested ---
    const isBossRoom = roomData.type === 'Boss';
    const removeTorches = isBossRoom && roomData.bossRoomData?.lighting?.removeTorches;
    
    if (removeTorches) {
        console.log(`[PlaceObjects][${roomData.id}] Boss room with removeTorches=true - skipping all torch placement`);
    }

    // --- NEW: Door Torch Logic ---
    const placeDoorTorches = !removeTorches && random() < 0.30; // Skip if boss room requests no torches
    const torchPrefabFunc = getPrefabFunction('torch', 'interior'); // Get the torch function once

    if (placeDoorTorches && torchPrefabFunc) {
        console.log(`[PlaceObjects][${roomData.id}] Placing torches next to doors (30% chance triggered).`);
        Object.entries(doorPositions).forEach(([dir, doorPos]) => {
            if (!doorPos) return; // Skip if no door in this direction

            // Determine wall rotation and tangent based on door direction
            let wallRotation = 0;
            const tangent = new THREE.Vector3();
            const inwardOffsetMult = 0.03; // Small inward offset
            const inwardNormal = new THREE.Vector3(); // Normal pointing into the room
            const outwardNormal = new THREE.Vector3(); // Normal pointing out of the wall

            switch(dir) {
                case 'n':
                    wallRotation = 0;
                    tangent.set(1, 0, 0); // Tangent is along X-axis
                    inwardNormal.set(0, 0, 1); // Inward is +Z
                    outwardNormal.set(0, 0, -1); // Outward is -Z
                    break;
                case 'e':
                    wallRotation = Math.PI / 2;
                    tangent.set(0, 0, -1); // Tangent is along -Z-axis
                    inwardNormal.set(-1, 0, 0); // Inward is -X
                    outwardNormal.set(1, 0, 0); // Outward is +X
                    break;
                case 's':
                    wallRotation = Math.PI;
                    tangent.set(-1, 0, 0); // Tangent is along -X-axis
                    inwardNormal.set(0, 0, -1); // Inward is -Z
                    outwardNormal.set(0, 0, 1); // Outward is +Z
                    break;
                case 'w':
                    wallRotation = -Math.PI / 2;
                    tangent.set(0, 0, 1); // Tangent is along Z-axis
                    inwardNormal.set(1, 0, 0); // Inward is +X
                    outwardNormal.set(-1, 0, 0); // Outward is -X
                    break;
            }

            const sideOffset = DOOR_WIDTH / 2 + VOXEL_SIZE * 2.5; // Offset from door center along the wall

            // Calculate positions for Left and Right torches
            const leftPos = doorPos.clone().addScaledVector(tangent, -sideOffset);
            const rightPos = doorPos.clone().addScaledVector(tangent, sideOffset);

            [leftPos, rightPos].forEach((torchBasePos, idx) => {
                const torchResult = torchPrefabFunc({ seed: random() * 1000 });
                // Handle both return types: object with group property or direct group
                const torchGroup = torchResult && torchResult.group ? torchResult.group : torchResult;

                if (torchGroup instanceof THREE.Group) {
                    // Apply outward normal offset first to place it on the wall surface
                    torchGroup.position.copy(torchBasePos).addScaledVector(outwardNormal, WALL_DEPTH / 2);
                    // Then apply small inward offset for visibility
                    torchGroup.position.addScaledVector(inwardNormal, inwardOffsetMult);

                    // Set rotation to face into the room (based on inward normal)
                    torchGroup.lookAt(torchGroup.position.clone().add(inwardNormal));

                    // Adjust height
                    torchGroup.position.y = WALL_HEIGHT * 0.6;
                    roomGroup.add(torchGroup);

                    // Add light source - Fixed to properly use the flame position
                    if (torchResult.flameLocalPosition) {
                        const light = _createTorchLight();
                        // Convert flame's local position to world position
                        const flameWorldPos = torchGroup.localToWorld(torchResult.flameLocalPosition.clone());
                        light.position.copy(flameWorldPos);
                        lights.push(light);
                        console.log(`[PlaceObjects][${roomData.id}] Added light source for door torch (${idx === 0 ? 'Left' : 'Right'}) next to ${dir.toUpperCase()} door.`);
                    }
                    console.log(`[PlaceObjects][${roomData.id}] Placed door torch (${idx === 0 ? 'Left' : 'Right'}) next to ${dir.toUpperCase()} door.`);
                }
            });
        });
    } else if (placeDoorTorches && !torchPrefabFunc) {
        console.warn(`[PlaceObjects][${roomData.id}] Wanted to place door torches, but torch prefab function was not found.`);
    }
    // --- END Door Torch Logic ---

    // If no interior objects are defined in areaData, exit early (after potentially placing door torches)
    if (!areaData.interiorObjects || areaData.interiorObjects.length === 0) {
        return;
    }

    // 1. Determine which objects to spawn and how many (from areaData)
    areaData.interiorObjects.forEach(objDef => {
        // Skip torch placement if boss room requests torch removal
        if (objDef.type === 'torch' && removeTorches) {
            console.log(`[PlaceObjects][${roomData.id}] Skipping torch objects for boss room with removeTorches=true`);
            return; // Skip this object definition
        }
        
        if (random() < objDef.probability) {

            let minQty = objDef.minQuantity;
            let maxQty = objDef.maxQuantity;

            // --- Dynamic Quantity Scaling for Torches ---
            if (objDef.type === 'torch') {
                const roomArea = (roomData.bounds && roomData.bounds.width && roomData.bounds.depth)
                                 ? roomData.bounds.width * roomData.bounds.depth
                                 : 196; // Default to small area if bounds are missing

                // Store original quantities from configuration
                const configMinQty = minQty;
                const configMaxQty = maxQty;

                // Define thresholds and scaled quantities
                const SMALL_AREA_THRESHOLD = 14 * 14; // 196
                const MEDIUM_AREA_THRESHOLD = 28 * 14; // 392

                // Scale quantities based on room size while respecting minimums
                // Reduced quantities for more realistic torch placement
                if (roomArea <= SMALL_AREA_THRESHOLD) {
                    minQty = 1; // Reduced to absolute minimum
                    maxQty = 2; // Maximum 2 for small rooms
                } else if (roomArea <= MEDIUM_AREA_THRESHOLD) {
                    minQty = 2; // Two torches minimum for medium rooms
                    maxQty = 3; // Maximum 3 for medium rooms
                } else { // Large rooms
                    minQty = 2; // Two torches minimum for large rooms
                    maxQty = 4; // Maximum 4 for large rooms
                }

                // Apply dark room logic: ensure at least 1 torch if NOT dark
                if (!isDarkRoom) {
                    minQty = Math.max(1, minQty);
                }
                console.log(`[PlaceObjects][${roomData.id}] Scaled TORCH quantity for area ${roomArea.toFixed(0)} (Dark: ${isDarkRoom}): min=${minQty}, max=${maxQty}`);
            }
            // --- End Dynamic Quantity Scaling ---

            // Use the potentially scaled quantities
            const quantity = Math.floor(random() * (maxQty - minQty + 1)) + minQty;

            // Logging for calculated quantity (keep existing)
            if (objDef.type === 'torch') {
                console.log(`[PlaceObjects][${roomData.id}] Calculated final quantity for TORCH: ${quantity} (using scaled min/max)`);
            }

            for (let i = 0; i < quantity; i++) {
                objectsToPlace.push({
                    type: objDef.type,
                    prefabFunc: getPrefabFunction(objDef.type, 'interior'),
                    placement: objDef.placement || 'floor',
                    seed: random() * 10000 + i,
                    isDestructible: objDef.isDestructible || false,
                    destructionEffect: objDef.destructionEffect || null,
                    placementDetail: objDef.placementDetail || ['random'],
                });
            }
        }
    });

    // Filter out any torches added by the area definition if door torches were already placed
    // (to avoid double counting or exceeding limits)
    const finalObjectsToPlace = objectsToPlace;

    if (finalObjectsToPlace.length === 0) return; // Exit if no objects left after filtering

    console.log(`[PlaceObjects] Attempting to place ${finalObjectsToPlace.length} REGULAR objects for room ${roomData.id}`);
    console.log(`[PlaceObjects][${roomData.id}] Regular objects to be placed:`, finalObjectsToPlace.map(o => o.type));

    // 2. Get potential placement locations
    // Create a separate array for each wall direction
    const wallPointsByDirection = {
        n: [], e: [], s: [], w: []
    };
    console.log(`[PlaceObjects][${roomData.id}] Processing ${wallSegments.length} wall segments for points...`);
    wallSegments.forEach((wallSeg, segIndex) => {
        // Check if this segment corresponds to a door location
        const isDoorWall = Object.entries(doorPositions).some(([dir, pos]) =>
            pos &&
            Math.abs(pos.x - wallSeg.position.x) < wallSeg.length / 2 + DOOR_WIDTH / 2 + 0.1 && // Check overlap with door width
            Math.abs(pos.z - wallSeg.position.z) < WALL_DEPTH / 2 + 0.1 &&
            wallSeg.direction === dir
        );

        // Only skip if it's a door wall - remove isVisible check to allow more placement points
        if (!isDoorWall && wallSeg.isOuterBoundary) {
            // FIXED: Don't skip invisible walls for collision - they still need collision detection
            // Only skip for object placement, not for collision generation
            if (wallSeg.isVisible === false) {
                console.log(`[Wall ${wallSeg.direction}] Wall is invisible but will still generate collision.`);
                // Continue processing for collision, but skip for object placement points
            }

            // Default point spacing for most objects
            const pointSpacing = VOXEL_SIZE * 3;
            const numPoints = Math.max(2, Math.floor(wallSeg.length / pointSpacing));
            const wallTangent = new THREE.Vector3(Math.cos(wallSeg.rotation), 0, -Math.sin(wallSeg.rotation));
            const wallStart = wallSeg.position.clone().addScaledVector(wallTangent, -wallSeg.length / 2);

            // Calculate wall normal based on direction instead of rotation
            let wallNormal;
            switch(wallSeg.direction) {
                case 'n': wallNormal = new THREE.Vector3(0, 0, -1); break; // North wall - normal points north (-Z)
                case 's': wallNormal = new THREE.Vector3(0, 0, 1); break;  // South wall - normal points south (+Z)
                case 'e': wallNormal = new THREE.Vector3(1, 0, 0); break;  // East wall - normal points east (+X)
                case 'w': wallNormal = new THREE.Vector3(-1, 0, 0); break; // West wall - normal points west (-X)
                default:
                    // Fallback to rotation-based calculation if no direction
                    wallNormal = new THREE.Vector3(Math.sin(wallSeg.rotation), 0, Math.cos(wallSeg.rotation));
            }
            const inwardNormal = wallNormal.clone().negate(); // Normal pointing into the room

            console.log(`[Wall ${wallSeg.direction}] Adding ${numPoints} points at ${wallSeg.position.x.toFixed(1)},${wallSeg.position.z.toFixed(1)}`);

            for(let i = 0; i < numPoints; i++) {
                const t = (i + 0.5) / numPoints;
                const centerLinePoint = wallStart.clone().addScaledVector(wallTangent, wallSeg.length * t);
                const placementPoint = centerLinePoint.clone().addScaledVector(wallNormal, -WALL_DEPTH / 2); // Move to inner surface

                // ADDITIONAL SAFETY: Validate wall point is within room shape (with Z-flip for floor geometry compatibility)
                if (!_isPositionInRoomShape(placementPoint.x, placementPoint.z, shapeKey, true)) {
                    continue; // Skip wall points outside valid room shape
                }

                const pointData = {
                    position: placementPoint,
                    rotationY: wallSeg.rotation,
                    wallNormal: wallNormal, // Outward normal
                    inwardNormal: inwardNormal, // Inward normal
                    direction: wallSeg.direction
                };

                if (wallSeg.direction && wallPointsByDirection[wallSeg.direction]) {
                    wallPointsByDirection[wallSeg.direction].push(pointData);
                }
            }
        } else {
            if (isDoorWall) console.log(`[Wall ${wallSeg.direction}] Skipping points because it contains a door.`);
        }
    });

    // Calculate total points available
    let totalWallPoints = 0;
    Object.values(wallPointsByDirection).forEach(arr => totalWallPoints += arr.length);
    console.log(`[PlaceObjects] REGULAR Wall Points - N:${wallPointsByDirection.n.length}, E:${wallPointsByDirection.e.length}, S:${wallPointsByDirection.s.length}, W:${wallPointsByDirection.w.length} (Total: ${totalWallPoints})`);
    const allWallPoints = [].concat(...Object.values(wallPointsByDirection)); // Combine wall points *after* counting

    // --- NEW: Shape-Aware Floor Points (Using actual floor mesh scanning) ---
    // First, try to scan for actual floor positions from the floor meshes
    const { shapeKey } = roomData;
    const scannedFloorPositions = _scanForValidFloorPositions(roomGroup, VOXEL_SIZE * 3, shapeKey);
    
    const allFloorPoints = [];
    
    if (scannedFloorPositions.length > 0) {
        // Use scanned positions if available (most accurate)
        console.log(`[PlaceObjects][${roomData.id}] Using ${scannedFloorPositions.length} scanned floor positions.`);
        scannedFloorPositions.forEach((scanData, index) => {
            allFloorPoints.push({
                position: scanData.position,
                segmentIndex: 0, // Not segment-specific when scanned
                scanned: true
            });
        });
    } else {
        // Fallback to segment-based generation if scanning fails
        console.log(`[PlaceObjects][${roomData.id}] Floor scan failed, falling back to segment-based generation.`);
        const gridSpacing = VOXEL_SIZE * 3; // Spacing between potential points
        const borderMargin = VOXEL_SIZE * 2; // Reduced margin for more placement options

        // Generate grid points directly within each floor segment
        floorSegments.forEach((segment, segmentIndex) => {
            const segmentMinX = segment.position.x - segment.width / 2 + borderMargin;
            const segmentMaxX = segment.position.x + segment.width / 2 - borderMargin;
            const segmentMinZ = segment.position.z - segment.depth / 2 + borderMargin;
            const segmentMaxZ = segment.position.z + segment.depth / 2 - borderMargin;

            // Generate grid points within this specific floor segment
            for (let x = segmentMinX; x < segmentMaxX; x += gridSpacing) {
                for (let z = segmentMinZ; z < segmentMaxZ; z += gridSpacing) {
                    const pointPos = new THREE.Vector3(x, 0, z);

                    // Additional validation: Ensure point is still within segment bounds (safety check)
                    if (x >= segmentMinX && x <= segmentMaxX && z >= segmentMinZ && z <= segmentMaxZ) {
                        allFloorPoints.push({ 
                            position: pointPos,
                            segmentIndex: segmentIndex, // Track which segment this point belongs to
                            segment: segment
                        });
                    }
                }
            }
        });
    }
    
    console.log(`[PlaceObjects][${roomData.id}] Total floor placement points: ${allFloorPoints.length}`);
    // --- END Floor Points Calculation ---

    // --- DEBUG LOGGING: Point Counts ---
    console.log(`[PlaceObjects][${roomData.id}] Calculated placement points: WALL=${allWallPoints.length}, FLOOR=${allFloorPoints.length}`);
    // --- END DEBUG ---

    // Shuffle the lists initially (Make sure shuffleArray is defined/accessible)
    function shuffleArray(array, rng) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(rng() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
    shuffleArray(allWallPoints, random); // Use 'random' which is the seeded RNG
    shuffleArray(allFloorPoints, random); // Use 'random' which is the seeded RNG

    // --- DEBUG LOGGING: Object Counts ---
    const countsByType = {};
    finalObjectsToPlace.forEach(obj => {
        countsByType[obj.type] = (countsByType[obj.type] || 0) + 1;
    });
    console.log(`[PlaceObjects][${roomData.id}] Objects to attempt placement:`, JSON.stringify(countsByType));
    // --- END DEBUG ---

    // 3. Place the collected objects
    console.log(`[PlaceObjects][${roomData.id}] Placing ${finalObjectsToPlace.length} total interior objects.`);

    // --- Separate Pillars from other objects ---
    const pillarObjects = finalObjectsToPlace.filter(o => o.type === 'stone_pillar');
    const otherObjects = finalObjectsToPlace.filter(o => o.type !== 'stone_pillar');
    // -----------------------------------------

    let placedCount = 0; // Track successfully placed objects

    // --- NEW: Place Pillars First (Shape-Aware Corner Detection) ---
    if (pillarObjects.length > 0) {
        console.log(`[PlaceObjects][${roomData.id}] Attempting to place ${pillarObjects.length} Pillars in corners using shape-aware placement.`);
        const objectRadius = getObjectRadius('stone_pillar');

        // Use floor segments to identify corner regions instead of a global bounding box
        const cornerCandidates = [];
        
        // For complex shapes, we need to be smarter about corner detection
        // Instead of using segment bounds, find actual corners from floor placement points
        if (allFloorPoints.length > 0) {
            // Find the actual bounds of placed floor points
            let minX = Infinity, maxX = -Infinity;
            let minZ = Infinity, maxZ = -Infinity;
            
            allFloorPoints.forEach(point => {
                minX = Math.min(minX, point.position.x);
                maxX = Math.max(maxX, point.position.x);
                minZ = Math.min(minZ, point.position.z);
                maxZ = Math.max(maxZ, point.position.z);
            });
            
            // Define potential corner regions based on actual floor bounds
            const cornerRegions = [
                { x: minX + VOXEL_SIZE * 2, z: minZ + VOXEL_SIZE * 2, name: 'SW' },
                { x: maxX - VOXEL_SIZE * 2, z: minZ + VOXEL_SIZE * 2, name: 'SE' },
                { x: minX + VOXEL_SIZE * 2, z: maxZ - VOXEL_SIZE * 2, name: 'NW' },
                { x: maxX - VOXEL_SIZE * 2, z: maxZ - VOXEL_SIZE * 2, name: 'NE' }
            ];
            
            // Add corners that are actually within the room shape
            cornerRegions.forEach(region => {
                if (_isPositionInRoomShape(region.x, region.z, shapeKey, true)) {
                    cornerCandidates.push(region);
                }
            });
            
            // For L-shapes and other complex shapes, find internal corners
            if (shapeKey === 'L_SHAPE') {
                // L-shape has an internal corner where the two segments meet
                // This is roughly at the center of the shape
                const internalCorner = { x: 0, z: 0, name: 'Internal' };
                if (_isPositionInRoomShape(internalCorner.x, internalCorner.z, shapeKey, true)) {
                    cornerCandidates.push(internalCorner);
                }
            }
        } else {
            // Fallback to segment-based corners if no floor points available
            floorSegments.forEach((segment, segmentIndex) => {
                const segmentCorners = [
                    { x: segment.position.x - segment.width / 2 + 1, z: segment.position.z - segment.depth / 2 + 1, name: `Seg${segmentIndex}_SW`, segment },
                    { x: segment.position.x + segment.width / 2 - 1, z: segment.position.z - segment.depth / 2 + 1, name: `Seg${segmentIndex}_SE`, segment },
                    { x: segment.position.x - segment.width / 2 + 1, z: segment.position.z + segment.depth / 2 - 1, name: `Seg${segmentIndex}_NW`, segment },
                    { x: segment.position.x + segment.width / 2 - 1, z: segment.position.z + segment.depth / 2 - 1, name: `Seg${segmentIndex}_NE`, segment },
                ];
                cornerCandidates.push(...segmentCorners);
            });
        }

        // Filter to ensure corners are within valid room shape AND have actual floor
        const corners = cornerCandidates.filter(corner => {
            const isValidCorner = _isPositionInRoomShape(corner.x, corner.z, shapeKey, true);
            if (!isValidCorner) {
                console.log(`[PlaceObjects][${roomData.id}] Filtering out corner ${corner.name} - outside valid room shape for ${shapeKey}`);
                return false;
            }
            
            // CRITICAL: Also validate that this corner position actually has floor beneath it
            // This prevents pillars spawning at segment corners that don't have actual floor
            const hasFloorAtCorner = allFloorPoints.some(point => {
                const dist = Math.sqrt(
                    Math.pow(point.position.x - corner.x, 2) + 
                    Math.pow(point.position.z - corner.z, 2)
                );
                return dist < VOXEL_SIZE * 3; // Within 3 voxels of a valid floor point
            });
            
            if (!hasFloorAtCorner) {
                console.log(`[PlaceObjects][${roomData.id}] Filtering out corner ${corner.name} - no floor at this corner position`);
                return false;
            }
            
            return true;
        });

        let pillarsPlaced = 0;
        const availableFloorPointsForPillars = [...allFloorPoints]; // Use a copy
        shuffleArray(availableFloorPointsForPillars, random);

        for (const corner of corners) {
            if (pillarsPlaced >= pillarObjects.length) break; // Stop if we've placed the requested number

            let chosenLocation = null;
            let placedInThisCorner = false;
            let attempts = 0;
            const targetCornerPos = new THREE.Vector3(corner.x, 0, corner.z);

            // --- Find nearest available point to the target corner position ---
            let bestPoint = null;
            let minDistSq = Infinity;

            // Search through ALL available floor points for the closest valid one
            for (const pointData of availableFloorPointsForPillars) {
                const candidatePosition = pointData.position;
                const distSq = candidatePosition.distanceToSquared(targetCornerPos);

                // Check validity (collision, doors) and if it's closer than the current best
                const farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
                const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);
                
                // NEW: Add raycasting validation for pillar placement
                let hasValidFloorSurface = true;
                const raycaster = new THREE.Raycaster();
                raycaster.set(
                    new THREE.Vector3(candidatePosition.x, candidatePosition.y + 5, candidatePosition.z),
                    new THREE.Vector3(0, -1, 0)
                );
                
                const floorMeshes = floorSegments.filter(segment => segment.userData?.isFloor === true);
                if (floorMeshes.length > 0) {
                    const intersects = raycaster.intersectObjects(floorMeshes, false);
                    hasValidFloorSurface = intersects.length > 0;
                    
                    if (hasValidFloorSurface) {
                        // Update the Y position to match the floor surface
                        candidatePosition.y = intersects[0].point.y;
                    }
                } else {
                    hasValidFloorSurface = false;
                }

                if (farEnoughFromDoors && noCollision && hasValidFloorSurface && distSq < minDistSq) {
                    minDistSq = distSq;
                    bestPoint = pointData;
                }
            }
            // --- End Search ---

            // If we found a suitable point near the corner
            if (bestPoint) {
                 chosenLocation = bestPoint;
                 placedInThisCorner = true; // We placed the pillar associated with this corner
            } else {
                 console.log(`[PlaceObjects][${roomData.id}] No valid point found near corner ${corner.name}.`);
                 continue; // Skip to the next corner if no valid point found nearby
            }

            // --- Try finding a point *within* this corner region first (REMOVED - Using nearest point instead)
            /*
            const isCorner = (point) => { ... };
            const cornerPoints = availableFloorPointsForPillars.filter(isCorner);
            shuffleArray(cornerPoints, random);

            for (const candidateLocation of cornerPoints) {
                 attempts++;
                 const candidatePosition = candidateLocation.position;
                 const farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
                 const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);

                 if (farEnoughFromDoors && noCollision) {
                     chosenLocation = candidateLocation;
                     placedInThisCorner = true;
                     break;
                 }
                 if (attempts >= MAX_PLACEMENT_ATTEMPTS / 2) break; // Limit attempts within corner region
            }
            */

            // If a valid spot was found (now using nearest point logic)
            if (chosenLocation && placedInThisCorner) {
                const objToPlace = pillarObjects[pillarsPlaced]; // Get the next pillar object data

                // --- Remove chosen location from ORIGINAL list ---
                const originalList = allFloorPoints; // Modify the main list
                const originalIndex = originalList.findIndex(p => p === chosenLocation);
                if (originalIndex !== -1) { originalList.splice(originalIndex, 1); }
                else {
                    const fallbackIndex = originalList.findIndex(p => p.position.equals(chosenLocation.position));
                    if (fallbackIndex !== -1) { originalList.splice(fallbackIndex, 1); }
                    else { console.warn(`[PillarPlace] Chosen corner location couldn't be removed.`); }
                }
                // Also remove from the temporary list used for this corner search
                 const tempIndex = availableFloorPointsForPillars.findIndex(p => p === chosenLocation);
                 if (tempIndex !== -1) availableFloorPointsForPillars.splice(tempIndex, 1);
                // ---------------------------------------------

                // --- Instantiate and Place Pillar ---
                let prefabOptions = { seed: objToPlace.seed, wallHeight: WALL_HEIGHT };
                const objectResult = objToPlace.prefabFunc(prefabOptions);
                let objectGroup = objectResult.group || objectResult;

                if (objectGroup && objectGroup instanceof THREE.Object3D) {
                    objectGroup.position.copy(chosenLocation.position);
                    objectGroup.rotation.y = random() * Math.PI * 2;
                    objectGroup.position.y = 0; // Prefab should handle its internal offset
                    roomGroup.add(objectGroup);

                    // UserData and Collision Mesh
                    if (!objectGroup.userData) objectGroup.userData = {};
                    objectGroup.userData.isDestructible = objToPlace.isDestructible;
                    objectGroup.userData.destructionEffect = objToPlace.destructionEffect;
                    objectGroup.userData.objectType = objToPlace.type;
                    collisionMeshes.push(objectGroup); // Add group itself as collider

                    // Track placement
                    placedObjectData.push({ position: objectGroup.position.clone(), radius: objectRadius });
                    placedCount++;
                    pillarsPlaced++;
                    console.log(`[PlaceObjects][${roomData.id}] Successfully placed PILLAR in corner ${corner.name}.`);
                } else {
                     console.error(`[PlaceObjects][${roomData.id}] Pillar prefab function failed.`);
                }
                // ------------------------------------
                chosenLocation = null; // Reset for next corner
            } else {
                console.log(`[PlaceObjects][${roomData.id}] Could not place pillar in corner ${corner.name}. No valid spot found.`);
            }
        }
        console.log(`[PlaceObjects][${roomData.id}] Finished placing ${pillarsPlaced} / ${pillarObjects.length} pillars.`);
    }
    // --- END Place Pillars ---

    // --- Place Other Objects (using the remaining points) ---
    console.log(`[PlaceObjects][${roomData.id}] Placing ${otherObjects.length} other interior objects.`);
    shuffleArray(otherObjects, random); // Shuffle remaining objects

    for (const objToPlace of otherObjects) { // Loop through NON-PILLAR objects
        const placementType = objToPlace.placement; // 'wall' or 'floor'
        // Use the potentially modified original point lists (pillars removed points)
        const availablePointsList = placementType === 'wall' ? allWallPoints : allFloorPoints;

        // --- Filter locations based on placementDetail (Corners, Random, etc.) ---
        let potentialLocations = availablePointsList; // Start with all available for the type
        const details = objToPlace.placementDetail;
        const objectRadius = getObjectRadius(objToPlace.type);
        if (placementType === 'floor' && details && !(details.length === 1 && details[0] === 'random')) {
            // Shape-aware corner detection using floor segments
            const isCorner = (point) => {
                const p = point.position;
                
                // Check if this point is near a corner of its floor segment
                if (point.segment) {
                    const seg = point.segment;
                    const segMinX = seg.position.x - seg.width / 2;
                    const segMaxX = seg.position.x + seg.width / 2;
                    const segMinZ = seg.position.z - seg.depth / 2;
                    const segMaxZ = seg.position.z + seg.depth / 2;
                    
                    const cornerMargin = Math.min(seg.width, seg.depth) * 0.25;
                    const isNearMinX = p.x < segMinX + cornerMargin;
                    const isNearMaxX = p.x > segMaxX - cornerMargin;
                    const isNearMinZ = p.z < segMinZ + cornerMargin;
                    const isNearMaxZ = p.z > segMaxZ - cornerMargin;
                    
                    const inCornerArea = (isNearMinX && isNearMinZ) || (isNearMinX && isNearMaxZ) ||
                                       (isNearMaxX && isNearMinZ) || (isNearMaxX && isNearMaxZ);

                    return inCornerArea && _isPositionInRoomShape(p.x, p.z, shapeKey, true);
                }
                return false;
            };

            let allowedPoints = [];
            if (details.includes('random')) allowedPoints = [...availablePointsList];
            if (details.includes('corners')) allowedPoints = allowedPoints.concat(availablePointsList.filter(isCorner));
            if (details.includes('triangle_center')) {
                // Special case for triangle_center - create 3 specific points
                const triangleRadius = Math.min(roomWidth, roomDepth) * 0.2; // 20% of room size
                const angleOffset = (Math.PI * 2) / 3; // 120 degrees between each monolith
                const startAngle = -Math.PI / 2; // Start with one monolith at the top
                
                const trianglePoints = [];
                for (let i = 0; i < 3; i++) {
                    const angle = startAngle + (i * angleOffset);
                    const x = Math.cos(angle) * triangleRadius;
                    const z = Math.sin(angle) * triangleRadius;
                    
                    // Find the closest floor point to our calculated position
                    let closestPoint = null;
                    let minDist = Infinity;
                    
                    for (const point of availablePointsList) {
                        const dist = Math.sqrt(
                            Math.pow(point.position.x - x, 2) + 
                            Math.pow(point.position.z - z, 2)
                        );
                        if (dist < minDist) {
                            minDist = dist;
                            closestPoint = point;
                        }
                    }
                    
                    if (closestPoint) {
                        trianglePoints.push(closestPoint);
                    }
                }
                allowedPoints = trianglePoints;
            }
            potentialLocations = [...new Set(allowedPoints)];
            console.log(`[PlaceObjects][${roomData.id}] Filtered points for '${objToPlace.type}' [${details.join(', ')}]: ${potentialLocations.length} points.`);
        } else {
            console.log(`[PlaceObjects][${roomData.id}] Using all ${availablePointsList.length} ${placementType} points for '${objToPlace.type}' (random or no detail).`);
        }

        if (potentialLocations.length === 0) {
            console.warn(`[PlaceObjects][${roomData.id}] No potential placement points left for object type '${objToPlace.type}' after detail filtering. Skipping.`);
            continue;
        }

        let chosenLocation = null;
        let attempts = 0;
        shuffleArray(potentialLocations, random);

        for (const candidateLocation of potentialLocations) {
            attempts++;
            const candidatePosition = candidateLocation.position;
            let farEnoughFromDoors = true;
            // Check door proximity for both floor AND wall objects
            farEnoughFromDoors = !isTooCloseToDoors(candidatePosition, doorPositions, MIN_DISTANCE_FROM_DOOR + objectRadius);
            const noCollision = !collidesWithPlacedObjects(candidatePosition, objectRadius, placedObjectData);

            // NEW: Add raycasting validation to confirm floor surface exists
            let hasValidFloorSurface = true;
            if (placementType === 'floor') {
                // Create raycaster from above the candidate position
                const raycaster = new THREE.Raycaster();
                raycaster.set(
                    new THREE.Vector3(candidatePosition.x, candidatePosition.y + 5, candidatePosition.z),
                    new THREE.Vector3(0, -1, 0)
                );
                
                // Get floor meshes for raycasting
                const floorMeshes = floorSegments.filter(segment => segment.userData?.isFloor === true);
                
                if (floorMeshes.length > 0) {
                    const intersects = raycaster.intersectObjects(floorMeshes, false);
                    hasValidFloorSurface = intersects.length > 0;
                    
                    if (hasValidFloorSurface) {
                        // Update the Y position to match the floor surface
                        candidatePosition.y = intersects[0].point.y;
                    }
                } else {
                    console.warn(`[PlaceObjects][${roomData.id}] No floor meshes available for raycasting validation`);
                }
            }

            if (farEnoughFromDoors && noCollision && hasValidFloorSurface) {
                chosenLocation = candidateLocation;
                // console.log(`[PlaceObjects][${roomData.id}] Found valid spot for '${objToPlace.type}' after ${attempts} checks.`);
                break;
            }
            if (attempts >= MAX_PLACEMENT_ATTEMPTS) {
                // console.log(`[PlaceObjects][${roomData.id}] Reached max placement attempts (${MAX_PLACEMENT_ATTEMPTS}) for '${objToPlace.type}'.`);
                break;
            }
        }

        if (!chosenLocation) {
            console.warn(`[PlaceObjects][${roomData.id}] Could not find a valid placement location for object type '${objToPlace.type}'. Skipping instance.`);
            continue;
        }

        const originalList = placementType === 'wall' ? allWallPoints : allFloorPoints;
        const originalIndex = originalList.findIndex(p => p === chosenLocation);
        if (originalIndex !== -1) {
            originalList.splice(originalIndex, 1);
        } else {
            const fallbackIndex = originalList.findIndex(p => p.position.equals(chosenLocation.position));
            if (fallbackIndex !== -1) {
                originalList.splice(fallbackIndex, 1);
            } else {
                console.warn(`[PlaceObjects][${roomData.id}] OTHER Object: Chosen location couldn't be removed.`);
                 const potentialIndex = potentialLocations.findIndex(p => p === chosenLocation);
                 if(potentialIndex !== -1) potentialLocations.splice(potentialIndex, 1);
            }
        }

        let prefabOptions = { seed: objToPlace.seed };
        // No special options needed for most other objects currently
        const objectResult = objToPlace.prefabFunc(prefabOptions);

        // Handle both return types: object with group property or direct group
        let objectGroup = objectResult && objectResult.group ? objectResult.group : objectResult;

        if (!objectGroup || !(objectGroup instanceof THREE.Object3D)) {
            console.error(`[PlaceObjects][${roomData.id}] Prefab function failed for '${objToPlace.type}'.`);
            continue;
        }

        if (!objectGroup.userData) objectGroup.userData = {};
        objectGroup.userData.isDestructible = objToPlace.isDestructible;
        objectGroup.userData.destructionEffect = objToPlace.destructionEffect;
        objectGroup.userData.objectType = objToPlace.type;

        if (placementType === 'wall') {
            if (!(objectGroup instanceof THREE.Group)) {
                // Wrap if needed...
            }
            // ENHANCED Wall placement logic with proper offsets
             objectGroup.position.copy(chosenLocation.position);
             objectGroup.lookAt(objectGroup.position.clone().add(chosenLocation.inwardNormal));

             // CONFIGURABLE: Use object-specific wall offsets to prevent clipping
             let wallOffset = WALL_OFFSETS[objToPlace.type] || WALL_OFFSETS.default;
             
             // Add epsilon to prevent exact overlap and z-fighting
             wallOffset += WALL_OFFSETS.EPSILON;

             objectGroup.position.addScaledVector(chosenLocation.inwardNormal, wallOffset);
             let yPos = WALL_HEIGHT * 0.5;
             if (objToPlace.type === 'vine') yPos = WALL_HEIGHT * 0.85;
             else if (objToPlace.type === 'torch') yPos = WALL_HEIGHT * 0.6;
             objectGroup.position.y = yPos;
        } else { // Floor placement
            objectGroup.position.copy(chosenLocation.position);
            objectGroup.rotation.y = random() * Math.PI * 2;

            // CRITICAL FIX: Validate floor position for object placement
            const objectPosition = new THREE.Vector3(chosenLocation.position.x, 0, chosenLocation.position.z);

            // Check if the DungeonHandler has floor validation available
            if (window.dungeonHandler && typeof window.dungeonHandler._hasValidFloor === 'function') {
                if (!window.dungeonHandler._hasValidFloor(objectPosition, 1.0, objToPlace.type)) {
                    console.warn(`[ObjectPlacement] Object ${objToPlace.type} placed on invalid floor, finding nearest valid position`);

                    // Try to find a nearby valid position
                    if (typeof window.dungeonHandler._findNearestValidFloorPosition === 'function') {
                        const nearestValidPos = window.dungeonHandler._findNearestValidFloorPosition(objectPosition, 3.0);
                        if (nearestValidPos) {
                            objectGroup.position.x = nearestValidPos.x;
                            objectGroup.position.z = nearestValidPos.z;
                            console.log(`[ObjectPlacement] Moved ${objToPlace.type} to valid floor position: (${nearestValidPos.x.toFixed(2)}, ${nearestValidPos.z.toFixed(2)})`);
                        } else {
                            console.warn(`[ObjectPlacement] No valid floor position found for ${objToPlace.type}, using original position`);
                        }
                    }
                }
            }

            // CRITICAL FIX: Calculate proper floor height accounting for curvature
            const curvedFloorY = _calculateFloorCurvatureHeight(objectGroup.position.x, objectGroup.position.z, 0, roomData);
            objectGroup.position.y = curvedFloorY;
        }

        roomGroup.add(objectGroup);

        placedObjectData.push({
            position: objectGroup.position.clone(),
            radius: objectRadius
        });
        placedCount++;

        if (objToPlace.isDestructible && objectGroup) {
            // UNIVERSAL FLOOR OBJECT SYSTEM: Apply consistent collision setup for ALL destructible floor objects
            console.log(`[PlaceObjects] Setting up collision for destructible object '${objToPlace.type}'`);

            // Generate unique identifier for this specific object instance
            const objectUniqueId = `floor_object_${roomData.id}_${objToPlace.type}_${placedCount}_${Date.now()}`;
            objectGroup.userData = objectGroup.userData || {};
            objectGroup.userData.uniqueId = objectUniqueId;

            // FIXED: Add the parent group to collision meshes (consistent with pillar system)
            if (objectGroup.isMesh) {
                collisionMeshes.push(objectGroup);
            } else if (objectGroup.isGroup) {
                collisionMeshes.push(objectGroup);

                // FIXED: Set up child meshes with parent references but DON'T add to collision array
                objectGroup.traverse(child => {
                    if (child.isMesh && child !== objectGroup) {
                        // Child meshes inherit from parent but are not directly destructible
                        child.userData = child.userData || {};
                        child.userData.isDestructible = false; // Child meshes are NOT directly destructible
                        child.userData.health = objectGroup.userData.health;
                        child.userData.objectType = objectGroup.userData.objectType;
                        child.userData.parentDestructible = objectGroup; // Reference to parent for destruction
                        child.userData.parentUniqueId = objectUniqueId; // CRITICAL: Add parent unique ID
                        child.name = `${objectUniqueId}_child_${child.id}`;
                        // DO NOT add child to collisionMeshes - only parent should be targetable
                        console.log(`[PlaceObjects] Set up child mesh for ${objToPlace.type}: ${child.name} (parent-only collision)`);
                    }
                });
            }
        }

        if (objToPlace.type === 'torch') {
            // Handle torch light creation
            if (objectResult && objectResult.flameLocalPosition) {
                const light = _createTorchLight();
                // Create a temporary vector for the world position calculation
                const worldPos = new THREE.Vector3();
                // Get the world position of the flame
                worldPos.copy(objectResult.flameLocalPosition).applyMatrix4(objectGroup.matrixWorld);
                light.position.copy(worldPos);
                lights.push(light);
            }
        }
        // console.log(`[PlaceObjects][${roomData.id}] Successfully placed '${objToPlace.type}'...`);
    }
    // --- END Place Other Objects ---

    console.log(`[PlaceObjects][${roomData.id}] Finished placing objects. Total successfully placed: ${placedCount} out of ${finalObjectsToPlace.length} initial instances.`);
}

/**
 * Generates the visual elements (walls, floors, doors, interior objects) for a room.
 * Uses the provided area data.
 * Returns an object containing the main group, collision meshes, light objects, bounding box, and door positions.
 */
export async function generateRoomVisuals(roomData, areaData) {
    if (!roomData) {
        console.error("[generateRoomVisuals] No room data provided.");
        return { roomGroup: new THREE.Group(), collisionMeshes: [], lights: [], boundingBox: { minX: 0, maxX: 0, minZ: 0, maxZ: 0 }, doorCenterPoints: {} };
    }
    if (!areaData) {
         console.error(`[generateRoomVisuals] Area data is missing for room ${roomData.id}. Cannot generate visuals.`);
        return { roomGroup: new THREE.Group(), collisionMeshes: [], lights: [], boundingBox: { minX: 0, maxX: 0, minZ: 0, maxZ: 0 }, doorCenterPoints: {} };
    }
    console.log(`[generateRoomVisuals] Generating visuals for room ${roomData.id} using area '${areaData.name}'`);

    roomData.walkableTiles = [];

    const roomGroup = new THREE.Group();
    roomGroup.position.set(0, 0, 0); // Room origin
    const collisionMeshes = [];
    const lights = [];
    const doorCenterPoints = { n: null, s: null, e: null, w: null };
    const doorPlaced = { n: false, s: false, e: false, w: false };
    let validFloorPositions = []; // Declare at function level for enemy spawning

    // --- Determine if room is dark ---
    let isDarkRoom = Math.random() < DARK_ROOM_PROBABILITY;
    if (isDarkRoom && areaData.name !== 'The Catacombs') {
        console.log(`[generateRoomVisuals] Room ${roomData.id} is a dark room.`);
    } else if (isDarkRoom && areaData.name === 'The Catacombs') {
         console.log(`[generateRoomVisuals] Room ${roomData.id} is Catacombs, overriding dark room.`);
         isDarkRoom = false;
    }
    // --- Store the result on roomData ---
    roomData.isDark = isDarkRoom;
    // ----------------------------------

    // Calculate room dimensions and bounding box early
    let roomWidth, roomDepth, halfWidth, halfDepth, boundingBox;
    const shapeKey = roomData.shapeKey || 'SQUARE_1X1';
    
    // Check for custom room size (used by special event rooms)
    const customSize = roomData.customSize || ROOM_WORLD_SIZE;
    
    switch (shapeKey) {
        case 'BOSS_ARENA':
            const B = 2 * ROOM_WORLD_SIZE; // Boss room is 2x larger
            roomWidth = 2 * B;  // 4x standard room width
            roomDepth = 2 * B;  // 4x standard room depth
            break;
        case 'RECTANGULAR': case 'RECT_2X1':
            roomWidth = ROOM_WORLD_SIZE * 2;
            roomDepth = ROOM_WORLD_SIZE;
            break;
        case 'L_SHAPE': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'T_SHAPE': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'CROSS_SHAPE': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'U_SHAPE_DOWN': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'U_SHAPE_UP': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'U_SHAPE_LEFT': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'U_SHAPE_RIGHT': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'CORRIDOR_LONG': roomWidth = ROOM_WORLD_SIZE * 4; roomDepth = ROOM_WORLD_SIZE; break;

        case 'CORRIDOR_SHORT': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE; break;
        case 'SQUARE_2X2': roomWidth = ROOM_WORLD_SIZE * 2; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'RECT_3X1': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE; break;
        case 'RECT_1X2': roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'RECT_1X3': roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE * 3; break;
        case 'RECT_3X2': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'U_SHAPE_DOWN': roomWidth = ROOM_WORLD_SIZE * 3; roomDepth = ROOM_WORLD_SIZE * 2; break;
        case 'CIRCLE': roomWidth = customSize; roomDepth = customSize; break;
        case 'SQUARE_1X1': default: roomWidth = ROOM_WORLD_SIZE; roomDepth = ROOM_WORLD_SIZE; break;
    }
    halfWidth = roomWidth / 2;
    halfDepth = roomDepth / 2;
    boundingBox = _calculateRoomBoundingBox(shapeKey, roomWidth, roomDepth, customSize);

    // Store the calculated bounds in roomData for object placement
    roomData.bounds = {
        width: roomWidth,
        depth: roomDepth,
        minX: boundingBox.minX,
        maxX: boundingBox.maxX,
        minZ: boundingBox.minZ,
        maxZ: boundingBox.maxZ
    };

    // Get room geometry segments with updated dimensions
    const { wallSegments, floorSegments } = _getRoomGeometrySegments(shapeKey, roomWidth, roomDepth, halfWidth, halfDepth, roomData.connections);

    // Note: Wall collision coverage validation was removed as it was returning empty arrays
    // If needed, gap-filling collision walls can be implemented here for complex room shapes

    const roomMaxZ = boundingBox.maxZ;

    // Use the environment type manager to select floor, wall, and door types
    const seed = roomData.id * 1000 + 37; // Generate a seed based on room ID
    let environmentTypes = environmentTypeManager.selectEnvironmentTypes({
        biome: areaData.name.toLowerCase().replace(/\s+/g, '_').replace(/^the_/, ''),
        roomType: roomData.type.toLowerCase(),
        seed
    });

    // ENHANCED: Apply event room material overrides if this is an event room
    if (roomData.type === 'EVENT' && roomData.eventRoomData && roomData.eventRoomData.materials) {
        const eventMaterials = roomData.eventRoomData.materials;
        console.log(`[generateRoomVisuals] Applying event room material overrides for room ${roomData.id}:`, eventMaterials);
        console.log(`[generateRoomVisuals] BEFORE override - environmentTypes:`, environmentTypes);

        // Override environment types with event room materials
        if (eventMaterials.walls) {
            environmentTypes.wallType = eventMaterials.walls;
            console.log(`[generateRoomVisuals] Wall override: ${eventMaterials.walls}`);
        }
        if (eventMaterials.floors) {
            environmentTypes.floorType = eventMaterials.floors;
            console.log(`[generateRoomVisuals] Floor override: ${eventMaterials.floors}`);
            console.log(`[generateRoomVisuals] CRITICAL: Event room floor type completely replaces default floor type`);
        }

        console.log(`[generateRoomVisuals] AFTER override - environmentTypes:`, environmentTypes);
    }

    console.log(`[generateRoomVisuals] Final environment types for room ${roomData.id}:`, environmentTypes);

    console.log(`[generateRoomVisuals] Looking up prefab functions for room ${roomData.id}:`);
    console.log(`[generateRoomVisuals] - Floor type: '${environmentTypes.floorType}'`);
    console.log(`[generateRoomVisuals] - Wall type: '${environmentTypes.wallType}'`);
    console.log(`[generateRoomVisuals] - Door type: '${environmentTypes.doorType}'`);
    
    const floorFunc = getPrefabFunction(environmentTypes.floorType, 'floor');
    const wallFunc = getPrefabFunction(environmentTypes.wallType, 'wall');
    const doorFunc = getPrefabFunction(environmentTypes.doorType, 'door');
    
    console.log(`[generateRoomVisuals] Prefab function results:`);
    console.log(`[generateRoomVisuals] - Floor function found: ${!!floorFunc}`);
    console.log(`[generateRoomVisuals] - Wall function found: ${!!wallFunc}`);
    console.log(`[generateRoomVisuals] - Door function found: ${!!doorFunc}`);

    // 1. Add Floor Segments
    console.log(`[generateRoomVisuals] Adding ${floorSegments.length} floor segment(s) for room ${roomData.id}`);
    floorSegments.forEach(floorSeg => {
        const floorMesh = addFloorSegment(roomGroup, collisionMeshes, floorSeg.width, floorSeg.depth, floorSeg.position, roomData, floorFunc, roomData.walkableTiles);

        // ENHANCED: Apply event room floor tint if specified
        if (roomData.type === 'EVENT' && roomData.eventRoomData && roomData.eventRoomData.materials && roomData.eventRoomData.materials.floorTint) {
            const floorTint = roomData.eventRoomData.materials.floorTint;
            console.log(`[generateRoomVisuals] Applying floor tint: 0x${floorTint.toString(16)}`);
            applyMaterialTint(floorMesh, floorTint);
        }
    });
    
    // CRITICAL: Update the scene graph to ensure floor meshes are properly integrated
    roomGroup.updateMatrixWorld(true);
    
    // DEBUG: Immediately check floor meshes after creation
    console.log(`[generateRoomVisuals] DEBUG: Checking floor meshes immediately after creation...`);
    let debugFloorCount = 0;
    let debugFloorGroups = 0;
    roomGroup.traverse(child => {
        if (child.isGroup && child.name?.toLowerCase().includes('floor')) {
            debugFloorGroups++;
            console.log(`[generateRoomVisuals] DEBUG: Found floor GROUP: ${child.name}, children: ${child.children.length}`);
        } else if (child.isMesh && child.userData?.isFloor) {
            debugFloorCount++;
            console.log(`[generateRoomVisuals] DEBUG: Found floor mesh with isFloor=true: ${child.name || 'unnamed'}`);
        }
    });
    console.log(`[generateRoomVisuals] DEBUG: Total floor meshes with isFloor=true: ${debugFloorCount}, floor groups: ${debugFloorGroups}`);

    // 2. Add Wall Segments and Doors
    console.log(`[generateRoomVisuals] Adding ${wallSegments.length} wall segment(s) for room ${roomData.id}`);
    const addedWallSegmentsData = []; // Store wall segment data for interior object placement
    wallSegments.forEach((wallSeg) => {
        const { position, rotation, length, isOuterBoundary, direction } = wallSeg;
        const connectionDirLower = direction ? direction.toLowerCase() : null;

        // For event rooms, only create doors that are defined in the event room configuration
        let isDoorCandidate = false;
        if (roomData.type === 'EVENT' && ((roomData.eventRoomData && roomData.eventRoomData.availableConnections) || (roomData.eventMechanics && roomData.eventMechanics.availableConnections))) {
            // Event room: Create doors where connections exist AND are in availableConnections
            // Event rooms typically only have one connection back to the parent room
            const hasActualConnection = roomData.connections && 
                                      roomData.connections[connectionDirLower] !== null && 
                                      roomData.connections[connectionDirLower] !== undefined;
            
            // Check both eventRoomData and eventMechanics for availableConnections
            const availableConnections = roomData.eventRoomData?.availableConnections || roomData.eventMechanics?.availableConnections;
            
            // Debug logging for room 24
            if (roomData.id === 24 && connectionDirLower === 'w') {
                console.log(`[DEBUG ROOM 24] Checking west door:`);
                console.log(`  - Has eventRoomData: ${!!roomData.eventRoomData}`);
                console.log(`  - Has eventMechanics: ${!!roomData.eventMechanics}`);
                console.log(`  - availableConnections:`, availableConnections);
                console.log(`  - hasActualConnection: ${hasActualConnection}`);
                console.log(`  - connections:`, roomData.connections);
            }
            
            // For event rooms, only create doors where there are actual connections
            // Event rooms are dead-ends, so they typically only have one connection back to parent
            if (hasActualConnection) {
                // Only create door if this direction is allowed by the event room config
                isDoorCandidate = isOuterBoundary &&
                                connectionDirLower &&
                                (!availableConnections || availableConnections[connectionDirLower] !== false);
            } else {
                isDoorCandidate = false;
            }
            
            if (connectionDirLower && !hasActualConnection) {
                // No actual connection in this direction - this is normal for event rooms
                if (isDoorCandidate) {
                    console.log(`[EVENT ROOM] Room ${roomData.id}: Creating door for ${connectionDirLower} despite no connection (shouldn't happen)`);
                }
            } else if (connectionDirLower && hasActualConnection && isDoorCandidate) {
                console.log(`[EVENT ROOM] Room ${roomData.id}: Creating door for ${connectionDirLower} - connection to room ${roomData.connections[connectionDirLower]}`);
            } else if (connectionDirLower && hasActualConnection && !isDoorCandidate) {
                console.log(`[EVENT ROOM] Room ${roomData.id}: Skipping door for ${connectionDirLower} - blocked by event room config`);
            }
        } else {
            // Normal room: Use standard door logic
            isDoorCandidate = isOuterBoundary &&
                            connectionDirLower &&
                            roomData.connections &&
                            roomData.connections[connectionDirLower] !== null &&
                            roomData.connections[connectionDirLower] !== undefined;
        }

        let isDoorLocation = isDoorCandidate && !doorPlaced[connectionDirLower];

        // Determine visibility BEFORE adding data
        // CRITICAL FIX: Respect isVisible property from wall segment definition
        let isVisible = wallSeg.isVisible !== undefined ? wallSeg.isVisible : true;
        const epsilon = 0.1;
        if (position.z >= roomMaxZ - epsilon) {
            isVisible = false;
        }

        // Always add the wall segment first (for seamless appearance)
        const wallSegmentGroup = addWallSegment(roomGroup, collisionMeshes, lights, length, position, rotation, isOuterBoundary, isDarkRoom, roomData, roomMaxZ, wallFunc, isVisible);

        // ENHANCED: Apply event room wall tint if specified
        if (roomData.type === 'EVENT' && roomData.eventRoomData && roomData.eventRoomData.materials && roomData.eventRoomData.materials.wallTint) {
            const wallTint = roomData.eventRoomData.materials.wallTint;
            console.log(`[generateRoomVisuals] Applying wall tint: 0x${wallTint.toString(16)}`);
            applyMaterialTint(wallSegmentGroup, wallTint);
        }

        // Then check if this wall should have a secret wall integrated into it
        if (isDoorLocation && wallSegmentGroup && window.dungeonHandler && window.dungeonHandler.secretRoomManager) {
            // Calculate door position to check for secret wall
            const inwardVector = new THREE.Vector3();
            switch(connectionDirLower) {
                case 'n': inwardVector.set(0, 0, 1); break;
                case 's': inwardVector.set(0, 0, -1); break;
                case 'e': inwardVector.set(-1, 0, 0); break;
                case 'w': inwardVector.set(1, 0, 0); break;
            }

            const wallOffset = 0.3;
            const doorPosition = position.clone().add(inwardVector.clone().multiplyScalar(wallOffset));
            doorPosition.y = 0;

            console.log(`[GenVis][${roomData.id}] Checking for secret wall integration in direction ${direction.toUpperCase()}`);
            console.log(`[GenVis][${roomData.id}] Wall segment group:`, wallSegmentGroup.name || 'unnamed');
            console.log(`[GenVis][${roomData.id}] Wall info: length=${length}, position=(${position.x.toFixed(2)}, ${position.z.toFixed(2)}), direction=${direction}`);

            // Check if this should have a secret wall integrated
            const secretWallInfo = window.dungeonHandler.secretRoomManager.integrateSecretWallIntoWallSegment(
                roomData.id,
                roomGroup,
                collisionMeshes,
                wallSegmentGroup,
                { [connectionDirLower]: doorPosition },
                { position, rotation, length, direction }
            );

            if (secretWallInfo && secretWallInfo.integrated) {
                console.log(`[GenVis][${roomData.id}] ✅ Successfully integrated secret wall into wall segment in direction ${direction.toUpperCase()}`);

                // Still add wall segment data for object placement
                addedWallSegmentsData.push({
                    position, rotation, length, direction, isOuterBoundary, isVisible,
                    isVisible: position.z < roomMaxZ - epsilon,
                    isSecretWall: true
                });

                // Skip the door creation section for secret walls
                // Don't create a regular door for this direction
                isDoorLocation = false;
            } else {
                console.log(`[GenVis][${roomData.id}] No secret wall integration needed for direction ${direction.toUpperCase()}`);
            }
        }

        // Add wall segment data for object placement (if not a secret wall)
        addedWallSegmentsData.push({
            position, rotation, length, direction, isOuterBoundary, isVisible,
            // Add explicit visibility check for south wall
            isVisible: position.z < roomMaxZ - epsilon
        });

        // If a door should be here, add it AFTER the wall is already placed
        if (isDoorLocation) {
            console.log(`[GenVis][${roomData.id}] Calling addDoorPrefab for direction ${direction.toUpperCase()} at pos ${position.x.toFixed(1)},${position.z.toFixed(1)}`);

            // Calculate door position with a small inward offset from the wall
            // First determine the inward direction vector based on the wall direction
            const inwardVector = new THREE.Vector3();
            switch(connectionDirLower) {
                case 'n': inwardVector.set(0, 0, 1); break;  // North wall - inward is +Z (south)
                case 's': inwardVector.set(0, 0, -1); break; // South wall - inward is -Z (north)
                case 'e': inwardVector.set(-1, 0, 0); break; // East wall - inward is -X (west)
                case 'w': inwardVector.set(1, 0, 0); break;  // West wall - inward is +X (east)
            }

            // Calculate the door position extending out from the wall surface
            let doorPosition;
            
            // ENHANCED: Use custom door position for event rooms
            if (roomData.type === 'EVENT' && roomData.eventRoomData && roomData.eventRoomData.doorPositions) {
                // For event rooms, we need to use the connection direction, not entrance direction
                // The door should be placed based on which wall it's on (connectionDirLower)
                const customDoorPos = roomData.eventRoomData.doorPositions[connectionDirLower];
                if (customDoorPos) {
                    doorPosition = new THREE.Vector3(customDoorPos.x, customDoorPos.y, customDoorPos.z);
                    
                    // For event rooms, door positions are already at the correct wall positions
                    // Only apply a tiny offset to prevent z-fighting
                    const wallOffset = 0.05; // Much smaller offset for event rooms
                    doorPosition.add(inwardVector.clone().multiplyScalar(wallOffset));
                    
                    console.log(`[EVENT DOOR] Room ${roomData.id}: Using custom door position for ${connectionDirLower} wall:`, doorPosition);
                    console.log(`[EVENT DOOR] Door rotation for ${connectionDirLower}: ${(rotation * 180/Math.PI).toFixed(0)}°`);
                } else {
                    console.warn(`[EVENT DOOR] Room ${roomData.id}: No custom door position for ${connectionDirLower} wall, using default`);
                }
            }
            
            // Fallback to default door positioning
            if (!doorPosition) {
                const wallOffset = 0.3; // INCREASED: Make doors extend further out from wall
                doorPosition = position.clone().add(
                    inwardVector.clone().multiplyScalar(wallOffset)
                );
            }

            // Set Y position to 0 to ensure door is at floor level
            doorPosition.y = 0;

            // Get the door prefab function from area data first
            const doorType = areaData.doors && areaData.doors[0]; // Get first door type
            const doorFunc = doorType ? getPrefabFunction(doorType, 'door') : null;
            if (!doorFunc) {
                console.error(`[GenVis][${roomData.id}] No door prefab function found for type: ${doorType}`);
                console.error(`[GenVis][${roomData.id}] Available door types in area:`, areaData.doors);
                return { roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints };
            }

            // Check if this should be a secret wall instead of a regular door
            let skipRegularDoor = false;
            if (window.dungeonHandler && window.dungeonHandler.secretRoomManager) {
                const secretWallInfo = window.dungeonHandler.secretRoomManager.checkAndCreateSecretWallDuringGeneration(
                    roomData.id,
                    roomGroup,
                    collisionMeshes,
                    { [connectionDirLower]: doorPosition }
                );

                if (secretWallInfo && secretWallInfo.skipDoor) {
                    console.log(`[GenVis][${roomData.id}] Created secret wall instead of regular door for direction ${direction.toUpperCase()}`);
                    doorCenterPoints[connectionDirLower] = doorPosition.clone();
                    doorPlaced[connectionDirLower] = true;
                    skipRegularDoor = true;
                }
            }

            // Create regular door if not replaced by secret wall
            if (!skipRegularDoor) {
                // Create a door at this adjusted inward position
                const doorGroup = addDoorPrefab(roomGroup, collisionMeshes, doorPosition, rotation, roomData, doorFunc, connectionDirLower);

                if (doorGroup) {
                    roomGroup.add(doorGroup);
                    doorCenterPoints[connectionDirLower] = doorGroup.position.clone();
                    doorPlaced[connectionDirLower] = true;

                    // Enhanced logging for event room doors
                    if (roomData.type === 'EVENT') {
                        console.log(`[EVENT DOOR] ✅ Door added to room ${roomData.id} on ${direction.toUpperCase()} wall`);
                        console.log(`[EVENT DOOR] Door position:`, doorGroup.position);
                        console.log(`[EVENT DOOR] Door rotation:`, doorGroup.rotation);
                        console.log(`[EVENT DOOR] Door visible:`, doorGroup.visible);
                        console.log(`[EVENT DOOR] Room group has ${roomGroup.children.length} children`);
                    }

                    // Special logging for room 0 door placement
                    if (roomData.id === 0) {
                        console.log(`[Room0Door] ✅ Door placed on ${direction.toUpperCase()} wall at position:`, doorGroup.position);
                    }
                } else {
                    console.error(`[GenVis][${roomData.id}-${shapeKey}] Failed to create Door Prefab for [${direction.toUpperCase()}].`);
                }
            }
        }
    });

    // --- MANDATORY VERIFICATION: Ensure Room 0 has north door ---
    if (roomData.id === 0) {
        if (doorPlaced.n) {
            console.log(`[Room0Verification] ✅ MANDATORY north door confirmed for Room 0`);
        } else {
            console.error(`[Room0Verification] ❌ CRITICAL: Room 0 missing mandatory north door!`);
            console.error(`[Room0Verification] Door placement status:`, doorPlaced);
            console.error(`[Room0Verification] Room connections:`, roomData.connections);
        }
    }

    // --- 4. Place Interior Objects using the environment object manager ---
    if (roomData.id !== 0) { // Skip for start room (handled separately below)
        // Check if this is an event room and handle it specially
        if (roomData.type === 'EVENT') {
            console.log(`[generateRoomVisuals] 🎯 DETECTED EVENT ROOM ${roomData.id}!`);
            console.log(`[generateRoomVisuals] Room type: ${roomData.type}`);
            console.log(`[generateRoomVisuals] Event room name: ${roomData.eventRoomName}`);
            console.log(`[generateRoomVisuals] Has eventRoomData: ${!!roomData.eventRoomData}`);
            console.log(`[generateRoomVisuals] Event room data:`, roomData.eventRoomData);

            if (roomData.eventRoomData) {
                console.log(`[generateRoomVisuals] ✅ Generating event room: ${roomData.eventRoomName}`);
                // Event rooms are handled by the EventRoomManager
                if (window.dungeonHandler && window.dungeonHandler.eventRoomManager) {
                    window.dungeonHandler.eventRoomManager.generateEventRoom(roomGroup, collisionMeshes, lights, roomData);
                } else {
                    console.warn('[generateRoomVisuals] ❌ Event room manager not available for event room generation');
                }
            } else {
                console.error(`[generateRoomVisuals] ❌ EVENT ROOM ${roomData.id} HAS NO eventRoomData!`);
                console.error(`[generateRoomVisuals] This means the event room configuration was not properly applied during dungeon generation.`);
                console.error(`[generateRoomVisuals] Room data keys:`, Object.keys(roomData));

                // EMERGENCY FIX: Try to apply event room configuration now
                console.warn(`[generateRoomVisuals] 🚨 EMERGENCY: Attempting to apply event room config now...`);

                if (window.dungeonHandler && window.dungeonHandler.eventRoomManager) {
                    console.log(`[generateRoomVisuals] Event room manager found, checking for event room key...`);

                    // Get the event room key for this floor
                    const floorNumber = roomData.floorLevel || 1;
                    console.log(`[generateRoomVisuals] Looking for event room on floor: ${floorNumber}`);

                    const eventRoomKey = window.dungeonHandler.eventRoomManager.getEventRoomForFloor(floorNumber);
                    console.log(`[generateRoomVisuals] Event room key found: ${eventRoomKey}`);

                    if (eventRoomKey) {
                        console.log(`[generateRoomVisuals] 🔧 Found event room key: ${eventRoomKey}, applying config...`);
                        window.dungeonHandler.eventRoomManager.applyEventRoomConfig(roomData, eventRoomKey);

                        // Try again with the newly applied config
                        if (roomData.eventRoomData) {
                            console.log(`[generateRoomVisuals] ✅ Emergency config application successful! Generating event room...`);
                            window.dungeonHandler.eventRoomManager.generateEventRoom(roomGroup, collisionMeshes, lights, roomData);
                            return; // Exit early, don't fall back to normal generation
                        } else {
                            console.error(`[generateRoomVisuals] ❌ Emergency config application failed!`);
                        }
                    } else {
                        console.error(`[generateRoomVisuals] ❌ No event room key found for floor ${floorNumber}`);

                        // Try to select a new event room for this floor
                        console.warn(`[generateRoomVisuals] 🔄 Attempting to select new event room for floor ${floorNumber}...`);
                        const newEventRoomKey = window.dungeonHandler.eventRoomManager.selectEventRoomForFloor(floorNumber);

                        if (newEventRoomKey) {
                            console.log(`[generateRoomVisuals] 🎲 Selected new event room: ${newEventRoomKey}, applying config...`);
                            window.dungeonHandler.eventRoomManager.applyEventRoomConfig(roomData, newEventRoomKey);

                            if (roomData.eventRoomData) {
                                console.log(`[generateRoomVisuals] ✅ New event room config successful! Generating event room...`);
                                window.dungeonHandler.eventRoomManager.generateEventRoom(roomGroup, collisionMeshes, lights, roomData);
                                return; // Exit early, don't fall back to normal generation
                            }
                        } else {
                            console.error(`[generateRoomVisuals] ❌ Failed to select new event room`);
                        }
                    }
                } else {
                    console.error(`[generateRoomVisuals] ❌ Event room manager not available for emergency fix`);
                }

                // If we get here, all emergency fixes failed
                console.warn(`[generateRoomVisuals] 🚨 All emergency fixes failed, falling back to normal room generation for EVENT room ${roomData.id}`);
            }
        } else if (roomData.type === 'Boss') {
            console.log(`[generateRoomVisuals] 🎯 DETECTED BOSS ROOM ${roomData.id}!`);
            console.log(`[generateRoomVisuals] Room type: ${roomData.type}`);
            console.log(`[generateRoomVisuals] Boss room name: ${roomData.bossRoomName}`);
            console.log(`[generateRoomVisuals] Has bossRoomData: ${!!roomData.bossRoomData}`);
            console.log(`[generateRoomVisuals] Boss room data:`, roomData.bossRoomData);

            if (roomData.bossRoomData) {
                console.log(`[generateRoomVisuals] ✅ Generating boss room: ${roomData.bossRoomName}`);
                // Boss rooms are handled by the BossRoomManager
                if (window.dungeonHandler && window.dungeonHandler.bossRoomManager) {
                    window.dungeonHandler.bossRoomManager.generateBossRoom(roomGroup, collisionMeshes, lights, roomData);
                    
                    // CRITICAL: Must return the expected room data object
                    return {
                        roomGroup,
                        collisionMeshes,
                        lights,
                        boundingBox,
                        floorData: {
                            floorMeshes: [],
                            validSpawnAreas: [],
                            floorBounds: boundingBox
                        }
                    };
                } else {
                    console.warn('[generateRoomVisuals] ❌ Boss room manager not available for boss room generation');
                }
            } else {
                console.error(`[generateRoomVisuals] ❌ BOSS ROOM ${roomData.id} HAS NO bossRoomData!`);
                
                // Try to apply boss room configuration now
                if (window.dungeonHandler && window.dungeonHandler.bossRoomManager && roomData.areaKey) {
                    console.log(`[generateRoomVisuals] 🚨 Attempting to apply boss room config for area: ${roomData.areaKey}`);
                    window.dungeonHandler.bossRoomManager.applyBossRoomConfig(roomData, roomData.areaKey);
                    
                    // Try again with the newly applied config
                    if (roomData.bossRoomData) {
                        console.log(`[generateRoomVisuals] ✅ Boss room config applied successfully! Generating boss room...`);
                        window.dungeonHandler.bossRoomManager.generateBossRoom(roomGroup, collisionMeshes, lights, roomData);
                        
                        // CRITICAL: Must return the expected room data object
                        return {
                            roomGroup,
                            collisionMeshes,
                            lights,
                            boundingBox,
                            floorData: {
                                floorMeshes: [],
                                validSpawnAreas: [],
                                floorBounds: boundingBox
                            }
                        };
                    }
                }
                
                // Fall back to normal generation
                const areaData = getAreaData(roomData.area || roomData.state?.area || 'catacombs');
                console.log(`[generateRoomVisuals] Falling back to normal room generation with area: ${areaData.name}`);
            }
        } else {
            // Regular room object placement using environment object manager
            // Improved seed generation with more entropy to avoid predictable patterns
            const roomIdHash = roomData.id * 31337; // Prime multiplier
            const floorHash = (roomData.floorLevel || 1) * 7919; // Another prime
            const typeHash = roomData.type.charCodeAt(0) * 251; // Type-based variation
            const objectSeed = roomIdHash + floorHash + typeHash + 73; // Combine for more entropy
            const biome = areaData.name.toLowerCase().replace(/\s+/g, '_').replace(/^the_/, '');

            // Get floor tracker for per-floor object limits
            const floorLevel = roomData.floorLevel || 1;
            const floorObjectTracker = environmentObjectManager.getFloorTracker(floorLevel);

            // Choose objects to place
            console.log(`[Room ${roomData.id}] Choosing objects for biome: ${biome}, roomType: ${roomData.type.toLowerCase()}, floor: ${floorLevel}`);
            console.log(`[Room ${roomData.id}] Object seed: ${objectSeed} (room: ${roomIdHash}, floor: ${floorHash}, type: ${typeHash})`);
            
            let objectsToPlace = environmentObjectManager.chooseObjectGroup({
                biome,
                roomType: roomData.type.toLowerCase(),
                roomData,
                seed: objectSeed,
                floorObjectTracker
            });

            // Check if this is a boss room with torch removal requested
            const isBossRoomEnv = roomData.type === 'Boss';
            const removeTorchesEnv = isBossRoomEnv && roomData.bossRoomData?.lighting?.removeTorches;
            
            if (removeTorchesEnv) {
                // Filter out any torch objects
                console.log(`[Room ${roomData.id}] Boss room with removeTorches=true - filtering out torches from environment objects`);
                objectsToPlace = objectsToPlace.filter(obj => obj.type !== 'torch');
            }

        console.log(`[Room ${roomData.id}] Objects to place:`, objectsToPlace.map(obj => `${obj.type} (${obj.placement})`));

        // Get placement positions
        const roomDimensions = {
            width: roomWidth,
            depth: roomDepth,
            minX: -halfWidth,
            maxX: halfWidth,
            minZ: -halfDepth,
            maxZ: halfDepth
        };

        // CRITICAL: Pass valid floor positions to the environment manager
        console.log(`[Room ${roomData.id}] About to scan for floor positions...`);
        console.log(`[Room ${roomData.id}] Room group has ${roomGroup.children.length} children`);
        console.log(`[Room ${roomData.id}] Area: ${areaData.name}, Floor type: ${environmentTypes.floorType}`);
        
        // DEBUG: Check for floor meshes before scanning
        console.log(`[Room ${roomData.id}] DEBUG: Checking for floor meshes before scanning...`);
        let preFloorCount = 0;
        let floorGroups = 0;
        roomGroup.traverse(child => {
            if (child.isGroup && child.name?.toLowerCase().includes('floor')) {
                floorGroups++;
                console.log(`[Room ${roomData.id}] DEBUG: Found floor GROUP: ${child.name}, children: ${child.children.length}`);
                // Check children of floor groups
                child.children.forEach((subChild, idx) => {
                    if (subChild.isMesh) {
                        console.log(`[Room ${roomData.id}] DEBUG:   - Child ${idx}: mesh="${subChild.name}", isFloor=${subChild.userData?.isFloor}`);
                    }
                });
            } else if (child.isMesh) {
                console.log(`[Room ${roomData.id}] DEBUG: Found mesh: ${child.name || 'unnamed'}, isFloor: ${child.userData?.isFloor}, material: ${child.material?.name}`);
                if (child.userData?.isFloor) {
                    preFloorCount++;
                }
            }
        });
        console.log(`[Room ${roomData.id}] DEBUG: Found ${preFloorCount} floor meshes, ${floorGroups} floor groups before scanning`);
        
        // Update matrices before scanning - this is crucial!
        roomGroup.updateMatrixWorld(true);
        
        validFloorPositions = _scanForValidFloorPositions(roomGroup, VOXEL_SIZE * 2, shapeKey);
        console.log(`[Room ${roomData.id}] Scanned ${validFloorPositions.length} valid floor positions for shape ${shapeKey}`);
        
        if (validFloorPositions.length === 0) {
            console.error(`[Room ${roomData.id}] WARNING: No valid floor positions found! Objects may spawn in void.`);
            console.error(`[Room ${roomData.id}] Room shape: ${shapeKey}, Area: ${areaData.name}, Floor type: ${environmentTypes.floorType}`);
        }
        
        const objectPlacements = environmentObjectManager.getObjectPlacements(
            objectsToPlace,
            roomDimensions,
            objectSeed,
            shapeKey,  // CRITICAL FIX: Pass room shape for validation
            validFloorPositions  // NEW: Pass actual floor positions
        );

        // Create object instances
        console.log(`[Room ${roomData.id}] Creating object instances for ${objectPlacements.length} placements`);
        const objectInstances = environmentObjectManager.createObjectInstances(objectPlacements);

        // Place objects in the room
        console.log(`[Room ${roomData.id}] Placing ${objectInstances.length} objects in the room`);
        console.log(`[Room ${roomData.id}] Object instances:`, objectInstances.map(inst => `${inst.type} (${inst.options.placement})`));

        // Process objects sequentially to handle async prefab functions
        for (let index = 0; index < objectInstances.length; index++) {
            const instance = objectInstances[index];
            const { prefabFunc, options, type } = instance;
            console.log(`[Room ${roomData.id}] Creating object ${index + 1}/${objectInstances.length}: ${type}`);

            try {
                // Handle both sync and async prefab functions
                const objectResult = await Promise.resolve(prefabFunc(options));

                // Handle both return types: object with group property or direct group
                const objectGroup = objectResult && objectResult.group ? objectResult.group : objectResult;

                if (objectGroup && objectGroup instanceof THREE.Object3D) {
                    console.log(`[Room ${roomData.id}] Successfully created object: ${type}`);
                    console.log(`[Room ${roomData.id}] Object userData:`, objectGroup.userData);
                    console.log(`[Room ${roomData.id}] isDestructible: ${objectGroup.userData?.isDestructible}`);

                    // Set position and rotation based on options
                    console.log(`[Room ${roomData.id}] Object options:`, options);

                    if (options.position) {
                        console.log(`[Room ${roomData.id}] Setting position: x=${options.position.x}, y=${options.position.y}, z=${options.position.z}`);
                        objectGroup.position.set(options.position.x, options.position.y, options.position.z);
                    }

                    if (options.rotation !== undefined) {
                        console.log(`[Room ${roomData.id}] Setting rotation: ${options.rotation}`);
                        objectGroup.rotation.y = options.rotation;
                    }

                    // Adjust height and orientation based on placement type
                    if (options.placement === 'wall') {
                        console.log(`[Room ${roomData.id}] Processing wall object: ${type}`);

                        // ENHANCED Wall placement logic with proper offsets
                        objectGroup.position.copy(options.position);
                        objectGroup.lookAt(objectGroup.position.clone().add(options.inwardNormal));

                        // CONFIGURABLE: Use object-specific wall offsets to prevent clipping
                        let wallOffset = WALL_OFFSETS[type] || WALL_OFFSETS.default;
                        
                        // Add epsilon to prevent exact overlap and z-fighting
                        wallOffset += WALL_OFFSETS.EPSILON;

                        objectGroup.position.addScaledVector(options.inwardNormal, wallOffset);
                        let yPos = WALL_HEIGHT * 0.5;
                        if (type === 'vine') yPos = WALL_HEIGHT * 0.85;
                        else if (type === 'torch') yPos = WALL_HEIGHT * 0.6;
                        objectGroup.position.y = yPos;
                    } else { // Floor placement
                        // Trust the validated positions from our floor scanner
                        // The positions have already been validated to be on textured floors only
                        // No need for secondary validation that might move objects to invalid positions
                        
                        // CRITICAL FIX: Calculate proper floor height accounting for curvature
                        // But start from the position's Y value (from floor scan) not 0
                        const baseY = options.position.y || 0;
                        const curvedFloorY = _calculateFloorCurvatureHeight(objectGroup.position.x, objectGroup.position.z, baseY, roomData);
                        objectGroup.position.y = curvedFloorY;
                    }

                    roomGroup.add(objectGroup);

                    // FIXED: Add collision meshes using the same system as pillars
                    if (objectGroup.userData?.isDestructible) {
                        // For destructible objects, add the parent group to collision meshes
                        collisionMeshes.push(objectGroup);
                        console.log(`[Room ${roomData.id}] Added destructible object group to collision: ${type}`);
                        console.log(`[Room ${roomData.id}] Object details: name=${objectGroup.name}, hasGeometry=${!!objectGroup.geometry}, childrenCount=${objectGroup.children?.length}`);

                        // Set up child meshes with parent references but DON'T add to collision array
                        objectGroup.traverse(child => {
                            if (child.isMesh && child !== objectGroup) {
                                // Child meshes inherit from parent but are not directly destructible
                                child.userData.isDestructible = false;
                                child.userData.parentDestructible = objectGroup;
                                child.userData.parentUniqueId = objectGroup.userData?.uniqueId;
                                // DO NOT add child to collisionMeshes - only parent should be targetable
                            }
                        });
                    } else {
                        // For non-destructible objects, add child meshes as before
                        objectGroup.traverse(child => {
                            if (child.isMesh) {
                                collisionMeshes.push(child);
                                console.log(`[Room ${roomData.id}] Added collision mesh for ${type}:`, child);
                            }
                        });
                    }

                    // Add lights if the object has them
                    if (objectGroup.userData && objectGroup.userData.lights) {
                        objectGroup.userData.lights.forEach(light => {
                            lights.push(light);
                        });
                    }

                    // Handle torch light creation
                    if (type === 'torch' && objectResult && objectResult.flameLocalPosition) {
                        const light = _createTorchLight();
                        light.position.copy(objectGroup.localToWorld(objectResult.flameLocalPosition.clone()));
                        lights.push(light);
                    }
                } else {
                    console.warn(`[Room ${roomData.id}] Prefab function returned null for object: ${type}`);
                }
            } catch (error) {
                console.error(`[Room ${roomData.id}] Error creating object ${type}:`, error);
            }
        }

            console.log(`[generateRoomVisuals] Placed ${objectInstances.length} objects in room ${roomData.id}`);
        } // End regular room object placement
    } // End non-room-0 check

    // --- NEW: Special Handling for Room 0 ---
    if (roomData.id === 0) {
        console.log(`[generateRoomVisuals][${roomData.id}] Placing Ritual Circle and Aether Torches.`);

        // Place Ritual Circle (already existing code)
        const ritualCirclePrefabFunc = getPrefabFunction('ritual_circle', 'interior');
        if (ritualCirclePrefabFunc) {
            const ritualCircleGroup = ritualCirclePrefabFunc();
            if (ritualCircleGroup) {
                // CRITICAL FIX: Calculate proper floor height accounting for curvature
                const curvedFloorY = _calculateFloorCurvatureHeight(0, 0, 0, roomData);
                ritualCircleGroup.position.set(0, curvedFloorY, 0);
                roomGroup.add(ritualCircleGroup);
                console.log(`[generateRoomVisuals][${roomData.id}] Ritual Circle added at room center with curvature adjustment (Y: ${curvedFloorY.toFixed(3)}).`);
            } else {
                console.warn(`[generateRoomVisuals][${roomData.id}] Failed to create ritual_circle prefab.`);
            }
        } else {
            console.warn(`[generateRoomVisuals][${roomData.id}] Could not find ritual_circle prefab function.`);
        }

        // Place Aether Torches around the circle
        const aetherTorchPrefabFunc = getPrefabFunction('aether_torch', 'interior');
        if (aetherTorchPrefabFunc) {
            const numTorches = 6; // Place 6 torches
            const torchRadius = VOXEL_SIZE * 25; // Radius from center (adjust as needed)
            const angleStep = (Math.PI * 2) / numTorches;

            for (let i = 0; i < numTorches; i++) {
                const angle = i * angleStep;
                const torchX = Math.cos(angle) * torchRadius;
                const torchZ = Math.sin(angle) * torchRadius;
                const torchYOffset = 0.01; // Small offset to avoid floor clipping

                const torchResult = aetherTorchPrefabFunc();
                if (torchResult && torchResult.group) {
                    const torchGroup = torchResult.group;
                    torchGroup.position.set(torchX, torchYOffset, torchZ);
                    // Optional: Rotate torch to face center? Or keep upright?
                    // torchGroup.lookAt(new THREE.Vector3(0, torchYOffset, 0)); // Faces center
                    roomGroup.add(torchGroup);

                    // Add the blue point light
                    if (torchResult.lightLocalPosition && torchResult.lightColor && torchResult.lightIntensity && torchResult.lightDistance) {
                        const light = new THREE.PointLight(
                            torchResult.lightColor,
                            torchResult.lightIntensity,
                            torchResult.lightDistance
                        );
                        // Convert light's local position to world position
                        const lightWorldPos = torchGroup.localToWorld(torchResult.lightLocalPosition.clone());
                        light.position.copy(lightWorldPos);
                        light.castShadow = false; // Torches usually don't cast strong shadows
                        light.name = `aetherTorchLight_${i}`;
                        lights.push(light); // Add to the room's light array
                        console.log(`[generateRoomVisuals][${roomData.id}] Added light for Aether Torch ${i}.`);
                    }
                } else {
                    console.warn(`[generateRoomVisuals][${roomData.id}] Failed to create aether_torch prefab instance ${i}.`);
                }
            }
            console.log(`[generateRoomVisuals][${roomData.id}] Added ${numTorches} Aether Torches around circle.`);
        } else {
            console.warn(`[generateRoomVisuals][${roomData.id}] Could not find aether_torch prefab function.`);
        }

        // --- LOCAL COORDINATE SYSTEM TEST: Place vase at specific coordinates with 50% chance ---
        // Use dungeon seed + room ID for deterministic but varying placement across different dungeons
        const dungeonSeed = roomData.dungeonSeed || Date.now(); // Fallback to timestamp if no dungeon seed
        const vasePlacementSeed = (roomData.id * 1337) + (dungeonSeed % 10000) + 42;
        const vasePlacementRng = mulberry32(vasePlacementSeed);
        const vaseSpawnChance = vasePlacementRng(); // Get random value 0-1

        console.log(`[LocalCoordinateTest][${roomData.id}] Dungeon seed: ${dungeonSeed}, Vase seed: ${vasePlacementSeed}`);
        // Configure spawn chance (1.0 = 100%, 0.5 = 50%, etc.)
        const STATUE_SPAWN_CHANCE = 1.0; // Currently set to 100% - change this to adjust spawn rate
        
        console.log(`[LocalCoordinateTest][${roomData.id}] Statue spawn chance: ${(vaseSpawnChance * 100).toFixed(1)}% (threshold: ${(STATUE_SPAWN_CHANCE * 100)}%)`);

        if (vaseSpawnChance <= STATUE_SPAWN_CHANCE) { // Configurable chance
            console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Spawning first angel statue at local coordinates (-3.947, 0.3, -3.33)`);

            // Local coordinates as specified
            const localCoords = { x: -3.947, y: 0.3, z: -3.33 };

            // Get large angel statue prefab function
            const angelStatuePrefabFunc = getPrefabFunction('large_angel_statue', 'interior');

            if (angelStatuePrefabFunc) {
                try {
                    const angelOptions = {
                        seed: vasePlacementSeed,
                        isDestructible: false, // SACRED: Angel statues in room 0 are indestructible
                        destructionEffect: 'none',
                        health: 999999, // Effectively indestructible
                        userData: {
                            placedViaLocalCoordinates: true,
                            localCoordinates: localCoords,
                            roomId: roomData.id,
                            statueNumber: 1,
                            isSacred: true // Mark as sacred object
                        }
                    };

                    const angelResult = angelStatuePrefabFunc(angelOptions);
                    const angelGroup = angelResult && angelResult.group ? angelResult.group : angelResult;

                    if (angelGroup && angelGroup instanceof THREE.Object3D) {
                        // Position using local coordinates (which are world coordinates relative to room center)
                        angelGroup.position.set(localCoords.x, localCoords.y, localCoords.z);

                        // Adjust for floor curvature if needed
                        const curvedFloorY = _calculateFloorCurvatureHeight(localCoords.x, localCoords.z, localCoords.y, roomData);
                        angelGroup.position.y = curvedFloorY;

                        // Add a distinctive name for easy identification
                        angelGroup.name = 'LocalCoordinateTestAngelStatue1';

                        roomGroup.add(angelGroup);

                        // FIXED: Add to collision meshes using consistent system
                        if (angelGroup.userData?.isDestructible) {
                            // For destructible objects, add the parent group to collision meshes
                            collisionMeshes.push(angelGroup);
                            console.log(`[Room ${roomData.id}] Added destructible angel statue group to collision`);

                            // Set up child meshes with parent references but DON'T add to collision array
                            angelGroup.traverse(child => {
                                if (child.isMesh && child !== angelGroup) {
                                    child.userData.isDestructible = false;
                                    child.userData.parentDestructible = angelGroup;
                                    child.userData.parentUniqueId = angelGroup.userData?.uniqueId;
                                }
                            });
                        } else {
                            // For non-destructible objects, add child meshes
                            angelGroup.traverse(child => {
                                if (child.isMesh) {
                                    collisionMeshes.push(child);
                                }
                            });
                        }

                        console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Successfully placed first angel statue at local coordinates (${localCoords.x}, ${localCoords.y}, ${localCoords.z}), adjusted Y: ${curvedFloorY.toFixed(3)}`);
                    } else {
                        console.error(`[LocalCoordinateTest][${roomData.id}] ❌ Failed to create first angel statue group`);
                    }
                } catch (error) {
                    console.error(`[LocalCoordinateTest][${roomData.id}] Error creating first angel statue:`, error);
                }
            } else {
                console.warn(`[LocalCoordinateTest][${roomData.id}] Could not find large_angel_statue prefab function`);
            }
        } else {
            console.log(`[LocalCoordinateTest][${roomData.id}] First angel statue not spawned (chance: ${(vaseSpawnChance * 100).toFixed(1)}% > 100%)`);
        }

        // --- SECOND ANGEL STATUE: Place second angel statue at different coordinates with 100% chance ---
        console.log(`[LocalCoordinateTest][${roomData.id}] Placing second angel statue at local coordinates (3.947, 0.3, -3.33)`);

        // Get large angel statue prefab function for second statue
        const secondAngelStatuePrefabFunc = getPrefabFunction('large_angel_statue', 'interior');

        if (secondAngelStatuePrefabFunc) {
            try {
                // Local coordinates for second angel statue as specified
                const secondLocalCoords = { x: 3.947, y: 0.3, z: -3.33 };

                const secondAngelOptions = {
                    seed: vasePlacementSeed + 1000, // Different seed for variation
                    isDestructible: false, // SACRED: Angel statues in room 0 are indestructible
                    destructionEffect: 'none',
                    health: 999999, // Effectively indestructible
                    userData: {
                        placedViaLocalCoordinates: true,
                        localCoordinates: secondLocalCoords,
                        roomId: roomData.id,
                        statueNumber: 2,
                        isSacred: true // Mark as sacred object
                    }
                };

                const secondAngelResult = secondAngelStatuePrefabFunc(secondAngelOptions);
                const secondAngelGroup = secondAngelResult && secondAngelResult.group ? secondAngelResult.group : secondAngelResult;

                if (secondAngelGroup && secondAngelGroup instanceof THREE.Object3D) {
                    // Position using local coordinates
                    secondAngelGroup.position.set(secondLocalCoords.x, secondLocalCoords.y, secondLocalCoords.z);

                    // Adjust for floor curvature if needed
                    const secondCurvedFloorY = _calculateFloorCurvatureHeight(secondLocalCoords.x, secondLocalCoords.z, secondLocalCoords.y, roomData);
                    secondAngelGroup.position.y = secondCurvedFloorY;

                    // Add a distinctive name for easy identification
                    secondAngelGroup.name = 'LocalCoordinateTestAngelStatue2';

                    roomGroup.add(secondAngelGroup);

                    // FIXED: Add to collision meshes using consistent system
                    if (secondAngelGroup.userData?.isDestructible) {
                        // For destructible objects, add the parent group to collision meshes
                        collisionMeshes.push(secondAngelGroup);
                        console.log(`[Room ${roomData.id}] Added destructible second angel statue group to collision`);

                        // Set up child meshes with parent references but DON'T add to collision array
                        secondAngelGroup.traverse(child => {
                            if (child.isMesh && child !== secondAngelGroup) {
                                child.userData.isDestructible = false;
                                child.userData.parentDestructible = secondAngelGroup;
                                child.userData.parentUniqueId = secondAngelGroup.userData?.uniqueId;
                            }
                        });
                    } else {
                        // For non-destructible objects, add child meshes
                        secondAngelGroup.traverse(child => {
                            if (child.isMesh) {
                                collisionMeshes.push(child);
                            }
                        });
                    }

                    console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Successfully placed second angel statue at local coordinates (${secondLocalCoords.x}, ${secondLocalCoords.y}, ${secondLocalCoords.z}), adjusted Y: ${secondCurvedFloorY.toFixed(3)}`);
                } else {
                    console.error(`[LocalCoordinateTest][${roomData.id}] ❌ Failed to create second angel statue group`);
                }
            } catch (error) {
                console.error(`[LocalCoordinateTest][${roomData.id}] Error creating second angel statue:`, error);
            }
        } else {
            console.warn(`[LocalCoordinateTest][${roomData.id}] Could not find large_angel_statue prefab function for second statue`);
        }
        // --- END SECOND ANGEL STATUE ---

        // --- END LOCAL COORDINATE SYSTEM TEST ---
    }

    // --- ANCIENT STONE PILLAR: Place in U-Shape (Left) rooms with 100% chance ---
    if (roomData.shapeKey === 'U_SHAPE_LEFT') {
        console.log(`[LocalCoordinateTest][${roomData.id}] Placing ancient stone pillar in U-Shape (Left) room at local coordinates (7.329, 0.3, 13.152)`);

        // Get ancient stone pillar prefab function
        const ancientPillarPrefabFunc = getPrefabFunction('ancient_stone_pillar', 'interior');

        if (ancientPillarPrefabFunc) {
            try {
                // Local coordinates as specified for U-Shape (Left) rooms
                const pillarLocalCoords = { x: 7.329, y: 0.0, z: 13.152 };

                const pillarOptions = {
                    seed: roomData.id * 1337 + 777, // Deterministic seed for pillar
                    isDestructible: true,
                    destructionEffect: 'collapse',
                    health: 1,
                    userData: {
                        placedViaLocalCoordinates: true,
                        localCoordinates: pillarLocalCoords,
                        roomId: roomData.id,
                        roomShape: roomData.shapeKey
                    }
                };

                const pillarResult = ancientPillarPrefabFunc(pillarOptions);
                const pillarGroup = pillarResult && pillarResult.group ? pillarResult.group : pillarResult;

                if (pillarGroup && pillarGroup instanceof THREE.Object3D) {
                    // Position using local coordinates
                    pillarGroup.position.set(pillarLocalCoords.x, pillarLocalCoords.y, pillarLocalCoords.z);

                    // Adjust for floor curvature if needed
                    const pillarCurvedFloorY = _calculateFloorCurvatureHeight(pillarLocalCoords.x, pillarLocalCoords.z, pillarLocalCoords.y, roomData);
                    pillarGroup.position.y = pillarCurvedFloorY;

                    // Add a distinctive name for easy identification
                    pillarGroup.name = 'LocalCoordinateTestAncientPillar';

                    // CRITICAL FIX: Add unique identifier to prevent cross-pillar destruction
                    const pillarUniqueId = `ancient_pillar_${roomData.id}_single`;
                    pillarGroup.userData.uniqueId = pillarUniqueId;

                    roomGroup.add(pillarGroup);

                    // CRITICAL FIX: Only add the pillar group to collision meshes
                    // Child meshes should NOT be in collision array to prevent duplicate targeting
                    collisionMeshes.push(pillarGroup);

                    // Set up child meshes with parent references but DON'T add to collision array
                    pillarGroup.traverse(child => {
                        if (child.isMesh) {
                            // Ensure child meshes inherit destructible properties from parent
                            child.userData.isDestructible = false; // Child meshes are NOT directly destructible
                            child.userData.parentDestructible = pillarGroup; // Reference to parent for destruction
                            child.userData.parentUniqueId = pillarUniqueId; // Parent unique ID for reference
                            child.name = `${pillarUniqueId}_child_${child.id}`;
                            // DO NOT add child to collisionMeshes - only parent should be targetable
                        }
                    });

                    console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Successfully placed ancient stone pillar at local coordinates (${pillarLocalCoords.x}, ${pillarLocalCoords.y}, ${pillarLocalCoords.z}), adjusted Y: ${pillarCurvedFloorY.toFixed(3)}`);
                } else {
                    console.error(`[LocalCoordinateTest][${roomData.id}] ❌ Failed to create ancient stone pillar group`);
                }
            } catch (error) {
                console.error(`[LocalCoordinateTest][${roomData.id}] Error creating ancient stone pillar:`, error);
            }
        } else {
            console.warn(`[LocalCoordinateTest][${roomData.id}] Could not find ancient_stone_pillar prefab function`);
        }
        // --- END ANCIENT STONE PILLAR ---
    }

    // --- STONE PILLARS: Place in RECT_1X3 rooms with 100% chance ---
    if (roomData.shapeKey === 'RECT_1X3') {
        console.log(`[LocalCoordinateTest][${roomData.id}] ✅ RECT_1X3 room detected! Placing stone pillars`);

        // Define pillar positions for RECT_1X3 rooms (moved away from walls for better spacing)
        const pillarPositions = [
            { x: -5.0, y: 0.0, z: 19.0 },   // SW corner - moved 1.28 units toward center (X) and 1.47 units toward center (Z)
            { x: 5.0, y: 0.0, z: 19.0 },    // SE corner - moved 1.25 units toward center (X) and 1.45 units toward center (Z)
            { x: 5.0, y: 0.0, z: -19.0 },   // NE corner - moved 1.24 units toward center (X) and 1.45 units toward center (Z)
            { x: -5.0, y: 0.0, z: -19.0 }   // NW corner - moved 1.26 units toward center (X) and 1.45 units toward center (Z)
        ];

        // Get ancient stone pillar prefab function (gray)
        const stonePillarPrefabFunc = getPrefabFunction('ancient_stone_pillar', 'interior');

        if (stonePillarPrefabFunc) {
            pillarPositions.forEach((pillarCoords, index) => {
                try {
                    const pillarOptions = {
                        seed: roomData.id * 1337 + 888 + index, // Deterministic seed per pillar
                        isDestructible: true,
                        destructionEffect: 'collapse',
                        health: 1,
                        userData: {
                            placedViaLocalCoordinates: true,
                            localCoordinates: pillarCoords,
                            roomId: roomData.id,
                            roomShape: roomData.shapeKey,
                            pillarIndex: index
                        }
                    };

                    const pillarResult = stonePillarPrefabFunc(pillarOptions);
                    const pillarGroup = pillarResult && pillarResult.group ? pillarResult.group : pillarResult;

                    if (pillarGroup && pillarGroup instanceof THREE.Object3D) {
                        // Position using local coordinates
                        pillarGroup.position.set(pillarCoords.x, pillarCoords.y, pillarCoords.z);

                        // Adjust for floor curvature if needed
                        const pillarCurvedFloorY = _calculateFloorCurvatureHeight(pillarCoords.x, pillarCoords.z, pillarCoords.y, roomData);
                        pillarGroup.position.y = pillarCurvedFloorY;

                        // Add a distinctive name for easy identification
                        pillarGroup.name = `LocalCoordinateTestStonePillar_${index}`;

                        // CRITICAL FIX: Add unique identifier to prevent cross-pillar destruction
                        const pillarUniqueId = `ancient_pillar_${roomData.id}_rect_${index}`;
                        pillarGroup.userData.uniqueId = pillarUniqueId;

                        roomGroup.add(pillarGroup);

                        // CRITICAL FIX: Only add the pillar group to collision meshes
                        // Child meshes should NOT be in collision array to prevent duplicate targeting
                        collisionMeshes.push(pillarGroup);

                        // Set up child meshes with parent references but DON'T add to collision array
                        pillarGroup.traverse(child => {
                            if (child.isMesh) {
                                // Ensure child meshes inherit destructible properties from parent
                                child.userData.isDestructible = false; // Child meshes are NOT directly destructible
                                child.userData.parentDestructible = pillarGroup; // Reference to parent for destruction
                                child.userData.parentUniqueId = pillarUniqueId; // Parent unique ID for reference
                                child.name = `${pillarUniqueId}_child_${child.id}`;
                                // DO NOT add child to collisionMeshes - only parent should be targetable
                            }
                        });

                        console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Successfully placed stone pillar ${index + 1} at local coordinates (${pillarCoords.x}, ${pillarCoords.y}, ${pillarCoords.z}), adjusted Y: ${pillarCurvedFloorY.toFixed(3)}`);
                    } else {
                        console.error(`[LocalCoordinateTest][${roomData.id}] ❌ Failed to create stone pillar ${index + 1} group`);
                    }
                } catch (error) {
                    console.error(`[LocalCoordinateTest][${roomData.id}] Error creating stone pillar ${index + 1}:`, error);
                }
            });
        } else {
            console.warn(`[LocalCoordinateTest][${roomData.id}] Could not find ancient_stone_pillar prefab function`);
        }
        // --- END STONE PILLARS ---
    }


    // --- STONE PILLARS: Place in CROSS_SHAPE rooms with 100% chance ---
    if (roomData.shapeKey === 'CROSS_SHAPE') {
        console.log(`[LocalCoordinateTest][${roomData.id}] ✅ CROSS_SHAPE room detected! Placing stone pillars`);

        // Define pillar positions for CROSS_SHAPE rooms (moved away from walls for better spacing)
        const crossPillarPositions = [
            { x: 5.0, y: 0.0, z: 5.0 },     // SE corner - moved 1.45 units toward center (X) and 1.46 units toward center (Z)
            { x: 5.0, y: 0.0, z: -5.0 },    // NE corner - moved 1.45 units toward center (X) and 1.46 units toward center (Z)
            { x: -5.0, y: 0.0, z: -5.0 },   // NW corner - moved 1.49 units toward center (X) and 1.44 units toward center (Z)
            { x: -5.0, y: 0.0, z: 5.0 }     // SW corner - moved 1.49 units toward center (X) and 1.44 units toward center (Z)
        ];

        // Get ancient stone pillar prefab function (gray)
        const stonePillarPrefabFunc = getPrefabFunction('ancient_stone_pillar', 'interior');

        if (stonePillarPrefabFunc) {
            crossPillarPositions.forEach((pillarCoords, index) => {
                try {
                    const pillarOptions = {
                        seed: roomData.id * 1337 + 999 + index, // Deterministic seed per pillar (different from RECT_1X3)
                        isDestructible: true,
                        destructionEffect: 'collapse',
                        health: 1,
                        userData: {
                            placedViaLocalCoordinates: true,
                            localCoordinates: pillarCoords,
                            roomId: roomData.id,
                            roomShape: roomData.shapeKey,
                            pillarIndex: index
                        }
                    };

                    const pillarResult = stonePillarPrefabFunc(pillarOptions);
                    const pillarGroup = pillarResult && pillarResult.group ? pillarResult.group : pillarResult;

                    if (pillarGroup && pillarGroup instanceof THREE.Object3D) {
                        // Position using local coordinates
                        pillarGroup.position.set(pillarCoords.x, pillarCoords.y, pillarCoords.z);

                        // Adjust for floor curvature if needed
                        const pillarCurvedFloorY = _calculateFloorCurvatureHeight(pillarCoords.x, pillarCoords.z, pillarCoords.y, roomData);
                        pillarGroup.position.y = pillarCurvedFloorY;

                        // Add a distinctive name for easy identification
                        pillarGroup.name = `LocalCoordinateTestCrossPillar_${index}`;

                        // CRITICAL FIX: Add unique identifier to prevent cross-pillar destruction
                        const pillarUniqueId = `ancient_pillar_${roomData.id}_cross_${index}`;
                        pillarGroup.userData.uniqueId = pillarUniqueId;

                        roomGroup.add(pillarGroup);

                        // CRITICAL FIX: Only add the pillar group to collision meshes
                        // Child meshes should NOT be in collision array to prevent duplicate targeting
                        collisionMeshes.push(pillarGroup);

                        // Set up child meshes with parent references but DON'T add to collision array
                        pillarGroup.traverse(child => {
                            if (child.isMesh) {
                                // Ensure child meshes inherit destructible properties from parent
                                child.userData.isDestructible = false; // Child meshes are NOT directly destructible
                                child.userData.parentDestructible = pillarGroup; // Reference to parent for destruction
                                child.userData.parentUniqueId = pillarUniqueId; // Parent unique ID for reference
                                child.name = `${pillarUniqueId}_child_${child.id}`;
                                // DO NOT add child to collisionMeshes - only parent should be targetable
                            }
                        });

                        console.log(`[LocalCoordinateTest][${roomData.id}] ✅ Successfully placed cross pillar ${index + 1} at local coordinates (${pillarCoords.x}, ${pillarCoords.y}, ${pillarCoords.z}), adjusted Y: ${pillarCurvedFloorY.toFixed(3)}`);
                    } else {
                        console.error(`[LocalCoordinateTest][${roomData.id}] ❌ Failed to create cross pillar ${index + 1} group`);
                    }
                } catch (error) {
                    console.error(`[LocalCoordinateTest][${roomData.id}] Error creating cross pillar ${index + 1}:`, error);
                }
            });
        } else {
            console.warn(`[LocalCoordinateTest][${roomData.id}] Could not find ancient_stone_pillar prefab function`);
        }
        // --- END CROSS STONE PILLARS ---
    }
    // --- END Special Handling for Room 0 ---

    // --- DEBUG: Log room shape for pillar placement debugging ---
    console.log(`[RoomGenerator][${roomData.id}] Room shape: ${roomData.shapeKey} (checking for pillar placement)`);

    // --- 4. Add Wall Corner Ambient Occlusion ---
    console.log(`[generateRoomVisuals] Adding corner ambient occlusion for room ${roomData.id}`);
    const cornerAO = generateWallCornerAO(boundingBox, addedWallSegmentsData, roomData);
    if (cornerAO && cornerAO.length > 0) {
        cornerAO.forEach(corner => roomGroup.add(corner));
        console.log(`[GenVis][${roomData.id}] Added ${cornerAO.length} corner AO elements.`);
    }

    // --- 5. Add Room Effects (e.g., Mist and Floor Overlay) ---
    if (areaData.name === 'The Catacombs') {
        // Get scene reference from global dungeonHandler if available
        const scene = (window.dungeonHandler && window.dungeonHandler.scene) ? window.dungeonHandler.scene : null;

        // Add floor mist with slightly more transparent opacity
        const mistPlanes = createFloorMistPlanes(boundingBox, 1.0, 3, 0.02, floorSegments, scene); // Reduced from 0.03 to 0.02 for more transparency
        roomGroup.add(mistPlanes);
        console.log(`[GenVis][${roomData.id}] Added floor mist planes for Catacombs with ${scene ? 'floor geometry stencil' : 'simple bounds'} (opacity: 0.02).`);
    }
    // --- END Floor Fog ---

    console.log(`[generateRoomVisuals] Completed visuals for room ${roomData.id}. Meshes: ${collisionMeshes.length}, Lights: ${lights.length}`);

    // CRITICAL FIX: Store wall segment data for torch placement validation
    if (window.dungeonHandler && typeof window.dungeonHandler._setCurrentRoomWallSegments === 'function') {
        window.dungeonHandler._setCurrentRoomWallSegments(addedWallSegmentsData);
    }

    // CRITICAL FIX: Store floor mesh data for floor object placement validation
    if (window.dungeonHandler && typeof window.dungeonHandler._setCurrentRoomFloorMeshes === 'function') {
        // Filter collision meshes to get only floor meshes
        const floorMeshes = collisionMeshes.filter(mesh => mesh.userData && mesh.userData.isFloor);
        window.dungeonHandler._setCurrentRoomFloorMeshes(floorMeshes);
    }

    // Store validated floor positions for enemy spawning
    const floorPositionsForEnemies = validFloorPositions || [];

    return { roomGroup, collisionMeshes, lights, boundingBox, doorCenterPoints, validFloorPositions: floorPositionsForEnemies };
}

// Helper function to calculate offset positions for wall segments beside doors
function calculateOffsetPosition(basePosition, rotation, offsetX, offsetZ) {
    // Create a rotation quaternion
    const rotationQuat = new THREE.Quaternion().setFromEuler(
        new THREE.Euler(0, rotation, 0)
    );

    // Create offset vector and apply rotation
    const offsetVec = new THREE.Vector3(offsetX, 0, offsetZ);
    offsetVec.applyQuaternion(rotationQuat);

    // Return new position
    return new THREE.Vector3(
        basePosition.x + offsetVec.x,
        basePosition.y,
        basePosition.z + offsetVec.z
    );
}

// --- NEW: Helper function to calculate room bounding box ---
// This simplifies getting bounds for different shapes
function _calculateRoomBoundingBox(shapeKey, roomWidth, roomDepth, customSize = null) {
    const halfWidth = roomWidth / 2;
    const halfDepth = roomDepth / 2;
    const R = customSize || ROOM_WORLD_SIZE;
    const H = R / 2;
    const B = 2 * R; // Boss room size multiplier

    let minX, maxX, minZ, maxZ;

    switch (shapeKey) {
        case 'BOSS_ARENA':
            minX = -B;  // Use full boss room size
            maxX = B;
            minZ = -B;
            maxZ = B;
            break;
        case 'RECTANGULAR': case 'RECT_2X1': case 'RECT_1X2':
        case 'SQUARE_1X1': case 'SQUARE_2X2':
        case 'RECT_3X1': case 'RECT_1X3': case 'RECT_3X2':
            minX = -halfWidth; maxX = halfWidth;
            minZ = -halfDepth; maxZ = halfDepth;
            break;
        case 'L_SHAPE': // L-shape with vertical part on left and horizontal part on bottom
            minX = -R; maxX = R;  // -14 to 14
            minZ = -R; maxZ = R;  // -14 to 14
            break;
        case 'T_SHAPE': // Assumes origin at junction
            minX = -(R + H); maxX = R + H; // -21 to 21
            minZ = -R; maxZ = R;      // -14 to 14
            break;
        case 'CROSS_SHAPE': // Assumes origin at center
            minX = -(R+H); maxX = R+H; // -21 to 21
            minZ = -(R+H); maxZ = R+H; // -21 to 21
            break;
        case 'U_SHAPE_DOWN': // U-shape with opening at bottom
            minX = -(R+H); maxX = R+H; // -21 to 21
            minZ = -R; maxZ = R;       // -14 to 14
            break;
        case 'U_SHAPE_UP': // U-shape with opening at top
            minX = -(R+H); maxX = R+H; // -21 to 21
            minZ = -R; maxZ = R;       // -14 to 14
            break;
        case 'U_SHAPE_LEFT': // U-shape with opening at left
            minX = -R; maxX = R;       // -14 to 14
            minZ = -(R+H); maxZ = R+H; // -21 to 21
            break;
        case 'U_SHAPE_RIGHT': // U-shape with opening at right
            minX = -R; maxX = R;       // -14 to 14
            minZ = -(R+H); maxZ = R+H; // -21 to 21
            break;
        case 'CORRIDOR_LONG': // Long corridor
            minX = -2*R; maxX = 2*R;   // -28 to 28
            minZ = -H; maxZ = H;       // -7 to 7
            break;
        case 'T_SHAPE_WIDE': // Wide T-shape (4x2)
            minX = -2*R; maxX = 2*R;   // -28 to 28
            minZ = -R; maxZ = R;       // -14 to 14
            break;
        case 'CORRIDOR_SHORT': // Short corridor (2x1)
            minX = -R; maxX = R;       // -14 to 14
            minZ = -H; maxZ = H;       // -7 to 7
            break;
        case 'SQUARE_2X2': // Large square (2x2)
            minX = -R; maxX = R;       // -14 to 14
            minZ = -R; maxZ = R;       // -14 to 14
            break;
        case 'CIRCLE': // Circular room - use square bounds for simplicity
            const circleRadius = R / 2;
            minX = -circleRadius; maxX = circleRadius;
            minZ = -circleRadius; maxZ = circleRadius;
            break;
        default:
            console.warn(`[CalcBounds] Unknown shapeKey: ${shapeKey}. Defaulting to SQUARE_1X1.`);
            minX = -H; maxX = H; minZ = -H; maxZ = H;
            break;
    }

    return { minX, maxX, minZ, maxZ, width: maxX - minX, depth: maxZ - minZ };
}
// --- END Helper ---



// REMOVED: _validateWallCollisionCoverage function was not implementing any actual validation
// All cases returned empty arrays. If wall collision gap-filling is needed in the future,
// implement proper collision detection and gap analysis here.

/**
 * Generates wall corner ambient occlusion elements
 * @param {Object} boundingBox - Room bounding box
 * @param {Array} wallSegments - Array of wall segment data
 * @param {Object} roomData - Room data
 * @returns {Array} Array of corner AO mesh objects
 */
function generateWallCornerAO(boundingBox, wallSegments, roomData) {
    const cornerAOElements = [];

    // Define corner positions based on room bounds
    const corners = [
        { x: boundingBox.minX, z: boundingBox.minZ, name: 'SW' }, // Southwest
        { x: boundingBox.maxX, z: boundingBox.minZ, name: 'SE' }, // Southeast
        { x: boundingBox.minX, z: boundingBox.maxZ, name: 'NW' }, // Northwest
        { x: boundingBox.maxX, z: boundingBox.maxZ, name: 'NE' }  // Northeast
    ];

    const cornerSize = 0.3; // Size of corner AO elements
    const cornerHeight = WALL_HEIGHT;

    corners.forEach(corner => {
        // Check if this corner has walls on both adjacent sides
        const hasWallNearby = wallSegments.some(wall => {
            const distance = Math.sqrt(
                Math.pow(wall.position.x - corner.x, 2) +
                Math.pow(wall.position.z - corner.z, 2)
            );
            return distance < 2.0; // Within 2 units of corner
        });

        if (hasWallNearby) {
            // Create corner AO geometry
            const cornerGeometry = new THREE.BoxGeometry(cornerSize, cornerHeight, cornerSize);

            // Create darker material for ambient occlusion effect
            const cornerMaterial = new THREE.MeshLambertMaterial({
                color: 0x1a1a1a, // Very dark gray
                transparent: true,
                opacity: 0.6
            });

            const cornerMesh = new THREE.Mesh(cornerGeometry, cornerMaterial);
            cornerMesh.position.set(corner.x, cornerHeight / 2, corner.z);
            cornerMesh.userData.isCornerAO = true;
            cornerMesh.userData.cornerName = corner.name;
            cornerMesh.name = `cornerAO_${corner.name}`;

            // Enable shadows
            cornerMesh.castShadow = false; // Don't cast shadows to avoid double-darkening
            cornerMesh.receiveShadow = true;

            cornerAOElements.push(cornerMesh);
        }
    });

    return cornerAOElements;
}

console.log("roomGenerator.js loaded - Refactored for areaData and interior objects");