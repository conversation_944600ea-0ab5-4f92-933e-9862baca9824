import * as THREE from 'three';
// import { FontLoader } from 'three/addons/loaders/FontLoader.js';
// import { TextGeometry } from 'three/addons/geometries/TextGeometry.js';
import { STATE } from '../constants.js';
// import { createTextMesh } from '../utils/textUtils.js'; // Assuming text helpers are moved

class HeroPageHandler {
    constructor(sceneManager) {
        this.sceneManager = sceneManager;
        this.scene = null; // Will be set in init
        // this.titleMesh = null; // Removed
        this.logoMesh = null;
        this.logoTexture = null;
        this.interactableObjects = [];
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.blinkTime = 0;
        this.rotateMessageElement = null; // Add reference for rotate message
        this.isStarting = false; // <<< ADD FLAG
        this.boundHandleOrientationChange = this._handleOrientationChange.bind(this); // Bind listener
        this.boundOnKeyDown = this.onKeyDown.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnClick = this.onClick.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnTouchEnd = this.onTouchEnd.bind(this); // <<< STORE BOUND FUNCTION
        this.boundOnMouseMove = this.onMouseMove.bind(this); // <<< STORE BOUND FUNCTION

        // DOM elements for video overlay - REMOVED FROM HERE
        // this.videoOverlay = document.getElementById('video-overlay');
        // this.introVideo = document.getElementById('intro-video');

        this.zoomAnimation = {
            active: false,
            startTime: 0,
            duration: 1200, // ms
            startPosition: new THREE.Vector3(),
            endPosition: new THREE.Vector3(0, -0.5, 1.5) // Zoom target near button
        };
    }

    /**
     * Get the optimal loop point for the main theme to avoid long pauses
     * For a 37-second track, loop at 36.4 seconds for optimal timing
     * @returns {number} Time in seconds where the loop should restart
     */
    _getMainThemeLoopPoint() {
        // For a 37-second track, loop at 36.4 seconds
        // This provides the perfect loop timing for seamless playback
        return 36.4;
    }

    async loadAssets() {
        const textureLoader = new THREE.TextureLoader();
        // const fontLoader = new FontLoader(); // Removed

        const promises = [];

        // Remove font loading
        /*
        if (!this.sceneManager.font) {
            console.log("HeroPageHandler: Loading font...");
            promises.push(
                fontLoader.loadAsync('assets/fonts/helvetiker_bold.typeface.json')
                    .then(font => {
                        console.log("HeroPageHandler: Font loaded.");
                        this.sceneManager.font = font;
                    })
                    .catch(err => {
                        console.error('ERROR loading font in HeroPageHandler:', err);
                        throw err; 
                    })
            );
        }
        */

        // Load Logo Texture
        console.log("HeroPageHandler: Loading logo texture...");
        promises.push(
            textureLoader.loadAsync('assets/textures/logo.png') // <<< CORRECTED PATH
                .then(texture => {
                    console.log("HeroPageHandler: Logo texture loaded.");
                    this.logoTexture = texture;
                    // Set filtering if needed (e.g., for pixel art)
                    // this.logoTexture.minFilter = THREE.NearestFilter;
                    // this.logoTexture.magFilter = THREE.NearestFilter;
                })
                .catch(err => {
                    console.error('ERROR loading logo texture in HeroPageHandler:', err);
                    // Handle texture loading error (e.g., maybe proceed without logo?)
                    throw err; // Re-throw for now
                })
        );

        await Promise.all(promises);
        console.log("HeroPageHandler: All assets loaded.");
    }

    async init(scene) {
        console.log("HeroPageHandler: Initializing...");
        this.scene = scene;
        this.scene.background = new THREE.Color(0x000000);

        // Reset blink timer
        this.blinkTime = 0;

        await this.loadAssets(); // Ensure assets are loaded

        // Remove font check
        /*
        if (!this.sceneManager.font) {
             console.error("HeroPageHandler: Font not available for title, cannot proceed.");
             return;
        }
        */
        if (!this.logoTexture) {
            console.error("HeroPageHandler: Logo texture not available, cannot create start button.");
            // Maybe show an error state?
            return;
        }

        // --- Remove Title Text ---
        /*
        const titleOptions = {
            fontStyle: 'bold 80px sans-serif',
            fontColor: '#00FFFF',
            emissiveColor: 0x00ffff,
            emissiveIntensity: 0.4
        };
        this.titleMesh = createTextMesh("Get Isekai'd", this.sceneManager.font, titleOptions, 0.015);
        this.titleMesh.position.y = 1.5; 
        this.scene.add(this.titleMesh);
        */

        // --- Start Button Logo ---
        const logoMaterial = new THREE.MeshBasicMaterial({
            map: this.logoTexture,
            transparent: true, // Needed for opacity animation and PNG transparency
            alphaTest: 0.1 // Adjust if needed to prevent transparent edges from blocking raycast
            // side: THREE.DoubleSide // If needed
        });

        // Determine aspect ratio to scale plane correctly
        const aspect = this.logoTexture.image.naturalWidth / this.logoTexture.image.naturalHeight;
        const logoHeight = 3.5; // <<< Slightly reduced size
        const logoWidth = logoHeight * aspect;

        const logoGeometry = new THREE.PlaneGeometry(logoWidth, logoHeight);
        this.logoMesh = new THREE.Mesh(logoGeometry, logoMaterial);

        // Set position (adjust z if needed so it's in front of background)
        this.logoMesh.position.set(0, 0, 0.1); // <<< Centered vertically

        // Store original scale for hover effect
        this.logoMesh.userData = {
            isButton: true,
            action: 'start_character_creation',
            originalScale: this.logoMesh.scale.clone() // Store initial scale
        };

        this.scene.add(this.logoMesh);

        // Make the logo interactable
        this.interactableObjects = [this.logoMesh];

        console.log("HeroPageHandler: Initialized with logo button.");

        // Add event listeners specific to this scene
        window.addEventListener('mousemove', this.boundOnMouseMove, false);
        window.addEventListener('click', this.boundOnClick, false);
        window.addEventListener('keydown', this.boundOnKeyDown, false);

        // Add touch event listeners for mobile
        window.addEventListener('touchend', this.boundOnTouchEnd, false);

        // Debug: Add global touch listener to see if touches are being detected
        this.debugTouchHandler = (e) => {
            console.log("Global touch detected:", e.type);
        };
        window.addEventListener('touchstart', this.debugTouchHandler, false);

        // Add multiple orientation detection methods for better reliability
        window.addEventListener('orientationchange', this.boundHandleOrientationChange);
        window.addEventListener('resize', this.boundHandleOrientationChange);

        // Create media query listener for more reliable detection
        this.portraitMediaQuery = window.matchMedia("(orientation: portrait)");
        if (this.portraitMediaQuery.addListener) {
            this.portraitMediaQuery.addListener(this.boundHandleOrientationChange);
        } else if (this.portraitMediaQuery.addEventListener) {
            this.portraitMediaQuery.addEventListener('change', this.boundHandleOrientationChange);
        }

        // Add touch listener specifically for mobile taps
        window.addEventListener('touchend', this.boundOnTouchEnd, false);

        // Get rotate message and blur overlay elements
        this.rotateMessageElement = document.getElementById('rotate-message');
        this.backgroundBlurOverlay = document.getElementById('background-blur-overlay');
        if (!this.rotateMessageElement) {
            console.warn("HeroPageHandler: Rotate message element not found.");
        }
        if (!this.backgroundBlurOverlay) {
            console.warn("HeroPageHandler: Background blur overlay element not found.");
        }

        // --- Initial Orientation Check ---
        this._handleOrientationChange(); // Check orientation on init
        // --------------------------------

        // Start playing main theme music on loop with custom timing
        if (this.sceneManager.audioManager) {
            console.log("HeroPageHandler: Starting main theme music");
            // Use custom loop end time to cut off silence/fade - adjust this value as needed
            const customLoopEnd = this._getMainThemeLoopPoint();
            this.sceneManager.audioManager.playSound('main_theme', true, 0.7, customLoopEnd);
        } else {
            console.warn("HeroPageHandler: AudioManager not found, cannot play main theme");
        }
    }

    cleanup() {
        console.log("HeroPageHandler: Cleaning up...");
        // Remove title mesh cleanup
        /*
        if (this.titleMesh) {
            this.scene.remove(this.titleMesh);
        }
        */
        if (this.logoMesh) {
            this.scene.remove(this.logoMesh);
            // Dispose geometry and material to free GPU memory
            this.logoMesh.geometry.dispose();
            this.logoMesh.material.dispose();
        }
         // Dispose texture if it's not used elsewhere (might be cached by Loader)
         if (this.logoTexture) {
             // this.logoTexture.dispose(); // Be cautious if texture is shared/cached
         }

        // Clean up video references - REMOVED FROM HERE
        // this.videoOverlay = null;
        // this.introVideo = null;

        // this.titleMesh = null; // Removed
        this.logoMesh = null;
        this.logoTexture = null;
        this.interactableObjects = [];

        // Remove event listeners
        window.removeEventListener('mousemove', this.boundOnMouseMove, false);
        window.removeEventListener('click', this.boundOnClick, false);
        window.removeEventListener('keydown', this.boundOnKeyDown, false);

        // Remove touch event listeners
        window.removeEventListener('touchend', this.boundOnTouchEnd, false);
        if (this.debugTouchHandler) {
            window.removeEventListener('touchstart', this.debugTouchHandler, false);
        }

        window.removeEventListener('orientationchange', this.boundHandleOrientationChange);
        window.removeEventListener('resize', this.boundHandleOrientationChange);

        // Remove media query listener
        if (this.portraitMediaQuery) {
            if (this.portraitMediaQuery.removeListener) {
                this.portraitMediaQuery.removeListener(this.boundHandleOrientationChange);
            } else if (this.portraitMediaQuery.removeEventListener) {
                this.portraitMediaQuery.removeEventListener('change', this.boundHandleOrientationChange);
            }
        }

        window.removeEventListener('touchend', this.boundOnTouchEnd, false);

        // Hide rotate message and blur overlay if shown
        if (this.rotateMessageElement) {
            this.rotateMessageElement.classList.remove('active');
        }
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.remove('active');
        }

        // Reset cursor
        document.body.style.cursor = 'default';

        // Stop main theme music if still playing
        if (this.sceneManager.audioManager) {
            this.sceneManager.audioManager.stopSound('main_theme');
        }

        console.log("HeroPageHandler: Cleaned up.");

        this._startGame();
    }

    update(deltaTime, scene, camera) {
        this.blinkTime += deltaTime;

        // Fading Blink Animation for Logo
        if (this.logoMesh && this.logoMesh.material) {
            // Simple sine wave blink: oscillates between 0.5 and 1.0 opacity over ~2 seconds
            const blinkSpeed = Math.PI; // Adjust speed (higher value = faster blink)
            const minOpacity = 0.5;
            const maxOpacity = 1.0;
            const range = maxOpacity - minOpacity;
            this.logoMesh.material.opacity = minOpacity + (Math.sin(this.blinkTime * blinkSpeed) + 1) / 2 * range;
        }

        // Update zoom animation if active
        if (this.zoomAnimation.active) {
            const elapsed = this.sceneManager.clock.getElapsedTime() - this.zoomAnimation.startTime;
            let progress = Math.min(elapsed / (this.zoomAnimation.duration / 1000), 1);
            progress = progress * progress * (3 - 2 * progress); // Smoothstep

            camera.position.lerpVectors(this.zoomAnimation.startPosition, this.zoomAnimation.endPosition, progress);

            if (progress >= 1) {
                console.log("HeroPage zoom finished.");
                this.zoomAnimation.active = false;
                // Transition is handled by the fade overlay logic in SceneManager now
                // The SceneManager will call changeState based on fade completion
            }
        }
        // Other hero page specific animations (like title pulse maybe)
    }

    // --- NEW: Public method to trigger start from SceneManager ---
    triggerStartGame() {
        console.log("HeroPageHandler: triggerStartGame called.");
        this._startGame();
    }
    // --- END NEW METHOD ---

    onMouseMove(event) {
        if (this.zoomAnimation.active) return; // Ignore hover during zoom

        // Always allow mouse tracking for raycasting, even when rotate message is showing
        // This ensures logo detection works in portrait mode
        this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = - (event.clientY / window.innerHeight) * 2 + 1;
        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);

        // Only apply hover effects if rotate message is NOT showing
        if (this.rotateMessageElement?.classList.contains('active')) {
            return; // Skip hover effects but allow raycasting
        }

        const intersects = this.raycaster.intersectObjects(this.interactableObjects);

        // Reset scale/cursor before checking intersects
        let intersectedButton = null;
        this.interactableObjects.forEach(obj => {
            if (obj && obj.userData.isButton && obj.userData.originalScale) {
                 // Check if scale exists before trying to copy (it should after init)
                 if (obj.scale) {
                    obj.scale.copy(obj.userData.originalScale);
                 }
            }
        });
        document.body.style.cursor = 'default';

        if (intersects.length > 0) {
            // Check the first *visible* intersected object
             for (let i = 0; i < intersects.length; i++) {
                const intersectedObject = intersects[i].object;
                 // Check if the object is visible and is a button before reacting
                 if (intersectedObject.visible && intersectedObject.userData.isButton) {
                    intersectedButton = intersectedObject;
                    break; // Found the topmost interactable button
                 }
            }
        }
        
        // Apply hover effect only to the intersected button
         if (intersectedButton && intersectedButton.userData.originalScale) {
             intersectedButton.scale.copy(intersectedButton.userData.originalScale).multiplyScalar(1.15); // Apply hover scale
             document.body.style.cursor = 'pointer';
         }
    }

    onClick(event) {
        // Allow logo interaction even in portrait mode - don't block here
        // The _handleTapOrClick method will handle portrait mode logic

        // This handles actual mouse clicks
        this._handleTapOrClick(event);
    }

    onTouchEnd(event) {
        // Allow logo interaction even in portrait mode - don't block here
        // The _handleTapOrClick method will handle portrait mode logic

        // Debug: Log touch events
        console.log("Touch end event detected in HeroPageHandler");
        const touch = event.changedTouches && event.changedTouches[0];
        if (touch) {
            console.log(`Touch coordinates: x=${touch.clientX}, y=${touch.clientY}`);
        }

        // This handles the end of a touch interaction (potential tap)
        // Prevent default if needed, e.g., to stop emulated clicks
        // event.preventDefault(); // <<< REMOVED to fix desktop clicks
        this._handleTapOrClick(event);
    }

    _handleTapOrClick(event) {
        console.log(`_handleTapOrClick triggered by event type: ${event.type}`);

        // --- REMOVED TEMPORARY VISUAL FEEDBACK --- 
        /*
        if (this.logoMesh && this.logoMesh.material) {
            const originalColor = this.logoMesh.material.color.clone();
            this.logoMesh.material.color.setHex(0xff0000); // Flash Red
            // Ensure opacity is high enough to see the flash
            const originalOpacity = this.logoMesh.material.opacity;
            this.logoMesh.material.opacity = 1.0;

            setTimeout(() => {
                 if (this.logoMesh && this.logoMesh.material) { // Check if still exists
                    this.logoMesh.material.color.copy(originalColor);
                    this.logoMesh.material.opacity = originalOpacity; // Restore opacity
                 }
            }, 150); // Duration of the flash
        }
        */
        // --- END FEEDBACK REMOVAL ---

        if (this.zoomAnimation.active) {
            console.log("Ignoring tap/click: Zoom animation active.");
            return; // Ignore click/tap during zoom
        }

        let clientX, clientY;

        // Determine coordinates based on event type
        if (event.type === 'click' || event.type === 'mousedown' || event.type === 'mouseup' || event.type === 'mousemove') {
            clientX = event.clientX;
            clientY = event.clientY;
        } else if (event.type === 'touchend' || event.type === 'touchstart' || event.type === 'touchmove') {
            // Use changedTouches for touchend to get the last touch point
            const touch = event.changedTouches && event.changedTouches[0];
            if (!touch) {
                console.log("Ignoring tap: No touch data found in touchend event.");
                return; // No touch data
            }
            clientX = touch.clientX;
            clientY = touch.clientY;
        } else {
            console.log(`Ignoring event: Unhandled type ${event.type}`);
            return; // Unhandled event type
        }

        // Use the determined coordinates for raycasting
        this.mouse.x = (clientX / window.innerWidth) * 2 - 1;
        this.mouse.y = - (clientY / window.innerHeight) * 2 + 1;

        // Debug: Log mouse coordinates and orientation
        const isPortrait = window.matchMedia("(orientation: portrait)").matches;
        console.log(`Mouse coords: x=${this.mouse.x.toFixed(3)}, y=${this.mouse.y.toFixed(3)}, portrait=${isPortrait}`);
        console.log(`Screen size: ${window.innerWidth}x${window.innerHeight}`);
        console.log(`Logo mesh exists: ${!!this.logoMesh}, interactable objects: ${this.interactableObjects.length}`);

        this.raycaster.setFromCamera(this.mouse, this.sceneManager.camera);
        const intersects = this.raycaster.intersectObjects(this.interactableObjects);
        console.log(`Raycaster intersects: ${intersects.length}`);

        let logoClicked = false;
        if (intersects.length > 0) {
            for (let i = 0; i < intersects.length; i++) {
                const intersectedObject = intersects[i].object;
                console.log(`Intersected object ${i}:`, intersectedObject.name || intersectedObject.uuid);
                 if (intersectedObject.visible && intersectedObject.userData.isButton && intersectedObject === this.logoMesh) {
                    logoClicked = true;
                    console.log("Intersection with logo confirmed.");
                    break;
                 }
            }
        }

        if (logoClicked) {
            console.log("Start Logo interaction detected.");

            // Check if rotate message is already showing
            const rotateMessageShowing = this.rotateMessageElement?.classList.contains('active');

            if (rotateMessageShowing) {
                console.log("Logo tapped while rotate message is showing - keeping message visible.");
                // Don't hide the message, keep it showing until orientation changes
                return;
            }

            // --- Mobile Portrait Check ---
            const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
            const isPortrait = window.matchMedia("(orientation: portrait)").matches;
            console.log(`Device Check: likelyMobile=${likelyMobile}, isPortrait=${isPortrait}`);

            if (likelyMobile && isPortrait) {
                console.log("Mobile portrait detected after logo tap. Showing rotate message.");
                this.showMobileRotateMessage();
                // Don't start game - wait for landscape rotation
            } else {
                console.log("Attempting to start game (Desktop or Mobile Landscape).");
                 // Hide rotate message and blur overlay if they were somehow visible
                 if (this.rotateMessageElement) {
                    this.rotateMessageElement.classList.remove('active');
                 }
                 if (this.backgroundBlurOverlay) {
                    this.backgroundBlurOverlay.classList.remove('active');
                 }
                // Ensure we don't double-start if called by both touch and click
                if (!this.zoomAnimation.active) {
                     this._startGame(); // Proceed to start game
                }
            }
            // --- End Mobile Check ---

        } else {
             console.log("Interaction was not with the logo or no intersection.");
             // Only hide rotate message if user clicks/taps elsewhere (not on logo)
             if (this.rotateMessageElement && !this.rotateMessageElement.classList.contains('active')) {
                // Don't hide if message is showing - let orientation change handle it
                if (this.rotateMessageElement) {
                    this.rotateMessageElement.classList.remove('active');
                    // Reset pointer events
                    this.rotateMessageElement.style.pointerEvents = '';
                }
                if (this.backgroundBlurOverlay) {
                    this.backgroundBlurOverlay.classList.remove('active');
                    // Reset pointer events
                    this.backgroundBlurOverlay.style.pointerEvents = '';
                }
             }
        }
    }

    onKeyDown(event) {
        // Block keyboard input if rotate message is showing (but allow logo clicks/taps)
        if (this.rotateMessageElement?.classList.contains('active')) {
            return;
        }

        if (event.key === 'Enter') {
            console.log("Enter key pressed on Hero Page.");
             // Hide rotate message and blur overlay if they were shown
             if (this.rotateMessageElement) {
                this.rotateMessageElement.classList.remove('active');
                // Reset pointer events
                this.rotateMessageElement.style.pointerEvents = '';
             }
             if (this.backgroundBlurOverlay) {
                this.backgroundBlurOverlay.classList.remove('active');
                // Reset pointer events
                this.backgroundBlurOverlay.style.pointerEvents = '';
             }
            this._startGame();
        }
    }

    _startGame() {
        // <<< ADD CHECK FOR FLAG >>>
        if (this.isStarting) {
            console.log("_startGame called again, but already starting. Ignoring.");
            return;
        }
        this.isStarting = true; // <<< SET FLAG
        console.log("HeroPageHandler: _startGame running...");

        // Stop main theme music before playing start button sound
        if (this.sceneManager.audioManager) {
            console.log("HeroPageHandler: Stopping main theme music");
            this.sceneManager.audioManager.stopSound('main_theme');
        }

        // Trigger zoom animation
        this.zoomAnimation.active = true;
        this.zoomAnimation.startTime = this.sceneManager.clock.getElapsedTime();
        this.zoomAnimation.startPosition.copy(this.sceneManager.camera.position);

        // Play sound (Assuming SceneManager has an audio manager)
        // Ensure audioManager exists before trying to play
        if (this.sceneManager.audioManager) {
             this.sceneManager.audioManager.playSound('start_button');
        } else {
            console.warn("AudioManager not found on SceneManager in HeroPageHandler");
        }

        // Trigger fade in SceneManager, and change state directly in callback
        this.sceneManager.startFade(() => {
            // This callback executes *after* the screen is black
            this.sceneManager.changeState(STATE.CHARACTER_CREATION);
        });
    }

    // --- Add Orientation Change Handler ---
    _handleOrientationChange() {
        // Add a small delay to ensure orientation has fully changed
        setTimeout(() => {
            this._doOrientationCheck();
        }, 100);
    }

    _doOrientationCheck() {
        // --- New Logic: Show/Hide based purely on orientation ---
        const likelyMobile = ('ontouchstart' in window) || (navigator.maxTouchPoints > 0);
        if (likelyMobile && this.rotateMessageElement) {
            const isPortrait = window.matchMedia("(orientation: portrait)").matches;
            console.log(`[HeroPageHandler] Orientation check: ${isPortrait ? 'Portrait' : 'Landscape'}`);

            if (isPortrait) {
                // Only show rotate message if user hasn't tapped logo yet
                // (The logo tap handler will show it if needed)
                console.log("Orientation: Portrait (Mobile) - Ready to show rotate message on logo tap.");
            } else {
                // Hide blur overlay and rotate message with fade out
                const wasShowingRotateMessage = this.rotateMessageElement.classList.contains('active');

                if (this.backgroundBlurOverlay) {
                    this.backgroundBlurOverlay.classList.remove('active');
                    // Reset pointer events
                    this.backgroundBlurOverlay.style.pointerEvents = '';
                }
                this.rotateMessageElement.classList.remove('active');
                // Reset pointer events
                this.rotateMessageElement.style.pointerEvents = '';

                // If we were showing the rotate message (after logo tap), start the game now
                if (wasShowingRotateMessage && !this.zoomAnimation.active) {
                    console.log("Orientation: Landscape (Mobile) - Starting game after rotation.");
                    this._startGame();
                } else {
                    console.log("Orientation: Landscape (Mobile) - Hiding rotate message and blur.");
                }
            }
        } else if (this.rotateMessageElement) {
            // Ensure message and blur are hidden on desktop
            if (this.backgroundBlurOverlay) {
                this.backgroundBlurOverlay.classList.remove('active');
                // Reset pointer events
                this.backgroundBlurOverlay.style.pointerEvents = '';
            }
            this.rotateMessageElement.classList.remove('active');
            // Reset pointer events
            this.rotateMessageElement.style.pointerEvents = '';
        }
        // --- End New Logic ---
    }

    // --- Mobile Rotate Message Method ---
    showMobileRotateMessage() {
        console.log("Showing mobile rotate message with blur overlay after logo tap");

        // Show blur overlay and rotate message
        if (this.backgroundBlurOverlay) {
            this.backgroundBlurOverlay.classList.add('active');
            // Ensure the blur overlay doesn't block pointer events to the logo
            this.backgroundBlurOverlay.style.pointerEvents = 'none';
        }
        if (this.rotateMessageElement) {
            this.rotateMessageElement.classList.add('active');
            // Ensure the rotate message doesn't block pointer events to the logo
            this.rotateMessageElement.style.pointerEvents = 'none';
        }
    }
    // --- End Orientation Change Handler ---
}

export default HeroPageHandler; 