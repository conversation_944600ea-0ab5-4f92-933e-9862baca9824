import SceneManager from './src/core/SceneManager.js';

// --- Global Variables (that might be needed across modules or for debugging) ---
// Keep essential variables if needed, otherwise move them into relevant modules.
// Example: Maybe keep font globally accessible initially, though it's better managed.
let font = null;

// --- Constants (if not moved to constants.js yet) ---
// Remove STATE object, questions_data, dialogues, etc., they will be handled by specific modules.

// --- FPS Monitor ---
class FPSMonitor {
    constructor() {
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
        this.startTime = performance.now();
        
        // Start monitoring
        this.monitor();
        
        // Log FPS every 5 seconds
        setInterval(() => {
            console.log(`[FPS Monitor] Current FPS: ${this.fps.toFixed(1)} | Uptime: ${((performance.now() - this.startTime) / 1000).toFixed(1)}s`);
        }, 5000);
    }
    
    monitor() {
        this.frameCount++;
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        // Calculate FPS every second
        if (deltaTime >= 1000) {
            this.fps = (this.frameCount * 1000) / deltaTime;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
        
        requestAnimationFrame(() => this.monitor());
    }
}

// --- Core Logic ---
async function main() {
    console.log("Starting application...");

    try {
        // Initialize worker system first
        console.log("🔧 Initializing worker thread optimization system...");
        const { initializeWorkerSystem } = await import('./src/examples/WorkerIntegrationExample.js');
        const workerSystem = await initializeWorkerSystem();
        console.log("✅ Worker system initialized successfully");

        // Create scene manager
        const sceneManager = new SceneManager();

        // Make sceneManager globally accessible for debugging
        window.sceneManager = sceneManager;

        // Initialize FPS monitor
        const fpsMonitor = new FPSMonitor();

        // TODO: Load assets here (like the font) before starting the SceneManager fully
        // For now, SceneManager's start method handles placeholder loading

        await sceneManager.start();

    } catch (error) {
        console.error("❌ Failed to initialize application:", error);
        // Continue without workers if initialization fails
        console.log("⚠️ Continuing without worker optimization...");

        const sceneManager = new SceneManager();
        window.sceneManager = sceneManager;
        const fpsMonitor = new FPSMonitor();
        sceneManager.start();
    }

    // Apply the NES preset on game start and then customize specific values
    setTimeout(() => {
        if (sceneManager.consolePresetEffect && sceneManager.consolePresetEffect.manager) {
            console.log("Applying NES preset on game start");
            // First apply the NES preset
            sceneManager.consolePresetEffect.manager.applyPreset('nes');

            // Then customize specific values
            if (sceneManager.hdrFramerateEffect && sceneManager.hdrFramerateEffect.manager) {
                console.log("Customizing visual settings");
                // First apply the NES preset in HDR settings
                sceneManager.hdrFramerateEffect.manager.applyPreset('nes');
                // Force the current preset to be 'nes'
                sceneManager.hdrFramerateEffect.manager.currentPreset = 'nes';
                // Then override specific values
                // Set bit depth to 4.0
                sceneManager.hdrFramerateEffect.manager.adjustParameter('bitDepth', 4.0);
                // Set dither strength to 0.1 as requested
                sceneManager.hdrFramerateEffect.manager.adjustParameter('ditherStrength', 0.1);
                // Make sure dithering is enabled
                sceneManager.hdrFramerateEffect.manager.adjustParameter('ditherEnabled', true);
            }

            // HDR and framerate settings are applied first
            // We'll apply CRT settings after a short delay to ensure proper order

            // Update the control panels if they exist
            if (sceneManager.consolePresetEffect.controlPanel) {
                sceneManager.consolePresetEffect.controlPanel.updateControls();
            }

            // First update, then try again after a delay to ensure it takes effect
            if (sceneManager.hdrFramerateEffect && sceneManager.hdrFramerateEffect.controlPanel) {
                console.log("First attempt to force NES preset selection");
                sceneManager.hdrFramerateEffect.controlPanel.forceSelectPreset('nes');
                sceneManager.hdrFramerateEffect.controlPanel.updateSliders();

                // Try again after a short delay
                setTimeout(() => {
                    console.log("Second attempt to force NES preset selection");
                    sceneManager.hdrFramerateEffect.controlPanel.forceSelectPreset('nes');

                    // As a last resort, directly modify the DOM
                    const select = document.getElementById('hdr-preset-select');
                    if (select) {
                        console.log("Direct DOM manipulation to select NES preset");
                        // Try to find the NES option
                        for (let i = 0; i < select.options.length; i++) {
                            if (select.options[i].value === 'nes') {
                                select.selectedIndex = i;
                                // Trigger change event
                                const event = new Event('change');
                                select.dispatchEvent(event);
                                console.log(`Selected NES preset at index ${i} via direct DOM access`);
                                break;
                            }
                        }
                    }
                }, 500);
            }

            // Apply CRT settings after HDR settings with a delay to ensure proper initialization
            setTimeout(() => {
                if (sceneManager.crtEffect && sceneManager.crtEffect.manager) {
                    console.log("Applying Arcade Machine CRT preset");
                    // Apply preset first, then enable
                    sceneManager.crtEffect.manager.applyPreset('arcade_monitor');
                    // Force enable (using the new enable method)
                    sceneManager.crtEffect.manager.enable();
                    // Force the current preset to be 'arcade_monitor'
                    sceneManager.crtEffect.manager.currentPreset = 'arcade_monitor';
                    // Enable Line-By-Line Refresh visualization with speed 0.10
                    sceneManager.crtEffect.manager.enableRefreshVisualization(true, 0.10);

                    // Update the CRT control panel
                    if (sceneManager.crtEffect.controlPanel) {
                        // Force select the arcade_monitor preset in the UI
                        sceneManager.crtEffect.controlPanel.forceSelectPreset('arcade_monitor');
                        console.log("CRT control panel updated with arcade_monitor preset");
                    }
                }
            }, 1000); // 1 second delay after HDR settings
        }
    }, 2000); // Increased delay to ensure everything is fully initialized

    // Make sceneManager accessible globally for debugging if needed
    window.sceneManager = sceneManager;

    // Final check to ensure CRT settings are applied correctly
    setTimeout(() => {
        if (sceneManager.crtEffect && sceneManager.crtEffect.manager) {
            console.log("Final check for CRT settings");
            if (sceneManager.crtEffect.manager.currentPreset !== 'arcade_monitor' || !sceneManager.crtEffect.manager.enabled) {
                console.log("Re-applying Arcade Machine CRT preset");
                // Apply preset first, then enable
                sceneManager.crtEffect.manager.applyPreset('arcade_monitor');
                // Force enable (using the new enable method)
                sceneManager.crtEffect.manager.enable();
                // Force the current preset to be 'arcade_monitor'
                sceneManager.crtEffect.manager.currentPreset = 'arcade_monitor';
                sceneManager.crtEffect.manager.enableRefreshVisualization(true, 0.10);

                if (sceneManager.crtEffect.controlPanel) {
                    // Force select the arcade_monitor preset in the UI
                    sceneManager.crtEffect.controlPanel.forceSelectPreset('arcade_monitor');
                    console.log("Final check: CRT control panel updated with arcade_monitor preset");
                }
            }
        }
    }, 5000); // Check 5 seconds after initialization
}

// --- Initialization ---
// Ensure the DOM is ready before starting
if (document.readyState === 'loading') {  // Loading hasn't finished yet
    document.addEventListener('DOMContentLoaded', async () => {
        console.log("DOMContentLoaded event fired. Running main()...");
        await main();
    });
} else {  // `DOMContentLoaded` has already fired
    console.log("DOM was already ready. Running main()...");
    main().catch(console.error);
}

// --- Remove Old Code ---
// Delete all the functions like init, createHeroPage, createQuestionnaireScene,
// createCardRevealScene, animate, onWindowResize, onMouseMove, onClick,
// typeWriter, fadeTransition, clearScene, etc.
// Delete all the global variable declarations for scene, camera, renderer, meshes,
// animation states, interactableObjects, environmentObjects, etc.
// These responsibilities are being moved to SceneManager and specific scene handlers.
