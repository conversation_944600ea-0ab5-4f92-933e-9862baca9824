// Debug script to verify event room door positions

window.debugEventRoomDoors = function() {
    console.log('🚪 DEBUG: Event Room Door Positions');
    console.log('=====================================');
    
    if (!window.dungeonHandler || !window.dungeonHandler.currentRoomData) {
        console.log('❌ Must be in a dungeon room to debug doors');
        return;
    }
    
    const roomData = window.dungeonHandler.currentRoomData;
    console.log(`\n📍 Current Room: ${roomData.id}`);
    console.log(`   Type: ${roomData.type}`);
    console.log(`   Shape: ${roomData.shapeKey}`);
    
    if (roomData.type === 'EVENT' || roomData.type === 'Event') {
        console.log(`   Event Room: ${roomData.eventRoomName || 'Unknown'}`);
        console.log(`   Entrance Direction: ${roomData.entranceDirection || 'Not set'}`);
        
        if (roomData.eventRoomData) {
            console.log('\n🚪 Event Room Door Configuration:');
            console.log('   Available Connections:', roomData.eventRoomData.availableConnections);
            console.log('   Door Positions:', roomData.eventRoomData.doorPositions);
        }
    }
    
    console.log('\n🔗 Room Connections:', roomData.connections);
    
    // Check door objects in the scene
    console.log('\n🎭 Door Objects in Scene:');
    const roomGroup = window.dungeonHandler.currentRoomGroup;
    if (roomGroup) {
        const doors = [];
        roomGroup.traverse(child => {
            if (child.userData && child.userData.isDoor) {
                doors.push({
                    name: child.name,
                    position: {
                        x: child.position.x.toFixed(2),
                        y: child.position.y.toFixed(2),
                        z: child.position.z.toFixed(2)
                    },
                    rotation: (child.rotation.y * 180 / Math.PI).toFixed(0) + '°'
                });
            }
        });
        
        if (doors.length > 0) {
            doors.forEach(door => {
                console.log(`   - ${door.name}: pos(${door.position.x}, ${door.position.y}, ${door.position.z}) rot(${door.rotation})`);
            });
        } else {
            console.log('   No door objects found');
        }
    }
    
    // Calculate expected door positions
    console.log('\n📐 Expected Door Positions (based on room shape):');
    const R = 14; // ROOM_WORLD_SIZE
    const shapeKey = roomData.shapeKey;
    
    const expectedPositions = {};
    switch(shapeKey) {
        case 'SQUARE_1X1':
            expectedPositions.n = { wall: { x: 0, z: -7 }, door: { x: 0, z: -7 + 0.3 } };
            expectedPositions.s = { wall: { x: 0, z: 7 }, door: { x: 0, z: 7 - 0.3 } };
            expectedPositions.e = { wall: { x: 7, z: 0 }, door: { x: 7 - 0.3, z: 0 } };
            expectedPositions.w = { wall: { x: -7, z: 0 }, door: { x: -7 + 0.3, z: 0 } };
            break;
        case 'SQUARE_2X2':
            expectedPositions.n = { wall: { x: 0, z: -14 }, door: { x: 0, z: -14 + 0.3 } };
            expectedPositions.s = { wall: { x: 0, z: 14 }, door: { x: 0, z: 14 - 0.3 } };
            expectedPositions.e = { wall: { x: 14, z: 0 }, door: { x: 14 - 0.3, z: 0 } };
            expectedPositions.w = { wall: { x: -14, z: 0 }, door: { x: -14 + 0.3, z: 0 } };
            break;
    }
    
    Object.entries(expectedPositions).forEach(([dir, pos]) => {
        console.log(`   ${dir.toUpperCase()}: Wall at (${pos.wall.x}, ${pos.wall.z}), Door should be at (${pos.door.x}, ${pos.door.z})`);
    });
};

// Add to window for global access
window.debugEventRoomDoors.description = "Debug event room door positions";

console.log('🚪 Event room door debug loaded. Run debugEventRoomDoors() in a room.');