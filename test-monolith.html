<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monolith Test</title>
    <style>
        body { margin: 0; overflow: hidden; background: #000; }
        #info { position: absolute; top: 10px; left: 10px; color: white; font-family: monospace; }
    </style>
</head>
<body>
    <div id="info">Monolith Test - Check console for errors</div>
    
    <script type="importmap">
    {
        "imports": {
            "three": "./three.js/build/three.module.js",
            "three/addons/": "./three.js/examples/jsm/"
        }
    }
    </script>
    
    <script type="module">
        import * as THREE from 'three';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import { createRuneMonolith } from './src/generators/prefabs/runeMonolith.js';
        
        // Setup scene
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x202020);
        
        // Setup camera
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(10, 10, 10);
        
        // Setup renderer
        const renderer = new THREE.WebGLRenderer();
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        document.body.appendChild(renderer.domElement);
        
        // Add orbit controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.target.set(0, 4, 0);
        controls.update();
        
        // Add lighting
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(5, 10, 5);
        directionalLight.castShadow = true;
        scene.add(directionalLight);
        
        // Add ground plane
        const groundGeometry = new THREE.PlaneGeometry(20, 20);
        const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x303030 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        scene.add(ground);
        
        // Create monoliths in triangle formation
        try {
            const monolithRadius = 5.0;
            const monolithCount = 3;
            const angleOffset = (Math.PI * 2) / monolithCount;
            const startAngle = -Math.PI / 2;
            
            for (let i = 0; i < monolithCount; i++) {
                const angle = startAngle + (i * angleOffset);
                const x = Math.cos(angle) * monolithRadius;
                const z = Math.sin(angle) * monolithRadius;
                
                console.log(`Creating monolith ${i} at position (${x.toFixed(2)}, 0, ${z.toFixed(2)})`);
                
                const monolith = createRuneMonolith();
                monolith.position.set(x, 0, z);
                monolith.rotation.y = angle + Math.PI;
                scene.add(monolith);
            }
            
            console.log('✅ All monoliths created successfully!');
            document.getElementById('info').textContent = 'Monolith Test - Success! 3 monoliths created';
        } catch (error) {
            console.error('❌ Error creating monoliths:', error);
            document.getElementById('info').textContent = 'Monolith Test - Error: ' + error.message;
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }
        animate();
        
        // Handle resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>