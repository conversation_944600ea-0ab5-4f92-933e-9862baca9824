<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Potion Removal Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #4CAF50;
            color: white;
        }
        .error {
            background-color: #f44336;
            color: white;
        }
        .info {
            background-color: #2196F3;
            color: white;
        }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Health Potion Removal Test</h1>
        
        <div class="test-section">
            <h2>Test Status</h2>
            <p>Testing that health potions have been completely removed from the game.</p>
            <div id="test-status" class="status info">Testing...</div>
        </div>

        <div class="test-section">
            <h2>Available Items</h2>
            <div id="item-list" class="status info">Loading item list...</div>
        </div>

        <div class="test-section">
            <h2>Manual Test</h2>
            <p>In the game console, try these commands to verify health potions are gone:</p>
            <code style="display: block; background: #000; padding: 10px; color: #0f0;">
                itemlist() - Should not show health potions<br>
                give('health_potion') - Should fail or give error<br>
                give('soul_heart') - Should work (still available)
            </code>
        </div>
    </div>

    <script type="module">
        // Test health potion removal
        import { ITEM_TYPES, getItemData } from './src/entities/ItemTypes.js';

        document.addEventListener('DOMContentLoaded', function() {
            const testStatus = document.getElementById('test-status');
            const itemList = document.getElementById('item-list');
            
            try {
                // Check if HEALTH_POTION constant exists
                const hasHealthPotionConstant = 'HEALTH_POTION' in ITEM_TYPES;
                
                // Check if health_potion data exists
                const healthPotionData = getItemData('health_potion');
                
                // Get all available items
                const allItems = Object.keys(ITEM_TYPES).map(key => ITEM_TYPES[key]);
                const hasHealthPotionInList = allItems.includes('health_potion');
                
                // Test results
                if (!hasHealthPotionConstant && !healthPotionData && !hasHealthPotionInList) {
                    testStatus.textContent = '✅ SUCCESS: Health potions completely removed from game';
                    testStatus.className = 'status success';
                } else {
                    testStatus.textContent = '❌ FAILED: Health potions still exist in game';
                    testStatus.className = 'status error';
                }
                
                // Show available items
                let itemListHtml = '<h3>Available Items:</h3><ul>';
                allItems.forEach(itemType => {
                    const itemData = getItemData(itemType);
                    if (itemData) {
                        itemListHtml += `<li><strong>${itemData.name}</strong> (${itemType}) - ${itemData.category}</li>`;
                    }
                });
                itemListHtml += '</ul>';
                
                itemList.innerHTML = itemListHtml;
                itemList.className = 'status info';
                
            } catch (error) {
                testStatus.textContent = '❌ ERROR: ' + error.message;
                testStatus.className = 'status error';
                
                itemList.textContent = 'Error loading item list: ' + error.message;
                itemList.className = 'status error';
            }
        });
    </script>
</body>
</html>