# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## CRITICAL DEVELOPMENT RULES

### NO FALLBACK SYSTEMS
**NEVER implement fallback systems, fallback models, or backup mechanisms.**
- Our goal is always to make the primary system work perfectly
- Fallbacks create complexity and mask real problems
- When something doesn't work, fix it - don't work around it
- Examples of BANNED patterns:
  - Fallback AI models
  - Backup authentication methods
  - Alternative code paths "just in case"
  - Pattern matching when AI fails
- Build systems that work reliably, not systems that fail gracefully

### TESTING IMPLEMENTATION RULE
**When implementing tests, ALWAYS include tests that don't require starting the game or opening HTML files.**
- Create Node.js inline tests using `node -e "..."` for logic validation
- Use bash commands (`grep`, `test -f`, etc.) for file/code structure validation
- Implement standalone JavaScript functions that can run without browser context
- Test core logic, algorithms, and data structures independently
- Examples of preferred test patterns:
  - `node -e "const result = myFunction(testData); console.log(result === expected ? '✅ PASS' : '❌ FAIL')"`
  - `grep -q "expectedPattern" file.js && echo "✅ PASS" || echo "❌ FAIL"`
  - `test -f critical-file.js && echo "✅ EXISTS" || echo "❌ MISSING"`
- Always provide expected outputs for easy validation
- Make tests copy-pasteable and runnable from terminal

## Project Overview

Soulpath is a 3D dungeon crawler game built with Three.js featuring:
- Voxel-based graphics with retro visual effects (CRT, bit depth reduction)
- Mobile and desktop support with adaptive controls
- Music-reactive boss battles
- Card-based progression system
- Procedural dungeon generation

## Common Development Commands

### Testing and Development
```bash
# Run boss battle tests
./run-boss-test.sh          # Main boss battle test
./run-direct-test.sh        # Direct boss test
./run-simple-test.sh        # Simple boss test
./run-ultra-test.sh         # Ultra boss test

# Start local development server (manual)
# Open index.html in browser or use live server extension
```

### Development Tools
```bash
# Generate cards (Python script)
python generate_cards.py

# Debug commands available in browser console:
refreshCurrentRoom()        # Save state, hard refresh, auto-restore position
restoreFromSave()          # Manual restore if auto-restore fails
refreshAssets()            # Update asset cache buster
startChessGame(difficulty)  # Start Devil's Chess Game (difficulty: 'easy', 'normal', 'hard', 'expert')
```

### Recent Fixes
- **Minimap Connection Alignment**: Fixed U-shape positioning to align door connection points with grid for seamless connections without gaps
- **Minimap Gaps Fix**: Fixed black gaps between connected rectangular rooms (RECT_3X1, RECT_1X2, RECT_1X3) by adding proper positioning logic in DungeonHandler.js minimap rendering
- **Secret Wall System**: Enhanced to prevent blocking regular doors, ensure exactly one per stage, and avoid event/boss rooms
- **U-Shape Rendering**: Fixed U_SHAPE_RIGHT, U_SHAPE_UP, and U_SHAPE_LEFT SVG paths for correct minimap display (all four U-shapes now render perfectly)
- **Boss Room Layout**: Enforced exactly one south door entrance for boss rooms

### Testing Commands (No Game Launch Required)

#### Code Quality Tests
```bash
# Validate secret wall placement logic
node -e "
const mockRooms = [
  { id: 0, type: 'Normal', connections: { e: 1 } },
  { id: 1, type: 'Normal', connections: { w: 0, n: 2 } },
  { id: 2, type: 'Event', connections: { s: 1 } },
  { id: 3, type: 'Boss', connections: {} },
  { id: 4, type: 'Normal', connections: {} }
];
const eligible = mockRooms.filter(r => 
  r.id !== 0 && 
  !['Boss', 'Event', 'BOSS', 'EVENT'].includes(r.type)
);
console.log('✅ Secret wall eligible rooms:', eligible.map(r => r.id));
console.log('Expected: [1, 4] - rooms without doors and not boss/event');
"

# Test door blocking logic
node -e "
const testRoom = { id: 5, connections: { n: 3, e: null, w: 7, s: null } };
const freeWalls = ['n', 'e', 'w'].filter(dir => 
  !(testRoom.connections[dir] !== null && testRoom.connections[dir] !== undefined)
);
console.log('✅ Free walls for secret placement:', freeWalls);
console.log('Expected: [e] - only east wall is free');
"

# Validate room type filtering
node -e "
const rooms = [
  { id: 1, type: 'Normal' },
  { id: 2, type: 'Boss' },
  { id: 3, type: 'Event' },
  { id: 4, type: 'EVENT' },
  { id: 5, type: 'boss' }
];
const safe = rooms.filter(r => !['Boss', 'Event', 'BOSS', 'EVENT', 'boss', 'event'].includes(r.type));
console.log('✅ Safe rooms for secret walls:', safe.map(r => r.id));
console.log('Expected: [1] - only normal rooms allowed');
"
```

#### File Structure Validation
```bash
# Check critical secret room files exist
test -f src/systems/SecretRoomManager.js && echo "✅ SecretRoomManager.js exists" || echo "❌ Missing SecretRoomManager.js"
test -f src/generators/DungeonGenerator.js && echo "✅ DungeonGenerator.js exists" || echo "❌ Missing DungeonGenerator.js"

# Verify secret wall methods are implemented
grep -q "findSuitableSecretWallRelaxed" src/systems/SecretRoomManager.js && echo "✅ Relaxed placement method exists" || echo "❌ Missing relaxed placement"
grep -q "forceSecretWallPlacement" src/systems/SecretRoomManager.js && echo "✅ Force placement method exists" || echo "❌ Missing force placement"

# Check door connection protection
grep -q "connections.*null.*undefined" src/systems/SecretRoomManager.js && echo "✅ Door connection protection implemented" || echo "❌ Missing door protection"
```

#### Secret Room Logic Tests
```bash
# Test single secret room per area logic
node -e "
const areas = new Map();
function generateSecretRoom(area) {
  if (areas.has(area)) {
    console.log('❌ BLOCKED: Area already has secret room');
    return false;
  }
  areas.set(area, true);
  console.log('✅ CREATED: Secret room for', area);
  return true;
}
generateSecretRoom('The Catacombs');
generateSecretRoom('The Catacombs');
console.log('Expected: First succeeds, second is blocked');
"

# Validate minimap visibility
grep -q "display.*block" index.html && echo "✅ Minimap container is visible" || echo "❌ Minimap may be hidden"
grep -q "minimap-grid.*display.*block" index.html && echo "✅ Minimap grid is visible" || echo "❌ Minimap grid may be hidden"
```

#### Performance Tests
```bash
# Check for console.log statements that should be removed in production
echo "🔍 Checking for debug logs in SecretRoomManager:"
grep -c "console.log" src/systems/SecretRoomManager.js | xargs -I {} echo "Found {} debug logs (consider removing for production)"

# Verify no hardcoded test data
grep -i "test\|debug\|temp" src/systems/SecretRoomManager.js | head -3 | xargs -I {} echo "⚠️ Potential test code: {}"
```

## High-Level Architecture

### Core Structure
- **main.js**: Entry point, initializes SceneManager and applies visual effects
- **src/core/SceneManager.js**: Central game state manager, handles scene transitions and mobile controls
- **src/scenes/**: Scene handlers (HeroPageHandler, CharacterCreationHandler, DungeonHandler)

### Scene Management Flow
1. **HERO_PAGE**: Logo/start screen with cursor visible
2. **CHARACTER_CREATION**: Interactive dialogue system with mobile navigation
3. **DUNGEON**: Main gameplay with procedural rooms and combat

### Key Systems

#### Mobile Controls Architecture
- **Landscape**: Dual joysticks (left: movement, right: camera/interaction)
- **Portrait**: Invisible touch zones covering screen halves
- **Center Touch Zone**: Double-tap for camera toggle in dungeon
- Mobile input processed in SceneManager, passed to PlayerController

#### Visual Effects Pipeline
Effects are applied in this order:
1. **HDR/Framerate Effects**: Tone mapping, bit depth reduction, dithering
2. **CRT Effects**: Scanlines, distortion, refresh visualization
3. **Console Preset Effects**: 8-bit code visualization, unified presets
4. **Voxel Shadow Manager**: Custom shadow system for voxel graphics

#### Music System
- **BossMusicTimeline.js**: Synchronizes boss patterns with music analysis
- **MusicConductor.js**: Manages audio state and transitions
- **BufferedMusicController.js**: Handles music timing and buffering
- Located in `src/audio/` with supporting analyzers and effects

#### Dungeon Generation
- **DungeonGenerator.js**: Creates room layouts and connections
- **roomGenerator.js**: Generates 3D visuals for rooms
- **prefabs/**: Voxel-based object definitions (walls, floors, enemies, items)
- **AreaThemes.js**: Visual themes for different dungeon areas

#### AI and Combat
- **AIFactory.js**: Creates and manages enemy AI behaviors
- **GroupCoordinator.js**: Coordinates enemy group tactics
- **animations/**: Enemy-specific animation handlers
- **ProjectileTypes.js**: Projectile definitions and behaviors

#### Systems Architecture
Core game systems in `src/systems/`:
- **CardSystem.js**: Card-based progression mechanics
- **WeaponSystem.js**: Weapon mechanics and display
- **ItemDropManager.js**: Loot and drop mechanics
- **SecretRoomManager.js**: Hidden room discovery
- **EventRoomManager.js**: Special room encounters
- **LocalCoordinateSystem.js**: Coordinate transformation utilities

### Key Development Patterns

#### Prefab System
All game objects use the prefab system in `src/generators/prefabs/`:
- Objects defined as functions returning Three.js groups
- Shared materials and geometries cached for performance
- Voxel-based construction with `VOXEL_SIZE` constant

#### State Management
- Global state handled by SceneManager
- Each scene handler manages its own lifecycle (init, update, cleanup)
- Mobile input centralized in SceneManager, distributed to handlers

#### Effect Integration
Visual effects are integrated through manager classes:
- Effects can be enabled/disabled independently
- Presets apply coordinated settings across multiple effects
- Each effect has its own control panel for debugging

#### Asset Loading
- Assets loaded with cache busting in development
- Music files have special mobile handling
- Textures and models loaded on-demand

### Performance Considerations
- 30 FPS cap with deltaTime scaling for consistent gameplay
- Frustum culling for room objects
- Geometry/material caching in prefab system
- Object pooling for projectiles and effects

### Development Environment
- ES6 modules with Three.js importmap
- No build process - runs directly in browser
- Cache busting system for development asset refreshing
- Multiple HTML test files for different scenarios