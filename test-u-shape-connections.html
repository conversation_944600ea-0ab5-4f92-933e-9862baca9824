<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>U-Shape Connection Alignment Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #222;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        #minimap-grid {
            position: relative;
            margin: 20px auto;
            background-color: transparent;
            border: 1px solid #666;
            padding: 20px;
            max-width: 600px;
        }
        
        #minimap-grid svg {
            display: block;
            background: #333;
        }
        
        .test-case {
            margin: 30px 0;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        
        button {
            margin: 5px;
            padding: 10px;
            background: #444;
            color: #fff;
            border: 1px solid #666;
            cursor: pointer;
        }
        
        button:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <h1>U-Shape Door Connection Alignment Test</h1>
    
    <div class="test-case">
        <h3>🎯 Testing Door Connection Points</h3>
        <p>This test verifies that U-shapes align their door openings with the grid points for seamless connections.</p>
        <button onclick="testVerticalConnection()">Test Vertical U-Shapes</button>
        <button onclick="testHorizontalConnection()">Test Horizontal U-Shapes</button>
        <button onclick="testAllConnections()">Test All Connection Types</button>
    </div>
    
    <div id="minimap-grid"></div>
    
    <script>
        // Helper function to create room shape SVG (matching DungeonHandler.js)
        function createRoomShapeSVG(shapeKey, room, cellSize = 14) {
            let svgContent = '';
            let width = cellSize;
            let height = cellSize;
            
            switch (shapeKey) {
                case 'U_SHAPE_DOWN':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `
                        <path d="M0,0 L${cellSize},0 L${cellSize},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${width},0 L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_UP':
                    width = cellSize * 3;
                    height = cellSize * 2;
                    svgContent = `
                        <path d="M0,${cellSize} L0,${height} L${width},${height} L${width},${cellSize} L${cellSize * 2},${cellSize} L${cellSize * 2},0 L${cellSize},0 L${cellSize},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_LEFT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    svgContent = `
                        <path d="M${width},0 L0,0 L0,${height} L${width},${height} L${width},${cellSize * 2} L${cellSize},${cellSize * 2} L${cellSize},${cellSize} L${width},${cellSize} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'U_SHAPE_RIGHT':
                    width = cellSize * 2;
                    height = cellSize * 3;
                    svgContent = `
                        <path d="M0,0 L${width},0 L${width},${cellSize} L${cellSize},${cellSize} L${cellSize},${cellSize * 2} L${width},${cellSize * 2} L${width},${height} L0,${height} Z" 
                              fill="currentColor" stroke="none"/>
                    `;
                    break;
                
                case 'SQUARE_1X1':
                default:
                    svgContent = `<rect x="0" y="0" width="${width}" height="${height}" fill="currentColor"/>`;
                    break;
            }
            
            return { svgContent, width, height };
        }
        
        function createMinimap(rooms, title) {
            const minimapElement = document.getElementById('minimap-grid');
            minimapElement.innerHTML = `<h3>${title}</h3>`;
            
            // Find bounds
            let minX = rooms[0].coords.x, maxX = rooms[0].coords.x;
            let minY = rooms[0].coords.y, maxY = rooms[0].coords.y;
            
            rooms.forEach(room => {
                minX = Math.min(minX, room.coords.x);
                maxX = Math.max(maxX, room.coords.x);
                minY = Math.min(minY, room.coords.y);
                maxY = Math.max(maxY, room.coords.y);
            });
            
            const cellSize = 20;
            const gridUnit = cellSize * 1.8;
            const mapWidth = (maxX - minX + 1) * gridUnit + cellSize * 6;
            const mapHeight = (maxY - minY + 1) * gridUnit + cellSize * 6;
            
            // Create SVG
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', mapWidth);
            svg.setAttribute('height', mapHeight);
            svg.style.display = 'block';
            svg.style.transform = 'rotate(180deg) scaleX(-1)';
            
            // Add grid lines for reference
            for (let x = 0; x <= maxX - minX; x++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridX = x * gridUnit + cellSize * 2;
                line.setAttribute('x1', gridX);
                line.setAttribute('y1', 0);
                line.setAttribute('x2', gridX);
                line.setAttribute('y2', mapHeight);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.3');
                svg.appendChild(line);
            }
            
            for (let y = 0; y <= maxY - minY; y++) {
                const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                const gridY = y * gridUnit + cellSize * 2;
                line.setAttribute('x1', 0);
                line.setAttribute('y1', gridY);
                line.setAttribute('x2', mapWidth);
                line.setAttribute('y2', gridY);
                line.setAttribute('stroke', '#555');
                line.setAttribute('stroke-width', '1');
                line.setAttribute('opacity', '0.3');
                svg.appendChild(line);
            }
            
            // Add rooms with CORRECTED positioning
            rooms.forEach(room => {
                const shapeKey = room.shapeKey || 'SQUARE_1X1';
                const { svgContent, width, height } = createRoomShapeSVG(shapeKey, room, cellSize);
                
                const baseX = (room.coords.x - minX) * gridUnit + cellSize * 2; 
                const baseY = (maxY - room.coords.y) * gridUnit + cellSize * 2;
                
                let gridX = baseX;
                let gridY = baseY;
                
                // CORRECTED positioning - align door connections
                if (shapeKey === 'SQUARE_1X1') {
                    gridX = baseX;
                    gridY = baseY;
                } else if (shapeKey === 'U_SHAPE_DOWN') {
                    // U down: align door connection at bottom opening center
                    gridX = baseX - cellSize; // Center the 3-wide shape
                    gridY = baseY - cellSize; // Position so bottom opening aligns with grid
                } else if (shapeKey === 'U_SHAPE_UP') {
                    // U up: align door connection at top opening center  
                    gridX = baseX - cellSize; // Center the 3-wide shape
                    gridY = baseY; // Position so top opening aligns with grid
                } else if (shapeKey === 'U_SHAPE_LEFT') {
                    // U left: align door connection at left opening center
                    gridX = baseX - cellSize; // Position so left opening aligns with grid
                    gridY = baseY - cellSize; // Center the 3-tall shape
                } else if (shapeKey === 'U_SHAPE_RIGHT') {
                    // U right: align door connection at right opening center
                    gridX = baseX; // Position so right opening aligns with grid
                    gridY = baseY - cellSize; // Center the 3-tall shape
                }
                
                const roomGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                roomGroup.setAttribute('transform', `translate(${gridX}, ${gridY})`);
                
                // Color coding
                let roomColor = '#8a8a8a';
                if (room.id === 0) roomColor = '#ffffff';
                else if (room.type === 'Boss') roomColor = '#ff3333';
                else if (room.type === 'Event') roomColor = '#3399ff';
                else if (room.type === 'Start') roomColor = '#33ff33';
                
                roomGroup.style.color = roomColor;
                roomGroup.innerHTML = svgContent;
                
                // Add connection points for visualization
                const connectionPoint = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                
                // Calculate door connection point relative to shape
                let doorX = 0, doorY = 0;
                if (shapeKey === 'U_SHAPE_DOWN') {
                    doorX = cellSize * 1.5; // Center of bottom opening
                    doorY = cellSize * 2; // Bottom edge
                } else if (shapeKey === 'U_SHAPE_UP') {
                    doorX = cellSize * 1.5; // Center of top opening
                    doorY = 0; // Top edge
                } else if (shapeKey === 'U_SHAPE_LEFT') {
                    doorX = 0; // Left edge
                    doorY = cellSize * 1.5; // Center of left opening
                } else if (shapeKey === 'U_SHAPE_RIGHT') {
                    doorX = cellSize * 2; // Right edge
                    doorY = cellSize * 1.5; // Center of right opening
                } else {
                    doorX = cellSize * 0.5;
                    doorY = cellSize * 0.5;
                }
                
                connectionPoint.setAttribute('cx', doorX);
                connectionPoint.setAttribute('cy', doorY);
                connectionPoint.setAttribute('r', '2');
                connectionPoint.setAttribute('fill', '#ffff00');
                connectionPoint.setAttribute('stroke', '#000');
                connectionPoint.setAttribute('stroke-width', '1');
                roomGroup.appendChild(connectionPoint);
                
                // Add tooltip
                const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
                title.textContent = `Room ${room.id} (${room.type}) - ${shapeKey}`;
                roomGroup.appendChild(title);
                
                svg.appendChild(roomGroup);
            });
            
            minimapElement.appendChild(svg);
        }
        
        function testVerticalConnection() {
            // Test vertical U-shapes connecting
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 0 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 1, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 1, y: 2 }, visited: true },
                { id: 3, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 1, y: 3 }, visited: true }
            ];
            
            createMinimap(rooms, 'Vertical U-Shape Connection Test (Yellow dots = door connections)');
            console.log('Generated vertical U-shape connection test');
        }
        
        function testHorizontalConnection() {
            // Test horizontal U-shapes connecting
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 0, y: 1 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 1, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 2, y: 1 }, visited: true },
                { id: 3, type: 'Boss', shapeKey: 'SQUARE_1X1', coords: { x: 3, y: 1 }, visited: true }
            ];
            
            createMinimap(rooms, 'Horizontal U-Shape Connection Test (Yellow dots = door connections)');
            console.log('Generated horizontal U-shape connection test');
        }
        
        function testAllConnections() {
            // Test all U-shapes in a cross pattern
            const rooms = [
                { id: 0, type: 'Start', shapeKey: 'SQUARE_1X1', coords: { x: 2, y: 2 }, visited: true },
                { id: 1, type: 'Normal', shapeKey: 'U_SHAPE_UP', coords: { x: 2, y: 1 }, visited: true },
                { id: 2, type: 'Normal', shapeKey: 'U_SHAPE_RIGHT', coords: { x: 3, y: 2 }, visited: true },
                { id: 3, type: 'Normal', shapeKey: 'U_SHAPE_DOWN', coords: { x: 2, y: 3 }, visited: true },
                { id: 4, type: 'Normal', shapeKey: 'U_SHAPE_LEFT', coords: { x: 1, y: 2 }, visited: true }
            ];
            
            createMinimap(rooms, 'All U-Shape Connection Alignment Test');
            console.log('Generated comprehensive U-shape connection test');
        }
        
        // Test vertical connections by default
        testVerticalConnection();
    </script>
</body>
</html>